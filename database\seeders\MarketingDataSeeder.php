<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WechatGroup;
use App\Models\PaymentChannel;

class MarketingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('开始填充营销数据...');

        // 为现有群组添加默认营销数据
        $this->seedGroupMarketingData();
        
        // 创建默认支付渠道
        $this->seedPaymentChannels();

        $this->command->info('营销数据填充完成！');
    }

    /**
     * 填充群组营销数据
     */
    private function seedGroupMarketingData(): void
    {
        $updatedCount = 0;
        
        WechatGroup::whereNull('read_count_display')->chunk(100, function ($groups) use (&$updatedCount) {
            foreach ($groups as $group) {
                $group->update([
                    'read_count_display' => $this->getRandomReadCount(),
                    'like_count' => rand(100, 999),
                    'want_see_count' => rand(50, 500),
                    'button_title' => $this->getRandomButtonTitle(),
                    'group_intro_title' => '群简介',
                    'group_intro_content' => $this->getRandomGroupIntro(),
                    'faq_title' => '常见问题',
                    'faq_content' => $this->getRandomFAQ(),
                    'member_reviews' => $this->getRandomReviews(),
                    'avatar_library' => rand(0, 1) ? 'qq' : 'za',
                    'wx_accessible' => 1,
                    'display_type' => 1,
                    'auto_city_replace' => 1,
                    'city_insert_strategy' => 'auto',
                    'virtual_members' => rand(50, 200),
                    'virtual_orders' => rand(20, 100),
                    'virtual_income' => rand(1000, 5000),
                    'today_views' => rand(10, 50),
                    'total_joins' => rand(100, 500),
                    'payment_status' => 1,
                    'show_customer_service' => rand(1, 2),
                    'customer_service_title' => 'VIP专属客服',
                    'customer_service_desc' => '出现不能付款，不能入群等问题，请联系我！看到信息秒回',
                    'marketing_tags' => $this->getRandomMarketingTags(),
                ]);
                $updatedCount++;
            }
        });

        $this->command->info("已更新 {$updatedCount} 个群组的营销数据");
    }

    /**
     * 填充支付渠道数据
     */
    private function seedPaymentChannels(): void
    {
        $channels = [
            [
                'channel_code' => 'wechat',
                'channel_name' => '微信支付',
                'channel_desc' => '使用微信扫码支付',
                'channel_icon' => '/images/payment/wechat.png',
                'status' => 1,
                'is_active' => true,
                'fee_rate' => 0.006,
                'min_amount' => 0.01,
                'max_amount' => 50000.00,
                'sort_order' => 1,
                'allow_substation' => true,
                'allow_distribution' => true,
                'config_template' => [
                    'appid' => [
                        'label' => '应用ID',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入微信应用ID'
                    ],
                    'appsecret' => [
                        'label' => '应用密钥',
                        'type' => 'password',
                        'required' => true,
                        'placeholder' => '请输入微信应用密钥'
                    ],
                    'mch_id' => [
                        'label' => '商户号',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入微信商户号'
                    ],
                    'pay_apikey' => [
                        'label' => 'API密钥',
                        'type' => 'password',
                        'required' => true,
                        'placeholder' => '请输入API密钥'
                    ]
                ]
            ],
            [
                'channel_code' => 'alipay',
                'channel_name' => '支付宝',
                'channel_desc' => '使用支付宝扫码支付',
                'channel_icon' => '/images/payment/alipay.png',
                'status' => 1,
                'is_active' => true,
                'fee_rate' => 0.006,
                'min_amount' => 0.01,
                'max_amount' => 50000.00,
                'sort_order' => 2,
                'allow_substation' => true,
                'allow_distribution' => true,
                'config_template' => [
                    'app_id' => [
                        'label' => '应用ID',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入支付宝应用ID'
                    ],
                    'private_key' => [
                        'label' => '应用私钥',
                        'type' => 'textarea',
                        'required' => true,
                        'placeholder' => '请输入应用私钥'
                    ],
                    'public_key' => [
                        'label' => '支付宝公钥',
                        'type' => 'textarea',
                        'required' => true,
                        'placeholder' => '请输入支付宝公钥'
                    ]
                ]
            ],
            [
                'channel_code' => 'payoreo',
                'channel_name' => '易支付',
                'channel_desc' => '第三方易支付平台',
                'channel_icon' => '/images/payment/payoreo.png',
                'status' => 1,
                'is_active' => true,
                'fee_rate' => 0.008,
                'min_amount' => 0.01,
                'max_amount' => 10000.00,
                'sort_order' => 3,
                'allow_substation' => true,
                'allow_distribution' => true,
                'config_template' => [
                    'api_url' => [
                        'label' => 'API地址',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入易支付API地址'
                    ],
                    'pid' => [
                        'label' => '商户ID',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入商户ID'
                    ],
                    'key' => [
                        'label' => '商户密钥',
                        'type' => 'password',
                        'required' => true,
                        'placeholder' => '请输入商户密钥'
                    ],
                    'notify_url' => [
                        'label' => '异步通知地址',
                        'type' => 'text',
                        'required' => false,
                        'placeholder' => '留空使用默认地址'
                    ],
                    'return_url' => [
                        'label' => '同步返回地址',
                        'type' => 'text',
                        'required' => false,
                        'placeholder' => '留空使用默认地址'
                    ]
                ]
            ],
            [
                'channel_code' => 'qqpay',
                'channel_name' => 'QQ钱包',
                'channel_desc' => '使用QQ钱包支付',
                'channel_icon' => '/images/payment/qqpay.png',
                'status' => 1,
                'is_active' => true,
                'fee_rate' => 0.006,
                'min_amount' => 0.01,
                'max_amount' => 50000.00,
                'sort_order' => 4,
                'allow_substation' => true,
                'allow_distribution' => true,
                'config_template' => [
                    'mch_id' => [
                        'label' => '商户号',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入QQ钱包商户号'
                    ],
                    'api_key' => [
                        'label' => 'API密钥',
                        'type' => 'password',
                        'required' => true,
                        'placeholder' => '请输入API密钥'
                    ]
                ]
            ],
            [
                'channel_code' => 'bank',
                'channel_name' => '银行转账',
                'channel_desc' => '银行卡转账支付',
                'channel_icon' => '/images/payment/bank.png',
                'status' => 1,
                'is_active' => true,
                'fee_rate' => 0.000,
                'min_amount' => 1.00,
                'max_amount' => 100000.00,
                'sort_order' => 5,
                'allow_substation' => true,
                'allow_distribution' => true,
                'config_template' => [
                    'bank_name' => [
                        'label' => '银行名称',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入银行名称'
                    ],
                    'account_name' => [
                        'label' => '账户名',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入账户名'
                    ],
                    'account_number' => [
                        'label' => '银行账号',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => '请输入银行账号'
                    ]
                ]
            ]
        ];

        foreach ($channels as $channelData) {
            PaymentChannel::updateOrCreate(
                ['channel_code' => $channelData['channel_code']],
                $channelData
            );
        }

        $this->command->info('已创建/更新 ' . count($channels) . ' 个支付渠道');
    }

    /**
     * 获取随机阅读数
     */
    private function getRandomReadCount(): string
    {
        $options = ['10万+', '5万+', '3万+', '1万+', '8888', '6666', '5555', '3333', '2222'];
        return $options[array_rand($options)];
    }

    /**
     * 获取随机按钮标题
     */
    private function getRandomButtonTitle(): string
    {
        $options = [
            '立即加入群聊',
            '加入群，学习更多副业知识',
            '点击进群',
            '马上加入',
            '立即进群学习',
            '加入我们',
            '免费进群',
            '扫码入群'
        ];
        return $options[array_rand($options)];
    }

    /**
     * 获取随机群简介
     */
    private function getRandomGroupIntro(): string
    {
        $intros = [
            '这是一个专业的学习交流群，汇聚了众多行业精英和专家，为大家提供最新的行业资讯和学习资源。',
            '欢迎加入我们的大家庭！在这里你可以结识志同道合的朋友，分享经验，共同成长。',
            '本群致力于为成员提供高质量的内容分享和深度交流，帮助大家在各自领域取得更好的发展。',
            '加入我们，开启全新的学习之旅！群内有丰富的学习资料和实战经验分享。',
            '这里是知识的海洋，智慧的聚集地。与优秀的人为伍，让自己变得更加优秀。'
        ];
        return $intros[array_rand($intros)];
    }

    /**
     * 获取随机FAQ
     */
    private function getRandomFAQ(): string
    {
        return "Q: 群里主要分享什么内容？----A: 主要分享行业最新资讯、实用技巧和经验交流\n" .
               "Q: 如何获取群内资料？----A: 群内会定期分享资料，也可以向管理员申请\n" .
               "Q: 群内可以发广告吗？----A: 请勿发送无关广告，违者将被移出群聊\n" .
               "Q: 如何联系管理员？----A: 可以在群内@管理员或私聊联系";
    }

    /**
     * 获取随机群友评价
     */
    private function getRandomReviews(): string
    {
        return "群里的内容真的很有价值，学到了很多东西----666\n" .
               "管理员很负责，群友也很友善----888\n" .
               "资料很全面，对我的工作帮助很大----999\n" .
               "氛围很好，大家都很乐于分享----520\n" .
               "强烈推荐，物超所值！----1314";
    }

    /**
     * 获取随机营销标签
     */
    private function getRandomMarketingTags(): array
    {
        $allTags = [
            '热门推荐', '限时优惠', '精品内容', '专业指导', 
            '实战经验', '行业精英', '资源丰富', '互动活跃',
            '干货满满', '价值连城', '口碑推荐', '品质保证'
        ];
        
        // 随机选择2-4个标签
        $selectedCount = rand(2, 4);
        $selectedTags = array_rand($allTags, $selectedCount);
        
        if (is_array($selectedTags)) {
            return array_map(function($index) use ($allTags) {
                return $allTags[$index];
            }, $selectedTags);
        } else {
            return [$allTags[$selectedTags]];
        }
    }
}