import request from '@/utils/request'

/**
 * 文件上传相关API
 */

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('avatar', file)
  
  return request({
    url: '/api/upload/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传图片
export function uploadImage(file, type = 'image') {
  const formData = new FormData()
  formData.append('image', file)
  formData.append('type', type)
  
  return request({
    url: '/api/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传文件
export function uploadFile(file, type = 'file') {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return request({
    url: '/api/upload/file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除文件
export function deleteFile(fileId) {
  return request({
    url: `/api/upload/file/${fileId}`,
    method: 'delete'
  })
}

// 获取文件列表
export function getFileList(params) {
  return request({
    url: '/api/upload/files',
    method: 'get',
    params
  })
}

// 批量上传
export function batchUpload(files, type = 'image') {
  const formData = new FormData()
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file)
  })
  formData.append('type', type)
  
  return request({
    url: '/api/upload/batch',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取上传配置
export function getUploadConfig() {
  return request({
    url: '/api/upload/config',
    method: 'get'
  })
}