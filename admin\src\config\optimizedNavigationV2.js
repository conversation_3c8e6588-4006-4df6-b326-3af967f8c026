/**
 * 第二阶段优化后的导航分组配置
 * 基于第一阶段权限控制，进一步优化导航结构和用户体验
 */

import { roleHierarchy } from './navigation'

// 优化后的导航分组结构（第二阶段）
export const optimizedNavigationGroupsV2 = {
  // 工作台 - 个性化工作区（新增）
  workbench: {
    title: '工作台',
    icon: 'Monitor',
    order: 1,
    description: '个性化工作区，快速访问常用功能',
    children: [
      {
        path: '/workbench/personal',
        title: '个人工作台',
        icon: 'User',
        description: '根据角色定制的个人工作区',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/workbench/quick-actions',
        title: '快速操作',
        icon: 'Lightning',
        description: '常用操作快捷入口',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/workbench/recent',
        title: '最近访问',
        icon: 'Clock',
        description: '最近使用的功能和页面',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      }
    ]
  },

  // 数据中心 - 保持原有结构，增强权限控制
  dataCenter: {
    title: '数据中心',
    icon: 'DataBoard',
    order: 2,
    description: '数据分析、统计报表、实时监控',
    children: [
      {
        path: '/dashboard',
        title: '实时看板',
        icon: 'Monitor',
        description: '核心业务数据总览',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        permissionScope: 'dashboard'
      },
      {
        path: '/data-screen',
        title: '数据大屏',
        icon: 'DataLine',
        description: '全屏数据展示',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        permissionScope: 'dashboard'
      },
      {
        path: '/analytics/business',
        title: '业务分析',
        icon: 'TrendCharts',
        description: '深度业务数据分析',
        roles: ['admin', 'substation', 'agent'],
        permissionScope: 'analytics'
      },
      {
        path: '/reports/comprehensive',
        title: '综合报表',
        icon: 'Document',
        description: '各类业务统计报表',
        roles: ['admin', 'substation'],
        permissionScope: 'reports'
      }
    ]
  },

  // 核心业务 - 重组业务功能
  coreBusiness: {
    title: '核心业务',
    icon: 'Management',
    order: 3,
    description: '社群运营、订单处理、客户服务等核心业务',
    children: [
      {
        path: '/business/community',
        title: '社群运营',
        icon: 'Comment',
        description: '群组管理、内容管理、营销活动',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner'],
        children: [
          { 
            path: '/community/groups', 
            title: '群组管理', 
            icon: 'UserFilled',
            roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
          },
          {
            path: '/community/add-enhanced',
            title: '创建群组',
            icon: 'Plus',
            roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
            protected: true // 标记为受保护的核心功能
          },
          { 
            path: '/community/templates', 
            title: '内容模板', 
            icon: 'DocumentCopy',
            roles: ['admin', 'substation', 'group_owner']
          },
          { 
            path: '/community/marketing', 
            title: '营销活动', 
            icon: 'Promotion',
            roles: ['admin', 'substation', 'agent', 'distributor']
          }
        ]
      },
      {
        path: '/business/orders',
        title: '订单中心',
        icon: 'ShoppingCart',
        description: '订单管理、支付处理、售后服务',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        children: [
          { 
            path: '/orders/list', 
            title: '订单管理', 
            icon: 'List',
            roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
          },
          { 
            path: '/orders/payments', 
            title: '支付管理', 
            icon: 'CreditCard',
            roles: ['admin', 'substation']
          },
          { 
            path: '/orders/refunds', 
            title: '售后服务', 
            icon: 'RefreshLeft',
            roles: ['admin', 'substation', 'group_owner']
          }
        ]
      },
      {
        path: '/business/customers',
        title: '客户服务',
        icon: 'UserFilled',
        description: '客户管理、服务记录、满意度调查',
        roles: ['admin', 'substation', 'agent', 'distributor'],
        children: [
          { 
            path: '/customers/list', 
            title: '客户管理', 
            icon: 'User',
            roles: ['admin', 'substation', 'agent', 'distributor']
          },
          { 
            path: '/customers/service', 
            title: '服务记录', 
            icon: 'Document',
            roles: ['admin', 'substation', 'agent']
          },
          { 
            path: '/customers/feedback', 
            title: '满意度调查', 
            icon: 'Star',
            roles: ['admin', 'substation']
          }
        ]
      }
    ]
  },

  // 分销网络 - 优化分销体系管理
  distributionNetwork: {
    title: '分销网络',
    icon: 'Share',
    order: 4,
    description: '分销体系、推广营销、佣金结算',
    children: [
      {
        path: '/distribution/hierarchy',
        title: '分销体系',
        icon: 'Connection',
        description: '代理商管理、分销员管理、层级关系',
        roles: ['admin', 'substation', 'agent'],
        children: [
          { 
            path: '/agents/list', 
            title: '代理商管理', 
            icon: 'Avatar',
            roles: ['admin', 'substation']
          },
          { 
            path: '/distributors/list', 
            title: '分销员管理', 
            icon: 'User',
            roles: ['admin', 'substation', 'agent']
          },
          { 
            path: '/distribution/relationships', 
            title: '层级关系', 
            icon: 'Connection',
            roles: ['admin', 'substation', 'agent']
          }
        ]
      },
      {
        path: '/distribution/marketing',
        title: '推广营销',
        icon: 'Promotion',
        description: '推广链接、营销工具、效果分析',
        roles: ['admin', 'substation', 'agent', 'distributor'],
        children: [
          { 
            path: '/promotion/links', 
            title: '推广链接', 
            icon: 'Link',
            roles: ['admin', 'substation', 'agent', 'distributor']
          },
          { 
            path: '/promotion/tools', 
            title: '营销工具', 
            icon: 'Tools',
            roles: ['admin', 'substation', 'agent']
          },
          { 
            path: '/promotion/analytics', 
            title: '效果分析', 
            icon: 'DataAnalysis',
            roles: ['admin', 'substation', 'agent', 'distributor']
          }
        ]
      },
      {
        path: '/distribution/commission',
        title: '佣金结算',
        icon: 'Money',
        description: '佣金规则、结算记录、提现管理',
        roles: ['admin', 'substation', 'agent', 'distributor'],
        children: [
          { 
            path: '/finance/commission/rules', 
            title: '佣金规则', 
            icon: 'Setting',
            roles: ['admin', 'substation']
          },
          { 
            path: '/finance/commission/logs', 
            title: '结算记录', 
            icon: 'List',
            roles: ['admin', 'substation', 'agent', 'distributor']
          },
          { 
            path: '/finance/withdrawals', 
            title: '提现管理', 
            icon: 'Upload',
            roles: ['admin', 'substation', 'agent', 'distributor']
          }
        ]
      },
      {
        path: '/distribution/substation',
        title: '分站管理',
        icon: 'OfficeBuilding',
        description: '多站点运营管理、财务分析、数据监控',
        roles: ['admin'],
        children: [
          { 
            path: '/substation/list', 
            title: '分站列表', 
            icon: 'List',
            roles: ['admin'],
            description: '查看和管理所有分站'
          },
          { 
            path: '/substation/finance', 
            title: '分站财务', 
            icon: 'Money',
            roles: ['admin'],
            description: '分站收入统计、佣金结算、财务报表'
          },
          { 
            path: '/substation/analytics', 
            title: '分站分析', 
            icon: 'DataAnalysis',
            roles: ['admin'],
            description: '分站运营数据分析、业绩对比'
          }
        ]
      }
    ]
  },

  // 系统管理 - 整合管理功能
  systemManagement: {
    title: '系统管理',
    icon: 'Setting',
    order: 5,
    description: '用户权限、系统配置、运维监控',
    children: [
      {
        path: '/system/users',
        title: '用户权限',
        icon: 'Lock',
        description: '用户管理、角色管理、权限配置',
        roles: ['admin', 'substation'],
        children: [
          { 
            path: '/users/list', 
            title: '用户管理', 
            icon: 'User',
            roles: ['admin', 'substation']
          },
          {
            path: '/admin/roles',
            title: '角色管理',
            icon: 'UserFilled',
            roles: ['admin', 'substation']
          },
          {
            path: '/admin/permission',
            title: '权限配置',
            icon: 'Key',
            roles: ['admin']
          }
        ]
      },
      {
        path: '/system/config',
        title: '系统配置',
        icon: 'Tools',
        description: '基础设置、支付配置、安全设置',
        roles: ['admin', 'substation'],
        children: [
          { 
            path: '/system/settings', 
            title: '基础设置', 
            icon: 'Setting',
            roles: ['admin', 'substation']
          },
          {
            path: '/admin/payment-settings',
            title: '支付设置',
            icon: 'CreditCard',
            roles: ['admin', 'substation'],
            description: '支付渠道配置、支付参数设置'
          },
          { 
            path: '/anti-block', 
            title: '防红配置', 
            icon: 'Shield',
            roles: ['admin', 'substation'],
            description: '防封系统配置、域名管理'
          },
          { 
            path: '/security/management', 
            title: '安全设置', 
            icon: 'Lock',
            roles: ['admin']
          }
        ]
      },
      {
        path: '/system/operations',
        title: '运维监控',
        icon: 'Monitor',
        description: '系统监控、日志管理、数据备份',
        roles: ['admin'],
        children: [
          { 
            path: '/system/monitor', 
            title: '系统监控', 
            icon: 'Monitor',
            roles: ['admin']
          },
          { 
            path: '/system/logs', 
            title: '日志管理', 
            icon: 'Document',
            roles: ['admin']
          },
          { 
            path: '/system/backup', 
            title: '数据备份', 
            icon: 'Folder',
            roles: ['admin']
          }
        ]
      }
    ]
  }
}

// 角色专属导航配置（第二阶段优化）
export const roleSpecificNavigationV2 = {
  admin: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness', 'distributionNetwork', 'systemManagement'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/admin/groups', icon: 'Plus', color: '#409eff' },
      { title: '分站管理', path: '/admin/substations', icon: 'OfficeBuilding', color: '#67c23a' },
      { title: '支付设置', path: '/admin/payment-settings', icon: 'CreditCard', color: '#e6a23c' },
      { title: '防红配置', path: '/admin/anti-block', icon: 'Shield', color: '#f56c6c' }
    ]
  },
  substation: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness', 'distributionNetwork', 'systemManagement'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/admin/groups', icon: 'Plus', color: '#409eff' },
      { title: '支付设置', path: '/admin/payment-settings', icon: 'CreditCard', color: '#67c23a' },
      { title: '防红配置', path: '/admin/anti-block', icon: 'Shield', color: '#e6a23c' },
      { title: '代理商管理', path: '/admin/agents', icon: 'Avatar', color: '#f56c6c' }
    ]
  },
  agent: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness', 'distributionNetwork'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
      { title: '团队管理', path: '/distributors/list', icon: 'User', color: '#67c23a' },
      { title: '佣金查看', path: '/finance/commission/logs', icon: 'Money', color: '#e6a23c' },
      { title: '推广分析', path: '/promotion/analytics', icon: 'TrendCharts', color: '#f56c6c' }
    ]
  },
  distributor: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness', 'distributionNetwork'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/admin/groups', icon: 'Plus', color: '#409eff' },
      { title: '客户管理', path: '/admin/users', icon: 'UserFilled', color: '#67c23a' },
      { title: '推广链接', path: '/admin/promotion', icon: 'Link', color: '#e6a23c' },
      { title: '佣金记录', path: '/admin/commission-logs', icon: 'Money', color: '#f56c6c' }
    ]
  },
  group_owner: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
      { title: '我的群组', path: '/community/groups', icon: 'Comment', color: '#67c23a' },
      { title: '内容模板', path: '/community/templates', icon: 'DocumentCopy', color: '#e6a23c' },
      { title: '群组数据', path: '/dashboard', icon: 'DataLine', color: '#f56c6c' }
    ]
  },
  user: {
    visibleGroups: ['workbench', 'dataCenter', 'coreBusiness'],
    defaultGroup: 'workbench',
    customWorkbench: '/workbench/personal',
    quickActions: [
      { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
      { title: '我的订单', path: '/orders/list', icon: 'Tickets', color: '#67c23a' },
      { title: '个人中心', path: '/user/center', icon: 'User', color: '#e6a23c' }
    ]
  }
}

// 增强搜索配置（第二阶段）
export const enhancedSearchConfig = {
  // 搜索快捷键
  shortcuts: {
    globalSearch: 'Ctrl+K',
    toggleSidebar: 'Ctrl+B',
    quickCreate: 'Ctrl+N',
    focusSearch: '/',
    clearSearch: 'Escape'
  },
  
  // 搜索分类（优化）
  categories: [
    { key: 'menus', title: '菜单功能', icon: 'Menu', weight: 10 },
    { key: 'groups', title: '群组', icon: 'Comment', weight: 9 },
    { key: 'users', title: '用户', icon: 'User', weight: 8 },
    { key: 'orders', title: '订单', icon: 'ShoppingCart', weight: 7 },
    { key: 'finance', title: '财务', icon: 'Money', weight: 6 },
    { key: 'content', title: '内容', icon: 'Document', weight: 5 },
    { key: 'system', title: '系统', icon: 'Setting', weight: 4 }
  ],
  
  // 搜索权重配置
  searchWeights: {
    title: 10,        // 标题匹配权重最高
    description: 5,   // 描述匹配权重中等
    keywords: 3,      // 关键词匹配权重较低
    path: 2,          // 路径匹配权重最低
    recent: 15        // 最近访问权重加成
  },
  
  // 智能推荐配置
  recommendations: {
    enabled: true,
    maxResults: 5,
    basedOnRole: true,      // 基于角色推荐
    basedOnUsage: true,     // 基于使用频率推荐
    basedOnTime: true       // 基于时间推荐
  }
}

// 个性化工作台配置
export const personalWorkbenchConfig = {
  admin: {
    title: '管理员控制台',
    description: '系统全局管理和监控',
    layout: 'dashboard',
    widgets: [
      { type: 'stats', title: '系统概览', span: 12 },
      { type: 'chart', title: '用户增长趋势', span: 12 },
      { type: 'activities', title: '最新动态', span: 8 },
      { type: 'alerts', title: '系统告警', span: 4 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  },
  substation: {
    title: '分站管理中心',
    description: '分站运营和用户管理',
    layout: 'dashboard',
    widgets: [
      { type: 'stats', title: '分站概览', span: 12 },
      { type: 'chart', title: '业务数据', span: 12 },
      { type: 'users', title: '用户管理', span: 8 },
      { type: 'finance', title: '财务数据', span: 4 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  },
  agent: {
    title: '代理商工作台',
    description: '团队管理和业绩分析',
    layout: 'performance',
    widgets: [
      { type: 'team-stats', title: '团队概览', span: 12 },
      { type: 'commission-chart', title: '佣金趋势', span: 12 },
      { type: 'team-list', title: '团队成员', span: 16 },
      { type: 'performance', title: '业绩排行', span: 8 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  },
  distributor: {
    title: '分销员工作台',
    description: '客户管理和群组运营',
    layout: 'customer',
    widgets: [
      { type: 'customer-stats', title: '客户概览', span: 12 },
      { type: 'group-stats', title: '群组数据', span: 12 },
      { type: 'customer-list', title: '客户列表', span: 16 },
      { type: 'commission', title: '佣金记录', span: 8 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  },
  group_owner: {
    title: '群主工作台',
    description: '群组内容和成员管理',
    layout: 'group',
    widgets: [
      { type: 'group-stats', title: '群组概览', span: 12 },
      { type: 'member-chart', title: '成员增长', span: 12 },
      { type: 'group-list', title: '我的群组', span: 16 },
      { type: 'content', title: '内容管理', span: 8 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  },
  user: {
    title: '个人中心',
    description: '个人信息和订单管理',
    layout: 'personal',
    widgets: [
      { type: 'user-stats', title: '个人概览', span: 12 },
      { type: 'order-chart', title: '订单统计', span: 12 },
      { type: 'order-list', title: '我的订单', span: 16 },
      { type: 'profile', title: '个人信息', span: 8 },
      { type: 'quick-actions', title: '快速操作', span: 24 }
    ]
  }
}

/**
 * 根据用户角色获取可见的导航分组
 * @param {string} userRole - 用户角色
 * @returns {Object} 过滤后的导航分组
 */
export function getVisibleNavigationGroups(userRole) {
  const roleConfig = roleSpecificNavigationV2[userRole]
  if (!roleConfig) return {}

  const visibleGroups = {}
  roleConfig.visibleGroups.forEach(groupKey => {
    if (optimizedNavigationGroupsV2[groupKey]) {
      visibleGroups[groupKey] = filterNavigationByRole(
        optimizedNavigationGroupsV2[groupKey], 
        userRole
      )
    }
  })

  return visibleGroups
}

/**
 * 根据角色过滤导航项
 * @param {Object} navigationGroup - 导航分组
 * @param {string} userRole - 用户角色
 * @returns {Object} 过滤后的导航分组
 */
function filterNavigationByRole(navigationGroup, userRole) {
  const filtered = { ...navigationGroup }
  
  if (filtered.children) {
    filtered.children = filtered.children.filter(child => {
      // 检查角色权限
      if (child.roles && !child.roles.includes(userRole)) {
        return false
      }
      
      // 递归过滤子菜单（支持多层嵌套）
      if (child.children) {
        child.children = child.children.filter(subChild => {
          // 检查子菜单的角色权限
          if (subChild.roles && !subChild.roles.includes(userRole)) {
            return false
          }
          
          // 如果还有更深层的子菜单，继续递归过滤
          if (subChild.children) {
            subChild.children = subChild.children.filter(deepChild => {
              return !deepChild.roles || deepChild.roles.includes(userRole)
            })
          }
          
          return true
        })
      }
      
      return true
    })
  }
  
  return filtered
}

/**
 * 获取用户的快速操作配置
 * @param {string} userRole - 用户角色
 * @returns {Array} 快速操作列表
 */
export function getUserQuickActions(userRole) {
  const roleConfig = roleSpecificNavigationV2[userRole]
  return roleConfig?.quickActions || []
}

/**
 * 获取用户的个性化工作台配置
 * @param {string} userRole - 用户角色
 * @returns {Object} 工作台配置
 */
export function getUserWorkbenchConfig(userRole) {
  return personalWorkbenchConfig[userRole] || personalWorkbenchConfig.user
}

/**
 * 初始化搜索数据（第二阶段优化）
 * @param {string} userRole - 用户角色
 * @returns {Array} 搜索数据
 */
export function initializeEnhancedSearchData(userRole) {
  const searchData = []
  const visibleGroups = getVisibleNavigationGroups(userRole)
  
  Object.entries(visibleGroups).forEach(([groupKey, group]) => {
    // 添加分组
    searchData.push({
      id: `group-${groupKey}`,
      title: group.title,
      description: group.description,
      type: 'group',
      category: 'menus',
      icon: group.icon,
      path: null,
      keywords: [group.title, group.description],
      weight: enhancedSearchConfig.searchWeights.title,
      roles: [userRole]
    })
    
    // 添加子菜单
    if (group.children) {
      group.children.forEach(child => {
        searchData.push({
          id: `menu-${child.path}`,
          title: child.title,
          description: child.description || '',
          type: 'menu',
          category: 'menus',
          icon: child.icon,
          path: child.path,
          group: group.title,
          keywords: [child.title, child.description || '', group.title],
          weight: enhancedSearchConfig.searchWeights.title,
          roles: child.roles || [userRole],
          protected: child.protected || false
        })
        
        // 添加子子菜单
        if (child.children) {
          child.children.forEach(subChild => {
            searchData.push({
              id: `submenu-${subChild.path}`,
              title: subChild.title,
              description: `${child.title} - ${subChild.title}`,
              type: 'submenu',
              category: 'menus',
              icon: subChild.icon,
              path: subChild.path,
              parent: child.title,
              group: group.title,
              keywords: [subChild.title, child.title, group.title],
              weight: enhancedSearchConfig.searchWeights.title,
              roles: subChild.roles || [userRole]
            })
          })
        }
      })
    }
  })
  
  return searchData
}