<template>
  <PageLayout
    title="数据报表"
    subtitle="查看详细的数据分析报表"
    icon="TrendCharts"
    :loading="loading"
  >
    <template #actions>
      <el-button @click="exportReport">
        <el-icon><Download /></el-icon>
        导出报表
      </el-button>
      <el-button type="primary" @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </template>

    <div class="reports-container">
      <!-- 时间筛选 -->
      <el-card class="filter-card" shadow="never">
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-select v-model="timeRange" placeholder="选择时间范围" @change="handleTimeRangeChange">
              <el-option label="今日" value="today" />
              <el-option label="昨日" value="yesterday" />
              <el-option label="最近7天" value="week" />
              <el-option label="最近30天" value="month" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-col>
          <el-col :span="10" v-if="timeRange === 'custom'">
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleCustomDateChange"
            />
          </el-col>
          <el-col :span="8">
            <el-button-group>
              <el-button 
                :type="reportType === 'overview' ? 'primary' : ''" 
                @click="reportType = 'overview'"
              >
                概览
              </el-button>
              <el-button 
                :type="reportType === 'user' ? 'primary' : ''" 
                @click="reportType = 'user'"
              >
                用户
              </el-button>
              <el-button 
                :type="reportType === 'finance' ? 'primary' : ''" 
                @click="reportType = 'finance'"
              >
                财务
              </el-button>
              <el-button 
                :type="reportType === 'group' ? 'primary' : ''" 
                @click="reportType = 'group'"
              >
                群组
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </el-card>

      <!-- 概览报表 -->
      <div v-if="reportType === 'overview'" class="report-section">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-card class="metric-card">
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(overviewData.totalRevenue) }}</div>
                <div class="metric-label">总收入（元）</div>
                <div class="metric-change positive">+{{ overviewData.revenueGrowth }}%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card">
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(overviewData.totalOrders) }}</div>
                <div class="metric-label">总订单数</div>
                <div class="metric-change positive">+{{ overviewData.ordersGrowth }}%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card">
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(overviewData.totalUsers) }}</div>
                <div class="metric-label">总用户数</div>
                <div class="metric-change positive">+{{ overviewData.usersGrowth }}%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="metric-card">
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(overviewData.totalGroups) }}</div>
                <div class="metric-label">总群组数</div>
                <div class="metric-change positive">+{{ overviewData.groupsGrowth }}%</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 趋势图表 -->
        <el-card class="chart-card" shadow="never">
          <template #header>
            <h3>收入趋势</h3>
          </template>
          <div class="chart-placeholder">
            <el-empty description="图表组件待集成" />
          </div>
        </el-card>
      </div>

      <!-- 用户报表 -->
      <div v-if="reportType === 'user'" class="report-section">
        <el-card shadow="never">
          <template #header>
            <h3>用户分析报表</h3>
          </template>
          <el-table :data="userData" style="width: 100%">
            <el-table-column prop="date" label="日期" />
            <el-table-column prop="newUsers" label="新增用户" />
            <el-table-column prop="activeUsers" label="活跃用户" />
            <el-table-column prop="retentionRate" label="留存率">
              <template #default="{ row }">
                {{ row.retentionRate }}%
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 财务报表 -->
      <div v-if="reportType === 'finance'" class="report-section">
        <el-card shadow="never">
          <template #header>
            <h3>财务分析报表</h3>
          </template>
          <el-table :data="financeData" style="width: 100%">
            <el-table-column prop="date" label="日期" />
            <el-table-column prop="revenue" label="收入（元）" />
            <el-table-column prop="orders" label="订单数" />
            <el-table-column prop="avgOrderValue" label="客单价（元）" />
          </el-table>
        </el-card>
      </div>

      <!-- 群组报表 -->
      <div v-if="reportType === 'group'" class="report-section">
        <el-card shadow="never">
          <template #header>
            <h3>群组分析报表</h3>
          </template>
          <el-table :data="groupData" style="width: 100%">
            <el-table-column prop="name" label="群组名称" />
            <el-table-column prop="members" label="成员数" />
            <el-table-column prop="messages" label="消息数" />
            <el-table-column prop="revenue" label="收入（元）" />
          </el-table>
        </el-card>
      </div>
    </div>
  </PageLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh, TrendCharts } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'

const loading = ref(false)
const timeRange = ref('week')
const customDateRange = ref([])
const reportType = ref('overview')

// 概览数据
const overviewData = reactive({
  totalRevenue: 125680.50,
  revenueGrowth: 15.2,
  totalOrders: 1256,
  ordersGrowth: 8.7,
  totalUsers: 8945,
  usersGrowth: 12.3,
  totalGroups: 156,
  groupsGrowth: 6.8
})

// 用户数据
const userData = ref([
  { date: '2024-08-01', newUsers: 45, activeUsers: 1250, retentionRate: 85.2 },
  { date: '2024-08-02', newUsers: 52, activeUsers: 1298, retentionRate: 86.1 }
])

// 财务数据
const financeData = ref([
  { date: '2024-08-01', revenue: 8520.50, orders: 85, avgOrderValue: 100.24 },
  { date: '2024-08-02', revenue: 9240.80, orders: 92, avgOrderValue: 100.44 }
])

// 群组数据
const groupData = ref([
  { name: 'VIP群组', members: 98, messages: 1250, revenue: 9800.00 },
  { name: '普通群组', members: 156, messages: 2340, revenue: 1560.00 }
])

// 格式化数字
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  if (value !== 'custom') {
    customDateRange.value = []
  }
  refreshData()
}

// 处理自定义日期变化
const handleCustomDateChange = () => {
  refreshData()
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 导出报表
const exportReport = () => {
  ElMessage.success('报表导出功能开发中')
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.reports-container {
  .filter-card {
    margin-bottom: 24px;
  }
  
  .report-section {
    .metric-card {
      margin-bottom: 24px;
      
      .metric-content {
        text-align: center;
        
        .metric-value {
          font-size: 32px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }
        
        .metric-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .metric-change {
          font-size: 12px;
          
          &.positive {
            color: #67c23a;
          }
          
          &.negative {
            color: #f56c6c;
          }
        }
      }
    }
    
    .chart-card {
      margin-bottom: 24px;
      
      .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
