import { useUserStore } from '@/stores/user'
import { checkMenuPermission, getUserDefaultRoute } from '@/config/navigation'

/**
 * 检查用户是否有指定角色
 * @param {string|Array} roles - 角色名称或角色数组
 * @returns {boolean}
 */
export function hasRole(roles) {
  const userStore = useUserStore()
  const userRole = userStore.userInfo?.role
  
  if (!userRole) return false
  
  if (Array.isArray(roles)) {
    return roles.includes(userRole)
  }
  
  return userRole === roles
}

/**
 * 检查用户是否有指定权限
 * @param {string|Array} permissions - 权限名称或权限数组
 * @returns {boolean}
 */
export function hasPermission(permissions) {
  const userStore = useUserStore()
  const userPermissions = userStore.userInfo?.permissions || []
  
  if (Array.isArray(permissions)) {
    return permissions.some(permission => userPermissions.includes(permission))
  }
  
  return userPermissions.includes(permissions)
}

/**
 * 检查用户是否为管理员
 * @returns {boolean}
 */
export function isAdmin() {
  return hasRole('admin')
}

/**
 * 检查用户是否为分销员
 * @returns {boolean}
 */
export function isDistributor() {
  return hasRole('distributor')
}

/**
 * 检查用户是否为群主
 * @returns {boolean}
 */
export function isGroupOwner() {
  return hasRole('group_owner')
}

/**
 * 检查用户是否为分站管理员
 * @returns {boolean}
 */
export function isSubstationAdmin() {
  return hasRole('substation')
}

/**
 * 检查用户是否为代理商
 * @returns {boolean}
 */
export function isAgent() {
  return hasRole('agent')
}

/**
 * 路由权限检查
 * @param {Object} route - 路由对象
 * @param {string} userRole - 用户角色
 * @returns {boolean}
 */
export function checkRoutePermission(route, userRole = null) {
  const userStore = useUserStore()
  const role = userRole || userStore.userInfo?.role
  
  if (!role) return false
  
  const { meta } = route
  
  // 如果路由没有权限要求，默认允许访问
  if (!meta) return true
  
  // 检查角色权限
  if (meta.roles && meta.roles.length > 0) {
    if (!meta.roles.includes(role)) {
      return false
    }
  }
  
  // 检查具体权限
  if (meta.permissions && meta.permissions.length > 0) {
    if (!hasPermission(meta.permissions)) {
      return false
    }
  }
  
  // 使用导航配置检查权限
  return checkMenuPermission(route, role)
}

/**
 * 获取用户可访问的路由
 * @param {Array} routes - 路由数组
 * @param {string} userRole - 用户角色
 * @returns {Array}
 */
export function getAccessibleRoutes(routes, userRole = null) {
  const userStore = useUserStore()
  const role = userRole || userStore.userInfo?.role
  
  if (!role || !routes) return []
  
  return routes.filter(route => {
    return checkRoutePermission(route, role)
  })
}

/**
 * 重定向到用户默认页面
 * @param {Object} router - 路由器实例
 * @param {string} userRole - 用户角色
 */
export function redirectToDefaultRoute(router, userRole = null) {
  const userStore = useUserStore()
  const role = userRole || userStore.userInfo?.role
  
  if (!role) {
    router.push('/login')
    return
  }
  
  const defaultRoute = getUserDefaultRoute(role)
  router.push(defaultRoute)
}

/**
 * 检查当前路由是否需要权限
 * @param {Object} route - 当前路由
 * @returns {boolean}
 */
export function routeRequiresAuth(route) {
  return route.meta?.requiresAuth !== false
}

/**
 * 检查是否为公开路由
 * @param {string} path - 路由路径
 * @returns {boolean}
 */
export function isPublicRoute(path) {
  const publicRoutes = ['/login', '/register', '/404', '/403']
  return publicRoutes.includes(path)
}

/**
 * 权限指令 - 用于模板中的权限控制
 * @param {string|Array} roles - 需要的角色
 * @returns {boolean}
 */
export function vPermission(roles) {
  return hasRole(roles)
}

/**
 * 权限指令 - 检查具体权限
 * @param {string|Array} permissions - 需要的权限
 * @returns {boolean}
 */
export function vAuth(permissions) {
  return hasPermission(permissions)
}