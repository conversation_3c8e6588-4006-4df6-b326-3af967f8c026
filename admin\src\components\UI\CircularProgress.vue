<template>
  <div class="circular-progress" :style="{ width: size + 'px', height: size + 'px' }">
    <svg :width="size" :height="size" class="progress-svg">
      <!-- 背景圆环 -->
      <circle
        :cx="center"
        :cy="center"
        :r="radius"
        :stroke="trackColor"
        :stroke-width="strokeWidth"
        fill="none"
        class="progress-track"
      />
      <!-- 进度圆环 -->
      <circle
        :cx="center"
        :cy="center"
        :r="radius"
        :stroke="strokeColor"
        :stroke-width="strokeWidth"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="strokeDashoffset"
        fill="none"
        class="progress-bar"
        :class="{ 'progress-animation': animated }"
      />
    </svg>
    
    <!-- 中心内容 -->
    <div class="progress-content">
      <slot>
        <div class="progress-text">
          <div class="progress-percentage">{{ percentage }}%</div>
          <div v-if="label" class="progress-label">{{ label }}</div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  percentage: {
    type: Number,
    default: 0,
    validator: (val) => val >= 0 && val <= 100
  },
  size: {
    type: Number,
    default: 120
  },
  strokeWidth: {
    type: Number,
    default: 8
  },
  strokeColor: {
    type: String,
    default: '#409EFF'
  },
  trackColor: {
    type: String,
    default: '#e5e9f2'
  },
  label: {
    type: String,
    default: ''
  },
  animated: {
    type: Boolean,
    default: true
  }
})

const center = computed(() => props.size / 2)
const radius = computed(() => (props.size - props.strokeWidth) / 2)
const circumference = computed(() => 2 * Math.PI * radius.value)
const strokeDashoffset = computed(() => {
  return circumference.value - (props.percentage / 100) * circumference.value
})
</script>

<style scoped>
.circular-progress {
  position: relative;
  display: inline-block;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-track {
  opacity: 0.3;
}

.progress-bar {
  transition: stroke-dashoffset 0.6s ease;
  stroke-linecap: round;
}

.progress-bar.progress-animation {
  animation: progress-appear 0.6s ease;
}

.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-text {
  color: #333;
}

.progress-percentage {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.progress-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

@keyframes progress-appear {
  0% {
    stroke-dashoffset: v-bind(circumference + 'px');
  }
  100% {
    stroke-dashoffset: v-bind(strokeDashoffset + 'px');
  }
}
</style>