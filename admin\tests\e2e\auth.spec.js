/**
 * Authentication E2E Tests
 * 测试认证相关的端到端流程
 */

import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // 清除存储，确保测试环境干净
    await page.context().clearCookies()
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  test.describe('Login Page', () => {
    test('should display login page correctly', async ({ page }) => {
      await page.goto('/login')
      
      // 检查页面标题
      await expect(page).toHaveTitle(/晨鑫流量变现系统/)
      
      // 检查主要元素
      await expect(page.locator('.login-card')).toBeVisible()
      await expect(page.locator('h1')).toContainText('晨鑫流量变现系统')
      await expect(page.locator('.welcome-section h2')).toContainText('管理员登录')
      
      // 检查表单元素
      await expect(page.locator('#username')).toBeVisible()
      await expect(page.locator('#password')).toBeVisible()
      await expect(page.locator('.modern-login-button')).toBeVisible()
      await expect(page.locator('.preview-button')).toBeVisible()
      
      // 检查状态指示器
      await expect(page.locator('.status-indicator')).toContainText('系统运行正常')
      await expect(page.locator('.status-dot')).toBeVisible()
    })

    test('should show password toggle functionality', async ({ page }) => {
      await page.goto('/login')
      
      const passwordInput = page.locator('#password')
      const toggleButton = page.locator('.password-toggle')
      
      // 初始状态应该是password类型
      await expect(passwordInput).toHaveAttribute('type', 'password')
      
      // 点击切换按钮
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'text')
      
      // 再次点击切换回password
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'password')
    })

    test('should handle form validation', async ({ page }) => {
      await page.goto('/login')
      
      // 尝试提交空表单
      await page.locator('.modern-login-button').click()
      
      // HTML5验证会阻止提交
      const usernameInput = page.locator('#username')
      await expect(usernameInput).toHaveAttribute('required')
      
      const passwordInput = page.locator('#password')
      await expect(passwordInput).toHaveAttribute('required')
    })

    test('should handle keyboard navigation', async ({ page }) => {
      await page.goto('/login')
      
      // Tab键导航
      await page.keyboard.press('Tab')
      await expect(page.locator('#username')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.locator('#password')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.locator('#remember')).toBeFocused()
    })
  })

  test.describe('Login Process', () => {
    test('should login successfully with valid credentials', async ({ page }) => {
      await page.goto('/login')
      
      // 填写登录信息
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟API响应
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              token: 'mock-jwt-token',
              user: {
                id: 1,
                username: 'admin',
                nickname: '超级管理员',
                role: 'admin',
                roles: ['admin'],
                permissions: ['*']
              }
            }
          })
        })
      })
      
      // 提交表单
      await page.click('.modern-login-button')
      
      // 等待跳转到仪表板
      await expect(page).toHaveURL('/dashboard')
      
      // 验证登录成功后的状态
      await expect(page.locator('.user-info')).toBeVisible()
    })

    test('should show error message for invalid credentials', async ({ page }) => {
      await page.goto('/login')
      
      await page.fill('#username', 'wronguser')
      await page.fill('#password', 'wrongpass')
      
      // 模拟登录失败响应
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        })
      })
      
      await page.click('.modern-login-button')
      
      // 验证错误消息显示
      await expect(page.locator('.el-message--error')).toContainText('用户名或密码错误')
      
      // 验证仍在登录页
      await expect(page).toHaveURL('/login')
    })

    test('should handle network errors gracefully', async ({ page }) => {
      await page.goto('/login')
      
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟网络错误
      await page.route('/api/admin/auth/login', (route) => {
        route.abort('failed')
      })
      
      await page.click('.modern-login-button')
      
      // 验证网络错误消息
      await expect(page.locator('.el-message--error')).toContainText('网络连接失败')
    })

    test('should disable form during login process', async ({ page }) => {
      await page.goto('/login')
      
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟延迟响应
      await page.route('/api/admin/auth/login', async (route) => {
        await page.waitForTimeout(2000)
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              token: 'mock-token',
              user: { id: 1, username: 'admin', role: 'admin' }
            }
          })
        })
      })
      
      const loginButton = page.locator('.modern-login-button')
      await loginButton.click()
      
      // 验证加载状态
      await expect(loginButton).toBeDisabled()
      await expect(loginButton).toContainText('登录中...')
      await expect(page.locator('.loading-spinner')).toBeVisible()
      
      // 等待登录完成
      await page.waitForURL('/dashboard')
    })
  })

  test.describe('Preview Mode', () => {
    test('should enter preview mode successfully', async ({ page }) => {
      await page.goto('/login')
      
      const previewButton = page.locator('.preview-button')
      await expect(previewButton).toContainText('进入预览模式')
      
      await previewButton.click()
      
      // 验证预览模式消息
      await expect(page.locator('.el-message--info')).toContainText('正在进入预览模式')
      
      // 等待跳转
      await page.waitForTimeout(600)
      await expect(page.locator('.el-message--success')).toContainText('欢迎来到预览模式')
      
      // 验证跳转到仪表板
      await expect(page).toHaveURL('/dashboard')
    })

    test('should show preview mode indicator in dashboard', async ({ page }) => {
      await page.goto('/login')
      
      await page.click('.preview-button')
      await page.waitForURL('/dashboard')
      
      // 验证预览模式指示器
      await expect(page.locator('.preview-mode-indicator')).toContainText('预览模式')
    })
  })

  test.describe('Remember Me Function', () => {
    test('should remember login state', async ({ page }) => {
      await page.goto('/login')
      
      // 勾选记住我
      await page.check('#remember')
      await expect(page.locator('#remember')).toBeChecked()
      
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟登录成功
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              token: 'remember-token',
              user: { id: 1, username: 'admin', role: 'admin' }
            }
          })
        })
      })
      
      await page.click('.modern-login-button')
      await page.waitForURL('/dashboard')
      
      // 验证token被保存（通过检查cookie或localStorage）
      const cookies = await page.context().cookies()
      const adminToken = cookies.find(cookie => cookie.name === 'Admin-Token')
      expect(adminToken).toBeTruthy()
    })
  })

  test.describe('Responsive Design', () => {
    test('should display correctly on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE尺寸
      await page.goto('/login')
      
      // 验证移动端布局
      await expect(page.locator('.login-card')).toBeVisible()
      await expect(page.locator('.logo-section')).toHaveCSS('flex-direction', 'column')
      
      // 验证输入框在移动端的可用性
      await page.fill('#username', 'testuser')
      await page.fill('#password', 'testpass')
      
      const usernameInput = page.locator('#username')
      await expect(usernameInput).toHaveValue('testuser')
    })

    test('should display correctly on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }) // iPad尺寸
      await page.goto('/login')
      
      await expect(page.locator('.login-card')).toBeVisible()
      await expect(page.locator('.login-form')).toBeVisible()
    })

    test('should handle different screen orientations', async ({ page }) => {
      // 横屏模式
      await page.setViewportSize({ width: 812, height: 375 })
      await page.goto('/login')
      
      await expect(page.locator('.login-card')).toBeVisible()
      
      // 验证在小高度下的布局调整
      const loginCard = page.locator('.login-card')
      const cardHeight = await loginCard.evaluate(el => el.offsetHeight)
      const viewportHeight = 375
      
      expect(cardHeight).toBeLessThan(viewportHeight)
    })
  })

  test.describe('Security Features', () => {
    test('should prevent XSS attacks in input fields', async ({ page }) => {
      await page.goto('/login')
      
      const xssPayload = '<script>alert("XSS")</script>'
      
      await page.fill('#username', xssPayload)
      await page.fill('#password', 'password123')
      
      // 验证脚本不会执行
      let alertFired = false
      page.on('dialog', () => {
        alertFired = true
      })
      
      await page.click('.modern-login-button')
      
      expect(alertFired).toBe(false)
    })

    test('should handle CSRF protection', async ({ page }) => {
      await page.goto('/login')
      
      // 检查是否存在CSRF token
      const csrfToken = await page.evaluate(() => {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
      })
      
      // 如果有CSRF保护，token应该存在
      if (csrfToken) {
        expect(csrfToken).toBeTruthy()
        expect(csrfToken.length).toBeGreaterThan(0)
      }
    })

    test('should handle rate limiting', async ({ page }) => {
      await page.goto('/login')
      
      // 模拟多次快速登录尝试
      for (let i = 0; i < 5; i++) {
        await page.fill('#username', `user${i}`)
        await page.fill('#password', 'wrongpass')
        
        await page.route('/api/admin/auth/login', (route) => {
          if (i < 3) {
            route.fulfill({
              status: 401,
              contentType: 'application/json',
              body: JSON.stringify({
                success: false,
                message: '用户名或密码错误'
              })
            })
          } else {
            route.fulfill({
              status: 429,
              contentType: 'application/json',
              body: JSON.stringify({
                success: false,
                message: '登录尝试过于频繁，请稍后再试'
              })
            })
          }
        })
        
        await page.click('.modern-login-button')
        
        if (i >= 3) {
          await expect(page.locator('.el-message--error')).toContainText('登录尝试过于频繁')
          break
        }
      }
    })
  })

  test.describe('Accessibility', () => {
    test('should be accessible via keyboard navigation', async ({ page }) => {
      await page.goto('/login')
      
      // 使用Tab键导航整个表单
      await page.keyboard.press('Tab') // username
      await expect(page.locator('#username')).toBeFocused()
      
      await page.keyboard.press('Tab') // password
      await expect(page.locator('#password')).toBeFocused()
      
      await page.keyboard.press('Tab') // remember checkbox
      await expect(page.locator('#remember')).toBeFocused()
      
      await page.keyboard.press('Tab') // forgot password link
      await expect(page.locator('.forgot-password')).toBeFocused()
      
      await page.keyboard.press('Tab') // login button
      await expect(page.locator('.modern-login-button')).toBeFocused()
    })

    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto('/login')
      
      // 检查表单标签
      const usernameLabel = page.locator('label[for="username"]')
      await expect(usernameLabel).toContainText('用户名或邮箱')
      
      const passwordLabel = page.locator('label[for="password"]')
      await expect(passwordLabel).toContainText('密码')
      
      // 检查按钮的可访问性
      const loginButton = page.locator('.modern-login-button')
      await expect(loginButton).toHaveAttribute('type', 'submit')
    })

    test('should support screen readers', async ({ page }) => {
      await page.goto('/login')
      
      // 检查页面结构的语义化
      await expect(page.locator('h1')).toBeVisible()
      await expect(page.locator('h2')).toBeVisible()
      
      // 检查表单结构
      await expect(page.locator('form')).toBeVisible()
      await expect(page.locator('form label')).toHaveCount(2) // username和password标签
    })

    test('should have sufficient color contrast', async ({ page }) => {
      await page.goto('/login')
      
      // 这个测试需要专门的工具来检查对比度
      // 这里我们检查重要元素是否可见
      await expect(page.locator('.login-card')).toBeVisible()
      await expect(page.locator('.modern-login-button')).toBeVisible()
      await expect(page.locator('.form-label')).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle server errors gracefully', async ({ page }) => {
      await page.goto('/login')
      
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟服务器错误
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: 'Internal Server Error'
          })
        })
      })
      
      await page.click('.modern-login-button')
      
      await expect(page.locator('.el-message--error')).toContainText('服务器内部错误')
    })

    test('should handle timeout errors', async ({ page }) => {
      await page.goto('/login')
      
      await page.fill('#username', 'admin')
      await page.fill('#password', 'password123')
      
      // 模拟请求超时
      await page.route('/api/admin/auth/login', (route) => {
        // 不响应，让请求超时
      })
      
      await page.click('.modern-login-button')
      
      // 等待超时错误消息
      await expect(page.locator('.el-message--error')).toContainText('请求超时', { timeout: 10000 })
    })

    test('should recover from errors', async ({ page }) => {
      await page.goto('/login')
      
      // 先失败一次
      await page.fill('#username', 'admin')
      await page.fill('#password', 'wrongpass')
      
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        })
      })
      
      await page.click('.modern-login-button')
      await expect(page.locator('.el-message--error')).toContainText('用户名或密码错误')
      
      // 然后成功登录
      await page.fill('#password', 'correctpass')
      
      await page.route('/api/admin/auth/login', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              token: 'success-token',
              user: { id: 1, username: 'admin', role: 'admin' }
            }
          })
        })
      })
      
      await page.click('.modern-login-button')
      await expect(page).toHaveURL('/dashboard')
    })
  })

  test.describe('Performance', () => {
    test('should load quickly', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/login')
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // 页面应该在3秒内加载完成
      expect(loadTime).toBeLessThan(3000)
    })

    test('should be responsive to user interactions', async ({ page }) => {
      await page.goto('/login')
      
      const startTime = Date.now()
      
      await page.fill('#username', 'testuser')
      await page.fill('#password', 'testpass')
      
      const interactionTime = Date.now() - startTime
      
      // 输入响应时间应该很快
      expect(interactionTime).toBeLessThan(500)
    })
  })
})