import{O as e,P as t}from"./element-plus-h2SQQM64.js";function n(e,t){return function(){return e.apply(t,arguments)}}const{toString:r}=Object.prototype,{getPrototypeOf:o}=Object,{iterator:s,toStringTag:i}=Symbol,a=(c=Object.create(null),e=>{const t=r.call(e);return c[t]||(c[t]=t.slice(8,-1).toLowerCase())});var c;const u=e=>(e=e.toLowerCase(),t=>a(t)===e),l=e=>t=>typeof t===e,{isArray:f}=Array,d=l("undefined");function p(e){return null!==e&&!d(e)&&null!==e.constructor&&!d(e.constructor)&&y(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const h=u("ArrayBuffer");const m=l("string"),y=l("function"),b=l("number"),g=e=>null!==e&&"object"==typeof e,w=e=>{if("object"!==a(e))return!1;const t=o(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||i in e||s in e)},E=u("Date"),O=u("File"),S=u("Blob"),R=u("FileList"),v=u("URLSearchParams"),[T,A,C,x]=["ReadableStream","Request","Response","Headers"].map(u);function j(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),f(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{if(p(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function N(e,t){if(p(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,P=e=>!d(e)&&e!==U;const k=(F="undefined"!=typeof Uint8Array&&o(Uint8Array),e=>F&&e instanceof F);var F;const B=u("HTMLFormElement"),_=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),L=u("RegExp"),D=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};j(n,(n,o)=>{let s;!1!==(s=t(n,o,e))&&(r[o]=s||n)}),Object.defineProperties(e,r)};const q=u("AsyncFunction"),I=(M="function"==typeof setImmediate,z=y(U.postMessage),M?setImmediate:z?(H=`axios@${Math.random()}`,W=[],U.addEventListener("message",({source:e,data:t})=>{e===U&&t===H&&W.length&&W.shift()()},!1),e=>{W.push(e),U.postMessage(H,"*")}):e=>setTimeout(e));var M,z,H,W;const J="undefined"!=typeof queueMicrotask?queueMicrotask.bind(U):"undefined"!=typeof process&&process.nextTick||I,K={isArray:f,isArrayBuffer:h,isBuffer:p,isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||y(e.append)&&("formdata"===(t=a(e))||"object"===t&&y(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&h(e.buffer),t},isString:m,isNumber:b,isBoolean:e=>!0===e||!1===e,isObject:g,isPlainObject:w,isEmptyObject:e=>{if(!g(e)||p(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(t){return!1}},isReadableStream:T,isRequest:A,isResponse:C,isHeaders:x,isUndefined:d,isDate:E,isFile:O,isBlob:S,isRegExp:L,isFunction:y,isStream:e=>g(e)&&y(e.pipe),isURLSearchParams:v,isTypedArray:k,isFileList:R,forEach:j,merge:function e(){const{caseless:t}=P(this)&&this||{},n={},r=(r,o)=>{const s=t&&N(n,o)||o;w(n[s])&&w(r)?n[s]=e(n[s],r):w(r)?n[s]=e({},r):f(r)?n[s]=r.slice():n[s]=r};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&j(arguments[o],r);return n},extend:(e,t,r,{allOwnKeys:o}={})=>(j(t,(t,o)=>{r&&y(t)?e[o]=n(t,r):e[o]=t},{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let s,i,a;const c={};if(t=t||{},null==e)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)a=s[i],r&&!r(a,e,t)||c[a]||(t[a]=e[a],c[a]=!0);e=!1!==n&&o(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:u,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(f(e))return e;let t=e.length;if(!b(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[s]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:B,hasOwnProperty:_,hasOwnProp:_,reduceDescriptors:D,freezeMethods:e=>{D(e,(t,n)=>{if(y(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];y(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return f(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:N,global:U,isContextDefined:P,isSpecCompliantForm:function(e){return!!(e&&y(e.append)&&"FormData"===e[i]&&e[s])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(g(e)){if(t.indexOf(e)>=0)return;if(p(e))return e;if(!("toJSON"in e)){t[r]=e;const o=f(e)?[]:{};return j(e,(e,t)=>{const s=n(e,r+1);!d(s)&&(o[t]=s)}),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:q,isThenable:e=>e&&(g(e)||y(e))&&y(e.then)&&y(e.catch),setImmediate:I,asap:J,isIterable:e=>null!=e&&y(e[s])};function V(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}K.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:K.toJSONObject(this.config),code:this.code,status:this.status}}});const $=V.prototype,X={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{X[e]={value:e}}),Object.defineProperties(V,X),Object.defineProperty($,"isAxiosError",{value:!0}),V.from=(e,t,n,r,o,s)=>{const i=Object.create($);return K.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),V.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function G(e){return K.isPlainObject(e)||K.isArray(e)}function Q(e){return K.endsWith(e,"[]")?e.slice(0,-2):e}function Z(e,t,n){return e?e.concat(t).map(function(e,t){return e=Q(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Y=K.toFlatObject(K,{},null,function(e){return/^is[A-Z]/.test(e)});function ee(e,t,n){if(!K.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=K.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!K.isUndefined(t[e])})).metaTokens,o=n.visitor||u,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&K.isSpecCompliantForm(t);if(!K.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(K.isDate(e))return e.toISOString();if(K.isBoolean(e))return e.toString();if(!a&&K.isBlob(e))throw new V("Blob is not supported. Use a Buffer instead.");return K.isArrayBuffer(e)||K.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(K.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(K.isArray(e)&&function(e){return K.isArray(e)&&!e.some(G)}(e)||(K.isFileList(e)||K.endsWith(n,"[]"))&&(a=K.toArray(e)))return n=Q(n),a.forEach(function(e,r){!K.isUndefined(e)&&null!==e&&t.append(!0===i?Z([n],r,s):null===i?n:n+"[]",c(e))}),!1;return!!G(e)||(t.append(Z(o,n,s),c(e)),!1)}const l=[],f=Object.assign(Y,{defaultVisitor:u,convertValue:c,isVisitable:G});if(!K.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!K.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+r.join("."));l.push(n),K.forEach(n,function(n,s){!0===(!(K.isUndefined(n)||null===n)&&o.call(t,n,K.isString(s)?s.trim():s,r,f))&&e(n,r?r.concat(s):[s])}),l.pop()}}(e),t}function te(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ne(e,t){this._pairs=[],e&&ee(e,this,t)}const re=ne.prototype;function oe(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function se(e,t,n){if(!t)return e;const r=n&&n.encode||oe;K.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(s=o?o(t,n):K.isURLSearchParams(t)?t.toString():new ne(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}re.append=function(e,t){this._pairs.push([e,t])},re.toString=function(e){const t=e?function(t){return e.call(this,t,te)}:te;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class ie{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){K.forEach(this.handlers,function(t){null!==t&&e(t)})}}const ae={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ce={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ne,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ue="undefined"!=typeof window&&"undefined"!=typeof document,le="object"==typeof navigator&&navigator||void 0,fe=ue&&(!le||["ReactNative","NativeScript","NS"].indexOf(le.product)<0),de="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,pe=ue&&window.location.href||"http://localhost",he={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ue,hasStandardBrowserEnv:fe,hasStandardBrowserWebWorkerEnv:de,navigator:le,origin:pe},Symbol.toStringTag,{value:"Module"})),...ce};function me(e){function t(e,n,r,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&K.isArray(r)?r.length:s,a)return K.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&K.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],o)&&K.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}(r[s])),!i}if(K.isFormData(e)&&K.isFunction(e.entries)){const n={};return K.forEachEntry(e,(e,r)=>{t(function(e){return K.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null}const ye={transitional:ae,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=K.isObject(e);o&&K.isHTMLForm(e)&&(e=new FormData(e));if(K.isFormData(e))return r?JSON.stringify(me(e)):e;if(K.isArrayBuffer(e)||K.isBuffer(e)||K.isStream(e)||K.isFile(e)||K.isBlob(e)||K.isReadableStream(e))return e;if(K.isArrayBufferView(e))return e.buffer;if(K.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ee(e,new he.classes.URLSearchParams,{visitor:function(e,t,n,r){return he.isNode&&K.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)},...t})}(e,this.formSerializer).toString();if((s=K.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ee(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(K.isString(e))try{return(t||JSON.parse)(e),K.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ye.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(K.isResponse(e)||K.isReadableStream(e))return e;if(e&&K.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw V.from(o,V.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:he.classes.FormData,Blob:he.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};K.forEach(["delete","get","head","post","put","patch"],e=>{ye.headers[e]={}});const be=K.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ge=Symbol("internals");function we(e){return e&&String(e).trim().toLowerCase()}function Ee(e){return!1===e||null==e?e:K.isArray(e)?e.map(Ee):String(e)}function Oe(e,t,n,r,o){return K.isFunction(r)?r.call(this,t,n):(o&&(t=n),K.isString(t)?K.isString(r)?-1!==t.indexOf(r):K.isRegExp(r)?r.test(t):void 0:void 0)}let Se=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=we(t);if(!o)throw new Error("header name must be a non-empty string");const s=K.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=Ee(e))}const s=(e,t)=>K.forEach(e,(e,n)=>o(e,n,t));if(K.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(K.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach(function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&be[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(K.isObject(e)&&K.isIterable(e)){let n,r,o={};for(const t of e){if(!K.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?K.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=we(e)){const n=K.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(K.isFunction(t))return t.call(this,e,n);if(K.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=we(e)){const n=K.findKey(this,e);return!(!n||void 0===this[n]||t&&!Oe(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=we(e)){const o=K.findKey(n,e);!o||t&&!Oe(0,n[o],o,t)||(delete n[o],r=!0)}}return K.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Oe(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return K.forEach(this,(r,o)=>{const s=K.findKey(n,o);if(s)return t[s]=Ee(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(o):String(o).trim();i!==o&&delete t[o],t[i]=Ee(r),n[i]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return K.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&K.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){const t=(this[ge]=this[ge]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=we(e);t[r]||(!function(e,t){const n=K.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})})}(n,e),t[r]=!0)}return K.isArray(e)?e.forEach(r):r(e),this}};function Re(e,t){const n=this||ye,r=t||n,o=Se.from(r.headers);let s=r.data;return K.forEach(e,function(e){s=e.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function ve(e){return!(!e||!e.__CANCEL__)}function Te(e,t,n){V.call(this,null==e?"canceled":e,V.ERR_CANCELED,t,n),this.name="CanceledError"}function Ae(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new V("Request failed with status code "+n.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}Se.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),K.reduceDescriptors(Se.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),K.freezeMethods(Se),K.inherits(Te,V,{__CANCEL__:!0});const Ce=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),u=r[i];o||(o=c),n[s]=a,r[s]=c;let l=i,f=0;for(;l!==s;)f+=n[l++],l%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const d=u&&c-u;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(e,t){let n,r,o=0,s=1e3/t;const i=(t,s=Date.now())=>{o=s,n=null,r&&(clearTimeout(r),r=null),e(...t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=s?i(e,t):(n=e,r||(r=setTimeout(()=>{r=null,i(n)},s-a)))},()=>n&&i(n)]}(n=>{const s=n.loaded,i=n.lengthComputable?n.total:void 0,a=s-r,c=o(a);r=s;e({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},xe=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},je=e=>(...t)=>K.asap(()=>e(...t)),Ne=he.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,he.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(he.origin),he.navigator&&/(msie|trident)/i.test(he.navigator.userAgent)):()=>!0,Ue=he.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];K.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),K.isString(r)&&i.push("path="+r),K.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Pe(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const ke=e=>e instanceof Se?{...e}:e;function Fe(e,t){t=t||{};const n={};function r(e,t,n,r){return K.isPlainObject(e)&&K.isPlainObject(t)?K.merge.call({caseless:r},e,t):K.isPlainObject(t)?K.merge({},t):K.isArray(t)?t.slice():t}function o(e,t,n,o){return K.isUndefined(t)?K.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function s(e,t){if(!K.isUndefined(t))return r(void 0,t)}function i(e,t){return K.isUndefined(t)?K.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,s){return s in t?r(n,o):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t,n)=>o(ke(e),ke(t),0,!0)};return K.forEach(Object.keys({...e,...t}),function(r){const s=c[r]||o,i=s(e[r],t[r],r);K.isUndefined(i)&&s!==a||(n[r]=i)}),n}const Be=e=>{const t=Fe({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:c}=t;if(t.headers=a=Se.from(a),t.url=se(Pe(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),K.isFormData(r))if(he.hasStandardBrowserEnv||he.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(he.hasStandardBrowserEnv&&(o&&K.isFunction(o)&&(o=o(t)),o||!1!==o&&Ne(t.url))){const e=s&&i&&Ue.read(i);e&&a.set(s,e)}return t},_e="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Be(e);let o=r.data;const s=Se.from(r.headers).normalize();let i,a,c,u,l,{responseType:f,onUploadProgress:d,onDownloadProgress:p}=r;function h(){u&&u(),l&&l(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function y(){if(!m)return;const r=Se.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Ae(function(e){t(e),h()},function(e){n(e),h()},{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(n(new V("Request aborted",V.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new V("Network Error",V.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||ae;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new V(t,o.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,m)),m=null},void 0===o&&s.setContentType(null),"setRequestHeader"in m&&K.forEach(s.toJSON(),function(e,t){m.setRequestHeader(t,e)}),K.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),f&&"json"!==f&&(m.responseType=r.responseType),p&&([c,l]=Ce(p,!0),m.addEventListener("progress",c)),d&&m.upload&&([a,u]=Ce(d),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Te(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const b=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);b&&-1===he.protocols.indexOf(b)?n(new V("Unsupported protocol "+b+":",V.ERR_BAD_REQUEST,e)):m.send(o||null)})},Le=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof V?t:new Te(t instanceof Error?t.message:t))}};let s=t&&setTimeout(()=>{s=null,o(new V(`timeout ${t} of ms exceeded`,V.ETIMEDOUT))},t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>K.asap(i),a}},De=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},qe=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Ie=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of qe(e))yield*De(n,t)}(e,t);let s,i=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=i+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},Me="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,ze=Me&&"function"==typeof ReadableStream,He=Me&&("function"==typeof TextEncoder?(We=new TextEncoder,e=>We.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var We;const Je=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},Ke=ze&&Je(()=>{let e=!1;const t=new Request(he.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ve=ze&&Je(()=>K.isReadableStream(new Response("").body)),$e={stream:Ve&&(e=>e.body)};var Xe;Me&&(Xe=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!$e[e]&&($e[e]=K.isFunction(Xe[e])?t=>t[e]():(t,n)=>{throw new V(`Response type '${e}' is not supported`,V.ERR_NOT_SUPPORT,n)})}));const Ge=async(e,t)=>{const n=K.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(K.isBlob(e))return e.size;if(K.isSpecCompliantForm(e)){const t=new Request(he.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return K.isArrayBufferView(e)||K.isArrayBuffer(e)?e.byteLength:(K.isURLSearchParams(e)&&(e+=""),K.isString(e)?(await He(e)).byteLength:void 0)})(t):n},Qe={http:null,xhr:_e,fetch:Me&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:u,headers:l,withCredentials:f="same-origin",fetchOptions:d}=Be(e);u=u?(u+"").toLowerCase():"text";let p,h=Le([o,s&&s.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let y;try{if(c&&Ke&&"get"!==n&&"head"!==n&&0!==(y=await Ge(l,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(K.isFormData(r)&&(e=n.headers.get("content-type"))&&l.setContentType(e),n.body){const[e,t]=xe(y,Ce(je(c)));r=Ie(n.body,65536,e,t)}}K.isString(f)||(f=f?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,{...d,signal:h,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:o?f:void 0});let s=await fetch(p,d);const i=Ve&&("stream"===u||"response"===u);if(Ve&&(a||i&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});const t=K.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&xe(t,Ce(je(a),!0))||[];s=new Response(Ie(s.body,65536,n,()=>{r&&r(),m&&m()}),e)}u=u||"text";let b=await $e[K.findKey($e,u)||"text"](s,e);return!i&&m&&m(),await new Promise((t,n)=>{Ae(t,n,{data:b,headers:Se.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:p})})}catch(b){if(m&&m(),b&&"TypeError"===b.name&&/Load failed|fetch/i.test(b.message))throw Object.assign(new V("Network Error",V.ERR_NETWORK,e,p),{cause:b.cause||b});throw V.from(b,b&&b.code,e,p)}})};K.forEach(Qe,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const Ze=e=>`- ${e}`,Ye=e=>K.isFunction(e)||null===e||!1===e,et=e=>{e=K.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!Ye(n)&&(r=Qe[(t=String(n)).toLowerCase()],void 0===r))throw new V(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+s]=r}if(!r){const e=Object.entries(o).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new V("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Ze).join("\n"):" "+Ze(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function tt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Te(null,e)}function nt(e){tt(e),e.headers=Se.from(e.headers),e.data=Re.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return et(e.adapter||ye.adapter)(e).then(function(t){return tt(e),t.data=Re.call(e,e.transformResponse,t),t.headers=Se.from(t.headers),t},function(t){return ve(t)||(tt(e),t&&t.response&&(t.response.data=Re.call(e,e.transformResponse,t.response),t.response.headers=Se.from(t.response.headers))),Promise.reject(t)})}const rt="1.11.0",ot={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ot[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const st={};ot.transitional=function(e,t,n){function r(e,t){return"[Axios v"+rt+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,s)=>{if(!1===e)throw new V(r(o," has been removed"+(t?" in "+t:"")),V.ERR_DEPRECATED);return t&&!st[o]&&(st[o]=!0,r(o," has been deprecated since v"+t+" and will be removed in the near future")),!e||e(n,o,s)}},ot.spelling=function(e){return(e,t)=>!0};const it={assertOptions:function(e,t,n){if("object"!=typeof e)throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const t=e[s],n=void 0===t||i(t,s,e);if(!0!==n)throw new V("option "+s+" must be "+n,V.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new V("Unknown option "+s,V.ERR_BAD_OPTION)}},validators:ot},at=it.validators;let ct=class{constructor(e){this.defaults=e||{},this.interceptors={request:new ie,response:new ie}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Fe(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&it.assertOptions(n,{silentJSONParsing:at.transitional(at.boolean),forcedJSONParsing:at.transitional(at.boolean),clarifyTimeoutError:at.transitional(at.boolean)},!1),null!=r&&(K.isFunction(r)?t.paramsSerializer={serialize:r}:it.assertOptions(r,{encode:at.function,serialize:at.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),it.assertOptions(t,{baseUrl:at.spelling("baseURL"),withXsrfToken:at.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&K.merge(o.common,o[t.method]);o&&K.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=Se.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach(function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const c=[];let u;this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let l,f=0;if(!a){const e=[nt.bind(this),void 0];for(e.unshift(...i),e.push(...c),l=e.length,u=Promise.resolve(t);f<l;)u=u.then(e[f++],e[f++]);return u}l=i.length;let d=t;for(f=0;f<l;){const e=i[f++],t=i[f++];try{d=e(d)}catch(p){t.call(this,p);break}}try{u=nt.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,l=c.length;f<l;)u=u.then(c[f++],c[f++]);return u}getUri(e){return se(Pe((e=Fe(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};K.forEach(["delete","get","head","options"],function(e){ct.prototype[e]=function(t,n){return this.request(Fe(n||{},{method:e,url:t,data:(n||{}).data}))}}),K.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,o){return this.request(Fe(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ct.prototype[e]=t(),ct.prototype[e+"Form"]=t(!0)});const ut={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ut).forEach(([e,t])=>{ut[t]=e});const lt=function e(t){const r=new ct(t),o=n(ct.prototype.request,r);return K.extend(o,ct.prototype,r,{allOwnKeys:!0}),K.extend(o,r,null,{allOwnKeys:!0}),o.create=function(n){return e(Fe(t,n))},o}(ye);lt.Axios=ct,lt.CanceledError=Te,lt.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,o){n.reason||(n.reason=new Te(e,r,o),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e(function(e){t=e}),cancel:t}}},lt.isCancel=ve,lt.VERSION=rt,lt.toFormData=ee,lt.AxiosError=V,lt.Cancel=lt.CanceledError,lt.all=function(e){return Promise.all(e)},lt.spread=function(e){return function(t){return e.apply(null,t)}},lt.isAxiosError=function(e){return K.isObject(e)&&!0===e.isAxiosError},lt.mergeConfig=Fe,lt.AxiosHeaders=Se,lt.formToJSON=e=>me(K.isHTMLForm(e)?new FormData(e):e),lt.getAdapter=et,lt.HttpStatusCode=ut,lt.default=lt;const{Axios:ft,AxiosError:dt,CanceledError:pt,isCancel:ht,CancelToken:mt,VERSION:yt,all:bt,Cancel:gt,isAxiosError:wt,spread:Et,toFormData:Ot,AxiosHeaders:St,HttpStatusCode:Rt,formToJSON:vt,getAdapter:Tt,mergeConfig:At}=lt;function Ct(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var xt=function e(t,n){function r(e,r,o){if("undefined"!=typeof document){"number"==typeof(o=Ct({},n,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var s="";for(var i in o)o[i]&&(s+="; "+i,!0!==o[i]&&(s+="="+o[i].split(";")[0]));return document.cookie=e+"="+t.write(r,e)+s}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var s=n[o].split("="),i=s.slice(1).join("=");try{var a=decodeURIComponent(s[0]);if(r[a]=t.read(i,a),e===a)break}catch(c){}}return e?r[e]:r}},remove:function(e,t){r(e,"",Ct({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,Ct({},this.attributes,t))},withConverter:function(t){return e(Ct({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),jt={exports:{}};jt.exports=function(){var e,t,n={version:"0.2.0"},r=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function o(e,t,n){return e<t?t:e>n?n:e}function s(e){return 100*(-1+e)}function i(e,t,n){var o;return(o="translate3d"===r.positionUsing?{transform:"translate3d("+s(e)+"%,0,0)"}:"translate"===r.positionUsing?{transform:"translate("+s(e)+"%,0)"}:{"margin-left":s(e)+"%"}).transition="all "+t+"ms "+n,o}n.configure=function(e){var t,n;for(t in e)void 0!==(n=e[t])&&e.hasOwnProperty(t)&&(r[t]=n);return this},n.status=null,n.set=function(e){var t=n.isStarted();e=o(e,r.minimum,1),n.status=1===e?null:e;var s=n.render(!t),u=s.querySelector(r.barSelector),l=r.speed,f=r.easing;return s.offsetWidth,a(function(t){""===r.positionUsing&&(r.positionUsing=n.getPositioningCSS()),c(u,i(e,l,f)),1===e?(c(s,{transition:"none",opacity:1}),s.offsetWidth,setTimeout(function(){c(s,{transition:"all "+l+"ms linear",opacity:0}),setTimeout(function(){n.remove(),t()},l)},l)):setTimeout(t,l)}),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var e=function(){setTimeout(function(){n.status&&(n.trickle(),e())},r.trickleSpeed)};return r.trickle&&e(),this},n.done=function(e){return e||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(e){var t=n.status;return t?("number"!=typeof e&&(e=(1-t)*o(Math.random()*t,.1,.95)),t=o(t+e,0,.994),n.set(t)):n.start()},n.trickle=function(){return n.inc(Math.random()*r.trickleRate)},e=0,t=0,n.promise=function(r){return r&&"resolved"!==r.state()?(0===t&&n.start(),e++,t++,r.always(function(){0===--t?(e=0,n.done()):n.set((e-t)/e)}),this):this},n.render=function(e){if(n.isRendered())return document.getElementById("nprogress");l(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=r.template;var o,i=t.querySelector(r.barSelector),a=e?"-100":s(n.status||0),u=document.querySelector(r.parent);return c(i,{transition:"all 0 linear",transform:"translate3d("+a+"%,0,0)"}),r.showSpinner||(o=t.querySelector(r.spinnerSelector))&&p(o),u!=document.body&&l(u,"nprogress-custom-parent"),u.appendChild(t),t},n.remove=function(){f(document.documentElement,"nprogress-busy"),f(document.querySelector(r.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&p(e)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective"in e?"translate3d":t+"Transform"in e?"translate":"margin"};var a=function(){var e=[];function t(){var n=e.shift();n&&n(t)}return function(n){e.push(n),1==e.length&&t()}}(),c=function(){var e=["Webkit","O","Moz","ms"],t={};function n(e){return e.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})}function r(t){var n=document.body.style;if(t in n)return t;for(var r,o=e.length,s=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((r=e[o]+s)in n)return r;return t}function o(e){return e=n(e),t[e]||(t[e]=r(e))}function s(e,t,n){t=o(t),e.style[t]=n}return function(e,t){var n,r,o=arguments;if(2==o.length)for(n in t)void 0!==(r=t[n])&&t.hasOwnProperty(n)&&s(e,n,r);else s(e,o[1],o[2])}}();function u(e,t){return("string"==typeof e?e:d(e)).indexOf(" "+t+" ")>=0}function l(e,t){var n=d(e),r=n+t;u(n,t)||(e.className=r.substring(1))}function f(e,t){var n,r=d(e);u(e,t)&&(n=r.replace(" "+t+" "," "),e.className=n.substring(1,n.length-1))}function d(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function p(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return n}();const Nt=t(jt.exports);export{Nt as N,xt as a,lt as b};
