import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'

// 安全配置
const SECURITY_CONFIG = {
  // 会话配置
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30分钟
  MAX_IDLE_TIME: 15 * 60 * 1000,   // 15分钟无操作
  
  // 请求配置
  MAX_RETRY_ATTEMPTS: 3,
  REQUEST_TIMEOUT: 30000,
  
  // 安全头配置
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  },
  
  // 敏感数据字段
  SENSITIVE_FIELDS: [
    'password', 'token', 'secret', 'key', 'private',
    'credit_card', 'ssn', 'phone', 'email'
  ]
}

// 会话管理类
class SessionManager {
  constructor() {
    this.lastActivity = Date.now()
    this.sessionTimer = null
    this.idleTimer = null
    this.isActive = true
    
    this.initSessionMonitoring()
  }
  
  // 初始化会话监控
  initSessionMonitoring() {
    // 监听用户活动
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.addEventListener(event, this.updateActivity.bind(this), true)
    })
    
    // 启动会话检查
    this.startSessionCheck()
    this.startIdleCheck()
  }
  
  // 更新活动时间
  updateActivity() {
    this.lastActivity = Date.now()
    this.isActive = true
  }
  
  // 启动会话检查
  startSessionCheck() {
    this.sessionTimer = setInterval(() => {
      const token = localStorage.getItem('admin_token')
      if (!token) {
        this.handleSessionExpired()
        return
      }
      
      // 检查token是否过期
      try {
        const payload = JSON.parse(atob(token.split('.')[1]))
        if (payload.exp * 1000 < Date.now()) {
          this.handleSessionExpired()
        }
      } catch (error) {
        console.error('Token解析失败:', error)
        this.handleSessionExpired()
      }
    }, 60000) // 每分钟检查一次
  }
  
  // 启动空闲检查
  startIdleCheck() {
    this.idleTimer = setInterval(() => {
      const idleTime = Date.now() - this.lastActivity
      
      if (idleTime > SECURITY_CONFIG.MAX_IDLE_TIME && this.isActive) {
        this.handleIdleTimeout()
      }
    }, 30000) // 每30秒检查一次
  }
  
  // 处理会话过期
  handleSessionExpired() {
    this.cleanup()
    
    ElMessageBox.alert(
      '您的登录会话已过期，请重新登录',
      '会话过期',
      {
        confirmButtonText: '重新登录',
        type: 'warning',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false
      }
    ).then(() => {
      this.logout()
    })
  }
  
  // 处理空闲超时
  handleIdleTimeout() {
    this.isActive = false
    
    ElMessageBox.confirm(
      '检测到您已长时间未操作，是否继续保持登录？',
      '空闲提醒',
      {
        confirmButtonText: '继续使用',
        cancelButtonText: '退出登录',
        type: 'warning'
      }
    ).then(() => {
      this.updateActivity()
    }).catch(() => {
      this.logout()
    })
  }
  
  // 登出
  logout() {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    localStorage.removeItem('admin_permissions')
    localStorage.removeItem('admin_roles')
    
    router.push('/login')
  }
  
  // 获取当前会话信息
  getCurrentSession() {
    const token = localStorage.getItem('admin_token')
    if (!token) return null
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        id: payload.jti || 'session_' + Date.now(),
        userId: payload.sub,
        username: payload.username,
        exp: payload.exp,
        iat: payload.iat,
        isValid: payload.exp * 1000 > Date.now()
      }
    } catch (error) {
      console.error('解析会话信息失败:', error)
      return null
    }
  }
  
  // 清理定时器
  cleanup() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer)
      this.sessionTimer = null
    }
    
    if (this.idleTimer) {
      clearInterval(this.idleTimer)
      this.idleTimer = null
    }
  }
  
  // 销毁会话管理器
  destroy() {
    this.cleanup()
    
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    events.forEach(event => {
      document.removeEventListener(event, this.updateActivity.bind(this), true)
    })
  }
}

// 请求安全中间件
class RequestSecurityMiddleware {
  constructor() {
    this.retryCount = new Map()
    this.requestQueue = new Map()
  }
  
  // 请求拦截器
  requestInterceptor(config) {
    // 添加安全头
    config.headers = {
      ...config.headers,
      ...SECURITY_CONFIG.SECURITY_HEADERS
    }
    
    // 添加CSRF令牌
    const csrfToken = this.getCSRFToken()
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = this.generateRequestId()
    
    // 设置超时时间
    config.timeout = SECURITY_CONFIG.REQUEST_TIMEOUT
    
    // 数据脱敏
    if (config.data) {
      config.data = this.sanitizeData(config.data)
    }
    
    return config
  }
  
  // 响应拦截器
  responseInterceptor(response) {
    // 检查响应安全头
    this.validateSecurityHeaders(response.headers)
    
    // 清除重试计数
    const requestId = response.config.headers['X-Request-ID']
    if (requestId) {
      this.retryCount.delete(requestId)
    }
    
    return response
  }
  
  // 错误处理
  errorHandler(error) {
    const { config, response } = error
    
    if (!config) {
      return Promise.reject(error)
    }
    
    const requestId = config.headers['X-Request-ID']
    
    // 处理网络错误
    if (!response) {
      return this.handleNetworkError(error, requestId)
    }
    
    // 处理HTTP错误
    return this.handleHttpError(error, requestId)
  }
  
  // 处理网络错误
  handleNetworkError(error, requestId) {
    const retryCount = this.retryCount.get(requestId) || 0
    
    if (retryCount < SECURITY_CONFIG.MAX_RETRY_ATTEMPTS) {
      this.retryCount.set(requestId, retryCount + 1)
      
      // 指数退避重试
      const delay = Math.pow(2, retryCount) * 1000
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(axios(error.config))
        }, delay)
      })
    }
    
    ElMessage.error('网络连接失败，请检查网络设置')
    return Promise.reject(error)
  }
  
  // 处理HTTP错误
  handleHttpError(error, requestId) {
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        this.handle401Error()
        break
      case 403:
        this.handle403Error(data)
        break
      case 429:
        this.handle429Error()
        break
      case 500:
        this.handle500Error(data)
        break
      default:
        this.handleGenericError(status, data)
    }
    
    return Promise.reject(error)
  }
  
  // 处理401未授权错误
  handle401Error() {
    ElMessage.error('登录已过期，请重新登录')
    sessionManager.logout()
  }
  
  // 处理403权限不足错误
  handle403Error(data) {
    ElMessage.error(data?.message || '权限不足，无法访问该资源')
    
    // 记录权限违规日志
    this.logSecurityEvent('permission_denied', {
      url: window.location.href,
      timestamp: new Date().toISOString()
    })
  }
  
  // 处理429请求过多错误
  handle429Error() {
    ElMessage.warning('请求过于频繁，请稍后再试')
    
    // 记录频率限制日志
    this.logSecurityEvent('rate_limit_exceeded', {
      url: window.location.href,
      timestamp: new Date().toISOString()
    })
  }
  
  // 处理500服务器错误
  handle500Error(data) {
    ElMessage.error('服务器内部错误，请稍后重试')
    
    // 记录服务器错误日志
    this.logSecurityEvent('server_error', {
      error: data?.message,
      timestamp: new Date().toISOString()
    })
  }
  
  // 处理通用错误
  handleGenericError(status, data) {
    ElMessage.error(data?.message || `请求失败 (${status})`)
  }
  
  // 获取CSRF令牌
  getCSRFToken() {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  }
  
  // 生成请求ID
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // 数据脱敏
  sanitizeData(data) {
    if (typeof data !== 'object' || data === null) {
      return data
    }
    
    const sanitized = Array.isArray(data) ? [] : {}
    
    for (const [key, value] of Object.entries(data)) {
      if (SECURITY_CONFIG.SENSITIVE_FIELDS.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        // 敏感字段脱敏
        sanitized[key] = this.maskSensitiveData(value)
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeData(value)
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }
  
  // 脱敏敏感数据
  maskSensitiveData(value) {
    if (typeof value !== 'string') {
      return value
    }
    
    if (value.length <= 4) {
      return '*'.repeat(value.length)
    }
    
    return value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2)
  }
  
  // 验证安全头
  validateSecurityHeaders(headers) {
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ]
    
    const missingHeaders = requiredHeaders.filter(header => !headers[header])
    
    if (missingHeaders.length > 0) {
      console.warn('缺少安全响应头:', missingHeaders)
      
      this.logSecurityEvent('missing_security_headers', {
        missing: missingHeaders,
        timestamp: new Date().toISOString()
      })
    }
  }
  
  // 记录安全事件
  logSecurityEvent(type, data) {
    const event = {
      type,
      data,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    }
    
    // 发送到安全日志服务
    console.log('Security Event:', event)
    
    // 这里可以发送到后端安全日志系统
    // fetch('/api/security/log', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(event)
    // })
  }
}

// 内容安全策略
class ContentSecurityPolicy {
  constructor() {
    // 注释掉CSP初始化，避免meta标签中的frame-ancestors警告
    // this.initCSP()
  }
  
  // 初始化CSP
  initCSP() {
    const cspMeta = document.createElement('meta')
    cspMeta.httpEquiv = 'Content-Security-Policy'
    cspMeta.content = this.generateCSPDirectives()
    document.head.appendChild(cspMeta)
  }
  
  // 生成CSP指令
  generateCSPDirectives() {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' data: https:",
      "font-src 'self' data: https://fonts.gstatic.com",
      "connect-src 'self' https:",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ')
  }
}

// 输入验证和清理
class InputSanitizer {
  // HTML清理
  static sanitizeHtml(html) {
    const div = document.createElement('div')
    div.textContent = html
    return div.innerHTML
  }
  
  // SQL注入防护
  static sanitizeSql(input) {
    if (typeof input !== 'string') return input
    
    const sqlKeywords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE',
      'ALTER', 'EXEC', 'UNION', 'SCRIPT', 'JAVASCRIPT'
    ]
    
    let sanitized = input
    sqlKeywords.forEach(keyword => {
      const regex = new RegExp(keyword, 'gi')
      sanitized = sanitized.replace(regex, '')
    })
    
    return sanitized
  }
  
  // XSS防护
  static sanitizeXss(input) {
    if (typeof input !== 'string') return input
    
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }
  
  // 路径遍历防护
  static sanitizePath(path) {
    if (typeof path !== 'string') return path
    
    return path
      .replace(/\.\./g, '')
      .replace(/[<>:"|?*]/g, '')
      .replace(/^\/+/, '')
  }
}

// 创建实例
const sessionManager = new SessionManager()
const requestSecurity = new RequestSecurityMiddleware()
const contentSecurity = new ContentSecurityPolicy()

// 安全日志记录器
class SecurityLogger {
  constructor() {
    this.logs = []
    this.maxLogs = 1000 // 最大日志条数
  }
  
  // 添加日志
  addLog(type, data) {
    const logEntry = {
      id: Date.now() + Math.random(),
      type,
      ...data,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }
    
    this.logs.unshift(logEntry)
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs)
    }
    
    console.log('Security Log:', logEntry)
    
    // 这里可以发送到后端日志系统
    // this.sendToServer(logEntry)
  }
  
  // 获取日志
  getLogs(type = null, limit = 100) {
    let filteredLogs = this.logs
    
    if (type) {
      filteredLogs = this.logs.filter(log => log.type === type)
    }
    
    return filteredLogs.slice(0, limit)
  }
  
  // 清空日志
  clearLogs() {
    this.logs = []
  }
  
  // 发送到服务器
  sendToServer(logEntry) {
    // fetch('/api/security/log', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(logEntry)
    // }).catch(error => {
    //   console.error('发送安全日志失败:', error)
    // })
  }
}

// 创建安全日志记录器实例
const securityLogger = new SecurityLogger()

// 日志记录函数
export const logAccess = (username, action, resource, details = {}) => {
  securityLogger.addLog('access', {
    username,
    action,
    resource,
    details,
    ip: 'unknown' // 在实际应用中应该从服务器获取
  })
}

export const logSession = (username, action, sessionId, details = {}) => {
  const logEntry = {
    type: 'session',
    username,
    action,
    sessionId,
    details,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent
  }
  
  console.log('Session Log:', logEntry)
  
  // 这里可以发送到后端日志系统
  // fetch('/api/security/session-log', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(logEntry)
  // })
}

export const logLogin = (username, success, details = {}) => {
  const logEntry = {
    type: 'login',
    username,
    success,
    details,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    ip: 'unknown' // 在实际应用中应该从服务器获取
  }
  
  console.log('Login Log:', logEntry)
  
  // 这里可以发送到后端日志系统
  // fetch('/api/security/login-log', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(logEntry)
  // })
}

// 导出安全工具
export {
  SECURITY_CONFIG,
  SessionManager,
  RequestSecurityMiddleware,
  ContentSecurityPolicy,
  InputSanitizer,
  SecurityLogger,
  sessionManager,
  requestSecurity,
  contentSecurity,
  securityLogger
}

// 导出默认配置
export default {
  sessionManager,
  requestSecurity,
  contentSecurity,
  InputSanitizer,
  logAccess,
  logSession
}
