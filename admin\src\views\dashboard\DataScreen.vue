<template>
  <div class="data-screen">
    <!-- 数据大屏头部 -->
    <header class="screen-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">📊</div>
          <div class="logo-text">
            <h1>LinkHub Pro 数据中心</h1>
            <p>实时运营数据监控</p>
          </div>
        </div>
      </div>
      
      <div class="header-center">
        <div class="time-display">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date">{{ currentDate }}</div>
        </div>
      </div>
      
      <div class="header-right">
        <div class="status-indicators">
          <div class="status-item">
            <span class="status-dot online"></span>
            <span>系统正常</span>
          </div>
          <div class="status-item">
            <span class="status-dot"></span>
            <span>实时更新</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="screen-content">
      <!-- 核心指标卡片 -->
      <section class="metrics-section">
        <div 
          v-for="metric in coreMetrics" 
          :key="metric.id"
          class="metric-card"
          :style="{ '--accent-color': metric.color }"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <span class="icon-emoji">{{ metric.icon }}</span>
            </div>
            <div class="metric-trend" :class="metric.trend.type">
              <span class="trend-icon">{{ metric.trend.icon }}</span>
              <span>{{ metric.trend.value }}%</span>
            </div>
          </div>
          
          <div class="metric-content">
            <div class="metric-value">
              <span class="value">{{ formatNumber(metric.value) }}</span>
              <span class="unit">{{ metric.unit }}</span>
            </div>
            <div class="metric-label">{{ metric.label }}</div>
          </div>
          
          <div class="metric-chart">
            <div class="mini-chart" :ref="el => setChartRef(metric.id, el)"></div>
          </div>
        </div>
      </section>

      <!-- 图表区域 -->
      <section class="charts-section">
        <!-- 主要趋势图表 -->
        <div class="chart-container main-chart">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
            <div class="chart-controls">
              <button 
                v-for="period in timePeriods" 
                :key="period.value"
                :class="['period-btn', { active: selectedPeriod === period.value }]"
                @click="selectedPeriod = period.value"
              >
                {{ period.label }}
              </button>
            </div>
          </div>
          <div class="chart-content" ref="mainChartRef"></div>
        </div>

        <!-- 订单分析图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>订单分析</h3>
          </div>
          <div class="chart-content" ref="orderChartRef"></div>
        </div>

        <!-- 收入分析图表 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3>收入分析</h3>
          </div>
          <div class="chart-content" ref="revenueChartRef"></div>
        </div>
      </section>

      <!-- 地图和详细数据区域 -->
      <section class="details-section">
        <!-- 中国地图 -->
        <div class="map-container">
          <div class="map-header">
            <h3>全国用户分布</h3>
            <div class="map-legend">
              <div class="legend-item">
                <span class="legend-color" style="background: #4ade80"></span>
                <span>0-1000</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #fbbf24"></span>
                <span>1000-5000</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #f87171"></span>
                <span>5000-10000</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #dc2626"></span>
                <span>10000+</span>
              </div>
            </div>
          </div>
          <div class="map-content" ref="mapChartRef"></div>
        </div>

        <!-- 排行榜 -->
        <div class="ranking-container">
          <div class="ranking-header">
            <h3>省份排行榜</h3>
          </div>
          <div class="ranking-list">
            <div 
              v-for="(item, index) in topProvinces" 
              :key="item.name"
              class="ranking-item"
              :class="{ 'top-three': index < 3 }"
            >
              <div class="ranking-number" :class="getRankClass(index)">
                {{ index + 1 }}
              </div>
              <div class="ranking-info">
                <div class="province-name">{{ item.name }}</div>
                <div class="province-value">{{ formatNumber(item.value) }}</div>
              </div>
              <div class="ranking-bar">
                <div 
                  class="bar-fill" 
                  :style="{ width: (item.value / topProvinces[0].value * 100) + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时数据表格 -->
        <div class="table-container">
          <div class="table-header">
            <h3>实时数据</h3>
            <div class="refresh-btn" @click="refreshData">
              <span :class="{ spinning: isRefreshing }">🔄</span>
            </div>
          </div>
          <div class="table-content">
            <table class="data-table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>新增用户</th>
                  <th>活跃用户</th>
                  <th>订单数</th>
                  <th>收入</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="row in realtimeData" :key="row.time">
                  <td>{{ row.time }}</td>
                  <td class="number">{{ row.newUsers }}</td>
                  <td class="number">{{ row.activeUsers }}</td>
                  <td class="number">{{ row.orders }}</td>
                  <td class="number">¥{{ formatNumber(row.revenue) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { loadChinaMap, getProvinceDataMap, formatMapData, generateVisualMap, downloadRealMapData, getMapDataSources } from '@/utils/mapLoader'

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const selectedPeriod = ref('7d')
const isRefreshing = ref(false)
const isMapLoaded = ref(false)

// 图表引用
const mainChartRef = ref(null)
const orderChartRef = ref(null)
const revenueChartRef = ref(null)
const mapChartRef = ref(null)
const chartRefs = ref({})

// 图表实例
const charts = ref({})

// 时间周期选项
const timePeriods = ref([
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
])

// 核心指标数据
const coreMetrics = ref([
  {
    id: 'users',
    label: '总用户数',
    value: 125680,
    unit: '',
    icon: '👥',
    color: '#3b82f6',
    trend: { type: 'up', value: 12.5, icon: '↗️' }
  },
  {
    id: 'orders',
    label: '总订单数',
    value: 89234,
    unit: '',
    icon: '📦',
    color: '#10b981',
    trend: { type: 'up', value: 8.3, icon: '↗️' }
  },
  {
    id: 'revenue',
    label: '总收入',
    value: 2456789,
    unit: '元',
    icon: '💰',
    color: '#f59e0b',
    trend: { type: 'up', value: 15.2, icon: '↗️' }
  },
  {
    id: 'conversion',
    label: '转化率',
    value: 68.5,
    unit: '%',
    icon: '📊',
    color: '#ef4444',
    trend: { type: 'down', value: 2.1, icon: '↘️' }
  }
])

// 省份数据 - 与地图数据保持一致
const provinceData = ref([
  { name: '广东', value: 25680 },
  { name: '江苏', value: 18950 },
  { name: '山东', value: 16780 },
  { name: '浙江', value: 14320 },
  { name: '北京', value: 15420 },
  { name: '河南', value: 13560 },
  { name: '四川', value: 12890 },
  { name: '上海', value: 12680 },
  { name: '湖北', value: 11450 },
  { name: '湖南', value: 10980 }
])

// 计算属性
const topProvinces = computed(() => {
  return [...provinceData.value]
    .sort((a, b) => b.value - a.value)
    .slice(0, 10)
})

// 实时数据
const realtimeData = ref([
  { time: '14:00', newUsers: 156, activeUsers: 2340, orders: 89, revenue: 12450 },
  { time: '14:05', newUsers: 142, activeUsers: 2380, orders: 95, revenue: 13200 },
  { time: '14:10', newUsers: 168, activeUsers: 2420, orders: 78, revenue: 11800 },
  { time: '14:15', newUsers: 134, activeUsers: 2390, orders: 102, revenue: 14600 },
  { time: '14:20', newUsers: 189, activeUsers: 2450, orders: 87, revenue: 12900 }
])

// 方法
const formatNumber = (num) => {
  if (num >= 10000) return (num / 10000).toFixed(1) + 'w'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
  return num.toString()
}

const getRankClass = (index) => {
  if (index === 0) return 'gold'
  if (index === 1) return 'silver'
  if (index === 2) return 'bronze'
  return 'normal'
}

const setChartRef = (id, el) => {
  if (el) {
    chartRefs.value[id] = el
  }
}

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  currentDate.value = now.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  })
}

const refreshData = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    // 这里可以添加实际的数据刷新逻辑
  }, 1000)
}

// 生命周期
onMounted(async () => {
  console.log('🚀 数据大屏开始初始化...')

  updateTime()
  setInterval(updateTime, 1000)
  console.log('⏰ 时钟初始化完成')

  await nextTick()

  // 延迟初始化图表，确保DOM完全渲染
  setTimeout(() => {
    console.log('📊 开始初始化图表...')
    initCharts()
    console.log('✅ 数据大屏初始化完成')

    // 窗口大小调整时重新调整图表大小
    window.addEventListener('resize', () => {
      Object.values(charts.value).forEach(chart => {
        if (chart && chart.resize) {
          chart.resize()
        }
      })
    })
  }, 100)
})

onUnmounted(() => {
  // 销毁图表实例
  Object.values(charts.value).forEach(chart => {
    if (chart) chart.dispose()
  })
})

// 初始化图表
const initCharts = async () => {
  try {
    console.log('📈 初始化主趋势图表...')
    initMainChart()

    console.log('🥧 初始化订单分析图表...')
    initOrderChart()

    console.log('💹 初始化收入分析图表...')
    initRevenueChart()

    console.log('🗺️ 加载地图数据...')
    // 使用本地真实地图数据
    isMapLoaded.value = await loadChinaMap('local')

    if (isMapLoaded.value) {
      console.log('🗺️ 初始化地图图表...')
      await initMapChart()
    } else {
      console.warn('⚠️ 地图数据加载失败，跳过地图初始化')
    }

    console.log('📊 初始化小型图表...')
    initMiniCharts()

    console.log('✅ 所有图表初始化完成')
  } catch (error) {
    console.error('❌ 图表初始化失败:', error)
  }
}

// 主趋势图表
const initMainChart = () => {
  if (!mainChartRef.value) {
    console.warn('⚠️ 主图表容器未找到')
    return
  }

  console.log('📈 主图表容器尺寸:', mainChartRef.value.offsetWidth, 'x', mainChartRef.value.offsetHeight)

  const chart = echarts.init(mainChartRef.value)
  charts.value.main = chart

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: {
        show: true,
        lineStyle: { color: 'rgba(255,255,255,0.3)' }
      },
      axisLabel: {
        color: 'rgba(255,255,255,0.8)',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: { color: 'rgba(255,255,255,0.3)' }
      },
      axisLabel: {
        color: 'rgba(255,255,255,0.8)',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: { color: 'rgba(255,255,255,0.1)' }
      }
    },
    series: [{
      name: '用户增长',
      data: [1200, 1320, 1010, 1340, 1290, 1330, 1320],
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#3b82f6',
        width: 3
      },
      itemStyle: {
        color: '#3b82f6',
        borderWidth: 2,
        borderColor: '#fff'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
          { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
        ])
      }
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' }
    }
  }

  chart.setOption(option)
  console.log('✅ 主趋势图表设置完成')
}

// 订单分析图表
const initOrderChart = () => {
  if (!orderChartRef.value) return

  const chart = echarts.init(orderChartRef.value)
  charts.value.order = chart

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' }
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: [
        { value: 1048, name: '已完成', itemStyle: { color: '#10b981' } },
        { value: 735, name: '进行中', itemStyle: { color: '#3b82f6' } },
        { value: 580, name: '已取消', itemStyle: { color: '#ef4444' } },
        { value: 484, name: '待付款', itemStyle: { color: '#f59e0b' } }
      ],
      label: {
        color: 'rgba(255,255,255,0.8)',
        fontSize: 12
      },
      labelLine: {
        lineStyle: { color: 'rgba(255,255,255,0.3)' }
      }
    }]
  }

  chart.setOption(option)
}

// 收入分析图表
const initRevenueChart = () => {
  if (!revenueChartRef.value) return

  const chart = echarts.init(revenueChartRef.value)
  charts.value.revenue = chart

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLine: { lineStyle: { color: 'rgba(255,255,255,0.3)' } },
      axisLabel: { color: 'rgba(255,255,255,0.8)' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: 'rgba(255,255,255,0.3)' } },
      axisLabel: { color: 'rgba(255,255,255,0.8)' },
      splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
    },
    series: [{
      data: [2300, 2800, 3200, 2900, 3500, 3800],
      type: 'bar',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#f59e0b' },
          { offset: 1, color: '#fbbf24' }
        ])
      },
      barWidth: '60%'
    }]
  }

  chart.setOption(option)
}

// 中国地图 - 真正的地图实现
const initMapChart = async () => {
  if (!mapChartRef.value) {
    console.warn('⚠️ 地图容器未找到')
    return
  }

  if (!isMapLoaded.value) {
    console.warn('⚠️ 地图数据未加载，跳过初始化')
    return
  }

  console.log('🗺️ 地图容器尺寸:', mapChartRef.value.offsetWidth, 'x', mapChartRef.value.offsetHeight)

  const chart = echarts.init(mapChartRef.value)
  charts.value.map = chart

  // 获取省份数据
  const provinceDataMap = getProvinceDataMap()
  const mapData = formatMapData(provinceDataMap)
  const maxValue = Math.max(...Object.values(provinceDataMap))
  const minValue = Math.min(...Object.values(provinceDataMap))

  console.log('🗺️ 地图数据统计:')
  console.log('   - 省份数据数量:', Object.keys(provinceDataMap).length)
  console.log('   - 地图数据数量:', mapData.length)
  console.log('   - 数值范围:', minValue, '-', maxValue)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: 10,
      right: 10,
      top: 50,
      bottom: 80,
      containLabel: true
    },
    title: {
      text: '全国用户分布',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      textStyle: { color: '#fff' },
      formatter: function (params) {
        try {
          if (params && params.name) {
            if (params.data &&
                params.data.value !== undefined &&
                params.data.value !== null &&
                typeof params.data.value === 'number') {
              return `${params.name}<br/>用户数: ${params.data.value.toLocaleString()}`
            }
            return `${params.name}<br/>暂无数据`
          }
          return '数据加载中...'
        } catch (error) {
          console.warn('⚠️ Tooltip格式化错误:', error, params)
          return '数据显示异常'
        }
      }
    },
    visualMap: generateVisualMap(minValue, maxValue),
    series: [{
      name: '用户分布',
      type: 'map',
      map: 'china',
      roam: false,
      zoom: 1.8,
      center: [105, 36],
      data: mapData,
      label: {
        show: true,
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        textBorderColor: 'rgba(0,0,0,0.5)',
        textBorderWidth: 1
      },
      itemStyle: {
        borderColor: 'rgba(255,255,255,0.3)',
        borderWidth: 1,
        areaColor: '#4a5568'
      },
      emphasis: {
        label: {
          show: true,
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold',
          textBorderColor: 'rgba(0,0,0,0.8)',
          textBorderWidth: 2
        },
        itemStyle: {
          areaColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: '#60a5fa',
          borderWidth: 3,
          shadowBlur: 15,
          shadowColor: 'rgba(59, 130, 246, 0.7)'
        }
      }
    }]
  }

  chart.setOption(option)
  console.log('✅ 中国地图设置完成')
}

// 小型图表
const initMiniCharts = () => {
  coreMetrics.value.forEach(metric => {
    const el = chartRefs.value[metric.id]
    if (el) {
      const chart = echarts.init(el)
      charts.value[metric.id] = chart

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        xAxis: {
          type: 'category',
          show: false,
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          data: [120, 132, 101, 134, 90, 230, 210],
          type: 'line',
          smooth: true,
          lineStyle: { color: metric.color, width: 2 },
          itemStyle: { color: metric.color },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: metric.color + '40' },
              { offset: 1, color: metric.color + '10' }
            ])
          },
          symbol: 'none'
        }]
      }

      chart.setOption(option)
    }
  })
}
</script>

<style scoped>
.data-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #ffffff;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.screen-header {
  height: 80px;
  padding: 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  font-size: 32px;
}

.logo-text h1 {
  font-size: 20px;
  margin: 0;
  font-weight: 600;
}

.logo-text p {
  font-size: 12px;
  margin: 0;
  opacity: 0.8;
}

.time-display {
  text-align: center;
}

.current-time {
  font-size: 24px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.current-date {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
}

.status-indicators {
  display: flex;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: #10b981;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.screen-content {
  flex: 1;
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
}

.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.metric-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: var(--accent-color);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.icon-emoji {
  font-size: 24px;
  filter: brightness(1.2);
}

.trend-icon {
  font-size: 12px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.metric-trend.up {
  color: #10b981;
}

.metric-trend.down {
  color: #ef4444;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.metric-value .unit {
  font-size: 16px;
  opacity: 0.8;
  margin-left: 4px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 16px;
}

.mini-chart {
  height: 40px;
  width: 100%;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 24px;
  height: 350px;
}

.chart-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart-header {
  padding: 20px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 4px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn.active,
.period-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.5);
  color: #ffffff;
}

.chart-content {
  flex: 1;
  padding: 16px 24px 24px;
  min-height: 200px;
}

.details-section {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr;
  gap: 24px;
  height: 500px;
}

.map-container,
.ranking-container,
.table-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.map-header,
.ranking-header,
.table-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-header h3,
.ranking-header h3,
.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.map-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.map-content {
  flex: 1;
  padding: 12px 16px 16px;
  min-height: 350px;
}

.ranking-list {
  flex: 1;
  padding: 16px 24px 24px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.ranking-number.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
}

.ranking-number.silver {
  background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
  color: #000;
}

.ranking-number.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #000;
}

.ranking-number.normal {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.ranking-info {
  flex: 1;
}

.province-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.province-value {
  font-size: 12px;
  opacity: 0.8;
}

.ranking-bar {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.table-content {
  flex: 1;
  overflow-y: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.data-table th {
  font-weight: 600;
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.05);
}

.data-table td.number {
  font-family: 'Courier New', monospace;
  text-align: right;
}

.refresh-btn {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-section,
  .details-section {
    grid-template-columns: 1fr;
    height: auto;
  }

  .chart-container,
  .map-container,
  .ranking-container,
  .table-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .screen-header {
    flex-direction: column;
    height: auto;
    padding: 16px;
    gap: 16px;
  }

  .screen-content {
    padding: 16px;
  }

  .metrics-section {
    grid-template-columns: 1fr;
  }
}
</style>
