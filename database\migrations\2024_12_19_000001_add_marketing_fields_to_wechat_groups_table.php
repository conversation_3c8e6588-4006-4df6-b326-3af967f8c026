<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 营销展示字段
            $table->string('read_count_display', 20)->nullable()->comment('阅读数显示文本')->after('description');
            $table->integer('like_count')->default(0)->comment('点赞数')->after('read_count_display');
            $table->integer('want_see_count')->default(0)->comment('想看数')->after('like_count');
            $table->string('button_title', 100)->nullable()->comment('入群按钮文案')->after('want_see_count');
            
            // 内容设置字段
            $table->string('group_intro_title', 60)->nullable()->comment('群简介标题')->after('button_title');
            $table->text('group_intro_content')->nullable()->comment('群简介内容')->after('group_intro_title');
            $table->string('faq_title', 60)->nullable()->comment('常见问题标题')->after('group_intro_content');
            $table->text('faq_content')->nullable()->comment('常见问题内容')->after('faq_title');
            $table->text('member_reviews')->nullable()->comment('群友评论')->after('faq_content');
            
            // 客服广告字段
            $table->string('customer_service_qr')->nullable()->comment('客服二维码')->after('member_reviews');
            $table->string('ad_qr_code')->nullable()->comment('广告二维码')->after('customer_service_qr');
            $table->tinyInteger('show_customer_service')->default(1)->comment('是否显示客服信息 1=不显示 2=显示')->after('ad_qr_code');
            $table->string('customer_service_avatar')->nullable()->comment('客服头像')->after('show_customer_service');
            $table->string('customer_service_title', 100)->nullable()->comment('客服标题')->after('customer_service_avatar');
            $table->string('customer_service_desc', 500)->nullable()->comment('客服描述')->after('customer_service_title');
            
            // 展示控制字段
            $table->string('avatar_library', 20)->default('qq')->comment('头像库选择 qq/za')->after('customer_service_desc');
            $table->tinyInteger('wx_accessible')->default(1)->comment('微信浏览器访问控制 1=能打开 2=不能打开')->after('avatar_library');
            $table->tinyInteger('display_type')->default(1)->comment('展示类型 1=文字+图片 2=纯图片')->after('wx_accessible');
            
            // 城市定位增强字段
            $table->tinyInteger('auto_city_replace')->default(1)->comment('自动城市替换 0=关闭 1=开启')->after('display_type');
            $table->string('city_insert_strategy', 20)->default('auto')->comment('城市插入策略')->after('auto_city_replace');
            
            // 虚拟数据增强字段
            $table->integer('virtual_members')->default(0)->comment('虚拟成员数')->after('city_insert_strategy');
            $table->integer('virtual_orders')->default(0)->comment('虚拟订单数')->after('virtual_members');
            $table->decimal('virtual_income', 10, 2)->default(0)->comment('虚拟收入')->after('virtual_orders');
            $table->integer('today_views')->default(0)->comment('今日浏览量')->after('virtual_income');
            $table->integer('total_joins')->default(0)->comment('总加入数')->after('today_views');
            
            // 营销标签
            $table->json('marketing_tags')->nullable()->comment('营销标签')->after('total_joins');
            
            // 扩展内容字段
            $table->string('extra_title1', 60)->nullable()->comment('扩展标题1')->after('marketing_tags');
            $table->text('extra_content1')->nullable()->comment('扩展内容1')->after('extra_title1');
            $table->string('extra_title2', 60)->nullable()->comment('扩展标题2')->after('extra_content1');
            $table->text('extra_content2')->nullable()->comment('扩展内容2')->after('extra_title2');
            $table->string('extra_title3', 60)->nullable()->comment('扩展标题3')->after('extra_content2');
            $table->text('extra_content3')->nullable()->comment('扩展内容3')->after('extra_title3');
            
            // 统计字段
            $table->integer('view_count')->default(0)->comment('总浏览次数')->after('extra_content3');
            $table->timestamp('last_activity_at')->nullable()->comment('最后活动时间')->after('view_count');
            
            // 支付相关字段（兼容源码包）
            $table->string('specific_payments')->nullable()->comment('指定支付方式ID列表')->after('last_activity_at');
            $table->tinyInteger('payment_status')->default(1)->comment('支付功能状态 1=启用 2=禁用')->after('specific_payments');
            
            // 索引优化
            $table->index(['status', 'auto_city_replace'], 'idx_status_city');
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['substation_id', 'status'], 'idx_substation_status');
            $table->index('view_count', 'idx_view_count');
            $table->index('last_activity_at', 'idx_last_activity');
            $table->index('payment_status', 'idx_payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_status_city');
            $table->dropIndex('idx_user_status');
            $table->dropIndex('idx_substation_status');
            $table->dropIndex('idx_view_count');
            $table->dropIndex('idx_last_activity');
            $table->dropIndex('idx_payment_status');
            
            // 删除字段
            $table->dropColumn([
                'read_count_display', 'like_count', 'want_see_count', 'button_title',
                'group_intro_title', 'group_intro_content', 'faq_title', 'faq_content', 'member_reviews',
                'customer_service_qr', 'ad_qr_code', 'show_customer_service', 
                'customer_service_avatar', 'customer_service_title', 'customer_service_desc',
                'avatar_library', 'wx_accessible', 'display_type',
                'auto_city_replace', 'city_insert_strategy',
                'virtual_members', 'virtual_orders', 'virtual_income', 'today_views', 'total_joins',
                'marketing_tags',
                'extra_title1', 'extra_content1', 'extra_title2', 'extra_content2', 'extra_title3', 'extra_content3',
                'view_count', 'last_activity_at',
                'specific_payments', 'payment_status'
            ]);
        });
    }
};