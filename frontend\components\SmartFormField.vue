<template>
  <div class="smart-form-field">
    <label v-if="label" class="block text-sm font-medium text-gray-700 mb-2">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <!-- 输入框 -->
      <component
        :is="inputComponent"
        :value="modelValue"
        v-bind="inputProps"
        :class="inputClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <!-- 智能建议按钮 -->
      <button
        v-if="showSuggestionButton && suggestions.length > 0"
        @click="toggleSuggestions"
        type="button"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800 transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
      </button>
      
      <!-- 验证状态图标 -->
      <div v-if="validationStatus" class="absolute right-3 top-1/2 transform -translate-y-1/2">
        <svg v-if="validationStatus === 'valid'" class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <svg v-else-if="validationStatus === 'invalid'" class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <svg v-else-if="validationStatus === 'warning'" class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
    
    <!-- 智能建议下拉 -->
    <div v-if="showSuggestions && suggestions.length > 0" class="mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10">
      <div class="text-xs font-medium text-gray-700 mb-2">💡 智能建议：</div>
      <div class="space-y-1">
        <button
          v-for="(suggestion, index) in suggestions"
          :key="index"
          @click="applySuggestion(suggestion)"
          class="block w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded transition-colors"
        >
          {{ suggestion.text || suggestion }}
        </button>
      </div>
    </div>
    
    <!-- 验证错误提示 -->
    <div v-if="error" class="mt-1 text-sm text-red-600 flex items-center">
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      {{ error }}
    </div>
    
    <!-- 优化提示 -->
    <div v-if="optimizationTip" class="mt-1 text-sm text-blue-600 flex items-center">
      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      {{ optimizationTip }}
    </div>
    
    <!-- 字符计数和转化率影响 -->
    <div v-if="showMeta" class="mt-2 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div v-if="helpText" class="text-xs text-gray-500">
          {{ helpText }}
        </div>
        <div v-if="conversionImpact" class="flex items-center text-xs text-green-600">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          转化率 {{ conversionImpact }}
        </div>
      </div>
      <div v-if="maxLength" :class="['text-xs', characterCount > maxLength * 0.8 ? 'text-orange-500' : 'text-gray-400']">
        {{ characterCount }}/{{ maxLength }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: null
  },
  minLength: {
    type: Number,
    default: null
  },
  validationRules: {
    type: Array,
    default: () => []
  },
  suggestions: {
    type: Array,
    default: () => []
  },
  helpText: {
    type: String,
    default: ''
  },
  optimizationRules: {
    type: Array,
    default: () => []
  },
  fieldType: {
    type: String,
    default: 'text' // text, textarea, select, number
  }
})

const emit = defineEmits(['update:modelValue', 'validation-change', 'optimization-change'])

// 响应式数据
const showSuggestions = ref(false)
const isFocused = ref(false)
const error = ref('')
const optimizationTip = ref('')
const conversionImpact = ref('')

// 计算属性
const inputComponent = computed(() => {
  switch (props.fieldType) {
    case 'textarea':
      return 'textarea'
    case 'select':
      return 'select'
    default:
      return 'input'
  }
})

const inputProps = computed(() => {
  const baseProps = {
    placeholder: props.placeholder,
    maxlength: props.maxLength
  }
  
  if (props.fieldType === 'textarea') {
    return { ...baseProps, rows: 4 }
  }
  
  if (props.fieldType !== 'select') {
    return { ...baseProps, type: props.type }
  }
  
  return baseProps
})

const inputClasses = computed(() => {
  const baseClasses = 'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors'
  
  if (error.value) {
    return `${baseClasses} border-red-300 bg-red-50`
  }
  
  if (validationStatus.value === 'valid') {
    return `${baseClasses} border-green-300 bg-green-50`
  }
  
  if (validationStatus.value === 'warning') {
    return `${baseClasses} border-yellow-300 bg-yellow-50`
  }
  
  return `${baseClasses} border-gray-300`
})

const characterCount = computed(() => {
  return String(props.modelValue || '').length
})

const validationStatus = computed(() => {
  if (error.value) return 'invalid'
  
  if (props.required && !props.modelValue) return null
  
  if (props.modelValue) {
    const isValid = validateValue(props.modelValue)
    if (isValid === true) return 'valid'
    if (isValid === 'warning') return 'warning'
  }
  
  return null
})

const showSuggestionButton = computed(() => {
  return props.suggestions.length > 0 && !isFocused.value
})

const showMeta = computed(() => {
  return props.helpText || props.maxLength || conversionImpact.value
})

// 方法
const validateValue = (value) => {
  // 基础验证
  if (props.required && !value) {
    return '此字段为必填项'
  }
  
  if (props.minLength && String(value).length < props.minLength) {
    return `至少需要 ${props.minLength} 个字符`
  }
  
  if (props.maxLength && String(value).length > props.maxLength) {
    return `不能超过 ${props.maxLength} 个字符`
  }
  
  // 自定义验证规则
  for (const rule of props.validationRules) {
    const result = rule(value)
    if (result !== true) {
      return result
    }
  }
  
  return true
}

const checkOptimization = (value) => {
  optimizationTip.value = ''
  conversionImpact.value = ''
  
  for (const rule of props.optimizationRules) {
    const result = rule(value)
    if (result.tip) {
      optimizationTip.value = result.tip
    }
    if (result.impact) {
      conversionImpact.value = result.impact
    }
  }
}

const handleInput = (event) => {
  const value = event.target.value
  emit('update:modelValue', value)
  
  // 实时验证
  const validationResult = validateValue(value)
  error.value = validationResult === true ? '' : validationResult
  
  // 优化检查
  checkOptimization(value)
  
  emit('validation-change', {
    isValid: validationResult === true,
    error: error.value
  })
  
  emit('optimization-change', {
    tip: optimizationTip.value,
    impact: conversionImpact.value
  })
}

const handleBlur = () => {
  isFocused.value = false
  showSuggestions.value = false
  
  // 完整验证
  const validationResult = validateValue(props.modelValue)
  error.value = validationResult === true ? '' : validationResult
}

const handleFocus = () => {
  isFocused.value = true
}

const toggleSuggestions = () => {
  showSuggestions.value = !showSuggestions.value
}

const applySuggestion = (suggestion) => {
  const value = suggestion.text || suggestion
  emit('update:modelValue', value)
  showSuggestions.value = false
  
  // 触发验证和优化检查
  nextTick(() => {
    handleInput({ target: { value } })
  })
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    checkOptimization(newValue)
  }
})
</script>

<style scoped>
.smart-form-field {
  @apply relative;
}

/* 自定义焦点样式 */
.smart-form-field input:focus,
.smart-form-field textarea:focus,
.smart-form-field select:focus {
  @apply outline-none;
}

/* 建议下拉动画 */
.smart-form-field .suggestions-dropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>