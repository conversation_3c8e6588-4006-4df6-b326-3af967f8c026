<template>
  <div class="audit-logs-container">
    <el-card>
        <template #header>
            <h3>系统日志</h3>
        </template>
        
        <!-- 筛选区域 -->
        <div class="filter-container">
            <el-input v-model="filters.user" placeholder="操作人" style="width: 180px;" clearable />
            <el-date-picker
                v-model="filters.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 380px; margin-left: 10px;"
            />
            <el-select v-model="filters.actionType" placeholder="操作类型" style="width: 150px; margin-left: 10px;" clearable>
                <el-option label="创建" value="create"></el-option>
                <el-option label="更新" value="update"></el-option>
                <el-option label="删除" value="delete"></el-option>
                <el-option label="登录" value="login"></el-option>
            </el-select>
            <el-button type="primary" icon="Search" @click="fetchLogs" style="margin-left: 10px;">搜索</el-button>
        </div>

        <!-- 日志表格 -->
        <el-table :data="logs" v-loading="loading" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="timestamp" label="操作时间" width="180">
                <template #default="{ row }">{{ formatDate(row.timestamp) }}</template>
            </el-table-column>
            <el-table-column prop="user.name" label="操作人" width="120" />
            <el-table-column prop="ip_address" label="IP地址" width="150" />
            <el-table-column label="操作类型" width="100">
                <template #default="{ row }">
                    <el-tag :type="getActionTypeTag(row.action_type)">{{ row.action_type.toUpperCase() }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="description" label="操作描述" />
            <el-table-column label="操作" width="100">
                <template #default="{ row }">
                    <el-button link type="primary" size="small" @click="viewLogDetail(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.limit"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="fetchLogs"
                @current-change="fetchLogs"
            />
        </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="日志详情" width="600px">
        <div v-if="selectedLog">
            <p><strong>操作描述:</strong> {{ selectedLog.description }}</p>
            <p><strong>操作人:</strong> {{ selectedLog.user.name }} (ID: {{ selectedLog.user.id }})</p>
            <p><strong>时间:</strong> {{ formatDate(selectedLog.timestamp) }}</p>
            <p><strong>IP 地址:</strong> {{ selectedLog.ip_address }}</p>
            <p><strong>User Agent:</strong> {{ selectedLog.user_agent }}</p>
            <div v-if="selectedLog.data_changes">
                <h4>数据变更:</h4>
                <pre class="code-block">{{ JSON.stringify(selectedLog.data_changes, null, 2) }}</pre>
            </div>
        </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { formatDate } from '@/utils/format'
import { Search } from '@element-plus/icons-vue'

const loading = ref(false)
const logs = ref([])
const filters = reactive({
    user: '',
    dateRange: [],
    actionType: '',
})
const pagination = reactive({
    page: 1,
    limit: 10,
    total: 0,
})

const detailDialogVisible = ref(false)
const selectedLog = ref(null)

const mockLogs = Array.from({ length: 50 }).map((_, i) => {
    const actions = ['create', 'update', 'delete', 'login']
    const users = [{id: 1, name: 'Admin'}, {id: 2, name: 'Operator'}]
    const action = actions[i % 4]
    return {
        id: i + 1,
        timestamp: new Date(Date.now() - i * 3600 * 1000).toISOString(),
        user: users[i % 2],
        ip_address: `192.168.1.${i+1}`,
        action_type: action,
        description: `${users[i % 2].name} ${action}了文章 '文章标题${i+1}'`,
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...',
        data_changes: action !== 'login' ? { before: { status: 'draft' }, after: { status: 'published' } } : null
    }
})

const fetchLogs = () => {
    loading.value = true
    setTimeout(() => {
        pagination.total = mockLogs.length
        const start = (pagination.page - 1) * pagination.limit
        const end = start + pagination.limit
        logs.value = mockLogs.slice(start, end)
        loading.value = false
    }, 500)
}

const getActionTypeTag = (action) => {
    const map = {
        create: 'success',
        update: 'primary',
        delete: 'danger',
        login: 'info',
    }
    return map[action] || 'default'
}

const viewLogDetail = (log) => {
    selectedLog.value = log
    detailDialogVisible.value = true
}

onMounted(() => {
    fetchLogs()
})
</script>

<style scoped>
.filter-container {
    display: flex;
    align-items: center;
}
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}
.code-block {
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>