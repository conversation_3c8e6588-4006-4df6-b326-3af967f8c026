<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\JWTService;
use Illuminate\Http\JsonResponse;

/**
 * JWT认证中间件
 */
class JwtMiddleware
{
    protected JWTService $jwtService;

    public function __construct(JWTService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $token = $this->jwtService->getTokenFromRequest();

        if (!$token) {
            return $this->unauthorizedResponse('Token not provided');
        }

        $user = $this->jwtService->validateToken($token);

        if (!$user) {
            return $this->unauthorizedResponse('Token is invalid or expired');
        }

        // 将用户信息添加到请求中
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // 设置当前认证用户
        auth()->setUser($user);

        return $next($request);
    }

    /**
     * 返回未授权响应
     */
    private function unauthorizedResponse(string $message): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'code' => 401,
        ], 401);
    }
}