/**
 * Auth Utilities Unit Tests
 * 测试认证工具函数的所有功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import Cookies from 'js-cookie'
import {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  isLoggedIn,
  clearAuth
} from '@/utils/auth'

// 模拟 js-cookie
vi.mock('js-cookie', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
}))

describe('Auth Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    sessionStorage.clear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Token Management', () => {
    describe('getToken', () => {
      it('should get token from cookies', () => {
        const mockToken = 'test-token'
        Cookies.get.mockReturnValue(mockToken)

        const result = getToken()

        expect(Cookies.get).toHaveBeenCalledWith('Admin-Token')
        expect(result).toBe(mockToken)
      })

      it('should return undefined when no token exists', () => {
        Cookies.get.mockReturnValue(undefined)

        const result = getToken()

        expect(result).toBeUndefined()
      })
    })

    describe('setToken', () => {
      it('should set token in cookies with 7 days expiry', () => {
        const token = 'new-token'
        Cookies.set.mockReturnValue(true)

        const result = setToken(token)

        expect(Cookies.set).toHaveBeenCalledWith('Admin-Token', token, { expires: 7 })
        expect(result).toBe(true)
      })

      it('should handle empty token', () => {
        const token = ''
        Cookies.set.mockReturnValue(true)

        setToken(token)

        expect(Cookies.set).toHaveBeenCalledWith('Admin-Token', '', { expires: 7 })
      })
    })

    describe('removeToken', () => {
      it('should remove both access and refresh tokens', () => {
        removeToken()

        expect(Cookies.remove).toHaveBeenCalledWith('Admin-Token')
        expect(Cookies.remove).toHaveBeenCalledWith('Admin-Refresh-Token')
        expect(Cookies.remove).toHaveBeenCalledTimes(2)
      })
    })
  })

  describe('Refresh Token Management', () => {
    describe('getRefreshToken', () => {
      it('should get refresh token from cookies', () => {
        const mockRefreshToken = 'refresh-token'
        Cookies.get.mockReturnValue(mockRefreshToken)

        const result = getRefreshToken()

        expect(Cookies.get).toHaveBeenCalledWith('Admin-Refresh-Token')
        expect(result).toBe(mockRefreshToken)
      })

      it('should return undefined when no refresh token exists', () => {
        Cookies.get.mockReturnValue(undefined)

        const result = getRefreshToken()

        expect(result).toBeUndefined()
      })
    })

    describe('setRefreshToken', () => {
      it('should set refresh token in cookies with 30 days expiry', () => {
        const refreshToken = 'new-refresh-token'
        Cookies.set.mockReturnValue(true)

        const result = setRefreshToken(refreshToken)

        expect(Cookies.set).toHaveBeenCalledWith('Admin-Refresh-Token', refreshToken, { expires: 30 })
        expect(result).toBe(true)
      })

      it('should handle empty refresh token', () => {
        const refreshToken = ''
        Cookies.set.mockReturnValue(true)

        setRefreshToken(refreshToken)

        expect(Cookies.set).toHaveBeenCalledWith('Admin-Refresh-Token', '', { expires: 30 })
      })
    })
  })

  describe('Authentication Status', () => {
    describe('isLoggedIn', () => {
      it('should return true when token exists', () => {
        Cookies.get.mockReturnValue('valid-token')

        const result = isLoggedIn()

        expect(Cookies.get).toHaveBeenCalledWith('Admin-Token')
        expect(result).toBe(true)
      })

      it('should return false when token does not exist', () => {
        Cookies.get.mockReturnValue(undefined)

        const result = isLoggedIn()

        expect(result).toBe(false)
      })

      it('should return false when token is empty string', () => {
        Cookies.get.mockReturnValue('')

        const result = isLoggedIn()

        expect(result).toBe(false)
      })

      it('should return false when token is null', () => {
        Cookies.get.mockReturnValue(null)

        const result = isLoggedIn()

        expect(result).toBe(false)
      })
    })
  })

  describe('Clear Authentication', () => {
    describe('clearAuth', () => {
      beforeEach(() => {
        // 设置一些初始数据
        localStorage.setItem('userInfo', JSON.stringify({ id: 1, username: 'test' }))
        localStorage.setItem('permissions', JSON.stringify(['admin']))
        sessionStorage.setItem('tempData', 'temporary')
      })

      it('should clear all authentication data', () => {
        const removeTokenSpy = vi.spyOn(localStorage, 'removeItem')
        const sessionClearSpy = vi.spyOn(sessionStorage, 'clear')

        clearAuth()

        // 验证tokens被移除
        expect(Cookies.remove).toHaveBeenCalledWith('Admin-Token')
        expect(Cookies.remove).toHaveBeenCalledWith('Admin-Refresh-Token')

        // 验证localStorage中的userInfo被移除
        expect(removeTokenSpy).toHaveBeenCalledWith('userInfo')

        // 验证sessionStorage被清空
        expect(sessionClearSpy).toHaveBeenCalled()
      })

      it('should handle errors when clearing storage', () => {
        // 模拟localStorage.removeItem出错
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
        localStorage.removeItem = vi.fn().mockImplementation(() => {
          throw new Error('Storage error')
        })

        // 函数不应该抛出错误，即使底层存储操作失败
        expect(() => clearAuth()).not.toThrow()

        consoleErrorSpy.mockRestore()
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle cookie operations when cookies are disabled', () => {
      // 模拟cookies被禁用
      Cookies.get.mockImplementation(() => {
        throw new Error('Cookies disabled')
      })

      expect(() => getToken()).toThrow('Cookies disabled')
    })

    it('should handle setting cookies when storage is full', () => {
      Cookies.set.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })

      expect(() => setToken('token')).toThrow('Storage quota exceeded')
    })

    it('should handle removing non-existent cookies', () => {
      // 模拟移除不存在的cookie不会出错
      Cookies.remove.mockReturnValue(undefined)

      expect(() => removeToken()).not.toThrow()
    })
  })

  describe('Token Validation', () => {
    it('should handle various token formats', () => {
      const validTokens = [
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        'simple-token-123',
        'Bearer token123',
        'preview-mode-token-1234567890'
      ]

      validTokens.forEach(token => {
        Cookies.get.mockReturnValue(token)
        expect(isLoggedIn()).toBe(true)
      })
    })

    it('should handle malformed tokens', () => {
      const malformedTokens = [
        'malformed.jwt.token',
        'invalid-base64-',
        'too.many.dots.in.token'
      ]

      malformedTokens.forEach(token => {
        Cookies.get.mockReturnValue(token)
        // 即使token格式不正确，只要存在就认为已登录
        // 实际的token验证应该在服务器端进行
        expect(isLoggedIn()).toBe(true)
      })
    })
  })

  describe('Security Considerations', () => {
    it('should use secure cookie settings in production', () => {
      // 模拟生产环境
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      const token = 'secure-token'
      setToken(token)

      // 在生产环境中应该使用安全的cookie设置
      expect(Cookies.set).toHaveBeenCalledWith('Admin-Token', token, { expires: 7 })
      // 注意：实际应用中应该包含secure和httpOnly等安全选项

      process.env.NODE_ENV = originalEnv
    })

    it('should handle XSS prevention', () => {
      const xssToken = '<script>alert("xss")</script>'
      
      Cookies.get.mockReturnValue(xssToken)
      
      const result = getToken()
      
      // 获取的token应该被原样返回，XSS防护应该在使用时处理
      expect(result).toBe(xssToken)
    })
  })

  describe('Token Expiry', () => {
    it('should set appropriate expiry times', () => {
      const token = 'test-token'
      const refreshToken = 'refresh-token'

      setToken(token)
      setRefreshToken(refreshToken)

      expect(Cookies.set).toHaveBeenCalledWith('Admin-Token', token, { expires: 7 })
      expect(Cookies.set).toHaveBeenCalledWith('Admin-Refresh-Token', refreshToken, { expires: 30 })
    })

    it('should handle different token lengths', () => {
      const tokens = [
        'a', // 单字符
        'short-token',
        'medium-length-token-with-some-data',
        'very-long-token-with-lots-of-data-that-might-be-used-in-some-systems-' + 'x'.repeat(1000)
      ]

      tokens.forEach(token => {
        expect(() => setToken(token)).not.toThrow()
        expect(Cookies.set).toHaveBeenCalledWith('Admin-Token', token, { expires: 7 })
      })
    })
  })
})