import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 性能监控 Composable
 * 用于监控应用性能指标
 */
export function usePerformanceMonitor() {
  const performanceData = ref({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    navigationTiming: null
  })

  const isSupported = ref(false)

  // 检查浏览器支持
  const checkSupport = () => {
    isSupported.value = !!(
      window.performance &&
      window.performance.timing &&
      window.performance.memory
    )
  }

  // 获取页面加载时间
  const getLoadTime = () => {
    if (!isSupported.value) return 0
    
    const timing = window.performance.timing
    return timing.loadEventEnd - timing.navigationStart
  }

  // 获取渲染时间
  const getRenderTime = () => {
    if (!isSupported.value) return 0
    
    const timing = window.performance.timing
    return timing.domContentLoadedEventEnd - timing.domLoading
  }

  // 获取内存使用情况
  const getMemoryUsage = () => {
    if (!isSupported.value || !window.performance.memory) return 0
    
    return Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024)
  }

  // 更新性能数据
  const updatePerformanceData = () => {
    if (!isSupported.value) return

    performanceData.value = {
      loadTime: getLoadTime(),
      renderTime: getRenderTime(),
      memoryUsage: getMemoryUsage(),
      navigationTiming: window.performance.timing
    }
  }

  // 记录性能指标
  const recordMetric = (name, value) => {
    if (window.performance && window.performance.mark) {
      window.performance.mark(`${name}-${value}`)
    }
  }

  // 测量函数执行时间
  const measureFunction = async (fn, name = 'function') => {
    const startTime = performance.now()
    const result = await fn()
    const endTime = performance.now()
    
    recordMetric(name, endTime - startTime)
    
    return {
      result,
      duration: endTime - startTime
    }
  }

  // 获取页面性能条目
  const getPerformanceEntries = (type = 'navigation') => {
    if (!window.performance || !window.performance.getEntriesByType) {
      return []
    }
    
    return window.performance.getEntriesByType(type)
  }

  // 监控长任务
  const monitorLongTasks = () => {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (entry.duration > 50) {
              console.warn('Long task detected:', entry)
            }
          })
        })
        
        observer.observe({ entryTypes: ['longtask'] })
        return observer
      } catch (error) {
        console.warn('Long task monitoring not supported:', error)
      }
    }
    return null
  }

  let performanceObserver = null

  onMounted(() => {
    checkSupport()
    
    // 延迟更新性能数据，确保页面完全加载
    setTimeout(() => {
      updatePerformanceData()
    }, 1000)

    // 启动长任务监控
    performanceObserver = monitorLongTasks()
  })

  onUnmounted(() => {
    if (performanceObserver) {
      performanceObserver.disconnect()
    }
  })

  return {
    performanceData,
    isSupported,
    updatePerformanceData,
    recordMetric,
    measureFunction,
    getPerformanceEntries,
    getLoadTime,
    getRenderTime,
    getMemoryUsage
  }
}
