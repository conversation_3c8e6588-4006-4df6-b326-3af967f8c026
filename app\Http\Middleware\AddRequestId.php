<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * 添加请求ID中间件
 * 为每个请求添加唯一的请求ID，便于跟踪和调试
 */
class AddRequestId
{
    /**
     * 处理请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 如果请求中已经有X-Request-ID，则使用它
        $requestId = $request->header('X-Request-ID');
        
        // 否则生成一个新的请求ID
        if (!$requestId) {
            $requestId = $this->generateRequestId();
            $request->headers->set('X-Request-ID', $requestId);
        }
        
        // 处理请求
        $response = $next($request);
        
        // 在响应中添加请求ID
        $response->header('X-Request-ID', $requestId);
        
        return $response;
    }
    
    /**
     * 生成唯一的请求ID
     *
     * @return string
     */
    protected function generateRequestId()
    {
        // 生成UUID v4
        $uuid = Str::uuid()->toString();
        
        // 添加时间戳前缀，便于排序和查找
        $timestamp = dechex(time());
        
        return $timestamp . '-' . $uuid;
    }
}