/**
 * 增强的数据权限控制工具（第一阶段优化）
 * 专门处理数据大屏和API请求的权限控制
 */

import { useUserStore } from '@/stores/user'
import { roleHierarchy } from '@/config/navigation'
import { 
  filterDataByPermission, 
  getDashboardScope, 
  getFinanceScope,
  canCreateGroup,
  canExportData
} from './dataPermission'

/**
 * 数据大屏权限控制器
 * 根据用户角色返回相应的数据大屏配置
 */
export class DashboardPermissionController {
  constructor(userRole, userId) {
    this.userRole = userRole
    this.userId = userId
    this.dashboardScope = getDashboardScope(userRole)
    this.financeScope = getFinanceScope(userRole)
  }

  /**
   * 获取用户可访问的数据大屏模块
   * @returns {Array} 可访问的模块列表
   */
  getAccessibleModules() {
    const baseModules = ['overview', 'realtime']
    
    switch (this.dashboardScope) {
      case 'global':
        return [...baseModules, 'users', 'orders', 'finance', 'groups', 'system']
      case 'substation':
        return [...baseModules, 'users', 'orders', 'finance', 'groups']
      case 'agent_team':
        return [...baseModules, 'team', 'commission', 'performance']
      case 'distributor_personal':
        return [...baseModules, 'customers', 'commission', 'groups']
      case 'group_owner_groups':
        return [...baseModules, 'groups', 'members', 'income']
      case 'user_personal':
        return ['overview', 'orders', 'consumption']
      default:
        return ['overview']
    }
  }

  /**
   * 过滤数据大屏数据
   * @param {Object} rawData - 原始数据
   * @returns {Object} 过滤后的数据
   */
  filterDashboardData(rawData) {
    if (!rawData) return {}

    const filteredData = { ...rawData }

    // 根据权限范围过滤数据
    switch (this.dashboardScope) {
      case 'global':
        // 管理员可以看到所有数据
        break
      case 'substation':
        filteredData = this.filterSubstationData(filteredData)
        break
      case 'agent_team':
        filteredData = this.filterAgentTeamData(filteredData)
        break
      case 'distributor_personal':
        filteredData = this.filterDistributorData(filteredData)
        break
      case 'group_owner_groups':
        filteredData = this.filterGroupOwnerData(filteredData)
        break
      case 'user_personal':
        filteredData = this.filterUserPersonalData(filteredData)
        break
    }

    return filteredData
  }

  /**
   * 过滤分站级数据
   */
  filterSubstationData(data) {
    return {
      ...data,
      scope: 'substation',
      userId: this.userId,
      // 只显示本分站及下级的数据
      userStats: this.filterStatsByHierarchy(data.userStats, ['substation', 'agent', 'distributor', 'group_owner', 'user']),
      orderStats: this.filterOrdersBySubstation(data.orderStats),
      financeStats: this.filterFinanceBySubstation(data.financeStats)
    }
  }

  /**
   * 过滤代理商团队数据
   */
  filterAgentTeamData(data) {
    return {
      ...data,
      scope: 'agent_team',
      userId: this.userId,
      // 只显示代理商团队的数据
      teamStats: this.filterStatsByHierarchy(data.userStats, ['agent', 'distributor', 'group_owner', 'user']),
      commissionStats: this.filterCommissionByAgent(data.financeStats),
      performanceStats: this.filterPerformanceByAgent(data.performanceStats)
    }
  }

  /**
   * 过滤分销员个人数据
   */
  filterDistributorData(data) {
    return {
      ...data,
      scope: 'distributor_personal',
      userId: this.userId,
      // 只显示分销员个人和客户数据
      customerStats: this.filterCustomersByDistributor(data.userStats),
      commissionStats: this.filterCommissionByDistributor(data.financeStats),
      groupStats: this.filterGroupsByDistributor(data.groupStats)
    }
  }

  /**
   * 过滤群主群组数据
   */
  filterGroupOwnerData(data) {
    return {
      ...data,
      scope: 'group_owner_groups',
      userId: this.userId,
      // 只显示群主自己的群组数据
      groupStats: this.filterGroupsByOwner(data.groupStats),
      memberStats: this.filterMembersByOwner(data.userStats),
      incomeStats: this.filterIncomeByOwner(data.financeStats)
    }
  }

  /**
   * 过滤用户个人数据
   */
  filterUserPersonalData(data) {
    return {
      ...data,
      scope: 'user_personal',
      userId: this.userId,
      // 只显示用户个人数据
      orderStats: this.filterOrdersByUser(data.orderStats),
      consumptionStats: this.filterConsumptionByUser(data.financeStats)
    }
  }

  // 辅助过滤方法
  filterStatsByHierarchy(stats, allowedRoles) {
    if (!stats) return {}
    const filtered = {}
    Object.keys(stats).forEach(key => {
      if (allowedRoles.some(role => key.includes(role)) || key === 'total') {
        filtered[key] = stats[key]
      }
    })
    return filtered
  }

  filterOrdersBySubstation(orders) {
    // 实现分站订单过滤逻辑
    return orders
  }

  filterFinanceBySubstation(finance) {
    // 实现分站财务过滤逻辑
    return finance
  }

  filterCommissionByAgent(finance) {
    // 实现代理商佣金过滤逻辑
    return finance
  }

  filterPerformanceByAgent(performance) {
    // 实现代理商绩效过滤逻辑
    return performance
  }

  filterCustomersByDistributor(customers) {
    // 实现分销员客户过滤逻辑
    return customers
  }

  filterCommissionByDistributor(commission) {
    // 实现分销员佣金过滤逻辑
    return commission
  }

  filterGroupsByDistributor(groups) {
    // 实现分销员群组过滤逻辑
    return groups
  }

  filterGroupsByOwner(groups) {
    // 实现群主群组过滤逻辑
    return groups
  }

  filterMembersByOwner(members) {
    // 实现群主成员过滤逻辑
    return members
  }

  filterIncomeByOwner(income) {
    // 实现群主收入过滤逻辑
    return income
  }

  filterOrdersByUser(orders) {
    // 实现用户订单过滤逻辑
    return orders
  }

  filterConsumptionByUser(consumption) {
    // 实现用户消费过滤逻辑
    return consumption
  }
}

/**
 * API请求权限中间件
 * 自动为API请求添加权限参数
 */
export class APIPermissionMiddleware {
  constructor() {
    this.userStore = useUserStore()
  }

  /**
   * 增强API请求配置
   * @param {Object} config - 原始请求配置
   * @param {Object} options - 权限选项
   * @returns {Object} 增强后的请求配置
   */
  enhanceRequest(config, options = {}) {
    const userRole = this.userStore.userInfo?.role
    const userId = this.userStore.userInfo?.id

    if (!userRole || !userId) {
      return config
    }

    const enhancedConfig = { ...config }

    // 添加权限参数
    if (!enhancedConfig.params) {
      enhancedConfig.params = {}
    }

    // 添加用户权限信息
    enhancedConfig.params.user_role = userRole
    enhancedConfig.params.user_id = userId

    // 根据API类型添加特定权限参数
    if (options.type) {
      switch (options.type) {
        case 'dashboard':
          enhancedConfig.params.dashboard_scope = getDashboardScope(userRole)
          break
        case 'finance':
          enhancedConfig.params.finance_scope = getFinanceScope(userRole)
          break
        case 'export':
          if (!canExportData(userRole)) {
            throw new Error('没有数据导出权限')
          }
          break
        case 'group_create':
          if (!canCreateGroup(userRole)) {
            throw new Error('没有群组创建权限')
          }
          break
      }
    }

    // 添加数据过滤参数
    const viewableRoles = roleHierarchy[userRole]?.canViewRoles || [userRole]
    enhancedConfig.params.viewable_roles = viewableRoles.join(',')

    return enhancedConfig
  }

  /**
   * 检查API访问权限
   * @param {string} endpoint - API端点
   * @param {string} method - HTTP方法
   * @returns {boolean}
   */
  checkAPIAccess(endpoint, method = 'GET') {
    const userRole = this.userStore.userInfo?.role
    
    if (!userRole) return false

    // 定义API访问规则
    const apiRules = {
      '/api/dashboard': {
        GET: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      '/api/users': {
        GET: ['admin', 'substation', 'agent', 'distributor'],
        POST: ['admin', 'substation'],
        PUT: ['admin', 'substation'],
        DELETE: ['admin', 'substation']
      },
      '/api/groups': {
        GET: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        POST: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'], // 保护群组创建
        PUT: ['admin', 'substation', 'group_owner'],
        DELETE: ['admin', 'substation', 'group_owner']
      },
      '/api/orders': {
        GET: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        POST: ['admin', 'substation', 'user'],
        PUT: ['admin', 'substation'],
        DELETE: ['admin']
      },
      '/api/finance': {
        GET: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        POST: ['admin', 'substation'],
        PUT: ['admin', 'substation'],
        DELETE: ['admin']
      },
      '/api/export': {
        GET: ['admin', 'substation'] // 限制数据导出权限
      }
    }

    // 检查具体API规则
    for (const [pattern, methods] of Object.entries(apiRules)) {
      if (endpoint.startsWith(pattern)) {
        const allowedRoles = methods[method] || []
        return allowedRoles.includes(userRole)
      }
    }

    // 默认允许访问（对于未定义的API）
    return true
  }
}

/**
 * 群组创建权限保护装饰器
 * 确保群组创建功能对所有角色开放
 */
export function protectGroupCreation(originalFunction) {
  return function(...args) {
    const userStore = useUserStore()
    const userRole = userStore.userInfo?.role

    // 检查群组创建权限
    if (!canCreateGroup(userRole)) {
      throw new Error('当前角色没有群组创建权限')
    }

    // 执行原始函数
    return originalFunction.apply(this, args)
  }
}

/**
 * 数据访问权限验证器
 * 用于组件级别的权限控制
 */
export class ComponentPermissionValidator {
  constructor(userRole, userId) {
    this.userRole = userRole
    this.userId = userId
  }

  /**
   * 验证组件访问权限
   * @param {string} componentType - 组件类型
   * @param {Object} componentProps - 组件属性
   * @returns {boolean}
   */
  validateComponentAccess(componentType, componentProps = {}) {
    switch (componentType) {
      case 'UserList':
        return this.validateUserListAccess(componentProps)
      case 'GroupCreateForm':
        return canCreateGroup(this.userRole) // 保护群组创建
      case 'FinanceDashboard':
        return this.validateFinanceAccess(componentProps)
      case 'DataExport':
        return canExportData(this.userRole)
      case 'SystemSettings':
        return ['admin'].includes(this.userRole)
      default:
        return true
    }
  }

  validateUserListAccess(props) {
    const viewableRoles = roleHierarchy[this.userRole]?.canViewRoles || []
    return viewableRoles.length > 1 // 至少能查看除自己外的其他角色
  }

  validateFinanceAccess(props) {
    const financeScope = getFinanceScope(this.userRole)
    return financeScope !== 'none'
  }
}

// 导出单例实例
export const apiPermissionMiddleware = new APIPermissionMiddleware()

/**
 * 创建权限控制器实例
 * @param {string} userRole - 用户角色
 * @param {number} userId - 用户ID
 * @returns {Object} 权限控制器集合
 */
export function createPermissionControllers(userRole, userId) {
  return {
    dashboard: new DashboardPermissionController(userRole, userId),
    component: new ComponentPermissionValidator(userRole, userId),
    api: apiPermissionMiddleware
  }
}