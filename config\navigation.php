<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 导航系统配置
    |--------------------------------------------------------------------------
    |
    | 四域导航系统的核心配置参数
    |
    */

    /**
     * 四域配置
     */
    'domains' => [
        'business' => [
            'name' => '业务核心域',
            'description' => '核心业务功能',
            'color' => '#1890ff',
            'icon' => 'business',
            'sort_order' => 1,
            'enabled' => true,
        ],
        'operation' => [
            'name' => '运营管理域',
            'description' => '运营和管理功能',
            'color' => '#52c41a', 
            'icon' => 'operation',
            'sort_order' => 2,
            'enabled' => true,
        ],
        'analytics' => [
            'name' => '数据分析域',
            'description' => '数据统计和分析',
            'color' => '#f5222d',
            'icon' => 'analytics', 
            'sort_order' => 3,
            'enabled' => true,
        ],
        'system' => [
            'name' => '系统配置域',
            'description' => '系统设置和配置',
            'color' => '#722ed1',
            'icon' => 'system',
            'sort_order' => 4,
            'enabled' => true,
        ]
    ],

    /**
     * 缓存配置
     */
    'cache' => [
        'enabled' => env('NAVIGATION_CACHE_ENABLED', true),
        'prefix' => env('NAVIGATION_CACHE_PREFIX', 'navigation:'),
        'default_ttl' => env('NAVIGATION_CACHE_TTL', 3600),
        
        'ttl' => [
            'navigation_tree' => 3600,      // 导航树 - 1小时
            'user_preferences' => 1800,     // 用户偏好 - 30分钟
            'permissions' => 3600,          // 权限数据 - 1小时
            'search_results' => 900,        // 搜索结果 - 15分钟
            'recommendations' => 1800,      // 推荐数据 - 30分钟
            'analytics' => 1800,            // 统计数据 - 30分钟
            'popular_queries' => 3600,      // 热门搜索 - 1小时
            'real_time_stats' => 60,        // 实时统计 - 1分钟
        ],

        'tags' => [
            'navigation' => 'nav_structure',
            'user' => 'user_data', 
            'permissions' => 'permissions',
            'search' => 'search_data',
            'analytics' => 'analytics_data',
        ]
    ],

    /**
     * 搜索配置
     */
    'search' => [
        'enabled' => env('NAVIGATION_SEARCH_ENABLED', true),
        'default_limit' => 20,
        'max_limit' => 100,
        'suggestion_limit' => 10,
        
        'types' => [
            'exact' => '精确匹配',
            'fuzzy' => '模糊搜索',
            'semantic' => '语义搜索'
        ],

        'log_searches' => env('NAVIGATION_LOG_SEARCHES', true),
        'min_query_length' => 1,
        'max_query_length' => 200,
    ],

    /**
     * 推荐系统配置
     */
    'recommendations' => [
        'enabled' => env('NAVIGATION_RECOMMENDATIONS_ENABLED', true),
        'default_limit' => 10,
        'max_limit' => 30,
        
        'types' => [
            'frequent' => '基于频率',
            'collaborative' => '协同过滤', 
            'role_based' => '基于角色',
            'trending' => '热门趋势',
            'contextual' => '智能推荐',
            'mixed' => '综合推荐'
        ],

        'weights' => [
            'frequent' => 0.3,
            'collaborative' => 0.25,
            'role_based' => 0.25,
            'trending' => 0.2
        ],

        'min_interactions' => 3,    // 最小交互次数
        'similarity_threshold' => 0.3,  // 相似度阈值
    ],

    /**
     * 统计分析配置
     */
    'analytics' => [
        'enabled' => env('NAVIGATION_ANALYTICS_ENABLED', true),
        'real_time_enabled' => env('NAVIGATION_REAL_TIME_STATS', true),
        'retention_days' => env('NAVIGATION_DATA_RETENTION', 365),
        
        'aggregation_intervals' => [
            'real_time' => 60,      // 1分钟
            'hourly' => 3600,       // 1小时
            'daily' => 86400,       // 1天
            'weekly' => 604800,     // 7天
            'monthly' => 2592000,   // 30天
        ],

        'metrics' => [
            'visits' => '访问次数',
            'users' => '用户数',
            'duration' => '停留时间',
            'bounce_rate' => '跳出率',
            'conversion' => '转化率'
        ],

        'export' => [
            'enabled' => true,
            'formats' => ['json', 'csv', 'excel', 'pdf'],
            'max_records' => 100000,
        ]
    ],

    /**
     * 权限配置
     */
    'permissions' => [
        'cache_permissions' => true,
        'check_menu_permissions' => true,
        'inherit_parent_permissions' => true,
        
        'levels' => [
            'none' => '无权限',
            'read' => '只读',
            'limited' => '受限访问', 
            'full' => '完全访问'
        ],

        'default_role_permissions' => [
            'user' => ['business' => 'read'],
            'operator' => ['business' => 'limited', 'operation' => 'full'],
            'manager' => ['business' => 'full', 'operation' => 'read', 'analytics' => 'read'],
            'admin' => ['business' => 'full', 'operation' => 'full', 'analytics' => 'read', 'system' => 'limited'],
            'super_admin' => ['business' => 'full', 'operation' => 'full', 'analytics' => 'full', 'system' => 'full']
        ]
    ],

    /**
     * 性能配置
     */
    'performance' => [
        'enable_query_caching' => true,
        'enable_eager_loading' => true,
        'pagination_size' => 50,
        'max_depth' => 5,           // 最大菜单层级
        
        'optimization' => [
            'preload_user_preferences' => true,
            'batch_permission_checks' => true,
            'lazy_load_children' => true,
            'compress_responses' => true
        ],

        'monitoring' => [
            'track_response_times' => true,
            'alert_slow_queries' => true,
            'slow_query_threshold' => 1000,  // 毫秒
        ]
    ],

    /**
     * 安全配置
     */
    'security' => [
        'rate_limiting' => [
            'search' => '100,1',        // 每分钟100次搜索
            'preferences' => '30,1',     // 每分钟30次偏好更新
            'analytics' => '200,1',      // 每分钟200次分析请求
        ],

        'sanitization' => [
            'strip_html_tags' => true,
            'escape_special_chars' => true,
            'max_input_length' => 1000
        ],

        'audit' => [
            'log_permission_checks' => false,
            'log_sensitive_operations' => true,
            'retention_days' => 90
        ]
    ],

    /**
     * 国际化配置
     */
    'localization' => [
        'enabled' => env('NAVIGATION_I18N_ENABLED', false),
        'default_locale' => 'zh-CN',
        'supported_locales' => ['zh-CN', 'en-US'],
        'fallback_locale' => 'zh-CN'
    ],

    /**
     * API配置
     */
    'api' => [
        'version' => 'v1',
        'prefix' => 'api/navigation',
        'middleware' => ['api', 'throttle:api'],
        
        'response_format' => [
            'success_format' => 'standard',    // standard, simple
            'include_meta' => true,
            'include_debug_info' => env('APP_DEBUG', false)
        ],

        'pagination' => [
            'default_per_page' => 20,
            'max_per_page' => 100
        ]
    ],

    /**
     * 调试和开发配置
     */
    'debug' => [
        'enabled' => env('NAVIGATION_DEBUG', false),
        'log_level' => env('NAVIGATION_LOG_LEVEL', 'info'),
        'log_queries' => env('NAVIGATION_LOG_QUERIES', false),
        'profile_requests' => env('NAVIGATION_PROFILE_REQUESTS', false),
        
        'dev_tools' => [
            'show_cache_keys' => env('APP_DEBUG', false),
            'show_query_count' => env('APP_DEBUG', false),
            'show_render_time' => env('APP_DEBUG', false)
        ]
    ]
];