<script setup>
// 错误页面不需要认证
definePageMeta({
  layout: false
})

// 获取错误信息
const route = useRoute()
const router = useRouter()

const errorCode = computed(() => route.query.code || '500')
const errorMessage = computed(() => route.query.message || '服务器内部错误')

// 错误信息配置
const errorConfig = {
  '400': {
    title: '请求错误',
    description: '您的请求有误，请检查后重试',
    icon: '⚠️'
  },
  '401': {
    title: '未授权访问',
    description: '您需要登录后才能访问此页面',
    icon: '🔒'
  },
  '403': {
    title: '权限不足',
    description: '您没有权限访问此页面',
    icon: '🚫'
  },
  '404': {
    title: '页面未找到',
    description: '抱歉，您访问的页面不存在',
    icon: '🔍'
  },
  '500': {
    title: '服务器错误',
    description: '服务器出现了一些问题，请稍后重试',
    icon: '💥'
  },
  '503': {
    title: '服务不可用',
    description: '服务暂时不可用，请稍后重试',
    icon: '🔧'
  }
}

const currentError = computed(() => {
  return errorConfig[errorCode.value] || errorConfig['500']
})

// 操作方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const retry = () => {
  window.location.reload()
}

const goLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
    <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
      <!-- 错误图标 -->
      <div class="text-6xl mb-4">
        {{ currentError.icon }}
      </div>
      
      <!-- 错误代码 -->
      <div class="text-4xl font-bold text-gray-800 mb-2">
        {{ errorCode }}
      </div>
      
      <!-- 错误标题 -->
      <h1 class="text-xl font-semibold text-gray-700 mb-3">
        {{ currentError.title }}
      </h1>
      
      <!-- 错误描述 -->
      <p class="text-gray-600 mb-6">
        {{ errorMessage || currentError.description }}
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-3">
        <!-- 主要操作 -->
        <div class="flex space-x-3">
          <button
            @click="goHome"
            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            返回首页
          </button>
          
          <button
            @click="goBack"
            class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            返回上页
          </button>
        </div>
        
        <!-- 次要操作 -->
        <div class="flex space-x-3">
          <button
            @click="retry"
            class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            重新加载
          </button>
          
          <button
            v-if="errorCode === '401'"
            @click="goLogin"
            class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            去登录
          </button>
        </div>
      </div>
      
      <!-- 帮助信息 -->
      <div class="mt-6 pt-6 border-t border-gray-200">
        <p class="text-sm text-gray-500">
          如果问题持续存在，请联系技术支持
        </p>
        <p class="text-xs text-gray-400 mt-1">
          错误时间: {{ new Date().toLocaleString() }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 添加一些动画效果 */
.min-h-screen {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
