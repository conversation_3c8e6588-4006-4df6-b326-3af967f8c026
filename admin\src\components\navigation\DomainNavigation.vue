<!-- 四域导航组件 - 基于新架构重构的导航系统 -->
<!-- admin/src/components/navigation/DomainNavigation.vue -->

<template>
  <div class="domain-navigation" :class="{ collapsed: collapsed }">
    <!-- 增强搜索面板 -->
    <div class="nav-search enhanced" v-if="!collapsed">
      <div class="search-input-container">
        <el-input
          ref="searchInput"
          v-model="searchQuery"
          placeholder="搜索功能、快速跳转..."
          :prefix-icon="Search"
          size="default"
          clearable
          @input="handleAdvancedSearch"
          @focus="showSearchResults = true"
          @keydown.enter="handleSearchEnter"
          @keydown.up="handleSearchNavigation(-1)"
          @keydown.down="handleSearchNavigation(1)"
          @keydown.esc="hideSearchResults"
          class="modern-search-input"
        >
          <template #suffix>
            <div class="search-shortcuts">
              <el-tooltip content="使用 Ctrl+K 快速搜索" placement="top">
                <span class="shortcut-hint">⌘K</span>
              </el-tooltip>
            </div>
          </template>
        </el-input>
        
        <!-- 搜索结果面板 -->
        <transition name="search-results-fade">
          <div v-if="showSearchResults && (searchQuery || recentSearches.length)" class="search-results-panel">
            <!-- 搜索建议 -->
            <div v-if="searchQuery && searchSuggestions.length" class="search-section">
              <div class="section-title">
                <el-icon><Search /></el-icon>
                搜索结果
              </div>
              <div class="suggestion-list">
                <div 
                  v-for="(suggestion, index) in searchSuggestions" 
                  :key="suggestion.id"
                  :class="['suggestion-item', { active: selectedSuggestionIndex === index }]"
                  @click="selectSuggestion(suggestion)"
                  @mouseenter="selectedSuggestionIndex = index"
                >
                  <div class="suggestion-icon">
                    <el-icon><component :is="suggestion.icon" /></el-icon>
                  </div>
                  <div class="suggestion-content">
                    <div class="suggestion-title" v-html="highlightMatch(suggestion.title, searchQuery)"></div>
                    <div class="suggestion-path">{{ suggestion.path }}</div>
                  </div>
                  <div class="suggestion-type">{{ suggestion.type }}</div>
                </div>
              </div>
            </div>
            
            <!-- 最近访问 -->
            <div v-if="!searchQuery && recentItems.length" class="search-section">
              <div class="section-title">
                <el-icon><Clock /></el-icon>
                最近访问
              </div>
              <div class="recent-list">
                <div 
                  v-for="item in recentItems" 
                  :key="item.path"
                  class="recent-item"
                  @click="navigateToItem(item)"
                >
                  <div class="recent-icon">
                    <el-icon><component :is="item.icon" /></el-icon>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">{{ item.title }}</div>
                    <div class="recent-time">{{ formatRecentTime(item.lastVisit) }}</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 搜索历史 -->
            <div v-if="!searchQuery && recentSearches.length" class="search-section">
              <div class="section-title">
                <el-icon><Clock /></el-icon>
                搜索历史
                <el-button 
                  type="text" 
                  size="small" 
                  @click="clearSearchHistory"
                  class="clear-history-btn"
                >
                  清除
                </el-button>
              </div>
              <div class="history-list">
                <div 
                  v-for="search in recentSearches" 
                  :key="search.query"
                  class="history-item"
                  @click="searchQuery = search.query; handleAdvancedSearch(search.query)"
                >
                  <el-icon><Search /></el-icon>
                  <span>{{ search.query }}</span>
                  <small>{{ formatRecentTime(search.timestamp) }}</small>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
      
      <!-- 搜索过滤器 -->
      <div class="search-filters" v-if="showAdvancedFilters">
        <div class="filter-chips">
          <el-tag
            v-for="filter in activeFilters"
            :key="filter.key"
            closable
            @close="removeFilter(filter.key)"
            :type="filter.type || 'primary'"
            size="small"
          >
            {{ filter.label }}
          </el-tag>
        </div>
        <el-dropdown @command="addFilter">
          <el-button type="primary" text size="small">
            添加过滤器 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="domain">按域分类</el-dropdown-item>
              <el-dropdown-item command="role">按角色权限</el-dropdown-item>
              <el-dropdown-item command="recent">最近使用</el-dropdown-item>
              <el-dropdown-item command="favorite">收藏夹</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 域分组导航 -->
    <div class="navigation-domains">
      <div 
        v-for="domain in filteredDomains" 
        :key="domain.key" 
        class="nav-domain"
        :class="{ 'domain-active': activeDomain === domain.key }"
      >
        <!-- 域标题 -->
        <div 
          class="domain-header"
          @click="toggleDomain(domain.key)"
          :title="collapsed ? domain.title : ''"
        >
          <div class="domain-icon" :style="{ color: domain.color }">
            <el-icon><component :is="domain.icon" /></el-icon>
          </div>
          <div class="domain-info" v-if="!collapsed">
            <span class="domain-title">{{ domain.title }}</span>
            <span class="domain-desc">{{ domain.description }}</span>
          </div>
          <div class="domain-indicator" v-if="!collapsed">
            <el-icon v-if="expandedDomains.includes(domain.key)">
              <ArrowDown />
            </el-icon>
            <el-icon v-else>
              <ArrowRight />
            </el-icon>
          </div>
        </div>

        <!-- 域模块列表 -->
        <transition name="domain-expand">
          <div 
            v-show="expandedDomains.includes(domain.key) || collapsed"
            class="domain-modules"
          >
            <div
              v-for="module in domain.modules"
              :key="module.key"
              class="module-item"
              :class="{ 
                'module-active': isModuleActive(module),
                'module-favorite': isFavoriteModule(module),
                'module-recent': isRecentModule(module)
              }"
            >
              <!-- 模块标题 -->
              <div 
                class="module-header"
                @click="handleModuleClick(module)"
                @contextmenu.prevent="showModuleContextMenu($event, module)"
                :title="collapsed ? module.title : ''"
              >
                <div class="module-icon">
                  <el-icon><component :is="module.icon" /></el-icon>
                </div>
                <div class="module-content" v-if="!collapsed">
                  <span class="module-title">{{ module.title }}</span>
                  
                  <!-- 个性化标识 -->
                  <div class="module-indicators">
                    <el-icon 
                      v-if="isFavoriteModule(module)" 
                      class="favorite-indicator" 
                      title="已收藏"
                    >
                      <StarFilled />
                    </el-icon>
                    <el-badge 
                      v-if="getModuleBadge(module)" 
                      :value="getModuleBadge(module)" 
                      class="module-badge"
                    />
                  </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="module-actions" v-if="!collapsed">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click.stop="toggleFavorite(module)"
                    class="favorite-btn"
                    :class="{ active: isFavoriteModule(module) }"
                  >
                    <el-icon>
                      <component :is="isFavoriteModule(module) ? 'StarFilled' : 'Star'" />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 模块子页面 -->
              <div v-if="!collapsed && module.children && module.children.length" class="module-children">
                <router-link
                  v-for="child in module.children"
                  :key="child.path"
                  :to="child.path"
                  class="child-item"
                  :class="{ 'child-active': $route.path === child.path }"
                >
                  <el-icon class="child-icon">
                    <component :is="child.icon" />
                  </el-icon>
                  <span class="child-title">{{ child.title }}</span>
                </router-link>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <!-- 折叠状态下的Tooltip -->
    <template v-if="collapsed">
      <el-tooltip
        v-for="domain in filteredDomains"
        :key="`tooltip-${domain.key}`"
        :content="domain.title"
        placement="right"
        :offset="10"
      >
        <template #reference>
          <div class="collapsed-domain-ref" :data-domain="domain.key"></div>
        </template>
        <div class="domain-tooltip-content">
          <h4>{{ domain.title }}</h4>
          <div class="tooltip-modules">
            <div 
              v-for="module in domain.modules" 
              :key="module.key"
              class="tooltip-module"
              @click="handleModuleClick(module)"
            >
              <el-icon><component :is="module.icon" /></el-icon>
              <span>{{ module.title }}</span>
            </div>
          </div>
        </div>
      </el-tooltip>
    </template>

    <!-- 个性化快捷操作面板 -->
    <div class="quick-actions enhanced" v-if="!collapsed">
      <!-- 收藏夹 -->
      <div v-if="favoriteItems.length" class="favorites-section">
        <div class="section-title">
          <el-icon><StarFilled /></el-icon>
          我的收藏
          <el-button type="text" size="small" @click="showAllFavorites = !showAllFavorites">
            {{ showAllFavorites ? '收起' : '全部' }}
          </el-button>
        </div>
        <div class="favorites-grid" v-show="showAllFavorites || favoriteItems.length <= 4">
          <div 
            v-for="favorite in (showAllFavorites ? favoriteItems : favoriteItems.slice(0, 4))" 
            :key="favorite.key"
            class="favorite-item"
            @click="navigateToFavorite(favorite)"
          >
            <el-icon><component :is="favorite.icon" /></el-icon>
            <span>{{ favorite.title }}</span>
          </div>
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div class="actions-section">
        <div class="quick-actions-title">
          <el-icon><Plus /></el-icon>
          快捷操作
        </div>
        <div class="quick-actions-grid">
          <div 
            v-for="action in quickActions" 
            :key="action.key"
            class="quick-action-item"
            @click="handleQuickAction(action)"
          >
            <el-icon><component :is="action.icon" /></el-icon>
            <span>{{ action.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  generateUserNavigation, 
  getRouteDomainInfo,
  NAVIGATION_DOMAINS 
} from '@/config/navigation-domains'
import {
  Search,
  ArrowDown,
  ArrowRight,
  Plus,
  Setting,
  Document,
  DataLine,
  Bell,
  Clock,
  Star,
  StarFilled,
  Filter,
  More,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const searchQuery = ref('')
const expandedDomains = ref([])
const activeDomain = ref('')
const filteredDomains = ref([])

// 搜索增强功能
const showSearchResults = ref(false)
const searchSuggestions = ref([])
const selectedSuggestionIndex = ref(-1)
const recentSearches = ref(JSON.parse(localStorage.getItem('navigation-recent-searches') || '[]'))
const recentItems = ref(JSON.parse(localStorage.getItem('navigation-recent-items') || '[]'))
const searchInput = ref(null)

// 个性化功能
const favoriteItems = ref(JSON.parse(localStorage.getItem('navigation-favorites') || '[]'))
const personalizedDomains = ref([])
const showAdvancedFilters = ref(false)
const activeFilters = ref([])
const showAllFavorites = ref(false)

// 计算属性
const userRole = computed(() => userStore.userInfo?.role || 'user')

const quickActions = computed(() => [
  { key: 'add-group', title: '创建群组', icon: 'Plus', path: '/community/add' },
  { key: 'data-export', title: '数据导出', icon: 'Document', path: '/system/export' },
  { key: 'system-monitor', title: '系统监控', icon: 'DataLine', path: '/system/monitor' },
  { key: 'notifications', title: '通知中心', icon: 'Bell', path: '/system/notifications' }
])

// 方法
const initNavigation = () => {
  const userNavigation = generateUserNavigation(userRole.value)
  filteredDomains.value = userNavigation
  
  // 默认展开第一个域
  if (userNavigation.length > 0) {
    expandedDomains.value = [userNavigation[0].key]
    activeDomain.value = userNavigation[0].key
  }
  
  // 根据当前路由设置激活状态
  updateActiveState()
}

// 增强搜索功能
const handleAdvancedSearch = (query) => {
  if (!query.trim()) {
    searchSuggestions.value = []
    initNavigation()
    return
  }
  
  // 生成搜索建议
  generateSearchSuggestions(query)
  
  // 过滤导航项
  const userNavigation = generateUserNavigation(userRole.value)
  const filtered = userNavigation.map(domain => ({
    ...domain,
    modules: domain.modules.filter(module => 
      module.title.toLowerCase().includes(query.toLowerCase()) ||
      module.children?.some(child => 
        child.title.toLowerCase().includes(query.toLowerCase())
      )
    )
  })).filter(domain => domain.modules.length > 0)
  
  filteredDomains.value = filtered
  expandedDomains.value = filtered.map(domain => domain.key)
}

const generateSearchSuggestions = (query) => {
  const userNavigation = generateUserNavigation(userRole.value)
  const suggestions = []
  
  userNavigation.forEach(domain => {
    domain.modules.forEach(module => {
      if (module.title.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push({
          id: `module-${module.key}`,
          title: module.title,
          path: module.path || domain.title,
          icon: module.icon,
          type: '模块',
          domain: domain.title,
          action: () => handleModuleClick(module)
        })
      }
      
      if (module.children) {
        module.children.forEach(child => {
          if (child.title.toLowerCase().includes(query.toLowerCase())) {
            suggestions.push({
              id: `child-${child.path}`,
              title: child.title,
              path: `${domain.title} > ${module.title}`,
              icon: child.icon,
              type: '页面',
              domain: domain.title,
              action: () => router.push(child.path)
            })
          }
        })
      }
    })
  })
  
  // 按相关性排序（精确匹配优先）
  suggestions.sort((a, b) => {
    const aExact = a.title.toLowerCase() === query.toLowerCase()
    const bExact = b.title.toLowerCase() === query.toLowerCase()
    if (aExact && !bExact) return -1
    if (!aExact && bExact) return 1
    return a.title.localeCompare(b.title)
  })
  
  searchSuggestions.value = suggestions.slice(0, 8)
}

const handleSearchEnter = () => {
  if (selectedSuggestionIndex.value >= 0 && searchSuggestions.value[selectedSuggestionIndex.value]) {
    selectSuggestion(searchSuggestions.value[selectedSuggestionIndex.value])
  } else if (searchSuggestions.value.length > 0) {
    selectSuggestion(searchSuggestions.value[0])
  }
}

const handleSearchNavigation = (direction) => {
  const maxIndex = searchSuggestions.value.length - 1
  selectedSuggestionIndex.value += direction
  
  if (selectedSuggestionIndex.value > maxIndex) {
    selectedSuggestionIndex.value = 0
  } else if (selectedSuggestionIndex.value < 0) {
    selectedSuggestionIndex.value = maxIndex
  }
}

const selectSuggestion = (suggestion) => {
  if (suggestion.action) {
    suggestion.action()
  }
  
  // 记录搜索历史
  addToSearchHistory(searchQuery.value)
  
  // 隐藏搜索结果
  hideSearchResults()
  searchQuery.value = ''
}

const hideSearchResults = () => {
  showSearchResults.value = false
  selectedSuggestionIndex.value = -1
}

const highlightMatch = (text, query) => {
  if (!query) return text
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const toggleDomain = (domainKey) => {
  if (props.collapsed) return
  
  const index = expandedDomains.value.indexOf(domainKey)
  if (index > -1) {
    expandedDomains.value.splice(index, 1)
  } else {
    expandedDomains.value.push(domainKey)
  }
  
  activeDomain.value = domainKey
}

const isModuleActive = (module) => {
  const routeInfo = getRouteDomainInfo(route.path)
  return routeInfo && routeInfo.moduleKey === module.key
}

// 个性化判断方法
const isFavoriteModule = (module) => {
  return favoriteItems.value.some(fav => fav.key === module.key)
}

const isRecentModule = (module) => {
  return recentItems.value.some(recent => recent.path === module.path)
}

const toggleFavorite = (module) => {
  const index = favoriteItems.value.findIndex(fav => fav.key === module.key)
  
  if (index >= 0) {
    favoriteItems.value.splice(index, 1)
  } else {
    favoriteItems.value.push({
      key: module.key,
      title: module.title,
      path: module.path,
      icon: module.icon,
      timestamp: Date.now()
    })
  }
  
  localStorage.setItem('navigation-favorites', JSON.stringify(favoriteItems.value))
}

const showModuleContextMenu = (event, module) => {
  // 这里可以实现右键菜单功能
  console.log('右键菜单', module)
}

const handleModuleClick = (module) => {
  if (module.path) {
    router.push(module.path)
  } else if (module.children && module.children.length > 0) {
    router.push(module.children[0].path)
  }
}

const getModuleBadge = (module) => {
  // 这里可以根据实际业务需求返回徽章数字
  // 例如：未读消息数、待处理任务数等
  const badges = {
    'system-notifications': 3,
    'order-management': 5,
    'security-management': 1
  }
  
  return badges[module.key] || null
}

const handleQuickAction = (action) => {
  if (action.path) {
    router.push(action.path)
    addToRecentItems(action)
  }
}

// 个性化功能方法
const addToSearchHistory = (query) => {
  if (!query.trim()) return
  
  const timestamp = Date.now()
  const existingIndex = recentSearches.value.findIndex(item => item.query === query)
  
  if (existingIndex >= 0) {
    recentSearches.value.splice(existingIndex, 1)
  }
  
  recentSearches.value.unshift({ query, timestamp })
  recentSearches.value = recentSearches.value.slice(0, 10) // 保持最近 10 条
  
  localStorage.setItem('navigation-recent-searches', JSON.stringify(recentSearches.value))
}

const addToRecentItems = (item) => {
  const timestamp = Date.now()
  const existingIndex = recentItems.value.findIndex(recent => recent.path === item.path)
  
  if (existingIndex >= 0) {
    recentItems.value.splice(existingIndex, 1)
  }
  
  recentItems.value.unshift({
    ...item,
    lastVisit: timestamp
  })
  
  recentItems.value = recentItems.value.slice(0, 8) // 保持最近 8 条
  
  localStorage.setItem('navigation-recent-items', JSON.stringify(recentItems.value))
}

const navigateToItem = (item) => {
  router.push(item.path)
  addToRecentItems(item)
  hideSearchResults()
}

const navigateToFavorite = (favorite) => {
  router.push(favorite.path)
  addToRecentItems(favorite)
}

const formatRecentTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const clearSearchHistory = () => {
  recentSearches.value = []
  localStorage.removeItem('navigation-recent-searches')
}

// 过滤器功能
const addFilter = (filterType) => {
  const filters = {
    domain: { key: 'domain', label: '按域分类', type: 'primary' },
    role: { key: 'role', label: '按角色权限', type: 'success' },
    recent: { key: 'recent', label: '最近使用', type: 'warning' },
    favorite: { key: 'favorite', label: '收藏夹', type: 'danger' }
  }
  
  const filter = filters[filterType]
  if (filter && !activeFilters.value.some(f => f.key === filter.key)) {
    activeFilters.value.push(filter)
    applyFilters()
  }
}

const removeFilter = (filterKey) => {
  activeFilters.value = activeFilters.value.filter(f => f.key !== filterKey)
  applyFilters()
}

const applyFilters = () => {
  // 这里实现过滤逻辑
  initNavigation()
}

// 系统快捷键支持
const setupKeyboardShortcuts = () => {
  const handleKeydown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault()
      if (searchInput.value) {
        searchInput.value.focus()
      }
    }
    
    if (e.key === 'Escape' && showSearchResults.value) {
      hideSearchResults()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  return () => {
    document.removeEventListener('keydown', handleKeydown)
  }
}

const updateActiveState = () => {
  const routeInfo = getRouteDomainInfo(route.path)
  if (routeInfo) {
    activeDomain.value = routeInfo.domainKey
    if (!expandedDomains.value.includes(routeInfo.domainKey)) {
      expandedDomains.value.push(routeInfo.domainKey)
    }
  }
}

// 监听
watch(() => route.path, () => {
  updateActiveState()
  // 记录访问项
  const currentRoute = route.matched[route.matched.length - 1]
  if (currentRoute?.meta?.title) {
    addToRecentItems({
      title: currentRoute.meta.title,
      path: route.path,
      icon: currentRoute.meta.icon || 'Document'
    })
  }
})

watch(() => userRole.value, () => {
  initNavigation()
})

// 点击外部隐藏搜索结果
watch(() => showSearchResults.value, (show) => {
  if (show) {
    nextTick(() => {
      const handleClickOutside = (e) => {
        const searchPanel = document.querySelector('.nav-search')
        if (searchPanel && !searchPanel.contains(e.target)) {
          showSearchResults.value = false
          document.removeEventListener('click', handleClickOutside)
        }
      }
      document.addEventListener('click', handleClickOutside)
    })
  }
})

// 生命周期
let cleanupKeyboardShortcuts

onMounted(() => {
  initNavigation()
  cleanupKeyboardShortcuts = setupKeyboardShortcuts()
})

onUnmounted(() => {
  if (cleanupKeyboardShortcuts) {
    cleanupKeyboardShortcuts()
  }
})
</script>

<style lang="scss" scoped>
.domain-navigation {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, transparent 100%);
    pointer-events: none;
    opacity: 0.3;
  }

  &.collapsed {
    .nav-search,
    .quick-actions {
      display: none;
    }
    
    .domain-header {
      justify-content: center;
      
      .domain-info,
      .domain-indicator {
        display: none;
      }
    }
    
    .module-header {
      justify-content: center;
      
      .module-title,
      .module-badge {
        display: none;
      }
    }
  }
}

// 增强搜索样式
.nav-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  
  &.enhanced {
    .search-input-container {
      position: relative;
      
      .modern-search-input {
        .el-input__wrapper {
          background: var(--bg-secondary);
          border: 2px solid var(--border-light);
          border-radius: var(--radius-lg);
          transition: all var(--duration-normal) var(--ease-out);
          
          &:hover {
            border-color: var(--border-medium);
          }
          
          &.is-focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
        
        .el-input__inner {
          font-size: var(--text-sm);
          color: var(--text-primary);
          
          &::placeholder {
            color: var(--text-light);
            font-style: italic;
          }
        }
      }
      
      .search-shortcuts {
        display: flex;
        align-items: center;
        padding-right: var(--spacing-xs);
        
        .shortcut-hint {
          font-size: 10px;
          color: var(--text-muted);
          background: var(--bg-muted);
          padding: 2px 4px;
          border-radius: var(--radius-sm);
          border: 1px solid var(--border-light);
          font-family: var(--font-family-mono, monospace);
        }
      }
    }
    
    .search-results-panel {
      position: absolute;
      top: calc(100% + 8px);
      left: 0;
      right: 0;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      z-index: var(--z-dropdown);
      max-height: 400px;
      overflow: hidden;
      
      .search-section {
        padding: var(--spacing-sm) 0;
        
        &:not(:last-child) {
          border-bottom: 1px solid var(--border-light);
        }
        
        .section-title {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-xs) var(--spacing-md);
          font-size: var(--text-xs);
          font-weight: var(--font-semibold);
          color: var(--text-muted);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          
          .clear-history-btn {
            margin-left: auto;
            font-size: var(--text-xs);
          }
        }
      }
      
      .suggestion-list,
      .recent-list,
      .history-list {
        max-height: 200px;
        overflow-y: auto;
      }
      
      .suggestion-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        cursor: pointer;
        transition: all var(--duration-fast) var(--ease-out);
        
        &:hover,
        &.active {
          background: var(--bg-secondary);
          transform: translateX(2px);
        }
        
        .suggestion-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--color-primary);
          background: rgba(59, 130, 246, 0.1);
          border-radius: var(--radius-sm);
          font-size: 14px;
        }
        
        .suggestion-content {
          flex: 1;
          min-width: 0;
          
          .suggestion-title {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            
            :deep(mark) {
              background: rgba(59, 130, 246, 0.2);
              color: var(--color-primary);
              padding: 1px 2px;
              border-radius: 2px;
            }
          }
          
          .suggestion-path {
            font-size: var(--text-xs);
            color: var(--text-muted);
            margin-top: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .suggestion-type {
          font-size: var(--text-xs);
          padding: 2px 6px;
          background: var(--bg-muted);
          color: var(--text-muted);
          border-radius: var(--radius-sm);
          font-weight: var(--font-medium);
        }
      }
      
      .recent-item,
      .history-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        cursor: pointer;
        transition: all var(--duration-fast) var(--ease-out);
        
        &:hover {
          background: var(--bg-secondary);
          transform: translateX(2px);
        }
        
        .recent-icon {
          width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--text-muted);
          font-size: 14px;
        }
        
        .recent-content {
          flex: 1;
          
          .recent-title {
            font-size: var(--text-sm);
            color: var(--text-primary);
            font-weight: var(--font-medium);
          }
          
          .recent-time {
            font-size: var(--text-xs);
            color: var(--text-light);
            margin-top: 1px;
          }
        }
        
        small {
          color: var(--text-light);
          font-size: var(--text-xs);
          margin-left: auto;
        }
      }
    }
    
    .search-filters {
      margin-top: var(--spacing-sm);
      padding-top: var(--spacing-sm);
      border-top: 1px solid var(--border-light);
      
      .filter-chips {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
        
        .el-tag {
          border-radius: var(--radius-full);
          font-size: var(--text-xs);
        }
      }
    }
  }
}

// 搜索结果动画
.search-results-fade-enter-active,
.search-results-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  transform-origin: top center;
}

.search-results-fade-enter-from,
.search-results-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.search-results-fade-enter-to,
.search-results-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.navigation-domains {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.nav-domain {
  margin-bottom: 8px;

  &.domain-active {
    .domain-header {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }
}

.domain-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 8px;

  &:hover {
    background: var(--el-fill-color-light);
  }

  .domain-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
  }

  .domain-info {
    flex: 1;

    .domain-title {
      display: block;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
    }

    .domain-desc {
      display: block;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 16px;
      margin-top: 2px;
    }
  }

  .domain-indicator {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    transition: transform 0.2s ease;
  }
}

.domain-expand-enter-active,
.domain-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.domain-expand-enter-from,
.domain-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.domain-expand-enter-to,
.domain-expand-leave-from {
  max-height: 500px;
  opacity: 1;
}

.domain-modules {
  padding-left: 16px;
}

.module-item {
  margin-bottom: 4px;

  &.module-active {
    .module-header {
      background: var(--el-color-primary);
      color: white;
    }
  }
}

.module-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--el-fill-color);
  }

  .module-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 16px;
  }

  .module-title {
    flex: 1;
    font-size: 13px;
    font-weight: 400;
  }

  .module-badge {
    margin-left: 8px;
  }
}

.module-children {
  margin-top: 4px;
  padding-left: 24px;
  border-left: 2px solid var(--el-border-color-lighter);
}

.child-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  color: var(--el-text-color-regular);
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background: var(--el-fill-color-light);
    color: var(--el-color-primary);
  }

  &.child-active {
    background: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
  }

  .child-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    font-size: 12px;
  }

  .child-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 现代化快速操作面板
.quick-actions {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: var(--spacing-md);
    right: var(--spacing-md);
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
    opacity: 0.3;
  }

  .quick-actions-title {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-bottom: 8px;
    font-weight: 500;
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left var(--duration-slow) var(--ease-out);
    }

    &:hover {
      background: var(--color-primary);
      border-color: var(--color-primary);
      color: white;
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      
      &::before {
        left: 100%;
      }
      
      .el-icon {
        color: white;
        transform: scale(1.1);
      }
      
      span {
        color: white;
      }
    }

    .el-icon {
      font-size: 18px;
      margin-bottom: var(--spacing-xs);
      color: var(--color-primary);
      transition: all var(--duration-normal) var(--ease-out);
    }

    span {
      font-size: 11px;
      color: var(--text-secondary);
      text-align: center;
      line-height: 1.2;
      font-weight: var(--font-medium);
      transition: all var(--duration-normal) var(--ease-out);
    }
  }
}

.collapsed-domain-ref {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
}

.domain-tooltip-content {
  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .tooltip-modules {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .tooltip-module {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;

    &:hover {
      background: var(--el-fill-color-light);
    }

    .el-icon {
      margin-right: 8px;
      font-size: 14px;
    }

    span {
      font-size: 12px;
    }
  }
}

// 现代化滚动条样式
.navigation-domains,
.search-results-panel .suggestion-list,
.search-results-panel .recent-list,
.search-results-panel .history-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-muted);
    border-radius: var(--radius-sm);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-sm);
    transition: background-color var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--border-dark);
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .nav-search.enhanced {
    padding: var(--spacing-sm);
    
    .search-results-panel {
      max-height: 300px;
    }
  }
  
  .quick-actions {
    padding: var(--spacing-sm);
    
    .quick-actions-grid,
    .favorites-grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-xs);
    }
  }
}

// 个性化动画关键帧
@keyframes sparkle {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.2); 
  }
}

@keyframes favoriteGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.6);
  }
}

@keyframes recentPulse {
  0%, 100% {
    border-color: var(--success-500);
  }
  50% {
    border-color: var(--success-400);
  }
}
</style>