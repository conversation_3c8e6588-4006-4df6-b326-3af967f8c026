<template>
  <div class="payment-config">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>支付配置</h1>
      <p class="page-description">
        配置您的支付通道参数，只能配置系统管理员授权的通道和字段
      </p>
    </div>

    <!-- 权限说明 -->
    <el-alert
      :title="getPermissionTip()"
      type="info"
      :closable="false"
      show-icon
      class="permission-tip"
    />

    <!-- 配置列表 -->
    <div class="config-list">
      <el-row :gutter="20">
        <el-col
          v-for="channel in configurableChannels"
          :key="channel.channel_code"
          :span="8"
        >
          <el-card class="channel-card" :class="{ 'configured': channel.is_configured }">
            <template #header>
              <div class="card-header">
                <div class="channel-info">
                  <img :src="channel.channel_icon" :alt="channel.channel_name" class="channel-icon" />
                  <span class="channel-name">{{ channel.channel_name }}</span>
                </div>
                <div class="channel-status">
                  <el-tag v-if="channel.is_configured" :type="channel.test_status ? 'success' : 'warning'">
                    {{ channel.test_status ? '已测试' : '未测试' }}
                  </el-tag>
                  <el-tag v-else type="info">未配置</el-tag>
                </div>
              </div>
            </template>

            <div class="card-content">
              <p class="channel-description">{{ channel.description }}</p>
              
              <div class="channel-details">
                <div class="detail-item">
                  <span class="label">手续费率:</span>
                  <span class="value">{{ (channel.fee_rate * 100).toFixed(2) }}%</span>
                </div>
                <div class="detail-item">
                  <span class="label">配置状态:</span>
                  <span class="value">
                    <el-tag :type="channel.config_status ? 'success' : 'danger'" size="small">
                      {{ channel.config_status ? '启用' : '禁用' }}
                    </el-tag>
                  </span>
                </div>
              </div>

              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="configureChannel(channel)"
                >
                  {{ channel.is_configured ? '修改配置' : '立即配置' }}
                </el-button>
                <el-button
                  v-if="channel.is_configured"
                  size="small"
                  @click="testChannel(channel)"
                  :loading="channel.testing"
                >
                  测试配置
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 无可配置通道提示 -->
      <el-empty
        v-if="configurableChannels.length === 0 && !loading"
        description="暂无可配置的支付通道"
      >
        <template #image>
          <el-icon size="60"><CreditCard /></el-icon>
        </template>
        <p>请联系系统管理员为您开通支付通道权限</p>
      </el-empty>
    </div>

    <!-- 配置对话框 -->
    <el-dialog
      v-model="configDialog.visible"
      :title="`配置 ${configDialog.channelName}`"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
      >
        <el-form-item label="配置名称" prop="config_name">
          <el-input
            v-model="configForm.config_name"
            placeholder="请输入配置名称"
          />
        </el-form-item>

        <!-- 动态生成配置字段 -->
        <el-form-item
          v-for="(field, key) in configDialog.template"
          :key="key"
          :label="field.label"
          :prop="`config_data.${key}`"
          :required="field.required"
        >
          <!-- 文本输入框 -->
          <el-input
            v-if="field.type === 'text'"
            v-model="configForm.config_data[key]"
            :placeholder="field.placeholder"
          />
          
          <!-- 密码输入框 -->
          <el-input
            v-else-if="field.type === 'password'"
            v-model="configForm.config_data[key]"
            type="password"
            :placeholder="field.placeholder"
            show-password
          />
          
          <!-- URL输入框 -->
          <el-input
            v-else-if="field.type === 'url'"
            v-model="configForm.config_data[key]"
            :placeholder="field.placeholder"
          />
          
          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="configForm.config_data[key]"
            type="textarea"
            :placeholder="field.placeholder"
            :rows="3"
          />

          <!-- 字段说明 -->
          <div v-if="field.help" class="field-help">
            <el-icon><InfoFilled /></el-icon>
            {{ field.help }}
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="configDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="configDialog.loading">
          保存配置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CreditCard, InfoFilled } from '@element-plus/icons-vue'
import { paymentConfigApi } from '@/api/payment'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const configurableChannels = ref([])

// 配置对话框
const configDialog = reactive({
  visible: false,
  loading: false,
  channelCode: '',
  channelName: '',
  template: {},
  allowedFields: {}
})

const configForm = reactive({
  config_name: '',
  config_data: {}
})

const configFormRef = ref()

// 表单验证规则
const configRules = computed(() => {
  const rules = {
    config_name: [
      { required: true, message: '请输入配置名称', trigger: 'blur' }
    ]
  }

  // 动态生成字段验证规则
  Object.keys(configDialog.template).forEach(key => {
    const field = configDialog.template[key]
    if (field.required) {
      rules[`config_data.${key}`] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
  })

  return rules
})

// 页面加载时获取数据
onMounted(() => {
  loadConfigurableChannels()
})

// 获取权限提示
const getPermissionTip = () => {
  const userType = userStore.userInfo?.user_type || 'user'
  const tips = {
    user: '普通用户只能配置基础信息字段',
    distributor: '分销商可以配置商户ID和密钥等核心参数',
    substation: '分站管理员可以配置完整的支付参数',
    admin: '系统管理员拥有所有配置权限'
  }
  return tips[userType] || tips.user
}

// 加载可配置通道
const loadConfigurableChannels = async () => {
  try {
    loading.value = true
    const response = await paymentConfigApi.getUserConfigurableChannels({
      user_type: userStore.userInfo?.user_type || 'user',
      user_id: userStore.userInfo?.id
    })
    
    configurableChannels.value = response.data.map(channel => ({
      ...channel,
      testing: false
    }))
  } catch (error) {
    ElMessage.error('加载支付通道失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 配置通道
const configureChannel = async (channel) => {
  try {
    // 获取配置模板
    const templateResponse = await paymentConfigApi.getConfigTemplate(channel.channel_code)
    
    configDialog.channelCode = channel.channel_code
    configDialog.channelName = channel.channel_name
    configDialog.template = templateResponse.data.template
    configDialog.allowedFields = channel.allowed_config_fields

    // 初始化表单
    configForm.config_name = channel.is_configured ? 
      `${channel.channel_name}配置` : 
      `${channel.channel_name}配置`
    
    configForm.config_data = {}
    
    // 如果已有配置，加载现有数据
    if (channel.is_configured) {
      // 这里应该加载现有配置数据
      // const configResponse = await paymentConfigApi.getConfig(channel.config_id)
      // configForm.config_data = configResponse.data.config_data
    }

    configDialog.visible = true
  } catch (error) {
    ElMessage.error('加载配置模板失败')
    console.error(error)
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    // 验证表单
    await configFormRef.value.validate()

    configDialog.loading = true

    // 过滤只保留允许的字段
    const filteredConfigData = {}
    Object.keys(configForm.config_data).forEach(key => {
      if (configDialog.allowedFields[key]) {
        filteredConfigData[key] = configForm.config_data[key]
      }
    })

    await paymentConfigApi.saveConfig({
      user_type: userStore.userInfo?.user_type || 'user',
      user_id: userStore.userInfo?.id,
      channel_code: configDialog.channelCode,
      config_name: configForm.config_name,
      config_data: filteredConfigData
    })

    ElMessage.success('配置保存成功')
    configDialog.visible = false
    
    // 刷新列表
    await loadConfigurableChannels()
  } catch (error) {
    if (error.name !== 'ElFormValidateError') {
      ElMessage.error('保存配置失败')
      console.error(error)
    }
  } finally {
    configDialog.loading = false
  }
}

// 测试通道配置
const testChannel = async (channel) => {
  try {
    channel.testing = true

    await ElMessageBox.confirm(
      `确定要测试"${channel.channel_name}"的配置吗？`,
      '确认测试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await paymentConfigApi.testConfig({
      user_type: userStore.userInfo?.user_type || 'user',
      user_id: userStore.userInfo?.id,
      channel_code: channel.channel_code
    })

    ElMessage.success('配置测试成功')
    
    // 刷新列表
    await loadConfigurableChannels()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('配置测试失败')
      console.error(error)
    }
  } finally {
    channel.testing = false
  }
}
</script>

<style scoped>
.payment-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.permission-tip {
  margin-bottom: 20px;
}

.config-list {
  margin-bottom: 20px;
}

.channel-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.channel-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.channel-card.configured {
  border-color: #67c23a;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-info {
  display: flex;
  align-items: center;
}

.channel-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
}

.channel-name {
  font-weight: 600;
  color: #303133;
}

.card-content {
  padding-top: 0;
}

.channel-description {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.channel-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item .label {
  color: #909399;
}

.detail-item .value {
  color: #303133;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.field-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-help .el-icon {
  font-size: 14px;
}
</style>