<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WithdrawRecord;
use App\Models\BalanceLog;
use App\Models\Transaction;
use App\Models\CommissionLog; // 引入CommissionLog模型
use App\Services\PaymentService; // 引入PaymentService
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 财务控制器 (V3.0 - 用户与管理员双功能版)
 * <AUTHOR>
 * @date 2024-07-26
 */
class FinanceController extends Controller
{
    public function __construct()
    {
        // 所有方法都需要认证
        $this->middleware('auth:api');
    }

    // ===================================================================
    // == 用户端 API
    // ===================================================================

    /**
     * 获取用户财务概览
     */
    public function getOverview(Request $request)
    {
        $user = $request->user();

        // 基础余额信息
        $balance = [
            'balance' => (float)$user->balance,
            'frozen' => (float)$user->frozen_balance,
        ];

        // 收益信息
        $total_earnings = (float)CommissionLog::where('user_id', $user->id)->sum('amount');
        $today_earnings = (float)CommissionLog::where('user_id', $user->id)->whereDate('created_at', Carbon::today())->sum('amount');
        $earnings = [
            'total_earnings' => $total_earnings,
            'today' => $today_earnings,
        ];
        
        // 待处理提现
        $pending_withdraw_stats = WithdrawRecord::where('user_id', $user->id)
            ->where('status', WithdrawRecord::STATUS_PENDING)
            ->selectRaw('COUNT(*) as count, SUM(amount) as total_amount')
            ->first();

        return response()->json([
            'code' => 0,
            'data' => [
                'balance' => $balance,
                'earnings' => $earnings,
                'pending_withdraw_count' => (int)$pending_withdraw_stats->count,
                'pending_withdraw_total' => (float)$pending_withdraw_stats->total_amount,
            ]
        ]);
    }

    /**
     * 获取用户收益图表数据
     */
    public function getEarningsChart(Request $request)
    {
        $user = $request->user();
        $period = $request->input('period', 'week');
        
        $days = match($period) {
            'month' => 30,
            '3months' => 90,
            'year' => 365,
            default => 7,
        };

        $endDate = Carbon::now()->endOfDay();
        $startDate = Carbon::now()->subDays($days - 1)->startOfDay();

        $data = CommissionLog::where('user_id', $user->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->get([
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as total')
            ])
            ->pluck('total', 'date');

        $labels = [];
        $values = [];
        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dateString = $date->format('Y-m-d');
            $labels[] = $date->format('m-d');
            $values[] = (float)($data[$dateString] ?? 0);
        }

        return response()->json([
            'code' => 0, 
            'data' => [
                'labels' => $labels,
                'data' => $values
            ]
        ]);
    }
    
    /**
     * 获取用户的交易记录
     */
    public function getUserTransactions(Request $request)
    {
        $user = $request->user();
        $query = Transaction::where('user_id', $user->id);

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $transactions = $query->orderBy('created_at', 'desc')
                               ->paginate($request->input('limit', 10));

        return response()->json(['code' => 0, 'data' => $transactions]);
    }

    /**
     * 获取用户的提现记录
     */
    public function getUserWithdrawals(Request $request)
    {
        $user = $request->user();
        $withdrawals = WithdrawRecord::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->input('limit', 10));
            
        return response()->json(['code' => 0, 'data' => $withdrawals]);
    }
    
    /**
     * 获取用户的佣金记录
     */
    public function getUserCommissions(Request $request)
    {
        $user = $request->user();
        $commissions = CommissionLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->input('limit', 10));
            
        return response()->json(['code' => 0, 'data' => $commissions]);
    }

    /**
     * 申请提现 (用户端)
     * 路由: POST /finance/withdrawals
     */
    public function requestWithdrawal(Request $request)
    {
        $user = $request->user();
        $data = $request->validate([
            'amount' => 'required|numeric|min:1', // 假设最低提现1元
            'remark' => 'nullable|string|max:255',
        ]);
        
        $amount = floatval($data['amount']);

        DB::beginTransaction();
        try {
            $user = User::where('id', $user->id)->lockForUpdate()->first();

            if ($user->balance < $amount) {
                DB::rollBack();
                return response()->json(['code' => 400, 'message' => '可用余额不足。'], 400);
            }
            
            $user->balance = bcsub(strval($user->balance), strval($amount), 2);
            $user->frozen_balance = bcadd(strval($user->frozen_balance), strval($amount), 2);
            $user->save();
            
            $withdrawRecord = WithdrawRecord::create([
                'user_id' => $user->id,
                'amount' => $amount,
                'fee' => 0, 
                'actual_amount' => $amount,
                'status' => WithdrawRecord::STATUS_PENDING,
                'user_remark' => $data['remark'] ?? null,
            ]);
            
            Transaction::create([
                'user_id' => $user->id,
                'amount' => -$amount,
                'type' => 'withdraw',
                'status' => 'pending',
                'description' => "申请提现, 等待审核。单号: {$withdrawRecord->withdraw_no}",
                'source_id' => $withdrawRecord->id,
                'source_type' => WithdrawRecord::class,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('提现申请失败: ' . $e->getMessage());
            return response()->json(['code' => 500, 'message' => '系统错误，提现申请失败。'], 500);
        }

        return response()->json(['code' => 0, 'message' => '提现申请已提交，等待审核。']);
    }

    /**
     * 创建充值订单 (用户端)
     */
    public function createRechargeOrder(Request $request, PaymentService $paymentService)
    {
        $user = $request->user();
        $data = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'channel_id' => 'required|integer|exists:payment_channels,id,status,1', // 确保支付渠道存在且启用
        ]);

        try {
            $order = $paymentService->createRechargeOrder($user, $data['amount'], $data['channel_id']);
            $paymentInfo = $paymentService->getPaymentInfo($order);

            return response()->json(['code' => 0, 'data' => $paymentInfo]);

        } catch (\Exception $e) {
            Log::error('创建充值订单失败: ' . $e->getMessage());
            return response()->json(['code' => 500, 'message' => $e->getMessage()], 500);
        }
    }


    // ===================================================================
    // == 管理员端 API (保持不变, 由路由中间件控制权限)
    // ===================================================================
    
    /**
     * 获取财务仪表盘核心统计数据
     */
    public function getDashboardStats(Request $request)
    {
        // 总收入 (正数交易)
        $total_income = Transaction::where('amount', '>', 0)->where('status', 'completed')->sum('amount');
        // 总支出 (负数交易，如提现成功)
        $total_expense = abs(Transaction::where('amount', '<', 0)->where('status', 'completed')->sum('amount'));
        // 净利润
        $net_profit = $total_income - $total_expense;
        // 所有用户总余额
        $total_balance = User::sum('balance');
        // 待处理提现总额
        $pending_withdraw = WithdrawRecord::where('status', WithdrawRecord::STATUS_PENDING)->sum('amount');

        // 注意: 变化率为模拟数据，真实应用需与上周期比较
        $stats = [
            'total_income' => (float)$total_income,
            'income_change' => 1.5,
            'total_expense' => (float)$total_expense,
            'expense_change' => -0.5,
            'net_profit' => (float)$net_profit,
            'profit_change' => 2.0,
            'total_balance' => (float)$total_balance,
            'pending_withdraw' => (float)$pending_withdraw,
        ];
        
        return response()->json(['code' => 0, 'data' => $stats]);
    }

    /**
     * 获取收支趋势数据
     */
    public function getTrendData(Request $request)
    {
        $period = $request->input('period', '30d');
        $days = match($period) {
            '7d' => 7,
            '90d' => 90,
            default => 30,
        };
        
        $endDate = Carbon::now()->endOfDay();
        $startDate = Carbon::now()->subDays($days - 1)->startOfDay();

        $income_data = Transaction::where('amount', '>', 0)->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->get([
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as total')
            ])
            ->pluck('total', 'date');
            
        $expense_data = Transaction::where('amount', '<', 0)->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->get([
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(ABS(amount)) as total')
            ])
            ->pluck('total', 'date');
            
        $dates = collect();
        for ($i = 0; $i < $days; $i++) {
            $dates->push(Carbon::now()->subDays($i)->format('Y-m-d'));
        }
        $dates = $dates->reverse();

        $trend = $dates->map(function ($date) use ($income_data, $expense_data) {
            return [
                'date' => $date,
                'income' => $income_data[$date] ?? 0,
                'expense' => $expense_data[$date] ?? 0,
            ];
        });

        return response()->json(['code' => 0, 'data' => $trend]);
    }

    /**
     * 获取收入来源构成
     */
    public function getIncomeSources(Request $request)
    {
        $sources = Transaction::where('amount', '>', 0)->where('status', 'completed')
            ->groupBy('type')
            ->get([
                'type',
                DB::raw('SUM(amount) as value')
            ])
            ->map(function($item) {
                // 可将类型映射为更友好的名称
                $typeName = match($item->type) {
                    'order_income' => '订单收入',
                    'recharge' => '用户充值',
                    default => '其他收入'
                };
                return ['name' => $typeName, 'value' => (float)$item->value];
            });

        return response()->json(['code' => 0, 'data' => $sources]);
    }

    /**
     * 获取最近的交易记录
     */
    public function getRecentTransactions(Request $request)
    {
        $transactions = Transaction::with('user:id,nickname')
            ->orderBy('created_at', 'desc')
            ->limit($request->input('limit', 10))
            ->get();
            
        return response()->json(['code' => 0, 'data' => $transactions]);
    }

    /**
     * 获取交易记录列表 (用于独立页面)
     */
    public function getTransactionList(Request $request)
    {
        $query = Transaction::with('user:id,nickname');

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $transactions = $query->orderBy('created_at', 'desc')
                               ->paginate($request->input('limit', 15));

        return response()->json([
            'code' => 0,
            'data' => [
                'items' => $transactions->items(),
                'total' => $transactions->total(),
            ]
        ]);
    }

    /**
     * 获取提现记录列表 (管理员端)
     */
    public function getWithdrawRecords(Request $request)
    {
        $query = WithdrawRecord::with('user:id,username,nickname');
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $records = $query->orderBy('created_at', 'desc')->paginate($request->get('limit', 15));

        return response()->json(['code' => 0, 'data' => $records]);
    }
    
    /**
     * 批准提现 (管理员端)
     */
    public function approveWithdraw(Request $request, $id)
    {
        $operator = $request->user();
        $record = WithdrawRecord::findOrFail($id);

        if ($record->status !== WithdrawRecord::STATUS_PENDING) {
            return response()->json(['code' => 1, 'message' => '该记录无法被批准'], 400);
        }

        DB::beginTransaction();
        try {
            $user = User::where('id', $record->user_id)->lockForUpdate()->first();
            
            $user->frozen_balance = bcsub(strval($user->frozen_balance), strval($record->amount), 2);
            $user->save();

            $record->status = WithdrawRecord::STATUS_APPROVED;
            $record->operator_id = $operator->id;
            $record->processed_at = now();
            $record->admin_remark = $request->input('remark', '批准提现，已线下打款');
            $record->save();
            
            // 将对应的交易流水更新为“已完成”
            Transaction::where('source_type', WithdrawRecord::class)
                ->where('source_id', $record->id)
                ->update(['status' => 'completed', 'description' => '提现成功: ' . $record->admin_remark]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("批准提现失败 (ID: {$id}): " . $e->getMessage());
            return response()->json(['code' => 1, 'message' => '操作失败，请稍后再试'], 500);
        }

        return response()->json(['code' => 0, 'message' => '提现已批准']);
    }

    /**
     * 拒绝提现 (管理员端)
     */
    public function rejectWithdraw(Request $request, $id)
    {
        $data = $request->validate(['remark' => 'required|string|max:255']);
        
        $operator = $request->user();
        $record = WithdrawRecord::findOrFail($id);

        if ($record->status !== WithdrawRecord::STATUS_PENDING) {
            return response()->json(['code' => 1, 'message' => '该记录无法被拒绝'], 400);
        }

        DB::beginTransaction();
        try {
            $user = User::where('id', $record->user_id)->lockForUpdate()->first();
            
            $user->balance = bcadd(strval($user->balance), strval($record->amount), 2);
            $user->frozen_balance = bcsub(strval($user->frozen_balance), strval($record->amount), 2);
            $user->save();
            
            $record->status = WithdrawRecord::STATUS_REJECTED;
            $record->operator_id = $operator->id;
            $record->processed_at = now();
            $record->admin_remark = $data['remark'];
            $record->save();
            
            // 将对应的交易流水更新为"失败"
            Transaction::where('source_type', WithdrawRecord::class)
                ->where('source_id', $record->id)
                ->update(['status' => 'failed', 'description' => '提现被拒: ' . $record->admin_remark]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("拒绝提现失败 (ID: {$id}): " . $e->getMessage());
            return response()->json(['code' => 1, 'message' => '操作失败，请稍后再试'], 500);
        }

        return response()->json(['code' => 0, 'message' => '提现已拒绝']);
    }

    /**
     * 导出财务报表
     */
    public function exportReport(Request $request)
    {
        try {
            $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
            
            // 获取交易数据
            $transactions = Transaction::whereBetween('created_at', [$startDate, $endDate])
                ->with('user:id,name,email')
                ->orderBy('created_at', 'desc')
                ->get();
            
            // 创建CSV内容
            $csvContent = "日期,用户,类型,金额,状态,备注\n";
            
            foreach ($transactions as $transaction) {
                $csvContent .= implode(',', [
                    $transaction->created_at->format('Y-m-d H:i:s'),
                    $transaction->user->name ?? '未知用户',
                    $transaction->type,
                    $transaction->amount,
                    $transaction->status,
                    '"' . ($transaction->description ?? '') . '"'
                ]) . "\n";
            }
            
            // 设置响应头
            $filename = "finance_report_{$startDate}_to_{$endDate}.csv";
            
            return response($csvContent, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ]);
            
        } catch (\Exception $e) {
            Log::error("导出财务报表失败: " . $e->getMessage());
            return response()->json(['code' => 1, 'message' => '导出失败，请稍后再试'], 500);
        }
    }
} 