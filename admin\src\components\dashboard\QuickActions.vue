<template>
  <div class="quick-actions-card">
    <div class="card-header">
      <h3 class="card-title">快捷操作</h3>
    </div>
    <div class="actions-grid">
      <div 
        v-for="action in actions" 
        :key="action.key"
        class="action-item"
        @click="handleAction(action)"
      >
        <div class="action-icon" :style="{ background: action.color }">
          <el-icon>
            <component :is="getIconComponent(action.icon)" />
          </el-icon>
        </div>
        <div class="action-label">{{ action.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { User, Connection, DataAnalysis, Setting } from '@element-plus/icons-vue'

const props = defineProps({
  actions: {
    type: Array,
    default: () => []
  }
})

const getIconComponent = (iconName) => {
  const iconMap = {
    'User': User,
    'Connection': Connection,
    'DataAnalysis': TrendCharts,
    'Setting': Setting
  }
  return iconMap[iconName] || Setting
}

const handleAction = (action) => {
  if (action.action && typeof action.action === 'function') {
    action.action()
  }
}
</script>

<style lang="scss" scoped>
.quick-actions-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f3f4f6;

    &:hover {
      background: #f9fafb;
      border-color: #3b82f6;
      transform: translateY(-2px);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .action-label {
      font-size: 12px;
      font-weight: 500;
      color: #1f2937;
      text-align: center;
    }
  }
}

@media (max-width: 768px) {
  .quick-actions-card {
    .actions-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>