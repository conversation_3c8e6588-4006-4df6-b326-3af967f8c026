<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="内容详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="content-detail-dialog" v-if="content">
      <div class="content-header">
        <div class="content-meta">
          <div class="meta-item">
            <span class="label">内容ID:</span>
            <span class="value">{{ content.id }}</span>
          </div>
          <div class="meta-item">
            <span class="label">类型:</span>
            <el-tag size="small">{{ getTypeText(content.type) }}</el-tag>
          </div>
          <div class="meta-item">
            <span class="label">状态:</span>
            <el-tag :type="getStatusTagType(content.status)" size="small">
              {{ getStatusText(content.status) }}
            </el-tag>
          </div>
          <div class="meta-item">
            <span class="label">风险等级:</span>
            <el-tag :type="getRiskTagType(content.risk_level)" size="small">
              {{ getRiskText(content.risk_level) }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="content-body">
        <div class="content-section">
          <h4>内容详情</h4>
          <div class="content-text">
            {{ content.content }}
          </div>
          
          <div v-if="content.image_url" class="content-media">
            <h5>附件图片</h5>
            <el-image
              :src="content.image_url"
              fit="contain"
              style="max-width: 100%; max-height: 300px;"
              :preview-src-list="[content.image_url]"
            />
          </div>
        </div>

        <div class="author-section">
          <h4>发布者信息</h4>
          <div class="author-info">
            <el-avatar :src="content.author.avatar" :alt="content.author.name" size="large">
              {{ content.author.name.charAt(0) }}
            </el-avatar>
            <div class="author-details">
              <div class="author-name">{{ content.author.name }}</div>
              <div class="author-meta">
                <span>用户ID: {{ content.author.id }}</span>
                <span>发布时间: {{ formatDate(content.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="group-section">
          <h4>所属群组</h4>
          <div class="group-info">
            <div class="group-name">{{ content.group.name }}</div>
            <div class="group-meta">
              <span>群组ID: {{ content.group.id }}</span>
            </div>
          </div>
        </div>

        <div v-if="content.ai_analysis" class="ai-section">
          <h4>AI分析结果</h4>
          <div class="ai-analysis">
            <div class="analysis-confidence">
              <span class="confidence-label">置信度:</span>
              <el-progress
                :percentage="content.ai_analysis.confidence * 100"
                :color="getConfidenceColor(content.ai_analysis.confidence)"
                :stroke-width="8"
              />
              <span class="confidence-value">{{ (content.ai_analysis.confidence * 100).toFixed(1) }}%</span>
            </div>
            
            <div class="analysis-tags">
              <span class="tags-label">检测标签:</span>
              <div class="tags-list">
                <el-tag
                  v-for="tag in content.ai_analysis.tags"
                  :key="tag"
                  type="warning"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <div v-if="content.ai_analysis.recommendation" class="analysis-recommendation">
              <span class="recommendation-label">AI建议:</span>
              <el-tag
                :type="getRecommendationTagType(content.ai_analysis.recommendation)"
                size="small"
              >
                {{ getRecommendationText(content.ai_analysis.recommendation) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="review-history">
          <h4>审核历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in reviewHistory"
              :key="index"
              :timestamp="formatDate(record.created_at)"
              :type="getTimelineType(record.action)"
            >
              <div class="history-item">
                <div class="action-info">
                  <span class="action-text">{{ getActionText(record.action) }}</span>
                  <span class="reviewer">by {{ record.reviewer }}</span>
                </div>
                <div v-if="record.reason" class="action-reason">
                  原因: {{ record.reason }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
        <el-button-group v-if="content && content.status === 'pending'">
          <el-button
            type="success"
            @click="$emit('action', 'approve', content)"
          >
            <el-icon><Check /></el-icon>
            通过
          </el-button>
          <el-button
            type="danger"
            @click="$emit('action', 'reject', content)"
          >
            <el-icon><Close /></el-icon>
            拒绝
          </el-button>
        </el-button-group>
        <el-button
          type="warning"
          @click="$emit('action', 'flag', content)"
        >
          <el-icon><Flag /></el-icon>
          标记关注
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Check, Close, Flag } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: Boolean,
  content: {
    type: Object,
    default: () => null
  }
})

defineEmits(['update:modelValue', 'action'])

// 模拟审核历史数据
const reviewHistory = ref([
  {
    action: 'created',
    reviewer: '系统',
    created_at: new Date(Date.now() - 3600000).toISOString(),
    reason: ''
  },
  {
    action: 'ai_analyzed',
    reviewer: 'AI系统',
    created_at: new Date(Date.now() - 1800000).toISOString(),
    reason: '检测到可能的敏感内容'
  }
])

// 工具函数
const getTypeText = (type) => {
  const texts = {
    text: '文本',
    image: '图片',
    video: '视频',
    link: '链接'
  }
  return texts[type] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    flagged: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    flagged: '已标记'
  }
  return texts[status] || '未知'
}

const getRiskTagType = (riskLevel) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return types[riskLevel] || 'info'
}

const getRiskText = (riskLevel) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[riskLevel] || '未知'
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const getRecommendationTagType = (recommendation) => {
  const types = {
    approve: 'success',
    reject: 'danger',
    flag: 'warning'
  }
  return types[recommendation] || 'info'
}

const getRecommendationText = (recommendation) => {
  const texts = {
    approve: '建议通过',
    reject: '建议拒绝',
    flag: '建议标记'
  }
  return texts[recommendation] || '无建议'
}

const getTimelineType = (action) => {
  const types = {
    created: 'primary',
    ai_analyzed: 'warning',
    approved: 'success',
    rejected: 'danger',
    flagged: 'info'
  }
  return types[action] || 'primary'
}

const getActionText = (action) => {
  const texts = {
    created: '内容创建',
    ai_analyzed: 'AI分析完成',
    approved: '审核通过',
    rejected: '审核拒绝',
    flagged: '标记关注'
  }
  return texts[action] || '未知操作'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.content-detail-dialog {
  .content-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .content-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-size: 14px;
          color: #909399;
          font-weight: 500;
        }

        .value {
          font-size: 14px;
          color: #303133;
          font-weight: 600;
        }
      }
    }
  }

  .content-body {
    .content-section,
    .author-section,
    .group-section,
    .ai-section,
    .review-history {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }

      h5 {
        margin: 16px 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
      }
    }

    .content-section {
      .content-text {
        background: #f8fafc;
        border-radius: 8px;
        padding: 16px;
        line-height: 1.6;
        color: #303133;
        white-space: pre-wrap;
        word-break: break-word;
      }

      .content-media {
        margin-top: 16px;
        text-align: center;
      }
    }

    .author-section {
      .author-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .author-details {
          .author-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .author-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .group-section {
      .group-info {
        background: #f8fafc;
        border-radius: 8px;
        padding: 12px;

        .group-name {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .group-meta {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .ai-section {
      .ai-analysis {
        background: #f8fafc;
        border-radius: 8px;
        padding: 16px;

        .analysis-confidence {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;

          .confidence-label {
            font-size: 14px;
            color: #606266;
            min-width: 60px;
          }

          :deep(.el-progress) {
            flex: 1;
          }

          .confidence-value {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            min-width: 50px;
          }
        }

        .analysis-tags {
          margin-bottom: 16px;

          .tags-label {
            display: block;
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }

          .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }

        .analysis-recommendation {
          display: flex;
          align-items: center;
          gap: 8px;

          .recommendation-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }

    .review-history {
      .history-item {
        .action-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .action-text {
            font-weight: 600;
            color: #303133;
          }

          .reviewer {
            font-size: 12px;
            color: #909399;
          }
        }

        .action-reason {
          font-size: 12px;
          color: #606266;
          font-style: italic;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 响应式设计
@media (max-width: 768px) {
  .content-detail-dialog {
    .content-header {
      .content-meta {
        flex-direction: column;
        gap: 8px;
      }
    }

    .content-body {
      .author-section {
        .author-info {
          flex-direction: column;
          align-items: flex-start;
        }
      }

      .ai-section {
        .ai-analysis {
          .analysis-confidence {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      }
    }
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
}
</style>