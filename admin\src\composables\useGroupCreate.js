import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGroupStore } from '@/stores/group'
import { 
  createGroup, 
  testPaymentConfig, 
  testCityReplacement,
  uploadFile,
  getRecommendedSettings,
  applyGroupTemplate
} from '@/api/group'

/**
 * 群组创建组合式函数
 */
export function useGroupCreate() {
  const router = useRouter()
  const groupStore = useGroupStore()
  
  // 响应式状态
  const currentStep = ref(0)
  const submitting = ref(false)
  const previewVisible = ref(false)
  const activeContentTab = ref('intro')
  const testCity = ref('')
  const testResult = ref('')
  const testingPayment = ref(false)
  const paymentTestResult = ref(null)
  
  // 表单引用
  const formRef = ref()
  
  // 表单数据
  const formData = reactive({
    // 基础信息
    title: '',
    category: '',
    price: 0,
    member_limit: 500,
    description: '',
    cover_image: '',
    qr_code: '',
    
    // 城市定位
    city_location: true,
    city_insert_strategy: 'auto',
    
    // 富媒体内容
    group_intro_title: '为什么选择我们的群组？',
    group_intro_content: `<h3>🎯 群组特色</h3>
<ul>
  <li>专业的内容分享</li>
  <li>活跃的社群氛围</li>
  <li>定期的线上活动</li>
  <li>优质的人脉资源</li>
</ul>

<h3>💡 你将获得</h3>
<p>加入我们的群组，你将获得：</p>
<ul>
  <li>最新的行业资讯</li>
  <li>专业的技能提升</li>
  <li>志同道合的朋友</li>
  <li>更多的合作机会</li>
</ul>`,
    faq_title: '常见问题解答',
    faq_content: `<div><strong>Q: 如何加入群组？</strong></div>
<div>A: 点击下方按钮，完成支付后即可获得群组二维码。</div>
<br>
<div><strong>Q: 群组有什么特色？</strong></div>
<div>A: 我们提供专业的内容分享、活跃的讨论氛围和定期的线上活动。</div>
<br>
<div><strong>Q: 是否有退款政策？</strong></div>
<div>A: 支持7天无理由退款，如有问题请联系客服。</div>`,
    member_reviews: `<div class="review-item">
  <div><strong>张先生</strong> ⭐⭐⭐⭐⭐</div>
  <div>群组内容很有价值，学到了很多实用的知识，推荐！</div>
</div>
<br>
<div class="review-item">
  <div><strong>李女士</strong> ⭐⭐⭐⭐⭐</div>
  <div>群友都很友善，经常有干货分享，值得加入。</div>
</div>
<br>
<div class="review-item">
  <div><strong>王同学</strong> ⭐⭐⭐⭐⭐</div>
  <div>通过群组认识了很多同行，获得了不少合作机会。</div>
</div>`,
    media_images: [],
    promo_video: '',
    audio_intro: '',
    
    // 营销展示
    read_count_display: '',
    like_count: 0,
    want_see_count: 0,
    button_title: '立即加入',
    virtual_members: 0,
    virtual_orders: 0,
    
    // 支付设置
    payment_methods: [],
    order_expire_minutes: 30,
    success_redirect_url: '',
    
    // 分销设置
    enable_distribution: false,
    commission_rate_1: 0,
    commission_rate_2: 0,
    commission_rate_3: 0,
    commission_settlement: 'daily',
    
    // 防红系统
    enable_anti_block: false,
    domain_pool_id: '',
    check_frequency: '10',
    switch_strategy: 'immediate',
    
    // 营销功能
    enable_limited_offer: false,
    offer_price: 0,
    offer_end_time: '',
    enable_urgency: false,
    remaining_slots: 0,
    show_countdown: false,
    enable_social_proof: false,
    recent_join_text: '',
    
    // 数据统计
    enable_analytics: true,
    enable_conversion_tracking: true,
    custom_analytics_code: '',
    
    // 创建选项
    auto_publish: true,
    send_notification: false,
    generate_qr_poster: true
  })
  
  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入群组名称', trigger: 'blur' },
      { min: 2, max: 200, message: '群组名称长度在2到200个字符', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请选择群组分类', trigger: 'change' }
    ],
    price: [
      { required: true, message: '请输入群组价格', trigger: 'blur' },
      { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
    ],
    member_limit: [
      { required: true, message: '请输入成员上限', trigger: 'blur' },
      { type: 'number', min: 1, max: 2000, message: '成员上限在1到2000之间', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入群组描述', trigger: 'blur' },
      { min: 10, max: 1000, message: '描述长度在10到1000个字符', trigger: 'blur' }
    ]
  }
  
  // 计算属性
  const canPreview = computed(() => {
    return formData.title && formData.category && formData.description
  })
  
  const canNextStep = computed(() => {
    switch (currentStep.value) {
      case 0:
        return formData.title && formData.category && formData.description
      case 1:
        return true
      case 2:
        return formData.payment_methods.length > 0 || formData.price === 0
      case 3:
        return true
      default:
        return true
    }
  })
  
  const parsedFAQ = computed(() => {
    if (!formData.faq_content) return []
    return formData.faq_content.split('\n')
      .filter(line => line.includes('----'))
      .map(line => {
        const [question, answer] = line.split('----')
        return { question: question.trim(), answer: answer.trim() }
      })
  })
  
  const parsedReviews = computed(() => {
    if (!formData.member_reviews) return []
    return formData.member_reviews.split('\n')
      .filter(line => line.includes('----'))
      .map(line => {
        const [content, rating] = line.split('----')
        return { content: content.trim(), rating: rating.trim() }
      })
  })
  
  // 监听分类变化，自动加载推荐设置
  watch(() => formData.category, async (newCategory) => {
    if (newCategory) {
      try {
        const response = await getRecommendedSettings(newCategory)
        const settings = response.data
        
        // 应用推荐设置
        if (settings.recommended_price) {
          formData.price = settings.recommended_price
        }
        if (settings.recommended_member_limit) {
          formData.member_limit = settings.recommended_member_limit
        }
        if (settings.recommended_button_title) {
          formData.button_title = settings.recommended_button_title
        }
      } catch (error) {
        console.error('获取推荐设置失败:', error)
      }
    }
  })
  
  // 方法
  const handleCancel = () => {
    ElMessageBox.confirm('确定要取消创建吗？未保存的数据将丢失。', '确认取消', {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }).then(() => {
      router.back()
    })
  }
  
  const handlePreview = () => {
    previewVisible.value = true
  }
  
  const handlePreviewClose = () => {
    previewVisible.value = false
  }
  
  const handleSubmit = async () => {
    try {
      await formRef.value.validate()
      submitting.value = true
      
      // 创建群组
      const response = await createGroup(formData)
      
      ElMessage.success('群组创建成功！')
      
      // 如果需要生成海报
      if (formData.generate_qr_poster) {
        ElMessage.info('正在生成推广海报...')
      }
      
      // 跳转到群组列表
      router.push('/community/groups')
      
      return response.data
    } catch (error) {
      console.error('创建群组失败:', error)
      ElMessage.error(error.message || '创建群组失败，请重试')
      throw error
    } finally {
      submitting.value = false
    }
  }
  
  const nextStep = async () => {
    // 验证当前步骤
    if (currentStep.value === 0) {
      try {
        await formRef.value.validateField(['title', 'category', 'description'])
      } catch (error) {
        return
      }
    }
    
    if (canNextStep.value && currentStep.value < 4) {
      currentStep.value++
    }
  }
  
  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--
    }
  }
  
  const loadTemplate = async () => {
    try {
      // 获取模板列表
      await groupStore.fetchGroupTemplates()
      
      // 显示模板选择对话框
      const templates = groupStore.groupTemplates
      if (templates.length === 0) {
        ElMessage.info('暂无可用模板')
        return
      }
      
      // 这里可以打开模板选择对话框
      ElMessage.info('模板功能开发中')
    } catch (error) {
      ElMessage.error('加载模板失败')
    }
  }
  
  const applyTemplate = async (templateId) => {
    try {
      const response = await applyGroupTemplate(templateId)
      const templateData = response.data
      
      // 应用模板数据到表单
      Object.keys(templateData).forEach(key => {
        if (key in formData) {
          formData[key] = templateData[key]
        }
      })
      
      ElMessage.success('模板应用成功')
    } catch (error) {
      ElMessage.error('应用模板失败')
    }
  }
  
  const handleCityLocationChange = (value) => {
    if (!value) {
      formData.city_insert_strategy = 'none'
    }
  }
  
  const testCityReplacement = async () => {
    if (!testCity.value || !formData.title) return
    
    try {
      const response = await testCityReplacement(
        formData.title, 
        testCity.value, 
        formData.city_insert_strategy
      )
      testResult.value = response.data.result
    } catch (error) {
      // 本地处理
      const title = formData.title
      let result = title
      
      switch (formData.city_insert_strategy) {
        case 'auto':
          if (title.includes('xxx')) {
            result = title.replace(/xxx/g, testCity.value)
          } else {
            result = `${testCity.value}${title}`
          }
          break
        case 'prefix':
          result = `${testCity.value}${title}`
          break
        case 'suffix':
          result = `${title}${testCity.value}`
          break
        case 'natural':
          result = title.replace(/xxx/g, testCity.value)
          break
        default:
          result = title
      }
      
      testResult.value = result
    }
  }
  
  const getCityReplacedTitle = () => {
    if (!formData.city_location || !formData.title) return formData.title
    
    const testCityName = '北京'
    let result = formData.title
    
    switch (formData.city_insert_strategy) {
      case 'auto':
        if (formData.title.includes('xxx')) {
          result = formData.title.replace(/xxx/g, testCityName)
        } else {
          result = `${testCityName}${formData.title}`
        }
        break
      case 'prefix':
        result = `${testCityName}${formData.title}`
        break
      case 'suffix':
        result = `${formData.title}${testCityName}`
        break
      case 'natural':
        result = formData.title.replace(/xxx/g, testCityName)
        break
      default:
        result = formData.title
    }
    
    return result
  }
  
  const insertTemplate = (type) => {
    const templates = {
      intro: `<h3>🎯 群组特色</h3>
<ul>
  <li>专业的内容分享</li>
  <li>活跃的社群氛围</li>
  <li>定期的线上活动</li>
  <li>优质的人脉资源</li>
</ul>

<h3>💡 你将获得</h3>
<p>加入我们的群组，你将获得：</p>
<ul>
  <li>最新的行业资讯</li>
  <li>专业的技能提升</li>
  <li>志同道合的朋友</li>
  <li>更多的合作机会</li>
</ul>`,
      faq: `<div><strong>Q: 如何加入群组？</strong></div>
<div>A: 点击下方按钮，完成支付后即可获得群组二维码。</div>
<br>
<div><strong>Q: 群组有什么特色？</strong></div>
<div>A: 我们提供专业的内容分享、活跃的讨论氛围和定期的线上活动。</div>
<br>
<div><strong>Q: 是否有退款政策？</strong></div>
<div>A: 支持7天无理由退款，如有问题请联系客服。</div>`,
      reviews: `<div class="review-item">
  <div><strong>张先生</strong> ⭐⭐⭐⭐⭐</div>
  <div>群组内容很有价值，学到了很多实用的知识，推荐！</div>
</div>
<br>
<div class="review-item">
  <div><strong>李女士</strong> ⭐⭐⭐⭐⭐</div>
  <div>群友都很友善，经常有干货分享，值得加入。</div>
</div>
<br>
<div class="review-item">
  <div><strong>王同学</strong> ⭐⭐⭐⭐⭐</div>
  <div>通过群组认识了很多同行，获得了不少合作机会。</div>
</div>`
    }
    
    switch (type) {
      case 'intro':
        formData.group_intro_content = templates.intro
        break
      case 'faq':
        formData.faq_content = templates.faq
        break
      case 'reviews':
        formData.member_reviews = templates.reviews
        break
    }
    
    ElMessage.success('模板插入成功')
  }
  
  const insertImage = (type) => {
    ElMessage.info('图片插入功能开发中')
  }
  
  const insertVideo = (type) => {
    ElMessage.info('视频插入功能开发中')
  }
  
  const handleAvatarSuccess = (response) => {
    formData.cover_image = response.data.url
    ElMessage.success('头像上传成功')
  }
  
  const handleQrSuccess = (response) => {
    formData.qr_code = response.data.url
    ElMessage.success('二维码上传成功')
  }
  
  const handleMediaSuccess = (response) => {
    formData.media_images.push({
      name: response.data.name,
      url: response.data.url
    })
    ElMessage.success('图片上传成功')
  }
  
  const handleMediaRemove = (file) => {
    const index = formData.media_images.findIndex(img => img.url === file.url)
    if (index > -1) {
      formData.media_images.splice(index, 1)
    }
  }
  
  const beforeAvatarUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!')
      return false
    }
    return true
  }
  
  const beforeQrUpload = beforeAvatarUpload
  
  const goToPaymentConfig = () => {
    router.push('/system/payment')
  }
  
  const testPaymentConfig = async () => {
    testingPayment.value = true
    try {
      const response = await testPaymentConfig(formData.payment_methods)
      paymentTestResult.value = response.data
    } catch (error) {
      paymentTestResult.value = {
        success: false,
        message: '支付配置测试失败'
      }
    } finally {
      testingPayment.value = false
    }
  }
  
  const manageDomainPools = () => {
    router.push('/system/domain-pools')
  }
  
  const getCategoryName = (category) => {
    const categoryMap = groupStore.categories.reduce((map, cat) => {
      map[cat.value] = cat.label
      return map
    }, {})
    return categoryMap[category] || category
  }
  
  const getPaymentMethodNames = () => {
    return formData.payment_methods
      .map(code => groupStore.paymentMethods.find(m => m.code === code)?.name)
      .filter(Boolean)
      .join('、') || '无'
  }
  
  // 自动保存草稿
  const saveDraft = () => {
    const draftKey = 'group_create_draft'
    localStorage.setItem(draftKey, JSON.stringify(formData))
  }
  
  const loadDraft = () => {
    const draftKey = 'group_create_draft'
    const draft = localStorage.getItem(draftKey)
    if (draft) {
      try {
        const draftData = JSON.parse(draft)
        Object.keys(draftData).forEach(key => {
          if (key in formData) {
            formData[key] = draftData[key]
          }
        })
        ElMessage.success('已恢复草稿数据')
      } catch (error) {
        console.error('加载草稿失败:', error)
      }
    }
  }
  
  const clearDraft = () => {
    const draftKey = 'group_create_draft'
    localStorage.removeItem(draftKey)
  }
  
  // 返回所有需要的状态和方法
  return {
    // 状态
    currentStep,
    submitting,
    previewVisible,
    activeContentTab,
    testCity,
    testResult,
    testingPayment,
    paymentTestResult,
    formRef,
    formData,
    formRules,
    
    // 计算属性
    canPreview,
    canNextStep,
    parsedFAQ,
    parsedReviews,
    
    // 方法
    handleCancel,
    handlePreview,
    handlePreviewClose,
    handleSubmit,
    nextStep,
    prevStep,
    loadTemplate,
    applyTemplate,
    handleCityLocationChange,
    testCityReplacement,
    getCityReplacedTitle,
    insertTemplate,
    insertImage,
    insertVideo,
    handleAvatarSuccess,
    handleQrSuccess,
    handleMediaSuccess,
    handleMediaRemove,
    beforeAvatarUpload,
    beforeQrUpload,
    goToPaymentConfig,
    testPaymentConfig,
    manageDomainPools,
    getCategoryName,
    getPaymentMethodNames,
    saveDraft,
    loadDraft,
    clearDraft
  }
}