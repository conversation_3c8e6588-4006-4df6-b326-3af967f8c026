<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LogAnalysisService;

/**
 * 日志分析命令
 */
class AnalyzeLogs extends Command
{
    protected $signature = 'logs:analyze 
                           {--file= : 指定日志文件}
                           {--lines=1000 : 分析行数}
                           {--level= : 过滤日志级别}
                           {--search= : 搜索关键词}
                           {--days=7 : 统计天数}
                           {--cleanup : 清理旧日志}
                           {--monitor : 监控异常}';
    
    protected $description = '分析系统日志';

    protected LogAnalysisService $logService;

    public function __construct(LogAnalysisService $logService)
    {
        parent::__construct();
        $this->logService = $logService;
    }

    public function handle()
    {
        if ($this->option('cleanup')) {
            return $this->cleanupLogs();
        }

        if ($this->option('monitor')) {
            return $this->monitorAnomalies();
        }

        return $this->analyzeLogs();
    }

    private function analyzeLogs(): int
    {
        $this->info('🔍 开始分析日志...');
        
        try {
            $options = [
                'lines' => (int) $this->option('lines'),
                'level_filter' => $this->option('level'),
                'search' => $this->option('search'),
            ];

            $result = $this->logService->analyzeLog($this->option('file'), $options);
            
            $this->displayAnalysisResult($result);

            // 生成统计报告
            if (!$this->option('file')) {
                $this->displayStatistics();
            }

        } catch (\Exception $e) {
            $this->error('❌ 日志分析失败: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function cleanupLogs(): int
    {
        $this->info('🧹 开始清理旧日志...');
        
        $keepDays = 30;
        if ($this->confirm("确定要删除 {$keepDays} 天前的日志文件吗？")) {
            try {
                $result = $this->logService->cleanupLogs($keepDays);
                
                $this->info("✅ 日志清理完成！");
                $this->line("  • 删除文件数: {$result['deleted_count']}");
                $this->line("  • 节省空间: {$result['size_saved_human']}");
                
                if (!empty($result['deleted_files'])) {
                    $this->line("  • 删除的文件:");
                    foreach ($result['deleted_files'] as $file) {
                        $this->line("    - {$file}");
                    }
                }

            } catch (\Exception $e) {
                $this->error('❌ 日志清理失败: ' . $e->getMessage());
                return 1;
            }
        }

        return 0;
    }

    private function monitorAnomalies(): int
    {
        $this->info('🔍 监控日志异常...');
        
        try {
            $result = $this->logService->monitorAnomalies();
            
            if ($result['status'] === 'normal') {
                $this->info('✅ 未发现异常');
            } else {
                $this->warn("⚠️  发现 {$result['anomaly_count']} 个异常:");
                
                foreach ($result['anomalies'] as $anomaly) {
                    $icon = match($anomaly['severity']) {
                        'high' => '🔴',
                        'medium' => '🟡',
                        'low' => '🟢',
                        default => '⚪'
                    };
                    
                    $this->line("  {$icon} {$anomaly['message']}");
                    if (isset($anomaly['value'])) {
                        $this->line("     当前值: {$anomaly['value']}");
                    }
                    if (isset($anomaly['threshold'])) {
                        $this->line("     阈值: {$anomaly['threshold']}");
                    }
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ 异常监控失败: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function displayAnalysisResult(array $result): void
    {
        $this->info("📊 日志分析结果:");
        $this->line("  • 文件: {$result['file']}");
        $this->line("  • 文件大小: {$result['file_size_human']}");
        $this->line("  • 最后修改: {$result['last_modified']->format('Y-m-d H:i:s')}");
        $this->line("  • 分析条目: {$result['total_entries']}");

        $this->newLine();
        $this->info("📈 级别统计:");
        foreach ($result['statistics']['level_counts'] as $level => $count) {
            if ($count > 0) {
                $icon = $this->getLevelIcon($level);
                $this->line("  {$icon} {$level}: {$count}");
            }
        }

        if (!empty($result['analysis']['top_errors'])) {
            $this->newLine();
            $this->warn("🔴 主要错误:");
            foreach (array_slice($result['analysis']['top_errors'], 0, 5) as $error) {
                $this->line("  • [{$error['count']}] " . substr($error['message'], 0, 80) . '...');
            }
        }

        if (!empty($result['analysis']['performance_issues'])) {
            $this->newLine();
            $this->warn("⚡ 性能问题:");
            foreach (array_slice($result['analysis']['performance_issues'], 0, 3) as $issue) {
                $this->line("  • {$issue['type']}: " . substr($issue['message'], 0, 60) . '...');
            }
        }

        if (!empty($result['analysis']['security_events'])) {
            $this->newLine();
            $this->error("🔒 安全事件:");
            foreach (array_slice($result['analysis']['security_events'], 0, 3) as $event) {
                $this->line("  • " . substr($event['message'], 0, 60) . '...');
            }
        }
    }

    private function displayStatistics(): void
    {
        $days = (int) $this->option('days');
        $stats = $this->logService->getLogStatistics($days);

        $this->newLine();
        $this->info("📊 {$days}天统计概览:");
        $this->line("  • 日志文件数: " . count($stats['files']));
        $this->line("  • 总大小: {$stats['total_size_human']}");
        
        $totalEntries = array_sum($stats['level_counts']);
        if ($totalEntries > 0) {
            $this->line("  • 总条目数: {$totalEntries}");
            
            $errorCount = $stats['level_counts']['error'] + $stats['level_counts']['critical'];
            $errorRate = round(($errorCount / $totalEntries) * 100, 2);
            $this->line("  • 错误率: {$errorRate}%");
        }

        if (!empty($stats['top_errors'])) {
            $this->newLine();
            $this->warn("🔴 常见错误 (Top 3):");
            foreach (array_slice($stats['top_errors'], 0, 3) as $error) {
                $this->line("  • [{$error['count']}] " . substr($error['message'], 0, 70) . '...');
            }
        }
    }

    private function getLevelIcon(string $level): string
    {
        return match($level) {
            'emergency' => '🚨',
            'alert' => '🔴',
            'critical' => '🔴',
            'error' => '❌',
            'warning' => '⚠️',
            'notice' => '📢',
            'info' => 'ℹ️',
            'debug' => '🐛',
            default => '⚪'
        };
    }
}