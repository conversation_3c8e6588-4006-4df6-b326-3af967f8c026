import{_ as a}from"./index-DtXAftX0.js";/* empty css                *//* empty css               *//* empty css               */import{af as e,ag as s,r as t,L as l,e as i,y as n,l as u,z as r,k as d,B as o,E as c,t as m,D as v,F as p,Y as f,u as _}from"./vue-vendor-Dy164gUc.js";import{aY as b,aZ as g,a_ as y,U as h,a$ as j,at as w,T as A,aX as M,ac as k,Q as x}from"./element-plus-h2SQQM64.js";import{P as C}from"./PageLayout-C6qH3ReN.js";import"./utils-D1VZuEZr.js";const R={key:0,class:"group-detail"},F={class:"info-item"},N={class:"info-item"},P={class:"info-item"},T={class:"info-item"},z={class:"info-item"},D={class:"info-item"},L={class:"info-item"},Z={class:"info-item"},$={class:"info-item"},I={key:0,class:"info-item"},V={key:1,class:"info-item"},Y={class:"stat-item"},B={class:"stat-value"},E={class:"stat-item"},G={class:"stat-value"},O={class:"stat-item"},Q={class:"stat-value"},S={class:"stat-item"},U={class:"stat-value"},X=a({__name:"GroupDetail",setup(a){const X=e(),q=s(),H=t(!0),J=l({id:"",name:"",type:"",status:"",ownerName:"",memberCount:0,maxMembers:0,joinFee:0,description:"",rules:"",tags:[],createdAt:"",lastActiveAt:"",stats:{totalMessages:0,activeMembers:0,totalRevenue:0,conversionRate:0}}),K=a=>({normal:"普通群",vip:"VIP群",distribution:"分销群",test:"测试群"}[a]||"未知"),W=a=>a?new Date(a).toLocaleString("zh-CN"):"-",aa=()=>{X.push(`/community/groups/edit/${q.params.id}`)};return i(()=>{(async()=>{try{H.value=!0,await new Promise(a=>setTimeout(a,1e3)),Object.assign(J,{id:q.params.id,name:"测试群组",type:"normal",status:"active",ownerName:"张三",memberCount:85,maxMembers:100,joinFee:9.9,description:"这是一个测试群组，用于演示群组功能。",rules:"1. 禁止发广告\n2. 保持友善交流\n3. 遵守群规",tags:["热门","活跃"],createdAt:"2024-01-15T10:30:00Z",lastActiveAt:"2024-08-02T14:20:00Z",stats:{totalMessages:1250,activeMembers:68,totalRevenue:841.5,conversionRate:12.5}})}catch(a){x.error("加载群组详情失败"),console.error(a)}finally{H.value=!1}})()}),(a,e)=>{const s=A,t=w,l=y,i=j,x=g,X=b;return u(),n(C,{title:J.name||"群组详情",subtitle:"查看群组详细信息",icon:"View",loading:H.value},{actions:r(()=>[c(t,{onClick:e[0]||(e[0]=e=>a.$router.go(-1))},{default:r(()=>[c(s,null,{default:r(()=>[c(_(M))]),_:1}),e[1]||(e[1]=v(" 返回 ",-1))]),_:1,__:[1]}),c(t,{type:"primary",onClick:aa},{default:r(()=>[c(s,null,{default:r(()=>[c(_(k))]),_:1}),e[2]||(e[2]=v(" 编辑群组 ",-1))]),_:1,__:[2]})]),default:r(()=>[H.value?o("",!0):(u(),d("div",R,[c(X,{class:"info-card",shadow:"never"},{header:r(()=>e[3]||(e[3]=[m("div",{class:"card-header"},[m("h3",null,"基本信息")],-1)])),default:r(()=>[c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",F,[e[4]||(e[4]=m("label",null,"群组名称：",-1)),m("span",null,h(J.name),1)])]),_:1}),c(l,{span:8},{default:r(()=>{return[m("div",N,[e[5]||(e[5]=m("label",null,"群组类型：",-1)),c(i,{type:(a=J.type,{normal:"",vip:"warning",distribution:"success",test:"info"}[a]||"")},{default:r(()=>[v(h(K(J.type)),1)]),_:1},8,["type"])])];var a}),_:1}),c(l,{span:8},{default:r(()=>[m("div",P,[e[6]||(e[6]=m("label",null,"群组状态：",-1)),c(i,{type:"active"===J.status?"success":"danger"},{default:r(()=>[v(h("active"===J.status?"启用":"禁用"),1)]),_:1},8,["type"])])]),_:1})]),_:1}),c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",T,[e[7]||(e[7]=m("label",null,"群主：",-1)),m("span",null,h(J.ownerName),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",z,[e[8]||(e[8]=m("label",null,"当前人数：",-1)),m("span",null,h(J.memberCount)+" / "+h(J.maxMembers),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",D,[e[9]||(e[9]=m("label",null,"入群费用：",-1)),m("span",null,h(J.joinFee)+"元",1)])]),_:1})]),_:1}),c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",L,[e[10]||(e[10]=m("label",null,"创建时间：",-1)),m("span",null,h(W(J.createdAt)),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",Z,[e[11]||(e[11]=m("label",null,"最后活跃：",-1)),m("span",null,h(W(J.lastActiveAt)),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",$,[e[12]||(e[12]=m("label",null,"群组标签：",-1)),(u(!0),d(p,null,f(J.tags,a=>(u(),n(i,{key:a,size:"small",style:{"margin-right":"8px"}},{default:r(()=>[v(h(a),1)]),_:2},1024))),128))])]),_:1})]),_:1}),J.description?(u(),d("div",I,[e[13]||(e[13]=m("label",null,"群组描述：",-1)),m("p",null,h(J.description),1)])):o("",!0),J.rules?(u(),d("div",V,[e[14]||(e[14]=m("label",null,"入群规则：",-1)),m("p",null,h(J.rules),1)])):o("",!0)]),_:1}),c(X,{class:"stats-card",shadow:"never"},{header:r(()=>e[15]||(e[15]=[m("div",{class:"card-header"},[m("h3",null,"统计信息")],-1)])),default:r(()=>[c(x,{gutter:24},{default:r(()=>[c(l,{span:6},{default:r(()=>[m("div",Y,[m("div",B,h(J.stats.totalMessages),1),e[16]||(e[16]=m("div",{class:"stat-label"},"总消息数",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",E,[m("div",G,h(J.stats.activeMembers),1),e[17]||(e[17]=m("div",{class:"stat-label"},"活跃成员",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",O,[m("div",Q,h(J.stats.totalRevenue),1),e[18]||(e[18]=m("div",{class:"stat-label"},"总收入（元）",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",S,[m("div",U,h(J.stats.conversionRate)+"%",1),e[19]||(e[19]=m("div",{class:"stat-label"},"转化率",-1))])]),_:1})]),_:1})]),_:1})]))]),_:1},8,["title","loading"])}}},[["__scopeId","data-v-69398620"]]);export{X as default};
