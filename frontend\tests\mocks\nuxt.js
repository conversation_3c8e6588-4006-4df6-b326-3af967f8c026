import { vi } from 'vitest'

export const useNuxtApp = vi.fn(() => ({
  $toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

export const useRouter = vi.fn(() => ({
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn()
}))

export const useRoute = vi.fn(() => ({
  query: {},
  params: {},
  path: '/login',
  name: 'login'
}))

export const navigateTo = vi.fn()

export const definePageMeta = vi.fn()

export const useSeoMeta = vi.fn()

export const nextTick = vi.fn(() => Promise.resolve())

export const ref = (value) => ({
  value,
  _isRef: true
})

export const reactive = (obj) => obj

export const computed = (fn) => ({
  value: fn(),
  _isRef: true
})
