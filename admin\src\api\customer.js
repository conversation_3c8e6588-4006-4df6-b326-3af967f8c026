import request from '@/utils/request'

/**
 * 客户管理API
 */
export const customerApi = {
  /**
   * 获取客户列表
   */
  getList(params) {
    return request({
      url: '/distributor/customers',
      method: 'get',
      params
    })
  },

  /**
   * 获取客户详情
   */
  getDetail(id) {
    return request({
      url: `/distributor/customers/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建客户
   */
  create(data) {
    return request({
      url: '/distributor/customers',
      method: 'post',
      data
    })
  },

  /**
   * 更新客户
   */
  update(id, data) {
    return request({
      url: `/distributor/customers/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除客户
   */
  delete(id) {
    return request({
      url: `/distributor/customers/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取客户统计
   */
  getStats() {
    return request({
      url: '/distributor/customers/stats',
      method: 'get'
    })
  },

  /**
   * 添加跟进记录
   */
  addFollowUp(customerId, data) {
    return request({
      url: `/distributor/customers/${customerId}/follow-ups`,
      method: 'post',
      data
    })
  },

  /**
   * 获取跟进记录
   */
  getFollowUps(customerId, params) {
    return request({
      url: `/distributor/customers/${customerId}/follow-ups`,
      method: 'get',
      params
    })
  },

  /**
   * 批量更新客户状态
   */
  batchUpdateStatus(data) {
    return request({
      url: '/distributor/customers/batch-status',
      method: 'post',
      data
    })
  },

  /**
   * 导出客户数据
   */
  export(params) {
    return request({
      url: '/distributor/customers/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 获取需要跟进的客户
   */
  getNeedFollowUp(params) {
    return request({
      url: '/distributor/customers/need-follow-up',
      method: 'get',
      params
    })
  },

  /**
   * 获取客户标签
   */
  getTags() {
    return request({
      url: '/distributor/customers/tags',
      method: 'get'
    })
  }
}