<template>
  <el-dialog
    title="选择模板"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    class="template-select-dialog"
  >
    <div class="template-header">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索模板..."
        style="width: 300px;"
        @input="filterTemplates"
      >
        <i slot="prefix" class="el-icon-search"></i>
      </el-input>
      
      <el-select v-model="categoryFilter" placeholder="分类筛选" @change="filterTemplates">
        <el-option label="全部分类" value=""></el-option>
        <el-option label="金融理财" value="finance"></el-option>
        <el-option label="教育培训" value="education"></el-option>
        <el-option label="生活服务" value="life"></el-option>
        <el-option label="电商购物" value="ecommerce"></el-option>
        <el-option label="娱乐休闲" value="entertainment"></el-option>
        <el-option label="其他" value="other"></el-option>
      </el-select>
    </div>

    <div class="template-grid">
      <div 
        v-for="template in filteredTemplates" 
        :key="template.id"
        class="template-card"
        :class="{ 'selected': selectedTemplate?.id === template.id }"
        @click="selectTemplate(template)"
      >
        <div class="template-image">
          <img :src="template.image || '/default-template.png'" :alt="template.name" />
          <div class="template-overlay">
            <el-button size="small" @click.stop="previewTemplate(template)">
              <i class="el-icon-view"></i> 预览
            </el-button>
          </div>
        </div>
        
        <div class="template-info">
          <h4>{{ template.name }}</h4>
          <p class="template-description">{{ template.description }}</p>
          
          <div class="template-tags">
            <el-tag 
              v-for="tag in template.tags" 
              :key="tag" 
              size="mini"
              style="margin-right: 5px;"
            >
              {{ tag }}
            </el-tag>
          </div>
          
          <div class="template-stats">
            <span><i class="el-icon-star-on"></i> {{ template.rating || 5.0 }}</span>
            <span><i class="el-icon-download"></i> {{ template.usage_count || 0 }}次使用</span>
          </div>
          
          <div class="template-category">
            <el-tag type="info" size="mini">{{ getCategoryText(template.category) }}</el-tag>
          </div>
        </div>
        
        <div class="template-actions">
          <el-button 
            size="small" 
            type="primary" 
            @click.stop="useTemplate(template)"
          >
            使用模板
          </el-button>
        </div>
      </div>
    </div>

    <div class="empty-state" v-if="filteredTemplates.length === 0">
      <i class="el-icon-document"></i>
      <p>没有找到符合条件的模板</p>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      title="模板预览"
      :visible.sync="showPreview"
      width="60%"
      :before-close="closePreview"
      append-to-body
    >
      <div v-if="previewTemplate" class="template-preview">
        <div class="preview-header">
          <h3>{{ previewTemplate.name }}</h3>
          <p>{{ previewTemplate.description }}</p>
        </div>
        
        <div class="preview-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="preview-section">
                <h4>基础信息</h4>
                <div class="preview-item">
                  <label>模板名称：</label>
                  <span>{{ previewTemplate.name }}</span>
                </div>
                <div class="preview-item">
                  <label>副标题：</label>
                  <span>{{ previewTemplate.subtitle || '无' }}</span>
                </div>
                <div class="preview-item">
                  <label>建议价格：</label>
                  <span>￥{{ previewTemplate.suggested_price || 0 }}</span>
                </div>
                <div class="preview-item">
                  <label>描述：</label>
                  <p>{{ previewTemplate.description }}</p>
                </div>
              </div>
            </el-col>
            
            <el-col :span="12">
              <div class="preview-section">
                <h4>营销数据</h4>
                <div class="preview-item" v-if="previewTemplate.marketing_data">
                  <label>红包数量：</label>
                  <span>{{ previewTemplate.marketing_data.red_packet_count || '10万+' }}</span>
                </div>
                <div class="preview-item" v-if="previewTemplate.marketing_data">
                  <label>点赞数：</label>
                  <span>{{ previewTemplate.marketing_data.like_count || 324 }}</span>
                </div>
                <div class="preview-item" v-if="previewTemplate.marketing_data">
                  <label>虚拟成员数：</label>
                  <span>{{ previewTemplate.marketing_data.virtual_member_count || 500 }}</span>
                </div>
              </div>
              
              <div class="preview-image" v-if="previewTemplate.image">
                <h4>模板图片</h4>
                <img :src="previewTemplate.image" alt="模板预览图" />
              </div>
            </el-col>
          </el-row>
          
          <div class="preview-section" v-if="previewTemplate.content">
            <h4>内容配置</h4>
            <div class="preview-item" v-if="previewTemplate.content.quick_content">
              <label>快捷内容：</label>
              <p>{{ previewTemplate.content.quick_content }}</p>
            </div>
            <div class="preview-item" v-if="previewTemplate.content.welcome_message">
              <label>欢迎消息：</label>
              <p>{{ previewTemplate.content.welcome_message }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <el-button @click="closePreview">关闭</el-button>
        <el-button type="primary" @click="usePreviewTemplate">使用此模板</el-button>
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="confirmSelect"
        :disabled="!selectedTemplate"
      >
        确定选择
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'TemplateSelectDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    templates: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      selectedTemplate: null,
      searchKeyword: '',
      categoryFilter: '',
      filteredTemplates: [],
      showPreview: false,
      previewTemplate: null
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal) {
        this.initTemplates()
      }
    },
    templates: {
      handler() {
        this.initTemplates()
      },
      immediate: true
    }
  },
  methods: {
    initTemplates() {
      this.filteredTemplates = [...this.templates]
      this.selectedTemplate = null
    },

    selectTemplate(template) {
      this.selectedTemplate = template
    },

    filterTemplates() {
      let filtered = [...this.templates]
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(template => 
          template.name.toLowerCase().includes(keyword) ||
          template.description.toLowerCase().includes(keyword) ||
          (template.tags && template.tags.some(tag => tag.toLowerCase().includes(keyword)))
        )
      }
      
      // 分类筛选
      if (this.categoryFilter) {
        filtered = filtered.filter(template => template.category === this.categoryFilter)
      }
      
      this.filteredTemplates = filtered
    },

    getCategoryText(category) {
      const categories = {
        'finance': '金融理财',
        'education': '教育培训',
        'life': '生活服务',
        'ecommerce': '电商购物',
        'entertainment': '娱乐休闲',
        'other': '其他'
      }
      return categories[category] || '未分类'
    },

    previewTemplate(template) {
      this.previewTemplate = template
      this.showPreview = true
    },

    closePreview() {
      this.showPreview = false
      this.previewTemplate = null
    },

    useTemplate(template) {
      this.selectedTemplate = template
      this.confirmSelect()
    },

    usePreviewTemplate() {
      if (this.previewTemplate) {
        this.selectedTemplate = this.previewTemplate
        this.closePreview()
        this.confirmSelect()
      }
    },

    confirmSelect() {
      if (this.selectedTemplate) {
        this.$emit('select', this.selectedTemplate)
      }
    },

    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.template-select-dialog ::v-deep .el-dialog {
  max-height: 90vh;
  overflow-y: auto;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px;
}

.template-card {
  border: 2px solid #e6e6e6;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.template-card.selected {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.template-image {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.template-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-info {
  padding: 15px;
}

.template-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.template-description {
  color: #666;
  font-size: 13px;
  margin: 0 0 10px 0;
  line-height: 1.4;
  height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.template-tags {
  margin-bottom: 10px;
  min-height: 22px;
}

.template-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.template-stats span {
  display: flex;
  align-items: center;
}

.template-stats i {
  margin-right: 3px;
}

.template-category {
  margin-bottom: 10px;
}

.template-actions {
  padding: 0 15px 15px 15px;
}

.template-actions .el-button {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
}

.template-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.preview-header h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.preview-header p {
  color: #666;
  margin: 0;
}

.preview-section {
  margin-bottom: 25px;
}

.preview-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.preview-item {
  margin-bottom: 10px;
}

.preview-item label {
  font-weight: bold;
  margin-right: 10px;
  color: #333;
}

.preview-item span {
  color: #666;
}

.preview-item p {
  margin: 5px 0 0 0;
  color: #666;
  line-height: 1.5;
}

.preview-image img {
  max-width: 100%;
  border-radius: 4px;
  border: 1px solid #eee;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .template-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .template-header .el-input,
  .template-header .el-select {
    width: 100% !important;
  }
}
</style>