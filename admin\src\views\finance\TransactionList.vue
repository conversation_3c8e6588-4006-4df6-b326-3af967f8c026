<template>
  <div class="app-container">
    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" @submit.prevent="handleQuery">
        <el-form-item label="关键字">
          <el-input v-model="queryParams.keyword" placeholder="订单号/用户昵称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="queryParams.type" placeholder="全部类型" clearable>
            <el-option v-for="(name, type) in transactionTypeMap" :key="type" :label="name" :value="type" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option v-for="(name, status) in statusMap" :key="status" :label="name" :value="status" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-download" @click="handleExport" :loading="exportLoading">导出</el-button>
      </el-col>
    </el-row>

    <!-- 列表 -->
    <el-card>
      <el-table v-loading="loading" :data="transactionList">
        <el-table-column label="交易号" prop="id" width="100" />
        <el-table-column label="用户" prop="user.nickname" width="150" />
        <el-table-column label="交易类型" align="center" width="120">
          <template #default="scope">
            <el-tag :type="getTransactionTypeColor(scope.row.type)">{{ transactionTypeMap[scope.row.type] || '未知' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="金额" width="150">
          <template #default="scope">
            <span :class="scope.row.amount > 0 ? 'text-green' : 'text-red'">
              {{ scope.row.amount > 0 ? '+' : '' }} ¥{{ formatNumber(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">{{ statusMap[scope.row.status] || '未知' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" show-overflow-tooltip />
        <el-table-column label="创建时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
                <el-button link type="primary" @click="handleView(scope.row)">详情</el-button>
            </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit" @pagination="getList" />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="交易详情" v-model="dialog.visible" width="600px">
        <el-descriptions v-if="dialog.data" :column="2" border>
            <el-descriptions-item label="交易号">{{ dialog.data.id }}</el-descriptions-item>
            <el-descriptions-item label="用户">{{ dialog.data.user?.nickname }} (ID: {{ dialog.data.user_id }})</el-descriptions-item>
            <el-descriptions-item label="交易类型">
                <el-tag :type="getTransactionTypeColor(dialog.data.type)">{{ transactionTypeMap[dialog.data.type] || '未知' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="交易状态">
                 <el-tag :type="getStatusColor(dialog.data.status)">{{ statusMap[dialog.data.status] || '未知' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="交易金额">
                 <span :class="dialog.data.amount > 0 ? 'text-green' : 'text-red'">
                    {{ dialog.data.amount > 0 ? '+' : '' }} ¥{{ formatNumber(dialog.data.amount) }}
                </span>
            </el-descriptions-item>
            <el-descriptions-item label="关联订单号">{{ dialog.data.order_id || '无' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ dialog.data.created_at }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ dialog.data.updated_at }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">{{ dialog.data.description }}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
            <el-button @click="dialog.visible = false">关闭</el-button>
        </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue';
import { getTransactionList } from '@/api/finance';
import { exportData } from '@/utils/export';
import Pagination from '@/components/Pagination/index.vue';

// =========== State ===========
const loading = ref(true);
const exportLoading = ref(false);
const transactionList = ref([]);
const total = ref(0);
const dateRange = ref([]);

const transactionTypeMap = {
  recharge: '充值',
  withdraw: '提现',
  commission: '佣金',
  order_payment: '订单支付',
  refund: '退款',
  system_adjust: '系统调账'
};

const statusMap = {
  pending: '处理中',
  completed: '已完成',
  failed: '已失败',
  cancelled: '已取消'
};

const data = reactive({
  queryParams: {
    page: 1,
    limit: 10,
    keyword: undefined,
    type: undefined,
    status: undefined,
    start_date: undefined,
    end_date: undefined,
  },
  dialog: {
    visible: false,
    data: null
  },
});

const { queryParams, dialog } = toRefs(data);

// =========== Helpers ===========
const formatNumber = (num) => num ? parseFloat(num).toFixed(2) : '0.00';
const getTransactionTypeColor = (type) => ({ recharge: 'success', withdraw: 'warning', commission: 'primary', order_payment: 'info', refund: 'danger', system_adjust: '' }[type] || 'info');
const getStatusColor = (status) => ({ pending: 'warning', completed: 'success', failed: 'danger', cancelled: 'info' }[status] || 'info');

// =========== Lifecycle ===========
onMounted(() => {
  getList();
});

// =========== API Calls & Actions ===========
async function getList() {
  loading.value = true;
  try {
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.value.start_date = dateRange.value[0];
      queryParams.value.end_date = dateRange.value[1];
    } else {
      queryParams.value.start_date = undefined;
      queryParams.value.end_date = undefined;
    }
    const response = await getTransactionList(queryParams.value);
    transactionList.value = response.data.data;
    total.value = response.data.total;
  } finally {
    loading.value = false;
  }
}

function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

function resetQuery() {
  dateRange.value = [];
  queryParams.value = {
    page: 1,
    limit: 10,
    keyword: undefined,
    type: undefined,
    status: undefined,
    start_date: undefined,
    end_date: undefined,
  };
  handleQuery();
}

async function handleExport() {
    exportLoading.value = true;
    try {
        await exportData('/finance/transactions/export', queryParams.value, `transactions_${Date.now()}.xlsx`);
    } finally {
        exportLoading.value = false;
    }
}

function handleView(row) {
    dialog.value.data = row;
    dialog.value.visible = true;
}

</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-card {
  margin-bottom: 20px;
}
.mb8 {
  margin-bottom: 20px;
}
.text-green {
    color: #67c23a;
}
.text-red {
    color: #f56c6c;
}
</style> 