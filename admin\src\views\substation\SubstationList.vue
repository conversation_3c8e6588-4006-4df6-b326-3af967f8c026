<template>
  <div class="modern-substation-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><OfficeBuilding /></el-icon>
          </div>
          <div class="header-text">
            <h1>分站管理</h1>
            <p>管理平台分站，配置分站权限，监控分站运营状况</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="showHelpDialog = true" class="action-btn secondary">
            <el-icon><QuestionFilled /></el-icon>
            功能说明
          </el-button>
          <el-button type="primary" @click="handleAdd" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            新增分站
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-card" v-for="stat in substationStats" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索分站名称、域名、管理员"
              prefix-icon="Search"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            />
            <el-select
              v-model="searchForm.status"
              placeholder="分站状态"
              clearable
              class="filter-select"
            >
              <el-option label="全部状态" value="" />
              <el-option label="正常运营" value="1" />
              <el-option label="已禁用" value="2" />
              <el-option label="已过期" value="3" />
            </el-select>
            <el-select
              v-model="searchForm.region"
              placeholder="所属区域"
              clearable
              class="filter-select"
            >
              <el-option label="全部区域" value="" />
              <el-option label="华北地区" value="north" />
              <el-option label="华东地区" value="east" />
              <el-option label="华南地区" value="south" />
              <el-option label="西部地区" value="west" />
            </el-select>
          </div>
          <div class="filter-right">
            <el-button @click="handleSearch" type="primary" class="search-btn">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset" class="reset-btn">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <el-card>
      <!-- 筛选区域 -->
      <el-form :inline="true" :model="queryParams" class="filter-container">
        <el-form-item label="分站名称">
          <el-input v-model="queryParams.name" placeholder="请输入分站名称" clearable />
        </el-form-item>
        <el-form-item label="域名">
          <el-input v-model="queryParams.domain" placeholder="请输入域名" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="2" />
            <el-option label="过期" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb-2">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增分站</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="!multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table :data="substationList" @selection-change="handleSelectionChange" v-loading="loading">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="分站名称" prop="name" show-overflow-tooltip />
        <el-table-column label="域名" prop="domain" show-overflow-tooltip />
        <el-table-column label="管理员" prop="user.nickname" />
        <el-table-column label="抽成比例" align="center">
          <template #default="scope">
            {{ (scope.row.commission_rate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
                      <el-table-column label="到期时间" prop="expire_at" />
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="操作" width="280" align="center">
          <template #default="scope">
            <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button type="warning" link icon="CreditCard" @click="handlePaymentConfig(scope.row)">支付配置</el-button>
            <el-button type="success" link icon="Clock" @click="handleRenew(scope.row)">续费</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.per_page"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑分站对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="分站名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分站名称" />
        </el-form-item>
        <el-form-item label="分站域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入分站域名">
            <template #append>.linkhub.pro</template>
          </el-input>
        </el-form-item>
        <el-form-item label="管理员" prop="user_id">
          <el-select v-model="form.user_id" placeholder="请选择管理员" filterable>
            <el-option
              v-for="admin in adminList"
              :key="admin.id"
              :label="`${admin.nickname}(${admin.username})`"
              :value="admin.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="抽成比例" prop="commission_rate">
          <el-input-number v-model="form.commission_rate" :precision="3" :step="0.001" :min="0" :max="1" />
          <span class="ml-2">（0-1之间的小数，如0.1表示10%）</span>
        </el-form-item>
        <el-form-item label="有效期" prop="expire_months">
          <el-select v-model="form.expire_months" placeholder="请选择有效期">
            <el-option label="1个月" :value="1" />
            <el-option label="3个月" :value="3" />
            <el-option label="6个月" :value="6" />
            <el-option label="12个月" :value="12" />
            <el-option label="24个月" :value="24" />
            <el-option label="36个月" :value="36" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入分站描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 续费对话框 -->
    <el-dialog title="分站续费" v-model="renewDialog.visible" width="500px" append-to-body>
      <el-form ref="renewFormRef" :model="renewForm" :rules="renewRules" label-width="100px">
        <el-form-item label="分站名称">
          <el-input :value="renewDialog.substationName" readonly />
        </el-form-item>
        <el-form-item label="当前到期时间">
          <el-input :value="renewDialog.currentExpireDate" readonly />
        </el-form-item>
        <el-form-item label="续费时长" prop="months">
          <el-select v-model="renewForm.months" placeholder="请选择续费时长">
            <el-option label="1个月" :value="1" />
            <el-option label="3个月" :value="3" />
            <el-option label="6个月" :value="6" />
            <el-option label="12个月" :value="12" />
            <el-option label="24个月" :value="24" />
          </el-select>
        </el-form-item>
        <el-form-item label="续费后到期时间" v-if="renewForm.months">
          <el-input :value="getNewExpireDate()" readonly />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitRenewForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 支付配置对话框 -->
    <el-dialog :title="paymentDialog.title" v-model="paymentDialog.visible" width="800px" append-to-body>
      <el-tabs v-model="activePaymentTab" type="card">
        <el-tab-pane 
          v-for="channel in availableChannels" 
          :key="channel.channel_code"
          :label="channel.channel_name" 
          :name="channel.channel_code"
        >
          <el-card shadow="never" style="margin-bottom: 20px;">
            <template #header>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ channel.channel_name }}配置</span>
                <div>
                  <el-tag v-if="channel.has_config" :type="channel.test_status ? 'success' : 'warning'">
                    {{ channel.test_status ? '已测试通过' : '未测试' }}
                  </el-tag>
                  <el-tag v-else type="info">未配置</el-tag>
                </div>
              </div>
            </template>
            
            <el-form 
              :ref="`paymentForm_${channel.channel_code}`"
              :model="paymentConfigs[channel.channel_code]"
              :rules="paymentRules"
              label-width="120px"
            >
              <template v-if="channel.channel_code === 'payoreo'">
                <el-form-item label="配置名称" prop="config_name">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].config_name" 
                    placeholder="请输入配置名称"
                  />
                </el-form-item>
                <el-form-item label="API地址" prop="api_url">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].api_url" 
                    placeholder="https://api.payoreo.com"
                  />
                </el-form-item>
                <el-form-item label="商户ID" prop="pid">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].pid" 
                    placeholder="请输入易支付商户ID"
                  />
                </el-form-item>
                <el-form-item label="商户秘钥" prop="key">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].key" 
                    type="password" 
                    show-password
                    placeholder="请输入易支付商户秘钥"
                  />
                </el-form-item>
                <el-form-item label="异步通知地址">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].notify_url" 
                    placeholder="留空则使用系统默认"
                  />
                  <div class="form-tip">系统默认: {{ getDefaultNotifyUrl() }}</div>
                </el-form-item>
                <el-form-item label="同步返回地址">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].return_url" 
                    placeholder="留空则使用系统默认"
                  />
                  <div class="form-tip">用户支付成功后的跳转页面</div>
                </el-form-item>
              </template>
              
              <template v-else>
                <el-alert 
                  :title="`${channel.channel_name}配置功能开发中`" 
                  type="info" 
                  show-icon 
                  :closable="false"
                />
              </template>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  @click="savePaymentConfig(channel.channel_code)"
                  :loading="saving"
                >
                  保存配置
                </el-button>
                <el-button 
                  type="success" 
                  @click="testPaymentConfig(channel.channel_code)"
                  :loading="testing"
                  :disabled="!channel.has_config"
                >
                  测试配置
                </el-button>
                <el-button 
                  type="info" 
                  @click="loadPaymentConfig(channel.channel_code)"
                  v-if="channel.has_config"
                >
                  重新加载
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="分站管理功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>🏢 功能概述</h3>
          <p>分站管理系统是平台多站点运营的核心功能，支持创建和管理多个独立的分站点，每个分站拥有独立的域名、管理员和配置，实现平台的规模化扩展和区域化运营。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Plus /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>分站创建</h4>
                  <p>创建新的分站点，配置基本信息和管理权限</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>分站配置</h4>
                  <p>设置分站域名、抽成比例、有效期等参数</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>支付配置</h4>
                  <p>为分站配置独立的支付通道和参数</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>管理员管理</h4>
                  <p>指定分站管理员，分配管理权限</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>续费管理</h4>
                  <p>管理分站有效期，处理续费申请</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Monitor /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>状态监控</h4>
                  <p>实时监控分站运行状态和业务数据</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 分站状态说明 -->
        <div class="help-section">
          <h3>📊 分站状态说明</h3>
          <el-table :data="statusExplanation" style="width: 100%">
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="状态描述" />
            <el-table-column prop="features" label="功能限制" />
            <el-table-column prop="action" label="处理建议" />
          </el-table>
        </div>

        <!-- 抽成比例设置 -->
        <div class="help-section">
          <h3>💰 抽成比例设置指南</h3>
          <div class="commission-guide">
            <div class="guide-item">
              <h4>🔸 抽成比例说明</h4>
              <p>抽成比例是平台从分站收入中提取的分成比例，用小数表示（如0.1表示10%）</p>
              <div class="commission-examples">
                <div class="example-item">
                  <span class="example-label">示例1：</span>
                  <span>设置0.05，表示平台抽成5%，分站保留95%</span>
                </div>
                <div class="example-item">
                  <span class="example-label">示例2：</span>
                  <span>设置0.15，表示平台抽成15%，分站保留85%</span>
                </div>
              </div>
            </div>
            <div class="guide-item">
              <h4>🔸 推荐设置范围</h4>
              <el-table :data="commissionRanges" size="small">
                <el-table-column prop="type" label="分站类型" width="120" />
                <el-table-column prop="range" label="推荐比例" width="120" />
                <el-table-column prop="reason" label="设置理由" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 支付配置指南 -->
        <div class="help-section">
          <h3>💳 支付配置指南</h3>
          <div class="payment-guide">
            <div class="guide-step">
              <h4>📋 配置步骤</h4>
              <ol>
                <li>点击分站列表中的"支付配置"按钮</li>
                <li>选择要配置的支付通道（如易支付）</li>
                <li>填写支付通道的配置信息</li>
                <li>测试配置是否正确</li>
                <li>保存配置并启用</li>
              </ol>
            </div>
            <div class="guide-step">
              <h4>⚙️ 易支付配置说明</h4>
              <div class="config-fields">
                <div class="field-item">
                  <strong>API地址：</strong>易支付平台的API接口地址
                </div>
                <div class="field-item">
                  <strong>商户ID：</strong>在易支付平台注册的商户标识
                </div>
                <div class="field-item">
                  <strong>商户秘钥：</strong>用于签名验证的密钥，请妥善保管
                </div>
                <div class="field-item">
                  <strong>通知地址：</strong>支付成功后的异步通知地址
                </div>
                <div class="field-item">
                  <strong>返回地址：</strong>支付成功后用户跳转的页面
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何创建新分站？" name="create-substation">
              <div class="guide-content">
                <ol>
                  <li>点击页面右上角的"新增分站"按钮</li>
                  <li>填写分站基本信息：
                    <ul>
                      <li>分站名称：建议使用有意义的名称</li>
                      <li>分站域名：输入二级域名（系统自动添加后缀）</li>
                      <li>管理员：选择负责该分站的管理员</li>
                      <li>抽成比例：设置平台分成比例</li>
                      <li>有效期：选择分站的使用期限</li>
                    </ul>
                  </li>
                  <li>填写分站描述（可选）</li>
                  <li>点击"确定"完成创建</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：域名创建后需要进行DNS解析配置才能正常访问
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何配置分站支付？" name="payment-config">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中找到目标分站</li>
                  <li>点击"支付配置"按钮</li>
                  <li>选择支付通道标签页</li>
                  <li>填写支付配置信息：
                    <ul>
                      <li>配置名称：便于识别的名称</li>
                      <li>API地址：支付平台的接口地址</li>
                      <li>商户信息：商户ID和密钥</li>
                      <li>回调地址：通知和返回地址</li>
                    </ul>
                  </li>
                  <li>点击"测试配置"验证设置</li>
                  <li>测试通过后点击"保存配置"</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：商户密钥等敏感信息请妥善保管，不要泄露
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何为分站续费？" name="renew-substation">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中找到需要续费的分站</li>
                  <li>点击"续费"按钮</li>
                  <li>查看当前到期时间</li>
                  <li>选择续费时长（1-24个月）</li>
                  <li>确认续费后的到期时间</li>
                  <li>点击"确定"完成续费</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：续费后分站状态会自动更新，有效期延长
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何管理分站状态？" name="manage-status">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中查看各分站的状态</li>
                  <li>正常状态：分站正常运行，所有功能可用</li>
                  <li>禁用状态：分站被暂停，用户无法访问</li>
                  <li>过期状态：分站已过期，需要续费才能恢复</li>
                  <li>可以通过编辑功能修改分站状态</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：定期检查分站状态，及时处理过期和异常情况
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 最佳实践 -->
        <div class="help-section">
          <h3>💡 最佳实践建议</h3>
          <div class="best-practices">
            <div class="practice-item">
              <div class="practice-icon">🎯</div>
              <div class="practice-content">
                <h4>合理规划分站</h4>
                <p>根据业务需求和地域特点合理规划分站数量和分布，避免过度分散或集中</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">👥</div>
              <div class="practice-content">
                <h4>选择合适管理员</h4>
                <p>为每个分站选择有经验、负责任的管理员，确保分站正常运营</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">💰</div>
              <div class="practice-content">
                <h4>合理设置抽成</h4>
                <p>根据分站规模、运营成本和市场情况合理设置抽成比例，平衡各方利益</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">🔒</div>
              <div class="practice-content">
                <h4>加强安全管理</h4>
                <p>定期检查分站安全状况，及时更新配置，防范安全风险</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="分站域名如何解析？" name="faq1">
              <p>分站创建后，需要在DNS服务商处添加CNAME记录，将分站域名指向主站域名。具体操作请联系技术支持。</p>
            </el-collapse-item>
            <el-collapse-item title="抽成比例可以随时修改吗？" name="faq2">
              <p>可以的。管理员可以随时编辑分站信息修改抽成比例，修改后立即生效，影响后续的收入分成计算。</p>
            </el-collapse-item>
            <el-collapse-item title="分站过期后数据会丢失吗？" name="faq3">
              <p>分站过期后数据不会立即删除，但用户无法访问。建议在过期前及时续费，或联系管理员备份重要数据。</p>
            </el-collapse-item>
            <el-collapse-item title="如何批量管理多个分站？" name="faq4">
              <p>可以使用表格上方的批量操作功能，选中多个分站后进行批量删除等操作。更多批量功能正在开发中。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getSubstationList, createSubstation, updateSubstation, deleteSubstation, renewSubstation } from '@/api/substation'
import { getUserList } from '@/api/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  Plus, QuestionFilled, Setting, CreditCard, User, Clock, Monitor,
  OfficeBuilding, Download, Search, RefreshLeft, CircleCheckFilled,
  Money, CaretTop, Edit, Delete
} from '@element-plus/icons-vue'
import Pagination from '@/components/Pagination/index.vue'
import dayjs from 'dayjs'

const loading = ref(true)
const substationList = ref([])
const selectedSubstations = ref([])
const total = ref(0)
const multiple = ref(false)
const ids = ref([])
const adminList = ref([])
const showHelpDialog = ref(false)

// 现代化搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  region: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20
})

// 现代化统计数据 - 与其他页面保持一致的设计
const substationStats = ref([
  {
    key: 'total',
    label: '总分站数',
    value: 12,
    icon: 'OfficeBuilding',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'CaretTop',
    change: '+3'
  },
  {
    key: 'active',
    label: '运营中',
    value: 9,
    icon: 'CircleCheckFilled',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'CaretTop',
    change: '+2'
  },
  {
    key: 'revenue',
    label: '总收益',
    value: '¥45,678',
    icon: 'Money',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'CaretTop',
    change: '+15.8%'
  },
  {
    key: 'users',
    label: '总用户数',
    value: 2456,
    icon: 'User',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'CaretTop',
    change: '+128'
  }
])

// 帮助对话框相关数据
const activeGuides = ref(['create-substation'])
const activeFAQ = ref([])

// 分站状态说明数据
const statusExplanation = ref([
  {
    status: '正常',
    color: 'success',
    description: '分站正常运行，所有功能可用',
    features: '无限制',
    action: '保持现状，定期监控'
  },
  {
    status: '禁用',
    color: 'info',
    description: '分站被管理员暂停使用',
    features: '用户无法访问，管理员可登录',
    action: '检查禁用原因，解决后重新启用'
  },
  {
    status: '过期',
    color: 'danger',
    description: '分站使用期限已到期',
    features: '所有功能停用，数据保留',
    action: '联系客户续费或备份数据'
  }
])

// 抽成比例范围数据
const commissionRanges = ref([
  {
    type: '新建分站',
    range: '5%-10%',
    reason: '吸引新客户，建立合作关系'
  },
  {
    type: '标准分站',
    range: '10%-15%',
    reason: '平衡收益与成本，维持正常运营'
  },
  {
    type: '高级分站',
    range: '15%-20%',
    reason: '提供更多服务，获得更高收益'
  },
  {
    type: '企业分站',
    range: '8%-12%',
    reason: '大客户优惠，长期合作考虑'
  }
])

const queryParams = reactive({
  page: 1,
  per_page: 10,
  name: undefined,
  domain: undefined,
  status: undefined
})

const dialog = reactive({
  visible: false,
  title: ''
})

const renewDialog = reactive({
  visible: false,
  substationName: '',
  currentExpireDate: ''
})

const paymentDialog = reactive({
  visible: false,
  title: '',
  substationId: null
})

const activePaymentTab = ref('payoreo')
const availableChannels = ref([])
const paymentConfigs = reactive({})
const saving = ref(false)
const testing = ref(false)

const formRef = ref()
const renewFormRef = ref()

const form = ref({})
const renewForm = ref({
  months: null
})

const rules = reactive({
  name: [{ required: true, message: '分站名称不能为空', trigger: 'blur' }],
  domain: [{ required: true, message: '分站域名不能为空', trigger: 'blur' }],
  user_id: [{ required: true, message: '管理员不能为空', trigger: 'change' }],
  commission_rate: [{ required: true, message: '抽成比例不能为空', trigger: 'blur' }],
  expire_months: [{ required: true, message: '有效期不能为空', trigger: 'change' }]
})

const renewRules = reactive({
  months: [{ required: true, message: '续费时长不能为空', trigger: 'change' }]
})

const paymentRules = reactive({
  config_name: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
  api_url: [
    { required: true, message: 'API地址不能为空', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  pid: [{ required: true, message: '商户ID不能为空', trigger: 'blur' }],
  key: [{ required: true, message: '商户秘钥不能为空', trigger: 'blur' }]
})

// 现代化方法集合
const methods = {
  // 获取分站列表
  async fetchSubstationList() {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 模拟数据 - 更丰富的数据结构
      substationList.value = [
        {
          id: 1,
          name: '北京分站',
          domain: 'beijing.example.com',
          manager: '张三',
          phone: '13800138001',
          email: '<EMAIL>',
          status: 1,
          userCount: 1256,
          revenue: 125800,
          todayRevenue: 3200,
          groupCount: 45,
          region: '华北',
          address: '北京市朝阳区建国门外大街1号',
          created_at: '2024-01-15',
          expire_at: '2025-01-15',
          lastLoginAt: '2024-03-15 14:30:00',
          commission_rate: 0.1,
          user: { nickname: '张三' },
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        {
          id: 2,
          name: '上海分站',
          domain: 'shanghai.example.com',
          manager: '李四',
          phone: '13800138002',
          email: '<EMAIL>',
          status: 1,
          userCount: 2134,
          revenue: 189000,
          todayRevenue: 4500,
          groupCount: 67,
          region: '华东',
          address: '上海市浦东新区陆家嘴环路1000号',
          created_at: '2024-01-20',
          expire_at: '2025-01-20',
          lastLoginAt: '2024-03-15 16:45:00',
          commission_rate: 0.12,
          user: { nickname: '李四' },
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        {
          id: 3,
          name: '广州分站',
          domain: 'guangzhou.example.com',
          manager: '王五',
          phone: '13800138003',
          email: '<EMAIL>',
          status: 2,
          userCount: 789,
          revenue: 56000,
          todayRevenue: 0,
          groupCount: 23,
          region: '华南',
          address: '广州市天河区珠江新城花城大道5号',
          created_at: '2024-02-01',
          expire_at: '2025-02-01',
          lastLoginAt: '2024-03-10 09:20:00',
          commission_rate: 0.08,
          user: { nickname: '王五' },
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        },
        {
          id: 4,
          name: '深圳分站',
          domain: 'shenzhen.example.com',
          manager: '赵六',
          phone: '13800138004',
          email: '<EMAIL>',
          status: 1,
          userCount: 1567,
          revenue: 234500,
          todayRevenue: 5600,
          groupCount: 89,
          region: '华南',
          address: '深圳市南山区科技园南区深南大道9988号',
          created_at: '2024-01-10',
          expire_at: '2025-01-10',
          lastLoginAt: '2024-03-15 18:20:00',
          commission_rate: 0.15,
          user: { nickname: '赵六' },
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        }
      ]
      total.value = 4
      
      // 更新统计数据
      methods.updateStats()
      
    } catch (error) {
      ElMessage.error('获取分站列表失败')
      console.error('获取分站列表错误:', error)
    } finally {
      loading.value = false
    }
  },

  // 更新统计数据
  updateStats() {
    const activeCount = substationList.value.filter(item => item.status === 1).length
    const totalRevenue = substationList.value.reduce((sum, item) => sum + item.revenue, 0)
    const totalUsers = substationList.value.reduce((sum, item) => sum + item.userCount, 0)
    
    substationStats.value[0].value = substationList.value.length
    substationStats.value[1].value = activeCount
    substationStats.value[2].value = `¥${(totalRevenue / 10000).toFixed(1)}万`
    substationStats.value[3].value = totalUsers
  },

  // 现代化搜索功能
  handleSearch() {
    pagination.page = 1
    methods.fetchSubstationList()
    ElMessage.success('搜索完成')
  },

  // 重置搜索
  handleReset() {
    Object.assign(searchForm, {
      keyword: '',
      status: '',
      region: ''
    })
    methods.handleSearch()
  },

  // 导出数据
  async handleExport() {
    try {
      ElMessage.info('正在导出数据...')
      
      // 模拟导出API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      ElMessage.success('导出成功')
    } catch (error) {
      ElMessage.error('导出失败')
    }
  }
}

const getList = async () => {
  // 使用现代化方法
  await methods.fetchSubstationList()
}

const handleQuery = () => {
  queryParams.page = 1
  getList()
}

const resetQuery = () => {
  queryParams.page = 1
  queryParams.name = undefined
  queryParams.domain = undefined
  queryParams.status = undefined
  handleQuery()
}

const handleAdd = async () => {
  await getAdminList()
  form.value = {
    commission_rate: 0.1,
    expire_months: 12
  }
  dialog.title = '新增分站'
  dialog.visible = true
}

const handleUpdate = async (row) => {
  await getAdminList()
  form.value = { ...row }
  dialog.title = '编辑分站'
  dialog.visible = true
}

const handleDelete = (row) => {
  const substationIds = row.id ? [row.id] : ids.value
  ElMessageBox.confirm(`是否确认删除ID为"${substationIds.join(',')}"的分站？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    for (const id of substationIds) {
      await deleteSubstation(id)
    }
    getList()
    ElMessage.success('删除成功')
  }).catch(() => {})
}

const handleRenew = (row) => {
  renewDialog.substationName = row.name
  renewDialog.currentExpireDate = row.expire_at
  renewForm.value = {
    substation_id: row.id,
    current_expire_date: row.expire_at,
    months: null
  }
  renewDialog.visible = true
}

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  ids.value = validSelection.map(item => item.id)
  multiple.value = validSelection.length > 0
}

const cancel = () => {
  dialog.visible = false
  formRef.value.resetFields()
}

const submitForm = async () => {
  await formRef.value.validate()
  if (form.value.id) {
    await updateSubstation(form.value.id, form.value)
    ElMessage.success('修改成功')
  } else {
    await createSubstation(form.value)
    ElMessage.success('新增成功')
  }
  dialog.visible = false
  getList()
}

const submitRenewForm = async () => {
  await renewFormRef.value.validate()
  await renewSubstation(renewForm.value.substation_id, renewForm.value.months)
  ElMessage.success('续费成功')
  renewDialog.visible = false
  getList()
}

const getAdminList = async () => {
  const { data } = await getUserList({ role: 'substation', per_page: 1000 })
  adminList.value = data.data
}

const getNewExpireDate = () => {
  if (!renewForm.value.months || !renewForm.value.current_expire_date) {
    return ''
  }
  return dayjs(renewForm.value.current_expire_date).add(renewForm.value.months, 'month').format('YYYY-MM-DD HH:mm:ss')
}

const getStatusTagType = (status) => {
  const statusMap = {
    1: 'success',
    2: 'info',
    3: 'danger'
  }
  return statusMap[status]
}

const getStatusName = (status) => {
  const statusMap = {
    1: '正常',
    2: '禁用',
    3: '过期'
  }
  return statusMap[status]
}

// 支付配置相关方法
const handlePaymentConfig = async (row) => {
  paymentDialog.title = `${row.name} - 支付配置`
  paymentDialog.substationId = row.id
  paymentDialog.visible = true
  
  // 获取可配置的支付通道
  await getAvailablePaymentChannels(row.id)
  
  // 初始化支付配置表单
  initPaymentConfigs()
}

const getAvailablePaymentChannels = async (substationId) => {
  try {
    // 调用API获取分站可配置的支付通道
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-channels`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        availableChannels.value = result.data
        
        // 如果有可用通道，默认选择第一个
        if (result.data.length > 0) {
          activePaymentTab.value = result.data[0].channel_code
        }
      }
    }
  } catch (error) {
    console.error('获取支付通道失败:', error)
    ElMessage.error('获取支付通道失败')
  }
}

const initPaymentConfigs = () => {
  availableChannels.value.forEach(channel => {
    if (!paymentConfigs[channel.channel_code]) {
      paymentConfigs[channel.channel_code] = {
        config_name: `${channel.channel_name}配置`,
        api_url: channel.channel_code === 'payoreo' ? 'https://api.payoreo.com' : '',
        pid: '',
        key: '',
        notify_url: '',
        return_url: ''
      }
    }
    
    // 如果已有配置，加载现有配置
    if (channel.has_config) {
      loadPaymentConfig(channel.channel_code)
    }
  })
}

const loadPaymentConfig = async (channelCode) => {
  try {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-config/${channelCode}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        paymentConfigs[channelCode] = { ...paymentConfigs[channelCode], ...result.data.config_data }
      }
    }
  } catch (error) {
    console.error('加载支付配置失败:', error)
  }
}

const savePaymentConfig = async (channelCode) => {
  try {
    saving.value = true
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-config`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        channel_code: channelCode,
        config_name: paymentConfigs[channelCode].config_name,
        config_data: paymentConfigs[channelCode]
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        ElMessage.success('支付配置保存成功')
        // 重新获取支付通道状态
        await getAvailablePaymentChannels(paymentDialog.substationId)
      } else {
        ElMessage.error(result.message || '保存失败')
      }
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存支付配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const testPaymentConfig = async (channelCode) => {
  try {
    testing.value = true
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/test-config`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        channel_code: channelCode
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        ElMessage.success('配置测试通过')
        // 重新获取支付通道状态
        await getAvailablePaymentChannels(paymentDialog.substationId)
      } else {
        ElMessage.error(result.message || '测试失败')
      }
    } else {
      ElMessage.error('测试失败')
    }
  } catch (error) {
    console.error('测试支付配置失败:', error)
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
  }
}

const getDefaultNotifyUrl = () => {
  return `${window.location.origin}/api/v1/payment/notify/payoreo`
}

// 页面初始化
onMounted(() => {
  methods.fetchSubstationList()
})

// 现代化搜索方法
const handleSearch = () => methods.handleSearch()
const handleReset = () => methods.handleReset()
const handleExport = () => methods.handleExport()
</script>

<style lang="scss" scoped>
.modern-substation-list {
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部样式
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    margin-bottom: 24px;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
      }

      .header-text {
        h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        p {
          margin: 0;
          opacity: 0.9;
          font-size: 14px;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .action-btn {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.secondary {
          background: rgba(255, 255, 255, 0.15);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
          }
        }

        &.primary {
          background: white;
          color: #667eea;
          border: none;

          &:hover {
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }

  // 统计卡片样式
  .stats-section {
    margin-bottom: 24px;
    padding: 0 32px;

    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--stat-color, #667eea);
      }

      .stat-icon {
        width: 56px;
        height: 56px;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: 700;
          color: #1a1a1a;
          margin-bottom: 4px;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;

        &.up {
          color: #10b981;
          background: #ecfdf5;
        }

        &.down {
          color: #ef4444;
          background: #fef2f2;
        }
      }
    }
  }

  // 筛选区域样式
  .filter-section {
    margin-bottom: 24px;
    padding: 0 32px;

    .filter-card {
      max-width: 1400px;
      margin: 0 auto;
      border-radius: 16px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .filter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;

      .filter-left {
        display: flex;
        gap: 16px;
        flex: 1;

        .search-input {
          width: 300px;

          :deep(.el-input__wrapper) {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          }
        }

        .filter-select {
          width: 160px;

          :deep(.el-select__wrapper) {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          }
        }
      }

      .filter-right {
        display: flex;
        gap: 12px;

        .search-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 8px;
          padding: 0 24px;
          height: 40px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }
        }

        .reset-btn {
          border-radius: 8px;
          height: 40px;
          padding: 0 20px;
          border: 1px solid #d1d5db;

          &:hover {
            border-color: #667eea;
            color: #667eea;
          }
        }
      }
    }
  }

  // 主要内容区域
  :deep(.el-card) {
    margin: 0 32px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    border-radius: 16px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .el-card__body {
      padding: 24px;
    }
  }

  // 表格样式优化
  :deep(.el-table) {
    border-radius: 12px;
    overflow: hidden;

    .el-table__header {
      background: #f8fafc;

      th {
        background: #f8fafc !important;
        border: none;
        color: #374151;
        font-weight: 600;
        font-size: 14px;
      }
    }

    .el-table__body {
      tr {
        transition: all 0.3s ease;

        &:hover {
          background: #f8fafc;
        }

        td {
          border: none;
          border-bottom: 1px solid #f1f5f9;
          padding: 16px 12px;
        }
      }
    }

    .el-button--link {
      margin-right: 8px;
      font-weight: 500;
      border-radius: 6px;
      padding: 4px 8px;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
      }
    }
  }

  // 分页样式
  :deep(.pagination-container) {
    margin-top: 24px;
    padding: 20px 0;
    border-top: 1px solid #f1f5f9;
  }

  // 对话框样式
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;

    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 24px;
      margin: 0;

      .el-dialog__title {
        color: white;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: white;
          font-size: 18px;

          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 20px 24px;
      border-top: 1px solid #f1f5f9;
    }
  }

  // 帮助对话框特殊样式
  .help-dialog {
    :deep(.el-dialog__body) {
      max-height: 70vh;
      overflow-y: auto;
      padding: 0;
    }

    .help-content {
      padding: 24px;

      .help-section {
        margin-bottom: 32px;

        h3 {
          color: #1f2937;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        p {
          color: #6b7280;
          line-height: 1.6;
          margin-bottom: 16px;
        }
      }

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: #f8fafc;
        border-radius: 12px;
        margin-bottom: 12px;

        .feature-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }

        .feature-content {
          h4 {
            margin: 0 0 4px 0;
            color: #1f2937;
            font-size: 14px;
            font-weight: 600;
          }

          p {
            margin: 0;
            color: #6b7280;
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }

      .best-practices {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;

        .practice-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 20px;
          background: white;
          border-radius: 12px;
          border: 1px solid #e5e7eb;

          .practice-icon {
            font-size: 24px;
            flex-shrink: 0;
          }

          .practice-content {
            h4 {
              margin: 0 0 8px 0;
              color: #1f2937;
              font-size: 16px;
              font-weight: 600;
            }

            p {
              margin: 0;
              color: #6b7280;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }

      .commission-guide {
        .guide-item {
          margin-bottom: 24px;
          padding: 20px;
          background: #f8fafc;
          border-radius: 12px;

          h4 {
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
          }

          .commission-examples {
            margin-top: 12px;

            .example-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              font-size: 14px;

              .example-label {
                color: #667eea;
                font-weight: 600;
              }
            }
          }
        }
      }

      .payment-guide {
        .guide-step {
          margin-bottom: 24px;
          padding: 20px;
          background: #f8fafc;
          border-radius: 12px;

          h4 {
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
          }

          ol {
            margin: 0;
            padding-left: 20px;
            color: #6b7280;

            li {
              margin-bottom: 8px;
              line-height: 1.5;

              ul {
                margin-top: 8px;
                padding-left: 20px;

                li {
                  margin-bottom: 4px;
                }
              }
            }
          }

          .config-fields {
            .field-item {
              margin-bottom: 12px;
              padding: 12px;
              background: white;
              border-radius: 8px;
              font-size: 14px;
              line-height: 1.5;

              strong {
                color: #1f2937;
              }
            }
          }
        }
      }

      .guide-content {
        ol {
          margin: 0;
          padding-left: 20px;
          color: #6b7280;

          li {
            margin-bottom: 8px;
            line-height: 1.5;

            ul {
              margin-top: 8px;
              padding-left: 20px;

              li {
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }

  // 表单提示样式
  .form-tip {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .page-header {
      padding: 16px 20px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
      }

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .stats-section,
    .filter-section {
      padding: 0 20px;
    }

    .stats-container {
      grid-template-columns: 1fr;
    }

    .filter-content {
      flex-direction: column;
      gap: 16px;

      .filter-left {
        width: 100%;
        flex-wrap: wrap;

        .search-input {
          width: 100%;
        }

        .filter-select {
          width: 48%;
        }
      }

      .filter-right {
        width: 100%;
        justify-content: flex-end;
      }
    }

    :deep(.el-card) {
      margin: 0 20px;
    }
  }
}
</style>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 20px;
  }
  
  .mb-2 {
    margin-bottom: 20px;
  }
  
  .ml-2 {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 