<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 数据库优化中间件
 * 监控和优化数据库查询性能
 */
class DatabaseOptimizationMiddleware
{
    /**
     * 慢查询阈值（毫秒）
     */
    private const SLOW_QUERY_THRESHOLD = 100;

    /**
     * N+1查询阈值
     */
    private const N_PLUS_ONE_THRESHOLD = 10;

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // 开始监控
        $this->startQueryMonitoring();
        
        $response = $next($request);
        
        // 结束监控并分析
        $this->endQueryMonitoring($request);
        
        return $response;
    }

    /**
     * 开始查询监控
     */
    private function startQueryMonitoring(): void
    {
        DB::listen(function ($query) {
            $this->analyzeQuery($query);
        });
    }

    /**
     * 结束查询监控
     */
    private function endQueryMonitoring(Request $request): void
    {
        $queryCount = $this->getQueryCount();
        $totalTime = $this->getTotalQueryTime();
        
        // 记录请求的查询统计
        $this->logQueryStats($request, $queryCount, $totalTime);
        
        // 检查是否存在N+1查询问题
        if ($queryCount > self::N_PLUS_ONE_THRESHOLD) {
            $this->logNPlusOneWarning($request, $queryCount);
        }
    }

    /**
     * 分析单个查询
     */
    private function analyzeQuery($query): void
    {
        $sql = $query->sql;
        $bindings = $query->bindings;
        $time = $query->time;
        
        // 检查慢查询
        if ($time > self::SLOW_QUERY_THRESHOLD) {
            $this->logSlowQuery($sql, $bindings, $time);
        }
        
        // 检查查询优化建议
        $suggestions = $this->getQueryOptimizationSuggestions($sql);
        if (!empty($suggestions)) {
            $this->logOptimizationSuggestions($sql, $suggestions);
        }
        
        // 累计查询统计
        $this->accumulateQueryStats($sql, $time);
    }

    /**
     * 记录慢查询
     */
    private function logSlowQuery(string $sql, array $bindings, float $time): void
    {
        Log::warning('Slow query detected', [
            'sql' => $sql,
            'bindings' => $bindings,
            'time' => $time,
            'url' => request()->fullUrl(),
            'timestamp' => now(),
        ]);
        
        // 存储到缓存中以便后续分析
        $cacheKey = 'slow_queries_' . date('Y-m-d');
        $slowQueries = Cache::get($cacheKey, []);
        $slowQueries[] = [
            'sql' => $sql,
            'time' => $time,
            'url' => request()->fullUrl(),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        Cache::put($cacheKey, $slowQueries, 86400); // 保存1天
    }

    /**
     * 记录N+1查询警告
     */
    private function logNPlusOneWarning(Request $request, int $queryCount): void
    {
        Log::warning('Potential N+1 query detected', [
            'query_count' => $queryCount,
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'timestamp' => now(),
        ]);
    }

    /**
     * 获取查询优化建议
     */
    private function getQueryOptimizationSuggestions(string $sql): array
    {
        $suggestions = [];
        
        // 检查是否缺少WHERE子句
        if (stripos($sql, 'SELECT') === 0 && stripos($sql, 'WHERE') === false) {
            $suggestions[] = '查询缺少WHERE子句，可能导致全表扫描';
        }
        
        // 检查是否使用了SELECT *
        if (stripos($sql, 'SELECT *') !== false) {
            $suggestions[] = '避免使用SELECT *，只选择需要的字段';
        }
        
        // 检查是否使用了LIKE '%xxx%'
        if (preg_match('/LIKE\s+[\'"]%.*%[\'"]/', $sql)) {
            $suggestions[] = '避免使用前导通配符的LIKE查询，考虑使用全文索引';
        }
        
        // 检查是否使用了ORDER BY RAND()
        if (stripos($sql, 'ORDER BY RAND()') !== false) {
            $suggestions[] = '避免使用ORDER BY RAND()，性能较差';
        }
        
        // 检查是否使用了子查询
        if (preg_match('/\(\s*SELECT/i', $sql)) {
            $suggestions[] = '考虑将子查询重写为JOIN以提高性能';
        }
        
        return $suggestions;
    }

    /**
     * 记录优化建议
     */
    private function logOptimizationSuggestions(string $sql, array $suggestions): void
    {
        Log::info('Query optimization suggestions', [
            'sql' => $sql,
            'suggestions' => $suggestions,
            'url' => request()->fullUrl(),
            'timestamp' => now(),
        ]);
    }

    /**
     * 累计查询统计
     */
    private function accumulateQueryStats(string $sql, float $time): void
    {
        $cacheKey = 'query_stats_' . date('Y-m-d');
        $stats = Cache::get($cacheKey, [
            'total_queries' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'slow_queries' => 0,
        ]);
        
        $stats['total_queries']++;
        $stats['total_time'] += $time;
        $stats['avg_time'] = $stats['total_time'] / $stats['total_queries'];
        
        if ($time > self::SLOW_QUERY_THRESHOLD) {
            $stats['slow_queries']++;
        }
        
        Cache::put($cacheKey, $stats, 86400);
    }

    /**
     * 记录请求查询统计
     */
    private function logQueryStats(Request $request, int $queryCount, float $totalTime): void
    {
        if ($queryCount > 5 || $totalTime > 200) {
            Log::info('Request query statistics', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'query_count' => $queryCount,
                'total_time' => $totalTime,
                'avg_time' => $totalTime / $queryCount,
                'timestamp' => now(),
            ]);
        }
    }

    /**
     * 获取查询数量
     */
    private function getQueryCount(): int
    {
        // 这里需要实现查询计数器
        return DB::getQueryLog() ? count(DB::getQueryLog()) : 0;
    }

    /**
     * 获取总查询时间
     */
    private function getTotalQueryTime(): float
    {
        $queries = DB::getQueryLog();
        if (!$queries) {
            return 0;
        }
        
        return array_sum(array_column($queries, 'time'));
    }

    /**
     * 获取今日查询统计
     */
    public static function getTodayQueryStats(): array
    {
        $cacheKey = 'query_stats_' . date('Y-m-d');
        return Cache::get($cacheKey, [
            'total_queries' => 0,
            'total_time' => 0,
            'avg_time' => 0,
            'slow_queries' => 0,
        ]);
    }

    /**
     * 获取今日慢查询
     */
    public static function getTodaySlowQueries(): array
    {
        $cacheKey = 'slow_queries_' . date('Y-m-d');
        return Cache::get($cacheKey, []);
    }

    /**
     * 获取查询性能建议
     */
    public static function getPerformanceRecommendations(): array
    {
        $stats = self::getTodayQueryStats();
        $recommendations = [];
        
        if ($stats['avg_time'] > 50) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => '平均查询时间较长，建议检查数据库索引',
                'metric' => 'avg_time',
                'value' => $stats['avg_time'],
            ];
        }
        
        if ($stats['slow_queries'] > 10) {
            $recommendations[] = [
                'type' => 'error',
                'message' => '慢查询过多，需要优化SQL语句',
                'metric' => 'slow_queries',
                'value' => $stats['slow_queries'],
            ];
        }
        
        $queryRatio = $stats['total_queries'] > 0 ? $stats['slow_queries'] / $stats['total_queries'] : 0;
        if ($queryRatio > 0.1) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => '慢查询比例过高，建议全面优化数据库',
                'metric' => 'slow_query_ratio',
                'value' => round($queryRatio * 100, 2) . '%',
            ];
        }
        
        return $recommendations;
    }
} 