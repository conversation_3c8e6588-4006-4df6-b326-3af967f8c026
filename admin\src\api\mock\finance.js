import { faker } from '@faker-js/faker'

const allCommissions = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  order_id: `ORD${faker.string.alphanumeric(10).toUpperCase()}`,
  user_id: faker.number.int({ min: 1, max: 100 }),
  user_name: faker.internet.userName(),
  user_avatar: faker.image.avatar(),
  amount: faker.finance.amount(5, 100, 2),
  status: faker.helpers.arrayElement(['pending', 'settled', 'frozen']),
  created_at: faker.date.past().toISOString(),
  settled_at: faker.helpers.arrayElement([null, faker.date.recent().toISOString()]),
}))

const allOrders = Array.from({ length: 20 }, () => ({
  id: faker.string.uuid(),
  order_no: `ORD${faker.string.alphanumeric(12)}`,
  product_name: faker.commerce.productName(),
  amount: faker.commerce.price(),
}));

const allUsers = Array.from({ length: 10 }, () => ({
  id: faker.number.int({ min: 1, max: 100 }),
  username: faker.internet.userName(),
  avatar: faker.image.avatar(),
}));


export const mockFinanceAPI = {
  batchSettleCommissions(ids) {
    console.log('[Mock] Batch settling commissions for IDs:', ids)
    return Promise.resolve({
      code: 0,
      data: { success: true, count: ids.length },
      message: '批量结算成功'
    })
  },

  addCommission(data) {
    console.log('[Mock] Adding commission:', data)
    const newCommission = {
      ...data,
      id: allCommissions.length + 1,
      created_at: new Date().toISOString(),
      status: 'pending'
    }
    allCommissions.unshift(newCommission)
    return Promise.resolve({
      code: 0,
      data: newCommission,
      message: '添加佣金成功'
    })
  },

  searchOrders(keyword) {
    const filtered = allOrders.filter(o => o.product_name.includes(keyword) || o.order_no.includes(keyword));
    return Promise.resolve({
      code: 0,
      data: { list: filtered },
      message: '成功'
    })
  },

  searchUsers(keyword) {
    const filtered = allUsers.filter(u => u.username.includes(keyword));
    return Promise.resolve({
      code: 0,
      data: { list: filtered },
      message: '成功'
    })
  }
}