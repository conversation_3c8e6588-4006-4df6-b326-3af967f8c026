<template>
  <div class="performance-monitor" v-if="showMonitor">
    <div class="monitor-header">
      <span>性能监控</span>
      <el-button size="small" @click="toggleMonitor">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    <div class="monitor-content">
      <div class="metric-item">
        <span class="metric-label">页面加载时间:</span>
        <span class="metric-value" :class="getLoadTimeClass(loadTime)">{{ loadTime }}ms</span>
      </div>
      <div class="metric-item">
        <span class="metric-label">内存使用:</span>
        <span class="metric-value">{{ memoryUsage }}MB</span>
      </div>
      <div class="metric-item">
        <span class="metric-label">FPS:</span>
        <span class="metric-value" :class="getFpsClass(fps)">{{ fps }}</span>
      </div>
      <div class="metric-item">
        <span class="metric-label">API响应:</span>
        <span class="metric-value" :class="getApiTimeClass(apiResponseTime)">{{ apiResponseTime }}ms</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Close } from '@element-plus/icons-vue'

const showMonitor = ref(false)
const loadTime = ref(0)
const memoryUsage = ref(0)
const fps = ref(60)
const apiResponseTime = ref(0)

let performanceObserver = null
let fpsCounter = null

const toggleMonitor = () => {
  showMonitor.value = !showMonitor.value
}

const getLoadTimeClass = (time) => {
  if (time < 1000) return 'good'
  if (time < 3000) return 'warning'
  return 'poor'
}

const getFpsClass = (fps) => {
  if (fps >= 55) return 'good'
  if (fps >= 30) return 'warning'
  return 'poor'
}

const getApiTimeClass = (time) => {
  if (time < 500) return 'good'
  if (time < 1500) return 'warning'
  return 'poor'
}

const measureLoadTime = () => {
  if (performance.timing) {
    const navigationStart = performance.timing.navigationStart
    const loadComplete = performance.timing.loadEventEnd
    loadTime.value = loadComplete - navigationStart
  }
}

const measureMemoryUsage = () => {
  if (performance.memory) {
    memoryUsage.value = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
  }
}

const measureFPS = () => {
  let frames = 0
  let lastTime = performance.now()
  
  const countFrames = (currentTime) => {
    frames++
    if (currentTime >= lastTime + 1000) {
      fps.value = Math.round((frames * 1000) / (currentTime - lastTime))
      frames = 0
      lastTime = currentTime
    }
    fpsCounter = requestAnimationFrame(countFrames)
  }
  
  fpsCounter = requestAnimationFrame(countFrames)
}

const measureApiResponseTime = () => {
  // 模拟API响应时间监控
  const startTime = performance.now()
  setTimeout(() => {
    apiResponseTime.value = Math.round(performance.now() - startTime)
  }, Math.random() * 500 + 200)
}

onMounted(() => {
  // 开发环境下显示性能监控
  if (import.meta.env.DEV) {
    showMonitor.value = true
  }
  
  measureLoadTime()
  measureMemoryUsage()
  measureFPS()
  measureApiResponseTime()
  
  // 定期更新性能指标
  const interval = setInterval(() => {
    measureMemoryUsage()
    measureApiResponseTime()
  }, 2000)
  
  onUnmounted(() => {
    clearInterval(interval)
    if (fpsCounter) {
      cancelAnimationFrame(fpsCounter)
    }
  })
})

// 暴露给父组件的方法
defineExpose({
  toggleMonitor,
  showMonitor
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 200px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: bold;
}

.monitor-header .el-button {
  background: transparent;
  border: none;
  color: white;
  padding: 4px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.metric-label {
  color: #ccc;
}

.metric-value {
  font-weight: bold;
}

.metric-value.good {
  color: #67C23A;
}

.metric-value.warning {
  color: #E6A23C;
}

.metric-value.poor {
  color: #F56C6C;
}
</style>