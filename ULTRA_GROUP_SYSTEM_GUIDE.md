# 超级群组系统使用指南

## 系统概述

超级群组系统是对原有群组创建功能的全面增强，整合了Laravel的高级功能和ThinkPHP的实用特性，提供了更强大的定位、防红和营销功能。

## 核心功能

### 1. 增强版定位服务 (EnhancedLocationService)
- **多源API支持**: 百度地图、高德地图、腾讯位置服务
- **智能降级策略**: API失败时自动切换备用服务
- **ThinkPHP兼容**: 支持xxx占位符和curcity2 Cookie
- **多级缓存**: Session > Cookie > Redis > Database

### 2. 超级防红系统 (UltraAntiBlockService)
- **四级防护等级**: low, medium, high, ultra
- **智能域名轮换**: 基于健康度的动态域名选择
- **URL混淆加密**: 多层混淆和时间戳验证
- **社交平台检测**: 自动识别微信/QQ等环境并引导

### 3. 虚拟数据生成器 (VirtualDataGenerator)
- **多风格头像库**: qq, za, business, young
- **智能成员生成**: 支持昵称、头像、活跃度等属性
- **评论模板系统**: 25+评论模板，支持点赞数分布
- **ThinkPHP格式兼容**: 支持"昵称----内容----点赞"格式

## 使用方法

### API端点

#### 创建超级群组
```
POST /api/v1/admin/ultra-groups/create
```

请求参数:
```json
{
  "title": "xxx本地交流群",
  "subtitle": "高质量社群",
  "description": "群组描述",
  "price": 9.9,
  "member_limit": 200,
  "anti_block_level": "high",
  "wechat_detection": true,
  "avatar_library": "qq",
  "virtual_member_count": 15,
  "use_geo_targeting": true
}
```

#### 测试功能

1. **测试定位服务**
```
GET /api/v1/admin/ultra-groups/test-location?ip=***************&content=xxx本地群
```

2. **测试虚拟数据**
```
GET /api/v1/admin/ultra-groups/test-virtual-data?style=qq&member_count=10
```

3. **测试防红系统**
```
GET /api/v1/admin/ultra-groups/test-anti-block?group_id=1&level=high
```

4. **获取系统状态**
```
GET /api/v1/admin/ultra-groups/system-status
```

### 命令行测试

运行集成测试:
```bash
php artisan test:ultra-group
```

测试特定功能:
```bash
php artisan test:ultra-group --test=location
php artisan test:ultra-group --test=antiblock
php artisan test:ultra-group --test=virtual
php artisan test:ultra-group --test=create
```

详细输出:
```bash
php artisan test:ultra-group --verbose
```

### 落地页访问

增强版落地页:
```
/landing/group/{id}
/landing/ultra/{id}
```

## 特色功能

### 地理位置占位符
支持多种占位符格式:
- `xxx` - ThinkPHP兼容格式
- `{city}` - 城市名
- `{省份}` - 省份名
- `{地区}` - 地区名
- `[位置]` - 完整地址

### 防红级别说明
- **low**: 基础防护，简单短链接
- **medium**: 标准防护，域名轮换
- **high**: 高级防护，URL混淆
- **ultra**: 终极防护，加密+时间戳验证

### 虚拟数据风格
- **qq**: QQ风格头像（46个）
- **za**: 综合风格头像（41个）
- **business**: 商务风格
- **young**: 年轻潮流风格

## 配置要求

### 环境变量
```env
# 百度地图API
BAIDU_MAP_API_KEY=VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om

# 高德地图API
AMAP_API_KEY=fdc6f731641c83601f4ffeb3dca10cd6
AMAP_SECURITY_CODE=61f55187b6feeb3794ccbe8fba390442

# 腾讯位置API（可选）
TENCENT_LOCATION_KEY=your_key_here
```

### 数据库要求
- 域名池表 (domain_pools)
- 短链接表 (short_links)
- 群组表需要虚拟数据字段

## 注意事项

1. **域名池管理**: 确保至少有一个活跃域名
2. **头像资源**: 确保public/face/目录下有相应头像
3. **权限设置**: API需要admin权限
4. **缓存清理**: 定期清理过期缓存

## 故障排查

### 定位服务不工作
- 检查API密钥配置
- 验证IP地址格式
- 查看缓存是否正常

### 防红链接生成失败
- 检查域名池是否有活跃域名
- 验证路由配置是否正确
- 查看日志文件

### 虚拟数据显示异常
- 确认头像文件存在
- 检查JSON解析是否正常
- 验证数据库字段类型

## 技术支持

如遇问题，请查看日志文件:
- Laravel日志: `storage/logs/laravel.log`
- 域名检查日志: `storage/logs/domain-check.log`
- 系统优化日志: `storage/logs/system-optimization.log`

---

*系统增强完成于 2024-12-19*