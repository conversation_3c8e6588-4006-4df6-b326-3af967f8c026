<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * 支付安全中间件
 * 防止支付接口被恶意调用
 */
class PaymentSecurityMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 1. IP白名单检查
        if (!$this->checkIpWhitelist($request)) {
            Log::warning('支付回调IP不在白名单', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl()
            ]);
            return response()->json(['error' => 'IP not allowed'], 403);
        }

        // 2. 频率限制
        if (!$this->checkRateLimit($request)) {
            Log::warning('支付回调频率过高', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            return response()->json(['error' => 'Too many requests'], 429);
        }

        // 3. 请求大小限制
        if (!$this->checkRequestSize($request)) {
            Log::warning('支付回调请求过大', [
                'size' => strlen($request->getContent()),
                'ip' => $request->ip()
            ]);
            return response()->json(['error' => 'Request too large'], 413);
        }

        // 4. User-Agent检查
        if (!$this->checkUserAgent($request)) {
            Log::warning('支付回调User-Agent异常', [
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);
            return response()->json(['error' => 'Invalid user agent'], 400);
        }

        return $next($request);
    }

    /**
     * 检查IP白名单
     */
    private function checkIpWhitelist(Request $request): bool
    {
        $whitelist = config('payment.security.ip_whitelist');
        
        // 如果没有配置白名单，则跳过检查
        if (empty($whitelist)) {
            return true;
        }

        $allowedIps = explode(',', $whitelist);
        $clientIp = $request->ip();

        foreach ($allowedIps as $allowedIp) {
            $allowedIp = trim($allowedIp);
            
            // 支持CIDR格式
            if (strpos($allowedIp, '/') !== false) {
                if ($this->ipInRange($clientIp, $allowedIp)) {
                    return true;
                }
            } else {
                if ($clientIp === $allowedIp) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查IP是否在CIDR范围内
     */
    private function ipInRange(string $ip, string $cidr): bool
    {
        list($subnet, $mask) = explode('/', $cidr);
        
        if ((ip2long($ip) & ~((1 << (32 - $mask)) - 1)) == ip2long($subnet)) {
            return true;
        }
        
        return false;
    }

    /**
     * 频率限制检查
     */
    private function checkRateLimit(Request $request): bool
    {
        $key = 'payment_rate_limit:' . $request->ip();
        $maxAttempts = 60; // 每分钟最多60次请求
        $decayMinutes = 1;

        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $maxAttempts) {
            return false;
        }

        Cache::put($key, $attempts + 1, $decayMinutes * 60);
        
        return true;
    }

    /**
     * 检查请求大小
     */
    private function checkRequestSize(Request $request): bool
    {
        $maxSize = 1024 * 10; // 10KB
        $contentLength = strlen($request->getContent());
        
        return $contentLength <= $maxSize;
    }

    /**
     * 检查User-Agent
     */
    private function checkUserAgent(Request $request): bool
    {
        $userAgent = $request->userAgent();
        
        // 如果没有User-Agent，拒绝请求
        if (empty($userAgent)) {
            return false;
        }

        // 检查是否包含恶意特征
        $maliciousPatterns = [
            'sqlmap',
            'nmap',
            'nikto',
            'curl/7.', // 简单的curl请求可能是恶意的
            'python-requests',
            'wget',
        ];

        $userAgentLower = strtolower($userAgent);
        
        foreach ($maliciousPatterns as $pattern) {
            if (strpos($userAgentLower, $pattern) !== false) {
                return false;
            }
        }

        return true;
    }
}