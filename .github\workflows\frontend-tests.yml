name: 前端测试流水线

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'admin/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'admin/**'

jobs:
  # 单元测试和覆盖率检查
  unit-tests:
    runs-on: ubuntu-latest
    name: 单元测试
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json
          
      - name: 安装依赖
        run: |
          cd admin
          npm ci
          
      - name: 代码风格检查
        run: |
          cd admin
          npm run lint
          
      - name: 运行单元测试
        run: |
          cd admin
          npm run test:coverage
          
      - name: 上传测试覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./admin/coverage/lcov.info
          directory: ./admin/coverage/
          flags: unittests
          name: admin-unit-tests
          
      - name: 保存测试结果
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: unit-test-results
          path: |
            admin/coverage/
            admin/test-results/

  # E2E 测试
  e2e-tests:
    runs-on: ubuntu-latest
    name: E2E测试
    needs: unit-tests
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json
          
      - name: 安装依赖
        run: |
          cd admin
          npm ci
          
      - name: 安装 Playwright
        run: |
          cd admin
          npx playwright install --with-deps ${{ matrix.browser }}
          
      - name: 构建应用
        run: |
          cd admin
          npm run build
          
      - name: 运行 E2E 测试
        run: |
          cd admin
          npm run test:e2e -- --project=${{ matrix.browser }}
        env:
          CI: true
          
      - name: 保存 E2E 测试结果
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-results-${{ matrix.browser }}
          path: |
            admin/playwright-report/
            admin/test-results/
          retention-days: 7

  # 性能测试
  performance-tests:
    runs-on: ubuntu-latest
    name: 性能测试
    needs: unit-tests
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json
          
      - name: 安装依赖
        run: |
          cd admin
          npm ci
          
      - name: 构建应用
        run: |
          cd admin
          npm run build
          
      - name: 安装 Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x
        
      - name: 运行 Lighthouse 性能测试
        run: |
          cd admin
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # 构建验证
  build-validation:
    runs-on: ubuntu-latest
    name: 构建验证
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json
          
      - name: 安装依赖
        run: |
          cd admin
          npm ci
          
      - name: 构建生产版本
        run: |
          cd admin
          npm run build
          
      - name: 分析构建产物
        run: |
          cd admin
          npx vite-bundle-analyzer dist/
          
      - name: 保存构建产物
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: admin/dist/

  # 测试报告汇总
  test-summary:
    runs-on: ubuntu-latest
    name: 测试结果汇总
    needs: [unit-tests, e2e-tests]
    if: always()
    
    steps:
      - name: 下载测试结果
        uses: actions/download-artifact@v3
        with:
          path: test-results/
          
      - name: 生成测试报告
        run: |
          echo "# 测试结果汇总" > test-summary.md
          echo "" >> test-summary.md
          
          if [ -d "test-results/unit-test-results" ]; then
            echo "## 单元测试 ✅" >> test-summary.md
            echo "单元测试已通过" >> test-summary.md
          else
            echo "## 单元测试 ❌" >> test-summary.md
            echo "单元测试失败，请检查详细日志" >> test-summary.md
          fi
          
          echo "" >> test-summary.md
          
          if [ -d "test-results/e2e-results-chromium" ]; then
            echo "## E2E测试 ✅" >> test-summary.md
            echo "E2E测试已通过" >> test-summary.md
          else
            echo "## E2E测试 ❌" >> test-summary.md
            echo "E2E测试失败，请检查详细日志" >> test-summary.md
          fi
          
          cat test-summary.md
          
      - name: 发布测试报告
        uses: actions/upload-artifact@v3
        with:
          name: test-summary
          path: test-summary.md

  # 通知
  notify:
    runs-on: ubuntu-latest
    name: 测试结果通知
    needs: [unit-tests, e2e-tests, performance-tests, build-validation]
    if: always()
    
    steps:
      - name: 测试成功通知
        if: needs.unit-tests.result == 'success' && needs.e2e-tests.result == 'success'
        run: |
          echo "🎉 所有测试已通过！"
          echo "✅ 单元测试: ${{ needs.unit-tests.result }}"
          echo "✅ E2E测试: ${{ needs.e2e-tests.result }}"
          echo "✅ 性能测试: ${{ needs.performance-tests.result }}"
          echo "✅ 构建验证: ${{ needs.build-validation.result }}"
          
      - name: 测试失败通知
        if: needs.unit-tests.result == 'failure' || needs.e2e-tests.result == 'failure'
        run: |
          echo "❌ 测试失败，请检查详细日志"
          echo "单元测试: ${{ needs.unit-tests.result }}"
          echo "E2E测试: ${{ needs.e2e-tests.result }}"
          echo "性能测试: ${{ needs.performance-tests.result }}"
          echo "构建验证: ${{ needs.build-validation.result }}"
          exit 1