<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * 支付回调控制器
 * 处理各种支付方式的回调通知
 */
class PaymentCallbackController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 微信支付回调
     */
    public function wechatNotify(Request $request): \Illuminate\Http\Response
    {
        try {
            Log::info('收到微信支付回调', [
                'headers' => $request->headers->all(),
                'body' => $request->getContent()
            ]);

            $xmlData = $request->getContent();
            $data = $this->xmlToArray($xmlData);

            $result = $this->paymentService->handleCallback('wechat', $data);

            if ($result['success']) {
                return response('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>')
                    ->header('Content-Type', 'application/xml');
            } else {
                return response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[' . $result['message'] . ']]></return_msg></xml>')
                    ->header('Content-Type', 'application/xml');
            }

        } catch (\Exception $e) {
            Log::error('微信支付回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>')
                ->header('Content-Type', 'application/xml');
        }
    }

    /**
     * 支付宝回调
     */
    public function alipayNotify(Request $request): \Illuminate\Http\Response
    {
        try {
            Log::info('收到支付宝回调', [
                'data' => $request->all()
            ]);

            $result = $this->paymentService->handleCallback('alipay', $request->all());

            if ($result['success']) {
                return response('success');
            } else {
                return response('fail');
            }

        } catch (\Exception $e) {
            Log::error('支付宝回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response('fail');
        }
    }

    /**
     * 易支付回调
     */
    public function payoreoNotify(Request $request): \Illuminate\Http\Response
    {
        try {
            Log::info('收到易支付回调', [
                'data' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // 验证回调数据
            $requiredFields = ['out_trade_no', 'trade_no', 'money', 'trade_status'];
            foreach ($requiredFields as $field) {
                if (!$request->has($field)) {
                    Log::error('易支付回调缺少必要参数', [
                        'missing_field' => $field,
                        'data' => $request->all()
                    ]);
                    return response('fail');
                }
            }

            $result = $this->paymentService->handleCallback('payoreo', $request->all());

            if ($result['success']) {
                Log::info('易支付回调处理成功', [
                    'out_trade_no' => $request->out_trade_no,
                    'trade_no' => $request->trade_no,
                    'money' => $request->money
                ]);
                return response('success');
            } else {
                Log::error('易支付回调处理失败', [
                    'message' => $result['message'],
                    'data' => $request->all()
                ]);
                return response('fail');
            }

        } catch (\Exception $e) {
            Log::error('易支付回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response('fail');
        }
    }

    /**
     * QQ钱包回调
     */
    public function qqpayNotify(Request $request): \Illuminate\Http\Response
    {
        try {
            Log::info('收到QQ钱包回调', [
                'data' => $request->all()
            ]);

            $result = $this->paymentService->handleCallback('qqpay', $request->all());

            if ($result['success']) {
                return response('success');
            } else {
                return response('fail');
            }

        } catch (\Exception $e) {
            Log::error('QQ钱包回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response('fail');
        }
    }

    /**
     * 银行转账确认回调
     */
    public function bankNotify(Request $request): JsonResponse
    {
        try {
            Log::info('收到银行转账确认', [
                'data' => $request->all()
            ]);

            $result = $this->paymentService->handleCallback('bank', $request->all());

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('银行转账回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '处理失败'
            ]);
        }
    }

    /**
     * 支付返回页面（用户支付完成后跳转）
     */
    public function paymentReturn(Request $request): \Illuminate\Http\RedirectResponse
    {
        try {
            $paymentMethod = $request->get('method', 'unknown');
            $orderNo = $request->get('out_trade_no', $request->get('order_no', ''));

            Log::info('用户支付返回', [
                'method' => $paymentMethod,
                'order_no' => $orderNo,
                'data' => $request->all()
            ]);

            if ($orderNo) {
                // 查询订单状态
                $orderStatus = $this->paymentService->queryOrderStatus($orderNo);
                
                if ($orderStatus['success'] && $orderStatus['data']['status'] === 'paid') {
                    // 支付成功，跳转到成功页面
                    return redirect()->route('group.success', ['order' => $orderNo])
                        ->with('success', '支付成功！');
                } else {
                    // 支付未完成，跳转回支付页面
                    return redirect()->route('payment.show', ['order' => $orderNo])
                        ->with('warning', '支付尚未完成，请继续支付');
                }
            }

            // 没有订单号，跳转到首页
            return redirect('/')->with('info', '支付处理中，请稍候查看订单状态');

        } catch (\Exception $e) {
            Log::error('支付返回页面处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return redirect('/')->with('error', '支付处理异常，请联系客服');
        }
    }

    /**
     * 测试回调接口（仅开发环境）
     */
    public function testCallback(Request $request): JsonResponse
    {
        if (!app()->environment('local', 'testing')) {
            return response()->json([
                'success' => false,
                'message' => '仅开发环境可用'
            ], 403);
        }

        try {
            $paymentMethod = $request->get('method', 'payoreo');
            $testData = $request->get('data', []);

            Log::info('测试支付回调', [
                'method' => $paymentMethod,
                'data' => $testData
            ]);

            $result = $this->paymentService->handleCallback($paymentMethod, $testData);

            return response()->json([
                'success' => true,
                'result' => $result,
                'message' => '测试回调处理完成'
            ]);

        } catch (\Exception $e) {
            Log::error('测试回调处理异常', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '测试回调处理失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取支付回调日志
     */
    public function getCallbackLogs(Request $request): JsonResponse
    {
        try {
            $method = $request->get('method');
            $date = $request->get('date', now()->format('Y-m-d'));
            $limit = min(100, max(1, $request->get('limit', 50)));

            // 这里应该从日志文件或数据库中获取回调日志
            // 暂时返回模拟数据
            $logs = [
                [
                    'id' => 1,
                    'method' => 'payoreo',
                    'order_no' => 'ORD20241201123456',
                    'status' => 'success',
                    'amount' => 99.00,
                    'callback_time' => now()->subMinutes(10)->toDateTimeString(),
                    'response_time' => 150,
                    'ip' => '127.0.0.1'
                ],
                [
                    'id' => 2,
                    'method' => 'wechat',
                    'order_no' => 'ORD20241201123457',
                    'status' => 'success',
                    'amount' => 199.00,
                    'callback_time' => now()->subMinutes(5)->toDateTimeString(),
                    'response_time' => 89,
                    'ip' => '127.0.0.1'
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'logs' => $logs,
                    'total' => count($logs),
                    'method' => $method,
                    'date' => $date
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取支付回调日志失败', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取日志失败'
            ]);
        }
    }

    /**
     * XML转数组（用于微信支付回调）
     */
    private function xmlToArray(string $xml): array
    {
        try {
            $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
            return is_array($data) ? $data : [];
        } catch (\Exception $e) {
            Log::error('XML解析失败', [
                'xml' => $xml,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 验证回调IP（可选的安全措施）
     */
    private function verifyCallbackIP(string $method, string $ip): bool
    {
        // 这里可以配置各支付平台的回调IP白名单
        $allowedIPs = [
            'wechat' => [
                // 微信支付回调IP段
                '*************/24',
                '************/24',
            ],
            'alipay' => [
                // 支付宝回调IP段
                '************/24',
                '************/24',
            ],
            'payoreo' => [
                // 易支付平台IP（需要根据实际情况配置）
                // 如果没有固定IP，可以跳过验证
            ]
        ];

        if (!isset($allowedIPs[$method]) || empty($allowedIPs[$method])) {
            return true; // 如果没有配置IP限制，则通过验证
        }

        foreach ($allowedIPs[$method] as $allowedIP) {
            if ($this->ipInRange($ip, $allowedIP)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查IP是否在指定范围内
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        
        return ($ip & $mask) === $subnet;
    }
}