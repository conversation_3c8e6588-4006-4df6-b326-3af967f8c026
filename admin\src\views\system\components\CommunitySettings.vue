<template>
  <div class="community-settings-container">
    <el-card>
      <template #header>
        <h3>社群模块设置</h3>
      </template>

      <el-form :model="settings" label-width="150px">
        <!-- 默认设置 -->
        <el-divider content-position="left">默认设置</el-divider>
        <el-form-item label="新成员默认欢迎语">
          <el-input
            type="textarea"
            :rows="3"
            v-model="settings.default_welcome_message"
            placeholder="可用 {memberName} 指代新成员昵称，{groupName} 指代群组名称"
          />
        </el-form-item>
        <el-form-item label="新群组默认规则">
           <el-select
            v-model="settings.default_rules"
            multiple
            placeholder="选择默认应用的自动化规则"
            style="width: 100%;"
          >
            <el-option label="回复“入群”关键词" value="rule_1"></el-option>
            <el-option label="禁止发送广告链接" value="rule_2"></el-option>
          </el-select>
        </el-form-item>

        <!-- 内容审核 -->
        <el-divider content-position="left">内容审核</el-divider>
        <el-form-item label="新内容先审后发">
          <el-switch v-model="settings.moderation_enabled" />
          <div class="form-item-help">开启后，所有用户发布的新帖子和评论都需要经过管理员审核才能显示。</div>
        </el-form-item>
        <el-form-item label="敏感词词库">
          <el-input
            type="textarea"
            :rows="5"
            v-model="settings.sensitive_words"
            placeholder="每行一个敏感词"
          />
           <div class="form-item-help">当用户发布的内容包含这些词汇时，将自动进入审核队列。</div>
        </el-form-item>

        <!-- 功能开关 -->
        <el-divider content-position="left">功能开关</el-divider>
        <el-form-item label="允许创建付费群组">
          <el-switch v-model="settings.paid_groups_enabled" />
        </el-form-item>
        <el-form-item label="开启活动功能">
          <el-switch v-model="settings.events_enabled" />
        </el-form-item>
        <el-form-item label="开启打卡功能">
          <el-switch v-model="settings.checkin_enabled" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)
const saving = ref(false)
const settings = reactive({
  default_welcome_message: '欢迎 {memberName} 加入 {groupName}！',
  default_rules: ['rule_2'],
  moderation_enabled: true,
  sensitive_words: '广告\n推广\n加V\n私聊',
  paid_groups_enabled: true,
  events_enabled: true,
  checkin_enabled: false,
})

const fetchSettings = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    // settings.value = apiResponse.data
    loading.value = false
  }, 500)
}

const saveSettings = () => {
  saving.value = true
  setTimeout(() => {
    ElMessage.success('社群模块设置已保存')
    saving.value = false
  }, 1000)
}

onMounted(() => {
  fetchSettings()
})
</script>

<style scoped>
.form-item-help {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  margin-top: 4px;
}
</style>