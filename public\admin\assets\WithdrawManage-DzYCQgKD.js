import{_ as e,n as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                 *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import{r as l,L as t,e as s,k as d,l as n,E as o,z as u,t as r,u as i,D as c,A as p,y as m,B as v,G as _}from"./vue-vendor-Dy164gUc.js";import{b as f}from"./export-BIRLwzxN.js";import{a as g,c as b}from"./format-3eU4VJ9V.js";import{Q as h,a_ as y,aY as w,U as k,aZ as x,bp as V,bq as j,aM as C,b9 as U,b8 as $,br as z,by as R,at as D,bh as L,bi as I,a$ as S,bw as B,bx as M,bk as O,bl as q,ay as E,b1 as F,R as T}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const A={class:"app-container"},G={class:"stats-content"},P={class:"stats-data"},Q={class:"stats-number"},W={class:"stats-amount"},Y={class:"stats-content"},Z={class:"stats-data"},H={class:"stats-number"},J={class:"stats-amount"},K={class:"stats-content"},N={class:"stats-data"},X={class:"stats-number"},ee={class:"stats-amount"},ae={class:"stats-content"},le={class:"stats-data"},te={class:"stats-number"},se={class:"stats-amount"},de={class:"user-info"},ne={class:"user-avatar"},oe=["src"],ue={class:"user-details"},re={class:"user-name"},ie={class:"user-id"},ce={class:"amount-info"},pe={class:"withdraw-amount"},me={class:"fee-amount"},ve={class:"actual-amount"},_e={class:"method-info"},fe={class:"account-info"},ge={class:"account-name"},be={class:"account-number"},he={class:"time-info"},ye={class:"apply-time"},we={key:0,class:"process-time"},ke={style:{"margin-top":"20px","text-align":"center"}},xe={key:0,class:"detail-container"},Ve={key:0,style:{"margin-top":"20px"}},je={key:1,style:{"margin-top":"20px"}},Ce={class:"dialog-footer"},Ue={key:0,style:{"margin-bottom":"20px"}},$e={class:"dialog-footer"},ze={style:{"margin-bottom":"20px"}},Re={class:"dialog-footer"},De=e({__name:"WithdrawManage",setup(e){const De=l([]),Le=l(0),Ie=l(!0),Se=l(!1),Be=l(!1),Me=l(!1),Oe=l([]),qe=l(!1),Ee=l(!1),Fe=l(!1),Te=l(null),Ae=l("approve"),Ge=l("approve"),Pe=l([]),Qe=t({page:1,limit:15,user_name:"",status:"",method:"",min_amount:null,max_amount:null,date_range:[]}),We=t({admin_remark:""}),Ye=t({admin_remark:""}),Ze=l({pending_count:0,pending_amount:0,approved_count:0,approved_amount:0,completed_count:0,completed_amount:0,total_count:0,total_amount:0});s(()=>{He(),Je()});const He=async()=>{Ie.value=!0;try{const e={...Qe};e.date_range&&2===e.date_range.length&&(e.start_date=e.date_range[0],e.end_date=e.date_range[1],delete e.date_range);const l=await a({url:"/withdraw-records",method:"get",params:e});De.value=l.data.data,Le.value=l.data.total}catch(e){h.error("获取数据失败")}finally{Ie.value=!1}},Je=async()=>{try{const e=await a({url:"/withdraw-records/stats",method:"get"});Ze.value=e.data}catch(e){console.error("获取统计数据失败")}},Ke=()=>{Qe.page=1,He()},Ne=()=>{Object.assign(Qe,{page:1,limit:15,user_name:"",status:"",method:"",min_amount:null,max_amount:null,date_range:[]}),He()},Xe=()=>{Je(),h.success("统计数据已刷新")},ea=e=>{Oe.value=e},aa=()=>{const e=Oe.value.filter(e=>"pending"===e.status);0!==e.length?(Ge.value="approve",Pe.value=e,Ye.admin_remark="",Fe.value=!0):h.warning("请选择待审核的提现申请")},la=()=>{const e=Oe.value.filter(e=>"pending"===e.status);0!==e.length?(Ge.value="reject",Pe.value=e,Ye.admin_remark="",Fe.value=!0):h.warning("请选择待审核的提现申请")},ta=async()=>{Me.value=!0;try{const e="approve"===Ge.value?"batch-approve":"batch-reject",l="approve"===Ge.value?"批准":"拒绝";await a({url:`/withdraw-records/${e}`,method:"post",data:{ids:Pe.value.map(e=>e.id),admin_remark:Ye.admin_remark}}),h.success(`批量${l}成功`),Fe.value=!1,He(),Je()}catch(e){h.error(`批量${"approve"===Ge.value?"批准":"拒绝"}失败`)}finally{Me.value=!1}},sa=async()=>{Se.value=!0;try{const e={...Qe,format:"excel",fields:["id","user_name","amount","fee","method","account_name","account_number","status","created_at"]},a=await f(e),l=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=l,t.download=`提现记录_${(new Date).toLocaleDateString()}.xlsx`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),h.success("导出成功")}catch(e){h.error("导出失败")}finally{Se.value=!1}},da=e=>{Te.value=e,Ae.value="approve",We.admin_remark="",Ee.value=!0,qe.value=!1},na=e=>{Te.value=e,Ae.value="reject",We.admin_remark="",Ee.value=!0,qe.value=!1},oa=async()=>{Be.value=!0;try{const e="approve"===Ae.value?"approve":"reject",l="approve"===Ae.value?"批准":"拒绝";await a({url:`/withdraw-records/${Te.value.id}/${e}`,method:"post",data:{admin_remark:We.admin_remark}}),h.success(`${l}成功`),Ee.value=!1,He(),Je()}catch(e){h.error(("approve"===Ae.value?"批准":"拒绝")+"失败")}finally{Be.value=!1}},ua=e=>({pending:"待审核",approved:"已批准",rejected:"已拒绝",processing:"处理中",completed:"已完成",failed:"失败"}[e]||"未知"),ra=e=>({pending:"warning",approved:"success",rejected:"danger",processing:"primary",completed:"success",failed:"danger"}[e]||"info"),ia=e=>({alipay:"支付宝",wechat:"微信",bank_card:"银行卡",balance:"余额"}[e]||"未知");return(e,l)=>{const t=w,s=y,f=x,ca=C,pa=j,ma=$,va=U,_a=z,fa=R,ga=D,ba=V,ha=I,ya=S,wa=L,ka=M,xa=q,Va=O,ja=E,Ca=F,Ua=B;return n(),d("div",A,[o(f,{gutter:20,style:{"margin-bottom":"20px"}},{default:u(()=>[o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",G,[l[19]||(l[19]=r("div",{class:"stats-icon pending-icon"},[r("i",{class:"el-icon-time"})],-1)),r("div",P,[r("div",Q,k(Ze.value.pending_count),1),l[18]||(l[18]=r("div",{class:"stats-label"},"待审核",-1)),r("div",W,"¥"+k(i(g)(Ze.value.pending_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",Y,[l[21]||(l[21]=r("div",{class:"stats-icon approved-icon"},[r("i",{class:"el-icon-check"})],-1)),r("div",Z,[r("div",H,k(Ze.value.approved_count),1),l[20]||(l[20]=r("div",{class:"stats-label"},"已批准",-1)),r("div",J,"¥"+k(i(g)(Ze.value.approved_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",K,[l[23]||(l[23]=r("div",{class:"stats-icon completed-icon"},[r("i",{class:"el-icon-success"})],-1)),r("div",N,[r("div",X,k(Ze.value.completed_count),1),l[22]||(l[22]=r("div",{class:"stats-label"},"已完成",-1)),r("div",ee,"¥"+k(i(g)(Ze.value.completed_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",ae,[l[25]||(l[25]=r("div",{class:"stats-icon total-icon"},[r("i",{class:"el-icon-wallet"})],-1)),r("div",le,[r("div",te,k(Ze.value.total_count),1),l[24]||(l[24]=r("div",{class:"stats-label"},"总提现",-1)),r("div",se,"¥"+k(i(g)(Ze.value.total_amount)),1)])])]),_:1})]),_:1})]),_:1}),o(t,{style:{"margin-bottom":"20px"}},{default:u(()=>[o(ba,{inline:!0,model:Qe,"label-width":"80px"},{default:u(()=>[o(pa,{label:"用户名"},{default:u(()=>[o(ca,{modelValue:Qe.user_name,"onUpdate:modelValue":l[0]||(l[0]=e=>Qe.user_name=e),placeholder:"请输入用户名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(pa,{label:"状态"},{default:u(()=>[o(va,{modelValue:Qe.status,"onUpdate:modelValue":l[1]||(l[1]=e=>Qe.status=e),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:u(()=>[o(ma,{label:"全部",value:""}),o(ma,{label:"待审核",value:"pending"}),o(ma,{label:"已批准",value:"approved"}),o(ma,{label:"已拒绝",value:"rejected"}),o(ma,{label:"处理中",value:"processing"}),o(ma,{label:"已完成",value:"completed"}),o(ma,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1}),o(pa,{label:"提现方式"},{default:u(()=>[o(va,{modelValue:Qe.method,"onUpdate:modelValue":l[2]||(l[2]=e=>Qe.method=e),placeholder:"请选择方式",clearable:"",style:{width:"150px"}},{default:u(()=>[o(ma,{label:"全部",value:""}),o(ma,{label:"支付宝",value:"alipay"}),o(ma,{label:"微信",value:"wechat"}),o(ma,{label:"银行卡",value:"bank_card"}),o(ma,{label:"余额",value:"balance"})]),_:1},8,["modelValue"])]),_:1}),o(pa,{label:"金额范围"},{default:u(()=>[o(_a,{modelValue:Qe.min_amount,"onUpdate:modelValue":l[3]||(l[3]=e=>Qe.min_amount=e),min:0,precision:2,placeholder:"最小金额",style:{width:"120px"}},null,8,["modelValue"]),l[26]||(l[26]=r("span",{style:{margin:"0 10px"}},"-",-1)),o(_a,{modelValue:Qe.max_amount,"onUpdate:modelValue":l[4]||(l[4]=e=>Qe.max_amount=e),min:0,precision:2,placeholder:"最大金额",style:{width:"120px"}},null,8,["modelValue"])]),_:1,__:[26]}),o(pa,{label:"时间范围"},{default:u(()=>[o(fa,{modelValue:Qe.date_range,"onUpdate:modelValue":l[5]||(l[5]=e=>Qe.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"250px"}},null,8,["modelValue"])]),_:1}),o(pa,null,{default:u(()=>[o(ga,{type:"primary",icon:"Search",onClick:Ke},{default:u(()=>l[27]||(l[27]=[c("搜索",-1)])),_:1,__:[27]}),o(ga,{icon:"Refresh",onClick:Ne},{default:u(()=>l[28]||(l[28]=[c("重置",-1)])),_:1,__:[28]})]),_:1})]),_:1},8,["model"])]),_:1}),o(t,{style:{"margin-bottom":"20px"}},{default:u(()=>[o(f,null,{default:u(()=>[o(s,{span:12},{default:u(()=>[o(ga,{type:"success",icon:"Check",disabled:!Oe.value.length,onClick:aa},{default:u(()=>l[29]||(l[29]=[c(" 批量批准 ",-1)])),_:1,__:[29]},8,["disabled"]),o(ga,{type:"danger",icon:"Close",disabled:!Oe.value.length,onClick:la},{default:u(()=>l[30]||(l[30]=[c(" 批量拒绝 ",-1)])),_:1,__:[30]},8,["disabled"]),o(ga,{type:"warning",icon:"Download",onClick:sa,loading:Se.value},{default:u(()=>l[31]||(l[31]=[c(" 导出数据 ",-1)])),_:1,__:[31]},8,["loading"])]),_:1}),o(s,{span:12,style:{"text-align":"right"}},{default:u(()=>[o(ga,{type:"primary",icon:"Refresh",onClick:Xe},{default:u(()=>l[32]||(l[32]=[c(" 刷新统计 ",-1)])),_:1,__:[32]})]),_:1})]),_:1})]),_:1}),o(t,null,{default:u(()=>[p((n(),m(wa,{data:De.value,border:"",fit:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:ea},{default:u(()=>[o(ha,{type:"selection",width:"55",align:"center"}),o(ha,{label:"ID",prop:"id",align:"center",width:"80"}),o(ha,{label:"用户信息",width:"180",align:"center"},{default:u(({row:e})=>[r("div",de,[r("div",ne,[r("img",{src:e.user?.avatar||"/default-avatar.png",alt:""},null,8,oe)]),r("div",ue,[r("div",re,k(e.user?.name||"未知用户"),1),r("div",ie,"ID: "+k(e.user?.id),1)])])]),_:1}),o(ha,{label:"提现金额",width:"150",align:"center"},{default:u(({row:e})=>[r("div",ce,[r("div",pe,"¥"+k(i(g)(e.amount)),1),r("div",me,"手续费: ¥"+k(i(g)(e.fee||0)),1),r("div",ve,"实际: ¥"+k(i(g)(e.amount-(e.fee||0))),1)])]),_:1}),o(ha,{label:"提现方式",width:"120",align:"center"},{default:u(({row:e})=>{return[r("div",_e,[o(ya,{type:(a=e.method,{alipay:"primary",wechat:"success",bank_card:"warning",balance:"info"}[a]||"info")},{default:u(()=>[c(k(ia(e.method)),1)]),_:2},1032,["type"])])];var a}),_:1}),o(ha,{label:"账户信息",width:"200",align:"center"},{default:u(({row:e})=>{return[r("div",fe,[r("div",ge,k(e.account_name),1),r("div",be,k((a=e.account_number,a?a.length<=4?a:a.replace(/(\d{4})\d*(\d{4})/,"$1****$2"):"-")),1)])];var a}),_:1}),o(ha,{label:"状态",width:"100",align:"center"},{default:u(({row:e})=>[o(ya,{type:ra(e.status)},{default:u(()=>[c(k(ua(e.status)),1)]),_:2},1032,["type"])]),_:1}),o(ha,{label:"时间信息",width:"180",align:"center"},{default:u(({row:e})=>[r("div",he,[r("div",ye,"申请: "+k(i(b)(e.created_at)),1),e.processed_at?(n(),d("div",we," 处理: "+k(i(b)(e.processed_at)),1)):v("",!0)])]),_:1}),o(ha,{label:"操作",width:"200",align:"center",fixed:"right"},{default:u(({row:e})=>[o(ga,{type:"text",size:"small",onClick:a=>(e=>{Te.value=e,qe.value=!0})(e)},{default:u(()=>l[33]||(l[33]=[c(" 详情 ",-1)])),_:2,__:[33]},1032,["onClick"]),"pending"===e.status?(n(),m(ga,{key:0,type:"text",size:"small",onClick:a=>da(e)},{default:u(()=>l[34]||(l[34]=[c(" 批准 ",-1)])),_:2,__:[34]},1032,["onClick"])):v("",!0),"pending"===e.status?(n(),m(ga,{key:1,type:"text",size:"small",onClick:a=>na(e)},{default:u(()=>l[35]||(l[35]=[c(" 拒绝 ",-1)])),_:2,__:[35]},1032,["onClick"])):v("",!0),"approved"===e.status?(n(),m(ga,{key:2,type:"text",size:"small",onClick:l=>(e=>{T.confirm("确定要处理这笔提现申请吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await a({url:`/withdraw-records/${e.id}/process`,method:"post"}),h.success("处理成功"),He(),Je()}catch(l){h.error("处理失败")}})})(e)},{default:u(()=>l[36]||(l[36]=[c(" 处理 ",-1)])),_:2,__:[36]},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[Ua,Ie.value]]),r("div",ke,[p(o(ka,{"current-page":Qe.page,"onUpdate:currentPage":l[6]||(l[6]=e=>Qe.page=e),"page-size":Qe.limit,"onUpdate:pageSize":l[7]||(l[7]=e=>Qe.limit=e),"page-sizes":[15,30,50,100],total:Le.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:He,onCurrentChange:He},null,8,["current-page","page-size","total"]),[[_,Le.value>0]])])]),_:1}),o(ja,{modelValue:qe.value,"onUpdate:modelValue":l[11]||(l[11]=e=>qe.value=e),title:"提现详情",width:"700px"},{footer:u(()=>[r("div",Ce,[o(ga,{onClick:l[8]||(l[8]=e=>qe.value=!1)},{default:u(()=>l[39]||(l[39]=[c("关闭",-1)])),_:1,__:[39]}),Te.value&&"pending"===Te.value.status?(n(),m(ga,{key:0,type:"success",onClick:l[9]||(l[9]=e=>da(Te.value))},{default:u(()=>l[40]||(l[40]=[c(" 批准 ",-1)])),_:1,__:[40]})):v("",!0),Te.value&&"pending"===Te.value.status?(n(),m(ga,{key:1,type:"danger",onClick:l[10]||(l[10]=e=>na(Te.value))},{default:u(()=>l[41]||(l[41]=[c(" 拒绝 ",-1)])),_:1,__:[41]})):v("",!0)])]),default:u(()=>[Te.value?(n(),d("div",xe,[o(Va,{column:2,border:""},{default:u(()=>[o(xa,{label:"用户"},{default:u(()=>[c(k(Te.value.user?.name||"未知用户"),1)]),_:1}),o(xa,{label:"用户ID"},{default:u(()=>[c(k(Te.value.user?.id),1)]),_:1}),o(xa,{label:"状态"},{default:u(()=>[o(ya,{type:ra(Te.value.status)},{default:u(()=>[c(k(ua(Te.value.status)),1)]),_:1},8,["type"])]),_:1}),o(xa,{label:"提现金额"},{default:u(()=>[c("¥"+k(i(g)(Te.value.amount)),1)]),_:1}),o(xa,{label:"手续费"},{default:u(()=>[c("¥"+k(i(g)(Te.value.fee||0)),1)]),_:1}),o(xa,{label:"实际到账"},{default:u(()=>[c("¥"+k(i(g)(Te.value.amount-(Te.value.fee||0))),1)]),_:1}),o(xa,{label:"提现方式"},{default:u(()=>[c(k(ia(Te.value.method)),1)]),_:1}),o(xa,{label:"账户姓名"},{default:u(()=>[c(k(Te.value.account_name),1)]),_:1}),o(xa,{label:"账户号码"},{default:u(()=>[c(k(Te.value.account_number),1)]),_:1}),o(xa,{label:"申请时间"},{default:u(()=>[c(k(i(b)(Te.value.created_at)),1)]),_:1}),o(xa,{label:"处理时间"},{default:u(()=>[c(k(Te.value.processed_at?i(b)(Te.value.processed_at):"-"),1)]),_:1}),o(xa,{label:"处理人"},{default:u(()=>[c(k(Te.value.processed_by||"-"),1)]),_:1})]),_:1}),Te.value.remark?(n(),d("div",Ve,[l[37]||(l[37]=r("h4",null,"申请备注",-1)),o(t,null,{default:u(()=>[r("p",null,k(Te.value.remark),1)]),_:1})])):v("",!0),Te.value.admin_remark?(n(),d("div",je,[l[38]||(l[38]=r("h4",null,"管理员备注",-1)),o(t,null,{default:u(()=>[r("p",null,k(Te.value.admin_remark),1)]),_:1})])):v("",!0)])):v("",!0)]),_:1},8,["modelValue"]),o(ja,{modelValue:Ee.value,"onUpdate:modelValue":l[14]||(l[14]=e=>Ee.value=e),title:"approve"===Ae.value?"批准提现":"拒绝提现",width:"500px"},{footer:u(()=>[r("div",$e,[o(ga,{onClick:l[13]||(l[13]=e=>Ee.value=!1)},{default:u(()=>l[42]||(l[42]=[c("取消",-1)])),_:1,__:[42]}),o(ga,{type:"approve"===Ae.value?"success":"danger",onClick:oa,loading:Be.value},{default:u(()=>[c(k("approve"===Ae.value?"批准":"拒绝"),1)]),_:1},8,["type","loading"])])]),default:u(()=>[Te.value?(n(),d("div",Ue,[o(Ca,{title:("approve"===Ae.value?"批准":"拒绝")+"提现申请",description:`用户: ${Te.value.user?.name} | 金额: ¥${i(g)(Te.value.amount)}`,type:"approve"===Ae.value?"success":"warning","show-icon":"",closable:!1},null,8,["title","description","type"])])):v("",!0),o(ba,{ref:"reviewFormRef",model:We,"label-width":"100px"},{default:u(()=>[o(pa,{label:"管理员备注",prop:"admin_remark"},{default:u(()=>[o(ca,{modelValue:We.admin_remark,"onUpdate:modelValue":l[12]||(l[12]=e=>We.admin_remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(ja,{modelValue:Fe.value,"onUpdate:modelValue":l[17]||(l[17]=e=>Fe.value=e),title:`批量${"approve"===Ge.value?"批准":"拒绝"}提现`,width:"500px"},{footer:u(()=>[r("div",Re,[o(ga,{onClick:l[16]||(l[16]=e=>Fe.value=!1)},{default:u(()=>l[43]||(l[43]=[c("取消",-1)])),_:1,__:[43]}),o(ga,{type:"approve"===Ge.value?"success":"danger",onClick:ta,loading:Me.value},{default:u(()=>[c(" 确认"+k("approve"===Ge.value?"批准":"拒绝"),1)]),_:1},8,["type","loading"])])]),default:u(()=>[r("div",ze,[o(Ca,{title:`即将${"approve"===Ge.value?"批准":"拒绝"}${Pe.value.length}条提现申请`,description:`总金额: ¥${i(g)(Pe.value.reduce((e,a)=>e+a.amount,0))}`,type:"approve"===Ge.value?"success":"warning","show-icon":"",closable:!1},null,8,["title","description","type"])]),o(ba,{ref:"batchReviewFormRef",model:Ye,"label-width":"100px"},{default:u(()=>[o(pa,{label:"管理员备注",prop:"admin_remark"},{default:u(()=>[o(ca,{modelValue:Ye.admin_remark,"onUpdate:modelValue":l[15]||(l[15]=e=>Ye.admin_remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-07782019"]]);export{De as default};
