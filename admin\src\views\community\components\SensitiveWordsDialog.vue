<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="敏感词库管理"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="sensitive-words-dialog">
      <div class="dialog-header">
        <el-alert
          title="敏感词库用于自动检测和标记可能违规的内容"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="words-management">
        <div class="management-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="newWord"
              placeholder="添加新的敏感词"
              style="width: 200px"
              @keyup.enter="addWord"
            />
            <el-select v-model="newWordCategory" placeholder="选择分类" style="width: 120px">
              <el-option
                v-for="(name, key) in categories"
                :key="key"
                :label="name"
                :value="key"
              />
            </el-select>
            <el-button type="primary" @click="addWord" :disabled="!newWord.trim()">
              添加
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-button @click="importWords">批量导入</el-button>
            <el-button @click="exportWords">导出词库</el-button>
          </div>
        </div>

        <div class="words-categories">
          <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
            <el-tab-pane
              v-for="(name, key) in categories"
              :key="key"
              :label="`${name} (${getWordsByCategory(key).length})`"
              :name="key"
            >
              <div class="words-list">
                <div
                  v-for="word in getWordsByCategory(key)"
                  :key="word.id"
                  class="word-item"
                >
                  <span class="word-text">{{ word.text }}</span>
                  <div class="word-actions">
                    <el-button
                      size="small"
                      type="primary"
                      text
                      @click="editWord(word)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      text
                      @click="deleteWord(word)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                
                <div v-if="getWordsByCategory(key).length === 0" class="empty-words">
                  <el-empty description="该分类下暂无敏感词" :image-size="60" />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <div class="statistics-section">
        <h4>词库统计</h4>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ totalWords }}</div>
              <div class="stat-label">总词数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ Object.keys(categories).length }}</div>
              <div class="stat-label">分类数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ recentlyAdded }}</div>
              <div class="stat-label">本周新增</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ hitCount }}</div>
              <div class="stat-label">本月命中</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
        <el-button type="primary" @click="saveChanges">保存更改</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

defineProps({
  modelValue: Boolean
})

defineEmits(['update:modelValue', 'update'])

const newWord = ref('')
const newWordCategory = ref('illegal')
const activeCategory = ref('illegal')

const categories = reactive({
  illegal: '违法违规',
  gambling: '赌博博彩',
  adult: '色情成人',
  violence: '暴力血腥',
  fraud: '诈骗欺诈',
  pyramid: '传销直销',
  spam: '垃圾广告',
  other: '其他'
})

const sensitiveWords = ref([
  { id: 1, text: '违法', category: 'illegal', created_at: new Date().toISOString() },
  { id: 2, text: '犯罪', category: 'illegal', created_at: new Date().toISOString() },
  { id: 3, text: '赌博', category: 'gambling', created_at: new Date().toISOString() },
  { id: 4, text: '博彩', category: 'gambling', created_at: new Date().toISOString() },
  { id: 5, text: '色情', category: 'adult', created_at: new Date().toISOString() },
  { id: 6, text: '暴力', category: 'violence', created_at: new Date().toISOString() },
  { id: 7, text: '诈骗', category: 'fraud', created_at: new Date().toISOString() },
  { id: 8, text: '传销', category: 'pyramid', created_at: new Date().toISOString() }
])

const totalWords = computed(() => sensitiveWords.value.length)
const recentlyAdded = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  return sensitiveWords.value.filter(word => 
    new Date(word.created_at) > oneWeekAgo
  ).length
})
const hitCount = ref(156) // 模拟数据

const getWordsByCategory = (category) => {
  return sensitiveWords.value.filter(word => word.category === category)
}

const addWord = () => {
  if (!newWord.value.trim()) {
    ElMessage.warning('请输入敏感词')
    return
  }

  // 检查是否已存在
  const exists = sensitiveWords.value.some(word => 
    word.text === newWord.value.trim()
  )
  
  if (exists) {
    ElMessage.warning('该敏感词已存在')
    return
  }

  const newId = Math.max(...sensitiveWords.value.map(w => w.id), 0) + 1
  sensitiveWords.value.push({
    id: newId,
    text: newWord.value.trim(),
    category: newWordCategory.value,
    created_at: new Date().toISOString()
  })

  newWord.value = ''
  ElMessage.success('添加成功')
}

const editWord = (word) => {
  ElMessageBox.prompt('修改敏感词', '编辑', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: word.text
  }).then(({ value }) => {
    if (value && value.trim()) {
      word.text = value.trim()
      ElMessage.success('修改成功')
    }
  }).catch(() => {})
}

const deleteWord = (word) => {
  ElMessageBox.confirm(
    `确定要删除敏感词 "${word.text}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = sensitiveWords.value.findIndex(w => w.id === word.id)
    if (index > -1) {
      sensitiveWords.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

const handleCategoryChange = () => {
  // 切换分类时的处理
}

const importWords = () => {
  ElMessage.info('批量导入功能开发中...')
}

const exportWords = () => {
  const data = sensitiveWords.value.map(word => ({
    敏感词: word.text,
    分类: categories[word.category],
    创建时间: new Date(word.created_at).toLocaleString('zh-CN')
  }))
  
  // 模拟导出
  const csvContent = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `敏感词库_${new Date().toISOString().split('T')[0]}.csv`
  link.click()
  
  ElMessage.success('导出成功')
}

const saveChanges = () => {
  ElMessage.success('敏感词库已更新')
  // 这里可以调用API保存到后端
  // emit('update')
}

onMounted(() => {
  // 组件挂载时的初始化
})
</script>

<style lang="scss" scoped>
.sensitive-words-dialog {
  .dialog-header {
    margin-bottom: 24px;
  }

  .words-management {
    margin-bottom: 24px;

    .management-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 8px;

      .toolbar-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }

    .words-categories {
      :deep(.el-tabs__header) {
        margin: 0 0 20px 0;
      }

      .words-list {
        max-height: 300px;
        overflow-y: auto;

        .word-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border: 1px solid #ebeef5;
          border-radius: 6px;
          margin-bottom: 8px;
          background: #fafafa;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f9ff;
            border-color: #3b82f6;
          }

          .word-text {
            font-weight: 500;
            color: #303133;
          }

          .word-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover .word-actions {
            opacity: 1;
          }
        }

        .empty-words {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
        }
      }
    }
  }

  .statistics-section {
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 8px;
      padding: 16px;
      text-align: center;

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #409eff;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .sensitive-words-dialog {
    .words-management {
      .management-toolbar {
        flex-direction: column;
        gap: 12px;

        .toolbar-left,
        .toolbar-right {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}
</style>
