import{_ as e}from"./index-DtXAftX0.js";/* empty css                         */import{af as a,ag as l,c as o,e as t,k as s,l as i,t as r,B as c,E as n,z as d,y as p,C as y,F as u,Y as k,D as b}from"./vue-vendor-Dy164gUc.js";import{aR as m,aX as f,aJ as v,be as h,aV as _,af as C,T as g,U as j,bL as w,bK as R,at as E}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const q={class:"error-page"},x={class:"error-container"},z={class:"error-content"},$={class:"error-icon"},B={class:"error-title"},D={class:"error-subtitle"},F={class:"error-description"},H={key:0,class:"error-details"},I={class:"error-actions"},J=e({__name:"ErrorPage",setup(e){const J=a(),K=l(),L={403:{code:"403",title:"访问被拒绝",description:"抱歉，您没有权限访问此页面。请联系管理员获取相应权限。",icon:C,iconColor:"#f56565",actions:[{key:"back",label:"返回上页",type:"primary",icon:f},{key:"home",label:"回到首页",type:"default",icon:v}]},404:{code:"404",title:"页面未找到",description:"抱歉，您访问的页面不存在或已被删除。",icon:_,iconColor:"#909399",actions:[{key:"home",label:"返回首页",type:"primary",icon:v},{key:"back",label:"返回上页",type:"default",icon:f}]},500:{code:"500",title:"服务器错误",description:"抱歉，服务器出现了一些问题。请稍后再试或联系技术支持。",icon:h,iconColor:"#f56565",actions:[{key:"retry",label:"重新加载",type:"primary",icon:m},{key:"back",label:"返回上页",type:"default",icon:f},{key:"home",label:"回到首页",type:"default",icon:v}]},load:{code:"ERROR",title:"组件加载失败",description:"抱歉，请求的页面组件无法正常加载。这可能是由于网络问题或组件文件缺失导致的。",icon:h,iconColor:"#f56565",actions:[{key:"retry",label:"重新加载",type:"primary",icon:m},{key:"back",label:"返回上页",type:"default",icon:f},{key:"home",label:"回到首页",type:"default",icon:v}]}},O=o(()=>K.params.type||K.query.type||"404"),P=o(()=>K.query.details||null),T=o(()=>L[O.value]||L[404]),U=()=>{window.history.length>1?J.go(-1):V()},V=()=>{J.push("/")},X=()=>{window.location.reload()};return t(()=>{document.title=`${T.value.code} - ${T.value.title}`}),(e,a)=>{const l=g,o=w,t=R,m=E;return i(),s("div",q,[r("div",x,[r("div",z,[r("div",$,[n(l,{size:120,color:T.value.iconColor},{default:d(()=>[(i(),p(y(T.value.icon)))]),_:1},8,["color"])]),r("h1",B,j(T.value.code),1),r("h2",D,j(T.value.title),1),r("p",F,j(T.value.description),1),P.value?(i(),s("div",H,[n(t,null,{default:d(()=>[n(o,{title:"错误详情",name:"details"},{default:d(()=>[r("pre",null,j(P.value),1)]),_:1})]),_:1})])):c("",!0),r("div",I,[(i(!0),s(u,null,k(T.value.actions,e=>(i(),p(m,{key:e.key,type:e.type,onClick:a=>(e=>{switch(e){case"back":U();break;case"home":V();break;case"retry":X()}})(e.key)},{default:d(()=>[n(l,null,{default:d(()=>[(i(),p(y(e.icon)))]),_:2},1024),b(" "+j(e.label),1)]),_:2},1032,["type","onClick"]))),128))])])])])}}},[["__scopeId","data-v-c756cb80"]]);export{J as default};
