<template>
  <div class="customer-detail">
    <div class="customer-header">
      <div class="customer-basic-info">
        <el-avatar :size="80" :src="customer.avatar" />
        <div class="info-content">
          <h3>{{ customer.name }}</h3>
          <div class="customer-meta">
            <el-tag :type="getLevelColor(customer.level)">{{ customer.level_text }}</el-tag>
            <el-tag :type="getStatusColor(customer.status)">{{ customer.status_text }}</el-tag>
            <span class="customer-id">ID: {{ customer.id }}</span>
          </div>
          <div class="contact-info">
            <span v-if="customer.phone">
              <el-icon><Phone /></el-icon>
              {{ customer.phone }}
            </span>
            <span v-if="customer.wechat">
              <el-icon><ChatDotRound /></el-icon>
              {{ customer.wechat }}
            </span>
            <span v-if="customer.email">
              <el-icon><Message /></el-icon>
              {{ customer.email }}
            </span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="$emit('edit', customer)">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button @click="$emit('follow-up', customer)">
          <el-icon><Plus /></el-icon>
          添加跟进
        </el-button>
      </div>
    </div>

    <!-- 客户统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ stats.total_follow_ups || 0 }}</div>
          <div class="stat-label">跟进次数</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ stats.successful_follow_ups || 0 }}</div>
          <div class="stat-label">成功跟进</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">¥{{ customer.total_spent || 0 }}</div>
          <div class="stat-label">总消费</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-value">{{ customer.order_count || 0 }}</div>
          <div class="stat-label">订单数</div>
        </div>
      </el-col>
    </el-row>

    <!-- 详细信息 -->
    <el-row :gutter="20" class="detail-section">
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>客户信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ customer.name }}</el-descriptions-item>
            <el-descriptions-item label="性别">
              {{ customer.gender === 'male' ? '男' : customer.gender === 'female' ? '女' : '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="生日">
              {{ customer.birthday ? formatDate(customer.birthday) : '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="职业">{{ customer.occupation || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="公司">{{ customer.company || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="客户来源">{{ customer.source_text }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(customer.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="最后联系">
              {{ customer.last_contact_at ? formatDate(customer.last_contact_at) : '从未联系' }}
            </el-descriptions-item>
            <el-descriptions-item label="地址" :span="2">
              {{ customer.address || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ customer.notes || '无' }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 客户标签 -->
          <div class="tags-section" v-if="customer.tags && customer.tags.length">
            <h4>客户标签</h4>
            <el-tag
              v-for="tag in customer.tags"
              :key="tag"
              class="tag-item"
              type="info"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-card>

        <!-- 跟进记录 -->
        <el-card class="follow-up-card">
          <template #header>
            <div class="card-header">
              <span>跟进记录</span>
              <el-button size="small" @click="loadFollowUps">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="follow-up-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="followUp in followUps"
                :key="followUp.id"
                :timestamp="formatDateTime(followUp.contact_time)"
                placement="top"
              >
                <div class="follow-up-item">
                  <div class="follow-up-header">
                    <span class="follow-up-title">{{ followUp.title }}</span>
                    <el-tag :type="getFollowUpResultColor(followUp.result)" size="small">
                      {{ followUp.result_text }}
                    </el-tag>
                    <el-tag type="info" size="small">{{ followUp.type_text }}</el-tag>
                  </div>
                  <div class="follow-up-content">{{ followUp.content }}</div>
                  <div class="follow-up-meta" v-if="followUp.next_follow_up">
                    <span class="next-follow-up">
                      下次跟进: {{ formatDateTime(followUp.next_follow_up) }}
                    </span>
                  </div>
                  <div class="follow-up-meta" v-if="followUp.potential_value">
                    <span class="potential-value">
                      潜在价值: ¥{{ followUp.potential_value }}
                    </span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div class="load-more" v-if="followUps.length >= 10">
            <el-button @click="loadMoreFollowUps">加载更多</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <!-- 客户价值评分 -->
        <el-card class="value-score-card">
          <template #header>
            <span>客户价值评分</span>
          </template>
          <div class="score-display">
            <el-progress
              type="circle"
              :percentage="stats.value_score || 0"
              :color="getScoreColor(stats.value_score)"
              :width="120"
            >
              <template #default="{ percentage }">
                <span class="score-text">{{ percentage }}</span>
              </template>
            </el-progress>
            <div class="score-description">
              <p>{{ getScoreDescription(stats.value_score) }}</p>
            </div>
          </div>
        </el-card>

        <!-- 最近订单 -->
        <el-card class="recent-orders-card">
          <template #header>
            <span>最近订单</span>
          </template>
          <div class="orders-list">
            <div
              v-for="order in customer.orders"
              :key="order.id"
              class="order-item"
            >
              <div class="order-info">
                <div class="order-title">{{ order.wechat_group?.title || '订单' }}</div>
                <div class="order-meta">
                  <span class="order-amount">¥{{ order.amount }}</span>
                  <span class="order-time">{{ formatDate(order.created_at) }}</span>
                </div>
              </div>
              <el-tag :type="getOrderStatusColor(order.status)" size="small">
                {{ getOrderStatusText(order.status) }}
              </el-tag>
            </div>
            <div v-if="!customer.orders || customer.orders.length === 0" class="no-orders">
              <el-empty description="暂无订单" />
            </div>
          </div>
        </el-card>

        <!-- 偏好设置 -->
        <el-card class="preferences-card" v-if="customer.preferences">
          <template #header>
            <span>偏好设置</span>
          </template>
          <div class="preferences-list">
            <div class="preference-item" v-if="customer.preferences.contact_method">
              <span class="label">联系方式偏好:</span>
              <span class="value">{{ getContactMethodText(customer.preferences.contact_method) }}</span>
            </div>
            <div class="preference-item" v-if="customer.preferences.contact_time">
              <span class="label">联系时间偏好:</span>
              <span class="value">{{ getContactTimeText(customer.preferences.contact_time) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Phone, ChatDotRound, Message, Edit, Plus, Refresh 
} from '@element-plus/icons-vue'
import { customerApi } from '@/api/customer'

const props = defineProps({
  customer: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'edit', 'follow-up'])

// 响应式数据
const followUps = ref([])
const stats = ref({})
const followUpPage = ref(1)

// 计算属性
const customerValueScore = computed(() => {
  return props.customer.value_score || 0
})

// 方法
const loadFollowUps = async () => {
  try {
    const response = await customerApi.getFollowUps(props.customer.id, {
      page: 1,
      limit: 10
    })
    followUps.value = response.data.data
  } catch (error) {
    ElMessage.error('加载跟进记录失败')
  }
}

const loadMoreFollowUps = async () => {
  try {
    followUpPage.value++
    const response = await customerApi.getFollowUps(props.customer.id, {
      page: followUpPage.value,
      limit: 10
    })
    followUps.value.push(...response.data.data)
  } catch (error) {
    ElMessage.error('加载更多跟进记录失败')
  }
}

const loadStats = async () => {
  try {
    const response = await customerApi.getDetail(props.customer.id)
    stats.value = response.data.stats
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'A': 'danger',
    'B': 'warning',
    'C': 'primary',
    'D': 'info'
  }
  return colors[level] || 'info'
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'warning',
    'potential': 'primary',
    'lost': 'danger'
  }
  return colors[status] || 'info'
}

const getFollowUpResultColor = (result) => {
  const colors = {
    'successful': 'success',
    'failed': 'danger',
    'pending': 'warning',
    'scheduled': 'primary'
  }
  return colors[result] || 'info'
}

const getScoreColor = (score) => {
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  if (score >= 40) return '#F56C6C'
  return '#909399'
}

const getScoreDescription = (score) => {
  if (score >= 80) return '高价值客户'
  if (score >= 60) return '中等价值客户'
  if (score >= 40) return '一般价值客户'
  return '低价值客户'
}

const getOrderStatusColor = (status) => {
  const colors = {
    'pending': 'warning',
    'paid': 'success',
    'cancelled': 'danger',
    'refunded': 'info'
  }
  return colors[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return texts[status] || '未知'
}

const getContactMethodText = (method) => {
  const texts = {
    'phone': '电话',
    'wechat': '微信',
    'email': '邮件'
  }
  return texts[method] || '未知'
}

const getContactTimeText = (time) => {
  const texts = {
    'morning': '上午',
    'afternoon': '下午',
    'evening': '晚上'
  }
  return texts[time] || '未知'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadFollowUps()
    loadStats()
  }
})
</script>

<style scoped>
.customer-detail {
  padding: 20px;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.customer-basic-info {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.info-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.customer-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.customer-id {
  color: #909399;
  font-size: 12px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.contact-info span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.detail-section {
  margin-bottom: 20px;
}

.tags-section {
  margin-top: 20px;
}

.tags-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.follow-up-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.follow-up-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.follow-up-item {
  padding: 10px 0;
}

.follow-up-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.follow-up-title {
  font-weight: 500;
  color: #303133;
}

.follow-up-content {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.follow-up-meta {
  font-size: 12px;
  color: #909399;
}

.next-follow-up,
.potential-value {
  margin-right: 15px;
}

.load-more {
  text-align: center;
  margin-top: 20px;
}

.value-score-card {
  margin-bottom: 20px;
}

.score-display {
  text-align: center;
  padding: 20px;
}

.score-text {
  font-size: 18px;
  font-weight: bold;
}

.score-description {
  margin-top: 15px;
}

.score-description p {
  margin: 0;
  color: #606266;
}

.recent-orders-card {
  margin-bottom: 20px;
}

.orders-list {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.order-meta {
  font-size: 12px;
  color: #909399;
}

.order-amount {
  color: #67C23A;
  font-weight: bold;
  margin-right: 10px;
}

.no-orders {
  padding: 20px;
  text-align: center;
}

.preferences-card {
  margin-bottom: 20px;
}

.preferences-list {
  padding: 10px 0;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preference-item:last-child {
  border-bottom: none;
}

.preference-item .label {
  color: #909399;
  font-size: 14px;
}

.preference-item .value {
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .customer-basic-info {
    flex-direction: column;
    text-align: center;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .detail-section .el-col {
    margin-bottom: 20px;
  }
}
</style>