<template>
  <div class="payment-channel-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>支付通道管理</h1>
      <p class="page-description">系统管理员可以控制支付通道的启用状态和用户权限</p>
    </div>

    <!-- 统计概览 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><CreditCard /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.total_channels }}</h3>
                <p>总通道数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.active_channels }}</h3>
                <p>启用通道</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon configs">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.active_configs }}</h3>
                <p>活跃配置</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon permissions">
                <el-icon><Key /></el-icon>
              </div>
              <div class="card-info">
                <h3>{{ overview.active_permissions }}</h3>
                <p>有效权限</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showGrantPermissionDialog">
        <el-icon><Plus /></el-icon>
        批量授权
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 支付通道列表 -->
    <el-card class="channel-list-card">
      <template #header>
        <div class="card-header">
          <span>支付通道列表</span>
        </div>
      </template>

      <el-table :data="channels" v-loading="loading" stripe>
        <el-table-column prop="channel_name" label="通道名称" min-width="120">
          <template #default="{ row }">
            <div class="channel-info">
              <img :src="row.channel_icon" :alt="row.channel_name" class="channel-icon" />
              <span>{{ row.channel_name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="channel_code" label="通道代码" width="120" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              @change="toggleChannelStatus(row)"
              :loading="row.statusLoading"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="fee_rate" label="手续费率" width="100">
          <template #default="{ row }">
            {{ (row.fee_rate * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        
        <el-table-column prop="config_count" label="配置数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.config_count }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="active_config_count" label="活跃配置" width="100">
          <template #default="{ row }">
            <el-tag type="success">{{ row.active_config_count }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewChannelUsers(row)">
              查看用户
            </el-button>
            <el-button size="small" type="primary" @click="grantChannelPermission(row)">
              授权
            </el-button>
            <el-button size="small" @click="editChannel(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 批量授权对话框 -->
    <el-dialog
      v-model="grantPermissionDialog.visible"
      title="批量授权支付通道权限"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="grantPermissionForm" label-width="120px">
        <el-form-item label="用户类型">
          <el-select v-model="grantPermissionForm.user_type" placeholder="请选择用户类型">
            <el-option label="普通用户" value="user" />
            <el-option label="分销商" value="distributor" />
            <el-option label="分站管理员" value="substation" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="用户ID">
          <el-input
            v-model="grantPermissionForm.user_id"
            placeholder="请输入用户ID"
            type="number"
          />
        </el-form-item>
        
        <el-form-item label="支付通道">
          <el-select v-model="grantPermissionForm.channel_codes" multiple placeholder="请选择支付通道">
            <el-option
              v-for="channel in channels"
              :key="channel.channel_code"
              :label="channel.channel_name"
              :value="channel.channel_code"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限类型">
          <el-checkbox-group v-model="grantPermissionForm.permission_types">
            <el-checkbox label="use">使用权限</el-checkbox>
            <el-checkbox label="config">配置权限</el-checkbox>
            <el-checkbox label="manage">管理权限</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="grantPermissionForm.expires_at"
            type="datetime"
            placeholder="选择过期时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="grantPermissionForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="grantPermissionDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitGrantPermission" :loading="grantPermissionDialog.loading">
          确认授权
        </el-button>
      </template>
    </el-dialog>

    <!-- 通道用户对话框 -->
    <el-dialog
      v-model="channelUsersDialog.visible"
      :title="`${channelUsersDialog.channelName} - 用户列表`"
      width="1000px"
    >
      <el-tabs v-model="channelUsersDialog.activeTab">
        <el-tab-pane label="已配置用户" name="configured">
          <el-table :data="channelUsersDialog.configuredUsers" stripe>
            <el-table-column prop="owner_type" label="用户类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getUserTypeTagType(row.owner_type)">
                  {{ getUserTypeName(row.owner_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="owner_id" label="用户ID" width="100" />
            <el-table-column prop="config_name" label="配置名称" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status ? 'success' : 'danger'">
                  {{ row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="test_status" label="测试状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.test_status ? 'success' : 'warning'">
                  {{ row.test_status ? '已测试' : '未测试' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180" />
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="已授权用户" name="authorized">
          <el-table :data="channelUsersDialog.authorizedUsers" stripe>
            <el-table-column prop="user_type" label="用户类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getUserTypeTagType(row.user_type)">
                  {{ getUserTypeName(row.user_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="user_id" label="用户ID" width="100" />
            <el-table-column prop="permission_type_name" label="权限类型" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status ? 'success' : 'danger'">
                  {{ row.status ? '有效' : '无效' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="granted_at" label="授权时间" width="180" />
            <el-table-column prop="expires_at" label="过期时间" width="180">
              <template #default="{ row }">
                {{ row.expires_at || '永不过期' }}
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CreditCard, Check, Setting, Key, Plus, Refresh } from '@element-plus/icons-vue'
import { paymentChannelManagementApi } from '@/api/payment'

// 响应式数据
const loading = ref(false)
const channels = ref([])
const overview = ref({
  total_channels: 0,
  active_channels: 0,
  inactive_channels: 0,
  total_configs: 0,
  active_configs: 0,
  tested_configs: 0,
  total_permissions: 0,
  active_permissions: 0,
  expiring_permissions: 0
})

// 批量授权对话框
const grantPermissionDialog = reactive({
  visible: false,
  loading: false
})

const grantPermissionForm = reactive({
  user_type: '',
  user_id: '',
  channel_codes: [],
  permission_types: [],
  expires_at: '',
  remark: ''
})

// 通道用户对话框
const channelUsersDialog = reactive({
  visible: false,
  channelName: '',
  activeTab: 'configured',
  configuredUsers: [],
  authorizedUsers: []
})

// 页面加载时获取数据
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  await Promise.all([
    loadChannels(),
    loadOverview()
  ])
}

// 加载支付通道列表
const loadChannels = async () => {
  try {
    loading.value = true
    const response = await paymentChannelManagementApi.getChannels()
    channels.value = response.data.map(channel => ({
      ...channel,
      statusLoading: false
    }))
  } catch (error) {
    ElMessage.error('加载支付通道列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 加载概览数据
const loadOverview = async () => {
  try {
    const response = await paymentChannelManagementApi.getOverview()
    overview.value = response.data
  } catch (error) {
    ElMessage.error('加载概览数据失败')
    console.error(error)
  }
}

// 切换通道状态
const toggleChannelStatus = async (channel) => {
  try {
    channel.statusLoading = true
    
    const action = channel.status ? '启用' : '禁用'
    await ElMessageBox.confirm(
      `确定要${action}支付通道"${channel.channel_name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await paymentChannelManagementApi.toggleStatus({
      channel_code: channel.channel_code,
      status: channel.status
    })

    ElMessage.success(`${action}成功`)
    await loadOverview() // 刷新概览数据
  } catch (error) {
    // 恢复原状态
    channel.status = !channel.status
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      console.error(error)
    }
  } finally {
    channel.statusLoading = false
  }
}

// 显示授权对话框
const showGrantPermissionDialog = () => {
  // 重置表单
  Object.assign(grantPermissionForm, {
    user_type: '',
    user_id: '',
    channel_codes: [],
    permission_types: [],
    expires_at: '',
    remark: ''
  })
  grantPermissionDialog.visible = true
}

// 为单个通道授权
const grantChannelPermission = (channel) => {
  grantPermissionForm.channel_codes = [channel.channel_code]
  showGrantPermissionDialog()
}

// 提交授权
const submitGrantPermission = async () => {
  try {
    // 验证表单
    if (!grantPermissionForm.user_type || !grantPermissionForm.user_id || 
        grantPermissionForm.channel_codes.length === 0 || 
        grantPermissionForm.permission_types.length === 0) {
      ElMessage.warning('请填写完整的授权信息')
      return
    }

    grantPermissionDialog.loading = true

    // 构建权限数据
    const permissions = []
    for (const channelCode of grantPermissionForm.channel_codes) {
      for (const permissionType of grantPermissionForm.permission_types) {
        permissions.push({
          user_type: grantPermissionForm.user_type,
          user_id: parseInt(grantPermissionForm.user_id),
          channel_code: channelCode,
          permission_type: permissionType,
          expires_at: grantPermissionForm.expires_at || null,
          remark: grantPermissionForm.remark || null
        })
      }
    }

    await paymentChannelManagementApi.grantPermissions({ permissions })

    ElMessage.success('权限授权成功')
    grantPermissionDialog.visible = false
    await loadOverview() // 刷新概览数据
  } catch (error) {
    ElMessage.error('权限授权失败')
    console.error(error)
  } finally {
    grantPermissionDialog.loading = false
  }
}

// 查看通道用户
const viewChannelUsers = async (channel) => {
  try {
    channelUsersDialog.channelName = channel.channel_name
    channelUsersDialog.visible = true
    
    const response = await paymentChannelManagementApi.getChannelUsers(channel.channel_code)
    channelUsersDialog.configuredUsers = response.data.configured_users
    channelUsersDialog.authorizedUsers = response.data.authorized_users
  } catch (error) {
    ElMessage.error('加载用户列表失败')
    console.error(error)
  }
}

// 编辑通道
const editChannel = (channel) => {
  ElMessage.info('编辑功能开发中...')
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 获取用户类型标签类型
const getUserTypeTagType = (userType) => {
  const types = {
    user: '',
    distributor: 'success',
    substation: 'warning'
  }
  return types[userType] || ''
}

// 获取用户类型名称
const getUserTypeName = (userType) => {
  const names = {
    user: '普通用户',
    distributor: '分销商',
    substation: '分站管理员'
  }
  return names[userType] || userType
}
</script>

<style scoped>
.payment-channel-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.configs {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.permissions {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.card-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.toolbar {
  margin-bottom: 20px;
}

.channel-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-info {
  display: flex;
  align-items: center;
}

.channel-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
}
</style>