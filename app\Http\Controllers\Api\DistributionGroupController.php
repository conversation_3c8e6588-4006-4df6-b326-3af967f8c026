<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DistributionGroup;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 分销组控制器
 * 处理分销组的增删改查、分销商管理等操作
 */
class DistributionGroupController extends Controller
{
    /**
     * 获取分销组列表
     */
    public function index(Request $request)
    {
        $query = DistributionGroup::with(['substation:id,name', 'users:id,username,nickname,created_at']);

        $user = Auth::user();
        
        // 权限控制
        if ($user->isSubstation()) {
            $query->where('substation_id', $user->substation->id);
        }

        // 筛选条件
        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('commission_rate_min')) {
            $query->where('commission_rate', '>=', $request->commission_rate_min);
        }

        if ($request->filled('commission_rate_max')) {
            $query->where('commission_rate', '<=', $request->commission_rate_max);
        }

        // 排序
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $groups = $query->paginate($request->input('per_page', 15));

        // 为每个分销组添加统计信息
        foreach ($groups as $group) {
            $group->stats = [
                'member_count' => $group->users->count(),
                'active_members' => $group->users->where('status', 1)->count(),
                'total_orders' => $group->getTotalOrders(),
                'total_commission' => $group->getTotalCommission(),
                'monthly_orders' => $group->getMonthlyOrders(),
                'monthly_commission' => $group->getMonthlyCommission(),
            ];
        }

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => $groups,
        ]);
    }

    /**
     * 创建分销组
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation()) {
            return response()->json(['success' => false, 'message' => '您没有权限创建分销组'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'description' => 'nullable|string|max:500',
            'status' => 'nullable|in:1,2',
            'substation_id' => 'nullable|exists:substations,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $data = $request->only(['name', 'commission_rate', 'description', 'status']);
        
        // 设置分站ID
        if ($user->isSubstation()) {
            $data['substation_id'] = $user->substation->id;
        } else {
            $data['substation_id'] = $request->substation_id;
        }

        $group = DistributionGroup::create($data);

        return response()->json([
            'success' => true,
            'message' => '创建成功',
            'data' => $group,
        ], 201);
    }

    /**
     * 获取分销组详情
     */
    public function show($id)
    {
        $group = DistributionGroup::with(['substation', 'users'])->find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        // 权限验证
        $this->authorize('view', $group);

        // 添加统计信息
        $group->stats = [
            'member_count' => $group->users->count(),
            'active_members' => $group->users->where('status', 1)->count(),
            'total_orders' => $group->getTotalOrders(),
            'total_commission' => $group->getTotalCommission(),
            'monthly_orders' => $group->getMonthlyOrders(),
            'monthly_commission' => $group->getMonthlyCommission(),
            'recent_members' => $group->users()->orderBy('created_at', 'desc')->limit(10)->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $group,
        ]);
    }

    /**
     * 更新分销组
     */
    public function update(Request $request, $id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        $this->authorize('update', $group);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:100',
            'commission_rate' => 'sometimes|required|numeric|min:0|max:100',
            'description' => 'nullable|string|max:500',
            'status' => 'nullable|in:1,2',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $group->update($request->only(['name', 'commission_rate', 'description', 'status']));

        return response()->json([
            'success' => true,
            'message' => '更新成功',
            'data' => $group,
        ]);
    }

    /**
     * 删除分销组
     */
    public function destroy($id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }
        
        $this->authorize('delete', $group);

        // 检查是否有关联的用户
        if ($group->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该分销组有关联用户，无法删除'
            ], 400);
        }

        $group->delete();

        return response()->json(['success' => true, 'message' => '删除成功']);
    }

    /**
     * 获取分销组成员列表
     */
    public function members(Request $request, $id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        $this->authorize('view', $group);

        $query = $group->users()->with(['parent:id,username,nickname']);

        // 筛选条件
        if ($request->filled('username')) {
            $query->where('username', 'like', '%' . $request->username . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        $members = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $members,
        ]);
    }

    /**
     * 添加成员到分销组
     */
    public function addMember(Request $request, $id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        $this->authorize('update', $group);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = User::find($request->user_id);
        
        // 检查用户是否已经在其他分销组中
        if ($user->distribution_group_id) {
            return response()->json([
                'success' => false,
                'message' => '该用户已在其他分销组中'
            ], 400);
        }

        $user->update(['distribution_group_id' => $group->id]);

        return response()->json([
            'success' => true,
            'message' => '添加成员成功',
        ]);
    }

    /**
     * 从分销组中移除成员
     */
    public function removeMember(Request $request, $id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        $this->authorize('update', $group);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = User::find($request->user_id);
        
        if ($user->distribution_group_id !== $group->id) {
            return response()->json([
                'success' => false,
                'message' => '该用户不在此分销组中'
            ], 400);
        }

        $user->update(['distribution_group_id' => null]);

        return response()->json([
            'success' => true,
            'message' => '移除成员成功',
        ]);
    }

    /**
     * 获取分销组统计数据
     */
    public function stats(Request $request, $id)
    {
        $group = DistributionGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '分销组不存在'], 404);
        }

        $this->authorize('view', $group);

        $stats = [
            'basic' => [
                'member_count' => $group->users()->count(),
                'active_members' => $group->users()->where('status', 1)->count(),
                'total_orders' => $group->getTotalOrders(),
                'total_commission' => $group->getTotalCommission(),
                'avg_commission' => $group->getAvgCommission(),
            ],
            'monthly' => [
                'orders' => $group->getMonthlyOrders(),
                'commission' => $group->getMonthlyCommission(),
                'new_members' => $group->users()->whereMonth('created_at', now()->month)->count(),
            ],
            'trends' => [
                'commission_trend' => $group->getCommissionTrend(),
                'member_trend' => $group->getMemberTrend(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 批量更新分销组状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:distribution_groups,id',
            'status' => 'required|in:1,2',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = Auth::user();
        $query = DistributionGroup::whereIn('id', $request->ids);

        // 权限控制
        if ($user->isSubstation()) {
            $query->where('substation_id', $user->substation->id);
        }

        $updated = $query->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => "成功更新 {$updated} 个分销组状态",
        ]);
    }
} 