<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use App\Models\PaymentConfig;
use App\Models\PaymentChannel;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 支付配置控制器
 * 处理用户支付配置的增删改查和测试
 */
class PaymentConfigController extends Controller
{
    protected PaymentService $paymentService;
    protected \App\Services\PaymentChannelManagementService $channelManagementService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
        $this->channelManagementService = app(\App\Services\PaymentChannelManagementService::class);
    }

    /**
     * 获取用户可配置的支付通道列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $userType = $request->input('user_type', 'user');
            $userId = $request->input('user_id', auth()->id());

            // 验证用户权限
            if (!$this->canManagePaymentConfig($userType, $userId)) {
                return $this->error('无权限管理支付配置', 403);
            }

            // 使用新的权限控制系统获取可配置通道
            $configs = $this->channelManagementService->getUserConfigurableChannels($userType, $userId);

            return $this->success($configs);

        } catch (\Exception $e) {
            Log::error('获取支付配置失败', [
                'user_type' => $request->input('user_type'),
                'user_id' => $request->input('user_id'),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取指定支付渠道的配置
     */
    public function show(Request $request, string $channelCode): JsonResponse
    {
        try {
            $userType = $request->input('user_type', 'user');
            $userId = $request->input('user_id', auth()->id());

            // 验证用户权限
            if (!$this->canManagePaymentConfig($userType, $userId)) {
                return $this->error('无权限查看支付配置', 403);
            }

            $config = PaymentConfig::where('owner_type', $userType)
                ->where('owner_id', $userId)
                ->where('channel_code', $channelCode)
                ->with('paymentChannel')
                ->first();

            if (!$config) {
                return $this->error('支付配置不存在');
            }

            return $this->success([
                'id' => $config->id,
                'channel_code' => $config->channel_code,
                'channel_name' => $config->paymentChannel->channel_name ?? '',
                'config_name' => $config->config_name,
                'config_data' => $config->config_data,
                'status' => $config->status,
                'test_status' => $config->test_status,
                'test_time' => $config->test_time?->toDateTimeString(),
                'is_complete' => $config->isConfigComplete(),
                'is_available' => $config->isAvailable(),
            ]);

        } catch (\Exception $e) {
            Log::error('获取支付配置详情失败', [
                'channel_code' => $channelCode,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付配置详情失败：' . $e->getMessage());
        }
    }

    /**
     * 保存支付配置（使用新的权限控制系统）
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'channel_code' => 'required|string|exists:payment_channels,channel_code',
                'config_name' => 'string|max:100',
                'config_data' => 'required|array',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $channelCode = $request->input('channel_code');

            // 验证用户权限
            if (!$this->canManagePaymentConfig($userType, $userId)) {
                return $this->error('无权限管理支付配置', 403);
            }

            // 使用新的权限控制系统保存配置
            $configData = $request->input('config_data');
            if ($request->has('config_name')) {
                $configData['config_name'] = $request->input('config_name');
            }

            $result = $this->channelManagementService->saveUserConfig(
                $userType,
                $userId,
                $channelCode,
                $configData
            );

            if ($result['success']) {
                return $this->success([
                    'config_id' => $result['config_id'],
                    'message' => $result['message']
                ], '支付配置保存成功');
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            Log::error('保存支付配置失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('保存支付配置失败：' . $e->getMessage());
        }
    }

    /**
     * 更新支付配置
     */
    public function update(Request $request, int $configId): JsonResponse
    {
        try {
            $config = PaymentConfig::findOrFail($configId);

            // 验证用户权限
            if (!$this->canManagePaymentConfig($config->owner_type, $config->owner_id)) {
                return $this->error('无权限修改此支付配置', 403);
            }

            $validator = Validator::make($request->all(), [
                'config_name' => 'string|max:100',
                'config_data' => 'array',
                'status' => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $updateData = [];
            if ($request->has('config_name')) {
                $updateData['config_name'] = $request->input('config_name');
            }
            if ($request->has('config_data')) {
                $updateData['config_data'] = $request->input('config_data');
                // 配置更新后重置测试状态
                $updateData['test_status'] = false;
                $updateData['test_time'] = null;
            }
            if ($request->has('status')) {
                $updateData['status'] = $request->input('status');
            }

            $config->update($updateData);

            return $this->success($config->fresh(), '支付配置更新成功');

        } catch (\Exception $e) {
            Log::error('更新支付配置失败', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);

            return $this->error('更新支付配置失败：' . $e->getMessage());
        }
    }

    /**
     * 删除支付配置
     */
    public function destroy(int $configId): JsonResponse
    {
        try {
            $config = PaymentConfig::findOrFail($configId);

            // 验证用户权限
            if (!$this->canManagePaymentConfig($config->owner_type, $config->owner_id)) {
                return $this->error('无权限删除此支付配置', 403);
            }

            $config->delete();

            return $this->success(null, '支付配置删除成功');

        } catch (\Exception $e) {
            Log::error('删除支付配置失败', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);

            return $this->error('删除支付配置失败：' . $e->getMessage());
        }
    }

    /**
     * 测试支付配置
     */
    public function test(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'channel_code' => 'required|string|exists:payment_channels,channel_code',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $channelCode = $request->input('channel_code');

            // 验证用户权限
            if (!$this->canManagePaymentConfig($userType, $userId)) {
                return $this->error('无权限测试支付配置', 403);
            }

            $result = $this->paymentService->testPaymentConfig($userType, $userId, $channelCode);

            if ($result['success']) {
                return $this->success($result, '支付配置测试成功');
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            Log::error('测试支付配置失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('测试支付配置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付渠道配置模板
     */
    public function template(string $channelCode): JsonResponse
    {
        try {
            $channel = PaymentChannel::where('channel_code', $channelCode)->first();
            
            if (!$channel) {
                return $this->error('支付渠道不存在');
            }

            $template = $channel->getConfigTemplate();

            return $this->success([
                'channel_code' => $channelCode,
                'channel_name' => $channel->channel_name,
                'template' => $template,
                'description' => $channel->description,
            ]);

        } catch (\Exception $e) {
            Log::error('获取配置模板失败', [
                'channel_code' => $channelCode,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取配置模板失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户可用的支付渠道
     */
    public function availableChannels(Request $request): JsonResponse
    {
        try {
            $userType = $request->input('user_type', 'user');
            $userId = $request->input('user_id', auth()->id());

            // 验证用户权限
            if (!$this->canManagePaymentConfig($userType, $userId)) {
                return $this->error('无权限查看支付渠道', 403);
            }

            $channels = $this->paymentService->getUserAvailableChannels($userType, $userId);

            return $this->success($channels);

        } catch (\Exception $e) {
            Log::error('获取可用支付渠道失败', [
                'user_type' => $request->input('user_type'),
                'user_id' => $request->input('user_id'),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取可用支付渠道失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户是否可以管理支付配置
     */
    private function canManagePaymentConfig(string $userType, int $userId): bool
    {
        $currentUser = auth()->user();
        
        if (!$currentUser) {
            return false;
        }

        // 管理员可以管理所有配置
        if ($currentUser->hasRole('admin')) {
            return true;
        }

        // 用户只能管理自己的配置
        if ($userType === 'user' && $userId === $currentUser->id) {
            return true;
        }

        // 分站管理员可以管理自己分站的配置
        if ($userType === 'substation' && $currentUser->hasRole('substation') && $userId === $currentUser->id) {
            return true;
        }

        // 分销商可以管理自己的配置
        if ($userType === 'distributor' && $currentUser->hasRole('distributor') && $userId === $currentUser->id) {
            return true;
        }

        return false;
    }
}