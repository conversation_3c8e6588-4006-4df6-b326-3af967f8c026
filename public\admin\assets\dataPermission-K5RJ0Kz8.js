import{u as e,i as r,j as n}from"./index-DtXAftX0.js";function u(e){const n=r[e];return!!n&&n.groupCreatePermission}function t(e){const n=r[e];return n?n.dashboardScope:"user_personal"}function s(e){const n=r[e];return n?n.financeScope:"user_consumption"}function i(r,u,t,s={}){const i=e(),o=i.userInfo?.id;if("admin"===t)return!0;switch(u){case"user":return function(e,r,u,t){const s=t.role;if(t.id,"view"===e)return n(r,s);if(["create","update","delete"].includes(e))return["admin","substation"].includes(r);return!1}(r,t,0,s);case"group":return function(e,r,u,t){const s=t.user_id||t.owner_id;if("view"===e)return n(r,t.owner_role||"user");if("create"===e)return!0;if(["update","delete"].includes(e))return u===s||["admin","substation"].includes(r);return!1}(r,t,o,s);case"order":return function(e,r,u,t){const s=t.user_id,i=t.user_role||"user";if("view"===e)return n(r,i)||u===s;return!1}(r,t,o,s);case"finance":return function(e,r,u,t){if("view"!==e)return!1;const s=t.user_id,i=t.user_role||"user";return n(r,i)||u===s}(r,t,o,s);default:return!1}}export{s as a,i as b,u as c,t as g};
