/**
 * 分销员组合式API单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { nextTick } from 'vue'
import { useDistributor } from '@/composables/useDistributor'
import { DistributorService } from '@/services/DistributorService'

// Mock 分销员服务
vi.mock('@/services/DistributorService')

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElNotification: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('useDistributor', () => {
  let mockService
  let composable

  beforeEach(() => {
    // 创建模拟服务实例
    mockService = {
      getCustomers: vi.fn(),
      getCustomerDetail: vi.fn(),
      createCustomer: vi.fn(),
      updateCustomer: vi.fn(),
      deleteCustomer: vi.fn(),
      getDistributorStats: vi.fn(),
      getCommissionLogs: vi.fn(),
      getOrders: vi.fn(),
      getPromotionLinks: vi.fn(),
      clearCache: vi.fn()
    }

    // Mock 构造函数
    DistributorService.mockImplementation(() => mockService)

    // 创建组合式API实例
    composable = useDistributor()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化状态', () => {
    it('应该有正确的初始状态', () => {
      expect(composable.loading.value).toBe(false)
      expect(composable.customers.value).toEqual([])
      expect(composable.stats.value).toEqual({})
      expect(composable.pagination.value).toEqual({
        current_page: 1,
        per_page: 20,
        total: 0,
        total_pages: 0
      })
      expect(composable.filters.value).toEqual({
        keyword: '',
        level: '',
        status: '',
        date_range: []
      })
    })

    it('应该创建服务实例', () => {
      expect(DistributorService).toHaveBeenCalled()
    })
  })

  describe('客户管理', () => {
    describe('loadCustomers', () => {
      const mockCustomersResponse = {
        data: [
          { id: 1, name: '张三', level: 'A' },
          { id: 2, name: '李四', level: 'B' }
        ],
        total: 2,
        total_pages: 1
      }

      it('应该成功加载客户列表', async () => {
        mockService.getCustomers.mockResolvedValue(mockCustomersResponse)

        await composable.loadCustomers()

        expect(composable.loading.value).toBe(false)
        expect(composable.customers.value).toHaveLength(2)
        expect(composable.customers.value[0].name).toBe('张三')
        expect(composable.pagination.value.total).toBe(2)
      })

      it('应该处理加载状态', async () => {
        let resolvePromise
        const promise = new Promise(resolve => {
          resolvePromise = resolve
        })
        mockService.getCustomers.mockReturnValue(promise)

        const loadPromise = composable.loadCustomers()
        
        // 检查加载状态
        expect(composable.loading.value).toBe(true)

        // 完成加载
        resolvePromise(mockCustomersResponse)
        await loadPromise

        expect(composable.loading.value).toBe(false)
      })

      it('应该传递正确的参数', async () => {
        mockService.getCustomers.mockResolvedValue(mockCustomersResponse)

        composable.pagination.value.current_page = 2
        composable.pagination.value.per_page = 50
        composable.filters.value.keyword = '张三'
        composable.filters.value.level = 'A'

        await composable.loadCustomers()

        expect(mockService.getCustomers).toHaveBeenCalledWith({
          page: 2,
          limit: 50,
          keyword: '张三',
          level: 'A',
          status: '',
          date_range: []
        })
      })

      it('应该处理错误', async () => {
        const error = new Error('加载失败')
        mockService.getCustomers.mockRejectedValue(error)

        await composable.loadCustomers()

        expect(composable.loading.value).toBe(false)
        expect(composable.customers.value).toEqual([])
      })

      it('应该支持强制刷新', async () => {
        mockService.getCustomers.mockResolvedValue(mockCustomersResponse)

        await composable.loadCustomers(true)

        expect(mockService.getCustomers).toHaveBeenCalledWith(
          expect.any(Object),
          false // useCache = false
        )
      })
    })

    describe('loadCustomerDetail', () => {
      const mockCustomerDetail = {
        id: 1,
        name: '张三',
        phone: '13800138001',
        level: 'A'
      }

      it('应该成功加载客户详情', async () => {
        mockService.getCustomerDetail.mockResolvedValue(mockCustomerDetail)

        const result = await composable.loadCustomerDetail(1)

        expect(mockService.getCustomerDetail).toHaveBeenCalledWith(1)
        expect(result).toEqual(mockCustomerDetail)
      })

      it('应该处理加载状态', async () => {
        let resolvePromise
        const promise = new Promise(resolve => {
          resolvePromise = resolve
        })
        mockService.getCustomerDetail.mockReturnValue(promise)

        const loadPromise = composable.loadCustomerDetail(1)
        
        expect(composable.loading.value).toBe(true)

        resolvePromise(mockCustomerDetail)
        await loadPromise

        expect(composable.loading.value).toBe(false)
      })

      it('应该处理错误', async () => {
        const error = new Error('加载失败')
        mockService.getCustomerDetail.mockRejectedValue(error)

        const result = await composable.loadCustomerDetail(1)

        expect(result).toBeNull()
        expect(composable.loading.value).toBe(false)
      })
    })

    describe('createCustomer', () => {
      const customerData = {
        name: '王五',
        phone: '13800138003',
        level: 'B'
      }

      const mockCreatedCustomer = {
        id: 3,
        ...customerData,
        created_at: new Date()
      }

      it('应该成功创建客户', async () => {
        mockService.createCustomer.mockResolvedValue(mockCreatedCustomer)

        const result = await composable.createCustomer(customerData)

        expect(mockService.createCustomer).toHaveBeenCalledWith(customerData)
        expect(result).toEqual(mockCreatedCustomer)
      })

      it('应该处理创建状态', async () => {
        let resolvePromise
        const promise = new Promise(resolve => {
          resolvePromise = resolve
        })
        mockService.createCustomer.mockReturnValue(promise)

        const createPromise = composable.createCustomer(customerData)
        
        expect(composable.loading.value).toBe(true)

        resolvePromise(mockCreatedCustomer)
        await createPromise

        expect(composable.loading.value).toBe(false)
      })

      it('应该在创建成功后刷新列表', async () => {
        mockService.createCustomer.mockResolvedValue(mockCreatedCustomer)
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.createCustomer(customerData)

        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该处理错误', async () => {
        const error = new Error('创建失败')
        mockService.createCustomer.mockRejectedValue(error)

        const result = await composable.createCustomer(customerData)

        expect(result).toBeNull()
        expect(composable.loading.value).toBe(false)
      })
    })

    describe('updateCustomer', () => {
      const updateData = {
        name: '张三更新',
        phone: '13800138001'
      }

      const mockUpdatedCustomer = {
        id: 1,
        ...updateData,
        level: 'A',
        updated_at: new Date()
      }

      it('应该成功更新客户', async () => {
        mockService.updateCustomer.mockResolvedValue(mockUpdatedCustomer)

        const result = await composable.updateCustomer(1, updateData)

        expect(mockService.updateCustomer).toHaveBeenCalledWith(1, updateData)
        expect(result).toEqual(mockUpdatedCustomer)
      })

      it('应该在更新成功后刷新列表', async () => {
        mockService.updateCustomer.mockResolvedValue(mockUpdatedCustomer)
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.updateCustomer(1, updateData)

        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该处理错误', async () => {
        const error = new Error('更新失败')
        mockService.updateCustomer.mockRejectedValue(error)

        const result = await composable.updateCustomer(1, updateData)

        expect(result).toBeNull()
      })
    })

    describe('deleteCustomer', () => {
      it('应该成功删除客户', async () => {
        mockService.deleteCustomer.mockResolvedValue(true)

        const result = await composable.deleteCustomer(1)

        expect(mockService.deleteCustomer).toHaveBeenCalledWith(1)
        expect(result).toBe(true)
      })

      it('应该在删除成功后刷新列表', async () => {
        mockService.deleteCustomer.mockResolvedValue(true)
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.deleteCustomer(1)

        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该处理错误', async () => {
        const error = new Error('删除失败')
        mockService.deleteCustomer.mockRejectedValue(error)

        const result = await composable.deleteCustomer(1)

        expect(result).toBe(false)
      })
    })
  })

  describe('统计数据', () => {
    describe('loadStats', () => {
      const mockStats = {
        customers: {
          total: 156,
          active: 128,
          new_this_month: 23,
          trend: 8.5
        },
        groups: {
          total: 12,
          active: 10,
          members: 1580,
          trend: 15.2
        },
        commission: {
          total: 45680.50,
          this_month: 8650.00,
          pending: 1250.00,
          trend: 23.4
        },
        orders: {
          total: 89,
          completed: 76,
          pending: 8,
          success_rate: 85.4
        }
      }

      it('应该成功加载统计数据', async () => {
        mockService.getDistributorStats.mockResolvedValue(mockStats)

        await composable.loadStats()

        expect(composable.stats.value).toEqual(mockStats)
        expect(mockService.getDistributorStats).toHaveBeenCalled()
      })

      it('应该处理加载状态', async () => {
        let resolvePromise
        const promise = new Promise(resolve => {
          resolvePromise = resolve
        })
        mockService.getDistributorStats.mockReturnValue(promise)

        const loadPromise = composable.loadStats()
        
        expect(composable.loading.value).toBe(true)

        resolvePromise(mockStats)
        await loadPromise

        expect(composable.loading.value).toBe(false)
      })

      it('应该处理错误', async () => {
        const error = new Error('加载统计失败')
        mockService.getDistributorStats.mockRejectedValue(error)

        await composable.loadStats()

        expect(composable.stats.value).toEqual({})
      })

      it('应该支持强制刷新', async () => {
        mockService.getDistributorStats.mockResolvedValue(mockStats)

        await composable.loadStats(true)

        expect(mockService.getDistributorStats).toHaveBeenCalledWith(false)
      })
    })
  })

  describe('过滤和搜索', () => {
    describe('setFilters', () => {
      it('应该正确设置过滤条件', async () => {
        const newFilters = {
          keyword: '张三',
          level: 'A',
          status: 'active'
        }

        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.setFilters(newFilters)

        expect(composable.filters.value.keyword).toBe('张三')
        expect(composable.filters.value.level).toBe('A')
        expect(composable.filters.value.status).toBe('active')
        expect(composable.pagination.value.current_page).toBe(1) // 重置页码
      })

      it('应该在设置过滤条件后重新加载数据', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.setFilters({ keyword: '张三' })

        expect(mockService.getCustomers).toHaveBeenCalled()
      })
    })

    describe('resetFilters', () => {
      it('应该重置所有过滤条件', async () => {
        // 先设置一些过滤条件
        composable.filters.value.keyword = '张三'
        composable.filters.value.level = 'A'
        composable.filters.value.status = 'active'
        composable.pagination.value.current_page = 3

        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.resetFilters()

        expect(composable.filters.value.keyword).toBe('')
        expect(composable.filters.value.level).toBe('')
        expect(composable.filters.value.status).toBe('')
        expect(composable.filters.value.date_range).toEqual([])
        expect(composable.pagination.value.current_page).toBe(1)
      })

      it('应该在重置后重新加载数据', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.resetFilters()

        expect(mockService.getCustomers).toHaveBeenCalled()
      })
    })

    describe('search', () => {
      it('应该执行搜索', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.search('张三')

        expect(composable.filters.value.keyword).toBe('张三')
        expect(composable.pagination.value.current_page).toBe(1)
        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该处理空搜索关键词', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.search('')

        expect(composable.filters.value.keyword).toBe('')
        expect(mockService.getCustomers).toHaveBeenCalled()
      })
    })
  })

  describe('分页', () => {
    describe('changePage', () => {
      it('应该正确切换页码', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.changePage(3)

        expect(composable.pagination.value.current_page).toBe(3)
        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该验证页码范围', async () => {
        composable.pagination.value.total_pages = 5
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        // 测试无效页码
        await composable.changePage(0)
        expect(composable.pagination.value.current_page).toBe(1)

        await composable.changePage(10)
        expect(composable.pagination.value.current_page).toBe(1)

        // 测试有效页码
        await composable.changePage(3)
        expect(composable.pagination.value.current_page).toBe(3)
      })
    })

    describe('changePageSize', () => {
      it('应该正确切换每页大小', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        await composable.changePageSize(50)

        expect(composable.pagination.value.per_page).toBe(50)
        expect(composable.pagination.value.current_page).toBe(1) // 重置页码
        expect(mockService.getCustomers).toHaveBeenCalled()
      })

      it('应该验证每页大小', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

        // 测试无效大小
        await composable.changePageSize(0)
        expect(composable.pagination.value.per_page).toBe(20) // 保持默认值

        await composable.changePageSize(1000)
        expect(composable.pagination.value.per_page).toBe(20) // 保持默认值

        // 测试有效大小
        await composable.changePageSize(50)
        expect(composable.pagination.value.per_page).toBe(50)
      })
    })
  })

  describe('缓存管理', () => {
    describe('refreshData', () => {
      it('应该清除缓存并重新加载数据', async () => {
        mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })
        mockService.getDistributorStats.mockResolvedValue({})

        await composable.refreshData()

        expect(mockService.clearCache).toHaveBeenCalled()
        expect(mockService.getCustomers).toHaveBeenCalledWith(
          expect.any(Object),
          false // useCache = false
        )
        expect(mockService.getDistributorStats).toHaveBeenCalledWith(false)
      })

      it('应该处理刷新错误', async () => {
        const error = new Error('刷新失败')
        mockService.getCustomers.mockRejectedValue(error)

        await composable.refreshData()

        expect(composable.loading.value).toBe(false)
      })
    })

    describe('clearCache', () => {
      it('应该清除指定类型的缓存', () => {
        composable.clearCache('customers')

        expect(mockService.clearCache).toHaveBeenCalledWith('customers')
      })

      it('应该清除所有缓存', () => {
        composable.clearCache()

        expect(mockService.clearCache).toHaveBeenCalledWith()
      })
    })
  })

  describe('工具方法', () => {
    describe('formatCustomerLevel', () => {
      it('应该正确格式化客户等级', () => {
        expect(composable.formatCustomerLevel('A')).toBe('A级客户')
        expect(composable.formatCustomerLevel('B')).toBe('B级客户')
        expect(composable.formatCustomerLevel('C')).toBe('C级客户')
        expect(composable.formatCustomerLevel('D')).toBe('D级客户')
        expect(composable.formatCustomerLevel('X')).toBe('C级客户')
      })
    })

    describe('formatCustomerStatus', () => {
      it('应该正确格式化客户状态', () => {
        expect(composable.formatCustomerStatus('active')).toBe('活跃')
        expect(composable.formatCustomerStatus('inactive')).toBe('不活跃')
        expect(composable.formatCustomerStatus('potential')).toBe('潜在')
        expect(composable.formatCustomerStatus('lost')).toBe('流失')
        expect(composable.formatCustomerStatus('unknown')).toBe('潜在')
      })
    })

    describe('formatMoney', () => {
      it('应该正确格式化金额', () => {
        expect(composable.formatMoney(1234.56)).toBe('1,234.56')
        expect(composable.formatMoney(1000000)).toBe('1,000,000.00')
        expect(composable.formatMoney(0)).toBe('0.00')
        expect(composable.formatMoney(null)).toBe('0.00')
        expect(composable.formatMoney(undefined)).toBe('0.00')
      })
    })

    describe('formatDate', () => {
      it('应该正确格式化日期', () => {
        const date = new Date('2024-01-15T10:30:00Z')
        expect(composable.formatDate(date)).toBe('2024-01-15')
        expect(composable.formatDate(date, 'datetime')).toBe('2024-01-15 10:30:00')
        expect(composable.formatDate(null)).toBe('')
        expect(composable.formatDate(undefined)).toBe('')
      })
    })
  })

  describe('响应式更新', () => {
    it('应该在数据变化时触发响应式更新', async () => {
      const mockCustomers = [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' }
      ]

      mockService.getCustomers.mockResolvedValue({
        data: mockCustomers,
        total: 2
      })

      await composable.loadCustomers()

      expect(composable.customers.value).toEqual(mockCustomers)
      expect(composable.pagination.value.total).toBe(2)
    })

    it('应该在过滤条件变化时重新加载', async () => {
      mockService.getCustomers.mockResolvedValue({ data: [], total: 0 })

      // 模拟过滤条件变化
      await composable.setFilters({ keyword: '张三' })
      await composable.setFilters({ level: 'A' })

      expect(mockService.getCustomers).toHaveBeenCalledTimes(2)
    })
  })

  describe('错误处理', () => {
    it('应该正确处理服务层错误', async () => {
      const error = new Error('服务错误')
      mockService.getCustomers.mockRejectedValue(error)

      await composable.loadCustomers()

      expect(composable.loading.value).toBe(false)
      expect(composable.customers.value).toEqual([])
    })

    it('应该在错误时保持数据完整性', async () => {
      // 先加载成功的数据
      const successData = [{ id: 1, name: '张三' }]
      mockService.getCustomers.mockResolvedValue({
        data: successData,
        total: 1
      })

      await composable.loadCustomers()
      expect(composable.customers.value).toEqual(successData)

      // 然后模拟错误
      const error = new Error('网络错误')
      mockService.getCustomers.mockRejectedValue(error)

      await composable.loadCustomers()

      // 数据应该被清空，但不会保留错误状态
      expect(composable.customers.value).toEqual([])
      expect(composable.loading.value).toBe(false)
    })
  })
})