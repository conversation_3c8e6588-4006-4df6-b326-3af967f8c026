import{_ as a,u as e,k as l,o as s,p as r,q as t}from"./index-DtXAftX0.js";/* empty css               *//* empty css                *//* empty css                     *//* empty css               */import{A as o}from"./AvatarUpload-Kj8d-M_w.js";import{a_ as u,aY as d,bp as n,bq as i,aM as p,a$ as c,U as m,at as v,aZ as _,Q as f}from"./element-plus-h2SQQM64.js";import{r as w,L as g,e as b,k as h,l as V,E as y,z as k,D as U,t as j}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                  *//* empty css                    */const q={class:"app-container"},x={class:"balance"},I={class:"stat-item"},z={class:"stat-value"},A={class:"stat-item"},C={class:"stat-value"},E={class:"stat-item"},F={class:"stat-value"},$={class:"stat-item"},B={class:"stat-value"},P={class:"login-info"},R=a({__name:"Profile",setup(a){const R=e(),D=w(),L=w(),M=w(!1),Q=w(!1),S=w({id:null,username:"",nickname:"",email:"",phone:"",avatar:"",role:"",balance:0,created_at:"",last_login_at:"",last_login_ip:""}),Y=w({current_password:"",password:"",password_confirmation:""}),Z=w({total_orders:0,total_amount:0,total_commission:0,children_count:0});w("/api/v1/upload"),w({Authorization:`Bearer ${l()}`});const G=g({nickname:[{required:!0,message:"昵称不能为空",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}]}),H=g({current_password:[{required:!0,message:"当前密码不能为空",trigger:"blur"}],password:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],password_confirmation:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{validator:(a,e,l)=>{e!==Y.value.password?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]}),J=async()=>{try{await D.value.validate(),M.value=!0;const{data:a}=await r({nickname:S.value.nickname,email:S.value.email,phone:S.value.phone,avatar:S.value.avatar});a.success&&(f.success("个人信息更新成功"),await R.getUserInfo())}catch(a){console.error("更新失败:",a)}finally{M.value=!1}},K=async()=>{try{await L.value.validate(),Q.value=!0;const{data:a}=await t({current_password:Y.value.current_password,password:Y.value.password,password_confirmation:Y.value.password_confirmation});a.success&&(f.success("密码修改成功"),N())}catch(a){console.error("密码修改失败:",a)}finally{Q.value=!1}},N=()=>{Y.value={current_password:"",password:"",password_confirmation:""},L.value?.resetFields()},O=a=>{S.value.avatar=a.url,R.userInfo&&(R.userInfo.avatar=a.url),f.success("头像上传成功!")},T=a=>{console.error("头像上传失败:",a),f.error("头像上传失败，请重试")},W=a=>({admin:"超级管理员",substation:"分站管理员",distributor:"分销商",user:"普通用户"}[a]);return b(()=>{(async()=>{try{const{data:a}=await s();a.success&&(S.value={...a.data.user},Z.value=a.data.stats||{})}catch(a){f.error("获取用户信息失败")}})()}),(a,e)=>{const l=i,s=p,r=c,t=v,f=n,w=d,g=u,b=_;return V(),h("div",q,[y(b,{gutter:20},{default:k(()=>[y(g,{span:12},{default:k(()=>[y(w,null,{header:k(()=>e[8]||(e[8]=[j("div",{class:"card-header"},[j("span",null,"个人信息")],-1)])),default:k(()=>[y(f,{ref_key:"profileFormRef",ref:D,model:S.value,rules:G,"label-width":"100px"},{default:k(()=>[y(l,{label:"头像"},{default:k(()=>[y(o,{modelValue:S.value.avatar,"onUpdate:modelValue":e[0]||(e[0]=a=>S.value.avatar=a),size:120,"max-size":5,"enable-preview":!0,onSuccess:O,onError:T},null,8,["modelValue"])]),_:1}),y(l,{label:"用户名",prop:"username"},{default:k(()=>[y(s,{modelValue:S.value.username,"onUpdate:modelValue":e[1]||(e[1]=a=>S.value.username=a),readonly:""},null,8,["modelValue"])]),_:1}),y(l,{label:"昵称",prop:"nickname"},{default:k(()=>[y(s,{modelValue:S.value.nickname,"onUpdate:modelValue":e[2]||(e[2]=a=>S.value.nickname=a),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),y(l,{label:"邮箱",prop:"email"},{default:k(()=>[y(s,{modelValue:S.value.email,"onUpdate:modelValue":e[3]||(e[3]=a=>S.value.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),y(l,{label:"手机号",prop:"phone"},{default:k(()=>[y(s,{modelValue:S.value.phone,"onUpdate:modelValue":e[4]||(e[4]=a=>S.value.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),y(l,{label:"角色"},{default:k(()=>{return[y(r,{type:(a=S.value.role,{admin:"danger",substation:"warning",distributor:"success",user:"info"}[a])},{default:k(()=>[U(m(W(S.value.role)),1)]),_:1},8,["type"])];var a}),_:1}),y(l,{label:"余额"},{default:k(()=>[j("span",x,m(S.value.balance)+" 元",1)]),_:1}),y(l,{label:"注册时间"},{default:k(()=>[j("span",null,m(S.value.created_at),1)]),_:1}),y(l,null,{default:k(()=>[y(t,{type:"primary",onClick:J,loading:M.value},{default:k(()=>e[9]||(e[9]=[U("更新信息",-1)])),_:1,__:[9]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1}),y(g,{span:12},{default:k(()=>[y(w,null,{header:k(()=>e[10]||(e[10]=[j("div",{class:"card-header"},[j("span",null,"修改密码")],-1)])),default:k(()=>[y(f,{ref_key:"passwordFormRef",ref:L,model:Y.value,rules:H,"label-width":"100px"},{default:k(()=>[y(l,{label:"当前密码",prop:"current_password"},{default:k(()=>[y(s,{modelValue:Y.value.current_password,"onUpdate:modelValue":e[5]||(e[5]=a=>Y.value.current_password=a),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,{label:"新密码",prop:"password"},{default:k(()=>[y(s,{modelValue:Y.value.password,"onUpdate:modelValue":e[6]||(e[6]=a=>Y.value.password=a),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,{label:"确认密码",prop:"password_confirmation"},{default:k(()=>[y(s,{modelValue:Y.value.password_confirmation,"onUpdate:modelValue":e[7]||(e[7]=a=>Y.value.password_confirmation=a),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,null,{default:k(()=>[y(t,{type:"primary",onClick:K,loading:Q.value},{default:k(()=>e[11]||(e[11]=[U("修改密码",-1)])),_:1,__:[11]},8,["loading"]),y(t,{onClick:N},{default:k(()=>e[12]||(e[12]=[U("重置",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1}),y(b,{gutter:20,class:"mt-4"},{default:k(()=>[y(g,{span:24},{default:k(()=>[y(w,null,{header:k(()=>e[13]||(e[13]=[j("div",{class:"card-header"},[j("span",null,"账户统计")],-1)])),default:k(()=>[y(b,{gutter:20,class:"stats-row"},{default:k(()=>[y(g,{span:6},{default:k(()=>[j("div",I,[j("div",z,m(Z.value.total_orders||0),1),e[14]||(e[14]=j("div",{class:"stat-label"},"总订单数",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[j("div",A,[j("div",C,m(Z.value.total_amount||0)+" 元",1),e[15]||(e[15]=j("div",{class:"stat-label"},"总消费金额",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[j("div",E,[j("div",F,m(Z.value.total_commission||0)+" 元",1),e[16]||(e[16]=j("div",{class:"stat-label"},"总佣金收入",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[j("div",$,[j("div",B,m(Z.value.children_count||0),1),e[17]||(e[17]=j("div",{class:"stat-label"},"下级用户数",-1))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),y(b,{class:"mt-4"},{default:k(()=>[y(g,{span:24},{default:k(()=>[y(w,null,{header:k(()=>e[18]||(e[18]=[j("div",{class:"card-header"},[j("span",null,"登录记录")],-1)])),default:k(()=>[j("div",P,[j("p",null,[e[19]||(e[19]=j("strong",null,"最后登录时间：",-1)),U(m(S.value.last_login_at||"从未登录"),1)]),j("p",null,[e[20]||(e[20]=j("strong",null,"最后登录IP：",-1)),U(m(S.value.last_login_ip||"未知"),1)])])]),_:1})]),_:1})]),_:1})])}}},[["__scopeId","data-v-a628de5a"]]);export{R as default};
