import api from './index'

// 分销管理相关API
export const getDistributors = (params) => {
  return api.get('/admin/distributors', { params })
}

export const getDistributorStats = () => {
  return api.get('/admin/distributors/stats')
}

export const addDistributor = (data) => {
  return api.post('/admin/distributors', data)
}

export const updateDistributor = (id, data) => {
  return api.put(`/admin/distributors/${id}`, data)
}

export const deleteDistributor = (id) => {
  return api.delete(`/admin/distributors/${id}`)
}

export const getDistributionGroups = () => {
  return api.get('/admin/distribution-groups')
}

export const updateDistributorLevel = (id, data) => {
  return api.put(`/admin/distributors/${id}/level`, data)
}

export const updateDistributorGroup = (id, data) => {
  return api.put(`/admin/distributors/${id}/group`, data)
}

export const updateDistributorStatus = (id, data) => {
  return api.put(`/admin/distributors/${id}/status`, data)
}

export const getDistributorDetail = (id) => {
  return api.get(`/admin/distributors/${id}`)
}

export const getDistributorStatistics = (id) => {
  return api.get(`/admin/distributors/${id}/statistics`)
}

// 分销组管理
export const getDistributionGroupList = (params) => {
  return api.get('/admin/distribution-groups', { params })
}

export const createDistributionGroup = (data) => {
  return api.post('/admin/distribution-groups', data)
}

export const updateDistributionGroup = (id, data) => {
  return api.put(`/admin/distribution-groups/${id}`, data)
}

export const deleteDistributionGroup = (id) => {
  return api.delete(`/admin/distribution-groups/${id}`)
}

export const getDistributionGroupStats = (id) => {
  return api.get(`/admin/distribution-groups/${id}/stats`)
}