<template>
  <div class="enhanced-global-search">
    <!-- 搜索触发器 -->
    <div class="search-trigger" @click="openSearchDialog">
      <el-icon class="search-icon"><Search /></el-icon>
      <span class="search-placeholder">搜索功能、用户、订单...</span>
      <kbd class="search-shortcut">{{ shortcuts.globalSearch }}</kbd>
    </div>

    <!-- 增强搜索对话框 -->
    <el-dialog
      v-model="showSearchDialog"
      :title="searchDialogTitle"
      width="600px"
      :show-close="false"
      :close-on-click-modal="true"
      class="enhanced-search-dialog"
      @opened="handleDialogOpened"
      @closed="handleDialogClosed"
    >
      <div class="search-content">
        <!-- 搜索输入框 -->
        <div class="search-input-container">
          <el-input
            ref="searchInputRef"
            v-model="searchQuery"
            placeholder="输入关键词搜索功能、用户、订单..."
            prefix-icon="Search"
            size="large"
            clearable
            @input="handleSearchInput"
            @keydown="handleSearchKeydown"
            @clear="handleSearchClear"
            class="search-input"
          />
          <div class="search-tips">
            <span class="tip-item">
              <kbd>↑↓</kbd> 选择
            </span>
            <span class="tip-item">
              <kbd>Enter</kbd> 确认
            </span>
            <span class="tip-item">
              <kbd>Esc</kbd> 关闭
            </span>
          </div>
        </div>

        <!-- 搜索结果区域 -->
        <div class="search-results-container">
          <!-- 搜索建议（无输入时显示） -->
          <div v-if="!searchQuery && searchSuggestions.length > 0" class="search-suggestions">
            <div class="suggestions-header">
              <el-icon><Star /></el-icon>
              <span>推荐功能</span>
            </div>
            <div class="suggestions-list">
              <div
                v-for="(suggestion, index) in searchSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                :class="{ active: selectedResultIndex === index }"
                @click="selectSearchResult(suggestion)"
                @mouseenter="selectedResultIndex = index"
              >
                <div class="suggestion-icon">
                  <el-icon><component :is="getSearchIcon(suggestion.icon)" /></el-icon>
                </div>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                </div>
                <div class="suggestion-badge" v-if="suggestion.protected">
                  <el-tag size="small" type="success">核心功能</el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 最近访问（无输入时显示） -->
          <div v-if="!searchQuery && recentVisits.length > 0" class="recent-visits">
            <div class="recent-header">
              <el-icon><Clock /></el-icon>
              <span>最近访问</span>
              <el-button text size="small" @click="clearRecentVisits">清空</el-button>
            </div>
            <div class="recent-list">
              <div
                v-for="(visit, index) in recentVisits.slice(0, 5)"
                :key="visit.id"
                class="recent-item"
                :class="{ active: selectedResultIndex === searchSuggestions.length + index }"
                @click="selectSearchResult(visit)"
                @mouseenter="selectedResultIndex = searchSuggestions.length + index"
              >
                <div class="recent-icon">
                  <el-icon><component :is="getSearchIcon(visit.icon)" /></el-icon>
                </div>
                <div class="recent-content">
                  <div class="recent-title">{{ visit.title }}</div>
                  <div class="recent-time">{{ formatVisitTime(visit.visitTime) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索结果 -->
          <div v-if="searchQuery && searchResults.length > 0" class="search-results">
            <!-- 分类显示搜索结果 -->
            <div
              v-for="category in categorizedResults"
              :key="category.key"
              class="result-category"
              v-show="category.results.length > 0"
            >
              <div class="category-header">
                <el-icon><component :is="getSearchIcon(category.icon)" /></el-icon>
                <span>{{ category.title }}</span>
                <el-badge :value="category.results.length" class="category-count" />
              </div>
              <div class="category-results">
                <div
                  v-for="(result, index) in category.results.slice(0, 5)"
                  :key="result.id"
                  class="search-result-item"
                  :class="{ active: selectedResultIndex === getResultGlobalIndex(category.key, index) }"
                  @click="selectSearchResult(result)"
                  @mouseenter="selectedResultIndex = getResultGlobalIndex(category.key, index)"
                >
                  <div class="result-icon">
                    <el-icon><component :is="getSearchIcon(result.icon)" /></el-icon>
                  </div>
                  <div class="result-content">
                    <div class="result-title" v-html="highlightSearchTerm(result.title)"></div>
                    <div class="result-path">{{ result.group || result.path }}</div>
                    <div class="result-desc" v-if="result.description" v-html="highlightSearchTerm(result.description)"></div>
                  </div>
                  <div class="result-badges">
                    <el-tag v-if="result.protected" size="small" type="success">核心</el-tag>
                    <el-tag v-if="result.recent" size="small" type="warning">最近</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 无搜索结果 -->
          <div v-if="searchQuery && searchResults.length === 0" class="search-empty">
            <div class="empty-icon">
              <el-icon><Search /></el-icon>
            </div>
            <div class="empty-text">
              <div class="empty-title">未找到相关结果</div>
              <div class="empty-desc">尝试使用其他关键词或检查拼写</div>
            </div>
            <div class="empty-suggestions">
              <span>建议搜索：</span>
              <el-button
                v-for="suggestion in quickSearchTerms"
                :key="suggestion"
                text
                size="small"
                @click="searchQuery = suggestion; handleSearchInput(suggestion)"
              >
                {{ suggestion }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 搜索统计 -->
        <div v-if="searchQuery" class="search-stats">
          找到 {{ searchResults.length }} 个结果，用时 {{ searchTime }}ms
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Search, Star, Clock, Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
  OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick,
  Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
  InfoFilled, CreditCard, Folder, View, Bell, Plus, Lightning, Management, Promotion,
  ShoppingCart, RefreshLeft
} from '@element-plus/icons-vue'
import { 
  initializeEnhancedSearchData, 
  getUserQuickActions, 
  enhancedSearchConfig 
} from '@/config/optimizedNavigationV2'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showSearchDialog = ref(false)
const searchQuery = ref('')
const searchResults = ref([])
const selectedResultIndex = ref(0)
const searchInputRef = ref(null)
const recentVisits = ref([])
const searchTime = ref(0)
const searchData = ref([])

// 计算属性
const shortcuts = computed(() => enhancedSearchConfig.shortcuts)

const searchDialogTitle = computed(() => {
  if (searchQuery.value) {
    return `搜索 "${searchQuery.value}"`
  }
  return '全局搜索'
})

// 搜索建议（基于角色的推荐）
const searchSuggestions = computed(() => {
  const userRole = userStore.userInfo?.role
  const quickActions = getUserQuickActions(userRole)
  
  // 将快速操作转换为搜索建议格式
  return quickActions.map(action => ({
    id: `suggestion-${action.path}`,
    title: action.title,
    description: `快速${action.title}`,
    icon: action.icon,
    path: action.path,
    type: 'suggestion',
    category: 'menus',
    protected: action.path === '/community/add' // 标记群组创建为核心功能
  }))
})

// 分类搜索结果
const categorizedResults = computed(() => {
  const categories = enhancedSearchConfig.categories.map(cat => ({
    ...cat,
    results: searchResults.value.filter(result => result.category === cat.key)
  }))
  
  // 按权重排序分类
  return categories
    .filter(cat => cat.results.length > 0)
    .sort((a, b) => b.weight - a.weight)
})

// 快速搜索建议词
const quickSearchTerms = computed(() => {
  const userRole = userStore.userInfo?.role
  const baseTerms = ['群组', '用户', '订单', '数据']
  
  switch (userRole) {
    case 'admin':
      return [...baseTerms, '系统', '监控', '权限']
    case 'substation':
      return [...baseTerms, '分站', '代理商', '财务']
    case 'agent':
      return [...baseTerms, '团队', '佣金', '绩效']
    case 'distributor':
      return [...baseTerms, '客户', '推广', '佣金']
    case 'group_owner':
      return [...baseTerms, '群组', '内容', '成员']
    default:
      return baseTerms
  }
})

// 方法
const openSearchDialog = () => {
  showSearchDialog.value = true
}

const handleDialogOpened = async () => {
  await nextTick()
  searchInputRef.value?.focus()
  selectedResultIndex.value = 0
}

const handleDialogClosed = () => {
  searchQuery.value = ''
  searchResults.value = []
  selectedResultIndex.value = 0
}

const handleSearchInput = (value) => {
  const startTime = performance.now()
  
  if (!value || value.trim() === '') {
    searchResults.value = []
    selectedResultIndex.value = 0
    return
  }
  
  // 执行搜索
  searchResults.value = performEnhancedSearch(value.trim())
  selectedResultIndex.value = 0
  
  const endTime = performance.now()
  searchTime.value = Math.round(endTime - startTime)
}

const handleSearchClear = () => {
  searchResults.value = []
  selectedResultIndex.value = 0
}

const handleSearchKeydown = (event) => {
  const totalResults = getTotalResultsCount()
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedResultIndex.value = Math.min(selectedResultIndex.value + 1, totalResults - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedResultIndex.value = Math.max(selectedResultIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      const selectedResult = getSelectedResult()
      if (selectedResult) {
        selectSearchResult(selectedResult)
      }
      break
    case 'Escape':
      showSearchDialog.value = false
      break
  }
}

const selectSearchResult = (result) => {
  if (!result.path) return
  
  // 添加到最近访问
  addToRecentVisits(result)
  
  // 跳转到目标页面
  router.push(result.path)
  
  // 关闭搜索对话框
  showSearchDialog.value = false
  searchQuery.value = ''
  searchResults.value = []
  
  ElMessage.success(`正在跳转到 ${result.title}`)
}

// 执行增强搜索
const performEnhancedSearch = (query) => {
  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0)
  const results = []
  
  searchData.value.forEach(item => {
    let score = 0
    let matchedTerms = 0
    
    searchTerms.forEach(term => {
      // 标题匹配
      if (item.title.toLowerCase().includes(term)) {
        score += enhancedSearchConfig.searchWeights.title
        matchedTerms++
      }
      
      // 描述匹配
      if (item.description && item.description.toLowerCase().includes(term)) {
        score += enhancedSearchConfig.searchWeights.description
        matchedTerms++
      }
      
      // 关键词匹配
      if (item.keywords && item.keywords.some(keyword => keyword.toLowerCase().includes(term))) {
        score += enhancedSearchConfig.searchWeights.keywords
        matchedTerms++
      }
      
      // 路径匹配
      if (item.path && item.path.toLowerCase().includes(term)) {
        score += enhancedSearchConfig.searchWeights.path
        matchedTerms++
      }
    })
    
    // 只有匹配到搜索词的项目才加入结果
    if (matchedTerms > 0) {
      // 最近访问加权
      if (isRecentVisit(item)) {
        score += enhancedSearchConfig.searchWeights.recent
        item.recent = true
      }
      
      // 核心功能加权
      if (item.protected) {
        score += 5
      }
      
      results.push({
        ...item,
        score,
        matchedTerms
      })
    }
  })
  
  // 按分数排序，分数相同时按匹配词数排序
  return results
    .sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score
      }
      return b.matchedTerms - a.matchedTerms
    })
    .slice(0, 50) // 限制结果数量
}

// 获取搜索图标组件
const getSearchIcon = (iconName) => {
  const iconMap = {
    Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
    OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick,
    Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
    InfoFilled, CreditCard, Folder, View, Bell, Plus, Lightning, Management, Promotion,
    ShoppingCart, RefreshLeft, Menu: Grid
  }
  return iconMap[iconName] || Document
}

// 高亮搜索词
const highlightSearchTerm = (text) => {
  if (!searchQuery.value || !text) return text
  
  const searchTerms = searchQuery.value.toLowerCase().split(' ').filter(term => term.length > 0)
  let highlightedText = text
  
  searchTerms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi')
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
  })
  
  return highlightedText
}

// 获取结果的全局索引
const getResultGlobalIndex = (categoryKey, categoryIndex) => {
  let globalIndex = 0
  
  for (const category of categorizedResults.value) {
    if (category.key === categoryKey) {
      return globalIndex + categoryIndex
    }
    globalIndex += Math.min(category.results.length, 5)
  }
  
  return globalIndex
}

// 获取总结果数量
const getTotalResultsCount = () => {
  if (!searchQuery.value) {
    return searchSuggestions.value.length + recentVisits.value.slice(0, 5).length
  }
  
  return categorizedResults.value.reduce((total, category) => {
    return total + Math.min(category.results.length, 5)
  }, 0)
}

// 获取当前选中的结果
const getSelectedResult = () => {
  if (!searchQuery.value) {
    const totalSuggestions = searchSuggestions.value.length
    if (selectedResultIndex.value < totalSuggestions) {
      return searchSuggestions.value[selectedResultIndex.value]
    } else {
      const recentIndex = selectedResultIndex.value - totalSuggestions
      return recentVisits.value[recentIndex]
    }
  }
  
  let currentIndex = 0
  for (const category of categorizedResults.value) {
    const categorySize = Math.min(category.results.length, 5)
    if (selectedResultIndex.value < currentIndex + categorySize) {
      return category.results[selectedResultIndex.value - currentIndex]
    }
    currentIndex += categorySize
  }
  
  return null
}

// 最近访问管理
const addToRecentVisits = (item) => {
  const visit = {
    ...item,
    visitTime: Date.now()
  }
  
  // 移除已存在的相同项目
  const existingIndex = recentVisits.value.findIndex(v => v.path === item.path)
  if (existingIndex > -1) {
    recentVisits.value.splice(existingIndex, 1)
  }
  
  // 添加到开头
  recentVisits.value.unshift(visit)
  
  // 限制数量
  if (recentVisits.value.length > 10) {
    recentVisits.value = recentVisits.value.slice(0, 10)
  }
  
  // 保存到本地存储
  localStorage.setItem('recent-visits', JSON.stringify(recentVisits.value))
}

const isRecentVisit = (item) => {
  return recentVisits.value.some(visit => visit.path === item.path)
}

const clearRecentVisits = () => {
  recentVisits.value = []
  localStorage.removeItem('recent-visits')
  ElMessage.success('已清空最近访问记录')
}

const formatVisitTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

// 全局快捷键处理
const handleGlobalKeydown = (event) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    openSearchDialog()
  }
  
  // / 键快速聚焦搜索（当没有输入框聚焦时）
  if (event.key === '/' && !event.target.matches('input, textarea')) {
    event.preventDefault()
    openSearchDialog()
  }
}

// 初始化
const initializeSearch = () => {
  const userRole = userStore.userInfo?.role
  if (userRole) {
    searchData.value = initializeEnhancedSearchData(userRole)
  }
  
  // 加载最近访问记录
  const savedVisits = localStorage.getItem('recent-visits')
  if (savedVisits) {
    try {
      recentVisits.value = JSON.parse(savedVisits)
    } catch (error) {
      console.warn('Failed to load recent visits:', error)
    }
  }
}

// 监听用户信息变化
watch(() => userStore.userInfo, (newUserInfo) => {
  if (newUserInfo?.role) {
    initializeSearch()
  }
}, { immediate: true })

onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
  initializeSearch()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})
</script>

<style lang="scss" scoped>
.enhanced-global-search {
  .search-trigger {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 300px;

    &:hover {
      border-color: #3b82f6;
      background: rgba(255, 255, 255, 0.9);
    }

    .search-icon {
      color: #64748b;
      margin-right: 8px;
    }

    .search-placeholder {
      flex: 1;
      color: #64748b;
      font-size: 14px;
    }

    .search-shortcut {
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 11px;
      color: #64748b;
    }
  }
}

:deep(.enhanced-search-dialog) {
  .el-dialog {
    border-radius: 16px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.search-content {
  .search-input-container {
    padding: 20px;
    border-bottom: 1px solid #f1f5f9;

    .search-input {
      margin-bottom: 12px;

      :deep(.el-input__wrapper) {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .search-tips {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #64748b;

      .tip-item {
        display: flex;
        align-items: center;
        gap: 4px;

        kbd {
          background: #f1f5f9;
          border: 1px solid #e2e8f0;
          border-radius: 3px;
          padding: 1px 4px;
          font-size: 10px;
        }
      }
    }
  }

  .search-results-container {
    max-height: 400px;
    overflow-y: auto;
  }

  // 搜索建议样式
  .search-suggestions {
    padding: 16px 20px;

    .suggestions-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #1f2937;
    }

    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover,
      &.active {
        background: #f8fafc;
      }

      .suggestion-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 8px;
        color: white;
        margin-right: 12px;
      }

      .suggestion-content {
        flex: 1;

        .suggestion-title {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .suggestion-desc {
          font-size: 12px;
          color: #64748b;
        }
      }

      .suggestion-badge {
        margin-left: 8px;
      }
    }
  }

  // 最近访问样式
  .recent-visits {
    padding: 16px 20px;
    border-top: 1px solid #f1f5f9;

    .recent-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #1f2937;

      .el-button {
        margin-left: auto;
      }
    }

    .recent-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover,
      &.active {
        background: #f8fafc;
      }

      .recent-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        margin-right: 12px;
      }

      .recent-content {
        flex: 1;

        .recent-title {
          font-size: 14px;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .recent-time {
          font-size: 11px;
          color: #9ca3af;
        }
      }
    }
  }

  // 搜索结果样式
  .search-results {
    .result-category {
      border-bottom: 1px solid #f1f5f9;

      &:last-child {
        border-bottom: none;
      }

      .category-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: #f8fafc;
        font-weight: 600;
        color: #374151;
        font-size: 13px;

        .category-count {
          margin-left: auto;
        }
      }

      .search-result-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover,
        &.active {
          background: #f1f5f9;
        }

        .result-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #e2e8f0;
          border-radius: 6px;
          color: #64748b;
          margin-right: 12px;
        }

        .result-content {
          flex: 1;

          .result-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;

            :deep(mark) {
              background: #fef3c7;
              color: #92400e;
              padding: 1px 2px;
              border-radius: 2px;
            }
          }

          .result-path {
            font-size: 11px;
            color: #9ca3af;
            margin-bottom: 2px;
          }

          .result-desc {
            font-size: 12px;
            color: #64748b;

            :deep(mark) {
              background: #fef3c7;
              color: #92400e;
              padding: 1px 2px;
              border-radius: 2px;
            }
          }
        }

        .result-badges {
          display: flex;
          gap: 4px;
        }
      }
    }
  }

  // 空状态样式
  .search-empty {
    padding: 40px 20px;
    text-align: center;

    .empty-icon {
      font-size: 48px;
      color: #d1d5db;
      margin-bottom: 16px;
    }

    .empty-text {
      margin-bottom: 20px;

      .empty-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 4px;
      }

      .empty-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .empty-suggestions {
      font-size: 12px;
      color: #6b7280;

      .el-button {
        margin: 0 4px;
      }
    }
  }

  // 搜索统计
  .search-stats {
    padding: 8px 20px;
    background: #f9fafb;
    border-top: 1px solid #f1f5f9;
    font-size: 11px;
    color: #6b7280;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enhanced-global-search {
    .search-trigger {
      width: 200px;

      .search-placeholder {
        display: none;
      }
    }
  }

  :deep(.enhanced-search-dialog) {
    .el-dialog {
      width: 90vw !important;
      margin: 5vh auto;
    }
  }
}
</style>