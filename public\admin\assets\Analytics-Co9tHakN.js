import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                       */import{f as a,n as t,o as s}from"./anti-block-CmiVNzQG.js";import{by as l,at as i,a_ as n,U as o,o as c,aZ as d,aY as r,bm as m,bn as u,bc as _,p,bh as h,bi as g,a$ as v,bw as b,ay as f,bp as y,aU as w,bq as S,bs as k,br as D,aM as V}from"./element-plus-h2SQQM64.js";import{k as C,l as x,t as M,E as T,z as A,D as j,F as U,Y as R,A as I,y as H}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const $={class:"analytics-page"},N={class:"page-header"},P={class:"page-actions"},z={class:"metrics-overview"},q={class:"metric-card"},L={class:"metric-content"},W={class:"metric-number"},F={class:"metric-trend"},O={class:"metric-card"},Q={class:"metric-content"},Y={class:"metric-number"},Z={class:"metric-trend"},B={class:"metric-card"},E={class:"metric-content"},G={class:"metric-number"},J={class:"metric-trend"},K={class:"metric-card"},X={class:"metric-content"},ee={class:"metric-number"},ae={class:"metric-trend"},te={class:"charts-section"},se={slot:"header",class:"card-header"},le={class:"chart-container"},ie={key:0,class:"chart-placeholder"},ne={key:1,class:"trend-chart"},oe={class:"chart-demo"},ce={slot:"header",class:"card-header"},de={class:"chart-container"},re={class:"domain-stats"},me={class:"domain-info"},ue={class:"domain-name"},_e={class:"domain-type"},pe={class:"domain-metrics"},he={class:"metric-item"},ge={class:"metric-value"},ve={class:"metric-item"},be={class:"metric-value"},fe={class:"metric-item"},ye={class:"details-section"},we={class:"stats-content"},Se={class:"stat-info"},ke={class:"stat-name"},De={class:"stat-percent"},Ve={class:"stat-bar"},Ce={class:"stat-count"},xe={class:"stats-content"},Me={class:"stat-info"},Te={class:"stat-name"},Ae={class:"stat-percent"},je={class:"stat-bar"},Ue={class:"stat-count"},Re={class:"stats-content"},Ie={class:"time-stats"},He={class:"time-label"},$e={class:"time-bar"},Ne={class:"time-count"},Pe={class:"alerts-section"},ze={slot:"header",class:"card-header"},qe={class:"alerts-content"},Le={class:"alert-summary"},We={class:"summary-item"},Fe={class:"summary-content"},Oe={class:"summary-number"},Qe={class:"summary-item"},Ye={class:"summary-content"},Ze={class:"summary-number"},Be={class:"summary-item"},Ee={class:"summary-content"},Ge={class:"summary-number"},Je={class:"alert-list"},Ke={class:"alert-icon"},Xe={class:"alert-content"},ea={class:"alert-title"},aa={class:"alert-desc"},ta={class:"alert-time"},sa={class:"alert-actions"},la={class:"domain-detail-content"},ia={"slot-scope":"scope"},na={class:"domain-text"},oa={"slot-scope":"scope"},ca={"slot-scope":"scope"},da={"slot-scope":"scope"},ra={"slot-scope":"scope"},ma={class:"alert-settings-content"},ua={slot:"footer",class:"dialog-footer"};const _a=e({name:"AntiBlockAnalytics",data:()=>({loading:!1,domainDetailVisible:!1,domainDetailLoading:!1,dateRange:[],trendPeriod:"7d",analytics:{total_clicks:0,unique_visitors:0,success_rate:0,avg_response_time:0,click_trend:0,visitor_trend:0,success_trend:0,response_trend:0},trendData:[],domainStats:[],domainDetailStats:[],sourceStats:[{name:"微信",count:1250,percentage:45},{name:"QQ",count:890,percentage:32},{name:"直接访问",count:456,percentage:16},{name:"其他",count:189,percentage:7}],regionStats:[{name:"广东",count:890,percentage:32},{name:"浙江",count:678,percentage:24},{name:"江苏",count:456,percentage:16},{name:"上海",count:234,percentage:8},{name:"其他",count:527,percentage:20}],hourlyStats:[],alerts:{warning_count:3,error_count:1,blocked_count:2},recentAlerts:[{id:1,type:"warning",title:"域名响应时间过长",description:"short1.example.com 响应时间超过5秒",created_at:new Date},{id:2,type:"error",title:"域名访问失败",description:"short2.example.com 返回404错误",created_at:new Date},{id:3,type:"blocked",title:"域名被封禁",description:"short3.example.com 被微信封禁",created_at:new Date}],alertSettingsVisible:!1,alertSettings:{domain_check_enabled:!0,domain_check_interval:5,block_threshold:3,response_time_threshold:5e3,success_rate_threshold:90,notification_enabled:!0,notification_email:"<EMAIL>",notification_webhook:"",auto_switch_enabled:!0,auto_switch_threshold:3}}),computed:{maxHourlyCount(){return Math.max(...this.hourlyStats.map(e=>e.count))}},mounted(){this.initDateRange(),this.loadAnalytics(),this.initHourlyStats()},methods:{initDateRange(){const e=new Date,a=new Date;a.setDate(e.getDate()-7),this.dateRange=[a.toISOString().split("T")[0],e.toISOString().split("T")[0]]},initHourlyStats(){this.hourlyStats=[];for(let e=0;e<24;e++)this.hourlyStats.push({hour:e,count:Math.floor(100*Math.random())+10})},async loadAnalytics(){this.loading=!0;try{const e={start_date:this.dateRange[0],end_date:this.dateRange[1]},{data:a}=await s(e);this.analytics=a,await this.loadDomainStats(),await this.loadTrendData()}catch(e){this.$message.error("加载统计数据失败")}finally{this.loading=!1}},async loadDomainStats(){try{const{data:e}=await a({per_page:10});this.domainStats=e.data.map(e=>({...e,usage_count:Math.floor(1e3*Math.random())+100,success_rate:Math.floor(20*Math.random())+80}))}catch(e){console.error("加载域名统计失败")}},async loadTrendData(){try{const e={period:this.trendPeriod,start_date:this.dateRange[0],end_date:this.dateRange[1]},{data:a}=await t(e);this.trendData=a.data||[]}catch(e){console.error("加载趋势数据失败")}},showDomainDetail(){this.domainDetailVisible=!0,this.loadDomainDetailStats()},async loadDomainDetailStats(){this.domainDetailLoading=!0;try{const{data:e}=await a({per_page:50});this.domainDetailStats=e.data.map(e=>({...e,usage_count:Math.floor(1e3*Math.random())+100,success_count:Math.floor(800*Math.random())+80,success_rate:Math.floor(20*Math.random())+80,avg_response_time:Math.floor(2e3*Math.random())+200}))}catch(e){this.$message.error("加载域名详情失败")}finally{this.domainDetailLoading=!1}},refreshData(){this.loadAnalytics()},exportReport(){this.$message.success("报告导出中...")},showAlertSettings(){this.alertSettingsVisible=!0,this.loadAlertSettings()},loadAlertSettings(){this.alertSettings={domain_check_enabled:!0,domain_check_interval:5,block_threshold:3,response_time_threshold:5e3,success_rate_threshold:90,notification_enabled:!0,notification_email:"<EMAIL>",notification_webhook:"",auto_switch_enabled:!0,auto_switch_threshold:3}},saveAlertSettings(){this.$message.loading("正在保存设置..."),setTimeout(()=>{this.$message.success("告警设置保存成功"),this.alertSettingsVisible=!1},1e3)},handleAlert(e){this.$message.success("告警处理成功")},formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"W":e.toString(),getTrendClass:e=>e>0?"trend-up":e<0?"trend-down":"trend-stable",getTrendIcon:e=>e>0?"el-icon-top":e<0?"el-icon-bottom":"el-icon-minus",getDomainTypeName:e=>({redirect:"短链接",landing:"中转页",api:"API服务"}[e]||e),getDomainTypeColor:e=>({redirect:"primary",landing:"success",api:"warning"}[e]||""),getHealthColor:e=>e>=90?"#67c23a":e>=80?"#409eff":e>=60?"#e6a23c":"#f56c6c",getAlertIcon:e=>({warning:"el-icon-warning",error:"el-icon-error",blocked:"el-icon-remove-outline"}[e]||"el-icon-info"),formatTime(e){return e?this.$dayjs(e).format("MM-DD HH:mm"):"-"}}},[["render",function(e,a,t,s,_a,pa){const ha=l,ga=i,va=n,ba=d,fa=u,ya=m,wa=r,Sa=_,ka=g,Da=v,Va=h,Ca=f,xa=w,Ma=k,Ta=S,Aa=D,ja=V,Ua=y,Ra=b;return x(),C("div",$,[M("div",N,[a[15]||(a[15]=M("div",{class:"page-title"},[M("h1",null,"📊 统计分析"),M("p",{class:"page-desc"},"防红系统的访问统计、域名分析和性能监控")],-1)),M("div",P,[T(ha,{modelValue:_a.dateRange,"onUpdate:modelValue":a[0]||(a[0]=e=>_a.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",onChange:pa.loadAnalytics},null,8,["modelValue","onChange"]),T(ga,{type:"primary",onClick:pa.exportReport},{default:A(()=>a[13]||(a[13]=[M("i",{class:"el-icon-download"},null,-1),j(" 导出报告 ",-1)])),_:1,__:[13]},8,["onClick"]),T(ga,{type:"success",onClick:pa.refreshData},{default:A(()=>a[14]||(a[14]=[M("i",{class:"el-icon-refresh"},null,-1),j(" 刷新 ",-1)])),_:1,__:[14]},8,["onClick"])])]),M("div",z,[T(ba,{gutter:20},{default:A(()=>[T(va,{span:6},{default:A(()=>[M("div",q,[a[18]||(a[18]=M("div",{class:"metric-icon total-clicks"},[M("i",{class:"el-icon-view"})],-1)),M("div",L,[M("div",W,o(pa.formatNumber(_a.analytics.total_clicks)),1),a[17]||(a[17]=M("div",{class:"metric-label"},"总访问量",-1)),M("div",F,[M("span",{class:c(pa.getTrendClass(_a.analytics.click_trend))},[M("i",{class:c(pa.getTrendIcon(_a.analytics.click_trend))},null,2),j(" "+o(Math.abs(_a.analytics.click_trend))+"% ",1)],2),a[16]||(a[16]=M("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),T(va,{span:6},{default:A(()=>[M("div",O,[a[21]||(a[21]=M("div",{class:"metric-icon unique-visitors"},[M("i",{class:"el-icon-user"})],-1)),M("div",Q,[M("div",Y,o(pa.formatNumber(_a.analytics.unique_visitors)),1),a[20]||(a[20]=M("div",{class:"metric-label"},"独立访客",-1)),M("div",Z,[M("span",{class:c(pa.getTrendClass(_a.analytics.visitor_trend))},[M("i",{class:c(pa.getTrendIcon(_a.analytics.visitor_trend))},null,2),j(" "+o(Math.abs(_a.analytics.visitor_trend))+"% ",1)],2),a[19]||(a[19]=M("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),T(va,{span:6},{default:A(()=>[M("div",B,[a[24]||(a[24]=M("div",{class:"metric-icon success-rate"},[M("i",{class:"el-icon-success"})],-1)),M("div",E,[M("div",G,o(_a.analytics.success_rate)+"%",1),a[23]||(a[23]=M("div",{class:"metric-label"},"访问成功率",-1)),M("div",J,[M("span",{class:c(pa.getTrendClass(_a.analytics.success_trend))},[M("i",{class:c(pa.getTrendIcon(_a.analytics.success_trend))},null,2),j(" "+o(Math.abs(_a.analytics.success_trend))+"% ",1)],2),a[22]||(a[22]=M("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1}),T(va,{span:6},{default:A(()=>[M("div",K,[a[27]||(a[27]=M("div",{class:"metric-icon response-time"},[M("i",{class:"el-icon-time"})],-1)),M("div",X,[M("div",ee,o(_a.analytics.avg_response_time)+"ms",1),a[26]||(a[26]=M("div",{class:"metric-label"},"平均响应时间",-1)),M("div",ae,[M("span",{class:c(pa.getTrendClass(-_a.analytics.response_trend))},[M("i",{class:c(pa.getTrendIcon(-_a.analytics.response_trend))},null,2),j(" "+o(Math.abs(_a.analytics.response_trend))+"ms ",1)],2),a[25]||(a[25]=M("span",{class:"trend-desc"},"较昨日",-1))])])])]),_:1})]),_:1})]),M("div",te,[T(ba,{gutter:20},{default:A(()=>[T(va,{span:12},{default:A(()=>[T(wa,{class:"chart-card"},{default:A(()=>[M("div",se,[a[31]||(a[31]=M("span",null,"📈 访问趋势",-1)),T(ya,{modelValue:_a.trendPeriod,"onUpdate:modelValue":a[1]||(a[1]=e=>_a.trendPeriod=e),size:"small",onChange:pa.loadTrendData},{default:A(()=>[T(fa,{label:"7d"},{default:A(()=>a[28]||(a[28]=[j("7天",-1)])),_:1,__:[28]}),T(fa,{label:"30d"},{default:A(()=>a[29]||(a[29]=[j("30天",-1)])),_:1,__:[29]}),T(fa,{label:"90d"},{default:A(()=>a[30]||(a[30]=[j("90天",-1)])),_:1,__:[30]})]),_:1},8,["modelValue","onChange"])]),M("div",le,[_a.trendData.length?(x(),C("div",ne,[M("div",oe,[a[33]||(a[33]=M("h4",null,"访问趋势图",-1)),M("p",null,"显示过去"+o(_a.trendPeriod)+"的访问统计",1)])])):(x(),C("div",ie,a[32]||(a[32]=[M("i",{class:"el-icon-loading"},null,-1),M("p",null,"加载中...",-1)])))])]),_:1})]),_:1}),T(va,{span:12},{default:A(()=>[T(wa,{class:"chart-card"},{default:A(()=>[M("div",ce,[a[35]||(a[35]=M("span",null,"🌐 域名使用分布",-1)),T(ga,{type:"text",onClick:pa.showDomainDetail},{default:A(()=>a[34]||(a[34]=[j("查看详情",-1)])),_:1,__:[34]},8,["onClick"])]),M("div",de,[M("div",re,[(x(!0),C(U,null,R(_a.domainStats,e=>(x(),C("div",{class:"domain-item",key:e.id},[M("div",me,[M("span",ue,o(e.domain),1),M("span",_e,o(pa.getDomainTypeName(e.type)),1)]),M("div",pe,[M("div",he,[a[36]||(a[36]=M("span",{class:"metric-label"},"使用次数",-1)),M("span",ge,o(e.usage_count),1)]),M("div",ve,[a[37]||(a[37]=M("span",{class:"metric-label"},"成功率",-1)),M("span",be,o(e.success_rate)+"%",1)]),M("div",fe,[a[38]||(a[38]=M("span",{class:"metric-label"},"健康度",-1)),T(Sa,{percentage:e.health_score,color:pa.getHealthColor(e.health_score),"stroke-width":6,"show-text":!1},null,8,["percentage","color"])])])]))),128))])])]),_:1})]),_:1})]),_:1})]),M("div",ye,[T(ba,{gutter:20},{default:A(()=>[T(va,{span:8},{default:A(()=>[T(wa,{class:"stats-card"},{default:A(()=>[a[39]||(a[39]=M("div",{slot:"header",class:"card-header"},[M("span",null,"📱 访问来源")],-1)),M("div",we,[(x(!0),C(U,null,R(_a.sourceStats,e=>(x(),C("div",{class:"stat-item",key:e.name},[M("div",Se,[M("div",ke,o(e.name),1),M("div",De,o(e.percentage)+"%",1)]),M("div",Ve,[M("div",{class:"stat-fill",style:p({width:e.percentage+"%"})},null,4)]),M("div",Ce,o(e.count),1)]))),128))])]),_:1,__:[39]})]),_:1}),T(va,{span:8},{default:A(()=>[T(wa,{class:"stats-card"},{default:A(()=>[a[40]||(a[40]=M("div",{slot:"header",class:"card-header"},[M("span",null,"🌍 地区分布")],-1)),M("div",xe,[(x(!0),C(U,null,R(_a.regionStats,e=>(x(),C("div",{class:"stat-item",key:e.name},[M("div",Me,[M("div",Te,o(e.name),1),M("div",Ae,o(e.percentage)+"%",1)]),M("div",je,[M("div",{class:"stat-fill",style:p({width:e.percentage+"%"})},null,4)]),M("div",Ue,o(e.count),1)]))),128))])]),_:1,__:[40]})]),_:1}),T(va,{span:8},{default:A(()=>[T(wa,{class:"stats-card"},{default:A(()=>[a[41]||(a[41]=M("div",{slot:"header",class:"card-header"},[M("span",null,"🕐 时间分布")],-1)),M("div",Re,[M("div",Ie,[(x(!0),C(U,null,R(_a.hourlyStats,e=>(x(),C("div",{class:"time-item",key:e.hour},[M("div",He,o(e.hour)+":00",1),M("div",$e,[M("div",{class:"time-fill",style:p({height:e.count/pa.maxHourlyCount*100+"%"})},null,4)]),M("div",Ne,o(e.count),1)]))),128))])])]),_:1,__:[41]})]),_:1})]),_:1})]),M("div",Pe,[T(wa,{class:"alerts-card"},{default:A(()=>[M("div",ze,[a[43]||(a[43]=M("span",null,"⚠️ 异常监控",-1)),T(ga,{type:"text",onClick:pa.showAlertSettings},{default:A(()=>a[42]||(a[42]=[j("告警设置",-1)])),_:1,__:[42]},8,["onClick"])]),M("div",qe,[M("div",Le,[M("div",We,[a[45]||(a[45]=M("div",{class:"summary-icon warning"},[M("i",{class:"el-icon-warning"})],-1)),M("div",Fe,[M("div",Oe,o(_a.alerts.warning_count),1),a[44]||(a[44]=M("div",{class:"summary-label"},"警告",-1))])]),M("div",Qe,[a[47]||(a[47]=M("div",{class:"summary-icon error"},[M("i",{class:"el-icon-error"})],-1)),M("div",Ye,[M("div",Ze,o(_a.alerts.error_count),1),a[46]||(a[46]=M("div",{class:"summary-label"},"错误",-1))])]),M("div",Be,[a[49]||(a[49]=M("div",{class:"summary-icon blocked"},[M("i",{class:"el-icon-remove-outline"})],-1)),M("div",Ee,[M("div",Ge,o(_a.alerts.blocked_count),1),a[48]||(a[48]=M("div",{class:"summary-label"},"封禁",-1))])])]),M("div",Je,[(x(!0),C(U,null,R(_a.recentAlerts,e=>(x(),C("div",{class:"alert-item",key:e.id},[M("div",Ke,[M("i",{class:c(pa.getAlertIcon(e.type))},null,2)]),M("div",Xe,[M("div",ea,o(e.title),1),M("div",aa,o(e.description),1),M("div",ta,o(pa.formatTime(e.created_at)),1)]),M("div",sa,[T(ga,{type:"text",size:"small",onClick:a=>pa.handleAlert(e)},{default:A(()=>a[50]||(a[50]=[j(" 处理 ",-1)])),_:2,__:[50]},1032,["onClick"])])]))),128))])])]),_:1})]),T(Ca,{title:"域名详细统计",visible:_a.domainDetailVisible,width:"800px"},{default:A(()=>[M("div",la,[I((x(),H(Va,{data:_a.domainDetailStats},{default:A(()=>[T(ka,{prop:"domain",label:"域名",width:"180"},{default:A(()=>[M("template",ia,[M("span",na,o(e.scope.row.domain),1)])]),_:1}),T(ka,{prop:"type",label:"类型",width:"100"},{default:A(()=>[M("template",oa,[T(Da,{size:"small",type:pa.getDomainTypeColor(e.scope.row.type)},{default:A(()=>[j(o(pa.getDomainTypeName(e.scope.row.type)),1)]),_:1},8,["type"])])]),_:1}),T(ka,{prop:"usage_count",label:"使用次数",width:"100"}),T(ka,{prop:"success_count",label:"成功次数",width:"100"}),T(ka,{prop:"success_rate",label:"成功率",width:"100"},{default:A(()=>[M("template",ca,[M("span",null,o(e.scope.row.success_rate)+"%",1)])]),_:1}),T(ka,{prop:"health_score",label:"健康度",width:"120"},{default:A(()=>[M("template",da,[T(Sa,{percentage:e.scope.row.health_score,color:pa.getHealthColor(e.scope.row.health_score),"stroke-width":8},null,8,["percentage","color"])])]),_:1}),T(ka,{prop:"avg_response_time",label:"平均响应时间",width:"120"},{default:A(()=>[M("template",ra,[M("span",null,o(e.scope.row.avg_response_time)+"ms",1)])]),_:1})]),_:1},8,["data"])),[[Ra,_a.domainDetailLoading]])])]),_:1},8,["visible"]),T(Ca,{title:"告警设置",visible:_a.alertSettingsVisible,width:"600px"},{default:A(()=>[M("div",ma,[T(Ua,{model:_a.alertSettings,"label-width":"150px"},{default:A(()=>[T(xa,{"content-position":"left"},{default:A(()=>a[51]||(a[51]=[j("域名检测设置",-1)])),_:1,__:[51]}),T(Ta,{label:"启用域名检测"},{default:A(()=>[T(Ma,{modelValue:_a.alertSettings.domain_check_enabled,"onUpdate:modelValue":a[2]||(a[2]=e=>_a.alertSettings.domain_check_enabled=e)},null,8,["modelValue"])]),_:1}),T(Ta,{label:"检测间隔（分钟）"},{default:A(()=>[T(Aa,{modelValue:_a.alertSettings.domain_check_interval,"onUpdate:modelValue":a[3]||(a[3]=e=>_a.alertSettings.domain_check_interval=e),min:1,max:60,disabled:!_a.alertSettings.domain_check_enabled},null,8,["modelValue","disabled"])]),_:1}),T(Ta,{label:"封禁阈值"},{default:A(()=>[T(Aa,{modelValue:_a.alertSettings.block_threshold,"onUpdate:modelValue":a[4]||(a[4]=e=>_a.alertSettings.block_threshold=e),min:1,max:10,disabled:!_a.alertSettings.domain_check_enabled},null,8,["modelValue","disabled"]),a[52]||(a[52]=M("span",{class:"form-help"},"连续失败次数超过此值时触发告警",-1))]),_:1,__:[52]}),T(xa,{"content-position":"left"},{default:A(()=>a[53]||(a[53]=[j("性能监控设置",-1)])),_:1,__:[53]}),T(Ta,{label:"响应时间阈值（毫秒）"},{default:A(()=>[T(Aa,{modelValue:_a.alertSettings.response_time_threshold,"onUpdate:modelValue":a[5]||(a[5]=e=>_a.alertSettings.response_time_threshold=e),min:1e3,max:1e4,step:100},null,8,["modelValue"])]),_:1}),T(Ta,{label:"成功率阈值（%）"},{default:A(()=>[T(Aa,{modelValue:_a.alertSettings.success_rate_threshold,"onUpdate:modelValue":a[6]||(a[6]=e=>_a.alertSettings.success_rate_threshold=e),min:50,max:100,step:1},null,8,["modelValue"])]),_:1}),T(xa,{"content-position":"left"},{default:A(()=>a[54]||(a[54]=[j("通知设置",-1)])),_:1,__:[54]}),T(Ta,{label:"启用通知"},{default:A(()=>[T(Ma,{modelValue:_a.alertSettings.notification_enabled,"onUpdate:modelValue":a[7]||(a[7]=e=>_a.alertSettings.notification_enabled=e)},null,8,["modelValue"])]),_:1}),T(Ta,{label:"邮件通知"},{default:A(()=>[T(ja,{modelValue:_a.alertSettings.notification_email,"onUpdate:modelValue":a[8]||(a[8]=e=>_a.alertSettings.notification_email=e),placeholder:"请输入邮箱地址",disabled:!_a.alertSettings.notification_enabled},null,8,["modelValue","disabled"])]),_:1}),T(Ta,{label:"Webhook通知"},{default:A(()=>[T(ja,{modelValue:_a.alertSettings.notification_webhook,"onUpdate:modelValue":a[9]||(a[9]=e=>_a.alertSettings.notification_webhook=e),placeholder:"请输入Webhook地址",disabled:!_a.alertSettings.notification_enabled},null,8,["modelValue","disabled"])]),_:1}),T(xa,{"content-position":"left"},{default:A(()=>a[55]||(a[55]=[j("自动切换设置",-1)])),_:1,__:[55]}),T(Ta,{label:"启用自动切换"},{default:A(()=>[T(Ma,{modelValue:_a.alertSettings.auto_switch_enabled,"onUpdate:modelValue":a[10]||(a[10]=e=>_a.alertSettings.auto_switch_enabled=e)},null,8,["modelValue"])]),_:1}),T(Ta,{label:"自动切换阈值"},{default:A(()=>[T(Aa,{modelValue:_a.alertSettings.auto_switch_threshold,"onUpdate:modelValue":a[11]||(a[11]=e=>_a.alertSettings.auto_switch_threshold=e),min:1,max:10,disabled:!_a.alertSettings.auto_switch_enabled},null,8,["modelValue","disabled"]),a[56]||(a[56]=M("span",{class:"form-help"},"域名异常次数超过此值时自动切换",-1))]),_:1,__:[56]})]),_:1},8,["model"])]),M("div",ua,[T(ga,{onClick:a[12]||(a[12]=e=>_a.alertSettingsVisible=!1)},{default:A(()=>a[57]||(a[57]=[j("取消",-1)])),_:1,__:[57]}),T(ga,{type:"primary",onClick:pa.saveAlertSettings},{default:A(()=>a[58]||(a[58]=[j("保存设置",-1)])),_:1,__:[58]},8,["onClick"])])]),_:1},8,["visible"])])}],["__scopeId","data-v-718314bd"]]);export{_a as default};
