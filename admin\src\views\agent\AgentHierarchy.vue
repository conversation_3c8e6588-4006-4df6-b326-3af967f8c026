<template>
  <div class="agent-hierarchy-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">团队层级管理</h1>
          <p class="page-subtitle">管理代理商团队结构和层级关系</p>
        </div>
        <div class="header-actions">
          <el-button @click="refreshHierarchy" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button type="primary" @click="showAddAgentDialog">
            <el-icon><Plus /></el-icon>
            添加下级代理
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Avatar /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ hierarchyStats.totalAgents }}</div>
          <div class="stat-label">团队总人数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ hierarchyStats.directChildren }}</div>
          <div class="stat-label">直属下级</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ hierarchyStats.maxLevel }}</div>
          <div class="stat-label">最大层级</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ hierarchyStats.totalCommission }}</div>
          <div class="stat-label">团队总佣金</div>
        </div>
      </div>
    </div>

    <!-- 层级树形结构 -->
    <el-card class="hierarchy-card" shadow="never">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon><Connection /></el-icon>
            <span>团队层级结构</span>
          </div>
          <div class="header-actions">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button value="tree">树形视图</el-radio-button>
              <el-radio-button value="table">表格视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree'" class="tree-view">
        <el-tree
          ref="hierarchyTree"
          :data="hierarchyData"
          :props="treeProps"
          :expand-on-click-node="false"
          :default-expand-all="false"
          node-key="id"
          class="hierarchy-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-info">
                <el-avatar :size="32" :src="data.avatar" class="node-avatar">
                  {{ data.agent_name?.charAt(0) }}
                </el-avatar>
                <div class="node-details">
                  <div class="node-name">{{ data.agent_name }}</div>
                  <div class="node-meta">
                    <el-tag :type="getLevelTagType(data.agent_level)" size="small">
                      {{ getLevelText(data.agent_level) }}
                    </el-tag>
                    <span class="node-code">{{ data.agent_code }}</span>
                  </div>
                </div>
              </div>
              <div class="node-stats">
                <div class="stat-item">
                  <span class="stat-label">下级:</span>
                  <span class="stat-value">{{ data.children_count || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">佣金:</span>
                  <span class="stat-value">¥{{ data.total_commission || 0 }}</span>
                </div>
              </div>
              <div class="node-actions">
                <el-button size="small" type="primary" link @click="viewAgentDetail(data)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button size="small" type="success" link @click="addSubAgent(data)">
                  <el-icon><Plus /></el-icon>
                  添加下级
                </el-button>
                <el-dropdown @command="handleNodeCommand" trigger="click">
                  <el-button size="small" type="info" link>
                    <el-icon><More /></el-icon>
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ action: 'edit', data }">
                        <el-icon><Edit /></el-icon>
                        编辑信息
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'commission', data }">
                        <el-icon><Money /></el-icon>
                        佣金管理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'performance', data }">
                        <el-icon><TrendCharts /></el-icon>
                        业绩分析
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'transfer', data }" divided>
                        <el-icon><Switch /></el-icon>
                        转移代理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'disable', data }" v-if="data.status === 'active'">
                        <el-icon><CircleClose /></el-icon>
                        停用代理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'enable', data }" v-else>
                        <el-icon><CircleCheck /></el-icon>
                        启用代理
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view">
        <el-table :data="flatHierarchyData" v-loading="loading" class="hierarchy-table">
          <el-table-column prop="agent_name" label="代理商" min-width="200">
            <template #default="{ row }">
              <div class="agent-info">
                <el-avatar :size="32" :src="row.avatar">
                  {{ row.agent_name?.charAt(0) }}
                </el-avatar>
                <div class="agent-details">
                  <div class="agent-name">{{ row.agent_name }}</div>
                  <div class="agent-code">{{ row.agent_code }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="level_path" label="层级路径" min-width="150">
            <template #default="{ row }">
              <div class="level-path">
                <span v-for="(level, index) in row.level_path" :key="index" class="level-item">
                  {{ level }}
                  <el-icon v-if="index < row.level_path.length - 1"><ArrowRight /></el-icon>
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="agent_level" label="代理等级" width="120">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.agent_level)" size="small">
                {{ getLevelText(row.agent_level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="children_count" label="下级数量" width="100" align="center">
            <template #default="{ row }">
              <el-badge :value="row.children_count || 0" :max="99" class="children-badge">
                <el-icon><Avatar /></el-icon>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="total_commission" label="累计佣金" width="120" align="right">
            <template #default="{ row }">
              <span class="commission-amount">¥{{ row.total_commission || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="加入时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="viewAgentDetail(row)">
                详情
              </el-button>
              <el-button size="small" type="success" link @click="addSubAgent(row)">
                添加下级
              </el-button>
              <el-dropdown @command="handleNodeCommand" trigger="click">
                <el-button size="small" type="info" link>
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'edit', data: row }">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'commission', data: row }">佣金</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'performance', data: row }">业绩</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 添加代理商对话框 -->
    <el-dialog v-model="showAddDialog" title="添加下级代理" width="600px" class="add-agent-dialog">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="代理商姓名" prop="agent_name">
          <el-input v-model="addForm.agent_name" placeholder="请输入代理商姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="addForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="代理等级" prop="agent_level">
          <el-select v-model="addForm.agent_level" placeholder="请选择代理等级">
            <el-option label="初级代理" value="junior" />
            <el-option label="中级代理" value="intermediate" />
            <el-option label="高级代理" value="senior" />
            <el-option label="金牌代理" value="gold" />
          </el-select>
        </el-form-item>
        <el-form-item label="上级代理" prop="parent_id">
          <el-cascader
            v-model="addForm.parent_id"
            :options="agentOptions"
            :props="cascaderProps"
            placeholder="请选择上级代理"
            clearable
          />
        </el-form-item>
        <el-form-item label="备注信息">
          <el-input v-model="addForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddAgent" :loading="addLoading">
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 代理商详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="代理商详情" width="800px" class="detail-dialog">
      <div v-if="selectedAgent" class="agent-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedAgent.avatar">
            {{ selectedAgent.agent_name?.charAt(0) }}
          </el-avatar>
          <div class="agent-info">
            <h3>{{ selectedAgent.agent_name }}</h3>
            <p class="agent-code">代理编号: {{ selectedAgent.agent_code }}</p>
            <el-tag :type="getLevelTagType(selectedAgent.agent_level)">
              {{ getLevelText(selectedAgent.agent_level) }}
            </el-tag>
          </div>
        </div>

        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <div class="info-grid">
              <div class="info-item">
                <label>手机号码:</label>
                <span>{{ selectedAgent.phone || '-' }}</span>
              </div>
              <div class="info-item">
                <label>邮箱地址:</label>
                <span>{{ selectedAgent.email || '-' }}</span>
              </div>
              <div class="info-item">
                <label>加入时间:</label>
                <span>{{ formatDate(selectedAgent.created_at) }}</span>
              </div>
              <div class="info-item">
                <label>状态:</label>
                <el-tag :type="selectedAgent.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ selectedAgent.status === 'active' ? '正常' : '停用' }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>直属下级:</label>
                <span>{{ selectedAgent.children_count || 0 }}人</span>
              </div>
              <div class="info-item">
                <label>团队总人数:</label>
                <span>{{ selectedAgent.team_count || 0 }}人</span>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="业绩统计" name="performance">
            <div class="performance-stats">
              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-value">¥{{ selectedAgent.total_commission || 0 }}</div>
                  <div class="stat-label">累计佣金</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">¥{{ selectedAgent.month_commission || 0 }}</div>
                  <div class="stat-label">本月佣金</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ selectedAgent.total_orders || 0 }}</div>
                  <div class="stat-label">累计订单</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ selectedAgent.month_orders || 0 }}</div>
                  <div class="stat-label">本月订单</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="团队结构" name="team">
            <div class="team-structure">
              <p>团队层级结构图表将在这里显示</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Plus, Avatar, Connection, TrendCharts, Money,
  View, Edit, More, Switch, CircleClose, CircleCheck,
  ArrowRight, ArrowDown
} from '@element-plus/icons-vue'

// 页面状态
const loading = ref(false)
const viewMode = ref('tree')
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showCommissionDialog = ref(false)
const showPerformanceDialog = ref(false)
const showTransferDialog = ref(false)
const addLoading = ref(false)
const selectedAgent = ref(null)
const activeTab = ref('basic')

// 层级数据
const hierarchyData = ref([])
const hierarchyStats = reactive({
  totalAgents: 0,
  directChildren: 0,
  maxLevel: 0,
  totalCommission: 0
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'agent_name'
}

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'agent_name',
  children: 'children'
}

// 添加代理表单
const addForm = reactive({
  agent_name: '',
  phone: '',
  email: '',
  agent_level: '',
  parent_id: null,
  remark: ''
})

// 编辑代理表单
const editForm = ref({
  agent_name: '',
  phone: '',
  email: '',
  agent_level: '',
  remark: ''
})

// 转移代理表单
const transferForm = ref({
  agent_id: null,
  new_parent_id: null,
  reason: ''
})

const addRules = {
  agent_name: [
    { required: true, message: '请输入代理商姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  agent_level: [
    { required: true, message: '请选择代理等级', trigger: 'change' }
  ]
}

const addFormRef = ref()

// 计算属性
const flatHierarchyData = computed(() => {
  const flattenData = (data, level = 1, parentPath = []) => {
    let result = []
    data.forEach(item => {
      const currentPath = [...parentPath, item.agent_name]
      const flatItem = {
        ...item,
        level,
        level_path: currentPath
      }
      result.push(flatItem)
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children, level + 1, currentPath))
      }
    })
    return result
  }
  return flattenData(hierarchyData.value)
})

const agentOptions = computed(() => {
  return hierarchyData.value
})

// 方法
const refreshHierarchy = async () => {
  await loadHierarchyData()
}

const showAddAgentDialog = () => {
  resetAddForm()
  showAddDialog.value = true
}

const resetAddForm = () => {
  Object.assign(addForm, {
    agent_name: '',
    phone: '',
    email: '',
    agent_level: '',
    parent_id: null,
    remark: ''
  })
  addFormRef.value?.clearValidate()
}

const handleAddAgent = async () => {
  try {
    await addFormRef.value.validate()
    addLoading.value = true

    // TODO: 调用API添加代理商
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('添加代理商成功')
    showAddDialog.value = false
    await loadHierarchyData()
  } catch (error) {
    console.error('添加代理商失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('添加代理商失败')
    }
  } finally {
    addLoading.value = false
  }
}

const viewAgentDetail = (agent) => {
  selectedAgent.value = agent
  activeTab.value = 'basic'
  showDetailDialog.value = true
}

const addSubAgent = (parentAgent) => {
  resetAddForm()
  addForm.parent_id = [parentAgent.id]
  showAddDialog.value = true
}

// 新增对话框方法
const showEditAgentDialog = (agent) => {
  selectedAgent.value = agent
  editForm.value = {
    agent_name: agent.agent_name,
    phone: agent.phone,
    email: agent.email,
    agent_level: agent.agent_level,
    remark: agent.remark || ''
  }
  showEditDialog.value = true
}

const showAgentCommissionDialog = (agent) => {
  selectedAgent.value = agent
  showCommissionDialog.value = true
  // 加载该代理商的佣金数据
  loadAgentCommissionData(agent.id)
}

const showAgentPerformanceDialog = (agent) => {
  selectedAgent.value = agent
  showPerformanceDialog.value = true
  // 加载该代理商的业绩数据
  loadAgentPerformanceData(agent.id)
}

const showTransferAgentDialog = (agent) => {
  selectedAgent.value = agent
  transferForm.value = {
    agent_id: agent.id,
    new_parent_id: null,
    reason: ''
  }
  showTransferDialog.value = true
}

const handleNodeCommand = async ({ action, data }) => {
  switch (action) {
    case 'edit':
      showEditAgentDialog(data)
      break
    case 'commission':
      showAgentCommissionDialog(data)
      break
    case 'performance':
      showAgentPerformanceDialog(data)
      break
    case 'transfer':
      showTransferAgentDialog(data)
      break
    case 'disable':
      await handleToggleStatus(data, 'inactive')
      break
    case 'enable':
      await handleToggleStatus(data, 'active')
      break
  }
}

const handleToggleStatus = async (agent, status) => {
  try {
    const action = status === 'active' ? '启用' : '停用'
    await ElMessageBox.confirm(
      `确定要${action}代理商 "${agent.agent_name}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用API更新状态
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success(`${action}成功`)
    await loadHierarchyData()
  } catch {
    ElMessage.info('已取消操作')
  }
}

const getLevelTagType = (level) => {
  const typeMap = {
    junior: 'info',
    intermediate: 'warning',
    senior: 'success',
    gold: 'danger'
  }
  return typeMap[level] || 'info'
}

const getLevelText = (level) => {
  const textMap = {
    junior: '初级代理',
    intermediate: '中级代理',
    senior: '高级代理',
    gold: '金牌代理'
  }
  return textMap[level] || '未知等级'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadHierarchyData = async () => {
  loading.value = true

  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟层级数据
    hierarchyData.value = [
      {
        id: 1,
        agent_name: '张三',
        agent_code: 'A001',
        agent_level: 'gold',
        phone: '13800138001',
        email: '<EMAIL>',
        avatar: '',
        status: 'active',
        children_count: 2,
        total_commission: 15000,
        month_commission: 3000,
        total_orders: 150,
        month_orders: 25,
        team_count: 8,
        created_at: '2024-01-01',
        children: [
          {
            id: 2,
            agent_name: '李四',
            agent_code: 'A002',
            agent_level: 'senior',
            phone: '13800138002',
            email: '<EMAIL>',
            avatar: '',
            status: 'active',
            children_count: 1,
            total_commission: 8000,
            month_commission: 1500,
            total_orders: 80,
            month_orders: 12,
            team_count: 3,
            created_at: '2024-01-15',
            children: [
              {
                id: 3,
                agent_name: '王五',
                agent_code: 'A003',
                agent_level: 'intermediate',
                phone: '13800138003',
                email: '<EMAIL>',
                avatar: '',
                status: 'active',
                children_count: 0,
                total_commission: 3000,
                month_commission: 800,
                total_orders: 30,
                month_orders: 8,
                team_count: 1,
                created_at: '2024-02-01',
                children: []
              }
            ]
          },
          {
            id: 4,
            agent_name: '赵六',
            agent_code: 'A004',
            agent_level: 'junior',
            phone: '13800138004',
            email: '<EMAIL>',
            avatar: '',
            status: 'inactive',
            children_count: 0,
            total_commission: 1200,
            month_commission: 200,
            total_orders: 15,
            month_orders: 3,
            team_count: 1,
            created_at: '2024-02-15',
            children: []
          }
        ]
      }
    ]

    // 计算统计数据
    const calculateStats = (data) => {
      let totalAgents = 0
      let directChildren = 0
      let maxLevel = 0
      let totalCommission = 0

      const traverse = (nodes, level = 1) => {
        nodes.forEach(node => {
          totalAgents++
          totalCommission += node.total_commission || 0
          maxLevel = Math.max(maxLevel, level)

          if (level === 1) {
            directChildren += node.children_count || 0
          }

          if (node.children && node.children.length > 0) {
            traverse(node.children, level + 1)
          }
        })
      }

      traverse(data)

      return { totalAgents, directChildren, maxLevel, totalCommission }
    }

    const stats = calculateStats(hierarchyData.value)
    Object.assign(hierarchyStats, stats)

  } catch (error) {
    console.error('加载层级数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadHierarchyData()
})
</script>

<style scoped>
.agent-hierarchy-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

/* 层级卡片 */
.hierarchy-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 树形视图 */
.tree-view {
  padding: 20px 0;
}

.hierarchy-tree {
  --el-tree-node-hover-bg-color: #f8fafc;
  --el-tree-node-content-height: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.tree-node:hover {
  background: #f8fafc;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.node-avatar {
  border: 2px solid #e5e7eb;
}

.node-details {
  flex: 1;
}

.node-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.node-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-code {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-stats {
  display: flex;
  gap: 16px;
  margin: 0 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-right: 4px;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
}

.node-actions {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  padding: 20px 0;
}

.hierarchy-table {
  --el-table-border-color: #e5e7eb;
  --el-table-bg-color: white;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-details {
  flex: 1;
}

.agent-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.agent-code {
  font-size: 12px;
  color: #6b7280;
}

.level-path {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.level-item {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
}

.children-badge {
  display: inline-flex;
}

.commission-amount {
  font-weight: 600;
  color: #059669;
}

/* 对话框样式 */
.add-agent-dialog .el-form-item {
  margin-bottom: 20px;
}

.detail-dialog {
  .agent-detail {
    .detail-header {
      display: flex;
      align-items: center;
      gap: 20px;
      padding: 20px 0;
      border-bottom: 1px solid #e5e7eb;
      margin-bottom: 20px;
    }

    .agent-info h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
    }

    .agent-code {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.detail-tabs {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 20px 0;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    label {
      font-size: 12px;
      color: #6b7280;
      font-weight: 500;
    }

    span {
      font-size: 14px;
      color: #1f2937;
      font-weight: 500;
    }
  }
}

.performance-stats {
  padding: 20px 0;

  .stat-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #6b7280;
      }
    }
  }
}

.team-structure {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-hierarchy-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .tree-node {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .node-stats {
    justify-content: space-around;
    margin: 0;
  }

  .node-actions {
    justify-content: center;
  }
}
</style>