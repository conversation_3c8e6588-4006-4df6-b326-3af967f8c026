<template>
  <el-dialog
    v-model="visible"
    title="选择模板"
    width="80%"
    :before-close="handleClose"
  >
    <div class="template-selector">
      <!-- 模板分类 -->
      <div class="template-categories">
        <el-radio-group v-model="selectedCategory" @change="filterTemplates">
          <el-radio-button value="all">全部模板</el-radio-button>
          <el-radio-button value="marketing">营销推广</el-radio-button>
          <el-radio-button value="product">产品展示</el-radio-button>
          <el-radio-button value="event">活动页面</el-radio-button>
          <el-radio-button value="form">表单收集</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 模板列表 -->
      <div class="template-grid" v-loading="loading">
        <div 
          v-for="template in filteredTemplates" 
          :key="template.id"
          class="template-card"
          :class="{ selected: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <div class="template-preview">
            <img :src="template.preview_image" :alt="template.name" />
            <div class="template-overlay">
              <el-button type="primary" size="small" @click.stop="handlePreviewTemplate(template)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
            </div>
          </div>
          
          <div class="template-info">
            <h4 class="template-name">{{ template.name }}</h4>
            <p class="template-desc">{{ template.description }}</p>
            
            <div class="template-meta">
              <div class="template-category">
                <el-tag size="small" :type="getCategoryColor(template.category)">
                  {{ getCategoryName(template.category) }}
                </el-tag>
              </div>
              <div class="template-stats">
                <span class="usage-count">
                  <el-icon><User /></el-icon>
                  {{ template.usage_count || 0 }}
                </span>
                <span class="rating">
                  <el-icon><Star /></el-icon>
                  {{ template.rating || 5.0 }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="template-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="useTemplate(template)"
            >
              使用模板
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0 && !loading" class="empty-state">
        <el-icon><Document /></el-icon>
        <p>暂无模板</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!selectedTemplate"
          @click="confirmSelection"
        >
          确定使用
        </el-button>
      </div>
    </template>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="模板预览"
      width="90%"
      append-to-body
    >
      <div class="template-preview-container">
        <iframe 
          v-if="currentPreviewTemplate"
          :src="previewUrl"
          frameborder="0"
          width="100%"
          height="600px"
        ></iframe>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { View, User, Star, Document } from '@element-plus/icons-vue'
import { promotionApi } from '@/api/promotion'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

const loading = ref(false)
const selectedCategory = ref('all')
const selectedTemplate = ref(null)
const previewVisible = ref(false)
const currentPreviewTemplate = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const templates = ref([])

// 模拟模板数据
const mockTemplates = [
  {
    id: 1,
    name: '产品营销模板',
    description: '适用于产品推广和营销活动的专业模板',
    category: 'marketing',
    preview_image: '/placeholder.svg?height=300&width=400',
    usage_count: 1250,
    rating: 4.8,
    preview_url: '/placeholder.svg?height=600&width=800&text=模板1预览'
  },
  {
    id: 2,
    name: '产品展示模板',
    description: '展示产品特性和优势的精美模板',
    category: 'product',
    preview_image: '/placeholder.svg?height=300&width=400',
    usage_count: 890,
    rating: 4.9,
    preview_url: '/placeholder.svg?height=600&width=800&text=模板2预览'
  },
  {
    id: 3,
    name: '活动报名模板',
    description: '在线活动报名和信息收集模板',
    category: 'event',
    preview_image: '/placeholder.svg?height=300&width=400',
    usage_count: 567,
    rating: 4.7,
    preview_url: '/placeholder.svg?height=600&width=800&text=模板3预览'
  },
  {
    id: 4,
    name: '表单收集模板',
    description: '用户信息收集和问卷调查模板',
    category: 'form',
    preview_image: '/placeholder.svg?height=300&width=400',
    usage_count: 432,
    rating: 4.6,
    preview_url: '/placeholder.svg?height=600&width=800&text=模板4预览'
  }
]

const filteredTemplates = computed(() => {
  if (selectedCategory.value === 'all') {
    return templates.value
  }
  return templates.value.filter(template => template.category === selectedCategory.value)
})

const previewUrl = computed(() => {
  return currentPreviewTemplate.value?.preview_url || ''
})

// 加载模板列表
const loadTemplates = async () => {
  loading.value = true
  try {
    const { data } = await promotionApi.getList({ type: 'template' })
    templates.value = data || mockTemplates
  } catch (error) {
    console.error('加载模板失败:', error)
    templates.value = mockTemplates
  } finally {
    loading.value = false
  }
}

const filterTemplates = () => {
  selectedTemplate.value = null
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
}

const handlePreviewTemplate = (template) => {
  currentPreviewTemplate.value = template
  previewVisible.value = true
}

const useTemplate = (template) => {
  selectedTemplate.value = template
  confirmSelection()
}

const confirmSelection = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请选择一个模板')
    return
  }
  
  emit('select', selectedTemplate.value)
  handleClose()
}

const handleClose = () => {
  visible.value = false
  selectedTemplate.value = null
}

const getCategoryColor = (category) => {
  const colors = {
    marketing: 'primary',
    product: 'success',
    event: 'warning',
    form: 'info'
  }
  return colors[category] || ''
}

const getCategoryName = (category) => {
  const names = {
    marketing: '营销推广',
    product: '产品展示',
    event: '活动页面',
    form: '表单收集'
  }
  return names[category] || '未知'
}

// 初始化
onMounted(() => {
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.template-selector {
  .template-categories {
    margin-bottom: 24px;
    text-align: center;
  }
  
  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    min-height: 400px;
    
    .template-card {
      background: white;
      border: 2px solid #ebeef5;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      }
      
      &.selected {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
      
      .template-preview {
        position: relative;
        height: 200px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .template-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover .template-overlay {
          opacity: 1;
        }
      }
      
      .template-info {
        padding: 16px;
        
        .template-name {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
        
        .template-desc {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #606266;
          line-height: 1.5;
        }
        
        .template-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .template-stats {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #909399;
            
            .usage-count,
            .rating {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }
      }
      
      .template-actions {
        padding: 0 16px 16px 16px;
        
        .el-button {
          width: 100%;
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #909399;
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

.template-preview-container {
  height: 600px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.dialog-footer {
  text-align: right;
}
</style>