<template>
  <div class="ai-enhanced-search">
    <!-- 搜索触发器 -->
    <div class="search-trigger" @click="openSearch">
      <el-icon class="search-icon"><Search /></el-icon>
      <span class="search-placeholder">AI智能搜索...</span>
      <div class="search-shortcuts">
        <kbd>Ctrl</kbd> + <kbd>K</kbd>
      </div>
    </div>

    <!-- 搜索对话框 -->
    <el-dialog
      v-model="showDialog"
      :show-close="false"
      :close-on-click-modal="false"
      width="700px"
      class="search-dialog"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <div class="header-title">
            <el-icon><MagicStick /></el-icon>
            <span>AI智能搜索</span>
          </div>
          <div class="header-actions">
            <el-button text @click="toggleVoiceSearch" :class="{ active: voiceSearchActive }">
              <el-icon><Microphone /></el-icon>
            </el-button>
            <el-button text @click="closeSearch">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <div class="search-content">
        <!-- 搜索输入区 -->
        <div class="search-input-container">
          <el-input
            v-model="searchQuery"
            ref="searchInputRef"
            placeholder="输入搜索内容，支持自然语言查询..."
            size="large"
            class="search-input"
            @input="handleSearchInput"
            @keydown="handleKeydown"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #suffix>
              <div class="input-actions">
                <el-button 
                  v-if="searchQuery" 
                  text 
                  @click="clearSearch"
                  class="clear-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
                <el-button 
                  text 
                  @click="toggleVoiceSearch"
                  :class="{ active: voiceSearchActive }"
                  class="voice-btn"
                >
                  <el-icon><Microphone /></el-icon>
                </el-button>
              </div>
            </template>
          </el-input>
          
          <!-- 搜索建议标签 -->
          <div class="search-suggestions" v-if="!searchQuery && searchSuggestions.length > 0">
            <el-tag
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              size="small"
              class="suggestion-tag"
              @click="searchQuery = suggestion"
            >
              {{ suggestion }}
            </el-tag>
          </div>
        </div>

        <!-- AI分析状态 -->
        <div class="ai-analysis" v-if="isAnalyzing">
          <div class="analysis-indicator">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>AI正在分析您的查询...</span>
          </div>
        </div>

        <!-- 搜索结果容器 -->
        <div class="search-results-container">
          <!-- 无搜索时显示推荐 -->
          <div v-if="!searchQuery && !isAnalyzing" class="recommendations">
            <!-- AI推荐功能 -->
            <div class="recommendation-section" v-if="aiRecommendations.length > 0">
              <div class="section-header">
                <el-icon><MagicStick /></el-icon>
                <span>AI为您推荐</span>
              </div>
              <div 
                v-for="(recommendation, index) in aiRecommendations" 
                :key="recommendation.id"
                class="recommendation-item"
                :class="{ active: selectedIndex === index }"
                @click="handleRecommendationClick(recommendation)"
              >
                <div class="item-icon ai-icon">
                  <el-icon><component :is="recommendation.icon || 'MagicStick'" /></el-icon>
                </div>
                <div class="item-content">
                  <div class="item-title">{{ recommendation.title }}</div>
                  <div class="item-desc">{{ recommendation.description }}</div>
                  <div class="ai-confidence" v-if="recommendation.confidence">
                    <span>AI置信度: {{ Math.round(recommendation.confidence * 100) }}%</span>
                  </div>
                </div>
                <div class="item-badge">
                  <el-tag type="info" size="small">AI推荐</el-tag>
                </div>
              </div>
            </div>

            <!-- 角色专属推荐 -->
            <div class="recommendation-section">
              <div class="section-header">
                <el-icon><User /></el-icon>
                <span>为您推荐</span>
              </div>
              <div 
                v-for="(item, index) in roleBasedRecommendations" 
                :key="item.id"
                class="recommendation-item"
                :class="{ 
                  active: selectedIndex === (aiRecommendations.length + index),
                  protected: item.protected 
                }"
                @click="handleItemClick(item)"
              >
                <div class="item-icon" :class="item.iconClass">
                  <el-icon><component :is="item.icon" /></el-icon>
                </div>
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-desc">{{ item.description }}</div>
                </div>
                <div class="item-badge" v-if="item.protected">
                  <el-tag type="warning" size="small">核心功能</el-tag>
                </div>
              </div>
            </div>

            <!-- 最近使用 -->
            <div class="recommendation-section" v-if="recentItems.length > 0">
              <div class="section-header">
                <el-icon><Clock /></el-icon>
                <span>最近使用</span>
              </div>
              <div 
                v-for="(item, index) in recentItems" 
                :key="item.id"
                class="recommendation-item recent-item"
                @click="handleItemClick(item)"
              >
                <div class="item-icon">
                  <el-icon><component :is="item.icon" /></el-icon>
                </div>
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-time">{{ formatTime(item.lastUsed) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索结果 -->
          <div v-else-if="searchQuery && !isAnalyzing" class="search-results">
            <div class="results-header">
              <span>找到 {{ searchResults.length }} 个结果</span>
              <span class="search-time">{{ searchTime }}ms</span>
            </div>
            
            <!-- AI解释 -->
            <div class="ai-explanation" v-if="aiExplanation">
              <div class="explanation-header">
                <el-icon><MagicStick /></el-icon>
                <span>AI理解</span>
              </div>
              <p>{{ aiExplanation }}</p>
            </div>

            <!-- 搜索结果列表 -->
            <VirtualScrollList
              :items="searchResults"
              :item-height="80"
              :container-height="300"
              class="results-list"
            >
              <template #default="{ item, index }">
                <div 
                  class="result-item"
                  :class="{ 
                    active: selectedIndex === index,
                    protected: item.protected,
                    'ai-recommended': item.aiScore > 0.8
                  }"
                  @click="handleItemClick(item)"
                >
                  <div class="result-icon" :class="item.iconClass">
                    <el-icon><component :is="item.icon" /></el-icon>
                  </div>
                  <div class="result-content">
                    <div class="result-title" v-html="highlightText(item.title, searchQuery)"></div>
                    <div class="result-path">{{ item.path }}</div>
                    <div class="result-description" v-if="item.description">
                      {{ item.description }}
                    </div>
                  </div>
                  <div class="result-badges">
                    <el-tag v-if="item.protected" type="warning" size="small">核心</el-tag>
                    <el-tag v-if="item.aiScore > 0.8" type="success" size="small">AI推荐</el-tag>
                    <div class="ai-score" v-if="item.aiScore">
                      {{ Math.round(item.aiScore * 100) }}%匹配
                    </div>
                  </div>
                </div>
              </template>
            </VirtualScrollList>
          </div>

          <!-- 无结果状态 -->
          <div v-else-if="searchQuery && searchResults.length === 0 && !isAnalyzing" class="no-results">
            <div class="no-results-icon">
              <el-icon><Search /></el-icon>
            </div>
            <div class="no-results-text">未找到相关结果</div>
            <div class="search-suggestions">
              <p>您可以尝试：</p>
              <el-button size="small" @click="searchQuery = '创建群组'">创建群组</el-button>
              <el-button size="small" @click="searchQuery = '用户管理'">用户管理</el-button>
              <el-button size="small" @click="searchQuery = '数据分析'">数据分析</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 语音搜索指示器 -->
    <div v-if="voiceSearchActive" class="voice-indicator">
      <div class="voice-animation">
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
      </div>
      <p>正在听取您的语音...</p>
      <el-button @click="stopVoiceSearch">停止</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { usePreferencesStore } from '@/stores/preferences'
import { ElMessage } from 'element-plus'
import VirtualScrollList from './common/VirtualScrollList.vue'
import {
  Search, MagicStick, Microphone, Close, Loading, User, Clock
} from '@element-plus/icons-vue'

// 依赖注入
const router = useRouter()
const userStore = useUserStore()
const preferencesStore = usePreferencesStore()

// 响应式数据
const showDialog = ref(false)
const searchQuery = ref('')
const searchInputRef = ref(null)
const selectedIndex = ref(0)
const isAnalyzing = ref(false)
const voiceSearchActive = ref(false)
const searchTime = ref(0)
const aiExplanation = ref('')

// AI推荐数据
const aiRecommendations = ref([
  {
    id: 'ai_group_creation',
    title: '智能群组创建助手',
    description: '基于您的历史数据，AI建议创建产品交流群',
    icon: 'ChatDotRound',
    confidence: 0.92,
    action: () => router.push('/groups/create?ai=true')
  },
  {
    id: 'ai_user_analysis',
    title: '用户行为分析',
    description: 'AI发现用户活跃度异常，建议查看详细分析',
    icon: 'DataAnalysis',
    confidence: 0.87,
    action: () => router.push('/analytics/users?ai=true')
  }
])

// 搜索建议
const searchSuggestions = ref([
  '创建群组', '用户管理', '数据分析', '订单处理', '系统设置'
])

// 搜索结果
const searchResults = ref([])

// 计算属性
const currentUser = computed(() => userStore.currentUser)

const roleBasedRecommendations = computed(() => {
  const baseRecommendations = [
    {
      id: 'create_group',
      title: '创建群组',
      description: '快速创建新的微信群组',
      icon: 'ChatDotRound',
      iconClass: 'text-blue-500',
      protected: true,
      path: '/groups/create',
      action: () => router.push('/groups/create')
    },
    {
      id: 'user_management',
      title: '用户管理',
      description: '管理系统用户和权限',
      icon: 'User',
      iconClass: 'text-green-500',
      path: '/users',
      action: () => router.push('/users')
    },
    {
      id: 'data_analysis',
      title: '数据分析',
      description: '查看业务数据和统计报表',
      icon: 'DataAnalysis',
      iconClass: 'text-purple-500',
      path: '/analytics',
      action: () => router.push('/analytics')
    }
  ]

  // 根据用户角色过滤推荐
  return baseRecommendations.filter(item => {
    // 群组创建功能对所有角色可见
    if (item.id === 'create_group') return true
    
    // 其他功能根据角色权限过滤
    const userRole = currentUser.value?.role
    if (userRole === 'admin') return true
    if (userRole === 'substation' && ['user_management', 'data_analysis'].includes(item.id)) return true
    
    return false
  })
})

const recentItems = computed(() => {
  // 从用户偏好中获取最近使用的功能
  const recentUsage = preferencesStore.behaviorData.featureUsage || {}
  return Object.entries(recentUsage)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([featureId, usage]) => ({
      id: featureId,
      title: getFeatureTitle(featureId),
      icon: getFeatureIcon(featureId),
      lastUsed: new Date(Date.now() - Math.random() * 86400000) // 模拟最近使用时间
    }))
})

// 方法
const openSearch = () => {
  showDialog.value = true
  nextTick(() => {
    searchInputRef.value?.focus()
  })
}

const closeSearch = () => {
  showDialog.value = false
  searchQuery.value = ''
  selectedIndex.value = 0
  aiExplanation.value = ''
}

const clearSearch = () => {
  searchQuery.value = ''
  selectedIndex.value = 0
  searchResults.value = []
  aiExplanation.value = ''
}

const handleSearchInput = async (value) => {
  if (!value.trim()) {
    searchResults.value = []
    aiExplanation.value = ''
    return
  }

  isAnalyzing.value = true
  const startTime = Date.now()

  try {
    // 模拟AI分析延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 执行搜索
    const results = await performAISearch(value)
    searchResults.value = results
    searchTime.value = Date.now() - startTime

    // 生成AI解释
    aiExplanation.value = generateAIExplanation(value, results)

    // 记录搜索行为
    preferencesStore.recordSearch(value, results)
    
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    isAnalyzing.value = false
  }
}

const performAISearch = async (query) => {
  // 模拟AI增强搜索算法
  const allItems = [
    {
      id: 'create_group',
      title: '创建群组',
      description: '快速创建新的微信群组',
      icon: 'ChatDotRound',
      iconClass: 'text-blue-500',
      path: '/groups/create',
      protected: true,
      keywords: ['创建', '群组', '微信', '新建', 'group', 'create'],
      category: 'core'
    },
    {
      id: 'manage_users',
      title: '用户管理',
      description: '管理系统用户和权限设置',
      icon: 'User',
      iconClass: 'text-green-500',
      path: '/users',
      keywords: ['用户', '管理', '权限', 'user', 'manage'],
      category: 'management'
    },
    {
      id: 'analytics',
      title: '数据分析',
      description: '查看业务数据和统计报表',
      icon: 'DataAnalysis',
      iconClass: 'text-purple-500',
      path: '/analytics',
      keywords: ['数据', '分析', '统计', '报表', 'analytics', 'data'],
      category: 'analytics'
    }
  ]

  const queryLower = query.toLowerCase()
  const results = []

  allItems.forEach(item => {
    let score = 0
    
    // 标题匹配
    if (item.title.toLowerCase().includes(queryLower)) {
      score += 10
    }
    
    // 描述匹配
    if (item.description.toLowerCase().includes(queryLower)) {
      score += 5
    }
    
    // 关键词匹配
    item.keywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(queryLower)) {
        score += 3
      }
    })
    
    // 路径匹配
    if (item.path.toLowerCase().includes(queryLower)) {
      score += 2
    }
    
    // 核心功能加权
    if (item.protected) {
      score += 5
    }
    
    // AI语义分析加权（模拟）
    const semanticScore = calculateSemanticScore(query, item)
    score += semanticScore

    if (score > 0) {
      results.push({
        ...item,
        aiScore: Math.min(score / 20, 1), // 归一化到0-1
        matchScore: score
      })
    }
  })

  // 按AI评分排序
  return results.sort((a, b) => b.matchScore - a.matchScore)
}

const calculateSemanticScore = (query, item) => {
  // 模拟AI语义分析
  const semanticMappings = {
    '创建': ['群组', '新建', '添加'],
    '管理': ['用户', '设置', '配置'],
    '分析': ['数据', '统计', '报表'],
    '群组': ['创建', '管理', '设置']
  }
  
  let score = 0
  Object.entries(semanticMappings).forEach(([key, values]) => {
    if (query.includes(key)) {
      values.forEach(value => {
        if (item.title.includes(value) || item.description.includes(value)) {
          score += 2
        }
      })
    }
  })
  
  return score
}

const generateAIExplanation = (query, results) => {
  if (results.length === 0) {
    return `AI未能理解查询"${query}"，请尝试使用更具体的关键词。`
  }
  
  const topResult = results[0]
  const confidence = Math.round(topResult.aiScore * 100)
  
  return `AI理解您想要"${query}"，推荐使用"${topResult.title}"功能（匹配度${confidence}%）。`
}

const handleKeydown = (event) => {
  const totalItems = searchQuery.value 
    ? searchResults.value.length 
    : aiRecommendations.value.length + roleBasedRecommendations.value.length

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, totalItems - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      handleEnterKey()
      break
    case 'Escape':
      event.preventDefault()
      closeSearch()
      break
  }
}

const handleEnterKey = () => {
  if (searchQuery.value && searchResults.value.length > 0) {
    const selectedItem = searchResults.value[selectedIndex.value]
    if (selectedItem) {
      handleItemClick(selectedItem)
    }
  } else if (!searchQuery.value) {
    const allRecommendations = [...aiRecommendations.value, ...roleBasedRecommendations.value]
    const selectedItem = allRecommendations[selectedIndex.value]
    if (selectedItem) {
      if (selectedItem.action) {
        selectedItem.action()
      } else {
        handleItemClick(selectedItem)
      }
    }
  }
}

const handleItemClick = (item) => {
  // 记录功能使用
  preferencesStore.recordFeatureUsage(item.id)
  
  if (item.action) {
    item.action()
  } else if (item.path) {
    router.push(item.path)
  }
  
  closeSearch()
}

const handleRecommendationClick = (recommendation) => {
  if (recommendation.action) {
    recommendation.action()
  }
  closeSearch()
}

const toggleVoiceSearch = () => {
  if (voiceSearchActive.value) {
    stopVoiceSearch()
  } else {
    startVoiceSearch()
  }
}

const startVoiceSearch = () => {
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    voiceSearchActive.value = true
    
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    const recognition = new SpeechRecognition()
    
    recognition.lang = 'zh-CN'
    recognition.continuous = false
    recognition.interimResults = false
    
    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      searchQuery.value = transcript
      handleSearchInput(transcript)
      voiceSearchActive.value = false
    }
    
    recognition.onerror = () => {
      voiceSearchActive.value = false
    }
    
    recognition.onend = () => {
      voiceSearchActive.value = false
    }
    
    recognition.start()
  } else {
    ElMessage.warning('您的浏览器不支持语音识别功能')
  }
}

const stopVoiceSearch = () => {
  voiceSearchActive.value = false
}

const highlightText = (text, query) => {
  if (!query) return text
  
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getFeatureTitle = (featureId) => {
  const titleMap = {
    'create_group': '创建群组',
    'manage_users': '用户管理',
    'analytics': '数据分析',
    'orders': '订单管理',
    'settings': '系统设置'
  }
  return titleMap[featureId] || featureId
}

const getFeatureIcon = (featureId) => {
  const iconMap = {
    'create_group': 'ChatDotRound',
    'manage_users': 'User',
    'analytics': 'DataAnalysis',
    'orders': 'ShoppingCart',
    'settings': 'Setting'
  }
  return iconMap[featureId] || 'Document'
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return time.toLocaleDateString()
}

// 键盘快捷键监听
const handleGlobalKeydown = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    openSearch()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})

// 监听器
watch(showDialog, (newValue) => {
  if (newValue) {
    selectedIndex.value = 0
  }
})
</script>

<style lang="scss" scoped>
.ai-enhanced-search {
  .search-trigger {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 320px;
    position: relative;

    &:hover {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
    }

    .search-icon {
      color: #6b7280;
      margin-right: 12px;
      font-size: 18px;
    }

    .search-placeholder {
      flex: 1;
      color: #6b7280;
      font-size: 14px;
      font-weight: 500;
    }

    .search-shortcuts {
      display: flex;
      gap: 2px;
      
      kbd {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 11px;
        color: #6b7280;
        font-family: monospace;
      }
    }
  }
}

:deep(.search-dialog) {
  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 8px;

        .el-button {
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          &.active {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }

  .el-dialog__body {
    padding: 0;
  }
}

.search-content {
  .search-input-container {
    padding: 24px;
    border-bottom: 1px solid #f1f5f9;

    .search-input {
      :deep(.el-input__wrapper) {
        border-radius: 16px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;

        &:hover,
        &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }
      }

      .input-actions {
        display: flex;
        gap: 4px;

        .clear-btn,
        .voice-btn {
          color: #9ca3af;

          &:hover {
            color: #6b7280;
          }

          &.active {
            color: #3b82f6;
          }
        }
      }
    }

    .search-suggestions {
      margin-top: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .suggestion-tag {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #3b82f6;
          color: white;
        }
      }
    }
  }

  .ai-analysis {
    padding: 16px 24px;
    background: linear-gradient(90deg, #eff6ff 0%, #dbeafe 100%);
    border-bottom: 1px solid #e0e7ff;

    .analysis-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #3b82f6;
      font-size: 14px;

      .el-icon {
        font-size: 16px;
      }
    }
  }

  .search-results-container {
    max-height: 500px;
    overflow: hidden;
  }

  // 推荐功能样式
  .recommendations {
    padding: 20px 24px;

    .recommendation-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-weight: 600;
        color: #1f2937;
        font-size: 14px;
      }

      .recommendation-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 8px;
        border: 1px solid transparent;

        &:hover,
        &.active {
          background: #f8fafc;
          border-color: #e2e8f0;
        }

        &.protected {
          background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
          border-color: #fbbf24;

          &:hover {
            background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
          }
        }

        &.recent-item {
          background: #f0f9ff;
          border-color: #bae6fd;
        }

        .item-icon {
          width: 40px;
          height: 40px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          margin-right: 16px;

          &.ai-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
          }
        }

        .item-content {
          flex: 1;

          .item-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            font-size: 15px;
          }

          .item-desc {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
          }

          .item-time {
            font-size: 12px;
            color: #9ca3af;
          }

          .ai-confidence {
            margin-top: 4px;
            font-size: 11px;
            color: #3b82f6;
            font-weight: 500;
          }
        }

        .item-badge {
          margin-left: 12px;
        }
      }
    }
  }

  // 搜索结果样式
  .search-results {
    padding: 20px 24px;

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      font-size: 13px;
      color: #6b7280;

      .search-time {
        color: #9ca3af;
      }
    }

    .ai-explanation {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #bae6fd;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;

      .explanation-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 600;
        color: #0369a1;
        font-size: 14px;
      }

      p {
        margin: 0;
        color: #0c4a6e;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .results-list {
      border-radius: 12px;
      border: 1px solid #f1f5f9;
      overflow: hidden;

      .result-item {
        display: flex;
        align-items: center;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 1px solid #f8fafc;

        &:last-child {
          border-bottom: none;
        }

        &:hover,
        &.active {
          background: #f1f5f9;
        }

        &.protected {
          background: linear-gradient(90deg, #fffbeb 0%, transparent 100%);
          border-left: 3px solid #fbbf24;
        }

        &.ai-recommended {
          background: linear-gradient(90deg, #f0f9ff 0%, transparent 100%);
          border-left: 3px solid #3b82f6;
        }

        .result-icon {
          width: 36px;
          height: 36px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          background: #f1f5f9;
          font-size: 16px;
        }

        .result-content {
          flex: 1;

          .result-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            font-size: 15px;

            :deep(mark) {
              background: #fef3c7;
              color: #92400e;
              padding: 1px 3px;
              border-radius: 3px;
              font-weight: 700;
            }
          }

          .result-path {
            font-size: 11px;
            color: #9ca3af;
            margin-bottom: 2px;
          }

          .result-description {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
          }
        }

        .result-badges {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;

          .ai-score {
            font-size: 10px;
            color: #3b82f6;
            font-weight: 500;
            background: #eff6ff;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }
      }
    }
  }

  // 无结果样式
  .no-results {
    padding: 60px 24px;
    text-align: center;

    .no-results-icon {
      font-size: 64px;
      color: #d1d5db;
      margin-bottom: 20px;
    }

    .no-results-text {
      font-size: 18px;
      color: #374151;
      margin-bottom: 24px;
    }

    .search-suggestions {
      p {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 16px;
      }

      .el-button {
        margin: 0 6px 8px 0;
      }
    }
  }
}

// 语音搜索指示器
.voice-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 32px;
  border-radius: 20px;
  text-align: center;
  z-index: 3000;

  .voice-animation {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 16px;

    .wave {
      width: 4px;
      height: 20px;
      background: #3b82f6;
      border-radius: 2px;
      animation: wave 1.2s ease-in-out infinite;

      &:nth-child(2) {
        animation-delay: 0.1s;
      }

      &:nth-child(3) {
        animation-delay: 0.2s;
      }
    }
  }

  p {
    margin: 0 0 16px 0;
    font-size: 16px;
  }
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-enhanced-search {
    .search-trigger {
      width: 250px;

      .search-placeholder {
        display: none;
      }

      .search-shortcuts {
        display: none;
      }
    }
  }

  :deep(.search-dialog) {
    .el-dialog {
      width: 95vw !important;
      margin: 2.5vh auto;
    }
  }

  .search-content {
    .search-input-container {
      padding: 16px;
    }

    .recommendations,
    .search-results {
      padding: 16px;
    }

    .search-results-container {
      max-height: 400px;
    }
  }
}
</style>