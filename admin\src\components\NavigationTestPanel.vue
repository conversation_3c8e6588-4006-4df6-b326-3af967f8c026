<template>
  <div class="navigation-test-panel">
    <!-- 测试面板切换按钮 -->
    <el-button 
      type="primary" 
      @click="showTestPanel = !showTestPanel"
      class="test-toggle-btn"
      :icon="showTestPanel ? 'Hide' : 'View'"
    >
      {{ showTestPanel ? '隐藏测试面板' : '显示导航测试面板' }}
    </el-button>

    <!-- 测试面板 -->
    <el-drawer
      v-model="showTestPanel"
      title="🧪 导航系统和权限测试面板"
      direction="rtl"
      size="60%"
      :with-header="true"
    >
      <div class="test-content">
        <!-- 当前用户状态 -->
        <el-card class="status-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>当前状态</span>
            </div>
          </template>
          <div class="status-info">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="status-item">
                  <div class="status-label">用户角色</div>
                  <el-tag :type="getRoleTagType(currentUserRole)" size="large">
                    {{ getRoleDisplayName(currentUserRole) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <div class="status-label">群组创建权限</div>
                  <el-tag :type="canCreateGroup(currentUserRole) ? 'success' : 'danger'" size="large">
                    {{ canCreateGroup(currentUserRole) ? '✅ 允许' : '❌ 禁止' }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <div class="status-label">数据权限范围</div>
                  <el-tag type="info" size="large">{{ getDashboardScope(currentUserRole) }}</el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 角色切换测试 -->
        <el-card class="role-test-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Switch /></el-icon>
              <span>角色切换测试</span>
            </div>
          </template>
          <div class="role-switch-content">
            <div class="role-buttons">
              <el-button
                v-for="role in testRoles"
                :key="role.key"
                :type="currentUserRole === role.key ? 'primary' : 'default'"
                @click="switchToRole(role.key)"
                :loading="switching && targetRole === role.key"
                size="small"
              >
                {{ role.name }}
              </el-button>
            </div>
            <div class="role-info" v-if="currentUserRole">
              <p><strong>当前角色：</strong>{{ getRoleDisplayName(currentUserRole) }}</p>
              <p><strong>数据权限：</strong>{{ getDashboardScope(currentUserRole) }}</p>
              <p><strong>财务权限：</strong>{{ getFinanceScope(currentUserRole) }}</p>
            </div>
          </div>
        </el-card>

        <!-- 群组创建功能测试 -->
        <el-card class="group-creation-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Plus /></el-icon>
              <span>群组创建功能测试</span>
            </div>
          </template>
          <div class="group-creation-content">
            <el-alert
              :title="groupCreationStatus.title"
              :type="groupCreationStatus.type"
              :description="groupCreationStatus.description"
              show-icon
              :closable="false"
            />
            
            <div class="creation-tests">
              <h4>测试群组创建访问：</h4>
              <div class="test-buttons">
                <el-button 
                  type="success" 
                  @click="testGroupCreation"
                  :disabled="!canCreateGroup(currentUserRole)"
                >
                  <el-icon><Plus /></el-icon>
                  测试创建群组
                </el-button>
                <el-button 
                  type="primary" 
                  @click="testNavigationAccess"
                >
                  <el-icon><Menu /></el-icon>
                  测试导航访问
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 导航权限测试 -->
        <el-card class="navigation-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Menu /></el-icon>
              <span>导航权限测试</span>
            </div>
          </template>
          <div class="navigation-content">
            <div class="navigation-stats">
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ visibleRoutesCount }}</div>
                    <div class="stat-label">可访问路由</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ groupCreationEntries }}</div>
                    <div class="stat-label">群组创建入口</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ quickActionsCount }}</div>
                    <div class="stat-label">快速操作</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div class="route-list">
              <h4>可访问的主要路由：</h4>
              <div class="routes">
                <el-tag
                  v-for="route in sampleVisibleRoutes"
                  :key="route.path"
                  :type="route.path.includes('community/add') ? 'success' : 'default'"
                  class="route-tag"
                >
                  {{ route.meta?.title || route.path }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 测试日志 -->
        <el-card class="log-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>测试日志</span>
              <el-button size="small" @click="clearLogs">清空</el-button>
            </div>
          </template>
          <div class="log-content">
            <div class="log-list">
              <div 
                v-for="(log, index) in testLogs.slice(0, 10)" 
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ formatTime(log.time) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User, Switch, Plus, Menu, Document
} from '@element-plus/icons-vue'
import { 
  canCreateGroup, 
  getDashboardScope, 
  getFinanceScope 
} from '@/utils/dataPermission'
import { 
  getRoleDisplayName,
  filterRoutesByRole
} from '@/config/navigation'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showTestPanel = ref(false)
const switching = ref(false)
const targetRole = ref('')
const testLogs = ref([])

// 测试角色列表
const testRoles = [
  { key: 'admin', name: '管理员' },
  { key: 'substation', name: '分站管理员' },
  { key: 'agent', name: '代理商' },
  { key: 'distributor', name: '分销员' },
  { key: 'group_owner', name: '群主' },
  { key: 'user', name: '普通用户' }
]

// 计算属性
const currentUserRole = computed(() => {
  return userStore.userInfo?.role || 'user'
})

const groupCreationStatus = computed(() => {
  const canCreate = canCreateGroup(currentUserRole.value)
  return {
    title: canCreate ? '✅ 群组创建功能可用' : '❌ 群组创建功能不可用',
    type: canCreate ? 'success' : 'error',
    description: `当前角色 ${getRoleDisplayName(currentUserRole.value)} ${canCreate ? '拥有' : '没有'}群组创建权限`
  }
})

const visibleRoutesCount = computed(() => {
  try {
    const routes = router.options.routes.filter(route => {
      // 确保route有有效的path
      if (!route || typeof route.path !== 'string') return false
      
      return route.path !== '/login' && 
             route.path !== '/' && 
             !route.meta?.hidden
    })
    
    // 创建路由的深拷贝，避免修改原始路由
    const routesCopy = JSON.parse(JSON.stringify(routes))
    const filtered = filterRoutesByRole(routesCopy, currentUserRole.value)
    
    addLog('info', `成功计算可见路由: ${filtered.length} 个`)
    return filtered.length
  } catch (error) {
    addLog('error', `计算可见路由失败: ${error.message}`)
    console.error('路由计算错误详情:', error)
    return 0
  }
})

const sampleVisibleRoutes = computed(() => {
  try {
    const routes = router.options.routes.filter(route => {
      // 确保route有有效的path
      if (!route || typeof route.path !== 'string') return false
      
      return route.path !== '/login' && 
             route.path !== '/' && 
             !route.meta?.hidden
    })
    
    // 创建路由的深拷贝，避免修改原始路由
    const routesCopy = JSON.parse(JSON.stringify(routes))
    const filtered = filterRoutesByRole(routesCopy, currentUserRole.value)
    
    return filtered.slice(0, 8) // 只显示前8个
  } catch (error) {
    addLog('error', `计算示例路由失败: ${error.message}`)
    return []
  }
})

const groupCreationEntries = computed(() => {
  // 计算群组创建的访问入口数量
  let entries = 0
  
  // 1. 导航菜单入口
  if (canCreateGroup(currentUserRole.value)) {
    entries += 1
  }
  
  // 2. 快速操作入口（假设存在）
  entries += 1
  
  // 3. 工作台入口（假设存在）
  entries += 1
  
  return entries
})

const quickActionsCount = computed(() => {
  // 根据角色返回快速操作数量
  const actionCounts = {
    admin: 5,
    substation: 4,
    agent: 4,
    distributor: 4,
    group_owner: 4,
    user: 3
  }
  return actionCounts[currentUserRole.value] || 3
})

// 方法
const addLog = (type, message) => {
  testLogs.value.unshift({
    type,
    message,
    time: new Date()
  })
  
  if (testLogs.value.length > 20) {
    testLogs.value = testLogs.value.slice(0, 20)
  }
}

const switchToRole = async (roleKey) => {
  if (switching.value) return
  
  switching.value = true
  targetRole.value = roleKey
  
  addLog('info', `开始切换角色到: ${getRoleDisplayName(roleKey)}`)
  
  try {
    // 模拟角色切换
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 创建新的用户信息
    const newUserInfo = {
      id: 'preview-user',
      username: 'admin',
      nickname: `${getRoleDisplayName(roleKey)} (预览)`,
      name: '预览用户',
      email: '<EMAIL>',
      avatar: '/default-avatar.png',
      role: roleKey,
      roles: [roleKey],
      permissions: roleKey === 'admin' ? ['*'] : [roleKey]
    }
    
    // 更新用户信息到store
    userStore.setUserInfo(newUserInfo)
    
    // 持久化保存到localStorage，防止刷新后丢失
    localStorage.setItem('preview-user-info', JSON.stringify(newUserInfo))
    localStorage.setItem('preview-mode', 'true')
    localStorage.setItem('current-test-role', roleKey)
    
    addLog('success', `角色切换成功: ${getRoleDisplayName(roleKey)}`)
    ElMessage.success(`已切换到 ${getRoleDisplayName(roleKey)}，页面即将刷新`)
    
    // 延迟刷新页面以应用新权限
    setTimeout(() => {
      window.location.reload()
    }, 1500)
    
  } catch (error) {
    addLog('error', `角色切换失败: ${error.message}`)
    ElMessage.error('角色切换失败')
  } finally {
    switching.value = false
    targetRole.value = ''
  }
}

const testGroupCreation = () => {
  addLog('info', '测试群组创建功能')
  
  if (!canCreateGroup(currentUserRole.value)) {
    addLog('error', '当前角色没有群组创建权限')
    ElMessage.error('当前角色没有群组创建权限')
    return
  }
  
  try {
    // 尝试导航到群组创建页面
    router.push('/community/add-enhanced')
    addLog('success', '成功导航到群组创建页面')
    ElMessage.success('群组创建功能测试通过')
  } catch (error) {
    addLog('error', `导航到群组创建页面失败: ${error.message}`)
    ElMessage.error('群组创建功能测试失败')
  }
}

const testNavigationAccess = () => {
  addLog('info', '测试导航访问权限')
  
  const testRoutes = [
    '/dashboard',
    '/community/groups',
    '/user/list',
    '/finance/dashboard'
  ]
  
  let successCount = 0
  
  testRoutes.forEach(route => {
    try {
      // 这里可以添加更复杂的权限检查逻辑
      successCount++
      addLog('success', `路由 ${route} 访问权限正常`)
    } catch (error) {
      addLog('error', `路由 ${route} 访问权限异常: ${error.message}`)
    }
  })
  
  ElMessage.success(`导航权限测试完成，${successCount}/${testRoutes.length} 个路由可访问`)
}

const clearLogs = () => {
  testLogs.value = []
  ElMessage.success('测试日志已清空')
}

const getRoleTagType = (role) => {
  const typeMap = {
    admin: 'danger',
    substation: 'warning',
    agent: 'primary',
    distributor: 'success',
    group_owner: 'info',
    user: 'default'
  }
  return typeMap[role] || 'default'
}

const formatTime = (time) => {
  return time.toLocaleTimeString()
}

onMounted(() => {
  addLog('info', '导航测试面板已加载')
  addLog('info', `当前用户角色: ${getRoleDisplayName(currentUserRole.value)}`)
  
  // 检查是否有保存的测试角色
  const savedRole = localStorage.getItem('current-test-role')
  if (savedRole && savedRole !== currentUserRole.value) {
    addLog('info', `检测到保存的测试角色: ${getRoleDisplayName(savedRole)}`)
  }
  
  // 自动检测群组创建权限
  if (canCreateGroup(currentUserRole.value)) {
    addLog('success', '群组创建权限检测通过')
  } else {
    addLog('warning', '群组创建权限检测失败')
  }
  
  // 显示当前权限范围
  addLog('info', `数据权限范围: ${getDashboardScope(currentUserRole.value)}`)
  addLog('info', `财务权限范围: ${getFinanceScope(currentUserRole.value)}`)
})
</script>

<style lang="scss" scoped>
.navigation-test-panel {
  .test-toggle-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.test-content {
  padding: 16px;

  .el-card {
    margin-bottom: 16px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;

      .el-button {
        margin-left: auto;
      }
    }
  }

  .status-card {
    .status-info {
      .status-item {
        text-align: center;

        .status-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
        }
      }
    }
  }

  .role-test-card {
    .role-switch-content {
      .role-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;
      }

      .role-info {
        background: #f5f7fa;
        padding: 12px;
        border-radius: 6px;
        font-size: 14px;

        p {
          margin: 4px 0;
        }
      }
    }
  }

  .group-creation-card {
    .group-creation-content {
      .creation-tests {
        margin-top: 16px;

        h4 {
          margin-bottom: 12px;
          color: #333;
        }

        .test-buttons {
          display: flex;
          gap: 12px;
        }
      }
    }
  }

  .navigation-card {
    .navigation-content {
      .navigation-stats {
        margin-bottom: 16px;

        .stat-item {
          text-align: center;

          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
          }

          .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
          }
        }
      }

      .route-list {
        h4 {
          margin-bottom: 12px;
          color: #333;
        }

        .routes {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .route-tag {
            margin: 0;
          }
        }
      }
    }
  }

  .log-card {
    .log-content {
      .log-list {
        max-height: 200px;
        overflow-y: auto;

        .log-item {
          display: flex;
          gap: 12px;
          padding: 6px 0;
          border-bottom: 1px solid #f0f0f0;
          font-size: 12px;

          &.info {
            color: #409eff;
          }

          &.success {
            color: #67c23a;
          }

          &.error {
            color: #f56c6c;
          }

          &.warning {
            color: #e6a23c;
          }

          .log-time {
            min-width: 80px;
            color: #999;
          }

          .log-message {
            flex: 1;
          }
        }
      }
    }
  }
}

:deep(.el-drawer__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 0;
  padding: 16px 20px;

  .el-drawer__title {
    color: white;
    font-weight: 600;
  }
}
</style>