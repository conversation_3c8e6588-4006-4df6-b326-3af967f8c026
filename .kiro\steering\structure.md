# Project Structure & Organization

## Root Directory Layout

```
ffjq/
├── app/                    # Laravel application core
├── admin/                  # Vue 3 admin panel
├── frontend/              # Nuxt 3 user frontend
├── config/                # Laravel configuration files
├── database/              # Migrations, seeders, factories
├── routes/                # API and web routes
├── public/                # Web-accessible files
├── storage/               # File storage and logs
├── docker/                # Docker configuration
├── nginx/                 # Nginx configuration
├── docs/                  # Project documentation
└── tests/                 # Test files
```

## Backend Structure (Laravel)

### Application Layer (`app/`)
- **Console/** - Artisan commands and kernel
- **Http/Controllers/Api/** - API controllers (17 controllers)
- **Http/Middleware/** - Custom middleware
- **Models/** - Eloquent models (28 models)
- **Services/** - Business logic services (10 services)
- **Observers/** - Model observers for events
- **Policies/** - Authorization policies
- **Providers/** - Service providers

### Key Models
- `User` - User management with roles and permissions
- `WechatGroup` - WeChat group management
- `Order` - Order processing and payments
- `Substation` - Regional management
- `DistributionGroup` - Distribution network
- `CommissionLog` - Commission tracking
- `DomainPool` - Anti-blocking domain management

### Services Architecture
- `AntiBlockService` - Domain management and link protection
- `PaymentService` - Payment processing integration
- `AnalyticsService` - Data analysis and reporting
- `NotificationService` - Multi-channel notifications

## Frontend Structure

### Admin Panel (`admin/`)
```
admin/
├── src/
│   ├── api/              # API service modules
│   ├── components/       # Reusable Vue components
│   ├── views/           # Page components
│   ├── router/          # Vue Router configuration
│   ├── stores/          # Pinia state management
│   ├── styles/          # SCSS stylesheets
│   └── utils/           # Utility functions
├── public/              # Static assets
└── dist/               # Built files
```

### User Frontend (`frontend/`)
```
frontend/
├── pages/              # Nuxt 3 file-based routing
├── components/         # Vue components
├── composables/        # Vue composition functions
├── layouts/           # Page layouts
├── middleware/        # Route middleware
├── stores/           # Pinia stores
├── types/            # TypeScript definitions
└── assets/           # Static assets
```

## API Architecture

### Route Organization
- **Public Routes** - Authentication, group browsing, payment callbacks
- **Authenticated Routes** - User profile, orders, groups
- **Admin Routes** - System management, user management, analytics
- **Distributor Routes** - Commission tracking, referral management
- **Substation Routes** - Regional management

### Controller Patterns
- Extend base `Controller` class with common response methods
- Use `success()`, `error()`, and `paginate()` helper methods
- Implement proper validation and authorization
- Follow RESTful conventions where applicable

## Database Structure

### Migration Organization
- User and authentication tables
- Business logic tables (groups, orders, commissions)
- System tables (settings, logs, notifications)
- Anti-blocking system tables

### Model Relationships
- **User** - Central model with multiple relationships
- **Hierarchical structures** - Parent-child relationships for distribution
- **Polymorphic relationships** - For flexible associations
- **Soft deletes** - For data integrity

## Configuration Management

### Environment Configuration
- `.env.example` - Template with all required variables
- `.env.production` - Production-specific settings
- `.env.optimized.example` - Performance-optimized template

### Config Categories
- **app.php** - Application settings
- **database.php** - Database connections
- **jwt.php** - JWT authentication
- **payment.php** - Payment gateway settings
- **anti-block.php** - Anti-blocking system
- **sms.php** - SMS service configuration

## File Organization Conventions

### Naming Conventions
- **Controllers** - Singular noun + "Controller" (e.g., `UserController`)
- **Models** - Singular noun (e.g., `User`, `WechatGroup`)
- **Services** - Descriptive name + "Service" (e.g., `PaymentService`)
- **Middleware** - Descriptive name + "Middleware"

### Code Organization
- Group related functionality in services
- Keep controllers thin, move business logic to services
- Use model observers for automatic actions
- Implement caching at the service level

## Deployment Structure

### Docker Organization
```
docker/
├── nginx/              # Nginx configuration
├── php/               # PHP-FPM configuration
├── mysql/             # MySQL configuration
└── redis/             # Redis configuration
```

### Production Files
- `docker-compose.yml` - Full stack orchestration
- `deploy-optimized.sh` - Automated deployment script
- `nginx.conf` - Web server configuration
- `supervisor.conf` - Process management

## Documentation Structure

### Documentation Categories
- **Deployment guides** - Setup and installation
- **API documentation** - Endpoint specifications
- **User guides** - Feature explanations
- **Troubleshooting** - Common issues and solutions
- **Security documentation** - Security features and best practices

### Key Documentation Files
- `README-OPTIMIZED.md` - Main project documentation
- `docs/PROJECT_STRUCTURE.md` - Detailed structure overview
- `docs/deployment-guide.md` - Deployment instructions
- `docs/api-usage.md` - API usage examples

## Development Workflow

### Code Standards
- Follow PSR-12 coding standards for PHP
- Use Laravel Pint for code formatting
- Implement proper error handling and logging
- Write comprehensive tests for critical functionality

### Git Organization
- Feature branches for new development
- Proper commit messages with context
- Code review process for production changes
- Automated testing on pull requests