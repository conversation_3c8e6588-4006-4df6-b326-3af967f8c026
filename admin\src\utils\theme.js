/**
 * 主题管理工具
 * 提供主题切换和持久化功能
 */

// 主题类型
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
};

// 存储键
const THEME_STORAGE_KEY = 'admin_theme_preference';

// 检查是否在浏览器环境
const isClient = typeof window !== 'undefined';

/**
 * 获取当前主题
 * @returns {string} 当前主题
 */
export function getCurrentTheme() {
  if (!isClient) return THEMES.SYSTEM;
  
  // 从本地存储获取
  const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
  
  // 如果有保存的主题，直接返回
  if (savedTheme && Object.values(THEMES).includes(savedTheme)) {
    return savedTheme;
  }
  
  // 否则返回系统主题
  return THEMES.SYSTEM;
}

/**
 * 设置主题
 * @param {string} theme 主题类型
 */
export function setTheme(theme) {
  if (!isClient) return;
  
  // 保存到本地存储
  localStorage.setItem(THEME_STORAGE_KEY, theme);
  
  // 应用主题
  applyTheme(theme);
}

/**
 * 应用主题
 * @param {string} theme 主题类型
 */
export function applyTheme(theme) {
  if (!isClient) return;
  
  const isDark = theme === THEMES.DARK || 
                (theme === THEMES.SYSTEM && window.matchMedia('(prefers-color-scheme: dark)').matches);
  
  // 添加或移除暗色模式类
  document.documentElement.classList.toggle('dark-mode', isDark);
  
  // 更新meta主题色
  const metaThemeColor = document.querySelector('meta[name="theme-color"]');
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', isDark ? '#18191a' : '#667eea');
  }
  
  // 触发主题变更事件
  window.dispatchEvent(new CustomEvent('themechange', { detail: { theme, isDark } }));
}

/**
 * 切换主题
 */
export function toggleTheme() {
  if (!isClient) return;
  
  const currentTheme = getCurrentTheme();
  
  if (currentTheme === THEMES.LIGHT) {
    setTheme(THEMES.DARK);
  } else if (currentTheme === THEMES.DARK) {
    setTheme(THEMES.SYSTEM);
  } else {
    setTheme(THEMES.LIGHT);
  }
}

/**
 * 初始化主题
 */
export function initTheme() {
  if (!isClient) return;
  
  // 应用保存的主题
  applyTheme(getCurrentTheme());
  
  // 监听系统主题变化
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    if (getCurrentTheme() === THEMES.SYSTEM) {
      applyTheme(THEMES.SYSTEM);
    }
  });
}