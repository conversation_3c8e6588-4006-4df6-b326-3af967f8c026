<template>
  <section class="features-component" :style="componentStyle">
    <div class="features-container">
      <div class="features-header" v-if="data.title">
        <h2 class="features-title">{{ data.title }}</h2>
        <p class="features-subtitle" v-if="data.subtitle">{{ data.subtitle }}</p>
      </div>
      
      <div class="features-grid">
        <div 
          v-for="(item, index) in data.items" 
          :key="index"
          class="feature-item"
        >
          <div class="feature-icon">
            <el-icon v-if="item.icon">
              <component :is="getIconComponent(item.icon)" />
            </el-icon>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">{{ item.title }}</h3>
            <p class="feature-description">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'
import { Star, Check } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      title: '产品特性',
      subtitle: '我们为您提供最优质的服务',
      items: [
        { title: '特性一', description: '特性描述', icon: 'star' },
        { title: '特性二', description: '特性描述', icon: 'heart' },
        { title: '特性三', description: '特性描述', icon: 'check' }
      ],
      backgroundColor: '#ffffff',
      textColor: '#333333'
    })
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const componentStyle = computed(() => ({
  backgroundColor: props.data.backgroundColor || '#ffffff',
  color: props.data.textColor || '#333333'
}))

const getIconComponent = (iconName) => {
  const icons = {
    star: Star,
    heart: Star, // 使用 Star 替代不存在的 Heart
    check: Check,
    trophy: Star, // 使用 Star 替代不存在的 Trophy
    shield: Star, // 使用 Star 替代不存在的 Shield
    rocket: Star // 使用 Star 替代不存在的 Rocket
  }
  return icons[iconName] || Star
}
</script>

<style lang="scss" scoped>
.features-component {
  padding: 80px 0;
  
  .features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    
    .features-header {
      text-align: center;
      margin-bottom: 60px;
      
      .features-title {
        font-size: 36px;
        font-weight: 700;
        margin: 0 0 16px 0;
      }
      
      .features-subtitle {
        font-size: 18px;
        margin: 0;
        opacity: 0.8;
      }
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;
      
      .feature-item {
        text-align: center;
        padding: 32px 24px;
        border-radius: 12px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        
        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 12px 32px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
          width: 80px;
          height: 80px;
          margin: 0 auto 24px auto;
          background: linear-gradient(135deg, #409eff, #67c23a);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .el-icon {
            font-size: 32px;
            color: white;
          }
        }
        
        .feature-content {
          .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 12px 0;
          }
          
          .feature-description {
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
            opacity: 0.8;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .features-component {
    padding: 60px 0;
    
    .features-container {
      .features-header {
        margin-bottom: 40px;
        
        .features-title {
          font-size: 28px;
        }
        
        .features-subtitle {
          font-size: 16px;
        }
      }
      
      .features-grid {
        grid-template-columns: 1fr;
        gap: 32px;
        
        .feature-item {
          padding: 24px 16px;
        }
      }
    }
  }
}
</style>