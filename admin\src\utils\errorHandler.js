import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

/**
 * 全局错误处理器
 */
export class ErrorHandler {
  /**
   * 处理HTTP错误
   * @param {Error} error - 错误对象
   */
  static handleHttpError(error) {
    const userStore = useUserStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          ElMessage.error(data.message || '请求参数错误')
          break
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          userStore.logout()
          router.push('/login')
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          this.handleValidationError(data)
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
        case 500:
          ElMessage.error('服务器内部错误，请联系管理员')
          break
        case 502:
          ElMessage.error('网关错误，请稍后重试')
          break
        case 503:
          ElMessage.error('服务暂时不可用，请稍后重试')
          break
        default:
          ElMessage.error(data.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求配置错误')
    }
  }
  
  /**
   * 处理表单验证错误
   * @param {Object} data - 错误数据
   */
  static handleValidationError(data) {
    if (data.errors) {
      const errors = Object.values(data.errors).flat()
      const errorMessage = errors.length > 0 ? errors[0] : '表单验证失败'
      ElMessage.error(errorMessage)
    } else {
      ElMessage.error(data.message || '表单验证失败')
    }
  }
  
  /**
   * 处理业务逻辑错误
   * @param {Error} error - 错误对象
   */
  static handleBusinessError(error) {
    const message = error.message || '操作失败'
    ElMessage.error(message)
  }
  
  /**
   * 处理JavaScript运行时错误
   * @param {Error} error - 错误对象
   */
  static handleRuntimeError(error) {
    console.error('Runtime Error:', error)

    // 过滤掉组件卸载相关的错误
    if (
      error?.message?.includes('Cannot read properties of null') ||
      error?.message?.includes('reading \'type\'') ||
      error?.stack?.includes('unmountComponent')
    ) {
      // 这些是组件卸载时的正常错误，不需要显示给用户
      return
    }

    if (process.env.NODE_ENV === 'development') {
      const errorMessage = error?.message || error?.toString() || '未知错误'
      ElMessage.error(`运行时错误: ${errorMessage}`)
    } else {
      ElMessage.error('系统出现异常，请刷新页面重试')
    }
  }
  
  /**
   * 处理Promise未捕获的错误
   * @param {PromiseRejectionEvent} event - Promise拒绝事件
   */
  static handleUnhandledRejection(event) {
    console.error('Unhandled Promise Rejection:', event.reason)

    // 过滤掉已知的无害错误
    if (
      event.reason?.message?.includes('拒绝了我们的连接请求') ||
      event.reason?.message?.includes('Network Error') ||
      event.reason?.message?.includes('ERR_CONNECTION_REFUSED') ||
      event.reason?.message?.includes('data2 is not iterable') ||
      event.reason?.message?.includes('is not iterable') ||
      event.reason?.message?.includes('Cannot read properties of null') ||
      event.reason?.stack?.includes('unmountComponent')
    ) {
      event.preventDefault()
      return
    }

    if (process.env.NODE_ENV === 'development') {
      const errorMessage = event.reason?.message || event.reason?.toString() || '未知错误'
      ElMessage.error(`未处理的Promise错误: ${errorMessage}`)
    } else {
      ElMessage.error('系统出现异常，请刷新页面重试')
    }

    // 阻止默认的错误处理
    event.preventDefault()
  }
  
  /**
   * 显示确认对话框
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   * @returns {Promise}
   */
  static showConfirm(message, title = '提示') {
    return ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
  }
  
  /**
   * 显示警告对话框
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   * @returns {Promise}
   */
  static showAlert(message, title = '提示') {
    return ElMessageBox.alert(message, title, {
      confirmButtonText: '确定'
    })
  }
}

// 全局错误处理器注册
export function setupErrorHandler() {
  // 处理Vue错误
  window.addEventListener('error', (event) => {
    ErrorHandler.handleRuntimeError(event.error)
  })
  
  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.handleUnhandledRejection(event)
  })
}

export default ErrorHandler