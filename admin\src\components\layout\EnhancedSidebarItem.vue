<template>
  <div class="enhanced-sidebar-item" v-if="!item.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <div v-if="hasChildren" class="nav-item-wrapper">
      <div 
        class="nav-item nav-item-parent"
        :class="itemClasses"
        @click="toggleExpanded"
      >
        <div class="nav-item-content">
          <div class="nav-item-icon">
            <el-icon v-if="item.meta?.icon">
              <component :is="item.meta.icon" />
            </el-icon>
          </div>
          
          <transition name="fade">
            <div v-show="!collapsed" class="nav-item-text">
              <span class="nav-item-title">{{ item.meta?.title || item.title }}</span>
              <span v-if="item.meta?.subtitle" class="nav-item-subtitle">{{ item.meta.subtitle }}</span>
            </div>
          </transition>
          
          <div class="nav-item-actions" v-if="!collapsed">
            <!-- 徽章 -->
            <transition name="badge-scale">
              <el-badge
                v-if="totalBadgeCount > 0"
                :value="totalBadgeCount"
                :type="getBadgeType()"
                class="nav-badge"
                :max="99"
              />
            </transition>
            
            <!-- 展开/收起箭头 -->
            <el-icon class="expand-arrow" :class="{ 'is-expanded': isExpanded }">
              <ArrowDown />
            </el-icon>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="nav-item-indicator" v-if="isActive || hasActiveChild" />
      </div>
      
      <!-- 子菜单 -->
      <collapse-transition>
        <div v-show="isExpanded || collapsed" class="nav-children">
          <enhanced-sidebar-item
            v-for="child in visibleChildren"
            :key="child.path"
            :item="child"
            :collapsed="collapsed"
            :level="level + 1"
            @item-click="handleChildClick"
          />
        </div>
      </collapse-transition>
    </div>
    
    <!-- 单个菜单项 -->
    <div v-else class="nav-item-wrapper">
      <div
        class="nav-item"
        :class="itemClasses"
        @click="handleItemClick"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div class="nav-item-content">
          <div class="nav-item-icon">
            <el-icon v-if="item.meta?.icon || item.icon">
              <component :is="item.meta?.icon || item.icon" />
            </el-icon>
          </div>
          
          <transition name="fade">
            <div v-show="!collapsed" class="nav-item-text">
              <span class="nav-item-title">{{ item.meta?.title || item.title }}</span>
              <span v-if="item.meta?.subtitle" class="nav-item-subtitle">{{ item.meta.subtitle }}</span>
            </div>
          </transition>
          
          <div class="nav-item-actions" v-if="!collapsed">
            <!-- 徽章 -->
            <transition name="badge-scale">
              <el-badge
                v-if="badgeCount > 0"
                :value="badgeCount"
                :type="getBadgeType()"
                class="nav-badge"
                :max="99"
              />
            </transition>
            
            <!-- 新功能标识 -->
            <el-tag v-if="item.meta?.isNew" size="small" type="warning" class="nav-tag">
              NEW
            </el-tag>
            
            <!-- 热门标识 -->
            <el-tag v-if="item.meta?.isHot" size="small" type="danger" class="nav-tag">
              HOT
            </el-tag>
            
            <!-- 外部链接图标 -->
            <el-icon v-if="isExternalLink" class="external-icon">
              <TopRight />
            </el-icon>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="nav-item-indicator" v-if="isActive" />
        
        <!-- 悬停效果 -->
        <div class="nav-item-hover-effect" />
      </div>
      
      <!-- Tooltip for collapsed state -->
      <el-tooltip 
        v-if="collapsed" 
        :content="tooltipContent"
        placement="right"
        :offset="12"
        :show-after="500"
      >
        <div class="tooltip-trigger" />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElIcon, ElBadge, ElTag, ElTooltip } from 'element-plus'
import { ArrowDown, TopRight } from '@element-plus/icons-vue'
import CollapseTransition from '../transitions/CollapseTransition.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  level: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['item-click'])

const route = useRoute()
const router = useRouter()

// 响应式数据
const isExpanded = ref(false)
const isHovered = ref(false)

// 计算属性
const hasChildren = computed(() => {
  return props.item.children && props.item.children.length > 0
})

const visibleChildren = computed(() => {
  if (!props.item.children) return []
  return props.item.children.filter(child => !child.meta?.hidden)
})

const resolvePath = computed(() => {
  if (isExternalLink.value) {
    return props.item.path
  }
  return props.item.path
})

const isActive = computed(() => {
  return route.path === resolvePath.value ||
         route.path.startsWith(resolvePath.value + '/')
})

const hasActiveChild = computed(() => {
  if (!hasChildren.value) return false
  return visibleChildren.value.some(child => {
    return route.path === child.path || route.path.startsWith(child.path + '/')
  })
})

const isExternalLink = computed(() => {
  return /^(https?:|mailto:|tel:)/.test(props.item.path)
})

const badgeCount = computed(() => {
  return props.item.badge || props.item.meta?.badge || 0
})

const totalBadgeCount = computed(() => {
  if (!hasChildren.value) return badgeCount.value
  
  let total = badgeCount.value
  visibleChildren.value.forEach(child => {
    total += (child.badge || child.meta?.badge || 0)
  })
  return total
})

const getBadgeType = () => {
  const count = hasChildren.value ? totalBadgeCount.value : badgeCount.value
  if (count > 10) return 'danger'
  if (count > 5) return 'warning'
  return 'primary'
}

const itemClasses = computed(() => ({
  'is-active': isActive.value && !hasChildren.value,
  'has-active-child': hasActiveChild.value,
  'is-expanded': isExpanded.value,
  'is-hovered': isHovered.value,
  'has-badge': (hasChildren.value ? totalBadgeCount.value : badgeCount.value) > 0,
  'is-new': props.item.meta?.isNew,
  'is-hot': props.item.meta?.isHot,
  'is-external': isExternalLink.value,
  [`nav-level-${props.level}`]: true
}))

const tooltipContent = computed(() => {
  let content = props.item.meta?.title || props.item.title
  if (props.item.meta?.subtitle) {
    content += `\n${props.item.meta.subtitle}`
  }
  if (hasChildren.value && visibleChildren.value.length > 0) {
    content += `\n包含 ${visibleChildren.value.length} 个子菜单`
  }
  return content
})

// 方法
const toggleExpanded = () => {
  if (props.collapsed) {
    // 收缩状态下点击直接导航到第一个子菜单
    if (visibleChildren.value.length > 0) {
      handleChildClick(visibleChildren.value[0])
    }
  } else {
    isExpanded.value = !isExpanded.value
  }
}

const handleItemClick = () => {
  if (isExternalLink.value) {
    window.open(resolvePath.value, '_blank')
  } else {
    router.push(resolvePath.value)
  }
  emit('item-click', props.item)
}

const handleChildClick = (child) => {
  emit('item-click', child)
}

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// 监听路由变化自动展开
watch(() => route.path, (newPath) => {
  if (hasActiveChild.value && !isExpanded.value && !props.collapsed) {
    isExpanded.value = true
  }
}, { immediate: true })

// 监听收缩状态
watch(() => props.collapsed, (collapsed) => {
  if (!collapsed && hasActiveChild.value) {
    isExpanded.value = true
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.enhanced-sidebar-item {
  .nav-item-wrapper {
    position: relative;
    margin-bottom: $nav-spacing-xs;
    
    .nav-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: $nav-spacing-sm $nav-spacing-md;
      border-radius: $nav-border-radius;
      cursor: pointer;
      transition: all $nav-transition-duration $nav-transition-ease;
      overflow: hidden;
      
      // 基础样式
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 0;
        background: $nav-gradient-primary;
        transition: width $nav-transition-duration $nav-transition-ease;
        border-radius: 0 $nav-border-radius-sm $nav-border-radius-sm 0;
      }
      
      .nav-item-content {
        display: flex;
        align-items: center;
        width: 100%;
        position: relative;
        z-index: 2;
        
        .nav-item-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: $nav-spacing-sm;
          transition: all $nav-transition-fast ease;
          
          .el-icon {
            font-size: 18px;
            color: $text-secondary-color;
            transition: all $nav-transition-fast ease;
          }
        }
        
        .nav-item-text {
          flex: 1;
          min-width: 0;
          
          .nav-item-title {
            display: block;
            font-size: map-get($nav-font-sizes, 'body');
            font-weight: map-get($nav-font-weights, 'medium');
            color: $text-color;
            line-height: 1.2;
            transition: all $nav-transition-fast ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .nav-item-subtitle {
            display: block;
            font-size: map-get($nav-font-sizes, 'xs');
            color: $text-muted-color;
            line-height: 1.2;
            margin-top: 2px;
            transition: all $nav-transition-fast ease;
          }
        }
        
        .nav-item-actions {
          display: flex;
          align-items: center;
          gap: $nav-spacing-xs;
          margin-left: $nav-spacing-sm;
          
          .nav-badge {
            :deep(.el-badge__content) {
              font-size: 10px;
              font-weight: map-get($nav-font-weights, 'bold');
              min-width: 16px;
              height: 16px;
              line-height: 16px;
              border-radius: 8px;
            }
          }
          
          .nav-tag {
            font-size: 9px;
            height: 16px;
            line-height: 14px;
            padding: 0 4px;
            border-radius: 3px;
            font-weight: map-get($nav-font-weights, 'bold');
          }
          
          .expand-arrow {
            font-size: 12px;
            color: $text-muted-color;
            transition: all $nav-transition-fast ease;
            
            &.is-expanded {
              transform: rotate(180deg);
              color: $nav-primary;
            }
          }
          
          .external-icon {
            font-size: 12px;
            color: $text-muted-color;
          }
        }
      }
      
      .nav-item-indicator {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 20px;
        background: $nav-gradient-primary;
        border-radius: 1.5px 0 0 1.5px;
        opacity: 0;
        transition: opacity $nav-transition-fast ease;
      }
      
      .nav-item-hover-effect {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba($nav-primary, 0.05), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
        pointer-events: none;
      }
      
      // 悬停状态
      &:hover {
        background: rgba($nav-primary, 0.05);
        transform: translateX(2px);
        
        .nav-item-icon .el-icon {
          color: $nav-primary;
          transform: scale(1.1);
        }
        
        .nav-item-text {
          .nav-item-title {
            color: $nav-primary;
          }
        }
        
        .nav-item-hover-effect {
          transform: translateX(100%);
        }
      }
      
      // 激活状态
      &.is-active {
        background: rgba($nav-primary, 0.1);
        
        &::before {
          width: 4px;
        }
        
        .nav-item-icon .el-icon {
          color: $nav-primary;
        }
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
          font-weight: map-get($nav-font-weights, 'semibold');
        }
        
        .nav-item-indicator {
          opacity: 1;
        }
      }
      
      // 有活跃子项
      &.has-active-child {
        background: rgba($nav-primary, 0.03);
        
        .nav-item-icon .el-icon {
          color: $nav-primary;
        }
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
        }
        
        .expand-arrow {
          color: $nav-primary;
        }
      }
      
      // 有徽章
      &.has-badge {
        .nav-item-text .nav-item-title {
          font-weight: map-get($nav-font-weights, 'semibold');
        }
      }
      
      // 新功能
      &.is-new {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: $nav-gradient-warning;
          border-radius: $nav-border-radius $nav-border-radius 0 0;
        }
      }
      
      // 热门功能
      &.is-hot {
        animation: pulse-glow 2s infinite;
      }
      
      // 外部链接
      &.is-external {
        .nav-item-text .nav-item-title {
          &::after {
            content: '';
            display: inline-block;
            width: 8px;
            height: 8px;
            margin-left: 4px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z'/%3E%3C/svg%3E") no-repeat center;
            background-size: contain;
            opacity: 0.5;
          }
        }
      }
      
      // 层级样式
      @for $i from 1 through 5 {
        &.nav-level-#{$i} {
          padding-left: #{$nav-spacing-md + ($i * $nav-spacing-lg)};
          
          &::before {
            left: #{($i * $nav-spacing-lg) - $nav-spacing-xs};
            width: 2px;
            background: rgba($nav-primary, 0.3);
          }
          
          .nav-item-icon {
            width: 20px;
            height: 20px;
            
            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }
    
    // 父级菜单项
    .nav-item-parent {
      font-weight: map-get($nav-font-weights, 'medium');
      
      &.is-expanded {
        background: rgba($nav-primary, 0.05);
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
        }
      }
    }
    
    // 子菜单容器
    .nav-children {
      overflow: hidden;
      
      .enhanced-sidebar-item {
        margin-bottom: 0;
        
        .nav-item-wrapper {
          margin-bottom: 2px;
        }
        
        .nav-item {
          padding: $nav-spacing-xs $nav-spacing-md;
          margin-left: $nav-spacing-lg;
          border-left: 2px solid transparent;
          border-radius: 0 $nav-border-radius $nav-border-radius 0;
          
          &::before {
            display: none;
          }
          
          &.is-active {
            border-left-color: $nav-primary;
            background: rgba($nav-primary, 0.08);
          }
          
          &:hover {
            background: rgba($nav-primary, 0.05);
            transform: translateX(4px);
          }
        }
      }
    }
    
    // Tooltip触发器
    .tooltip-trigger {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
    }
  }
}

// 动画效果
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba($nav-error, 0.3);
  }
  50% {
    box-shadow: 0 0 0 4px rgba($nav-error, 0.1);
  }
}

.badge-scale-enter-active,
.badge-scale-leave-active {
  transition: all $nav-transition-fast ease;
  transform-origin: center;
}

.badge-scale-enter-from,
.badge-scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity $nav-transition-fast ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: map-get($nav-breakpoints, 'md')) {
  .enhanced-sidebar-item {
    .nav-item {
      padding: $nav-spacing-md;
      
      .nav-item-content {
        .nav-item-icon {
          margin-right: $nav-spacing-md;
        }
        
        .nav-item-text .nav-item-title {
          font-size: map-get($nav-font-sizes, 'body');
        }
      }
    }
  }
}

// 深色主题
.theme-dark {
  .enhanced-sidebar-item {
    .nav-item {
      .nav-item-content {
        .nav-item-icon .el-icon {
          color: $nav-dark-text-secondary;
        }
        
        .nav-item-text {
          .nav-item-title {
            color: $nav-dark-text;
          }
          
          .nav-item-subtitle {
            color: $nav-dark-text-secondary;
          }
        }
        
        .nav-item-actions {
          .expand-arrow {
            color: $nav-dark-text-secondary;
            
            &.is-expanded {
              color: $nav-primary;
            }
          }
          
          .external-icon {
            color: $nav-dark-text-secondary;
          }
        }
      }
      
      &:hover {
        background: rgba($nav-primary, 0.1);
        
        .nav-item-icon .el-icon {
          color: $nav-primary;
        }
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
        }
      }
      
      &.is-active {
        background: rgba($nav-primary, 0.15);
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
        }
      }
      
      &.has-active-child {
        background: rgba($nav-primary, 0.08);
        
        .nav-item-text .nav-item-title {
          color: $nav-primary;
        }
      }
    }
  }
}
</style>