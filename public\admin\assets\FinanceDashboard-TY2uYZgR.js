import{_ as l}from"./index-DtXAftX0.js";/* empty css                         *//* empty css                 *//* empty css               *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                */import{r as a,d as e,e as s,n as t,H as n,k as i,l as c,af as o,t as d,E as u,z as r,u as p,D as v,F as f,Y as _,y as m,C as h}from"./vue-vendor-Dy164gUc.js";import{p as g,ai as b,T as y,a4 as w,at as k,aV as x,aR as C,a_ as j,aY as F,bb as V,U as z,aZ as I,a7 as T,a2 as q,a5 as L,o as P,bh as A,bi as E,a6 as O,a$ as R,ap as U,al as Z,c4 as D,bG as H,bK as K,bL as S,b1 as Y,ay as B,Q as G}from"./element-plus-h2SQQM64.js";import{L as J}from"./LineChart-CydsJ2U8.js";import{i as M}from"./echarts-D68jitv0.js";import{g as N,e as Q,a as W,b as X}from"./finance-DBah1Ldq.js";import{f as $}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const ll={__name:"PieChart",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:Number,default:300}},setup(l){const o=l,d=a();let u=null;const r=()=>{if(!u||!o.data)return;const l=o.data.datasets?.[0];if(!l)return;const a=o.data.labels?.map((a,e)=>({name:a,value:l.data[e],itemStyle:{color:l.backgroundColor[e]}}))||[],e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:o.data.labels||[]},series:[{name:"数据",type:"pie",radius:"50%",data:a,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}],...o.options};u.setOption(e)},p=()=>{u&&u.resize()};return e(()=>o.data,()=>{t(()=>{r()})},{deep:!0}),e(()=>o.options,()=>{t(()=>{r()})},{deep:!0}),s(()=>{t(()=>{d.value&&(u=M(d.value),r()),window.addEventListener("resize",p)})}),n(()=>{u&&u.dispose(),window.removeEventListener("resize",p)}),(a,e)=>(c(),i("div",{ref_key:"chartRef",ref:d,style:g({height:l.height+"px"})},null,4))}},al={class:"modern-finance-dashboard"},el={class:"page-header"},sl={class:"header-content"},tl={class:"header-left"},nl={class:"header-icon"},il={class:"header-actions"},cl={class:"stats-section"},ol={class:"stats-container"},dl={class:"stat-content"},ul={class:"stat-value"},rl={class:"stat-label"},pl={class:"card-header"},vl={class:"chart-controls"},fl={class:"chart-content"},_l={class:"chart-content"},ml={class:"quick-actions"},hl={class:"action-grid"},gl={class:"card-header"},bl={class:"recent-transactions"},yl={class:"transaction-info"},wl={class:"transaction-title"},kl={class:"transaction-time"},xl={class:"card-header"},Cl={class:"money-text"},jl={class:"money-text"},Fl={class:"money-text"},Vl={class:"money-text"},zl={class:"money-text total"},Il={class:"help-content"},Tl={class:"help-section"},ql={class:"feature-item"},Ll={class:"feature-icon"},Pl={class:"feature-item"},Al={class:"feature-icon"},El={class:"feature-item"},Ol={class:"feature-icon"},Rl={class:"feature-item"},Ul={class:"feature-icon"},Zl={class:"feature-item"},Dl={class:"feature-icon"},Hl={class:"feature-item"},Kl={class:"feature-icon"},Sl={class:"help-section"},Yl={class:"help-section"},Bl={class:"revenue-sources"},Gl={class:"source-item"},Jl={class:"source-icon",style:{background:"#1890ff"}},Ml={class:"source-item"},Nl={class:"source-icon",style:{background:"#52c41a"}},Ql={class:"source-item"},Wl={class:"source-icon",style:{background:"#faad14"}},Xl={class:"source-item"},$l={class:"source-icon",style:{background:"#f5222d"}},la={class:"help-section"},aa={class:"guide-content"},ea={class:"guide-content"},sa={class:"guide-content"},ta={class:"guide-content"},na={class:"help-section"},ia={class:"risk-alerts"},ca={class:"help-section"},oa=l({__name:"FinanceDashboard",setup(l){const e=o(),t=a({total_revenue:0,total_commission:0,pending_withdrawal:0,today_revenue:0,revenue_growth:0,commission_growth:0,today_growth:0,pending_count:0}),n=a([{key:"revenue",label:"总收入",value:"¥89,234",icon:"Money",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"CaretTop",change:"+18.5%"},{key:"commission",label:"总佣金",value:"¥23,456",icon:"Wallet",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"CaretTop",change:"+12.3%"},{key:"pending",label:"待提现",value:"¥5,678",icon:"CreditCard",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"CaretTop",change:"23笔申请"},{key:"today",label:"今日收入",value:"¥1,234",icon:"TrendCharts",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"CaretTop",change:"+25.8%"}]),M=a("7d"),oa=a([]),da=a([]),ua=a(!1),ra=a(["view-data"]),pa=a([]),va=a([{metric:"总收入",color:"primary",description:"平台所有收入来源的总和",calculation:"群组收入 + 佣金收入 + 分站收入 + 其他收入",significance:"反映平台整体盈利能力和业务规模"},{metric:"总佣金",color:"success",description:"支付给分销商的佣金总额",calculation:"所有分销商佣金的累计金额",significance:"体现分销体系的活跃度和激励效果"},{metric:"待提现",color:"warning",description:"用户申请但尚未处理的提现金额",calculation:"所有待审核提现申请的金额总和",significance:"反映资金流动性需求和风险控制情况"},{metric:"今日收入",color:"info",description:"当日产生的收入总额",calculation:"当日所有收入来源的实时统计",significance:"监控日常运营状况和收入波动"}]),fa=[{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"},{label:"1年",value:"1y"}],_a=a({labels:[],datasets:[{label:"收入",data:[],borderColor:"#1890ff",backgroundColor:"rgba(24, 144, 255, 0.1)",fill:!0,tension:.4}]}),ma=a({labels:["群组收入","佣金收入","分站收入","其他收入"],datasets:[{data:[0,0,0,0],backgroundColor:["#1890ff","#52c41a","#faad14","#f5222d"]}]}),ha=a({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{callback:function(l){return"¥"+(l/1e3).toFixed(1)+"k"}}}}}),ga=a({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}),ba=async()=>{try{const{data:l}=await W();t.value=l.overview,da.value=l.details}catch(l){console.error("获取财务统计失败:",l),G.error("获取财务统计失败")}},ya=async()=>{try{const{data:l}=await N({period:M.value});_a.value=l.revenue,ma.value=l.source}catch(l){console.error("获取图表数据失败:",l)}},wa=async()=>{try{const{data:l}=await X({limit:10});oa.value=l}catch(l){console.error("获取最新交易失败:",l)}},ka=async()=>{await Promise.all([ba(),ya(),wa()]),G.success("数据刷新成功")},xa=()=>{e.push("/finance/transactions")},Ca=()=>{e.push("/finance/commission-logs")},ja=()=>{e.push("/finance/withdraw")},Fa=async()=>{try{await Q(),G.success("报表导出成功")}catch(l){G.error("导出失败")}};return s(()=>{ka()}),(l,a)=>{const e=y,s=k,t=V,o=F,G=j,N=I,Q=E,W=A,X=R,ba=Y,wa=S,Va=K,za=B;return c(),i("div",al,[d("div",el,[d("div",sl,[d("div",tl,[d("div",nl,[u(e,{size:"24"},{default:r(()=>[u(p(b))]),_:1})]),a[4]||(a[4]=d("div",{class:"header-text"},[d("h1",null,"财务管理"),d("p",null,"全面掌握平台财务状况，实时监控收支情况，高效管理资金流转")],-1))]),d("div",il,[u(s,{onClick:Fa,class:"action-btn secondary"},{default:r(()=>[u(e,null,{default:r(()=>[u(p(w))]),_:1}),a[5]||(a[5]=v(" 导出报表 ",-1))]),_:1,__:[5]}),u(s,{onClick:a[0]||(a[0]=l=>ua.value=!0),class:"action-btn secondary"},{default:r(()=>[u(e,null,{default:r(()=>[u(p(x))]),_:1}),a[6]||(a[6]=v(" 功能说明 ",-1))]),_:1,__:[6]}),u(s,{type:"primary",onClick:ka,class:"action-btn primary"},{default:r(()=>[u(e,null,{default:r(()=>[u(p(C))]),_:1}),a[7]||(a[7]=v(" 刷新数据 ",-1))]),_:1,__:[7]})])])]),d("div",cl,[d("div",ol,[(c(!0),i(f,null,_(n.value,l=>(c(),i("div",{class:"stat-card",key:l.key},[d("div",{class:"stat-icon",style:g({background:l.color})},[u(e,{size:"20"},{default:r(()=>[(c(),m(h(l.icon)))]),_:2},1024)],4),d("div",dl,[d("div",ul,z(l.value),1),d("div",rl,z(l.label),1)]),d("div",{class:P(["stat-trend",l.trend])},[u(e,{size:"14"},{default:r(()=>[(c(),m(h(l.trendIcon)))]),_:2},1024),d("span",null,z(l.change),1)],2)]))),128))])]),u(N,{gutter:20,class:"chart-section"},{default:r(()=>[u(G,{span:16},{default:r(()=>[u(o,null,{header:r(()=>[d("div",pl,[a[8]||(a[8]=d("h3",null,"收入趋势分析",-1)),d("div",vl,[u(t,null,{default:r(()=>[(c(),i(f,null,_(fa,l=>u(s,{key:l.value,type:M.value===l.value?"primary":"default",size:"small",onClick:a=>{M.value=l.value,ya()}},{default:r(()=>[v(z(l.label),1)]),_:2},1032,["type","onClick"])),64))]),_:1})])])]),default:r(()=>[d("div",fl,[u(J,{data:_a.value,options:ha.value,height:300},null,8,["data","options"])])]),_:1})]),_:1}),u(G,{span:8},{default:r(()=>[u(o,null,{header:r(()=>a[9]||(a[9]=[d("div",{class:"card-header"},[d("h3",null,"收入来源分布")],-1)])),default:r(()=>[d("div",_l,[u(ll,{data:ma.value,options:ga.value,height:300},null,8,["data","options"])])]),_:1})]),_:1})]),_:1}),u(N,{gutter:20,class:"action-section"},{default:r(()=>[u(G,{span:12},{default:r(()=>[u(o,null,{header:r(()=>a[10]||(a[10]=[d("div",{class:"card-header"},[d("h3",null,"快捷操作")],-1)])),default:r(()=>[d("div",ml,[d("div",hl,[d("div",{class:"action-item",onClick:xa},[u(e,null,{default:r(()=>[u(p(T))]),_:1}),a[11]||(a[11]=d("span",null,"交易记录",-1))]),d("div",{class:"action-item",onClick:Ca},[u(e,null,{default:r(()=>[u(p(q))]),_:1}),a[12]||(a[12]=d("span",null,"佣金明细",-1))]),d("div",{class:"action-item",onClick:ja},[u(e,null,{default:r(()=>[u(p(L))]),_:1}),a[13]||(a[13]=d("span",null,"提现管理",-1))]),d("div",{class:"action-item",onClick:Fa},[u(e,null,{default:r(()=>[u(p(w))]),_:1}),a[14]||(a[14]=d("span",null,"导出报表",-1))])])])]),_:1})]),_:1}),u(G,{span:12},{default:r(()=>[u(o,null,{header:r(()=>[d("div",gl,[a[16]||(a[16]=d("h3",null,"最新交易",-1)),u(s,{type:"text",onClick:xa},{default:r(()=>a[15]||(a[15]=[v("查看全部",-1)])),_:1,__:[15]})])]),default:r(()=>[d("div",bl,[(c(!0),i(f,null,_(oa.value,l=>(c(),i("div",{key:l.id,class:"transaction-item"},[d("div",yl,[d("div",wl,z(l.title),1),d("div",kl,z(p($)(l.created_at)),1)]),d("div",{class:P(["transaction-amount","income"===l.type?"positive":"negative"])},z("income"===l.type?"+":"-")+"¥"+z(l.amount.toFixed(2)),3)]))),128))])]),_:1})]),_:1})]),_:1}),u(o,null,{header:r(()=>[d("div",xl,[a[18]||(a[18]=d("h3",null,"财务统计详情",-1)),d("div",null,[u(s,{type:"primary",size:"small",onClick:ka},{default:r(()=>[u(e,null,{default:r(()=>[u(p(C))]),_:1}),a[17]||(a[17]=v(" 刷新数据 ",-1))]),_:1,__:[17]})])])]),default:r(()=>[u(W,{data:da.value,border:""},{default:r(()=>[u(Q,{label:"统计项目",prop:"name",width:"150"}),u(Q,{label:"今日",width:"120"},{default:r(({row:l})=>[d("span",Cl,"¥"+z(l.today.toFixed(2)),1)]),_:1}),u(Q,{label:"本周",width:"120"},{default:r(({row:l})=>[d("span",jl,"¥"+z(l.week.toFixed(2)),1)]),_:1}),u(Q,{label:"本月",width:"120"},{default:r(({row:l})=>[d("span",Fl,"¥"+z(l.month.toFixed(2)),1)]),_:1}),u(Q,{label:"本年",width:"120"},{default:r(({row:l})=>[d("span",Vl,"¥"+z(l.year.toFixed(2)),1)]),_:1}),u(Q,{label:"总计"},{default:r(({row:l})=>[d("span",zl,"¥"+z(l.total.toFixed(2)),1)]),_:1}),u(Q,{label:"增长率",width:"100"},{default:r(({row:l})=>[d("span",{class:P(l.growth>=0?"positive-growth":"negative-growth")},z(l.growth>=0?"+":"")+z(l.growth.toFixed(1))+"% ",3)]),_:1})]),_:1},8,["data"])]),_:1}),u(za,{modelValue:ua.value,"onUpdate:modelValue":a[3]||(a[3]=l=>ua.value=l),title:"财务管理工作台功能说明",width:"1000px",class:"help-dialog"},{default:r(()=>[d("div",Il,[a[53]||(a[53]=d("div",{class:"help-section"},[d("h3",null,"💰 功能概述"),d("p",null,"财务管理工作台是平台资金管理的核心系统，提供全面的财务数据统计、收支分析、提现管理等功能，帮助您实时掌握平台财务状况，做出明智的财务决策。")],-1)),d("div",Tl,[a[25]||(a[25]=d("h3",null,"🚀 核心功能模块",-1)),u(N,{gutter:20},{default:r(()=>[u(G,{span:8},{default:r(()=>[d("div",ql,[d("div",Ll,[u(e,null,{default:r(()=>[u(p(O))]),_:1})]),a[19]||(a[19]=d("div",{class:"feature-content"},[d("h4",null,"财务概览"),d("p",null,"实时显示总收入、总佣金、待提现等关键财务指标")],-1))])]),_:1}),u(G,{span:8},{default:r(()=>[d("div",Pl,[d("div",Al,[u(e,null,{default:r(()=>[u(p(O))]),_:1})]),a[20]||(a[20]=d("div",{class:"feature-content"},[d("h4",null,"数据分析"),d("p",null,"收入趋势图表、来源分布分析、增长率统计")],-1))])]),_:1}),u(G,{span:8},{default:r(()=>[d("div",El,[d("div",Ol,[u(e,null,{default:r(()=>[u(p(T))]),_:1})]),a[21]||(a[21]=d("div",{class:"feature-content"},[d("h4",null,"交易管理"),d("p",null,"查看所有交易记录，支持筛选和导出功能")],-1))])]),_:1}),u(G,{span:8},{default:r(()=>[d("div",Rl,[d("div",Ul,[u(e,null,{default:r(()=>[u(p(q))]),_:1})]),a[22]||(a[22]=d("div",{class:"feature-content"},[d("h4",null,"佣金管理"),d("p",null,"佣金明细查询、结算记录、分成比例管理")],-1))])]),_:1}),u(G,{span:8},{default:r(()=>[d("div",Zl,[d("div",Dl,[u(e,null,{default:r(()=>[u(p(L))]),_:1})]),a[23]||(a[23]=d("div",{class:"feature-content"},[d("h4",null,"提现管理"),d("p",null,"处理用户提现申请、审核提现资格、资金划转")],-1))])]),_:1}),u(G,{span:8},{default:r(()=>[d("div",Hl,[d("div",Kl,[u(e,null,{default:r(()=>[u(p(w))]),_:1})]),a[24]||(a[24]=d("div",{class:"feature-content"},[d("h4",null,"报表导出"),d("p",null,"生成财务报表、数据导出、定期统计分析")],-1))])]),_:1})]),_:1})]),d("div",Sl,[a[26]||(a[26]=d("h3",null,"📊 财务指标说明",-1)),u(W,{data:va.value,style:{width:"100%"}},{default:r(()=>[u(Q,{prop:"metric",label:"指标名称",width:"120"},{default:r(({row:l})=>[u(X,{type:l.color},{default:r(()=>[v(z(l.metric),1)]),_:2},1032,["type"])]),_:1}),u(Q,{prop:"description",label:"指标说明"}),u(Q,{prop:"calculation",label:"计算方式"}),u(Q,{prop:"significance",label:"业务意义"})]),_:1},8,["data"])]),d("div",Yl,[a[31]||(a[31]=d("h3",null,"💸 收入来源分析",-1)),d("div",Bl,[d("div",Gl,[d("div",Jl,[u(e,null,{default:r(()=>[u(p(U))]),_:1})]),a[27]||(a[27]=d("div",{class:"source-content"},[d("h4",null,"群组收入"),d("p",null,"用户加入付费群组产生的收入，是平台主要收入来源"),d("div",{class:"source-details"},[d("span",null,"占比：约60-70%"),d("span",null,"特点：稳定性高，增长潜力大")])],-1))]),d("div",Ml,[d("div",Nl,[u(e,null,{default:r(()=>[u(p(Z))]),_:1})]),a[28]||(a[28]=d("div",{class:"source-content"},[d("h4",null,"佣金收入"),d("p",null,"分销商推广产生的佣金收入，激励推广积极性"),d("div",{class:"source-details"},[d("span",null,"占比：约20-25%"),d("span",null,"特点：增长快速，波动较大")])],-1))]),d("div",Ql,[d("div",Wl,[u(e,null,{default:r(()=>[u(p(D))]),_:1})]),a[29]||(a[29]=d("div",{class:"source-content"},[d("h4",null,"分站收入"),d("p",null,"分站管理费用和服务费收入"),d("div",{class:"source-details"},[d("span",null,"占比：约10-15%"),d("span",null,"特点：稳定增长，利润率高")])],-1))]),d("div",Xl,[d("div",$l,[u(e,null,{default:r(()=>[u(p(H))]),_:1})]),a[30]||(a[30]=d("div",{class:"source-content"},[d("h4",null,"其他收入"),d("p",null,"广告收入、增值服务等其他收入来源"),d("div",{class:"source-details"},[d("span",null,"占比：约5-10%"),d("span",null,"特点：多样化，补充性收入")])],-1))])])]),a[54]||(a[54]=d("div",{class:"help-section"},[d("h3",null,"🏦 提现管理流程"),d("div",{class:"withdrawal-process"},[d("div",{class:"process-step"},[d("div",{class:"step-number"},"1"),d("div",{class:"step-content"},[d("h4",null,"用户申请"),d("p",null,"用户在前台提交提现申请，填写提现金额和收款信息")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"2"),d("div",{class:"step-content"},[d("h4",null,"系统审核"),d("p",null,"系统自动检查用户余额、提现限额、实名认证等条件")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"3"),d("div",{class:"step-content"},[d("h4",null,"人工审核"),d("p",null,"管理员审核提现申请，核实用户身份和提现合规性")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"4"),d("div",{class:"step-content"},[d("h4",null,"资金处理"),d("p",null,"审核通过后，系统自动或手动处理资金转账")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"5"),d("div",{class:"step-content"},[d("h4",null,"完成提现"),d("p",null,"资金到账，更新提现状态，发送通知给用户")])])])],-1)),d("div",la,[a[40]||(a[40]=d("h3",null,"📝 操作指南",-1)),u(Va,{modelValue:ra.value,"onUpdate:modelValue":a[1]||(a[1]=l=>ra.value=l)},{default:r(()=>[u(wa,{title:"如何查看财务数据？",name:"view-data"},{default:r(()=>[d("div",aa,[a[33]||(a[33]=d("ol",null,[d("li",null,"在工作台首页查看核心财务指标卡片"),d("li",null,'查看"收入趋势分析"图表了解收入变化'),d("li",null,'在"收入来源分布"图表中查看收入结构'),d("li",null,"在财务统计表格中查看详细的分时段数据"),d("li",null,"可以切换时间段查看不同期间的数据")],-1)),u(ba,{type:"info",closable:!1},{default:r(()=>a[32]||(a[32]=[v(" 💡 提示：数据每小时更新一次，增长率基于同期对比计算 ",-1)])),_:1,__:[32]})])]),_:1}),u(wa,{title:"如何处理提现申请？",name:"withdrawal-process"},{default:r(()=>[d("div",ea,[a[35]||(a[35]=d("ol",null,[d("li",null,'点击"提现管理"进入提现管理页面'),d("li",null,"查看待审核的提现申请列表"),d("li",null,'点击"详情"查看申请人信息和提现详情'),d("li",null,"核实用户身份、余额、提现合规性"),d("li",null,'点击"通过"或"拒绝"处理申请'),d("li",null,"填写审核意见并确认操作")],-1)),u(ba,{type:"warning",closable:!1},{default:r(()=>a[34]||(a[34]=[v(" ⚠️ 注意：提现审核要严格核实用户身份，防范资金风险 ",-1)])),_:1,__:[34]})])]),_:1}),u(wa,{title:"如何导出财务报表？",name:"export-report"},{default:r(()=>[d("div",sa,[a[37]||(a[37]=d("ol",null,[d("li",null,'点击"导出报表"按钮'),d("li",null,"选择报表类型（收入报表、佣金报表、提现报表等）"),d("li",null,"设置导出时间范围"),d("li",null,"选择导出格式（Excel、PDF等）"),d("li",null,'点击"确认导出"生成报表'),d("li",null,"下载生成的报表文件")],-1)),u(ba,{type:"success",closable:!1},{default:r(()=>a[36]||(a[36]=[v(" ✅ 说明：报表支持多种格式，可用于财务分析和对账 ",-1)])),_:1,__:[36]})])]),_:1}),u(wa,{title:"如何分析收入趋势？",name:"revenue-analysis"},{default:r(()=>[d("div",ta,[a[39]||(a[39]=d("ol",null,[d("li",null,"查看收入趋势图表，观察收入变化规律"),d("li",null,"切换不同时间段（7天、30天、90天、1年）"),d("li",null,"对比不同时期的收入水平和增长率"),d("li",null,"分析收入来源分布，识别主要收入驱动因素"),d("li",null,"结合业务活动分析收入波动原因")],-1)),u(ba,{type:"info",closable:!1},{default:r(()=>a[38]||(a[38]=[v(" 💡 建议：定期分析收入趋势，制定相应的运营策略 ",-1)])),_:1,__:[38]})])]),_:1})]),_:1},8,["modelValue"])]),d("div",na,[a[47]||(a[47]=d("h3",null,"⚠️ 风险提示与注意事项",-1)),d("div",ia,[u(ba,{type:"error",closable:!1,style:{"margin-bottom":"15px"}},{title:r(()=>a[41]||(a[41]=[d("strong",null,"资金安全风险",-1)])),default:r(()=>[a[42]||(a[42]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"严格审核提现申请，防范虚假提现和洗钱风险"),d("li",null,"定期核对账务，确保资金流水准确无误"),d("li",null,"建立资金监控机制，及时发现异常交易")],-1))]),_:1,__:[42]}),u(ba,{type:"warning",closable:!1,style:{"margin-bottom":"15px"}},{title:r(()=>a[43]||(a[43]=[d("strong",null,"合规风险",-1)])),default:r(()=>[a[44]||(a[44]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"确保所有财务操作符合相关法律法规"),d("li",null,"完善财务记录，保留必要的凭证和文档"),d("li",null,"定期进行财务审计，确保合规经营")],-1))]),_:1,__:[44]}),u(ba,{type:"info",closable:!1},{title:r(()=>a[45]||(a[45]=[d("strong",null,"操作建议",-1)])),default:r(()=>[a[46]||(a[46]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"建议每日查看财务数据，及时发现异常情况"),d("li",null,"定期备份财务数据，防止数据丢失"),d("li",null,"建立多人审核机制，降低操作风险")],-1))]),_:1,__:[46]})])]),d("div",ca,[a[52]||(a[52]=d("h3",null,"❓ 常见问题",-1)),u(Va,{modelValue:pa.value,"onUpdate:modelValue":a[2]||(a[2]=l=>pa.value=l)},{default:r(()=>[u(wa,{title:"财务数据多久更新一次？",name:"faq1"},{default:r(()=>a[48]||(a[48]=[d("p",null,'财务数据每小时自动更新一次，重要指标如收入、佣金等实时更新。您也可以点击"刷新数据"按钮手动更新。',-1)])),_:1,__:[48]}),u(wa,{title:"提现申请的处理时间是多久？",name:"faq2"},{default:r(()=>a[49]||(a[49]=[d("p",null,"一般情况下，提现申请在1-3个工作日内处理完成。具体时间取决于申请金额、用户等级和审核复杂度。",-1)])),_:1,__:[49]}),u(wa,{title:"如何设置提现限额？",name:"faq3"},{default:r(()=>a[50]||(a[50]=[d("p",null,"可以在系统设置中配置不同用户等级的提现限额，包括单次提现限额、日提现限额和月提现限额。",-1)])),_:1,__:[50]}),u(wa,{title:"财务报表可以定期自动生成吗？",name:"faq4"},{default:r(()=>a[51]||(a[51]=[d("p",null,"系统支持定期自动生成财务报表功能，可以设置每日、每周或每月自动生成并发送到指定邮箱。",-1)])),_:1,__:[51]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-af89d98c"]]);export{oa as default};
