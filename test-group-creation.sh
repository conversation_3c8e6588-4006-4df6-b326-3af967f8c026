#!/bin/bash

# 群组创建功能测试脚本
# 使用前请确保设置了正确的TOKEN

API_BASE="http://localhost:8000/api"
TOKEN="your-jwt-token-here"

echo "========================================="
echo "群组创建功能优化测试"
echo "========================================="

# 1. 测试基础创建
echo -e "\n[1] 测试基础群组创建..."
curl -X POST "${API_BASE}/wechat-groups" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试优化群组xxx",
    "price": 99.00,
    "member_limit": 200,
    "category": "tech",
    "owner_name": "测试群主",
    "owner_avatar": "/images/avatar.jpg",
    "rules": "1. 友好交流\n2. 禁止广告\n3. 积极分享",
    "introduction": "这是一个技术交流群组",
    "announcement": "欢迎加入技术交流群",
    "virtual_members": 150,
    "keywords": "技术,编程,开发",
    "status": "active",
    "payment_methods": ["wechat", "alipay"]
  }'

echo -e "\n\n[2] 测试带模板的群组创建..."
curl -X POST "${API_BASE}/wechat-groups" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "模板群组xxx",
    "price": 199.00,
    "member_limit": 300,
    "category": "business",
    "owner_name": "商业群主",
    "template_id": 1,
    "rules": "群规内容",
    "status": "active"
  }'

echo -e "\n\n[3] 测试验证错误处理..."
curl -X POST "${API_BASE}/wechat-groups" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "price": -10
  }'

echo -e "\n\n[4] 测试获取群组列表..."
curl -X GET "${API_BASE}/wechat-groups?limit=10" \
  -H "Authorization: Bearer ${TOKEN}"

echo -e "\n\n========================================="
echo "测试完成！"
echo "请检查上述结果是否符合预期。"
echo "========================================="