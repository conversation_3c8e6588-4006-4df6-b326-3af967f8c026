/**
 * 模拟客户管理API - 用于开发环境测试
 */

// 模拟数据
const mockCustomers = [
  {
    id: 1,
    name: '张三',
    phone: '13800138001',
    wechat: 'zhangsan001',
    level: 'A',
    level_text: 'A级客户',
    status: 'active',
    status_text: '活跃',
    company: '阿里巴巴集团',
    total_spent: 15800,
    order_count: 12,
    last_contact_at: new Date(Date.now() - 86400000 * 2),
    next_follow_up: new Date(Date.now() + 86400000 * 3),
    follow_up_days: 3,
    tags: ['高价值', '决策者'],
    source: 'referral',
    created_at: new Date(Date.now() - 86400000 * 30)
  },
  {
    id: 2,
    name: '李四',
    phone: '13800138002',
    wechat: 'lisi002',
    level: 'B',
    level_text: 'B级客户',
    status: 'active',
    status_text: '活跃',
    company: '腾讯科技',
    total_spent: 8600,
    order_count: 8,
    last_contact_at: new Date(Date.now() - 86400000 * 5),
    next_follow_up: new Date(Date.now() + 86400000 * 7),
    follow_up_days: 7,
    tags: ['技术导向'],
    source: 'advertisement',
    created_at: new Date(Date.now() - 86400000 * 45)
  },
  {
    id: 3,
    name: '王五',
    phone: '13800138003',
    wechat: 'wangwu003',
    level: 'C',
    level_text: 'C级客户',
    status: 'potential',
    status_text: '潜在',
    company: '字节跳动',
    total_spent: 3200,
    order_count: 3,
    last_contact_at: new Date(Date.now() - 86400000 * 10),
    next_follow_up: new Date(Date.now() - 86400000 * 1),
    follow_up_days: -1,
    tags: ['价格敏感'],
    source: 'social_media',
    created_at: new Date(Date.now() - 86400000 * 60)
  },
  {
    id: 4,
    name: '赵六',
    phone: '13800138004',
    wechat: 'zhaoliu004',
    level: 'B',
    level_text: 'B级客户',
    status: 'inactive',
    status_text: '不活跃',
    company: '美团',
    total_spent: 5400,
    order_count: 6,
    last_contact_at: new Date(Date.now() - 86400000 * 20),
    next_follow_up: new Date(Date.now() + 86400000 * 1),
    follow_up_days: 1,
    tags: ['需要激活'],
    source: 'direct',
    created_at: new Date(Date.now() - 86400000 * 90)
  },
  {
    id: 5,
    name: '孙七',
    phone: '13800138005',
    wechat: 'sunqi005',
    level: 'D',
    level_text: 'D级客户',
    status: 'lost',
    status_text: '流失',
    company: '小米科技',
    total_spent: 800,
    order_count: 2,
    last_contact_at: new Date(Date.now() - 86400000 * 100),
    next_follow_up: null,
    follow_up_days: null,
    tags: ['已流失'],
    source: 'other',
    created_at: new Date(Date.now() - 86400000 * 120)
  }
]

const mockStats = {
  total_customers: 156,
  active_customers: 89,
  potential_customers: 34,
  need_follow_up: 23
}

const mockTags = ['高价值', '决策者', '技术导向', '价格敏感', '需要激活', '已流失', '新客户', '老客户', 'VIP']

// 模拟API延迟
const delay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

export const mockCustomerApi = {
  /**
   * 获取客户列表
   */
  async getList(params = {}) {
    await delay()
    
    let filteredCustomers = [...mockCustomers]
    
    // 关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.name.toLowerCase().includes(keyword) ||
        customer.phone.includes(keyword) ||
        customer.wechat.toLowerCase().includes(keyword)
      )
    }
    
    // 状态筛选
    if (params.status) {
      filteredCustomers = filteredCustomers.filter(customer => customer.status === params.status)
    }
    
    // 等级筛选
    if (params.level) {
      filteredCustomers = filteredCustomers.filter(customer => customer.level === params.level)
    }
    
    // 来源筛选
    if (params.source) {
      filteredCustomers = filteredCustomers.filter(customer => customer.source === params.source)
    }
    
    // 标签筛选
    if (params.tag) {
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.tags && customer.tags.includes(params.tag)
      )
    }
    
    // 需要跟进筛选
    if (params.need_follow_up) {
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.next_follow_up && customer.follow_up_days <= 7
      )
    }
    
    // 分页
    const page = params.page || 1
    const limit = params.limit || 20
    const start = (page - 1) * limit
    const end = start + limit
    const paginatedCustomers = filteredCustomers.slice(start, end)
    
    return {
      data: {
        data: paginatedCustomers,
        total: filteredCustomers.length,
        current_page: page,
        per_page: limit,
        last_page: Math.ceil(filteredCustomers.length / limit)
      }
    }
  },

  /**
   * 获取客户详情
   */
  async getDetail(id) {
    await delay()
    const customer = mockCustomers.find(c => c.id === parseInt(id))
    if (!customer) {
      throw new Error('客户不存在')
    }
    return { data: customer }
  },

  /**
   * 创建客户
   */
  async create(data) {
    await delay()
    const newCustomer = {
      id: Math.max(...mockCustomers.map(c => c.id)) + 1,
      ...data,
      total_spent: 0,
      order_count: 0,
      status: 'potential',
      status_text: '潜在',
      level_text: data.level + '级客户',
      created_at: new Date(),
      last_contact_at: null,
      next_follow_up: null,
      follow_up_days: null,
      tags: data.tags || []
    }
    mockCustomers.push(newCustomer)
    mockStats.total_customers++
    mockStats.potential_customers++
    return { data: newCustomer }
  },

  /**
   * 更新客户
   */
  async update(id, data) {
    await delay()
    const index = mockCustomers.findIndex(c => c.id === parseInt(id))
    if (index === -1) {
      throw new Error('客户不存在')
    }
    
    mockCustomers[index] = {
      ...mockCustomers[index],
      ...data,
      level_text: data.level ? data.level + '级客户' : mockCustomers[index].level_text,
      status_text: data.status ? getStatusText(data.status) : mockCustomers[index].status_text
    }
    
    return { data: mockCustomers[index] }
  },

  /**
   * 删除客户
   */
  async delete(id) {
    await delay()
    const index = mockCustomers.findIndex(c => c.id === parseInt(id))
    if (index === -1) {
      throw new Error('客户不存在')
    }
    
    const deletedCustomer = mockCustomers.splice(index, 1)[0]
    mockStats.total_customers--
    
    // 更新对应状态统计
    if (deletedCustomer.status === 'active') mockStats.active_customers--
    if (deletedCustomer.status === 'potential') mockStats.potential_customers--
    if (deletedCustomer.next_follow_up && deletedCustomer.follow_up_days <= 7) mockStats.need_follow_up--
    
    return { data: { message: '删除成功' } }
  },

  /**
   * 获取客户统计
   */
  async getStats() {
    await delay()
    return { data: mockStats }
  },

  /**
   * 添加跟进记录
   */
  async addFollowUp(customerId, data) {
    await delay()
    const customer = mockCustomers.find(c => c.id === parseInt(customerId))
    if (!customer) {
      throw new Error('客户不存在')
    }
    
    // 更新客户的跟进信息
    customer.last_contact_at = new Date()
    customer.next_follow_up = new Date(data.next_follow_up)
    customer.follow_up_days = Math.ceil((new Date(data.next_follow_up) - new Date()) / (1000 * 60 * 60 * 24))
    
    const followUp = {
      id: Date.now(),
      customer_id: customerId,
      content: data.content,
      method: data.method,
      next_follow_up: data.next_follow_up,
      created_at: new Date(),
      ...data
    }
    
    return { data: followUp }
  },

  /**
   * 获取跟进记录
   */
  async getFollowUps(customerId, params = {}) {
    await delay()
    // 模拟跟进记录数据
    const followUps = [
      {
        id: 1,
        customer_id: customerId,
        content: '电话沟通了解需求，客户对产品很感兴趣',
        method: 'phone',
        created_at: new Date(Date.now() - 86400000 * 3),
        next_follow_up: new Date(Date.now() + 86400000 * 7)
      },
      {
        id: 2,
        customer_id: customerId,
        content: '发送了产品资料和报价单',
        method: 'wechat',
        created_at: new Date(Date.now() - 86400000 * 7),
        next_follow_up: new Date(Date.now() - 86400000 * 3)
      }
    ]
    
    return {
      data: {
        data: followUps,
        total: followUps.length,
        current_page: 1,
        per_page: 20
      }
    }
  },

  /**
   * 批量更新客户状态
   */
  async batchUpdateStatus(data) {
    await delay()
    const { customer_ids, status } = data
    
    customer_ids.forEach(id => {
      const customer = mockCustomers.find(c => c.id === id)
      if (customer) {
        customer.status = status
        customer.status_text = getStatusText(status)
      }
    })
    
    return { data: { message: '批量更新成功' } }
  },

  /**
   * 导出客户数据
   */
  async export(params = {}) {
    await delay()
    // 模拟导出功能
    const blob = new Blob(['客户数据导出文件'], { type: 'application/vnd.ms-excel' })
    return { data: blob }
  },

  /**
   * 获取需要跟进的客户
   */
  async getNeedFollowUp(params = {}) {
    await delay()
    const needFollowUpCustomers = mockCustomers.filter(customer => 
      customer.next_follow_up && customer.follow_up_days <= 7
    )
    
    return {
      data: {
        data: needFollowUpCustomers,
        total: needFollowUpCustomers.length
      }
    }
  },

  /**
   * 获取客户标签
   */
  async getTags() {
    await delay()
    return { data: mockTags }
  }
}

// 辅助函数
function getStatusText(status) {
  const statusMap = {
    'active': '活跃',
    'inactive': '不活跃',
    'potential': '潜在',
    'lost': '流失'
  }
  return statusMap[status] || '未知'
}

export default mockCustomerApi