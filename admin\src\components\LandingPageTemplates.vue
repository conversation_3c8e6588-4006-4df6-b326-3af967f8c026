<template>
  <div class="landing-page-templates">
    <div class="templates-header">
      <h2>选择落地页模板</h2>
      <p>选择适合您群组的专业模板，快速创建高转化率的落地页</p>
    </div>

    <!-- 模板网格 -->
    <div class="templates-grid">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-card"
        :class="{ 'selected': selectedTemplate?.id === template.id }"
        @click="selectTemplate(template)"
      >
        <!-- 模板预览图 -->
        <div class="template-preview">
          <img :src="template.preview" :alt="template.name" @error="handleImageError" />
          <div class="template-overlay">
            <div class="overlay-actions">
              <el-button size="small" @click.stop="previewTemplateHandler(template)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" type="primary" @click.stop="useTemplate(template)">
                <el-icon><Check /></el-icon>
                使用
              </el-button>
            </div>
          </div>

          <!-- 模板评分和下载量 -->
          <div class="template-stats">
            <div class="stat-item">
              <el-icon><Star /></el-icon>
              <span>{{ template.rating }}</span>
            </div>
            <div class="stat-item">
              <el-icon><Download /></el-icon>
              <span>{{ template.downloads }}</span>
            </div>
          </div>
        </div>

        <!-- 模板信息 -->
        <div class="template-info">
          <div class="template-header">
            <h3 class="template-name">{{ template.name }}</h3>
            <el-tag :type="getIndustryTagType(template.industry)" size="small">
              {{ template.industry }}
            </el-tag>
          </div>

          <p class="template-description">{{ template.description }}</p>

          <!-- 特色功能 -->
          <div class="template-features">
            <h4>特色功能</h4>
            <ul>
              <li v-for="feature in template.features.slice(0, 3)" :key="feature">
                <el-icon><Check /></el-icon>
                {{ feature }}
              </li>
            </ul>
          </div>

          <div class="template-meta">
            <div class="template-tags">
              <el-tag
                v-for="tag in template.tags"
                :key="tag"
                size="small"
                effect="plain"
              >
                {{ tag }}
              </el-tag>
            </div>

            <div class="template-suitable">
              <span class="suitable-label">适用于：</span>
              <span class="suitable-text">{{ template.suitable }}</span>
            </div>
          </div>
        </div>

        <!-- 选中标识 -->
        <div v-if="selectedTemplate?.id === template.id" class="selected-badge">
          <el-icon><Check /></el-icon>
        </div>
      </div>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="`${currentPreviewTemplate?.name} - 模板预览`"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="template-preview-content">
        <div class="preview-device">
          <div class="device-frame">
            <div v-if="!currentPreviewTemplate" class="preview-loading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <p>加载预览中...</p>
            </div>
            <iframe
              v-else
              :src="getPreviewUrl(currentPreviewTemplate, props.userData)"
              frameborder="0"
              width="100%"
              height="100%"
              @load="onIframeLoad"
              @error="onIframeError"
            ></iframe>
          </div>
        </div>

        <div class="preview-info">
          <div class="template-meta-info">
            <h3>{{ currentPreviewTemplate?.name }}</h3>
            <el-tag :type="getIndustryTagType(currentPreviewTemplate?.industry)" size="small">
              {{ currentPreviewTemplate?.industry }}
            </el-tag>
          </div>

          <p class="template-desc">{{ currentPreviewTemplate?.description }}</p>

          <div class="template-stats">
            <div class="stat-item">
              <el-icon><Star /></el-icon>
              <span>{{ currentPreviewTemplate?.rating }} 评分</span>
            </div>
            <div class="stat-item">
              <el-icon><Download /></el-icon>
              <span>{{ currentPreviewTemplate?.downloads }} 下载</span>
            </div>
          </div>

          <div class="template-features-preview">
            <h4>特色功能</h4>
            <ul>
              <li v-for="feature in currentPreviewTemplate?.features?.slice(0, 4)" :key="feature">
                <el-icon><Check /></el-icon>
                {{ feature }}
              </li>
            </ul>
          </div>

          <div class="template-suitable-info">
            <h4>适用场景</h4>
            <p>{{ currentPreviewTemplate?.suitable }}</p>
          </div>

          <div class="preview-actions">
            <el-button @click="previewVisible = false">取消</el-button>
            <el-button type="primary" @click="useTemplate(currentPreviewTemplate)">
              <el-icon><Check /></el-icon>
              使用此模板
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Check, Star, Download, Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  },
  userData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'template-selected'])

// 响应式数据
const selectedTemplate = ref(props.modelValue)
const previewVisible = ref(false)
const currentPreviewTemplate = ref(null)
const previewUrls = ref(new Set()) // 存储生成的blob URLs用于清理

// 模板数据
const templates = ref([
  {
    id: 'edu-001',
    name: '专业教育模板',
    description: '适合在线教育、培训机构使用的专业模板，简洁大方的设计风格，突出教育价值',
    category: 'education',
    preview: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
    rating: 4.8,
    downloads: 2340,
    tags: ['教育', '专业', '简洁', '培训'],
    features: [
      '清晰的课程展示',
      '师资力量介绍',
      '学员评价展示',
      '在线报名功能',
      '课程大纲预览'
    ],
    config: {
      primaryColor: '#1890ff',
      layout: 'professional',
      showVideo: true,
      showTestimonials: true
    },
    industry: '教育培训',
    suitable: '在线教育、培训机构、知识付费'
  },
  {
    id: 'bus-001',
    name: '商务精英模板',
    description: '高端商务风格，适合企业交流群使用，体现专业性和权威性',
    category: 'business',
    preview: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop',
    rating: 4.9,
    downloads: 1890,
    tags: ['商务', '高端', '专业', '企业'],
    features: [
      '企业品牌展示',
      '团队介绍模块',
      '服务案例展示',
      '联系方式突出',
      '行业资质认证'
    ],
    config: {
      primaryColor: '#722ed1',
      layout: 'corporate',
      showVideo: false,
      showTestimonials: true
    },
    industry: '商务服务',
    suitable: '企业交流、商务合作、行业协会'
  },
  {
    id: 'ent-001',
    name: '活力娱乐模板',
    description: '年轻活力的设计风格，适合娱乐社交群，色彩丰富，互动性强',
    category: 'entertainment',
    preview: 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=400&h=300&fit=crop',
    rating: 4.7,
    downloads: 3120,
    tags: ['娱乐', '活力', '年轻', '社交'],
    features: [
      '动态背景效果',
      '互动元素丰富',
      '社交分享功能',
      '活动展示模块',
      '趣味动画效果'
    ],
    config: {
      primaryColor: '#f5222d',
      layout: 'creative',
      showVideo: true,
      showTestimonials: false
    },
    industry: '娱乐社交',
    suitable: '娱乐群组、兴趣社区、粉丝群'
  },
  {
    id: 'fin-001',
    name: '金融理财模板',
    description: '专业的金融理财模板，突出安全性和专业性，适合投资理财群组',
    category: 'finance',
    preview: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop',
    rating: 4.6,
    downloads: 1560,
    tags: ['金融', '理财', '投资', '专业'],
    features: [
      '收益数据展示',
      '风险提示模块',
      '专家团队介绍',
      '投资案例分析',
      '安全保障说明'
    ],
    config: {
      primaryColor: '#52c41a',
      layout: 'financial',
      showVideo: false,
      showTestimonials: true
    },
    industry: '金融投资',
    suitable: '理财群组、投资交流、金融服务'
  },
  {
    id: 'health-001',
    name: '健康养生模板',
    description: '清新自然的健康养生模板，适合健康、医疗、养生类群组',
    category: 'health',
    preview: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
    rating: 4.5,
    downloads: 980,
    tags: ['健康', '养生', '医疗', '自然'],
    features: [
      '健康知识展示',
      '专家医生介绍',
      '养生方案推荐',
      '用户案例分享',
      '健康测评工具'
    ],
    config: {
      primaryColor: '#13c2c2',
      layout: 'wellness',
      showVideo: true,
      showTestimonials: true
    },
    industry: '健康医疗',
    suitable: '健康群组、医疗咨询、养生交流'
  },
  {
    id: 'tech-001',
    name: '科技创新模板',
    description: '现代科技风格模板，适合IT、科技、创新类群组，设计感强',
    category: 'technology',
    preview: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    rating: 4.8,
    downloads: 2100,
    tags: ['科技', '创新', 'IT', '现代'],
    features: [
      '技术栈展示',
      '项目案例介绍',
      '团队技能展示',
      '创新成果展示',
      '技术交流模块'
    ],
    config: {
      primaryColor: '#2f54eb',
      layout: 'tech',
      showVideo: true,
      showTestimonials: false
    },
    industry: '科技创新',
    suitable: 'IT群组、技术交流、创业团队'
  }
])

// 方法
const selectTemplate = (template) => {
  selectedTemplate.value = template
  emit('update:modelValue', template)
  emit('template-selected', template)
}

const previewTemplateHandler = (template) => {
  currentPreviewTemplate.value = template
  previewVisible.value = true

  // 添加消息监听器，处理iframe中的交互
  nextTick(() => {
    const handleMessage = (event) => {
      if (event.data === 'select-template') {
        useTemplate(template)
        window.removeEventListener('message', handleMessage)
      }
    }
    window.addEventListener('message', handleMessage)
  })
}

const useTemplate = (template) => {
  selectTemplate(template)
  previewVisible.value = false
  ElMessage.success(`已选择模板：${template.name}`)
}

const onIframeLoad = () => {
  console.log('预览页面加载完成')
}

const onIframeError = () => {
  ElMessage.error('预览页面加载失败，请重试')
}

const getPreviewUrl = (template, userData = null) => {
  // 生成动态预览页面的blob URL
  const previewHtml = generatePreviewHtml(template, userData)
  const blob = new Blob([previewHtml], { type: 'text/html' })
  const url = URL.createObjectURL(blob)

  // 跟踪URL用于后续清理
  previewUrls.value.add(url)

  return url
}

const handleImageError = (event) => {
  // 使用占位图片
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xNzUgMTIwSDIyNVYxODBIMTc1VjEyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDEwQzE0LjQ3NzIgMTAgMTAgMTQuNDc3MiAxMCAyMEMxMCAyNS41MjI4IDE0LjQ3NzIgMzAgMjAgMzBDMjUuNTIyOCAzMCAzMCAyNS41MjI4IDMwIDIwQzMwIDE0LjQ3NzIgMjUuNTIyOCAxMCAyMCAxMFpNMjAgMjZDMTcuNzkwOSAyNiAxNiAyNC4yMDkxIDE2IDIyQzE2IDE5Ljc5MDkgMTcuNzkwOSAxOCAyMCAxOEMyMi4yMDkxIDE4IDI0IDE5Ljc5MDkgMjQgMjJDMjQgMjQuMjA5MSAyMi4yMDkxIDI2IDIwIDI2WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4KPC9zdmc+'
}

const generatePreviewHtml = (template, userData = null) => {
  const primaryColor = template.config?.primaryColor || '#409eff'
  const layout = template.config?.layout || 'default'

  // 生成用户图片轮播HTML
  const generateImageCarousel = (images) => {
    if (!images || !images.length) return ''

    return `
      <div class="user-image-carousel">
        <div class="carousel-container">
          <div class="carousel-slides">
            ${images.map((img, index) => `
              <div class="carousel-slide ${index === 0 ? 'active' : ''}">
                <img src="${img.url}" alt="${img.title || '用户图片'}" />
                ${img.title ? `<div class="slide-caption">${img.title}</div>` : ''}
              </div>
            `).join('')}
          </div>
          ${images.length > 1 ? `
            <div class="carousel-controls">
              <button class="carousel-btn prev" onclick="prevSlide()">‹</button>
              <button class="carousel-btn next" onclick="nextSlide()">›</button>
            </div>
            <div class="carousel-indicators">
              ${images.map((_, index) => `
                <button class="indicator ${index === 0 ? 'active' : ''}" onclick="goToSlide(${index})"></button>
              `).join('')}
            </div>
          ` : ''}
        </div>
      </div>
    `
  }

  // 生成介绍视频HTML
  const generateVideoSection = (videoData) => {
    if (!videoData || !videoData.url) return ''

    return `
      <div class="user-video-section">
        <div class="video-container">
          <video controls poster="${videoData.poster || ''}" style="width: 100%; max-height: 300px; border-radius: 8px;">
            <source src="${videoData.url}" type="video/mp4">
            您的浏览器不支持视频播放。
          </video>
          ${videoData.title ? `<h3 class="video-title">${videoData.title}</h3>` : ''}
          ${videoData.description ? `<p class="video-description">${videoData.description}</p>` : ''}
        </div>
      </div>
    `
  }

  // 生成城市定位HTML
  const generateLocationSection = (userData) => {
    if (userData.auto_city_replace !== 1 || !userData.current_city) return ''

    const strategyMap = {
      'auto': '智能判断',
      'prefix': '前缀模式（城市·标题）',
      'suffix': '后缀模式（标题·城市）',
      'natural': '自然插入',
      'none': '不插入'
    }

    return `
      <div class="user-location-section">
        <div class="location-container">
          <div class="location-header">
            <span class="location-icon">📍</span>
            <span class="location-text">当前定位：${userData.current_city}</span>
            <span class="location-badge">智能定位</span>
          </div>
          <div class="location-strategy">
            <span class="strategy-label">定位策略：</span>
            <span class="strategy-value">${strategyMap[userData.city_insert_strategy] || '智能判断'}</span>
          </div>
          ${userData.original_title !== userData.title ? `
            <div class="title-transformation">
              <div class="original">原始：${userData.original_title}</div>
              <div class="arrow">↓</div>
              <div class="processed">处理后：${userData.title}</div>
            </div>
          ` : ''}
        </div>
      </div>
    `
  }

  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${template.name} - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: ${getLayoutBackground(layout)};
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, ${primaryColor}15, ${primaryColor}05);
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            color: ${primaryColor};
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }

        .price {
            font-size: 28px;
            font-weight: 700;
            color: #f56c6c;
            margin-bottom: 20px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
        }

        .stat-item {
            font-size: 14px;
            color: #999;
        }

        .features {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .features h3 {
            font-size: 20px;
            color: ${primaryColor};
            margin-bottom: 20px;
            text-align: center;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            list-style: none;
        }

        .features-list li {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .features-list li::before {
            content: '✓';
            color: ${primaryColor};
            font-weight: bold;
            font-size: 16px;
        }

        .description {
            background: white;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            margin-bottom: 30px;
        }

        .description h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
        }

        .description p {
            color: #666;
            line-height: 1.8;
        }

        .cta-button {
            text-align: center;
            padding: 30px 0;
        }

        .btn {
            background: ${primaryColor};
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: ${adjustColor(primaryColor, -20)};
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .tags {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tag {
            background: ${primaryColor}20;
            color: ${primaryColor};
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .industry-badge {
            text-align: center;
            margin-bottom: 20px;
        }

        .industry-badge span {
            background: ${primaryColor};
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        /* 用户图片轮播样式 */
        .user-image-carousel {
            margin: 30px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .carousel-container {
            position: relative;
        }

        .carousel-slides {
            position: relative;
            height: 300px;
            overflow: hidden;
        }

        .carousel-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .carousel-slide.active {
            opacity: 1;
        }

        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .slide-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 20px;
            font-size: 16px;
            font-weight: 500;
        }

        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
        }

        .carousel-btn {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: white;
            transform: scale(1.2);
        }

        /* 用户视频样式 */
        .user-video-section {
            margin: 30px 0;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .video-container {
            text-align: center;
        }

        .video-title {
            font-size: 20px;
            color: ${primaryColor};
            margin: 15px 0 10px 0;
        }

        .video-description {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        /* 城市定位样式 */
        .user-location-section {
            margin: 30px 0;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #bae6fd;
        }

        .location-container {
            text-align: center;
        }

        .location-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .location-icon {
            font-size: 18px;
        }

        .location-text {
            font-size: 18px;
            font-weight: 600;
            color: #0c4a6e;
        }

        .location-badge {
            background: ${primaryColor};
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .location-strategy {
            margin-bottom: 15px;
            font-size: 14px;
            color: #64748b;
        }

        .strategy-value {
            color: #0c4a6e;
            font-weight: 500;
        }

        .title-transformation {
            background: white;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
        }

        .original,
        .processed {
            margin: 5px 0;
        }

        .original {
            color: #64748b;
        }

        .processed {
            color: ${primaryColor};
            font-weight: 600;
        }

        .arrow {
            color: #0ea5e9;
            font-size: 16px;
            margin: 8px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .title {
                font-size: 24px;
            }

            .stats {
                flex-direction: column;
                gap: 10px;
            }

            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="industry-badge">
                <span>${template.industry}</span>
            </div>
            <h1 class="title">${template.name}</h1>
            <p class="subtitle">${template.description}</p>
            <div class="price">¥99.00</div>
            <div class="stats">
                <div class="stat-item">⭐ ${template.rating} 评分</div>
                <div class="stat-item">📥 ${template.downloads} 下载</div>
                <div class="stat-item">👥 1000+ 用户</div>
            </div>
            <div class="tags">
                ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
        </div>

        <!-- 城市定位显示 -->
        ${userData?.auto_city_replace === 1 ? generateLocationSection(userData) : ''}

        <!-- 用户自定义图片轮播 -->
        ${userData?.media_images ? generateImageCarousel(userData.media_images) : ''}

        <!-- 用户介绍视频 -->
        ${userData?.intro_video_url ? generateVideoSection({
          url: userData.intro_video_url,
          poster: userData.intro_video_poster,
          title: userData.intro_video_title,
          description: userData.intro_video_desc
        }) : ''}

        <div class="features">
            <h3>🎯 核心特色功能</h3>
            <ul class="features-list">
                ${template.features.map(feature => `<li>${feature}</li>`).join('')}
            </ul>
        </div>

        <div class="description">
            <h3>📋 详细介绍</h3>
            <p>${template.description}</p>
            <br>
            <p><strong>适用场景：</strong>${template.suitable}</p>
            <p><strong>设计风格：</strong>${getLayoutDescription(layout)}</p>
            <p><strong>主色调：</strong><span style="color: ${primaryColor}; font-weight: bold;">${primaryColor}</span></p>
        </div>

        <div class="cta-button">
            <a href="#" class="btn" onclick="parent.postMessage('select-template', '*')">
                立即使用此模板
            </a>
        </div>
    </div>

    <script>
        // 图片轮播控制
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const indicators = document.querySelectorAll('.indicator');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
            currentSlide = index;
        }

        function nextSlide() {
            const nextIndex = (currentSlide + 1) % slides.length;
            showSlide(nextIndex);
        }

        function prevSlide() {
            const prevIndex = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(prevIndex);
        }

        function goToSlide(index) {
            showSlide(index);
        }

        // 自动轮播
        if (slides.length > 1) {
            setInterval(nextSlide, 5000);
        }
    `

  // 使用变量来避免Vue编译器解析HTML标签
  const scriptEndTag = '</' + 'script>'
  const bodyEndTag = '</' + 'body>'
  const htmlEndTag = '</' + 'html>'

  return htmlContent + scriptEndTag + bodyEndTag + htmlEndTag
}

const getLayoutBackground = (layout) => {
  const backgrounds = {
    'professional': 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
    'corporate': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'creative': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'financial': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'wellness': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'tech': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'default': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  }
  return backgrounds[layout] || backgrounds.default
}

const getLayoutDescription = (layout) => {
  const descriptions = {
    'professional': '专业简洁，突出权威性',
    'corporate': '商务高端，体现企业形象',
    'creative': '创意活泼，富有想象力',
    'financial': '稳重可信，强调安全性',
    'wellness': '清新自然，传递健康理念',
    'tech': '现代科技，展现创新精神',
    'default': '通用设计，适合多种场景'
  }
  return descriptions[layout] || descriptions.default
}

const adjustColor = (color, amount) => {
  // 简单的颜色调整函数
  const num = parseInt(color.replace("#", ""), 16)
  const amt = Math.round(2.55 * amount)
  const R = (num >> 16) + amt
  const G = (num >> 8 & 0x00FF) + amt
  const B = (num & 0x0000FF) + amt
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
}

const getIndustryTagType = (industry) => {
  const tagTypes = {
    '教育培训': 'primary',
    '商务服务': 'success',
    '娱乐社交': 'warning',
    '金融投资': 'danger',
    '健康医疗': 'info',
    '科技创新': ''
  }
  return tagTypes[industry] || 'info'
}

// 生命周期
onMounted(() => {
  if (props.modelValue) {
    selectedTemplate.value = props.modelValue
  }
})

onUnmounted(() => {
  // 清理所有生成的blob URLs
  previewUrls.value.forEach(url => {
    URL.revokeObjectURL(url)
  })
  previewUrls.value.clear()
})
</script>

<style lang="scss" scoped>
.landing-page-templates {
  .templates-header {
    text-align: center;
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
  
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .template-card {
    border: 2px solid #e9ecef;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
      border-color: #409eff;

      .template-overlay {
        opacity: 1;
      }
    }

    &.selected {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
      transform: translateY(-2px);
    }
    
    .template-preview {
      position: relative;
      height: 220px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .template-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(103, 58, 183, 0.9));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .overlay-actions {
          display: flex;
          gap: 12px;

          .el-button {
            border-radius: 20px;
            font-weight: 500;
          }
        }
      }

      &:hover .template-overlay {
        opacity: 1;
      }

      .template-stats {
        position: absolute;
        top: 12px;
        right: 12px;
        display: flex;
        gap: 8px;

        .stat-item {
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(4px);
          padding: 4px 8px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #333;
          font-weight: 500;

          .el-icon {
            font-size: 12px;
            color: #faad14;
          }
        }
      }
    }
    
    .template-info {
      padding: 24px;

      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .template-name {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin: 0;
          flex: 1;
        }
      }

      .template-description {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 16px;
      }

      .template-features {
        margin-bottom: 16px;

        h4 {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;

            .el-icon {
              color: #52c41a;
              font-size: 12px;
            }
          }
        }
      }

      .template-meta {
        .template-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          margin-bottom: 12px;
        }

        .template-suitable {
          font-size: 12px;
          color: #999;
          line-height: 1.4;

          .suitable-label {
            font-weight: 500;
          }

          .suitable-text {
            color: #666;
          }
        }
      }
    }
    
    .selected-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 24px;
      height: 24px;
      background: #409eff;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
  }
  
  .template-preview-content {
    display: flex;
    gap: 30px;
    height: 75vh;

    .preview-device {
      flex: 1;
      display: flex;
      justify-content: center;

      .device-frame {
        width: 400px;
        height: 100%;
        border: 8px solid #333;
        border-radius: 20px;
        overflow: hidden;
        background: white;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        position: relative;

        .preview-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #999;

          .loading-icon {
            font-size: 32px;
            margin-bottom: 10px;
            animation: spin 1s linear infinite;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }

        iframe {
          border: none;
          border-radius: 12px;
        }
      }
    }

    .preview-info {
      width: 320px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;

      .template-meta-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;

        h3 {
          font-size: 20px;
          color: #333;
          margin: 0;
          flex: 1;
        }
      }

      .template-desc {
        color: #666;
        line-height: 1.6;
        margin-bottom: 20px;
        font-size: 14px;
      }

      .template-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 13px;
          color: #666;

          .el-icon {
            color: #faad14;
            font-size: 14px;
          }
        }
      }

      .template-features-preview {
        margin-bottom: 20px;

        h4 {
          font-size: 16px;
          color: #333;
          margin-bottom: 10px;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;
            margin-bottom: 6px;

            .el-icon {
              color: #52c41a;
              font-size: 12px;
            }
          }
        }
      }

      .template-suitable-info {
        margin-bottom: 30px;

        h4 {
          font-size: 16px;
          color: #333;
          margin-bottom: 8px;
        }

        p {
          font-size: 13px;
          color: #666;
          line-height: 1.5;
          margin: 0;
        }
      }

      .preview-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        .el-button {
          border-radius: 20px;
          font-weight: 500;
        }
      }
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

@media (max-width: 768px) {
  .landing-page-templates {
    .templates-grid {
      grid-template-columns: 1fr;
    }
    
    .template-preview-content {
      flex-direction: column;
      height: auto;
      
      .preview-device {
        .device-frame {
          width: 100%;
          height: 400px;
        }
      }
      
      .preview-info {
        width: 100%;
      }
    }
  }
}
</style>
