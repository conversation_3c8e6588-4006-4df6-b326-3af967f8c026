<template>
  <div class="dashboard-charts">
    <!-- 实时数据图表 -->
    <div class="chart-card">
      <div class="card-header">
        <h3>实时数据趋势</h3>
        <div class="chart-controls">
          <el-radio-group v-model="chartTimeRange" size="small">
            <el-radio-button label="24h">24小时</el-radio-button>
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="chart-container">
        <v-chart 
          class="chart" 
          :option="chartOption" 
          autoresize
          @click="handleChartClick"
        />
      </div>
    </div>

    <!-- 用户活动热力图 -->
    <div class="activity-card">
      <div class="card-header">
        <h3>用户活动热力图</h3>
        <el-tooltip content="显示用户在不同时间段的活跃度">
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="heatmap-container">
        <v-chart 
          class="heatmap" 
          :option="heatmapOption" 
          autoresize
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, HeatmapChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CalendarComponent,
  VisualMapComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { InfoFilled } from '@element-plus/icons-vue'

use([
  CanvasRenderer,
  LineChart,
  HeatmapChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CalendarComponent,
  VisualMapComponent
])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['chart-click'])

const chartTimeRange = ref('24h')

// 实时数据图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['访问量', '转化量', '收入']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: generateTimeLabels()
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '访问量',
      type: 'line',
      stack: 'Total',
      smooth: true,
      data: generateRandomData(24)
    },
    {
      name: '转化量',
      type: 'line',
      stack: 'Total',
      smooth: true,
      data: generateRandomData(24, 0.3)
    },
    {
      name: '收入',
      type: 'line',
      stack: 'Total',
      smooth: true,
      data: generateRandomData(24, 0.1)
    }
  ]
}))

// 热力图配置
const heatmapOption = computed(() => ({
  tooltip: {
    position: 'top'
  },
  grid: {
    height: '50%',
    top: '10%'
  },
  xAxis: {
    type: 'category',
    data: ['00', '02', '04', '06', '08', '10', '12', '14', '16', '18', '20', '22'],
    splitArea: {
      show: true
    }
  },
  yAxis: {
    type: 'category',
    data: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    splitArea: {
      show: true
    }
  },
  visualMap: {
    min: 0,
    max: 100,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '15%'
  },
  series: [{
    name: '活跃度',
    type: 'heatmap',
    data: generateHeatmapData(),
    label: {
      show: true
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 生成时间标签
function generateTimeLabels() {
  const labels = []
  for (let i = 0; i < 24; i++) {
    labels.push(`${i.toString().padStart(2, '0')}:00`)
  }
  return labels
}

// 生成随机数据
function generateRandomData(count, factor = 1) {
  const data = []
  for (let i = 0; i < count; i++) {
    data.push(Math.floor(Math.random() * 1000 * factor))
  }
  return data
}

// 生成热力图数据
function generateHeatmapData() {
  const data = []
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 12; j++) {
      data.push([j, i, Math.floor(Math.random() * 100)])
    }
  }
  return data
}

const handleChartClick = (params) => {
  emit('chart-click', params)
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.dashboard-charts {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart-card, .activity-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.chart-container {
  height: 400px;
}

.chart {
  height: 100%;
  width: 100%;
}

.heatmap-container {
  height: 300px;
}

.heatmap {
  height: 100%;
  width: 100%;
}

.chart-controls {
  .el-radio-group {
    --el-radio-button-checked-bg-color: #409eff;
    --el-radio-button-checked-text-color: #ffffff;
  }
}
</style>
