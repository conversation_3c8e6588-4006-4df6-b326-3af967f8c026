/**
 * Vitest 测试环境设置文件
 * 配置全局模拟、测试工具和测试环境
 */

import { vi, beforeEach, afterEach } from 'vitest'
import { config } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'

// Element Plus 组件全局配置
config.global.stubs = {
  // Element Plus 组件
  'el-button': true,
  'el-input': true,
  'el-form': true,
  'el-form-item': true,
  'el-menu': true,
  'el-menu-item': true,
  'el-submenu': true,
  'el-breadcrumb': true,
  'el-breadcrumb-item': true,
  'el-dropdown': true,
  'el-dropdown-menu': true,
  'el-dropdown-item': true,
  'el-dialog': true,
  'el-drawer': true,
  'el-table': true,
  'el-table-column': true,
  'el-pagination': true,
  'el-loading': true,
  'el-message': true,
  'el-notification': true,
  'el-tooltip': true,
  'el-popover': true,
  
  // Element Plus Icons
  'el-icon': true,
  
  // Router 组件
  'router-link': true,
  'router-view': true,
  
  // 第三方组件
  'chart': true,
  'v-chart': true
}

// 全局插件配置
config.global.plugins = [createPinia()]

// 模拟全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }))
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟 sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
})

// 模拟 CSS.supports
window.CSS = { supports: vi.fn().mockReturnValue(false) }

// 模拟 console 方法以避免测试输出污染
const originalConsole = global.console
beforeEach(() => {
  global.console = {
    ...originalConsole,
    // 保留 error 和 warn 用于调试
    log: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: originalConsole.warn,
    error: originalConsole.error
  }
  
  // 重置 Pinia
  setActivePinia(createPinia())
})

afterEach(() => {
  // 恢复 console
  global.console = originalConsole
  
  // 清理所有模拟
  vi.clearAllMocks()
  
  // 重置 localStorage 模拟
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
})

// 模拟 Vue Router
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')
  return {
    ...actual,
    useRouter: () => ({
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      currentRoute: { value: { name: 'test', path: '/test' } }
    }),
    useRoute: () => ({
      name: 'test',
      path: '/test',
      params: {},
      query: {},
      meta: {}
    })
  }
})

// 模拟 Element Plus 消息组件
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      error: vi.fn()
    },
    ElNotification: {
      success: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
      error: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue('confirm'),
      alert: vi.fn().mockResolvedValue('confirm'),
      prompt: vi.fn().mockResolvedValue({ value: 'test' })
    }
  }
})

// 模拟 API 请求
vi.mock('@/utils/request', () => ({
  default: vi.fn(() => Promise.resolve({ 
    data: {}, 
    status: 200, 
    message: 'success' 
  }))
}))

// 模拟 NProgress
vi.mock('nprogress', () => ({
  start: vi.fn(),
  done: vi.fn(),
  configure: vi.fn()
}))

// 设置测试环境变量
process.env.NODE_ENV = 'test'