<template>
  <div class="content-card" :class="{ 'selected': selected, 'high-risk': content.risk_level === 'high' }">
    <div class="card-header">
      <div class="content-type">
        <div class="type-icon" :class="content.type">
          <el-icon v-if="content.type === 'text'"><Document /></el-icon>
          <el-icon v-else-if="content.type === 'image'"><Picture /></el-icon>
          <el-icon v-else-if="content.type === 'video'"><VideoPlay /></el-icon>
          <el-icon v-else><Link /></el-icon>
        </div>
        <span class="type-label">{{ getTypeText(content.type) }}</span>
      </div>
      <div class="card-actions">
        <el-checkbox 
          :model-value="selected" 
          @change="$emit('select', content.id)"
          class="select-checkbox"
        />
        <el-tag :type="getRiskTagType(content.risk_level)" size="small">
          {{ getRiskText(content.risk_level) }}
        </el-tag>
      </div>
    </div>

    <div class="card-content">
      <div class="content-preview">
        <div class="content-text">{{ content.content }}</div>
        <div v-if="content.image_url" class="content-image">
          <el-image :src="content.image_url" fit="cover" />
        </div>
      </div>

      <div class="content-meta">
        <div class="meta-row">
          <span class="meta-item">
            <el-icon><User /></el-icon>
            {{ content.author.name }}
          </span>
          <span class="meta-item">
            <el-icon><ChatDotRound /></el-icon>
            {{ content.group.name }}
          </span>
        </div>
        <div class="meta-row">
          <span class="meta-item">
            <el-icon><Clock /></el-icon>
            {{ formatDate(content.created_at) }}
          </span>
          <el-tag :type="getStatusTagType(content.status)" size="small">
            {{ getStatusText(content.status) }}
          </el-tag>
        </div>
      </div>

      <!-- AI分析结果 -->
      <div v-if="content.ai_analysis" class="ai-analysis">
        <div class="analysis-header">
          <span class="analysis-title">AI分析结果</span>
          <span class="confidence-score">
            置信度: {{ (content.ai_analysis.confidence * 100).toFixed(1) }}%
          </span>
        </div>
        <div class="analysis-progress">
          <el-progress
            :percentage="content.ai_analysis.confidence * 100"
            :color="getConfidenceColor(content.ai_analysis.confidence)"
            :stroke-width="4"
            :show-text="false"
          />
        </div>
        <div class="analysis-tags">
          <el-tag
            v-for="tag in content.ai_analysis.tags"
            :key="tag"
            size="small"
            type="info"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <div class="footer-actions">
        <el-button-group v-if="content.status === 'pending'">
          <el-button
            type="success"
            size="small"
            @click="$emit('action', 'approve', content)"
          >
            <el-icon><Check /></el-icon>
            通过
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="$emit('action', 'reject', content)"
          >
            <el-icon><Close /></el-icon>
            拒绝
          </el-button>
        </el-button-group>
        <el-button
          size="small"
          @click="$emit('action', 'detail', content)"
        >
          <el-icon><View /></el-icon>
          详情
        </el-button>
        <el-button
          v-if="!content.ai_analysis"
          size="small"
          @click="$emit('action', 'analyze', content)"
          :loading="content.analyzing"
        >
          <el-icon><MagicStick /></el-icon>
          AI分析
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Document, Picture, VideoPlay, Link, User, ChatDotRound, Clock,
  Check, Close, View, MagicStick
} from '@element-plus/icons-vue'

defineProps({
  content: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

defineEmits(['select', 'action'])

// 工具函数
const getTypeText = (type) => {
  const texts = {
    text: '文本',
    image: '图片',
    video: '视频',
    link: '链接'
  }
  return texts[type] || '未知'
}

const getRiskTagType = (riskLevel) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return types[riskLevel] || 'info'
}

const getRiskText = (riskLevel) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[riskLevel] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    flagged: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    flagged: '已标记'
  }
  return texts[status] || '未知'
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.content-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 16px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  &.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  }

  &.high-risk {
    border-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .content-type {
      display: flex;
      align-items: center;
      gap: 8px;

      .type-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;

        &.text {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        &.image {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        &.video {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        &.link {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
      }

      .type-label {
        font-weight: 500;
        color: #374151;
      }
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .card-content {
    .content-preview {
      margin-bottom: 12px;

      .content-text {
        font-size: 14px;
        color: #374151;
        line-height: 1.5;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      .content-image {
        width: 100%;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;

        :deep(.el-image) {
          width: 100%;
          height: 100%;
        }
      }
    }

    .content-meta {
      margin-bottom: 12px;

      .meta-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        font-size: 12px;
        color: #6b7280;

        &:last-child {
          margin-bottom: 0;
        }

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .ai-analysis {
      background: #f8fafc;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;

      .analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .analysis-title {
          font-size: 12px;
          font-weight: 600;
          color: #374151;
        }

        .confidence-score {
          font-size: 11px;
          color: #6b7280;
        }
      }

      .analysis-progress {
        margin-bottom: 8px;

        :deep(.el-progress) {
          .el-progress-bar__outer {
            border-radius: 2px;
            background: #e5e7eb;
          }

          .el-progress-bar__inner {
            border-radius: 2px;
          }
        }
      }

      .analysis-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }
  }

  .card-footer {
    .footer-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-card {
    padding: 12px;

    .card-header {
      .content-type {
        .type-label {
          display: none;
        }
      }
    }

    .card-content {
      .content-meta {
        .meta-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }
    }

    .card-footer {
      .footer-actions {
        justify-content: center;
      }
    }
  }
}
</style>