<template>
  <footer class="footer-component" :style="componentStyle">
    <div class="footer-container">
      <div class="footer-content">
        <!-- 公司信息 -->
        <div class="footer-section company-info">
          <div class="company-logo">
            <img v-if="data.logo" :src="data.logo" :alt="data.companyName" />
            <h3 v-else>{{ data.companyName }}</h3>
          </div>
          <p class="company-desc">{{ data.description }}</p>
          
          <!-- 社交媒体 -->
          <div class="social-links" v-if="data.socialLinks && data.socialLinks.length">
            <a 
              v-for="(link, index) in data.socialLinks" 
              :key="index"
              :href="link.url" 
              target="_blank"
              class="social-link"
            >
              <el-icon>
                <component :is="getSocialIcon(link.type)" />
              </el-icon>
            </a>
          </div>
        </div>
        
        <!-- 快速链接 -->
        <div class="footer-section quick-links" v-if="data.quickLinks && data.quickLinks.length">
          <h4 class="section-title">快速链接</h4>
          <ul class="link-list">
            <li v-for="(link, index) in data.quickLinks" :key="index">
              <a :href="link.url" @click.prevent>{{ link.text }}</a>
            </li>
          </ul>
        </div>
        
        <!-- 产品服务 -->
        <div class="footer-section services" v-if="data.services && data.services.length">
          <h4 class="section-title">产品服务</h4>
          <ul class="link-list">
            <li v-for="(service, index) in data.services" :key="index">
              <a :href="service.url" @click.prevent>{{ service.text }}</a>
            </li>
          </ul>
        </div>
        
        <!-- 联系信息 -->
        <div class="footer-section contact-info" v-if="data.contactInfo">
          <h4 class="section-title">联系我们</h4>
          <div class="contact-list">
            <div class="contact-item" v-if="data.contactInfo.address">
              <el-icon><Location /></el-icon>
              <span>{{ data.contactInfo.address }}</span>
            </div>
            <div class="contact-item" v-if="data.contactInfo.phone">
              <el-icon><Phone /></el-icon>
              <span>{{ data.contactInfo.phone }}</span>
            </div>
            <div class="contact-item" v-if="data.contactInfo.email">
              <el-icon><Message /></el-icon>
              <span>{{ data.contactInfo.email }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>{{ data.copyright || `© ${new Date().getFullYear()} ${data.companyName}. 保留所有权利。` }}</p>
        </div>
        
        <div class="footer-links" v-if="data.footerLinks && data.footerLinks.length">
          <a 
            v-for="(link, index) in data.footerLinks" 
            :key="index"
            :href="link.url" 
            @click.prevent
          >
            {{ link.text }}
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { Location, Phone, Message, ChatDotRound } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      companyName: '公司名称',
      description: '这里是公司的简短描述，介绍公司的主要业务和价值观。',
      logo: '',
      quickLinks: [
        { text: '关于我们', url: '#' },
        { text: '产品介绍', url: '#' },
        { text: '新闻动态', url: '#' },
        { text: '联系我们', url: '#' }
      ],
      services: [
        { text: '产品服务', url: '#' },
        { text: '技术支持', url: '#' },
        { text: '客户服务', url: '#' },
        { text: '合作伙伴', url: '#' }
      ],
      contactInfo: {
        address: '北京市朝阳区某某大厦',
        phone: '************',
        email: '<EMAIL>'
      },
      socialLinks: [
        { type: 'wechat', url: '#' },
        { type: 'weibo', url: '#' },
        { type: 'qq', url: '#' }
      ],
      footerLinks: [
        { text: '隐私政策', url: '#' },
        { text: '服务条款', url: '#' },
        { text: '网站地图', url: '#' }
      ],
      copyright: '',
      backgroundColor: '#2c3e50',
      textColor: '#ffffff'
    })
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const componentStyle = computed(() => ({
  backgroundColor: props.data.backgroundColor || '#2c3e50',
  color: props.data.textColor || '#ffffff'
}))

const getSocialIcon = (type) => {
  // 由于Element Plus图标有限，这里使用简单的文字替代
  // 在实际项目中可以使用专门的社交媒体图标库
  return ChatDotRound
}
</script>

<style lang="scss" scoped>
.footer-component {
  padding: 60px 0 20px 0;
  
  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    
    .footer-content {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1.5fr;
      gap: 40px;
      margin-bottom: 40px;
      
      .footer-section {
        .section-title {
          font-size: 18px;
          font-weight: 600;
          margin: 0 0 20px 0;
          color: inherit;
        }
        
        &.company-info {
          .company-logo {
            margin-bottom: 16px;
            
            img {
              height: 40px;
              width: auto;
            }
            
            h3 {
              margin: 0;
              font-size: 24px;
              font-weight: 700;
            }
          }
          
          .company-desc {
            font-size: 14px;
            line-height: 1.6;
            margin: 0 0 20px 0;
            opacity: 0.8;
          }
          
          .social-links {
            display: flex;
            gap: 12px;
            
            .social-link {
              width: 40px;
              height: 40px;
              background: rgba(255,255,255,0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: inherit;
              text-decoration: none;
              transition: all 0.3s ease;
              
              &:hover {
                background: rgba(255,255,255,0.2);
                transform: translateY(-2px);
              }
              
              .el-icon {
                font-size: 18px;
              }
            }
          }
        }
        
        .link-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            margin-bottom: 12px;
            
            a {
              color: inherit;
              text-decoration: none;
              font-size: 14px;
              opacity: 0.8;
              transition: opacity 0.3s ease;
              
              &:hover {
                opacity: 1;
              }
            }
          }
        }
        
        &.contact-info {
          .contact-list {
            .contact-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 12px;
              font-size: 14px;
              opacity: 0.8;
              
              .el-icon {
                font-size: 16px;
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }
    
    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 20px;
      border-top: 1px solid rgba(255,255,255,0.1);
      
      .copyright {
        p {
          margin: 0;
          font-size: 14px;
          opacity: 0.6;
        }
      }
      
      .footer-links {
        display: flex;
        gap: 24px;
        
        a {
          color: inherit;
          text-decoration: none;
          font-size: 14px;
          opacity: 0.6;
          transition: opacity 0.3s ease;
          
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .footer-component {
    padding: 40px 0 20px 0;
    
    .footer-container {
      .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
        margin-bottom: 32px;
        
        .footer-section {
          &.company-info {
            text-align: center;
            
            .social-links {
              justify-content: center;
            }
          }
        }
      }
      
      .footer-bottom {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        
        .footer-links {
          flex-wrap: wrap;
          justify-content: center;
          gap: 16px;
        }
      }
    }
  }
}
</style>
