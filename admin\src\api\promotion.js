import request from '@/utils/request'

/**
 * 推广链接相关API
 */
export const promotionApi = {
  /**
   * 获取推广链接列表
   */
  getList(params = {}) {
    return request({
      url: '/admin/promotions',
      method: 'get',
      params
    })
  },

  /**
   * 获取推广链接详情
   */
  getDetail(id) {
    return request({
      url: `/admin/promotions/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建推广链接
   */
  create(data) {
    return request({
      url: '/admin/promotions',
      method: 'post',
      data
    })
  },

  /**
   * 更新推广链接
   */
  update(id, data) {
    return request({
      url: `/admin/promotions/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除推广链接
   */
  delete(id) {
    return request({
      url: `/admin/promotions/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量操作推广链接
   */
  batchAction(data) {
    return request({
      url: '/admin/promotions/batch-action',
      method: 'post',
      data
    })
  },

  /**
   * 复制推广链接
   */
  duplicate(id, data = {}) {
    return request({
      url: `/admin/promotions/${id}/duplicate`,
      method: 'post',
      data
    })
  },

  /**
   * 重新生成短码
   */
  regenerateShortCode(id) {
    return request({
      url: `/admin/promotions/${id}/regenerate-code`,
      method: 'post'
    })
  },

  /**
   * 生成推广素材
   */
  generateMaterials(data) {
    return request({
      url: '/admin/promotions/generate-materials',
      method: 'post',
      data
    })
  },

  /**
   * 获取推广统计数据
   */
  getStats(params = {}) {
    return request({
      url: '/admin/promotions/stats',
      method: 'get',
      params
    })
  },

  /**
   * 获取即将过期的链接
   */
  getExpiringLinks(days = 7) {
    return request({
      url: '/admin/promotions/expiring-links',
      method: 'get',
      params: { days }
    })
  },

  /**
   * 清理过期链接
   */
  cleanupExpired() {
    return request({
      url: '/admin/promotions/cleanup-expired',
      method: 'post'
    })
  }
}

/**
 * 短链接相关API
 */
export const shortLinkApi = {
  /**
   * 获取短链接信息
   */
  getInfo(shortCode) {
    return request({
      url: `/short-links/${shortCode}/info`,
      method: 'get'
    })
  },

  /**
   * 预览短链接
   */
  preview(shortCode) {
    return request({
      url: `/short-links/${shortCode}/preview`,
      method: 'get'
    })
  },

  /**
   * 获取短链接统计
   */
  getStats(shortCode, params = {}) {
    return request({
      url: `/short-links/${shortCode}/stats`,
      method: 'get',
      params
    })
  },

  /**
   * 生成二维码
   */
  generateQRCode(shortCode, params = {}) {
    return request({
      url: `/short-links/${shortCode}/qrcode`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 批量检查链接状态
   */
  batchCheck(shortCodes) {
    return request({
      url: '/short-links/batch-check',
      method: 'post',
      data: { short_codes: shortCodes }
    })
  },

  /**
   * 获取热门短链接
   */
  getPopular(params = {}) {
    return request({
      url: '/short-links/popular',
      method: 'get',
      params
    })
  },

  /**
   * 健康检查
   */
  healthCheck() {
    return request({
      url: '/short-links/health-check',
      method: 'get'
    })
  }
}

/**
 * 推广素材相关API
 */
export const promotionMaterialApi = {
  /**
   * 生成二维码
   */
  generateQRCode(url, options = {}) {
    return request({
      url: '/admin/promotions/generate-qrcode',
      method: 'post',
      data: { url, ...options },
      responseType: 'blob'
    })
  },

  /**
   * 生成推广海报
   */
  generatePoster(groupId, options = {}) {
    return request({
      url: '/admin/promotions/generate-poster',
      method: 'post',
      data: { group_id: groupId, ...options },
      responseType: 'blob'
    })
  },

  /**
   * 获取分享文案模板
   */
  getShareTextTemplates(groupId) {
    return request({
      url: '/admin/promotions/share-text-templates',
      method: 'get',
      params: { group_id: groupId }
    })
  },

  /**
   * 批量生成推广素材
   */
  batchGenerate(data) {
    return request({
      url: '/admin/promotions/batch-generate-materials',
      method: 'post',
      data
    })
  }
}

/**
 * 推广分析相关API
 */
export const promotionAnalyticsApi = {
  /**
   * 获取点击趋势数据
   */
  getClickTrend(params = {}) {
    return request({
      url: '/admin/promotions/analytics/click-trend',
      method: 'get',
      params
    })
  },

  /**
   * 获取来源分析
   */
  getSourceAnalysis(params = {}) {
    return request({
      url: '/admin/promotions/analytics/source-analysis',
      method: 'get',
      params
    })
  },

  /**
   * 获取地域分析
   */
  getRegionAnalysis(params = {}) {
    return request({
      url: '/admin/promotions/analytics/region-analysis',
      method: 'get',
      params
    })
  },

  /**
   * 获取设备分析
   */
  getDeviceAnalysis(params = {}) {
    return request({
      url: '/admin/promotions/analytics/device-analysis',
      method: 'get',
      params
    })
  },

  /**
   * 获取转化漏斗数据
   */
  getConversionFunnel(params = {}) {
    return request({
      url: '/admin/promotions/analytics/conversion-funnel',
      method: 'get',
      params
    })
  },

  /**
   * 导出分析报告
   */
  exportReport(params = {}) {
    return request({
      url: '/admin/promotions/analytics/export-report',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}

export default {
  promotionApi,
  shortLinkApi,
  promotionMaterialApi,
  promotionAnalyticsApi
}