// 增强权限控制系统
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'

// 权限映射配置
const PERMISSION_MAP = {
  // 角色基础权限
  roles: {
    admin: ['*'], // 超级管理员拥有所有权限
    manager: [
      'dashboard.view',
      'community.manage', 'community.view',
      'distribution.manage', 'distribution.view',
      'finance.view', 'finance.manage',
      'user.view', 'user.manage',
      'system.settings', 'system.logs'
    ],
    distributor: [
      'dashboard.view',
      'distributor.dashboard',
      'distributor.groups',
      'distributor.links',
      'distributor.commission',
      'profile.manage'
    ],
    group_owner: [
      'dashboard.view',
      'owner.dashboard',
      'owner.groups',
      'profile.manage'
    ],
    user: [
      'dashboard.view',
      'profile.view'
    ]
  },
  
  // 领域权限映射
  domains: {
    'business-core': {
      base: ['dashboard.view'],
      modules: {
        'community-management': ['community.view', 'community.manage'],
        'distribution-system': ['distribution.view', 'distribution.manage'],
        'order-management': ['orders.view', 'orders.manage'],
        'user-management': ['user.view', 'user.manage']
      }
    },
    'operation-management': {
      base: ['system.view'],
      modules: {
        'anti-block-system': ['antiblock.view', 'antiblock.manage'],
        'promotion-management': ['promotion.view', 'promotion.manage'],
        'substation-management': ['substation.view', 'substation.manage']
      }
    },
    'data-analytics': {
      base: ['analytics.view'],
      modules: {
        'finance-analytics': ['finance.view', 'finance.analytics'],
        'user-analytics': ['user.analytics'],
        'performance-monitor': ['system.monitor']
      }
    },
    'system-config': {
      base: ['system.view'],
      modules: {
        'system-settings': ['system.settings'],
        'permission-management': ['permissions.manage'],
        'data-export': ['data.export'],
        'operation-logs': ['logs.view']
      }
    }
  }
}

// 路由权限配置
const ROUTE_PERMISSIONS = {
  '/dashboard': ['dashboard.view'],
  '/community': ['community.view'],
  '/community/add-enhanced': ['community.manage'],
  '/distribution': ['distribution.view'],
  '/distribution/detail': ['distribution.manage'],
  '/finance': ['finance.view'],
  '/user': ['user.view'],
  '/user/add': ['user.manage'],
  '/system': ['system.view'],
  '/system/settings': ['system.settings'],
  '/anti-block': ['antiblock.view'],
  '/orders': ['orders.view'],
  '/substation': ['substation.view'],
  '/promotion': ['promotion.view'],
  '/distributor': ['distributor.dashboard'],
  '/owner': ['owner.dashboard']
}

export function usePermissionSystem() {
  const userStore = useUserStore()
  
  // 获取用户权限列表
  const getUserPermissions = (userRole) => {
    if (!userRole) return []
    
    const rolePermissions = PERMISSION_MAP.roles[userRole] || []
    
    // 如果包含通配符，返回所有权限
    if (rolePermissions.includes('*')) {
      return ['*']
    }
    
    return rolePermissions
  }
  
  // 检查单个权限
  const hasPermission = (permission, userRole = null) => {
    const role = userRole || userStore.userInfo?.role
    if (!role) return false
    
    const userPermissions = getUserPermissions(role)
    
    // 超级管理员权限
    if (userPermissions.includes('*')) return true
    
    // 精确匹配
    if (userPermissions.includes(permission)) return true
    
    // 通配符匹配
    return userPermissions.some(perm => {
      if (perm.endsWith('.*')) {
        const prefix = perm.slice(0, -2)
        return permission.startsWith(prefix + '.')
      }
      return false
    })
  }
  
  // 检查多个权限（需要全部满足）
  const hasAllPermissions = (permissions, userRole = null) => {
    return permissions.every(permission => hasPermission(permission, userRole))
  }
  
  // 检查多个权限（满足任一即可）
  const hasAnyPermission = (permissions, userRole = null) => {
    return permissions.some(permission => hasPermission(permission, userRole))
  }
  
  // 检查路由权限
  const hasRoutePermission = (routePath, userRole = null) => {
    // 精确匹配
    if (ROUTE_PERMISSIONS[routePath]) {
      return hasAnyPermission(ROUTE_PERMISSIONS[routePath], userRole)
    }
    
    // 模糊匹配
    for (const [pattern, permissions] of Object.entries(ROUTE_PERMISSIONS)) {
      if (routePath.startsWith(pattern)) {
        return hasAnyPermission(permissions, userRole)
      }
    }
    
    // 默认需要基础权限
    return hasPermission('dashboard.view', userRole)
  }
  
  // 检查域权限
  const hasDomainPermission = (domainKey, userRole = null) => {
    const domainConfig = PERMISSION_MAP.domains[domainKey]
    if (!domainConfig) return false
    
    return hasAnyPermission(domainConfig.base, userRole)
  }
  
  // 检查模块权限
  const hasModulePermission = (domainKey, moduleKey, userRole = null) => {
    const domainConfig = PERMISSION_MAP.domains[domainKey]
    if (!domainConfig || !domainConfig.modules[moduleKey]) return false
    
    return hasAnyPermission(domainConfig.modules[moduleKey], userRole)
  }
  
  // 过滤可访问的路由
  const filterAccessibleRoutes = (routes, userRole = null) => {
    return routes.filter(route => {
      // 检查路由权限
      if (!hasRoutePermission(route.path, userRole)) {
        return false
      }
      
      // 检查角色限制
      if (route.meta?.roles) {
        const role = userRole || userStore.userInfo?.role
        return route.meta.roles.includes(role)
      }
      
      return true
    })
  }
  
  // 过滤可访问的域
  const filterAccessibleDomains = (domains, userRole = null) => {
    return domains.filter(domain => hasDomainPermission(domain.key, userRole))
  }
  
  // 过滤可访问的模块
  const filterAccessibleModules = (domainKey, modules, userRole = null) => {
    return modules.filter(module => 
      hasModulePermission(domainKey, module.key, userRole)
    )
  }
  
  // 获取用户默认路由
  const getUserDefaultRoute = (userRole) => {
    const defaultRoutes = {
      admin: '/dashboard',
      manager: '/dashboard',
      distributor: '/distributor/dashboard',
      group_owner: '/owner/dashboard',
      user: '/dashboard'
    }
    
    return defaultRoutes[userRole] || '/dashboard'
  }
  
  // 权限检查中间件
  const createPermissionMiddleware = () => {
    return (permission) => {
      return (to, from, next) => {
        if (hasPermission(permission)) {
          next()
        } else {
          next('/403')
        }
      }
    }
  }
  
  // 角色检查中间件
  const createRoleMiddleware = () => {
    return (allowedRoles) => {
      return (to, from, next) => {
        const userRole = userStore.userInfo?.role
        if (allowedRoles.includes(userRole)) {
          next()
        } else {
          next('/403')
        }
      }
    }
  }
  
  // 动态权限检查
  const checkDynamicPermission = async (resource, action, context = {}) => {
    try {
      // 这里可以实现复杂的动态权限逻辑
      // 例如：检查用户是否可以编辑特定的内容
      const permission = `${resource}.${action}`
      
      // 基础权限检查
      if (!hasPermission(permission)) {
        return false
      }
      
      // 上下文相关的权限检查
      if (context.ownerId && context.ownerId !== userStore.userInfo?.id) {
        // 检查是否有管理其他用户资源的权限
        return hasPermission(`${resource}.manage_others`)
      }
      
      return true
    } catch (error) {
      console.error('动态权限检查失败:', error)
      return false
    }
  }
  
  // 权限状态响应式计算
  const permissionState = computed(() => {
    const userRole = userStore.userInfo?.role
    if (!userRole) return { hasAnyPermission: false, userRole: null }
    
    return {
      hasAnyPermission: getUserPermissions(userRole).length > 0,
      userRole,
      isAdmin: userRole === 'admin',
      isManager: userRole === 'manager',
      isDistributor: userRole === 'distributor',
      isGroupOwner: userRole === 'group_owner',
      isUser: userRole === 'user'
    }
  })
  
  return {
    // 权限检查方法
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRoutePermission,
    hasDomainPermission,
    hasModulePermission,
    
    // 过滤方法
    filterAccessibleRoutes,
    filterAccessibleDomains,
    filterAccessibleModules,
    
    // 工具方法
    getUserPermissions,
    getUserDefaultRoute,
    checkDynamicPermission,
    
    // 中间件创建
    createPermissionMiddleware,
    createRoleMiddleware,
    
    // 响应式状态
    permissionState,
    
    // 配置
    PERMISSION_MAP,
    ROUTE_PERMISSIONS
  }
}