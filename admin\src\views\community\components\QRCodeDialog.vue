<template>
  <el-dialog
    v-model="dialogVisible"
    title="推广二维码"
    width="600px"
    :destroy-on-close="true"
  >
    <div class="qrcode-container">
      <!-- 二维码类型选择 -->
      <div class="qrcode-tabs">
        <el-radio-group v-model="activeTab" @change="handleTabChange">
          <el-radio-button value="promotion">推广二维码</el-radio-button>
          <el-radio-button value="entry">入群二维码</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 推广二维码 -->
      <div v-if="activeTab === 'promotion'" class="qrcode-display">
        <div v-if="promotionQrCode" class="qrcode-image">
          <img :src="promotionQrCode" alt="推广二维码" />
          <div class="qrcode-info">
            <h4>{{ groupData.title || groupData.name }}</h4>
            <p>扫码访问落地页，引导用户付费</p>
            <div class="qrcode-url">
              <el-input
                v-model="promotionUrl"
                readonly
                size="small"
                placeholder="推广链接"
              >
                <template #append>
                  <el-button @click="copyPromotionUrl" size="small">
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>
        </div>
        <div v-else class="no-qrcode">
          <el-empty description="正在生成推广二维码..." :image-size="100" />
          <el-button type="primary" @click="generatePromotionQrCode" :loading="generating">
            生成推广二维码
          </el-button>
        </div>
      </div>

      <!-- 入群二维码 -->
      <div v-else class="qrcode-display">
        <div v-if="groupData.qr_code" class="qrcode-image">
          <img :src="groupData.qr_code" alt="入群二维码" />
          <div class="qrcode-info">
            <h4>{{ groupData.title || groupData.name }}</h4>
            <p>付费后展示给用户的入群二维码</p>
            <el-alert
              title="注意：这是付费后内容，只有完成支付的用户才能看到"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </div>
        <div v-else class="no-qrcode">
          <el-empty description="暂未配置入群二维码" :image-size="100" />
          <p class="tip-text">请在群组编辑页面的"付费后内容配置"中上传入群二维码</p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="qrcode-actions">
        <!-- 推广二维码操作 -->
        <template v-if="activeTab === 'promotion'">
          <el-button v-if="promotionQrCode" @click="downloadQRCode(promotionQrCode, '推广二维码')">
            <el-icon><Download /></el-icon>
            下载推广二维码
          </el-button>
          <el-button @click="copyPromotionUrl">
            <el-icon><DocumentCopy /></el-icon>
            复制推广链接
          </el-button>
          <el-button @click="refreshPromotionQrCode" :loading="generating">
            <el-icon><Refresh /></el-icon>
            刷新二维码
          </el-button>
          <el-button type="success" @click="goToLinkManagement">
            <el-icon><Link /></el-icon>
            推广链接管理
          </el-button>
        </template>

        <!-- 入群二维码操作 -->
        <template v-else>
          <el-button type="primary" @click="goToGroupEdit">
            <el-icon><Edit /></el-icon>
            配置入群内容
          </el-button>
          <el-button v-if="groupData.qr_code" @click="downloadQRCode(groupData.qr_code, '入群二维码')">
            <el-icon><Download /></el-icon>
            下载入群二维码
          </el-button>
        </template>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Upload, Download, Delete, DocumentCopy, Refresh,
  Link, Edit
} from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import { generateGroupPromotionLink } from '@/api/anti-block'
import { updateGroupQrCode } from '@/api/community'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'qrcode-updated'])
const router = useRouter()

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 新增的响应式数据
const activeTab = ref('promotion')
const generating = ref(false)
const promotionQrCode = ref('')
const promotionUrl = ref('')

// 上传配置
const uploadUrl = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/wechat-groups/${props.groupData.id}/qr-code`
})

const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}))

// 监听对话框打开，自动生成推广二维码
watch(dialogVisible, (newVal) => {
  if (newVal && props.groupData.id) {
    generatePromotionQrCode()
  }
})

// 标签页切换
const handleTabChange = (tab) => {
  activeTab.value = tab
}

// 生成推广二维码（集成防红系统和短链接）
const generatePromotionQrCode = async () => {
  if (!props.groupData.id) return

  generating.value = true
  try {
    // 使用统一的API调用生成防红推广链接
    const result = await generateGroupPromotionLink(props.groupData.id, {
      enable_anti_block: true,
      enable_short_link: true,
      link_type: 'promotion'
    })

    if (result.code === 200 && result.data) {
      const linkData = result.data

      // 优先使用防红短链接，其次使用防红链接，最后使用原始链接
      promotionUrl.value = linkData.short_url || linkData.anti_block_url || linkData.original_url

      // 生成推广二维码
      promotionQrCode.value = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(promotionUrl.value)}`

      console.log('✅ 防红推广二维码生成成功:', {
        groupId: props.groupData.id,
        originalUrl: linkData.original_url,
        antiBlockUrl: linkData.anti_block_url,
        shortUrl: linkData.short_url,
        finalUrl: promotionUrl.value,
        antiBlockEnabled: linkData.anti_block_enabled,
        shortLinkEnabled: linkData.short_link_enabled,
        qrCode: promotionQrCode.value
      })

      // 显示成功信息
      const features = []
      if (linkData.anti_block_enabled) features.push('防红保护')
      if (linkData.short_link_enabled) features.push('短链接')

      if (features.length > 0) {
        ElMessage.success(`推广链接生成成功，已启用：${features.join('、')}`)
      } else {
        ElMessage.success('推广链接生成成功')
      }
    } else {
      throw new Error(result.message || '生成推广链接失败')
    }
  } catch (error) {
    console.error('生成防红推广二维码失败:', error)

    // 降级方案：生成普通推广链接
    console.log('⚠️ 防红系统不可用，使用降级方案')
    const groupId = props.groupData.id
    promotionUrl.value = `${window.location.origin}/landing/group/${groupId}`
    promotionQrCode.value = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(promotionUrl.value)}`

    ElMessage.warning('防红系统暂时不可用，已生成普通推广链接')
  } finally {
    generating.value = false
  }
}

// 刷新推广二维码
const refreshPromotionQrCode = () => {
  generatePromotionQrCode()
}

// 复制推广链接
const copyPromotionUrl = async () => {
  if (!promotionUrl.value) {
    ElMessage.warning('推广链接还未生成')
    return
  }

  try {
    await navigator.clipboard.writeText(promotionUrl.value)
    ElMessage.success('推广链接已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = promotionUrl.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('推广链接已复制到剪贴板')
  }
}

// 下载二维码
const downloadQRCode = (qrCodeUrl, fileName) => {
  if (!qrCodeUrl) return

  const link = document.createElement('a')
  link.href = qrCodeUrl
  link.download = `${props.groupData.title || props.groupData.name}-${fileName}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  ElMessage.success(`${fileName}下载成功`)
}

// 跳转到推广链接管理
const goToLinkManagement = () => {
  router.push('/promotion/links')
  dialogVisible.value = false
}

// 跳转到群组编辑
const goToGroupEdit = () => {
  router.push(`/community/groups/edit/${props.groupData.id}`)
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.qrcode-container {
  .qrcode-tabs {
    margin-bottom: 20px;
    text-align: center;
  }

  .qrcode-display {
    text-align: center;
    margin-bottom: 24px;

    .qrcode-image {
      img {
        width: 200px;
        height: 200px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        margin-bottom: 16px;
      }

      .qrcode-info {
        h4 {
          margin: 0 0 8px 0;
          color: #1e293b;
          font-weight: 600;
        }

        p {
          margin: 0 0 12px 0;
          color: #64748b;
          font-size: 14px;
        }

        .qrcode-url {
          margin-top: 12px;
          max-width: 400px;
          margin-left: auto;
          margin-right: auto;
        }

        .el-alert {
          margin-top: 12px;
          text-align: left;
        }
      }
    }

    .no-qrcode {
      padding: 40px 0;

      .tip-text {
        margin-top: 12px;
        color: #64748b;
        font-size: 14px;
      }
    }
  }

  .qrcode-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>