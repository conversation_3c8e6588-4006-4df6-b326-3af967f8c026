<!-- 四域导航架构 V2 - 现代化实现 -->
<template>
  <div class="domain-navigation-v2" :class="navigationClasses">
    <!-- 智能搜索 -->
    <SearchHeader 
      v-if="!collapsed" 
      v-model="searchState"
      @search="handleGlobalSearch"
      @shortcut="handleSearchShortcut"
    />

    <!-- 域导航容器 -->
    <div class="navigation-container" ref="navigationContainer">
      <!-- 虚拟滚动容器 -->
      <VirtualScrollList
        :items="visibleDomains"
        :item-height="getDynamicItemHeight"
        :buffer-size="5"
        class="domains-list"
      >
        <template #item="{ item: domain }">
          <DomainSection
            :domain="domain"
            :collapsed="collapsed"
            :active="activeDomain === domain.key"
            :expanded="expandedDomains.includes(domain.key)"
            @toggle="toggleDomain"
            @module-click="handleModuleClick"
            @badge-update="updateModuleBadge"
          />
        </template>
      </VirtualScrollList>
    </div>

    <!-- 智能推荐面板 -->
    <RecommendationPanel
      v-if="!collapsed && showRecommendations"
      :user-role="userRole"
      :usage-stats="usageStats"
      @action="handleRecommendedAction"
    />

    <!-- 快捷操作栏 -->
    <QuickActionBar
      v-if="!collapsed"
      :actions="contextualActions"
      @action="handleQuickAction"
    />

    <!-- 折叠状态提示 -->
    <CollapsedTooltips
      v-if="collapsed"
      :domains="visibleDomains"
      @navigate="handleTooltipNavigation"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { usePreferencesStore } from '@/stores/preferences'
import { usePerformanceMonitor } from '@/composables/usePerformanceMonitor'
import { useNavigationAnalytics } from '@/composables/useNavigationAnalytics'

// 子组件导入
import SearchHeader from './components/SearchHeader.vue'
import DomainSection from './components/DomainSection.vue'
import VirtualScrollList from '@/components/common/VirtualScrollList.vue'
import RecommendationPanel from './components/RecommendationPanel.vue'
import QuickActionBar from './components/QuickActionBar.vue'
import CollapsedTooltips from './components/CollapsedTooltips.vue'

// 配置和工具导入
import { DOMAIN_CONFIG, generateUserDomains } from '@/config/navigation-domains'
import { debounce, throttle } from '@/utils/performance'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'compact', 'minimal'].includes(value)
  }
})

const emit = defineEmits(['update:collapsed', 'navigation-change', 'domain-switch'])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const preferencesStore = usePreferencesStore()
const { trackNavigation, getUsageStats } = useNavigationAnalytics()
const { measurePerformance } = usePerformanceMonitor()

// 响应式状态
const searchState = ref({
  query: '',
  active: false,
  results: [],
  selectedIndex: 0
})

const navigationContainer = ref(null)
const activeDomain = ref('')
const expandedDomains = ref([])
const visibleDomains = ref([])
const usageStats = ref({})
const showRecommendations = ref(false)

// 计算属性
const userRole = computed(() => userStore.userInfo?.role || 'user')

const navigationClasses = computed(() => ({
  'navigation-collapsed': props.collapsed,
  'navigation-compact': props.variant === 'compact',
  'navigation-minimal': props.variant === 'minimal',
  'navigation-searching': searchState.value.active
}))

const contextualActions = computed(() => {
  const baseActions = [
    { key: 'create', icon: 'Plus', title: '快速创建', color: '#409EFF' },
    { key: 'export', icon: 'Download', title: '数据导出', color: '#67C23A' },
    { key: 'settings', icon: 'Setting', title: '系统设置', color: '#909399' }
  ]
  
  // 根据当前域和角色动态调整
  if (activeDomain.value === 'business-core') {
    baseActions.unshift({
      key: 'analytics', 
      icon: 'DataLine', 
      title: '数据分析', 
      color: '#E6A23C'
    })
  }
  
  return baseActions
})

// 核心方法
const initNavigation = async () => {
  const startTime = performance.now()
  
  try {
    // 生成用户专属导航
    const userDomains = await generateUserDomains(userRole.value)
    visibleDomains.value = userDomains
    
    // 恢复用户偏好
    await restoreNavigationState()
    
    // 设置初始激活状态
    updateActiveState()
    
    // 加载使用统计
    usageStats.value = await getUsageStats(userStore.userInfo?.id)
    
    // 决定是否显示推荐
    showRecommendations.value = shouldShowRecommendations()
    
  } catch (error) {
    console.error('导航初始化失败:', error)
  } finally {
    measurePerformance('navigation-init', performance.now() - startTime)
  }
}

const restoreNavigationState = async () => {
  const preferences = await preferencesStore.getNavigationPreferences()
  if (preferences) {
    expandedDomains.value = preferences.expandedDomains || []
    activeDomain.value = preferences.activeDomain || ''
  }
}

const saveNavigationState = debounce(async () => {
  await preferencesStore.saveNavigationPreferences({
    expandedDomains: expandedDomains.value,
    activeDomain: activeDomain.value,
    timestamp: Date.now()
  })
}, 1000)

const handleGlobalSearch = debounce((query) => {
  if (!query.trim()) {
    searchState.value.results = []
    return
  }
  
  const results = performIntelligentSearch(query)
  searchState.value.results = results
  searchState.value.selectedIndex = 0
}, 300)

const performIntelligentSearch = (query) => {
  const results = []
  const lowerQuery = query.toLowerCase()
  
  visibleDomains.value.forEach(domain => {
    domain.modules.forEach(module => {
      // 模块名称匹配
      if (module.title.toLowerCase().includes(lowerQuery)) {
        results.push({
          type: 'module',
          title: module.title,
          description: `${domain.title} > ${module.title}`,
          path: module.path,
          icon: module.icon,
          score: calculateRelevanceScore(module.title, query)
        })
      }
      
      // 子页面匹配
      if (module.children) {
        module.children.forEach(child => {
          if (child.title.toLowerCase().includes(lowerQuery)) {
            results.push({
              type: 'page',
              title: child.title,
              description: `${domain.title} > ${module.title} > ${child.title}`,
              path: child.path,
              icon: child.icon,
              score: calculateRelevanceScore(child.title, query)
            })
          }
        })
      }
    })
  })
  
  // 按相关性评分排序
  return results.sort((a, b) => b.score - a.score).slice(0, 10)
}

const calculateRelevanceScore = (text, query) => {
  const lowerText = text.toLowerCase()
  const lowerQuery = query.toLowerCase()
  
  if (lowerText === lowerQuery) return 100
  if (lowerText.startsWith(lowerQuery)) return 80
  if (lowerText.includes(lowerQuery)) return 60
  
  // 模糊匹配评分
  let score = 0
  for (let i = 0; i < lowerQuery.length; i++) {
    if (lowerText.includes(lowerQuery[i])) {
      score += 1
    }
  }
  return score
}

const toggleDomain = async (domainKey) => {
  const startTime = performance.now()
  
  if (props.collapsed) return
  
  const index = expandedDomains.value.indexOf(domainKey)
  if (index > -1) {
    expandedDomains.value.splice(index, 1)
  } else {
    expandedDomains.value.push(domainKey)
  }
  
  activeDomain.value = domainKey
  emit('domain-switch', domainKey)
  
  // 保存状态
  await saveNavigationState()
  
  // 性能监控
  measurePerformance('domain-toggle', performance.now() - startTime)
  
  // 分析追踪
  trackNavigation('domain-toggle', {
    domainKey,
    expanded: expandedDomains.value.includes(domainKey),
    userRole: userRole.value
  })
}

const handleModuleClick = async (module) => {
  const startTime = performance.now()
  
  try {
    let targetPath = module.path
    
    if (!targetPath && module.children?.length > 0) {
      targetPath = module.children[0].path
    }
    
    if (targetPath) {
      await router.push(targetPath)
      
      // 更新使用统计
      await updateUsageStats(module.key)
      
      emit('navigation-change', {
        type: 'module',
        target: module,
        timestamp: Date.now()
      })
    }
  } catch (error) {
    console.error('模块导航失败:', error)
  } finally {
    measurePerformance('module-navigation', performance.now() - startTime)
  }
}

const updateUsageStats = async (moduleKey) => {
  try {
    await preferencesStore.incrementModuleUsage(moduleKey)
    usageStats.value = await getUsageStats(userStore.userInfo?.id)
  } catch (error) {
    console.error('更新使用统计失败:', error)
  }
}

const getDynamicItemHeight = (item) => {
  const baseHeight = 60
  const moduleHeight = 40
  const childHeight = 32
  
  let height = baseHeight
  
  if (expandedDomains.value.includes(item.key)) {
    height += item.modules.length * moduleHeight
    
    item.modules.forEach(module => {
      if (module.children && !props.collapsed) {
        height += module.children.length * childHeight
      }
    })
  }
  
  return height
}

const shouldShowRecommendations = () => {
  // 根据用户使用模式决定是否显示推荐
  const totalUsage = Object.values(usageStats.value).reduce((sum, count) => sum + count, 0)
  return totalUsage > 10 && !props.collapsed
}

const updateActiveState = () => {
  const currentPath = route.path
  
  for (const domain of visibleDomains.value) {
    for (const module of domain.modules) {
      if (module.path === currentPath || 
          module.children?.some(child => child.path === currentPath)) {
        activeDomain.value = domain.key
        if (!expandedDomains.value.includes(domain.key)) {
          expandedDomains.value.push(domain.key)
        }
        return
      }
    }
  }
}

// 事件处理
const handleSearchShortcut = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    searchState.value.active = true
  }
}

const handleRecommendedAction = async (action) => {
  await handleModuleClick(action.module)
}

const handleQuickAction = async (action) => {
  const actionMap = {
    create: () => router.push('/community/add-enhanced'),
    export: () => router.push('/system/export'),
    analytics: () => router.push('/dashboard/analytics'),
    settings: () => router.push('/system/settings')
  }
  
  const handler = actionMap[action.key]
  if (handler) {
    await handler()
  }
}

const handleTooltipNavigation = async (path) => {
  await router.push(path)
}

const updateModuleBadge = (moduleKey, count) => {
  // 更新模块徽章数据
  const domain = visibleDomains.value.find(d => 
    d.modules.some(m => m.key === moduleKey)
  )
  
  if (domain) {
    const module = domain.modules.find(m => m.key === moduleKey)
    if (module) {
      module.badge = count
    }
  }
}

// 响应式监听
watch(() => route.path, () => {
  updateActiveState()
})

watch(() => userRole.value, () => {
  initNavigation()
})

watch(() => props.collapsed, (newValue) => {
  if (newValue) {
    searchState.value.active = false
  }
})

// 性能优化的滚动处理
const handleScroll = throttle((event) => {
  // 处理滚动事件，可以实现无限滚动等功能
  console.log('Navigation scroll:', event.target.scrollTop)
}, 100)

// 生命周期
onMounted(async () => {
  await initNavigation()
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleSearchShortcut)
  
  // 添加滚动监听（如果需要）
  if (navigationContainer.value) {
    navigationContainer.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleSearchShortcut)
  
  if (navigationContainer.value) {
    navigationContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style lang="scss" scoped>
.domain-navigation-v2 {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-radius: 0 16px 16px 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg,
      transparent 0%,
      rgba(99, 102, 241, 0.2) 50%,
      transparent 100%
    );
  }
  
  // 折叠状态
  &.navigation-collapsed {
    width: 80px;
    
    .navigation-container {
      padding: 8px;
    }
  }
  
  // 紧凑模式
  &.navigation-compact {
    .navigation-container {
      padding: 6px;
    }
  }
  
  // 最小模式
  &.navigation-minimal {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
  }
  
  // 搜索激活状态
  &.navigation-searching {
    .navigation-container {
      opacity: 0.7;
      filter: blur(1px);
    }
  }
}

.navigation-container {
  flex: 1;
  overflow: hidden;
  padding: 16px 12px;
  position: relative;
  
  // 自定义滚动条
  .domains-list {
    height: 100%;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.02);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg,
        rgba(99, 102, 241, 0.3) 0%,
        rgba(99, 102, 241, 0.6) 100%
      );
      border-radius: 3px;
      transition: all 0.3s ease;
      
      &:hover {
        background: linear-gradient(180deg,
          rgba(99, 102, 241, 0.6) 0%,
          rgba(99, 102, 241, 0.8) 100%
        );
      }
    }
  }
}

// 响应式设计
@media (max-width: 1440px) {
  .domain-navigation-v2:not(.navigation-collapsed) {
    width: 260px;
  }
}

@media (max-width: 1200px) {
  .domain-navigation-v2:not(.navigation-collapsed) {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .domain-navigation-v2 {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    z-index: 1000;
    transform: translateX(-100%);
    border-radius: 0;
    
    &:not(.navigation-collapsed) {
      transform: translateX(0);
      box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
    }
  }
}

// 动画效果
@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.domains-list {
  animation: fadeInSlide 0.6s ease-out;
}
</style>