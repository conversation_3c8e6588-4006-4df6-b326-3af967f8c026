/**
 * 分销员工具类库单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  formatMoney,
  formatNumber,
  formatPercentage,
  formatDate,
  formatRelativeTime,
  validatePhone,
  validateEmail,
  validateIdCard,
  validateCustomerLevel,
  validateCustomerStatus,
  getCustomerLevelInfo,
  getCustomerStatusInfo,
  calculateCommission,
  generateCustomerCode,
  generateOrderCode,
  debounce,
  throttle,
  deepClone,
  objectDiff,
  LocalStorageManager,
  CUSTOMER_LEVELS,
  CUSTOMER_STATUSES
} from '@/utils/distributorUtils'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('distributorUtils - 数据格式化工具', () => {
  describe('formatMoney', () => {
    it('应该正确格式化金额', () => {
      expect(formatMoney(1234.56)).toBe('¥1,234.56')
      expect(formatMoney(1234567.89)).toBe('¥1,234,567.89')
      expect(formatMoney(0)).toBe('¥0.00')
      expect(formatMoney('1234.56')).toBe('¥1,234.56')
    })

    it('应该支持自定义小数位数', () => {
      expect(formatMoney(1234.567, 1)).toBe('¥1,234.6')
      expect(formatMoney(1234.567, 3)).toBe('¥1,234.567')
    })

    it('应该支持隐藏货币符号', () => {
      expect(formatMoney(1234.56, 2, false)).toBe('1,234.56')
    })

    it('应该处理无效输入', () => {
      expect(formatMoney(null)).toBe('¥0.00')
      expect(formatMoney(undefined)).toBe('¥0.00')
      expect(formatMoney('invalid')).toBe('¥0.00')
    })
  })

  describe('formatNumber', () => {
    it('应该正确格式化数字', () => {
      expect(formatNumber(1234)).toBe('1.2k')
      expect(formatNumber(12345)).toBe('1.2万')
      expect(formatNumber(123456789)).toBe('1.2亿')
      expect(formatNumber(999)).toBe('999')
    })

    it('应该支持自定义小数位数', () => {
      expect(formatNumber(12345, 2)).toBe('1.23万')
      expect(formatNumber(12345, 0)).toBe('1万')
    })

    it('应该处理无效输入', () => {
      expect(formatNumber(null)).toBe('0')
      expect(formatNumber('invalid')).toBe('0')
    })
  })

  describe('formatPercentage', () => {
    it('应该正确格式化百分比', () => {
      expect(formatPercentage(12.345)).toBe('12.3%')
      expect(formatPercentage(0)).toBe('0.0%')
      expect(formatPercentage(100)).toBe('100.0%')
    })

    it('应该支持自定义小数位数', () => {
      expect(formatPercentage(12.345, 2)).toBe('12.35%')
      expect(formatPercentage(12.345, 0)).toBe('12%')
    })
  })

  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2024-01-15 10:30:45')
      expect(formatDate(date)).toBe('2024-01-15')
      expect(formatDate(date, 'YYYY-MM-DD HH:mm:ss')).toBe('2024-01-15 10:30:45')
    })

    it('应该处理字符串日期', () => {
      expect(formatDate('2024-01-15')).toBe('2024-01-15')
    })

    it('应该处理无效日期', () => {
      expect(formatDate(null)).toBe('-')
      expect(formatDate('invalid')).toBe('-')
    })
  })

  describe('formatRelativeTime', () => {
    beforeEach(() => {
      vi.useFakeTimers()
      vi.setSystemTime(new Date('2024-01-15 12:00:00'))
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该正确格式化相对时间', () => {
      expect(formatRelativeTime(new Date('2024-01-15 11:59:30'))).toBe('刚刚')
      expect(formatRelativeTime(new Date('2024-01-15 11:55:00'))).toBe('5分钟前')
      expect(formatRelativeTime(new Date('2024-01-15 10:00:00'))).toBe('2小时前')
      expect(formatRelativeTime(new Date('2024-01-14 12:00:00'))).toBe('1天前')
    })
  })
})

describe('distributorUtils - 数据验证工具', () => {
  describe('validatePhone', () => {
    it('应该验证有效手机号', () => {
      expect(validatePhone('13800138001')).toBe(true)
      expect(validatePhone('15912345678')).toBe(true)
      expect(validatePhone('18888888888')).toBe(true)
    })

    it('应该拒绝无效手机号', () => {
      expect(validatePhone('12800138001')).toBe(false) // 不是1开头的有效号段
      expect(validatePhone('1380013800')).toBe(false) // 位数不对
      expect(validatePhone('138001380011')).toBe(false) // 位数过多
      expect(validatePhone('abc')).toBe(false)
      expect(validatePhone('')).toBe(false)
      expect(validatePhone(null)).toBe(false)
    })
  })

  describe('validateEmail', () => {
    it('应该验证有效邮箱', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('应该拒绝无效邮箱', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('test@')).toBe(false)
      expect(validateEmail('@example.com')).toBe(false)
      expect(validateEmail('')).toBe(false)
      expect(validateEmail(null)).toBe(false)
    })
  })

  describe('validateCustomerLevel', () => {
    it('应该验证有效客户等级', () => {
      expect(validateCustomerLevel('A')).toBe(true)
      expect(validateCustomerLevel('B')).toBe(true)
      expect(validateCustomerLevel('C')).toBe(true)
      expect(validateCustomerLevel('D')).toBe(true)
    })

    it('应该拒绝无效客户等级', () => {
      expect(validateCustomerLevel('E')).toBe(false)
      expect(validateCustomerLevel('a')).toBe(false)
      expect(validateCustomerLevel('')).toBe(false)
      expect(validateCustomerLevel(null)).toBe(false)
    })
  })

  describe('validateCustomerStatus', () => {
    it('应该验证有效客户状态', () => {
      expect(validateCustomerStatus('active')).toBe(true)
      expect(validateCustomerStatus('inactive')).toBe(true)
      expect(validateCustomerStatus('potential')).toBe(true)
      expect(validateCustomerStatus('lost')).toBe(true)
    })

    it('应该拒绝无效客户状态', () => {
      expect(validateCustomerStatus('unknown')).toBe(false)
      expect(validateCustomerStatus('')).toBe(false)
      expect(validateCustomerStatus(null)).toBe(false)
    })
  })
})

describe('distributorUtils - 数据处理工具', () => {
  describe('getCustomerLevelInfo', () => {
    it('应该返回正确的等级信息', () => {
      const levelA = getCustomerLevelInfo('A')
      expect(levelA.name).toBe('A级客户')
      expect(levelA.commission_rate).toBe(20.0)
      expect(levelA.color).toBe('#F56C6C')
    })

    it('应该返回默认等级信息', () => {
      const defaultLevel = getCustomerLevelInfo('invalid')
      expect(defaultLevel.name).toBe('C级客户')
      expect(defaultLevel.commission_rate).toBe(10.0)
    })
  })

  describe('getCustomerStatusInfo', () => {
    it('应该返回正确的状态信息', () => {
      const statusActive = getCustomerStatusInfo('active')
      expect(statusActive.name).toBe('活跃')
      expect(statusActive.color).toBe('success')
    })

    it('应该返回默认状态信息', () => {
      const defaultStatus = getCustomerStatusInfo('invalid')
      expect(defaultStatus.name).toBe('潜在')
      expect(defaultStatus.color).toBe('primary')
    })
  })

  describe('calculateCommission', () => {
    it('应该正确计算佣金', () => {
      expect(calculateCommission(1000, 'A')).toBe(200) // 20%
      expect(calculateCommission(1000, 'B')).toBe(150) // 15%
      expect(calculateCommission(1000, 'C')).toBe(100) // 10%
      expect(calculateCommission(1000, 'D')).toBe(50)  // 5%
    })

    it('应该支持自定义佣金率', () => {
      expect(calculateCommission(1000, 'A', 25)).toBe(250)
    })

    it('应该处理无效输入', () => {
      expect(calculateCommission('invalid', 'A')).toBe(0)
      expect(calculateCommission(1000, 'invalid')).toBe(100) // 默认C级
    })
  })

  describe('generateCustomerCode', () => {
    it('应该生成正确格式的客户编号', () => {
      const code = generateCustomerCode()
      expect(code).toMatch(/^C\d{8}[A-Z0-9]{4}$/)
    })

    it('应该支持自定义前缀', () => {
      const code = generateCustomerCode('CUST')
      expect(code).toMatch(/^CUST\d{8}[A-Z0-9]{4}$/)
    })
  })

  describe('generateOrderCode', () => {
    beforeEach(() => {
      vi.useFakeTimers()
      vi.setSystemTime(new Date('2024-01-15'))
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该生成正确格式的订单编号', () => {
      const code = generateOrderCode()
      expect(code).toMatch(/^ORD20240115\d{6}$/)
    })

    it('应该支持自定义前缀', () => {
      const code = generateOrderCode('ORDER')
      expect(code).toMatch(/^ORDER20240115\d{6}$/)
    })
  })
})

describe('distributorUtils - 性能优化工具', () => {
  describe('debounce', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该延迟执行函数', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 300)

      debouncedFn()
      expect(mockFn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(300)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('应该取消之前的调用', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 300)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      vi.advanceTimersByTime(300)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('应该支持立即执行', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 300, true)

      debouncedFn()
      expect(mockFn).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(300)
      expect(mockFn).toHaveBeenCalledTimes(1)
    })
  })

  describe('throttle', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该限制函数执行频率', () => {
      const mockFn = vi.fn()
      const throttledFn = throttle(mockFn, 300)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(mockFn).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(300)
      throttledFn()
      expect(mockFn).toHaveBeenCalledTimes(2)
    })
  })

  describe('deepClone', () => {
    it('应该深拷贝对象', () => {
      const original = {
        name: '张三',
        age: 30,
        address: {
          city: '北京',
          district: '朝阳区'
        },
        hobbies: ['读书', '游泳']
      }

      const cloned = deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.address).not.toBe(original.address)
      expect(cloned.hobbies).not.toBe(original.hobbies)
    })

    it('应该处理基本类型', () => {
      expect(deepClone(null)).toBe(null)
      expect(deepClone(undefined)).toBe(undefined)
      expect(deepClone(123)).toBe(123)
      expect(deepClone('test')).toBe('test')
      expect(deepClone(true)).toBe(true)
    })

    it('应该处理日期对象', () => {
      const date = new Date('2024-01-15')
      const clonedDate = deepClone(date)

      expect(clonedDate).toEqual(date)
      expect(clonedDate).not.toBe(date)
    })
  })

  describe('objectDiff', () => {
    it('应该正确比较对象差异', () => {
      const obj1 = { name: '张三', age: 30, city: '北京' }
      const obj2 = { name: '李四', age: 30, city: '上海' }

      const diff = objectDiff(obj1, obj2)

      expect(diff).toEqual({
        name: { old: '张三', new: '李四' },
        city: { old: '北京', new: '上海' }
      })
    })

    it('应该处理相同对象', () => {
      const obj1 = { name: '张三', age: 30 }
      const obj2 = { name: '张三', age: 30 }

      const diff = objectDiff(obj1, obj2)
      expect(diff).toEqual({})
    })
  })
})

describe('distributorUtils - 本地存储工具', () => {
  let storageManager

  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
    localStorageMock.clear.mockClear()
    storageManager = new LocalStorageManager('test_')
  })

  describe('LocalStorageManager', () => {
    it('应该正确设置存储项', () => {
      const testData = { name: '张三', age: 30 }
      storageManager.setItem('user', testData)

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test_user',
        expect.stringContaining('"value":{"name":"张三","age":30}')
      )
    })

    it('应该正确获取存储项', () => {
      const testData = {
        value: { name: '张三', age: 30 },
        timestamp: Date.now(),
        expiry: null
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(testData))

      const result = storageManager.getItem('user')
      expect(result).toEqual({ name: '张三', age: 30 })
    })

    it('应该处理过期数据', () => {
      const expiredData = {
        value: { name: '张三' },
        timestamp: Date.now() - 10000,
        expiry: Date.now() - 5000 // 已过期
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredData))

      const result = storageManager.getItem('user', 'default')
      expect(result).toBe('default')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test_user')
    })

    it('应该返回默认值', () => {
      localStorageMock.getItem.mockReturnValue(null)

      const result = storageManager.getItem('nonexistent', 'default')
      expect(result).toBe('default')
    })

    it('应该正确移除存储项', () => {
      storageManager.removeItem('user')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test_user')
    })
  })
})

describe('distributorUtils - 常量定义', () => {
  it('应该包含完整的客户等级配置', () => {
    expect(CUSTOMER_LEVELS).toHaveProperty('A')
    expect(CUSTOMER_LEVELS).toHaveProperty('B')
    expect(CUSTOMER_LEVELS).toHaveProperty('C')
    expect(CUSTOMER_LEVELS).toHaveProperty('D')

    expect(CUSTOMER_LEVELS.A.commission_rate).toBe(20.0)
    expect(CUSTOMER_LEVELS.B.commission_rate).toBe(15.0)
    expect(CUSTOMER_LEVELS.C.commission_rate).toBe(10.0)
    expect(CUSTOMER_LEVELS.D.commission_rate).toBe(5.0)
  })

  it('应该包含完整的客户状态配置', () => {
    expect(CUSTOMER_STATUSES).toHaveProperty('active')
    expect(CUSTOMER_STATUSES).toHaveProperty('inactive')
    expect(CUSTOMER_STATUSES).toHaveProperty('potential')
    expect(CUSTOMER_STATUSES).toHaveProperty('lost')

    expect(CUSTOMER_STATUSES.active.color).toBe('success')
    expect(CUSTOMER_STATUSES.inactive.color).toBe('warning')
    expect(CUSTOMER_STATUSES.potential.color).toBe('primary')
    expect(CUSTOMER_STATUSES.lost.color).toBe('danger')
  })
})