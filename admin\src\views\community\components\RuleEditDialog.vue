<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑规则' : '创建新规则'"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="form.name" placeholder="例如：回复“入群”关键词" />
      </el-form-item>

      <el-form-item label="规则类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择规则类型" style="width: 100%">
          <el-option label="关键词自动回复" value="keyword_reply" />
          <el-option label="新成员欢迎语" value="new_member_welcome" />
          <el-option label="违规自动踢人" value="auto_kick" />
          <el-option label="定时发送消息" value="scheduled_message" />
        </el-select>
      </el-form-item>

      <el-form-item label="目标群组" prop="target_group_ids">
        <el-select
          v-model="form.target_group_ids"
          multiple
          filterable
          placeholder="选择目标群组（可留空表示所有）"
          style="width: 100%"
        >
          <el-option label="所有群组" value="all" />
          <!-- 实际项目中应从API获取群组列表 -->
          <el-option v-for="group in groupList" :key="group.id" :label="group.name" :value="group.id" />
        </el-select>
      </el-form-item>

      <!-- 动态配置项 -->
      <div class="dynamic-config">
        <template v-if="form.type === 'keyword_reply'">
          <el-form-item label="关键词" prop="config.keyword">
            <el-input v-model="form.config.keyword" placeholder="多个关键词用逗号隔开" />
          </el-form-item>
          <el-form-item label="回复内容" prop="config.reply_content">
            <el-input type="textarea" v-model="form.config.reply_content" :rows="3" placeholder="请输入回复内容" />
          </el-form-item>
        </template>

        <template v-if="form.type === 'new_member_welcome'">
          <el-form-item label="欢迎语" prop="config.message">
            <el-input type="textarea" v-model="form.config.message" :rows="3" placeholder="可用 {memberName} 指代新成员昵称" />
          </el-form-item>
        </template>

        <template v-if="form.type === 'auto_kick'">
          <el-form-item label="触发条件" prop="config.trigger">
            <el-select v-model="form.config.trigger" placeholder="请选择触发条件" style="width: 100%">
              <el-option label="消息包含链接" value="contains_url" />
              <el-option label="消息包含敏感词" value="contains_sensitive_word" />
              <el-option label="发送文件" value="sends_file" />
            </el-select>
          </el-form-item>
           <el-form-item v-if="form.config.trigger === 'contains_sensitive_word'" label="敏感词列表" prop="config.sensitive_words">
            <el-input v-model="form.config.sensitive_words" placeholder="多个敏感词用逗号隔开" />
          </el-form-item>
          <el-form-item label="踢出提示" prop="config.kick_reason">
            <el-input v-model="form.config.kick_reason" placeholder="例如：您因发送广告被移出群聊" />
          </el-form-item>
        </template>

        <template v-if="form.type === 'scheduled_message'">
          <el-form-item label="CRON表达式" prop="config.cron">
            <el-input v-model="form.config.cron" placeholder="例如：0 20 * * * (每天晚上8点)" />
          </el-form-item>
          <el-form-item label="发送内容" prop="config.message">
            <el-input type="textarea" v-model="form.config.message" :rows="3" placeholder="请输入要定时发送的内容" />
          </el-form-item>
        </template>
      </div>

      <el-form-item label="是否启用">
        <el-switch v-model="form.enabled" />
      </el-form-item>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: Boolean,
  ruleData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)
const groupList = ref([]) // 模拟群组列表

const isEdit = computed(() => !!props.ruleData)

const initialFormState = {
  name: '',
  type: '',
  target_group_ids: [],
  enabled: true,
  config: {},
}

const form = reactive({ ...initialFormState })

const rules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
  'config.keyword': [{ required: true, message: '请输入关键词', trigger: 'blur' }],
  'config.reply_content': [{ required: true, message: '请输入回复内容', trigger: 'blur' }],
  'config.message': [{ required: true, message: '请输入内容', trigger: 'blur' }],
  'config.trigger': [{ required: true, message: '请选择触发条件', trigger: 'change' }],
  'config.kick_reason': [{ required: true, message: '请输入踢出提示', trigger: 'blur' }],
  'config.cron': [{ required: true, message: '请输入CRON表达式', trigger: 'blur' }],
}

watch(() => props.ruleData, (newVal) => {
  if (newVal) {
    Object.assign(form, JSON.parse(JSON.stringify(newVal)))
  } else {
    Object.assign(form, JSON.parse(JSON.stringify(initialFormState)))
  }
})

watch(() => form.type, () => {
  // 重置配置项，避免类型切换时数据残留
  form.config = {}
})

const fetchGroupList = () => {
  // 模拟API调用
  setTimeout(() => {
    groupList.value = [
      { id: 101, name: '创业精英交流群' },
      { id: 102, name: '技术爱好者社区' },
      { id: 103, name: '产品经理学习圈' },
    ]
  }, 300)
}

const handleClose = () => {
  formRef.value.resetFields()
  Object.assign(form, JSON.parse(JSON.stringify(initialFormState)))
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true
      // 模拟API提交
      setTimeout(() => {
        submitting.value = false
        ElMessage.success(isEdit.value ? '规则更新成功' : '规则创建成功')
        emit('success', form)
        emit('update:modelValue', false)
      }, 500)
    }
  })
}

fetchGroupList()
</script>

<style scoped>
.dynamic-config {
  padding: 10px 20px;
  margin-bottom: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>