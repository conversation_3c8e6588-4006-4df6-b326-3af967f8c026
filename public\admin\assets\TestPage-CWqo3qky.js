import{_ as s}from"./index-DtXAftX0.js";import{af as t,k as a,l as e,t as o,E as r,z as l,D as n}from"./vue-vendor-Dy164gUc.js";import{at as p}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const m={class:"test-page"},u=s({__name:"TestPage",setup(s){const u=t(),i=()=>{u.go(-1)};return(s,t)=>{const u=p;return e(),a("div",m,[t[1]||(t[1]=o("h1",null,"测试页面",-1)),t[2]||(t[2]=o("p",null,"如果您能看到这个页面，说明路由系统工作正常。",-1)),r(u,{type:"primary",onClick:i},{default:l(()=>t[0]||(t[0]=[n("返回",-1)])),_:1,__:[0]})])}}},[["__scopeId","data-v-c06e9cfd"]]);export{u as default};
