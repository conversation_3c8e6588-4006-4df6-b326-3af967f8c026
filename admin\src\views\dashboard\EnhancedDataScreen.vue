<template>
  <div class="enhanced-data-screen">
    <!-- 科技风格背景 -->
    <div class="tech-background">
      <div class="grid-overlay"></div>
      <div class="particle-system">
        <div v-for="i in 30" :key="i" class="particle"></div>
      </div>
    </div>

    <!-- 顶部控制栏 -->
    <div class="screen-header">
      <div class="header-left">
        <div class="brand-logo">
          <div class="logo-core"></div>
          <span class="brand-text">LinkHub Pro 数据中心</span>
        </div>
      </div>
      <div class="header-center">
        <h1 class="screen-title">实时运营数据大屏</h1>
      </div>
      <div class="header-right">
        <div class="time-display">{{ currentTime }}</div>
        <div class="control-buttons">
          <button class="ctrl-btn" @click="refreshData">刷新</button>
          <button class="ctrl-btn" @click="exportData">导出</button>
        </div>
      </div>
    </div>

    <!-- 核心指标区域 -->
    <div class="metrics-section">
      <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
        <div class="metric-header">
          <div class="metric-icon">{{ metric.icon }}</div>
          <div class="metric-trend" :class="metric.trend.type">
            {{ metric.trend.value }}%
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
        </div>
        <div class="metric-chart">
          <div class="mini-chart">
            <div v-for="(point, index) in metric.chartData" 
                 :key="index" 
                 class="chart-bar"
                 :style="{ height: point + '%' }">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="charts-section">
      <!-- 收入趋势图 -->
      <div class="chart-container large">
        <div class="chart-header">
          <h3>收入趋势分析</h3>
          <div class="chart-controls">
            <button v-for="period in timePeriods" 
                    :key="period.value"
                    :class="['period-btn', { active: selectedPeriod === period.value }]"
                    @click="selectedPeriod = period.value">
              {{ period.label }}
            </button>
          </div>
        </div>
        <div class="chart-content">
          <div class="revenue-chart">
            <canvas ref="revenueCanvas" width="800" height="300"></canvas>
          </div>
        </div>
      </div>

      <!-- 用户活跃度 -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>用户活跃度</h3>
        </div>
        <div class="chart-content">
          <div class="activity-chart">
            <div class="activity-ring">
              <svg viewBox="0 0 200 200" class="ring-svg">
                <circle cx="100" cy="100" r="80" 
                        fill="none" 
                        stroke="rgba(59, 130, 246, 0.2)" 
                        stroke-width="8"/>
                <circle cx="100" cy="100" r="80" 
                        fill="none" 
                        stroke="url(#activityGradient)" 
                        stroke-width="8"
                        stroke-dasharray="502"
                        :stroke-dashoffset="502 - (502 * activityPercentage / 100)"
                        class="activity-progress"/>
                <defs>
                  <linearGradient id="activityGradient">
                    <stop offset="0%" stop-color="#3b82f6"/>
                    <stop offset="100%" stop-color="#06b6d4"/>
                  </linearGradient>
                </defs>
              </svg>
              <div class="ring-center">
                <div class="activity-value">{{ activityPercentage }}%</div>
                <div class="activity-label">活跃度</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const currentTime = ref('')
const selectedPeriod = ref('7d')
const isRefreshing = ref(false)
const activityPercentage = ref(78)

// 时间周期选项
const timePeriods = [
  { label: '今日', value: '1d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '总收入',
    value: '¥1,254,890',
    icon: '💰',
    trend: { type: 'up', value: 12.5 },
    chartData: [20, 35, 28, 45, 38, 52, 48, 60, 55, 70]
  },
  {
    key: 'users',
    label: '活跃用户',
    value: '8,652',
    icon: '👥',
    trend: { type: 'up', value: 8.3 },
    chartData: [30, 25, 40, 35, 50, 45, 60, 55, 65, 58]
  },
  {
    key: 'orders',
    label: '订单总数',
    value: '15,428',
    icon: '📦',
    trend: { type: 'up', value: 15.2 },
    chartData: [25, 30, 35, 28, 42, 38, 55, 50, 58, 62]
  },
  {
    key: 'conversion',
    label: '转化率',
    value: '23.8%',
    icon: '📈',
    trend: { type: 'down', value: 2.1 },
    chartData: [45, 42, 48, 40, 38, 35, 40, 38, 42, 45]
  }
])

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 刷新数据
const refreshData = async () => {
  isRefreshing.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新指标数据
    coreMetrics.value.forEach(metric => {
      const change = (Math.random() - 0.5) * 20
      metric.trend.value = Math.abs(Math.round(change * 10) / 10)
      metric.trend.type = change > 0 ? 'up' : 'down'
    })
    
    activityPercentage.value = Math.round(Math.random() * 30 + 60)
    
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

// 导出数据
const exportData = () => {
  try {
    const data = {
      exportTime: new Date().toISOString(),
      metrics: coreMetrics.value,
      activityPercentage: activityPercentage.value
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `数据大屏_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

// 生命周期
let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
@import '@/styles/data-screen-responsive.css';
.enhanced-data-screen {
  min-height: 100vh;
  background: 
    radial-gradient(ellipse at top, rgba(16, 185, 129, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #e2e8f0;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 科技风格背景 */
.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.particle-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, #3b82f6, transparent);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
  animation-delay: -2s;
  background: radial-gradient(circle, #06b6d4, transparent);
}

.particle:nth-child(3n) {
  animation-delay: -4s;
  background: radial-gradient(circle, #8b5cf6, transparent);
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) translateX(0px);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  50% { 
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
}

/* 为每个粒子设置随机位置 */
.particle:nth-child(1) { left: 10%; top: 20%; }
.particle:nth-child(2) { left: 20%; top: 80%; }
.particle:nth-child(3) { left: 30%; top: 40%; }
.particle:nth-child(4) { left: 40%; top: 70%; }
.particle:nth-child(5) { left: 50%; top: 30%; }
.particle:nth-child(6) { left: 60%; top: 90%; }
.particle:nth-child(7) { left: 70%; top: 10%; }
.particle:nth-child(8) { left: 80%; top: 60%; }
.particle:nth-child(9) { left: 90%; top: 35%; }
.particle:nth-child(10) { left: 15%; top: 75%; }

/* 顶部控制栏 */
.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 10;
}

.header-left .brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-core {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #8b5cf6);
  border-radius: 50%;
  position: relative;
  animation: logoSpin 8s linear infinite;
}

.logo-core::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: #0f172a;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

@keyframes logoSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.brand-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.screen-title {
  margin: 0;
  font-size: 28px;
  font-weight: 800;
  text-align: center;
  background: linear-gradient(135deg, #60a5fa, #34d399, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
  animation: titlePulse 3s ease-in-out infinite;
}

@keyframes titlePulse {
  0%, 100% { 
    filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.5));
    transform: scale(1);
  }
  50% { 
    filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.8));
    transform: scale(1.02);
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.time-display {
  font-size: 18px;
  font-weight: 600;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.control-buttons {
  display: flex;
  gap: 12px;
}

.ctrl-btn {
  padding: 8px 16px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #60a5fa;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 14px;
  font-weight: 500;
}

.ctrl-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #93c5fd;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 核心指标区域 */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 32px;
  position: relative;
  z-index: 5;
}

.metric-card {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
  border-radius: 16px 16px 0 0;
}

.metric-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon {
  font-size: 32px;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-trend.up {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.metric-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.metric-trend.up::before {
  content: '↗';
}

.metric-trend.down::before {
  content: '↘';
}

.metric-value {
  font-size: 32px;
  font-weight: 800;
  color: #e2e8f0;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
}

.metric-label {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

.metric-chart {
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 80px;
  height: 40px;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 100%;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, rgba(59, 130, 246, 0.8), rgba(59, 130, 246, 0.3));
  border-radius: 1px;
  min-height: 4px;
  transition: all 0.3s ease;
}

.metric-card:hover .chart-bar {
  background: linear-gradient(to top, rgba(59, 130, 246, 1), rgba(59, 130, 246, 0.5));
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  padding: 0 32px 32px;
  position: relative;
  z-index: 5;
}

.chart-container {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-container:hover {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 6px 12px;
  background: transparent;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
}

.period-btn:hover,
.period-btn.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #60a5fa;
}

.chart-content {
  padding: 24px;
}

/* 活跃度环形图 */
.activity-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
}

.activity-ring {
  position: relative;
  width: 200px;
  height: 200px;
}

.ring-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.activity-progress {
  transition: stroke-dashoffset 1s ease-in-out;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.ring-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.activity-value {
  font-size: 32px;
  font-weight: 800;
  color: #60a5fa;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.activity-label {
  font-size: 14px;
  color: #94a3b8;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .screen-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }
  
  .metrics-section {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 16px;
  }
  
  .charts-section {
    padding: 0 16px 16px;
    gap: 16px;
  }
  
  .metric-card {
    padding: 16px;
  }
  
  .metric-value {
    font-size: 24px;
  }
  
  .screen-title {
    font-size: 20px;
  }
}
</style>