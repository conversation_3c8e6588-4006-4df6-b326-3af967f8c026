<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">配色测试页面</h1>
        <p class="mt-2 text-gray-600">测试各种配色组合的可读性</p>
      </div>

      <!-- 白色背景测试 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">白色背景测试</h2>
          <p class="text-gray-800 mb-2">这是深灰色文字 (text-gray-800)</p>
          <p class="text-gray-600 mb-2">这是中灰色文字 (text-gray-600)</p>
          <p class="text-gray-400 mb-2">这是浅灰色文字 (text-gray-400)</p>
          <p class="text-white mb-2">这是白色文字 (应该被修复为深色)</p>
          <p class="text-gray-50 mb-2">这是极浅灰色文字 (应该被修复)</p>
          <p class="text-gray-100 mb-2">这是浅灰色文字 (应该被修复)</p>
        </div>

        <div class="bg-gray-50 p-6 rounded-lg shadow">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">浅灰背景测试</h2>
          <p class="text-gray-900 mb-2">这是深灰色文字 (text-gray-900)</p>
          <p class="text-gray-700 mb-2">这是中深灰色文字 (text-gray-700)</p>
          <p class="text-gray-500 mb-2">这是中灰色文字 (text-gray-500)</p>
          <p class="text-white mb-2">这是白色文字 (应该被修复)</p>
        </div>
      </div>

      <!-- 卡片组件测试 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">统计卡片</h3>
          <p class="text-2xl font-bold text-gray-900">¥12,345</p>
          <p class="text-sm text-gray-600">今日收益</p>
          <p class="text-white">白色文字测试</p>
        </div>

        <div class="modern-card">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">现代卡片</h3>
          <p class="text-2xl font-bold text-gray-900">1,234</p>
          <p class="text-sm text-gray-600">总订单</p>
          <p class="text-white">白色文字测试</p>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">普通白色卡片</h3>
          <p class="text-2xl font-bold text-gray-900">567</p>
          <p class="text-sm text-gray-600">用户数量</p>
          <p class="text-white">白色文字测试</p>
        </div>
      </div>

      <!-- 按钮测试 -->
      <div class="bg-white p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">按钮测试</h2>
        <div class="flex flex-wrap gap-4">
          <button class="btn btn-primary">主要按钮</button>
          <button class="btn btn-secondary">次要按钮</button>
          <button class="modern-btn primary">现代主要按钮</button>
          <button class="modern-btn secondary">现代次要按钮</button>
          <button class="bg-white text-white px-4 py-2 rounded">白色背景白色文字</button>
        </div>
      </div>

      <!-- 表单测试 -->
      <div class="bg-white p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">表单测试</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              用户名
            </label>
            <input
              type="text"
              class="input"
              placeholder="请输入用户名"
              value="测试文字"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-white mb-1">
              白色标签 (应该被修复)
            </label>
            <input
              type="text"
              class="form-input"
              placeholder="请输入内容"
            />
          </div>
        </div>
      </div>

      <!-- 表格测试 -->
      <div class="bg-white p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">表格测试</h2>
        <table class="table w-full">
          <thead>
            <tr>
              <th class="text-left">姓名</th>
              <th class="text-left">邮箱</th>
              <th class="text-left">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>张三</td>
              <td><EMAIL></td>
              <td class="text-success">活跃</td>
            </tr>
            <tr>
              <td class="text-white">白色文字</td>
              <td><EMAIL></td>
              <td class="text-error">禁用</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 状态颜色测试 -->
      <div class="bg-white p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">状态颜色测试</h2>
        <div class="space-y-2">
          <p class="text-success">成功状态文字</p>
          <p class="text-error">错误状态文字</p>
          <p class="text-warning">警告状态文字</p>
          <p class="text-info">信息状态文字</p>
          <p class="text-green-500">绿色文字</p>
          <p class="text-red-500">红色文字</p>
          <p class="text-yellow-500">黄色文字</p>
          <p class="text-blue-500">蓝色文字</p>
        </div>
      </div>

      <!-- 链接测试 -->
      <div class="bg-white p-6 rounded-lg shadow mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">链接测试</h2>
        <div class="space-y-2">
          <p><a href="#" class="text-primary-600">主色调链接</a></p>
          <p><a href="#" class="text-blue-600">蓝色链接</a></p>
          <p><a href="#" class="text-white">白色链接 (应该被修复)</a></p>
        </div>
      </div>

      <!-- 内联样式测试 -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">内联样式测试</h2>
        <div class="space-y-2">
          <p style="color: white;">内联白色文字 (应该被修复)</p>
          <p style="color: #fff;">内联#fff文字 (应该被修复)</p>
          <p style="color: #ffffff;">内联#ffffff文字 (应该被修复)</p>
          <p style="background: white; color: white;">白色背景白色文字</p>
          <div style="background-color: white;">
            <p style="color: white;">嵌套白色文字</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  title: '配色测试',
  description: '测试页面配色和文字可读性'
})

// 头部设置
useHead({
  title: '配色测试 - FFJQ系统',
  meta: [
    { name: 'description', content: '测试页面配色和文字可读性' }
  ]
})
</script>

<style scoped>
/* 测试页面特定样式 */
.card {
  @apply bg-white p-6 rounded-lg shadow;
}

.modern-card {
  @apply bg-white p-6 rounded-lg shadow-lg;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.btn {
  @apply px-4 py-2 rounded font-medium transition-colors;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.modern-btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all;
}

.modern-btn.primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}

.modern-btn.secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50;
}

.input,
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.table {
  @apply min-w-full;
}

.table th {
  @apply px-4 py-2 bg-gray-50 font-semibold text-gray-700;
}

.table td {
  @apply px-4 py-2 border-t border-gray-200;
}

.text-success {
  @apply text-green-600;
}

.text-error {
  @apply text-red-600;
}

.text-warning {
  @apply text-yellow-600;
}

.text-info {
  @apply text-blue-600;
}
</style>
