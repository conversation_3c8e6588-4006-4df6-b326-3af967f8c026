<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

/**
 * 系统设置控制器
 * 管理系统配置和设置
 */
class SystemController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('role:admin');
    }

    /**
     * 获取系统设置
     */
    public function getSettings(Request $request)
    {
        try {
            $category = $request->input('category');
            
            $query = SystemSetting::query();
            
            if ($category) {
                $query->where('category', $category);
            }
            
            $settings = $query->get()->groupBy('category');
            
            // 格式化设置数据
            $formattedSettings = [];
            foreach ($settings as $cat => $items) {
                $formattedSettings[$cat] = [];
                foreach ($items as $item) {
                    $formattedSettings[$cat][$item->key] = [
                        'value' => $item->value,
                        'type' => $item->type,
                        'description' => $item->description,
                        'options' => $item->options,
                        'required' => $item->required,
                        'updated_at' => $item->updated_at,
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => '系统设置获取成功',
                'data' => $formattedSettings,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统设置获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 保存系统设置
     */
    public function saveSettings(Request $request)
    {
        try {
            $settings = $request->input('settings', []);
            $updated = [];

            foreach ($settings as $category => $categorySettings) {
                foreach ($categorySettings as $key => $data) {
                    $value = $data['value'] ?? $data;
                    
                    // 验证设置值
                    if (!$this->validateSettingValue($category, $key, $value)) {
                        return response()->json([
                            'success' => false,
                            'message' => "设置项 {$category}.{$key} 的值无效",
                        ], 422);
                    }

                    SystemSetting::updateOrCreate(
                        ['category' => $category, 'key' => $key],
                        [
                            'value' => is_array($value) ? json_encode($value) : $value,
                            'updated_by' => Auth::id(),
                        ]
                    );

                    $updated[] = "{$category}.{$key}";
                }
            }

            // 清除设置缓存
            Cache::tags(['system_settings'])->flush();

            // 记录操作日志
            Log::info('系统设置已更新', [
                'user_id' => Auth::id(),
                'updated_settings' => $updated,
            ]);

            return response()->json([
                'success' => true,
                'message' => '系统设置保存成功',
                'data' => [
                    'updated_count' => count($updated),
                    'updated_settings' => $updated,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('系统设置保存失败', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '系统设置保存失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 重置系统设置
     */
    public function resetSettings(Request $request)
    {
        try {
            $category = $request->input('category');
            
            if ($category) {
                // 重置指定分类的设置
                SystemSetting::where('category', $category)->delete();
                $message = "分类 {$category} 的设置已重置";
            } else {
                // 重置所有设置
                SystemSetting::truncate();
                $message = "所有系统设置已重置";
            }

            // 清除设置缓存
            Cache::tags(['system_settings'])->flush();

            // 重新初始化默认设置
            $this->initializeDefaultSettings($category);

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '设置重置失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取系统信息
     */
    public function getSystemInfo()
    {
        try {
            $info = [
                'application' => [
                    'name' => config('app.name'),
                    'version' => '1.0.0',
                    'environment' => config('app.env'),
                    'debug' => config('app.debug'),
                    'url' => config('app.url'),
                    'timezone' => config('app.timezone'),
                ],
                'server' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                    'operating_system' => PHP_OS,
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'upload_max_filesize' => ini_get('upload_max_filesize'),
                ],
                'database' => [
                    'driver' => config('database.default'),
                    'host' => config('database.connections.' . config('database.default') . '.host'),
                    'database' => config('database.connections.' . config('database.default') . '.database'),
                ],
                'cache' => [
                    'driver' => config('cache.default'),
                ],
                'queue' => [
                    'driver' => config('queue.default'),
                ],
                'storage' => [
                    'disk_usage' => $this->getDiskUsage(),
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $info,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统信息获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 系统维护模式
     */
    public function maintenanceMode(Request $request)
    {
        try {
            $action = $request->input('action'); // 'enable' or 'disable'
            $message = $request->input('message', '系统维护中，请稍后访问');

            if ($action === 'enable') {
                Artisan::call('down', [
                    '--message' => $message,
                    '--retry' => 60,
                ]);
                $result = '维护模式已启用';
            } elseif ($action === 'disable') {
                Artisan::call('up');
                $result = '维护模式已关闭';
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '无效的操作类型',
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '维护模式操作失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 备份系统设置
     */
    public function backupSettings()
    {
        try {
            $settings = SystemSetting::all();
            $backup = [
                'timestamp' => now()->toDateTimeString(),
                'version' => '1.0.0',
                'settings' => $settings->toArray(),
            ];

            $filename = 'system_settings_backup_' . now()->format('Y_m_d_H_i_s') . '.json';
            $path = storage_path('app/backups/' . $filename);

            // 确保备份目录存在
            if (!is_dir(dirname($path))) {
                mkdir(dirname($path), 0755, true);
            }

            file_put_contents($path, json_encode($backup, JSON_PRETTY_PRINT));

            return response()->json([
                'success' => true,
                'message' => '系统设置备份成功',
                'data' => [
                    'filename' => $filename,
                    'path' => $path,
                    'size' => filesize($path),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统设置备份失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取公开设置（无需认证）
     */
    public function getPublicSettings()
    {
        try {
            $publicSettings = [
                'site_name' => SystemSetting::get('site_name', '晨鑫流量变现系统', 'basic'),
                'site_description' => SystemSetting::get('site_description', '智能社群营销与多级分销平台', 'basic'),
                'site_logo' => SystemSetting::get('site_logo', '', 'basic'),
                'contact_info' => [
                    'service_phone' => SystemSetting::get('service_phone', '', 'contact'),
                    'service_email' => SystemSetting::get('service_email', '', 'contact'),
                    'service_qq' => SystemSetting::get('service_qq', '', 'contact'),
                    'service_wechat' => SystemSetting::get('service_wechat', '', 'contact'),
                ],
                'payment_config' => [
                    'min_amount' => SystemSetting::get('min_order_amount', '1', 'payment'),
                    'max_amount' => SystemSetting::get('max_order_amount', '10000', 'payment'),
                    'supported_methods' => SystemSetting::get('supported_methods', ['alipay', 'wechat'], 'payment'),
                ],
                'system_config' => [
                    'registration_enabled' => SystemSetting::get('registration_enabled', true, 'system'),
                    'maintenance_mode' => SystemSetting::get('maintenance_mode', false, 'system'),
                    'version' => '1.0.0',
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $publicSettings,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取公开设置失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 恢复系统设置
     */
    public function restoreSettings(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'backup_file' => 'required|file|mimes:json',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $file = $request->file('backup_file');
            $content = file_get_contents($file->getPathname());
            $backup = json_decode($content, true);

            if (!$backup || !isset($backup['settings'])) {
                return response()->json([
                    'success' => false,
                    'message' => '备份文件格式无效',
                ], 422);
            }

            // 清空现有设置
            SystemSetting::truncate();

            // 恢复设置
            foreach ($backup['settings'] as $setting) {
                SystemSetting::create([
                    'category' => $setting['category'],
                    'key' => $setting['key'],
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                    'description' => $setting['description'],
                    'options' => $setting['options'],
                    'required' => $setting['required'],
                    'updated_by' => Auth::id(),
                ]);
            }

            // 清除设置缓存
            Cache::tags(['system_settings'])->flush();

            return response()->json([
                'success' => true,
                'message' => '系统设置恢复成功',
                'data' => [
                    'restored_count' => count($backup['settings']),
                    'backup_timestamp' => $backup['timestamp'],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统设置恢复失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 验证设置值
     */
    private function validateSettingValue(string $category, string $key, $value): bool
    {
        // 获取设置定义
        $setting = SystemSetting::where('category', $category)
                                ->where('key', $key)
                                ->first();

        if (!$setting) {
            return true; // 新设置，允许
        }

        // 根据类型验证
        switch ($setting->type) {
            case 'boolean':
                return is_bool($value) || in_array($value, ['true', 'false', '1', '0', 1, 0]);
            case 'integer':
                return is_numeric($value);
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
            case 'json':
                json_decode($value);
                return json_last_error() === JSON_ERROR_NONE;
            default:
                return true;
        }
    }

    /**
     * 初始化默认设置
     */
    private function initializeDefaultSettings(?string $category = null)
    {
        $defaultSettings = [
            'basic' => [
                'site_name' => ['value' => '晨鑫流量变现系统', 'type' => 'string', 'description' => '网站名称'],
                'site_domain' => ['value' => '', 'type' => 'string', 'description' => '网站域名'],
                'api_domain' => ['value' => '', 'type' => 'string', 'description' => 'API域名'],
                'site_description' => ['value' => '智能社群营销与多级分销平台', 'type' => 'text', 'description' => '网站描述'],
            ],
            'payment' => [
                'default_currency' => ['value' => 'CNY', 'type' => 'string', 'description' => '默认货币'],
                'min_withdraw_amount' => ['value' => '100', 'type' => 'integer', 'description' => '最小提现金额'],
                'withdraw_fee_rate' => ['value' => '0.01', 'type' => 'decimal', 'description' => '提现手续费率'],
            ],
            'notification' => [
                'email_enabled' => ['value' => 'true', 'type' => 'boolean', 'description' => '启用邮件通知'],
                'sms_enabled' => ['value' => 'false', 'type' => 'boolean', 'description' => '启用短信通知'],
            ],
            'security' => [
                'login_attempts' => ['value' => '5', 'type' => 'integer', 'description' => '登录尝试次数限制'],
                'session_timeout' => ['value' => '120', 'type' => 'integer', 'description' => '会话超时时间(分钟)'],
            ],
        ];

        $categoriesToInit = $category ? [$category => $defaultSettings[$category] ?? []] : $defaultSettings;

        foreach ($categoriesToInit as $cat => $settings) {
            foreach ($settings as $key => $config) {
                SystemSetting::updateOrCreate(
                    ['category' => $cat, 'key' => $key],
                    [
                        'value' => $config['value'],
                        'type' => $config['type'],
                        'description' => $config['description'],
                        'required' => true,
                        'updated_by' => Auth::id(),
                    ]
                );
            }
        }
    }

    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage(): array
    {
        $path = storage_path();
        $totalSpace = disk_total_space($path);
        $freeSpace = disk_free_space($path);
        $usedSpace = $totalSpace - $freeSpace;

        return [
            'total' => $this->formatBytes($totalSpace),
            'used' => $this->formatBytes($usedSpace),
            'free' => $this->formatBytes($freeSpace),
            'usage_percent' => round(($usedSpace / $totalSpace) * 100, 2),
        ];
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}