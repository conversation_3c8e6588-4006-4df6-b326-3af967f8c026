<?php

namespace App\Services;

use App\Models\NavigationMenu;
use App\Models\NavigationSearchLog;
use App\Models\NavigationUsageStat;
use App\Models\UserNavigationPreference;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 导航搜索服务
 * 
 * 提供全局搜索、搜索建议和搜索统计功能
 */
class NavigationSearchService
{
    const CACHE_TTL = 1800; // 30分钟缓存

    /**
     * 执行导航搜索
     */
    public function search(
        string $query,
        ?string $domain = null,
        int $limit = 20,
        ?User $user = null,
        string $searchType = 'fuzzy'
    ): array {
        $startTime = microtime(true);

        // 构建搜索查询
        $searchQuery = $this->buildSearchQuery($query, $domain, $searchType, $user);

        // 执行搜索
        $results = $searchQuery->limit($limit)->get();

        // 应用权限过滤
        if ($user) {
            $permissionService = app(NavigationPermissionService::class);
            $results = $permissionService->filterMenusByPermissions($results, $user);
        }

        // 应用个性化排序
        if ($user) {
            $results = $this->applyPersonalizedRanking($results, $user, $query);
        }

        $searchTime = microtime(true) - $startTime;

        return [
            'results' => $this->formatSearchResults($results, $query),
            'total' => $results->count(),
            'search_time' => round($searchTime * 1000, 2), // 毫秒
            'filters_applied' => $this->getAppliedFilters($domain, $user)
        ];
    }

    /**
     * 获取搜索建议
     */
    public function getSuggestions(string $query, ?string $domain = null, ?User $user = null, int $limit = 10): array
    {
        $cacheKey = "search_suggestions:{$query}:{$domain}:" . ($user?->id ?? 'guest') . ":{$limit}";

        return Cache::remember($cacheKey, 3600, function () use ($query, $domain, $user, $limit) {
            $suggestions = [];

            // 1. 基于菜单名称的建议
            $menuSuggestions = $this->getMenuNameSuggestions($query, $domain, $user, ceil($limit * 0.4));
            $suggestions = array_merge($suggestions, $menuSuggestions);

            // 2. 基于历史搜索的建议
            if ($user) {
                $historySuggestions = $this->getHistoryBasedSuggestions($query, $user->id, ceil($limit * 0.3));
                $suggestions = array_merge($suggestions, $historySuggestions);
            }

            // 3. 基于热门搜索的建议
            $popularSuggestions = $this->getPopularSearchSuggestions($query, $domain, ceil($limit * 0.3));
            $suggestions = array_merge($suggestions, $popularSuggestions);

            // 去重并限制数量
            $uniqueSuggestions = collect($suggestions)
                ->unique('text')
                ->take($limit)
                ->values()
                ->toArray();

            return $uniqueSuggestions;
        });
    }

    /**
     * 获取热门搜索词
     */
    public function getPopularQueries(?string $domain = null, string $period = 'week', int $limit = 20): array
    {
        $days = match ($period) {
            'today' => 1,
            'week' => 7,
            'month' => 30,
            default => 7
        };

        return NavigationSearchLog::select('query', DB::raw('COUNT(*) as search_count'), DB::raw('COUNT(DISTINCT user_id) as unique_users'))
            ->where('searched_at', '>=', now()->subDays($days))
            ->when($domain, function ($query) use ($domain) {
                $query->whereHas('results', function ($subQuery) use ($domain) {
                    // 这里需要根据实际的results字段结构调整
                });
            })
            ->groupBy('query')
            ->havingRaw('search_count > 1')
            ->orderByDesc('search_count')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'query' => $item->query,
                    'search_count' => $item->search_count,
                    'unique_users' => $item->unique_users,
                    'trend' => $this->calculateQueryTrend($item->query)
                ];
            })
            ->toArray();
    }

    /**
     * 获取用户常用菜单
     */
    public function getFrequentMenus(int $userId, ?string $domain = null, int $limit = 10): array
    {
        $query = NavigationUsageStat::select('menu_code', DB::raw('SUM(visit_count) as total_visits'))
            ->where('user_id', $userId)
            ->where('date', '>=', now()->subDays(30))
            ->with(['navigationMenu' => function ($query) use ($domain) {
                if ($domain) {
                    $query->where('domain', $domain);
                }
                $query->where('is_visible', true);
            }])
            ->groupBy('menu_code')
            ->orderByDesc('total_visits')
            ->limit($limit);

        if ($domain) {
            $query->where('domain', $domain);
        }

        return $query->get()
            ->filter(function ($stat) {
                return $stat->navigationMenu !== null;
            })
            ->map(function ($stat) {
                return [
                    'menu' => $stat->navigationMenu,
                    'visit_count' => $stat->total_visits,
                    'frequency_score' => $this->calculateFrequencyScore($stat->total_visits)
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * 获取用户最近搜索
     */
    public function getRecentSearches(int $userId, int $limit = 5): array
    {
        return NavigationSearchLog::where('user_id', $userId)
            ->orderByDesc('searched_at')
            ->limit($limit)
            ->get(['query', 'result_count', 'searched_at'])
            ->map(function ($log) {
                return [
                    'query' => $log->query,
                    'result_count' => $log->result_count,
                    'searched_at' => $log->searched_at->timestamp,
                    'relative_time' => $log->searched_at->diffForHumans()
                ];
            })
            ->toArray();
    }

    /**
     * 获取用户搜索统计
     */
    public function getUserSearchStats(int $userId, string $period = 'week', ?string $domain = null): array
    {
        $days = match ($period) {
            'today' => 1,
            'week' => 7,
            'month' => 30,
            default => 7
        };

        $query = NavigationSearchLog::where('user_id', $userId)
            ->where('searched_at', '>=', now()->subDays($days));

        if ($domain) {
            // 根据实际需求过滤域
        }

        $logs = $query->get();

        return [
            'total_searches' => $logs->count(),
            'unique_queries' => $logs->unique('query')->count(),
            'avg_results_per_search' => $logs->avg('result_count'),
            'most_searched_terms' => $logs->groupBy('query')
                ->map->count()
                ->sortDesc()
                ->take(10)
                ->toArray(),
            'search_frequency' => $this->calculateSearchFrequency($logs, $days),
            'success_rate' => $this->calculateSearchSuccessRate($logs),
            'daily_distribution' => $this->getSearchDistribution($logs, $days)
        ];
    }

    /**
     * 语义搜索
     */
    public function semanticSearch(string $query, ?string $domain = null, int $limit = 20): Collection
    {
        // 简化的语义搜索实现
        // 在实际项目中可以集成Elasticsearch或其他搜索引擎
        
        $keywords = $this->extractKeywords($query);
        
        $baseQuery = NavigationMenu::where('is_visible', true);
        
        if ($domain) {
            $baseQuery->where('domain', $domain);
        }

        $results = $baseQuery->where(function ($query) use ($keywords) {
            foreach ($keywords as $keyword) {
                $query->orWhere('name', 'like', "%{$keyword}%")
                     ->orWhere('code', 'like', "%{$keyword}%")
                     ->orWhereJsonContains('meta->keywords', $keyword)
                     ->orWhereJsonContains('meta->description', $keyword);
            }
        })->get();

        // 计算相关性评分
        return $results->map(function ($menu) use ($keywords) {
            $menu->relevance_score = $this->calculateRelevanceScore($menu, $keywords);
            return $menu;
        })->sortByDesc('relevance_score');
    }

    // ========== 私有方法 ==========

    private function buildSearchQuery(string $query, ?string $domain, string $searchType, ?User $user)
    {
        $baseQuery = NavigationMenu::where('is_visible', true);

        if ($domain) {
            $baseQuery->where('domain', $domain);
        }

        switch ($searchType) {
            case 'exact':
                return $baseQuery->where(function ($q) use ($query) {
                    $q->where('name', $query)
                      ->orWhere('code', $query);
                });

            case 'semantic':
                return $this->semanticSearch($query, $domain);

            case 'fuzzy':
            default:
                return $baseQuery->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('code', 'like', "%{$query}%")
                      ->orWhereJsonContains('meta->keywords', $query)
                      ->orWhereJsonContains('meta->description', $query);
                });
        }
    }

    private function applyPersonalizedRanking(Collection $results, User $user, string $query): Collection
    {
        $userStats = NavigationUsageStat::where('user_id', $user->id)
            ->whereIn('menu_code', $results->pluck('code'))
            ->get()
            ->keyBy('menu_code');

        return $results->map(function ($menu) use ($userStats, $query) {
            $stats = $userStats->get($menu->code);
            
            // 基础相关性评分
            $relevanceScore = $this->calculateTextRelevance($menu->name, $query);
            
            // 用户使用频率加权
            $frequencyScore = $stats ? log($stats->visit_count + 1) : 0;
            
            // 最近使用加权
            $recencyScore = $stats && $stats->last_visited_at 
                ? 1 / (now()->diffInDays($stats->last_visited_at) + 1) 
                : 0;

            $menu->search_score = $relevanceScore + $frequencyScore * 0.3 + $recencyScore * 0.2;
            
            return $menu;
        })->sortByDesc('search_score');
    }

    private function formatSearchResults(Collection $results, string $query): array
    {
        return $results->map(function ($menu) use ($query) {
            return [
                'code' => $menu->code,
                'name' => $menu->name,
                'domain' => $menu->domain,
                'icon' => $menu->icon,
                'route' => $menu->route,
                'component' => $menu->component,
                'highlighted_name' => $this->highlightText($menu->name, $query),
                'relevance_score' => $menu->search_score ?? $menu->relevance_score ?? 1,
                'meta' => $menu->meta,
                'breadcrumb' => $this->generateBreadcrumb($menu)
            ];
        })->toArray();
    }

    private function getAppliedFilters(?string $domain, ?User $user): array
    {
        $filters = [];
        
        if ($domain) {
            $filters['domain'] = $domain;
        }
        
        if ($user) {
            $filters['permissions'] = 'applied';
            $filters['personalization'] = 'enabled';
        }
        
        return $filters;
    }

    private function getMenuNameSuggestions(string $query, ?string $domain, ?User $user, int $limit): array
    {
        $baseQuery = NavigationMenu::where('is_visible', true)
            ->where('name', 'like', "%{$query}%");

        if ($domain) {
            $baseQuery->where('domain', $domain);
        }

        return $baseQuery->limit($limit)
            ->get(['name', 'code'])
            ->map(function ($menu) {
                return [
                    'text' => $menu->name,
                    'type' => 'menu_name',
                    'menu_code' => $menu->code
                ];
            })
            ->toArray();
    }

    private function getHistoryBasedSuggestions(string $query, int $userId, int $limit): array
    {
        return NavigationSearchLog::where('user_id', $userId)
            ->where('query', 'like', "%{$query}%")
            ->where('result_count', '>', 0)
            ->orderByDesc('searched_at')
            ->limit($limit)
            ->pluck('query')
            ->unique()
            ->map(function ($q) {
                return [
                    'text' => $q,
                    'type' => 'history'
                ];
            })
            ->toArray();
    }

    private function getPopularSearchSuggestions(string $query, ?string $domain, int $limit): array
    {
        return NavigationSearchLog::where('query', 'like', "%{$query}%")
            ->where('searched_at', '>=', now()->subDays(7))
            ->where('result_count', '>', 0)
            ->select('query', DB::raw('COUNT(*) as count'))
            ->groupBy('query')
            ->orderByDesc('count')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'text' => $item->query,
                    'type' => 'popular',
                    'search_count' => $item->count
                ];
            })
            ->toArray();
    }

    private function calculateQueryTrend(string $query): string
    {
        $recentCount = NavigationSearchLog::where('query', $query)
            ->where('searched_at', '>=', now()->subDays(3))
            ->count();
            
        $previousCount = NavigationSearchLog::where('query', $query)
            ->where('searched_at', '>=', now()->subDays(6))
            ->where('searched_at', '<', now()->subDays(3))
            ->count();

        if ($recentCount > $previousCount) {
            return 'up';
        } elseif ($recentCount < $previousCount) {
            return 'down';
        } else {
            return 'stable';
        }
    }

    private function calculateFrequencyScore(int $visits): float
    {
        // 对数缩放避免极值影响
        return round(log($visits + 1) / log(100), 2);
    }

    private function calculateSearchFrequency(Collection $logs, int $days): array
    {
        $searchesByDay = $logs->groupBy(function ($log) {
            return $log->searched_at->format('Y-m-d');
        })->map->count();

        return [
            'avg_per_day' => round($logs->count() / $days, 2),
            'max_per_day' => $searchesByDay->max() ?? 0,
            'most_active_day' => $searchesByDay->keys()->first()
        ];
    }

    private function calculateSearchSuccessRate(Collection $logs): float
    {
        if ($logs->isEmpty()) {
            return 0;
        }

        $successfulSearches = $logs->where('result_count', '>', 0)->count();
        return round(($successfulSearches / $logs->count()) * 100, 2);
    }

    private function getSearchDistribution(Collection $logs, int $days): array
    {
        // 按小时分布
        $hourlyDistribution = $logs->groupBy(function ($log) {
            return $log->searched_at->format('H');
        })->map->count();

        return $hourlyDistribution->toArray();
    }

    private function extractKeywords(string $query): array
    {
        // 简化的关键词提取
        return array_filter(explode(' ', strtolower($query)));
    }

    private function calculateRelevanceScore($menu, array $keywords): float
    {
        $score = 0;
        $menuText = strtolower($menu->name . ' ' . $menu->code);
        
        foreach ($keywords as $keyword) {
            if (str_contains($menuText, $keyword)) {
                $score += 1;
            }
        }
        
        return $score / max(count($keywords), 1);
    }

    private function calculateTextRelevance(string $text, string $query): float
    {
        $text = strtolower($text);
        $query = strtolower($query);
        
        if ($text === $query) {
            return 1.0;
        }
        
        if (str_contains($text, $query)) {
            return 0.8;
        }
        
        // 简化的文本相似度计算
        similar_text($text, $query, $percent);
        return $percent / 100;
    }

    private function highlightText(string $text, string $query): string
    {
        return str_ireplace($query, "<mark>$query</mark>", $text);
    }

    private function generateBreadcrumb($menu): array
    {
        $breadcrumb = [];
        
        if ($menu->parent_id && $menu->parent_id > 0) {
            $parent = NavigationMenu::find($menu->parent_id);
            if ($parent) {
                $breadcrumb[] = [
                    'code' => $parent->code,
                    'name' => $parent->name
                ];
            }
        }
        
        $breadcrumb[] = [
            'code' => $menu->code,
            'name' => $menu->name
        ];
        
        return $breadcrumb;
    }
}