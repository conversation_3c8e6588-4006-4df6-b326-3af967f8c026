<template>
  <div class="city-placeholder-test">
    <el-card>
      <template #header>
        <h3>🏙️ 城市占位符测试</h3>
        <p>验证模板中的 {{city}} 占位符是否正确保留</p>
      </template>

      <div class="test-section">
        <h4>📋 测试结果</h4>
        
        <el-alert 
          v-if="testResults.length > 0"
          :title="allTestsPassed ? '✅ 所有测试通过' : '❌ 发现问题'"
          :type="allTestsPassed ? 'success' : 'error'"
          style="margin-bottom: 20px"
        />

        <el-table :data="testResults" border style="width: 100%">
          <el-table-column prop="template" label="模板名称" width="200" />
          <el-table-column prop="field" label="字段" width="120" />
          <el-table-column prop="content" label="内容" min-width="300" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'pass' ? 'success' : 'danger'">
                {{ scope.row.status === 'pass' ? '通过' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div style="margin-top: 20px">
          <el-button @click="runTests" type="primary" :loading="testing">
            重新测试
          </el-button>
          <el-button @click="exportResults" type="success" v-if="testResults.length > 0">
            导出结果
          </el-button>
        </div>
      </div>

      <div class="test-section" style="margin-top: 30px">
        <h4>🔧 城市占位符说明</h4>
        <el-descriptions border :column="1">
          <el-descriptions-item label="占位符格式">{{city}}</el-descriptions-item>
          <el-descriptions-item label="预期行为">在落地页根据用户地理位置动态替换</el-descriptions-item>
          <el-descriptions-item label="后台行为">保留占位符，不进行替换</el-descriptions-item>
          <el-descriptions-item label="name字段">不应包含{{city}}占位符（避免与城市定位开关重复）</el-descriptions-item>
          <el-descriptions-item label="description/introduction">应包含{{city}}占位符用于本地化</el-descriptions-item>
          <el-descriptions-item label="示例">description: "汇聚{{city}}地区行业精英" → "汇聚北京地区行业精英"</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 测试状态
const testing = ref(false)
const testResults = ref([])

// 计算属性
const allTestsPassed = computed(() => {
  return testResults.value.length > 0 && testResults.value.every(result => result.status === 'pass')
})

// 模拟模板数据（从实际组件中获取）
const getTestTemplates = () => {
  return [
    {
      name: '创业交流群模板',
      template: {
        name: '创业精英交流群',
        description: '聚集{{city}}最优秀的创业者，分享创业经验、对接项目资源、寻找合作伙伴。',
        introduction: '本群是{{city}}地区最活跃的创业者交流平台，汇聚了各行业的创业精英、投资人和行业专家。'
      }
    },
    {
      name: '技术交流群模板',
      template: {
        name: '程序员技术交流群',
        description: '汇聚{{city}}地区优秀程序员，分享技术经验，探讨前沿技术，助力职业发展。',
        introduction: '专业的技术交流平台，汇聚行业精英，分享最新技术动态和实战经验。'
      }
    },
    {
      name: 'AI生成模板',
      template: {
        name: '高端商务交流圈',
        description: '汇聚{{city}}地区行业精英，分享前沿资讯，共同探讨商业机会与发展趋势。',
        introduction: '欢迎加入{{city}}精英交流圈！这里汇聚了本地各行各业的优秀人才。'
      }
    }
  ]
}

// 运行测试
const runTests = async () => {
  testing.value = true
  testResults.value = []

  try {
    const templates = getTestTemplates()
    
    for (const template of templates) {
      for (const [field, content] of Object.entries(template.template)) {
        if (typeof content === 'string') {
          const result = {
            template: template.name,
            field: field,
            content: content,
            status: 'pass'
          }

          // 检查是否包含{{city}}占位符
          if (content.includes('{{city}}')) {
            // 检查是否有硬编码的城市名称
            const hardcodedCities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '天津', '重庆']
            const hasHardcodedCity = hardcodedCities.some(city => content.includes(city))

            // 检查是否有重复的{{city}}占位符
            const cityMatches = content.match(/\{\{city\}\}/g)
            const hasDuplicatePlaceholder = cityMatches && cityMatches.length > 1

            if (hasHardcodedCity) {
              result.status = 'fail'
              result.content += ' ❌ 发现硬编码城市名称'
            } else if (hasDuplicatePlaceholder) {
              result.status = 'fail'
              result.content += ` ❌ 发现重复占位符(${cityMatches.length}个)`
            } else {
              result.content += ' ✅ 正确使用占位符'
            }
          } else {
            // 检查是否应该包含城市占位符
            if (field === 'description' || field === 'introduction') {
              result.status = 'fail'
              result.content += ' ❌ 缺少{{city}}占位符'
            } else if (field === 'name') {
              result.content += ' ✅ 正确，name字段不应包含{{city}}占位符'
            }
          }

          testResults.value.push(result)
        }
      }
    }

    ElMessage.success(`测试完成！共检查 ${testResults.value.length} 个字段`)
  } catch (error) {
    console.error('测试失败:', error)
    ElMessage.error('测试执行失败')
  } finally {
    testing.value = false
  }
}

// 导出结果
const exportResults = () => {
  const data = testResults.value.map(result => ({
    模板名称: result.template,
    字段: result.field,
    内容: result.content,
    状态: result.status === 'pass' ? '通过' : '失败'
  }))

  const csv = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')

  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `城市占位符测试结果_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()

  ElMessage.success('测试结果已导出')
}

// 组件挂载时运行测试
onMounted(() => {
  runTests()
})
</script>

<style scoped>
.city-placeholder-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h4 {
  margin-bottom: 15px;
  color: #409eff;
}
</style>
