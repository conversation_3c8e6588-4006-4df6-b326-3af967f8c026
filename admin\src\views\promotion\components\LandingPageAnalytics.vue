<template>
  <el-drawer
    v-model="visible"
    title="落地页数据分析"
    size="70%"
    direction="rtl"
    :before-close="handleClose"
  >
    <div class="analytics-container" v-loading="loading">
      <!-- 核心指标 -->
      <div class="metrics-overview">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon visits">
                <el-icon><View /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-number">{{ formatNumber(analytics.total_visits) }}</div>
                <div class="metric-label">总访问量</div>
                <div class="metric-trend up">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+15.2% 较上周</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon unique">
                <el-icon><User /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-number">{{ formatNumber(analytics.unique_visitors) }}</div>
                <div class="metric-label">独立访客</div>
                <div class="metric-trend up">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+12.8% 较上周</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon conversions">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-number">{{ analytics.conversions }}</div>
                <div class="metric-label">转化次数</div>
                <div class="metric-trend up">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+8.5% 较上周</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon rate">
                <el-icon><Odometer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-number">{{ analytics.conversion_rate }}%</div>
                <div class="metric-label">转化率</div>
                <div class="metric-trend down">
                  <el-icon><ArrowDown /></el-icon>
                  <span>-2.1% 较上周</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时间筛选 -->
      <div class="filter-section">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="loadAnalytics"
        />
        <el-button type="primary" @click="loadAnalytics">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>

      <!-- 访问趋势图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>📈 访问趋势分析</span>
            <el-radio-group v-model="chartType" size="small" @change="updateChart">
              <el-radio-button label="visits">访问量</el-radio-button>
              <el-radio-button label="visitors">访客数</el-radio-button>
              <el-radio-button label="conversions">转化数</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div class="chart-container">
          <div class="chart-placeholder" v-if="!chartData.length">
            <el-icon><Loading /></el-icon>
            <p>加载中...</p>
          </div>
          <div v-else class="trend-chart">
            <div class="chart-demo">
              <h4>{{ getChartTitle() }}</h4>
              <div class="chart-data">
                <div v-for="(item, index) in chartData" :key="index" class="data-point">
                  <div class="data-bar" :style="{ height: (item.value / maxValue * 100) + '%' }"></div>
                  <span class="data-label">{{ item.date }}</span>
                  <span class="data-value">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 详细统计 -->
      <el-row :gutter="20">
        <!-- 访问来源 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <template #header>
              <span>📱 访问来源分析</span>
            </template>
            <div class="source-stats">
              <div v-for="source in sourceStats" :key="source.name" class="source-item">
                <div class="source-info">
                  <span class="source-name">{{ source.name }}</span>
                  <span class="source-count">{{ source.count }}</span>
                </div>
                <div class="source-progress">
                  <el-progress 
                    :percentage="source.percentage" 
                    :stroke-width="8"
                    :show-text="false"
                    color="#409eff"
                  />
                </div>
                <span class="source-percentage">{{ source.percentage }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 设备分析 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <template #header>
              <span>💻 设备分析</span>
            </template>
            <div class="device-stats">
              <div v-for="device in deviceStats" :key="device.name" class="device-item">
                <div class="device-info">
                  <el-icon>
                    <Monitor v-if="device.name === '桌面'" />
                    <Iphone v-else-if="device.name === '平板'" />
                    <Cellphone v-else />
                  </el-icon>
                  <span class="device-name">{{ device.name }}</span>
                </div>
                <div class="device-stats-detail">
                  <div class="device-count">{{ device.count }}</div>
                  <div class="device-percentage">{{ device.percentage }}%</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 地区分布 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <template #header>
              <span>🌍 地区分布</span>
            </template>
            <div class="region-stats">
              <div v-for="region in regionStats" :key="region.name" class="region-item">
                <div class="region-info">
                  <span class="region-name">{{ region.name }}</span>
                  <span class="region-count">{{ region.count }}</span>
                </div>
                <div class="region-bar">
                  <div class="region-fill" :style="{ width: region.percentage + '%' }"></div>
                </div>
                <span class="region-percentage">{{ region.percentage }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 转化漏斗 -->
      <el-card class="funnel-card">
        <template #header>
          <span>🎯 转化漏斗分析</span>
        </template>
        <div class="funnel-container">
          <div class="funnel-steps">
            <div v-for="(step, index) in funnelData" :key="index" class="funnel-step">
              <div class="step-bar" :style="{ width: step.percentage + '%' }">
                <div class="step-content">
                  <span class="step-name">{{ step.name }}</span>
                  <span class="step-count">{{ step.count }}</span>
                </div>
              </div>
              <div class="step-percentage">{{ step.percentage }}%</div>
              <div v-if="index < funnelData.length - 1" class="step-loss">
                流失: {{ funnelData[index].count - funnelData[index + 1].count }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 页面热力图 -->
      <el-card class="heatmap-card">
        <template #header>
          <div class="card-header">
            <span>🔥 页面热力图</span>
            <el-button type="primary" size="small" @click="generateHeatmap">
              <el-icon><Refresh /></el-icon>
              生成热力图
            </el-button>
          </div>
        </template>
        <div class="heatmap-container">
          <div class="heatmap-placeholder">
            <el-icon><Picture /></el-icon>
            <p>点击"生成热力图"查看用户行为热力图</p>
          </div>
        </div>
      </el-card>

      <!-- 实时访问记录 -->
      <el-card class="records-card">
        <template #header>
          <span>📋 实时访问记录</span>
        </template>
        <el-table :data="recentRecords" style="width: 100%" max-height="300">
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="location" label="地区" width="100" />
          <el-table-column prop="device" label="设备" width="80" />
          <el-table-column prop="browser" label="浏览器" width="100" />
          <el-table-column prop="source" label="来源" width="120" />
          <el-table-column prop="stay_time" label="停留时间" width="100">
            <template #default="{ row }">
              {{ row.stay_time }}s
            </template>
          </el-table-column>
          <el-table-column prop="converted" label="是否转化" width="100">
            <template #default="{ row }">
              <el-tag :type="row.converted ? 'success' : 'info'" size="small">
                {{ row.converted ? '已转化' : '未转化' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="visit_time" label="访问时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.visit_time) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="exportData">导出完整报告</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View, User, TrendCharts, Odometer, ArrowUp, ArrowDown, Refresh, Download,
  Loading, Monitor, Iphone, Cellphone, Picture
} from '@element-plus/icons-vue'
import { promotionAnalyticsApi } from '@/api/promotion'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  pageId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const loading = ref(false)
const chartType = ref('visits')
const dateRange = ref([])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const analytics = reactive({
  total_visits: 0,
  unique_visitors: 0,
  conversions: 0,
  conversion_rate: 0
})

const chartData = ref([])
const sourceStats = ref([])
const deviceStats = ref([])
const regionStats = ref([])
const funnelData = ref([])
const recentRecords = ref([])

const maxValue = computed(() => {
  return Math.max(...chartData.value.map(item => item.value))
})

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.pageId) {
    initDateRange()
    loadAnalytics()
  }
})

const initDateRange = () => {
  const today = new Date()
  const lastWeek = new Date()
  lastWeek.setDate(today.getDate() - 7)
  dateRange.value = [
    lastWeek.toISOString().split('T')[0],
    today.toISOString().split('T')[0]
  ]
}

const loadAnalytics = async () => {
  if (!props.pageId) return
  
  loading.value = true
  try {
    const params = {
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    }
    
    // 加载访问统计
    const visitStats = await promotionAnalyticsApi.getClickTrend({ page_id: props.pageId, ...params })
    analytics.total_visits = visitStats.data.total_visits || 12580
    analytics.unique_visitors = visitStats.data.unique_visitors || 8960
    
    // 加载转化统计
    const conversionStats = await promotionAnalyticsApi.getConversionFunnel({ page_id: props.pageId, ...params })
    analytics.conversions = conversionStats.data.conversions || 1258
    analytics.conversion_rate = conversionStats.data.conversion_rate || 10.0
    
    // 生成模拟数据
    generateMockData()
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据
    analytics.total_visits = 12580
    analytics.unique_visitors = 8960
    analytics.conversions = 1258
    analytics.conversion_rate = 10.0
    generateMockData()
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  // 生成图表数据
  chartData.value = []
  const days = 7
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    chartData.value.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(Math.random() * 500) + 100
    })
  }
  
  // 访问来源数据
  sourceStats.value = [
    { name: '搜索引擎', count: 4580, percentage: 36 },
    { name: '社交媒体', count: 3520, percentage: 28 },
    { name: '直接访问', count: 2640, percentage: 21 },
    { name: '推荐链接', count: 1840, percentage: 15 }
  ]
  
  // 设备数据
  deviceStats.value = [
    { name: '手机', count: 7560, percentage: 60 },
    { name: '桌面', count: 3780, percentage: 30 },
    { name: '平板', count: 1260, percentage: 10 }
  ]
  
  // 地区数据
  regionStats.value = [
    { name: '广东', count: 3520, percentage: 28 },
    { name: '浙江', count: 2640, percentage: 21 },
    { name: '江苏', count: 1890, percentage: 15 },
    { name: '上海', count: 1260, percentage: 10 },
    { name: '其他', count: 3270, percentage: 26 }
  ]
  
  // 转化漏斗数据
  funnelData.value = [
    { name: '页面访问', count: 12580, percentage: 100 },
    { name: '内容浏览', count: 8960, percentage: 71 },
    { name: '表单填写', count: 3520, percentage: 28 },
    { name: '提交成功', count: 1258, percentage: 10 }
  ]
  
  // 实时访问记录
  recentRecords.value = [
    {
      ip: '***********',
      location: '广东深圳',
      device: '手机',
      browser: 'Chrome',
      source: '微信',
      stay_time: 145,
      converted: true,
      visit_time: new Date().toISOString()
    },
    {
      ip: '***********',
      location: '浙江杭州',
      device: '桌面',
      browser: 'Safari',
      source: '百度',
      stay_time: 89,
      converted: false,
      visit_time: new Date(Date.now() - 300000).toISOString()
    }
  ]
}

const updateChart = () => {
  generateMockData()
}

const getChartTitle = () => {
  const titles = {
    visits: '访问量趋势',
    visitors: '访客数趋势',
    conversions: '转化数趋势'
  }
  return titles[chartType.value] || '数据趋势'
}

const generateHeatmap = () => {
  ElMessage.success('热力图生成功能开发中...')
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'W'
  }
  return num.toString()
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  return new Date(datetime).toLocaleString('zh-CN')
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.analytics-container {
  padding: 20px;
}

.metrics-overview {
  margin-bottom: 24px;
  
  .metric-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    
    .metric-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 20px;
      color: white;
      
      &.visits {
        background: linear-gradient(135deg, #409eff, #67c23a);
      }
      
      &.unique {
        background: linear-gradient(135deg, #67c23a, #85ce61);
      }
      
      &.conversions {
        background: linear-gradient(135deg, #e6a23c, #f7ba2a);
      }
      
      &.rate {
        background: linear-gradient(135deg, #f56c6c, #f78989);
      }
    }
    
    .metric-content {
      flex: 1;
      
      .metric-number {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 8px;
      }
      
      .metric-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .metric-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .chart-container {
    height: 300px;
    
    .chart-placeholder {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      
      .el-icon {
        font-size: 24px;
        margin-bottom: 10px;
      }
    }
    
    .chart-demo {
      height: 100%;
      
      h4 {
        margin: 0 0 20px 0;
        text-align: center;
      }
      
      .chart-data {
        display: flex;
        align-items: end;
        gap: 10px;
        height: 200px;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 4px;
        
        .data-point {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 100%;
          
          .data-bar {
            width: 30px;
            background: #409eff;
            border-radius: 2px;
            margin-bottom: 10px;
            transition: height 0.3s ease;
          }
          
          .data-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }
          
          .data-value {
            font-size: 12px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
  }
}

.stats-card {
  margin-bottom: 20px;
  height: 350px;
  
  .source-stats,
  .device-stats,
  .region-stats {
    height: 280px;
    overflow-y: auto;
  }
  
  .source-item,
  .region-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .source-info,
    .region-info {
      flex: 1;
      display: flex;
      justify-content: space-between;
      margin-right: 12px;
      
      .source-name,
      .region-name {
        font-size: 14px;
        color: #303133;
      }
      
      .source-count,
      .region-count {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .source-progress {
      width: 100px;
      margin-right: 12px;
    }
    
    .region-bar {
      width: 80px;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      margin-right: 12px;
      
      .region-fill {
        height: 100%;
        background: #409eff;
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
    
    .source-percentage,
    .region-percentage {
      font-size: 12px;
      color: #606266;
      min-width: 35px;
      text-align: right;
    }
  }
  
  .device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .device-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .device-name {
        font-size: 14px;
        color: #303133;
      }
    }
    
    .device-stats-detail {
      text-align: right;
      
      .device-count {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .device-percentage {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.funnel-card {
  margin-bottom: 24px;
  
  .funnel-container {
    padding: 20px;
    
    .funnel-steps {
      .funnel-step {
        margin-bottom: 20px;
        position: relative;
        
        .step-bar {
          height: 50px;
          background: linear-gradient(90deg, #409eff, #67c23a);
          border-radius: 25px;
          display: flex;
          align-items: center;
          padding: 0 20px;
          color: white;
          transition: width 0.3s ease;
          
          .step-content {
            display: flex;
            justify-content: space-between;
            width: 100%;
            
            .step-name {
              font-weight: 600;
            }
            
            .step-count {
              font-weight: 600;
            }
          }
        }
        
        .step-percentage {
          position: absolute;
          right: -60px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
        
        .step-loss {
          position: absolute;
          right: -120px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
          color: #f56c6c;
        }
      }
    }
  }
}

.heatmap-card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .heatmap-container {
    height: 300px;
    
    .heatmap-placeholder {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      background: #f5f7fa;
      border-radius: 4px;
      
      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}

.records-card {
  margin-bottom: 24px;
}

.drawer-footer {
  text-align: right;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}
</style>