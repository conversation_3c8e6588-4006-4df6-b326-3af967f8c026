# Technology Stack & Build System

## Backend Stack

### Core Framework
- **Laravel 10.x** - PHP web application framework
- **PHP 8.1+** - Required PHP version with modern features
- **MySQL 8.0+** - Primary database with InnoDB engine
- **Redis 7.x** - Caching, sessions, and queue management

### Key Dependencies
- **JWT Authentication** - `tymon/jwt-auth` for stateless API authentication
- **Role & Permissions** - `spatie/laravel-permission` for RBAC
- **Image Processing** - `intervention/image` for avatar/image handling
- **Excel Operations** - `maatwebsite/excel` for data import/export
- **Queue Management** - `laravel/horizon` for Redis queue monitoring
- **Error Tracking** - `sentry/sentry-laravel` for production error monitoring
- **Search** - `laravel/scout` with Elasticsearch integration

## Frontend Stack

### Admin Panel
- **Vue 3** - Progressive JavaScript framework
- **Element Plus** - Vue 3 UI component library
- **Vite** - Fast build tool and dev server
- **Pinia** - State management for Vue 3
- **Axios** - HTTP client for API communication
- **ECharts** - Data visualization and charts

### User Frontend
- **Nuxt 3** - Vue.js meta-framework with SSR/SSG
- **TypeScript** - Type-safe JavaScript development
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Chart.js** - Simple yet flexible charting library
- **VueUse** - Collection of Vue composition utilities

## Development & Deployment

### Build Commands

#### Backend (Laravel)
```bash
# Install dependencies
composer install --optimize-autoloader --no-dev

# Database operations
php artisan migrate --force
php artisan db:seed --force

# Cache optimization
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize

# Queue management
php artisan queue:work --daemon
php artisan horizon
php artisan schedule:work
```

#### Admin Panel
```bash
# Development
cd admin
npm install
npm run dev

# Production build
npm run build
npm run preview
```

#### User Frontend
```bash
# Development
cd frontend
npm install
npm run dev

# Production build
npm run build
npm run generate
npm run preview
```

### Docker Deployment
```bash
# Full stack deployment
docker-compose up -d

# Individual services
docker-compose up -d mysql redis
docker-compose up -d app queue scheduler
docker-compose up -d nginx admin frontend
```

### Testing
```bash
# Run PHP tests
php artisan test
./vendor/bin/phpunit

# Code quality
./vendor/bin/pint  # Laravel Pint for code formatting
```

## Infrastructure

### Production Environment
- **Nginx** - Web server and reverse proxy
- **Supervisor** - Process management for queues
- **SSL/TLS** - HTTPS encryption with automatic certificate management
- **Docker** - Containerized deployment with docker-compose

### Monitoring & Logging
- **Prometheus** - Metrics collection and monitoring
- **Grafana** - Visualization and alerting dashboards
- **Elasticsearch** - Log aggregation and search
- **Kibana** - Log analysis and visualization

### Performance Optimization
- **Redis Caching** - Multi-level caching strategy
- **Database Indexing** - 15+ performance indexes
- **CDN Integration** - Static asset delivery
- **Code Splitting** - Frontend optimization
- **Queue Processing** - Background job handling

## Configuration Files

### Key Config Files
- `.env` - Environment variables and secrets
- `config/database.php` - Database connections
- `config/jwt.php` - JWT authentication settings
- `config/payment.php` - Payment gateway configuration
- `config/anti-block.php` - Anti-blocking system settings
- `docker-compose.yml` - Container orchestration
- `nginx.conf` - Web server configuration

### Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Generate application key
php artisan key:generate

# Generate JWT secret
php artisan jwt:secret

# Run system checks
php optimize-check.php
php system-check.php
```