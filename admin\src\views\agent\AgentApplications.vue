<template>
  <div class="agent-applications">
    <div class="page-header">
      <h2>代理商申请管理</h2>
      <p>审核和管理代理商申请，包括申请列表、审核流程和批量操作</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="总申请数"
          :value="applicationStats.total || 0"
          icon="Document"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="待审核"
          :value="applicationStats.pending || 0"
          icon="Clock"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="已通过"
          :value="applicationStats.approved || 0"
          icon="Check"
          color="#67C23A"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="已拒绝"
          :value="applicationStats.rejected || 0"
          icon="Close"
          color="#F56C6C"
        />
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、姓名或手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商类型">
          <el-select v-model="searchForm.agent_type" placeholder="选择类型" clearable>
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商等级">
          <el-select v-model="searchForm.agent_level" placeholder="选择等级" clearable>
            <el-option label="平台代理商" value="platform" />
            <el-option label="分站代理商" value="substation" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedApplications.length > 0">
      <el-alert
        :title="`已选择 ${selectedApplications.length} 个申请`"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="batch-buttons">
        <el-button type="success" @click="batchApprove">
          <el-icon><Check /></el-icon>
          批量通过
        </el-button>
        <el-button type="danger" @click="batchReject">
          <el-icon><Close /></el-icon>
          批量拒绝
        </el-button>
      </div>
    </div>

    <!-- 申请列表 -->
    <el-card class="applications-table">
      <template #header>
        <span>申请列表</span>
      </template>
      
      <el-table
        :data="applications.data"
        stripe
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="user.username" label="用户名" />
        
        <el-table-column prop="user.name" label="姓名" />
        
        <el-table-column prop="user.phone" label="手机号" />
        
        <el-table-column prop="agent_type_text" label="代理商类型">
          <template #default="{ row }">
            <el-tag :type="getAgentTypeColor(row.agent_type)">
              {{ row.agent_type_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_level_text" label="代理商等级">
          <template #default="{ row }">
            <el-tag :type="getAgentLevelColor(row.agent_level)">
              {{ row.agent_level_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="expected_commission_rate" label="期望佣金">
          <template #default="{ row }">
            {{ row.expected_commission_rate }}%
          </template>
        </el-table-column>
        
        <el-table-column prop="status_text" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="申请时间">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewApplication(row)">
              查看详情
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="success"
              @click="approveApplication(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="danger"
              @click="rejectApplication(row)"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="applications.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="申请详情" width="800px">
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人">
            {{ currentApplication.user?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ currentApplication.user?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentApplication.user?.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentApplication.user?.email }}
          </el-descriptions-item>
          <el-descriptions-item label="代理商类型">
            <el-tag :type="getAgentTypeColor(currentApplication.agent_type)">
              {{ currentApplication.agent_type_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="代理商等级">
            <el-tag :type="getAgentLevelColor(currentApplication.agent_level)">
              {{ currentApplication.agent_level_text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="期望佣金比例">
            {{ currentApplication.expected_commission_rate }}%
          </el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusColor(currentApplication.status)">
              {{ currentApplication.status_text }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>企业信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公司名称">
              {{ currentApplication.business_info?.company_name || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="营业执照">
              {{ currentApplication.business_info?.business_license || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="经营范围" :span="2">
              {{ currentApplication.business_info?.business_scope || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>联系信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="联系人">
              {{ currentApplication.contact_info?.contact_person }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ currentApplication.contact_info?.contact_phone }}
            </el-descriptions-item>
            <el-descriptions-item label="联系邮箱">
              {{ currentApplication.contact_info?.contact_email || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系地址">
              {{ currentApplication.contact_info?.contact_address || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>申请理由</h4>
          <p>{{ currentApplication.application_reason }}</p>
        </div>

        <div v-if="currentApplication.review_comment" class="detail-section">
          <h4>审核意见</h4>
          <p>{{ currentApplication.review_comment }}</p>
          <p class="review-info">
            审核人：{{ currentApplication.reviewer?.name }}
            审核时间：{{ formatDate(currentApplication.reviewed_at) }}
          </p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentApplication?.status === 'pending'"
          type="success"
          @click="approveApplication(currentApplication)"
        >
          通过申请
        </el-button>
        <el-button
          v-if="currentApplication?.status === 'pending'"
          type="danger"
          @click="rejectApplication(currentApplication)"
        >
          拒绝申请
        </el-button>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="reviewDialogVisible" :title="reviewTitle" width="500px">
      <el-form :model="reviewForm" label-width="80px">
        <el-form-item label="审核意见">
          <el-input
            v-model="reviewForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview" :loading="reviewLoading">
          确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Check, Close, Document, Clock } from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import { agentApplicationApi } from '@/api/agent'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const reviewLoading = ref(false)

const applicationStats = ref({})
const applications = ref({ data: [], total: 0 })
const selectedApplications = ref([])
const currentApplication = ref(null)
const currentReviewAction = ref('')

const searchForm = reactive({
  keyword: '',
  status: '',
  agent_type: '',
  agent_level: ''
})

const reviewForm = reactive({
  comment: ''
})

// 计算属性
const reviewTitle = computed(() => {
  return currentReviewAction.value === 'approve' ? '通过申请' : '拒绝申请'
})

// 方法
const loadApplicationStats = async () => {
  try {
    const response = await agentApplicationApi.getStats()
    applicationStats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadApplications = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const response = await agentApplicationApi.getList(params)
    applications.value = response.data
  } catch (error) {
    ElMessage.error('加载申请列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadApplications()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadApplications()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadApplications()
}

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedApplications.value = validSelection
}

const viewApplication = (application) => {
  currentApplication.value = application
  detailDialogVisible.value = true
}

const approveApplication = (application) => {
  currentApplication.value = application
  currentReviewAction.value = 'approve'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

const rejectApplication = (application) => {
  currentApplication.value = application
  currentReviewAction.value = 'reject'
  reviewForm.comment = ''
  reviewDialogVisible.value = true
}

const confirmReview = async () => {
  try {
    reviewLoading.value = true
    await agentApplicationApi.review(currentApplication.value.id, {
      action: currentReviewAction.value,
      comment: reviewForm.comment
    })
    
    ElMessage.success(currentReviewAction.value === 'approve' ? '申请已通过' : '申请已拒绝')
    reviewDialogVisible.value = false
    detailDialogVisible.value = false
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    ElMessage.error('审核失败')
  } finally {
    reviewLoading.value = false
  }
}

const batchApprove = async () => {
  try {
    await ElMessageBox.confirm('确定要批量通过选中的申请吗？', '确认操作', {
      type: 'warning'
    })
    
    const applicationIds = selectedApplications.value.map(app => app.id)
    await agentApplicationApi.batchReview({
      application_ids: applicationIds,
      action: 'approve',
      comment: '批量通过'
    })
    
    ElMessage.success('批量通过成功')
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量通过失败')
    }
  }
}

const batchReject = async () => {
  try {
    await ElMessageBox.confirm('确定要批量拒绝选中的申请吗？', '确认操作', {
      type: 'warning'
    })
    
    const applicationIds = selectedApplications.value.map(app => app.id)
    await agentApplicationApi.batchReview({
      application_ids: applicationIds,
      action: 'reject',
      comment: '批量拒绝'
    })
    
    ElMessage.success('批量拒绝成功')
    loadApplications()
    loadApplicationStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量拒绝失败')
    }
  }
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'cancelled': 'info'
  }
  return colors[status] || 'info'
}

const getAgentTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getAgentLevelColor = (level) => {
  const colors = {
    'platform': 'primary',
    'substation': 'success'
  }
  return colors[level] || 'info'
}

// 生命周期
onMounted(() => {
  loadApplicationStats()
  loadApplications()
})
</script>

<style scoped>
.agent-applications {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

.applications-table {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.application-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.review-info {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}
</style>