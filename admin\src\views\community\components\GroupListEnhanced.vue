<template>
  <div class="group-list-enhanced">
    <!-- 实时数据面板 -->
    <div class="real-time-panel" v-if="showRealTimeData">
      <el-card class="real-time-card">
        <template #header>
          <div class="card-header">
            <span>实时数据监控</span>
            <el-switch v-model="autoRefresh" @change="toggleAutoRefresh">
              <template #active-text>自动刷新</template>
            </el-switch>
          </div>
        </template>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="real-time-item">
              <div class="item-value">{{ realTimeData.activeUsers }}</div>
              <div class="item-label">在线用户</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="real-time-item">
              <div class="item-value">{{ realTimeData.newMessages }}</div>
              <div class="item-label">新消息</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="real-time-item">
              <div class="item-value">¥{{ realTimeData.todayRevenue }}</div>
              <div class="item-label">今日收益</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="real-time-item">
              <div class="item-value">{{ realTimeData.pendingReviews }}</div>
              <div class="item-label">待审核</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 智能筛选面板 -->
    <div class="smart-filter-panel">
      <el-card class="filter-card">
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchQuery"
              placeholder="智能搜索：群组名称、群主、标签..."
              class="smart-search"
              @input="handleSmartSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #suffix>
                <el-button 
                  text 
                  @click="showAdvancedFilter = !showAdvancedFilter"
                  :type="showAdvancedFilter ? 'primary' : ''"
                >
                  高级筛选
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="filter-right">
            <el-button-group>
              <el-button 
                v-for="preset in filterPresets" 
                :key="preset.key"
                :type="currentPreset === preset.key ? 'primary' : ''"
                @click="applyFilterPreset(preset.key)"
                size="small"
              >
                {{ preset.label }}
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 高级筛选面板 -->
        <el-collapse-transition>
          <div v-show="showAdvancedFilter" class="advanced-filter">
            <el-row :gutter="16">
              <el-col :span="6">
                <el-select v-model="filters.healthScore" placeholder="健康度" clearable>
                  <el-option label="优秀 (80-100)" value="excellent" />
                  <el-option label="良好 (60-79)" value="good" />
                  <el-option label="一般 (40-59)" value="fair" />
                  <el-option label="较差 (0-39)" value="poor" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select v-model="filters.memberRange" placeholder="成员数量" clearable>
                  <el-option label="0-50人" value="0-50" />
                  <el-option label="51-200人" value="51-200" />
                  <el-option label="201-500人" value="201-500" />
                  <el-option label="500+人" value="500+" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select v-model="filters.revenueRange" placeholder="收益范围" clearable>
                  <el-option label="0-1000元" value="0-1000" />
                  <el-option label="1001-5000元" value="1001-5000" />
                  <el-option label="5001-10000元" value="5001-10000" />
                  <el-option label="10000+元" value="10000+" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="filters.dateRange"
                  type="daterange"
                  placeholder="创建时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="batch-toolbar" v-if="selectedGroups.length > 0">
      <el-card class="toolbar-card">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <span class="selection-info">
              已选择 {{ selectedGroups.length }} 个群组
            </span>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button @click="batchExport" :loading="batchLoading">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
              <el-button @click="batchUpdateStatus('active')" :loading="batchLoading">
                <el-icon><Check /></el-icon>
                批量启用
              </el-button>
              <el-button @click="batchUpdateStatus('paused')" :loading="batchLoading">
                <el-icon><VideoPause /></el-icon>
                批量暂停
              </el-button>
              <el-button @click="batchAnalyze" :loading="batchLoading">
                <el-icon><TrendCharts /></el-icon>
                批量分析
              </el-button>
              <el-button type="danger" @click="batchDelete" :loading="batchLoading">
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 群组列表 -->
    <div class="group-list-container">
      <el-card class="list-card">
        <template #header>
          <div class="list-header">
            <div class="header-left">
              <h3>群组列表</h3>
              <el-tag type="info">{{ total }} 个群组</el-tag>
            </div>
            <div class="header-right">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="table">
                  <el-icon><Grid /></el-icon>
                  表格
                </el-radio-button>
                <el-radio-button label="card">
                  <el-icon><Postcard /></el-icon>
                  卡片
                </el-radio-button>
                <el-radio-button label="kanban">
                  <el-icon><Operation /></el-icon>
                  看板
                </el-radio-button>
              </el-radio-group>
              <el-divider direction="vertical" />
              <el-button @click="refreshData" :loading="loading">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button @click="showColumnSettings = true">
                <el-icon><Setting /></el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="groupList"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
            row-key="id"
            lazy
            :load="loadGroupDetails"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="expand" width="30">
              <template #default="{ row }">
                <div class="expand-content">
                  <GroupQuickInfo :group="row" />
                </div>
              </template>
            </el-table-column>
            
            <!-- 动态列配置 -->
            <el-table-column
              v-for="column in visibleColumns"
              :key="column.key"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              :sortable="column.sortable"
              :fixed="column.fixed"
            >
              <template #default="{ row }">
                <component 
                  :is="column.component" 
                  :data="row" 
                  :column="column"
                  @action="handleColumnAction"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <el-row :gutter="20">
            <el-col 
              v-for="group in groupList" 
              :key="group.id"
              :span="8"
            >
              <GroupCard 
                :group="group"
                :selected="selectedGroups.includes(group.id)"
                @select="toggleGroupSelection"
                @action="handleGroupAction"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 看板视图 -->
        <div v-else-if="viewMode === 'kanban'" class="kanban-view">
          <GroupKanban 
            :groups="groupList"
            @update="handleKanbanUpdate"
            @action="handleGroupAction"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 列设置对话框 -->
    <ColumnSettings
      v-model="showColumnSettings"
      :columns="allColumns"
      :visible-columns="visibleColumns"
      @update="updateColumnSettings"
    />

    <!-- 批量分析对话框 -->
    <BatchAnalysisDialog
      v-model="showBatchAnalysis"
      :group-ids="selectedGroups"
      @complete="handleBatchAnalysisComplete"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Download, Check, VideoPause, Delete, TrendCharts,
  Grid, Postcard, Operation, Refresh, Setting
} from '@element-plus/icons-vue'

// 组件导入
import GroupCard from './GroupCard.vue'
import GroupKanban from './GroupKanban.vue'
import GroupQuickInfo from './GroupQuickInfo.vue'
import ColumnSettings from './ColumnSettings.vue'
import BatchAnalysisDialog from './BatchAnalysisDialog.vue'

// API导入
import { 
  getGroupList, 
  batchUpdateGroupStatus,
  exportGroups
} from '@/api/community'
import {
  getRealTimeGroupStatus,
  batchManageMembers,
  generateCommunityReport
} from '@/api/community-enhanced'

// 响应式数据
const loading = ref(false)
const batchLoading = ref(false)
const autoRefresh = ref(false)
const showRealTimeData = ref(true)
const showAdvancedFilter = ref(false)
const showColumnSettings = ref(false)
const showBatchAnalysis = ref(false)

const viewMode = ref('table')
const searchQuery = ref('')
const currentPreset = ref('all')
const selectedGroups = ref([])
const groupList = ref([])
const total = ref(0)

const pagination = reactive({
  page: 1,
  size: 20
})

const filters = reactive({
  status: '',
  category: '',
  healthScore: '',
  memberRange: '',
  revenueRange: '',
  dateRange: null
})

const realTimeData = reactive({
  activeUsers: 0,
  newMessages: 0,
  todayRevenue: 0,
  pendingReviews: 0
})

// 筛选预设
const filterPresets = [
  { key: 'all', label: '全部群组' },
  { key: 'active', label: '活跃群组' },
  { key: 'high_revenue', label: '高收益' },
  { key: 'need_attention', label: '需要关注' },
  { key: 'new_groups', label: '新建群组' }
]

// 列配置
const allColumns = ref([
  {
    key: 'info',
    label: '群组信息',
    prop: 'name',
    width: 280,
    fixed: 'left',
    sortable: false,
    component: 'GroupInfoColumn'
  },
  {
    key: 'owner',
    label: '群主',
    prop: 'owner_name',
    width: 120,
    sortable: true,
    component: 'OwnerColumn'
  },
  {
    key: 'members',
    label: '成员统计',
    prop: 'member_count',
    width: 150,
    sortable: true,
    component: 'MemberStatsColumn'
  },
  {
    key: 'revenue',
    label: '收益',
    prop: 'total_revenue',
    width: 120,
    sortable: true,
    component: 'RevenueColumn'
  },
  {
    key: 'health',
    label: '健康度',
    prop: 'health_score',
    width: 100,
    sortable: true,
    component: 'HealthScoreColumn'
  },
  {
    key: 'status',
    label: '状态',
    prop: 'status',
    width: 100,
    sortable: true,
    component: 'StatusColumn'
  },
  {
    key: 'created_at',
    label: '创建时间',
    prop: 'created_at',
    width: 160,
    sortable: true,
    component: 'DateColumn'
  },
  {
    key: 'actions',
    label: '操作',
    width: 200,
    fixed: 'right',
    component: 'ActionsColumn'
  }
])

const visibleColumns = ref([...allColumns.value])

// 计算属性
const tableRef = ref()
let refreshTimer = null

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchQuery.value,
      ...filters
    }
    
    const response = await getGroupList(params)
    groupList.value = response.data.list || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadRealTimeData = async () => {
  try {
    const response = await getRealTimeGroupStatus()
    Object.assign(realTimeData, response.data)
  } catch (error) {
    console.error('加载实时数据失败:', error)
  }
}

const handleSmartSearch = (value) => {
  // 智能搜索逻辑
  if (value.length > 2) {
    loadData()
  }
}

const applyFilterPreset = (preset) => {
  currentPreset.value = preset
  
  // 重置筛选条件
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  
  // 应用预设筛选
  switch (preset) {
    case 'active':
      filters.status = 'active'
      break
    case 'high_revenue':
      filters.revenueRange = '5000+'
      break
    case 'need_attention':
      filters.healthScore = 'poor'
      break
    case 'new_groups':
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      filters.dateRange = [sevenDaysAgo.toISOString().split('T')[0], new Date().toISOString().split('T')[0]]
      break
  }
  
  loadData()
}

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedGroups.value = validSelection.map(item => item.id)
}

const toggleGroupSelection = (groupId) => {
  const index = selectedGroups.value.indexOf(groupId)
  if (index > -1) {
    selectedGroups.value.splice(index, 1)
  } else {
    selectedGroups.value.push(groupId)
  }
}

const batchUpdateStatus = async (status) => {
  if (selectedGroups.value.length === 0) {
    ElMessage.warning('请先选择要操作的群组')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要${status === 'active' ? '启用' : '暂停'}选中的 ${selectedGroups.value.length} 个群组吗？`,
      '批量操作确认'
    )
    
    batchLoading.value = true
    await batchUpdateGroupStatus(selectedGroups.value, status)
    ElMessage.success('批量操作成功')
    selectedGroups.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const batchExport = async () => {
  try {
    batchLoading.value = true
    const params = {
      group_ids: selectedGroups.value
    }
    await exportGroups(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    batchLoading.value = false
  }
}

const batchAnalyze = () => {
  if (selectedGroups.value.length === 0) {
    ElMessage.warning('请先选择要分析的群组')
    return
  }
  showBatchAnalysis.value = true
}

const batchDelete = async () => {
  if (selectedGroups.value.length === 0) {
    ElMessage.warning('请先选择要删除的群组')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedGroups.value.length} 个群组吗？此操作不可恢复！`,
      '批量删除确认',
      { type: 'error' }
    )
    
    batchLoading.value = true
    // 批量删除逻辑
    ElMessage.success('批量删除成功')
    selectedGroups.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      loadRealTimeData()
    }, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

const refreshData = () => {
  loadData()
  loadRealTimeData()
}

const handleSortChange = ({ column, prop, order }) => {
  // 排序逻辑
  loadData()
}

const handlePageSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const loadGroupDetails = (tree, treeNode, resolve) => {
  // 懒加载群组详情
  setTimeout(() => {
    resolve([])
  }, 1000)
}

const handleColumnAction = (action, data) => {
  // 处理列操作
  console.log('Column action:', action, data)
}

const handleGroupAction = (action, group) => {
  // 处理群组操作
  console.log('Group action:', action, group)
}

const handleKanbanUpdate = (update) => {
  // 处理看板更新
  console.log('Kanban update:', update)
}

const updateColumnSettings = (columns) => {
  visibleColumns.value = columns
}

const handleBatchAnalysisComplete = () => {
  showBatchAnalysis.value = false
  ElMessage.success('批量分析完成')
}

// 生命周期
onMounted(() => {
  loadData()
  loadRealTimeData()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style lang="scss" scoped>
.group-list-enhanced {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.real-time-panel {
  margin-bottom: 20px;
  
  .real-time-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
    }
    
    .real-time-item {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      
      .item-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 4px;
      }
      
      .item-label {
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }
}

.smart-filter-panel {
  margin-bottom: 20px;
  
  .filter-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .filter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .filter-left {
        flex: 1;
        margin-right: 20px;
        
        .smart-search {
          max-width: 400px;
        }
      }
    }
    
    .advanced-filter {
      padding-top: 16px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.batch-toolbar {
  margin-bottom: 20px;
  
  .toolbar-card {
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .selection-info {
        font-weight: 600;
      }
    }
  }
}

.group-list-container {
  .list-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
        
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
      }
      
      .header-right {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
    
    .table-view {
      :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;
        
        .el-table__header {
          background: #f8fafc;
        }
        
        .el-table__row {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f8fafc;
          }
        }
      }
      
      .expand-content {
        padding: 20px;
        background: #f8fafc;
        border-radius: 8px;
        margin: 10px;
      }
    }
    
    .card-view {
      padding: 20px 0;
    }
    
    .kanban-view {
      padding: 20px 0;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .real-time-panel {
    .real-time-item {
      margin-bottom: 12px;
    }
  }
  
  .smart-filter-panel {
    .filter-content {
      flex-direction: column;
      gap: 16px;
      
      .filter-left,
      .filter-right {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .group-list-enhanced {
    padding: 12px;
  }
  
  .batch-toolbar {
    .toolbar-content {
      flex-direction: column;
      gap: 12px;
    }
  }
  
  .group-list-container {
    .list-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.real-time-card,
.filter-card,
.toolbar-card,
.list-card {
  animation: slideInUp 0.6s ease-out;
}
</style>
