<template>
  <div class="simple-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: isCollapsed }">
      <div class="logo">
        <h2>管理后台</h2>
      </div>
      
      <nav class="nav-menu">
        <!-- 业务核心域 -->
        <div class="nav-group">
          <div class="nav-group-title" v-if="!isCollapsed">业务核心</div>

          <router-link to="/dashboard" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">数据看板</span>
          </router-link>

          <router-link to="/data-screen" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">数据大屏</span>
          </router-link>

          <router-link to="/community/groups" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">社群管理</span>
          </router-link>

          <router-link to="/distribution/distributors" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">分销管理</span>
          </router-link>

          <router-link to="/orders/list" class="nav-item">
            <el-icon><Document /></el-icon>
            <span v-if="!isCollapsed">订单管理</span>
          </router-link>
        </div>

        <!-- 运营管理域 -->
        <div class="nav-group">
          <div class="nav-group-title" v-if="!isCollapsed">运营管理</div>

          <router-link to="/anti-block/dashboard" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">防红系统</span>
          </router-link>

          <router-link to="/promotion/links" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">推广管理</span>
          </router-link>

          <router-link to="/substation/list" class="nav-item">
            <el-icon><Document /></el-icon>
            <span v-if="!isCollapsed">分站管理</span>
          </router-link>

          <router-link to="/user/list" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">用户管理</span>
          </router-link>

          <router-link to="/agent/dashboard" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">代理商管理</span>
          </router-link>
        </div>

        <!-- 财务管理域 -->
        <div class="nav-group">
          <div class="nav-group-title" v-if="!isCollapsed">财务管理</div>

          <router-link to="/finance/dashboard" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">财务管理</span>
          </router-link>

          <router-link to="/payment/settings" class="nav-item">
            <el-icon><Document /></el-icon>
            <span v-if="!isCollapsed">支付管理</span>
          </router-link>
        </div>

        <!-- 系统配置域 -->
        <div class="nav-group">
          <div class="nav-group-title" v-if="!isCollapsed">系统配置</div>

          <router-link to="/permission/roles" class="nav-item">
            <el-icon><UserFilled /></el-icon>
            <span v-if="!isCollapsed">权限管理</span>
          </router-link>

          <router-link to="/system/settings" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">系统管理</span>
          </router-link>

          <router-link to="/security/management" class="nav-item">
            <el-icon><Monitor /></el-icon>
            <span v-if="!isCollapsed">安全管理</span>
          </router-link>
        </div>
      </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部栏 -->
      <header class="header">
        <div class="header-left">
          <el-button @click="toggleSidebar" :icon="Menu" circle />
          <span class="page-title">{{ $route.meta.title || '管理后台' }}</span>
        </div>
        
        <div class="header-right">
          <el-button @click="handleLogout" type="primary">退出登录</el-button>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="page-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, UserFilled, Document, Menu } from '@element-plus/icons-vue'

const router = useRouter()
const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 清除本地存储
    localStorage.clear()
    sessionStorage.clear()
    
    // 跳转到登录页
    router.push('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.simple-layout {
  display: flex;
  height: 100vh;
  background: #f0f2f5;
}

.sidebar {
  width: 250px;
  background: #001529;
  color: white;
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 80px;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #1f1f1f;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
}

.nav-menu {
  padding: 20px 0;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.nav-group {
  margin-bottom: 20px;
}

.nav-group-title {
  padding: 8px 20px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.65);
  text-decoration: none;
  transition: all 0.3s;
}

.nav-item:hover {
  background: #1890ff;
  color: white;
}

.nav-item.router-link-active {
  background: #1890ff;
  color: white;
}

.nav-item .el-icon {
  margin-right: 12px;
  font-size: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  height: 64px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}
</style>
