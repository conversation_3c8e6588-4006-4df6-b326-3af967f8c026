import api from './index'

// 模板管理相关API
export const getTemplateList = (params) => {
  return api.get('/admin/templates', { params })
}

export const getTemplateDetail = (id) => {
  return api.get(`/admin/templates/${id}`)
}

export const createTemplate = (data) => {
  return api.post('/admin/templates', data)
}

export const updateTemplate = (id, data) => {
  return api.put(`/admin/templates/${id}`, data)
}

export const deleteTemplate = (id) => {
  return api.delete(`/admin/templates/${id}`)
}

export const getTemplateCategories = () => {
  return api.get('/admin/group-templates/categories')
}

export const toggleTemplateStatus = (id) => {
  return api.post(`/admin/templates/${id}/toggle-status`)
}

export const uploadTemplateImage = (data) => {
  return api.post('/admin/templates/upload', data)
}

export const batchOperateTemplates = (data) => {
  return api.post('/admin/templates/batch', data)
}