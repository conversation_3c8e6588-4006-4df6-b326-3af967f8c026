<template>
  <div class="profile-settings">
    <el-form 
      ref="profileFormRef"
      :model="profileForm"
      :rules="profileRules"
      label-width="120px"
      class="settings-form"
    >
      <!-- 头像上传 -->
      <el-form-item label="头像" prop="avatar">
        <div class="avatar-upload-container">
          <el-avatar 
            :size="100" 
            :src="profileForm.avatar || defaultAvatar"
            class="avatar-preview"
          />
          <el-upload
            ref="avatarUploadRef"
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            :on-error="handleAvatarError"
            accept="image/*"
          >
            <el-button type="primary" size="small" :loading="uploading">
              <el-icon><Camera /></el-icon>
              更换头像
            </el-button>
          </el-upload>
          <el-button 
            v-if="profileForm.avatar" 
            size="small" 
            type="danger" 
            plain
            @click="removeAvatar"
          >
            <el-icon><Delete /></el-icon>
            移除
          </el-button>
        </div>
      </el-form-item>

      <!-- 基本信息 -->
      <el-form-item label="用户名" prop="username">
        <el-input v-model="profileForm.username" disabled placeholder="用户名不可修改" />
      </el-form-item>

      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="profileForm.nickname" placeholder="请输入昵称" maxlength="20" show-word-limit />
      </el-form-item>

      <el-form-item label="真实姓名" prop="real_name">
        <el-input v-model="profileForm.real_name" placeholder="请输入真实姓名" maxlength="10" />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model="profileForm.email" placeholder="请输入邮箱地址" />
        <el-button 
          v-if="!userStore.userInfo?.email_verified" 
          type="primary" 
          size="small" 
          style="margin-left: 10px"
          @click="sendEmailVerification"
          :loading="emailSending"
        >
          验证邮箱
        </el-button>
        <el-tag v-else type="success" size="small" style="margin-left: 10px">
          <el-icon><Check /></el-icon>
          已验证
        </el-tag>
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input v-model="profileForm.phone" placeholder="请输入手机号" maxlength="11" />
        <el-button 
          v-if="!userStore.userInfo?.phone_verified" 
          type="primary" 
          size="small" 
          style="margin-left: 10px"
          @click="sendPhoneVerification"
          :loading="phoneSending"
        >
          验证手机
        </el-button>
        <el-tag v-else type="success" size="small" style="margin-left: 10px">
          <el-icon><Check /></el-icon>
          已验证
        </el-tag>
      </el-form-item>

      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="profileForm.gender">
          <el-radio value="male">男</el-radio>
          <el-radio value="female">女</el-radio>
          <el-radio value="other">保密</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="生日" prop="birthday">
        <el-date-picker
          v-model="profileForm.birthday"
          type="date"
          placeholder="选择生日"
          :disabled-date="disabledBirthday"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="个人简介" prop="bio">
        <el-input
          v-model="profileForm.bio"
          type="textarea"
          :rows="4"
          placeholder="请输入个人简介，最多200字"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="所在地" prop="location">
        <el-cascader
          v-model="profileForm.location"
          :options="regionOptions"
          placeholder="请选择省市区"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="个人网站" prop="website">
        <el-input v-model="profileForm.website" placeholder="请输入个人网站URL" />
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="saveProfile" :loading="saving">
          <el-icon><Check /></el-icon>
          保存修改
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 邮箱验证对话框 -->
    <el-dialog
      v-model="emailVerifyVisible"
      title="邮箱验证"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="emailVerifyFormRef" :model="emailVerifyForm" :rules="emailVerifyRules">
        <el-form-item label="验证码" prop="code">
          <el-input
            v-model="emailVerifyForm.code"
            placeholder="请输入验证码"
            maxlength="6"
          >
            <template #append>
              <el-button 
                @click="resendEmailCode" 
                :disabled="emailCountdown > 0"
              >
                {{ emailCountdown > 0 ? `${emailCountdown}s` : '重新发送' }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="emailVerifyVisible = false">取消</el-button>
        <el-button type="primary" @click="verifyEmail" :loading="emailVerifying">
          确认验证
        </el-button>
      </template>
    </el-dialog>

    <!-- 手机验证对话框 -->
    <el-dialog
      v-model="phoneVerifyVisible"
      title="手机验证"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="phoneVerifyFormRef" :model="phoneVerifyForm" :rules="phoneVerifyRules">
        <el-form-item label="验证码" prop="code">
          <el-input
            v-model="phoneVerifyForm.code"
            placeholder="请输入验证码"
            maxlength="6"
          >
            <template #append>
              <el-button 
                @click="resendPhoneCode" 
                :disabled="phoneCountdown > 0"
              >
                {{ phoneCountdown > 0 ? `${phoneCountdown}s` : '重新发送' }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="phoneVerifyVisible = false">取消</el-button>
        <el-button type="primary" @click="verifyPhone" :loading="phoneVerifying">
          确认验证
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Check, Delete } from '@element-plus/icons-vue'
import { uploadImage } from '@/api/upload'
import { updateProfile, sendVerificationCode, verifyCode } from '@/api/user'

const userStore = useUserStore()

// 表单引用
const profileFormRef = ref()
const emailVerifyFormRef = ref()
const phoneVerifyFormRef = ref()

// 表单数据
const profileForm = reactive({
  avatar: '',
  username: '',
  nickname: '',
  real_name: '',
  email: '',
  phone: '',
  gender: 'other',
  birthday: '',
  bio: '',
  location: [],
  website: ''
})

// 验证规则
const profileRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  real_name: [
    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  website: [
    { type: 'url', message: '请输入正确的网址', trigger: 'blur' }
  ]
}

// 地区选项（简化版）
const regionOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'dongcheng', label: '东城区' },
      { value: 'xicheng', label: '西城区' },
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'jingan', label: '静安区' },
      { value: 'pudong', label: '浦东新区' }
    ]
  },
  {
    value: 'guangdong',
    label: '广东省',
    children: [
      { value: 'guangzhou', label: '广州市', children: [
        { value: 'tianhe', label: '天河区' },
        { value: 'yuexiu', label: '越秀区' }
      ]},
      { value: 'shenzhen', label: '深圳市', children: [
        { value: 'nanshan', label: '南山区' },
        { value: 'futian', label: '福田区' }
      ]}
    ]
  }
]

// 头像上传相关
const uploading = ref(false)
const uploadUrl = '/api/upload/avatar'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

const defaultAvatar = '/default-avatar.png'

// 邮箱验证相关
const emailVerifyVisible = ref(false)
const emailSending = ref(false)
const emailVerifying = ref(false)
const emailCountdown = ref(0)
const emailVerifyForm = reactive({
  code: ''
})

const emailVerifyRules = {
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// 手机验证相关
const phoneVerifyVisible = ref(false)
const phoneSending = ref(false)
const phoneVerifying = ref(false)
const phoneCountdown = ref(0)
const phoneVerifyForm = reactive({
  code: ''
})

const phoneVerifyRules = {
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// 保存状态
const saving = ref(false)

// 生日日期限制
const disabledBirthday = (time) => {
  return time.getTime() > Date.now()
}

// 初始化表单数据
const initFormData = () => {
  const userInfo = userStore.userInfo
  if (userInfo) {
    Object.assign(profileForm, {
      avatar: userInfo.avatar || '',
      username: userInfo.username || '',
      nickname: userInfo.nickname || '',
      real_name: userInfo.real_name || '',
      email: userInfo.email || '',
      phone: userInfo.phone || '',
      gender: userInfo.gender || 'other',
      birthday: userInfo.birthday || '',
      bio: userInfo.bio || '',
      location: userInfo.location || [],
      website: userInfo.website || ''
    })
  }
}

// 头像上传成功处理
const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    profileForm.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
  uploading.value = false
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  uploading.value = true
  return true
}

// 头像上传错误处理
const handleAvatarError = () => {
  ElMessage.error('头像上传失败')
  uploading.value = false
}

// 移除头像
const removeAvatar = () => {
  ElMessageBox.confirm('确定要移除头像吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    profileForm.avatar = ''
    ElMessage.success('头像已移除')
  })
}

// 发送邮箱验证码
const sendEmailVerification = async () => {
  if (!profileForm.email) {
    ElMessage.error('请先填写邮箱地址')
    return
  }
  
  try {
    emailSending.value = true
    const res = await sendVerificationCode({
      type: 'email',
      email: profileForm.email
    })
    
    if (res.code === 200) {
      emailVerifyVisible.value = true
      emailCountdown.value = 60
      const timer = setInterval(() => {
        emailCountdown.value--
        if (emailCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      ElMessage.success('验证码已发送到您的邮箱')
    } else {
      ElMessage.error(res.message || '发送失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '发送失败')
  } finally {
    emailSending.value = false
  }
}

// 发送手机验证码
const sendPhoneVerification = async () => {
  if (!profileForm.phone) {
    ElMessage.error('请先填写手机号')
    return
  }
  
  try {
    phoneSending.value = true
    const res = await sendVerificationCode({
      type: 'phone',
      phone: profileForm.phone
    })
    
    if (res.code === 200) {
      phoneVerifyVisible.value = true
      phoneCountdown.value = 60
      const timer = setInterval(() => {
        phoneCountdown.value--
        if (phoneCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      ElMessage.success('验证码已发送到您的手机')
    } else {
      ElMessage.error(res.message || '发送失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '发送失败')
  } finally {
    phoneSending.value = false
  }
}

// 验证邮箱
const verifyEmail = async () => {
  try {
    await emailVerifyFormRef.value.validate()
    emailVerifying.value = true
    
    const res = await verifyCode({
      type: 'email',
      email: profileForm.email,
      code: emailVerifyForm.code
    })
    
    if (res.code === 200) {
      ElMessage.success('邮箱验证成功')
      emailVerifyVisible.value = false
      emailVerifyForm.code = ''
      // 更新用户信息
      await userStore.getUserInfo()
    } else {
      ElMessage.error(res.message || '验证失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '验证失败')
  } finally {
    emailVerifying.value = false
  }
}

// 验证手机
const verifyPhone = async () => {
  try {
    await phoneVerifyFormRef.value.validate()
    phoneVerifying.value = true
    
    const res = await verifyCode({
      type: 'phone',
      phone: profileForm.phone,
      code: phoneVerifyForm.code
    })
    
    if (res.code === 200) {
      ElMessage.success('手机验证成功')
      phoneVerifyVisible.value = false
      phoneVerifyForm.code = ''
      // 更新用户信息
      await userStore.getUserInfo()
    } else {
      ElMessage.error(res.message || '验证失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '验证失败')
  } finally {
    phoneVerifying.value = false
  }
}

// 重新发送邮箱验证码
const resendEmailCode = async () => {
  await sendEmailVerification()
}

// 重新发送手机验证码
const resendPhoneCode = async () => {
  await sendPhoneVerification()
}

// 保存个人资料
const saveProfile = async () => {
  try {
    await profileFormRef.value.validate()
    saving.value = true
    
    const res = await updateProfile(profileForm)
    
    if (res.code === 200) {
      ElMessage.success('个人资料更新成功')
      await userStore.getUserInfo()
    } else {
      ElMessage.error(res.message || '更新失败')
    }
  } catch (error) {
    if (error === false) {
      // 表单验证失败
      return
    }
    ElMessage.error(error.message || '更新失败')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  ElMessageBox.confirm('确定要重置表单吗？所有未保存的修改将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    initFormData()
    profileFormRef.value.clearValidate()
    ElMessage.info('表单已重置')
  })
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
})

// 暴露方法给父组件
defineExpose({
  saveProfile
})
</script>

<style scoped>
.profile-settings {
  padding: 30px;
}

.settings-form {
  max-width: 600px;
}

.avatar-upload-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-preview {
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.avatar-preview:hover {
  border-color: #667eea;
  transform: scale(1.05);
}

.avatar-uploader {
  display: inline-block;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
  color: #c0c4cc;
}

@media screen and (max-width: 768px) {
  .profile-settings {
    padding: 20px;
  }
  
  .avatar-upload-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .settings-form {
    max-width: 100%;
  }
  
  :deep(.el-form-item__label) {
    text-align: left;
    float: none;
    display: block;
    margin-bottom: 8px;
  }
  
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style>