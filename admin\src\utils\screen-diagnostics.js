/**
 * 数据大屏显示诊断工具
 * 用于检测和修复数据大屏显示问题
 */

export class ScreenDiagnostics {
  constructor() {
    this.issues = []
    this.recommendations = []
  }

  /**
   * 运行完整诊断
   */
  async runDiagnostics() {
    this.issues = []
    this.recommendations = []
    
    this.checkViewportSize()
    this.checkScrollbars()
    this.checkOverflow()
    this.checkZIndex()
    this.checkFlexLayout()
    this.checkGridLayout()
    this.checkResponsiveBreakpoints()
    
    return {
      issues: this.issues,
      recommendations: this.recommendations,
      summary: this.generateSummary()
    }
  }

  /**
   * 检查视口尺寸
   */
  checkViewportSize() {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio || 1
    }
    
    console.log('🖥️ 视口信息:', viewport)
    
    if (viewport.width < 1200) {
      this.issues.push({
        type: 'warning',
        message: `屏幕宽度较小 (${viewport.width}px)，建议使用1200px以上的屏幕`,
        element: 'viewport'
      })
      this.recommendations.push('考虑使用响应式布局或移动端优化版本')
    }
    
    if (viewport.height < 800) {
      this.issues.push({
        type: 'warning',
        message: `屏幕高度较小 (${viewport.height}px)，可能导致内容显示不完整`,
        element: 'viewport'
      })
      this.recommendations.push('启用垂直滚动或调整布局密度')
    }
    
    return viewport
  }

  /**
   * 检查滚动条问题
   */
  checkScrollbars() {
    const body = document.body
    const html = document.documentElement
    
    const hasHorizontalScroll = body.scrollWidth > body.clientWidth
    const hasVerticalScroll = body.scrollHeight > body.clientHeight
    
    if (hasHorizontalScroll) {
      this.issues.push({
        type: 'error',
        message: '检测到水平滚动条，可能导致布局错乱',
        element: 'body'
      })
      this.recommendations.push('检查元素宽度设置，确保不超过100vw')
    }
    
    console.log('📏 滚动条状态:', { hasHorizontalScroll, hasVerticalScroll })
  }

  /**
   * 检查溢出问题
   */
  checkOverflow() {
    const dataScreens = document.querySelectorAll('.data-screen, .ultra-data-screen, .enhanced-data-screen, .optimized-data-screen')
    
    dataScreens.forEach((screen, index) => {
      const rect = screen.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(screen)
      
      if (rect.width > window.innerWidth) {
        this.issues.push({
          type: 'error',
          message: `数据大屏 ${index + 1} 宽度溢出 (${rect.width}px > ${window.innerWidth}px)`,
          element: screen
        })
      }
      
      if (rect.height > window.innerHeight && computedStyle.overflow !== 'auto') {
        this.issues.push({
          type: 'warning',
          message: `数据大屏 ${index + 1} 高度溢出且无滚动 (${rect.height}px > ${window.innerHeight}px)`,
          element: screen
        })
      }
    })
  }

  /**
   * 检查z-index层级
   */
  checkZIndex() {
    const elements = document.querySelectorAll('[style*="z-index"], .fullscreen-mode')
    
    elements.forEach(el => {
      const zIndex = window.getComputedStyle(el).zIndex
      if (zIndex !== 'auto' && parseInt(zIndex) > 9999) {
        console.log('⚠️ 高z-index元素:', el, zIndex)
      }
    })
  }

  /**
   * 检查Flex布局
   */
  checkFlexLayout() {
    const flexContainers = document.querySelectorAll('.data-screen, .screen-content, .charts-grid')
    
    flexContainers.forEach(container => {
      const style = window.getComputedStyle(container)
      if (style.display === 'flex') {
        const children = Array.from(container.children)
        const totalFlexGrow = children.reduce((sum, child) => {
          return sum + (parseFloat(window.getComputedStyle(child).flexGrow) || 0)
        }, 0)
        
        if (totalFlexGrow === 0 && children.length > 0) {
          this.issues.push({
            type: 'info',
            message: 'Flex容器中没有flex-grow元素，可能导致空间分配问题',
            element: container
          })
        }
      }
    })
  }

  /**
   * 检查Grid布局
   */
  checkGridLayout() {
    const gridContainers = document.querySelectorAll('.metrics-grid, .charts-grid')
    
    gridContainers.forEach(container => {
      const style = window.getComputedStyle(container)
      if (style.display === 'grid') {
        const rect = container.getBoundingClientRect()
        const children = container.children
        
        if (children.length === 0) {
          this.issues.push({
            type: 'warning',
            message: 'Grid容器为空',
            element: container
          })
        }
        
        console.log('📐 Grid布局信息:', {
          element: container.className,
          columns: style.gridTemplateColumns,
          rows: style.gridTemplateRows,
          gap: style.gap,
          childrenCount: children.length
        })
      }
    })
  }

  /**
   * 检查响应式断点
   */
  checkResponsiveBreakpoints() {
    const width = window.innerWidth
    const breakpoints = {
      xs: 480,
      sm: 768,
      md: 1024,
      lg: 1200,
      xl: 1400,
      xxl: 1600,
      xxxl: 1920,
      xxxxl: 2560
    }
    
    let currentBreakpoint = 'xs'
    for (const [name, minWidth] of Object.entries(breakpoints)) {
      if (width >= minWidth) {
        currentBreakpoint = name
      }
    }
    
    console.log('📱 当前断点:', currentBreakpoint, `(${width}px)`)
    
    // 检查是否有对应的CSS规则
    const mediaQueries = Array.from(document.styleSheets)
      .flatMap(sheet => {
        try {
          return Array.from(sheet.cssRules || [])
        } catch (e) {
          return []
        }
      })
      .filter(rule => rule.type === CSSRule.MEDIA_RULE)
    
    const hasResponsiveRules = mediaQueries.some(rule => 
      rule.conditionText.includes(`${breakpoints[currentBreakpoint]}px`)
    )
    
    if (!hasResponsiveRules) {
      this.issues.push({
        type: 'warning',
        message: `当前断点 ${currentBreakpoint} 可能缺少对应的CSS媒体查询规则`,
        element: 'css'
      })
    }
  }

  /**
   * 自动修复常见问题
   */
  autoFix() {
    const fixes = []
    
    // 修复水平滚动
    const body = document.body
    if (body.scrollWidth > body.clientWidth) {
      body.style.overflowX = 'hidden'
      fixes.push('隐藏水平滚动条')
    }
    
    // 修复数据大屏溢出
    const dataScreens = document.querySelectorAll('.data-screen, .ultra-data-screen, .enhanced-data-screen, .optimized-data-screen')
    dataScreens.forEach(screen => {
      const rect = screen.getBoundingClientRect()
      if (rect.width > window.innerWidth) {
        screen.style.width = '100vw'
        screen.style.maxWidth = '100vw'
        fixes.push('修复数据大屏宽度溢出')
      }
      
      if (rect.height > window.innerHeight) {
        screen.style.height = '100vh'
        screen.style.overflowY = 'auto'
        fixes.push('修复数据大屏高度溢出')
      }
    })
    
    return fixes
  }

  /**
   * 生成诊断摘要
   */
  generateSummary() {
    const errorCount = this.issues.filter(issue => issue.type === 'error').length
    const warningCount = this.issues.filter(issue => issue.type === 'warning').length
    const infoCount = this.issues.filter(issue => issue.type === 'info').length
    
    return {
      total: this.issues.length,
      errors: errorCount,
      warnings: warningCount,
      info: infoCount,
      status: errorCount > 0 ? 'error' : warningCount > 0 ? 'warning' : 'success'
    }
  }

  /**
   * 输出诊断报告
   */
  printReport() {
    console.group('🔍 数据大屏显示诊断报告')
    
    const summary = this.generateSummary()
    console.log('📊 诊断摘要:', summary)
    
    if (this.issues.length > 0) {
      console.group('⚠️ 发现的问题:')
      this.issues.forEach((issue, index) => {
        const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️'
        console.log(`${icon} ${index + 1}. ${issue.message}`)
      })
      console.groupEnd()
    }
    
    if (this.recommendations.length > 0) {
      console.group('💡 优化建议:')
      this.recommendations.forEach((rec, index) => {
        console.log(`💡 ${index + 1}. ${rec}`)
      })
      console.groupEnd()
    }
    
    console.groupEnd()
    
    return summary
  }
}

/**
 * Vue 3 Composition API 钩子
 */
export function useScreenDiagnostics() {
  const diagnostics = new ScreenDiagnostics()
  
  const runDiagnostics = async () => {
    const result = await diagnostics.runDiagnostics()
    diagnostics.printReport()
    return result
  }
  
  const autoFix = () => {
    return diagnostics.autoFix()
  }
  
  return {
    runDiagnostics,
    autoFix,
    diagnostics
  }
}

/**
 * 快速诊断函数
 */
export const quickDiagnose = () => {
  const diagnostics = new ScreenDiagnostics()
  return diagnostics.runDiagnostics().then(() => {
    return diagnostics.printReport()
  })
}

/**
 * 快速修复函数
 */
export const quickFix = () => {
  const diagnostics = new ScreenDiagnostics()
  const fixes = diagnostics.autoFix()
  console.log('🔧 应用的修复:', fixes)
  return fixes
}
