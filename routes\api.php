<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\GroupController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\DistributorControllerMerged as DistributorController;
use App\Http\Controllers\Api\GroupOwnerController;
use App\Http\Controllers\Api\UserManagementController;
use App\Http\Controllers\Api\TemplateController;
use App\Http\Controllers\Api\DistributionGroupController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\StatisticsController;
use App\Http\Controllers\Api\WithdrawalController;
use App\Http\Controllers\Api\CommissionController;
use App\Http\Controllers\Api\WechatGroupController;
use App\Http\Controllers\Api\SubstationController;
use App\Http\Controllers\Api\SubstationFinanceController;
use App\Http\Controllers\Api\AgentApplicationController;
use App\Http\Controllers\Api\AgentController;
use App\Http\Controllers\Api\FinanceController;
use App\Http\Controllers\Api\DistributionController;
use App\Http\Controllers\Api\CommissionLogController;
use App\Http\Controllers\Api\PaymentChannelController;
use App\Http\Controllers\Api\PaymentChannelManagementController;
use App\Http\Controllers\Api\PaymentConfigController;
use App\Http\Controllers\Api\PaymentPermissionController;
use App\Http\Controllers\Api\PaymentUsageLogController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\SystemController;
use App\Http\Controllers\Api\PromotionLinkController;
use App\Http\Controllers\Api\Admin\AntiBlockController;
use App\Http\Controllers\Api\PaymentCallbackController;
use App\Http\Controllers\Api\LandingPageController;
use App\Http\Controllers\Api\TestController;
use App\Http\Controllers\Api\SystemMonitorController;
use App\Services\AntiBlockService;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// 公开路由 - 添加速率限制
Route::prefix('api/v1')->middleware('throttle:60,1')->group(function () {
    // 健康检查
    Route::get('health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0')
        ]);
    });
    
    // 认证相关路由
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('logout', [AuthController::class, 'logout']);
    
    // 微信群相关公开路由
    Route::get('groups', [WechatGroupController::class, 'index']);
    Route::get('groups/{id}', [WechatGroupController::class, 'show']);
    Route::post('groups/{id}/join', [WechatGroupController::class, 'join']);
    
    // 分销员登录
    Route::post('distributor/login', [DistributorController::class, 'login']);
    
    // 获取群组模板
    Route::get('templates', [TemplateController::class, 'index']);
    Route::get('templates/{id}', [TemplateController::class, 'show']);
    
    // 支付相关路由
    Route::prefix('payment')->group(function () {
        // 支付回调（使用专门的回调控制器）
        Route::post('notify/wechat', [PaymentCallbackController::class, 'wechatNotify']);
        Route::post('notify/alipay', [PaymentCallbackController::class, 'alipayNotify']);
        Route::post('notify/payoreo', [PaymentCallbackController::class, 'payoreoNotify']);
        Route::post('notify/qqpay', [PaymentCallbackController::class, 'qqpayNotify']);
        Route::post('notify/bank', [PaymentCallbackController::class, 'bankNotify']);
        Route::get('return', [PaymentCallbackController::class, 'paymentReturn']);
        
        // 兼容旧的通用回调接口
        Route::post('notify/{method}', [App\Http\Controllers\Api\PaymentController::class, 'notify']);
        
        // 其他支付相关接口
        Route::get('show/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'show']);
        Route::get('methods', [App\Http\Controllers\Api\PaymentController::class, 'getPaymentMethods']);
        
        // 测试和日志接口（需要认证）
        Route::middleware('auth:api')->group(function () {
            Route::post('test-callback', [PaymentCallbackController::class, 'testCallback']);
            Route::get('callback-logs', [PaymentCallbackController::class, 'getCallbackLogs']);
        });
    });
    
    // 短链接跳转
    Route::get('s/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'redirect']);
    
    // 公开设置
    Route::get('public/settings', [SystemController::class, 'getPublicSettings']);
    Route::get('payment-channels/active', [PaymentChannelController::class, 'getActiveChannels']);
});

// 需要认证的路由
Route::prefix('api/v1')->middleware('auth:api')->group(function () {
    
    // 支付相关路由（需要认证）
    Route::prefix('payment')->group(function () {
        Route::post('create-order', [App\Http\Controllers\Api\PaymentController::class, 'createOrder']);
        Route::post('create-payment', [App\Http\Controllers\Api\PaymentController::class, 'createPayment']);
        Route::get('query/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'queryOrder']);
        Route::post('cancel/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'cancelOrder']);
    });
    
    // 用户相关路由
    Route::prefix('user')->group(function () {
        Route::get('profile', [UserController::class, 'profile']);
        Route::put('profile', [UserController::class, 'updateProfile']);
        Route::post('change-password', [UserController::class, 'changePassword']);
        Route::get('orders', [UserController::class, 'orders']);
        Route::get('groups', [UserController::class, 'groups']);
        Route::get('statistics', [UserController::class, 'statistics']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::get('info', [AuthController::class, 'me']);
        Route::get('stats', [AuthController::class, 'getUserStats']);
    });
    
    // 认证相关
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::post('update-profile', [AuthController::class, 'updateProfile']);
    });
    
    // 订单相关路由
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::post('/', [OrderController::class, 'store']);
        Route::get('{id}', [OrderController::class, 'show']);
        Route::put('{id}', [OrderController::class, 'update']);
        Route::delete('{id}', [OrderController::class, 'destroy']);
        Route::post('{id}/pay', [OrderController::class, 'pay']);
        Route::post('{id}/cancel', [OrderController::class, 'cancel']);
        Route::post('{id}/refund', [OrderController::class, 'refund']);
    });
    
    // 群主相关路由
    Route::prefix('owner')->group(function () {
        Route::get('groups', [GroupOwnerController::class, 'getOwnerGroups']);
        Route::post('groups', [GroupOwnerController::class, 'createGroup']);
        Route::get('groups/{id}', [GroupOwnerController::class, 'getGroupDetail']);
        Route::put('groups/{id}', [GroupOwnerController::class, 'updateGroup']);
        Route::delete('groups/{id}', [GroupOwnerController::class, 'deleteGroup']);
        Route::post('groups/{id}/duplicate', [GroupOwnerController::class, 'duplicateGroup']);
        Route::post('groups/batch', [GroupOwnerController::class, 'batchOperation']);
        Route::post('groups/upload-cover', [GroupOwnerController::class, 'uploadCoverImage']);
        Route::post('groups/upload-qr', [GroupOwnerController::class, 'uploadQRCode']);
        Route::get('templates', [GroupOwnerController::class, 'getAvailableTemplates']);
        Route::post('templates/{id}/use', [GroupOwnerController::class, 'createGroupFromTemplate']);
        Route::get('statistics', [GroupOwnerController::class, 'getStatistics']);
    });
    
    // 分销员相关路由
    Route::prefix('distributor')->group(function () {
        Route::get('dashboard', [DistributorController::class, 'dashboard']);
        Route::get('profile', [DistributorController::class, 'profile']);
        Route::put('profile', [DistributorController::class, 'updateProfile']);
        Route::get('groups', [DistributorController::class, 'groups']);
        Route::get('orders', [DistributorController::class, 'orders']);
        Route::get('statistics', [DistributorController::class, 'statistics']);
        Route::get('commissions', [DistributorController::class, 'commissions']);
        Route::get('withdrawals', [DistributorController::class, 'withdrawals']);
        Route::post('withdrawals', [DistributorController::class, 'createWithdrawal']);
        Route::get('sub-distributors', [DistributorController::class, 'subDistributors']);
        Route::post('sub-distributors', [DistributorController::class, 'createSubDistributor']);
        Route::get('templates', [DistributorController::class, 'templates']);
        Route::get('my-info', [DistributionController::class, 'getMyDistributionInfo']);
        Route::get('my-commissions', [DistributionController::class, 'getMyCommissions']);
        Route::post('generate-invite', [DistributionController::class, 'generateInviteLink']);
    });
    
    // 仪表板
    Route::prefix('dashboard')->group(function() {
        Route::get('stats', [DashboardController::class, 'getStats']);
        Route::get('chart-data', [DashboardController::class, 'getChartData']);
    });
    
    // 微信群管理
    Route::prefix('wechat-groups')->group(function () {
        Route::get('/', [WechatGroupController::class, 'index']);
        Route::post('/', [WechatGroupController::class, 'store']);
        Route::get('stats', [WechatGroupController::class, 'stats']);
        Route::get('{group}', [WechatGroupController::class, 'show']);
        Route::put('{group}', [WechatGroupController::class, 'update']);
        Route::delete('{group}', [WechatGroupController::class, 'destroy']);
        Route::get('{id}/detailed-stats', [WechatGroupController::class, 'getDetailedStats']);
        Route::patch('{id}/status', [WechatGroupController::class, 'updateStatus']);
        Route::post('{id}/qr-code', [WechatGroupController::class, 'updateQrCode']);
        Route::post('batch-delete', [WechatGroupController::class, 'batchDelete']);
        Route::get('export', [WechatGroupController::class, 'export']);
        Route::post('upload-cover', [WechatGroupController::class, 'uploadCover']);
        
        // 内容管理相关路由
        Route::get('{id}/content', [WechatGroupController::class, 'getGroupContent']);
        Route::put('{id}/content', [WechatGroupController::class, 'updateGroupContent']);
        Route::get('{id}/members', [WechatGroupController::class, 'getGroupMembers']);
        Route::get('{id}/analytics', [WechatGroupController::class, 'getGroupAnalytics']);
        
        // 营销功能路由
        Route::get('{id}/marketing-config', [WechatGroupController::class, 'getMarketingConfig']);
        Route::put('{id}/marketing-config', [WechatGroupController::class, 'updateMarketingConfig']);
        Route::post('{id}/virtual-members', [WechatGroupController::class, 'generateVirtualMembers']);
    });
    
    // 推广链接
    Route::apiResource('promotion-links', PromotionLinkController::class);
    
    // 防红系统管理
    Route::prefix('anti-block')->name('anti-block.')->group(function () {
        Route::get('overview', [AntiBlockController::class, 'overview'])->name('overview');
        Route::post('batch-check-domains', [AntiBlockController::class, 'batchCheckDomains'])->name('batch-check-domains');
        Route::get('system-config', [AntiBlockController::class, 'getSystemConfig'])->name('system-config');
        Route::put('system-config', [AntiBlockController::class, 'updateSystemConfig'])->name('update-system-config');
        Route::get('logs', [AntiBlockController::class, 'getLogs'])->name('logs');
        
        // 群组防红管理
        Route::prefix('groups/{groupId}')->name('groups.')->group(function () {
            Route::get('config', [AntiBlockController::class, 'getGroupAntiBlockConfig'])->name('config');
            Route::put('config', [AntiBlockController::class, 'updateGroupAntiBlockConfig'])->name('update-config');
            Route::post('generate-link', [AntiBlockController::class, 'generateGroupPromotionLink'])->name('generate-link');
            Route::post('promotion-link', [AntiBlockController::class, 'generateGroupPromotionLink'])->name('promotion-link');
            Route::post('check-switch-domain', [AntiBlockController::class, 'checkAndSwitchGroupDomain'])->name('check-switch-domain');
            Route::get('promotion-links', [AntiBlockController::class, 'getGroupPromotionLinks'])->name('promotion-links');
        });
        
        // 推广链接批量操作
        Route::post('promotion-links/batch-operate', [AntiBlockController::class, 'batchOperatePromotionLinks'])->name('promotion-links.batch-operate');
        
        // 域名健康报告
        Route::get('domain-pools/{domainPoolId}/health-report', [AntiBlockController::class, 'getDomainHealthReport'])->name('domain-health-report');
    });
    
    // 短链接相关路由
    Route::prefix('short-links')->group(function () {
        Route::get('{shortCode}/info', [App\Http\Controllers\ShortLinkController::class, 'info']);
        Route::get('{shortCode}/preview', [App\Http\Controllers\ShortLinkController::class, 'preview']);
        Route::get('{shortCode}/stats', [App\Http\Controllers\ShortLinkController::class, 'stats']);
        Route::get('{shortCode}/qrcode', [App\Http\Controllers\ShortLinkController::class, 'qrcode']);
        Route::post('batch-check', [App\Http\Controllers\ShortLinkController::class, 'batchCheck']);
        Route::get('popular', [App\Http\Controllers\ShortLinkController::class, 'popular']);
        Route::get('health-check', [App\Http\Controllers\ShortLinkController::class, 'healthCheck']);
    });
    
    // 财务相关
    Route::prefix('finance')->group(function() {
        Route::get('overview', [FinanceController::class, 'getOverview']);
        Route::get('earnings-chart', [FinanceController::class, 'getEarningsChart']);
        Route::get('transactions', [FinanceController::class, 'getUserTransactions']);
        Route::get('withdrawals', [FinanceController::class, 'getUserWithdrawals']);
        Route::get('commissions', [FinanceController::class, 'getUserCommissions']);
        Route::post('withdrawals', [FinanceController::class, 'requestWithdrawal']);
        Route::post('recharge', [FinanceController::class, 'createRechargeOrder']);
    });
    
    // 通知
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/mark-as-read', [NotificationController::class, 'markAsRead']);
    });
    
    // 代理商申请（用户端）
    Route::prefix('agent-applications')->group(function () {
        Route::post('/', [AgentApplicationController::class, 'store']);
        Route::get('my', [AgentApplicationController::class, 'getMy']);
        Route::post('{id}/cancel', [AgentApplicationController::class, 'cancel']);
        Route::get('available-substations', [AgentApplicationController::class, 'getAvailableSubstations']);
    });
    
    // 文件上传
    Route::prefix('upload')->group(function () {
        Route::post('image', [UserController::class, 'uploadImage']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::post('qr-code', [GroupOwnerController::class, 'uploadQRCode']);
        Route::post('cover', [GroupOwnerController::class, 'uploadCoverImage']);
        Route::post('template', [TemplateController::class, 'uploadImage']);
    });
    
    // 管理员相关路由
    Route::prefix('admin')->middleware('role:admin')->group(function () {
        // 仪表板
        Route::get('dashboard', [AdminController::class, 'dashboard']);
        Route::get('dashboard/full-stats', [DashboardController::class, 'getFullStatsForAdmin']);
        Route::get('dashboard/charts', [DashboardController::class, 'getDashboardCharts']);
        Route::get('dashboard/income-chart', [DashboardController::class, 'getIncomeChart']);
        Route::get('dashboard/user-growth-chart', [DashboardController::class, 'getUserGrowthChart']);
        Route::get('dashboard/order-chart', [DashboardController::class, 'getOrderChart']);
        Route::get('dashboard/region-chart', [DashboardController::class, 'getRegionChart']);
        Route::get('dashboard/popular-groups', [DashboardController::class, 'getPopularGroups']);
        Route::get('dashboard/active-users', [DashboardController::class, 'getActiveUsers']);
        Route::get('dashboard/recent-activities', [DashboardController::class, 'getRecentActivities']);
        
        // 前端调用的缺失路由
        Route::get('dashboard/income-trend', [DashboardController::class, 'getIncomeTrend']);
        Route::get('dashboard/order-source', [DashboardController::class, 'getOrderSourceDistribution']);
        Route::get('dashboard/user-growth', [DashboardController::class, 'getUserGrowthData']);
        Route::get('dashboard/system-status', [DashboardController::class, 'getSystemStatus']);
        Route::get('dashboard/recent-orders', [DashboardController::class, 'getRecentOrders']);
        Route::get('dashboard/top-distributors', [DashboardController::class, 'getTopDistributors']);
        Route::get('dashboard/system-overview', [DashboardController::class, 'getSystemOverview']);
        Route::get('dashboard/business-metrics', [DashboardController::class, 'getBusinessMetrics']);
        Route::get('dashboard/realtime', [DashboardController::class, 'getRealTimeData']);
        Route::get('dashboard/notifications', [DashboardController::class, 'getNotifications']);
        Route::post('dashboard/notifications/{id}/read', [DashboardController::class, 'markNotificationRead']);
        Route::get('dashboard/todos', [DashboardController::class, 'getTodoList']);
        
        // 用户管理
        Route::prefix('users')->group(function () {
            Route::get('/', [UserManagementController::class, 'getUserList']);
            Route::get('stats', [UserManagementController::class, 'getUserStats']); // 添加统计端点
            Route::post('/', [UserManagementController::class, 'createUser']);
            Route::get('{id}', [UserManagementController::class, 'getUserDetail']);
            Route::put('{id}', [UserManagementController::class, 'updateUser']);
            Route::delete('{id}', [UserManagementController::class, 'deleteUser']);
            Route::put('{id}/reset-password', [UserManagementController::class, 'resetPassword']);
            Route::post('batch', [UserManagementController::class, 'batchCreateUsers']);
            Route::put('batch-status', [UserManagementController::class, 'batchUpdateStatus']); // 添加批量状态更新
            Route::get('export', [UserManagementController::class, 'exportUsers']);
            Route::get('template', [UserManagementController::class, 'downloadTemplate']);
            Route::post('{user}/status', [UserController::class, 'updateStatus']);
            Route::post('{user}/adjust-balance', [UserController::class, 'adjustBalance']);
            Route::get('{user}/balance-logs', [UserController::class, 'getBalanceLogs']);
            Route::get('{user}/children', [UserController::class, 'getChildren']);
            Route::get('{user}/stats', [UserController::class, 'getUserStats']);
            Route::post('{user}/reset-password', [UserController::class, 'resetPassword']);
        });
        
        // 群组管理
        Route::prefix('groups')->group(function () {
            Route::get('/', [WechatGroupController::class, 'adminIndex']);
            Route::get('stats', [WechatGroupController::class, 'getGroupStats']); // 添加统计端点
            Route::get('{id}', [WechatGroupController::class, 'adminShow']);
            Route::put('{id}', [WechatGroupController::class, 'adminUpdate']);
            Route::delete('{id}', [WechatGroupController::class, 'adminDestroy']);
            Route::post('batch', [WechatGroupController::class, 'batchOperation']);
            Route::put('batch-status', [WechatGroupController::class, 'batchUpdateStatus']); // 添加批量状态更新
            Route::get('{id}/statistics', [WechatGroupController::class, 'statistics']);
        });
        
        // 订单管理
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'adminIndex']);
            Route::get('full-list', [OrderController::class, 'getFullOrderList']);
            Route::get('stats', [OrderController::class, 'getOrderStats']);
            Route::post('batch-process', [OrderController::class, 'batchProcess']);
            Route::get('export', [OrderController::class, 'exportOrders']);
            Route::get('{id}', [OrderController::class, 'adminShow']);
            Route::put('{id}', [OrderController::class, 'adminUpdate']);
            Route::delete('{id}', [OrderController::class, 'adminDestroy']);
            Route::post('{id}/refund', [OrderController::class, 'refund']);
            Route::post('batch', [OrderController::class, 'batchOperation']);
            Route::get('statistics', [OrderController::class, 'statistics']);
        });
        
        // 模板管理
        Route::prefix('templates')->group(function () {
            Route::get('/', [TemplateController::class, 'adminIndex']);
            Route::post('/', [TemplateController::class, 'store']);
            Route::get('{id}', [TemplateController::class, 'adminShow']);
            Route::put('{id}', [TemplateController::class, 'update']);
            Route::delete('{id}', [TemplateController::class, 'destroy']);
            Route::post('batch', [TemplateController::class, 'batchOperation']);
            Route::post('upload', [TemplateController::class, 'uploadImage']);
        });
        
        // 分销组管理
        Route::prefix('distribution-groups')->group(function () {
            Route::get('/', [DistributionGroupController::class, 'index']);
            Route::post('/', [DistributionGroupController::class, 'store']);
            Route::get('{id}', [DistributionGroupController::class, 'show']);
            Route::put('{id}', [DistributionGroupController::class, 'update']);
            Route::delete('{id}', [DistributionGroupController::class, 'destroy']);
            Route::post('batch-delete', [DistributionGroupController::class, 'batchDelete']);
            Route::post('{id}/members', [DistributionGroupController::class, 'addMember']);
            Route::delete('{id}/members', [DistributionGroupController::class, 'removeMember']);
            Route::get('{id}/stats', [DistributionGroupController::class, 'getStats']);
            Route::post('batch-status', [DistributionGroupController::class, 'batchUpdateStatus']);
        });
        
        // 分销员管理
        Route::prefix('distributors')->group(function () {
            Route::get('/', [DistributorController::class, 'adminIndex']);
            Route::get('stats', [DistributorController::class, 'getStats']);
            Route::get('{id}', [DistributorController::class, 'adminShow']);
            Route::put('{id}', [DistributorController::class, 'adminUpdate']);
            Route::delete('{id}', [DistributorController::class, 'adminDestroy']);
            Route::put('{id}/status', [DistributorController::class, 'updateStatus']);
            Route::put('{id}/upgrade', [DistributorController::class, 'upgrade']);
            Route::put('{id}/balance', [DistributorController::class, 'manageBalance']);
            Route::put('{id}/level', [DistributorController::class, 'updateLevel']);
            Route::put('{id}/group', [DistributorController::class, 'updateDistributionGroup']);
            Route::post('{id}/templates', [DistributorController::class, 'assignTemplates']);
            Route::get('{id}/statistics', [DistributorController::class, 'adminStatistics']);
        });
        
        // 提现管理
        Route::prefix('withdrawals')->group(function () {
            Route::get('/', [WithdrawalController::class, 'adminIndex']);
            Route::get('{id}', [WithdrawalController::class, 'adminShow']);
            Route::put('{id}/approve', [WithdrawalController::class, 'approve']);
            Route::put('{id}/reject', [WithdrawalController::class, 'reject']);
            Route::put('{id}/complete', [WithdrawalController::class, 'complete']);
            Route::get('statistics', [WithdrawalController::class, 'statistics']);
        });
        
        // 佣金管理
        Route::prefix('commissions')->group(function () {
            Route::get('/', [CommissionController::class, 'adminIndex']);
            Route::get('{id}', [CommissionController::class, 'adminShow']);
            Route::post('recalculate', [CommissionController::class, 'recalculate']);
            Route::get('statistics', [CommissionController::class, 'statistics']);
        });
        
        // 佣金记录管理
        Route::prefix('commission-logs')->group(function () {
            Route::get('/', [CommissionLogController::class, 'index']);
            Route::get('{id}', [CommissionLogController::class, 'show']);
            Route::put('{id}', [CommissionLogController::class, 'update']);
            Route::delete('{id}', [CommissionLogController::class, 'destroy']);
            Route::post('batch-settle', [CommissionLogController::class, 'batchSettle']);
            Route::post('{log}/settle', [CommissionLogController::class, 'settle']);
        });
        
        // 财务管理
        Route::prefix('finance')->group(function() {
            Route::get('dashboard-stats', [FinanceController::class, 'getDashboardStats']);
            Route::get('trend-data', [FinanceController::class, 'getTrendData']);
            Route::get('income-sources', [FinanceController::class, 'getIncomeSources']);
            Route::get('transactions', [FinanceController::class, 'getTransactionList']);
            Route::get('withdrawals', [FinanceController::class, 'getWithdrawRecords']);
            Route::post('withdrawals/{id}/approve', [FinanceController::class, 'approveWithdraw']);
            Route::post('withdrawals/{id}/reject', [FinanceController::class, 'rejectWithdraw']);
            Route::get('recent-transactions', [FinanceController::class, 'getTransactionList']);
            Route::get('export-report', [FinanceController::class, 'exportReport']);
        });
        
        // 分销管理
        Route::prefix('distribution')->group(function() {
            Route::get('overview', [DistributionController::class, 'getOverview']);
            Route::get('distributors', [DistributionController::class, 'getDistributors']);
            Route::get('groups', [DistributionController::class, 'getGroups']);
            Route::get('commissions', [DistributionController::class, 'getCommissions']);
            Route::get('level-config', [DistributionController::class, 'getLevelConfig']);
            Route::post('batch-operation', [DistributionController::class, 'batchOperation']);
            Route::get('export-data', [DistributionController::class, 'exportData']);
        });
        
        // 推广管理
        Route::prefix('promotions')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\Admin\PromotionController::class, 'index']);
            Route::post('/', [App\Http\Controllers\Api\Admin\PromotionController::class, 'store']);
            Route::get('stats', [App\Http\Controllers\Api\Admin\PromotionController::class, 'getStats']);
            Route::get('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'show']);
            Route::put('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'update']);
            Route::delete('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'destroy']);
            Route::post('batch-action', [App\Http\Controllers\Api\Admin\PromotionController::class, 'batchAction']);
            Route::post('generate-materials', [App\Http\Controllers\Api\Admin\PromotionController::class, 'generateMaterials']);
            Route::post('{promotionLink}/duplicate', [App\Http\Controllers\Api\Admin\PromotionController::class, 'duplicate']);
            Route::post('{promotionLink}/regenerate-code', [App\Http\Controllers\Api\Admin\PromotionController::class, 'regenerateShortCode']);
            Route::get('expiring-links', [App\Http\Controllers\Api\Admin\PromotionController::class, 'getExpiringLinks']);
            Route::post('cleanup-expired', [App\Http\Controllers\Api\Admin\PromotionController::class, 'cleanupExpired']);
        });

        // 反屏蔽系统
        Route::prefix('anti-block')->group(function () {
            Route::get('stats', [AntiBlockController::class, 'getStats']);
            Route::get('domains', [AntiBlockController::class, 'index']);
            Route::post('domains', [AntiBlockController::class, 'store']);
            Route::get('domains/{id}', [AntiBlockController::class, 'show']);
            Route::put('domains/{id}', [AntiBlockController::class, 'update']);
            Route::delete('domains/{id}', [AntiBlockController::class, 'destroy']);
            Route::post('domains/batch-delete', [AntiBlockController::class, 'batchDeleteDomains']);
            Route::post('domains/batch-check', [AntiBlockController::class, 'batchCheckDomains']);
            Route::get('short-links', [AntiBlockController::class, 'getShortLinks']);
            Route::post('short-links', [AntiBlockController::class, 'createShortLink']);
            Route::delete('short-links/{id}', [AntiBlockController::class, 'deleteShortLink']);
            Route::get('access-stats', [AntiBlockController::class, 'getAccessStats']);
            Route::get('access-logs', [AntiBlockController::class, 'getAccessLogs']);
            Route::get('click-trends', [AntiBlockController::class, 'getClickTrends']);
            Route::get('region-stats', [AntiBlockController::class, 'getRegionStats']);
            Route::get('platform-stats', [AntiBlockController::class, 'getPlatformStats']);
            Route::post('qrcode', [AntiBlockController::class, 'generateQRCode']);
        });
        
        // 落地页管理
        Route::prefix('landing-pages')->group(function () {
            Route::get('/', [LandingPageController::class, 'index']);
            Route::post('/', [LandingPageController::class, 'store']);
            Route::get('/types', [LandingPageController::class, 'getPageTypes']);
            Route::get('/default-config', [LandingPageController::class, 'getDefaultConfig']);
            Route::get('/{landingPage}', [LandingPageController::class, 'show']);
            Route::put('/{landingPage}', [LandingPageController::class, 'update']);
            Route::delete('/{landingPage}', [LandingPageController::class, 'destroy']);
            Route::post('/{landingPage}/preview', [LandingPageController::class, 'preview']);
        });
        
        // 支付渠道管理
        Route::prefix('payment-channels')->group(function () {
            Route::get('/', [PaymentChannelController::class, 'index']);
            Route::post('/', [PaymentChannelController::class, 'store']);
            Route::get('{id}', [PaymentChannelController::class, 'show']);
            Route::put('{id}', [PaymentChannelController::class, 'update']);
            Route::delete('{id}', [PaymentChannelController::class, 'destroy']);
        });
        
        // 支付通道管理（系统管理员专用）
        Route::prefix('payment-channel-management')->group(function () {
            Route::get('/', [PaymentChannelManagementController::class, 'index']);
            Route::post('toggle-status', [PaymentChannelManagementController::class, 'toggleStatus']);
            Route::post('grant-permissions', [PaymentChannelManagementController::class, 'grantPermissions']);
            Route::get('statistics', [PaymentChannelManagementController::class, 'statistics']);
            Route::get('overview', [PaymentChannelManagementController::class, 'overview']);
            Route::get('{channelCode}/users', [PaymentChannelManagementController::class, 'getChannelUsers']);
            Route::put('channels/{id}', [PaymentChannelManagementController::class, 'updateChannel']);
        });
        
        // 支付配置管理
        Route::prefix('payment-configs')->group(function () {
            Route::get('/', [PaymentConfigController::class, 'index']);
            Route::post('/', [PaymentConfigController::class, 'store']);
            Route::get('{id}', [PaymentConfigController::class, 'show']);
            Route::put('{id}', [PaymentConfigController::class, 'update']);
            Route::delete('{id}', [PaymentConfigController::class, 'destroy']);
            Route::post('test', [PaymentConfigController::class, 'test']);
            Route::get('template/{channelCode}', [PaymentConfigController::class, 'template']);
            Route::get('available-channels', [PaymentConfigController::class, 'availableChannels']);
        });
        
        // 支付权限管理
        Route::prefix('payment-permissions')->group(function () {
            Route::get('/', [PaymentPermissionController::class, 'index']);
            Route::post('grant', [PaymentPermissionController::class, 'grant']);
            Route::post('revoke', [PaymentPermissionController::class, 'revoke']);
            Route::post('check', [PaymentPermissionController::class, 'check']);
            Route::get('expiring', [PaymentPermissionController::class, 'expiring']);
            Route::get('statistics', [PaymentPermissionController::class, 'statistics']);
        });
        
        // 支付使用日志
        Route::prefix('payment-logs')->group(function () {
            Route::get('/', [PaymentUsageLogController::class, 'index']);
            Route::get('{id}', [PaymentUsageLogController::class, 'show']);
            Route::get('statistics/payment', [PaymentUsageLogController::class, 'statistics']);
            Route::get('statistics/channel', [PaymentUsageLogController::class, 'channelStatistics']);
            Route::post('export', [PaymentUsageLogController::class, 'export']);
        });
        
        // 注意：dashboard路由已在上面定义，此处删除重复定义
        
        // 导出管理
        Route::prefix('exports')->group(function () {
            Route::get('/', [ExportController::class, 'adminIndex']);
            Route::delete('{id}', [ExportController::class, 'adminDestroy']);
            Route::post('cleanup', [ExportController::class, 'cleanup']);
        });
        
        // 系统设置
        Route::prefix('system')->group(function () {
            Route::get('settings', [SystemController::class, 'getSettings']);
            Route::post('settings', [SystemController::class, 'saveSettings']);
            Route::get('logs', [SystemController::class, 'getSystemLogs']);
            Route::get('info', [SystemController::class, 'getSystemInfo']);
            Route::get('statistics', [SystemController::class, 'getSystemStats']);
            Route::get('realtime', [SystemController::class, 'getRealtimeData']);
            Route::post('clear-cache', [SystemController::class, 'clearCache']);
            Route::post('restart-queue', [SystemController::class, 'restartQueue']);
            Route::post('logs/clean', [SystemController::class, 'cleanLogs']);
            Route::get('services', [SystemController::class, 'getServiceStatus']);
            Route::post('services/{name}/{action}', [SystemController::class, 'controlService']);
            Route::get('logs/export', [SystemController::class, 'exportLogs']);
            Route::get('health', [SystemController::class, 'getHealth']);
            Route::post('backup', [SystemController::class, 'backupDatabase']);
            Route::post('restore', [SystemController::class, 'restoreDatabase']);
        });
        
        // 系统监控
        Route::prefix('monitor')->group(function () {
            Route::get('health', [SystemMonitorController::class, 'getHealthStatus']);
            Route::get('performance', [SystemMonitorController::class, 'getPerformanceMetrics']);
            Route::get('alerts', [SystemMonitorController::class, 'getAlerts']);
            Route::get('suggestions', [SystemMonitorController::class, 'getOptimizationSuggestions']);
            Route::get('slow-queries', [SystemMonitorController::class, 'getSlowQueries']);
            Route::get('realtime', [SystemMonitorController::class, 'getRealTimeMetrics']);
            Route::get('events', [SystemMonitorController::class, 'getSystemEvents']);
            Route::get('configuration', [SystemMonitorController::class, 'getSystemConfiguration']);
            Route::post('check', [SystemMonitorController::class, 'runSystemCheck']);
        });
        
        // 统计数据
        Route::prefix('statistics')->group(function () {
            Route::get('overview', [StatisticsController::class, 'overview']);
            Route::get('users', [StatisticsController::class, 'userStats']);
            Route::get('groups', [StatisticsController::class, 'groupStats']);
            Route::get('orders', [StatisticsController::class, 'orderStats']);
            Route::get('revenue', [StatisticsController::class, 'revenueStats']);
            Route::get('distributors', [StatisticsController::class, 'distributorStats']);
            Route::get('export', [StatisticsController::class, 'exportStats']);
        });
        
        // 群组模板管理
        Route::prefix('group-templates')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\GroupTemplateController::class, 'index']);
            Route::post('/', [App\Http\Controllers\Api\GroupTemplateController::class, 'store']);
            Route::get('/categories', [App\Http\Controllers\Api\GroupTemplateController::class, 'categories']);
            Route::post('/upload-cover', [App\Http\Controllers\Api\GroupTemplateController::class, 'uploadCover']);
            Route::post('/create-group', [App\Http\Controllers\Api\GroupTemplateController::class, 'createGroupFromTemplate']);
            Route::get('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'show']);
            Route::put('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'update']);
            Route::delete('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'destroy']);
            Route::post('/export-group/{groupId}', [App\Http\Controllers\Api\GroupTemplateController::class, 'exportGroupAsTemplate']);
        });
        
        // 招募相关
        Route::prefix('recruit')->group(function() {
            Route::post('generate-invite-code', [DistributorController::class, 'generateInviteCode']);
            Route::get('invite-stats', [DistributorController::class, 'getInviteStats']);
            Route::get('invite-list', [DistributorController::class, 'getInviteList']);
            Route::get('promotion-materials', [DistributorController::class, 'getPromotionMaterials']);
            Route::post('set-target', [DistributorController::class, 'setRecruitTarget']);
        });
    });
});

// 分站管理员路由
Route::prefix('api/v1/substation')->middleware(['auth:sanctum', 'role:substation'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'substationDashboard']);
    Route::get('users', [UserManagementController::class, 'substationUsers']);
    Route::get('groups', [WechatGroupController::class, 'substationGroups']);
    Route::get('orders', [OrderController::class, 'substationOrders']);
    Route::get('distributors', [DistributorController::class, 'substationDistributors']);
    Route::get('statistics', [StatisticsController::class, 'substationStats']);
    Route::apiResource('substations', SubstationController::class);
    
    // 分站财务管理
    Route::prefix('finance')->group(function () {
        Route::get('stats', [SubstationFinanceController::class, 'getStats']);
        Route::post('report', [SubstationFinanceController::class, 'generateReport']);
        Route::get('settlements', [SubstationFinanceController::class, 'getSettlementRecords']);
        Route::post('batch-settle', [SubstationFinanceController::class, 'batchSettle']);
        Route::get('revenue-trend', [SubstationFinanceController::class, 'getRevenueTrend']);
        Route::get('commission-analysis', [SubstationFinanceController::class, 'getCommissionAnalysis']);
        Route::get('top-agents', [SubstationFinanceController::class, 'getTopAgents']);
        Route::post('export', [SubstationFinanceController::class, 'exportData']);
    });
    
    // 代理商申请管理
    Route::prefix('agent-applications')->group(function () {
        Route::get('/', [AgentApplicationController::class, 'index']);
        Route::get('stats', [AgentApplicationController::class, 'getStats']);
        Route::get('{id}', [AgentApplicationController::class, 'show']);
        Route::post('{id}/review', [AgentApplicationController::class, 'review']);
        Route::post('batch-review', [AgentApplicationController::class, 'batchReview']);
    });
});

// 高级群组管理路由
Route::middleware(['auth:sanctum'])->prefix('api/v1/groups/advanced')->group(function () {
    Route::get('{groupId}/health', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getGroupHealth']);
    Route::get('{groupId}/monitoring', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPerformanceMonitoring']);
    Route::post('{groupId}/optimize', [App\Http\Controllers\Api\AdvancedGroupController::class, 'autoOptimizeGroup']);
    Route::get('{groupId}/pricing-recommendation', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getSmartPricingRecommendation']);
    Route::get('recommendations', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPersonalizedRecommendations']);
    Route::get('search', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPersonalizedSearch']);
    Route::post('ab-tests', [App\Http\Controllers\Api\AdvancedGroupController::class, 'createABTest']);
    Route::get('ab-tests/{testId}/results', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getABTestResults']);
    Route::post('ab-tests/{testId}/stop', [App\Http\Controllers\Api\AdvancedGroupController::class, 'stopABTest']);
    Route::get('ab-tests/templates', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getABTestTemplates']);
    Route::get('templates', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getGroupTemplates']);
    Route::post('templates/create', [App\Http\Controllers\Api\AdvancedGroupController::class, 'createGroupFromTemplate']);
    Route::post('templates/batch-create', [App\Http\Controllers\Api\AdvancedGroupController::class, 'batchCreateGroups']);
    Route::post('{groupId}/export-template', [App\Http\Controllers\Api\AdvancedGroupController::class, 'exportGroupAsTemplate']);
    Route::get('templates/recommended', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getRecommendedTemplates']);
    Route::get('quick-create-configs', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getQuickCreateConfigs']);
    Route::get('dashboard', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getDashboard']);
    Route::get('analytics', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getAnalyticsReport']);
});

// V1 API 兼容路由
Route::prefix('api/v1')->group(function () {
    Route::get('/', function () {
        return response()->json([
            'success' => true,
            'message' => 'API v1 Ready',
            'version' => '1.0',
            'timestamp' => now()->toDateTimeString(),
        ]);
    });
    
    Route::get('test/simple', function () {
        return response()->json([
            'success' => true,
            'message' => 'API v1 测试成功',
            'data' => [
                'timestamp' => now()->toDateTimeString(),
                'path' => request()->path(),
                'full_url' => request()->fullUrl(),
            ],
        ]);
    });

    // 其他V1兼容路由可以在这里添加
});

// 测试路由
Route::get('api/v1/test/response', [TestController::class, 'testResponse']);

// 404 API 路由
Route::fallback(function () {
    return response()->json([
        'error' => 'API route not found',
        'message' => 'The requested API endpoint does not exist'
    ], 404);
});
// 分销员客户管理路由
Route::middleware(['auth:api', 'role:distributor'])->group(function () {
    Route::prefix('distributor/customers')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\DistributorCustomerController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\DistributorCustomerController::class, 'store']);
        Route::get('stats', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getStats']);
        Route::get('need-follow-up', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getNeedFollowUp']);
        Route::get('tags', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getTags']);
        Route::get('export', [App\Http\Controllers\Api\DistributorCustomerController::class, 'export']);
        Route::post('batch-status', [App\Http\Controllers\Api\DistributorCustomerController::class, 'batchUpdateStatus']);
        
        Route::get('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'show']);
        Route::put('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'update']);
        Route::delete('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'destroy']);
        
        // 跟进记录相关
        Route::post('{id}/follow-ups', [App\Http\Controllers\Api\DistributorCustomerController::class, 'addFollowUp']);
        Route::get('{id}/follow-ups', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getFollowUps']);
    });
});

// 增强分站权限管理路由
Route::middleware(['auth:api', 'substation.permission:user_management'])->group(function () {
    Route::prefix('substation/permissions')->group(function () {
        Route::get('config', [App\Http\Controllers\Api\SubstationPermissionController::class, 'getConfig']);
        Route::post('config', [App\Http\Controllers\Api\SubstationPermissionController::class, 'updateConfig']);
        Route::get('available', [App\Http\Controllers\Api\SubstationPermissionController::class, 'getAvailablePermissions']);
    });
});

// 代理商API (需要认证)
Route::middleware('auth:sanctum')->prefix('admin/agent')->group(function () {
    Route::get('my', [AgentController::class, 'getMy']);
    Route::get('my-stats', [AgentController::class, 'getMyStats']);
    Route::get('team', [AgentController::class, 'getTeamData']);
    Route::get('commission', [AgentController::class, 'getCommissionData']);
});

// 城市定位API
Route::prefix('location')->group(function () {
    Route::get('ip', [App\Http\Controllers\Api\LocationController::class, 'getLocationByIP']);
    Route::post('reverse', [App\Http\Controllers\Api\LocationController::class, 'reverseGeocode']);
    Route::get('cities', [App\Http\Controllers\Api\LocationController::class, 'getCities']);
    Route::get('recommend', [App\Http\Controllers\Api\LocationController::class, 'recommendCities']);
    Route::post('batch', [App\Http\Controllers\Api\LocationController::class, 'batchLocation']);
});

// 群组营销功能API
Route::prefix('groups/{id}')->middleware('auth:api')->group(function () {
    Route::get('marketing-config', [WechatGroupController::class, 'getMarketingConfig']);
    Route::put('marketing-config', [WechatGroupController::class, 'updateMarketingConfig']);
    Route::post('virtual-members', [WechatGroupController::class, 'generateVirtualMembers']);
    Route::get('preview', [WechatGroupController::class, 'previewGroup']);
    Route::post('test-city', [WechatGroupController::class, 'testCityLocation']);
    Route::post('apply-template', [WechatGroupController::class, 'applyMarketingTemplate']);
});

// 营销模板API
Route::get('marketing-templates', [WechatGroupController::class, 'getMarketingTemplates']);
Route::post('groups/batch-marketing', [WechatGroupController::class, 'batchUpdateMarketing'])->middleware('auth:api');

// 防封系统增强API
Route::prefix('anti-block')->group(function () {
    Route::get('domain-health', [AntiBlockController::class, 'getDomainHealth']);
    Route::post('check-domain', [AntiBlockController::class, 'checkDomainHealth']);
    Route::get('browser-stats', [AntiBlockController::class, 'getBrowserStats']);
    Route::post('validate-access/{groupId}', [AntiBlockController::class, 'validateGroupAccess']);
    Route::get('access-report/{groupId}', [AntiBlockController::class, 'getAccessReport']);
});

// 群组落地页和支付相关路由（公开访问）
Route::prefix('group')->group(function () {
    Route::get('{slug}', [\App\Http\Controllers\GroupLandingController::class, 'landing'])->name('group.landing');
    Route::post('create-order', [\App\Http\Controllers\GroupLandingController::class, 'createOrder']);
    Route::post('query-order-status', [\App\Http\Controllers\GroupLandingController::class, 'queryOrderStatus']);
    Route::get('success/{orderNo}', [\App\Http\Controllers\GroupLandingController::class, 'success'])->name('group.success');
    Route::post('{groupId}/generate-promotion-link', [\App\Http\Controllers\GroupLandingController::class, 'generatePromotionLink']);
});

// 群组创建相关路由
Route::prefix('admin')->middleware(['auth:api', 'admin'])->group(function () {
    // 群组管理
    Route::apiResource('groups', \App\Http\Controllers\Api\Admin\GroupController::class);
    Route::patch('groups/{id}/status', [\App\Http\Controllers\Api\Admin\GroupController::class, 'toggleStatus']);
    Route::get('groups/{id}/stats', [\App\Http\Controllers\Api\Admin\GroupController::class, 'stats']);
    Route::get('groups/{id}/members', [\App\Http\Controllers\Api\Admin\GroupController::class, 'members']);
    Route::delete('groups/{groupId}/members/{userId}', [\App\Http\Controllers\Api\Admin\GroupController::class, 'removeMember']);
    Route::get('groups/{id}/orders', [\App\Http\Controllers\Api\Admin\GroupController::class, 'orders']);
    Route::post('groups/batch-delete', [\App\Http\Controllers\Api\Admin\GroupController::class, 'batchDelete']);
    Route::post('groups/preview', [\App\Http\Controllers\Api\Admin\GroupController::class, 'preview']);
    Route::post('groups/{id}/poster', [\App\Http\Controllers\Api\Admin\GroupController::class, 'generatePoster']);
    Route::post('groups/{id}/duplicate', [\App\Http\Controllers\Api\Admin\GroupController::class, 'duplicate']);
    Route::get('groups/{id}/export', [\App\Http\Controllers\Api\Admin\GroupController::class, 'export']);
    Route::get('groups/{id}/analytics', [\App\Http\Controllers\Api\Admin\GroupController::class, 'analytics']);
    Route::post('groups/test-city-replacement', [\App\Http\Controllers\Api\Admin\GroupController::class, 'testCityReplacement']);
    Route::get('groups/recommended-settings', [\App\Http\Controllers\Api\Admin\GroupController::class, 'recommendedSettings']);
    
    // 超级群组创建（增强版）
    Route::prefix('ultra-groups')->group(function () {
        Route::post('create', [\App\Http\Controllers\Api\UltraGroupController::class, 'createSuperGroup']);
        Route::get('test-location', [\App\Http\Controllers\Api\UltraGroupController::class, 'testLocation']);
        Route::get('test-virtual-data', [\App\Http\Controllers\Api\UltraGroupController::class, 'testVirtualData']);
        Route::get('test-anti-block', [\App\Http\Controllers\Api\UltraGroupController::class, 'testAntiBlock']);
        Route::post('batch-create', [\App\Http\Controllers\Api\UltraGroupController::class, 'batchCreateGroups']);
        Route::get('system-status', [\App\Http\Controllers\Api\UltraGroupController::class, 'getSystemStatus']);
    });
    
    // 支付配置测试
    Route::post('payment/test', [\App\Http\Controllers\Api\Admin\PaymentTestController::class, 'test']);
    Route::get('payment/status', [\App\Http\Controllers\Api\Admin\PaymentTestController::class, 'status']);
    
    // 域名池管理
    Route::apiResource('domain-pools', \App\Http\Controllers\Api\Admin\DomainPoolController::class);
    
    // 群组模板管理
    Route::apiResource('group-templates', \App\Http\Controllers\Api\Admin\GroupTemplateController::class);
    Route::post('group-templates/{id}/apply', [\App\Http\Controllers\Api\Admin\GroupTemplateController::class, 'apply']);
});

// 文件上传路由
Route::middleware(['auth:api'])->group(function () {
    Route::post('upload', [\App\Http\Controllers\Api\UploadController::class, 'upload']);
    Route::post('upload/batch', [\App\Http\Controllers\Api\UploadController::class, 'batchUpload']);
    Route::delete('upload', [\App\Http\Controllers\Api\UploadController::class, 'delete']);
    Route::get('upload/info', [\App\Http\Controllers\Api\UploadController::class, 'info']);
});

// 公共API路由
Route::prefix('common')->group(function () {
    Route::get('cities', function () {
        return response()->json([
            'code' => 200,
            'message' => '获取城市列表成功',
            'data' => [
                ['code' => 'beijing', 'name' => '北京'],
                ['code' => 'shanghai', 'name' => '上海'],
                ['code' => 'guangzhou', 'name' => '广州'],
                ['code' => 'shenzhen', 'name' => '深圳'],
                ['code' => 'hangzhou', 'name' => '杭州'],
                ['code' => 'nanjing', 'name' => '南京'],
                ['code' => 'suzhou', 'name' => '苏州'],
                ['code' => 'wuhan', 'name' => '武汉'],
                ['code' => 'chengdu', 'name' => '成都'],
                ['code' => 'chongqing', 'name' => '重庆']
            ]
        ]);
    });
});