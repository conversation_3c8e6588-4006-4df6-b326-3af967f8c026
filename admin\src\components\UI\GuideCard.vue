<template>
  <el-card class="guide-card">
    <template #header>
      <div class="guide-header">
        <el-icon class="guide-icon"><QuestionFilled /></el-icon>
        <span class="guide-title">{{ title }}</span>
      </div>
    </template>
    
    <div class="guide-content">
      <p class="guide-description">{{ description }}</p>
      
      <div v-if="steps.length > 0" class="guide-steps">
        <h4>操作步骤：</h4>
        <ol>
          <li v-for="(step, index) in steps" :key="index">{{ step }}</li>
        </ol>
      </div>
      
      <div v-if="tips.length > 0" class="guide-tips">
        <h4>注意事项：</h4>
        <ul>
          <li v-for="(tip, index) in tips" :key="index">{{ tip }}</li>
        </ul>
      </div>
      
      <div v-if="examples.length > 0" class="guide-examples">
        <h4>配置示例：</h4>
        <div class="example-list">
          <div v-for="(example, index) in examples" :key="index" class="example-item">
            <span class="example-label">{{ example.label }}:</span>
            <span class="example-value">{{ example.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'

defineProps({
  title: {
    type: String,
    default: '使用说明'
  },
  description: {
    type: String,
    default: ''
  },
  steps: {
    type: Array,
    default: () => []
  },
  tips: {
    type: Array,
    default: () => []
  },
  examples: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang="scss" scoped>
.guide-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .guide-header {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .guide-icon {
      font-size: 18px;
      color: #3b82f6;
    }
    
    .guide-title {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }
  }
  
  .guide-content {
    .guide-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #6b7280;
      line-height: 1.5;
    }
    
    .guide-steps,
    .guide-tips,
    .guide-examples {
      margin-bottom: 16px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
      }
      
      ol, ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
          font-size: 13px;
          color: #6b7280;
          line-height: 1.4;
        }
      }
    }
    
    .guide-examples {
      .example-list {
        .example-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f8fafc;
          border-radius: 6px;
          margin-bottom: 8px;
          
          .example-label {
            font-size: 13px;
            color: #6b7280;
          }
          
          .example-value {
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            font-family: monospace;
          }
        }
      }
    }
  }
}
</style> 