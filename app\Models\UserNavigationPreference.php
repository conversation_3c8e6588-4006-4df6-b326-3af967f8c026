<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 用户导航偏好模型
 */
class UserNavigationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'navigation_key', 'visit_count', 'custom_sort',
        'is_favorite', 'is_hidden', 'custom_config', 'last_visited_at'
    ];

    protected $casts = [
        'is_favorite' => 'boolean',
        'is_hidden' => 'boolean',
        'visit_count' => 'integer',
        'custom_sort' => 'integer',
        'custom_config' => 'array',
        'last_visited_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联导航配置
     */
    public function navigationConfig(): BelongsTo
    {
        return $this->belongsTo(NavigationConfig::class, 'navigation_key', 'key');
    }

    /**
     * 记录访问
     */
    public function recordVisit()
    {
        $this->increment('visit_count');
        $this->update(['last_visited_at' => now()]);
        
        // 同时记录到统计表
        NavigationUsageStat::create([
            'navigation_key' => $this->navigation_key,
            'user_id' => $this->user_id,
            'user_role' => $this->user->role ?? 'guest',
            'action' => 'visit',
            'occurred_at' => now(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * 获取用户的热门导航
     */
    public static function getPopularForUser($userId, $limit = 10)
    {
        return self::where('user_id', $userId)
            ->where('visit_count', '>', 0)
            ->orderByDesc('visit_count')
            ->orderByDesc('last_visited_at')
            ->limit($limit)
            ->with('navigationConfig')
            ->get();
    }

    /**
     * 获取用户收藏的导航
     */
    public static function getFavoritesForUser($userId)
    {
        return self::where('user_id', $userId)
            ->where('is_favorite', true)
            ->where('is_hidden', false)
            ->orderBy('custom_sort', 'asc')
            ->orderBy('created_at', 'desc')
            ->with('navigationConfig')
            ->get();
    }
}