<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建模板使用记录表
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('template_usages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id')->comment('模板ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('instance_type')->comment('实例类型');
            $table->unsignedBigInteger('instance_id')->comment('实例ID');
            $table->json('customization_data')->nullable()->comment('自定义数据');
            $table->json('performance_metrics')->nullable()->comment('性能指标');
            $table->string('status')->default('pending')->comment('状态');
            $table->bigInteger('created_at_timestamp')->nullable()->comment('创建时间戳');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->decimal('rating', 3, 1)->nullable()->comment('评分');
            $table->text('feedback')->nullable()->comment('反馈');
            $table->timestamps();
            
            // 索引
            $table->index(['template_id', 'user_id']);
            $table->index(['instance_type', 'instance_id']);
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'created_at']);
            
            // 外键约束
            $table->foreign('template_id')->references('id')->on('templates')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('template_usages');
    }
};
