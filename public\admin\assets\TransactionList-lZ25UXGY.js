import{n as a,_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                       *//* empty css                  */import{r as l,L as t,$ as d,e as o,k as s,l as i,E as n,z as u,J as r,u as p,a3 as c,F as m,Y as f,D as _,A as v,y as b,t as y,G as w,B as g}from"./vue-vendor-Dy164gUc.js";import{k as h}from"./finance-DBah1Ldq.js";import{P as k}from"./index-ByaD-6N-.js";import{bp as j,bq as x,aM as V,b9 as U,b8 as C,by as L,at as P,aY as Y,a_ as D,aZ as q,bh as R,bi as F,a$ as M,U as T,o as Z,bw as $,bk as z,bl as A,ay as B}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";/* empty css                      */const E={class:"app-container"},I=e({__name:"TransactionList",setup(e){const I=l(!0),J=l(!1),K=l([]),O=l(0),G=l([]),S={recharge:"充值",withdraw:"提现",commission:"佣金",order_payment:"订单支付",refund:"退款",system_adjust:"系统调账"},H={pending:"处理中",completed:"已完成",failed:"已失败",cancelled:"已取消"},N=t({queryParams:{page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},dialog:{visible:!1,data:null}}),{queryParams:Q,dialog:W}=d(N),X=a=>a?parseFloat(a).toFixed(2):"0.00",aa=a=>({recharge:"success",withdraw:"warning",commission:"primary",order_payment:"info",refund:"danger",system_adjust:""}[a]||"info"),ea=a=>({pending:"warning",completed:"success",failed:"danger",cancelled:"info"}[a]||"info");async function la(){I.value=!0;try{G.value&&2===G.value.length?(Q.value.start_date=G.value[0],Q.value.end_date=G.value[1]):(Q.value.start_date=void 0,Q.value.end_date=void 0);const a=await h(Q.value);K.value=a.data.data,O.value=a.data.total}finally{I.value=!1}}function ta(){Q.value.page=1,la()}function da(){G.value=[],Q.value={page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},ta()}async function oa(){J.value=!0;try{await function(e,l={},t="export.xlsx"){return new Promise((d,o)=>{a({url:e,method:"get",params:l,responseType:"blob"}).then(a=>{const e=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l=window.URL.createObjectURL(e),o=document.createElement("a");o.href=l,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(l),d(a)}).catch(a=>{o(a)})})}("/finance/transactions/export",Q.value,`transactions_${Date.now()}.xlsx`)}finally{J.value=!1}}return o(()=>{la()}),(a,e)=>{const l=V,t=x,d=C,o=U,h=L,N=P,sa=j,ia=Y,na=D,ua=q,ra=F,pa=M,ca=R,ma=A,fa=z,_a=B,va=$;return i(),s("div",E,[n(ia,{class:"filter-card"},{default:u(()=>[n(sa,{inline:!0,model:p(Q),onSubmit:r(ta,["prevent"])},{default:u(()=>[n(t,{label:"关键字"},{default:u(()=>[n(l,{modelValue:p(Q).keyword,"onUpdate:modelValue":e[0]||(e[0]=a=>p(Q).keyword=a),placeholder:"订单号/用户昵称",clearable:"",onKeyup:c(ta,["enter"])},null,8,["modelValue"])]),_:1}),n(t,{label:"交易类型"},{default:u(()=>[n(o,{modelValue:p(Q).type,"onUpdate:modelValue":e[1]||(e[1]=a=>p(Q).type=a),placeholder:"全部类型",clearable:""},{default:u(()=>[(i(),s(m,null,f(S,(a,e)=>n(d,{key:e,label:a,value:e},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(t,{label:"交易状态"},{default:u(()=>[n(o,{modelValue:p(Q).status,"onUpdate:modelValue":e[2]||(e[2]=a=>p(Q).status=a),placeholder:"全部状态",clearable:""},{default:u(()=>[(i(),s(m,null,f(H,(a,e)=>n(d,{key:e,label:a,value:e},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(t,{label:"创建时间"},{default:u(()=>[n(h,{modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=a=>G.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(t,null,{default:u(()=>[n(N,{type:"primary",icon:"el-icon-search",onClick:ta},{default:u(()=>e[8]||(e[8]=[_("查询",-1)])),_:1,__:[8]}),n(N,{icon:"el-icon-refresh",onClick:da},{default:u(()=>e[9]||(e[9]=[_("重置",-1)])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1}),n(ua,{gutter:10,class:"mb8"},{default:u(()=>[n(na,{span:1.5},{default:u(()=>[n(N,{type:"success",plain:"",icon:"el-icon-download",onClick:oa,loading:J.value},{default:u(()=>e[10]||(e[10]=[_("导出",-1)])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1}),n(ia,null,{default:u(()=>[v((i(),b(ca,{data:K.value},{default:u(()=>[n(ra,{label:"交易号",prop:"id",width:"100"}),n(ra,{label:"用户",prop:"user.nickname",width:"150"}),n(ra,{label:"交易类型",align:"center",width:"120"},{default:u(a=>[n(pa,{type:aa(a.row.type)},{default:u(()=>[_(T(S[a.row.type]||"未知"),1)]),_:2},1032,["type"])]),_:1}),n(ra,{label:"金额",width:"150"},{default:u(a=>[y("span",{class:Z(a.row.amount>0?"text-green":"text-red")},T(a.row.amount>0?"+":"")+" ¥"+T(X(a.row.amount)),3)]),_:1}),n(ra,{label:"状态",align:"center",width:"100"},{default:u(a=>[n(pa,{type:ea(a.row.status)},{default:u(()=>[_(T(H[a.row.status]||"未知"),1)]),_:2},1032,["type"])]),_:1}),n(ra,{label:"描述",prop:"description","show-overflow-tooltip":""}),n(ra,{label:"创建时间",prop:"created_at",width:"160"}),n(ra,{label:"操作",width:"100",fixed:"right"},{default:u(a=>[n(N,{link:"",type:"primary",onClick:e=>{return l=a.row,W.value.data=l,void(W.value.visible=!0);var l}},{default:u(()=>e[11]||(e[11]=[_("详情",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[va,I.value]]),v(n(k,{total:O.value,page:p(Q).page,"onUpdate:page":e[4]||(e[4]=a=>p(Q).page=a),limit:p(Q).limit,"onUpdate:limit":e[5]||(e[5]=a=>p(Q).limit=a),onPagination:la},null,8,["total","page","limit"]),[[w,O.value>0]])]),_:1}),n(_a,{title:"交易详情",modelValue:p(W).visible,"onUpdate:modelValue":e[7]||(e[7]=a=>p(W).visible=a),width:"600px"},{footer:u(()=>[n(N,{onClick:e[6]||(e[6]=a=>p(W).visible=!1)},{default:u(()=>e[12]||(e[12]=[_("关闭",-1)])),_:1,__:[12]})]),default:u(()=>[p(W).data?(i(),b(fa,{key:0,column:2,border:""},{default:u(()=>[n(ma,{label:"交易号"},{default:u(()=>[_(T(p(W).data.id),1)]),_:1}),n(ma,{label:"用户"},{default:u(()=>[_(T(p(W).data.user?.nickname)+" (ID: "+T(p(W).data.user_id)+")",1)]),_:1}),n(ma,{label:"交易类型"},{default:u(()=>[n(pa,{type:aa(p(W).data.type)},{default:u(()=>[_(T(S[p(W).data.type]||"未知"),1)]),_:1},8,["type"])]),_:1}),n(ma,{label:"交易状态"},{default:u(()=>[n(pa,{type:ea(p(W).data.status)},{default:u(()=>[_(T(H[p(W).data.status]||"未知"),1)]),_:1},8,["type"])]),_:1}),n(ma,{label:"交易金额"},{default:u(()=>[y("span",{class:Z(p(W).data.amount>0?"text-green":"text-red")},T(p(W).data.amount>0?"+":"")+" ¥"+T(X(p(W).data.amount)),3)]),_:1}),n(ma,{label:"关联订单号"},{default:u(()=>[_(T(p(W).data.order_id||"无"),1)]),_:1}),n(ma,{label:"创建时间"},{default:u(()=>[_(T(p(W).data.created_at),1)]),_:1}),n(ma,{label:"更新时间"},{default:u(()=>[_(T(p(W).data.updated_at),1)]),_:1}),n(ma,{label:"描述",span:2},{default:u(()=>[_(T(p(W).data.description),1)]),_:1})]),_:1})):g("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4412ac9a"]]);export{I as default};
