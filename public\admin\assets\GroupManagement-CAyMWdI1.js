import{_ as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css               */import{T as e,aw as t,at as s,aR as l,a_ as i,ap as o,U as r,bd as n,an as c,bR as u,a6 as d,aT as p,aZ as m,bh as _,bi as v,a$ as f,bc as g,az as w,aB as h,aC as y,bw as b,bx as C,aM as k,aL as j,b9 as x,b8 as z,aY as V,ay as D,ad as T,Q as F,R as A}from"./element-plus-h2SQQM64.js";import{G as E}from"./GroupCreateForm-CUdmzNwa.js";import{r as q,L as B,e as U,k as I,l as P,t as $,E as R,z as S,D as G,u as L,A as Q,y as Z,B as M}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                          *//* empty css                        */import"./community-DNWNbya4.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";import"./RichTextEditor-Cp69n7mq.js";const J={class:"group-management"},K={class:"page-header"},N={class:"header-actions"},Y={class:"stat-card"},H={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},O={class:"stat-content"},W={class:"stat-value"},X={class:"stat-trend positive"},aa={class:"stat-card"},ea={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},ta={class:"stat-content"},sa={class:"stat-value"},la={class:"stat-trend positive"},ia={class:"stat-card"},oa={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},ra={class:"stat-content"},na={class:"stat-value"},ca={class:"stat-trend positive"},ua={class:"stat-card"},da={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},pa={class:"stat-content"},ma={class:"stat-value"},_a={class:"stat-trend negative"},va={class:"card-header"},fa={class:"header-actions"},ga={class:"group-info"},wa={class:"group-name"},ha={class:"group-desc"},ya={class:"pagination-wrapper"},ba={class:"dev-notice"},Ca=a({__name:"GroupManagement",setup(a){const Ca=q(!1),ka=q(!1),ja=q(!1),xa=q(""),za=q(""),Va=B({total_groups:12,total_members:1580,active_groups:8,conversion_rate:15.8}),Da=q([]),Ta=B({current_page:1,per_page:20,total:0}),Fa=B({type:"distribution",auto_city_replace:1,read_count_display:"5万+",like_count:666,want_see_count:888,button_title:"立即加入分销群",avatar_library:"qq"}),Aa=[{id:1,name:"高端客户VIP群",description:"专为高端客户提供的VIP服务群",platform:"wechat",status:"active",member_count:156,daily_messages:89,conversion_count:12,activity_rate:85,created_at:new Date(Date.now()-2592e6)},{id:2,name:"产品交流群",description:"用户产品使用交流和反馈",platform:"qq",status:"active",member_count:234,daily_messages:156,conversion_count:8,activity_rate:72,created_at:new Date(Date.now()-3888e6)},{id:3,name:"新手指导群",description:"新用户入门指导和答疑",platform:"wechat",status:"inactive",member_count:89,daily_messages:23,conversion_count:3,activity_rate:35,created_at:new Date(Date.now()-5184e6)}],Ea=async()=>{try{Ca.value=!0,await new Promise(a=>setTimeout(a,500));let a=[...Aa];xa.value&&(a=a.filter(a=>a.name.includes(xa.value)||a.description.includes(xa.value))),za.value&&(a=a.filter(a=>a.status===za.value)),Da.value=a,Ta.total=a.length}catch(a){F.error("加载群组列表失败")}finally{Ca.value=!1}},qa=()=>{Ea(),F.success("数据已刷新")},Ba=function(a,e){let t;return function(...s){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),a(...s)},e)}}(()=>{Ta.current_page=1,Ea()},500),Ua=async a=>{try{await A.confirm(`确定要暂停群组"${a.name}"吗？`,"确认暂停",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),a.status="paused",F.success("群组已暂停")}catch(e){"cancel"!==e&&F.error("暂停失败")}},Ia=async a=>{try{a.status="active",F.success("群组已恢复")}catch(e){F.error("恢复失败")}},Pa=async a=>{try{await A.confirm(`确定要删除群组"${a.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=Da.value.findIndex(e=>e.id===a.id);e>-1&&(Da.value.splice(e,1),Va.total_groups--),F.success("群组删除成功")}catch(e){"cancel"!==e&&F.error("删除失败")}},$a=a=>{const e={id:a.id||Date.now(),name:a.title,description:a.description,platform:"wechat",type:a.type,price:a.price,status:"active",member_count:0,daily_messages:0,conversion_count:0,activity_rate:0,created_at:new Date,enable_marketing:!0,commission_rate:5};Da.value.unshift(e),Va.total_groups++,ka.value=!1,F.success("群组创建成功！已启用分销功能。")},Ra=()=>{ka.value=!1},Sa=a=>({wechat:"微信",qq:"QQ",dingtalk:"钉钉",work_wechat:"企微"}[a]||"未知"),Ga=a=>({active:"活跃",inactive:"不活跃",paused:"已暂停"}[a]||"未知");return U(()=>{Ea()}),(a,A)=>{const q=e,B=s,U=i,Aa=m,La=k,Qa=z,Za=x,Ma=v,Ja=f,Ka=g,Na=y,Ya=h,Ha=w,Oa=_,Wa=C,Xa=V,ae=D,ee=b;return P(),I("div",J,[$("div",K,[A[10]||(A[10]=$("div",{class:"header-left"},[$("h2",null,"群组管理"),$("p",{class:"page-description"},"管理您负责的群组，监控群组活跃度，优化群组运营")],-1)),$("div",N,[R(B,{type:"primary",onClick:A[0]||(A[0]=a=>ka.value=!0)},{default:S(()=>[R(q,null,{default:S(()=>[R(L(t))]),_:1}),A[8]||(A[8]=G(" 创建群组 ",-1))]),_:1,__:[8]}),R(B,{onClick:qa},{default:S(()=>[R(q,null,{default:S(()=>[R(L(l))]),_:1}),A[9]||(A[9]=G(" 刷新数据 ",-1))]),_:1,__:[9]})])]),R(Aa,{gutter:20,class:"stats-row"},{default:S(()=>[R(U,{span:6},{default:S(()=>[$("div",Y,[$("div",H,[R(q,{size:24},{default:S(()=>[R(L(o))]),_:1})]),$("div",O,[$("div",W,r(Va.total_groups),1),A[12]||(A[12]=$("div",{class:"stat-title"},"管理群组",-1)),$("div",X,[R(q,null,{default:S(()=>[R(L(n))]),_:1}),A[11]||(A[11]=G(" 12.5% ",-1))])])])]),_:1}),R(U,{span:6},{default:S(()=>[$("div",aa,[$("div",ea,[R(q,{size:24},{default:S(()=>[R(L(c))]),_:1})]),$("div",ta,[$("div",sa,r(Va.total_members),1),A[14]||(A[14]=$("div",{class:"stat-title"},"群组成员",-1)),$("div",la,[R(q,null,{default:S(()=>[R(L(n))]),_:1}),A[13]||(A[13]=G(" 8.3% ",-1))])])])]),_:1}),R(U,{span:6},{default:S(()=>[$("div",ia,[$("div",oa,[R(q,{size:24},{default:S(()=>[R(L(u))]),_:1})]),$("div",ra,[$("div",na,r(Va.active_groups),1),A[16]||(A[16]=$("div",{class:"stat-title"},"活跃群组",-1)),$("div",ca,[R(q,null,{default:S(()=>[R(L(n))]),_:1}),A[15]||(A[15]=G(" 15.2% ",-1))])])])]),_:1}),R(U,{span:6},{default:S(()=>[$("div",ua,[$("div",da,[R(q,{size:24},{default:S(()=>[R(L(d))]),_:1})]),$("div",pa,[$("div",ma,r(Va.conversion_rate)+"%",1),A[18]||(A[18]=$("div",{class:"stat-title"},"转化率",-1)),$("div",_a,[R(q,null,{default:S(()=>[R(L(p))]),_:1}),A[17]||(A[17]=G(" 2.1% ",-1))])])])]),_:1})]),_:1}),R(Xa,null,{header:S(()=>[$("div",va,[A[19]||(A[19]=$("span",null,"群组列表",-1)),$("div",fa,[R(La,{modelValue:xa.value,"onUpdate:modelValue":A[1]||(A[1]=a=>xa.value=a),placeholder:"搜索群组名称",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:L(Ba)},{prefix:S(()=>[R(q,null,{default:S(()=>[R(L(j))]),_:1})]),_:1},8,["modelValue","onInput"]),R(Za,{modelValue:za.value,"onUpdate:modelValue":A[2]||(A[2]=a=>za.value=a),placeholder:"群组状态",style:{width:"120px"},onChange:Ea},{default:S(()=>[R(Qa,{label:"全部",value:""}),R(Qa,{label:"活跃",value:"active"}),R(Qa,{label:"不活跃",value:"inactive"}),R(Qa,{label:"已暂停",value:"paused"})]),_:1},8,["modelValue"])])])]),default:S(()=>[Q((P(),Z(Oa,{data:Da.value,stripe:""},{default:S(()=>[R(Ma,{prop:"name",label:"群组名称",width:"200"},{default:S(({row:a})=>[$("div",ga,[$("div",wa,r(a.name),1),$("div",ha,r(a.description),1)])]),_:1}),R(Ma,{prop:"platform",label:"平台",width:"100"},{default:S(({row:a})=>{return[R(Ja,{type:(e=a.platform,{wechat:"success",qq:"primary",dingtalk:"warning",work_wechat:"info"}[e]||"info"),size:"small"},{default:S(()=>[G(r(Sa(a.platform)),1)]),_:2},1032,["type"])];var e}),_:1}),R(Ma,{prop:"member_count",label:"成员数量",width:"100"}),R(Ma,{label:"状态",width:"100"},{default:S(({row:a})=>{return[R(Ja,{type:(e=a.status,{active:"success",inactive:"warning",paused:"danger"}[e]||"info"),size:"small"},{default:S(()=>[G(r(Ga(a.status)),1)]),_:2},1032,["type"])];var e}),_:1}),R(Ma,{prop:"daily_messages",label:"日消息量",width:"100"}),R(Ma,{prop:"conversion_count",label:"转化数",width:"100"}),R(Ma,{label:"活跃度",width:"120"},{default:S(({row:a})=>{return[R(Ka,{percentage:a.activity_rate,color:(e=a.activity_rate,e>=80?"#67C23A":e>=60?"#E6A23C":e>=40?"#F56C6C":"#909399"),"stroke-width":8},null,8,["percentage","color"])];var e}),_:1}),R(Ma,{label:"创建时间",width:"120"},{default:S(({row:a})=>{return[G(r((e=a.created_at,new Date(e).toLocaleDateString("zh-CN"))),1)];var e}),_:1}),R(Ma,{label:"操作",width:"200",fixed:"right"},{default:S(({row:a})=>[R(B,{size:"small",onClick:e=>{return t=a,F.info(`查看群组"${t.name}"的详细信息`),void(ja.value=!0);var t}},{default:S(()=>A[20]||(A[20]=[G(" 详情 ",-1)])),_:2,__:[20]},1032,["onClick"]),R(B,{size:"small",onClick:e=>{return t=a,F.info(`管理群组"${t.name}"`),void(ja.value=!0);var t}},{default:S(()=>A[21]||(A[21]=[G(" 管理 ",-1)])),_:2,__:[21]},1032,["onClick"]),R(Ha,{onCommand:e=>((a,e)=>{switch(a){case"edit":F.info(`编辑群组"${e.name}"`),ja.value=!0;break;case"pause":Ua(e);break;case"resume":Ia(e);break;case"delete":Pa(e)}})(e,a)},{dropdown:S(()=>[R(Ya,null,{default:S(()=>[R(Na,{command:"edit"},{default:S(()=>A[23]||(A[23]=[G("编辑",-1)])),_:1,__:[23]}),"active"===a.status?(P(),Z(Na,{key:0,command:"pause"},{default:S(()=>A[24]||(A[24]=[G("暂停",-1)])),_:1,__:[24]})):M("",!0),"paused"===a.status?(P(),Z(Na,{key:1,command:"resume"},{default:S(()=>A[25]||(A[25]=[G("恢复",-1)])),_:1,__:[25]})):M("",!0),R(Na,{command:"delete",divided:""},{default:S(()=>A[26]||(A[26]=[G("删除",-1)])),_:1,__:[26]})]),_:2},1024)]),default:S(()=>[R(B,{size:"small"},{default:S(()=>[A[22]||(A[22]=G(" 更多",-1)),R(q,{class:"el-icon--right"},{default:S(()=>[R(L(p))]),_:1})]),_:1,__:[22]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[ee,Ca.value]]),$("div",ya,[R(Wa,{"current-page":Ta.current_page,"onUpdate:currentPage":A[3]||(A[3]=a=>Ta.current_page=a),"page-size":Ta.per_page,"onUpdate:pageSize":A[4]||(A[4]=a=>Ta.per_page=a),total:Ta.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ea,onCurrentChange:Ea},null,8,["current-page","page-size","total"])])]),_:1}),R(ae,{modelValue:ka.value,"onUpdate:modelValue":A[5]||(A[5]=a=>ka.value=a),title:"创建群组",width:"900px","close-on-click-modal":!1,class:"unified-create-dialog"},{default:S(()=>[R(E,{mode:"dialog","user-role":"distributor","default-values":Fa,"show-preview":!0,"show-templates":!1,onSuccess:$a,onCancel:Ra},null,8,["default-values"])]),_:1},8,["modelValue"]),R(ae,{modelValue:ja.value,"onUpdate:modelValue":A[7]||(A[7]=a=>ja.value=a),title:"功能开发中",width:"400px",center:""},{footer:S(()=>[R(B,{type:"primary",onClick:A[6]||(A[6]=a=>ja.value=!1)},{default:S(()=>A[30]||(A[30]=[G("知道了",-1)])),_:1,__:[30]})]),default:S(()=>[$("div",ba,[R(q,{size:60,color:"#409EFF"},{default:S(()=>[R(L(T))]),_:1}),A[27]||(A[27]=$("h3",null,"功能开发中",-1)),A[28]||(A[28]=$("p",null,"该功能正在紧急开发中，敬请期待！",-1)),A[29]||(A[29]=$("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-57941b79"]]);export{Ca as default};
