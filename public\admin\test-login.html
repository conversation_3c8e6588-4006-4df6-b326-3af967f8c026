<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 登录页面诊断</h1>
        
        <div class="status success">
            <h3>✅ 开发服务器状态</h3>
            <p>Vite 开发服务器正在运行</p>
            <p>端口: 3001</p>
        </div>
        
        <div class="status" id="vue-status">
            <h3>🔍 Vue 应用状态</h3>
            <p id="vue-message">正在检测...</p>
        </div>
        
        <div class="status" id="route-status">
            <h3>🛣️ 路由状态</h3>
            <p id="route-message">正在检测...</p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/admin/" class="btn">🚀 访问登录页面</a>
            <a href="/admin/#/login" class="btn">🔗 直接访问登录路由</a>
        </div>
        
        <div style="margin-top: 20px; font-size: 14px; color: #666;">
            <p>如果登录页面无法加载，可能的原因：</p>
            <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                <li>Vue 组件语法错误</li>
                <li>CSS 样式冲突</li>
                <li>路由配置问题</li>
                <li>依赖包缺失</li>
                <li>浏览器缓存问题</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 检测 Vue 应用状态
        function checkVueApp() {
            const vueStatus = document.getElementById('vue-status');
            const vueMessage = document.getElementById('vue-message');
            
            try {
                // 尝试访问主应用
                fetch('/admin/')
                    .then(response => {
                        if (response.ok) {
                            vueStatus.className = 'status success';
                            vueMessage.textContent = '✅ Vue 应用响应正常';
                        } else {
                            vueStatus.className = 'status error';
                            vueMessage.textContent = `❌ Vue 应用响应异常 (${response.status})`;
                        }
                    })
                    .catch(error => {
                        vueStatus.className = 'status error';
                        vueMessage.textContent = `❌ Vue 应用无法访问: ${error.message}`;
                    });
            } catch (error) {
                vueStatus.className = 'status error';
                vueMessage.textContent = `❌ 检测失败: ${error.message}`;
            }
        }
        
        // 检测路由状态
        function checkRoutes() {
            const routeStatus = document.getElementById('route-status');
            const routeMessage = document.getElementById('route-message');
            
            // 检查是否是 hash 路由模式
            const isHashMode = window.location.hash.length > 0;
            
            if (isHashMode) {
                routeStatus.className = 'status success';
                routeMessage.textContent = '✅ 检测到 Hash 路由模式';
            } else {
                routeStatus.className = 'status';
                routeMessage.textContent = '🔍 使用 History 路由模式';
            }
        }
        
        // 页面加载完成后执行检测
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkVueApp();
                checkRoutes();
            }, 1000);
        });
        
        // 每5秒刷新一次状态
        setInterval(() => {
            checkVueApp();
        }, 5000);
    </script>
</body>
</html>
