<template>
  <div class="group-owner-dashboard">
    <div class="page-header">
      <h2>我的群组管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <i class="el-icon-plus"></i> 创建群组
        </el-button>
        <el-button @click="showTemplateDialog = true">
          <i class="el-icon-document"></i> 使用模板
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-s-grid"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalGroups }}</div>
              <div class="stat-label">总群组数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-card">
            <div class="stat-icon success">
              <i class="el-icon-shopping-cart-2"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-card">
            <div class="stat-icon warning">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">￥{{ totalIncome }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-card">
            <div class="stat-icon info">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ todayOrders }}</div>
              <div class="stat-label">今日订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="群组名称">
          <el-input 
            v-model="searchForm.search" 
            placeholder="搜索群组名称"
            @keyup.enter="loadGroups"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态">
            <el-option label="全部" value=""></el-option>
            <el-option label="正常" value="active"></el-option>
            <el-option label="禁用" value="inactive"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板">
          <el-select v-model="searchForm.template_id" placeholder="选择模板">
            <el-option label="全部" value=""></el-option>
            <el-option 
              v-for="template in templates" 
              :key="template.id" 
              :label="template.name" 
              :value="template.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadGroups">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 群组列表 -->
    <el-card>
      <el-table 
        :data="groups" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="群组信息" min-width="200">
          <template #default="scope">
            <div class="group-info">
              <el-avatar 
                :size="50" 
                :src="scope.row.cover_image || '/default-group.png'"
                shape="square"
              />
              <div class="group-details">
                <div class="group-title">{{ scope.row.title }}</div>
                <div class="group-subtitle">{{ scope.row.subtitle }}</div>
                <div class="group-price">￥{{ scope.row.price }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="统计数据" width="150">
          <template #default="scope">
            <div class="stats-info">
              <div>总订单: {{ scope.row.stats?.total_orders || 0 }}</div>
              <div>总收入: ￥{{ scope.row.stats?.total_income || 0 }}</div>
              <div>今日订单: {{ scope.row.stats?.today_orders || 0 }}</div>
              <div>转化率: {{ scope.row.stats?.conversion_rate || 0 }}%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="template.name" label="使用模板" width="120"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="mini" @click="viewGroup(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" @click="editGroup(scope.row)">编辑</el-button>
            <el-button size="mini" type="info" @click="duplicateGroup(scope.row)">复制</el-button>
            <el-dropdown trigger="click">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="manageContent(scope.row)">
                  内容管理
                </el-dropdown-item>
                <el-dropdown-item @click.native="viewStats(scope.row)">
                  数据统计
                </el-dropdown-item>
                <el-dropdown-item @click.native="toggleStatus(scope.row)">
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item @click.native="deleteGroup(scope.row)" class="danger">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedGroups.length > 0">
        <span>已选择 {{ selectedGroups.length }} 项</span>
        <el-button size="small" @click="batchOperation('activate')">批量启用</el-button>
        <el-button size="small" @click="batchOperation('deactivate')">批量禁用</el-button>
        <el-button size="small" type="danger" @click="batchOperation('delete')">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total > 0"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current_page"
        :page-sizes="[15, 30, 50]"
        :page-size="pagination.per_page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px;"
      />
    </el-card>

    <!-- 创建群组对话框 -->
    <GroupCreateDialog 
      v-if="showCreateDialog"
      :visible="showCreateDialog"
      :templates="templates"
      @close="showCreateDialog = false"
      @success="handleCreateSuccess"
    />

    <!-- 模板选择对话框 -->
    <TemplateSelectDialog
      v-if="showTemplateDialog"
      :visible="showTemplateDialog"
      :templates="templates"
      @close="showTemplateDialog = false"
      @select="handleTemplateSelect"
    />

    <!-- 群组编辑对话框 -->
    <GroupEditDialog
      v-if="showEditDialog"
      :visible="showEditDialog"
      :group="currentGroup"
      @close="showEditDialog = false"
      @success="handleEditSuccess"
    />

    <!-- 内容管理对话框 -->
    <ContentManageDialog
      v-if="showContentDialog"
      :visible="showContentDialog"
      :group="currentGroup"
      @close="showContentDialog = false"
      @success="handleContentSuccess"
    />
  </div>
</template>

<script>
import GroupCreateDialog from './components/GroupCreateDialog.vue'
import TemplateSelectDialog from './components/TemplateSelectDialog.vue'
import GroupEditDialog from './components/GroupEditDialog.vue'
import ContentManageDialog from './components/ContentManageDialog.vue'

export default {
  name: 'GroupOwnerDashboard',
  components: {
    GroupCreateDialog,
    TemplateSelectDialog,
    GroupEditDialog,
    ContentManageDialog
  },
  data() {
    return {
      groups: [],
      templates: [],
      loading: false,
      searchForm: {
        search: '',
        status: '',
        template_id: ''
      },
      pagination: {
        current_page: 1,
        per_page: 15,
        total: 0
      },
      selectedGroups: [],
      
      // 统计数据
      totalGroups: 0,
      totalOrders: 0,
      totalIncome: 0,
      todayOrders: 0,
      
      // 对话框显示状态
      showCreateDialog: false,
      showTemplateDialog: false,
      showEditDialog: false,
      showContentDialog: false,
      
      currentGroup: null
    }
  },
  mounted() {
    this.loadGroups()
    this.loadTemplates()
    this.loadStats()
  },
  methods: {
    async loadGroups() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current_page,
          per_page: this.pagination.per_page,
          ...this.searchForm
        }
        const response = await this.$api.get('/owner/groups', { params })
        this.groups = response.data.data
        this.pagination = {
          current_page: response.data.current_page,
          per_page: response.data.per_page,
          total: response.data.total
        }
      } catch (error) {
        this.$message.error('加载群组列表失败')
      } finally {
        this.loading = false
      }
    },

    async loadTemplates() {
      try {
        const response = await this.$api.get('/owner/templates')
        this.templates = response.data
      } catch (error) {
        console.error('加载模板失败:', error)
      }
    },

    async loadStats() {
      try {
        // 计算统计数据
        const response = await this.$api.get('/owner/groups?per_page=999')
        const allGroups = response.data.data
        
        this.totalGroups = allGroups.length
        this.totalOrders = allGroups.reduce((sum, group) => sum + (group.stats?.total_orders || 0), 0)
        this.totalIncome = allGroups.reduce((sum, group) => sum + (group.stats?.total_income || 0), 0)
        this.todayOrders = allGroups.reduce((sum, group) => sum + (group.stats?.today_orders || 0), 0)
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    resetSearch() {
      this.searchForm = {
        search: '',
        status: '',
        template_id: ''
      }
      this.pagination.current_page = 1
      this.loadGroups()
    },

    handleSelectionChange(selection) {
      this.selectedGroups = selection
    },

    handleSizeChange(size) {
      this.pagination.per_page = size
      this.pagination.current_page = 1
      this.loadGroups()
    },

    handleCurrentChange(page) {
      this.pagination.current_page = page
      this.loadGroups()
    },

    viewGroup(group) {
      this.$router.push(`/owner/groups/${group.id}`)
    },

    editGroup(group) {
      this.currentGroup = group
      this.showEditDialog = true
    },

    async duplicateGroup(group) {
      try {
        await this.$api.post(`/owner/groups/${group.id}/duplicate`)
        this.$message.success('群组复制成功')
        this.loadGroups()
      } catch (error) {
        this.$message.error('群组复制失败')
      }
    },

    manageContent(group) {
      this.currentGroup = group
      this.showContentDialog = true
    },

    viewStats(group) {
      this.$router.push(`/owner/groups/${group.id}/stats`)
    },

    async toggleStatus(group) {
      try {
        const newStatus = group.status === 'active' ? 'inactive' : 'active'
        await this.$api.put(`/owner/groups/${group.id}`, { status: newStatus })
        this.$message.success('状态更新成功')
        this.loadGroups()
      } catch (error) {
        this.$message.error('状态更新失败')
      }
    },

    deleteGroup(group) {
      this.$confirm('确定要删除这个群组吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.$api.delete(`/owner/groups/${group.id}`)
          this.$message.success('删除成功')
          this.loadGroups()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },

    async batchOperation(action) {
      const groupIds = this.selectedGroups.map(group => group.id)
      try {
        await this.$api.post('/owner/groups/batch', {
          action,
          group_ids: groupIds
        })
        this.$message.success('批量操作成功')
        this.loadGroups()
        this.selectedGroups = []
      } catch (error) {
        this.$message.error('批量操作失败')
      }
    },

    handleCreateSuccess() {
      this.showCreateDialog = false
      this.loadGroups()
      this.loadStats()
    },

    handleTemplateSelect(template) {
      this.showTemplateDialog = false
      this.$router.push(`/owner/groups/create?template=${template.id}`)
    },

    handleEditSuccess() {
      this.showEditDialog = false
      this.currentGroup = null
      this.loadGroups()
    },

    handleContentSuccess() {
      this.showContentDialog = false
      this.currentGroup = null
      this.loadGroups()
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString()
    }
  }
}
</script>

<style scoped>
.group-owner-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.page-header h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions .el-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.stats-cards {
  margin-bottom: 25px;
}

.stats-cards .el-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.stats-cards .el-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  margin-right: 20px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.stat-icon.success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c 0%, #f7ba2a 100%);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.stat-icon.info {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.filter-card {
  margin-bottom: 25px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.filter-card ::v-deep .el-card__body {
  padding: 20px;
}

.filter-card .el-form-item {
  margin-bottom: 0;
}

.filter-card .el-input,
.filter-card .el-select {
  border-radius: 8px;
}

.filter-card .el-button {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
}

.el-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.el-table ::v-deep .el-table__header {
  background: #fafafa;
}

.el-table ::v-deep .el-table__header th {
  background: #fafafa;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.el-table ::v-deep .el-table__body tr:hover {
  background: #f8f9fa;
}

.group-info {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.group-details {
  margin-left: 16px;
}

.group-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  margin-bottom: 4px;
}

.group-subtitle {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.group-price {
  font-size: 14px;
  color: #e6a23c;
  font-weight: 600;
}

.stats-info {
  font-size: 12px;
  line-height: 1.6;
  color: #606266;
}

.stats-info div {
  margin-bottom: 2px;
}

.el-avatar {
  border-radius: 8px;
}

.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

.el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--mini {
  padding: 5px 12px;
  font-size: 12px;
}

.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.batch-actions {
  margin-top: 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #e4e7ed;
}

.batch-actions span {
  color: #606266;
  font-weight: 500;
}

.batch-actions .el-button {
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 13px;
}

.danger {
  color: #f56c6c;
}

.el-pagination {
  text-align: center;
  margin-top: 20px;
}

.el-pagination ::v-deep .el-pager li {
  border-radius: 6px;
  margin: 0 2px;
}

.el-pagination ::v-deep .btn-prev,
.el-pagination ::v-deep .btn-next {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-owner-dashboard {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-cards .el-col {
    margin-bottom: 15px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .filter-card .el-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-card .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .batch-actions .el-button {
    width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-cards .el-card {
  animation: fadeInUp 0.6s ease-out;
}

.stats-cards .el-col:nth-child(1) .el-card {
  animation-delay: 0.1s;
}

.stats-cards .el-col:nth-child(2) .el-card {
  animation-delay: 0.2s;
}

.stats-cards .el-col:nth-child(3) .el-card {
  animation-delay: 0.3s;
}

.stats-cards .el-col:nth-child(4) .el-card {
  animation-delay: 0.4s;
}

.filter-card {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.el-card:last-child {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

/* 加载状态 */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>