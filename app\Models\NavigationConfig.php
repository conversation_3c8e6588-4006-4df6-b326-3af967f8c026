<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

/**
 * 导航配置模型
 */
class NavigationConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'key', 'domain', 'name', 'icon', 'route', 'url', 'meta',
        'sort_order', 'is_active', 'is_system', 'parent_key', 'permissions'
    ];

    protected $casts = [
        'meta' => 'array',
        'permissions' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'sort_order' => 'integer',
    ];

    // 缓存键前缀
    const CACHE_PREFIX = 'navigation_config:';
    const CACHE_TTL = 3600; // 1小时

    /**
     * 获取子导航
     */
    public function children(): HasMany
    {
        return $this->hasMany(NavigationConfig::class, 'parent_key', 'key')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    /**
     * 获取父导航
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(NavigationConfig::class, 'parent_key', 'key');
    }

    /**
     * 获取使用统计
     */
    public function usageStats(): HasMany
    {
        return $this->hasMany(NavigationUsageStat::class, 'navigation_key', 'key');
    }

    /**
     * 获取用户偏好
     */
    public function userPreferences(): HasMany
    {
        return $this->hasMany(UserNavigationPreference::class, 'navigation_key', 'key');
    }

    /**
     * 查询作用域：按域过滤
     */
    public function scopeByDomain($query, $domain)
    {
        return $query->where('domain', $domain);
    }

    /**
     * 查询作用域：活跃的导航
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 查询作用域：顶级导航
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_key');
    }

    /**
     * 获取带缓存的导航树
     */
    public static function getCachedTree($domain = null, $userId = null)
    {
        $cacheKey = self::CACHE_PREFIX . 'tree:' . ($domain ?? 'all') . ':' . ($userId ?? 'guest');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($domain, $userId) {
            $query = self::with(['children' => function ($query) {
                $query->orderBy('sort_order');
            }])
            ->active()
            ->topLevel()
            ->orderBy('sort_order');
            
            if ($domain) {
                $query->byDomain($domain);
            }
            
            $navigation = $query->get();
            
            // 如果有用户ID，加载用户偏好
            if ($userId) {
                $navigation = self::attachUserPreferences($navigation, $userId);
            }
            
            return $navigation;
        });
    }

    /**
     * 附加用户偏好数据
     */
    public static function attachUserPreferences($navigation, $userId)
    {
        $preferences = UserNavigationPreference::where('user_id', $userId)
            ->get()
            ->keyBy('navigation_key');
        
        return $navigation->map(function ($item) use ($preferences) {
            $pref = $preferences->get($item->key);
            $item->user_preference = $pref ? [
                'visit_count' => $pref->visit_count,
                'is_favorite' => $pref->is_favorite,
                'is_hidden' => $pref->is_hidden,
                'custom_sort' => $pref->custom_sort,
                'last_visited_at' => $pref->last_visited_at,
            ] : null;
            
            // 递归处理子项
            if ($item->children) {
                $item->children = self::attachUserPreferences($item->children, $userId);
            }
            
            return $item;
        });
    }

    /**
     * 清除导航缓存
     */
    public static function clearCache($domain = null)
    {
        $pattern = self::CACHE_PREFIX . 'tree:' . ($domain ?? '*') . ':*';
        Cache::forget($pattern);
    }

    /**
     * 检查用户是否有权限访问
     */
    public function hasPermission($user)
    {
        if (!$this->permissions || empty($this->permissions)) {
            return true;
        }

        foreach ($this->permissions as $permission) {
            if ($user->hasPermissionTo($permission)) {
                return true;
            }
        }

        return false;
    }
}