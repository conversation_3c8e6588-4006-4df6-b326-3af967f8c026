function t(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const r=new Date(t);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),i=String(r.getDate()).padStart(2,"0"),s=String(r.getHours()).padStart(2,"0"),c=String(r.getMinutes()).padStart(2,"0"),g=String(r.getSeconds()).padStart(2,"0");return e.replace("YYYY",a).replace("MM",n).replace("DD",i).replace("HH",s).replace("mm",c).replace("ss",g)}function e(t,e=0){return null==t||isNaN(t)?"0":Number(t).toLocaleString("zh-CN",{minimumFractionDigits:e,maximumFractionDigits:e})}function r(t,e="HH:mm:ss"){if(!t)return"";const r=new Date(t);if(isNaN(r.getTime()))return"";const a=String(r.getHours()).padStart(2,"0"),n=String(r.getMinutes()).padStart(2,"0"),i=String(r.getSeconds()).padStart(2,"0");return e.replace("HH",a).replace("mm",n).replace("ss",i)}function a(e,r="YYYY-MM-DD HH:mm:ss"){return t(e,r)}export{e as a,r as b,a as c,t as f};
