import { defineStore } from 'pinia'
import { promotionApi } from '@/api/promotion'

/**
 * 推广管理状态存储
 */
export const usePromotionStore = defineStore('promotion', {
  state: () => ({
    // 推广链接列表
    promotionLinks: [],
    
    // 当前推广链接
    currentLink: null,
    
    // 统计数据
    stats: {
      overview: {
        total_links: 0,
        active_links: 0,
        expired_links: 0,
        disabled_links: 0,
        total_clicks: 0,
        avg_clicks_per_link: 0
      },
      popular_links: [],
      recent_links: []
    },
    
    // 加载状态
    loading: {
      list: false,
      stats: false,
      create: false,
      update: false,
      delete: false
    },
    
    // 分页信息
    pagination: {
      current_page: 1,
      last_page: 1,
      per_page: 20,
      total: 0
    },
    
    // 筛选条件
    filters: {
      search: '',
      group_id: '',
      status: '',
      created_by: '',
      sort_by: 'created_at',
      sort_order: 'desc'
    }
  }),

  getters: {
    // 获取活跃链接数量
    activeLinksCount: (state) => state.stats.overview.active_links,
    
    // 获取过期链接数量
    expiredLinksCount: (state) => state.stats.overview.expired_links,
    
    // 获取总点击量
    totalClicks: (state) => state.stats.overview.total_clicks,
    
    // 获取平均点击率
    avgClickRate: (state) => state.stats.overview.avg_clicks_per_link,
    
    // 获取热门链接
    popularLinks: (state) => state.stats.popular_links,
    
    // 获取最近创建的链接
    recentLinks: (state) => state.stats.recent_links,
    
    // 检查是否有数据
    hasData: (state) => state.promotionLinks.length > 0,
    
    // 检查是否正在加载
    isLoading: (state) => Object.values(state.loading).some(loading => loading)
  },

  actions: {
    /**
     * 获取推广链接列表
     */
    async getPromotionLinks(params = {}) {
      this.loading.list = true
      try {
        const response = await promotionApi.getList({
          ...this.filters,
          ...params
        })
        
        this.promotionLinks = response.data.links || []
        this.pagination = response.data.pagination || this.pagination
        
        return response.data
      } catch (error) {
        console.error('获取推广链接列表失败:', error)
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 获取推广链接详情
     */
    async getPromotionLink(id) {
      try {
        const response = await promotionApi.getDetail(id)
        this.currentLink = response.data.link
        return response.data
      } catch (error) {
        console.error('获取推广链接详情失败:', error)
        throw error
      }
    },

    /**
     * 创建推广链接
     */
    async createPromotionLink(data) {
      this.loading.create = true
      try {
        const response = await promotionApi.create(data)
        
        // 添加到列表开头
        this.promotionLinks.unshift(response.data.link)
        
        return response.data
      } catch (error) {
        console.error('创建推广链接失败:', error)
        throw error
      } finally {
        this.loading.create = false
      }
    },

    /**
     * 更新推广链接
     */
    async updatePromotionLink(id, data) {
      this.loading.update = true
      try {
        const response = await promotionApi.update(id, data)
        
        // 更新列表中的数据
        const index = this.promotionLinks.findIndex(link => link.id === id)
        if (index !== -1) {
          this.promotionLinks[index] = response.data
        }
        
        // 更新当前链接
        if (this.currentLink && this.currentLink.id === id) {
          this.currentLink = response.data
        }
        
        return response.data
      } catch (error) {
        console.error('更新推广链接失败:', error)
        throw error
      } finally {
        this.loading.update = false
      }
    },

    /**
     * 删除推广链接
     */
    async deletePromotionLink(id) {
      this.loading.delete = true
      try {
        await promotionApi.delete(id)
        
        // 从列表中移除
        this.promotionLinks = this.promotionLinks.filter(link => link.id !== id)
        
        // 清除当前链接
        if (this.currentLink && this.currentLink.id === id) {
          this.currentLink = null
        }
        
        return true
      } catch (error) {
        console.error('删除推广链接失败:', error)
        throw error
      } finally {
        this.loading.delete = false
      }
    },

    /**
     * 批量操作推广链接
     */
    async batchAction(data) {
      try {
        const response = await promotionApi.batchAction(data)
        
        // 重新加载列表
        await this.getPromotionLinks()
        
        return response.data
      } catch (error) {
        console.error('批量操作失败:', error)
        throw error
      }
    },

    /**
     * 复制推广链接
     */
    async duplicatePromotionLink(id, overrides = {}) {
      try {
        const response = await promotionApi.duplicate(id, overrides)
        
        // 添加到列表开头
        this.promotionLinks.unshift(response.data)
        
        return response.data
      } catch (error) {
        console.error('复制推广链接失败:', error)
        throw error
      }
    },

    /**
     * 重新生成短码
     */
    async regenerateShortCode(id) {
      try {
        const response = await promotionApi.regenerateShortCode(id)
        
        // 更新列表中的数据
        const index = this.promotionLinks.findIndex(link => link.id === id)
        if (index !== -1) {
          this.promotionLinks[index] = response.data
        }
        
        return response.data
      } catch (error) {
        console.error('重新生成短码失败:', error)
        throw error
      }
    },

    /**
     * 生成推广素材
     */
    async generatePromotionMaterials(groupId, options = {}) {
      try {
        const response = await promotionApi.generateMaterials({
          group_id: groupId,
          ...options
        })
        
        return response.data
      } catch (error) {
        console.error('生成推广素材失败:', error)
        throw error
      }
    },

    /**
     * 获取推广统计数据
     */
    async getPromotionStats(params = {}) {
      this.loading.stats = true
      try {
        const response = await promotionApi.getStats(params)
        this.stats = response.data
        return response.data
      } catch (error) {
        console.error('获取推广统计失败:', error)
        throw error
      } finally {
        this.loading.stats = false
      }
    },

    /**
     * 获取即将过期的链接
     */
    async getExpiringLinks(days = 7) {
      try {
        const response = await promotionApi.getExpiringLinks(days)
        return response.data
      } catch (error) {
        console.error('获取即将过期链接失败:', error)
        throw error
      }
    },

    /**
     * 清理过期链接
     */
    async cleanupExpiredLinks() {
      try {
        const response = await promotionApi.cleanupExpired()
        
        // 重新加载列表
        await this.getPromotionLinks()
        
        return response.data
      } catch (error) {
        console.error('清理过期链接失败:', error)
        throw error
      }
    },

    /**
     * 设置筛选条件
     */
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    /**
     * 重置筛选条件
     */
    resetFilters() {
      this.filters = {
        search: '',
        group_id: '',
        status: '',
        created_by: '',
        sort_by: 'created_at',
        sort_order: 'desc'
      }
    },

    /**
     * 设置当前链接
     */
    setCurrentLink(link) {
      this.currentLink = link
    },

    /**
     * 清除当前链接
     */
    clearCurrentLink() {
      this.currentLink = null
    },

    /**
     * 更新分页信息
     */
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    /**
     * 重置状态
     */
    resetState() {
      this.promotionLinks = []
      this.currentLink = null
      this.stats = {
        overview: {
          total_links: 0,
          active_links: 0,
          expired_links: 0,
          disabled_links: 0,
          total_clicks: 0,
          avg_clicks_per_link: 0
        },
        popular_links: [],
        recent_links: []
      }
      this.pagination = {
        current_page: 1,
        last_page: 1,
        per_page: 20,
        total: 0
      }
      this.resetFilters()
    }
  }
})