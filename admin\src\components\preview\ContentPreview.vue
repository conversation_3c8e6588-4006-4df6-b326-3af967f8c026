<template>
  <div class="content-preview">
    <div class="content-container">
      <div class="content-header">
        <h3 class="content-title">群组介绍</h3>
      </div>
      
      <div class="content-body">
        <div
          v-if="richContent"
          class="rich-content"
          :style="contentStyle"
          v-html="richContent"
        ></div>
        <div v-else class="content-placeholder">
          <el-icon class="placeholder-icon"><Document /></el-icon>
          <div class="placeholder-text">暂无详细介绍内容</div>
          <div class="placeholder-hint">请在编辑器中添加群组介绍</div>
        </div>
      </div>
      
      <!-- 展开/收起按钮 -->
      <div v-if="showExpandButton" class="content-actions">
        <el-button
          @click="toggleExpanded"
          type="primary"
          text
          size="small"
        >
          {{ isExpanded ? '收起' : '展开全部' }}
          <el-icon>
            <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Document, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const isExpanded = ref(false)

// 计算属性
const richContent = computed(() => {
  return props.groupData.rich_content || ''
})

const maxHeight = computed(() => {
  return props.section.config?.maxHeight || 300
})

const showExpandButton = computed(() => {
  // 简单判断内容是否超出高度限制
  return richContent.value && richContent.value.length > 500
})

const contentStyle = computed(() => {
  const style = {
    lineHeight: '1.6',
    color: '#606266'
  }
  
  if (!isExpanded.value && showExpandButton.value) {
    style.maxHeight = `${maxHeight.value}px`
    style.overflow = 'hidden'
    style.position = 'relative'
  }
  
  return style
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style lang="scss" scoped>
.content-preview {
  .content-container {
    padding: 20px;
    
    .content-header {
      margin-bottom: 16px;
      
      .content-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        display: inline-block;
      }
    }
    
    .content-body {
      .rich-content {
        // 富文本内容样式
        :deep(p) {
          margin: 0 0 12px 0;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          margin: 16px 0 8px 0;
          color: #303133;
          
          &:first-child {
            margin-top: 0;
          }
        }
        
        :deep(h1) { font-size: 24px; }
        :deep(h2) { font-size: 20px; }
        :deep(h3) { font-size: 18px; }
        :deep(h4) { font-size: 16px; }
        :deep(h5) { font-size: 14px; }
        :deep(h6) { font-size: 12px; }
        
        :deep(ul), :deep(ol) {
          padding-left: 20px;
          margin: 12px 0;
          
          li {
            margin: 4px 0;
          }
        }
        
        :deep(blockquote) {
          border-left: 4px solid #409eff;
          padding-left: 16px;
          margin: 16px 0;
          color: #606266;
          background: #f8f9fa;
          padding: 12px 16px;
          border-radius: 0 4px 4px 0;
        }
        
        :deep(img) {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          margin: 8px 0;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        :deep(a) {
          color: #409eff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        :deep(code) {
          background: #f1f2f3;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
          color: #e74c3c;
        }
        
        :deep(pre) {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 4px;
          overflow-x: auto;
          margin: 12px 0;
          
          code {
            background: none;
            padding: 0;
            color: #303133;
          }
        }
        
        :deep(table) {
          width: 100%;
          border-collapse: collapse;
          margin: 12px 0;
          
          th, td {
            border: 1px solid #dcdfe6;
            padding: 8px 12px;
            text-align: left;
          }
          
          th {
            background: #f5f7fa;
            font-weight: 600;
          }
        }
        
        // 渐变遮罩效果（当内容被截断时）
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 40px;
          background: linear-gradient(transparent, white);
          pointer-events: none;
        }
      }
      
      .content-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        color: #909399;
        text-align: center;
        
        .placeholder-icon {
          font-size: 32px;
          margin-bottom: 12px;
        }
        
        .placeholder-text {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 8px;
        }
        
        .placeholder-hint {
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }
    
    .content-actions {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
    }
  }
}
</style>
