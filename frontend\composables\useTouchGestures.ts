import { ref, onMounted, onUnmounted } from 'vue'

export interface TouchGestureOptions {
  threshold?: number
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onTap?: () => void
  onDoubleTap?: () => void
  onLongPress?: () => void
  onPinch?: (scale: number) => void
  onRotate?: (angle: number) => void
}

interface TouchState {
  startX: number
  startY: number
  startTime: number
  lastX: number
  lastY: number
  lastTime: number
  isPressed: boolean
  isMultiTouch: boolean
  initialDistance: number
  initialAngle: number
}

export function useTouchGestures(elementRef: Ref<HTMLElement | null>, options: TouchGestureOptions = {}) {
  const {
    threshold = 50,
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onTap,
    onDoubleTap,
    onLongPress,
    onPinch,
    onRotate,
  } = options

  const touchState = ref<TouchState>({
    startX: 0,
    startY: 0,
    startTime: 0,
    lastX: 0,
    lastY: 0,
    lastTime: 0,
    isPressed: false,
    isMultiTouch: false,
    initialDistance: 0,
    initialAngle: 0,
  })

  const lastTapTime = ref(0)
  const longPressTimer = ref<NodeJS.Timeout | null>(null)

  const getDistance = (touch1: Touch, touch2: Touch): number => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }

  const getAngle = (touch1: Touch, touch2: Touch): number => {
    const dx = touch2.clientX - touch1.clientX
    const dy = touch2.clientY - touch1.clientY
    return Math.atan2(dy, dx) * 180 / Math.PI
  }

  const handleTouchStart = (event: TouchEvent) => {
    const touch = event.touches[0]
    const now = Date.now()

    touchState.value = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: now,
      lastX: touch.clientX,
      lastY: touch.clientY,
      lastTime: now,
      isPressed: true,
      isMultiTouch: event.touches.length > 1,
      initialDistance: 0,
      initialAngle: 0,
    }

    if (event.touches.length === 2) {
      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      touchState.value.initialDistance = getDistance(touch1, touch2)
      touchState.value.initialAngle = getAngle(touch1, touch2)
    }

    // 长按检测
    if (onLongPress) {
      longPressTimer.value = setTimeout(() => {
        if (touchState.value.isPressed) {
          onLongPress()
        }
      }, 500)
    }
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
    }

    const touch = event.touches[0]
    touchState.value.lastX = touch.clientX
    touchState.value.lastY = touch.clientY
    touchState.value.lastTime = Date.now()

    if (event.touches.length === 2 && (onPinch || onRotate)) {
      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      
      if (onPinch) {
        const currentDistance = getDistance(touch1, touch2)
        const scale = currentDistance / touchState.value.initialDistance
        onPinch(scale)
      }

      if (onRotate) {
        const currentAngle = getAngle(touch1, touch2)
        const angle = currentAngle - touchState.value.initialAngle
        onRotate(angle)
      }
    }
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
    }

    const state = touchState.value
    state.isPressed = false

    if (event.changedTouches.length === 0) return

    const touch = event.changedTouches[0]
    const deltaX = touch.clientX - state.startX
    const deltaY = touch.clientY - state.startY
    const deltaTime = Date.now() - state.startTime

    // 判断是否为点击
    if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
      const now = Date.now()
      
      // 双击检测
      if (now - lastTapTime.value < 300) {
        onDoubleTap?.()
        lastTapTime.value = 0
      } else {
        // 单击
        setTimeout(() => {
          if (lastTapTime.value === now) {
            onTap?.()
          }
        }, 300)
        lastTapTime.value = now
      }
      return
    }

    // 滑动检测
    if (Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (deltaX > 0) {
          onSwipeRight?.()
        } else {
          onSwipeLeft?.()
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          onSwipeDown?.()
        } else {
          onSwipeUp?.()
        }
      }
    }
  }

  const handleTouchCancel = () => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
    }
    touchState.value.isPressed = false
  }

  onMounted(() => {
    const element = elementRef.value
    if (!element) return

    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchmove', handleTouchMove, { passive: true })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })
    element.addEventListener('touchcancel', handleTouchCancel, { passive: true })
  })

  onUnmounted(() => {
    const element = elementRef.value
    if (!element) return

    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
    element.removeEventListener('touchcancel', handleTouchCancel)

    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
    }
  })

  return {
    touchState,
  }
}

// 滑动手势指令
export const vSwipe = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    useTouchGestures(ref(el), typeof value === 'function' ? { onSwipeLeft: value } : value)
  }
}

// 长按指令
export const vLongPress = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    useTouchGestures(ref(el), typeof value === 'function' ? { onLongPress: value } : value)
  }
}