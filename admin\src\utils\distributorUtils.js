/**
 * 分销员工具类库
 * 提供分销员相关的通用工具函数和常量
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

// ==================== 常量定义 ====================

/**
 * 客户等级配置
 */
export const CUSTOMER_LEVELS = {
  A: {
    code: 'A',
    name: 'A级客户',
    color: '#F56C6C',
    commission_rate: 20.0,
    priority: 1,
    description: '高价值客户，优先服务'
  },
  B: {
    code: 'B',
    name: 'B级客户',
    color: '#E6A23C',
    commission_rate: 15.0,
    priority: 2,
    description: '重要客户，定期跟进'
  },
  C: {
    code: 'C',
    name: 'C级客户',
    color: '#409EFF',
    commission_rate: 10.0,
    priority: 3,
    description: '普通客户，正常维护'
  },
  D: {
    code: 'D',
    name: 'D级客户',
    color: '#909399',
    commission_rate: 5.0,
    priority: 4,
    description: '潜在客户，培养转化'
  }
}

/**
 * 客户状态配置
 */
export const CUSTOMER_STATUSES = {
  active: {
    code: 'active',
    name: '活跃',
    color: 'success',
    description: '客户活跃，经常互动'
  },
  inactive: {
    code: 'inactive',
    name: '不活跃',
    color: 'warning',
    description: '客户不活跃，需要激活'
  },
  potential: {
    code: 'potential',
    name: '潜在',
    color: 'primary',
    description: '潜在客户，需要培养'
  },
  lost: {
    code: 'lost',
    name: '流失',
    color: 'danger',
    description: '客户流失，需要挽回'
  }
}

/**
 * 订单状态配置
 */
export const ORDER_STATUSES = {
  pending: {
    code: 'pending',
    name: '待支付',
    color: 'warning',
    description: '订单已创建，等待支付'
  },
  paid: {
    code: 'paid',
    name: '已支付',
    color: 'primary',
    description: '订单已支付，等待处理'
  },
  completed: {
    code: 'completed',
    name: '已完成',
    color: 'success',
    description: '订单已完成，服务交付'
  },
  cancelled: {
    code: 'cancelled',
    name: '已取消',
    color: 'info',
    description: '订单已取消'
  },
  refunded: {
    code: 'refunded',
    name: '已退款',
    color: 'danger',
    description: '订单已退款'
  }
}

/**
 * 佣金状态配置
 */
export const COMMISSION_STATUSES = {
  pending: {
    code: 'pending',
    name: '待结算',
    color: 'warning',
    description: '佣金待结算'
  },
  settled: {
    code: 'settled',
    name: '已结算',
    color: 'success',
    description: '佣金已结算'
  },
  frozen: {
    code: 'frozen',
    name: '已冻结',
    color: 'danger',
    description: '佣金已冻结'
  }
}

// ==================== 数据格式化工具 ====================

/**
 * 格式化金额
 * @param {number|string} amount - 金额
 * @param {number} decimals - 小数位数，默认2位
 * @param {boolean} showSymbol - 是否显示货币符号，默认true
 * @returns {string} 格式化后的金额
 */
export function formatMoney(amount, decimals = 2, showSymbol = true) {
  try {
    const num = parseFloat(amount) || 0
    const formatted = num.toFixed(decimals)
    
    // 添加千分位分隔符
    const parts = formatted.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    
    const result = parts.join('.')
    return showSymbol ? `¥${result}` : result
  } catch (error) {
    console.error('格式化金额失败:', error)
    return showSymbol ? '¥0.00' : '0.00'
  }
}

/**
 * 格式化数字（支持万、千等单位）
 * @param {number|string} num - 数字
 * @param {number} decimals - 小数位数，默认1位
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, decimals = 1) {
  try {
    const number = parseFloat(num) || 0
    
    if (number >= 100000000) {
      return (number / 100000000).toFixed(decimals) + '亿'
    } else if (number >= 10000) {
      return (number / 10000).toFixed(decimals) + '万'
    } else if (number >= 1000) {
      return (number / 1000).toFixed(decimals) + 'k'
    }
    
    return number.toString()
  } catch (error) {
    console.error('格式化数字失败:', error)
    return '0'
  }
}

/**
 * 格式化百分比
 * @param {number|string} value - 数值
 * @param {number} decimals - 小数位数，默认1位
 * @returns {string} 格式化后的百分比
 */
export function formatPercentage(value, decimals = 1) {
  try {
    const num = parseFloat(value) || 0
    return num.toFixed(decimals) + '%'
  } catch (error) {
    console.error('格式化百分比失败:', error)
    return '0.0%'
  }
}

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  try {
    if (!date) return '-'
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return '-'
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  } catch (error) {
    console.error('格式化日期失败:', error)
    return '-'
  }
}

/**
 * 格式化相对时间
 * @param {string|Date} date - 日期
 * @returns {string} 相对时间描述
 */
export function formatRelativeTime(date) {
  try {
    if (!date) return '-'
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return '-'
    
    const now = new Date()
    const diffMs = now - d
    const diffSeconds = Math.floor(diffMs / 1000)
    const diffMinutes = Math.floor(diffSeconds / 60)
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffSeconds < 60) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return formatDate(date)
    }
  } catch (error) {
    console.error('格式化相对时间失败:', error)
    return '-'
  }
}

// ==================== 数据验证工具 ====================

/**
 * 验证手机号
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
export function validatePhone(phone) {
  if (!phone) return false
  return /^1[3-9]\d{9}$/.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
export function validateEmail(email) {
  if (!email) return false
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

/**
 * 验证身份证号
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否有效
 */
export function validateIdCard(idCard) {
  if (!idCard) return false
  return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
}

/**
 * 验证客户等级
 * @param {string} level - 客户等级
 * @returns {boolean} 是否有效
 */
export function validateCustomerLevel(level) {
  return Object.keys(CUSTOMER_LEVELS).includes(level)
}

/**
 * 验证客户状态
 * @param {string} status - 客户状态
 * @returns {boolean} 是否有效
 */
export function validateCustomerStatus(status) {
  return Object.keys(CUSTOMER_STATUSES).includes(status)
}

// ==================== 数据处理工具 ====================

/**
 * 获取客户等级信息
 * @param {string} level - 等级代码
 * @returns {Object} 等级信息
 */
export function getCustomerLevelInfo(level) {
  return CUSTOMER_LEVELS[level] || CUSTOMER_LEVELS.C
}

/**
 * 获取客户状态信息
 * @param {string} status - 状态代码
 * @returns {Object} 状态信息
 */
export function getCustomerStatusInfo(status) {
  return CUSTOMER_STATUSES[status] || CUSTOMER_STATUSES.potential
}

/**
 * 获取订单状态信息
 * @param {string} status - 状态代码
 * @returns {Object} 状态信息
 */
export function getOrderStatusInfo(status) {
  return ORDER_STATUSES[status] || ORDER_STATUSES.pending
}

/**
 * 获取佣金状态信息
 * @param {string} status - 状态代码
 * @returns {Object} 状态信息
 */
export function getCommissionStatusInfo(status) {
  return COMMISSION_STATUSES[status] || COMMISSION_STATUSES.pending
}

/**
 * 计算佣金金额
 * @param {number} orderAmount - 订单金额
 * @param {string} customerLevel - 客户等级
 * @param {number} customRate - 自定义佣金率（可选）
 * @returns {number} 佣金金额
 */
export function calculateCommission(orderAmount, customerLevel, customRate = null) {
  try {
    const amount = parseFloat(orderAmount) || 0
    const rate = customRate || getCustomerLevelInfo(customerLevel).commission_rate
    return (amount * rate) / 100
  } catch (error) {
    console.error('计算佣金失败:', error)
    return 0
  }
}

/**
 * 生成客户编号
 * @param {string} prefix - 前缀，默认'C'
 * @returns {string} 客户编号
 */
export function generateCustomerCode(prefix = 'C') {
  const timestamp = Date.now().toString().slice(-8)
  const random = Math.random().toString(36).substr(2, 4).toUpperCase()
  return `${prefix}${timestamp}${random}`
}

/**
 * 生成订单编号
 * @param {string} prefix - 前缀，默认'ORD'
 * @returns {string} 订单编号
 */
export function generateOrderCode(prefix = 'ORD') {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const timestamp = Date.now().toString().slice(-6)
  return `${prefix}${year}${month}${day}${timestamp}`
}

// ==================== 交互工具 ====================

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @returns {Promise<boolean>} 是否成功
 */
export async function copyToClipboard(text, successMessage = '复制成功') {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 现代浏览器
      await navigator.clipboard.writeText(text)
    } else {
      // 降级处理
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }
    
    ElNotification.success({
      title: '操作成功',
      message: successMessage
    })
    
    return true
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
    return false
  }
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @param {string} type - 对话框类型
 * @returns {Promise<boolean>} 用户是否确认
 */
export async function confirmDialog(message, title = '确认操作', type = 'warning') {
  try {
    await ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type
    })
    return true
  } catch (error) {
    return false
  }
}

/**
 * 成功通知
 * @param {string} title - 标题
 * @param {string} message - 消息
 */
export function successNotification(title, message) {
  ElNotification.success({
    title,
    message,
    duration: 3000
  })
}

/**
 * 错误通知
 * @param {string} title - 标题
 * @param {string} message - 消息
 */
export function errorNotification(title, message) {
  ElNotification.error({
    title,
    message,
    duration: 5000
  })
}

/**
 * 警告通知
 * @param {string} title - 标题
 * @param {string} message - 消息
 */
export function warningNotification(title, message) {
  ElNotification.warning({
    title,
    message,
    duration: 4000
  })
}

// ==================== 数据导出工具 ====================

/**
 * 导出CSV文件
 * @param {Array} data - 数据数组
 * @param {Array} headers - 表头数组
 * @param {string} filename - 文件名
 */
export function exportToCSV(data, headers, filename = 'export.csv') {
  try {
    // 构建CSV内容
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header] || ''
          // 处理包含逗号的值
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value
        }).join(',')
      )
    ].join('\n')
    
    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    successNotification('导出成功', `文件 ${filename} 已下载`)
  } catch (error) {
    console.error('导出CSV失败:', error)
    errorNotification('导出失败', '导出CSV文件时发生错误')
  }
}

/**
 * 导出Excel文件（需要引入相关库）
 * @param {Array} data - 数据数组
 * @param {Array} headers - 表头数组
 * @param {string} filename - 文件名
 */
export function exportToExcel(data, headers, filename = 'export.xlsx') {
  // 这里需要引入 xlsx 库来实现
  // 暂时提供占位实现
  ElMessage.info('Excel导出功能需要引入xlsx库')
}

// ==================== 性能优化工具 ====================

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 对象差异比较
 * @param {Object} obj1 - 对象1
 * @param {Object} obj2 - 对象2
 * @returns {Object} 差异对象
 */
export function objectDiff(obj1, obj2) {
  const diff = {}
  
  for (const key in obj2) {
    if (obj2.hasOwnProperty(key)) {
      if (obj1[key] !== obj2[key]) {
        diff[key] = {
          old: obj1[key],
          new: obj2[key]
        }
      }
    }
  }
  
  return diff
}

// ==================== 本地存储工具 ====================

/**
 * 本地存储管理器
 */
export class LocalStorageManager {
  constructor(prefix = 'distributor_') {
    this.prefix = prefix
  }

  /**
   * 设置存储项
   * @param {string} key - 键
   * @param {any} value - 值
   * @param {number} expiry - 过期时间（毫秒）
   */
  setItem(key, value, expiry = null) {
    try {
      const item = {
        value,
        timestamp: Date.now(),
        expiry: expiry ? Date.now() + expiry : null
      }
      localStorage.setItem(this.prefix + key, JSON.stringify(item))
    } catch (error) {
      console.error('设置本地存储失败:', error)
    }
  }

  /**
   * 获取存储项
   * @param {string} key - 键
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值
   */
  getItem(key, defaultValue = null) {
    try {
      const itemStr = localStorage.getItem(this.prefix + key)
      if (!itemStr) return defaultValue

      const item = JSON.parse(itemStr)
      
      // 检查是否过期
      if (item.expiry && Date.now() > item.expiry) {
        this.removeItem(key)
        return defaultValue
      }

      return item.value
    } catch (error) {
      console.error('获取本地存储失败:', error)
      return defaultValue
    }
  }

  /**
   * 移除存储项
   * @param {string} key - 键
   */
  removeItem(key) {
    try {
      localStorage.removeItem(this.prefix + key)
    } catch (error) {
      console.error('移除本地存储失败:', error)
    }
  }

  /**
   * 清除所有存储项
   */
  clear() {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error('清除本地存储失败:', error)
    }
  }
}

// 创建默认的本地存储管理器实例
export const localStorage = new LocalStorageManager()

/**
 * 使用示例：
 * 
 * import { 
 *   formatMoney, 
 *   formatDate, 
 *   validatePhone, 
 *   copyToClipboard,
 *   debounce,
 *   localStorage
 * } from '@/utils/distributorUtils'
 * 
 * // 格式化金额
 * const formattedAmount = formatMoney(12345.67) // ¥12,345.67
 * 
 * // 验证手机号
 * const isValidPhone = validatePhone('13800138001') // true
 * 
 * // 复制到剪贴板
 * await copyToClipboard('要复制的文本')
 * 
 * // 防抖搜索
 * const debouncedSearch = debounce((keyword) => {
 *   console.log('搜索:', keyword)
 * }, 500)
 * 
 * // 本地存储
 * localStorage.setItem('userPrefs', { theme: 'dark' }, 24 * 60 * 60 * 1000) // 24小时过期
 * const userPrefs = localStorage.getItem('userPrefs', {})
 */