<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\GroupTemplate;

class DistributorGroupController extends Controller
{
    /**
     * 分销员群组列表
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = WechatGroup::where('distributor_id', $user->id)
            ->with(['template:id,name', 'orders'])
            ->withCount('orders')
            ->withSum('orders', 'amount');

        // 搜索过滤
        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json($groups);
    }

    /**
     * 创建新群组
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|string',
            'template_id' => 'nullable|exists:group_templates,id',
            'qr_code' => 'required|string',
            
            // 营销字段
            'red_packet_count' => 'nullable|string|max:20',
            'like_count' => 'nullable|integer|min:0',
            'message_count' => 'nullable|integer|min:0',
            'quick_content' => 'nullable|string',
            'member_content' => 'nullable|string',
            'ad_content' => 'nullable|string',
            'virtual_member_count' => 'nullable|integer|min:0',
            'virtual_total_income' => 'nullable|numeric|min:0',
            'virtual_order_count' => 'nullable|integer|min:0',
            'marketing_tags' => 'nullable|string',
        ]);

        $user = $request->user();

        // 检查模板权限
        if ($request->template_id) {
            $template = GroupTemplate::findOrFail($request->template_id);
            if (!$this->checkTemplatePermission($user, $template)) {
                return response()->json(['error' => '无权使用此模板'], 403);
            }
        }

        $group = WechatGroup::create([
            'distributor_id' => $user->id,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'description' => $request->description,
            'price' => $request->price,
            'image' => $request->image,
            'template_id' => $request->template_id,
            'qr_code' => $request->qr_code,
            'status' => 'active',
            
            // 营销字段
            'red_packet_count' => $request->red_packet_count ?? '10万+',
            'like_count' => $request->like_count ?? 324,
            'message_count' => $request->message_count ?? 341,
            'quick_content' => $request->quick_content,
            'member_content' => $request->member_content,
            'ad_content' => $request->ad_content,
            'virtual_member_count' => $request->virtual_member_count ?? 500,
            'virtual_total_income' => $request->virtual_total_income ?? 0,
            'virtual_order_count' => $request->virtual_order_count ?? 100,
            'show_virtual_data' => true,
            'marketing_tags' => $request->marketing_tags,
        ]);

        return response()->json([
            'message' => '群组创建成功',
            'group' => $group->load('template')
        ], 201);
    }

    /**
     * 群组详情
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        
        $group = WechatGroup::where('distributor_id', $user->id)
            ->with(['template', 'orders.user'])
            ->withCount('orders')
            ->withSum('orders', 'amount')
            ->findOrFail($id);

        // 统计数据
        $stats = [
            'total_orders' => $group->orders_count,
            'total_income' => $group->orders_sum_amount ?? 0,
            'commission' => $this->calculateCommission($group->orders_sum_amount ?? 0, $user),
            'conversion_rate' => $this->calculateConversionRate($group),
        ];

        // 最近订单
        $recent_orders = $group->orders()
            ->with('user:id,name')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'group' => $group,
            'stats' => $stats,
            'recent_orders' => $recent_orders,
        ]);
    }

    /**
     * 更新群组
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'sometimes|required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'price' => 'sometimes|required|numeric|min:0',
            'image' => 'nullable|string',
            'qr_code' => 'sometimes|required|string',
            'status' => 'sometimes|required|in:active,inactive',
            
            // 营销字段
            'red_packet_count' => 'nullable|string|max:20',
            'like_count' => 'nullable|integer|min:0',
            'message_count' => 'nullable|integer|min:0',
            'quick_content' => 'nullable|string',
            'member_content' => 'nullable|string',
            'ad_content' => 'nullable|string',
            'virtual_member_count' => 'nullable|integer|min:0',
            'virtual_total_income' => 'nullable|numeric|min:0',
            'virtual_order_count' => 'nullable|integer|min:0',
            'show_virtual_data' => 'nullable|boolean',
            'auto_increase_count' => 'nullable|boolean',
            'marketing_tags' => 'nullable|string',
        ]);

        $user = $request->user();
        
        $group = WechatGroup::where('distributor_id', $user->id)->findOrFail($id);
        
        $group->update($request->all());

        return response()->json([
            'message' => '群组更新成功',
            'group' => $group->load('template')
        ]);
    }

    /**
     * 删除群组
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        
        $group = WechatGroup::where('distributor_id', $user->id)->findOrFail($id);
        
        // 检查是否有订单
        if ($group->orders()->count() > 0) {
            return response()->json(['error' => '存在订单记录，无法删除'], 400);
        }
        
        $group->delete();

        return response()->json(['message' => '群组删除成功']);
    }

    /**
     * 批量操作
     */
    public function batchAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'group_ids' => 'required|array',
            'group_ids.*' => 'exists:wechat_groups,id',
        ]);

        $user = $request->user();
        
        $groups = WechatGroup::where('distributor_id', $user->id)
            ->whereIn('id', $request->group_ids);

        switch ($request->action) {
            case 'activate':
                $groups->update(['status' => 'active']);
                $message = '群组已批量启用';
                break;
                
            case 'deactivate':
                $groups->update(['status' => 'inactive']);
                $message = '群组已批量禁用';
                break;
                
            case 'delete':
                // 检查是否有订单
                $hasOrders = $groups->whereHas('orders')->exists();
                if ($hasOrders) {
                    return response()->json(['error' => '存在订单记录的群组无法删除'], 400);
                }
                $groups->delete();
                $message = '群组已批量删除';
                break;
        }

        return response()->json(['message' => $message]);
    }

    /**
     * 群组统计数据
     */
    public function statistics(Request $request, $id)
    {
        $user = $request->user();
        
        $group = WechatGroup::where('distributor_id', $user->id)->findOrFail($id);

        // 按日期统计订单
        $dailyStats = DB::table('orders')
            ->where('wechat_group_id', $id)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(amount) as income')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->limit(30)
            ->get();

        // 订单状态统计
        $statusStats = DB::table('orders')
            ->where('wechat_group_id', $id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        return response()->json([
            'daily_stats' => $dailyStats,
            'status_stats' => $statusStats,
            'total_visits' => $this->getGroupVisits($id),
            'conversion_rate' => $this->calculateConversionRate($group),
        ]);
    }

    /**
     * 复制群组
     */
    public function duplicate(Request $request, $id)
    {
        $user = $request->user();
        
        $originalGroup = WechatGroup::where('distributor_id', $user->id)->findOrFail($id);
        
        $newGroup = $originalGroup->replicate();
        $newGroup->title = $originalGroup->title . ' (副本)';
        $newGroup->created_at = now();
        $newGroup->updated_at = now();
        $newGroup->save();

        return response()->json([
            'message' => '群组复制成功',
            'group' => $newGroup->load('template')
        ]);
    }

    // 私有方法
    private function checkTemplatePermission($user, $template)
    {
        if ($template->is_public) {
            return true;
        }

        $allowedDistributors = $template->allowed_distributors ?? [];
        $allowedGroups = $template->allowed_distribution_groups ?? [];

        return in_array($user->id, $allowedDistributors) || 
               in_array($user->distribution_group_id, $allowedGroups);
    }

    private function calculateCommission($income, $user)
    {
        $distributionGroup = $user->distributionGroup;
        $rate = $distributionGroup ? $distributionGroup->commission_rate : 0.1;
        return $income * $rate;
    }

    private function calculateConversionRate($group)
    {
        $visits = $this->getGroupVisits($group->id);
        $orders = $group->orders_count ?? 0;
        
        return $visits > 0 ? round(($orders / $visits) * 100, 2) : 0;
    }

    private function getGroupVisits($groupId)
    {
        // 这里可以从访问统计表获取数据
        // 暂时返回随机数作为示例
        return rand(100, 1000);
    }
}