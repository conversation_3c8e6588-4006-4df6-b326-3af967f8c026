// SCSS 混合宏定义

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 绝对居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Flex 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flex 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flex 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == 'md' {
    @media (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == 'lg' {
    @media (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == 'xl' {
    @media (max-width: #{$breakpoint-xl - 1px}) {
      @content;
    }
  }
  @if $breakpoint == 'sm-up' {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == 'md-up' {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == 'lg-up' {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == 'xl-up' {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 按钮样式
@mixin button-variant($color, $background, $border: $background) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover {
    color: $color;
    background-color: darken($background, 7.5%);
    border-color: darken($border, 10%);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($background, 0.25);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12.5%);
  }
  
  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

// 渐变按钮
@mixin button-gradient($gradient) {
  background: $gradient;
  border: none;
  color: white;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 卡片样式
@mixin card($padding: $spacing-6, $radius: $border-radius-lg) {
  background: $card-bg;
  border: 1px solid $card-border-color;
  border-radius: $radius;
  box-shadow: $card-shadow;
  padding: $padding;
}

// 现代化卡片
@mixin modern-card($padding: $spacing-6, $radius: $border-radius-lg) {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $radius;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: $padding;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
}

// 玻璃效果
@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 输入框样式
@mixin input-variant($border-color: $input-border-color, $focus-color: $input-focus-border-color) {
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: $spacing-3 $spacing-4;
  transition: $transition-base;
  
  &:focus {
    outline: none;
    border-color: $focus-color;
    box-shadow: 0 0 0 3px rgba($focus-color, 0.1);
  }
  
  &::placeholder {
    color: $input-placeholder-color;
  }
}

// 阴影变体
@mixin shadow-variant($level: 'md') {
  @if $level == 'sm' {
    box-shadow: $shadow-sm;
  } @else if $level == 'md' {
    box-shadow: $shadow-md;
  } @else if $level == 'lg' {
    box-shadow: $shadow-lg;
  } @else if $level == 'xl' {
    box-shadow: $shadow-xl;
  } @else if $level == '2xl' {
    box-shadow: $shadow-2xl;
  } @else {
    box-shadow: $shadow;
  }
}

// 渐变文字
@mixin gradient-text($gradient: $gradient-primary) {
  background: $gradient;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

// 渐变边框
@mixin gradient-border($gradient: $gradient-primary, $width: 1px) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: $width;
    background: $gradient;
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}

// 动画关键帧
@mixin keyframes($name) {
  @-webkit-keyframes #{$name} {
    @content;
  }
  @keyframes #{$name} {
    @content;
  }
}

// 过渡动画
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 变换
@mixin transform($transforms) {
  -webkit-transform: $transforms;
  -moz-transform: $transforms;
  -ms-transform: $transforms;
  -o-transform: $transforms;
  transform: $transforms;
}

// 旋转
@mixin rotate($degrees) {
  @include transform(rotate(#{$degrees}deg));
}

// 缩放
@mixin scale($scale) {
  @include transform(scale($scale));
}

// 平移
@mixin translate($x, $y) {
  @include transform(translate($x, $y));
}

// 倾斜
@mixin skew($x, $y) {
  @include transform(skew(#{$x}deg, #{$y}deg));
}

// 滤镜
@mixin filter($filter-type, $filter-amount) {
  -webkit-filter: $filter-type + unquote('(#{$filter-amount})');
  -moz-filter: $filter-type + unquote('(#{$filter-amount})');
  -ms-filter: $filter-type + unquote('(#{$filter-amount})');
  -o-filter: $filter-type + unquote('(#{$filter-amount})');
  filter: $filter-type + unquote('(#{$filter-amount})');
}

// 模糊
@mixin blur($amount) {
  @include filter(blur, $amount);
}

// 亮度
@mixin brightness($amount) {
  @include filter(brightness, $amount);
}

// 对比度
@mixin contrast($amount) {
  @include filter(contrast, $amount);
}

// 饱和度
@mixin saturate($amount) {
  @include filter(saturate, $amount);
}

// 色相旋转
@mixin hue-rotate($amount) {
  @include filter(hue-rotate, $amount);
}

// 反转
@mixin invert($amount) {
  @include filter(invert, $amount);
}

// 不透明度
@mixin opacity($amount) {
  @include filter(opacity, $amount);
}

// 深褐色
@mixin sepia($amount) {
  @include filter(sepia, $amount);
}

// 阴影滤镜
@mixin drop-shadow($x, $y, $blur, $color) {
  @include filter(drop-shadow, $x $y $blur $color);
}

// 用户选择
@mixin user-select($select) {
  -webkit-user-select: $select;
  -moz-user-select: $select;
  -ms-user-select: $select;
  user-select: $select;
}

// 禁用选择
@mixin no-select {
  @include user-select(none);
}

// 占位符样式
@mixin placeholder {
  &::-webkit-input-placeholder {
    @content;
  }
  &:-moz-placeholder {
    @content;
  }
  &::-moz-placeholder {
    @content;
  }
  &:-ms-input-placeholder {
    @content;
  }
}

// 滚动条样式
@mixin scrollbar($size: 8px, $track-color: transparent, $thumb-color: rgba(0, 0, 0, 0.2)) {
  &::-webkit-scrollbar {
    width: $size;
    height: $size;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $size / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $size / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 三角形
@mixin triangle($direction, $size, $color) {
  width: 0;
  height: 0;
  
  @if $direction == up {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-bottom: $size solid $color;
  } @else if $direction == down {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-top: $size solid $color;
  } @else if $direction == left {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-right: $size solid $color;
  } @else if $direction == right {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-left: $size solid $color;
  }
}

// 箭头
@mixin arrow($direction, $size: 8px, $color: $border-color) {
  &::after {
    content: '';
    position: absolute;
    @include triangle($direction, $size, $color);
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: $primary-color, $thickness: 2px) {
  width: $size;
  height: $size;
  border: $thickness solid rgba($color, 0.2);
  border-top-color: $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 脉冲动画
@mixin pulse($scale: 1.05, $duration: 2s) {
  animation: pulse $duration infinite;
  
  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale($scale);
    }
  }
}

// 弹跳动画
@mixin bounce($height: 30px, $duration: 1s) {
  animation: bounce $duration infinite;
  
  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -$height, 0);
    }
    70% {
      transform: translate3d(0, -$height / 2, 0);
    }
    90% {
      transform: translate3d(0, -$height / 7, 0);
    }
  }
}

// 摇摆动画
@mixin shake($distance: 10px, $duration: 0.5s) {
  animation: shake $duration;
  
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-$distance);
    }
    20%, 40%, 60%, 80% {
      transform: translateX($distance);
    }
  }
}

// 渐入动画
@mixin fade-in($duration: 0.5s) {
  animation: fadeIn $duration ease-in;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

// 滑入动画
@mixin slide-in($direction: up, $distance: 30px, $duration: 0.5s) {
  animation: slideIn#{capitalize($direction)} $duration ease-out;
  
  @if $direction == up {
    @keyframes slideInUp {
      from {
        transform: translateY($distance);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  } @else if $direction == down {
    @keyframes slideInDown {
      from {
        transform: translateY(-$distance);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }
  } @else if $direction == left {
    @keyframes slideInLeft {
      from {
        transform: translateX(-$distance);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  } @else if $direction == right {
    @keyframes slideInRight {
      from {
        transform: translateX($distance);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }
}

// 缩放动画
@mixin zoom-in($scale: 0.8, $duration: 0.3s) {
  animation: zoomIn $duration ease-out;
  
  @keyframes zoomIn {
    from {
      transform: scale($scale);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
}

// 悬停效果
@mixin hover-lift($distance: 2px, $shadow: 0 8px 24px rgba(0, 0, 0, 0.15)) {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-$distance);
    box-shadow: $shadow;
  }
}

// 悬停缩放
@mixin hover-scale($scale: 1.05) {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale($scale);
  }
}

// 网格布局
@mixin grid($columns: 12, $gap: $spacing-4) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

// Flex 布局
@mixin flex($direction: row, $wrap: nowrap, $justify: flex-start, $align: stretch) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
  justify-content: $justify;
  align-items: $align;
}

// 固定宽高比
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: ($height / $width) * 100%;
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 文字描边
@mixin text-stroke($width: 1px, $color: $black) {
  -webkit-text-stroke: $width $color;
  text-stroke: $width $color;
}

// 文字阴影
@mixin text-shadow($x: 1px, $y: 1px, $blur: 2px, $color: rgba(0, 0, 0, 0.5)) {
  text-shadow: $x $y $blur $color;
}

// 背景渐变
@mixin background-gradient($direction: 135deg, $colors...) {
  background: linear-gradient($direction, $colors);
}

// 径向渐变
@mixin radial-gradient($shape: circle, $colors...) {
  background: radial-gradient($shape, $colors);
}