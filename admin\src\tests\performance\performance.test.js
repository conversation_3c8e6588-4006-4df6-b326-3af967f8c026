/**
 * 性能测试套件
 * 验证分销员工作台的性能优化效果
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { performanceOptimizer } from '@/config/performance'
import { DistributorService } from '@/services/DistributorService'
import { useDistributor } from '@/composables/useDistributor'

describe('性能测试', () => {
  let service
  let composable
  
  beforeEach(() => {
    service = new DistributorService()
    composable = useDistributor()
    performanceOptimizer.clearAllMetrics()
  })
  
  afterEach(() => {
    performanceOptimizer.clearAllMetrics()
  })

  describe('缓存性能测试', () => {
    it('应该显著提升重复请求的响应速度', async () => {
      const mockData = {
        data: Array.from({ length: 100 }, (_, i) => ({
          id: i + 1,
          name: `客户${i + 1}`,
          level: 'A'
        })),
        total: 100
      }
      
      // Mock API响应
      service.apiClient = {
        getCustomers: vi.fn().mockImplementation(() => 
          new Promise(resolve => setTimeout(() => resolve(mockData), 100))
        )
      }
      
      // 第一次请求（无缓存）
      performanceOptimizer.startMeasure('first_request')
      await service.getCustomers({ page: 1 })
      const firstRequestTime = performanceOptimizer.endMeasure('first_request')
      
      // 第二次请求（使用缓存）
      performanceOptimizer.startMeasure('cached_request')
      await service.getCustomers({ page: 1 })
      const cachedRequestTime = performanceOptimizer.endMeasure('cached_request')
      
      // 缓存请求应该显著更快
      expect(cachedRequestTime).toBeLessThan(firstRequestTime * 0.1)
      expect(service.apiClient.getCustomers).toHaveBeenCalledTimes(1)
    })
    
    it('应该正确处理缓存过期', async () => {
      const mockData = { data: [], total: 0 }
      service.apiClient = {
        getCustomers: vi.fn().mockResolvedValue(mockData)
      }
      
      // 设置短缓存时间
      const originalTTL = service.cacheTTL
      service.cacheTTL = 50 // 50ms
      
      // 第一次请求
      await service.getCustomers({ page: 1 })
      expect(service.apiClient.getCustomers).toHaveBeenCalledTimes(1)
      
      // 立即第二次请求（使用缓存）
      await service.getCustomers({ page: 1 })
      expect(service.apiClient.getCustomers).toHaveBeenCalledTimes(1)
      
      // 等待缓存过期
      await new Promise(resolve => setTimeout(resolve, 60))
      
      // 第三次请求（缓存已过期）
      await service.getCustomers({ page: 1 })
      expect(service.apiClient.getCustomers).toHaveBeenCalledTimes(2)
      
      // 恢复原始TTL
      service.cacheTTL = originalTTL
    })
    
    it('应该限制缓存大小', () => {
      const maxSize = 5
      service.cache.maxSize = maxSize
      
      // 添加超过最大大小的缓存项
      for (let i = 0; i < maxSize + 3; i++) {
        service.setCache(`test_key_${i}`, { data: `test_${i}` })
      }
      
      // 缓存大小不应超过限制
      expect(service.cache.size).toBeLessThanOrEqual(maxSize)
    })
  })

  describe('数据处理性能测试', () => {
    it('应该高效处理大量客户数据', () => {
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        phone: `138${String(i).padStart(8, '0')}`,
        level: ['A', 'B', 'C', 'D'][i % 4],
        status: ['active', 'inactive', 'potential', 'lost'][i % 4],
        created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
      }))
      
      performanceOptimizer.startMeasure('data_processing')
      
      const processedData = largeDataset.map(item => service.processCustomerItem(item))
      
      const processingTime = performanceOptimizer.endMeasure('data_processing')
      
      // 处理10000条数据应该在合理时间内完成（< 1000ms）
      expect(processingTime).toBeLessThan(1000)
      expect(processedData).toHaveLength(10000)
      expect(processedData[0]).toHaveProperty('level_text')
      expect(processedData[0]).toHaveProperty('status_text')
      expect(processedData[0]).toHaveProperty('days_since_created')
    })
    
    it('应该高效进行数据验证', () => {
      const testData = Array.from({ length: 1000 }, (_, i) => ({
        name: `客户${i + 1}`,
        phone: `138${String(i).padStart(8, '0')}`,
        email: `customer${i + 1}@example.com`,
        level: ['A', 'B', 'C', 'D'][i % 4]
      }))
      
      performanceOptimizer.startMeasure('validation')
      
      const validationResults = testData.map(item => {
        try {
          service.validateCustomerData(item)
          return true
        } catch (error) {
          return false
        }
      })
      
      const validationTime = performanceOptimizer.endMeasure('validation')
      
      // 验证1000条数据应该很快（< 100ms）
      expect(validationTime).toBeLessThan(100)
      expect(validationResults.every(result => result === true)).toBe(true)
    })
  })

  describe('内存使用测试', () => {
    it('应该有效管理内存使用', async () => {
      if (!performance.memory) {
        console.warn('Performance.memory API not available, skipping memory test')
        return
      }
      
      const initialMemory = performance.memory.usedJSHeapSize
      
      // 创建大量数据
      const largeData = Array.from({ length: 5000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        data: new Array(100).fill(`数据${i}`)
      }))
      
      // 处理数据
      const processedData = largeData.map(item => service.processCustomerItem(item))
      
      const peakMemory = performance.memory.usedJSHeapSize
      
      // 清理数据
      largeData.length = 0
      processedData.length = 0
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc()
      }
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const finalMemory = performance.memory.usedJSHeapSize
      const memoryIncrease = (peakMemory - initialMemory) / 1024 / 1024 // MB
      const memoryRecovered = (peakMemory - finalMemory) / 1024 / 1024 // MB
      
      console.log(`内存使用情况:`)
      console.log(`  初始: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`)
      console.log(`  峰值: ${(peakMemory / 1024 / 1024).toFixed(2)}MB`)
      console.log(`  最终: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`)
      console.log(`  增长: ${memoryIncrease.toFixed(2)}MB`)
      console.log(`  回收: ${memoryRecovered.toFixed(2)}MB`)
      
      // 内存增长应该在合理范围内（< 50MB）
      expect(memoryIncrease).toBeLessThan(50)
      
      // 应该有一定的内存回收
      expect(memoryRecovered).toBeGreaterThan(0)
    })
  })

  describe('并发处理测试', () => {
    it('应该正确处理并发请求', async () => {
      const mockData = { data: [], total: 0 }
      let requestCount = 0
      
      service.apiClient = {
        getCustomers: vi.fn().mockImplementation(() => {
          requestCount++
          return new Promise(resolve => 
            setTimeout(() => resolve(mockData), 50)
          )
        })
      }
      
      performanceOptimizer.startMeasure('concurrent_requests')
      
      // 同时发起10个相同的请求
      const promises = Array.from({ length: 10 }, () => 
        service.getCustomers({ page: 1 })
      )
      
      const results = await Promise.all(promises)
      
      const concurrentTime = performanceOptimizer.endMeasure('concurrent_requests')
      
      // 由于缓存，实际只应该发起1个请求
      expect(requestCount).toBe(1)
      expect(results).toHaveLength(10)
      expect(results.every(result => result === mockData)).toBe(true)
      
      // 并发处理时间应该接近单个请求时间
      expect(concurrentTime).toBeLessThan(100)
    })
    
    it('应该正确处理不同参数的并发请求', async () => {
      const mockData = { data: [], total: 0 }
      let requestCount = 0
      
      service.apiClient = {
        getCustomers: vi.fn().mockImplementation(() => {
          requestCount++
          return new Promise(resolve => 
            setTimeout(() => resolve(mockData), 30)
          )
        })
      }
      
      // 同时发起不同参数的请求
      const promises = [
        service.getCustomers({ page: 1 }),
        service.getCustomers({ page: 2 }),
        service.getCustomers({ page: 1, level: 'A' }),
        service.getCustomers({ page: 1, keyword: '张三' })
      ]
      
      await Promise.all(promises)
      
      // 不同参数应该发起不同的请求
      expect(requestCount).toBe(4)
    })
  })

  describe('组合式API性能测试', () => {
    it('应该高效处理状态更新', async () => {
      const mockCustomers = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        level: 'A'
      }))
      
      // Mock服务方法
      const mockService = {
        getCustomers: vi.fn().mockResolvedValue({
          data: mockCustomers,
          total: 1000
        })
      }
      
      composable.service = mockService
      
      performanceOptimizer.startMeasure('state_update')
      
      await composable.loadCustomers()
      
      const updateTime = performanceOptimizer.endMeasure('state_update')
      
      // 状态更新应该很快（< 50ms）
      expect(updateTime).toBeLessThan(50)
      expect(composable.customers.value).toHaveLength(1000)
      expect(composable.pagination.value.total).toBe(1000)
    })
    
    it('应该高效处理过滤操作', async () => {
      const mockCustomers = Array.from({ length: 500 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        level: ['A', 'B', 'C'][i % 3],
        status: ['active', 'inactive'][i % 2]
      }))
      
      composable.customers.value = mockCustomers
      
      performanceOptimizer.startMeasure('filtering')
      
      // 执行多次过滤操作
      for (let i = 0; i < 100; i++) {
        const filtered = composable.customers.value.filter(customer => 
          customer.level === 'A' && customer.status === 'active'
        )
      }
      
      const filterTime = performanceOptimizer.endMeasure('filtering')
      
      // 过滤操作应该很快（< 100ms）
      expect(filterTime).toBeLessThan(100)
    })
  })

  describe('错误处理性能测试', () => {
    it('应该快速处理错误情况', async () => {
      const error = new Error('测试错误')
      service.apiClient = {
        getCustomers: vi.fn().mockRejectedValue(error)
      }
      
      performanceOptimizer.startMeasure('error_handling')
      
      try {
        await service.getCustomers({ page: 1 })
      } catch (e) {
        // 预期的错误
      }
      
      const errorTime = performanceOptimizer.endMeasure('error_handling')
      
      // 错误处理应该很快（< 10ms）
      expect(errorTime).toBeLessThan(10)
    })
    
    it('应该高效处理多个错误', async () => {
      const error = new Error('批量错误测试')
      service.apiClient = {
        getCustomers: vi.fn().mockRejectedValue(error)
      }
      
      performanceOptimizer.startMeasure('batch_error_handling')
      
      const promises = Array.from({ length: 50 }, async () => {
        try {
          await service.getCustomers({ page: Math.random() })
        } catch (e) {
          return e
        }
      })
      
      await Promise.all(promises)
      
      const batchErrorTime = performanceOptimizer.endMeasure('batch_error_handling')
      
      // 批量错误处理应该在合理时间内完成（< 100ms）
      expect(batchErrorTime).toBeLessThan(100)
    })
  })

  describe('UI渲染性能测试', () => {
    it('应该高效渲染大量数据', () => {
      const largeDataset = Array.from({ length: 2000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        level: ['A', 'B', 'C', 'D'][i % 4],
        status: ['active', 'inactive', 'potential', 'lost'][i % 4]
      }))
      
      performanceOptimizer.startMeasure('data_formatting')
      
      // 模拟数据格式化（UI渲染前的数据处理）
      const formattedData = largeDataset.map(item => ({
        ...item,
        level_text: composable.formatCustomerLevel(item.level),
        status_text: composable.formatCustomerStatus(item.status),
        display_name: `${item.name} (${item.level}级)`
      }))
      
      const formatTime = performanceOptimizer.endMeasure('data_formatting')
      
      // 数据格式化应该很快（< 200ms）
      expect(formatTime).toBeLessThan(200)
      expect(formattedData).toHaveLength(2000)
      expect(formattedData[0]).toHaveProperty('level_text')
      expect(formattedData[0]).toHaveProperty('status_text')
      expect(formattedData[0]).toHaveProperty('display_name')
    })
    
    it('应该高效处理搜索和过滤', () => {
      const dataset = Array.from({ length: 5000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        phone: `138${String(i).padStart(8, '0')}`,
        level: ['A', 'B', 'C', 'D'][i % 4],
        status: ['active', 'inactive', 'potential', 'lost'][i % 4]
      }))
      
      performanceOptimizer.startMeasure('search_filter')
      
      // 模拟复杂的搜索和过滤操作
      const searchKeyword = '客户1'
      const levelFilter = 'A'
      const statusFilter = 'active'
      
      const filteredData = dataset.filter(item => {
        const matchesSearch = item.name.includes(searchKeyword) || 
                             item.phone.includes(searchKeyword)
        const matchesLevel = !levelFilter || item.level === levelFilter
        const matchesStatus = !statusFilter || item.status === statusFilter
        
        return matchesSearch && matchesLevel && matchesStatus
      })
      
      const searchTime = performanceOptimizer.endMeasure('search_filter')
      
      // 搜索过滤应该很快（< 50ms）
      expect(searchTime).toBeLessThan(50)
      expect(filteredData.length).toBeGreaterThan(0)
      expect(filteredData.every(item => 
        item.name.includes(searchKeyword) && 
        item.level === levelFilter && 
        item.status === statusFilter
      )).toBe(true)
    })
  })

  describe('网络请求性能测试', () => {
    it('应该高效处理批量请求', async () => {
      const mockResponses = Array.from({ length: 20 }, (_, i) => ({
        data: [{ id: i + 1, name: `客户${i + 1}` }],
        total: 1
      }))
      
      let requestIndex = 0
      service.apiClient = {
        getCustomers: vi.fn().mockImplementation(() => {
          const response = mockResponses[requestIndex % mockResponses.length]
          requestIndex++
          return Promise.resolve(response)
        })
      }
      
      performanceOptimizer.startMeasure('batch_requests')
      
      // 批量发起请求
      const batchPromises = Array.from({ length: 20 }, (_, i) => 
        service.getCustomers({ page: i + 1 })
      )
      
      const results = await Promise.all(batchPromises)
      
      const batchTime = performanceOptimizer.endMeasure('batch_requests')
      
      // 批量请求应该在合理时间内完成（< 500ms）
      expect(batchTime).toBeLessThan(500)
      expect(results).toHaveLength(20)
      expect(service.apiClient.getCustomers).toHaveBeenCalledTimes(20)
    })
    
    it('应该正确处理请求超时', async () => {
      service.apiClient = {
        getCustomers: vi.fn().mockImplementation(() => 
          new Promise((resolve, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), 100)
          })
        )
      }
      
      performanceOptimizer.startMeasure('timeout_handling')
      
      try {
        await service.getCustomers({ page: 1 })
      } catch (error) {
        expect(error.message).toBe('Request timeout')
      }
      
      const timeoutTime = performanceOptimizer.endMeasure('timeout_handling')
      
      // 超时处理应该在预期时间内完成（约100ms）
      expect(timeoutTime).toBeGreaterThan(90)
      expect(timeoutTime).toBeLessThan(150)
    })
  })

  describe('性能基准测试', () => {
    it('应该满足性能基准要求', async () => {
      const performanceMetrics = {
        dataProcessing: 0,
        cacheAccess: 0,
        stateUpdate: 0,
        errorHandling: 0
      }
      
      // 数据处理基准测试
      const testData = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `客户${i + 1}`,
        level: 'A'
      }))
      
      performanceOptimizer.startMeasure('benchmark_data_processing')
      testData.forEach(item => service.processCustomerItem(item))
      performanceMetrics.dataProcessing = performanceOptimizer.endMeasure('benchmark_data_processing')
      
      // 缓存访问基准测试
      service.setCache('benchmark_test', { data: 'test' })
      performanceOptimizer.startMeasure('benchmark_cache_access')
      for (let i = 0; i < 1000; i++) {
        service.getCache('benchmark_test')
      }
      performanceMetrics.cacheAccess = performanceOptimizer.endMeasure('benchmark_cache_access')
      
      // 状态更新基准测试
      performanceOptimizer.startMeasure('benchmark_state_update')
      composable.customers.value = testData
      composable.pagination.value.total = testData.length
      performanceMetrics.stateUpdate = performanceOptimizer.endMeasure('benchmark_state_update')
      
      // 错误处理基准测试
      performanceOptimizer.startMeasure('benchmark_error_handling')
      try {
        service.validateCustomerData(null)
      } catch (error) {
        // 预期错误
      }
      performanceMetrics.errorHandling = performanceOptimizer.endMeasure('benchmark_error_handling')
      
      console.log('性能基准测试结果:')
      console.log(`  数据处理: ${performanceMetrics.dataProcessing.toFixed(2)}ms`)
      console.log(`  缓存访问: ${performanceMetrics.cacheAccess.toFixed(2)}ms`)
      console.log(`  状态更新: ${performanceMetrics.stateUpdate.toFixed(2)}ms`)
      console.log(`  错误处理: ${performanceMetrics.errorHandling.toFixed(2)}ms`)
      
      // 验证性能基准
      expect(performanceMetrics.dataProcessing).toBeLessThan(500) // 数据处理 < 500ms
      expect(performanceMetrics.cacheAccess).toBeLessThan(50)     // 缓存访问 < 50ms
      expect(performanceMetrics.stateUpdate).toBeLessThan(20)     // 状态更新 < 20ms
      expect(performanceMetrics.errorHandling).toBeLessThan(5)    // 错误处理 < 5ms
    })
    
    it('应该在不同数据量下保持稳定性能', () => {
      const dataSizes = [100, 500, 1000, 2000, 5000]
      const processingTimes = []
      
      dataSizes.forEach(size => {
        const testData = Array.from({ length: size }, (_, i) => ({
          id: i + 1,
          name: `客户${i + 1}`,
          level: ['A', 'B', 'C', 'D'][i % 4]
        }))
        
        performanceOptimizer.startMeasure(`processing_${size}`)
        testData.forEach(item => service.processCustomerItem(item))
        const time = performanceOptimizer.endMeasure(`processing_${size}`)
        
        processingTimes.push({ size, time })
      })
      
      console.log('不同数据量处理时间:')
      processingTimes.forEach(({ size, time }) => {
        console.log(`  ${size}条数据: ${time.toFixed(2)}ms (${(time/size).toFixed(3)}ms/条)`)
      })
      
      // 验证线性扩展性（处理时间应该大致与数据量成正比）
      const timePerItem = processingTimes.map(({ size, time }) => time / size)
      const avgTimePerItem = timePerItem.reduce((sum, time) => sum + time, 0) / timePerItem.length
      const maxDeviation = Math.max(...timePerItem.map(time => Math.abs(time - avgTimePerItem)))
      
      // 每条数据的处理时间偏差不应超过平均值的50%
      expect(maxDeviation).toBeLessThan(avgTimePerItem * 0.5)
    })
  })
})