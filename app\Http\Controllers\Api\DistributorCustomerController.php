<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DistributorCustomer;
use App\Models\CustomerFollowUp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

/**
 * 分销员客户管理控制器
 */
class DistributorCustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('role:distributor');
    }

    /**
     * 获取客户列表
     */
    public function index(Request $request)
    {
        $distributor = $request->user();
        
        $query = DistributorCustomer::where('distributor_id', $distributor->id)
                                   ->with(['latestFollowUp', 'nextFollowUp']);

        // 搜索过滤
        if ($request->filled('keyword')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('phone', 'like', '%' . $request->keyword . '%')
                  ->orWhere('wechat', 'like', '%' . $request->keyword . '%')
                  ->orWhere('company', 'like', '%' . $request->keyword . '%');
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 等级筛选
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // 来源筛选
        if ($request->filled('source')) {
            $query->where('source', $request->source);
        }

        // 标签筛选
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // 需要跟进筛选
        if ($request->filled('need_follow_up') && $request->need_follow_up) {
            $query->needFollowUp();
        }

        // 排序
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $customers = $query->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * 创建客户
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20|unique:distributor_customers,phone',
            'wechat' => 'nullable|string|max:100',
            'email' => 'nullable|email|max:100',
            'gender' => 'nullable|in:male,female,unknown',
            'birthday' => 'nullable|date',
            'occupation' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:200',
            'address' => 'nullable|string',
            'tags' => 'nullable|array',
            'level' => 'required|in:A,B,C,D',
            'source' => 'required|in:referral,advertisement,social_media,direct,other',
            'notes' => 'nullable|string',
            'preferences' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $customer = DistributorCustomer::create(array_merge(
                $request->validated(),
                [
                    'distributor_id' => $request->user()->id,
                    'status' => DistributorCustomer::STATUS_POTENTIAL,
                ]
            ));

            return response()->json([
                'success' => true,
                'data' => $customer->load(['latestFollowUp', 'nextFollowUp']),
                'message' => '客户创建成功',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '客户创建失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取客户详情
     */
    public function show($id)
    {
        $customer = DistributorCustomer::where('distributor_id', request()->user()->id)
                                      ->with([
                                          'followUps' => function ($query) {
                                              $query->orderBy('contact_time', 'desc')->limit(10);
                                          },
                                          'orders' => function ($query) {
                                              $query->orderBy('created_at', 'desc')->limit(5);
                                          }
                                      ])
                                      ->findOrFail($id);

        // 获取客户统计数据
        $stats = [
            'total_follow_ups' => $customer->followUps()->count(),
            'successful_follow_ups' => $customer->followUps()->where('result', 'successful')->count(),
            'last_contact_days' => $customer->last_contact_at ? 
                now()->diffInDays($customer->last_contact_at) : null,
            'value_score' => $customer->value_score,
            'conversion_rate' => $customer->order_count > 0 ? 
                ($customer->followUps()->where('result', 'successful')->count() / $customer->followUps()->count()) * 100 : 0,
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => $customer,
                'stats' => $stats,
            ],
        ]);
    }

    /**
     * 更新客户信息
     */
    public function update(Request $request, $id)
    {
        $customer = DistributorCustomer::where('distributor_id', $request->user()->id)
                                      ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20|unique:distributor_customers,phone,' . $id,
            'wechat' => 'nullable|string|max:100',
            'email' => 'nullable|email|max:100',
            'gender' => 'nullable|in:male,female,unknown',
            'birthday' => 'nullable|date',
            'occupation' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:200',
            'address' => 'nullable|string',
            'tags' => 'nullable|array',
            'level' => 'required|in:A,B,C,D',
            'status' => 'required|in:active,inactive,potential,lost',
            'source' => 'required|in:referral,advertisement,social_media,direct,other',
            'notes' => 'nullable|string',
            'preferences' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $customer->update($request->validated());

            return response()->json([
                'success' => true,
                'data' => $customer->load(['latestFollowUp', 'nextFollowUp']),
                'message' => '客户信息更新成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '客户信息更新失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 删除客户
     */
    public function destroy($id)
    {
        $customer = DistributorCustomer::where('distributor_id', request()->user()->id)
                                      ->findOrFail($id);

        try {
            $customer->delete();

            return response()->json([
                'success' => true,
                'message' => '客户删除成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '客户删除失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 添加跟进记录
     */
    public function addFollowUp(Request $request, $id)
    {
        $customer = DistributorCustomer::where('distributor_id', $request->user()->id)
                                      ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'type' => 'required|in:call,wechat,email,meeting,other',
            'title' => 'required|string|max:200',
            'content' => 'required|string',
            'result' => 'required|in:successful,failed,pending,scheduled',
            'contact_time' => 'required|date',
            'next_follow_up' => 'nullable|date|after:contact_time',
            'potential_value' => 'nullable|numeric|min:0',
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            DB::beginTransaction();

            $followUp = CustomerFollowUp::create(array_merge(
                $request->validated(),
                [
                    'customer_id' => $customer->id,
                    'distributor_id' => $request->user()->id,
                ]
            ));

            // 更新客户最后联系时间
            $customer->update([
                'last_contact_at' => $request->contact_time,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $followUp,
                'message' => '跟进记录添加成功',
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '跟进记录添加失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取跟进记录列表
     */
    public function getFollowUps(Request $request, $id)
    {
        $customer = DistributorCustomer::where('distributor_id', $request->user()->id)
                                      ->findOrFail($id);

        $query = $customer->followUps()->with('distributor:id,name');

        // 按类型筛选
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // 按结果筛选
        if ($request->filled('result')) {
            $query->where('result', $request->result);
        }

        // 时间范围筛选
        if ($request->filled('start_date')) {
            $query->whereDate('contact_time', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('contact_time', '<=', $request->end_date);
        }

        $followUps = $query->orderBy('contact_time', 'desc')
                          ->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $followUps,
        ]);
    }

    /**
     * 获取客户统计数据
     */
    public function getStats(Request $request)
    {
        $distributor = $request->user();
        $cacheKey = 'distributor_customer_stats_' . $distributor->id;

        $stats = Cache::remember($cacheKey, 300, function () use ($distributor) {
            $query = DistributorCustomer::where('distributor_id', $distributor->id);

            return [
                'total_customers' => $query->count(),
                'active_customers' => (clone $query)->active()->count(),
                'potential_customers' => (clone $query)->potential()->count(),
                'lost_customers' => (clone $query)->where('status', 'lost')->count(),
                'level_a_customers' => (clone $query)->byLevel('A')->count(),
                'level_b_customers' => (clone $query)->byLevel('B')->count(),
                'level_c_customers' => (clone $query)->byLevel('C')->count(),
                'level_d_customers' => (clone $query)->byLevel('D')->count(),
                'total_value' => (clone $query)->sum('total_spent'),
                'avg_order_value' => (clone $query)->avg('avg_order_value'),
                'need_follow_up' => (clone $query)->needFollowUp()->count(),
                'today_follow_ups' => CustomerFollowUp::where('distributor_id', $distributor->id)
                                                     ->today()->count(),
                'this_week_follow_ups' => CustomerFollowUp::where('distributor_id', $distributor->id)
                                                         ->thisWeek()->count(),
                'successful_follow_ups' => CustomerFollowUp::where('distributor_id', $distributor->id)
                                                          ->where('result', 'successful')->count(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 批量更新客户状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_ids' => 'required|array',
            'customer_ids.*' => 'integer',
            'status' => 'required|in:active,inactive,potential,lost',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $updatedCount = DistributorCustomer::where('distributor_id', $request->user()->id)
                                              ->whereIn('id', $request->customer_ids)
                                              ->update(['status' => $request->status]);

            return response()->json([
                'success' => true,
                'message' => "成功更新 {$updatedCount} 个客户状态",
                'updated_count' => $updatedCount,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量更新失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 导出客户数据
     */
    public function export(Request $request)
    {
        $distributor = $request->user();
        
        $query = DistributorCustomer::where('distributor_id', $distributor->id)
                                   ->with(['latestFollowUp', 'orders']);

        // 应用筛选条件
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        $customers = $query->orderBy('created_at', 'desc')->get();

        $exportData = $customers->map(function ($customer) {
            return [
                'ID' => $customer->id,
                '姓名' => $customer->name,
                '手机号' => $customer->phone,
                '微信号' => $customer->wechat,
                '邮箱' => $customer->email,
                '性别' => $customer->gender === 'male' ? '男' : ($customer->gender === 'female' ? '女' : '未知'),
                '职业' => $customer->occupation,
                '公司' => $customer->company,
                '客户等级' => $customer->level_text,
                '客户状态' => $customer->status_text,
                '客户来源' => $customer->source_text,
                '总消费' => $customer->total_spent,
                '订单数' => $customer->order_count,
                '平均订单价值' => $customer->avg_order_value,
                '最后联系时间' => $customer->last_contact_at ? $customer->last_contact_at->format('Y-m-d H:i:s') : '',
                '最后下单时间' => $customer->last_order_at ? $customer->last_order_at->format('Y-m-d H:i:s') : '',
                '创建时间' => $customer->created_at->format('Y-m-d H:i:s'),
                '备注' => $customer->notes,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'message' => '客户数据导出成功',
        ]);
    }

    /**
     * 获取需要跟进的客户
     */
    public function getNeedFollowUp(Request $request)
    {
        $distributor = $request->user();
        
        $customers = DistributorCustomer::where('distributor_id', $distributor->id)
                                       ->needFollowUp()
                                       ->with(['nextFollowUp'])
                                       ->orderBy('created_at', 'desc')
                                       ->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * 获取客户标签列表
     */
    public function getTags(Request $request)
    {
        $distributor = $request->user();
        
        $tags = DistributorCustomer::where('distributor_id', $distributor->id)
                                  ->whereNotNull('tags')
                                  ->pluck('tags')
                                  ->flatten()
                                  ->unique()
                                  ->values();

        return response()->json([
            'success' => true,
            'data' => $tags,
        ]);
    }
}