import{_ as e}from"./index-DtXAftX0.js";/* empty css                         *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               */import{af as l,r as a,e as t,k as s,l as n,t as i,E as u,z as o,D as r,u as c,F as d,Y as p,y as m,C as _,n as v}from"./vue-vendor-Dy164gUc.js";import{U as f,a$ as h,T as g,aV as b,at as y,a_ as w,aZ as C,al as k,ae as x,ai as j,ce as V,a6 as q,a0 as D,aY as F,bm as A,bn as U,aS as E,aM as z,ab as M,ay as S,bh as L,bi as Q,bj as R,bg as $,b1 as T,bK as H,bL as I,Q as N}from"./element-plus-h2SQQM64.js";import{S as Y}from"./StatCard-u_ssO_Ky.js";import{L as Z}from"./LineChart-CydsJ2U8.js";import{D as K}from"./DoughnutChart-CCHHIMjz.js";import{a as O}from"./agent-BTWzqVJ0.js";import{b as X}from"./browser-DJkR4j8n.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const B={class:"agent-dashboard"},G={class:"page-header"},J={class:"header-right"},P={class:"header-info"},W={class:"agent-code"},ee={class:"header-actions"},le={class:"card-header"},ae={class:"card-header"},te={class:"team-overview"},se={class:"team-stats"},ne={class:"team-stat-item"},ie={class:"value"},ue={class:"team-stat-item"},oe={class:"value"},re={class:"team-stat-item"},ce={class:"value"},de={class:"recent-members"},pe={class:"member-info"},me={class:"member-name"},_e={class:"member-time"},ve={class:"card-header"},fe={class:"activities-list"},he={class:"activity-icon"},ge={class:"activity-content"},be={class:"activity-title"},ye={class:"activity-desc"},we={class:"activity-time"},Ce={class:"qr-code-container"},ke={class:"qr-code-tips"},xe={class:"help-content"},je={class:"help-section"},Ve={class:"feature-item"},qe={class:"feature-icon"},De={class:"feature-item"},Fe={class:"feature-icon"},Ae={class:"feature-item"},Ue={class:"feature-icon"},Ee={class:"feature-item"},ze={class:"feature-icon"},Me={class:"feature-item"},Se={class:"feature-icon"},Le={class:"feature-item"},Qe={class:"feature-icon"},Re={class:"help-section"},$e={class:"help-section"},Te={class:"commission-rules"},He={class:"rule-item"},Ie={class:"example"},Ne={class:"rule-item"},Ye={class:"example"},Ze={class:"rule-item"},Ke={class:"help-section"},Oe={class:"tips-content"},Xe={class:"tips-content"},Be={class:"help-section"},Ge={class:"guide-content"},Je={class:"guide-content"},Pe={class:"guide-content"},We={class:"help-section"},el=e({__name:"AgentDashboard",setup(e){const el=l();a(!1);const ll=a("month"),al=a(!1),tl=a(!1),sl=a(),nl=a({}),il=a({}),ul=a({}),ol=a([]),rl=a([]),cl=a(""),dl=a("channels"),pl=a(["link-generation"]),ml=a([]),_l=a([{level:"初级代理",color:"info",name:"新手代理",requirements:"注册成功，完成实名认证",commission_rate:"5%",benefits:"基础推广工具、新手培训"},{level:"中级代理",color:"primary",name:"进阶代理",requirements:"推广用户≥10人，月佣金≥500元",commission_rate:"8%",benefits:"专属客服、营销素材、数据分析"},{level:"高级代理",color:"warning",name:"资深代理",requirements:"推广用户≥50人，团队≥5人，月佣金≥2000元",commission_rate:"12%",benefits:"优先结算、专属培训、活动优先权"},{level:"金牌代理",color:"danger",name:"顶级代理",requirements:"推广用户≥200人，团队≥20人，月佣金≥10000元",commission_rate:"15%",benefits:"专属经理、定制服务、年度奖励"}]),vl=a({direct:10,team:3}),fl=a({max_levels:5}),hl=a([{level:"1级",rate:"10%",description:"直接推广用户"},{level:"2级",rate:"3%",description:"下级代理推广用户"},{level:"3级",rate:"1%",description:"三级代理推广用户"},{level:"4级",rate:"0.5%",description:"四级代理推广用户"},{level:"5级",rate:"0.2%",description:"五级代理推广用户"}]),gl=a({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),bl=a({labels:["直接推广","团队推广","活动推广","其他"],datasets:[{data:[0,0,0,0],backgroundColor:["#409EFF","#67C23A","#E6A23C","#F56C6C"]}]}),yl={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},wl={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},Cl=async()=>{try{const e={week:{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120,190,300,500,200,300,450]},month:{labels:Array.from({length:30},(e,l)=>`${l+1}日`),data:Array.from({length:30},()=>Math.floor(1e3*Math.random()))},quarter:{labels:["1月","2月","3月"],data:[8e3,12e3,15e3]}}[ll.value];gl.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}}catch(e){N.error("加载佣金趋势失败")}},kl=async()=>{try{rl.value=[{id:1,type:"commission",title:"佣金到账",description:"您获得了 ¥150.00 的推广佣金",created_at:new Date},{id:2,type:"user",title:"新用户注册",description:'通过您的推广链接，新增用户"王五"',created_at:new Date(Date.now()-36e5)},{id:3,type:"team",title:"团队成员升级",description:'下级代理商"赵六"升级为二级代理',created_at:new Date(Date.now()-72e5)}]}catch(e){N.error("加载活动数据失败")}},xl=()=>{nl.value.agent_code&&(cl.value=`${window.location.origin}/register?agent=${nl.value.agent_code}`)},jl=async()=>{try{await navigator.clipboard.writeText(cl.value),N.success("推广链接已复制到剪贴板")}catch(e){N.error("复制失败，请手动复制")}},Vl=async()=>{try{al.value=!0,await v();await X.toCanvas(sl.value,cl.value,{width:200,margin:2})}catch(e){N.error("生成二维码失败")}},ql=()=>{const e=sl.value.querySelector("canvas");if(e){const l=document.createElement("a");l.download=`推广二维码-${nl.value.agent_code}.png`,l.href=e.toDataURL(),l.click()}},Dl=()=>{kl(),N.success("动态已刷新")},Fl=()=>{el.push("/distributor/promotion-links")},Al=()=>{el.push("/agent/hierarchy")},Ul=()=>{el.push("/agent/commission")},El=()=>{el.push("/agent/training")},zl=()=>{el.push("/agent/performance")},Ml=()=>{el.push("/agent/settings")},Sl=e=>new Date(e).toLocaleString("zh-CN"),Ll=e=>({commission:"Money",user:"User",team:"Connection"}[e]||"InfoFilled");return t(()=>{(async()=>{try{const e=await O.getMy();nl.value=e.data,xl()}catch(e){N.error("加载代理商信息失败")}})(),(async()=>{try{const e=await O.getMyStats();il.value=e.data}catch(e){N.error("加载统计数据失败")}})(),Cl(),(async()=>{try{ul.value={direct_children:12,total_children:45,active_members:38},ol.value=[{id:1,name:"张三",avatar:"",agent_type:"individual",agent_type_text:"个人代理",created_at:new Date},{id:2,name:"李四",avatar:"",agent_type:"enterprise",agent_type_text:"企业代理",created_at:new Date(Date.now()-864e5)}]}catch(e){N.error("加载团队数据失败")}})(),kl()}),(e,l)=>{const a=h,t=g,v=y,N=w,O=C,X=F,el=U,kl=A,xl=E,Ql=z,Rl=S,$l=Q,Tl=L,Hl=T,Il=$,Nl=R,Yl=I,Zl=H;return n(),s("div",B,[i("div",G,[l[9]||(l[9]=i("div",{class:"header-left"},[i("h2",null,"代理商工作台"),i("p",{class:"page-description"},"管理您的推广业务，跟踪团队业绩，提升佣金收入")],-1)),i("div",J,[i("div",P,[u(a,{type:"primary"},{default:o(()=>[r(f(nl.value.agent_level_text),1)]),_:1}),u(a,{type:"success"},{default:o(()=>[r(f(nl.value.agent_type_text),1)]),_:1}),i("span",W,"编码: "+f(nl.value.agent_code),1)]),i("div",ee,[u(v,{type:"info",onClick:l[0]||(l[0]=e=>tl.value=!0)},{default:o(()=>[u(t,null,{default:o(()=>[u(c(b))]),_:1}),l[8]||(l[8]=r(" 功能说明 ",-1))]),_:1,__:[8]})])])]),u(O,{gutter:20,class:"stats-row"},{default:o(()=>[u(N,{span:6},{default:o(()=>[u(Y,{title:"推广用户数",value:il.value.total_users||0,icon:"User",color:"#409EFF",trend:il.value.user_growth_rate},null,8,["value","trend"])]),_:1}),u(N,{span:6},{default:o(()=>[u(Y,{title:"总佣金收入",value:il.value.total_commission||0,icon:"Money",color:"#67C23A",prefix:"¥",trend:il.value.commission_growth_rate},null,8,["value","trend"])]),_:1}),u(N,{span:6},{default:o(()=>[u(Y,{title:"本月佣金",value:il.value.monthly_commission||0,icon:"Coin",color:"#E6A23C",prefix:"¥"},null,8,["value"])]),_:1}),u(N,{span:6},{default:o(()=>[u(Y,{title:"下级代理商",value:il.value.child_agents_count||0,icon:"Connection",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),u(X,{class:"quick-actions-card"},{header:o(()=>l[10]||(l[10]=[i("span",null,"快捷操作",-1)])),default:o(()=>[u(O,{gutter:15},{default:o(()=>[u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:Fl},[u(t,{class:"action-icon"},{default:o(()=>[u(c(k))]),_:1}),l[11]||(l[11]=i("span",null,"推广工具",-1))])]),_:1}),u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:Al},[u(t,{class:"action-icon"},{default:o(()=>[u(c(x))]),_:1}),l[12]||(l[12]=i("span",null,"团队管理",-1))])]),_:1}),u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:Ul},[u(t,{class:"action-icon"},{default:o(()=>[u(c(j))]),_:1}),l[13]||(l[13]=i("span",null,"佣金中心",-1))])]),_:1}),u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:El},[u(t,{class:"action-icon"},{default:o(()=>[u(c(V))]),_:1}),l[14]||(l[14]=i("span",null,"培训中心",-1))])]),_:1}),u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:zl},[u(t,{class:"action-icon"},{default:o(()=>[u(c(q))]),_:1}),l[15]||(l[15]=i("span",null,"绩效分析",-1))])]),_:1}),u(N,{span:4},{default:o(()=>[i("div",{class:"action-item",onClick:Ml},[u(t,{class:"action-icon"},{default:o(()=>[u(c(D))]),_:1}),l[16]||(l[16]=i("span",null,"账户设置",-1))])]),_:1})]),_:1})]),_:1}),u(O,{gutter:20,class:"charts-row"},{default:o(()=>[u(N,{span:12},{default:o(()=>[u(X,null,{header:o(()=>[i("div",le,[l[20]||(l[20]=i("span",null,"佣金收入趋势",-1)),u(kl,{modelValue:ll.value,"onUpdate:modelValue":l[1]||(l[1]=e=>ll.value=e),size:"small",onChange:Cl},{default:o(()=>[u(el,{label:"week"},{default:o(()=>l[17]||(l[17]=[r("近7天",-1)])),_:1,__:[17]}),u(el,{label:"month"},{default:o(()=>l[18]||(l[18]=[r("近30天",-1)])),_:1,__:[18]}),u(el,{label:"quarter"},{default:o(()=>l[19]||(l[19]=[r("近3个月",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"])])]),default:o(()=>[u(Z,{data:gl.value,options:yl,height:"300px"},null,8,["data"])]),_:1})]),_:1}),u(N,{span:12},{default:o(()=>[u(X,null,{header:o(()=>l[21]||(l[21]=[i("span",null,"用户来源分析",-1)])),default:o(()=>[u(K,{data:bl.value,options:wl,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),u(O,{gutter:20,class:"info-row"},{default:o(()=>[u(N,{span:12},{default:o(()=>[u(X,null,{header:o(()=>[i("div",ae,[l[23]||(l[23]=i("span",null,"我的团队",-1)),u(v,{size:"small",onClick:Al},{default:o(()=>l[22]||(l[22]=[r("查看全部",-1)])),_:1,__:[22]})])]),default:o(()=>[i("div",te,[i("div",se,[i("div",ne,[l[24]||(l[24]=i("span",{class:"label"},"直属下级:",-1)),i("span",ie,f(ul.value.direct_children||0),1)]),i("div",ue,[l[25]||(l[25]=i("span",{class:"label"},"团队总人数:",-1)),i("span",oe,f(ul.value.total_children||0),1)]),i("div",re,[l[26]||(l[26]=i("span",{class:"label"},"活跃成员:",-1)),i("span",ce,f(ul.value.active_members||0),1)])]),i("div",de,[l[27]||(l[27]=i("h4",null,"最新加入成员",-1)),(n(!0),s(d,null,p(ol.value,e=>{return n(),s("div",{key:e.id,class:"member-item"},[u(xl,{size:32,src:e.avatar},null,8,["src"]),i("div",pe,[i("div",me,f(e.name),1),i("div",_e,f(Sl(e.created_at)),1)]),u(a,{size:"small",type:(l=e.agent_type,{individual:"primary",enterprise:"success",channel:"warning"}[l]||"info")},{default:o(()=>[r(f(e.agent_type_text),1)]),_:2},1032,["type"])]);var l}),128))])])]),_:1})]),_:1}),u(N,{span:12},{default:o(()=>[u(X,null,{header:o(()=>[i("div",ve,[l[29]||(l[29]=i("span",null,"最新动态",-1)),u(v,{size:"small",onClick:Dl},{default:o(()=>l[28]||(l[28]=[r("刷新",-1)])),_:1,__:[28]})])]),default:o(()=>[i("div",fe,[(n(!0),s(d,null,p(rl.value,e=>{return n(),s("div",{key:e.id,class:"activity-item"},[i("div",he,[u(t,{color:(l=e.type,{commission:"#67C23A",user:"#409EFF",team:"#E6A23C"}[l]||"#909399")},{default:o(()=>[(n(),m(_(Ll(e.type))))]),_:2},1032,["color"])]),i("div",ge,[i("div",be,f(e.title),1),i("div",ye,f(e.description),1),i("div",we,f(Sl(e.created_at)),1)])]);var l}),128))])]),_:1})]),_:1})]),_:1}),u(X,{class:"promotion-card"},{header:o(()=>l[30]||(l[30]=[i("span",null,"推广链接快速生成",-1)])),default:o(()=>[u(O,{gutter:20},{default:o(()=>[u(N,{span:16},{default:o(()=>[u(Ql,{modelValue:cl.value,"onUpdate:modelValue":l[2]||(l[2]=e=>cl.value=e),placeholder:"您的专属推广链接",readonly:""},{prepend:o(()=>l[31]||(l[31]=[r("推广链接",-1)])),append:o(()=>[u(v,{onClick:jl},{default:o(()=>[u(t,null,{default:o(()=>[u(c(M))]),_:1}),l[32]||(l[32]=r(" 复制 ",-1))]),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1}),u(N,{span:8},{default:o(()=>[u(v,{type:"primary",onClick:Vl},{default:o(()=>[u(t,null,{default:o(()=>[u(c(D))]),_:1}),l[33]||(l[33]=r(" 生成二维码 ",-1))]),_:1,__:[33]}),u(v,{onClick:Fl},{default:o(()=>[u(t,null,{default:o(()=>[u(c(D))]),_:1}),l[34]||(l[34]=r(" 更多工具 ",-1))]),_:1,__:[34]})]),_:1})]),_:1})]),_:1}),u(Rl,{modelValue:al.value,"onUpdate:modelValue":l[3]||(l[3]=e=>al.value=e),title:"推广二维码",width:"400px"},{default:o(()=>[i("div",Ce,[i("div",{ref_key:"qrCodeRef",ref:sl,class:"qr-code"},null,512),i("div",ke,[l[36]||(l[36]=i("p",null,"扫描二维码或分享链接进行推广",-1)),u(v,{type:"primary",onClick:ql},{default:o(()=>l[35]||(l[35]=[r("下载二维码",-1)])),_:1,__:[35]})])])]),_:1},8,["modelValue"]),u(Rl,{modelValue:tl.value,"onUpdate:modelValue":l[7]||(l[7]=e=>tl.value=e),title:"代理商工作台功能说明",width:"1000px",class:"help-dialog"},{default:o(()=>[i("div",xe,[l[77]||(l[77]=i("div",{class:"help-section"},[i("h3",null,"🎯 功能概述"),i("p",null,"代理商工作台是您管理推广业务的核心平台，提供全面的数据统计、团队管理、佣金跟踪等功能，帮助您高效开展推广业务，最大化收益。")],-1)),i("div",je,[l[43]||(l[43]=i("h3",null,"🚀 核心功能模块",-1)),u(O,{gutter:20},{default:o(()=>[u(N,{span:8},{default:o(()=>[i("div",Ve,[i("div",qe,[u(t,null,{default:o(()=>[u(c(k))]),_:1})]),l[37]||(l[37]=i("div",{class:"feature-content"},[i("h4",null,"推广工具"),i("p",null,"专属推广链接、二维码生成、推广素材管理")],-1))])]),_:1}),u(N,{span:8},{default:o(()=>[i("div",De,[i("div",Fe,[u(t,null,{default:o(()=>[u(c(x))]),_:1})]),l[38]||(l[38]=i("div",{class:"feature-content"},[i("h4",null,"团队管理"),i("p",null,"下级代理商管理、团队业绩统计、层级关系维护")],-1))])]),_:1}),u(N,{span:8},{default:o(()=>[i("div",Ae,[i("div",Ue,[u(t,null,{default:o(()=>[u(c(j))]),_:1})]),l[39]||(l[39]=i("div",{class:"feature-content"},[i("h4",null,"佣金中心"),i("p",null,"佣金收入统计、提现申请、收益明细查询")],-1))])]),_:1}),u(N,{span:8},{default:o(()=>[i("div",Ee,[i("div",ze,[u(t,null,{default:o(()=>[u(c(q))]),_:1})]),l[40]||(l[40]=i("div",{class:"feature-content"},[i("h4",null,"绩效分析"),i("p",null,"推广数据分析、转化率统计、业绩趋势图表")],-1))])]),_:1}),u(N,{span:8},{default:o(()=>[i("div",Me,[i("div",Se,[u(t,null,{default:o(()=>[u(c(V))]),_:1})]),l[41]||(l[41]=i("div",{class:"feature-content"},[i("h4",null,"培训中心"),i("p",null,"推广技巧学习、产品知识培训、营销资料下载")],-1))])]),_:1}),u(N,{span:8},{default:o(()=>[i("div",Le,[i("div",Qe,[u(t,null,{default:o(()=>[u(c(D))]),_:1})]),l[42]||(l[42]=i("div",{class:"feature-content"},[i("h4",null,"账户设置"),i("p",null,"个人信息管理、收款账户设置、通知偏好配置")],-1))])]),_:1})]),_:1})]),i("div",Re,[l[44]||(l[44]=i("h3",null,"🏆 代理商等级体系",-1)),u(Tl,{data:_l.value,style:{width:"100%"}},{default:o(()=>[u($l,{prop:"level",label:"等级",width:"100"},{default:o(({row:e})=>[u(a,{type:e.color},{default:o(()=>[r(f(e.level),1)]),_:2},1032,["type"])]),_:1}),u($l,{prop:"name",label:"等级名称",width:"120"}),u($l,{prop:"requirements",label:"升级条件"}),u($l,{prop:"commission_rate",label:"佣金比例",width:"100"}),u($l,{prop:"benefits",label:"专属权益"})]),_:1},8,["data"])]),i("div",$e,[l[56]||(l[56]=i("h3",null,"💰 佣金计算规则",-1)),i("div",Te,[i("div",He,[l[48]||(l[48]=i("h4",null,"🔸 直推佣金",-1)),i("p",null,[l[45]||(l[45]=r("直接推广用户产生的订单，您可获得 ",-1)),i("strong",null,f(vl.value.direct)+"%",1),l[46]||(l[46]=r(" 的佣金",-1))]),i("div",Ie,[l[47]||(l[47]=i("span",{class:"example-label"},"示例：",-1)),r(" 用户通过您的链接购买100元产品，您获得"+f(vl.value.direct)+"元佣金 ",1)])]),i("div",Ne,[l[52]||(l[52]=i("h4",null,"🔸 团队佣金",-1)),i("p",null,[l[49]||(l[49]=r("下级代理商推广产生的订单，您可获得 ",-1)),i("strong",null,f(vl.value.team)+"%",1),l[50]||(l[50]=r(" 的团队佣金",-1))]),i("div",Ye,[l[51]||(l[51]=i("span",{class:"example-label"},"示例：",-1)),r(" 下级代理推广100元订单，您获得"+f(vl.value.team)+"元团队佣金 ",1)])]),i("div",Ze,[l[55]||(l[55]=i("h4",null,"🔸 层级佣金",-1)),i("p",null,[l[53]||(l[53]=r("支持多层级佣金分配，最多支持 ",-1)),i("strong",null,f(fl.value.max_levels),1),l[54]||(l[54]=r(" 级分佣",-1))]),u(Tl,{data:hl.value,size:"small",style:{"margin-top":"10px"}},{default:o(()=>[u($l,{prop:"level",label:"层级",width:"80"}),u($l,{prop:"rate",label:"佣金比例",width:"100"}),u($l,{prop:"description",label:"说明"})]),_:1},8,["data"])])])]),i("div",Ke,[l[64]||(l[64]=i("h3",null,"📈 推广技巧与建议",-1)),u(Nl,{modelValue:dl.value,"onUpdate:modelValue":l[4]||(l[4]=e=>dl.value=e),type:"card"},{default:o(()=>[u(Il,{label:"推广渠道",name:"channels"},{default:o(()=>[i("div",Oe,[l[58]||(l[58]=i("h4",null,"🌟 推荐推广渠道",-1)),l[59]||(l[59]=i("ul",null,[i("li",null,[i("strong",null,"社交媒体"),r("：微信朋友圈、QQ空间、微博等社交平台分享")]),i("li",null,[i("strong",null,"社群营销"),r("：微信群、QQ群、论坛等社群推广")]),i("li",null,[i("strong",null,"内容营销"),r("：撰写产品评测、使用心得等优质内容")]),i("li",null,[i("strong",null,"线下推广"),r("：朋友推荐、活动宣传等线下渠道")]),i("li",null,[i("strong",null,"短视频平台"),r("：抖音、快手等短视频平台推广")])],-1)),u(Hl,{type:"success",closable:!1,style:{"margin-top":"15px"}},{default:o(()=>l[57]||(l[57]=[r(" 💡 建议：多渠道组合推广，提高覆盖面和转化率 ",-1)])),_:1,__:[57]})])]),_:1}),u(Il,{label:"推广话术",name:"scripts"},{default:o(()=>l[60]||(l[60]=[i("div",{class:"tips-content"},[i("h4",null,"💬 推广话术模板"),i("div",{class:"script-item"},[i("h5",null,"朋友圈推广"),i("div",{class:"script-text"},[r(' "发现一个不错的平台，可以通过推广赚取佣金💰'),i("br"),r(" 产品质量有保障，佣金结算及时✅"),i("br"),r(" 感兴趣的朋友可以了解一下👇"),i("br"),r(' [推广链接]" ')])]),i("div",{class:"script-item"},[i("h5",null,"私聊推广"),i("div",{class:"script-text"},[r(' "Hi，最近在做一个项目，产品不错，佣金也挺可观的。'),i("br"),r(" 如果你有兴趣了解或者想要产品的话，可以通过我的链接购买，"),i("br"),r(" 这样我也能获得一些佣金收入😊"),i("br"),r(' 链接：[推广链接]" ')])])],-1)])),_:1,__:[60]}),u(Il,{label:"注意事项",name:"notes"},{default:o(()=>[i("div",Xe,[l[62]||(l[62]=i("h4",null,"⚠️ 推广注意事项",-1)),l[63]||(l[63]=i("ul",null,[i("li",null,[i("strong",null,"诚信推广"),r("：如实介绍产品特点，不夸大宣传")]),i("li",null,[i("strong",null,"合规操作"),r("：遵守平台规则，不进行违规推广")]),i("li",null,[i("strong",null,"用户体验"),r("：关注用户反馈，提供优质服务")]),i("li",null,[i("strong",null,"持续学习"),r("：关注产品更新，学习推广技巧")]),i("li",null,[i("strong",null,"数据分析"),r("：定期分析推广数据，优化推广策略")])],-1)),u(Hl,{type:"warning",closable:!1,style:{"margin-top":"15px"}},{default:o(()=>l[61]||(l[61]=[r(" ⚠️ 警告：严禁虚假宣传、恶意刷单等违规行为，一经发现将取消代理资格 ",-1)])),_:1,__:[61]})])]),_:1})]),_:1},8,["modelValue"])]),i("div",Be,[l[71]||(l[71]=i("h3",null,"📝 操作指南",-1)),u(Zl,{modelValue:pl.value,"onUpdate:modelValue":l[5]||(l[5]=e=>pl.value=e)},{default:o(()=>[u(Yl,{title:"如何生成推广链接？",name:"link-generation"},{default:o(()=>[i("div",Ge,[l[66]||(l[66]=i("ol",null,[i("li",null,'在工作台下方找到"推广链接快速生成"区域'),i("li",null,"系统会自动生成您的专属推广链接"),i("li",null,'点击"复制"按钮复制链接到剪贴板'),i("li",null,'也可以点击"生成二维码"创建推广二维码'),i("li",null,"将链接或二维码分享给潜在用户")],-1)),u(Hl,{type:"info",closable:!1},{default:o(()=>l[65]||(l[65]=[r(" 💡 提示：推广链接包含您的专属代理编码，用户通过此链接注册购买，您将获得相应佣金 ",-1)])),_:1,__:[65]})])]),_:1}),u(Yl,{title:"如何查看佣金收入？",name:"commission-check"},{default:o(()=>[i("div",Je,[l[68]||(l[68]=i("ol",null,[i("li",null,"在统计卡片中查看总佣金收入和本月佣金"),i("li",null,'点击"佣金中心"查看详细的佣金明细'),i("li",null,"在佣金收入趋势图中查看收入变化"),i("li",null,"可以按时间段筛选查看不同期间的收入")],-1)),u(Hl,{type:"success",closable:!1},{default:o(()=>l[67]||(l[67]=[r(" ✅ 说明：佣金每日结算，T+1到账，可在佣金中心申请提现 ",-1)])),_:1,__:[67]})])]),_:1}),u(Yl,{title:"如何管理我的团队？",name:"team-management"},{default:o(()=>[i("div",Pe,[l[70]||(l[70]=i("ol",null,[i("li",null,'在"我的团队"卡片中查看团队概况'),i("li",null,'点击"团队管理"进入详细的团队管理页面'),i("li",null,"可以查看下级代理商的业绩和状态"),i("li",null,"为团队成员提供培训和指导"),i("li",null,"关注团队成员的推广数据和收益情况")],-1)),u(Hl,{type:"info",closable:!1},{default:o(()=>l[69]||(l[69]=[r(" 💡 建议：定期与团队成员沟通，分享推广经验，共同提升业绩 ",-1)])),_:1,__:[69]})])]),_:1})]),_:1},8,["modelValue"])]),i("div",We,[l[76]||(l[76]=i("h3",null,"❓ 常见问题",-1)),u(Zl,{modelValue:ml.value,"onUpdate:modelValue":l[6]||(l[6]=e=>ml.value=e)},{default:o(()=>[u(Yl,{title:"佣金什么时候到账？",name:"faq1"},{default:o(()=>l[72]||(l[72]=[i("p",null,"佣金采用T+1结算模式，即今日产生的佣金将在明日到账。您可以在佣金中心查看详细的结算记录。",-1)])),_:1,__:[72]}),u(Yl,{title:"如何提升代理商等级？",name:"faq2"},{default:o(()=>l[73]||(l[73]=[i("p",null,"代理商等级根据您的推广业绩自动评定，包括推广用户数、团队规模、佣金收入等指标。持续推广并发展团队即可提升等级。",-1)])),_:1,__:[73]}),u(Yl,{title:"推广链接有有效期吗？",name:"faq3"},{default:o(()=>l[74]||(l[74]=[i("p",null,"推广链接长期有效，但建议定期更新推广素材。如果您的代理资格发生变化，系统会自动更新链接状态。",-1)])),_:1,__:[74]}),u(Yl,{title:"可以同时推广多个产品吗？",name:"faq4"},{default:o(()=>l[75]||(l[75]=[i("p",null,"可以的。您可以推广平台上的所有产品，每个产品的佣金比例可能不同，具体以产品页面显示为准。",-1)])),_:1,__:[75]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-8e2685b6"]]);export{el as default};
