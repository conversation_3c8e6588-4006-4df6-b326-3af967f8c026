<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

/**
 * 文件上传控制器
 */
class UploadController extends Controller
{
    /**
     * 允许的文件类型配置
     */
    private array $allowedTypes = [
        'image' => [
            'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'mimes' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'max_size' => 5120, // 5MB
            'dimensions' => [
                'max_width' => 2048,
                'max_height' => 2048
            ]
        ],
        'video' => [
            'extensions' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
            'mimes' => ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv'],
            'max_size' => 51200, // 50MB
        ],
        'audio' => [
            'extensions' => ['mp3', 'wav', 'aac', 'm4a'],
            'mimes' => ['audio/mpeg', 'audio/wav', 'audio/aac', 'audio/mp4'],
            'max_size' => 10240, // 10MB
        ],
        'document' => [
            'extensions' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
            'mimes' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            ],
            'max_size' => 20480, // 20MB
        ]
    ];

    /**
     * 通用文件上传
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file',
                'type' => 'required|string|in:image,video,audio,document',
                'category' => 'nullable|string|in:avatar,cover,qr_code,media,document',
                'resize' => 'nullable|boolean',
                'width' => 'nullable|integer|min:50|max:2048',
                'height' => 'nullable|integer|min:50|max:2048',
                'quality' => 'nullable|integer|min:10|max:100'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 400, $validator->errors());
            }

            $file = $request->file('file');
            $type = $request->input('type', 'image');
            $category = $request->input('category', 'media');

            // 验证文件类型和大小
            $validation = $this->validateFile($file, $type);
            if (!$validation['valid']) {
                return $this->error($validation['message'], 400);
            }

            // 生成文件路径和名称
            $path = $this->generateFilePath($type, $category);
            $filename = $this->generateFilename($file);
            $fullPath = $path . '/' . $filename;

            // 处理图片文件
            if ($type === 'image') {
                $result = $this->handleImageUpload($file, $fullPath, $request);
            } else {
                $result = $this->handleFileUpload($file, $fullPath);
            }

            if (!$result['success']) {
                return $this->error($result['message'], 500);
            }

            // 记录上传日志
            $this->logUpload($file, $fullPath, $type, $category);

            return $this->success([
                'url' => Storage::url($fullPath),
                'path' => $fullPath,
                'name' => $filename,
                'size' => $file->getSize(),
                'type' => $type,
                'category' => $category,
                'mime_type' => $file->getMimeType(),
                'original_name' => $file->getClientOriginalName()
            ], '文件上传成功');

        } catch (\Exception $e) {
            \Log::error('文件上传失败', [
                'error' => $e->getMessage(),
                'file' => $request->file('file')?->getClientOriginalName(),
                'user_id' => auth()->id()
            ]);

            return $this->error('文件上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量文件上传
     */
    public function batchUpload(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'files' => 'required|array|max:10',
                'files.*' => 'required|file',
                'type' => 'required|string|in:image,video,audio,document',
                'category' => 'nullable|string|in:avatar,cover,qr_code,media,document'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 400, $validator->errors());
            }

            $files = $request->file('files');
            $type = $request->input('type', 'image');
            $category = $request->input('category', 'media');
            $results = [];
            $errors = [];

            foreach ($files as $index => $file) {
                try {
                    // 验证文件
                    $validation = $this->validateFile($file, $type);
                    if (!$validation['valid']) {
                        $errors[] = [
                            'index' => $index,
                            'filename' => $file->getClientOriginalName(),
                            'error' => $validation['message']
                        ];
                        continue;
                    }

                    // 上传文件
                    $path = $this->generateFilePath($type, $category);
                    $filename = $this->generateFilename($file);
                    $fullPath = $path . '/' . $filename;

                    if ($type === 'image') {
                        $result = $this->handleImageUpload($file, $fullPath, $request);
                    } else {
                        $result = $this->handleFileUpload($file, $fullPath);
                    }

                    if ($result['success']) {
                        $results[] = [
                            'index' => $index,
                            'url' => Storage::url($fullPath),
                            'path' => $fullPath,
                            'name' => $filename,
                            'size' => $file->getSize(),
                            'original_name' => $file->getClientOriginalName()
                        ];

                        $this->logUpload($file, $fullPath, $type, $category);
                    } else {
                        $errors[] = [
                            'index' => $index,
                            'filename' => $file->getClientOriginalName(),
                            'error' => $result['message']
                        ];
                    }

                } catch (\Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'filename' => $file->getClientOriginalName(),
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $this->success([
                'uploaded' => $results,
                'errors' => $errors,
                'total' => count($files),
                'success_count' => count($results),
                'error_count' => count($errors)
            ], '批量上传完成');

        } catch (\Exception $e) {
            return $this->error('批量上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除文件
     */
    public function delete(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'path' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 400, $validator->errors());
            }

            $path = $request->input('path');

            // 安全检查：确保路径在允许的目录内
            if (!$this->isPathSafe($path)) {
                return $this->error('非法的文件路径', 403);
            }

            if (!Storage::exists($path)) {
                return $this->error('文件不存在', 404);
            }

            $deleted = Storage::delete($path);

            if ($deleted) {
                // 记录删除日志
                \Log::info('文件删除成功', [
                    'path' => $path,
                    'user_id' => auth()->id()
                ]);

                return $this->success(null, '文件删除成功');
            } else {
                return $this->error('文件删除失败', 500);
            }

        } catch (\Exception $e) {
            \Log::error('文件删除失败', [
                'error' => $e->getMessage(),
                'path' => $request->input('path'),
                'user_id' => auth()->id()
            ]);

            return $this->error('文件删除失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取文件信息
     */
    public function info(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'path' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 400, $validator->errors());
            }

            $path = $request->input('path');

            if (!Storage::exists($path)) {
                return $this->error('文件不存在', 404);
            }

            $info = [
                'path' => $path,
                'url' => Storage::url($path),
                'size' => Storage::size($path),
                'last_modified' => Storage::lastModified($path),
                'mime_type' => Storage::mimeType($path),
                'exists' => true
            ];

            // 如果是图片，获取图片信息
            if (strpos($info['mime_type'], 'image/') === 0) {
                try {
                    $fullPath = Storage::path($path);
                    $imageInfo = getimagesize($fullPath);
                    if ($imageInfo) {
                        $info['width'] = $imageInfo[0];
                        $info['height'] = $imageInfo[1];
                        $info['type'] = 'image';
                    }
                } catch (\Exception $e) {
                    // 忽略图片信息获取错误
                }
            }

            return $this->success($info, '获取文件信息成功');

        } catch (\Exception $e) {
            return $this->error('获取文件信息失败：' . $e->getMessage(), 500);
        }
    }

    // 私有方法

    /**
     * 验证文件
     */
    private function validateFile($file, string $type): array
    {
        if (!isset($this->allowedTypes[$type])) {
            return ['valid' => false, 'message' => '不支持的文件类型'];
        }

        $config = $this->allowedTypes[$type];
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();
        $size = $file->getSize() / 1024; // 转换为KB

        // 检查扩展名
        if (!in_array($extension, $config['extensions'])) {
            return ['valid' => false, 'message' => '不支持的文件扩展名'];
        }

        // 检查MIME类型
        if (!in_array($mimeType, $config['mimes'])) {
            return ['valid' => false, 'message' => '不支持的文件格式'];
        }

        // 检查文件大小
        if ($size > $config['max_size']) {
            return ['valid' => false, 'message' => '文件大小超出限制'];
        }

        // 检查图片尺寸
        if ($type === 'image' && isset($config['dimensions'])) {
            try {
                $imageInfo = getimagesize($file->getPathname());
                if ($imageInfo) {
                    $width = $imageInfo[0];
                    $height = $imageInfo[1];
                    
                    if ($width > $config['dimensions']['max_width'] || 
                        $height > $config['dimensions']['max_height']) {
                        return ['valid' => false, 'message' => '图片尺寸超出限制'];
                    }
                }
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => '无法读取图片信息'];
            }
        }

        return ['valid' => true, 'message' => '验证通过'];
    }

    /**
     * 生成文件路径
     */
    private function generateFilePath(string $type, string $category): string
    {
        $date = date('Y/m/d');
        return "uploads/{$type}/{$category}/{$date}";
    }

    /**
     * 生成文件名
     */
    private function generateFilename($file): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = Str::random(32);
        return $hash . '.' . $extension;
    }

    /**
     * 处理图片上传
     */
    private function handleImageUpload($file, string $path, Request $request): array
    {
        try {
            $resize = $request->boolean('resize', false);
            $width = $request->input('width');
            $height = $request->input('height');
            $quality = $request->input('quality', 90);

            if ($resize && ($width || $height)) {
                // 使用Intervention Image处理图片
                $image = Image::make($file);
                
                if ($width && $height) {
                    $image->resize($width, $height, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                } elseif ($width) {
                    $image->widen($width, function ($constraint) {
                        $constraint->upsize();
                    });
                } elseif ($height) {
                    $image->heighten($height, function ($constraint) {
                        $constraint->upsize();
                    });
                }

                // 保存处理后的图片
                $saved = Storage::put($path, $image->encode(null, $quality)->__toString());
            } else {
                // 直接保存原图
                $saved = Storage::putFileAs(
                    dirname($path),
                    $file,
                    basename($path)
                );
            }

            return [
                'success' => $saved !== false,
                'message' => $saved ? '图片上传成功' : '图片上传失败'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '图片处理失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理普通文件上传
     */
    private function handleFileUpload($file, string $path): array
    {
        try {
            $saved = Storage::putFileAs(
                dirname($path),
                $file,
                basename($path)
            );

            return [
                'success' => $saved !== false,
                'message' => $saved ? '文件上传成功' : '文件上传失败'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '文件上传失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查路径安全性
     */
    private function isPathSafe(string $path): bool
    {
        // 检查路径是否在uploads目录内
        return strpos($path, 'uploads/') === 0 && 
               !strpos($path, '..') && 
               !strpos($path, '//');
    }

    /**
     * 记录上传日志
     */
    private function logUpload($file, string $path, string $type, string $category): void
    {
        \Log::info('文件上传成功', [
            'original_name' => $file->getClientOriginalName(),
            'path' => $path,
            'size' => $file->getSize(),
            'type' => $type,
            'category' => $category,
            'mime_type' => $file->getMimeType(),
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}