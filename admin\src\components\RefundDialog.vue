<template>
  <el-dialog
    v-model="visible"
    title="订单退款"
    width="600px"
    :before-close="handleClose"
  >
    <div class="refund-form">
      <el-form :model="refundForm" :rules="rules" ref="refundFormRef" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="orderData.order_no" disabled />
        </el-form-item>
        
        <el-form-item label="订单金额">
          <el-input v-model="orderData.amount" disabled>
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="退款金额" prop="refund_amount">
          <el-input-number
            v-model="refundForm.refund_amount"
            :min="0.01"
            :max="orderData.amount"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="退款原因" prop="refund_reason">
          <el-select v-model="refundForm.refund_reason" placeholder="请选择退款原因" style="width: 100%">
            <el-option label="用户申请退款" value="user_request" />
            <el-option label="商品缺货" value="out_of_stock" />
            <el-option label="系统错误" value="system_error" />
            <el-option label="重复支付" value="duplicate_payment" />
            <el-option label="其他原因" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="退款说明" prop="refund_note">
          <el-input
            v-model="refundForm.refund_note"
            type="textarea"
            :rows="4"
            placeholder="请输入退款说明（可选）"
          />
        </el-form-item>
        
        <el-form-item label="退款方式">
          <el-radio-group v-model="refundForm.refund_method">
            <el-radio label="original">原路退回</el-radio>
            <el-radio label="manual">人工处理</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <div class="refund-info">
        <el-alert
          title="退款说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>退款将在1-3个工作日内到账</li>
              <li>原路退回：退款将返回到原支付账户</li>
              <li>人工处理：需要手动处理退款流程</li>
              <li>退款成功后将自动发送通知给用户</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认退款
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const refundFormRef = ref()
const loading = ref(false)

const refundForm = reactive({
  refund_amount: 0,
  refund_reason: '',
  refund_note: '',
  refund_method: 'original'
})

const rules = {
  refund_amount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '退款金额必须大于0.01', trigger: 'blur' }
  ],
  refund_reason: [
    { required: true, message: '请选择退款原因', trigger: 'change' }
  ]
}

// 监听订单数据变化，初始化退款金额
watch(() => props.orderData, (newData) => {
  if (newData && newData.amount) {
    refundForm.refund_amount = newData.amount
  }
}, { immediate: true })

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  if (refundFormRef.value) {
    refundFormRef.value.resetFields()
  }
  Object.assign(refundForm, {
    refund_amount: 0,
    refund_reason: '',
    refund_note: '',
    refund_method: 'original'
  })
}

const handleSubmit = async () => {
  if (!refundFormRef.value) return
  
  try {
    await refundFormRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('退款申请提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('退款失败:', error)
    ElMessage.error('退款申请提交失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.refund-form {
  padding: 20px 0;
}

.refund-info {
  margin-top: 20px;
}

.refund-info ul {
  margin: 0;
  padding-left: 20px;
}

.refund-info li {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>