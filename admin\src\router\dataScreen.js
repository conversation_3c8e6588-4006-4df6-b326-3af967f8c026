// 数据大屏路由配置
export const dataScreenRoutes = [
  {
    path: '/data-screen',
    name: 'DataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: '数据大屏',
      icon: 'Monitor',
      requiresAuth: false, // 暂时禁用认证
      fullscreen: true
    }
  },
  {
    path: '/data-screen/ultra',
    name: 'UltraDataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: 'Ultra 数据大屏',
      fullscreen: true,
      requiresAuth: true
    }
  },
  {
    path: '/data-screen/enhanced',
    name: 'EnhancedDataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: '增强数据大屏',
      fullscreen: true,
      requiresAuth: true
    }
  },
  {
    path: '/data-screen/classic',
    name: 'ClassicDataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: '经典数据大屏',
      fullscreen: true,
      requiresAuth: false
    }
  },
  {
    path: '/data-screen/simple',
    name: 'SimpleDataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: '简约数据大屏',
      fullscreen: true,
      requiresAuth: true
    }
  },
  {
    path: '/data-screen/fullscreen',
    name: 'DataScreenFullscreen',
    component: () => import('@/views/dashboard/DataScreenFullscreen.vue'),
    meta: {
      title: '全屏数据大屏',
      fullscreen: true,
      requiresAuth: true
    }
  },
  {
    path: '/data-screen/optimized',
    name: 'OptimizedDataScreen',
    component: () => import('@/views/dashboard/DataScreen.vue'),
    meta: {
      title: '优化数据大屏',
      fullscreen: true,
      requiresAuth: true
    }
  },
  {
    path: '/data-screen/test-suite',
    name: 'ScreenTestSuite',
    component: () => import('@/views/dashboard/ScreenTestSuite.vue'),
    meta: {
      title: '显示测试套件',
      requiresAuth: true,
      roles: ['admin']
    }
  }
]

// 导航菜单配置
export const dataScreenMenus = [
  {
    path: '/data-screen',
    name: 'DataScreen',
    meta: {
      title: '数据大屏',
      icon: 'Monitor',
      order: 2
    },
    children: [
      {
        path: '/data-screen',
        name: 'DataScreenDemo',
        meta: {
          title: '大屏演示',
          icon: 'TrendCharts'
        }
      },
      {
        path: '/data-screen/ultra',
        name: 'UltraDataScreen',
        meta: {
          title: 'Ultra版本',
          icon: 'Lightning'
        }
      },
      {
        path: '/data-screen/enhanced',
        name: 'EnhancedDataScreen',
        meta: {
          title: '增强版本',
          icon: 'Star'
        }
      },
      {
        path: '/data-screen/classic',
        name: 'ClassicDataScreen',
        meta: {
          title: '经典版本',
          icon: 'Grid'
        }
      },
      {
        path: '/data-screen/optimized',
        name: 'OptimizedDataScreen',
        meta: {
          title: '优化版本',
          icon: 'Monitor'
        }
      },
      {
        path: '/data-screen/test-suite',
        name: 'ScreenTestSuite',
        meta: {
          title: '显示测试',
          icon: 'Setting'
        }
      }
    ]
  }
]

export default {
  routes: dataScreenRoutes,
  menus: dataScreenMenus
}