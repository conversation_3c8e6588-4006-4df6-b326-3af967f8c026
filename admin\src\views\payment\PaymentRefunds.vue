<template>
  <div class="payment-refunds">
    <PageLayout title="退款管理" subtitle="管理支付退款申请和处理">
      <!-- 搜索筛选 -->
      <div class="filter-section">
        <el-card class="filter-card">
          <el-form :model="searchForm" inline>
            <el-form-item label="退款单号">
              <el-input v-model="searchForm.refund_no" placeholder="请输入退款单号" clearable />
            </el-form-item>
            <el-form-item label="订单号">
              <el-input v-model="searchForm.order_no" placeholder="请输入订单号" clearable />
            </el-form-item>
            <el-form-item label="退款状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="待处理" value="pending" />
                <el-option label="处理中" value="processing" />
                <el-option label="已完成" value="completed" />
                <el-option label="已拒绝" value="rejected" />
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="searchForm.date_range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 退款列表 -->
      <div class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <h3>退款列表</h3>
              <div class="header-actions">
                <el-button @click="exportRefunds" :loading="exporting">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="refundList" v-loading="loading" stripe>
            <el-table-column prop="refund_no" label="退款单号" width="180" />
            <el-table-column prop="order_no" label="订单号" width="180" />
            <el-table-column prop="amount" label="退款金额" width="120">
              <template #default="{ row }">
                <span class="amount">¥{{ row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="退款原因" width="200" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="payment_method" label="支付方式" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getPaymentMethodText(row.payment_method) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="applicant" label="申请人" width="120" />
            <el-table-column prop="created_at" label="申请时间" width="160" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewRefund(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'" 
                  size="small" 
                  type="success" 
                  @click="approveRefund(row)"
                >
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'" 
                  size="small" 
                  type="danger" 
                  @click="rejectRefund(row)"
                >
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </PageLayout>

    <!-- 退款详情对话框 -->
    <el-dialog v-model="detailVisible" title="退款详情" width="800px">
      <div v-if="currentRefund" class="refund-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="退款单号">{{ currentRefund.refund_no }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ currentRefund.order_no }}</el-descriptions-item>
          <el-descriptions-item label="退款金额">
            <span class="amount">¥{{ currentRefund.amount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ getPaymentMethodText(currentRefund.payment_method) }}
          </el-descriptions-item>
          <el-descriptions-item label="退款状态">
            <el-tag :type="getStatusType(currentRefund.status)">
              {{ getStatusText(currentRefund.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentRefund.applicant }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ currentRefund.created_at }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentRefund.processed_at || '未处理' }}</el-descriptions-item>
        </el-descriptions>

        <div class="refund-reason">
          <h4>退款原因</h4>
          <p>{{ currentRefund.reason }}</p>
        </div>

        <div v-if="currentRefund.remark" class="refund-remark">
          <h4>处理备注</h4>
          <p>{{ currentRefund.remark }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 处理退款对话框 -->
    <el-dialog v-model="processVisible" :title="processType === 'approve' ? '通过退款' : '拒绝退款'" width="500px">
      <el-form :model="processForm" label-width="80px">
        <el-form-item label="处理备注" required>
          <el-input
            v-model="processForm.remark"
            type="textarea"
            :rows="4"
            :placeholder="processType === 'approve' ? '请输入通过原因' : '请输入拒绝原因'"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="processVisible = false">取消</el-button>
        <el-button 
          :type="processType === 'approve' ? 'success' : 'danger'" 
          @click="confirmProcess"
          :loading="processing"
        >
          确认{{ processType === 'approve' ? '通过' : '拒绝' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Download, View, Check, Close
} from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import { getRefunds, processRefund, exportRefundData } from '@/api/payment'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const processing = ref(false)
const detailVisible = ref(false)
const processVisible = ref(false)
const processType = ref('approve') // approve | reject
const currentRefund = ref(null)

const searchForm = reactive({
  refund_no: '',
  order_no: '',
  status: '',
  date_range: []
})

const processForm = reactive({
  remark: ''
})

const refundList = ref([])
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法
const loadRefunds = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await getRefunds(params)
    refundList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    // 使用模拟数据
    refundList.value = [
      {
        id: 1,
        refund_no: 'RF202401010001',
        order_no: 'ORD202401010001',
        amount: 99.00,
        reason: '商品质量问题',
        status: 'pending',
        payment_method: 'alipay',
        applicant: '张三',
        created_at: '2024-01-01 10:00:00',
        processed_at: null,
        remark: ''
      },
      {
        id: 2,
        refund_no: 'RF202401010002',
        order_no: 'ORD202401010002',
        amount: 199.00,
        reason: '不喜欢商品',
        status: 'completed',
        payment_method: 'wechat',
        applicant: '李四',
        created_at: '2024-01-01 11:00:00',
        processed_at: '2024-01-01 12:00:00',
        remark: '已处理完成'
      }
    ]
    pagination.total = 2
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadRefunds()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    refund_no: '',
    order_no: '',
    status: '',
    date_range: []
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadRefunds()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadRefunds()
}

const viewRefund = (refund) => {
  currentRefund.value = refund
  detailVisible.value = true
}

const approveRefund = (refund) => {
  currentRefund.value = refund
  processType.value = 'approve'
  processForm.remark = ''
  processVisible.value = true
}

const rejectRefund = (refund) => {
  currentRefund.value = refund
  processType.value = 'reject'
  processForm.remark = ''
  processVisible.value = true
}

const confirmProcess = async () => {
  if (!processForm.remark.trim()) {
    ElMessage.warning('请输入处理备注')
    return
  }

  processing.value = true
  try {
    await processRefund(currentRefund.value.id, {
      action: processType.value,
      remark: processForm.remark
    })
    
    ElMessage.success(`退款${processType.value === 'approve' ? '通过' : '拒绝'}成功`)
    processVisible.value = false
    loadRefunds()
  } catch (error) {
    ElMessage.error('处理失败：' + error.message)
  } finally {
    processing.value = false
  }
}

const exportRefunds = async () => {
  exporting.value = true
  try {
    await exportRefundData(searchForm)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  } finally {
    exporting.value = false
  }
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

const getPaymentMethodText = (method) => {
  const texts = {
    alipay: '支付宝',
    wechat: '微信支付',
    easypay: '易支付',
    bank: '银行卡'
  }
  return texts[method] || '未知'
}

// 生命周期
onMounted(() => {
  loadRefunds()
})
</script>

<style lang="scss" scoped>
.payment-refunds {
  .filter-section {
    margin-bottom: 20px;

    .filter-card {
      .el-form {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  .table-section {
    .table-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }
      }

      .amount {
        font-weight: 600;
        color: #f56c6c;
      }

      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }

  .refund-detail {
    .refund-reason,
    .refund-remark {
      margin-top: 20px;

      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      p {
        margin: 0;
        padding: 12px;
        background: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-refunds {
    .filter-section {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .table-section {
      .card-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }
    }
  }
}
</style>