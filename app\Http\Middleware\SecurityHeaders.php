<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * 安全HTTP头中间件
 * 添加各种安全相关的HTTP响应头
 */
class SecurityHeaders
{
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // 基本安全头
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // HSTS (HTTPS Strict Transport Security)
        if (config('app.env') === 'production') {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Content Security Policy
        $csp = $this->getContentSecurityPolicy($request);
        $response->headers->set('Content-Security-Policy', $csp);

        // 移除敏感的响应头
        $response->headers->remove('X-Powered-By');
        
        return $response;
    }

    /**
     * 获取内容安全策略
     */
    private function getContentSecurityPolicy(Request $request): string
    {
        $directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.google.com *.googleapis.com *.gstatic.com",
            "style-src 'self' 'unsafe-inline' *.googleapis.com *.gstatic.com",
            "img-src 'self' data: *.gravatar.com *.googleapis.com *.gstatic.com",
            "font-src 'self' *.googleapis.com *.gstatic.com",
            "connect-src 'self' *.googleapis.com localhost:* ws: wss:",
            "media-src 'self'",
            "object-src 'none'",
            "child-src 'none'",
            "worker-src 'self'",
            "frame-ancestors 'self'",
            "base-uri 'self'",
            "form-action 'self'",
        ];

        return implode('; ', $directives);
    }
}