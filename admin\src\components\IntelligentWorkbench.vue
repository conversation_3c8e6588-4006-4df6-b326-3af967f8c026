<template>
  <div class="intelligent-workbench">
    <!-- 个性化工作台头部 -->
    <div class="workbench-header">
      <div class="user-greeting">
        <div class="greeting-content">
          <h2 class="greeting-title">
            {{ getGreetingMessage() }}，{{ currentUser?.name || '用户' }}！
          </h2>
          <p class="greeting-subtitle">
            {{ workbenchConfig.description }}
          </p>
        </div>
        <div class="user-avatar">
          <el-avatar :size="60" :src="currentUser?.avatar">
            {{ currentUser?.name?.charAt(0) || 'U' }}
          </el-avatar>
          <div class="user-status">
            <el-badge :value="unreadNotifications" :max="99" class="notification-badge">
              <el-icon><Bell /></el-icon>
            </el-badge>
          </div>
        </div>
      </div>
      
      <!-- AI推荐横幅 -->
      <div class="ai-recommendation-banner" v-if="aiRecommendations.length > 0">
        <div class="banner-icon">
          <el-icon><MagicStick /></el-icon>
        </div>
        <div class="banner-content">
          <h4>🤖 AI智能推荐</h4>
          <p>{{ aiRecommendations[0].message || '基于您的使用习惯，为您推荐相关功能' }}</p>
        </div>
        <el-button type="primary" size="small" @click="handleAIRecommendation(aiRecommendations[0])">
          立即体验
        </el-button>
      </div>
    </div>

    <!-- 个性化快速操作区 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <h3>⚡ 快速操作</h3>
        <el-button text @click="customizeQuickActions">
          <el-icon><Setting /></el-icon>
          自定义
        </el-button>
      </div>
      
      <div class="quick-actions-grid">
        <div 
          v-for="action in personalizedQuickActions" 
          :key="action.id"
          class="quick-action-card"
          :class="{ 'protected-action': action.protected }"
          @click="handleQuickAction(action)"
        >
          <div class="action-icon">
            <el-icon :class="action.iconClass">
              <component :is="action.icon" />
            </el-icon>
          </div>
          <div class="action-content">
            <h4>{{ action.title }}</h4>
            <p>{{ action.description }}</p>
            <div class="action-stats" v-if="action.stats">
              <span class="stat-item">
                <el-icon><TrendCharts /></el-icon>
                {{ action.stats.usage }}次使用
              </span>
            </div>
          </div>
          <div class="action-badge" v-if="action.protected">
            <el-tag type="warning" size="small">核心功能</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 个性化仪表板组件 -->
    <div class="dashboard-widgets-section">
      <div class="section-header">
        <h3>📊 个人仪表板</h3>
        <el-button text @click="customizeDashboard">
          <el-icon><Grid /></el-icon>
          布局设置
        </el-button>
      </div>
      
      <div class="widgets-grid" ref="widgetsContainer">
        <div 
          v-for="widget in personalizedWidgets" 
          :key="widget.id"
          class="widget-card"
          :class="`widget-${widget.size}`"
        >
          <div class="widget-header">
            <h4>{{ widget.title }}</h4>
            <el-dropdown @command="handleWidgetAction">
              <el-icon class="widget-menu"><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`refresh-${widget.id}`">刷新</el-dropdown-item>
                  <el-dropdown-item :command="`settings-${widget.id}`">设置</el-dropdown-item>
                  <el-dropdown-item :command="`remove-${widget.id}`" divided>移除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="widget-content">
            <!-- 动态组件渲染 -->
            <component 
              :is="widget.component" 
              :config="widget.config"
              :data="widget.data"
              @update="handleWidgetUpdate"
            />
          </div>
        </div>
        
        <!-- 添加组件按钮 -->
        <div class="add-widget-card" @click="showAddWidgetDialog = true">
          <el-icon class="add-icon"><Plus /></el-icon>
          <p>添加组件</p>
        </div>
      </div>
    </div>

    <!-- 最近活动和智能提醒 -->
    <div class="activity-section">
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="activity-card">
            <div class="card-header">
              <h3>🕒 最近活动</h3>
              <el-button text size="small" @click="viewAllActivities">查看全部</el-button>
            </div>
            <div class="activity-list">
              <div 
                v-for="activity in recentActivities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon :class="activity.iconClass">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <p class="activity-title">{{ activity.title }}</p>
                  <p class="activity-time">{{ formatTime(activity.time) }}</p>
                </div>
                <div class="activity-action" v-if="activity.actionable">
                  <el-button size="small" text @click="handleActivityAction(activity)">
                    {{ activity.actionText }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="reminders-card">
            <div class="card-header">
              <h3>💡 智能提醒</h3>
              <el-button text size="small" @click="customizeReminders">设置</el-button>
            </div>
            <div class="reminders-list">
              <div 
                v-for="reminder in intelligentReminders" 
                :key="reminder.id"
                class="reminder-item"
                :class="`reminder-${reminder.priority}`"
              >
                <div class="reminder-icon">
                  <el-icon><Bell /></el-icon>
                </div>
                <div class="reminder-content">
                  <p class="reminder-title">{{ reminder.title }}</p>
                  <p class="reminder-desc">{{ reminder.description }}</p>
                </div>
                <div class="reminder-actions">
                  <el-button size="small" @click="handleReminder(reminder)">
                    处理
                  </el-button>
                  <el-button size="small" text @click="dismissReminder(reminder.id)">
                    忽略
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 添加组件对话框 -->
    <el-dialog
      v-model="showAddWidgetDialog"
      title="添加仪表板组件"
      width="600px"
      class="add-widget-dialog"
    >
      <div class="widget-gallery">
        <div 
          v-for="widgetType in availableWidgets" 
          :key="widgetType.id"
          class="widget-option"
          @click="addWidget(widgetType)"
        >
          <div class="widget-preview">
            <el-icon><component :is="widgetType.icon" /></el-icon>
          </div>
          <div class="widget-info">
            <h4>{{ widgetType.title }}</h4>
            <p>{{ widgetType.description }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 快速操作自定义对话框 -->
    <el-dialog
      v-model="showCustomizeDialog"
      title="自定义快速操作"
      width="700px"
      class="customize-dialog"
    >
      <div class="customize-content">
        <div class="available-actions">
          <h4>可用操作</h4>
          <div class="actions-list">
            <div 
              v-for="element in availableActions" 
              :key="element.id"
              class="action-item"
              @click="addActionToSelected(element)"
            >
              <el-icon><component :is="element.icon" /></el-icon>
              <span>{{ element.title }}</span>
            </div>
          </div>
        </div>
        
        <div class="selected-actions">
          <h4>已选操作</h4>
          <div class="actions-list">
            <div 
              v-for="element in personalizedQuickActions" 
              :key="element.id"
              class="action-item selected"
            >
              <el-icon><component :is="element.icon" /></el-icon>
              <span>{{ element.title }}</span>
              <el-icon class="remove-icon" @click="removeAction(element.id)">
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showCustomizeDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomization">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { usePreferencesStore } from '@/stores/preferences'
import { 
  Bell, MagicStick, Setting, Grid, Plus, MoreFilled, 
  TrendCharts, Close, User, Folder, DataAnalysis,
  ChatDotRound, ShoppingCart, Monitor
} from '@element-plus/icons-vue'
// import draggable from 'vuedraggable' // 暂时注释，避免依赖问题

// 组件引用
const router = useRouter()
const userStore = useUserStore()
const preferencesStore = usePreferencesStore()

// 响应式数据
const showAddWidgetDialog = ref(false)
const showCustomizeDialog = ref(false)
const widgetsContainer = ref(null)

// 计算属性
const currentUser = computed(() => userStore.userInfo || {
  name: '用户',
  avatar: '',
  role: 'user'
})
const workbenchConfig = computed(() => {
  const roleConfigs = {
    admin: {
      title: '管理员智能控制台',
      description: '系统全局管理和智能监控中心'
    },
    substation: {
      title: '分站智能管理中心',
      description: '分站运营和团队协作智能助手'
    },
    agent: {
      title: '代理商智能工作台',
      description: '团队管理和业绩分析智能平台'
    },
    distributor: {
      title: '分销员智能助手',
      description: '客户管理和销售优化智能工具'
    },
    group_owner: {
      title: '群主智能运营台',
      description: '群组内容和成员管理智能中心'
    },
    user: {
      title: '个人智能助手',
      description: '个人信息和订单管理智能平台'
    }
  }
  return roleConfigs[currentUser.value.role] || roleConfigs.user
})

// AI推荐数据
const aiRecommendations = computed(() => {
  return preferencesStore.aiRecommendations || [
    {
      id: 1,
      type: 'feature_suggestion',
      title: '智能群组创建助手',
      description: '基于您的使用习惯，推荐使用群组批量管理功能',
      message: '基于您的使用习惯，推荐使用群组批量管理功能',
      action: 'navigate',
      target: '/groups/batch-manage',
      confidence: 0.85
    }
  ]
})

// 导入配置函数
import { getUserQuickActions } from '@/config/optimizedNavigationV2'

// 个性化快速操作（从配置中获取）
const personalizedQuickActions = computed(() => {
  const userRole = currentUser.value?.role || 'user'
  
  // 从配置中获取角色专属的快速操作
  const configQuickActions = getUserQuickActions(userRole) || []
  
  // 转换为组件需要的格式
  const actions = configQuickActions.map(action => ({
    id: action.path.replace(/\//g, '_'),
    title: action.title,
    description: getActionDescription(action.title),
    icon: action.icon,
    iconClass: getIconClass(action.icon),
    protected: action.title === '创建群组', // 群组创建功能保护
    stats: { usage: Math.floor(Math.random() * 200) + 50 },
    action: () => router.push(action.path)
  }))
  
  // 确保群组创建功能始终存在且排在第一位
  const groupCreateAction = actions.find(a => a.title === '创建群组')
  if (!groupCreateAction) {
    actions.unshift({
      id: 'create_group',
      title: '创建群组',
      description: '快速创建新的微信群组',
      icon: 'Plus',
      iconClass: 'text-blue-500',
      protected: true,
      stats: { usage: 156 },
      action: () => router.push('/community/add')
    })
  }
  
  return actions
})

// 获取操作描述的辅助函数
const getActionDescription = (title) => {
  const descriptions = {
    '创建群组': '快速创建新的微信群组',
    '支付设置': '配置支付渠道和参数',
    '防红配置': '配置防封系统和域名管理',
    '用户管理': '管理系统用户和权限',
    '代理商管理': '管理代理商和团队',
    '数据大屏': '查看全屏数据展示',
    '系统监控': '监控系统运行状态'
  }
  return descriptions[title] || `管理${title}相关功能`
}

// 获取图标样式的辅助函数
const getIconClass = (icon) => {
  const iconClasses = {
    'Plus': 'text-blue-500',
    'CreditCard': 'text-green-500',
    'Lock': 'text-orange-500',
    'User': 'text-purple-500',
    'Avatar': 'text-red-500',
    'DataBoard': 'text-indigo-500',
    'Monitor': 'text-gray-500'
  }
  return iconClasses[icon] || 'text-gray-500'
}

// 个性化仪表板组件
const personalizedWidgets = ref([
  {
    id: 'stats_overview',
    title: '数据概览',
    component: 'StatsOverviewWidget',
    size: 'large',
    config: { showTrends: true },
    data: {}
  },
  {
    id: 'recent_groups',
    title: '最近群组',
    component: 'RecentGroupsWidget',
    size: 'medium',
    config: { limit: 5 },
    data: {}
  },
  {
    id: 'performance_chart',
    title: '性能监控',
    component: 'PerformanceChartWidget',
    size: 'medium',
    config: { timeRange: '7d' },
    data: {}
  }
])

// 可用组件
const availableWidgets = ref([
  {
    id: 'todo_list',
    title: '待办事项',
    description: '管理您的任务和提醒',
    icon: 'Folder',
    component: 'TodoListWidget'
  },
  {
    id: 'system_monitor',
    title: '系统监控',
    description: '实时系统状态监控',
    icon: 'Monitor',
    component: 'SystemMonitorWidget'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '创建了新群组"产品交流群"',
    time: new Date(Date.now() - 1000 * 60 * 30),
    icon: 'ChatDotRound',
    iconClass: 'text-blue-500',
    actionable: true,
    actionText: '查看'
  },
  {
    id: 2,
    title: '处理了5个用户申请',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    icon: 'User',
    iconClass: 'text-green-500'
  }
])

// 智能提醒
const intelligentReminders = ref([
  {
    id: 1,
    title: '群组活跃度下降',
    description: '有3个群组本周活跃度下降超过20%',
    priority: 'high',
    action: 'view_groups'
  },
  {
    id: 2,
    title: '系统更新可用',
    description: '新版本包含性能优化和安全更新',
    priority: 'medium',
    action: 'update_system'
  }
])

// 通知数量
const unreadNotifications = computed(() => {
  return intelligentReminders.value.filter(r => r.priority === 'high').length
})

// 可用操作列表
const availableActions = ref([
  {
    id: 'file_management',
    title: '文件管理',
    icon: 'Folder',
    action: () => router.push('/files')
  }
])

// 方法
const getGreetingMessage = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const handleAIRecommendation = (recommendation) => {
  if (recommendation.action === 'navigate') {
    router.push(recommendation.target)
  }
}

const handleQuickAction = (action) => {
  if (action.action) {
    action.action()
  }
}

const customizeQuickActions = () => {
  showCustomizeDialog.value = true
}

const customizeDashboard = () => {
  // 实现仪表板自定义逻辑
  console.log('自定义仪表板')
}

const handleWidgetAction = (command) => {
  const [action, widgetId] = command.split('-')
  console.log(`Widget action: ${action} for ${widgetId}`)
}

const handleWidgetUpdate = (widgetId, data) => {
  const widget = personalizedWidgets.value.find(w => w.id === widgetId)
  if (widget) {
    widget.data = { ...widget.data, ...data }
  }
}

const addWidget = (widgetType) => {
  const newWidget = {
    id: `${widgetType.id}_${Date.now()}`,
    title: widgetType.title,
    component: widgetType.component,
    size: 'medium',
    config: {},
    data: {}
  }
  personalizedWidgets.value.push(newWidget)
  showAddWidgetDialog.value = false
}

const viewAllActivities = () => {
  router.push('/activities')
}

const handleActivityAction = (activity) => {
  console.log('处理活动:', activity)
}

const customizeReminders = () => {
  console.log('自定义提醒设置')
}

const handleReminder = (reminder) => {
  console.log('处理提醒:', reminder)
}

const dismissReminder = (reminderId) => {
  const index = intelligentReminders.value.findIndex(r => r.id === reminderId)
  if (index > -1) {
    intelligentReminders.value.splice(index, 1)
  }
}

const addActionToSelected = (action) => {
  // 将操作从可用列表移动到已选列表
  const index = availableActions.value.findIndex(a => a.id === action.id)
  if (index > -1) {
    availableActions.value.splice(index, 1)
    personalizedQuickActions.value.push(action)
  }
}

const removeAction = (actionId) => {
  const index = personalizedQuickActions.value.findIndex(a => a.id === actionId)
  if (index > -1) {
    const removedAction = personalizedQuickActions.value.splice(index, 1)[0]
    availableActions.value.push(removedAction)
  }
}

const saveCustomization = () => {
  // 保存个性化设置到后端
  preferencesStore.saveQuickActions(personalizedQuickActions.value)
  showCustomizeDialog.value = false
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return time.toLocaleDateString()
}

// 生命周期
onMounted(() => {
  // 确保用户信息可用（预览模式）
  if (!userStore.userInfo) {
    userStore.enterPreviewMode()
  }
  
  // 加载用户个性化设置（使用try-catch处理API错误）
  try {
    preferencesStore.loadUserPreferences()
  } catch (error) {
    console.log('用户偏好设置加载失败，使用默认设置')
  }
})
</script>

<style lang="scss" scoped>
.intelligent-workbench {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;

  // 工作台头部
  .workbench-header {
    margin-bottom: 32px;

    .user-greeting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
      padding: 24px;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      margin-bottom: 16px;

      .greeting-content {
        flex: 1;

        .greeting-title {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .greeting-subtitle {
          font-size: 16px;
          color: #6b7280;
          margin: 0;
        }
      }

      .user-avatar {
        position: relative;

        .user-status {
          position: absolute;
          top: -8px;
          right: -8px;

          .notification-badge {
            :deep(.el-badge__content) {
              background: #ef4444;
              border: 2px solid white;
            }
          }
        }
      }
    }

    .ai-recommendation-banner {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);

      .banner-icon {
        font-size: 24px;
        margin-right: 16px;
      }

      .banner-content {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }
  }

  // 快速操作区域
  .quick-actions-section {
    margin-bottom: 32px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;

      .quick-action-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        &.protected-action {
          border-color: #fbbf24;
          background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);

          .action-badge {
            position: absolute;
            top: 12px;
            right: 12px;
          }
        }

        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          margin-bottom: 12px;
          background: #f8fafc;
        }

        .action-content {
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
          }

          p {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 8px 0;
          }

          .action-stats {
            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #9ca3af;
            }
          }
        }
      }
    }
  }

  // 仪表板组件区域
  .dashboard-widgets-section {
    margin-bottom: 32px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .widgets-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .widget-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        overflow: hidden;

        &.widget-large {
          grid-column: span 2;
        }

        .widget-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #f1f5f9;

          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
          }

          .widget-menu {
            cursor: pointer;
            color: #9ca3af;
            
            &:hover {
              color: #6b7280;
            }
          }
        }

        .widget-content {
          padding: 20px;
          min-height: 200px;
        }
      }

      .add-widget-card {
        background: #f8fafc;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          background: #eff6ff;
        }

        .add-icon {
          font-size: 32px;
          color: #9ca3af;
          margin-bottom: 8px;
        }

        p {
          color: #6b7280;
          margin: 0;
        }
      }
    }
  }

  // 活动区域
  .activity-section {
    .activity-card,
    .reminders-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      overflow: hidden;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f1f5f9;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }
      }

      .activity-list,
      .reminders-list {
        padding: 16px 20px;
        max-height: 300px;
        overflow-y: auto;
      }

      .activity-item,
      .reminder-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8fafc;

        &:last-child {
          border-bottom: none;
        }

        .activity-icon,
        .reminder-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          background: #f1f5f9;
        }

        .activity-content,
        .reminder-content {
          flex: 1;

          .activity-title,
          .reminder-title {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin: 0 0 2px 0;
          }

          .activity-time,
          .reminder-desc {
            font-size: 12px;
            color: #9ca3af;
            margin: 0;
          }
        }

        .activity-action,
        .reminder-actions {
          display: flex;
          gap: 8px;
        }
      }

      .reminder-item {
        &.reminder-high {
          background: #fef2f2;
          border-left: 4px solid #ef4444;
        }

        &.reminder-medium {
          background: #fffbeb;
          border-left: 4px solid #f59e0b;
        }

        &.reminder-low {
          background: #f0f9ff;
          border-left: 4px solid #3b82f6;
        }
      }
    }
  }
}

// 对话框样式
:deep(.add-widget-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .widget-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .widget-option {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #3b82f6;
        background: #f8fafc;
      }

      .widget-preview {
        text-align: center;
        margin-bottom: 12px;
        font-size: 32px;
        color: #6b7280;
      }

      .widget-info {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 14px;
          color: #6b7280;
          margin: 0;
        }
      }
    }
  }
}

:deep(.customize-dialog) {
  .customize-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;

    .available-actions,
    .selected-actions {
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 12px 0;
      }

      .actions-list {
        min-height: 200px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px;

        .action-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          margin-bottom: 8px;
          cursor: move;

          &.selected {
            background: #eff6ff;
            border-color: #3b82f6;
          }

          .remove-icon {
            margin-left: auto;
            cursor: pointer;
            color: #ef4444;

            &:hover {
              color: #dc2626;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .intelligent-workbench {
    padding: 16px;

    .workbench-header {
      .user-greeting {
        flex-direction: column;
        text-align: center;
        gap: 16px;
      }

      .ai-recommendation-banner {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }
    }

    .quick-actions-grid {
      grid-template-columns: 1fr;
    }

    .widgets-grid {
      grid-template-columns: 1fr;

      .widget-card.widget-large {
        grid-column: span 1;
      }
    }

    .activity-section {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>