<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class DistributorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['error' => '未认证'], 401);
        }
        
        if (!$user->is_distributor || $user->distributor_status != 1) {
            return response()->json(['error' => '无分销员权限'], 403);
        }
        
        return $next($request);
    }
}