// 性能优化工具集合
export class PerformanceUtils {
  // 防抖函数
  static debounce(func, wait, immediate = false) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        timeout = null
        if (!immediate) func.apply(this, args)
      }
      const callNow = immediate && !timeout
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      if (callNow) func.apply(this, args)
    }
  }

  // 节流函数
  static throttle(func, limit) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // RAF节流
  static rafThrottle(func) {
    let rafId
    return function executedFunction(...args) {
      if (rafId) return
      rafId = requestAnimationFrame(() => {
        func.apply(this, args)
        rafId = null
      })
    }
  }

  // 延迟执行
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 空闲时执行
  static idleCallback(callback, options = {}) {
    if (typeof requestIdleCallback === 'function') {
      return requestIdleCallback(callback, options)
    } else {
      // 降级处理
      return setTimeout(callback, 0)
    }
  }

  // 分时函数 - 大量数据处理
  static timeSlicing(array, handler, batchSize = 100) {
    return new Promise((resolve, reject) => {
      let index = 0
      const results = []

      function processBatch() {
        const endIndex = Math.min(index + batchSize, array.length)
        
        try {
          for (let i = index; i < endIndex; i++) {
            results.push(handler(array[i], i))
          }
          
          index = endIndex
          
          if (index < array.length) {
            // 使用 requestIdleCallback 或 setTimeout 继续处理
            PerformanceUtils.idleCallback(processBatch)
          } else {
            resolve(results)
          }
        } catch (error) {
          reject(error)
        }
      }

      processBatch()
    })
  }

  // 预加载资源
  static preloadImages(urls) {
    return Promise.all(urls.map(url => {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve(url)
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
        img.src = url
      })
    }))
  }

  // 懒加载图片
  static createLazyImageObserver(callback, options = {}) {
    const defaultOptions = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }

    if (!('IntersectionObserver' in window)) {
      // 降级处理
      return {
        observe: (element) => callback(element),
        disconnect: () => {}
      }
    }

    return new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          callback(entry.target)
        }
      })
    }, defaultOptions)
  }

  // 内存优化 - WeakMap缓存
  static createWeakCache() {
    const cache = new WeakMap()
    
    return {
      get(key) {
        return cache.get(key)
      },
      set(key, value) {
        cache.set(key, value)
        return value
      },
      has(key) {
        return cache.has(key)
      },
      delete(key) {
        return cache.delete(key)
      },
      clear() {
        // WeakMap 不支持 clear，需要重新创建
        return PerformanceUtils.createWeakCache()
      }
    }
  }

  // LRU缓存实现
  static createLRUCache(maxSize = 100) {
    const cache = new Map()

    return {
      get(key) {
        if (cache.has(key)) {
          const value = cache.get(key)
          // 重新插入以更新顺序
          cache.delete(key)
          cache.set(key, value)
          return value
        }
        return undefined
      },
      
      set(key, value) {
        if (cache.has(key)) {
          cache.delete(key)
        } else if (cache.size >= maxSize) {
          // 删除最旧的项目
          const firstKey = cache.keys().next().value
          cache.delete(firstKey)
        }
        cache.set(key, value)
        return value
      },
      
      has(key) {
        return cache.has(key)
      },
      
      delete(key) {
        return cache.delete(key)
      },
      
      clear() {
        cache.clear()
      },
      
      get size() {
        return cache.size
      },
      
      entries() {
        return Array.from(cache.entries())
      }
    }
  }

  // 批量DOM更新
  static batchDOMUpdates(updates) {
    return new Promise(resolve => {
      // 使用 DocumentFragment 批量更新
      const fragment = document.createDocumentFragment()
      
      updates.forEach(update => {
        if (typeof update === 'function') {
          update(fragment)
        }
      })
      
      // 一次性插入DOM
      requestAnimationFrame(() => {
        resolve(fragment)
      })
    })
  }

  // 性能监控
  static measurePerformance(name, fn) {
    return async function(...args) {
      const start = performance.now()
      
      try {
        const result = await fn.apply(this, args)
        const end = performance.now()
        const duration = end - start
        
        // 记录性能数据
        if (performance.mark && performance.measure) {
          performance.mark(`${name}-start`)
          performance.mark(`${name}-end`)
          performance.measure(name, `${name}-start`, `${name}-end`)
        }
        
        console.log(`${name} took ${duration.toFixed(2)}ms`)
        return result
      } catch (error) {
        const end = performance.now()
        console.error(`${name} failed after ${(end - start).toFixed(2)}ms:`, error)
        throw error
      }
    }
  }

  // 组件懒加载
  static lazyLoadComponent(importFn) {
    return () => ({
      component: importFn(),
      loading: {
        template: '<div class="lazy-loading">Loading...</div>'
      },
      error: {
        template: '<div class="lazy-error">Failed to load component</div>'
      },
      delay: 200,
      timeout: 3000
    })
  }

  // 路由懒加载
  static lazyLoadRoute(importFn) {
    return () => importFn().catch(error => {
      console.error('Route lazy load failed:', error)
      return import('@/views/ErrorPage.vue')
    })
  }
}

// 导出具体的优化函数
export const debounce = PerformanceUtils.debounce
export const throttle = PerformanceUtils.throttle
export const rafThrottle = PerformanceUtils.rafThrottle
export const delay = PerformanceUtils.delay
export const idleCallback = PerformanceUtils.idleCallback
export const timeSlicing = PerformanceUtils.timeSlicing
export const measurePerformance = PerformanceUtils.measurePerformance

// 导出缓存工具
export const createWeakCache = PerformanceUtils.createWeakCache
export const createLRUCache = PerformanceUtils.createLRUCache

// 导出懒加载工具
export const lazyLoadComponent = PerformanceUtils.lazyLoadComponent
export const lazyLoadRoute = PerformanceUtils.lazyLoadRoute

// 性能监控 Composable
import { ref, onMounted, onUnmounted } from 'vue'

export function usePerformanceMonitor() {
  const metrics = ref({
    // 页面加载性能
    loadTime: 0,
    domContentLoaded: 0,
    firstPaint: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    
    // 运行时性能
    memoryUsage: 0,
    frameRate: 0,
    
    // 网络性能
    networkType: 'unknown',
    downlink: 0,
    rtt: 0
  })

  const performanceEntries = ref([])
  const isMonitoring = ref(false)

  // 收集页面加载性能
  const collectLoadMetrics = () => {
    if (!performance.timing) return

    const timing = performance.timing
    const navigation = performance.navigation

    metrics.value = {
      ...metrics.value,
      loadTime: timing.loadEventEnd - timing.navigationStart,
      domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
      // 获取 Paint 指标
      ...getWebVitals()
    }
  }

  // 获取 Web Vitals
  const getWebVitals = () => {
    const paintEntries = performance.getEntriesByType('paint')
    const vitals = {}

    paintEntries.forEach(entry => {
      if (entry.name === 'first-paint') {
        vitals.firstPaint = entry.startTime
      } else if (entry.name === 'first-contentful-paint') {
        vitals.firstContentfulPaint = entry.startTime
      }
    })

    // LCP
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint')
    if (lcpEntries.length > 0) {
      vitals.largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime
    }

    return vitals
  }

  // 监控内存使用
  const monitorMemory = () => {
    if (performance.memory) {
      metrics.value.memoryUsage = performance.memory.usedJSHeapSize
    }
  }

  // 监控帧率
  const monitorFrameRate = () => {
    let lastTime = performance.now()
    let frameCount = 0

    const measureFPS = () => {
      const currentTime = performance.now()
      frameCount++

      if (currentTime - lastTime >= 1000) {
        metrics.value.frameRate = Math.round((frameCount * 1000) / (currentTime - lastTime))
        frameCount = 0
        lastTime = currentTime
      }

      if (isMonitoring.value) {
        requestAnimationFrame(measureFPS)
      }
    }

    requestAnimationFrame(measureFPS)
  }

  // 监控网络信息
  const monitorNetwork = () => {
    if ('connection' in navigator) {
      const connection = navigator.connection
      metrics.value.networkType = connection.effectiveType || 'unknown'
      metrics.value.downlink = connection.downlink || 0
      metrics.value.rtt = connection.rtt || 0
    }
  }

  // 收集性能条目
  const collectPerformanceEntries = () => {
    const entries = performance.getEntries()
    performanceEntries.value = entries.map(entry => ({
      name: entry.name,
      type: entry.entryType,
      startTime: entry.startTime,
      duration: entry.duration
    }))
  }

  // 开始监控
  const startMonitoring = () => {
    isMonitoring.value = true
    monitorFrameRate()
    
    // 定期更新指标
    const interval = setInterval(() => {
      if (isMonitoring.value) {
        monitorMemory()
        monitorNetwork()
        collectPerformanceEntries()
      } else {
        clearInterval(interval)
      }
    }, 1000)
  }

  // 停止监控
  const stopMonitoring = () => {
    isMonitoring.value = false
  }

  // 导出性能报告
  const exportReport = () => {
    return {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: metrics.value,
      performanceEntries: performanceEntries.value
    }
  }

  // 性能评分
  const getPerformanceScore = () => {
    const { firstContentfulPaint, largestContentfulPaint, loadTime } = metrics.value
    
    let score = 100
    
    // FCP 评分 (0-2.5s = 100, >4s = 0)
    if (firstContentfulPaint > 4000) score -= 30
    else if (firstContentfulPaint > 2500) score -= 15
    
    // LCP 评分 (0-2.5s = 100, >4s = 0)
    if (largestContentfulPaint > 4000) score -= 30
    else if (largestContentfulPaint > 2500) score -= 15
    
    // 加载时间评分
    if (loadTime > 5000) score -= 20
    else if (loadTime > 3000) score -= 10
    
    // 内存使用评分
    const memoryMB = metrics.value.memoryUsage / 1024 / 1024
    if (memoryMB > 100) score -= 10
    else if (memoryMB > 50) score -= 5
    
    return Math.max(0, score)
  }

  onMounted(() => {
    // 页面加载完成后收集指标
    if (document.readyState === 'complete') {
      collectLoadMetrics()
    } else {
      window.addEventListener('load', collectLoadMetrics)
    }
    
    startMonitoring()
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    metrics,
    performanceEntries,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    exportReport,
    getPerformanceScore
  }
}