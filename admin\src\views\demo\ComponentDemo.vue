<template>
  <PageLayout
    title="组件演示"
    subtitle="展示设计系统中的各种组件"
    icon="el-icon-magic-stick"
    :stats="demoStats"
  >
    <template #actions>
      <el-button class="modern-btn primary">
        <i class="el-icon-plus"></i>
        主要按钮
      </el-button>
      <el-button class="modern-btn success">
        <i class="el-icon-check"></i>
        成功按钮
      </el-button>
      <el-button class="modern-btn warning">
        <i class="el-icon-warning"></i>
        警告按钮
      </el-button>
    </template>

    <!-- 仪表板卡片演示 -->
    <div class="demo-section">
      <h3 class="section-title">仪表板卡片</h3>
      <el-row :gutter="20">
        <el-col :span="6" v-for="card in dashboardCards" :key="card.title">
          <DashboardCard v-bind="card" />
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格演示 -->
    <div class="demo-section">
      <h3 class="section-title">数据表格</h3>
      <DataTable
        :data="tableData"
        :columns="tableColumns"
        :loading="false"
        :pagination="tablePagination"
        :search-config="searchConfig"
        :batch-actions="batchActions"
        @search="handleSearch"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @batch-action="handleBatchAction"
        @row-action="handleRowAction"
      />
    </div>

    <!-- 动态表单演示 -->
    <div class="demo-section">
      <h3 class="section-title">动态表单</h3>
      <div class="form-demo">
        <DynamicForm
          :fields="formFields"
          :model="formData"
          :rules="formRules"
          @submit="handleFormSubmit"
          @cancel="handleFormCancel"
        />
      </div>
    </div>
  </PageLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import PageLayout from '@/components/layout/PageLayout.vue'
import DashboardCard from '@/components/dashboard/DashboardCard.vue'
import DataTable from '@/components/common/DataTable.vue'
import DynamicForm from '@/components/common/DynamicForm.vue'

// 统计数据
const demoStats = computed(() => [
  {
    icon: 'el-icon-data-line',
    label: '总组件数',
    value: 15,
    color: 'primary',
    change: '+3',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-view',
    label: '页面浏览',
    value: 2847,
    color: 'success',
    change: '+12.5%',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-user',
    label: '活跃用户',
    value: 1234,
    color: 'warning',
    change: '+8.3%',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-star-on',
    label: '满意度',
    value: '98%',
    color: 'info',
    change: '+2.1%',
    changeType: 'increase'
  }
])

// 仪表板卡片数据
const dashboardCards = ref([
  {
    type: 'stat',
    title: '销售额',
    value: '¥125,678',
    icon: 'el-icon-money',
    theme: 'primary',
    change: '+12.5%',
    changeType: 'increase',
    description: '较上月增长'
  },
  {
    type: 'progress',
    title: '任务完成率',
    value: 75,
    icon: 'el-icon-s-data',
    theme: 'success',
    description: '本月目标进度'
  },
  {
    type: 'list',
    title: '最新动态',
    icon: 'el-icon-bell',
    theme: 'warning',
    items: [
      { label: '新用户注册', value: '5分钟前' },
      { label: '订单支付成功', value: '10分钟前' },
      { label: '系统更新完成', value: '1小时前' }
    ]
  },
  {
    type: 'custom',
    title: '访问趋势',
    icon: 'el-icon-trend-charts',
    theme: 'info',
    chartData: {
      type: 'line',
      data: [12, 19, 3, 5, 2, 3, 8, 15, 23, 18, 25, 30]
    }
  }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    avatar: '',
    created_at: '2024-01-15 10:30:00',
    balance: 1250.50
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    avatar: '',
    created_at: '2024-01-16 14:20:00',
    balance: 890.25
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    role: 'distributor',
    status: 'disabled',
    avatar: '',
    created_at: '2024-01-17 09:15:00',
    balance: 2150.75
  }
])

// 表格列配置
const tableColumns = ref([
  { type: 'selection', width: 55 },
  { prop: 'id', label: 'ID', width: 80 },
  {
    prop: 'avatar',
    label: '头像',
    width: 80,
    type: 'image',
    fallback: (row) => row.name?.charAt(0) || 'U'
  },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  {
    prop: 'role',
    label: '角色',
    width: 100,
    type: 'tag',
    tagMap: {
      admin: { text: '管理员', type: 'danger' },
      user: { text: '用户', type: 'info' },
      distributor: { text: '分销员', type: 'success' }
    }
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    type: 'tag',
    tagMap: {
      active: { text: '正常', type: 'success' },
      disabled: { text: '禁用', type: 'danger' }
    }
  },
  { prop: 'balance', label: '余额', width: 120, type: 'currency' },
  { prop: 'created_at', label: '创建时间', width: 160, type: 'date' },
  {
    type: 'actions',
    label: '操作',
    width: 200,
    fixed: 'right',
    actions: [
      { key: 'edit', label: '编辑', type: 'primary', size: 'small' },
      { key: 'delete', label: '删除', type: 'danger', size: 'small' }
    ]
  }
])

// 表格分页
const tablePagination = ref({
  current: 1,
  size: 10,
  total: 3
})

// 搜索配置
const searchConfig = ref({
  fields: [
    {
      key: 'keyword',
      type: 'input',
      placeholder: '搜索姓名、邮箱',
      width: 200
    },
    {
      key: 'role',
      type: 'select',
      placeholder: '选择角色',
      width: 120,
      options: [
        { label: '全部', value: '' },
        { label: '管理员', value: 'admin' },
        { label: '用户', value: 'user' },
        { label: '分销员', value: 'distributor' }
      ]
    }
  ]
})

// 批量操作
const batchActions = ref([
  { key: 'enable', label: '批量启用', type: 'success', icon: 'el-icon-check' },
  { key: 'disable', label: '批量禁用', type: 'warning', icon: 'el-icon-close' },
  { key: 'delete', label: '批量删除', type: 'danger', icon: 'el-icon-delete' }
])

// 表单数据
const formData = ref({
  name: '',
  email: '',
  phone: '',
  gender: '',
  role: 'user',
  status: 'active',
  bio: '',
  tags: [],
  notifications: true
})

// 表单字段
const formFields = ref([
  {
    group: '基本信息',
    fields: [
      {
        key: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名',
        required: true,
        span: 12
      },
      {
        key: 'email',
        label: '邮箱',
        type: 'input',
        placeholder: '请输入邮箱',
        required: true,
        span: 12
      },
      {
        key: 'phone',
        label: '手机号',
        type: 'input',
        placeholder: '请输入手机号',
        span: 12
      },
      {
        key: 'gender',
        label: '性别',
        type: 'radio',
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ],
        span: 12
      }
    ]
  },
  {
    group: '账户设置',
    fields: [
      {
        key: 'role',
        label: '角色',
        type: 'select',
        options: [
          { label: '用户', value: 'user' },
          { label: '管理员', value: 'admin' },
          { label: '分销员', value: 'distributor' }
        ],
        required: true,
        span: 12
      },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '正常', value: 'active' },
          { label: '禁用', value: 'disabled' }
        ],
        required: true,
        span: 12
      },
      {
        key: 'notifications',
        label: '接收通知',
        type: 'switch',
        description: '是否接收系统通知',
        span: 24
      }
    ]
  },
  {
    group: '其他信息',
    fields: [
      {
        key: 'tags',
        label: '标签',
        type: 'checkbox',
        options: [
          { label: 'VIP', value: 'vip' },
          { label: '活跃', value: 'active' },
          { label: '新用户', value: 'new' }
        ],
        span: 24
      },
      {
        key: 'bio',
        label: '简介',
        type: 'textarea',
        placeholder: '请输入个人简介',
        rows: 4,
        span: 24
      }
    ]
  }
])

// 表单验证规则
const formRules = ref({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 事件处理
const handleSearch = (params) => {
  console.log('搜索参数:', params)
  ElMessage.success('搜索功能演示')
}

const handlePageChange = (page) => {
  tablePagination.value.current = page
  console.log('页码变化:', page)
}

const handleSizeChange = (size) => {
  tablePagination.value.size = size
  console.log('页面大小变化:', size)
}

const handleBatchAction = (action, selectedRows) => {
  console.log('批量操作:', action, selectedRows)
  ElMessage.success(`批量操作: ${action.label}`)
}

const handleRowAction = (action, row) => {
  console.log('行操作:', action, row)
  ElMessage.success(`${action.label}: ${row.name}`)
}

const handleFormSubmit = (data) => {
  console.log('表单提交:', data)
  ElMessage.success('表单提交成功')
}

const handleFormCancel = () => {
  console.log('表单取消')
  ElMessage.info('表单操作已取消')
}
</script>

<style lang="scss" scoped>
.demo-section {
  margin-bottom: var(--spacing-3xl);
  
  .section-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--color-primary);
    display: inline-block;
  }
}

.form-demo {
  max-width: 600px;
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-2xl);
}
</style>
