import api from './index'

// 防屏蔽系统相关API
export const getAntiBlockStats = () => {
  return api.get('/admin/anti-block/stats')
}

export const getDomains = (params) => {
  return api.get('/admin/anti-block/domains', { params })
}

export const getDomainList = (params) => {
  return api.get('/admin/anti-block/domains', { params })
}

export const addDomain = (data) => {
  return api.post('/admin/anti-block/domains', data)
}

export const updateDomain = (id, data) => {
  return api.put(`/admin/anti-block/domains/${id}`, data)
}

export const deleteDomain = (id) => {
  return api.delete(`/admin/anti-block/domains/${id}`)
}

export const checkDomains = (data) => {
  return api.post('/admin/anti-block/domains/batch-check', data)
}

export const batchDeleteDomains = (data) => {
  return api.post('/admin/anti-block/domains/batch-delete', data)
}

// 短链接管理
export const getShortLinks = (params) => {
  return api.get('/admin/anti-block/short-links', { params })
}

export const createShortLink = (data) => {
  return api.post('/admin/anti-block/short-links', data)
}

export const deleteShortLink = (id) => {
  return api.delete(`/admin/anti-block/short-links/${id}`)
}

export const batchDeleteShortLinks = (data) => {
  return api.post('/admin/anti-block/short-links/batch-delete', data)
}

export const exportShortLinks = (params) => {
  return api.get('/admin/anti-block/short-links/export', { params })
}

// 统计分析
export const getAccessStats = (params) => {
  return api.get('/admin/anti-block/access-stats', { params })
}

export const getAccessLogs = (params) => {
  return api.get('/admin/anti-block/access-logs', { params })
}

export const getClickTrends = (params) => {
  return api.get('/admin/anti-block/click-trends', { params })
}

export const getRegionStats = (params) => {
  return api.get('/admin/anti-block/region-stats', { params })
}

export const getPlatformStats = (params) => {
  return api.get('/admin/anti-block/platform-stats', { params })
}

export const generateQRCode = (data) => {
  return api.post('/admin/anti-block/qrcode', data)
}

export const updateShortLink = (id, data) => {
  return api.put(`/admin/anti-block/short-links/${id}`, data)
}

export const switchShortLinkDomain = (id, data) => {
  return api.post(`/admin/anti-block/short-links/${id}/switch-domain`, data)
}

// 群组推广链接相关API
export const generateGroupPromotionLink = (groupId, config) => {
  return api.post(`/api/admin/groups/${groupId}/promotion-link`, config)
}

export const getGroupPromotionLinks = (groupId, params) => {
  return api.get(`/api/admin/groups/${groupId}/promotion-links`, { params })
}

export const updatePromotionLinkConfig = (groupId, config) => {
  return api.put(`/api/admin/groups/${groupId}/anti-block-config`, config)
}
