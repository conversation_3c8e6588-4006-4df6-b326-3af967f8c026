/**
 * 增强的错误处理服务
 * 提供统一的错误处理、日志记录和错误恢复机制
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

class ErrorHandlerService {
  constructor() {
    this.errorQueue = []
    this.maxQueueSize = 100
    this.isReporting = false
    this.initGlobalHandlers()
  }

  /**
   * 初始化全局错误处理器
   */
  initGlobalHandlers() {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.handleGlobalError(event)
    })

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleUnhandledRejection(event)
    })

    // Vue全局错误处理
    if (window.Vue) {
      window.Vue.config.errorHandler = (error, instance, info) => {
        this.handleVueError(error, instance, info)
      }
    }
  }

  /**
   * 处理HTTP错误
   */
  handleHttpError(error, context = '') {
    const errorInfo = this.parseError(error)
    
    // 记录错误
    this.logError({
      type: 'HTTP_ERROR',
      context,
      ...errorInfo,
      timestamp: Date.now()
    })

    // 显示用户友好的错误信息
    this.showUserFriendlyError(errorInfo)

    // 执行错误恢复策略
    this.handleErrorRecovery(errorInfo)

    return errorInfo
  }

  /**
   * 处理业务逻辑错误
   */
  handleBusinessError(message, code = null, context = '') {
    const errorInfo = {
      type: 'BUSINESS_ERROR',
      message: message || '操作失败',
      code,
      context,
      timestamp: Date.now()
    }

    this.logError(errorInfo)
    this.showUserFriendlyError(errorInfo)
    
    return errorInfo
  }

  /**
   * 处理验证错误
   */
  handleValidationError(errors, context = '') {
    const errorInfo = {
      type: 'VALIDATION_ERROR',
      errors,
      context,
      timestamp: Date.now()
    }

    this.logError(errorInfo)
    
    // 显示验证错误
    if (Array.isArray(errors)) {
      errors.forEach(error => {
        ElMessage.error(error)
      })
    } else if (typeof errors === 'object') {
      Object.values(errors).forEach(errorArray => {
        if (Array.isArray(errorArray)) {
          errorArray.forEach(error => ElMessage.error(error))
        }
      })
    }

    return errorInfo
  }

  /**
   * 处理运行时错误
   */
  handleRuntimeError(error, context = '') {
    const errorInfo = this.parseError(error)
    
    Object.assign(errorInfo, {
      type: 'RUNTIME_ERROR',
      context,
      timestamp: Date.now()
    })

    this.logError(errorInfo)
    this.showUserFriendlyError(errorInfo)
    
    // 如果是严重错误，尝试恢复
    if (errorInfo.severity === 'critical') {
      this.handleCriticalError(errorInfo)
    }

    return errorInfo
  }

  /**
   * 处理全局错误
   */
  handleGlobalError(event) {
    const errorInfo = {
      type: 'GLOBAL_ERROR',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: Date.now()
    }

    this.logError(errorInfo)
    
    if (import.meta.env.DEV) {
      console.error('Global Error:', errorInfo)
    }
  }

  /**
   * 处理未处理的Promise拒绝
   */
  handleUnhandledRejection(event) {
    const errorInfo = {
      type: 'UNHANDLED_REJECTION',
      reason: event.reason,
      stack: event.reason?.stack,
      timestamp: Date.now()
    }

    this.logError(errorInfo)
    
    if (import.meta.env.DEV) {
      console.error('Unhandled Promise Rejection:', errorInfo)
    }

    event.preventDefault()
  }

  /**
   * 处理Vue错误
   */
  handleVueError(error, instance, info) {
    const errorInfo = {
      type: 'VUE_ERROR',
      message: error.message,
      stack: error.stack,
      component: instance?.$options?.name || 'Unknown',
      info,
      timestamp: Date.now()
    }

    this.logError(errorInfo)
  }

  /**
   * 解析错误信息
   */
  parseError(error) {
    if (error.response) {
      // HTTP错误
      const { status, data } = error.response
      return {
        status,
        message: data?.message || this.getHttpErrorMessage(status),
        code: data?.code,
        errors: data?.errors,
        severity: this.getErrorSeverity(status)
      }
    } else if (error.request) {
      // 网络错误
      return {
        status: 0,
        message: '网络连接异常，请检查网络设置',
        severity: 'medium'
      }
    } else {
      // 其他错误
      return {
        status: -1,
        message: error.message || '未知错误',
        stack: error.stack,
        severity: 'low'
      }
    }
  }

  /**
   * 获取HTTP错误消息
   */
  getHttpErrorMessage(status) {
    const messages = {
      400: '请求参数错误',
      401: '登录已过期，请重新登录',
      403: '没有权限访问该资源',
      404: '请求的资源不存在',
      408: '请求超时',
      409: '资源冲突',
      422: '数据验证失败',
      429: '请求过于频繁，请稍后再试',
      500: '服务器内部错误，请联系管理员',
      502: '网关错误，请稍后重试',
      503: '服务暂时不可用，请稍后重试',
      504: '网关超时，请稍后重试'
    }

    return messages[status] || `请求失败 (${status})`
  }

  /**
   * 获取错误严重程度
   */
  getErrorSeverity(status) {
    if (status >= 500) return 'critical'
    if (status >= 400) return 'high'
    if (status >= 300) return 'medium'
    return 'low'
  }

  /**
   * 显示用户友好的错误信息
   */
  showUserFriendlyError(errorInfo) {
    const { message, severity, type } = errorInfo

    // 根据错误类型和严重程度选择合适的显示方式
    if (severity === 'critical') {
      ElNotification.error({
        title: '系统错误',
        message,
        duration: 0,
        showClose: true
      })
    } else if (severity === 'high') {
      ElMessage.error(message)
    } else if (type === 'VALIDATION_ERROR') {
      ElMessage.warning(message)
    } else {
      // 低优先级错误只在开发环境显示
      if (import.meta.env.DEV) {
        ElMessage.info(message)
      }
    }
  }

  /**
   * 错误恢复处理
   */
  handleErrorRecovery(errorInfo) {
    const { status, type } = errorInfo

    // 处理认证错误
    if (status === 401 || type === 'AUTH_ERROR') {
      this.handleAuthError()
      return
    }

    // 处理权限错误
    if (status === 403) {
      this.handlePermissionError()
      return
    }

    // 处理网络错误
    if (status === 0) {
      this.handleNetworkError()
      return
    }
  }

  /**
   * 处理认证错误
   */
  handleAuthError() {
    const userStore = useUserStore()
    
    ElMessageBox.confirm(
      '您的登录已过期，请重新登录',
      '登录过期',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      userStore.logout()
      router.push('/login')
    })
  }

  /**
   * 处理权限错误
   */
  handlePermissionError() {
    ElMessage.error('您没有权限访问该资源')
    router.push('/403')
  }

  /**
   * 处理网络错误
   */
  handleNetworkError() {
    ElMessage.error('网络连接失败，请检查网络设置')
  }

  /**
   * 处理严重错误
   */
  handleCriticalError(errorInfo) {
    // 记录错误详情
    console.error('Critical Error:', errorInfo)
    
    // 尝试恢复
    ElMessageBox.confirm(
      '系统遇到严重错误，是否尝试刷新页面恢复？',
      '系统错误',
      {
        confirmButtonText: '刷新页面',
        cancelButtonText: '取消',
        type: 'error'
      }
    ).then(() => {
      window.location.reload()
    })
  }

  /**
   * 记录错误日志
   */
  logError(errorInfo) {
    // 添加到错误队列
    this.errorQueue.unshift(errorInfo)
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(0, this.maxQueueSize)
    }

    // 本地存储错误日志
    if (import.meta.env.PROD) {
      try {
        const existingLogs = JSON.parse(localStorage.getItem('error_logs') || '[]')
        existingLogs.unshift(errorInfo)
        
        // 限制存储大小
        if (existingLogs.length > 50) {
          existingLogs.splice(50)
        }
        
        localStorage.setItem('error_logs', JSON.stringify(existingLogs))
      } catch (e) {
        console.error('Failed to save error log:', e)
      }
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLogs(limit = 10) {
    return this.errorQueue.slice(0, limit)
  }

  /**
   * 清除错误日志
   */
  clearErrorLogs() {
    this.errorQueue = []
    localStorage.removeItem('error_logs')
  }

  /**
   * 导出错误日志
   */
  exportErrorLogs() {
    const logs = this.getErrorLogs(this.errorQueue.length)
    const blob = new Blob([JSON.stringify(logs, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 重试机制
   */
  async retry(operation, maxRetries = 3, delay = 1000) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        if (i === maxRetries - 1) {
          throw error
        }
        
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }
  }

  /**
   * 显示错误详情对话框
   */
  showErrorDetails(errorInfo) {
    ElMessageBox.alert(
      `
        <div style="max-height: 400px; overflow-y: auto;">
          <p><strong>错误类型:</strong> ${errorInfo.type}</p>
          <p><strong>错误信息:</strong> ${errorInfo.message}</p>
          <p><strong>发生时间:</strong> ${new Date(errorInfo.timestamp).toLocaleString()}</p>
          ${errorInfo.stack ? `<p><strong>堆栈信息:</strong></p><pre style="background: #f5f5f5; padding: 10px; white-space: pre-wrap;">${errorInfo.stack}</pre>` : ''}
        </div>
      `,
      '错误详情',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )
  }
}

// 创建单例实例
const errorHandler = new ErrorHandlerService()

// 快捷方法
export const handleError = (error, context = '') => errorHandler.handleHttpError(error, context)
export const handleBusinessError = (message, code = null) => errorHandler.handleBusinessError(message, code)
export const handleValidationError = (errors, context = '') => errorHandler.handleValidationError(errors, context)

export default errorHandler