import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                */import{r as a,c as l,L as t,d as s,y as i,l as n,z as o,E as d,t as r,D as c,k as u,B as p,C as m,F as _,Y as y,n as f,af as v,e as g,u as b,a3 as h,A as w}from"./vue-vendor-Dy164gUc.js";import{bp as k,bq as V,aM as C,bm as z,bB as x,at as U,U as D,ay as j,Q as S,cf as T,T as B,a$ as L,aj as I,a4 as K,aw as $,b9 as E,b8 as A,aL as M,c9 as P,aY as O,bh as N,bi as R,p as q,ac as F,a9 as Y,az as Q,aA as G,aB as H,aC as J,ab as W,ao as X,bQ as Z,bw as ee,bx as ae,R as le,o as te}from"./element-plus-h2SQQM64.js";/* empty css                     *//* empty css                       *//* empty css                 *//* empty css                *//* empty css                */import"./utils-D1VZuEZr.js";const se={class:"dialog-footer"},ie=e({__name:"RoleDialog",props:{modelValue:{type:Boolean,default:!1},roleData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:u}){const p=e,m=u,_=a(),y=a(!1),f=l({get:()=>p.modelValue,set:e=>m("update:modelValue",e)}),v=l(()=>!!p.roleData?.id),g=t({name:"",display_name:"",description:"",is_system:!1,status:"active"}),b={name:[{required:!0,message:"请输入角色标识",trigger:"blur"},{pattern:/^[a-z_]+$/,message:"角色标识只能包含小写字母和下划线",trigger:"blur"}],display_name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]};s(()=>p.roleData,e=>{e&&Object.keys(e).length>0?Object.assign(g,{name:e.name||"",display_name:e.display_name||"",description:e.description||"",is_system:e.is_system||!1,status:e.status||"active"}):Object.assign(g,{name:"",display_name:"",description:"",is_system:!1,status:"active"})},{immediate:!0,deep:!0});const h=async()=>{try{await _.value.validate(),y.value=!0,await new Promise(e=>setTimeout(e,1e3)),S.success(v.value?"角色更新成功":"角色创建成功"),m("success"),w()}catch(e){console.error("表单验证失败:",e)}finally{y.value=!1}},w=()=>{_.value?.resetFields(),f.value=!1};return(e,a)=>{const l=C,t=V,s=x,u=z,p=k,m=U,S=j;return n(),i(S,{modelValue:f.value,"onUpdate:modelValue":a[5]||(a[5]=e=>f.value=e),title:v.value?"编辑角色":"创建角色",width:"600px","before-close":w,class:"modern-dialog"},{footer:o(()=>[r("div",se,[d(m,{onClick:w},{default:o(()=>a[12]||(a[12]=[c("取消",-1)])),_:1,__:[12]}),d(m,{type:"primary",onClick:h,loading:y.value},{default:o(()=>[c(D(v.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:o(()=>[d(p,{ref_key:"formRef",ref:_,model:g,rules:b,"label-width":"100px",size:"large"},{default:o(()=>[d(t,{label:"角色标识",prop:"name"},{default:o(()=>[d(l,{modelValue:g.name,"onUpdate:modelValue":a[0]||(a[0]=e=>g.name=e),placeholder:"请输入角色标识（英文）",disabled:v.value},null,8,["modelValue","disabled"]),a[6]||(a[6]=r("div",{class:"form-tip"},"角色标识用于系统内部识别，创建后不可修改",-1))]),_:1,__:[6]}),d(t,{label:"角色名称",prop:"display_name"},{default:o(()=>[d(l,{modelValue:g.display_name,"onUpdate:modelValue":a[1]||(a[1]=e=>g.display_name=e),placeholder:"请输入角色显示名称"},null,8,["modelValue"])]),_:1}),d(t,{label:"角色描述",prop:"description"},{default:o(()=>[d(l,{modelValue:g.description,"onUpdate:modelValue":a[2]||(a[2]=e=>g.description=e),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),d(t,{label:"角色类型",prop:"is_system"},{default:o(()=>[d(u,{modelValue:g.is_system,"onUpdate:modelValue":a[3]||(a[3]=e=>g.is_system=e)},{default:o(()=>[d(s,{label:!1},{default:o(()=>a[7]||(a[7]=[c("自定义角色",-1)])),_:1,__:[7]}),d(s,{label:!0},{default:o(()=>a[8]||(a[8]=[c("系统角色",-1)])),_:1,__:[8]})]),_:1},8,["modelValue"]),a[9]||(a[9]=r("div",{class:"form-tip"},"系统角色具有特殊权限，请谨慎选择",-1))]),_:1,__:[9]}),d(t,{label:"状态",prop:"status"},{default:o(()=>[d(u,{modelValue:g.status,"onUpdate:modelValue":a[4]||(a[4]=e=>g.status=e)},{default:o(()=>[d(s,{label:"active"},{default:o(()=>a[10]||(a[10]=[c("启用",-1)])),_:1,__:[10]}),d(s,{label:"inactive"},{default:o(()=>a[11]||(a[11]=[c("禁用",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-0b474751"]]),ne={class:"permission-content"},oe={class:"permission-tree"},de={class:"tree-header"},re={class:"tree-actions"},ce={class:"tree-node"},ue={class:"node-content"},pe={class:"node-label"},me={key:0,class:"node-description"},_e={class:"selected-permissions"},ye={class:"permission-tags"},fe={class:"dialog-footer"},ve=e({__name:"PermissionConfigDialog",props:{modelValue:{type:Boolean,default:!1},roleData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:t}){const v=e,g=t,b=a(),h=a(!1),w=a([]),k=a([]),V=l({get:()=>v.modelValue,set:e=>g("update:modelValue",e)}),C={children:"children",label:"label"},z=[{id:"dashboard",label:"数据看板",icon:"Monitor",color:"#409eff",type:"模块",description:"查看系统数据统计和分析",children:[{id:"dashboard.view",label:"查看数据看板",icon:"View",type:"查看"},{id:"dashboard.export",label:"导出数据",icon:"Download",type:"操作"}]},{id:"user",label:"用户管理",icon:"User",color:"#67c23a",type:"模块",description:"管理系统用户信息和权限",children:[{id:"user.list",label:"查看用户列表",icon:"List",type:"查看"},{id:"user.create",label:"创建用户",icon:"Plus",type:"创建"},{id:"user.edit",label:"编辑用户",icon:"Edit",type:"编辑"},{id:"user.delete",label:"删除用户",icon:"Delete",type:"删除"},{id:"user.export",label:"导出用户数据",icon:"Download",type:"操作"}]},{id:"community",label:"社群管理",icon:"Comment",color:"#e6a23c",type:"模块",description:"管理微信群组和社群活动",children:[{id:"community.list",label:"查看群组列表",icon:"List",type:"查看"},{id:"community.create",label:"创建群组",icon:"Plus",type:"创建"},{id:"community.edit",label:"编辑群组",icon:"Edit",type:"编辑"},{id:"community.delete",label:"删除群组",icon:"Delete",type:"删除"},{id:"community.analytics",label:"群组数据分析",icon:"DataLine",type:"分析"}]},{id:"finance",label:"财务管理",icon:"Money",color:"#f56c6c",type:"模块",description:"管理财务数据和交易记录",children:[{id:"finance.dashboard",label:"财务总览",icon:"Monitor",type:"查看"},{id:"finance.transactions",label:"交易记录",icon:"List",type:"查看"},{id:"finance.commission",label:"佣金管理",icon:"Money",type:"管理"},{id:"finance.withdraw",label:"提现管理",icon:"Upload",type:"管理"},{id:"finance.export",label:"导出财务数据",icon:"Download",type:"操作"}]},{id:"distribution",label:"分销管理",icon:"Share",color:"#909399",type:"模块",description:"管理分销网络和代理商",children:[{id:"distribution.list",label:"分销商列表",icon:"List",type:"查看"},{id:"distribution.create",label:"添加分销商",icon:"Plus",type:"创建"},{id:"distribution.edit",label:"编辑分销商",icon:"Edit",type:"编辑"},{id:"distribution.analytics",label:"分销数据分析",icon:"DataLine",type:"分析"}]},{id:"permission",label:"权限管理",icon:"Lock",color:"#606266",type:"模块",description:"管理系统角色和权限配置",children:[{id:"permission.roles",label:"角色管理",icon:"User",type:"管理"},{id:"permission.permissions",label:"权限配置",icon:"Key",type:"配置"},{id:"permission.assign",label:"分配权限",icon:"Setting",type:"操作"}]},{id:"system",label:"系统管理",icon:"Setting",color:"#303133",type:"模块",description:"系统设置和维护功能",children:[{id:"system.settings",label:"系统设置",icon:"Tools",type:"设置"},{id:"system.logs",label:"操作日志",icon:"List",type:"查看"},{id:"system.backup",label:"数据备份",icon:"Download",type:"操作"},{id:"system.monitor",label:"系统监控",icon:"Monitor",type:"监控"}]}],x=(e,a)=>{I()},I=()=>{const e=b.value?.getCheckedKeys()||[],a=b.value?.getCheckedNodes()||[];k.value=a.filter(e=>!e.children||0===e.children.length),w.value=e},K=()=>{const e=[],a=l=>{l.forEach(l=>{l.children&&l.children.length>0&&(e.push(l.id),a(l.children))})};a(z),f(()=>{e.forEach(e=>{const a=b.value?.getNode(e);a&&(a.expanded=!0)})})},$=()=>{const e=[],a=l=>{l.forEach(l=>{l.children&&l.children.length>0&&(e.push(l.id),a(l.children))})};a(z),f(()=>{e.forEach(e=>{const a=b.value?.getNode(e);a&&(a.expanded=!1)})})},E=()=>{const e=[],a=l=>{l.forEach(l=>{e.push(l.id),l.children&&l.children.length>0&&a(l.children)})};a(z),b.value?.setCheckedKeys(e),I()},A=()=>{b.value?.setCheckedKeys([]),I()};s(()=>v.roleData,e=>{if(e&&e.id){const e=["dashboard.view","user.list","user.create","community.list","finance.dashboard"];f(()=>{b.value?.setCheckedKeys(e),I()})}},{immediate:!0});const M=async()=>{try{h.value=!0;const e=b.value?.getCheckedKeys()||[];await new Promise(e=>setTimeout(e,1e3)),console.log("保存权限配置:",{roleId:v.roleData?.id,permissions:e}),S.success("权限配置保存成功"),g("success"),P()}catch(e){S.error("保存失败")}finally{h.value=!1}},P=()=>{V.value=!1};return(a,l)=>{const t=U,s=B,f=L,v=T,g=j;return n(),i(g,{modelValue:V.value,"onUpdate:modelValue":l[0]||(l[0]=e=>V.value=e),title:`配置角色权限 - ${e.roleData?.display_name||e.roleData?.name}`,width:"800px","before-close":P,class:"modern-dialog permission-dialog"},{footer:o(()=>[r("div",fe,[d(t,{onClick:P},{default:o(()=>l[6]||(l[6]=[c("取消",-1)])),_:1,__:[6]}),d(t,{type:"primary",onClick:M,loading:h.value},{default:o(()=>l[7]||(l[7]=[c(" 保存权限配置 ",-1)])),_:1,__:[7]},8,["loading"])])]),default:o(()=>[r("div",ne,[r("div",oe,[r("div",de,[l[5]||(l[5]=r("h4",null,"权限配置",-1)),r("div",re,[d(t,{size:"small",onClick:K},{default:o(()=>l[1]||(l[1]=[c("全部展开",-1)])),_:1,__:[1]}),d(t,{size:"small",onClick:$},{default:o(()=>l[2]||(l[2]=[c("全部收起",-1)])),_:1,__:[2]}),d(t,{size:"small",type:"primary",onClick:E},{default:o(()=>l[3]||(l[3]=[c("全选",-1)])),_:1,__:[3]}),d(t,{size:"small",onClick:A},{default:o(()=>l[4]||(l[4]=[c("取消全选",-1)])),_:1,__:[4]})])]),d(v,{ref_key:"treeRef",ref:b,data:z,props:C,"show-checkbox":"","node-key":"id","default-checked-keys":w.value,onCheck:x,class:"permission-tree-component"},{default:o(({node:e,data:a})=>{return[r("div",ce,[r("div",ue,[d(s,{class:"node-icon",color:a.color||"#409eff"},{default:o(()=>[(n(),i(m(a.icon||"Key")))]),_:2},1032,["color"]),r("span",pe,D(e.label),1),a.type?(n(),i(f,{key:0,size:"small",type:(l=a.type,{"模块":"primary","查看":"info","创建":"success","编辑":"warning","删除":"danger","操作":"primary","管理":"warning","配置":"info","分析":"success","设置":"info","监控":"warning"}[l]||"")},{default:o(()=>[c(D(a.type),1)]),_:2},1032,["type"])):p("",!0)]),a.description?(n(),u("div",me,D(a.description),1)):p("",!0)])];var l}),_:1},8,["default-checked-keys"])]),r("div",_e,[r("h4",null,"已选权限 ("+D(k.value.length)+")",1),r("div",ye,[(n(!0),u(_,null,y(k.value,e=>(n(),i(f,{key:e.id,closable:"",onClose:a=>{return l=e.id,b.value?.setChecked(l,!1),void I();var l},class:"permission-tag"},{default:o(()=>[d(s,null,{default:o(()=>[(n(),i(m(e.icon||"Key")))]),_:2},1024),c(" "+D(e.label),1)]),_:2},1032,["onClose"]))),128))])])])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-c0863620"]]),ge={class:"modern-role-management"},be={class:"page-header"},he={class:"header-content"},we={class:"header-left"},ke={class:"header-icon"},Ve={class:"header-actions"},Ce={class:"stats-section"},ze={class:"stats-container"},xe={class:"stat-content"},Ue={class:"stat-value"},De={class:"stat-label"},je={class:"filter-section"},Se={class:"filter-content"},Te={class:"filter-left"},Be={class:"filter-right"},Le={class:"table-section"},Ie={class:"table-header"},Ke={class:"table-title"},$e={class:"table-actions"},Ee={class:"role-info"},Ae={class:"role-details"},Me={class:"role-name"},Pe={class:"role-code"},Oe={class:"time-info"},Ne={class:"action-buttons"},Re={class:"pagination-wrapper"},qe=e({__name:"RoleManagement",setup(e){v();const l=a(!1),s=a([]),f=a([]),k=a(!1),V=a(!1),z=a({}),x=a(0),j=t({keyword:"",status:"",type:""}),T=t({page:1,size:20}),se=a([{key:"total",label:"总角色数",value:8,icon:"Avatar",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+2"},{key:"active",label:"启用角色",value:6,icon:"UserFilled",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+1"},{key:"permissions",label:"总权限数",value:45,icon:"Key",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"+5"},{key:"users",label:"已分配用户",value:156,icon:"Management",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12"}]),ne=[{id:1,name:"admin",display_name:"超级管理员",description:"拥有系统所有权限，可以管理所有功能模块",permissions_count:45,users_count:3,is_system:!0,status:"active",created_at:"2024-01-01 10:00:00"},{id:2,name:"substation",display_name:"分站管理员",description:"管理分站运营，拥有分站内所有权限",permissions_count:32,users_count:8,is_system:!0,status:"active",created_at:"2024-01-15 14:20:00"},{id:3,name:"agent",display_name:"代理商",description:"管理下级分销员，拥有团队管理权限",permissions_count:25,users_count:23,is_system:!0,status:"active",created_at:"2024-02-01 09:30:00"},{id:4,name:"distributor",display_name:"分销员",description:"推广群组链接，获得佣金收益",permissions_count:18,users_count:89,is_system:!0,status:"active",created_at:"2024-02-15 16:10:00"},{id:5,name:"group_owner",display_name:"群主",description:"管理自己的群组，发布群组内容",permissions_count:12,users_count:34,is_system:!0,status:"active",created_at:"2024-03-01 11:45:00"},{id:6,name:"user",display_name:"普通用户",description:"基础用户权限，可以加入群组",permissions_count:8,users_count:1234,is_system:!0,status:"active",created_at:"2024-01-01 10:00:00"},{id:7,name:"custom_role",display_name:"自定义角色",description:"用户自定义的角色，可以灵活配置权限",permissions_count:15,users_count:5,is_system:!1,status:"active",created_at:"2024-04-01 15:30:00"},{id:8,name:"test_role",display_name:"测试角色",description:"用于测试的角色，已禁用",permissions_count:10,users_count:0,is_system:!1,status:"inactive",created_at:"2024-05-01 09:15:00"}],oe=e=>({admin:"Star",substation:"Management",agent:"Avatar",distributor:"Share",group_owner:"UserFilled",user:"User",custom_role:"Setting",test_role:"Key"}[e]||"Setting"),de=e=>e?new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):"",re=()=>{z.value={},k.value=!0},ce=async e=>{try{await le.confirm(`确定要复制角色"${e.display_name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const a={...e,id:Date.now(),name:`${e.name}_copy`,display_name:`${e.display_name}_副本`,is_system:!1,users_count:0,created_at:(new Date).toISOString()};s.value.push(a),x.value++,S.success("角色复制成功")}catch(a){"cancel"!==a&&S.error("复制失败")}},ue=e=>{S.info(`用户管理功能开发中... 角色：${e.display_name}`)},pe=async e=>{const a="active"===e.status?"inactive":"active",l="inactive"===a?"禁用":"启用";try{await le.confirm(`确定要${l}角色"${e.display_name}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status=a,S.success(`${l}成功`)}catch(t){"cancel"!==t&&S.error("操作失败")}},me=async e=>{if(e.is_system)S.warning("系统角色不能删除");else try{await le.confirm(`确定要删除角色"${e.display_name}"吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),x.value--),S.success("删除成功")}catch(a){"cancel"!==a&&S.error("删除失败")}},_e=e=>{f.value=e},ye=async()=>{if(0===f.value.length)return void S.warning("请选择要删除的角色");if(f.value.filter(e=>e.is_system).length>0)S.warning("选中的角色包含系统角色，系统角色不能删除");else try{await le.confirm(`确定要批量删除选中的 ${f.value.length} 个角色吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),f.value.forEach(e=>{const a=s.value.findIndex(a=>a.id===e.id);a>-1&&(s.value.splice(a,1),x.value--)}),f.value=[],S.success("批量删除成功")}catch(e){"cancel"!==e&&S.error("批量删除失败")}},fe=()=>{l.value=!0,setTimeout(()=>{let e=[...ne];if(j.keyword&&(e=e.filter(e=>e.display_name.includes(j.keyword)||e.name.includes(j.keyword)||e.description.includes(j.keyword))),j.status&&(e=e.filter(e=>e.status===j.status)),j.type){const a="system"===j.type;e=e.filter(e=>e.is_system===a)}s.value=e,x.value=e.length,l.value=!1},500)},qe=()=>{j.keyword="",j.status="",j.type="",s.value=[...ne],x.value=ne.length,S.info("搜索条件已重置")},Fe=e=>{T.page=e},Ye=e=>{T.size=e,T.page=1},Qe=async()=>{try{S.success("导出功能开发中...")}catch(e){S.error("导出失败")}},Ge=()=>{s.value=[...ne],S.success("角色信息更新成功")},He=()=>{s.value=[...ne],S.success("权限配置更新成功")};return g(()=>{s.value=[...ne],x.value=ne.length}),(e,a)=>{const t=B,v=U,g=C,S=A,le=E,ne=O,Je=L,We=R,Xe=J,Ze=H,ea=Q,aa=N,la=ae,ta=ee;return n(),u("div",ge,[r("div",be,[r("div",he,[r("div",we,[r("div",ke,[d(t,{size:"24"},{default:o(()=>[d(b(I))]),_:1})]),a[7]||(a[7]=r("div",{class:"header-text"},[r("h1",null,"角色管理"),r("p",null,"管理系统角色权限，配置用户访问控制和功能权限")],-1))]),r("div",Ve,[d(v,{onClick:Qe,class:"action-btn secondary"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(K))]),_:1}),a[8]||(a[8]=c(" 导出角色 ",-1))]),_:1,__:[8]}),d(v,{type:"primary",onClick:re,class:"action-btn primary"},{default:o(()=>[d(t,null,{default:o(()=>[d(b($))]),_:1}),a[9]||(a[9]=c(" 创建角色 ",-1))]),_:1,__:[9]})])])]),r("div",Ce,[r("div",ze,[(n(!0),u(_,null,y(se.value,e=>(n(),u("div",{class:"stat-card",key:e.key},[r("div",{class:"stat-icon",style:q({background:e.color})},[d(t,{size:"20"},{default:o(()=>[(n(),i(m(e.icon)))]),_:2},1024)],4),r("div",xe,[r("div",Ue,D(e.value),1),r("div",De,D(e.label),1)]),r("div",{class:te(["stat-trend",e.trend])},[d(t,{size:"14"},{default:o(()=>[(n(),i(m(e.trendIcon)))]),_:2},1024),r("span",null,D(e.change),1)],2)]))),128))])]),r("div",je,[d(ne,{class:"filter-card",shadow:"never"},{default:o(()=>[r("div",Se,[r("div",Te,[d(g,{modelValue:j.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>j.keyword=e),placeholder:"搜索角色名称、标识、描述","prefix-icon":"Search",clearable:"",class:"search-input",onKeyup:h(fe,["enter"])},null,8,["modelValue"]),d(le,{modelValue:j.status,"onUpdate:modelValue":a[1]||(a[1]=e=>j.status=e),placeholder:"角色状态",clearable:"",class:"filter-select"},{default:o(()=>[d(S,{label:"全部状态",value:""}),d(S,{label:"启用",value:"active"}),d(S,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"]),d(le,{modelValue:j.type,"onUpdate:modelValue":a[2]||(a[2]=e=>j.type=e),placeholder:"角色类型",clearable:"",class:"filter-select"},{default:o(()=>[d(S,{label:"全部类型",value:""}),d(S,{label:"系统角色",value:"system"}),d(S,{label:"自定义角色",value:"custom"})]),_:1},8,["modelValue"])]),r("div",Be,[d(v,{onClick:fe,type:"primary",class:"search-btn"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(M))]),_:1}),a[10]||(a[10]=c(" 搜索 ",-1))]),_:1,__:[10]}),d(v,{onClick:qe,class:"reset-btn"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(P))]),_:1}),a[11]||(a[11]=c(" 重置 ",-1))]),_:1,__:[11]})])])]),_:1})]),r("div",Le,[d(ne,{class:"table-card",shadow:"never"},{header:o(()=>[r("div",Ie,[r("div",Ke,[a[12]||(a[12]=r("span",null,"角色列表",-1)),d(Je,{size:"small",type:"info"},{default:o(()=>[c("共 "+D(x.value)+" 个角色",1)]),_:1})]),r("div",$e,[f.value.length>0?(n(),i(v,{key:0,onClick:ye,type:"danger",size:"small",plain:""},{default:o(()=>[d(t,null,{default:o(()=>[d(b(Z))]),_:1}),c(" 批量删除 ("+D(f.value.length)+") ",1)]),_:1})):p("",!0)])])]),default:o(()=>[w((n(),i(aa,{data:s.value,onSelectionChange:_e,class:"modern-table",stripe:"",border:""},{default:o(()=>[d(We,{type:"selection",width:"55",align:"center"}),d(We,{label:"角色信息","min-width":"200"},{default:o(({row:e})=>{return[r("div",Ee,[r("div",{class:"role-icon",style:q({background:(a=e.name,{admin:"linear-gradient(135deg, #f56c6c 0%, #ff9a9e 100%)",substation:"linear-gradient(135deg, #e6a23c 0%, #ffd666 100%)",agent:"linear-gradient(135deg, #409eff 0%, #4facfe 100%)",distributor:"linear-gradient(135deg, #67c23a 0%, #43e97b 100%)",group_owner:"linear-gradient(135deg, #909399 0%, #c0c4cc 100%)",user:"linear-gradient(135deg, #909399 0%, #c0c4cc 100%)",custom_role:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",test_role:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"}[a]||"linear-gradient(135deg, #667eea 0%, #764ba2 100%)")})},[d(t,{size:"18",color:"white"},{default:o(()=>[(n(),i(m(oe(e.name))))]),_:2},1024)],4),r("div",Ae,[r("div",Me,D(e.display_name||e.name),1),r("div",Pe,D(e.name),1)])])];var a}),_:1}),d(We,{label:"角色描述",prop:"description","min-width":"200"}),d(We,{label:"权限数量",width:"100",align:"center"},{default:o(({row:e})=>[d(Je,{type:"info",size:"small"},{default:o(()=>[c(D(e.permissions_count||0),1)]),_:2},1024)]),_:1}),d(We,{label:"用户数量",width:"100",align:"center"},{default:o(({row:e})=>[d(Je,{type:"primary",size:"small"},{default:o(()=>[c(D(e.users_count||0),1)]),_:2},1024)]),_:1}),d(We,{label:"角色类型",width:"100",align:"center"},{default:o(({row:e})=>[d(Je,{type:e.is_system?"warning":"success",size:"small"},{default:o(()=>[c(D(e.is_system?"系统角色":"自定义"),1)]),_:2},1032,["type"])]),_:1}),d(We,{label:"状态",width:"80",align:"center"},{default:o(({row:e})=>[d(Je,{type:"active"===e.status?"success":"danger",size:"small"},{default:o(()=>[c(D("active"===e.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),d(We,{label:"创建时间",width:"160"},{default:o(({row:e})=>{return[r("div",Oe,[r("div",null,D((a=e.created_at,a?new Date(a).toLocaleDateString("zh-CN"):"")),1),r("small",null,D(de(e.created_at)),1)])];var a}),_:1}),d(We,{label:"操作",width:"280",fixed:"right"},{default:o(({row:e})=>[r("div",Ne,[d(v,{onClick:a=>(e=>{z.value={...e},k.value=!0})(e),type:"primary",size:"small",plain:""},{default:o(()=>[d(t,null,{default:o(()=>[d(b(F))]),_:1}),a[13]||(a[13]=c(" 编辑 ",-1))]),_:2,__:[13]},1032,["onClick"]),d(v,{onClick:a=>(e=>{z.value={...e},V.value=!0})(e),type:"success",size:"small",plain:""},{default:o(()=>[d(t,null,{default:o(()=>[d(b(Y))]),_:1}),a[14]||(a[14]=c(" 权限 ",-1))]),_:2,__:[14]},1032,["onClick"]),d(ea,{onCommand:a=>((e,a)=>{switch(e){case"copy":ce(a);break;case"users":ue(a);break;case"toggle":pe(a);break;case"delete":me(a)}})(a,e),trigger:"click"},{dropdown:o(()=>[d(Ze,null,{default:o(()=>[d(Xe,{command:"copy"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(W))]),_:1}),a[16]||(a[16]=c(" 复制角色 ",-1))]),_:1,__:[16]}),d(Xe,{command:"users"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(X))]),_:1}),a[17]||(a[17]=c(" 用户管理 ",-1))]),_:1,__:[17]}),d(Xe,{command:"toggle",divided:!0},{default:o(()=>[d(t,null,{default:o(()=>[(n(),i(m("active"===e.status?"Lock":"Unlock")))]),_:2},1024),c(" "+D("active"===e.status?"禁用角色":"启用角色"),1)]),_:2},1024),e.is_system?p("",!0):(n(),i(Xe,{key:0,command:"delete"},{default:o(()=>[d(t,null,{default:o(()=>[d(b(Z))]),_:1}),a[18]||(a[18]=c(" 删除角色 ",-1))]),_:1,__:[18]}))]),_:2},1024)]),default:o(()=>[d(v,{type:"info",size:"small",plain:""},{default:o(()=>[d(t,null,{default:o(()=>[d(b(G))]),_:1}),a[15]||(a[15]=c(" 更多 ",-1))]),_:1,__:[15]})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[ta,l.value]]),r("div",Re,[d(la,{"current-page":T.page,"onUpdate:currentPage":a[3]||(a[3]=e=>T.page=e),"page-size":T.size,"onUpdate:pageSize":a[4]||(a[4]=e=>T.size=e),total:x.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ye,onCurrentChange:Fe,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),d(ie,{modelValue:k.value,"onUpdate:modelValue":a[5]||(a[5]=e=>k.value=e),"role-data":z.value,onSuccess:Ge},null,8,["modelValue","role-data"]),d(ve,{modelValue:V.value,"onUpdate:modelValue":a[6]||(a[6]=e=>V.value=e),"role-data":z.value,onSuccess:He},null,8,["modelValue","role-data"])])}}},[["__scopeId","data-v-be66fda8"]]);export{qe as default};
