<template>
  <div class="video-player" :class="{ 'is-mobile': isMobile }">
    <div class="video-container" ref="videoContainer">
      <video
        ref="videoElement"
        :src="src"
        :poster="poster"
        :muted="muted"
        :autoplay="autoplay"
        :loop="loop"
        :controls="showControls"
        :playsinline="true"
        :webkit-playsinline="true"
        :preload="preload"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedData"
        @canplay="onCanPlay"
        @play="onPlay"
        @pause="onPause"
        @ended="onEnded"
        @error="onError"
        @timeupdate="onTimeUpdate"
        @volumechange="onVolumeChange"
      >
        <source :src="src" :type="videoType">
        您的浏览器不支持视频播放
      </video>

      <!-- 自定义控制层 -->
      <div v-if="!showControls" class="custom-controls" :class="{ 'show': showCustomControls }">
        <!-- 播放/暂停按钮 -->
        <div class="play-pause-overlay" @click="togglePlay">
          <div class="play-button" v-if="!isPlaying">
            <el-icon size="60"><VideoPlay /></el-icon>
          </div>
        </div>

        <!-- 底部控制栏 -->
        <div class="controls-bar">
          <div class="controls-left">
            <button class="control-btn" @click="togglePlay">
              <el-icon v-if="isPlaying"><VideoPause /></el-icon>
              <el-icon v-else><VideoPlay /></el-icon>
            </button>
            
            <div class="time-display">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>
          </div>

          <div class="controls-center">
            <div class="progress-container" @click="seekTo">
              <div class="progress-bar">
                <div class="progress-buffer" :style="{ width: `${bufferedPercent}%` }"></div>
                <div class="progress-played" :style="{ width: `${playedPercent}%` }"></div>
                <div class="progress-thumb" :style="{ left: `${playedPercent}%` }"></div>
              </div>
            </div>
          </div>

          <div class="controls-right">
            <button class="control-btn" @click="toggleMute">
              <el-icon v-if="isMuted"><Mute /></el-icon>
              <el-icon v-else><Microphone /></el-icon>
            </button>
            
            <div class="volume-container" v-if="!isMobile">
              <div class="volume-slider" @click="setVolume">
                <div class="volume-bar">
                  <div class="volume-fill" :style="{ width: `${volume * 100}%` }"></div>
                </div>
              </div>
            </div>

            <button class="control-btn" @click="toggleFullscreen" v-if="!isMobile">
              <el-icon><FullScreen /></el-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <el-icon class="is-loading"><Loading /></el-icon>
        </div>
        <div class="loading-text">视频加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-icon">
          <el-icon size="48"><Warning /></el-icon>
        </div>
        <div class="error-text">视频加载失败</div>
        <button class="retry-btn" @click="retry">重试</button>
      </div>
    </div>

    <!-- 视频信息 -->
    <div v-if="showInfo && (title || description)" class="video-info">
      <h3 v-if="title" class="video-title">{{ title }}</h3>
      <p v-if="description" class="video-description">{{ description }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  VideoPlay, VideoPause, Microphone, Mute,
  FullScreen, Loading, Warning
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  src: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: true
  },
  loop: {
    type: Boolean,
    default: false
  },
  showControls: {
    type: Boolean,
    default: false
  },
  showInfo: {
    type: Boolean,
    default: true
  },
  preload: {
    type: String,
    default: 'metadata',
    validator: (value) => ['none', 'metadata', 'auto'].includes(value)
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: 'auto'
  }
})

// Emits
const emit = defineEmits([
  'loadstart', 'loadeddata', 'canplay', 'play', 'pause', 
  'ended', 'error', 'timeupdate', 'volumechange'
])

// 响应式数据
const videoElement = ref(null)
const videoContainer = ref(null)
const isPlaying = ref(false)
const isLoading = ref(false)
const hasError = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(props.muted)
const isFullscreen = ref(false)
const showCustomControls = ref(false)
const bufferedPercent = ref(0)

// 计算属性
const isMobile = computed(() => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

const videoType = computed(() => {
  const extension = props.src.split('.').pop().toLowerCase()
  const typeMap = {
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'ogg': 'video/ogg',
    'mov': 'video/quicktime',
    'avi': 'video/x-msvideo'
  }
  return typeMap[extension] || 'video/mp4'
})

const playedPercent = computed(() => {
  return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
})

// 方法
const togglePlay = () => {
  if (!videoElement.value) return
  
  if (isPlaying.value) {
    videoElement.value.pause()
  } else {
    videoElement.value.play()
  }
}

const toggleMute = () => {
  if (!videoElement.value) return
  
  videoElement.value.muted = !videoElement.value.muted
  isMuted.value = videoElement.value.muted
}

const setVolume = (event) => {
  if (!videoElement.value) return
  
  const rect = event.currentTarget.getBoundingClientRect()
  const percent = (event.clientX - rect.left) / rect.width
  const newVolume = Math.max(0, Math.min(1, percent))
  
  videoElement.value.volume = newVolume
  volume.value = newVolume
  
  if (newVolume > 0 && isMuted.value) {
    videoElement.value.muted = false
    isMuted.value = false
  }
}

const seekTo = (event) => {
  if (!videoElement.value || duration.value === 0) return
  
  const rect = event.currentTarget.getBoundingClientRect()
  const percent = (event.clientX - rect.left) / rect.width
  const newTime = percent * duration.value
  
  videoElement.value.currentTime = newTime
}

const toggleFullscreen = () => {
  if (!videoContainer.value) return
  
  if (!isFullscreen.value) {
    if (videoContainer.value.requestFullscreen) {
      videoContainer.value.requestFullscreen()
    } else if (videoContainer.value.webkitRequestFullscreen) {
      videoContainer.value.webkitRequestFullscreen()
    } else if (videoContainer.value.msRequestFullscreen) {
      videoContainer.value.msRequestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
  }
}

const formatTime = (seconds) => {
  if (isNaN(seconds)) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const updateBuffered = () => {
  if (!videoElement.value || !videoElement.value.buffered.length) return
  
  const buffered = videoElement.value.buffered
  const currentTime = videoElement.value.currentTime
  
  for (let i = 0; i < buffered.length; i++) {
    if (buffered.start(i) <= currentTime && currentTime <= buffered.end(i)) {
      bufferedPercent.value = (buffered.end(i) / duration.value) * 100
      break
    }
  }
}

const retry = () => {
  hasError.value = false
  isLoading.value = true
  if (videoElement.value) {
    videoElement.value.load()
  }
}

// 事件处理
const onLoadStart = (event) => {
  isLoading.value = true
  hasError.value = false
  emit('loadstart', event)
}

const onLoadedData = (event) => {
  isLoading.value = false
  duration.value = videoElement.value.duration
  emit('loadeddata', event)
}

const onCanPlay = (event) => {
  isLoading.value = false
  emit('canplay', event)
}

const onPlay = (event) => {
  isPlaying.value = true
  emit('play', event)
}

const onPause = (event) => {
  isPlaying.value = false
  emit('pause', event)
}

const onEnded = (event) => {
  isPlaying.value = false
  emit('ended', event)
}

const onError = (event) => {
  isLoading.value = false
  hasError.value = true
  emit('error', event)
}

const onTimeUpdate = (event) => {
  currentTime.value = videoElement.value.currentTime
  updateBuffered()
  emit('timeupdate', event)
}

const onVolumeChange = (event) => {
  volume.value = videoElement.value.volume
  isMuted.value = videoElement.value.muted
  emit('volumechange', event)
}

// 控制栏显示/隐藏
let controlsTimer = null

const showControls = () => {
  showCustomControls.value = true
  clearTimeout(controlsTimer)
  controlsTimer = setTimeout(() => {
    if (isPlaying.value) {
      showCustomControls.value = false
    }
  }, 3000)
}

const hideControls = () => {
  if (!isPlaying.value) return
  showCustomControls.value = false
}

// 全屏状态监听
const onFullscreenChange = () => {
  isFullscreen.value = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  )
}

// 生命周期
onMounted(() => {
  if (videoContainer.value) {
    videoContainer.value.addEventListener('mouseenter', showControls)
    videoContainer.value.addEventListener('mousemove', showControls)
    videoContainer.value.addEventListener('mouseleave', hideControls)
  }
  
  document.addEventListener('fullscreenchange', onFullscreenChange)
  document.addEventListener('webkitfullscreenchange', onFullscreenChange)
  document.addEventListener('msfullscreenchange', onFullscreenChange)
})

onUnmounted(() => {
  if (controlsTimer) {
    clearTimeout(controlsTimer)
  }
  
  if (videoContainer.value) {
    videoContainer.value.removeEventListener('mouseenter', showControls)
    videoContainer.value.removeEventListener('mousemove', showControls)
    videoContainer.value.removeEventListener('mouseleave', hideControls)
  }
  
  document.removeEventListener('fullscreenchange', onFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', onFullscreenChange)
  document.removeEventListener('msfullscreenchange', onFullscreenChange)
})

// 监听器
watch(() => props.src, () => {
  hasError.value = false
  isLoading.value = true
})
</script>

<style lang="scss" scoped>
.video-player {
  width: v-bind(width);
  position: relative;
  
  .video-container {
    position: relative;
    width: 100%;
    height: v-bind(height);
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    
    video {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
    }
  }
  
  .custom-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    
    &.show {
      opacity: 1;
    }
    
    .play-pause-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      .play-button {
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(0, 0, 0, 0.8);
          transform: scale(1.1);
        }
      }
    }
    
    .controls-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      padding: 20px 15px 15px;
      display: flex;
      align-items: center;
      gap: 15px;
      
      .controls-left {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .controls-center {
        flex: 1;
      }
      
      .controls-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .control-btn {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: background 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
      
      .time-display {
        color: white;
        font-size: 12px;
        font-family: monospace;
        min-width: 80px;
      }
      
      .progress-container {
        cursor: pointer;
        padding: 5px 0;
        
        .progress-bar {
          height: 4px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
          position: relative;
          
          .progress-buffer {
            height: 100%;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 2px;
            transition: width 0.3s ease;
          }
          
          .progress-played {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: #409eff;
            border-radius: 2px;
            transition: width 0.1s ease;
          }
          
          .progress-thumb {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #409eff;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .progress-thumb {
          opacity: 1;
        }
      }
      
      .volume-container {
        display: flex;
        align-items: center;
        
        .volume-slider {
          width: 60px;
          cursor: pointer;
          padding: 5px 0;
          
          .volume-bar {
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 1.5px;
            position: relative;
            
            .volume-fill {
              height: 100%;
              background: white;
              border-radius: 1.5px;
              transition: width 0.1s ease;
            }
          }
        }
      }
    }
  }
  
  .loading-overlay,
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    
    .loading-spinner,
    .error-icon {
      margin-bottom: 15px;
      font-size: 24px;
    }
    
    .loading-text,
    .error-text {
      font-size: 14px;
      margin-bottom: 15px;
    }
    
    .retry-btn {
      background: #409eff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        background: #66b1ff;
      }
    }
  }
  
  .video-info {
    padding: 15px 0;
    
    .video-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }
    
    .video-description {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }
  
  &.is-mobile {
    .custom-controls {
      .play-pause-overlay .play-button {
        width: 60px;
        height: 60px;
        
        .el-icon {
          font-size: 40px;
        }
      }
      
      .controls-bar {
        padding: 15px 10px 10px;
        gap: 10px;
        
        .time-display {
          font-size: 11px;
          min-width: 70px;
        }
      }
    }
  }
}
</style>
