<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\DistributionGroup;
use App\Models\CommissionLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\CommissionService;

/**
 * 分销系统主控制器
 * 整合分销商、分销组、佣金等相关功能
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
class DistributionController extends Controller
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
        $this->middleware('auth:api');
        $this->middleware('role:admin')->except(['getMyDistributionInfo', 'getMyCommissions']);
    }

    /**
     * 获取分销系统总览统计
     */
    public function getOverview(Request $request)
    {
        $stats = [
            // 分销商统计
            'distributors' => [
                'total' => User::where('role', 'distributor')->count(),
                'active' => User::where('role', 'distributor')->where('status', 1)->count(),
                'inactive' => User::where('role', 'distributor')->where('status', 0)->count(),
                'today_new' => User::where('role', 'distributor')
                    ->whereDate('created_at', today())
                    ->count(),
            ],
            
            // 分销组统计
            'groups' => [
                'total' => DistributionGroup::count(),
                'active' => DistributionGroup::where('status', 1)->count(),
                'members_total' => User::whereNotNull('distribution_group_id')->count(),
            ],
            
            // 佣金统计
            'commissions' => [
                'total_amount' => CommissionLog::sum('amount'),
                'today_amount' => CommissionLog::whereDate('created_at', today())->sum('amount'),
                'month_amount' => CommissionLog::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->sum('amount'),
                'total_count' => CommissionLog::count(),
            ],
            
            // 订单相关统计
            'orders' => [
                'total_orders' => DB::table('orders')->where('status', 'paid')->count(),
                'commission_orders' => CommissionLog::distinct('order_id')->count('order_id'),
            ],
        ];

        // 最近7天趋势数据
        $trends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $trends[] = [
                'date' => $date,
                'new_distributors' => User::where('role', 'distributor')
                    ->whereDate('created_at', $date)
                    ->count(),
                'commission_amount' => CommissionLog::whereDate('created_at', $date)
                    ->sum('amount'),
                'commission_count' => CommissionLog::whereDate('created_at', $date)
                    ->count(),
            ];
        }

        return response()->json([
            'code' => 0,
            'data' => [
                'stats' => $stats,
                'trends' => $trends,
            ]
        ]);
    }

    /**
     * 获取分销商列表（简化版）
     */
    public function getDistributors(Request $request)
    {
        $query = User::where('role', 'distributor')
            ->with(['distributionGroup:id,name'])
            ->select(['id', 'name', 'email', 'level', 'status', 'distribution_group_id', 'created_at']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('group_id')) {
            $query->where('distribution_group_id', $request->group_id);
        }

        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%");
            });
        }

        $distributors = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'code' => 0,
            'data' => $distributors
        ]);
    }

    /**
     * 获取分销组列表（简化版）
     */
    public function getGroups(Request $request)
    {
        $query = DistributionGroup::withCount('members')
            ->select(['id', 'name', 'description', 'status', 'created_at']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $groups = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'code' => 0,
            'data' => $groups
        ]);
    }

    /**
     * 获取佣金记录
     */
    public function getCommissions(Request $request)
    {
        $query = CommissionLog::with(['user:id,name', 'order:id,order_no'])
            ->select(['id', 'user_id', 'order_id', 'amount', 'type', 'description', 'created_at']);

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $commissions = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'code' => 0,
            'data' => $commissions
        ]);
    }

    /**
     * 获取我的分销信息（用户端）
     */
    public function getMyDistributionInfo(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'distributor') {
            return response()->json([
                'code' => 1,
                'message' => '您不是分销商'
            ], 403);
        }

        $info = [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'level' => $user->level,
                'status' => $user->status,
                'balance' => $user->balance,
                'invite_code' => $user->invite_code,
            ],
            'group' => $user->distributionGroup,
            'stats' => [
                'children_count' => $user->children()->count(),
                'total_commission' => $user->commissionLogs()->sum('amount'),
                'month_commission' => $user->commissionLogs()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->sum('amount'),
                'order_count' => $user->commissionLogs()->distinct('order_id')->count('order_id'),
            ]
        ];

        return response()->json([
            'code' => 0,
            'data' => $info
        ]);
    }

    /**
     * 获取我的佣金记录（用户端）
     */
    public function getMyCommissions(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'distributor') {
            return response()->json([
                'code' => 1,
                'message' => '您不是分销商'
            ], 403);
        }

        $query = $user->commissionLogs()
            ->with(['order:id,order_no'])
            ->select(['id', 'order_id', 'amount', 'type', 'description', 'created_at']);

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $commissions = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'code' => 0,
            'data' => $commissions
        ]);
    }

    /**
     * 获取分销商等级配置
     */
    public function getLevelConfig()
    {
        // 这里可以从配置文件或数据库读取等级配置
        $levels = [
            1 => ['name' => '初级分销商', 'commission_rate' => 0.05, 'min_orders' => 0],
            2 => ['name' => '中级分销商', 'commission_rate' => 0.08, 'min_orders' => 10],
            3 => ['name' => '高级分销商', 'commission_rate' => 0.10, 'min_orders' => 50],
            4 => ['name' => '金牌分销商', 'commission_rate' => 0.12, 'min_orders' => 100],
            5 => ['name' => '钻石分销商', 'commission_rate' => 0.15, 'min_orders' => 200],
        ];

        return response()->json([
            'code' => 0,
            'data' => $levels
        ]);
    }

    /**
     * 生成邀请链接
     */
    public function generateInviteLink(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'distributor') {
            return response()->json([
                'code' => 1,
                'message' => '您不是分销商'
            ], 403);
        }

        $inviteCode = $user->invite_code;
        if (!$inviteCode) {
            // 生成邀请码
            $inviteCode = 'INV' . strtoupper(substr(md5($user->id . time()), 0, 8));
            $user->update(['invite_code' => $inviteCode]);
        }

        $baseUrl = config('app.url');
        $inviteLink = "{$baseUrl}/register?invite={$inviteCode}";

        return response()->json([
            'code' => 0,
            'data' => [
                'invite_code' => $inviteCode,
                'invite_link' => $inviteLink,
                'qr_code_url' => "{$baseUrl}/api/v1/distribution/qr-code/{$inviteCode}"
            ]
        ]);
    }

    /**
     * 批量操作分销商
     */
    public function batchOperation(Request $request)
    {
        $data = $request->validate([
            'action' => 'required|in:enable,disable,delete,upgrade',
            'ids' => 'required|array',
            'ids.*' => 'exists:users,id',
            'level' => 'required_if:action,upgrade|integer|min:1|max:5'
        ]);

        $count = 0;
        switch ($data['action']) {
            case 'enable':
                $count = User::whereIn('id', $data['ids'])
                    ->where('role', 'distributor')
                    ->update(['status' => 1]);
                break;
                
            case 'disable':
                $count = User::whereIn('id', $data['ids'])
                    ->where('role', 'distributor')
                    ->update(['status' => 0]);
                break;
                
            case 'upgrade':
                $count = User::whereIn('id', $data['ids'])
                    ->where('role', 'distributor')
                    ->update(['level' => $data['level']]);
                break;
                
            case 'delete':
                $count = User::whereIn('id', $data['ids'])
                    ->where('role', 'distributor')
                    ->delete();
                break;
        }

        return response()->json([
            'code' => 0,
            'message' => "成功操作 {$count} 个分销商",
            'data' => ['affected_count' => $count]
        ]);
    }

    /**
     * 导出分销数据
     */
    public function exportData(Request $request)
    {
        $type = $request->get('type', 'distributors'); // distributors, commissions, groups
        
        // 这里应该实现具体的导出逻辑
        // 可以使用 Laravel Excel 或其他导出库
        
        return response()->json([
            'code' => 0,
            'message' => '导出任务已创建，请稍后下载',
            'data' => [
                'export_id' => 'EXP' . time(),
                'download_url' => '/api/v1/distribution/download/' . time()
            ]
        ]);
    }
} 