<!-- 面包屑导航组件 -->
<!-- admin/src/components/navigation/Breadcrumb.vue -->

<template>
  <nav 
    class="enhanced-breadcrumb" 
    :class="breadcrumbClasses"
    v-if="items.length > 0"
  >
    <div class="breadcrumb-container">
      <!-- 首页按钮 -->
      <div class="home-button" @click="navigateHome" v-if="showHome">
        <el-tooltip content="返回首页" placement="bottom">
          <div class="home-btn">
            <el-icon><House /></el-icon>
            <span v-if="!compact">首页</span>
          </div>
        </el-tooltip>
      </div>
      
      <!-- 面包屑列表 -->
      <transition-group 
        name="breadcrumb-item" 
        tag="div" 
        class="breadcrumb-list"
        @enter="onEnter"
        @leave="onLeave"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="item.key || item.path || index"
          class="breadcrumb-item"
          :class="getItemClasses(item, index)"
        >
          <!-- 分隔符 -->
          <div class="breadcrumb-separator" v-if="index > 0 || showHome">
            <el-icon><component :is="separatorIcon" /></el-icon>
          </div>
          
          <!-- 面包屑项目 -->
          <div class="breadcrumb-link-wrapper">
            <!-- 可点击的链接 -->
            <router-link 
              v-if="item.path && index < visibleItems.length - 1"
              :to="item.path"
              class="breadcrumb-link clickable"
              @click="handleItemClick(item, index)"
            >
              <div class="item-content">
                <el-icon v-if="item.icon" class="item-icon">
                  <component :is="item.icon" />
                </el-icon>
                <span class="item-text">{{ item.title }}</span>
                <el-badge 
                  v-if="item.badge"
                  :value="item.badge"
                  :type="item.badgeType || 'primary'"
                  class="item-badge"
                />
              </div>
            </router-link>
            
            <!-- 当前页面（不可点击） -->
            <div 
              v-else
              class="breadcrumb-link current"
              :class="{ 'has-menu': item.actions && item.actions.length > 0 }"
            >
              <div class="item-content">
                <el-icon v-if="item.icon" class="item-icon">
                  <component :is="item.icon" />
                </el-icon>
                <span class="item-text">{{ item.title }}</span>
                <el-badge 
                  v-if="item.badge"
                  :value="item.badge"
                  :type="item.badgeType || 'primary'"
                  class="item-badge"
                />
                
                <!-- 当前页面操作菜单 -->
                <el-dropdown 
                  v-if="item.actions && item.actions.length > 0"
                  @command="handleActionCommand"
                  trigger="click"
                  class="page-actions"
                >
                  <el-button type="text" size="small" class="action-trigger">
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="action in item.actions"
                        :key="action.command"
                        :command="action.command"
                        :divided="action.divided"
                        :disabled="action.disabled"
                      >
                        <el-icon v-if="action.icon">
                          <component :is="action.icon" />
                        </el-icon>
                        {{ action.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </transition-group>
      
      <!-- 折叠指示器 -->
      <el-dropdown 
        v-if="hasHiddenItems"
        @command="handleHiddenItemClick"
        trigger="hover"
        class="hidden-items-dropdown"
      >
        <div class="breadcrumb-item collapsed-indicator">
          <div class="breadcrumb-separator">
            <el-icon><component :is="separatorIcon" /></el-icon>
          </div>
          <div class="breadcrumb-link ellipsis">
            <el-icon><More /></el-icon>
          </div>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="hidden-items-menu">
            <el-dropdown-item
              v-for="(item, index) in hiddenItems"
              :key="item.key || item.path || index"
              :command="{ item, index: hiddenStartIndex + index }"
              class="hidden-item"
            >
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              {{ item.title }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 页面信息 -->
    <div class="page-info" v-if="showPageInfo && currentPage">
      <div class="page-meta">
        <div class="page-title">{{ currentPage.title }}</div>
        <div class="page-description" v-if="currentPage.description">
          {{ currentPage.description }}
        </div>
        <div class="page-tags" v-if="currentPage.tags && currentPage.tags.length">
          <el-tag
            v-for="tag in currentPage.tags"
            :key="tag"
            size="small"
            :type="getTagType(tag)"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
      
      <!-- 页面操作 -->
      <div class="page-actions-bar" v-if="pageActions.length">
        <el-button
          v-for="action in pageActions"
          :key="action.key"
          :type="action.type || 'primary'"
          :size="action.size || 'small'"
          :icon="action.icon"
          @click="handlePageAction(action)"
          :disabled="action.disabled"
          :loading="action.loading"
        >
          {{ action.label }}
        </el-button>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useSafeTimer } from '@/utils/componentCleanup'
import { 
  ArrowRight, 
  ArrowDown,
  House, 
  More,
  Document,
  Setting,
  Edit,
  Delete,
  Share,
  Download
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 面包屑数据
  items: {
    type: Array,
    default: () => []
  },
  // 是否显示首页按钮
  showHome: {
    type: Boolean,
    default: true
  },
  // 分隔符图标
  separatorIcon: {
    type: String,
    default: 'ArrowRight'
  },
  // 最大显示数量
  maxItems: {
    type: Number,
    default: 5
  },
  // 紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 是否显示页面信息
  showPageInfo: {
    type: Boolean,
    default: false
  },
  // 当前页面信息
  currentPage: {
    type: Object,
    default: null
  },
  // 页面操作按钮
  pageActions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['item-click', 'action-click', 'page-action'])

const router = useRouter()
const route = useRoute()
const safeTimer = useSafeTimer()

// 响应式数据
const containerWidth = ref(0)

// 计算属性
const breadcrumbClasses = computed(() => ({
  'compact': props.compact,
  'with-page-info': props.showPageInfo,
  'has-actions': props.pageActions.length > 0
}))

// 可见的面包屑项目
const visibleItems = computed(() => {
  if (props.items.length <= props.maxItems) {
    return props.items
  }
  
  // 保留最后两项和第一项
  const result = []
  result.push(props.items[0]) // 第一项
  
  if (props.items.length > props.maxItems) {
    // 添加最后几项
    const lastItems = props.items.slice(-(props.maxItems - 1))
    result.push(...lastItems)
  }
  
  return result
})

// 隐藏的面包屑项目
const hiddenItems = computed(() => {
  if (props.items.length <= props.maxItems) return []
  
  const hiddenStart = 1
  const hiddenEnd = props.items.length - (props.maxItems - 1)
  return props.items.slice(hiddenStart, hiddenEnd)
})

// 是否有隐藏项目
const hasHiddenItems = computed(() => hiddenItems.value.length > 0)

// 隐藏项目的起始索引
const hiddenStartIndex = computed(() => 1)

// 方法
const navigateHome = () => {
  router.push('/')
  emit('item-click', { title: '首页', path: '/' }, -1)
}

const handleItemClick = (item, index) => {
  emit('item-click', item, index)
}

const handleHiddenItemClick = ({ item, index }) => {
  if (item.path) {
    router.push(item.path)
  }
  emit('item-click', item, index)
}

const handleActionCommand = (command) => {
  emit('action-click', command)
}

const handlePageAction = (action) => {
  emit('page-action', action)
}

const getItemClasses = (item, index) => ({
  'first': index === 0,
  'last': index === visibleItems.value.length - 1,
  'current': index === visibleItems.value.length - 1,
  'has-badge': item.badge,
  'is-new': item.isNew,
  'is-important': item.important
})

const getTagType = (tag) => {
  const tagTypes = {
    '新': 'success',
    '热门': 'danger',
    '推荐': 'warning',
    '测试': 'info'
  }
  return tagTypes[tag] || 'primary'
}

// 动画钩子
const onEnter = (el, done) => {
  el.style.opacity = '0'
  el.style.transform = 'translateX(-20px)'
  
  nextTick(() => {
    el.style.transition = 'all 0.3s ease'
    el.style.opacity = '1'
    el.style.transform = 'translateX(0)'
    
    safeTimer.setTimeout(done, 300)
  })
}

const onLeave = (el, done) => {
  el.style.transition = 'all 0.3s ease'
  el.style.opacity = '0'
  el.style.transform = 'translateX(20px)'
  
  safeTimer.setTimeout(done, 300)
}

// 监听路由变化
watch(() => route.path, () => {
  // 可以在这里自动更新面包屑
}, { immediate: true })
</script>

<style lang="scss" scoped>
.enhanced-breadcrumb {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    box-shadow: var(--shadow-md);
  }
  
  &.compact {
    padding: var(--spacing-sm);
    
    .breadcrumb-container {
      gap: var(--spacing-xs);
    }
    
    .breadcrumb-item {
      .item-content {
        .item-text {
          font-size: var(--text-xs);
        }
      }
    }
  }
  
  &.with-page-info {
    .page-info {
      margin-top: var(--spacing-md);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--border-light);
    }
  }
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.home-button {
  .home-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    color: var(--text-muted);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    
    &:hover {
      background: var(--color-primary);
      border-color: var(--color-primary);
      color: white;
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  position: relative;
  
  &.first {
    .breadcrumb-link-wrapper .breadcrumb-link {
      font-weight: var(--font-semibold);
    }
  }
  
  &.last {
    .breadcrumb-link-wrapper .breadcrumb-link.current {
      background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
      color: var(--color-primary);
      font-weight: var(--font-semibold);
    }
  }
  
  &.has-badge {
    .item-content {
      position: relative;
    }
  }
  
  &.is-new::after {
    content: 'NEW';
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--success-500);
    color: white;
    font-size: 9px;
    padding: 1px 4px;
    border-radius: var(--radius-sm);
    font-weight: bold;
  }
  
  &.is-important {
    .breadcrumb-link-wrapper .breadcrumb-link {
      border-left: 3px solid var(--warning-500);
      padding-left: calc(var(--spacing-sm) - 3px);
    }
  }
}

.breadcrumb-separator {
  display: flex;
  align-items: center;
  margin: 0 var(--spacing-xs);
  color: var(--text-light);
  font-size: 12px;
  transition: color var(--duration-normal) var(--ease-out);
}

.breadcrumb-link-wrapper {
  .breadcrumb-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
      transition: left var(--duration-slow) var(--ease-out);
    }
    
    &.clickable {
      color: var(--text-secondary);
      
      &:hover {
        background: var(--bg-secondary);
        color: var(--color-primary);
        transform: translateX(2px);
        
        &::before {
          left: 100%;
        }
        
        .item-icon {
          transform: scale(1.1);
          color: var(--color-primary);
        }
      }
    }
    
    &.current {
      background: var(--bg-secondary);
      color: var(--text-primary);
      font-weight: var(--font-medium);
      border: 1px solid var(--border-light);
      
      &.has-menu {
        padding-right: var(--spacing-xs);
      }
    }
    
    &.ellipsis {
      background: var(--bg-muted);
      color: var(--text-muted);
      cursor: pointer;
      
      &:hover {
        background: var(--bg-secondary);
        color: var(--color-primary);
      }
    }
    
    .item-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      min-width: 0;
      
      .item-icon {
        font-size: 14px;
        color: var(--text-muted);
        transition: all var(--duration-normal) var(--ease-out);
      }
      
      .item-text {
        font-size: var(--text-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
      }
      
      .item-badge {
        :deep(.el-badge__content) {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
    
    .page-actions {
      margin-left: var(--spacing-xs);
      
      .action-trigger {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        padding: 0;
        
        .el-icon {
          font-size: 12px;
        }
        
        &:hover {
          background: var(--bg-muted);
        }
      }
    }
  }
}

.collapsed-indicator {
  .breadcrumb-link.ellipsis {
    width: 32px;
    height: 32px;
    justify-content: center;
    padding: 0;
    
    .el-icon {
      font-size: 16px;
    }
  }
}

.hidden-items-dropdown {
  .hidden-items-menu {
    max-height: 200px;
    overflow-y: auto;
    
    .hidden-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      
      .el-icon {
        font-size: 14px;
        color: var(--text-muted);
      }
    }
  }
}

// 页面信息区域
.page-info {
  .page-meta {
    .page-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-xs);
      background: var(--gradient-primary);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .page-description {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      line-height: 1.5;
      margin-bottom: var(--spacing-sm);
    }
    
    .page-tags {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-md);
    }
  }
  
  .page-actions-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }
}

// 动画
.breadcrumb-item-enter-active,
.breadcrumb-item-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.breadcrumb-item-enter-from,
.breadcrumb-item-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.breadcrumb-item-move {
  transition: transform var(--duration-normal) var(--ease-out);
}

// 响应式
@media (max-width: 768px) {
  .enhanced-breadcrumb {
    padding: var(--spacing-sm);
    
    .breadcrumb-container {
      flex-wrap: wrap;
    }
    
    .breadcrumb-item {
      .item-content {
        .item-text {
          max-width: 100px;
        }
      }
    }
    
    .page-info {
      .page-actions-bar {
        justify-content: flex-start;
        
        .el-button {
          font-size: var(--text-xs);
          padding: var(--spacing-xs);
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .enhanced-breadcrumb {
    margin-bottom: var(--spacing-sm);
    
    .breadcrumb-item {
      .item-content {
        .item-text {
          max-width: 80px;
        }
      }
    }
    
    .page-info {
      .page-meta {
        .page-title {
          font-size: var(--text-base);
        }
        
        .page-description {
          font-size: var(--text-xs);
        }
      }
    }
  }
}
</style>