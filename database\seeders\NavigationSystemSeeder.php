<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NavigationMenu;
use App\Models\RoleNavigationPermission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

/**
 * 导航系统种子数据
 * 
 * 初始化四域导航架构和权限配置
 */
class NavigationSystemSeeder extends Seeder
{
    public function run(): void
    {
        // 创建导航权限
        $this->createNavigationPermissions();
        
        // 创建四域导航菜单
        $this->createBusinessDomainMenus();
        $this->createOperationDomainMenus(); 
        $this->createAnalyticsDomainMenus();
        $this->createSystemDomainMenus();
        
        // 创建角色导航权限配置
        $this->createRoleNavigationPermissions();
    }

    /**
     * 创建导航相关权限
     */
    private function createNavigationPermissions(): void
    {
        $permissions = [
            // 导航配置管理权限
            'view-navigation-config' => '查看导航配置',
            'create-navigation-menu' => '创建导航菜单',
            'update-navigation-menu' => '更新导航菜单',
            'delete-navigation-menu' => '删除导航菜单',
            
            // 分析统计权限
            'view-navigation-analytics' => '查看导航统计',
            'generate-navigation-reports' => '生成导航报告',
            'export-navigation-data' => '导出导航数据',
            
            // 业务核心域权限
            'access-business-domain' => '访问业务核心域',
            'manage-wechat-groups' => '管理微信群组',
            'manage-users' => '管理用户',
            'manage-orders' => '管理订单',
            'manage-payments' => '管理支付',
            
            // 运营管理域权限
            'access-operation-domain' => '访问运营管理域',
            'manage-distributors' => '管理分销商',
            'manage-promotions' => '管理推广',
            'manage-content' => '管理内容',
            'manage-templates' => '管理模板',
            
            // 数据分析域权限
            'access-analytics-domain' => '访问数据分析域',
            'view-dashboards' => '查看数据看板',
            'view-reports' => '查看报告',
            'manage-data-exports' => '管理数据导出',
            
            // 系统配置域权限
            'access-system-domain' => '访问系统配置域',
            'manage-system-settings' => '管理系统设置',
            'manage-permissions' => '管理权限',
            'manage-logs' => '管理日志',
            'system-monitor' => '系统监控',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(
                ['name' => $name, 'guard_name' => 'web'],
                ['description' => $description]
            );
        }
    }

    /**
     * 创建业务核心域菜单
     */
    private function createBusinessDomainMenus(): void
    {
        $businessMenus = [
            [
                'code' => 'business_dashboard',
                'name' => '业务概览',
                'domain' => 'business',
                'icon' => 'dashboard',
                'route' => '/business/dashboard',
                'component' => 'business/Dashboard',
                'sort_order' => 1,
                'permission' => 'access-business-domain',
                'meta' => [
                    'title' => '业务概览',
                    'keywords' => ['概览', '仪表板', '业务'],
                    'description' => '业务数据概览和核心指标'
                ]
            ],
            [
                'code' => 'wechat_groups',
                'name' => '群组管理',
                'domain' => 'business',
                'icon' => 'groups',
                'route' => '/business/groups',
                'component' => 'business/GroupList',
                'sort_order' => 2,
                'permission' => 'manage-wechat-groups',
                'meta' => [
                    'title' => '群组管理',
                    'keywords' => ['群组', '微信', '管理'],
                    'description' => '微信群组创建和管理'
                ]
            ],
            [
                'code' => 'user_management',
                'name' => '用户管理',
                'domain' => 'business',
                'icon' => 'users',
                'route' => '/business/users',
                'component' => 'business/UserList',
                'sort_order' => 3,
                'permission' => 'manage-users',
                'meta' => [
                    'title' => '用户管理',
                    'keywords' => ['用户', '会员', '管理'],
                    'description' => '用户信息管理和权限设置'
                ]
            ],
            [
                'code' => 'order_management',
                'name' => '订单管理',
                'domain' => 'business',
                'icon' => 'shopping',
                'route' => '/business/orders',
                'component' => 'business/OrderList',
                'sort_order' => 4,
                'permission' => 'manage-orders',
                'meta' => [
                    'title' => '订单管理',
                    'keywords' => ['订单', '交易', '支付'],
                    'description' => '订单查看和处理'
                ]
            ],
            [
                'code' => 'payment_management',
                'name' => '支付管理',
                'domain' => 'business',
                'icon' => 'payment',
                'route' => '/business/payments',
                'component' => 'business/PaymentList',
                'sort_order' => 5,
                'permission' => 'manage-payments',
                'meta' => [
                    'title' => '支付管理',
                    'keywords' => ['支付', '收款', '财务'],
                    'description' => '支付渠道和交易管理'
                ]
            ]
        ];

        foreach ($businessMenus as $menu) {
            NavigationMenu::create($menu);
        }
    }

    /**
     * 创建运营管理域菜单
     */
    private function createOperationDomainMenus(): void
    {
        $operationMenus = [
            [
                'code' => 'operation_dashboard',
                'name' => '运营概览',
                'domain' => 'operation',
                'icon' => 'operation',
                'route' => '/operation/dashboard',
                'component' => 'operation/Dashboard',
                'sort_order' => 1,
                'permission' => 'access-operation-domain',
                'meta' => [
                    'title' => '运营概览',
                    'keywords' => ['运营', '概览', '管理'],
                    'description' => '运营数据和指标概览'
                ]
            ],
            [
                'code' => 'distributor_management',
                'name' => '分销商管理',
                'domain' => 'operation',
                'icon' => 'distributor',
                'route' => '/operation/distributors',
                'component' => 'operation/DistributorList',
                'sort_order' => 2,
                'permission' => 'manage-distributors',
                'meta' => [
                    'title' => '分销商管理',
                    'keywords' => ['分销商', '代理', '渠道'],
                    'description' => '分销商和代理管理'
                ]
            ],
            [
                'code' => 'promotion_management',
                'name' => '推广管理',
                'domain' => 'operation',
                'icon' => 'promotion',
                'route' => '/operation/promotions',
                'component' => 'operation/PromotionList',
                'sort_order' => 3,
                'permission' => 'manage-promotions',
                'meta' => [
                    'title' => '推广管理',
                    'keywords' => ['推广', '营销', '活动'],
                    'description' => '推广活动和营销管理'
                ]
            ],
            [
                'code' => 'content_management',
                'name' => '内容管理',
                'domain' => 'operation',
                'icon' => 'content',
                'route' => '/operation/content',
                'component' => 'operation/ContentList',
                'sort_order' => 4,
                'permission' => 'manage-content',
                'meta' => [
                    'title' => '内容管理',
                    'keywords' => ['内容', '文章', '素材'],
                    'description' => '内容创建和发布管理'
                ]
            ],
            [
                'code' => 'template_management',
                'name' => '模板管理',
                'domain' => 'operation',
                'icon' => 'template',
                'route' => '/operation/templates',
                'component' => 'operation/TemplateList',
                'sort_order' => 5,
                'permission' => 'manage-templates',
                'meta' => [
                    'title' => '模板管理',
                    'keywords' => ['模板', '样式', '设计'],
                    'description' => '内容模板和样式管理'
                ]
            ]
        ];

        foreach ($operationMenus as $menu) {
            NavigationMenu::create($menu);
        }
    }

    /**
     * 创建数据分析域菜单
     */
    private function createAnalyticsDomainMenus(): void
    {
        $analyticsMenus = [
            [
                'code' => 'analytics_dashboard',
                'name' => '数据概览',
                'domain' => 'analytics',
                'icon' => 'analytics',
                'route' => '/analytics/dashboard',
                'component' => 'analytics/Dashboard',
                'sort_order' => 1,
                'permission' => 'access-analytics-domain',
                'meta' => [
                    'title' => '数据概览',
                    'keywords' => ['数据', '分析', '统计'],
                    'description' => '数据分析和统计概览'
                ]
            ],
            [
                'code' => 'business_analytics',
                'name' => '业务分析',
                'domain' => 'analytics',
                'icon' => 'chart-bar',
                'route' => '/analytics/business',
                'component' => 'analytics/BusinessAnalytics',
                'sort_order' => 2,
                'permission' => 'view-dashboards',
                'meta' => [
                    'title' => '业务分析',
                    'keywords' => ['业务', '分析', '图表'],
                    'description' => '业务数据深度分析'
                ]
            ],
            [
                'code' => 'user_analytics',
                'name' => '用户分析',
                'domain' => 'analytics',
                'icon' => 'chart-user',
                'route' => '/analytics/users',
                'component' => 'analytics/UserAnalytics',
                'sort_order' => 3,
                'permission' => 'view-dashboards',
                'meta' => [
                    'title' => '用户分析',
                    'keywords' => ['用户', '行为', '分析'],
                    'description' => '用户行为和偏好分析'
                ]
            ],
            [
                'code' => 'financial_reports',
                'name' => '财务报表',
                'domain' => 'analytics',
                'icon' => 'report',
                'route' => '/analytics/finance',
                'component' => 'analytics/FinanceReports',
                'sort_order' => 4,
                'permission' => 'view-reports',
                'meta' => [
                    'title' => '财务报表',
                    'keywords' => ['财务', '报表', '收入'],
                    'description' => '财务数据和收入分析'
                ]
            ],
            [
                'code' => 'data_export',
                'name' => '数据导出',
                'domain' => 'analytics',
                'icon' => 'export',
                'route' => '/analytics/export',
                'component' => 'analytics/DataExport',
                'sort_order' => 5,
                'permission' => 'manage-data-exports',
                'meta' => [
                    'title' => '数据导出',
                    'keywords' => ['导出', '备份', '数据'],
                    'description' => '数据导出和备份管理'
                ]
            ]
        ];

        foreach ($analyticsMenus as $menu) {
            NavigationMenu::create($menu);
        }
    }

    /**
     * 创建系统配置域菜单
     */
    private function createSystemDomainMenus(): void
    {
        $systemMenus = [
            [
                'code' => 'system_dashboard',
                'name' => '系统概览',
                'domain' => 'system',
                'icon' => 'system',
                'route' => '/system/dashboard',
                'component' => 'system/Dashboard',
                'sort_order' => 1,
                'permission' => 'access-system-domain',
                'meta' => [
                    'title' => '系统概览',
                    'keywords' => ['系统', '监控', '状态'],
                    'description' => '系统状态和性能监控'
                ]
            ],
            [
                'code' => 'system_settings',
                'name' => '系统设置',
                'domain' => 'system',
                'icon' => 'settings',
                'route' => '/system/settings',
                'component' => 'system/Settings',
                'sort_order' => 2,
                'permission' => 'manage-system-settings',
                'meta' => [
                    'title' => '系统设置',
                    'keywords' => ['设置', '配置', '参数'],
                    'description' => '系统参数和配置管理'
                ]
            ],
            [
                'code' => 'permission_management',
                'name' => '权限管理',
                'domain' => 'system',
                'icon' => 'permission',
                'route' => '/system/permissions',
                'component' => 'system/PermissionList',
                'sort_order' => 3,
                'permission' => 'manage-permissions',
                'meta' => [
                    'title' => '权限管理',
                    'keywords' => ['权限', '角色', '访问'],
                    'description' => '用户角色和权限管理'
                ]
            ],
            [
                'code' => 'operation_logs',
                'name' => '操作日志',
                'domain' => 'system',
                'icon' => 'logs',
                'route' => '/system/logs',
                'component' => 'system/LogList',
                'sort_order' => 4,
                'permission' => 'manage-logs',
                'meta' => [
                    'title' => '操作日志',
                    'keywords' => ['日志', '记录', '审计'],
                    'description' => '系统操作日志和审计'
                ]
            ],
            [
                'code' => 'system_monitor',
                'name' => '系统监控',
                'domain' => 'system',
                'icon' => 'monitor',
                'route' => '/system/monitor',
                'component' => 'system/Monitor',
                'sort_order' => 5,
                'permission' => 'system-monitor',
                'meta' => [
                    'title' => '系统监控',
                    'keywords' => ['监控', '性能', '健康'],
                    'description' => '系统性能和健康监控'
                ]
            ]
        ];

        foreach ($systemMenus as $menu) {
            NavigationMenu::create($menu);
        }
    }

    /**
     * 创建角色导航权限配置
     */
    private function createRoleNavigationPermissions(): void
    {
        $roleConfigs = [
            'super_admin' => [
                'allowed_menus' => ['*'], // 所有菜单
                'denied_menus' => [],
                'domain_access' => [
                    'business' => ['level' => 'full'],
                    'operation' => ['level' => 'full'], 
                    'analytics' => ['level' => 'full'],
                    'system' => ['level' => 'full']
                ],
                'feature_permissions' => [
                    'can_create_menus' => true,
                    'can_manage_permissions' => true,
                    'can_export_data' => true
                ]
            ],
            'admin' => [
                'allowed_menus' => [
                    'business_dashboard', 'wechat_groups', 'user_management', 'order_management',
                    'operation_dashboard', 'distributor_management', 'promotion_management',
                    'analytics_dashboard', 'business_analytics', 'user_analytics',
                    'system_settings', 'operation_logs'
                ],
                'denied_menus' => ['permission_management', 'system_monitor'],
                'domain_access' => [
                    'business' => ['level' => 'full'],
                    'operation' => ['level' => 'full'],
                    'analytics' => ['level' => 'read'],
                    'system' => ['level' => 'limited']
                ],
                'feature_permissions' => [
                    'can_create_menus' => false,
                    'can_manage_permissions' => false,
                    'can_export_data' => true
                ]
            ],
            'manager' => [
                'allowed_menus' => [
                    'business_dashboard', 'wechat_groups', 'user_management',
                    'operation_dashboard', 'distributor_management',
                    'analytics_dashboard', 'business_analytics'
                ],
                'denied_menus' => [],
                'domain_access' => [
                    'business' => ['level' => 'full'],
                    'operation' => ['level' => 'read'],
                    'analytics' => ['level' => 'read'],
                    'system' => ['level' => 'none']
                ],
                'feature_permissions' => [
                    'can_create_menus' => false,
                    'can_manage_permissions' => false,
                    'can_export_data' => false
                ]
            ],
            'operator' => [
                'allowed_menus' => [
                    'business_dashboard', 'wechat_groups',
                    'operation_dashboard', 'content_management', 'template_management'
                ],
                'denied_menus' => [],
                'domain_access' => [
                    'business' => ['level' => 'limited'],
                    'operation' => ['level' => 'full'],
                    'analytics' => ['level' => 'none'],
                    'system' => ['level' => 'none']
                ],
                'feature_permissions' => [
                    'can_create_menus' => false,
                    'can_manage_permissions' => false,
                    'can_export_data' => false
                ]
            ],
            'user' => [
                'allowed_menus' => ['business_dashboard'],
                'denied_menus' => [],
                'domain_access' => [
                    'business' => ['level' => 'read'],
                    'operation' => ['level' => 'none'],
                    'analytics' => ['level' => 'none'],
                    'system' => ['level' => 'none']
                ],
                'feature_permissions' => [
                    'can_create_menus' => false,
                    'can_manage_permissions' => false,
                    'can_export_data' => false
                ]
            ]
        ];

        foreach ($roleConfigs as $roleName => $config) {
            RoleNavigationPermission::create(array_merge($config, [
                'role' => $roleName,
                'is_active' => true
            ]));
        }
    }
}