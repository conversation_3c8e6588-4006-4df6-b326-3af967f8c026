<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索订单号、用户名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
        clearable
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="全部" value="" />
        <el-option label="待支付" value="pending" />
        <el-option label="已支付" value="paid" />
        <el-option label="已取消" value="cancelled" />
        <el-option label="已退款" value="refunded" />
      </el-select>
      <el-select
        v-model="listQuery.payment_method"
        placeholder="支付方式"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="全部" value="" />
        <el-option label="微信支付" value="wechat" />
        <el-option label="支付宝" value="alipay" />
        <el-option label="QQ钱包" value="qqpay" />
        <el-option label="银行卡" value="bank" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        @change="handleDateChange"
      />
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="warning" icon="Download" @click="handleExport">
        导出数据
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <stat-card
          type="primary"
          :icon="Tickets"
          :value="stats.total_orders"
          label="总订单数"
          :trend="{ type: 'up', value: '+125', desc: '较上月' }"
          clickable
          @click="navigateToList"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="success"
          :icon="Money"
          :value="stats.total_amount"
          label="总交易额"
          prefix="¥"
          :trend="{ type: 'up', value: '+28.5%', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="warning"
          :icon="Check"
          :value="stats.paid_orders"
          label="已支付订单"
          :trend="{ type: 'up', value: '+18.2%', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="danger"
          :icon="TrendCharts"
          :value="stats.success_rate"
          label="支付成功率"
          suffix="%"
          :decimals="1"
          :trend="{ type: 'up', value: '+2.1%', desc: '较上月' }"
        />
      </el-col>
    </el-row>

    <!-- 订单列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>订单列表</h3>
          <div>
            <el-button type="primary" size="small" @click="handleBatchOperation">批量操作</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="订单号" width="180">
          <template #default="{ row }">
            <div class="order-no">
              <span>{{ row.order_no }}</span>
              <el-button 
                type="text" 
                size="small" 
                @click="copyToClipboard(row.order_no)"
                class="copy-btn"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :src="row.user?.avatar" size="small">
                {{ row.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.user?.username }}</div>
                <div class="user-id">ID: {{ row.user_id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="群组信息" width="200">
          <template #default="{ row }">
            <div class="group-info" v-if="row.wechat_group">
              <div class="group-name">{{ row.wechat_group.name }}</div>
              <div class="group-price">¥{{ row.wechat_group.price }}</div>
            </div>
            <span v-else class="no-group">-</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" width="100">
          <template #default="{ row }">
            <span class="order-amount">¥{{ row.amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag :type="getPaymentMethodTagType(row.payment_method)" v-if="row.payment_method">
              {{ getPaymentMethodText(row.payment_method) }}
            </el-tag>
            <span v-else class="no-payment">-</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="支付时间" width="160">
          <template #default="{ row }">
            {{ row.paid_at ? formatDate(row.paid_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleRefund(row)"
              v-if="row.status === 'paid'"
            >
              退款
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`detail-${row.id}`">订单详情</el-dropdown-item>
                  <el-dropdown-item :command="`payment-${row.id}`" v-if="row.status === 'pending'">支付信息</el-dropdown-item>
                  <el-dropdown-item :command="`cancel-${row.id}`" v-if="row.status === 'pending'">取消订单</el-dropdown-item>
                  <el-dropdown-item :command="`resend-${row.id}`" v-if="row.status === 'paid'">重发通知</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-data="currentOrder"
    />

    <!-- 退款对话框 -->
    <RefundDialog
      v-model="refundDialogVisible"
      :order-data="currentOrder"
      @success="handleRefundSuccess"
    />

    <!-- 支付信息对话框 -->
    <PaymentInfoDialog
      v-model="paymentDialogVisible"
      :order-data="currentOrder"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Tickets, 
  Money, 
  Check, 
  TrendCharts, 
  Search, 
  Download, 
  ArrowDown,
  DocumentCopy
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import OrderDetailDialog from '@/components/OrderDetailDialog.vue'
import RefundDialog from '@/components/RefundDialog.vue'
import PaymentInfoDialog from '@/components/PaymentInfoDialog.vue'
import { getOrderList, cancelOrder, refundOrder, getOrderStats } from '@/api/order'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const detailDialogVisible = ref(false)
const refundDialogVisible = ref(false)
const paymentDialogVisible = ref(false)
const currentOrder = ref({})
const multipleSelection = ref([])
const dateRange = ref([])

// 统计数据
const stats = ref({
  total_orders: 0,
  total_amount: 0,
  paid_orders: 0,
  success_rate: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  payment_method: '',
  start_date: '',
  end_date: ''
})

// 获取订单列表
const getList = async () => {
  listLoading.value = true
  try {
    const { data } = await getOrderList(listQuery)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await getOrderStats()
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 筛选
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 日期范围变化
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    listQuery.start_date = dates[0]
    listQuery.end_date = dates[1]
  } else {
    listQuery.start_date = ''
    listQuery.end_date = ''
  }
}

// 查看订单
const handleView = (row) => {
  currentOrder.value = { ...row }
  detailDialogVisible.value = true
}

// 退款处理
const handleRefund = (row) => {
  currentOrder.value = { ...row }
  refundDialogVisible.value = true
}

// 查看支付信息
const handlePaymentInfo = (row) => {
  currentOrder.value = { ...row }
  paymentDialogVisible.value = true
}

// 取消订单
const handleCancel = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await cancelOrder(orderId)
    ElMessage.success('订单取消成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败')
    }
  }
}

// 重发通知
const handleResendNotification = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要重新发送通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('通知发送成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('发送通知失败')
    }
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('订单号已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const [action, orderId] = command.split('-')
  const id = parseInt(orderId)
  const order = list.value.find(o => o.id === id)
  
  switch (action) {
    case 'detail':
      handleView(order)
      break
    case 'payment':
      handlePaymentInfo(order)
      break
    case 'cancel':
      handleCancel(id)
      break
    case 'resend':
      handleResendNotification(id)
      break
  }
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要操作的订单')
    return
  }
  
  ElMessageBox.confirm('请选择批量操作类型', '批量操作', {
    distinguishCancelAndClose: true,
    confirmButtonText: '批量取消',
    cancelButtonText: '批量导出'
  }).then(() => {
    batchCancelOrders()
  }).catch((action) => {
    if (action === 'cancel') {
      batchExportOrders()
    }
  })
}

// 批量取消订单
const batchCancelOrders = async () => {
  try {
    const orderIds = multipleSelection.value
      .filter(order => order.status === 'pending')
      .map(order => order.id)
    
    if (orderIds.length === 0) {
      ElMessage.warning('没有可取消的订单')
      return
    }
    
    ElMessage.success('批量取消成功')
    getList()
  } catch (error) {
    ElMessage.error('批量取消失败')
  }
}

// 批量导出订单
const batchExportOrders = async () => {
  try {
    const orderIds = multipleSelection.value.map(order => order.id)
    ElMessage.success('批量导出成功')
  } catch (error) {
    ElMessage.error('批量导出失败')
  }
}

// 选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 分页
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 退款成功回调
const handleRefundSuccess = () => {
  getList()
  getStats()
}

// 导航到列表
const navigateToList = () => {
  getList()
}

// 工具函数
const getPaymentMethodTagType = (method) => {
  const types = {
    wechat: 'success',
    alipay: 'primary',
    qqpay: 'warning',
    bank: 'info'
  }
  return types[method] || 'info'
}

const getPaymentMethodText = (method) => {
  const texts = {
    wechat: '微信支付',
    alipay: '支付宝',
    qqpay: 'QQ钱包',
    bank: '银行卡'
  }
  return texts[method] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'info',
    refunded: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return texts[status] || '未知'
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 24px;
}

.order-no {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .copy-btn {
    padding: 0;
    min-height: auto;
    
    &:hover {
      color: #409eff;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .user-details {
    .username {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 2px;
    }
    
    .user-id {
      font-size: 12px;
      color: #666;
    }
  }
}

.group-info {
  .group-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
  }
  
  .group-price {
    font-size: 12px;
    color: #f56c6c;
    font-weight: 600;
  }
}

.no-group,
.no-payment {
  color: #999;
  font-style: italic;
}

.order-amount {
  color: #f56c6c;
  font-weight: 600;
  font-size: 16px;
}

.pagination-container {
  padding: 32px 16px;
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}
</style>