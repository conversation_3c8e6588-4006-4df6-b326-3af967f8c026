<template>
  <div class="customer-dialog">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入客户姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="微信号" prop="wechat">
            <el-input v-model="form.wechat" placeholder="请输入微信号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别">
              <el-option label="男" value="male" />
              <el-option label="女" value="female" />
              <el-option label="未知" value="unknown" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户等级" prop="level">
            <el-select v-model="form.level" placeholder="请选择客户等级">
              <el-option label="A级客户" value="A" />
              <el-option label="B级客户" value="B" />
              <el-option label="C级客户" value="C" />
              <el-option label="D级客户" value="D" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户来源" prop="source">
            <el-select v-model="form.source" placeholder="请选择客户来源">
              <el-option label="推荐" value="referral" />
              <el-option label="广告" value="advertisement" />
              <el-option label="社交媒体" value="social_media" />
              <el-option label="直接访问" value="direct" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生日" prop="birthday">
            <el-date-picker
              v-model="form.birthday"
              type="date"
              placeholder="请选择生日"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职业" prop="occupation">
            <el-input v-model="form.occupation" placeholder="请输入职业" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="公司" prop="company">
            <el-input v-model="form.company" placeholder="请输入公司名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户状态" prop="status" v-if="customer">
            <el-select v-model="form.status" placeholder="请选择客户状态">
              <el-option label="活跃" value="active" />
              <el-option label="不活跃" value="inactive" />
              <el-option label="潜在" value="potential" />
              <el-option label="流失" value="lost" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="地址" prop="address">
        <el-input
          v-model="form.address"
          type="textarea"
          :rows="2"
          placeholder="请输入详细地址"
        />
      </el-form-item>

      <el-form-item label="客户标签" prop="tags">
        <div class="tags-input">
          <el-tag
            v-for="tag in form.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
            class="tag-input"
          />
          <el-button
            v-else
            size="small"
            @click="showInput"
            class="add-tag-btn"
          >
            + 添加标签
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <el-form-item label="偏好设置" prop="preferences">
        <div class="preferences-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系方式偏好">
                <el-select v-model="form.preferences.contact_method" placeholder="选择偏好">
                  <el-option label="电话" value="phone" />
                  <el-option label="微信" value="wechat" />
                  <el-option label="邮件" value="email" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系时间偏好">
                <el-select v-model="form.preferences.contact_time" placeholder="选择时间">
                  <el-option label="上午" value="morning" />
                  <el-option label="下午" value="afternoon" />
                  <el-option label="晚上" value="evening" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ customer ? '更新' : '创建' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { customerApi } from '@/api/customer'

const props = defineProps({
  customer: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const loading = ref(false)
const formRef = ref()
const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

const form = reactive({
  name: '',
  phone: '',
  wechat: '',
  email: '',
  gender: 'unknown',
  birthday: null,
  occupation: '',
  company: '',
  address: '',
  tags: [],
  level: 'C',
  status: 'potential',
  source: 'other',
  notes: '',
  preferences: {
    contact_method: '',
    contact_time: ''
  }
})

const rules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ],
  source: [
    { required: true, message: '请选择客户来源', trigger: 'change' }
  ]
}

// 监听客户数据变化
watch(() => props.customer, (newCustomer) => {
  if (newCustomer) {
    Object.assign(form, {
      name: newCustomer.name || '',
      phone: newCustomer.phone || '',
      wechat: newCustomer.wechat || '',
      email: newCustomer.email || '',
      gender: newCustomer.gender || 'unknown',
      birthday: newCustomer.birthday || null,
      occupation: newCustomer.occupation || '',
      company: newCustomer.company || '',
      address: newCustomer.address || '',
      tags: newCustomer.tags || [],
      level: newCustomer.level || 'C',
      status: newCustomer.status || 'potential',
      source: newCustomer.source || 'other',
      notes: newCustomer.notes || '',
      preferences: newCustomer.preferences || {
        contact_method: '',
        contact_time: ''
      }
    })
  } else {
    // 重置表单
    Object.assign(form, {
      name: '',
      phone: '',
      wechat: '',
      email: '',
      gender: 'unknown',
      birthday: null,
      occupation: '',
      company: '',
      address: '',
      tags: [],
      level: 'C',
      status: 'potential',
      source: 'other',
      notes: '',
      preferences: {
        contact_method: '',
        contact_time: ''
      }
    })
  }
}, { immediate: true })

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    const formData = { ...form }
    
    if (props.customer) {
      await customerApi.update(props.customer.id, formData)
      ElMessage.success('客户信息更新成功')
    } else {
      await customerApi.create(formData)
      ElMessage.success('客户创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error !== false) {
      ElMessage.error(props.customer ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

const removeTag = (tag) => {
  const index = form.tags.indexOf(tag)
  if (index > -1) {
    form.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !form.tags.includes(inputValue.value)) {
    form.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style scoped>
.customer-dialog {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  margin: 0;
}

.tag-input {
  width: 100px;
}

.add-tag-btn {
  border-style: dashed;
}

.preferences-section {
  width: 100%;
}

.preferences-section .el-form-item {
  margin-bottom: 10px;
}

.preferences-section .el-form-item__label {
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-dialog .el-row {
    flex-direction: column;
  }
  
  .customer-dialog .el-col {
    margin-bottom: 10px;
  }
  
  .tags-input {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>