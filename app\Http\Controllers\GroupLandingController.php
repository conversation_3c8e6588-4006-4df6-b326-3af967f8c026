<?php

namespace App\Http\Controllers;

use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\GroupMember;
use App\Models\DomainPool;
use App\Services\PaymentService;
use App\Services\IPLocationService;
use App\Services\BrowserDetectionService;
use App\Services\GroupAnalyticsService;
use App\Services\AntiBlockService;
use App\Services\AntiBlockLinkService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 群组落地页控制器
 * 处理群组的完整访问流程：落地页 -> 支付 -> 成功页面
 */
class GroupLandingController extends Controller
{
    private PaymentService $paymentService;
    private IPLocationService $ipLocationService;
    private BrowserDetectionService $browserService;
    private GroupAnalyticsService $analyticsService;
    private AntiBlockService $antiBlockService;
    private AntiBlockLinkService $antiBlockLinkService;

    public function __construct(
        PaymentService $paymentService,
        IPLocationService $ipLocationService,
        BrowserDetectionService $browserService,
        GroupAnalyticsService $analyticsService,
        AntiBlockService $antiBlockService,
        AntiBlockLinkService $antiBlockLinkService
    ) {
        $this->paymentService = $paymentService;
        $this->ipLocationService = $ipLocationService;
        $this->browserService = $browserService;
        $this->analyticsService = $analyticsService;
        $this->antiBlockService = $antiBlockService;
        $this->antiBlockLinkService = $antiBlockLinkService;
    }

    /**
     * 群组落地页（推广链接访问入口）
     */
    public function landing(Request $request, string $slug)
    {
        try {
            // 通过slug查找群组
            $group = WechatGroup::where('slug', $slug)
                ->where('status', 'active')
                ->first();

            if (!$group) {
                return $this->showErrorPage('群组不存在或已下架');
            }

            // 记录访问统计
            $this->analyticsService->recordPageView($group->id, [
                'source' => $request->get('source', 'direct'),
                'referrer' => $request->header('referer'),
                'campaign' => $request->get('utm_campaign'),
                'medium' => $request->get('utm_medium')
            ]);

            // 获取用户地理位置
            $userIP = $this->ipLocationService->getClientIP();
            $userCity = $this->ipLocationService->getCity($userIP);
            
            // 检查浏览器限制
            if ($this->shouldRedirectToBrowser($group)) {
                return $this->showBrowserGuidePage($group, $slug);
            }

            // 生成城市替换后的标题
            $displayTitle = $group->getCityReplacedTitle($userCity);
            
            // 准备页面数据
            $pageData = $this->prepareLandingPageData($group, $userCity, $displayTitle, $request);

            // 记录转化事件 - 查看详情
            $this->analyticsService->recordConversion($group->id, 'view');

            return view('landing.group', $pageData);

        } catch (\Exception $e) {
            Log::error('群组落地页访问异常', [
                'slug' => $slug,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return $this->showErrorPage('页面加载失败，请稍后重试');
        }
    }

    /**
     * 创建支付订单
     */
    public function createOrder(Request $request)
    {
        $request->validate([
            'group_id' => 'required|integer|exists:wechat_groups,id',
            'customer_name' => 'nullable|string|max:50',
            'customer_phone' => 'nullable|string|max:20',
            'customer_wechat' => 'nullable|string|max:50',
            'payment_method' => 'required|string|in:wechat,alipay,qq,bank'
        ]);

        DB::beginTransaction();
        
        try {
            $group = WechatGroup::find($request->group_id);
            
            if (!$group || $group->status !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => '群组不可用'
                ]);
            }

            // 检查群组是否已满
            if (!$group->canJoin()) {
                return response()->json([
                    'success' => false,
                    'message' => '群组已满，无法加入'
                ]);
            }

            // 获取用户信息
            $userIP = $this->ipLocationService->getClientIP();
            $userCity = $this->ipLocationService->getCity($userIP);
            
            // 创建订单
            $orderData = [
                'group_id' => $group->id,
                'user_name' => $request->customer_name ?: '匿名用户',
                'user_phone' => $request->customer_phone,
                'user_wechat' => $request->customer_wechat,
                'amount' => $group->price,
                'payment_method' => $request->payment_method,
                'user_ip' => $userIP,
                'user_city' => $userCity,
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'source' => $request->get('source', 'direct')
            ];

            $order = $this->paymentService->createGroupOrder($orderData);

            // 记录转化事件 - 点击支付
            $this->analyticsService->recordConversion($group->id, 'click');

            // 如果是免费群组，直接加入
            if ($group->price == 0) {
                $this->processSuccessfulPayment($order);
                
                DB::commit();
                
                return response()->json([
                    'success' => true,
                    'message' => '加入成功',
                    'redirect_url' => route('group.success', ['order' => $order->order_no])
                ]);
            }

            // 创建支付
            $paymentResult = $this->paymentService->createPayment($order, $request->payment_method);
            
            if (!$paymentResult['success']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['message']
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '订单创建成功',
                'order_no' => $order->order_no,
                'payment_url' => $paymentResult['payment_url'],
                'qr_code' => $paymentResult['qr_code'] ?? null,
                'amount' => $order->amount,
                'expire_time' => $order->expire_at->timestamp
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('创建支付订单失败', [
                'group_id' => $request->group_id,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '订单创建失败，请重试'
            ]);
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrderStatus(Request $request)
    {
        $request->validate([
            'order_no' => 'required|string'
        ]);

        try {
            $order = Order::where('order_no', $request->order_no)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => '订单不存在'
                ]);
            }

            // 检查订单是否过期
            if ($order->isExpired()) {
                $order->update(['status' => 'expired']);
                
                return response()->json([
                    'success' => false,
                    'status' => 'expired',
                    'message' => '订单已过期'
                ]);
            }

            // 如果订单已支付，处理成功逻辑
            if ($order->isPaid()) {
                $this->processSuccessfulPayment($order);
                
                return response()->json([
                    'success' => true,
                    'status' => 'paid',
                    'message' => '支付成功',
                    'redirect_url' => route('group.success', ['order' => $order->order_no])
                ]);
            }

            return response()->json([
                'success' => true,
                'status' => $order->status,
                'message' => '订单状态：' . $order->status_name,
                'remaining_time' => $order->expire_at->timestamp - time()
            ]);

        } catch (\Exception $e) {
            Log::error('查询订单状态失败', [
                'order_no' => $request->order_no,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '查询失败，请重试'
            ]);
        }
    }

    /**
     * 支付成功页面
     */
    public function success(Request $request, string $orderNo)
    {
        try {
            $order = Order::where('order_no', $orderNo)
                ->with(['group', 'user'])
                ->first();

            if (!$order) {
                return $this->showErrorPage('订单不存在');
            }

            if (!$order->isPaid()) {
                return redirect()->route('group.landing', ['slug' => $order->group->slug])
                    ->with('message', '订单尚未支付');
            }

            $group = $order->group;
            
            // 准备成功页面数据
            $pageData = [
                'order' => $order,
                'group' => $group,
                'qr_code_url' => $group->qr_code_url,
                'customer_service' => [
                    'show' => $group->show_customer_service,
                    'avatar' => $group->customer_service_avatar,
                    'title' => $group->customer_service_title ?: 'VIP专属客服',
                    'desc' => $group->customer_service_desc ?: '如有问题，请联系客服',
                    'qr_code' => $group->customer_service_qr
                ],
                'success_content' => $this->getSuccessPageContent($group),
                'next_steps' => $this->getNextSteps($group)
            ];

            return view('landing.success', $pageData);

        } catch (\Exception $e) {
            Log::error('支付成功页面异常', [
                'order_no' => $orderNo,
                'error' => $e->getMessage()
            ]);

            return $this->showErrorPage('页面加载失败');
        }
    }

    /**
     * 生成推广链接
     */
    public function generatePromotionLink(Request $request, int $groupId)
    {
        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return response()->json([
                    'success' => false,
                    'message' => '群组不存在'
                ]);
            }

            $params = $request->only(['source', 'utm_campaign', 'utm_medium', 'utm_source']);
            
            // 使用防红链接服务生成推广链接
            $result = $this->antiBlockLinkService->generatePromotionLink($group, $params);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('生成推广链接失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '生成推广链接失败'
            ]);
        }
    }

    // 私有方法

    /**
     * 检查是否需要跳转到浏览器
     */
    private function shouldRedirectToBrowser(WechatGroup $group): bool
    {
        // 如果设置了微信浏览器限制且当前是微信浏览器
        return $group->wx_accessible == 2 && $this->browserService->isWechatBrowser();
    }

    /**
     * 显示浏览器引导页面
     */
    private function showBrowserGuidePage(WechatGroup $group, string $slug)
    {
        $targetUrl = route('group.landing', ['slug' => $slug]);
        $guideHtml = $this->browserService->generateBrowserGuidePage($targetUrl, $group->title);
        
        return response($guideHtml)->header('Content-Type', 'text/html');
    }

    /**
     * 准备落地页数据
     */
    private function prepareLandingPageData(WechatGroup $group, string $userCity, string $displayTitle, Request $request): array
    {
        return [
            'group' => $group,
            'display_title' => $displayTitle,
            'original_title' => $group->title,
            'user_city' => $userCity,
            'has_city_placeholder' => strpos($group->title, 'xxx') !== false,
            
            // 虚拟数据
            'virtual_members' => $group->generateVirtualMembers(),
            'virtual_reviews' => $group->generateVirtualReviews(),
            'virtual_notifications' => $this->generateVirtualNotifications($group),
            
            // 内容数据
            'rich_content' => $group->rich_content,
            'formatted_faq' => $group->formatted_faq_content,
            'formatted_reviews' => $group->formatted_member_reviews,
            
            // 营销数据
            'marketing_config' => $group->marketing_config,
            'stats' => [
                'view_count' => $group->view_count,
                'like_count' => $group->like_count,
                'want_see_count' => $group->want_see_count,
                'read_count_display' => $group->read_count_display ?: '10万+',
                'virtual_stats' => $group->virtual_stats,
            ],
            
            // 支付相关
            'payment_methods' => $group->getDetailedPaymentMethods(),
            'is_free' => $group->price == 0,
            'formatted_price' => $group->formatted_price,
            
            // 客服信息
            'customer_service' => [
                'show' => $group->show_customer_service,
                'avatar' => $group->customer_service_avatar,
                'title' => $group->customer_service_title ?: 'VIP专属客服',
                'desc' => $group->customer_service_desc ?: '如有问题，请联系客服',
                'qr_code' => $group->customer_service_qr
            ],
            
            // 页面配置
            'page_config' => [
                'show_countdown' => $group->marketing_config['urgency']['show_countdown'] ?? false,
                'remaining_slots' => $group->marketing_config['urgency']['remaining_slots'] ?? 0,
                'limited_offer' => $group->marketing_config['limited_offer'] ?? null,
                'button_title' => $group->button_title ?: '立即加入'
            ],
            
            // 追踪参数
            'tracking' => [
                'source' => $request->get('source', 'direct'),
                'utm_campaign' => $request->get('utm_campaign'),
                'utm_medium' => $request->get('utm_medium'),
                'utm_source' => $request->get('utm_source')
            ]
        ];
    }

    /**
     * 处理支付成功后的逻辑
     */
    private function processSuccessfulPayment(Order $order): void
    {
        if ($order->processed) {
            return; // 避免重复处理
        }

        DB::transaction(function () use ($order) {
            // 创建群组成员记录
            GroupMember::create([
                'group_id' => $order->group_id,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'joined_at' => now(),
                'status' => 'active'
            ]);

            // 更新群组统计
            $order->group->increment('member_count');
            $order->group->increment('order_count');
            $order->group->increment('total_revenue', $order->amount);

            // 标记订单已处理
            $order->update(['processed' => true]);

            // 记录转化事件 - 完成支付
            $this->analyticsService->recordConversion($order->group_id, 'order', $order->amount);
        });
    }

    /**
     * 生成虚拟通知
     */
    private function generateVirtualNotifications(WechatGroup $group): array
    {
        if (!($group->marketing_config['social_proof']['enabled'] ?? false)) {
            return [];
        }

        $notifications = [
            '刚刚有3人加入了群组',
            '5分钟前有人完成了支付',
            '今日已有' . rand(10, 50) . '人加入',
            '限时优惠仅剩' . rand(5, 20) . '个名额'
        ];

        return array_slice($notifications, 0, rand(2, 4));
    }

    /**
     * 获取成功页面内容
     */
    private function getSuccessPageContent(WechatGroup $group): array
    {
        return [
            'title' => '支付成功！',
            'subtitle' => '恭喜您成功加入 ' . $group->title,
            'description' => '请保存下方二维码，扫码即可加入群组',
            'tips' => [
                '请及时保存群组二维码',
                '如二维码失效，请联系客服',
                '群组内容仅供学习交流使用',
                '请遵守群组规则，文明交流'
            ]
        ];
    }

    /**
     * 获取下一步操作
     */
    private function getNextSteps(WechatGroup $group): array
    {
        return [
            [
                'title' => '扫码加群',
                'description' => '使用微信扫描下方二维码加入群组',
                'icon' => 'qrcode'
            ],
            [
                'title' => '保存二维码',
                'description' => '长按保存二维码到相册，方便后续使用',
                'icon' => 'save'
            ],
            [
                'title' => '联系客服',
                'description' => '如遇问题，请及时联系专属客服',
                'icon' => 'service'
            ]
        ];
    }

    /**
     * 生成二维码
     */
    private function generateQRCode(string $url): string
    {
        return 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($url);
    }

    /**
     * 生成短链接
     */
    private function generateShortUrl(string $url): string
    {
        // 这里可以集成短链接服务
        // 暂时返回原链接
        return $url;
    }

    /**
     * 显示错误页面
     */
    private function showErrorPage(string $message): \Illuminate\Http\Response
    {
        return response()->view('landing.error', [
            'message' => $message,
            'redirect_url' => 'https://www.baidu.com'
        ]);
    }

    /**
     * 生成防红链接URL
     */
    private function generateAntiBlockUrl(WechatGroup $group): string
    {
        try {
            // 检查是否启用防红系统
            if (!$this->isAntiBlockEnabled($group)) {
                return route('group.landing', ['slug' => $group->slug]);
            }

            // 获取群组的域名池配置
            $antiBlockConfig = $group->anti_block_config ?? [];
            $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;

            if (!$domainPoolId) {
                Log::warning('群组启用防红但未配置域名池', ['group_id' => $group->id]);
                return route('group.landing', ['slug' => $group->slug]);
            }

            // 从域名池获取健康域名
            $healthyDomain = $this->getHealthyDomainFromPool($domainPoolId);
            
            if (!$healthyDomain) {
                Log::warning('域名池中没有可用的健康域名', [
                    'group_id' => $group->id,
                    'domain_pool_id' => $domainPoolId
                ]);
                return route('group.landing', ['slug' => $group->slug]);
            }

            // 构建防红链接
            $antiBlockUrl = "https://{$healthyDomain}/group/{$group->slug}";
            
            Log::info('生成防红链接成功', [
                'group_id' => $group->id,
                'domain' => $healthyDomain,
                'url' => $antiBlockUrl
            ]);

            return $antiBlockUrl;

        } catch (\Exception $e) {
            Log::error('生成防红链接失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            // 失败时返回默认链接
            return route('group.landing', ['slug' => $group->slug]);
        }
    }

    /**
     * 生成防红短链接
     */
    private function generateAntiBlockShortUrl(WechatGroup $group, string $originalUrl): string
    {
        try {
            // 如果启用防红系统，使用防红服务生成短链接
            if ($this->isAntiBlockEnabled($group)) {
                $shortLink = $this->antiBlockService->createShortLink(
                    $originalUrl,
                    "群组推广-{$group->title}",
                    $group->creator_id
                );

                if ($shortLink) {
                    return $shortLink->full_short_url;
                }
            }

            // 否则返回原链接
            return $originalUrl;

        } catch (\Exception $e) {
            Log::error('生成防红短链接失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return $originalUrl;
        }
    }

    /**
     * 检查群组是否启用防红系统
     */
    private function isAntiBlockEnabled(WechatGroup $group): bool
    {
        $antiBlockConfig = $group->anti_block_config ?? [];
        return ($antiBlockConfig['enabled'] ?? false) === true;
    }

    /**
     * 从域名池获取健康域名
     */
    private function getHealthyDomainFromPool(int $domainPoolId): ?string
    {
        try {
            $domainPool = DomainPool::find($domainPoolId);
            
            if (!$domainPool || $domainPool->status !== 'active') {
                return null;
            }

            // 优先使用负载均衡域名
            $healthyDomain = $domainPool->getLoadBalancedDomain();
            
            if (!$healthyDomain) {
                // 如果负载均衡失败，尝试获取最佳健康域名
                $healthyDomain = $domainPool->getBestHealthyDomain();
            }
            
            if (!$healthyDomain) {
                // 最后尝试随机获取一个域名
                $healthyDomain = $domainPool->getRandomActiveDomain();
            }

            // 记录域名使用
            if ($healthyDomain) {
                $domainPool->increment('switch_count');
                
                Log::info('成功获取健康域名', [
                    'domain_pool_id' => $domainPoolId,
                    'domain' => $healthyDomain,
                    'method' => 'load_balanced'
                ]);
            }

            return $healthyDomain;

        } catch (\Exception $e) {
            Log::error('从域名池获取健康域名失败', [
                'domain_pool_id' => $domainPoolId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 获取域名信息
     */
    private function getDomainInfo(WechatGroup $group): array
    {
        if (!$this->isAntiBlockEnabled($group)) {
            return [
                'enabled' => false,
                'domain_pool_id' => null,
                'current_domain' => parse_url(config('app.url'), PHP_URL_HOST),
                'health_status' => 'default'
            ];
        }

        $antiBlockConfig = $group->anti_block_config ?? [];
        $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;

        if (!$domainPoolId) {
            return [
                'enabled' => true,
                'domain_pool_id' => null,
                'current_domain' => parse_url(config('app.url'), PHP_URL_HOST),
                'health_status' => 'not_configured'
            ];
        }

        $healthyDomain = $this->getHealthyDomainFromPool($domainPoolId);

        return [
            'enabled' => true,
            'domain_pool_id' => $domainPoolId,
            'current_domain' => $healthyDomain ?: parse_url(config('app.url'), PHP_URL_HOST),
            'health_status' => $healthyDomain ? 'healthy' : 'no_healthy_domain'
        ];
    }
}