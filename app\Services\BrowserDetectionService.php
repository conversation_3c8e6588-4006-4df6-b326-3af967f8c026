<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

/**
 * 浏览器检测服务
 * 检测用户浏览器类型，支持防封和访问控制
 */
class BrowserDetectionService
{
    // 浏览器类型常量
    const BROWSER_WECHAT = 'wechat';
    const BROWSER_QQ = 'qq';
    const BROWSER_ALIPAY = 'alipay';
    const BROWSER_CHROME = 'chrome';
    const BROWSER_SAFARI = 'safari';
    const BROWSER_FIREFOX = 'firefox';
    const BROWSER_EDGE = 'edge';
    const BROWSER_IE = 'ie';
    const BROWSER_UNKNOWN = 'unknown';

    // 移动端浏览器
    const BROWSER_MOBILE_QQ = 'mobile_qq';
    const BROWSER_UC = 'uc';
    const BROWSER_BAIDU = 'baidu';
    const BROWSER_SOGOU = 'sogou';

    /**
     * 检测是否为微信浏览器
     */
    public function isWechatBrowser(): bool
    {
        $userAgent = $this->getUserAgent();
        return strpos($userAgent, 'MicroMessenger') !== false;
    }

    /**
     * 检测浏览器类型
     */
    public function getBrowserType(): string
    {
        $userAgent = $this->getUserAgent();

        // 微信浏览器
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            return self::BROWSER_WECHAT;
        }

        // QQ浏览器
        if (strpos($userAgent, 'QQBrowser') !== false || strpos($userAgent, 'MQQBrowser') !== false) {
            return self::BROWSER_MOBILE_QQ;
        }

        // QQ内置浏览器
        if (strpos($userAgent, 'QQ/') !== false) {
            return self::BROWSER_QQ;
        }

        // 支付宝
        if (strpos($userAgent, 'Alipay') !== false || strpos($userAgent, 'AlipayClient') !== false) {
            return self::BROWSER_ALIPAY;
        }

        // UC浏览器
        if (strpos($userAgent, 'UCBrowser') !== false || strpos($userAgent, 'UCWEB') !== false) {
            return self::BROWSER_UC;
        }

        // 百度浏览器
        if (strpos($userAgent, 'baiduboxapp') !== false || strpos($userAgent, 'BaiduHD') !== false) {
            return self::BROWSER_BAIDU;
        }

        // 搜狗浏览器
        if (strpos($userAgent, 'SogouMobileBrowser') !== false || strpos($userAgent, 'MetaSr') !== false) {
            return self::BROWSER_SOGOU;
        }

        // Edge浏览器
        if (strpos($userAgent, 'Edg/') !== false || strpos($userAgent, 'Edge/') !== false) {
            return self::BROWSER_EDGE;
        }

        // Chrome浏览器
        if (strpos($userAgent, 'Chrome') !== false && strpos($userAgent, 'Edg/') === false) {
            return self::BROWSER_CHROME;
        }

        // Safari浏览器
        if (strpos($userAgent, 'Safari') !== false && strpos($userAgent, 'Chrome') === false) {
            return self::BROWSER_SAFARI;
        }

        // Firefox浏览器
        if (strpos($userAgent, 'Firefox') !== false) {
            return self::BROWSER_FIREFOX;
        }

        // IE浏览器
        if (strpos($userAgent, 'MSIE') !== false || strpos($userAgent, 'Trident') !== false) {
            return self::BROWSER_IE;
        }

        return self::BROWSER_UNKNOWN;
    }

    /**
     * 获取详细的浏览器信息
     */
    public function getBrowserInfo(): array
    {
        $userAgent = $this->getUserAgent();
        $browserType = $this->getBrowserType();

        return [
            'type' => $browserType,
            'name' => $this->getBrowserName($browserType),
            'version' => $this->getBrowserVersion($userAgent, $browserType),
            'is_mobile' => $this->isMobile(),
            'is_wechat' => $this->isWechatBrowser(),
            'is_app' => $this->isAppBrowser(),
            'platform' => $this->getPlatform(),
            'user_agent' => $userAgent,
        ];
    }

    /**
     * 检测是否为移动端
     */
    public function isMobile(): bool
    {
        $userAgent = $this->getUserAgent();
        
        $mobileKeywords = [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 
            'Windows Phone', 'Opera Mini', 'IEMobile', 'Mobile Safari'
        ];

        foreach ($mobileKeywords as $keyword) {
            if (strpos($userAgent, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检测是否为App内置浏览器
     */
    public function isAppBrowser(): bool
    {
        $browserType = $this->getBrowserType();
        
        $appBrowsers = [
            self::BROWSER_WECHAT,
            self::BROWSER_QQ,
            self::BROWSER_ALIPAY,
            self::BROWSER_BAIDU,
        ];

        return in_array($browserType, $appBrowsers);
    }

    /**
     * 获取操作系统平台
     */
    public function getPlatform(): string
    {
        $userAgent = $this->getUserAgent();

        if (strpos($userAgent, 'Windows NT') !== false) {
            return 'Windows';
        } elseif (strpos($userAgent, 'Macintosh') !== false || strpos($userAgent, 'Mac OS X') !== false) {
            return 'macOS';
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            return 'iOS';
        } elseif (strpos($userAgent, 'Android') !== false) {
            return 'Android';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            return 'Linux';
        }

        return 'Unknown';
    }

    /**
     * 生成浏览器引导页面
     */
    public function generateBrowserGuidePage(?string $targetUrl = null, ?string $groupTitle = null): string
    {
        $fallbackUrl = 'https://www.baidu.com/s?wd=' . urlencode('今日头条');
        $targetUrl = $targetUrl ?: $fallbackUrl;
        $title = $groupTitle ? urlencode($groupTitle) : urlencode('加载中...');

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请在浏览器中打开</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
            text-align: center;
        }
        .container { 
            max-width: 400px; 
            margin: 50px auto; 
            background: white; 
            border-radius: 10px; 
            padding: 30px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon { 
            font-size: 48px; 
            margin-bottom: 20px; 
        }
        .title { 
            font-size: 18px; 
            font-weight: bold; 
            margin-bottom: 10px; 
            color: #333;
        }
        .desc { 
            color: #666; 
            line-height: 1.5; 
            margin-bottom: 20px;
        }
        .btn { 
            background: #007AFF; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            font-size: 16px;
            cursor: pointer;
        }
        .steps {
            text-align: left;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .step {
            margin: 8px 0;
            color: #555;
        }
    </style>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="icon">🌐</div>
        <div class="title">请在浏览器中打开</div>
        <div class="desc">为了更好的体验，请在浏览器中打开此链接</div>
        
        <div class="steps">
            <div class="step">1. 点击右上角菜单按钮</div>
            <div class="step">2. 选择"在浏览器中打开"</div>
            <div class="step">3. 或复制链接到浏览器访问</div>
        </div>
        
        <button class="btn" onclick="copyLink()">复制链接</button>
        <button class="btn" onclick="tryOpen()" style="margin-left: 10px; background: #28a745;">继续访问</button>
    </div>

<script>
document.title = decodeURIComponent(atob("' . base64_encode($title) . '"));

function copyLink() {
    var url = "' . $targetUrl . '";
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(function() {
            alert("链接已复制到剪贴板");
        });
    } else {
        // 兼容旧浏览器
        var textArea = document.createElement("textarea");
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        alert("链接已复制到剪贴板");
    }
}

function tryOpen() {
    try {
        setTimeout(function() { 
            window.location.href = "' . $targetUrl . '";
        }, 500);
    } catch(e) {
        window.location.href = "' . $fallbackUrl . '";
    }
}

// 自动检测并尝试跳转
setTimeout(function() {
    var ua = navigator.userAgent;
    // 如果不是微信浏览器，直接跳转
    if (ua.indexOf("MicroMessenger") === -1) {
        tryOpen();
    }
}, 2000);
</script>
</body>
</html>';
    }

    /**
     * 检查浏览器是否支持某个功能
     */
    public function supportFeature(string $feature): bool
    {
        $browserType = $this->getBrowserType();
        
        $featureSupport = [
            'webp' => [
                self::BROWSER_CHROME,
                self::BROWSER_FIREFOX,
                self::BROWSER_EDGE,
                self::BROWSER_WECHAT,
            ],
            'payment_api' => [
                self::BROWSER_CHROME,
                self::BROWSER_SAFARI,
                self::BROWSER_EDGE,
            ],
            'push_notification' => [
                self::BROWSER_CHROME,
                self::BROWSER_FIREFOX,
                self::BROWSER_EDGE,
                self::BROWSER_SAFARI,
            ],
        ];

        return in_array($browserType, $featureSupport[$feature] ?? []);
    }

    /**
     * 获取浏览器安全等级
     */
    public function getSecurityLevel(): string
    {
        $browserType = $this->getBrowserType();
        
        $securityLevels = [
            self::BROWSER_CHROME => 'high',
            self::BROWSER_FIREFOX => 'high',
            self::BROWSER_SAFARI => 'high',
            self::BROWSER_EDGE => 'high',
            self::BROWSER_WECHAT => 'medium',
            self::BROWSER_QQ => 'medium',
            self::BROWSER_UC => 'medium',
            self::BROWSER_ALIPAY => 'medium',
            self::BROWSER_IE => 'low',
        ];

        return $securityLevels[$browserType] ?? 'unknown';
    }

    /**
     * 获取用户代理字符串
     */
    private function getUserAgent(): string
    {
        return request()->header('User-Agent', '');
    }

    /**
     * 获取浏览器名称
     */
    private function getBrowserName(string $browserType): string
    {
        $names = [
            self::BROWSER_WECHAT => '微信浏览器',
            self::BROWSER_QQ => 'QQ浏览器',
            self::BROWSER_MOBILE_QQ => 'QQ浏览器',
            self::BROWSER_ALIPAY => '支付宝',
            self::BROWSER_UC => 'UC浏览器',
            self::BROWSER_BAIDU => '百度浏览器',
            self::BROWSER_SOGOU => '搜狗浏览器',
            self::BROWSER_CHROME => 'Chrome',
            self::BROWSER_SAFARI => 'Safari',
            self::BROWSER_FIREFOX => 'Firefox',
            self::BROWSER_EDGE => 'Edge',
            self::BROWSER_IE => 'Internet Explorer',
            self::BROWSER_UNKNOWN => '未知浏览器',
        ];

        return $names[$browserType] ?? '未知浏览器';
    }

    /**
     * 获取浏览器版本
     */
    private function getBrowserVersion(string $userAgent, string $browserType): string
    {
        $patterns = [
            self::BROWSER_CHROME => '/Chrome\/([0-9.]+)/',
            self::BROWSER_FIREFOX => '/Firefox\/([0-9.]+)/',
            self::BROWSER_SAFARI => '/Version\/([0-9.]+)/',
            self::BROWSER_EDGE => '/Edg\/([0-9.]+)/',
            self::BROWSER_IE => '/MSIE ([0-9.]+)/',
            self::BROWSER_WECHAT => '/MicroMessenger\/([0-9.]+)/',
            self::BROWSER_QQ => '/QQ\/([0-9.]+)/',
            self::BROWSER_UC => '/UCBrowser\/([0-9.]+)/',
        ];

        if (isset($patterns[$browserType])) {
            if (preg_match($patterns[$browserType], $userAgent, $matches)) {
                return $matches[1];
            }
        }

        return 'Unknown';
    }

    /**
     * 获取浏览器统计信息
     */
    public function getBrowserStats(int $days = 7): array
    {
        return Cache::remember('browser_stats_' . $days, 3600, function () use ($days) {
            $logs = \App\Models\GroupAccessLog::where('created_at', '>=', now()->subDays($days))
                ->selectRaw('browser_type, COUNT(*) as count, COUNT(DISTINCT visitor_ip) as unique_visitors')
                ->groupBy('browser_type')
                ->orderBy('count', 'desc')
                ->get();

            $total = $logs->sum('count');
            $stats = [];

            foreach ($logs as $log) {
                $stats[] = [
                    'browser_type' => $log->browser_type,
                    'browser_name' => $this->getBrowserName($log->browser_type),
                    'count' => $log->count,
                    'unique_visitors' => $log->unique_visitors,
                    'percentage' => $total > 0 ? round(($log->count / $total) * 100, 2) : 0,
                ];
            }

            return $stats;
        });
    }
}