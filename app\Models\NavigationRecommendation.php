<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 导航推荐模型
 */
class NavigationRecommendation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'navigation_key', 'recommendation_type', 'score',
        'reason', 'is_clicked', 'recommended_at', 'clicked_at'
    ];

    protected $casts = [
        'score' => 'decimal:4',
        'reason' => 'array',
        'is_clicked' => 'boolean',
        'recommended_at' => 'datetime',
        'clicked_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联导航配置
     */
    public function navigationConfig(): BelongsTo
    {
        return $this->belongsTo(NavigationConfig::class, 'navigation_key', 'key');
    }

    /**
     * 记录点击
     */
    public function recordClick()
    {
        $this->update([
            'is_clicked' => true,
            'clicked_at' => now()
        ]);
    }

    /**
     * 获取推荐效果统计
     */
    public static function getPerformanceStats($days = 30)
    {
        $baseQuery = self::where('recommended_at', '>=', now()->subDays($days));

        return [
            'total_recommendations' => (clone $baseQuery)->count(),
            'total_clicks' => (clone $baseQuery)->where('is_clicked', true)->count(),
            'ctr' => (clone $baseQuery)->avg('is_clicked') * 100,
            'by_type' => (clone $baseQuery)
                ->selectRaw('recommendation_type, COUNT(*) as total, AVG(is_clicked) * 100 as ctr')
                ->groupBy('recommendation_type')
                ->get()
        ];
    }
}