import { faker } from '@faker-js/faker'

// 生成模拟群组数据
const generateMockGroups = (count) => {
  const groups = []
  const categories = ['startup', 'finance', 'tech', 'education', 'other']
  const statuses = ['active', 'paused', 'full', 'pending']
  for (let i = 1; i <= count; i++) {
    groups.push({
      id: i,
      name: faker.company.name() + '交流群',
      description: faker.lorem.sentence(5),
      avatar: faker.image.avatar(),
      owner_name: faker.person.fullName(),
      owner_role: '群主',
      category: faker.helpers.arrayElement(categories),
      price: faker.helpers.arrayElement([0, 0, 0, 9.9, 19.9, 29.9]),
      current_members: faker.number.int({ min: 50, max: 500 }),
      max_members: 500,
      health_score: faker.number.int({ min: 30, max: 100 }),
      status: faker.helpers.arrayElement(statuses),
      created_at: faker.date.past().toISOString()
    })
  }
  return groups
}

const allGroups = generateMockGroups(123)

export const mockCommunityAPI = {
  getGroupList(params) {
    const { page = 1, limit = 10, keyword, status, category } = params

    let filteredData = allGroups.filter(group => {
      let match = true
      if (keyword && !group.name.includes(keyword) && !(group.owner_name || '').includes(keyword)) {
        match = false
      }
      if (status && group.status !== status) {
        match = false
      }
      if (category && group.category !== category) {
        match = false
      }
      return match
    })

    const start = (page - 1) * limit
    const end = page * limit
    const paginatedData = filteredData.slice(start, end)

    // **修正返回值结构**
    return Promise.resolve({
      code: 0,
      data: {
        list: paginatedData,
        total: filteredData.length
      },
      message: '成功'
    })
  },

  getGroupStats() {
    return Promise.resolve({
      code: 0,
      data: {
        total_groups: allGroups.length,
        active_groups: allGroups.filter(g => g.status === 'active').length,
        total_members: allGroups.reduce((sum, g) => sum + g.current_members, 0),
        total_revenue: allGroups.reduce((sum, g) => sum + g.price * g.current_members, 0) / 100 // 假设收入与价格和人数有关
      },
      message: '成功'
    })
  },

  // 其他mock方法...
  getGroupDetail(id) {
    const group = allGroups.find(g => g.id === id)
    return Promise.resolve({
      code: 0,
      data: group || null,
      message: '成功'
    })
  },

  deleteGroup(id) {
    const index = allGroups.findIndex(g => g.id === id)
    if (index !== -1) {
      allGroups.splice(index, 1)
    }
    return Promise.resolve({ code: 0, message: '删除成功' })
  },

  updateGroupStatus(id, status) {
    const group = allGroups.find(g => g.id === id)
    if (group) {
      group.status = status
    }
    return Promise.resolve({ code: 0, message: '状态更新成功' })
  },

  // ========== 模板管理相关API ==========

  // 获取模板列表
  getTemplates(params = {}) {
    const templates = [
      {
        id: 1,
        template_name: '高端商务群模板',
        template_code: 'BUSINESS_001',
        description: '适用于商务交流、项目合作等高端群组',
        category: 'business',
        category_name: '商务类',
        is_preset: true,
        is_active: true,
        usage_count: 156,
        cover_image_url: 'https://picsum.photos/200/120?random=1',
        creator: { username: '系统' },
        created_at: '2024-01-15T10:00:00Z',
        template_data: {
          title: '{{city}}商务精英交流群',
          description: '汇聚{{city}}地区商务精英，分享商机，共创未来',
          price: 99.00,
          member_limit: 500,
          virtual_members: 328,
          virtual_orders: 89,
          virtual_income: 15680.50
        },
        sort_order: 100
      },
      {
        id: 2,
        template_name: '副业赚钱群模板',
        template_code: 'SIDEJOB_001',
        description: '专为副业创业者设计的群组模板',
        category: 'finance',
        category_name: '财经类',
        is_preset: true,
        is_active: true,
        usage_count: 289,
        cover_image_url: 'https://picsum.photos/200/120?random=2',
        creator: { username: '系统' },
        created_at: '2024-01-10T14:30:00Z',
        template_data: {
          title: '{{city}}副业赚钱交流群',
          description: '分享副业项目，交流赚钱经验，实现财务自由',
          price: 58.00,
          member_limit: 300,
          virtual_members: 267,
          virtual_orders: 156,
          virtual_income: 8960.00
        },
        sort_order: 90
      },
      {
        id: 3,
        template_name: '学习成长群模板',
        template_code: 'STUDY_001',
        description: '知识分享、技能提升类群组模板',
        category: 'education',
        category_name: '教育类',
        is_preset: false,
        is_active: true,
        usage_count: 78,
        cover_image_url: 'https://picsum.photos/200/120?random=3',
        creator: { username: 'admin' },
        created_at: '2024-01-20T09:15:00Z',
        template_data: {
          title: '{{city}}学习成长交流群',
          description: '一起学习，共同成长，分享知识与经验',
          price: 29.00,
          member_limit: 200,
          virtual_members: 145,
          virtual_orders: 67,
          virtual_income: 1943.00
        },
        sort_order: 80
      },
      {
        id: 4,
        template_name: '健身运动群模板',
        template_code: 'FITNESS_001',
        description: '健身爱好者交流群组模板',
        category: 'lifestyle',
        category_name: '生活类',
        is_preset: false,
        is_active: true,
        usage_count: 45,
        cover_image_url: 'https://picsum.photos/200/120?random=4',
        creator: { username: 'admin' },
        created_at: '2024-01-25T16:45:00Z',
        template_data: {
          title: '{{city}}健身运动交流群',
          description: '分享健身经验，制定运动计划，一起变得更健康',
          price: 39.00,
          member_limit: 150,
          virtual_members: 89,
          virtual_orders: 34,
          virtual_income: 1326.00
        },
        sort_order: 70
      }
    ]

    // 模拟分页和筛选
    const { page = 1, per_page = 10, keyword, category, is_preset, is_active } = params
    let filteredTemplates = [...templates]

    // 关键词筛选
    if (keyword) {
      filteredTemplates = filteredTemplates.filter(t =>
        t.template_name.includes(keyword) ||
        t.description.includes(keyword) ||
        t.template_code.includes(keyword)
      )
    }

    // 分类筛选
    if (category) {
      filteredTemplates = filteredTemplates.filter(t => t.category === category)
    }

    // 类型筛选
    if (typeof is_preset === 'boolean') {
      filteredTemplates = filteredTemplates.filter(t => t.is_preset === is_preset)
    }

    // 状态筛选
    if (typeof is_active === 'boolean') {
      filteredTemplates = filteredTemplates.filter(t => t.is_active === is_active)
    }

    // 分页
    const total = filteredTemplates.length
    const start = (page - 1) * per_page
    const end = start + per_page
    const data = filteredTemplates.slice(start, end)

    return Promise.resolve({
      code: 200,
      data: {
        data,
        total,
        current_page: page,
        per_page,
        last_page: Math.ceil(total / per_page)
      },
      message: '获取成功'
    })
  },

  // 获取模板详情
  getTemplate(id) {
    return this.getTemplates().then(res => {
      const template = res.data.data.find(t => t.id == id)
      if (template) {
        return Promise.resolve({
          code: 200,
          data: template,
          message: '获取成功'
        })
      } else {
        return Promise.resolve({
          code: 404,
          data: null,
          message: '模板不存在'
        })
      }
    })
  },

  // 获取模板分类
  getTemplateCategories() {
    const categories = {
      business: '商务类',
      finance: '财经类',
      education: '教育类',
      lifestyle: '生活类',
      entertainment: '娱乐类',
      technology: '科技类'
    }

    return Promise.resolve({
      code: 200,
      data: categories,
      message: '获取成功'
    })
  },

  // 创建模板
  createTemplate(data) {
    const newTemplate = {
      id: Date.now(),
      ...data,
      usage_count: 0,
      creator: { username: 'admin' },
      created_at: new Date().toISOString(),
      is_preset: false
    }

    return Promise.resolve({
      code: 200,
      data: newTemplate,
      message: '创建成功'
    })
  },

  // 更新模板
  updateTemplate(id, data) {
    return Promise.resolve({
      code: 200,
      data: { id, ...data },
      message: '更新成功'
    })
  },

  // 删除模板
  deleteTemplate(id) {
    return Promise.resolve({
      code: 200,
      data: null,
      message: '删除成功'
    })
  },

  // 复制模板
  copyTemplate(id) {
    return this.getTemplate(id).then(res => {
      if (res.code === 200) {
        const template = res.data
        const newTemplate = {
          ...template,
          id: Date.now(),
          template_name: template.template_name + ' (副本)',
          usage_count: 0,
          created_at: new Date().toISOString(),
          is_preset: false
        }
        return Promise.resolve({
          code: 200,
          data: newTemplate,
          message: '复制成功'
        })
      }
      return res
    })
  },

  // 切换模板状态
  toggleTemplateStatus(id, status) {
    return Promise.resolve({
      code: 200,
      data: { id, is_active: status },
      message: '状态更新成功'
    })
  }
}