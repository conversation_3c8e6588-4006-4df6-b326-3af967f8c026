<template>
  <div class="group-list">
    <div class="page-header">
      <h1>社群管理</h1>
      <el-button type="primary" @click="goToCreatePage">
        <el-icon><Plus /></el-icon>
        创建社群
      </el-button>
    </div>

    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索社群名称..."
        style="width: 300px"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="group-grid">
      <el-card 
        v-for="group in filteredGroups" 
        :key="group.id" 
        class="group-card"
        shadow="hover"
      >
        <div class="group-header">
          <div class="group-avatar">
            <img :src="group.avatar" :alt="group.name" />
          </div>
          <div class="group-info">
            <h3>{{ group.name }}</h3>
            <p>{{ group.description }}</p>
          </div>
        </div>

        <div class="group-stats">
          <div class="stat-item">
            <span class="stat-label">成员数</span>
            <span class="stat-value">{{ group.memberCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">今日活跃</span>
            <span class="stat-value">{{ group.activeToday }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">状态</span>
            <el-tag :type="group.status === 'active' ? 'success' : 'warning'">
              {{ group.status === 'active' ? '正常' : '暂停' }}
            </el-tag>
          </div>
        </div>

        <div class="group-actions">
          <el-button size="small" @click="viewGroup(group)">查看</el-button>
          <el-button size="small" type="primary" @click="editGroup(group)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteGroup(group)">删除</el-button>
        </div>
      </el-card>
    </div>

    <!-- 创建社群对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建社群" width="500px">
      <el-form :model="newGroup" label-width="80px">
        <el-form-item label="社群名称">
          <el-input v-model="newGroup.name" placeholder="请输入社群名称" />
        </el-form-item>
        <el-form-item label="社群描述">
          <el-input 
            v-model="newGroup.description" 
            type="textarea" 
            placeholder="请输入社群描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="入群价格">
          <el-input-number v-model="newGroup.price" :min="0" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createGroup">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const searchQuery = ref('')
const showCreateDialog = ref(false)

const newGroup = ref({
  name: '',
  description: '',
  price: 0
})

// 模拟数据
const groups = ref([
  {
    id: 1,
    name: '前端开发交流群',
    description: '专注前端技术交流与学习',
    avatar: 'https://via.placeholder.com/60',
    memberCount: 1234,
    activeToday: 89,
    status: 'active'
  },
  {
    id: 2,
    name: '产品经理成长营',
    description: '产品经理技能提升与经验分享',
    avatar: 'https://via.placeholder.com/60',
    memberCount: 567,
    activeToday: 45,
    status: 'active'
  },
  {
    id: 3,
    name: '设计师联盟',
    description: 'UI/UX设计师交流平台',
    avatar: 'https://via.placeholder.com/60',
    memberCount: 890,
    activeToday: 67,
    status: 'paused'
  },
  {
    id: 4,
    name: '创业者俱乐部',
    description: '创业经验分享与资源对接',
    avatar: 'https://via.placeholder.com/60',
    memberCount: 345,
    activeToday: 23,
    status: 'active'
  }
])

const filteredGroups = computed(() => {
  if (!searchQuery.value) return groups.value
  
  return groups.value.filter(group => 
    group.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    group.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const viewGroup = (group) => {
  ElMessage.info(`查看社群：${group.name}`)
}

const editGroup = (group) => {
  ElMessage.info(`编辑社群：${group.name}`)
}

const deleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除社群"${group.name}"吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除操作
    const index = groups.value.findIndex(g => g.id === group.id)
    if (index > -1) {
      groups.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消
  }
}

// 跳转到完整的创建页面
const goToCreatePage = () => {
  router.push('/community/add-enhanced')
}

const createGroup = () => {
  if (!newGroup.value.name.trim()) {
    ElMessage.error('请输入社群名称')
    return
  }
  
  // 模拟创建操作
  const newId = Math.max(...groups.value.map(g => g.id)) + 1
  groups.value.push({
    id: newId,
    name: newGroup.value.name,
    description: newGroup.value.description,
    avatar: 'https://via.placeholder.com/60',
    memberCount: 0,
    activeToday: 0,
    status: 'active'
  })
  
  // 重置表单
  newGroup.value = {
    name: '',
    description: '',
    price: 0
  }
  
  showCreateDialog.value = false
  ElMessage.success('创建成功')
}
</script>

<style scoped>
.group-list {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #262626;
}

.search-bar {
  margin-bottom: 24px;
}

.group-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.group-card {
  border-radius: 8px;
}

.group-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.group-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.group-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #262626;
}

.group-info p {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

.group-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #262626;
}

.group-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
