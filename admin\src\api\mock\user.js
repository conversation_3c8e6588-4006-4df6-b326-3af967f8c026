import { faker } from '@faker-js/faker'

// 模拟用户数据
const allUsers = Array.from({ length: 150 }, (_, i) => ({
  id: i + 1,
  username: faker.internet.userName(),
  name: faker.person.fullName(),
  email: faker.internet.email(),
  phone: faker.phone.number(),
  avatar: faker.image.avatar(),
  role: faker.helpers.arrayElement(['user', 'agent', 'group_owner']),
  status: faker.helpers.arrayElement([0, 1]), // 0: 禁用, 1: 正常
  vip_level: faker.helpers.arrayElement([0, 1, 2, 3, 4, 5]),
  balance: faker.finance.amount(0, 10000, 2),
  points: faker.number.int({ min: 0, max: 100000 }),
  created_at: faker.date.past({ years: 2 }),
  last_login_at: faker.date.recent(),
  bio: faker.lorem.paragraph(),
}))

// 模拟订单数据
const allOrders = Array.from({ length: 500 }, (_, i) => ({
  id: i + 1,
  user_id: faker.number.int({ min: 1, max: 150 }),
  order_no: faker.string.uuid(),
  amount: faker.finance.amount(10, 500, 2),
  status: faker.helpers.arrayElement(['pending', 'paid', 'cancelled', 'refunded']),
  created_at: faker.date.past({ years: 1 }),
  wechat_group: {
    title: faker.lorem.words(3)
  }
}))

// 模拟积分记录
const allPointsHistory = Array.from({ length: 1000 }, (_, i) => ({
  id: i + 1,
  user_id: faker.number.int({ min: 1, max: 150 }),
  type: faker.helpers.arrayElement(['add', 'subtract']),
  points: faker.number.int({ min: 10, max: 1000 }),
  description: faker.lorem.sentence(),
  created_at: faker.date.past({ years: 1 }),
}))

export const mockUserAPI = {
  getProfile: () => Promise.resolve({
    code: 0,
    data: allUsers[0], // 总是返回第一个用户作为当前登录用户
    message: '成功'
  }),

  getStats: () => Promise.resolve({
    code: 0,
    data: {
      total_orders: faker.number.int({ min: 50, max: 200 }),
      total_spent: faker.finance.amount(5000, 50000, 2),
      points: allUsers[0].points,
      coupons: faker.number.int({ min: 0, max: 20 }),
    },
    message: '成功'
  }),

  getRecentOrders: (params) => {
    const { limit = 5 } = params
    const userOrders = allOrders.filter(o => o.user_id === allUsers[0].id)
    return Promise.resolve({
      code: 0,
      data: userOrders.slice(0, limit),
      message: '成功'
    })
  },

  getPointsHistory: (params) => {
    const { limit = 5 } = params
    const userPointsHistory = allPointsHistory.filter(p => p.user_id === allUsers[0].id)
    return Promise.resolve({
      code: 0,
      data: userPointsHistory.slice(0, limit),
      message: '成功'
    })
  },

  getConsumptionAnalysis: (params) => {
    const { period = 'month' } = params
    const labels = Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`)
    const data = Array.from({ length: 30 }, () => faker.finance.amount(0, 1000, 2))
    
    return Promise.resolve({
      code: 0,
      data: {
        summary: {
          total_amount: data.reduce((sum, val) => sum + parseFloat(val), 0).toFixed(2),
          total_orders: faker.number.int({ min: 10, max: 50 }),
          avg_amount: (data.reduce((sum, val) => sum + parseFloat(val), 0) / data.length).toFixed(2),
          max_amount: Math.max(...data).toFixed(2),
        },
        chart: {
          labels,
          data,
        }
      },
      message: '成功'
    })
  },

  updateProfile: (data) => {
    const userIndex = allUsers.findIndex(u => u.id === allUsers[0].id)
    if (userIndex !== -1) {
      allUsers[userIndex] = { ...allUsers[userIndex], ...data }
    }
    return Promise.resolve({
      code: 0,
      data: null,
      message: '更新成功'
    })
  },
  
  // 其他 user.js 中导出的函数
  getUserList: (params) => {
    const { page = 1, limit = 10 } = params
    const start = (page - 1) * limit
    const end = page * limit
    return Promise.resolve({
      code: 0,
      data: {
        list: allUsers.slice(start, end),
        total: allUsers.length
      },
      message: '成功'
    })
  },
  
  getUserStats: () => Promise.resolve({
    code: 0,
    data: {
      total_users: allUsers.length,
      active_today: faker.number.int({ min: 100, max: 500 }),
      new_today: faker.number.int({ min: 10, max: 50 }),
      banned_count: allUsers.filter(u => u.status === 0).length
    },
    message: '成功'
  }),
  
  getUserDetail: (id) => Promise.resolve({
    code: 0,
    data: allUsers.find(u => u.id === id),
    message: '成功'
  }),
  
  createUser: (data) => {
    const newUser = {
      id: allUsers.length + 1,
      ...data,
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString(),
    }
    allUsers.push(newUser)
    return Promise.resolve({
      code: 0,
      data: newUser,
      message: '创建成功'
    })
  },
  
  updateUser: (id, data) => {
    const userIndex = allUsers.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      allUsers[userIndex] = { ...allUsers[userIndex], ...data }
    }
    return Promise.resolve({
      code: 0,
      data: null,
      message: '更新成功'
    })
  },
  
  deleteUser: (id) => {
    const userIndex = allUsers.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      allUsers.splice(userIndex, 1)
    }
    return Promise.resolve({
      code: 0,
      data: null,
      message: '删除成功'
    })
  },
  
  updateUserStatus: (id, status) => {
    const userIndex = allUsers.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      allUsers[userIndex].status = status
    }
    return Promise.resolve({
      code: 0,
      data: null,
      message: '状态更新成功'
    })
  },
  
  adjustUserBalance: (id, data) => {
    const userIndex = allUsers.findIndex(u => u.id === id)
    if (userIndex !== -1) {
      allUsers[userIndex].balance = (parseFloat(allUsers[userIndex].balance) + parseFloat(data.amount)).toFixed(2)
    }
    return Promise.resolve({
      code: 0,
      data: null,
      message: '余额调整成功'
    })
  },
  
  resetUserPassword: (id) => Promise.resolve({
    code: 0,
    data: null,
    message: '密码重置成功'
  }),
  
  exportUsers: () => Promise.resolve({
    code: 0,
    data: new Blob(["csv,content\nuser1,data1"], { type: 'text/csv' }),
    message: '导出成功'
  }),
  
  batchUpdateUserStatus: (userIds, status) => {
    allUsers.forEach(user => {
      if (userIds.includes(user.id)) {
        user.status = status
      }
    })
    return Promise.resolve({
      code: 0,
      data: null,
      message: '批量更新成功'
    })
  },
  
  getUserBalanceLogs: (id, params) => {
    const { page = 1, limit = 10 } = params
    const logs = Array.from({ length: 20 }, () => ({
      id: faker.string.uuid(),
      amount: faker.finance.amount(-100, 100, 2),
      description: faker.lorem.sentence(),
      created_at: faker.date.past({ years: 1 }),
    }))
    return Promise.resolve({
      code: 0,
      data: {
        list: logs.slice((page - 1) * limit, page * limit),
        total: logs.length
      },
      message: '成功'
    })
  },
  
  getUserOrders: (id, params) => {
    const { page = 1, limit = 10 } = params
    const userOrders = allOrders.filter(o => o.user_id === id)
    return Promise.resolve({
      code: 0,
      data: {
        list: userOrders.slice((page - 1) * limit, page * limit),
        total: userOrders.length
      },
      message: '成功'
    })
  },
}