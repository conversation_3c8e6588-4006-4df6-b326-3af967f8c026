<template>
  <div class="conversion-dashboard bg-white rounded-xl shadow-lg p-6">
    <!-- 仪表板头部 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">转化率分析仪表板</h3>
          <p class="text-sm text-gray-600">实时预测和优化建议</p>
        </div>
      </div>
      
      <!-- 刷新按钮 -->
      <button 
        @click="refreshAnalysis"
        :disabled="isAnalyzing"
        class="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 transition-colors"
      >
        <svg :class="['w-4 h-4 mr-1', { 'animate-spin': isAnalyzing }]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        {{ isAnalyzing ? '分析中...' : '刷新分析' }}
      </button>
    </div>

    <!-- 核心指标 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-green-600">{{ metrics.conversionRate }}%</div>
            <div class="text-sm text-green-700">预估转化率</div>
          </div>
          <div class="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
        <div class="mt-2 flex items-center text-xs">
          <span :class="['mr-1', metrics.conversionTrend > 0 ? 'text-green-600' : 'text-red-600']">
            {{ metrics.conversionTrend > 0 ? '↗' : '↘' }}
          </span>
          <span class="text-gray-600">较昨日 {{ Math.abs(metrics.conversionTrend) }}%</span>
        </div>
      </div>

      <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-blue-600">{{ metrics.expectedOrders }}</div>
            <div class="text-sm text-blue-700">预期日订单</div>
          </div>
          <div class="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"></path>
            </svg>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-600">
          基于当前设置预测
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-purple-600">¥{{ metrics.expectedRevenue }}</div>
            <div class="text-sm text-purple-700">预期日收入</div>
          </div>
          <div class="w-8 h-8 bg-purple-200 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-600">
          {{ metrics.expectedOrders }} × ¥{{ formData.price || 0 }}
        </div>
      </div>

      <div class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4 border border-orange-200">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-orange-600">{{ metrics.optimizationScore }}/100</div>
            <div class="text-sm text-orange-700">优化评分</div>
          </div>
          <div class="w-8 h-8 bg-orange-200 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-600">
          还有 {{ 100 - metrics.optimizationScore }} 分提升空间
        </div>
      </div>
    </div>

    <!-- 转化漏斗分析 -->
    <div class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-4">转化漏斗分析</h4>
      <div class="space-y-3">
        <div 
          v-for="(stage, index) in conversionFunnel" 
          :key="stage.name"
          class="relative"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <div :class="['w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3', stage.color]">
                {{ index + 1 }}
              </div>
              <span class="font-medium text-gray-900">{{ stage.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900">{{ stage.rate }}%</span>
              <span class="text-xs text-gray-500">({{ stage.count }} 人)</span>
            </div>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 ml-9">
            <div 
              :class="['h-2 rounded-full transition-all duration-500', stage.bgColor]"
              :style="{ width: `${stage.rate}%` }"
            ></div>
          </div>
          
          <!-- 优化建议 -->
          <div v-if="stage.suggestion" class="ml-9 mt-2 text-xs text-blue-600 bg-blue-50 rounded px-2 py-1 inline-block">
            💡 {{ stage.suggestion }}
          </div>
        </div>
      </div>
    </div>

    <!-- 竞争对手分析 -->
    <div class="mb-6">
      <h4 class="text-lg font-semibold text-gray-900 mb-4">竞争对手分析</h4>
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-lg font-bold text-gray-900">{{ competitorAnalysis.averagePrice }}</div>
            <div class="text-sm text-gray-600">同类群组均价</div>
            <div :class="['text-xs mt-1', priceCompetitiveness.class]">
              {{ priceCompetitiveness.text }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-gray-900">{{ competitorAnalysis.averageConversion }}%</div>
            <div class="text-sm text-gray-600">同类平均转化率</div>
            <div :class="['text-xs mt-1', conversionCompetitiveness.class]">
              {{ conversionCompetitiveness.text }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-gray-900">{{ competitorAnalysis.marketShare }}%</div>
            <div class="text-sm text-gray-600">预估市场份额</div>
            <div class="text-xs text-gray-600 mt-1">
              基于当前设置
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化建议列表 -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">智能优化建议</h4>
        <div class="text-sm text-gray-500">
          {{ priorityRecommendations.length }} 条建议
        </div>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="recommendation in priorityRecommendations" 
          :key="recommendation.id"
          class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
          @click="applyRecommendation(recommendation)"
        >
          <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium', recommendation.priorityColor]">
            {{ recommendation.priority }}
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between mb-1">
              <h5 class="font-medium text-gray-900">{{ recommendation.title }}</h5>
              <div class="flex items-center text-sm text-green-600">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                +{{ recommendation.impact }}% 转化率
              </div>
            </div>
            <p class="text-sm text-gray-600 mb-2">{{ recommendation.description }}</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2 text-xs text-gray-500">
                <span>难度: {{ recommendation.difficulty }}</span>
                <span>•</span>
                <span>预计用时: {{ recommendation.timeEstimate }}</span>
              </div>
              <button class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                立即应用 →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- A/B测试建议 -->
    <div v-if="abTestSuggestions.length > 0" class="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
      <h4 class="text-lg font-semibold text-gray-900 mb-3">🧪 A/B测试建议</h4>
      <div class="space-y-2">
        <div 
          v-for="test in abTestSuggestions" 
          :key="test.id"
          class="flex items-center justify-between p-3 bg-white rounded-lg"
        >
          <div>
            <div class="font-medium text-gray-900">{{ test.title }}</div>
            <div class="text-sm text-gray-600">{{ test.description }}</div>
          </div>
          <button 
            @click="startABTest(test)"
            class="px-3 py-1 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 transition-colors"
          >
            开始测试
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['apply-recommendation', 'start-ab-test'])

// 响应式数据
const isAnalyzing = ref(false)
const metrics = ref({
  conversionRate: 0,
  conversionTrend: 0,
  expectedOrders: 0,
  expectedRevenue: 0,
  optimizationScore: 0
})

const competitorAnalysis = ref({
  averagePrice: '¥29.9',
  averageConversion: 4.2,
  marketShare: 15
})

const conversionFunnel = ref([
  { name: '页面访问', rate: 100, count: 1000, color: 'bg-blue-500 text-white', bgColor: 'bg-blue-500' },
  { name: '产生兴趣', rate: 45, count: 450, color: 'bg-green-500 text-white', bgColor: 'bg-green-500', suggestion: '优化标题和描述可提升兴趣度' },
  { name: '查看详情', rate: 25, count: 250, color: 'bg-yellow-500 text-white', bgColor: 'bg-yellow-500', suggestion: '添加社会证明可提升查看率' },
  { name: '点击购买', rate: 12, count: 120, color: 'bg-orange-500 text-white', bgColor: 'bg-orange-500', suggestion: '优化CTA按钮文案' },
  { name: '完成支付', rate: 8, count: 80, color: 'bg-red-500 text-white', bgColor: 'bg-red-500', suggestion: '简化支付流程' }
])

const priorityRecommendations = ref([
  {
    id: 1,
    priority: 1,
    priorityColor: 'bg-red-500',
    title: '优化群组标题',
    description: '当前标题吸引力不足，建议使用更具诱惑力的词汇',
    impact: 2.3,
    difficulty: '简单',
    timeEstimate: '5分钟',
    action: 'optimize_title'
  },
  {
    id: 2,
    priority: 2,
    priorityColor: 'bg-orange-500',
    title: '添加限时优惠',
    description: '设置24小时限时优惠，创造紧迫感',
    impact: 1.8,
    difficulty: '简单',
    timeEstimate: '3分钟',
    action: 'add_urgency'
  },
  {
    id: 3,
    priority: 3,
    priorityColor: 'bg-yellow-500',
    title: '增加用户评价',
    description: '添加更多真实用户评价，提升信任度',
    impact: 1.5,
    difficulty: '中等',
    timeEstimate: '10分钟',
    action: 'add_reviews'
  }
])

const abTestSuggestions = ref([
  {
    id: 1,
    title: '价格策略测试',
    description: '测试 ¥19.9 vs ¥29.9 的转化效果',
    variants: ['¥19.9', '¥29.9']
  },
  {
    id: 2,
    title: 'CTA按钮文案测试',
    description: '测试不同按钮文案的点击率',
    variants: ['立即加入', '马上进群', '限时加入']
  }
])

// 计算属性
const priceCompetitiveness = computed(() => {
  const price = props.formData.price || 0
  const avgPrice = 29.9
  
  if (price < avgPrice * 0.8) {
    return { text: '价格优势明显', class: 'text-green-600' }
  } else if (price > avgPrice * 1.2) {
    return { text: '价格偏高', class: 'text-red-600' }
  } else {
    return { text: '价格合理', class: 'text-blue-600' }
  }
})

const conversionCompetitiveness = computed(() => {
  const rate = metrics.value.conversionRate
  const avgRate = competitorAnalysis.value.averageConversion
  
  if (rate > avgRate * 1.2) {
    return { text: '转化率优秀', class: 'text-green-600' }
  } else if (rate < avgRate * 0.8) {
    return { text: '转化率偏低', class: 'text-red-600' }
  } else {
    return { text: '转化率正常', class: 'text-blue-600' }
  }
})

// 方法
const calculateMetrics = () => {
  const formData = props.formData
  
  // 基础转化率计算
  let baseRate = 2.5
  let optimizationScore = 30
  
  // 标题优化
  if (formData.title && formData.title.length >= 5) {
    baseRate += 0.8
    optimizationScore += 15
  }
  
  // 价格优化
  if (formData.price >= 9.9 && formData.price <= 99.9) {
    baseRate += 1.2
    optimizationScore += 20
  }
  
  // 社会证明
  if (formData.like_count > 0) {
    baseRate += 1.0
    optimizationScore += 15
  }
  
  // 紧迫感营销
  if (formData.show_limited_time) {
    baseRate += 1.5
    optimizationScore += 20
  }
  
  // 用户评价
  const reviewCount = formData.reviews?.filter(r => r.content).length || 0
  if (reviewCount >= 2) {
    baseRate += 0.8
    optimizationScore += 10
  }
  
  const conversionRate = Math.min(baseRate, 15.0)
  const expectedOrders = Math.round(conversionRate * 10)
  const expectedRevenue = Math.round(expectedOrders * (formData.price || 0))
  
  metrics.value = {
    conversionRate: Math.round(conversionRate * 10) / 10,
    conversionTrend: Math.random() > 0.5 ? Math.round(Math.random() * 2 * 10) / 10 : -Math.round(Math.random() * 1 * 10) / 10,
    expectedOrders,
    expectedRevenue,
    optimizationScore: Math.min(optimizationScore, 100)
  }
}

const refreshAnalysis = async () => {
  isAnalyzing.value = true
  
  // 模拟分析过程
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  calculateMetrics()
  isAnalyzing.value = false
}

const applyRecommendation = (recommendation) => {
  emit('apply-recommendation', recommendation)
}

const startABTest = (test) => {
  emit('start-ab-test', test)
}

// 监听表单数据变化
watch(() => props.formData, () => {
  calculateMetrics()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  calculateMetrics()
})
</script>

<style scoped>
.conversion-dashboard {
  @apply relative;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 渐变动画 */
.gradient-animate {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>