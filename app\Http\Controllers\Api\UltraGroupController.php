<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Services\EnhancedLocationService;
use App\Services\UltraAntiBlockService;
use App\Services\VirtualDataGenerator;
use App\Services\GroupTemplateService;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 超级群组控制器
 * 整合定位、防红、虚拟数据等所有高级功能
 * 
 * <AUTHOR> Enhancement
 * @date 2024-12-19
 */
class UltraGroupController extends Controller
{
    protected $locationService;
    protected $antiBlockService;
    protected $virtualDataGenerator;
    protected $templateService;
    protected $paymentService;
    
    public function __construct(
        EnhancedLocationService $locationService,
        UltraAntiBlockService $antiBlockService,
        VirtualDataGenerator $virtualDataGenerator,
        GroupTemplateService $templateService,
        PaymentService $paymentService
    ) {
        $this->locationService = $locationService;
        $this->antiBlockService = $antiBlockService;
        $this->virtualDataGenerator = $virtualDataGenerator;
        $this->templateService = $templateService;
        $this->paymentService = $paymentService;
    }
    
    /**
     * 创建超级群组（整合所有增强功能）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function createSuperGroup(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:200',
            'subtitle' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'member_limit' => 'required|integer|min:1|max:500',
            
            // 防红设置
            'anti_block_level' => 'nullable|in:low,medium,high,ultra',
            'wechat_detection' => 'nullable|boolean',
            
            // 虚拟数据设置
            'avatar_library' => 'nullable|in:qq,za,business,young',
            'virtual_member_count' => 'nullable|integer|min:5|max:50',
            
            // 地理位置设置
            'use_geo_targeting' => 'nullable|boolean'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            $user = Auth::user();
            $data = $request->all();
            
            // 1. 智能处理地理位置占位符（兼容ThinkPHP的xxx）
            $this->processGeoTargeting($data);
            
            // 2. 生成虚拟数据
            $virtualData = $this->generateVirtualData($request);
            $data['virtual_members'] = json_encode($virtualData['members']);
            $data['virtual_comments'] = json_encode($virtualData['comments']);
            $data['virtual_stats'] = json_encode($virtualData['stats']);
            
            // 3. 配置防红系统
            $antiBlockConfig = $this->configureAntiBlock($request);
            $data['anti_block_config'] = json_encode($antiBlockConfig);
            
            // 4. 设置基础信息
            $data['user_id'] = $user->id;
            $data['substation_id'] = $user->substation_id ?? 1;
            $data['status'] = 1; // 默认激活
            
            // 5. 创建群组
            $group = WechatGroup::create($data);
            
            // 6. 生成多层防红链接
            $links = $this->generateSecureLinks($group, $antiBlockConfig);
            
            // 7. 更新群组链接信息
            $group->update([
                'short_link' => $links['short'] ?? null,
                'secure_link' => $links['secure'] ?? null,
                'primary_domain' => $links['domain'] ?? null
            ]);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => '超级群组创建成功',
                'data' => [
                    'group' => $group,
                    'links' => $links,
                    'features' => [
                        'geo_targeting' => $data['use_geo_targeting'] ?? false,
                        'anti_block' => $antiBlockConfig['level'],
                        'virtual_data' => true
                    ]
                ]
            ], 201);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create super group', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 处理地理位置定位
     */
    private function processGeoTargeting(&$data)
    {
        // 检测标题中是否包含xxx（兼容ThinkPHP）
        if (strpos($data['title'], 'xxx') !== false) {
            $data['use_geo_targeting'] = true;
            $data['original_title'] = $data['title'];
            // 保留xxx占位符，在落地页动态替换
        }
        
        // 检测其他占位符
        $placeholders = ['{city}', '{省份}', '{地区}', '[位置]'];
        foreach ($placeholders as $placeholder) {
            if (strpos($data['title'], $placeholder) !== false) {
                $data['use_geo_targeting'] = true;
                $data['original_title'] = $data['title'];
                break;
            }
        }
    }
    
    /**
     * 生成虚拟数据
     */
    private function generateVirtualData(Request $request): array
    {
        $avatarLibrary = $request->input('avatar_library', 'qq');
        $memberCount = $request->input('virtual_member_count', 13);
        
        // 生成虚拟成员
        $members = $this->virtualDataGenerator->generateMembers($memberCount, $avatarLibrary);
        
        // 生成虚拟评论
        $comments = $this->virtualDataGenerator->generateComments(5, $avatarLibrary);
        
        // 生成虚拟统计
        $stats = $this->virtualDataGenerator->generateStats();
        
        return compact('members', 'comments', 'stats');
    }
    
    /**
     * 配置防红系统
     */
    private function configureAntiBlock(Request $request): array
    {
        $level = $request->input('anti_block_level', 'high');
        
        return [
            'level' => $level,
            'domain_strategy' => 'auto_rotate',
            'wechat_detection' => $request->input('wechat_detection', true),
            'enable_obfuscation' => in_array($level, ['high', 'ultra']),
            'enable_encryption' => $level === 'ultra',
            'enable_timestamp' => $level === 'ultra',
            'wxonoff' => $request->input('wechat_detection', true) ? 2 : 1 // 兼容ThinkPHP
        ];
    }
    
    /**
     * 生成安全链接
     */
    private function generateSecureLinks($group, $antiBlockConfig): array
    {
        $landingUrl = route('landing.group', ['id' => $group->id]);
        
        $linkResult = $this->antiBlockService->generateUltraSecureLink(
            $group->id,
            $landingUrl,
            $antiBlockConfig['level']
        );
        
        if (!$linkResult['success']) {
            Log::warning('Failed to generate secure links', [
                'group_id' => $group->id,
                'error' => $linkResult['message'] ?? 'Unknown error'
            ]);
            
            return [
                'landing' => $landingUrl,
                'short' => null,
                'secure' => null,
                'domain' => null
            ];
        }
        
        return array_merge($linkResult['links'], [
            'landing' => $landingUrl,
            'domain' => $linkResult['domain'] ?? null
        ]);
    }
    
    /**
     * 显示超级群组落地页
     * 
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showLandingPage($id)
    {
        $group = WechatGroup::findOrFail($id);
        
        // 解析虚拟数据
        $virtualMembers = json_decode($group->virtual_members ?? '[]', true);
        $virtualComments = json_decode($group->virtual_comments ?? '[]', true);
        $virtualStats = json_decode($group->virtual_stats ?? '[]', true);
        
        // 如果没有虚拟数据，临时生成
        if (empty($virtualMembers)) {
            $virtualMembers = $this->virtualDataGenerator->generateMembers(13, 'qq');
        }
        
        if (empty($virtualComments)) {
            $virtualComments = $this->virtualDataGenerator->generateComments(5, 'qq');
        }
        
        if (empty($virtualStats)) {
            $virtualStats = $this->virtualDataGenerator->generateStats();
        }
        
        return view('landing.ultra-group', compact('group', 'virtualMembers', 'virtualComments', 'virtualStats'));
    }
    
    /**
     * 显示增强版落地页
     */
    public function showUltraLandingPage($id)
    {
        return $this->showLandingPage($id);
    }
    
    /**
     * 测试位置服务
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testLocation(Request $request): JsonResponse
    {
        $ip = $request->input('ip', request()->ip());
        $content = $request->input('content', 'xxx本地群，火爆招募中！{city}最新群组');
        
        $locationData = $this->locationService->getFullLocationData($ip);
        $replacedContent = $this->locationService->smartReplace($content, $ip);
        
        return response()->json([
            'success' => true,
            'data' => [
                'original_content' => $content,
                'replaced_content' => $replacedContent,
                'location_data' => $locationData,
                'test_ip' => $ip
            ]
        ]);
    }
    
    /**
     * 测试虚拟数据生成
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testVirtualData(Request $request): JsonResponse
    {
        $style = $request->input('style', 'qq');
        $memberCount = $request->input('member_count', 13);
        $commentCount = $request->input('comment_count', 5);
        
        $members = $this->virtualDataGenerator->generateMembers($memberCount, $style);
        $comments = $this->virtualDataGenerator->generateComments($commentCount, $style);
        $stats = $this->virtualDataGenerator->generateStats();
        
        return response()->json([
            'success' => true,
            'data' => [
                'members' => $members,
                'comments' => $comments,
                'stats' => $stats,
                'settings' => [
                    'style' => $style,
                    'member_count' => $memberCount,
                    'comment_count' => $commentCount
                ]
            ]
        ]);
    }
    
    /**
     * 测试防红系统
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testAntiBlock(Request $request): JsonResponse
    {
        $groupId = $request->input('group_id', 1);
        $level = $request->input('level', 'high');
        $originalUrl = $request->input('url', route('landing.group', ['id' => $groupId]));
        
        $result = $this->antiBlockService->generateUltraSecureLink($groupId, $originalUrl, $level);
        
        // 检测微信环境
        $wechatCheck = $this->antiBlockService->handleWechatEnvironment($request);
        
        // 获取系统状态
        $systemStatus = $this->antiBlockService->getSystemStatus();
        
        return response()->json([
            'success' => true,
            'data' => [
                'link_result' => $result,
                'wechat_check' => $wechatCheck,
                'system_status' => $systemStatus,
                'test_settings' => [
                    'group_id' => $groupId,
                    'level' => $level,
                    'original_url' => $originalUrl
                ]
            ]
        ]);
    }
    
    /**
     * 批量创建群组
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchCreateGroups(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'groups' => 'required|array|min:1|max:10',
            'groups.*.title' => 'required|string|max:200',
            'groups.*.price' => 'required|numeric|min:0',
            'common_settings' => 'nullable|array'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        $results = [];
        $commonSettings = $request->input('common_settings', []);
        
        DB::beginTransaction();
        
        try {
            foreach ($request->input('groups') as $groupData) {
                $mergedData = array_merge($commonSettings, $groupData);
                $request->merge($mergedData);
                
                $response = $this->createSuperGroup($request);
                $responseData = $response->getData(true);
                
                if ($responseData['success']) {
                    $results[] = [
                        'title' => $groupData['title'],
                        'success' => true,
                        'group_id' => $responseData['data']['group']['id'] ?? null,
                        'links' => $responseData['data']['links'] ?? []
                    ];
                } else {
                    $results[] = [
                        'title' => $groupData['title'],
                        'success' => false,
                        'error' => $responseData['message'] ?? 'Unknown error'
                    ];
                }
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => '批量创建完成',
                'data' => [
                    'total' => count($results),
                    'success_count' => count(array_filter($results, fn($r) => $r['success'])),
                    'failed_count' => count(array_filter($results, fn($r) => !$r['success'])),
                    'results' => $results
                ]
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => '批量创建失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取系统状态
     * 
     * @return JsonResponse
     */
    public function getSystemStatus(): JsonResponse
    {
        $locationStatus = [
            'service' => 'EnhancedLocationService',
            'active' => true,
            'apis_available' => [
                'baidu' => true,
                'amap' => true,
                'tencent' => config('services.ip_location.tencent.key') ? true : false
            ]
        ];
        
        $antiBlockStatus = $this->antiBlockService->getSystemStatus();
        
        $virtualDataStatus = [
            'service' => 'VirtualDataGenerator',
            'active' => true,
            'avatar_libraries' => ['qq', 'za', 'business', 'young'],
            'nickname_count' => 45,
            'comment_template_count' => 25
        ];
        
        return response()->json([
            'success' => true,
            'data' => [
                'location_service' => $locationStatus,
                'anti_block_service' => $antiBlockStatus,
                'virtual_data_service' => $virtualDataStatus,
                'system_health' => 'excellent',
                'timestamp' => now()->toDateTimeString()
            ]
        ]);
    }
}