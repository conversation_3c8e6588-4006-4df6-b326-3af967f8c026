<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * 已认证用户重定向中间件
 * 如果用户已认证则重定向到主页
 */
class RedirectIfAuthenticated
{
    /**
     * 处理传入的请求
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // API请求返回JSON错误
                if ($request->expectsJson() || $request->is('api/*')) {
                    return response()->json([
                        'success' => false,
                        'message' => '已经登录',
                        'code' => 400
                    ], 400);
                }

                return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
} 