<template>
  <div class="optimized-data-screen">
    <!-- 顶部控制栏 -->
    <header class="screen-header">
      <div class="header-left">
        <div class="brand-info">
          <div class="brand-icon">📊</div>
          <div class="brand-text">
            <h1>LinkHub Pro</h1>
            <span>数据运营中心</span>
          </div>
        </div>
      </div>
      
      <div class="header-center">
        <h2 class="main-title">实时数据大屏</h2>
      </div>
      
      <div class="header-right">
        <div class="header-controls">
          <button class="control-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '进入全屏'">
            <i :class="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"></i>
          </button>
          <button class="control-btn" @click="refreshData" title="刷新数据">
            <i class="el-icon-refresh"></i>
          </button>
          <button class="control-btn" @click="diagnoseScreen" title="诊断显示问题">
            <i class="el-icon-warning"></i>
          </button>
        </div>
        <div class="time-info">
          <div class="current-time">{{ currentTime }}</div>
          <div class="update-status">
            <span class="status-dot"></span>
            实时更新
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="screen-content">
      <!-- 核心指标卡片 -->
      <section class="metrics-grid">
        <div 
          v-for="metric in coreMetrics" 
          :key="metric.key"
          class="metric-card"
        >
          <div class="metric-header">
            <div class="metric-icon" :style="{ background: metric.color }">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-trend" :class="metric.trend">
              <span>{{ metric.change }}%</span>
              <i :class="metric.trend === 'up' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatNumber(metric.value) }}</div>
            <div class="metric-label">{{ metric.label }}</div>
          </div>
          <div class="metric-chart">
            <svg viewBox="0 0 100 30" class="mini-chart">
              <polyline
                :points="generateSparkline(metric.sparkline)"
                fill="none"
                :stroke="metric.color"
                stroke-width="2"
              />
            </svg>
          </div>
        </div>
      </section>

      <!-- 图表区域 -->
      <section class="charts-grid">
        <!-- 收入趋势图 -->
        <div class="chart-card large">
          <div class="chart-header">
            <h3>收入趋势</h3>
            <div class="chart-controls">
              <button 
                v-for="period in ['今日', '本周', '本月']"
                :key="period"
                class="period-btn"
                :class="{ active: selectedPeriod === period }"
                @click="selectedPeriod = period"
              >
                {{ period }}
              </button>
            </div>
          </div>
          <div class="chart-content">
            <LineChart :data="revenueData" :options="chartOptions" height="200" />
          </div>
        </div>

        <!-- 用户分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户分布</h3>
          </div>
          <div class="chart-content">
            <DoughnutChart :data="userDistributionData" :options="doughnutOptions" height="180" />
          </div>
        </div>

        <!-- 订单状态 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单状态</h3>
          </div>
          <div class="chart-content">
            <BarChart :data="orderStatusData" :options="barOptions" height="180" />
          </div>
        </div>
      </section>
    </main>

    <!-- 底部状态栏 -->
    <footer class="screen-footer">
      <div class="footer-content">
        <div class="system-status">
          <span class="status-item">
            <i class="status-icon online"></i>
            系统正常
          </span>
          <span class="status-item">
            <i class="status-icon"></i>
            在线用户: {{ onlineUsers }}
          </span>
        </div>
        <div class="footer-info">
          <span>© 2024 LinkHub Pro - 数据更新时间: {{ lastUpdateTime }}</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import { getDashboardStats, getDashboardCharts, getRealTimeData } from '@/api/dashboard'
import { useScreenAdapter, formatters } from '@/utils/screen-adapter'
import { useScreenDiagnostics } from '@/utils/screen-diagnostics'

// 屏幕适配器
const {
  adapter,
  currentBreakpoint,
  isFullscreen,
  toggleFullscreen: adapterToggleFullscreen,
  getLayoutConfig,
  getChartConfig
} = useScreenAdapter()

// 屏幕诊断工具
const { runDiagnostics, autoFix } = useScreenDiagnostics()

// 响应式数据
const currentTime = ref('')
const lastUpdateTime = ref('')
const selectedPeriod = ref('今日')
const onlineUsers = ref(0)
const loading = ref(false)

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '今日收入',
    value: 0,
    change: 0,
    trend: 'up',
    icon: 'el-icon-money',
    color: '#3b82f6',
    sparkline: [20, 25, 30, 28, 35, 40, 38, 45]
  },
  {
    key: 'orders',
    label: '今日订单',
    value: 0,
    change: 0,
    trend: 'up',
    icon: 'el-icon-shopping-cart-2',
    color: '#10b981',
    sparkline: [15, 18, 22, 25, 28, 30, 32, 35]
  },
  {
    key: 'users',
    label: '总用户',
    value: 0,
    change: 0,
    trend: 'up',
    icon: 'el-icon-user',
    color: '#f59e0b',
    sparkline: [100, 105, 110, 115, 120, 125, 130, 135]
  },
  {
    key: 'groups',
    label: '活跃群组',
    value: 0,
    change: 0,
    trend: 'up',
    icon: 'el-icon-s-custom',
    color: '#8b5cf6',
    sparkline: [50, 52, 55, 58, 60, 62, 65, 68]
  }
])

// 图表数据
const revenueData = ref({
  labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
  datasets: [{
    label: '收入',
    data: [0, 0, 0, 0, 0, 0],
    borderColor: '#3b82f6',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    fill: true,
    tension: 0.4
  }]
})

const userDistributionData = ref({
  labels: ['新用户', '活跃用户', '沉睡用户'],
  datasets: [{
    data: [0, 0, 0],
    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b']
  }]
})

const orderStatusData = ref({
  labels: ['待支付', '已支付', '处理中', '已完成'],
  datasets: [{
    label: '订单数',
    data: [0, 0, 0, 0],
    backgroundColor: ['#f59e0b', '#10b981', '#3b82f6', '#8b5cf6']
  }]
})

// 图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    x: {
      grid: { color: 'rgba(255, 255, 255, 0.1)' },
      ticks: { color: '#e2e8f0' }
    },
    y: {
      grid: { color: 'rgba(255, 255, 255, 0.1)' },
      ticks: { color: '#e2e8f0' }
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: { color: '#e2e8f0' }
    }
  }
}

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false }
  },
  scales: {
    x: {
      grid: { display: false },
      ticks: { color: '#e2e8f0' }
    },
    y: {
      grid: { color: 'rgba(255, 255, 255, 0.1)' },
      ticks: { color: '#e2e8f0' }
    }
  }
}

// 工具方法
const formatNumber = formatters.number

const generateSparkline = (data) => {
  return data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = 30 - (value / Math.max(...data)) * 30
    return `${x},${y}`
  }).join(' ')
}

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN')
  lastUpdateTime.value = now.toLocaleString('zh-CN')
}

// 全屏切换
const toggleFullscreen = adapterToggleFullscreen

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await loadData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 诊断屏幕显示
const diagnoseScreen = async () => {
  try {
    const result = await runDiagnostics()

    if (result.summary.status === 'success') {
      ElMessage.success('屏幕显示正常，未发现问题')
    } else if (result.summary.status === 'warning') {
      ElMessage.warning(`发现 ${result.summary.warnings} 个警告，请查看控制台`)
    } else {
      ElMessage.error(`发现 ${result.summary.errors} 个错误，请查看控制台`)

      // 尝试自动修复
      const fixes = autoFix()
      if (fixes.length > 0) {
        ElMessage.info(`已自动应用 ${fixes.length} 个修复`)
      }
    }
  } catch (error) {
    console.error('诊断失败:', error)
    ElMessage.error('诊断失败')
  }
}



// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const [statsRes, chartsRes, realtimeRes] = await Promise.all([
      getDashboardStats(),
      getDashboardCharts(),
      getRealTimeData()
    ])

    if (statsRes.code === 200) {
      updateMetrics(statsRes.data)
    }
    
    if (chartsRes.code === 200) {
      updateCharts(chartsRes.data)
    }
    
    if (realtimeRes.code === 200) {
      onlineUsers.value = realtimeRes.data.online_users || 0
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败，使用模拟数据')
    loadMockData()
  } finally {
    loading.value = false
  }
}

const updateMetrics = (data) => {
  if (data.core_metrics) {
    const metrics = data.core_metrics
    coreMetrics.value[0].value = metrics.total_revenue || 0
    coreMetrics.value[1].value = metrics.total_orders || 0
    coreMetrics.value[2].value = metrics.total_users || 0
    coreMetrics.value[3].value = metrics.active_groups || 0
  }
}

const updateCharts = (data) => {
  if (data.revenue_chart) {
    revenueData.value = {
      ...revenueData.value,
      labels: data.revenue_chart.labels || revenueData.value.labels,
      datasets: [{
        ...revenueData.value.datasets[0],
        data: data.revenue_chart.data || revenueData.value.datasets[0].data
      }]
    }
  }
}

const loadMockData = () => {
  coreMetrics.value[0].value = 25680
  coreMetrics.value[1].value = 156
  coreMetrics.value[2].value = 8924
  coreMetrics.value[3].value = 342
  onlineUsers.value = 1247
}

// 生命周期
let timeInterval = null
let dataInterval = null

onMounted(async () => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  loadData()
  dataInterval = setInterval(loadData, 60000) // 每分钟更新一次

  // 延迟执行自动诊断，确保DOM完全渲染
  setTimeout(async () => {
    try {
      const result = await runDiagnostics()
      if (result.summary.errors > 0) {
        console.warn('🔧 检测到显示问题，尝试自动修复...')
        autoFix()
      }
    } catch (error) {
      console.error('自动诊断失败:', error)
    }
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) clearInterval(timeInterval)
  if (dataInterval) clearInterval(dataInterval)
})
</script>

<style scoped>
.optimized-data-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #e2e8f0;
  font-family: 'Inter', 'SF Pro Display', system-ui, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  font-size: var(--base-font-size, 16px);

  /* CSS变量默认值 */
  --metrics-columns: 4;
  --charts-columns: 2fr 1fr 1fr;
  --card-height: 350px;
  --content-padding: 24px 32px;
  --grid-gap: 20px;
  --base-font-size: 16px;
}

/* 头部区域 */
.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  height: 80px;
  box-sizing: border-box;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  font-size: 32px;
}

.brand-text h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.brand-text span {
  font-size: 12px;
  opacity: 0.7;
}

.main-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  background: linear-gradient(45deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-controls {
  display: flex;
  gap: 8px;
  margin-right: 16px;
}

.control-btn {
  width: 36px;
  height: 36px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.time-info {
  text-align: right;
}

.current-time {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.update-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.8;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 主要内容区域 */
.screen-content {
  flex: 1;
  padding: var(--content-padding);
  display: flex;
  flex-direction: column;
  gap: var(--grid-gap);
  min-height: 0;
  overflow-y: auto;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(var(--metrics-columns), 1fr);
  gap: var(--grid-gap);
  flex-shrink: 0;
}

.metric-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
}

.metric-trend.up {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.metric-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.7;
}

.mini-chart {
  width: 100%;
  height: 30px;
  margin-top: 8px;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: var(--charts-columns);
  gap: var(--grid-gap);
  flex: 1;
  min-height: 0;
}

.chart-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: var(--card-height);
}

.chart-card.large {
  grid-column: span 1;
}

.chart-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 4px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn.active,
.period-btn:hover {
  background: #3b82f6;
  border-color: #3b82f6;
}

.chart-content {
  flex: 1;
  padding: 16px 20px;
  min-height: 0;
}

/* 底部状态栏 */
.screen-footer {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 32px;
  flex-shrink: 0;
  height: 50px;
  box-sizing: border-box;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.system-status {
  display: flex;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
}

.status-icon.online {
  background: #10b981;
  animation: pulse 2s infinite;
}

.footer-info {
  font-size: 12px;
  opacity: 0.7;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .screen-header {
    padding: 12px 16px;
    height: 70px;
  }
  
  .main-title {
    font-size: 18px;
  }
  
  .screen-content {
    padding: 16px;
    gap: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .metric-card {
    padding: 16px;
  }
  
  .screen-footer {
    padding: 8px 16px;
    height: 40px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
