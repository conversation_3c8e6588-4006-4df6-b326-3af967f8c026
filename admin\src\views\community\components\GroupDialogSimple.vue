<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑群组' : '创建群组'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="simple-group-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="simple-form"
      >
        <!-- 基础信息 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>基础信息</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入群组名称"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组分类" prop="category">
                <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%">
                  <el-option label="创业交流" value="startup" />
                  <el-option label="投资理财" value="finance" />
                  <el-option label="科技互联网" value="tech" />
                  <el-option label="教育培训" value="education" />
                  <el-option label="生活服务" value="life" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="入群价格" prop="price">
                <el-input-number
                  v-model="formData.price"
                  :min="0"
                  :max="9999"
                  :precision="2"
                  style="width: 100%"
                />
                <div class="form-tip">设置为0表示免费群组</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大成员数" prop="max_members">
                <el-input-number
                  v-model="formData.max_members"
                  :min="10"
                  :max="10000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="群组描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入群组描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-card>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref(null)
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 是否为编辑模式
const isEdit = computed(() => props.groupData && props.groupData.id)

// 表单数据
const formData = reactive({
  name: '',
  category: '',
  price: 0,
  max_members: 500,
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择群组分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入入群价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: '请输入最大成员数', trigger: 'blur' },
    { type: 'number', min: 10, max: 10000, message: '成员数在10-10000之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入群组描述', trigger: 'blur' },
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ]
}

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    name: '',
    category: '',
    price: 0,
    max_members: 500,
    description: ''
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '群组更新成功' : '群组创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.groupData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      name: newData.name || '',
      category: newData.category || '',
      price: newData.price || 0,
      max_members: newData.max_members || 500,
      description: newData.description || ''
    })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.simple-group-dialog {
  .dialog-content {
    padding: 0;
  }
  
  .form-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .simple-group-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }
}
</style>
