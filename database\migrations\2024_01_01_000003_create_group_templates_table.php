<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 群组模板表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('group_templates', function (Blueprint $table) {
            $table->id();
            
            // 基础信息
            $table->string('name', 100)->comment('模板名称');
            $table->string('category', 50)->comment('适用分类');
            $table->text('description')->nullable()->comment('模板描述');
            $table->string('preview_image', 500)->nullable()->comment('预览图片');
            
            // 模板数据 (JSON)
            $table->json('template_data')->comment('模板数据');
            
            // 使用统计
            $table->integer('use_count')->default(0)->comment('使用次数');
            $table->decimal('rating', 3, 2)->default(0)->comment('评分');
            $table->integer('rating_count')->default(0)->comment('评分次数');
            
            // 状态和权限
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->enum('type', ['system', 'user'])->default('system')->comment('类型');
            $table->boolean('is_public')->default(true)->comment('是否公开');
            
            // 创建者
            $table->unsignedBigInteger('creator_id')->nullable()->comment('创建者ID');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('set null');
            
            // 排序
            $table->integer('sort_order')->default(0)->comment('排序权重');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index(['category', 'status']);
            $table->index(['type', 'is_public']);
            $table->index('sort_order');
            $table->index('use_count');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('group_templates');
    }
};