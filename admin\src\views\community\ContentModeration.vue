<template>
  <div class="app-container">
    <PageLayout>
      <template #header>
        <div class="page-header">
          <h1>内容审核与管理</h1>
          <p>审核用户在社群中发布的内容，确保社区环境健康、合规。</p>
        </div>
      </template>

      <div class="toolbar-container">
        <div class="toolbar-left">
           <el-dropdown @command="handleBatchCommand" v-if="selectedContent.length > 0">
              <el-button type="primary">
                批量操作 ({{ selectedContent.length }})<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="approve">批量批准</el-dropdown-item>
                  <el-dropdown-item command="reject">批量拒绝</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
        </div>
        <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索内容、作者"
              style="width: 250px; margin-right: 10px;"
              clearable
              @keyup.enter="fetchContent"
            />
            <el-select v-model="filterOptions.contentType" placeholder="内容类型" clearable style="width: 120px; margin-right: 10px;">
                <el-option label="全部类型" value=""></el-option>
                <el-option label="帖子" value="post"></el-option>
                <el-option label="评论" value="comment"></el-option>
            </el-select>
            <el-button type="primary" @click="fetchContent">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
        </div>
      </div>

      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="content-tabs">
        <el-tab-pane :label="`待审核 (${counts.pending})`" name="pending"></el-tab-pane>
        <el-tab-pane :label="`已批准 (${counts.approved})`" name="approved"></el-tab-pane>
        <el-tab-pane :label="`已拒绝 (${counts.rejected})`" name="rejected"></el-tab-pane>
      </el-tabs>

      <div class="content-list" v-loading="loading">
        <el-table
            :data="contentList"
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55" />
            <el-table-column label="内容" min-width="300">
                <template #default="{ row }">
                    <div class="content-cell">
                        <div class="content-text">{{ row.content }}</div>
                        <div class="content-meta">
                            <span><el-icon><User /></el-icon> {{ row.author.name }}</span>
                            <span>群组: {{ row.group.name }}</span>
                            <span>时间: {{ formatDate(row.created_at) }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="类型" width="100">
                <template #default="{ row }">
                    <el-tag size="small">{{ row.type === 'post' ? '帖子' : '评论' }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
                 <template #default="{ row }">
                    <el-tag :type="statusMap[row.status].type">{{ statusMap[row.status].text }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                    <div v-if="row.status === 'pending'">
                        <el-button link type="primary" size="small" @click="handleApprove(row)">批准</el-button>
                        <el-button link type="danger" size="small" @click="handleReject(row)">拒绝</el-button>
                    </div>
                    <el-button link type="info" size="small" @click="handleViewDetail(row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>

         <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="fetchContent"
              @current-change="fetchContent"
            />
        </div>
      </div>

    </PageLayout>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, User, ArrowDown } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import { formatDate } from '@/utils/format'

const loading = ref(false)
const activeTab = ref('pending')
const searchKeyword = ref('')
const filterOptions = reactive({ contentType: '' })
const contentList = ref([])
const selectedContent = ref([])

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0,
})

const counts = reactive({
  pending: 0,
  approved: 0,
  rejected: 0,
})

const statusMap = {
  pending: { text: '待审核', type: 'warning' },
  approved: { text: '已批准', type: 'success' },
  rejected: { text: '已拒绝', type: 'danger' },
}

const mockData = {
  pending: Array.from({ length: 5 }).map((_, i) => ({ id: 100 + i, content: `这是第${i+1}条待审核的内容，请管理员尽快处理。`, author: { id: 201, name: '用户A' }, group: { id: 301, name: '技术交流群' }, type: 'comment', status: 'pending', created_at: new Date().toISOString() })),
  approved: Array.from({ length: 20 }).map((_, i) => ({ id: 200 + i, content: `这是第${i+1}条已批准的内容。`, author: { id: 202, name: '用户B' }, group: { id: 302, name: '产品设计圈' }, type: 'post', status: 'approved', created_at: new Date().toISOString() })),
  rejected: Array.from({ length: 8 }).map((_, i) => ({ id: 300 + i, content: `这是第${i+1}条已拒绝的内容，包含不当言论。`, author: { id: 203, name: '用户C' }, group: { id: 301, name: '技术交流群' }, type: 'comment', status: 'rejected', created_at: new Date().toISOString() })),
}

const fetchContent = () => {
  loading.value = true
  setTimeout(() => {
    const sourceData = mockData[activeTab.value]
    pagination.total = sourceData.length
    counts.pending = mockData.pending.length
    counts.approved = mockData.approved.length
    counts.rejected = mockData.rejected.length
    
    const start = (pagination.page - 1) * pagination.limit
    const end = start + pagination.limit
    contentList.value = sourceData.slice(start, end)
    loading.value = false
  }, 500)
}

const handleTabClick = () => {
  pagination.page = 1
  fetchContent()
}

const handleSelectionChange = (selection) => {
  selectedContent.value = selection
}

const handleApprove = (item) => {
  ElMessage.success(`内容(ID: ${item.id}) 已批准`)
  fetchContent()
}

const handleReject = (item) => {
   ElMessageBox.prompt('请输入拒绝原因（可选）', '确认拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(({ value }) => {
      ElMessage.warning(`内容(ID: ${item.id}) 已拒绝，原因: ${value || '无'}`)
      fetchContent()
    }).catch(() => {})
}

const handleViewDetail = (item) => {
    ElMessage.info(`查看内容(ID: ${item.id})的详情...`)
}

const handleBatchCommand = (command) => {
    const ids = selectedContent.value.map(item => item.id)
    if (command === 'approve') {
        ElMessage.success(`已批量批准 ${ids.length} 条内容`)
    } else if (command === 'reject') {
        ElMessage.warning(`已批量拒绝 ${ids.length} 条内容`)
    }
    fetchContent()
    selectedContent.value = []
}

onMounted(() => {
  fetchContent()
})
</script>

<style lang="scss" scoped>
.page-header {
  h1 { 
    display: flex; 
    align-items: center; 
    gap: 8px; 
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
  }
  p { 
    color: #64748b; 
    font-size: 14px; 
    margin-top: 0; 
  }
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-tabs {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  padding: 0 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-list {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-cell {
  .content-text {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.5;
    max-width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .content-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #909399;
    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8fafc;
}

:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

:deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}
</style>