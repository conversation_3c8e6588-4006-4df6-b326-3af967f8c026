<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * 群组分析数据模型
 */
class GroupAnalytics extends Model
{
    use HasFactory;

    protected $table = 'group_analytics';

    protected $fillable = [
        'group_id',
        'date',
        'period_type',
        'page_views',
        'unique_visitors',
        'new_visitors',
        'returning_visitors',
        'bounce_rate',
        'avg_session_duration',
        'conversion_views',
        'conversion_clicks',
        'conversion_orders',
        'conversion_rate',
        'conversion_revenue',
        'new_members',
        'active_members',
        'total_members',
        'member_growth_rate',
        'new_orders',
        'paid_orders',
        'cancelled_orders',
        'order_revenue',
        'avg_order_value',
        'traffic_sources',
        'referrer_domains',
        'user_agents',
        'geo_locations',
        'device_types',
        'browser_types',
        'os_types'
    ];

    protected $casts = [
        'date' => 'date',
        'page_views' => 'integer',
        'unique_visitors' => 'integer',
        'new_visitors' => 'integer',
        'returning_visitors' => 'integer',
        'bounce_rate' => 'decimal:2',
        'avg_session_duration' => 'integer',
        'conversion_views' => 'integer',
        'conversion_clicks' => 'integer',
        'conversion_orders' => 'integer',
        'conversion_rate' => 'decimal:2',
        'conversion_revenue' => 'decimal:2',
        'new_members' => 'integer',
        'active_members' => 'integer',
        'total_members' => 'integer',
        'member_growth_rate' => 'decimal:2',
        'new_orders' => 'integer',
        'paid_orders' => 'integer',
        'cancelled_orders' => 'integer',
        'order_revenue' => 'decimal:2',
        'avg_order_value' => 'decimal:2',
        'traffic_sources' => 'array',
        'referrer_domains' => 'array',
        'user_agents' => 'array',
        'geo_locations' => 'array',
        'device_types' => 'array',
        'browser_types' => 'array',
        'os_types' => 'array'
    ];

    // 关联关系

    /**
     * 关联群组
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(WechatGroup::class, 'group_id');
    }

    // 查询作用域

    /**
     * 按群组筛选作用域
     */
    public function scopeByGroup(Builder $query, int $groupId): Builder
    {
        return $query->where('group_id', $groupId);
    }

    /**
     * 按日期范围筛选作用域
     */
    public function scopeByDateRange(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 按周期类型筛选作用域
     */
    public function scopeByPeriod(Builder $query, string $periodType): Builder
    {
        return $query->where('period_type', $periodType);
    }

    /**
     * 今日数据作用域
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->where('date', today());
    }

    /**
     * 昨日数据作用域
     */
    public function scopeYesterday(Builder $query): Builder
    {
        return $query->where('date', today()->subDay());
    }

    /**
     * 本周数据作用域
     */
    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * 本月数据作用域
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereBetween('date', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    // 访问器

    /**
     * 获取周期类型名称
     */
    public function getPeriodTypeNameAttribute(): string
    {
        $types = [
            'hour' => '小时',
            'day' => '天',
            'week' => '周',
            'month' => '月'
        ];

        return $types[$this->period_type] ?? $this->period_type;
    }

    /**
     * 获取格式化的平均会话时长
     */
    public function getFormattedSessionDurationAttribute(): string
    {
        $seconds = $this->avg_session_duration;
        
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            return floor($seconds / 60) . '分' . ($seconds % 60) . '秒';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . '小时' . $minutes . '分钟';
        }
    }

    /**
     * 获取主要流量来源
     */
    public function getTopTrafficSourceAttribute(): ?string
    {
        $sources = $this->traffic_sources ?? [];
        
        if (empty($sources)) {
            return null;
        }

        $maxCount = 0;
        $topSource = null;

        foreach ($sources as $source => $count) {
            if ($count > $maxCount) {
                $maxCount = $count;
                $topSource = $source;
            }
        }

        return $topSource;
    }

    /**
     * 获取主要设备类型
     */
    public function getTopDeviceTypeAttribute(): ?string
    {
        $devices = $this->device_types ?? [];
        
        if (empty($devices)) {
            return null;
        }

        $maxCount = 0;
        $topDevice = null;

        foreach ($devices as $device => $count) {
            if ($count > $maxCount) {
                $maxCount = $count;
                $topDevice = $device;
            }
        }

        return $topDevice;
    }

    // 静态方法

    /**
     * 创建或更新分析数据
     */
    public static function createOrUpdate(int $groupId, Carbon $date, string $periodType, array $data): self
    {
        return static::updateOrCreate(
            [
                'group_id' => $groupId,
                'date' => $date,
                'period_type' => $periodType
            ],
            $data
        );
    }

    /**
     * 获取群组的分析摘要
     */
    public static function getGroupSummary(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = static::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->byPeriod('day')
            ->get();

        if ($analytics->isEmpty()) {
            return static::getEmptySummary();
        }

        return [
            'total_page_views' => $analytics->sum('page_views'),
            'total_unique_visitors' => $analytics->sum('unique_visitors'),
            'total_new_visitors' => $analytics->sum('new_visitors'),
            'avg_bounce_rate' => $analytics->avg('bounce_rate'),
            'avg_session_duration' => $analytics->avg('avg_session_duration'),
            'total_conversion_orders' => $analytics->sum('conversion_orders'),
            'total_conversion_revenue' => $analytics->sum('conversion_revenue'),
            'avg_conversion_rate' => $analytics->avg('conversion_rate'),
            'total_new_members' => $analytics->sum('new_members'),
            'total_new_orders' => $analytics->sum('new_orders'),
            'total_paid_orders' => $analytics->sum('paid_orders'),
            'total_order_revenue' => $analytics->sum('order_revenue'),
            'avg_order_value' => $analytics->avg('avg_order_value'),
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'days' => $startDate->diffInDays($endDate) + 1
            ]
        ];
    }

    /**
     * 获取空的摘要数据
     */
    private static function getEmptySummary(): array
    {
        return [
            'total_page_views' => 0,
            'total_unique_visitors' => 0,
            'total_new_visitors' => 0,
            'avg_bounce_rate' => 0,
            'avg_session_duration' => 0,
            'total_conversion_orders' => 0,
            'total_conversion_revenue' => 0,
            'avg_conversion_rate' => 0,
            'total_new_members' => 0,
            'total_new_orders' => 0,
            'total_paid_orders' => 0,
            'total_order_revenue' => 0,
            'avg_order_value' => 0
        ];
    }

    /**
     * 获取趋势数据
     */
    public static function getTrendData(int $groupId, Carbon $startDate, Carbon $endDate, string $metric): array
    {
        $analytics = static::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->byPeriod('day')
            ->orderBy('date')
            ->get();

        $trendData = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dayData = $analytics->where('date', $currentDate->format('Y-m-d'))->first();
            
            $trendData[] = [
                'date' => $currentDate->format('Y-m-d'),
                'value' => $dayData ? $dayData->{$metric} : 0
            ];

            $currentDate->addDay();
        }

        return $trendData;
    }

    /**
     * 获取对比数据
     */
    public static function getComparisonData(int $groupId, Carbon $currentStart, Carbon $currentEnd, Carbon $previousStart, Carbon $previousEnd): array
    {
        $currentData = static::getGroupSummary($groupId, $currentStart, $currentEnd);
        $previousData = static::getGroupSummary($groupId, $previousStart, $previousEnd);

        $comparison = [];
        $metrics = [
            'total_page_views',
            'total_unique_visitors',
            'total_conversion_orders',
            'total_conversion_revenue',
            'avg_conversion_rate',
            'total_new_members',
            'total_paid_orders',
            'total_order_revenue'
        ];

        foreach ($metrics as $metric) {
            $current = $currentData[$metric] ?? 0;
            $previous = $previousData[$metric] ?? 0;
            
            $change = $current - $previous;
            $changePercent = $previous > 0 ? round(($change / $previous) * 100, 2) : 0;

            $comparison[$metric] = [
                'current' => $current,
                'previous' => $previous,
                'change' => $change,
                'change_percent' => $changePercent,
                'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable')
            ];
        }

        return $comparison;
    }

    /**
     * 记录页面访问
     */
    public static function recordPageView(int $groupId, array $visitorData = []): void
    {
        $today = today();
        
        $analytics = static::firstOrCreate([
            'group_id' => $groupId,
            'date' => $today,
            'period_type' => 'day'
        ]);

        $analytics->increment('page_views');
        
        // 如果是新访客
        if ($visitorData['is_new'] ?? false) {
            $analytics->increment('new_visitors');
        } else {
            $analytics->increment('returning_visitors');
        }

        $analytics->increment('unique_visitors');
        
        // 更新流量来源
        if (!empty($visitorData['source'])) {
            static::updateTrafficSource($analytics, $visitorData['source']);
        }

        // 更新设备信息
        if (!empty($visitorData['device'])) {
            static::updateDeviceInfo($analytics, $visitorData['device']);
        }
    }

    /**
     * 记录转化事件
     */
    public static function recordConversion(int $groupId, string $type, float $value = 0): void
    {
        $today = today();
        
        $analytics = static::firstOrCreate([
            'group_id' => $groupId,
            'date' => $today,
            'period_type' => 'day'
        ]);

        switch ($type) {
            case 'view':
                $analytics->increment('conversion_views');
                break;
            case 'click':
                $analytics->increment('conversion_clicks');
                break;
            case 'order':
                $analytics->increment('conversion_orders');
                $analytics->increment('conversion_revenue', $value);
                break;
        }

        // 重新计算转化率
        static::recalculateConversionRate($analytics);
    }

    /**
     * 更新流量来源
     */
    private static function updateTrafficSource(self $analytics, string $source): void
    {
        $sources = $analytics->traffic_sources ?? [];
        $sources[$source] = ($sources[$source] ?? 0) + 1;
        $analytics->update(['traffic_sources' => $sources]);
    }

    /**
     * 更新设备信息
     */
    private static function updateDeviceInfo(self $analytics, array $deviceInfo): void
    {
        $updates = [];

        if (!empty($deviceInfo['type'])) {
            $deviceTypes = $analytics->device_types ?? [];
            $deviceTypes[$deviceInfo['type']] = ($deviceTypes[$deviceInfo['type']] ?? 0) + 1;
            $updates['device_types'] = $deviceTypes;
        }

        if (!empty($deviceInfo['browser'])) {
            $browserTypes = $analytics->browser_types ?? [];
            $browserTypes[$deviceInfo['browser']] = ($browserTypes[$deviceInfo['browser']] ?? 0) + 1;
            $updates['browser_types'] = $browserTypes;
        }

        if (!empty($deviceInfo['os'])) {
            $osTypes = $analytics->os_types ?? [];
            $osTypes[$deviceInfo['os']] = ($osTypes[$deviceInfo['os']] ?? 0) + 1;
            $updates['os_types'] = $osTypes;
        }

        if (!empty($updates)) {
            $analytics->update($updates);
        }
    }

    /**
     * 重新计算转化率
     */
    private static function recalculateConversionRate(self $analytics): void
    {
        if ($analytics->conversion_views > 0) {
            $rate = ($analytics->conversion_orders / $analytics->conversion_views) * 100;
            $analytics->update(['conversion_rate' => round($rate, 2)]);
        }
    }
}