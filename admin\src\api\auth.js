import request from '@/utils/request'
import { ElMessage } from 'element-plus'

// 用户认证相关API
export const login = (data) => {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  })
}

export const logout = () => {
  return request({
    url: '/admin/auth/logout',
    method: 'post'
  })
}

export const refreshToken = () => {
  return request({
    url: '/admin/auth/refresh',
    method: 'post'
  })
}

export const getInfo = () => {
  return request({
    url: '/admin/auth/user',
    method: 'get'
  })
}

export const getUserInfo = () => {
  return request({
    url: '/admin/auth/user',
    method: 'get'
  })
}

export const updateProfile = (data) => {
  return request({
    url: '/admin/auth/profile',
    method: 'put',
    data
  })
}

export const changePassword = (data) => {
  return request({
    url: '/admin/auth/password',
    method: 'put',
    data
  })
}

// 权限管理
export const getUserPermissions = () => {
  return request({
    url: '/admin/auth/permissions',
    method: 'get'
  })
}

export const checkPermission = (permission) => {
  return request({
    url: '/admin/auth/check-permission',
    method: 'post',
    data: { permission }
  })
}

// 安全设置
export const getSecuritySettings = () => {
  return request({
    url: '/admin/auth/security-settings',
    method: 'get'
  })
}

export const updateSecuritySettings = (data) => {
  return request({
    url: '/admin/auth/security-settings',
    method: 'put',
    data
  })
}

export const enableTwoFactor = (data) => {
  return request({
    url: '/admin/auth/2fa/enable',
    method: 'post',
    data
  })
}

export const disableTwoFactor = (data) => {
  return request({
    url: '/admin/auth/2fa/disable',
    method: 'post',
    data
  })
}

export const verifyTwoFactor = (data) => {
  return request({
    url: '/admin/auth/2fa/verify',
    method: 'post',
    data
  })
}

// 登录日志
export const getLoginLogs = (params) => {
  return request({
    url: '/admin/auth/login-logs',
    method: 'get',
    params
  })
}

export const getSecurityLogs = (params) => {
  return request({
    url: '/admin/auth/security-logs',
    method: 'get',
    params
  })
}

// 会话管理
export const getActiveSessions = () => {
  return request({
    url: '/admin/auth/sessions',
    method: 'get'
  })
}

export const terminateSession = (sessionId) => {
  return request({
    url: `/admin/auth/sessions/${sessionId}`,
    method: 'delete'
  })
}

export const terminateAllSessions = () => {
  return request({
    url: '/admin/auth/sessions/all',
    method: 'delete'
  })
}

// API密钥管理
export const getApiKeys = () => {
  return request({
    url: '/admin/auth/api-keys',
    method: 'get'
  })
}

export const createApiKey = (data) => {
  return request({
    url: '/admin/auth/api-keys',
    method: 'post',
    data
  })
}

export const updateApiKey = (keyId, data) => {
  return request({
    url: `/admin/auth/api-keys/${keyId}`,
    method: 'put',
    data
  })
}

export const deleteApiKey = (keyId) => {
  return request({
    url: `/admin/auth/api-keys/${keyId}`,
    method: 'delete'
  })
}

// 密码重置
export const sendResetEmail = (email) => {
  return request({
    url: '/admin/auth/password/reset-email',
    method: 'post',
    data: { email }
  })
}

export const resetPassword = (data) => {
  return request({
    url: '/admin/auth/password/reset',
    method: 'post',
    data
  })
}

// 账户锁定/解锁
export const lockAccount = (userId, reason) => {
  return request({
    url: `/admin/auth/accounts/${userId}/lock`,
    method: 'post',
    data: { reason }
  })
}

export const unlockAccount = (userId) => {
  return request({
    url: `/admin/auth/accounts/${userId}/unlock`,
    method: 'post'
  })
}

// 登录限制
export const getLoginRestrictions = () => {
  return request({
    url: '/admin/auth/login-restrictions',
    method: 'get'
  })
}

export const updateLoginRestrictions = (data) => {
  return request({
    url: '/admin/auth/login-restrictions',
    method: 'put',
    data
  })
}

// 设备管理
export const getTrustedDevices = () => {
  return request({
    url: '/admin/auth/trusted-devices',
    method: 'get'
  })
}

export const addTrustedDevice = (data) => {
  return request({
    url: '/admin/auth/trusted-devices',
    method: 'post',
    data
  })
}

export const removeTrustedDevice = (deviceId) => {
  return request({
    url: `/admin/auth/trusted-devices/${deviceId}`,
    method: 'delete'
  })
}

// 认证中间件
export const authMiddleware = {
  // 请求拦截器
  requestInterceptor: (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },

  // 响应拦截器
  responseInterceptor: (response) => {
    return response
  },

  // 错误处理
  errorHandler: (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_user')
          window.location.href = '/login'
          ElMessage.error('登录已过期，请重新登录')
          break
        case 403:
          ElMessage.error('权限不足，无法访问该资源')
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
}

// 权限检查工具
export const hasPermission = (permission) => {
  const userPermissions = JSON.parse(localStorage.getItem('admin_permissions') || '[]')
  return userPermissions.includes(permission) || userPermissions.includes('*')
}

export const hasAnyPermission = (permissions) => {
  return permissions.some(permission => hasPermission(permission))
}

export const hasAllPermissions = (permissions) => {
  return permissions.every(permission => hasPermission(permission))
}

// 角色检查工具
export const hasRole = (role) => {
  const userRoles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  return userRoles.includes(role) || userRoles.includes('super_admin')
}

export const hasAnyRole = (roles) => {
  return roles.some(role => hasRole(role))
}

// 安全工具
export const generateSecurePassword = (length = 12) => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  return password
}

export const validatePasswordStrength = (password) => {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)
  
  let score = 0
  let feedback = []
  
  if (password.length >= minLength) {
    score += 1
  } else {
    feedback.push(`密码长度至少${minLength}位`)
  }
  
  if (hasUpperCase) score += 1
  else feedback.push('包含大写字母')
  
  if (hasLowerCase) score += 1
  else feedback.push('包含小写字母')
  
  if (hasNumbers) score += 1
  else feedback.push('包含数字')
  
  if (hasSpecialChar) score += 1
  else feedback.push('包含特殊字符')
  
  let strength = 'weak'
  if (score >= 4) strength = 'strong'
  else if (score >= 3) strength = 'medium'
  
  return {
    score,
    strength,
    feedback,
    isValid: score >= 3
  }
}

// 设备指纹
export const getDeviceFingerprint = () => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx.textBaseline = 'top'
  ctx.font = '14px Arial'
  ctx.fillText('Device fingerprint', 2, 2)
  
  const fingerprint = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    screen: `${screen.width}x${screen.height}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    canvas: canvas.toDataURL(),
    timestamp: Date.now()
  }
  
  return btoa(JSON.stringify(fingerprint))
}

// 导出默认配置
export default {
  authMiddleware,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  hasAnyRole,
  generateSecurePassword,
  validatePasswordStrength,
  getDeviceFingerprint
}