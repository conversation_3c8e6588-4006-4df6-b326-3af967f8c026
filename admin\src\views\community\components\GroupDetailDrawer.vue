<template>
  <el-drawer
    v-model="drawerVisible"
    title="群组详情"
    size="650px"
    :destroy-on-close="true"
    class="group-detail-drawer"
  >
    <template #header>
      <div class="drawer-header">
        <h3 class="drawer-title">群组详情</h3>
        <div class="drawer-actions">
          <el-button type="primary" size="small" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleQRCode">
            <el-icon><Picture /></el-icon>
            二维码
          </el-button>
        </div>
      </div>
    </template>
    
    <el-scrollbar>
      <div v-loading="loading" class="drawer-content">
        <!-- 标签页导航 -->
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <!-- 基本信息 -->
            <div class="detail-section">
              <div class="section-header">
                <h4 class="section-title">基本信息</h4>
              </div>
          <div class="group-basic-info">
            <div class="group-avatar">
              <el-avatar :src="groupData.avatar" :size="100">
                <el-icon><Comment /></el-icon>
              </el-avatar>
              <div class="group-status">
                <el-tag :type="getStatusTagType(groupData.status)" size="small">
                  {{ getStatusText(groupData.status) }}
                </el-tag>
              </div>
            </div>
            <div class="group-info">
              <h3 class="group-name">{{ groupData.name }}</h3>
              <div class="group-meta">
                <div class="meta-item">
                  <span class="meta-label">群组类型:</span>
                  <el-tag :type="getTypeTagType(groupData.type)" size="small">
                    {{ getTypeText(groupData.type) }}
                  </el-tag>
                </div>
                <div class="meta-item">
                  <span class="meta-label">价格:</span>
                  <span class="meta-value price">{{ groupData.price > 0 ? `¥${groupData.price}` : '免费' }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">成员数:</span>
                  <span class="meta-value">{{ groupData.member_count || 0 }}/{{ groupData.max_members || 0 }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">创建时间:</span>
                  <span class="meta-value">{{ formatDate(groupData.created_at) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="group-description">
            <div class="desc-label">群组描述:</div>
            <div class="desc-content">{{ groupData.description || '暂无描述' }}</div>
          </div>
          
          <div class="group-tags" v-if="groupData.tags && groupData.tags.length > 0">
            <div class="tags-label">标签:</div>
            <div class="tags-list">
              <el-tag
                v-for="tag in parseTags(groupData.tags)"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <!-- 群主信息 -->
        <div class="detail-section">
          <div class="section-header">
            <h4 class="section-title">群主信息</h4>
          </div>
          <div class="owner-info" v-if="groupData.owner">
            <el-avatar :src="groupData.owner.avatar" :size="50">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="owner-details">
              <div class="owner-name">{{ groupData.owner.nickname || groupData.owner.username }}</div>
              <div class="owner-meta">
                <div class="meta-item">
                  <span class="meta-label">手机:</span>
                  <span class="meta-value">{{ groupData.owner.phone || '未绑定' }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">注册时间:</span>
                  <span class="meta-value">{{ formatDate(groupData.owner.created_at) }}</span>
                </div>
              </div>
            </div>
            <div class="owner-actions">
              <el-button type="primary" link @click="viewOwner(groupData.owner.id)">
                查看详情
              </el-button>
            </div>
          </div>
          <div class="empty-info" v-else>
            <el-empty description="暂无群主信息" :image-size="60" />
          </div>
        </div>
        
        <!-- 入群须知 -->
        <div class="detail-section">
          <div class="section-header">
            <h4 class="section-title">入群须知</h4>
          </div>
          <div class="join-notice">
            <div class="notice-content">
              {{ groupData.join_notice || '暂无入群须知' }}
            </div>
          </div>
        </div>
        
        <!-- 成员统计 -->
        <div class="detail-section">
          <div class="section-header">
            <h4 class="section-title">成员统计</h4>
            <el-button type="primary" link @click="viewMembers">
              查看全部
            </el-button>
          </div>
          <div class="members-stats">
            <div class="stats-item">
              <div class="stats-value">{{ groupData.member_count || 0 }}</div>
              <div class="stats-label">总成员数</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ groupData.active_members || 0 }}</div>
              <div class="stats-label">活跃成员</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ groupData.new_members_today || 0 }}</div>
              <div class="stats-label">今日新增</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ groupData.max_members - (groupData.member_count || 0) }}</div>
              <div class="stats-label">剩余名额</div>
            </div>
          </div>
          <div class="members-progress">
            <div class="progress-header">
              <span>成员容量</span>
              <span>{{ Math.round((groupData.member_count / groupData.max_members) * 100) }}%</span>
            </div>
            <el-progress
              :percentage="(groupData.member_count / groupData.max_members) * 100"
              :stroke-width="10"
              :color="getProgressColor(groupData.member_count / groupData.max_members)"
            />
          </div>
        </div>
        
            <!-- 收入统计 -->
            <div class="detail-section">
              <div class="section-header">
                <h4 class="section-title">收入统计</h4>
                <el-button type="primary" link @click="viewOrders">
                  查看订单
                </el-button>
              </div>
              <div class="income-stats">
                <div class="stats-item">
                  <div class="stats-value">¥{{ groupData.total_income || 0 }}</div>
                  <div class="stats-label">总收入</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">¥{{ groupData.today_income || 0 }}</div>
                  <div class="stats-label">今日收入</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ groupData.total_orders || 0 }}</div>
                  <div class="stats-label">总订单数</div>
                </div>
                <div class="stats-item">
                  <div class="stats-value">{{ groupData.today_orders || 0 }}</div>
                  <div class="stats-label">今日订单</div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 内容管理标签页 -->
          <el-tab-pane label="内容管理" name="content">
            <GroupContentManager 
              :group-id="props.groupId" 
              :group-data="groupData"
              @content-updated="handleContentUpdated"
            />
          </el-tab-pane>

          <!-- 成员管理标签页 -->
          <el-tab-pane label="成员管理" name="members">
            <GroupMemberManager 
              :group-id="props.groupId"
              :group-data="groupData"
            />
          </el-tab-pane>

          <!-- 数据统计标签页 -->
          <el-tab-pane label="数据统计" name="analytics">
            <GroupAnalytics 
              :group-id="props.groupId"
              :group-data="groupData"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-scrollbar>
    
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑群组</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Picture, Comment, User } from '@element-plus/icons-vue'
import { getGroupDetail } from '@/api/community'
import GroupContentManager from './GroupContentManager.vue'
import GroupMemberManager from './GroupMemberManager.vue'
import GroupAnalytics from './GroupAnalytics.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit', 'view-members', 'view-orders', 'view-qrcode'])

// 响应式数据
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const groupData = ref({})
const activeTab = ref('basic')

// 监听群组ID变化
watch(() => props.groupId, (newVal) => {
  if (newVal && drawerVisible.value) {
    fetchGroupDetail()
  }
}, { immediate: true })

// 监听抽屉可见性
watch(() => drawerVisible.value, (newVal) => {
  if (newVal && props.groupId) {
    fetchGroupDetail()
  }
})

// 方法
const fetchGroupDetail = async () => {
  if (!props.groupId) return
  
  loading.value = true
  try {
    const { data } = await getGroupDetail(props.groupId)
    groupData.value = data
  } catch (error) {
    console.error('获取群组详情失败:', error)
    ElMessage.error('获取群组详情失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  emit('edit', groupData.value)
  drawerVisible.value = false
}

const handleQRCode = () => {
  emit('view-qrcode', groupData.value)
  drawerVisible.value = false
}

const viewOwner = (ownerId) => {
  // 跳转到用户详情页面
  ElMessage.info(`查看用户ID: ${ownerId}`)
}

const viewMembers = () => {
  emit('view-members', groupData.value)
  drawerVisible.value = false
}

const viewOrders = () => {
  emit('view-orders', groupData.value)
  drawerVisible.value = false
}

// 辅助方法
const getTypeTagType = (type) => {
  const typeMap = {
    free: '',
    paid: 'warning',
    vip: 'danger'
  }
  return typeMap[type] || ''
}

const getTypeText = (type) => {
  const typeMap = {
    free: '免费群',
    paid: '付费群',
    vip: 'VIP群'
  }
  return typeMap[type] || '未知'
}

const getStatusTagType = (status) => {
  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '已关闭',
    1: '正常',
    2: '已满'
  }
  return statusMap[status] || '未知'
}

const getProgressColor = (percentage) => {
  if (percentage < 0.5) return '#67c23a'
  if (percentage < 0.8) return '#e6a23c'
  return '#f56c6c'
}

const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString('zh-CN')
}

const parseTags = (tags) => {
  if (!tags) return []
  if (Array.isArray(tags)) return tags
  return tags.split(',').filter(tag => tag)
}

// 内容管理相关方法
const handleContentUpdated = () => {
  // 内容更新后刷新群组详情
  fetchGroupDetail()
  ElMessage.success('内容更新成功')
}
</script>

<style lang="scss" scoped>
.group-detail-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
  }
  
  :deep(.el-drawer__body) {
    padding: 0;
  }
  
  :deep(.el-drawer__footer) {
    padding: 16px 20px;
    border-top: 1px solid #e2e8f0;
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .drawer-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }
  
  .drawer-actions {
    display: flex;
    gap: 8px;
  }
}

.drawer-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}

.group-basic-info {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  
  .group-avatar {
    position: relative;
    
    .group-status {
      position: absolute;
      bottom: 0;
      right: 0;
      background: white;
      border-radius: 10px;
      padding: 2px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .group-info {
    flex: 1;
    
    .group-name {
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 12px 0;
    }
    
    .group-meta {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      
      .meta-item {
        display: flex;
        align-items: center;
        
        .meta-label {
          color: #64748b;
          margin-right: 8px;
          font-size: 14px;
        }
        
        .meta-value {
          color: #1e293b;
          font-weight: 500;
          font-size: 14px;
          
          &.price {
            color: #f59e0b;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.group-description {
  margin-bottom: 16px;
  
  .desc-label {
    color: #64748b;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .desc-content {
    color: #1e293b;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-line;
  }
}

.group-tags {
  .tags-label {
    color: #64748b;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

.owner-info {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .owner-details {
    flex: 1;
    
    .owner-name {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8px;
    }
    
    .owner-meta {
      display: flex;
      gap: 16px;
      
      .meta-item {
        display: flex;
        align-items: center;
        
        .meta-label {
          color: #64748b;
          margin-right: 4px;
          font-size: 13px;
        }
        
        .meta-value {
          color: #1e293b;
          font-size: 13px;
        }
      }
    }
  }
}

.join-notice {
  .notice-content {
    color: #1e293b;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-line;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }
}

.members-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
  
  .stats-item {
    padding: 16px;
    background: white;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .stats-value {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4px;
    }
    
    .stats-label {
      font-size: 13px;
      color: #64748b;
    }
  }
}

.members-progress {
  .progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #64748b;
  }
}

.income-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  
  .stats-item {
    padding: 16px;
    background: white;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .stats-value {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4px;
    }
    
    .stats-label {
      font-size: 13px;
      color: #64748b;
    }
  }
}

.empty-info {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .group-basic-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .group-info {
      .group-meta {
        grid-template-columns: 1fr;
        
        .meta-item {
          justify-content: center;
        }
      }
    }
  }
  
  .owner-info {
    flex-direction: column;
    text-align: center;
    
    .owner-details {
      .owner-meta {
        flex-direction: column;
        gap: 8px;
        
        .meta-item {
          justify-content: center;
        }
      }
    }
  }
  
  .members-stats,
  .income-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>