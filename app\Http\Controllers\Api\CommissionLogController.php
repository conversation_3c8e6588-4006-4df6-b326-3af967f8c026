<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CommissionLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 佣金记录控制器
 * 用于查看佣金明细、统计等
 */
class CommissionLogController extends Controller
{
    /**
     * 获取佣金记录列表
     */
    public function index(Request $request)
    {
        $query = CommissionLog::with(['user:id,username,nickname', 'order:id,order_no,amount', 'fromUser:id,username,nickname']);

        $user = Auth::user();

        // 权限：分销商只能看自己的，分站长看自己站点的，超管看全部
        if ($user->isDistributor()) {
            $query->where('user_id', $user->id);
        } elseif ($user->isSubstation()) {
            $distributorIds = $user->substation->users()->where('role', 'distributor')->pluck('id');
            $query->whereIn('user_id', $distributorIds);
        }

        // 筛选条件
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }
        
        if ($request->filled('order_no')) {
            $query->whereHas('order', function($q) use ($request) {
                $q->where('order_no', 'like', '%' . $request->order_no . '%');
            });
        }
        
        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }
        
        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        // 排序
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $logs = $query->paginate($request->input('per_page', 15));

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => $logs,
        ]);
    }

    /**
     * 获取佣金统计数据
     */
    public function stats(Request $request)
    {
        $user = Auth::user();
        $query = CommissionLog::query();

        // 权限控制
        if ($user->isDistributor()) {
            $query->where('user_id', $user->id);
        } elseif ($user->isSubstation()) {
            $distributorIds = $user->substation->users()->where('role', 'distributor')->pluck('id');
            $query->whereIn('user_id', $distributorIds);
        }

        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth());
        
        $query->whereBetween('created_at', [$startDate, $endDate]);

        // 基础统计
        $stats = [
            'total_amount' => $query->sum('amount'),
            'total_count' => $query->count(),
            'avg_amount' => $query->avg('amount'),
            'max_amount' => $query->max('amount'),
            'min_amount' => $query->min('amount'),
        ];

        // 按类型统计
        $typeStats = $query->select('type', DB::raw('SUM(amount) as total_amount'), DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->get();

        // 按日期统计（最近30天）
        $dailyStats = $query->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        // 按用户统计（TOP 10）
        $userStats = $query->select('user_id', DB::raw('SUM(amount) as total_amount'), DB::raw('COUNT(*) as count'))
            ->with('user:id,username,nickname')
            ->groupBy('user_id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'basic' => $stats,
                'by_type' => $typeStats,
                'daily' => $dailyStats,
                'top_users' => $userStats,
            ],
        ]);
    }

    /**
     * 获取佣金详情
     */
    public function show($id)
    {
        $log = CommissionLog::with(['user', 'order', 'fromUser'])->find($id);
        
        if (!$log) {
            return response()->json([
                'success' => false,
                'message' => '佣金记录不存在'
            ], 404);
        }

        $user = Auth::user();
        
        // 权限验证
        if ($user->isDistributor() && $log->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => '无权限查看此记录'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $log,
        ]);
    }

    /**
     * 手动添加佣金记录
     * 需要管理员权限
     */
    public function store(Request $request)
    {
        $this->authorize('create', CommissionLog::class);

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'order_id' => 'required|exists:orders,id',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'commission_amount' => 'required|numeric|min:0',
            'remark' => 'nullable|string|max:255',
        ]);

        $order = \App\Models\Order::find($validated['order_id']);
        $validated['order_amount'] = $order->amount;
        $validated['status'] = 2; // 手动添加的默认为待结算

        $log = CommissionLog::create($validated);

        return response()->json([
            'success' => true,
            'message' => '佣金记录添加成功',
            'data' => $log
        ], 201);
    }

    /**
     * 结算单笔佣金
     */
    public function settle(CommissionLog $log)
    {
        $this->authorize('settle', $log);

        if ($log->status != 2) {
            return response()->json(['success' => false, 'message' => '该佣金记录无法结算'], 400);
        }

        try {
            DB::transaction(function () use ($log) {
                $log->update(['status' => 1]); // 1: 已结算
                $this->addUserBalance($log->user, $log->commission_amount, 'commission', $log->id);
            });
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => '结算失败: ' . $e->getMessage()], 500);
        }

        return response()->json(['success' => true, 'message' => '佣金结算成功']);
    }

    /**
     * 批量结算佣金
     */
    public function batchSettle(Request $request)
    {
        $this->authorize('batchSettle', CommissionLog::class);
        
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:commission_logs,id',
        ]);

        $settledCount = 0;
        $errors = [];

        foreach ($validated['ids'] as $id) {
            $log = CommissionLog::find($id);
            if ($log && $log->status == 2) { // 2: 待结算
                try {
                    DB::transaction(function () use ($log) {
                        $log->update(['status' => 1]); // 1: 已结算
                        $this->addUserBalance($log->user, $log->commission_amount, 'commission', $log->id);
                    });
                    $settledCount++;
                } catch (\Exception $e) {
                    $errors[] = "ID {$id} 结算失败: " . $e->getMessage();
                }
            }
        }

        if (empty($errors)) {
            return response()->json(['success' => true, 'message' => "成功结算 {$settledCount} 笔佣金"]);
        } else {
            return response()->json([
                'success' => false, 
                'message' => "完成 {$settledCount} 笔结算，但有错误发生",
                'errors' => $errors
            ], 500);
        }
    }

    /**
     * 辅助方法：增加用户余额并记录日志
     */
    private function addUserBalance(User $user, $amount, $type, $relatedId)
    {
        $user->balance += $amount;
        $user->save();

        \App\Models\BalanceLog::create([
            'user_id' => $user->id,
            'amount' => $amount,
            'balance_after' => $user->balance,
            'type' => $type,
            'related_id' => $relatedId,
            'remark' => '佣金结算'
        ]);
    }

    /**
     * 导出佣金记录
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $query = CommissionLog::with(['user', 'order', 'fromUser']);

        // 权限控制
        if ($user->isDistributor()) {
            $query->where('user_id', $user->id);
        } elseif ($user->isSubstation()) {
            $distributorIds = $user->substation->users()->where('role', 'distributor')->pluck('id');
            $query->whereIn('user_id', $distributorIds);
        }

        // 应用筛选条件
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $logs = $query->orderBy('created_at', 'desc')->get();

        // 生成CSV数据
        $csvData = [];
        $csvData[] = ['日期', '用户', '类型', '金额', '订单号', '来源用户', '备注'];
        
        foreach ($logs as $log) {
            $csvData[] = [
                $log->created_at->format('Y-m-d H:i:s'),
                $log->user->username ?? '',
                $log->type_text,
                $log->amount,
                $log->order->order_no ?? '',
                $log->fromUser->username ?? '',
                $log->remark ?? '',
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $csvData,
            'filename' => '佣金记录_' . date('Y-m-d') . '.csv',
        ]);
    }
} 