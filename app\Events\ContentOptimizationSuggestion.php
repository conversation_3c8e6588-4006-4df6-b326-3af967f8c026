<?php

namespace App\Events;

use App\Models\WechatGroup;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 内容优化建议事件
 * 当系统生成新的优化建议时触发
 */
class ContentOptimizationSuggestion implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly WechatGroup $group,
        public readonly array $suggestions
    ) {}

    /**
     * 获取事件应该广播的频道
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->group->user_id),
            new PrivateChannel('group.' . $this->group->id),
        ];
    }

    /**
     * 广播事件名称
     */
    public function broadcastAs(): string
    {
        return 'content.optimization.suggestion';
    }

    /**
     * 广播数据
     */
    public function broadcastWith(): array
    {
        return [
            'group' => [
                'id' => $this->group->id,
                'title' => $this->group->title,
                'category' => $this->group->category,
            ],
            'suggestions' => $this->formatSuggestions($this->suggestions),
            'suggestion_summary' => $this->generateSuggestionSummary($this->suggestions),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * 格式化建议数据
     */
    private function formatSuggestions(array $suggestions): array
    {
        return array_map(function ($suggestion) {
            return [
                'type' => $suggestion['type'],
                'priority' => $suggestion['priority'],
                'title' => $suggestion['title'],
                'description' => $suggestion['description'],
                'actions' => $suggestion['actions'] ?? [],
                'estimated_impact' => $suggestion['estimated_impact'] ?? '',
                'difficulty' => $this->calculateDifficulty($suggestion),
                'time_estimate' => $this->estimateTimeRequired($suggestion),
            ];
        }, $suggestions);
    }

    /**
     * 生成建议摘要
     */
    private function generateSuggestionSummary(array $suggestions): array
    {
        $priorityCounts = [
            'high' => 0,
            'medium' => 0,
            'low' => 0,
        ];

        $typeCounts = [];

        foreach ($suggestions as $suggestion) {
            $priority = $suggestion['priority'] ?? 'medium';
            $type = $suggestion['type'] ?? 'general';
            
            if (isset($priorityCounts[$priority])) {
                $priorityCounts[$priority]++;
            }
            
            $typeCounts[$type] = ($typeCounts[$type] ?? 0) + 1;
        }

        return [
            'total_suggestions' => count($suggestions),
            'priority_breakdown' => $priorityCounts,
            'type_breakdown' => $typeCounts,
            'recommended_action' => $this->getRecommendedAction($suggestions),
            'estimated_total_impact' => $this->calculateTotalImpact($suggestions),
        ];
    }

    /**
     * 计算建议难度
     */
    private function calculateDifficulty(array $suggestion): string
    {
        $type = $suggestion['type'] ?? '';
        
        return match ($type) {
            'performance_optimization' => 'medium',
            'traffic_boost' => 'high',
            'competitive_improvement' => 'high',
            'content_quality' => 'low',
            'seo_optimization' => 'medium',
            default => 'medium',
        };
    }

    /**
     * 估算所需时间
     */
    private function estimateTimeRequired(array $suggestion): string
    {
        $difficulty = $this->calculateDifficulty($suggestion);
        $actionCount = count($suggestion['actions'] ?? []);
        
        return match ($difficulty) {
            'low' => $actionCount <= 2 ? '5-10分钟' : '10-20分钟',
            'medium' => $actionCount <= 3 ? '20-30分钟' : '30-60分钟',
            'high' => $actionCount <= 4 ? '1-2小时' : '2-4小时',
            default => '30分钟',
        };
    }

    /**
     * 获取推荐行动
     */
    private function getRecommendedAction(array $suggestions): string
    {
        $highPriority = array_filter($suggestions, fn($s) => ($s['priority'] ?? '') === 'high');
        
        if (!empty($highPriority)) {
            $firstHigh = reset($highPriority);
            return "优先处理：{$firstHigh['title']}";
        }
        
        if (!empty($suggestions)) {
            $first = reset($suggestions);
            return "建议开始：{$first['title']}";
        }
        
        return '暂无紧急建议';
    }

    /**
     * 计算总体影响
     */
    private function calculateTotalImpact(array $suggestions): string
    {
        $highCount = count(array_filter($suggestions, fn($s) => ($s['priority'] ?? '') === 'high'));
        $mediumCount = count(array_filter($suggestions, fn($s) => ($s['priority'] ?? '') === 'medium'));
        
        if ($highCount >= 2) {
            return '预期显著提升（20-40%）';
        } elseif ($highCount >= 1 || $mediumCount >= 3) {
            return '预期中等提升（10-20%）';
        } elseif ($mediumCount >= 1) {
            return '预期轻微提升（5-10%）';
        }
        
        return '预期小幅改善（<5%）';
    }
}