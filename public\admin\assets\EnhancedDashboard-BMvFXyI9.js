import{m as a,_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                     *//* empty css                        *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css               */import{T as l,aR as s,at as t,aw as c,a_ as r,ae as o,U as d,ci as u,c6 as n,be as i,ch as _,aZ as p,aY as m,bh as v,bi as f,a$ as h,bc as b,V as k,bR as y,ck as g,af as w,az as V,aT as x,aB as z,aC as j,bw as C,bx as q,b9 as S,b8 as U,bp as D,bq as A,aM as H,br as Q,ay as B,bk as L,bl as R,cl as $,Q as E,R as I}from"./element-plus-h2SQQM64.js";import{i as O}from"./echarts-D68jitv0.js";import{r as P,L as T,c as Y,e as F,k as G,l as M,t as N,E as Z,z as J,D as K,u as W,A as X,y as aa,F as ea,Y as la,B as sa,n as ta}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const ca={getDomainHealth:()=>a.get("/anti-block/domain-health"),checkDomainHealth:e=>a.post("/anti-block/check-domain",{domain:e}),getBrowserStats:(e=7)=>a.get("/anti-block/browser-stats",{params:{days:e}}),validateGroupAccess:e=>a.post(`/anti-block/validate-access/${e}`),getAccessReport:(e,l="7days")=>a.get(`/anti-block/access-report/${e}`,{params:{period:l}})},ra={class:"anti-block-dashboard"},oa={class:"page-header"},da={class:"header-content"},ua={class:"header-actions"},na={class:"stats-section"},ia={class:"stats-card primary"},_a={class:"stats-content"},pa={class:"stats-icon"},ma={class:"stats-info"},va={class:"stats-value"},fa={class:"stats-trend"},ha={class:"stats-card success"},ba={class:"stats-content"},ka={class:"stats-icon"},ya={class:"stats-info"},ga={class:"stats-value"},wa={class:"stats-trend positive"},Va={class:"stats-card warning"},xa={class:"stats-content"},za={class:"stats-icon"},ja={class:"stats-info"},Ca={class:"stats-value"},qa={class:"stats-card danger"},Sa={class:"stats-content"},Ua={class:"stats-icon"},Da={class:"stats-info"},Aa={class:"stats-value"},Ha={class:"card-header"},Qa={class:"header-actions"},Ba={class:"health-score"},La={key:0},Ra={key:1,class:"text-muted"},$a={class:"access-status"},Ea={class:"pagination-wrapper"},Ia={class:"browser-stats"},Oa={class:"browser-info"},Pa={class:"browser-name"},Ta={class:"browser-count"},Ya={key:0,class:"domain-details"},Fa={key:0,class:"mt-4"},Ga={class:"check-item"},Ma={class:"check-item"},Na={class:"check-item"},Za={class:"check-item"},Ja={class:"check-item"},Ka={class:"check-item"},Wa=e({__name:"EnhancedDashboard",setup(a){const e=P(!1),Wa=P([]),Xa=P({total:0,normal:0,abnormal:0,blocked:0,avg_health_score:0}),ae=P([]),ee=P(""),le=P(!1),se=P(!1),te=P(null),ce=P(null),re=T({page:1,size:20,total:0}),oe=T({domain:"",domain_type:"redirect",priority:5,remarks:""}),de=Y(()=>ee.value?Wa.value.filter(a=>{switch(ee.value){case"normal":return 1===a.status;case"abnormal":return 2===a.status;case"blocked":return 3===a.status;default:return!0}}):Wa.value),ue=async()=>{e.value=!0;try{const a=await ca.getDomainHealth();Wa.value=a.data.domains||[],Xa.value=a.data.stats||{},re.total=a.data.total||0}catch(a){E.error("获取域名列表失败")}finally{e.value=!1}},ne=async()=>{try{const a=await ca.checkDomainHealth();E.success(`批量检查完成，检查了 ${a.data.checked} 个域名`),ue()}catch(a){E.error("批量检查失败")}},ie=async()=>{if(oe.domain)try{E.success("域名添加成功"),le.value=!1,ue(),Object.keys(oe).forEach(a=>{oe[a]="domain_type"===a?"redirect":"priority"===a?5:""})}catch(a){E.error("域名添加失败")}else E.warning("请输入域名")},_e=async({action:a,domain:e})=>{switch(a){case"restore":try{E.success("域名已恢复"),ue()}catch(l){E.error("域名恢复失败")}break;case"block":try{E.success("域名已封禁"),ue()}catch(l){E.error("域名封禁失败")}break;case"delete":try{await I.confirm("确定要删除这个域名吗？","确认删除",{type:"warning"}),E.success("域名已删除"),ue()}catch(l){"cancel"!==l&&E.error("域名删除失败")}}},pe=a=>{switch(a){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},me=a=>a?new Date(a).toLocaleString():"-";return F(()=>{ue(),(async()=>{try{const a=await ca.getBrowserStats(7);ae.value=a.data||[]}catch(a){console.error("获取浏览器统计失败:",a)}})(),ta(()=>{ce.value&&O(ce.value).setOption({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[120,200,150,80,70,110,130],type:"line",smooth:!0,areaStyle:{opacity:.3}}]})})}),(a,I)=>{const O=l,P=t,T=r,Y=p,F=U,ta=S,Wa=f,ve=h,fe=b,he=k,be=j,ke=z,ye=V,ge=v,we=q,Ve=m,xe=H,ze=A,je=Q,Ce=D,qe=B,Se=R,Ue=L,De=C;return M(),G("div",ra,[N("div",oa,[N("div",da,[I[13]||(I[13]=N("div",{class:"header-left"},[N("h1",{class:"page-title"},"防封系统管理"),N("p",{class:"page-subtitle"},"监控和管理域名健康状态、访问统计和防封策略")],-1)),N("div",ua,[Z(P,{type:"primary",onClick:ne},{default:J(()=>[Z(O,null,{default:J(()=>[Z(W(s))]),_:1}),I[11]||(I[11]=K(" 检查所有域名 ",-1))]),_:1,__:[11]}),Z(P,{onClick:I[0]||(I[0]=a=>le.value=!0)},{default:J(()=>[Z(O,null,{default:J(()=>[Z(W(c))]),_:1}),I[12]||(I[12]=K(" 添加域名 ",-1))]),_:1,__:[12]})])])]),N("div",na,[Z(Y,{gutter:24},{default:J(()=>[Z(T,{span:6},{default:J(()=>[N("div",ia,[N("div",_a,[N("div",pa,[Z(O,null,{default:J(()=>[Z(W(o))]),_:1})]),N("div",ma,[N("div",va,d(Xa.value.total),1),I[14]||(I[14]=N("div",{class:"stats-label"},"总域名数",-1))])]),N("div",fa,[N("span",null,"健康域名: "+d(Xa.value.normal),1)])])]),_:1}),Z(T,{span:6},{default:J(()=>[N("div",ha,[N("div",ba,[N("div",ka,[Z(O,null,{default:J(()=>[Z(W(u))]),_:1})]),N("div",ya,[N("div",ga,d(Xa.value.avg_health_score)+"%",1),I[15]||(I[15]=N("div",{class:"stats-label"},"平均健康分数",-1))])]),N("div",wa,[Z(O,null,{default:J(()=>[Z(W(n))]),_:1}),I[16]||(I[16]=N("span",null,"+2.3%",-1))])])]),_:1}),Z(T,{span:6},{default:J(()=>[N("div",Va,[N("div",xa,[N("div",za,[Z(O,null,{default:J(()=>[Z(W(i))]),_:1})]),N("div",ja,[N("div",Ca,d(Xa.value.abnormal),1),I[17]||(I[17]=N("div",{class:"stats-label"},"异常域名",-1))])]),I[18]||(I[18]=N("div",{class:"stats-trend"},[N("span",null,"需要关注")],-1))])]),_:1}),Z(T,{span:6},{default:J(()=>[N("div",qa,[N("div",Sa,[N("div",Ua,[Z(O,null,{default:J(()=>[Z(W(_))]),_:1})]),N("div",Da,[N("div",Aa,d(Xa.value.blocked),1),I[19]||(I[19]=N("div",{class:"stats-label"},"封禁域名",-1))])]),I[20]||(I[20]=N("div",{class:"stats-trend negative"},[N("span",null,"需要处理")],-1))])]),_:1})]),_:1})]),Z(Y,{gutter:24},{default:J(()=>[Z(T,{span:16},{default:J(()=>[Z(Ve,{title:"域名管理"},{header:J(()=>[N("div",Ha,[I[21]||(I[21]=N("span",null,"域名管理",-1)),N("div",Qa,[Z(ta,{modelValue:ee.value,"onUpdate:modelValue":I[1]||(I[1]=a=>ee.value=a),placeholder:"筛选状态",style:{width:"120px"}},{default:J(()=>[Z(F,{label:"全部",value:""}),Z(F,{label:"正常",value:"normal"}),Z(F,{label:"异常",value:"abnormal"}),Z(F,{label:"封禁",value:"blocked"})]),_:1},8,["modelValue"])])])]),default:J(()=>[X((M(),aa(ge,{data:de.value},{default:J(()=>[Z(Wa,{prop:"domain",label:"域名","min-width":"200"}),Z(Wa,{prop:"status_name",label:"状态",width:"100"},{default:J(({row:a})=>[Z(ve,{type:pe(a.status)},{default:J(()=>[K(d(a.status_name),1)]),_:2},1032,["type"])]),_:1}),Z(Wa,{prop:"health_score",label:"健康分数",width:"120"},{default:J(({row:a})=>{return[Z(fe,{percentage:a.health_score,color:(e=a.health_score,e>=80?"#67c23a":e>=60?"#e6a23c":"#f56c6c"),"show-text":!1},null,8,["percentage","color"]),N("span",Ba,d(a.health_score)+"%",1)];var e}),_:1}),Z(Wa,{prop:"use_count",label:"使用次数",width:"100"}),Z(Wa,{prop:"last_check_time",label:"最后检查",width:"150"},{default:J(({row:a})=>[a.last_check_time?(M(),G("span",La,d(me(a.last_check_time)),1)):(M(),G("span",Ra,"未检查"))]),_:1}),Z(Wa,{label:"访问检测",width:"120"},{default:J(({row:a})=>[N("div",$a,[Z(he,{content:"微信访问",placement:"top"},{default:J(()=>[Z(O,{color:a.check_results?.wechat_accessible?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(y))]),_:2},1032,["color"])]),_:2},1024),Z(he,{content:"QQ访问",placement:"top"},{default:J(()=>[Z(O,{color:a.check_results?.qq_accessible?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(g))]),_:2},1032,["color"])]),_:2},1024),Z(he,{content:"SSL证书",placement:"top"},{default:J(()=>[Z(O,{color:a.check_results?.ssl_valid?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(w))]),_:2},1032,["color"])]),_:2},1024)])]),_:1}),Z(Wa,{label:"操作",width:"200",fixed:"right"},{default:J(({row:a})=>[Z(P,{size:"small",onClick:e=>(async a=>{try{await ca.checkDomainHealth(a.domain),E.success("域名检查完成"),ue()}catch(e){E.error("域名检查失败")}})(a)},{default:J(()=>I[22]||(I[22]=[K("检查",-1)])),_:2,__:[22]},1032,["onClick"]),Z(P,{size:"small",type:"success",onClick:e=>{return l=a,te.value=l,void(se.value=!0);var l}},{default:J(()=>I[23]||(I[23]=[K("详情",-1)])),_:2,__:[23]},1032,["onClick"]),Z(ye,{onCommand:_e},{dropdown:J(()=>[Z(ke,null,{default:J(()=>[Z(be,{command:{action:"restore",domain:a}},{default:J(()=>I[25]||(I[25]=[K("恢复",-1)])),_:2,__:[25]},1032,["command"]),Z(be,{command:{action:"block",domain:a}},{default:J(()=>I[26]||(I[26]=[K("封禁",-1)])),_:2,__:[26]},1032,["command"]),Z(be,{command:{action:"delete",domain:a},divided:""},{default:J(()=>I[27]||(I[27]=[K("删除",-1)])),_:2,__:[27]},1032,["command"])]),_:2},1024)]),default:J(()=>[Z(P,{size:"small"},{default:J(()=>[I[24]||(I[24]=K(" 更多",-1)),Z(O,null,{default:J(()=>[Z(W(x))]),_:1})]),_:1,__:[24]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[De,e.value]]),N("div",Ea,[Z(we,{"current-page":re.page,"onUpdate:currentPage":I[2]||(I[2]=a=>re.page=a),"page-size":re.size,"onUpdate:pageSize":I[3]||(I[3]=a=>re.size=a),total:re.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",onSizeChange:ue,onCurrentChange:ue},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),Z(T,{span:8},{default:J(()=>[Z(Ve,{title:"浏览器统计",class:"mb-4"},{default:J(()=>[N("div",Ia,[(M(!0),G(ea,null,la(ae.value,a=>(M(),G("div",{key:a.browser_type,class:"browser-item"},[N("div",Oa,[N("span",Pa,d(a.browser_name),1),N("span",Ta,d(a.count),1)]),Z(fe,{percentage:a.percentage,"show-text":!1,"stroke-width":6},null,8,["percentage"])]))),128))])]),_:1}),Z(Ve,{title:"访问趋势"},{default:J(()=>[N("div",{class:"trend-chart",ref_key:"trendChart",ref:ce,style:{height:"200px"}},null,512)]),_:1})]),_:1})]),_:1}),Z(qe,{modelValue:le.value,"onUpdate:modelValue":I[9]||(I[9]=a=>le.value=a),title:"添加域名",width:"500px"},{footer:J(()=>[Z(P,{onClick:I[8]||(I[8]=a=>le.value=!1)},{default:J(()=>I[28]||(I[28]=[K("取消",-1)])),_:1,__:[28]}),Z(P,{type:"primary",onClick:ie},{default:J(()=>I[29]||(I[29]=[K("添加",-1)])),_:1,__:[29]})]),default:J(()=>[Z(Ce,{model:oe,"label-width":"100px"},{default:J(()=>[Z(ze,{label:"域名",required:""},{default:J(()=>[Z(xe,{modelValue:oe.domain,"onUpdate:modelValue":I[4]||(I[4]=a=>oe.domain=a),placeholder:"请输入域名，如：example.com"},null,8,["modelValue"])]),_:1}),Z(ze,{label:"域名类型"},{default:J(()=>[Z(ta,{modelValue:oe.domain_type,"onUpdate:modelValue":I[5]||(I[5]=a=>oe.domain_type=a)},{default:J(()=>[Z(F,{label:"重定向域名",value:"redirect"}),Z(F,{label:"落地页域名",value:"landing"}),Z(F,{label:"API域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),Z(ze,{label:"优先级"},{default:J(()=>[Z(je,{modelValue:oe.priority,"onUpdate:modelValue":I[6]||(I[6]=a=>oe.priority=a),min:1,max:10},null,8,["modelValue"])]),_:1}),Z(ze,{label:"备注"},{default:J(()=>[Z(xe,{modelValue:oe.remarks,"onUpdate:modelValue":I[7]||(I[7]=a=>oe.remarks=a),type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),Z(qe,{modelValue:se.value,"onUpdate:modelValue":I[10]||(I[10]=a=>se.value=a),title:"域名详情",width:"70%"},{default:J(()=>[te.value?(M(),G("div",Ya,[Z(Ue,{column:2,border:""},{default:J(()=>[Z(Se,{label:"域名"},{default:J(()=>[K(d(te.value.domain),1)]),_:1}),Z(Se,{label:"状态"},{default:J(()=>[Z(ve,{type:pe(te.value.status)},{default:J(()=>[K(d(te.value.status_name),1)]),_:1},8,["type"])]),_:1}),Z(Se,{label:"健康分数"},{default:J(()=>[K(d(te.value.health_score)+"%",1)]),_:1}),Z(Se,{label:"使用次数"},{default:J(()=>[K(d(te.value.use_count),1)]),_:1}),Z(Se,{label:"最后检查"},{default:J(()=>[K(d(me(te.value.last_check_time)),1)]),_:1}),Z(Se,{label:"最后使用"},{default:J(()=>[K(d(me(te.value.last_use_time)),1)]),_:1})]),_:1}),te.value.check_results?(M(),G("div",Fa,[I[36]||(I[36]=N("h4",null,"检查结果详情",-1)),Z(Y,{gutter:16},{default:J(()=>[Z(T,{span:8},{default:J(()=>[N("div",Ga,[Z(O,{color:te.value.check_results.accessible?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(o))]),_:1},8,["color"]),I[30]||(I[30]=N("span",null,"可访问性",-1)),Z(ve,{type:te.value.check_results.accessible?"success":"danger",size:"small"},{default:J(()=>[K(d(te.value.check_results.accessible?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),Z(T,{span:8},{default:J(()=>[N("div",Ma,[Z(O,{color:te.value.check_results.dns_resolved?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(o))]),_:1},8,["color"]),I[31]||(I[31]=N("span",null,"DNS解析",-1)),Z(ve,{type:te.value.check_results.dns_resolved?"success":"danger",size:"small"},{default:J(()=>[K(d(te.value.check_results.dns_resolved?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),Z(T,{span:8},{default:J(()=>[N("div",Na,[Z(O,{color:te.value.check_results.ssl_valid?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(w))]),_:1},8,["color"]),I[32]||(I[32]=N("span",null,"SSL证书",-1)),Z(ve,{type:te.value.check_results.ssl_valid?"success":"danger",size:"small"},{default:J(()=>[K(d(te.value.check_results.ssl_valid?"有效":"无效"),1)]),_:1},8,["type"])])]),_:1})]),_:1}),Z(Y,{gutter:16,class:"mt-3"},{default:J(()=>[Z(T,{span:8},{default:J(()=>[N("div",Za,[Z(O,{color:te.value.check_results.wechat_accessible?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(y))]),_:1},8,["color"]),I[33]||(I[33]=N("span",null,"微信访问",-1)),Z(ve,{type:te.value.check_results.wechat_accessible?"success":"danger",size:"small"},{default:J(()=>[K(d(te.value.check_results.wechat_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),Z(T,{span:8},{default:J(()=>[N("div",Ja,[Z(O,{color:te.value.check_results.qq_accessible?"#67c23a":"#f56c6c"},{default:J(()=>[Z(W(g))]),_:1},8,["color"]),I[34]||(I[34]=N("span",null,"QQ访问",-1)),Z(ve,{type:te.value.check_results.qq_accessible?"success":"danger",size:"small"},{default:J(()=>[K(d(te.value.check_results.qq_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),Z(T,{span:8},{default:J(()=>[N("div",Ka,[Z(O,null,{default:J(()=>[Z(W($))]),_:1}),I[35]||(I[35]=N("span",null,"响应时间",-1)),Z(ve,{size:"small"},{default:J(()=>[K(d(te.value.check_results.response_time)+"ms",1)]),_:1})])]),_:1})]),_:1})])):sa("",!0)])):sa("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-7035d1c4"]]);export{Wa as default};
