<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="批量数据分析"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="batch-analysis-dialog">
      <div class="analysis-header">
        <el-alert
          :title="`正在分析 ${groupIds.length} 个群组的数据`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="analysis-options">
        <h4>分析选项</h4>
        <el-form :model="analysisForm" label-width="120px">
          <el-form-item label="分析时间范围">
            <el-radio-group v-model="analysisForm.timeRange">
              <el-radio label="7d">最近7天</el-radio>
              <el-radio label="30d">最近30天</el-radio>
              <el-radio label="90d">最近90天</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="分析维度">
            <el-checkbox-group v-model="analysisForm.dimensions">
              <el-checkbox label="activity">活跃度分析</el-checkbox>
              <el-checkbox label="revenue">收益分析</el-checkbox>
              <el-checkbox label="growth">成长趋势</el-checkbox>
              <el-checkbox label="health">健康度评估</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="报告格式">
            <el-radio-group v-model="analysisForm.format">
              <el-radio label="online">在线查看</el-radio>
              <el-radio label="pdf">PDF报告</el-radio>
              <el-radio label="excel">Excel表格</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="analyzing" class="analysis-progress">
        <h4>分析进度</h4>
        <div class="progress-list">
          <div
            v-for="(step, index) in analysisSteps"
            :key="index"
            class="progress-item"
            :class="{ 'completed': step.completed, 'active': step.active }"
          >
            <div class="step-icon">
              <el-icon v-if="step.completed"><Check /></el-icon>
              <el-icon v-else-if="step.active" class="loading"><Loading /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
          </div>
        </div>
        
        <div class="overall-progress">
          <el-progress
            :percentage="overallProgress"
            :stroke-width="8"
            :show-text="false"
          />
          <span class="progress-text">{{ overallProgress }}% 完成</span>
        </div>
      </div>

      <div v-if="analysisResult" class="analysis-result">
        <h4>分析结果预览</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="result-card">
              <div class="card-title">总体评分</div>
              <div class="card-value score">{{ analysisResult.overallScore }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-card">
              <div class="card-title">优秀群组</div>
              <div class="card-value excellent">{{ analysisResult.excellentGroups }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-card">
              <div class="card-title">需要关注</div>
              <div class="card-value warning">{{ analysisResult.needAttention }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="result-card">
              <div class="card-title">改进建议</div>
              <div class="card-value info">{{ analysisResult.suggestions }}</div>
            </div>
          </el-col>
        </el-row>

        <div class="result-summary">
          <h5>关键发现</h5>
          <ul>
            <li v-for="finding in analysisResult.keyFindings" :key="finding">
              {{ finding }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)" :disabled="analyzing">
          取消
        </el-button>
        <el-button
          v-if="!analyzing && !analysisResult"
          type="primary"
          @click="startAnalysis"
          :disabled="analysisForm.dimensions.length === 0"
        >
          开始分析
        </el-button>
        <el-button
          v-if="analysisResult"
          type="success"
          @click="viewFullReport"
        >
          查看完整报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: Boolean,
  groupIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'complete'])

const analyzing = ref(false)
const analysisResult = ref(null)

const analysisForm = reactive({
  timeRange: '30d',
  dimensions: ['activity', 'revenue'],
  format: 'online'
})

const analysisSteps = ref([
  {
    title: '数据收集',
    description: '收集群组基础数据和统计信息',
    completed: false,
    active: false
  },
  {
    title: '活跃度分析',
    description: '分析群组成员活跃度和互动情况',
    completed: false,
    active: false
  },
  {
    title: '收益分析',
    description: '计算群组收益和转化率',
    completed: false,
    active: false
  },
  {
    title: '趋势分析',
    description: '分析群组发展趋势和预测',
    completed: false,
    active: false
  },
  {
    title: '生成报告',
    description: '整合分析结果并生成报告',
    completed: false,
    active: false
  }
])

const overallProgress = computed(() => {
  const completedSteps = analysisSteps.value.filter(step => step.completed).length
  return Math.round((completedSteps / analysisSteps.value.length) * 100)
})

const startAnalysis = async () => {
  analyzing.value = true
  analysisResult.value = null

  try {
    // 模拟分析过程
    for (let i = 0; i < analysisSteps.value.length; i++) {
      analysisSteps.value[i].active = true
      
      // 模拟分析时间
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
      
      analysisSteps.value[i].completed = true
      analysisSteps.value[i].active = false
    }

    // 生成模拟分析结果
    analysisResult.value = {
      overallScore: Math.round(75 + Math.random() * 20),
      excellentGroups: Math.round(props.groupIds.length * 0.3),
      needAttention: Math.round(props.groupIds.length * 0.2),
      suggestions: Math.round(props.groupIds.length * 0.8),
      keyFindings: [
        '60% 的群组活跃度高于平均水平',
        '收益增长率在过去30天内提升了15%',
        '新成员留存率达到85%',
        '建议优化3个群组的运营策略'
      ]
    }

    ElMessage.success('批量分析完成！')
  } catch (error) {
    ElMessage.error('分析过程中出现错误')
  } finally {
    analyzing.value = false
  }
}

const viewFullReport = () => {
  ElMessage.info('正在跳转到详细报告页面...')
  emit('complete')
}
</script>

<style lang="scss" scoped>
.batch-analysis-dialog {
  .analysis-header {
    margin-bottom: 24px;
  }

  .analysis-options {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .analysis-progress {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .progress-list {
      margin-bottom: 20px;

      .progress-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.completed {
          .step-icon {
            background: #67c23a;
            color: white;
          }
          
          .step-title {
            color: #67c23a;
          }
        }

        &.active {
          .step-icon {
            background: #409eff;
            color: white;
            
            .loading {
              animation: rotate 1s linear infinite;
            }
          }
          
          .step-title {
            color: #409eff;
          }
        }

        .step-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #f0f0f0;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 14px;
          font-weight: 600;
        }

        .step-content {
          flex: 1;

          .step-title {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .step-description {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .overall-progress {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.el-progress) {
        flex: 1;
      }

      .progress-text {
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  .analysis-result {
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .result-card {
      background: #f8fafc;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      margin-bottom: 16px;

      .card-title {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
      }

      .card-value {
        font-size: 24px;
        font-weight: 700;

        &.score {
          color: #409eff;
        }

        &.excellent {
          color: #67c23a;
        }

        &.warning {
          color: #e6a23c;
        }

        &.info {
          color: #909399;
        }
      }
    }

    .result-summary {
      background: #f8fafc;
      border-radius: 8px;
      padding: 16px;

      h5 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          font-size: 14px;
          color: #606266;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>