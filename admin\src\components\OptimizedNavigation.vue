<template>
  <div class="optimized-navigation">
    <!-- 个性化工作台入口 -->
    <div class="workbench-section">
      <div class="workbench-card" @click="goToWorkbench">
        <div class="workbench-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="workbench-content">
          <div class="workbench-title">{{ workbenchConfig.title }}</div>
          <div class="workbench-desc">{{ workbenchConfig.description }}</div>
        </div>
        <div class="workbench-arrow">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <el-icon><Lightning /></el-icon>
        <span>快速操作</span>
      </div>
      <div class="quick-actions-grid">
        <div
          v-for="action in quickActions"
          :key="action.path"
          class="quick-action-item"
          :class="{ 'protected': action.path === '/community/add' }"
          @click="handleQuickAction(action)"
        >
          <div class="action-icon" :style="{ background: action.color }">
            <el-icon><component :is="getIconComponent(action.icon)" /></el-icon>
          </div>
          <div class="action-title">{{ action.title }}</div>
        </div>
      </div>
    </div>

    <!-- 优化后的导航分组 -->
    <div class="navigation-groups">
      <div
        v-for="(group, groupKey) in visibleGroups"
        :key="groupKey"
        class="nav-group"
      >
        <div class="group-header">
          <div class="group-icon">
            <el-icon><component :is="getIconComponent(group.icon)" /></el-icon>
          </div>
          <div class="group-info">
            <div class="group-title">{{ group.title }}</div>
            <div class="group-desc">{{ group.description }}</div>
          </div>
          <div class="group-badge">
            <el-badge :value="group.children.length" type="primary" />
          </div>
        </div>
        
        <div class="group-children">
          <div
            v-for="child in group.children"
            :key="child.path"
            class="nav-item"
            :class="{ 
              'active': isActiveRoute(child.path),
              'protected': child.protected 
            }"
            @click="navigateTo(child.path)"
          >
            <div class="item-icon">
              <el-icon><component :is="getIconComponent(child.icon)" /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-title">{{ child.title }}</div>
              <div class="item-desc">{{ child.description }}</div>
            </div>
            <div class="item-indicators">
              <el-tag v-if="child.protected" size="small" type="success">核心</el-tag>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近访问 -->
    <div class="recent-section" v-if="recentVisits.length > 0">
      <div class="section-header">
        <el-icon><Clock /></el-icon>
        <span>最近访问</span>
        <el-button text size="small" @click="clearRecentVisits">清空</el-button>
      </div>
      <div class="recent-list">
        <div
          v-for="visit in recentVisits.slice(0, 5)"
          :key="visit.path"
          class="recent-item"
          @click="navigateTo(visit.path)"
        >
          <div class="recent-icon">
            <el-icon><component :is="getIconComponent(visit.icon)" /></el-icon>
          </div>
          <div class="recent-content">
            <div class="recent-title">{{ visit.title }}</div>
            <div class="recent-time">{{ formatVisitTime(visit.visitTime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Monitor, Lightning, ArrowRight, Clock,
  DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
  OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick, 
  Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
  InfoFilled, CreditCard, Folder, View, Bell, Plus, Management, Promotion,
  ShoppingCart, RefreshLeft
} from '@element-plus/icons-vue'
import { getSimpleVisibleNavigationGroups, getSimpleUserQuickActions } from '@/config/simpleNavigationTest'
import { getUserWorkbenchConfig } from '@/config/navigation'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const recentVisits = ref([])

// 计算属性
const currentUserRole = computed(() => {
  return userStore.userInfo?.role || 'user'
})

const visibleGroups = computed(() => {
  return getSimpleVisibleNavigationGroups(currentUserRole.value)
})

const quickActions = computed(() => {
  return getSimpleUserQuickActions(currentUserRole.value)
})

const workbenchConfig = computed(() => {
  return getUserWorkbenchConfig(currentUserRole.value)
})

// 方法
const getIconComponent = (iconName) => {
  const iconMap = {
    Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
    OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick,
    Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
    InfoFilled, CreditCard, Folder, View, Bell, Plus, Lightning, Management, Promotion,
    ShoppingCart, RefreshLeft
  }
  return iconMap[iconName] || Document
}

const isActiveRoute = (path) => {
  return route.path === path || route.path.startsWith(path + '/')
}

const navigateTo = (path) => {
  if (!path) return
  
  // 添加到最近访问
  addToRecentVisits({
    path,
    title: getRouteTitle(path),
    icon: getRouteIcon(path),
    visitTime: Date.now()
  })
  
  router.push(path)
  ElMessage.success(`正在跳转到 ${getRouteTitle(path)}`)
}

const handleQuickAction = (action) => {
  navigateTo(action.path)
}

const goToWorkbench = () => {
  // 这里可以跳转到个性化工作台页面
  ElMessage.info('个性化工作台功能即将上线')
}

const addToRecentVisits = (visit) => {
  // 移除已存在的相同项目
  const existingIndex = recentVisits.value.findIndex(v => v.path === visit.path)
  if (existingIndex > -1) {
    recentVisits.value.splice(existingIndex, 1)
  }
  
  // 添加到开头
  recentVisits.value.unshift(visit)
  
  // 限制数量
  if (recentVisits.value.length > 10) {
    recentVisits.value = recentVisits.value.slice(0, 10)
  }
  
  // 保存到本地存储
  localStorage.setItem('recent-visits', JSON.stringify(recentVisits.value))
}

const clearRecentVisits = () => {
  recentVisits.value = []
  localStorage.removeItem('recent-visits')
  ElMessage.success('已清空最近访问记录')
}

const formatVisitTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const getRouteTitle = (path) => {
  // 根据路径获取标题的简单实现
  const titleMap = {
    '/admin/dashboard': '数据看板',
    '/admin/groups': '社群管理',
    '/admin/groups': '创建群组',
    '/admin/users': '用户管理',
    '/admin/finance': '财务总览',
    '/admin/orders': '订单管理'
  }
  return titleMap[path] || path
}

const getRouteIcon = (path) => {
  // 根据路径获取图标的简单实现
  const iconMap = {
    '/admin/dashboard': 'Monitor',
    '/admin/groups': 'Comment',
    '/admin/groups': 'Plus',
    '/admin/users': 'User',
    '/admin/finance': 'Money',
    '/admin/orders': 'ShoppingCart'
  }
  return iconMap[path] || 'Document'
}

// 初始化
onMounted(() => {
  // 加载最近访问记录
  const savedVisits = localStorage.getItem('recent-visits')
  if (savedVisits) {
    try {
      recentVisits.value = JSON.parse(savedVisits)
    } catch (error) {
      console.warn('Failed to load recent visits:', error)
    }
  }
})
</script>

<style lang="scss" scoped>
.optimized-navigation {
  padding: 16px;
  
  // 个性化工作台入口
  .workbench-section {
    margin-bottom: 24px;
    
    .workbench-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }
      
      .workbench-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          font-size: 24px;
        }
      }
      
      .workbench-content {
        flex: 1;
        
        .workbench-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .workbench-desc {
          font-size: 12px;
          opacity: 0.9;
        }
      }
      
      .workbench-arrow {
        opacity: 0.8;
      }
    }
  }
  
  // 快速操作区域
  .quick-actions-section {
    margin-bottom: 24px;
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #374151;
    }
    
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 12px;
      
      .quick-action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 8px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: #f9fafb;
          transform: translateY(-1px);
        }
        
        &.protected {
          background: #fef3c7;
          border: 1px solid #fbbf24;
          
          &:hover {
            background: #fef3c7;
            box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
          }
        }
        
        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          color: white;
          
          .el-icon {
            font-size: 20px;
          }
        }
        
        .action-title {
          font-size: 12px;
          text-align: center;
          color: #374151;
          font-weight: 500;
        }
      }
    }
  }
  
  // 导航分组
  .navigation-groups {
    .nav-group {
      margin-bottom: 20px;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      overflow: hidden;
      
      .group-header {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        
        .group-icon {
          width: 40px;
          height: 40px;
          background: #e5e7eb;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #6b7280;
        }
        
        .group-info {
          flex: 1;
          
          .group-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;
          }
          
          .group-desc {
            font-size: 12px;
            color: #6b7280;
          }
        }
        
        .group-badge {
          .el-badge {
            :deep(.el-badge__content) {
              background: #3b82f6;
            }
          }
        }
      }
      
      .group-children {
        .nav-item {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          border-bottom: 1px solid #f3f4f6;
          
          &:last-child {
            border-bottom: none;
          }
          
          &:hover {
            background: #f9fafb;
          }
          
          &.active {
            background: #eff6ff;
            border-left: 3px solid #3b82f6;
          }
          
          &.protected {
            background: #fef3c7;
            
            &:hover {
              background: #fef3c7;
            }
          }
          
          .item-icon {
            width: 32px;
            height: 32px;
            background: #f3f4f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #6b7280;
          }
          
          .item-content {
            flex: 1;
            
            .item-title {
              font-size: 14px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 2px;
            }
            
            .item-desc {
              font-size: 12px;
              color: #6b7280;
            }
          }
          
          .item-indicators {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .arrow-icon {
              color: #9ca3af;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  
  // 最近访问
  .recent-section {
    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #374151;
      
      .el-button {
        margin-left: auto;
      }
    }
    
    .recent-list {
      .recent-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: #f9fafb;
        }
        
        .recent-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
          margin-right: 12px;
        }
        
        .recent-content {
          flex: 1;
          
          .recent-title {
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 2px;
          }
          
          .recent-time {
            font-size: 11px;
            color: #9ca3af;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .optimized-navigation {
    padding: 12px;
    
    .quick-actions-grid {
      grid-template-columns: repeat(4, 1fr) !important;
    }
    
    .workbench-card {
      padding: 12px !important;
      
      .workbench-icon {
        width: 40px !important;
        height: 40px !important;
        margin-right: 12px !important;
      }
    }
  }
}
</style>