import{_ as e}from"./index-DtXAftX0.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import{H as a}from"./echarts-D68jitv0.js";import{a_ as l,aY as t,U as s,aZ as n,bm as i,bn as o,p as d,o as r,by as c,at as u,bp as p,bq as v,aM as m,b9 as g,b8 as _,ay as f,Q as h}from"./element-plus-h2SQQM64.js";import{L as y,r as b,e as V,k as x,l as k,E as w,z as C,t as j,u as U,D as z,F as A,Y as F}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const L={class:"app-container"},S={class:"stat-content"},D={class:"stat-data"},M={class:"stat-number"},E={class:"stat-change"},I={class:"change-text"},P={class:"stat-content"},Y={class:"stat-data"},q={class:"stat-number"},N={class:"stat-change"},Q={class:"change-text"},T={class:"stat-content"},Z={class:"stat-data"},B={class:"stat-number"},G={class:"stat-change"},H={class:"change-text"},J={class:"stat-content"},K={class:"stat-data"},O={class:"stat-number"},R={class:"stat-change"},W={class:"change-text"},X={class:"card-header"},$={class:"region-list"},ee={class:"region-info"},ae={class:"region-name"},le={class:"region-count"},te={class:"region-bar"},se={class:"device-stats"},ne={class:"device-info"},ie={class:"device-name"},oe={class:"device-count"},de={class:"device-percent"},re={class:"card-header"},ce={class:"behavior-card"},ue={class:"feature-list"},pe={class:"feature-rank"},ve={class:"feature-info"},me={class:"feature-name"},ge={class:"feature-usage"},_e={class:"feature-bar"},fe={class:"behavior-card"},he={class:"behavior-card"},ye={class:"retention-table"},be={class:"retention-rate"},Ve={class:"retention-rate"},xe={class:"card-header"},ke={class:"portrait-section"},we={class:"portrait-section"},Ce={class:"age-distribution"},je={class:"age-range"},Ue={class:"age-bar"},ze={class:"age-percent"},Ae={class:"segment-conditions"},Fe={class:"segment-preview"},Le={class:"dialog-footer"},Se=e({__name:"UserAnalytics",setup(e){const Se=y({total_users:12543,new_this_month:1245,active_users:8976,active_rate:71.5,vip_users:456,vip_rate:3.6,avg_user_value:1256.78,lifetime_value:3456.78}),De=b([{name:"北京",count:2543,percentage:85},{name:"上海",count:2134,percentage:71},{name:"广州",count:1876,percentage:63},{name:"深圳",count:1654,percentage:55},{name:"其他",count:4336,percentage:45}]),Me=b([{type:"mobile",name:"手机",icon:"el-icon-mobile-phone",count:8543,percentage:68},{type:"desktop",name:"电脑",icon:"el-icon-monitor",count:3210,percentage:26},{type:"tablet",name:"平板",icon:"el-icon-tablet",count:790,percentage:6}]),Ee=b([{name:"用户登录",usage:15643,percentage:100},{name:"查看商品",usage:12453,percentage:80},{name:"下单购买",usage:8976,percentage:57},{name:"分享推广",usage:6543,percentage:42},{name:"提现申请",usage:3456,percentage:22}]),Ie=b([{date:"2024-01-01",new_users:123,day1_retention:85,day7_retention:45},{date:"2024-01-02",new_users:156,day1_retention:78,day7_retention:42},{date:"2024-01-03",new_users:134,day1_retention:82,day7_retention:48}]),Pe=b([{range:"18-25",percentage:35,color:"#409EFF"},{range:"26-35",percentage:42,color:"#67C23A"},{range:"36-45",percentage:18,color:"#E6A23C"},{range:"46+",percentage:5,color:"#F56C6C"}]),Ye=b("30d"),qe=b([]),Ne=b({}),Qe=b({}),Te=b({}),Ze=b({}),Be=b({}),Ge=y({visible:!1}),He=y({name:"",conditions:[{field:"",operator:"",value:""}]}),Je=y({count:0,percentage:0}),Ke=()=>{const e=[],a=[],l=[];for(let t=("7d"===Ye.value?7:"30d"===Ye.value?30:90)-1;t>=0;t--){const s=new Date;s.setDate(s.getDate()-t),e.push(s.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})),a.push(Math.floor(100*Math.random())+50),l.push(Math.floor(200*Math.random())+100)}Ne.value={tooltip:{trigger:"axis"},legend:{data:["新增用户","活跃用户"]},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:a,itemStyle:{color:"#409EFF"}},{name:"活跃用户",type:"line",data:l,itemStyle:{color:"#67C23A"}}]}},Oe=()=>{h.success("行为数据已更新")},Re=()=>{Ge.visible=!0},We=()=>{He.conditions.push({field:"",operator:"",value:""})},Xe=()=>{h.success("用户分群创建成功"),Ge.visible=!1};return V(()=>{Ke(),Qe.value={tooltip:{trigger:"item"},series:[{name:"用户活跃度",type:"pie",radius:["30%","70%"],data:[{value:6543,name:"高活跃"},{value:2433,name:"中活跃"},{value:3567,name:"低活跃"}]}]},Te.value={tooltip:{trigger:"item"},series:[{name:"用户等级",type:"pie",radius:"60%",data:[{value:8543,name:"普通用户"},{value:3210,name:"银牌用户"},{value:790,name:"金牌用户"},{value:200,name:"VIP用户"}]}]},Ze.value={xAxis:{type:"category",data:["00:00","04:00","08:00","12:00","16:00","20:00"]},yAxis:{type:"value"},series:[{data:[120,200,150,800,700,500],type:"line",smooth:!0}]},Be.value={xAxis:{type:"category",data:["0-100","100-500","500-1000","1000-5000","5000+"]},yAxis:{type:"value"},series:[{data:[1200,3400,2800,1600,400],type:"bar"}]}}),(e,h)=>{const y=t,b=l,V=n,$e=o,ea=i,aa=c,la=u,ta=m,sa=v,na=_,ia=g,oa=p,da=f;return k(),x("div",L,[w(V,{gutter:20},{default:C(()=>[w(b,{span:6},{default:C(()=>[w(y,{class:"stat-card total-users"},{default:C(()=>[j("div",S,[h[6]||(h[6]=j("div",{class:"stat-icon"},[j("i",{class:"el-icon-user"})],-1)),j("div",D,[j("div",M,s(Se.total_users.toLocaleString()),1),h[5]||(h[5]=j("div",{class:"stat-label"},"总用户数",-1)),j("div",E,[j("span",I,"本月新增 "+s(Se.new_this_month),1)])])])]),_:1})]),_:1}),w(b,{span:6},{default:C(()=>[w(y,{class:"stat-card active-users"},{default:C(()=>[j("div",P,[h[8]||(h[8]=j("div",{class:"stat-icon"},[j("i",{class:"el-icon-star-on"})],-1)),j("div",Y,[j("div",q,s(Se.active_users.toLocaleString()),1),h[7]||(h[7]=j("div",{class:"stat-label"},"活跃用户",-1)),j("div",N,[j("span",Q,"活跃率 "+s(Se.active_rate)+"%",1)])])])]),_:1})]),_:1}),w(b,{span:6},{default:C(()=>[w(y,{class:"stat-card vip-users"},{default:C(()=>[j("div",T,[h[10]||(h[10]=j("div",{class:"stat-icon"},[j("i",{class:"el-icon-trophy"})],-1)),j("div",Z,[j("div",B,s(Se.vip_users.toLocaleString()),1),h[9]||(h[9]=j("div",{class:"stat-label"},"VIP用户",-1)),j("div",G,[j("span",H,"占比 "+s(Se.vip_rate)+"%",1)])])])]),_:1})]),_:1}),w(b,{span:6},{default:C(()=>[w(y,{class:"stat-card avg-value"},{default:C(()=>[j("div",J,[h[12]||(h[12]=j("div",{class:"stat-icon"},[j("i",{class:"el-icon-coin"})],-1)),j("div",K,[j("div",O,"¥"+s(Se.avg_user_value),1),h[11]||(h[11]=j("div",{class:"stat-label"},"用户均值",-1)),j("div",R,[j("span",W,"LTV ¥"+s(Se.lifetime_value),1)])])])]),_:1})]),_:1})]),_:1}),w(V,{gutter:20,style:{"margin-top":"20px"}},{default:C(()=>[w(b,{span:12},{default:C(()=>[w(y,null,{header:C(()=>[j("div",X,[h[16]||(h[16]=j("span",null,"📈 用户增长趋势",-1)),w(ea,{modelValue:Ye.value,"onUpdate:modelValue":h[0]||(h[0]=e=>Ye.value=e),size:"small",onChange:Ke},{default:C(()=>[w($e,{label:"7d"},{default:C(()=>h[13]||(h[13]=[z("7天",-1)])),_:1,__:[13]}),w($e,{label:"30d"},{default:C(()=>h[14]||(h[14]=[z("30天",-1)])),_:1,__:[14]}),w($e,{label:"90d"},{default:C(()=>h[15]||(h[15]=[z("90天",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])])]),default:C(()=>[w(U(a),{class:"chart",option:Ne.value,autoresize:""},null,8,["option"])]),_:1})]),_:1}),w(b,{span:12},{default:C(()=>[w(y,null,{header:C(()=>h[17]||(h[17]=[j("div",{class:"card-header"},[j("span",null,"🎯 用户活跃度")],-1)])),default:C(()=>[w(U(a),{class:"chart",option:Qe.value,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),w(V,{gutter:20,style:{"margin-top":"20px"}},{default:C(()=>[w(b,{span:8},{default:C(()=>[w(y,null,{header:C(()=>h[18]||(h[18]=[j("div",{class:"card-header"},[j("span",null,"🌍 地域分布")],-1)])),default:C(()=>[j("div",$,[(k(!0),x(A,null,F(De.value,e=>(k(),x("div",{class:"region-item",key:e.name},[j("div",ee,[j("span",ae,s(e.name),1),j("span",le,s(e.count)+"人",1)]),j("div",te,[j("div",{class:"region-progress",style:d({width:e.percentage+"%"})},null,4)])]))),128))])]),_:1})]),_:1}),w(b,{span:8},{default:C(()=>[w(y,null,{header:C(()=>h[19]||(h[19]=[j("div",{class:"card-header"},[j("span",null,"👥 用户等级分布")],-1)])),default:C(()=>[w(U(a),{class:"chart",option:Te.value,autoresize:""},null,8,["option"])]),_:1})]),_:1}),w(b,{span:8},{default:C(()=>[w(y,null,{header:C(()=>h[20]||(h[20]=[j("div",{class:"card-header"},[j("span",null,"📱 设备类型")],-1)])),default:C(()=>[j("div",se,[(k(!0),x(A,null,F(Me.value,e=>(k(),x("div",{class:"device-item",key:e.type},[j("div",{class:r(["device-icon",e.type])},[j("i",{class:r(e.icon)},null,2)],2),j("div",ne,[j("div",ie,s(e.name),1),j("div",oe,s(e.count)+"人",1),j("div",de,s(e.percentage)+"%",1)])]))),128))])]),_:1})]),_:1})]),_:1}),w(y,{style:{"margin-top":"20px"}},{header:C(()=>[j("div",re,[h[21]||(h[21]=j("span",null,"🔍 用户行为分析",-1)),j("div",null,[w(aa,{modelValue:qe.value,"onUpdate:modelValue":h[1]||(h[1]=e=>qe.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small",onChange:Oe},null,8,["modelValue"])])])]),default:C(()=>[w(V,{gutter:20},{default:C(()=>[w(b,{span:8},{default:C(()=>[j("div",ce,[h[22]||(h[22]=j("h4",null,"🚀 功能使用排行",-1)),j("div",ue,[(k(!0),x(A,null,F(Ee.value,(e,a)=>(k(),x("div",{class:"feature-item",key:e.name},[j("div",pe,s(a+1),1),j("div",ve,[j("div",me,s(e.name),1),j("div",ge,s(e.usage)+"次使用",1)]),j("div",_e,[j("div",{class:"feature-progress",style:d({width:e.percentage+"%"})},null,4)])]))),128))])])]),_:1}),w(b,{span:8},{default:C(()=>[j("div",fe,[h[23]||(h[23]=j("h4",null,"⏰ 活跃时段分析",-1)),w(U(a),{class:"mini-chart",option:Ze.value,autoresize:""},null,8,["option"])])]),_:1}),w(b,{span:8},{default:C(()=>[j("div",he,[h[25]||(h[25]=j("h4",null,"📊 用户留存分析",-1)),j("div",ye,[h[24]||(h[24]=j("div",{class:"retention-header"},[j("span",null,"时期"),j("span",null,"新增用户"),j("span",null,"次日留存"),j("span",null,"7日留存")],-1)),(k(!0),x(A,null,F(Ie.value,e=>(k(),x("div",{class:"retention-row",key:e.date},[j("span",null,s(e.date),1),j("span",null,s(e.new_users),1),j("span",be,s(e.day1_retention)+"%",1),j("span",Ve,s(e.day7_retention)+"%",1)]))),128))])])]),_:1})]),_:1})]),_:1}),w(y,{style:{"margin-top":"20px"}},{header:C(()=>[j("div",xe,[h[27]||(h[27]=j("span",null,"👤 用户画像分析",-1)),w(la,{type:"primary",onClick:Re},{default:C(()=>h[26]||(h[26]=[z("创建用户分群",-1)])),_:1,__:[26]})])]),default:C(()=>[w(V,{gutter:20},{default:C(()=>[w(b,{span:12},{default:C(()=>[j("div",ke,[h[28]||(h[28]=j("h4",null,"📈 消费能力分布",-1)),w(U(a),{class:"chart",option:Be.value,autoresize:""},null,8,["option"])])]),_:1}),w(b,{span:12},{default:C(()=>[j("div",we,[h[29]||(h[29]=j("h4",null,"🎂 年龄段分布",-1)),j("div",Ce,[(k(!0),x(A,null,F(Pe.value,e=>(k(),x("div",{class:"age-item",key:e.range},[j("div",je,s(e.range),1),j("div",Ue,[j("div",{class:"age-progress",style:d({width:e.percentage+"%",background:e.color})},null,4)]),j("div",ze,s(e.percentage)+"%",1)]))),128))])])]),_:1})]),_:1})]),_:1}),w(da,{title:"创建用户分群",modelValue:Ge.visible,"onUpdate:modelValue":h[4]||(h[4]=e=>Ge.visible=e),width:"800px"},{footer:C(()=>[j("div",Le,[w(la,{onClick:h[3]||(h[3]=e=>Ge.visible=!1)},{default:C(()=>h[31]||(h[31]=[z("取消",-1)])),_:1,__:[31]}),w(la,{type:"primary",onClick:Xe},{default:C(()=>h[32]||(h[32]=[z("创建分群",-1)])),_:1,__:[32]})])]),default:C(()=>[w(oa,{model:He,"label-width":"100px"},{default:C(()=>[w(sa,{label:"分群名称"},{default:C(()=>[w(ta,{modelValue:He.name,"onUpdate:modelValue":h[2]||(h[2]=e=>He.name=e),placeholder:"请输入分群名称"},null,8,["modelValue"])]),_:1}),w(sa,{label:"分群条件"},{default:C(()=>[j("div",Ae,[(k(!0),x(A,null,F(He.conditions,(e,a)=>(k(),x("div",{class:"condition-item",key:a},[w(ia,{modelValue:e.field,"onUpdate:modelValue":a=>e.field=a,placeholder:"选择字段"},{default:C(()=>[w(na,{label:"注册时间",value:"register_time"}),w(na,{label:"最后登录",value:"last_login"}),w(na,{label:"消费金额",value:"consumption"}),w(na,{label:"用户等级",value:"level"}),w(na,{label:"地域",value:"region"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),w(ia,{modelValue:e.operator,"onUpdate:modelValue":a=>e.operator=a,placeholder:"条件"},{default:C(()=>[w(na,{label:"等于",value:"="}),w(na,{label:"大于",value:">"}),w(na,{label:"小于",value:"<"}),w(na,{label:"包含",value:"in"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),w(ta,{modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,placeholder:"值"},null,8,["modelValue","onUpdate:modelValue"]),w(la,{type:"danger",onClick:e=>(e=>{He.conditions.splice(e,1)})(a),icon:"el-icon-delete",circle:""},null,8,["onClick"])]))),128)),w(la,{type:"primary",onClick:We,icon:"el-icon-plus"},{default:C(()=>h[30]||(h[30]=[z("添加条件",-1)])),_:1,__:[30]})])]),_:1}),w(sa,{label:"预览结果"},{default:C(()=>[j("div",Fe,[j("span",null,"预计匹配用户数："+s(Je.count),1),j("span",null,"占总用户比例："+s(Je.percentage)+"%",1)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-7b21837f"]]);export{Se as default};
