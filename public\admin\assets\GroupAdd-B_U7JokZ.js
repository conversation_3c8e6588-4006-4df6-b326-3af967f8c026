import{_ as e}from"./index-DtXAftX0.js";/* empty css                     *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                        */import{af as l,r as a,L as t,e as u,k as d,l as i,t as s,E as o,z as r,D as _,u as n,y as c,B as p,F as m,Y as v}from"./vue-vendor-Dy164gUc.js";import{T as f,aX as b,at as g,Y as h,bv as y,aY as V,aZ as w,a_ as k,bq as x,aM as q,br as U,bt as j,bu as C,bR as z,ai as A,_ as Q,b9 as $,b8 as B,bm as P,bB as I,bA as R,aw as Y,$ as D,bs as F,U as M,bX as Z,bU as E,bG as G,bS as L,bQ as S,am as T,b7 as W,bY as X,a0 as J,bp as K,ay as O,Q as H}from"./element-plus-h2SQQM64.js";import{c as N}from"./community-DNWNbya4.js";import{R as ee}from"./RichTextEditor-Cp69n7mq.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const le={class:"group-add-container"},ae={class:"page-header"},te={class:"header-content"},ue={class:"header-actions"},de={class:"form-container"},ie={class:"card-header"},se=["src"],oe={key:0,class:"image-list"},re=["src"],_e={class:"rich-editor-container"},ne={class:"card-header"},ce={key:0,class:"test-result"},pe={class:"card-header"},me={class:"card-header"},ve={class:"rich-editor-container"},fe={class:"editor-actions"},be={class:"rich-text-editor-wrapper"},ge={class:"editor-help"},he={class:"rich-editor-container"},ye={class:"editor-actions"},Ve={class:"rich-text-editor-wrapper"},we={class:"editor-help"},ke={class:"rich-editor-container"},xe={class:"editor-actions"},qe={class:"rich-text-editor-wrapper"},Ue={class:"editor-help"},je={class:"card-header"},Ce={class:"card-header"},ze=["src"],Ae=["src"],Qe=["src"],$e={class:"card-header"},Be={key:0,class:"test-result"},Pe={key:0,class:"preview-content"},Ie={class:"preview-header"},Re={class:"preview-stats"},Ye={class:"preview-price"},De={key:0,class:"preview-intro"},Fe={key:1,class:"preview-members"},Me={class:"member-list"},Ze=["src","alt"],Ee={class:"preview-button"},Ge=e({__name:"GroupAdd",setup(e){const Ge=l(),Le=a();a(!1);const Se=a(!1),Te=a(!1),We=a(null),Xe=a(""),Je=a([]),Ke=a(""),Oe=a("北京"),He=a(13),Ne=a("/api/upload/image"),el=t({title:"",price:0,payment_methods:["wechat","alipay"],type:"normal",status:"active",description:"",paid_content_type:"qr_code",qr_code:"",paid_images:[],paid_link:"",paid_link_desc:"",paid_document_content:"",paid_video_url:"",paid_video_title:"",paid_video_desc:"",auto_city_replace:0,city_insert_strategy:"auto",read_count_display:"10万+",like_count:888,want_see_count:666,button_title:"立即加入群聊",avatar_library:"qq",display_type:1,wx_accessible:1,group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",member_reviews:"",virtual_members:100,virtual_orders:50,virtual_income:5e3,today_views:1200,show_virtual_activity:1,show_member_avatars:1,show_member_reviews:1,show_customer_service:1,customer_service_title:"",customer_service_desc:"",customer_service_avatar:"",customer_service_qr:"",ad_qr_code:""}),ll={title:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入群组价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],type:[{required:!0,message:"请选择群组类型",trigger:"change"}]},al=e=>{e&&(el.title.includes("xxx")||H.info('建议在群组名称中使用"xxx"作为城市占位符，如："xxx交流群"'))},tl=()=>{if(!el.title)return void H.warning("请先输入群组名称");let e=el.title;if(el.auto_city_replace)switch(el.city_insert_strategy){case"prefix":e=Oe.value+el.title.replace(/^xxx/,"");break;case"suffix":e=el.title.replace(/xxx/,"")+"("+Oe.value+"版)";break;case"natural":default:e=el.title.replace(/xxx/g,Oe.value);break;case"auto":e=el.title.includes("xxx")?el.title.replace(/xxx/g,Oe.value):Oe.value+el.title}Ke.value=e,H.success("城市替换测试完成")},ul=()=>{if(!Xe.value)return void H.warning("请选择营销模板");const e=Je.value.find(e=>e.id===Xe.value);e&&e.config&&(Object.assign(el,e.config),H.success(`已应用${e.name}模板配置`))},dl=()=>{const e=["最美的太阳花","孤海的浪漫","薰衣草","木槿，花","森林小巷少女与狐@","冬日暖阳","午後の夏天","嘴角的美人痣。","朽梦挽歌","心淡然","青春不散场","时光不老我们不散","岁月如歌","梦想起航","阳光少年"],l=Array.from({length:He.value},(l,a)=>({nickname:e[a%e.length]+(a>e.length-1?a:""),avatar:`/face/${el.avatar_library}/${a%41+1}.${"qq"===el.avatar_library?"jpg":"jpeg"}`,join_time:(new Date).toLocaleString()}));return H.success(`成功生成 ${He.value} 个虚拟成员`),l},il=()=>{We.value={title:Ke.value||el.title,price:el.price,read_count_display:el.read_count_display,like_count:el.like_count,want_see_count:el.want_see_count,button_title:el.button_title,group_intro_title:el.group_intro_title,group_intro_content:el.group_intro_content,virtual_members:dl()},Te.value=!0},sl=()=>{Ge.go(-1)},ol=e=>{el.qr_code=e.data.url,H.success("二维码上传成功")},rl=e=>{el.customer_service_avatar=e.data.url,H.success("客服头像上传成功")},_l=e=>{el.customer_service_qr=e.data.url,H.success("客服二维码上传成功")},nl=e=>{el.ad_qr_code=e.data.url,H.success("广告二维码上传成功")},cl=e=>{el.paid_images||(el.paid_images=[]),el.paid_images.push(e.data.url),H.success("图片上传成功")},pl=e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<2;return l?!!a||(H.error("图片大小不能超过 2MB!"),!1):(H.error("只能上传图片文件!"),!1)},ml=e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<5;return l?!!a||(H.error("图片大小不能超过 5MB!"),!1):(H.error("只能上传图片文件!"),!1)},vl=pl,fl=async()=>{try{await Le.value.validate(),Se.value=!0;const e={title:el.title,price:el.price,description:el.description,status:"active"===el.status?1:0,paid_content_type:el.paid_content_type,qr_code:el.qr_code,paid_images:el.paid_images,paid_link:el.paid_link,paid_link_desc:el.paid_link_desc,paid_document_content:el.paid_document_content,paid_video_url:el.paid_video_url,paid_video_title:el.paid_video_title,paid_video_desc:el.paid_video_desc,auto_city_replace:el.auto_city_replace,city_insert_strategy:el.city_insert_strategy,read_count_display:el.read_count_display,like_count:el.like_count,want_see_count:el.want_see_count,button_title:el.button_title,avatar_library:el.avatar_library,display_type:el.display_type,wx_accessible:el.wx_accessible,group_intro_title:el.group_intro_title,group_intro_content:el.group_intro_content,faq_title:el.faq_title,faq_content:el.faq_content,member_reviews:el.member_reviews,virtual_members:el.virtual_members,virtual_orders:el.virtual_orders,virtual_income:el.virtual_income,today_views:el.today_views,show_virtual_activity:el.show_virtual_activity,show_member_avatars:el.show_member_avatars,show_member_reviews:el.show_member_reviews,show_customer_service:el.show_customer_service,customer_service_title:el.customer_service_title,customer_service_desc:el.customer_service_desc,customer_service_avatar:el.customer_service_avatar,customer_service_qr:el.customer_service_qr,ad_qr_code:el.ad_qr_code},l=await N(e);200===l.code?(H.success("群组创建成功！所有营销配置已保存"),Ge.push("/community/groups")):H.error(l.message||"创建失败")}catch(e){console.error("创建群组失败:",e),H.error("创建失败，请重试")}finally{Se.value=!1}},bl=e=>{let l="";switch(e){case"intro":l="<h3>🎯 群组特色</h3>\n<p>• <strong>专业交流</strong>：汇聚行业精英，分享最新资讯</p>\n<p>• <strong>资源共享</strong>：独家资料、工具、经验分享</p>\n<p>• <strong>人脉拓展</strong>：结识志同道合的朋友</p>\n<p>• <strong>持续成长</strong>：定期活动、培训、讲座</p>\n\n<h3>💎 加入收获</h3>\n<p>✅ 获得行业内幕消息和趋势分析</p>\n<p>✅ 学习成功案例和实战经验</p>\n<p>✅ 建立有价值的商业人脉</p>\n<p>✅ 参与线上线下活动交流</p>",el.group_intro_content=l;break;case"faq":l="<div><strong>Q: 这个群主要讨论什么内容？</strong></div>\n<div>A: 我们主要分享行业资讯、经验交流、资源共享，以及定期组织线上线下活动。</div>\n<br>\n<div><strong>Q: 群里会有广告吗？</strong></div>\n<div>A: 我们严格管理群内容，禁止无关广告，只允许有价值的内容分享。</div>\n<br>\n<div><strong>Q: 如何参与群内活动？</strong></div>\n<div>A: 群内会定期发布活动通知，大家可以根据兴趣自由参与。</div>\n<br>\n<div><strong>Q: 有什么群规需要遵守？</strong></div>\n<div>A: 请保持友善交流，分享有价值内容，禁止发布广告和无关信息。</div>",el.faq_content=l;break;case"reviews":l="<div><strong>张先生</strong>：群里的资源真的很棒，学到了很多实用的技巧！⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>李女士</strong>：群主很用心，经常分享有价值的内容，强烈推荐！⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>王总</strong>：通过这个群认识了很多同行朋友，合作机会很多。⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>陈经理</strong>：群内氛围很好，大家都很乐于分享，收获满满。⭐⭐⭐⭐⭐</div>",el.member_reviews=l}H.success("模板内容已插入")};return u(()=>{(async()=>{try{Je.value=[{id:1,name:"商务交流模板",config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入商务群",group_intro_title:"商务交流群简介",group_intro_content:"专业的商务交流平台，汇聚各行业精英",virtual_members:150,virtual_orders:80}},{id:2,name:"社交娱乐模板",config:{read_count_display:"10万+",like_count:2e3,want_see_count:1500,button_title:"快来聊天吧",group_intro_title:"欢乐聊天群",group_intro_content:"轻松愉快的聊天环境，结识更多朋友",virtual_members:200,virtual_orders:120}},{id:3,name:"学习教育模板",config:{read_count_display:"8万+",like_count:1500,want_see_count:1e3,button_title:"加入学习群",group_intro_title:"学习交流群",group_intro_content:"专业的学习交流平台，共同进步成长",virtual_members:180,virtual_orders:90}}]}catch(e){console.error("获取营销模板失败:",e)}})()}),(e,l)=>{const a=f,t=g,u=q,N=x,Ge=k,gl=U,hl=w,yl=C,Vl=j,wl=B,kl=$,xl=I,ql=P,Ul=R,jl=V,Cl=F,zl=K,Al=O;return i(),d("div",le,[s("div",ae,[s("div",te,[l[51]||(l[51]=s("div",{class:"header-left"},[s("h1",{class:"page-title"},"创建群组"),s("p",{class:"page-subtitle"},"创建新的微信群组并配置完整的营销功能")],-1)),s("div",ue,[o(t,{onClick:sl},{default:r(()=>[o(a,null,{default:r(()=>[o(n(b))]),_:1}),l[48]||(l[48]=_(" 返回 ",-1))]),_:1,__:[48]}),o(t,{type:"success",onClick:il,disabled:!el.title},{default:r(()=>[o(a,null,{default:r(()=>[o(n(h))]),_:1}),l[49]||(l[49]=_(" 预览效果 ",-1))]),_:1,__:[49]},8,["disabled"]),o(t,{type:"primary",onClick:fl,loading:Se.value},{default:r(()=>[o(a,null,{default:r(()=>[o(n(y))]),_:1}),l[50]||(l[50]=_(" 创建群组 ",-1))]),_:1,__:[50]},8,["loading"])])])]),s("div",de,[o(zl,{ref_key:"formRef",ref:Le,model:el,rules:ll,"label-width":"120px",size:"default"},{default:r(()=>[o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",ie,[o(a,null,{default:r(()=>[o(n(D))]),_:1}),l[52]||(l[52]=s("span",null,"基础信息",-1))])]),default:r(()=>[o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"群组名称",prop:"title"},{default:r(()=>[o(u,{modelValue:el.title,"onUpdate:modelValue":l[0]||(l[0]=e=>el.title=e),placeholder:"请输入群组名称，支持xxx占位符",maxlength:"200","show-word-limit":""},null,8,["modelValue"]),l[53]||(l[53]=s("div",{class:"form-tip"},' 💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市 ',-1))]),_:1,__:[53]})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"群组价格",prop:"price"},{default:r(()=>[o(gl,{modelValue:el.price,"onUpdate:modelValue":l[1]||(l[1]=e=>el.price=e),min:0,precision:2,placeholder:"0.00",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(N,{label:"付款方式",prop:"payment_methods"},{default:r(()=>[o(Vl,{modelValue:el.payment_methods,"onUpdate:modelValue":l[2]||(l[2]=e=>el.payment_methods=e)},{default:r(()=>[o(yl,{value:"wechat",border:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(z))]),_:1}),l[54]||(l[54]=_(" 微信支付 ",-1))]),_:1,__:[54]}),o(yl,{value:"alipay",border:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(A))]),_:1}),l[55]||(l[55]=_(" 支付宝 ",-1))]),_:1,__:[55]}),o(yl,{value:"epay",border:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(Q))]),_:1}),l[56]||(l[56]=_(" 易支付 ",-1))]),_:1,__:[56]})]),_:1},8,["modelValue"]),l[57]||(l[57]=s("div",{class:"form-tip"}," 选择支持的付款方式，用户可通过选中的方式进行付费 ",-1))]),_:1,__:[57]}),o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"群组类型",prop:"type"},{default:r(()=>[o(kl,{modelValue:el.type,"onUpdate:modelValue":l[3]||(l[3]=e=>el.type=e),placeholder:"请选择群组类型",style:{width:"100%"}},{default:r(()=>[o(wl,{label:"普通群",value:"normal"}),o(wl,{label:"VIP群",value:"vip"}),o(wl,{label:"分销群",value:"distribution"}),o(wl,{label:"测试群",value:"test"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"群组状态",prop:"status"},{default:r(()=>[o(ql,{modelValue:el.status,"onUpdate:modelValue":l[4]||(l[4]=e=>el.status=e)},{default:r(()=>[o(xl,{value:"active"},{default:r(()=>l[58]||(l[58]=[_("启用",-1)])),_:1,__:[58]}),o(xl,{value:"inactive"},{default:r(()=>l[59]||(l[59]=[_("禁用",-1)])),_:1,__:[59]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(N,{label:"群组描述"},{default:r(()=>[o(u,{modelValue:el.description,"onUpdate:modelValue":l[5]||(l[5]=e=>el.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),o(N,{label:"付费后内容类型"},{default:r(()=>[o(ql,{modelValue:el.paid_content_type,"onUpdate:modelValue":l[6]||(l[6]=e=>el.paid_content_type=e)},{default:r(()=>[o(xl,{value:"qr_code"},{default:r(()=>l[60]||(l[60]=[_("入群二维码",-1)])),_:1,__:[60]}),o(xl,{value:"image"},{default:r(()=>l[61]||(l[61]=[_("图片资源",-1)])),_:1,__:[61]}),o(xl,{value:"link"},{default:r(()=>l[62]||(l[62]=[_("下载链接",-1)])),_:1,__:[62]}),o(xl,{value:"document"},{default:r(()=>l[63]||(l[63]=[_("文档资料",-1)])),_:1,__:[63]}),o(xl,{value:"video"},{default:r(()=>l[64]||(l[64]=[_("视频链接",-1)])),_:1,__:[64]})]),_:1},8,["modelValue"]),l[65]||(l[65]=s("div",{class:"form-tip"},"选择用户付费成功后要展示的内容类型",-1))]),_:1,__:[65]}),"qr_code"===el.paid_content_type?(i(),c(N,{key:0,label:"入群二维码"},{default:r(()=>[o(Ul,{class:"qr-uploader",action:Ne.value,"show-file-list":!1,"on-success":ol,"before-upload":pl},{default:r(()=>[el.qr_code?(i(),d("img",{key:0,src:el.qr_code,class:"qr-image"},null,8,se)):(i(),c(a,{key:1,class:"qr-uploader-icon"},{default:r(()=>[o(n(Y))]),_:1}))]),_:1},8,["action"]),l[66]||(l[66]=s("div",{class:"form-tip"},"上传微信群二维码，用户付费后可扫码入群",-1))]),_:1,__:[66]})):p("",!0),"image"===el.paid_content_type?(i(),c(N,{key:1,label:"图片资源"},{default:r(()=>[o(Ul,{class:"image-uploader",action:Ne.value,"show-file-list":!1,"on-success":cl,"before-upload":ml,multiple:""},{default:r(()=>[el.paid_images&&el.paid_images.length?(i(),d("div",oe,[(i(!0),d(m,null,v(el.paid_images,(e,a)=>(i(),d("div",{key:a,class:"image-item"},[s("img",{src:e},null,8,re),o(t,{size:"small",type:"danger",onClick:e=>(e=>{el.paid_images.splice(e,1)})(a)},{default:r(()=>l[67]||(l[67]=[_("删除",-1)])),_:2,__:[67]},1032,["onClick"])]))),128))])):(i(),c(a,{key:1,class:"image-uploader-icon"},{default:r(()=>[o(n(Y))]),_:1}))]),_:1},8,["action"]),l[68]||(l[68]=s("div",{class:"form-tip"},"上传付费后展示的图片资源",-1))]),_:1,__:[68]})):p("",!0),"link"===el.paid_content_type?(i(),d(m,{key:2},[o(N,{label:"下载链接"},{default:r(()=>[o(u,{modelValue:el.paid_link,"onUpdate:modelValue":l[7]||(l[7]=e=>el.paid_link=e),placeholder:"请输入下载链接"},null,8,["modelValue"]),l[69]||(l[69]=s("div",{class:"form-tip"},"用户付费后可访问的下载链接",-1))]),_:1,__:[69]}),o(N,{label:"链接描述"},{default:r(()=>[o(u,{modelValue:el.paid_link_desc,"onUpdate:modelValue":l[8]||(l[8]=e=>el.paid_link_desc=e),placeholder:"如：专业资料包下载"},null,8,["modelValue"])]),_:1})],64)):p("",!0),"document"===el.paid_content_type?(i(),c(N,{key:3,label:"文档内容"},{default:r(()=>[s("div",_e,[o(ee,{modelValue:el.paid_document_content,"onUpdate:modelValue":l[9]||(l[9]=e=>el.paid_document_content=e),height:200,placeholder:"输入付费后展示的文档内容","max-length":5e3},null,8,["modelValue"])])]),_:1})):p("",!0),"video"===el.paid_content_type?(i(),d(m,{key:4},[o(N,{label:"视频链接"},{default:r(()=>[o(u,{modelValue:el.paid_video_url,"onUpdate:modelValue":l[10]||(l[10]=e=>el.paid_video_url=e),placeholder:"请输入视频链接"},null,8,["modelValue"]),l[70]||(l[70]=s("div",{class:"form-tip"},"支持各大视频平台链接",-1))]),_:1,__:[70]}),o(N,{label:"视频标题"},{default:r(()=>[o(u,{modelValue:el.paid_video_title,"onUpdate:modelValue":l[11]||(l[11]=e=>el.paid_video_title=e),placeholder:"如：专业培训视频"},null,8,["modelValue"])]),_:1}),o(N,{label:"视频描述"},{default:r(()=>[o(u,{modelValue:el.paid_video_desc,"onUpdate:modelValue":l[12]||(l[12]=e=>el.paid_video_desc=e),type:"textarea",rows:3,placeholder:"视频内容描述"},null,8,["modelValue"])]),_:1})],64)):p("",!0)]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",ne,[o(a,null,{default:r(()=>[o(n(Z))]),_:1}),l[71]||(l[71]=s("span",null,"城市定位配置",-1))])]),default:r(()=>[o(N,{label:"启用城市定位"},{default:r(()=>[o(Cl,{modelValue:el.auto_city_replace,"onUpdate:modelValue":l[13]||(l[13]=e=>el.auto_city_replace=e),"active-value":1,"inactive-value":0,onChange:al},null,8,["modelValue"]),l[72]||(l[72]=s("span",{class:"form-tip"},"启用后，系统会根据用户IP自动替换标题中的城市信息",-1))]),_:1,__:[72]}),el.auto_city_replace?(i(),d(m,{key:0},[o(N,{label:"城市插入策略"},{default:r(()=>[o(kl,{modelValue:el.city_insert_strategy,"onUpdate:modelValue":l[14]||(l[14]=e=>el.city_insert_strategy=e),style:{width:"100%"}},{default:r(()=>[o(wl,{label:"智能判断（推荐）",value:"auto"}),o(wl,{label:"前缀模式（北京+标题）",value:"prefix"}),o(wl,{label:"后缀模式（标题+北京）",value:"suffix"}),o(wl,{label:"自然插入（智能融入）",value:"natural"}),o(wl,{label:"不插入",value:"none"})]),_:1},8,["modelValue"])]),_:1}),o(N,{label:"城市定位测试"},{default:r(()=>[o(hl,{gutter:12},{default:r(()=>[o(Ge,{span:8},{default:r(()=>[o(u,{modelValue:Oe.value,"onUpdate:modelValue":l[15]||(l[15]=e=>Oe.value=e),placeholder:"输入测试城市"},null,8,["modelValue"])]),_:1}),o(Ge,{span:8},{default:r(()=>[o(t,{onClick:tl},{default:r(()=>l[73]||(l[73]=[_("测试替换效果",-1)])),_:1,__:[73]})]),_:1}),o(Ge,{span:8},{default:r(()=>[Ke.value?(i(),d("span",ce,M(Ke.value),1)):p("",!0)]),_:1})]),_:1})]),_:1})],64)):p("",!0)]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",pe,[o(a,null,{default:r(()=>[o(n(E))]),_:1}),l[74]||(l[74]=s("span",null,"营销展示配置",-1))])]),default:r(()=>[o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:8},{default:r(()=>[o(N,{label:"阅读数显示"},{default:r(()=>[o(u,{modelValue:el.read_count_display,"onUpdate:modelValue":l[16]||(l[16]=e=>el.read_count_display=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:8},{default:r(()=>[o(N,{label:"点赞数"},{default:r(()=>[o(gl,{modelValue:el.like_count,"onUpdate:modelValue":l[17]||(l[17]=e=>el.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:8},{default:r(()=>[o(N,{label:"想看数"},{default:r(()=>[o(gl,{modelValue:el.want_see_count,"onUpdate:modelValue":l[18]||(l[18]=e=>el.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"入群按钮文案"},{default:r(()=>[o(u,{modelValue:el.button_title,"onUpdate:modelValue":l[19]||(l[19]=e=>el.button_title=e),placeholder:"如：立即加入群聊"},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"头像库选择"},{default:r(()=>[o(kl,{modelValue:el.avatar_library,"onUpdate:modelValue":l[20]||(l[20]=e=>el.avatar_library=e),style:{width:"100%"}},{default:r(()=>[o(wl,{label:"QQ风格（扩列交友）",value:"qq"}),o(wl,{label:"综合随机",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"展示类型"},{default:r(()=>[o(ql,{modelValue:el.display_type,"onUpdate:modelValue":l[21]||(l[21]=e=>el.display_type=e)},{default:r(()=>[o(xl,{value:1},{default:r(()=>l[75]||(l[75]=[_("文字+图片",-1)])),_:1,__:[75]}),o(xl,{value:2},{default:r(()=>l[76]||(l[76]=[_("纯图片",-1)])),_:1,__:[76]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"微信访问控制"},{default:r(()=>[o(ql,{modelValue:el.wx_accessible,"onUpdate:modelValue":l[22]||(l[22]=e=>el.wx_accessible=e)},{default:r(()=>[o(xl,{value:1},{default:r(()=>l[77]||(l[77]=[_("微信能打开",-1)])),_:1,__:[77]}),o(xl,{value:2},{default:r(()=>l[78]||(l[78]=[_("微信内不能打开",-1)])),_:1,__:[78]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",me,[o(a,null,{default:r(()=>[o(n(T))]),_:1}),l[79]||(l[79]=s("span",null,"内容配置",-1))])]),default:r(()=>[o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"群简介标题"},{default:r(()=>[o(u,{modelValue:el.group_intro_title,"onUpdate:modelValue":l[23]||(l[23]=e=>el.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"FAQ标题"},{default:r(()=>[o(u,{modelValue:el.faq_title,"onUpdate:modelValue":l[24]||(l[24]=e=>el.faq_title=e),placeholder:"如：常见问题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(N,{label:"群简介内容"},{default:r(()=>[s("div",ve,[s("div",fe,[o(t,{size:"small",type:"primary",onClick:l[25]||(l[25]=e=>bl("intro"))},{default:r(()=>[o(a,null,{default:r(()=>[o(n(G))]),_:1}),l[80]||(l[80]=_(" 插入模板 ",-1))]),_:1,__:[80]}),o(t,{size:"small",onClick:l[26]||(l[26]=e=>{H.info("图片上传功能开发中，敬请期待")})},{default:r(()=>[o(a,null,{default:r(()=>[o(n(L))]),_:1}),l[81]||(l[81]=_(" 插入图片 ",-1))]),_:1,__:[81]}),o(t,{size:"small",onClick:l[27]||(l[27]=e=>el.group_intro_content=""),type:"danger",plain:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(S))]),_:1}),l[82]||(l[82]=_(" 清空内容 ",-1))]),_:1,__:[82]})]),s("div",be,[o(ee,{modelValue:el.group_intro_content,"onUpdate:modelValue":l[28]||(l[28]=e=>el.group_intro_content=e),height:200,placeholder:"详细介绍群组的价值和特色，支持富文本格式。可以添加粗体、斜体、列表、链接、图片等内容。","max-length":2e3},null,8,["modelValue"])]),s("div",ge,[o(a,null,{default:r(()=>[o(n(D))]),_:1}),l[83]||(l[83]=s("span",null,[_("富文本编辑器支持："),s("strong",null,"粗体"),_("、"),s("em",null,"斜体"),_("、列表、链接、图片等格式")],-1))])])]),_:1}),o(N,{label:"常见问题"},{default:r(()=>[s("div",he,[s("div",ye,[o(t,{size:"small",type:"primary",onClick:l[29]||(l[29]=e=>bl("faq"))},{default:r(()=>[o(a,null,{default:r(()=>[o(n(G))]),_:1}),l[84]||(l[84]=_(" 插入FAQ模板 ",-1))]),_:1,__:[84]}),o(t,{size:"small",onClick:l[30]||(l[30]=e=>el.faq_content=""),type:"danger",plain:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(S))]),_:1}),l[85]||(l[85]=_(" 清空内容 ",-1))]),_:1,__:[85]})]),s("div",Ve,[o(ee,{modelValue:el.faq_content,"onUpdate:modelValue":l[31]||(l[31]=e=>el.faq_content=e),height:180,placeholder:"输入常见问题和答案，支持富文本格式。建议格式：Q: 问题内容 A: 答案内容","max-length":3e3},null,8,["modelValue"])]),s("div",we,[o(a,null,{default:r(()=>[o(n(D))]),_:1}),l[86]||(l[86]=s("span",null,[_("建议格式："),s("strong",null,"Q: 问题内容"),_(),s("br"),_(),s("strong",null,"A: 答案内容"),_("，每个问答占一行")],-1))])])]),_:1}),o(N,{label:"群友评论"},{default:r(()=>[s("div",ke,[s("div",xe,[o(t,{size:"small",type:"primary",onClick:l[32]||(l[32]=e=>bl("reviews"))},{default:r(()=>[o(a,null,{default:r(()=>[o(n(G))]),_:1}),l[87]||(l[87]=_(" 插入评价模板 ",-1))]),_:1,__:[87]}),o(t,{size:"small",onClick:l[33]||(l[33]=e=>el.member_reviews=""),type:"danger",plain:""},{default:r(()=>[o(a,null,{default:r(()=>[o(n(S))]),_:1}),l[88]||(l[88]=_(" 清空内容 ",-1))]),_:1,__:[88]})]),s("div",qe,[o(ee,{modelValue:el.member_reviews,"onUpdate:modelValue":l[34]||(l[34]=e=>el.member_reviews=e),height:160,placeholder:"输入群友评价内容，支持富文本格式。建议格式：用户名：评价内容 ⭐⭐⭐⭐⭐","max-length":2e3},null,8,["modelValue"])]),s("div",Ue,[o(a,null,{default:r(()=>[o(n(D))]),_:1}),l[89]||(l[89]=s("span",null,[_("建议格式："),s("strong",null,"用户名：评价内容 ⭐⭐⭐⭐⭐"),_("，每个评价占一行")],-1))])])]),_:1})]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",je,[o(a,null,{default:r(()=>[o(n(W))]),_:1}),l[90]||(l[90]=s("span",null,"虚拟数据配置",-1))])]),default:r(()=>[o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:6},{default:r(()=>[o(N,{label:"虚拟成员数"},{default:r(()=>[o(gl,{modelValue:el.virtual_members,"onUpdate:modelValue":l[35]||(l[35]=e=>el.virtual_members=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:6},{default:r(()=>[o(N,{label:"虚拟订单数"},{default:r(()=>[o(gl,{modelValue:el.virtual_orders,"onUpdate:modelValue":l[36]||(l[36]=e=>el.virtual_orders=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:6},{default:r(()=>[o(N,{label:"虚拟收入"},{default:r(()=>[o(gl,{modelValue:el.virtual_income,"onUpdate:modelValue":l[37]||(l[37]=e=>el.virtual_income=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:6},{default:r(()=>[o(N,{label:"今日浏览量"},{default:r(()=>[o(gl,{modelValue:el.today_views,"onUpdate:modelValue":l[38]||(l[38]=e=>el.today_views=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:8},{default:r(()=>[o(N,{label:"显示虚拟活动"},{default:r(()=>[o(Cl,{modelValue:el.show_virtual_activity,"onUpdate:modelValue":l[39]||(l[39]=e=>el.show_virtual_activity=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:8},{default:r(()=>[o(N,{label:"显示成员头像"},{default:r(()=>[o(Cl,{modelValue:el.show_member_avatars,"onUpdate:modelValue":l[40]||(l[40]=e=>el.show_member_avatars=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:8},{default:r(()=>[o(N,{label:"显示群友评论"},{default:r(()=>[o(Cl,{modelValue:el.show_member_reviews,"onUpdate:modelValue":l[41]||(l[41]=e=>el.show_member_reviews=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(N,{label:"生成虚拟成员"},{default:r(()=>[o(hl,{gutter:12},{default:r(()=>[o(Ge,{span:8},{default:r(()=>[o(gl,{modelValue:He.value,"onUpdate:modelValue":l[42]||(l[42]=e=>He.value=e),min:1,max:50},null,8,["modelValue"])]),_:1}),o(Ge,{span:8},{default:r(()=>[o(t,{onClick:dl},{default:r(()=>l[91]||(l[91]=[_("生成成员数据",-1)])),_:1,__:[91]})]),_:1}),o(Ge,{span:8},{default:r(()=>l[92]||(l[92]=[s("span",{class:"form-tip"},"将自动生成指定数量的虚拟成员",-1)])),_:1,__:[92]})]),_:1})]),_:1})]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",Ce,[o(a,null,{default:r(()=>[o(n(X))]),_:1}),l[93]||(l[93]=s("span",null,"客服配置",-1))])]),default:r(()=>[o(N,{label:"显示客服信息"},{default:r(()=>[o(ql,{modelValue:el.show_customer_service,"onUpdate:modelValue":l[43]||(l[43]=e=>el.show_customer_service=e)},{default:r(()=>[o(xl,{value:1},{default:r(()=>l[94]||(l[94]=[_("不显示",-1)])),_:1,__:[94]}),o(xl,{value:2},{default:r(()=>l[95]||(l[95]=[_("显示",-1)])),_:1,__:[95]})]),_:1},8,["modelValue"])]),_:1}),2===el.show_customer_service?(i(),d(m,{key:0},[o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"客服标题"},{default:r(()=>[o(u,{modelValue:el.customer_service_title,"onUpdate:modelValue":l[44]||(l[44]=e=>el.customer_service_title=e),placeholder:"如：VIP专属客服"},null,8,["modelValue"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"客服描述"},{default:r(()=>[o(u,{modelValue:el.customer_service_desc,"onUpdate:modelValue":l[45]||(l[45]=e=>el.customer_service_desc=e),placeholder:"如：有问题请联系客服"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(hl,{gutter:24},{default:r(()=>[o(Ge,{span:12},{default:r(()=>[o(N,{label:"客服头像"},{default:r(()=>[o(Ul,{class:"avatar-uploader",action:Ne.value,"show-file-list":!1,"on-success":rl,"before-upload":n(vl)},{default:r(()=>[el.customer_service_avatar?(i(),d("img",{key:0,src:el.customer_service_avatar,class:"avatar"},null,8,ze)):(i(),c(a,{key:1,class:"avatar-uploader-icon"},{default:r(()=>[o(n(Y))]),_:1}))]),_:1},8,["action","before-upload"])]),_:1})]),_:1}),o(Ge,{span:12},{default:r(()=>[o(N,{label:"客服二维码"},{default:r(()=>[o(Ul,{class:"qr-uploader",action:Ne.value,"show-file-list":!1,"on-success":_l,"before-upload":pl},{default:r(()=>[el.customer_service_qr?(i(),d("img",{key:0,src:el.customer_service_qr,class:"qr-image"},null,8,Ae)):(i(),c(a,{key:1,class:"qr-uploader-icon"},{default:r(()=>[o(n(Y))]),_:1}))]),_:1},8,["action"])]),_:1})]),_:1})]),_:1})],64)):p("",!0),o(N,{label:"广告二维码"},{default:r(()=>[o(Ul,{class:"qr-uploader",action:Ne.value,"show-file-list":!1,"on-success":nl,"before-upload":pl},{default:r(()=>[el.ad_qr_code?(i(),d("img",{key:0,src:el.ad_qr_code,class:"qr-image"},null,8,Qe)):(i(),c(a,{key:1,class:"qr-uploader-icon"},{default:r(()=>[o(n(Y))]),_:1}))]),_:1},8,["action"]),l[96]||(l[96]=s("div",{class:"form-tip"},"可选：用于在群组页面显示广告",-1))]),_:1,__:[96]})]),_:1}),o(jl,{class:"config-card",shadow:"never"},{header:r(()=>[s("div",$e,[o(a,null,{default:r(()=>[o(n(J))]),_:1}),l[97]||(l[97]=s("span",null,"快速配置",-1))])]),default:r(()=>[o(N,{label:"应用营销模板"},{default:r(()=>[o(hl,{gutter:12},{default:r(()=>[o(Ge,{span:8},{default:r(()=>[o(kl,{modelValue:Xe.value,"onUpdate:modelValue":l[46]||(l[46]=e=>Xe.value=e),placeholder:"选择营销模板"},{default:r(()=>[(i(!0),d(m,null,v(Je.value,e=>(i(),c(wl,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(Ge,{span:8},{default:r(()=>[o(t,{onClick:ul},{default:r(()=>l[98]||(l[98]=[_("应用模板",-1)])),_:1,__:[98]})]),_:1}),o(Ge,{span:8},{default:r(()=>[o(t,{onClick:tl,disabled:!el.title},{default:r(()=>l[99]||(l[99]=[_("测试城市替换",-1)])),_:1,__:[99]},8,["disabled"])]),_:1})]),_:1}),Ke.value?(i(),d("div",Be,[s("span",null,"测试结果："+M(Ke.value),1)])):p("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),o(Al,{modelValue:Te.value,"onUpdate:modelValue":l[47]||(l[47]=e=>Te.value=e),title:"群组预览",width:"60%"},{default:r(()=>[We.value?(i(),d("div",Pe,[s("div",Ie,[s("h3",null,M(We.value.title),1),s("div",Re,[s("span",null,"阅读 "+M(We.value.read_count_display||"0"),1),s("span",null,"点赞 "+M(We.value.like_count||0),1),s("span",null,"想看 "+M(We.value.want_see_count||0),1)]),s("div",Ye,"¥"+M(We.value.price||el.price),1)]),We.value.group_intro_content?(i(),d("div",De,[s("h4",null,M(We.value.group_intro_title||"群简介"),1),s("p",null,M(We.value.group_intro_content),1)])):p("",!0),We.value.virtual_members&&We.value.virtual_members.length?(i(),d("div",Fe,[s("h4",null,"群成员 ("+M(We.value.virtual_members.length)+"人)",1),s("div",Me,[(i(!0),d(m,null,v(We.value.virtual_members.slice(0,8),e=>(i(),d("div",{key:e.nickname,class:"member-item"},[s("img",{src:e.avatar,alt:e.nickname},null,8,Ze),s("span",null,M(e.nickname),1)]))),128))])])):p("",!0),s("div",Ee,[o(t,{type:"primary",size:"large"},{default:r(()=>[_(M(We.value.button_title||"立即加入群聊"),1)]),_:1})])])):p("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0aad8018"]]);export{Ge as default};
