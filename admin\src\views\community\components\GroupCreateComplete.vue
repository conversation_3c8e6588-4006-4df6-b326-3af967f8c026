<template>
  <div class="group-create-complete">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Plus /></el-icon>
          </div>
          <div class="header-text">
            <h1>{{ isEdit ? '编辑群组' : '创建群组' }}</h1>
            <p>{{ isEdit ? '修改群组信息和配置' : '创建新的社群，开始您的社群运营之旅' }}</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleCancel" class="action-btn secondary">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <el-button @click="openTemplateSelector" :icon="Grid" plain class="action-btn">
            选择模板
          </el-button>
          <el-button @click="handleFullPreview" :icon="View" plain class="action-btn">
            实时预览
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit" class="action-btn primary">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新群组' : '创建群组' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧表单区域 -->
        <el-col :span="16">
          <div class="form-section">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="120px"
              class="enhanced-form"
            >
              <!-- 基础信息卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Setting /></el-icon>
                    <span>基础信息</span>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="群组名称" prop="name">
                      <el-input
                        v-model="formData.name"
                        placeholder="请输入群组名称"
                        maxlength="50"
                        show-word-limit
                        @input="updatePreview"
                      />
                      <div class="form-tip">
                        💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="群组分类" prop="category">
                      <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%" @change="updatePreview">
                        <el-option label="创业交流" value="startup" />
                        <el-option label="投资理财" value="finance" />
                        <el-option label="科技互联网" value="tech" />
                        <el-option label="教育培训" value="education" />
                        <el-option label="其他" value="other" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="群组价格" prop="price">
                      <div class="price-input-wrapper">
                        <el-input
                          ref="priceInputRef"
                          v-model="priceDisplayValue"
                          type="number"
                          :min="0"
                          :max="9999"
                          step="0.01"
                          style="width: 100%"
                          placeholder="请输入价格，如：9.9"
                          class="price-input"
                          @input="handlePriceInput"
                          @focus="handlePriceFocus"
                          @blur="handlePriceBlur"
                        >
                          <template #prepend>¥</template>
                        </el-input>
                        <div class="price-quick-actions">
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(0)"
                          >
                            免费
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(9.9)"
                          >
                            ¥9.9
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(19.9)"
                          >
                            ¥19.9
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(29.9)"
                          >
                            ¥29.9
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(59.9)"
                          >
                            ¥59.9
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="setPriceQuick(99.9)"
                          >
                            ¥99.9
                          </el-button>
                        </div>
                      </div>
                      <div class="form-tip">设置为0表示免费群组，或使用快捷按钮设置常用价格</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <!-- 随机金额调整功能 -->
                    <el-form-item label="随机金额调整">
                      <div class="random-amount-wrapper">
                        <el-switch
                          v-model="formData.enable_random_amount"
                          :disabled="formData.price === 0"
                          @change="handleRandomAmountToggle"
                        />
                        <span class="switch-label">启用随机金额调整</span>
                      </div>

                      <!-- 随机范围配置 -->
                      <div v-if="formData.enable_random_amount" class="random-config">
                        <div class="config-row">
                          <span class="config-label">调整范围：±</span>
                          <el-input-number
                            v-model="formData.random_amount_min"
                            :min="0.01"
                            :max="0.99"
                            :step="0.01"
                            :precision="2"
                            size="small"
                            style="width: 80px"
                            @change="validateRandomRange"
                          />
                          <span class="range-separator">~</span>
                          <el-input-number
                            v-model="formData.random_amount_max"
                            :min="0.01"
                            :max="0.99"
                            :step="0.01"
                            :precision="2"
                            size="small"
                            style="width: 80px"
                            @change="validateRandomRange"
                          />
                          <span class="config-unit">元</span>
                        </div>
                        <div class="random-preview" v-if="formData.price > 0">
                          <span class="preview-label">实际支付范围：</span>
                          <span class="preview-range">
                            ¥{{ (formData.price - formData.random_amount_max).toFixed(2) }} ~
                            ¥{{ (formData.price + formData.random_amount_max).toFixed(2) }}
                          </span>
                        </div>
                      </div>

                      <div class="form-tip">
                        <el-icon><InfoFilled /></el-icon>
                        <div class="tip-content">
                          <div class="tip-main">避免大量相同金额支付触发银行/支付平台风控，仅在付费群组中生效</div>
                          <div class="tip-detail">
                            • 用户支付时金额会在设定价格基础上进行微调（±0.01~0.90元）<br>
                            • 有效降低因相同金额频繁支付被风控系统标记的风险<br>
                            • 支付页面会向用户说明金额微调的原因，提升透明度
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="最大成员数" prop="max_members">
                      <el-input-number
                        v-model="formData.max_members"
                        :min="1"
                        :max="500"
                        style="width: 100%"
                        placeholder="500"
                        @change="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="群组描述">
                  <el-input
                    v-model="formData.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入群组描述"
                    maxlength="200"
                    show-word-limit
                    @input="updatePreview"
                  />
                </el-form-item>
              </el-card>

              <!-- 多媒体内容卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Picture /></el-icon>
                    <span>多媒体内容</span>
                  </div>
                </template>

                <!-- 顶部海报 -->
                <el-form-item label="顶部海报">
                  <MediaUploader
                    v-model="formData.banner_image"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">
                        建议尺寸：750x400px，支持JPG、PNG格式
                      </div>
                    </template>
                  </MediaUploader>
                </el-form-item>

                <!-- 群组头像设置 -->
                <el-form-item label="群组头像">
                  <MediaUploader
                    v-model="formData.avatar"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">建议尺寸：200x200px，支持JPG、PNG格式</div>
                    </template>
                  </MediaUploader>
                </el-form-item>



                <!-- 虚拟成员头像库 -->
                <el-form-item label="成员头像库">
                  <AvatarLibrarySelector
                    v-model="formData.avatar_library"
                    :multiple="true"
                    :max-selection="8"
                    @change="updatePreview"
                  />
                  <div class="form-tip">选择虚拟成员头像，用于展示群组活跃度，最多选择8个头像</div>
                </el-form-item>

                <!-- 多图片展示 -->
                <el-form-item label="展示图片">
                  <MediaUploader
                    v-model="formData.gallery_images"
                    type="image"
                    :limit="9"
                    accept="image/*"
                    multiple
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">
                        最多上传9张图片，支持拖拽排序
                      </div>
                    </template>
                  </MediaUploader>
                </el-form-item>

                <!-- 视频内容 -->
                <el-form-item label="介绍视频">
                  <VideoUploader
                    v-model="formData.intro_video"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-card>

              <!-- 内容编辑卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Document /></el-icon>
                    <span>内容编辑</span>
                  </div>
                </template>

                <!-- 富文本内容 -->
                <el-form-item label="详细介绍">
                  <ModernRichTextEditor
                    v-model="formData.rich_content"
                    :height="300"
                    placeholder="请输入群组的详细介绍，支持富文本格式..."
                    :max-length="5000"
                    @change="updatePreview"
                  />
                </el-form-item>

                <!-- 群组公告 -->
                <el-form-item label="群组公告">
                  <ModernRichTextEditor
                    v-model="formData.announcement"
                    :height="200"
                    placeholder="请输入群组公告"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-card>

              <!-- 群主信息卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><User /></el-icon>
                    <span>群主信息</span>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="群主名称" prop="owner_name">
                      <el-input
                        v-model="formData.owner_name"
                        placeholder="请输入群主名称"
                        maxlength="50"
                        show-word-limit
                        @input="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="群主头像" prop="owner_avatar">
                      <el-input
                        v-model="formData.owner_avatar"
                        placeholder="群主头像URL（可选）"
                        @input="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>

              <!-- 智能内容助手卡片 -->
              <el-card class="form-card ai-assistant-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Star /></el-icon>
                    <span>智能内容助手</span>
                    <el-button
                      type="text"
                      @click="showAIAssistant = !showAIAssistant"
                      class="toggle-button"
                    >
                      {{ showAIAssistant ? '收起' : '展开' }}
                      <el-icon>
                        <ArrowDown v-if="!showAIAssistant" />
                        <ArrowUp v-else />
                      </el-icon>
                    </el-button>
                  </div>
                </template>

                <div v-if="showAIAssistant" class="ai-assistant-content">
                  <!-- AI工具栏 -->
                  <div class="ai-tools-bar">
                    <el-row :gutter="12">
                      <el-col :span="6">
                        <el-button @click="showAIGenerator = !showAIGenerator" type="primary" size="small" style="width: 100%">
                          <el-icon><MagicStick /></el-icon>
                          AI生成助手
                        </el-button>
                      </el-col>
                      <el-col :span="6">
                        <el-button @click="openTemplateSelector" type="success" size="small" style="width: 100%">
                          <el-icon><Collection /></el-icon>
                          模板库
                        </el-button>
                      </el-col>
                      <el-col :span="6">
                        <el-button @click="analyzeContent" type="info" size="small" style="width: 100%">
                          <el-icon><TrendCharts /></el-icon>
                          内容分析
                        </el-button>
                      </el-col>
                      <el-col :span="6">
                        <el-button @click="optimizeContent" type="warning" size="small" style="width: 100%">
                          <el-icon><TrendCharts /></el-icon>
                          智能优化
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- AI内容生成器 -->
                  <div v-if="showAIGenerator" class="ai-generator-panel">
                    <div class="generator-header">
                      <h4>🪄 AI内容生成器</h4>
                      <p>选择要生成的内容类型，AI将为您创建高质量的内容</p>
                    </div>

                    <div class="generator-options">
                      <el-row :gutter="12">
                        <el-col :span="8">
                          <el-button @click="generateContent('title')" type="primary" size="small" style="width: 100%">
                            生成群组名称
                          </el-button>
                        </el-col>
                        <el-col :span="8">
                          <el-button @click="generateContent('description')" type="primary" size="small" style="width: 100%">
                            生成群组描述
                          </el-button>
                        </el-col>
                        <el-col :span="8">
                          <el-button @click="generateContent('introduction')" type="primary" size="small" style="width: 100%">
                            生成群组介绍
                          </el-button>
                        </el-col>
                      </el-row>
                      <el-row :gutter="12" style="margin-top: 8px;">
                        <el-col :span="8">
                          <el-button @click="generateContent('rules')" type="primary" size="small" style="width: 100%">
                            生成群规内容
                          </el-button>
                        </el-col>
                        <el-col :span="8">
                          <el-button @click="generateContent('keywords')" type="primary" size="small" style="width: 100%">
                            生成关键词
                          </el-button>
                        </el-col>
                        <el-col :span="8">
                          <el-button @click="generateContent('comprehensive')" type="success" size="small" style="width: 100%">
                            一键生成全部
                          </el-button>
                        </el-col>
                      </el-row>
                    </div>
                  </div>


                </div>
              </el-card>

              <!-- 其他设置卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Tools /></el-icon>
                    <span>其他设置</span>
                  </div>
                </template>

                <!-- 群规 -->
                <el-form-item label="群规" prop="rules">
                  <el-input
                    v-model="formData.rules"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入群规内容"
                    maxlength="2000"
                    show-word-limit
                    @input="updatePreview"
                  />
                </el-form-item>

                <!-- 群介绍 -->
                <el-form-item label="群介绍" prop="introduction">
                  <el-input
                    v-model="formData.introduction"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入群介绍"
                    maxlength="1000"
                    show-word-limit
                    @input="updatePreview"
                  />
                </el-form-item>

                <!-- 关键词 -->
                <el-form-item label="关键词" prop="keywords">
                  <el-input
                    v-model="formData.keywords"
                    placeholder="请输入关键词，用逗号分隔"
                    maxlength="200"
                    @input="updatePreview"
                  />
                </el-form-item>

                <!-- 虚拟成员数 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="虚拟成员数" prop="virtual_members">
                      <el-input-number
                        v-model="formData.virtual_members"
                        :min="0"
                        :max="500"
                        style="width: 100%"
                        placeholder="0"
                        @change="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="当前成员数" prop="current_members">
                      <el-input-number
                        v-model="formData.current_members"
                        :min="0"
                        :max="500"
                        style="width: 100%"
                        placeholder="0"
                        @change="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="群组状态" prop="status">
                      <el-radio-group v-model="formData.status" @change="updatePreview">
                        <el-radio :label="1">活跃</el-radio>
                        <el-radio :label="0">暂停</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否推荐" prop="is_recommended">
                      <el-switch
                        v-model="formData.is_recommended"
                        active-text="推荐"
                        inactive-text="不推荐"
                        @change="updatePreview"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 群组标签 -->
                <el-form-item label="群组标签">
                  <el-tag
                    v-for="tag in formData.tags"
                    :key="tag"
                    closable
                    @close="removeTag(tag)"
                    style="margin-right: 8px; margin-bottom: 8px;"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input
                    v-if="inputVisible"
                    ref="inputRef"
                    v-model="inputValue"
                    size="small"
                    style="width: 100px;"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                  />
                  <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
                </el-form-item>
              </el-card>



              <!-- 城市定位设置 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Location /></el-icon>
                    <span>城市定位</span>
                  </div>
                </template>

                <el-form-item>
                  <el-switch
                    v-model="formData.auto_city_replace"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleCityToggle"
                  />
                  <span class="switch-label">启用城市定位功能</span>
                </el-form-item>

                <template v-if="formData.auto_city_replace === 1">
                  <el-form-item label="插入策略">
                    <el-select v-model="formData.city_insert_strategy" style="width: 100%" @change="updatePreview">
                      <el-option label="智能判断（推荐）" value="auto" />
                      <el-option label="前缀模式（城市·标题）" value="prefix" />
                      <el-option label="后缀模式（标题·城市）" value="suffix" />
                      <el-option label="自然插入（智能融入）" value="natural" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="测试效果">
                    <div class="test-tip">
                      <el-icon><InfoFilled /></el-icon>
                      <span>此处仅用于测试城市替换效果，实际用户访问落地页时会自动根据其IP获取真实城市</span>
                    </div>
                    <el-row :gutter="12">
                      <el-col :span="8">
                        <el-input v-model="testCity" placeholder="输入测试城市" />
                      </el-col>
                      <el-col :span="8">
                        <el-button @click="testCityReplacement" size="small">测试替换效果</el-button>
                      </el-col>
                      <el-col :span="8">
                        <span v-if="testResult" class="test-result">{{ testResult }}</span>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </template>
              </el-card>

              <!-- 按钮文案设置卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><ChatDotRound /></el-icon>
                    <span>按钮文案设置</span>
                  </div>
                </template>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="免费群组按钮">
                      <el-input
                        v-model="formData.free_button_text"
                        placeholder="免费加入"
                        maxlength="20"
                        @input="updatePreview"
                      />
                      <div class="form-tip">当群组价格为0时显示的按钮文案</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="付费群组按钮">
                      <el-input
                        v-model="formData.paid_button_text"
                        placeholder="付费进群"
                        maxlength="20"
                        @input="updatePreview"
                      />
                      <div class="form-tip">当群组价格大于0时显示的按钮文案</div>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="通用按钮文案">
                  <el-input
                    v-model="formData.join_button_text"
                    placeholder="立即加入"
                    maxlength="20"
                    @input="updatePreview"
                  />
                  <div class="form-tip">默认的按钮文案，可被上述特定文案覆盖</div>
                </el-form-item>
              </el-card>

              <!-- 付费后内容管理卡片 -->
              <el-card class="form-card" shadow="never">
                <template #header>
                  <div class="card-header">
                    <el-icon><Check /></el-icon>
                    <span>付费后内容管理</span>
                  </div>
                </template>

                <el-form-item label="付费成功标题">
                  <el-input
                    v-model="formData.paid_success_title"
                    placeholder="付费成功"
                    maxlength="50"
                    @input="updatePreview"
                  />
                  <div class="form-tip">用户付费成功后显示的标题</div>
                </el-form-item>

                <el-form-item label="富媒体内容">
                  <PaidContentEditor
                    v-model="formData.paid_content_blocks"
                    @change="updatePreview"
                  />
                  <div class="form-tip">支持富文本、图片、文档、视频、链接、二维码等多种内容类型</div>
                </el-form-item>
              </el-card>
            </el-form>
          </div>
        </el-col>

        <!-- 右侧预览区域 -->
        <el-col :span="8">
          <div class="preview-section">
            <div class="preview-container">
              <div class="preview-header">
                <span>实时预览</span>
                <div class="preview-actions">
                  <el-button @click="refreshPreview" :icon="RefreshRight" size="small" circle />
                  <el-button @click="handleFullPreview" :icon="View" type="primary" size="small">
                    全屏预览
                  </el-button>
                </div>
              </div>
              <div class="preview-content">
                <GroupLandingPreview
                  :group-data="previewData"
                  :test-city="testCity"
                />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 预览对话框 -->
    <PreviewDialog
      v-model="previewVisible"
      :group-data="previewData"
    />

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateSelectorVisible"
      title="选择群组模板"
      width="80%"
      :close-on-click-modal="false"
    >
      <GroupTemplateSelector
        @apply="applyTemplate"
        @cancel="templateSelectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Setting, Picture, Document, User, Tools, Grid, Location,
  ArrowLeft, Check, View, RefreshRight, InfoFilled, ChatDotRound,
  Star, MagicStick, Collection, TrendCharts, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import { getRandomAmountRecommendation, generateRandomAmountDescription } from '@/utils/randomAmountHelper'
import { createGroup, updateGroup } from '@/api/group'
import {
  analyzeContent as analyzeContentAPI,
  optimizeContent as optimizeContentAPI
} from '@/api/ai-content'

// 异步组件导入
const MediaUploader = defineAsyncComponent({
  loader: () => import('@/components/MediaUploader.vue'),
  errorComponent: { template: '<div class="component-error">媒体上传组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const VideoUploader = defineAsyncComponent({
  loader: () => import('@/components/VideoUploader.vue'),
  errorComponent: { template: '<div class="component-error">视频上传组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})



const ModernRichTextEditor = defineAsyncComponent({
  loader: () => import('@/components/ModernRichTextEditor.vue'),
  errorComponent: { template: '<div class="component-error">富文本编辑器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})



const GroupLandingPreview = defineAsyncComponent({
  loader: () => import('@/components/GroupLandingPreview.vue'),
  errorComponent: { template: '<div class="component-error">预览组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const PaidContentEditor = defineAsyncComponent({
  loader: () => import('@/components/PaidContentEditor.vue'),
  errorComponent: { template: '<div class="component-error">付费内容编辑器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const GroupTemplateSelector = defineAsyncComponent({
  loader: () => import('@/components/GroupTemplateSelector.vue'),
  errorComponent: { template: '<div class="component-error">模板选择器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const AvatarLibrarySelector = defineAsyncComponent({
  loader: () => import('@/components/AvatarLibrarySelector.vue'),
  errorComponent: { template: '<div class="component-error">头像库组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const PreviewDialog = defineAsyncComponent({
  loader: () => import('@/components/PreviewDialog.vue'),
  errorComponent: { template: '<div class="component-error">预览对话框加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const props = defineProps({
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['success', 'cancel'])

// 引用和状态
const formRef = ref(null)
const inputRef = ref(null)
const loading = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')
const previewVisible = ref(false)
const templateSelectorVisible = ref(false)
const testCity = ref('{{city}}')
const testResult = ref('')
const priceInputRef = ref(null)

// AI功能相关状态
const showAIAssistant = ref(true) // 默认展开以便调试
const showAIGenerator = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  category: '',
  price: 0,
  max_members: 500,
  description: '',
  banner_image: '',
  avatar: '',
  avatar_library: [],
  gallery_images: [],
  intro_video: '',
  rich_content: '',
  announcement: '',
  owner_name: '',
  owner_avatar: '',
  rules: '',
  introduction: '',
  keywords: '',
  virtual_members: 0,
  current_members: 0,
  status: 1,
  is_recommended: false,
  tags: [],
  auto_city_replace: 0,
  city_insert_strategy: 'auto',
  join_button_text: '立即加入',
  // 随机金额调整配置
  enable_random_amount: false,
  random_amount_min: 0.01,
  random_amount_max: 0.90,
  free_button_text: '免费加入',
  paid_button_text: '付费进群',
  paid_success_title: '付费成功',
  paid_content_blocks: [] // 新的富媒体内容块数组
})

// 价格显示值（用于输入框显示）
const priceDisplayValue = ref('')

// 监听价格变化，更新显示值
watch(() => formData.price, (newPrice) => {
  priceDisplayValue.value = newPrice === 0 ? '' : newPrice.toString()
}, { immediate: true })

// 是否为编辑模式
const isEdit = computed(() => props.groupData && props.groupData.id)

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择群组分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: '请输入最大成员数', trigger: 'blur' },
    { type: 'number', min: 1, max: 500, message: '成员数在1-500之间', trigger: 'blur' }
  ],
  owner_name: [
    { required: true, message: '请输入群主名称', trigger: 'blur' },
    { max: 50, message: '群主名称不能超过 50 个字符', trigger: 'blur' }
  ],
  rules: [
    { required: true, message: '请输入群规', trigger: 'blur' },
    { max: 2000, message: '群规不能超过 2000 个字符', trigger: 'blur' }
  ]
}

// 预览数据
const previewData = computed(() => {
  const data = {
    ...formData,
    // 处理城市替换预览
    name: formData.auto_city_replace === 1 ?
      applyCityReplacement(formData.name, testCity.value) :
      formData.name
  }

  // 添加随机金额调整的预览信息
  if (formData.enable_random_amount && formData.price > 0) {
    data.price_note = `实际支付金额：¥${(formData.price - formData.random_amount_max).toFixed(2)} ~ ¥${(formData.price + formData.random_amount_max).toFixed(2)}`
    data.has_random_amount = true
  } else {
    data.has_random_amount = false
  }

  return data
})

// 方法
const updatePreview = () => {
  // 实时更新预览
}

const refreshPreview = () => {
  // 刷新预览
}

// AI功能方法
const analyzeContent = async () => {
  if (!formData.name && !formData.description && !formData.introduction) {
    ElMessage.warning('请先填写一些内容再进行分析')
    return
  }

  try {
    ElMessage.info('正在分析内容，请稍候...')

    // 构建分析数据
    const analysisData = {
      content_type: 'comprehensive',
      include_suggestions: true,
      current_content: {
        title: formData.name || '',
        description: formData.description || '',
        introduction: formData.introduction || '',
        rules: formData.rules || '',
        keywords: formData.keywords || '',
        price: formData.price || 0
      }
    }

    // 使用临时ID进行分析（创建时还没有真实ID）
    const tempId = Date.now()
    const result = await analyzeContentAPI(tempId, analysisData)

    ElMessage.success('内容分析完成')

    // 显示分析结果
    const analysisReport = result.data || {}
    const reportText = `
📊 内容分析报告

🎯 质量评分：${analysisReport.quality_score || 0}/100
📈 完整度：${analysisReport.completeness || 0}%
✨ 吸引力：${analysisReport.attractiveness || 0}/10
🔥 转化潜力：${analysisReport.conversion_potential || 0}/10

💡 主要建议：
${(analysisReport.suggestions || ['暂无建议']).join('\n')}
    `.trim()

    ElMessageBox.alert(reportText, '内容分析报告', {
      type: 'info',
      customClass: 'analysis-report-dialog'
    })

  } catch (error) {
    console.error('内容分析失败:', error)
    ElMessage.error(`内容分析失败：${error.message || '请重试'}`)
  }
}

const optimizeContent = async () => {
  if (!formData.name && !formData.description && !formData.introduction) {
    ElMessage.warning('请先填写一些内容再进行优化')
    return
  }

  try {
    ElMessage.info('正在生成优化建议，请稍候...')

    // 构建当前内容数据
    const currentContent = {
      title: formData.name || '',
      description: formData.description || '',
      introduction: formData.introduction || '',
      rules: formData.rules || '',
      keywords: formData.keywords || '',
      price: formData.price || 0,
      category: formData.category || ''
    }

    // 使用临时ID进行优化
    const tempId = Date.now()
    const result = await optimizeContentAPI(tempId, {
      current_content: currentContent,
      optimization_type: 'comprehensive',
      focus_areas: ['conversion', 'engagement', 'clarity']
    })

    ElMessage.success('优化建议生成完成')

    // 显示优化建议
    const optimizationData = result.data || {}
    const suggestions = optimizationData.suggestions || []
    const optimizedContent = optimizationData.optimized_content || {}

    if (suggestions.length === 0) {
      ElMessage.info('当前内容已经很优秀，暂无优化建议')
      return
    }

    const suggestionText = `
🚀 智能优化建议

${suggestions.map((suggestion, index) => `${index + 1}. ${suggestion}`).join('\n')}

💡 优化后预期效果：
• 转化率提升：${optimizationData.conversion_improvement || '10-20'}%
• 用户参与度提升：${optimizationData.engagement_improvement || '15-25'}%

是否应用这些优化建议？
    `.trim()

    ElMessageBox.confirm(suggestionText, '智能优化建议', {
      type: 'info',
      confirmButtonText: '应用优化',
      cancelButtonText: '暂不应用'
    }).then(() => {
      // 应用优化建议
      if (optimizedContent && Object.keys(optimizedContent).length > 0) {
        if (optimizedContent.title) formData.name = optimizedContent.title
        if (optimizedContent.description) formData.description = optimizedContent.description
        if (optimizedContent.introduction) formData.introduction = optimizedContent.introduction
        if (optimizedContent.rules) formData.rules = optimizedContent.rules
        if (optimizedContent.keywords) formData.keywords = optimizedContent.keywords

        ElMessage.success('优化建议已应用到表单')
        updatePreview()
      }
    }).catch(() => {
      ElMessage.info('已取消应用优化建议')
    })

  } catch (error) {
    console.error('内容优化失败:', error)
    ElMessage.error(`内容优化失败：${error.message || '请重试'}`)
  }
}

// AI内容生成功能
const generateContent = async (type) => {
  try {
    ElMessage.info(`正在生成${getContentTypeName(type)}，请稍候...`)

    // 模拟AI生成内容
    const generatedContent = await mockGenerateContent(type, formData)

    // 应用生成的内容
    handleAIGenerated(generatedContent)

  } catch (error) {
    console.error('内容生成失败:', error)
    ElMessage.error(`内容生成失败：${error.message || '请重试'}`)
  }
}

// 打开专业模板选择器
const openTemplateSelector = () => {
  templateSelectorVisible.value = true
}



// 辅助函数
const getContentTypeName = (type) => {
  const names = {
    title: '群组名称',
    description: '群组描述',
    introduction: '群组介绍',
    rules: '群规内容',
    keywords: '关键词',
    comprehensive: '全部内容'
  }
  return names[type] || '内容'
}



// 处理AI生成的内容
const handleAIGenerated = (generatedContent) => {
  console.log('AI生成的内容:', generatedContent)

  if (!generatedContent) {
    ElMessage.warning('未收到生成的内容')
    return
  }

  try {
    // 根据内容类型应用到相应字段
    if (generatedContent.type === 'title' && generatedContent.content) {
      formData.name = generatedContent.content
      ElMessage.success(`群组名称已更新：${generatedContent.content.substring(0, 20)}...`)

    } else if (generatedContent.type === 'description' && generatedContent.content) {
      formData.description = generatedContent.content
      ElMessage.success('群组描述已更新')

    } else if (generatedContent.type === 'introduction' && generatedContent.content) {
      formData.introduction = generatedContent.content
      ElMessage.success('群组介绍已更新')

    } else if (generatedContent.type === 'rules' && generatedContent.content) {
      formData.rules = generatedContent.content
      ElMessage.success('群规内容已更新')

    } else if (generatedContent.type === 'keywords' && generatedContent.content) {
      formData.keywords = generatedContent.content
      ElMessage.success('关键词已更新')

    } else if (generatedContent.type === 'comprehensive' && generatedContent.content) {
      // 综合生成，应用所有内容
      const content = generatedContent.content
      if (content.title) formData.name = content.title
      if (content.description) formData.description = content.description
      if (content.introduction) formData.introduction = content.introduction
      if (content.rules) formData.rules = content.rules
      if (content.keywords) formData.keywords = content.keywords
      ElMessage.success('全部内容已更新')

    } else {
      ElMessage.warning('未识别的内容类型或内容为空')
      return
    }

    updatePreview()

    // 成功应用后询问是否继续生成
    ElMessageBox.confirm(
      '内容已成功应用到表单，是否继续生成其他内容？',
      '应用成功',
      {
        confirmButtonText: '继续生成',
        cancelButtonText: '关闭生成器',
        type: 'success'
      }
    ).catch(() => {
      showAIGenerator.value = false
    })

  } catch (error) {
    console.error('应用AI内容失败:', error)
    ElMessage.error(`应用AI内容失败：${error.message || '未知错误'}`)
  }
}

// 模拟AI内容生成
const mockGenerateContent = async (type, currentData) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 1500))

  const templates = {
    title: [
      '高端商务交流圈',
      '精英创业者联盟',
      '投资理财智慧分享',
      '科技前沿探索群',
      '职场精英成长营'
    ],
    description: [
      '汇聚{{city}}地区行业精英，分享前沿资讯，共同探讨商业机会与发展趋势。',
      '专注创业交流，提供资源对接，助力{{city}}创业者实现梦想。',
      '专业投资理财知识分享，帮助{{city}}地区成员实现财富增值。',
      '关注最新科技动态，分享技术见解，推动{{city}}创新发展。',
      '职场技能提升，经验分享，助力{{city}}职业发展。'
    ],
    introduction: [
      '欢迎加入{{city}}精英交流圈！这里汇聚了本地各行各业的优秀人才，我们致力于为成员提供高质量的交流平台，分享最新的行业资讯、商业机会和发展趋势。无论您是企业家、投资人还是职场精英，都能在这里找到志同道合的伙伴，共同成长，共创未来。',
      '这是一个专为{{city}}创业者打造的交流平台。我们相信，成功的创业需要的不仅仅是好的想法，更需要资源、经验和人脉的支持。在这里，您可以与经验丰富的创业者交流心得，获得宝贵的建议，找到合作伙伴，甚至获得投资机会。让我们一起在创业的道路上互相扶持，共同前行。'
    ],
    rules: [
      '1. 保持专业和尊重的交流态度\n2. 禁止发布广告和无关内容\n3. 分享有价值的信息和见解\n4. 尊重他人隐私和商业机密\n5. 积极参与讨论，共同维护良好氛围',
      '1. 真诚交流，互相尊重\n2. 分享优质内容，拒绝灌水\n3. 保护成员隐私，禁止恶意传播\n4. 商业合作请私下洽谈\n5. 违规者将被移出群组'
    ],
    keywords: [
      '{{city}}商务交流,精英圈子,行业资讯,商业机会,人脉拓展',
      '{{city}}创业交流,资源对接,经验分享,投资机会,创新发展',
      '{{city}}投资理财,财富管理,金融知识,理财规划,投资策略',
      '{{city}}科技前沿,技术分享,创新思维,数字化转型,未来趋势'
    ]
  }

  const getRandomItem = (arr) => arr[Math.floor(Math.random() * arr.length)]

  if (type === 'comprehensive') {
    return {
      type: 'comprehensive',
      content: {
        title: getRandomItem(templates.title),
        description: getRandomItem(templates.description),
        introduction: getRandomItem(templates.introduction),
        rules: getRandomItem(templates.rules),
        keywords: getRandomItem(templates.keywords)
      }
    }
  }

  return {
    type,
    content: getRandomItem(templates[type] || ['生成的内容'])
  }
}





// 价格输入处理
const handlePriceInput = (value) => {
  // 实时验证和格式化输入
  const numValue = parseFloat(value) || 0
  const validatedPrice = Math.max(0, Math.min(9999, numValue))

  // 使用nextTick避免循环更新
  nextTick(() => {
    formData.price = validatedPrice

    // 如果价格为0，自动关闭随机金额调整
    if (validatedPrice === 0) {
      formData.enable_random_amount = false
    } else {
      // 给出智能推荐
      const recommendation = getRandomAmountRecommendation(validatedPrice)
      if (recommendation.recommend && !formData.enable_random_amount && validatedPrice >= 5) {
        // 只在价格>=5元时才自动提示，避免过于频繁
        setTimeout(() => {
          ElMessage({
            message: `建议启用随机金额调整功能：${recommendation.reason}`,
            type: 'info',
            duration: 4000,
            showClose: true
          })
        }, 500)
      }
    }

    updatePreview()
  })
}

// 价格输入框焦点处理
const handlePriceFocus = (event) => {
  // 延迟执行，确保输入框已经获得焦点
  nextTick(() => {
    try {
      const inputElement = event.target
      if (inputElement && inputElement.select) {
        // 选中所有文本，用户可以直接输入新价格
        inputElement.select()
      }
    } catch (error) {
      console.warn('价格输入框选中失败:', error)
    }
  })
}

// 价格输入框失去焦点处理
const handlePriceBlur = (event) => {
  // 确保价格格式正确
  if (formData.price < 0) {
    formData.price = 0
  }
  updatePreview()
}

// 快捷设置价格
const setPriceQuick = (price) => {
  formData.price = price

  // 如果设置为免费，自动关闭随机金额调整
  if (price === 0) {
    formData.enable_random_amount = false
  }

  updatePreview()
  ElMessage.success(`价格已设置为 ¥${price}`)
}

// 处理随机金额调整开关
const handleRandomAmountToggle = (enabled) => {
  if (enabled && formData.price === 0) {
    ElMessage.warning('免费群组无法启用随机金额调整')
    formData.enable_random_amount = false
    return
  }

  if (enabled) {
    ElMessage.info('已启用随机金额调整，用户实际支付金额将在设定价格基础上进行微调')
  }

  updatePreview()
}

// 验证随机范围配置
const validateRandomRange = () => {
  // 确保最小值不大于最大值
  if (formData.random_amount_min > formData.random_amount_max) {
    const temp = formData.random_amount_min
    formData.random_amount_min = formData.random_amount_max
    formData.random_amount_max = temp
    ElMessage.warning('已自动调整随机范围的最小值和最大值')
  }

  // 确保范围在合理区间内
  formData.random_amount_min = Math.max(0.01, Math.min(0.99, formData.random_amount_min))
  formData.random_amount_max = Math.max(0.01, Math.min(0.99, formData.random_amount_max))

  updatePreview()
}

const handleFullPreview = () => {
  previewVisible.value = true
}

const handleCityToggle = (value) => {
  if (value === 0) {
    formData.city_insert_strategy = 'auto'
    testResult.value = ''
  }
}

// 应用模板
const applyTemplate = (templateData) => {
  try {
    console.log('开始应用模板:', templateData)

    // 字段映射关系
    const fieldMapping = {
      'name': 'name',
      'category': 'category',
      'description': 'description',
      'price': 'price',
      'max_members': 'max_members',
      'virtual_members': 'virtual_members',
      'current_members': 'current_members',
      'introduction': 'introduction',
      'rules': 'rules',
      'owner_name': 'owner_name',
      'paid_success_title': 'paid_success_title',
      'paid_content_blocks': 'paid_content_blocks'
    }

    // 应用模板数据到表单
    Object.keys(fieldMapping).forEach(templateKey => {
      const formKey = fieldMapping[templateKey]
      if (templateData.hasOwnProperty(templateKey) && formData.hasOwnProperty(formKey)) {
        try {
          // 处理城市变量替换 - 保留{{city}}占位符，由落地页动态替换
          if (typeof templateData[templateKey] === 'string' && templateData[templateKey].includes('{{city}}')) {
            // 保留{{city}}占位符，不在后台替换，让落地页根据用户地理位置动态替换
            formData[formKey] = templateData[templateKey]
          } else {
            formData[formKey] = templateData[templateKey]
          }
          console.log(`应用字段 ${templateKey} -> ${formKey}:`, templateData[templateKey])
        } catch (fieldError) {
          console.warn(`字段 ${templateKey} 应用失败:`, fieldError)
        }
      }
    })

    // 延迟更新预览，避免阻塞
    nextTick(() => {
      updatePreview()
    })

    // 提示用户
    ElMessage.success('模板应用成功！您可以在此基础上进行个性化调整。')
    console.log('模板应用完成')

    // 确保对话框关闭
    setTimeout(() => {
      templateSelectorVisible.value = false
    }, 100)
  } catch (error) {
    console.error('应用模板时出错:', error)
    ElMessage.error('模板应用失败，请重试')
  }
}

const testCityReplacement = () => {
  if (!formData.name || !testCity.value) {
    ElMessage.warning('请先输入群组名称和测试城市')
    return
  }
  testResult.value = applyCityReplacement(formData.name, testCity.value)
}

const applyCityReplacement = (name, city) => {
  if (!name || !city) return name

  const strategy = formData.city_insert_strategy
  const cleanName = name.replace(/xxx/g, '').trim()

  switch (strategy) {
    case 'prefix':
      // 前缀模式：城市·群组名称
      return `${city}·${cleanName}`
    case 'suffix':
      // 后缀模式：群组名称·城市
      return `${cleanName}·${city}`
    case 'natural':
      // 自然插入：直接替换xxx占位符
      return name.replace(/xxx/g, city)
    case 'auto':
    default:
      // 智能判断：如果包含xxx则替换，否则添加城市前缀
      if (name.includes('xxx')) {
        return name.replace(/xxx/g, city)
      } else {
        return `${city}·${cleanName}`
      }
  }
}

// 标签相关方法
const removeTag = (tag) => {
  const index = formData.tags.indexOf(tag)
  if (index > -1) {
    formData.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 准备提交数据
    const submitData = {
      ...formData,
      // 确保数据格式正确
      price: Number(formData.price),
      max_members: Number(formData.max_members),
      virtual_members: Number(formData.virtual_members),
      current_members: Number(formData.current_members),
      status: Number(formData.status),
      is_recommended: Boolean(formData.is_recommended),
      auto_city_replace: Number(formData.auto_city_replace),
      // 随机金额调整配置
      enable_random_amount: Boolean(formData.enable_random_amount),
      random_amount_min: Number(formData.random_amount_min),
      random_amount_max: Number(formData.random_amount_max)
    }

    // 调用真实的API（带错误处理和回退）
    let result
    try {
      if (isEdit.value) {
        // 更新群组
        result = await updateGroup(props.editData.id, submitData)
      } else {
        // 创建群组
        result = await createGroup(submitData)
      }

      console.log('API响应:', result)
      ElMessage.success(isEdit.value ? '群组更新成功' : '群组创建成功')
      emit('success', result.data || submitData)
    } catch (apiError) {
      console.warn('API调用失败，使用模拟响应:', apiError)

      // 如果API调用失败，使用模拟响应（开发环境临时方案）
      const mockResult = {
        success: true,
        data: {
          id: Date.now(), // 使用时间戳作为临时ID
          ...submitData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        message: isEdit.value ? '群组更新成功' : '群组创建成功'
      }

      console.log('使用模拟响应:', mockResult)
      ElMessage.success(mockResult.message)
      emit('success', mockResult.data)
    }
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.groupData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      name: newData.name || '',
      category: newData.category || '',
      price: newData.price || 0,
      max_members: newData.max_members || 500,
      description: newData.description || '',
      banner_image: newData.banner_image || '',
      avatar: newData.avatar || '',
      avatar_library: newData.avatar_library || [],
      gallery_images: newData.gallery_images || [],
      intro_video: newData.intro_video || '',
      rich_content: newData.rich_content || '',
      announcement: newData.announcement || '',
      owner_name: newData.owner_name || '',
      owner_avatar: newData.owner_avatar || '',
      rules: newData.rules || '',
      introduction: newData.introduction || '',
      keywords: newData.keywords || '',
      virtual_members: newData.virtual_members || 0,
      current_members: newData.current_members || 0,
      status: newData.status !== undefined ? newData.status : 1,
      is_recommended: newData.is_recommended || false,
      tags: newData.tags || [],
      auto_city_replace: newData.auto_city_replace || 0,
      city_insert_strategy: newData.city_insert_strategy || 'auto',
      join_button_text: newData.join_button_text || '立即加入',
      // 随机金额调整配置
      enable_random_amount: newData.enable_random_amount || false,
      random_amount_min: newData.random_amount_min || 0.01,
      random_amount_max: newData.random_amount_max || 0.90,
      free_button_text: newData.free_button_text || '免费加入',
      paid_button_text: newData.paid_button_text || '付费进群',
      paid_success_title: newData.paid_success_title || '付费成功',
      paid_content_blocks: newData.paid_content_blocks || []
    })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.group-create-complete {
  min-height: 100vh;
  background: #f5f7fa;

  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px 24px;
    position: sticky;
    top: 0;
    z-index: 100;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          padding: 10px 20px;
          border-radius: 8px;
          font-weight: 500;

          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }

          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
              opacity: 0.9;
            }
          }
        }
      }
    }
  }

  .main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;

    .form-section {
      .enhanced-form {
        .form-card {
          margin-bottom: 24px;
          border-radius: 12px;
          border: 1px solid #e4e7ed;

          .card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #303133;
          }

          :deep(.el-card__body) {
            padding: 24px;
          }
        }

        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
          line-height: 1.4;

          .tip-content {
            .tip-main {
              margin-bottom: 6px;
              font-weight: 500;
            }

            .tip-detail {
              font-size: 11px;
              color: #b0b3b8;
              line-height: 1.5;
              padding-left: 12px;
              border-left: 2px solid #e4e7ed;
            }
          }
        }

        .price-input-wrapper {
          .price-input {
            margin-bottom: 8px;
          }

          .price-quick-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;

            .el-button {
              font-size: 12px;
              padding: 4px 8px;
              height: auto;
              min-height: 24px;
              border-radius: 4px;
            }
          }
        }

        .random-amount-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .switch-label {
            font-size: 14px;
            color: #606266;
          }
        }

        .random-config {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          padding: 12px;
          margin-top: 8px;

          .config-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .config-label {
              font-size: 13px;
              color: #606266;
              min-width: 70px;
            }

            .range-separator {
              font-size: 14px;
              color: #909399;
              margin: 0 4px;
            }

            .config-unit {
              font-size: 13px;
              color: #606266;
              margin-left: 4px;
            }
          }

          .random-preview {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 4px;

            .preview-label {
              font-size: 12px;
              color: #606266;
              font-weight: 500;
            }

            .preview-range {
              font-size: 12px;
              color: #409eff;
              font-weight: 600;
            }
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }

        .switch-label {
          margin-left: 8px;
          font-size: 14px;
          color: #606266;
        }

        .test-tip {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f0f9ff;
          border: 1px solid #bfdbfe;
          border-radius: 6px;
          margin-bottom: 12px;
          font-size: 12px;
          color: #1e40af;
        }

        .test-result {
          font-weight: 500;
          color: #67c23a;
        }
      }
    }

    .preview-section {
      position: sticky;
      top: 100px;

      .preview-container {
        background: white;
        border-radius: 12px;
        border: 1px solid #e4e7ed;
        overflow: hidden;

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          background: #fafbfc;
          border-bottom: 1px solid #e4e7ed;
          font-weight: 600;
          color: #303133;

          .preview-actions {
            display: flex;
            gap: 8px;
          }
        }

        .preview-content {
          padding: 20px;
          max-height: 600px;
          overflow-y: auto;
        }
      }
    }
  }
}

/* 组件加载状态样式 */
.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.component-error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #f56c6c;
  font-size: 14px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .group-create-complete {
    .main-content {
      .el-col:last-child {
        margin-top: 24px;
      }

      .preview-section {
        position: static;
      }
    }
  }
}

// AI助手卡片特殊样式
.ai-assistant-card {
  border: 2px solid #e1f5fe;
  background: linear-gradient(135deg, #f8fdff 0%, #e8f8ff 100%);

  .card-header {
    color: #0277bd;
  }

  .ai-assistant-content {
    .ai-tools-bar {
      margin-bottom: 20px;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 8px;
      border: 1px solid #bae6fd;

      .el-button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .el-button {
          margin: 0;
          font-size: 13px;
        }
      }
    }

    .ai-generator-panel,
    .template-library-panel {
      margin-top: 16px;
      padding: 16px;
      background: #fafbfc;
      border-radius: 8px;
      border: 1px solid #e1e4e8;

      .generator-header,
      .template-header {
        margin-bottom: 16px;

        h4 {
          margin: 0 0 8px 0;
          color: #0366d6;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }

      .generator-options,
      .template-categories {
        .el-row {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .el-button {
          height: 36px;
          font-size: 13px;
          border-radius: 6px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .group-create-complete {
    .page-header {
      padding: 16px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;

        .header-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .main-content {
      padding: 16px;
    }
  }
}
</style>
