name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: linkhub_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, dom, filter, gd, iconv, json, mbstring, pdo, redis
        coverage: xdebug

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.testing', '.env');"

    - name: Generate key
      run: php artisan key:generate

    - name: Generate JWT secret
      run: php artisan jwt:secret

    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Create Database
      run: |
        mysql -h 127.0.0.1 -u root -proot -e 'CREATE DATABASE IF NOT EXISTS linkhub_test;'

    - name: Run Migrations
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: linkhub_test
        DB_USERNAME: root
        DB_PASSWORD: root
      run: php artisan migrate --force

    - name: Run Tests
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: linkhub_test
        DB_USERNAME: root
        DB_PASSWORD: root
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
      run: php artisan test --coverage-clover=coverage.xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Run PHP CS Fixer
      run: vendor/bin/pint --test

    - name: Run PHPStan
      run: vendor/bin/phpstan analyse --memory-limit=2G

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Security Audit
      run: composer audit

  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: admin/package-lock.json

    - name: Install Admin Dependencies
      working-directory: ./admin
      run: npm ci

    - name: Build Admin
      working-directory: ./admin
      run: npm run build

    - name: Setup Node.js for Frontend
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install Frontend Dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Build Frontend
      working-directory: ./frontend
      run: npm run build

  deploy:
    needs: [test, code-quality, security, frontend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3

    - name: Deploy to Production
      run: |
        echo "部署到生产环境"
        # 这里添加实际的部署脚本