<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑群组' : '创建群组'"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="enhanced-group-dialog"
    :destroy-on-close="true"
  >
    <div class="dialog-content" v-loading="componentError">
      <!-- 错误提示 -->
      <el-alert
        v-if="componentError"
        title="组件加载失败"
        type="error"
        :description="componentError"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />

      <!-- 左侧表单区域 -->
      <div class="form-section" v-if="!componentError">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          class="enhanced-form"
        >
          <!-- 基础信息卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>基础信息</span>
              </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入群组名称"
                    maxlength="50"
                    show-word-limit
                    @input="updatePreview"
                  />
                  <div class="form-tip">
                    💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="群组分类" prop="category">
                  <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%" @change="updatePreview">
                    <el-option label="创业交流" value="startup" />
                    <el-option label="投资理财" value="finance" />
                    <el-option label="科技互联网" value="tech" />
                    <el-option label="教育培训" value="education" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组价格" prop="price">
                  <el-input-number
                    v-model="formData.price"
                    :min="0"
                    :max="9999"
                    :precision="2"
                    style="width: 100%"
                    placeholder="0.00"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大成员数" prop="max_members">
                  <el-input-number
                    v-model="formData.max_members"
                    :min="1"
                    :max="500"
                    style="width: 100%"
                    placeholder="500"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="群组描述">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入群组描述"
                maxlength="200"
                show-word-limit
                @input="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 多媒体内容卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Picture /></el-icon>
                <span>多媒体内容</span>
              </div>
            </template>

            <!-- 顶部海报 -->
            <el-form-item label="顶部海报">
              <MediaUploader
                v-model="formData.banner_image"
                type="image"
                :limit="1"
                accept="image/*"
                @change="updatePreview"
              >
                <template #tip>
                  <div class="upload-tip">
                    建议尺寸：750x400px，支持JPG、PNG格式
                  </div>
                </template>
              </MediaUploader>
            </el-form-item>

            <!-- 群组头像和二维码 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组头像">
                  <MediaUploader
                    v-model="formData.avatar"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">建议尺寸：200x200px</div>
                    </template>
                  </MediaUploader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二维码">
                  <MediaUploader
                    v-model="formData.qr_code"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">群组二维码图片</div>
                    </template>
                  </MediaUploader>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 虚拟成员头像库 -->
            <el-form-item label="成员头像库">
              <AvatarLibrarySelector
                v-model="formData.avatar_library"
                @change="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 内容编辑卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>内容编辑</span>
              </div>
            </template>

            <!-- 富文本内容 -->
            <el-form-item label="详细介绍">
              <ModernRichTextEditor
                v-model="formData.rich_content"
                :height="300"
                placeholder="请输入群组的详细介绍，支持富文本格式..."
                :max-length="5000"
                @change="updatePreview"
              />
            </el-form-item>

            <!-- 多图片展示 -->
            <el-form-item label="展示图片">
              <MediaUploader
                v-model="formData.gallery_images"
                type="image"
                :limit="9"
                accept="image/*"
                multiple
                @change="updatePreview"
              >
                <template #tip>
                  <div class="upload-tip">
                    最多上传9张图片，支持拖拽排序
                  </div>
                </template>
              </MediaUploader>
            </el-form-item>

            <!-- 视频内容 -->
            <el-form-item label="介绍视频">
              <VideoUploader
                v-model="formData.intro_video"
                @change="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 群主信息卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><User /></el-icon>
                <span>群主信息</span>
              </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群主名称" prop="owner_name">
                  <el-input
                    v-model="formData.owner_name"
                    placeholder="请输入群主名称"
                    maxlength="50"
                    show-word-limit
                    @input="updatePreview"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="群主头像" prop="owner_avatar">
                  <el-input
                    v-model="formData.owner_avatar"
                    placeholder="群主头像URL（可选）"
                    @input="updatePreview"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- 智能内容助手卡片 -->
          <el-card class="form-card ai-assistant-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Star /></el-icon>
                <span>智能内容助手</span>
                <el-button
                  type="text"
                  @click="showAIAssistant = !showAIAssistant"
                  class="toggle-button"
                >
                  {{ showAIAssistant ? '收起' : '展开' }}
                  <el-icon>
                    <ArrowDown v-if="!showAIAssistant" />
                    <ArrowUp v-else />
                  </el-icon>
                </el-button>
              </div>
            </template>

            <div v-if="showAIAssistant" class="ai-assistant-content">
              <!-- AI工具栏 -->
              <div class="ai-tools-bar">
                <el-row :gutter="12">
                  <el-col :span="6">
                    <el-button @click="showAIGenerator = !showAIGenerator" type="primary" size="small" style="width: 100%">
                      <el-icon><MagicStick /></el-icon>
                      AI生成助手
                    </el-button>
                  </el-col>
                  <el-col :span="6">
                    <el-button @click="showTemplateLibrary = !showTemplateLibrary" type="success" size="small" style="width: 100%">
                      <el-icon><Collection /></el-icon>
                      模板库
                    </el-button>
                  </el-col>
                  <el-col :span="6">
                    <el-button @click="analyzeContent" type="info" size="small" style="width: 100%">
                      <el-icon><TrendCharts /></el-icon>
                      内容分析
                    </el-button>
                  </el-col>
                  <el-col :span="6">
                    <el-button @click="optimizeContent" type="warning" size="small" style="width: 100%">
                      <el-icon><TrendCharts /></el-icon>
                      智能优化
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- AI内容生成器 -->
              <div v-if="showAIGenerator" class="ai-generator-panel">
                <el-alert
                  title="AI内容生成器"
                  type="info"
                  description="AI内容生成功能正在加载中..."
                  show-icon
                  :closable="false"
                />
              </div>

              <!-- 模板库 -->
              <div v-if="showTemplateLibrary" class="template-library-panel">
                <el-alert
                  title="内容模板库"
                  type="success"
                  description="内容模板库功能正在加载中..."
                  show-icon
                  :closable="false"
                />
              </div>
            </div>
          </el-card>

          <!-- 其他设置卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Tools /></el-icon>
                <span>其他设置</span>
              </div>
            </template>

            <!-- 群规 -->
            <el-form-item label="群规" prop="rules">
              <el-input
                v-model="formData.rules"
                type="textarea"
                :rows="4"
                placeholder="请输入群规内容"
                maxlength="2000"
                show-word-limit
                @input="updatePreview"
              />
            </el-form-item>

            <!-- 群介绍 -->
            <el-form-item label="群介绍" prop="introduction">
              <el-input
                v-model="formData.introduction"
                type="textarea"
                :rows="3"
                placeholder="请输入群介绍"
                maxlength="1000"
                show-word-limit
                @input="updatePreview"
              />
            </el-form-item>

            <!-- 关键词 -->
            <el-form-item label="关键词" prop="keywords">
              <el-input
                v-model="formData.keywords"
                placeholder="请输入关键词，用逗号分隔"
                maxlength="200"
                @input="updatePreview"
              />
            </el-form-item>

            <!-- 虚拟成员数 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="虚拟成员数" prop="virtual_members">
                  <el-input-number
                    v-model="formData.virtual_members"
                    :min="0"
                    :max="500"
                    style="width: 100%"
                    placeholder="0"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="当前成员数" prop="current_members">
                  <el-input-number
                    v-model="formData.current_members"
                    :min="0"
                    :max="500"
                    style="width: 100%"
                    placeholder="0"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
            </el-row>


            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组状态" prop="status">
                  <el-radio-group v-model="formData.status" @change="updatePreview">
                    <el-radio :label="1">活跃</el-radio>
                    <el-radio :label="0">暂停</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否推荐" prop="is_recommended">
                  <el-switch
                    v-model="formData.is_recommended"
                    active-text="推荐"
                    inactive-text="不推荐"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="群组标签">
              <el-tag
                v-for="tag in formData.tags"
                :key="tag"
                closable
                @close="removeTag(tag)"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="inputVisible"
                ref="inputRef"
                v-model="inputValue"
                size="small"
                style="width: 100px;"
                @keyup.enter="handleInputConfirm"
                @blur="handleInputConfirm"
              />
              <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
            </el-form-item>

            <el-form-item label="群组公告">
              <ModernRichTextEditor
                v-model="formData.announcement"
                :height="200"
                placeholder="请输入群组公告"
                @change="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 布局设计卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Grid /></el-icon>
                <span>布局设计</span>
              </div>
            </template>

            <LayoutDesigner
              v-model="formData.layout_config"
              @change="updatePreview"
            />
          </el-card>

          <!-- 城市定位设置 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Location /></el-icon>
                <span>城市定位</span>
              </div>
            </template>

            <el-form-item>
              <el-switch
                v-model="formData.auto_city_replace"
                :active-value="1"
                :inactive-value="0"
                @change="handleCityToggle"
              />
              <span class="switch-label">启用城市定位功能</span>
            </el-form-item>

            <template v-if="formData.auto_city_replace === 1">
              <el-form-item label="插入策略">
                <el-select v-model="formData.city_insert_strategy" style="width: 100%" @change="updatePreview">
                  <el-option label="智能判断（推荐）" value="auto" />
                  <el-option label="前缀模式（城市·标题）" value="prefix" />
                  <el-option label="后缀模式（标题·城市）" value="suffix" />
                  <el-option label="自然插入（智能融入）" value="natural" />
                </el-select>
              </el-form-item>

              <el-form-item label="测试效果">
                <div class="test-tip">
                  <el-icon><InfoFilled /></el-icon>
                  <span>此处仅用于测试城市替换效果，实际用户访问落地页时会自动根据其IP获取真实城市</span>
                </div>
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-input v-model="testCity" placeholder="输入测试城市" />
                  </el-col>
                  <el-col :span="8">
                    <el-button @click="testCityReplacement" size="small">测试替换效果</el-button>
                  </el-col>
                  <el-col :span="8">
                    <span v-if="testResult" class="test-result">{{ testResult }}</span>
                  </el-col>
                </el-row>
              </el-form-item>
            </template>
          </el-card>
        </el-form>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-section">
        <div class="preview-container">
          <div class="preview-header">
            <span>实时预览</span>
            <div class="preview-actions">
              <el-button @click="refreshPreview" :icon="RefreshRight" size="small" circle />
              <el-button @click="handleFullPreview" :icon="View" type="primary" size="small">
                全屏预览
              </el-button>
            </div>
          </div>
          <div class="preview-content">
            <LandingPagePreview
              :group-data="previewData"
              :layout-config="formData.layout_config"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <PreviewDialog
      v-model="previewVisible"
      :group-data="previewData"
      :layout-config="formData.layout_config"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleFullPreview" :icon="View" plain>
          实时预览
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Setting, Picture, Document, Grid, Location, User, Tools,
  InfoFilled, RefreshRight, View, Star, MagicStick, Collection, TrendCharts, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { createGroup, updateGroup } from '@/api/community'
import {
  analyzeContent as analyzeContentAPI,
  optimizeContent as optimizeContentAPI
} from '@/api/ai-content'

// 组件导入 - 使用异步导入避免加载问题
const MediaUploader = defineAsyncComponent({
  loader: () => import('@/components/MediaUploader.vue'),
  errorComponent: { template: '<div class="component-error">媒体上传组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const VideoUploader = defineAsyncComponent({
  loader: () => import('@/components/VideoUploader.vue'),
  errorComponent: { template: '<div class="component-error">视频上传组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const AIContentGenerator = defineAsyncComponent({
  loader: () => import('@/components/AIContentGenerator.vue'),
  errorComponent: { template: '<div class="component-error">AI内容生成组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const ContentTemplateLibrary = defineAsyncComponent({
  loader: () => import('@/components/ContentTemplateLibrary.vue'),
  errorComponent: { template: '<div class="component-error">内容模板库组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const AvatarLibrarySelector = defineAsyncComponent({
  loader: () => import('@/components/AvatarLibrarySelector.vue'),
  errorComponent: { template: '<div class="component-error">头像库组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const ModernRichTextEditor = defineAsyncComponent({
  loader: () => import('@/components/ModernRichTextEditor.vue'),
  errorComponent: { template: '<div class="component-error">富文本编辑器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const LayoutDesigner = defineAsyncComponent({
  loader: () => import('@/components/LayoutDesigner.vue'),
  errorComponent: { template: '<div class="component-error">布局设计器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const LandingPagePreview = defineAsyncComponent({
  loader: () => import('@/components/LandingPagePreview.vue'),
  errorComponent: { template: '<div class="component-error">预览组件加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const PreviewDialog = defineAsyncComponent({
  loader: () => import('@/components/PreviewDialog.vue'),
  errorComponent: { template: '<div class="component-error">预览对话框加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const userStore = useUserStore()
const formRef = ref(null)
const inputRef = ref(null)
const loading = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')
const previewVisible = ref(false)
const testCity = ref('北京')
const testResult = ref('')
const componentError = ref(null)

// AI功能相关状态
const showAIAssistant = ref(true) // 默认展开以便调试
const showAIGenerator = ref(false)
const showTemplateLibrary = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 是否为编辑模式
const isEdit = computed(() => props.groupData && props.groupData.id)

// 预览数据
const previewData = computed(() => {
  return {
    ...formData,
    title: processedTitle.value,
    formatted_price: formData.price === 0 ? '免费' : `¥${formData.price}`
  }
})

// 城市替换后的标题
const processedTitle = computed(() => {
  if (!formData.name) return ''
  
  if (formData.auto_city_replace !== 1) {
    return formData.name
  }
  
  const currentCity = testCity.value
  let result = formData.name
  
  switch (formData.city_insert_strategy) {
    case 'prefix':
      const cleanTitle = formData.name.replace(/^xxx/, '')
      result = currentCity + '·' + cleanTitle
      break
    case 'suffix':
      const baseTitleSuffix = formData.name.replace(/xxx/, '')
      result = baseTitleSuffix + '·' + currentCity
      break
    case 'natural':
      result = formData.name.replace(/xxx/g, currentCity)
      break
    case 'auto':
      if (formData.name.includes('xxx')) {
        result = formData.name.replace(/xxx/g, currentCity)
      } else {
        result = currentCity + '·' + formData.name
      }
      break
    default:
      result = formData.name.replace(/xxx/g, currentCity)
  }
  
  return result
})

// 表单数据
const formData = reactive({
  // 基础信息
  name: '',
  category: '',
  price: 0,
  max_members: 500,
  description: '',
  
  // 多媒体内容
  banner_image: '',
  avatar: '',
  qr_code: '',
  avatar_library: 'default',
  
  // 富内容编辑
  rich_content: '',
  gallery_images: [],
  intro_video: '',
  
  // 群主信息
  owner_name: '',
  owner_avatar: '',
  
  // 其他设置
  rules: '',
  introduction: '',
  keywords: '',
  current_members: 0,
  virtual_members: 0,
  status: 1,
  is_recommended: false,
  tags: [],
  announcement: '',
  
  // 布局设计
  layout_config: {
    sections: [
      { id: 'banner', name: '顶部海报', visible: true, order: 1 },
      { id: 'info', name: '基础信息', visible: true, order: 2 },
      { id: 'content', name: '详细介绍', visible: true, order: 3 },
      { id: 'gallery', name: '图片展示', visible: true, order: 4 },
      { id: 'video', name: '介绍视频', visible: true, order: 5 },
      { id: 'members', name: '成员展示', visible: true, order: 6 },
      { id: 'qrcode', name: '二维码', visible: true, order: 7 }
    ]
  },
  
  // 城市定位
  auto_city_replace: 0,
  city_insert_strategy: 'auto'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择群组分类', trigger: 'change' }
  ],
  owner_name: [
    { required: true, message: '请输入群主名称', trigger: 'blur' },
    { max: 50, message: '群主名称不能超过 50 个字符', trigger: 'blur' }
  ],
  rules: [
    { required: true, message: '请输入群规', trigger: 'blur' },
    { max: 2000, message: '群规不能超过 2000 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入入群价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: '请输入最大成员数', trigger: 'blur' },
    { type: 'number', min: 1, max: 500, message: '成员数在 1 到 500 之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ],
  introduction: [
    { max: 1000, message: '群介绍不能超过 1000 个字符', trigger: 'blur' }
  ],
  keywords: [
    { max: 200, message: '关键词不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 预览相关方法
const updatePreview = () => {
  // 实时更新预览
}

const refreshPreview = () => {
  // 刷新预览
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    category: '',
    price: 0,
    max_members: 500,
    description: '',
    banner_image: '',
    avatar: '',
    qr_code: '',
    avatar_library: 'default',
    rich_content: '',
    gallery_images: [],
    intro_video: '',
    owner_name: '',
    owner_avatar: '',
    rules: '',
    introduction: '',
    keywords: '',
    current_members: 0,
    virtual_members: 0,
    status: 1,
    is_recommended: false,
    tags: [],
    announcement: '',
    layout_config: {
      sections: [
        { id: 'banner', name: '顶部海报', visible: true, order: 1 },
        { id: 'info', name: '基础信息', visible: true, order: 2 },
        { id: 'content', name: '详细介绍', visible: true, order: 3 },
        { id: 'gallery', name: '图片展示', visible: true, order: 4 },
        { id: 'video', name: '介绍视频', visible: true, order: 5 },
        { id: 'members', name: '成员展示', visible: true, order: 6 },
        { id: 'qrcode', name: '二维码', visible: true, order: 7 }
      ]
    },
    auto_city_replace: 0,
    city_insert_strategy: 'auto'
  })
  updatePreview()
}

// 监听群组数据变化
watch(() => props.groupData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      name: newData.name || newData.title || '',
      category: newData.category || '',
      price: newData.price || 0,
      max_members: newData.max_members || newData.member_limit || 500,
      description: newData.description || '',
      banner_image: newData.banner_image || '',
      avatar: newData.avatar || '',
      qr_code: newData.qr_code || '',
      avatar_library: newData.avatar_library || 'default',
      rich_content: newData.rich_content || '',
      gallery_images: newData.gallery_images || [],
      intro_video: newData.intro_video || '',
      owner_name: newData.owner_name || '',
      owner_avatar: newData.owner_avatar || '',
      rules: newData.rules || '',
      introduction: newData.introduction || '',
      keywords: newData.keywords || '',
      current_members: newData.current_members || 0,
      virtual_members: newData.virtual_members || 0,
      status: newData.status !== undefined ? newData.status : 1,
      is_recommended: newData.is_recommended || false,
      tags: newData.tags || [],
      announcement: newData.announcement || '',
      layout_config: newData.layout_config || {
        sections: [
          { id: 'banner', name: '顶部海报', visible: true, order: 1 },
          { id: 'info', name: '基础信息', visible: true, order: 2 },
          { id: 'content', name: '详细介绍', visible: true, order: 3 },
          { id: 'gallery', name: '图片展示', visible: true, order: 4 },
          { id: 'video', name: '介绍视频', visible: true, order: 5 },
          { id: 'members', name: '成员展示', visible: true, order: 6 },
          { id: 'qrcode', name: '二维码', visible: true, order: 7 }
        ]
      },
      auto_city_replace: newData.auto_city_replace || 0,
      city_insert_strategy: newData.city_insert_strategy || 'auto'
    })
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })





const handleFullPreview = () => {
  previewVisible.value = true
}

// 城市替换相关方法
const handleCityToggle = (value) => {
  if (value) {
    if (!formData.name.includes('xxx')) {
      ElMessage.info('建议在群组名称中使用"xxx"作为城市占位符')
    }
  }
  updatePreview()
}

const testCityReplacement = () => {
  if (!formData.name) {
    ElMessage.warning('请先输入群组名称')
    return
  }
  
  testResult.value = processedTitle.value
  ElMessage.success(`城市替换效果：${processedTitle.value}`)
}

// 显示标签输入框
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 确认添加标签
const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 移除标签
const removeTag = (tag) => {
  const index = formData.tags.indexOf(tag)
  if (index > -1) {
    formData.tags.splice(index, 1)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 准备提交数据，确保字段名称与后端API匹配
    const submitData = { 
      ...formData,
      // 后端API需要的字段映射
      title: processedTitle.value,  // 使用处理后的标题（包含城市替换）
      member_limit: formData.max_members  // 后端使用 member_limit
    }
    
    if (isEdit.value) {
      await updateGroup(props.groupData.id, submitData)
      ElMessage.success('群组更新成功')
    } else {
      await createGroup(submitData)
      ElMessage.success('群组创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

// AI功能方法
const analyzeContent = async () => {
  if (!formData.name && !formData.description && !formData.introduction) {
    ElMessage.warning('请先填写一些内容再进行分析')
    return
  }

  try {
    ElMessage.info('正在分析内容，请稍候...')

    // 构建分析数据
    const analysisData = {
      content_type: 'comprehensive',
      include_suggestions: true,
      current_content: {
        title: formData.name || '',
        description: formData.description || '',
        introduction: formData.introduction || '',
        rules: formData.rules || '',
        keywords: formData.keywords || '',
        price: formData.price || 0
      }
    }

    // 使用临时ID进行分析（创建时还没有真实ID）
    const tempId = Date.now()
    const result = await analyzeContentAPI(tempId, analysisData)

    ElMessage.success('内容分析完成')

    // 显示分析结果
    const analysisReport = result.data || {}
    const reportText = `
📊 内容分析报告

🎯 质量评分：${analysisReport.quality_score || 0}/100
📈 完整度：${analysisReport.completeness || 0}%
✨ 吸引力：${analysisReport.attractiveness || 0}/10
🔥 转化潜力：${analysisReport.conversion_potential || 0}/10

💡 主要建议：
${(analysisReport.suggestions || ['暂无建议']).join('\n')}
    `.trim()

    ElMessageBox.alert(reportText, '内容分析报告', {
      type: 'info',
      customClass: 'analysis-report-dialog'
    })

  } catch (error) {
    console.error('内容分析失败:', error)
    ElMessage.error(`内容分析失败：${error.message || '请重试'}`)
  }
}

const optimizeContent = async () => {
  if (!formData.name && !formData.description && !formData.introduction) {
    ElMessage.warning('请先填写一些内容再进行优化')
    return
  }

  try {
    ElMessage.info('正在生成优化建议，请稍候...')

    // 构建当前内容数据
    const currentContent = {
      title: formData.name || '',
      description: formData.description || '',
      introduction: formData.introduction || '',
      rules: formData.rules || '',
      keywords: formData.keywords || '',
      price: formData.price || 0,
      category: formData.category || ''
    }

    // 使用临时ID进行优化
    const tempId = Date.now()
    const result = await optimizeContentAPI(tempId, {
      current_content: currentContent,
      optimization_type: 'comprehensive',
      focus_areas: ['conversion', 'engagement', 'clarity']
    })

    ElMessage.success('优化建议生成完成')

    // 显示优化建议
    const optimizationData = result.data || {}
    const suggestions = optimizationData.suggestions || []
    const optimizedContent = optimizationData.optimized_content || {}

    if (suggestions.length === 0) {
      ElMessage.info('当前内容已经很优秀，暂无优化建议')
      return
    }

    const suggestionText = `
🚀 智能优化建议

${suggestions.map((suggestion, index) => `${index + 1}. ${suggestion}`).join('\n')}

💡 优化后预期效果：
• 转化率提升：${optimizationData.conversion_improvement || '10-20'}%
• 用户参与度提升：${optimizationData.engagement_improvement || '15-25'}%

是否应用这些优化建议？
    `.trim()

    ElMessageBox.confirm(suggestionText, '智能优化建议', {
      type: 'info',
      confirmButtonText: '应用优化',
      cancelButtonText: '暂不应用'
    }).then(() => {
      // 应用优化建议
      if (optimizedContent && Object.keys(optimizedContent).length > 0) {
        if (optimizedContent.title) formData.name = optimizedContent.title
        if (optimizedContent.description) formData.description = optimizedContent.description
        if (optimizedContent.introduction) formData.introduction = optimizedContent.introduction
        if (optimizedContent.rules) formData.rules = optimizedContent.rules
        if (optimizedContent.keywords) formData.keywords = optimizedContent.keywords

        ElMessage.success('优化建议已应用到表单')
        updatePreview()
      }
    }).catch(() => {
      ElMessage.info('已取消应用优化建议')
    })

  } catch (error) {
    console.error('内容优化失败:', error)
    ElMessage.error(`内容优化失败：${error.message || '请重试'}`)
  }
}

const handleAIGenerated = (generatedContent) => {
  console.log('AI生成的内容:', generatedContent)

  if (!generatedContent) {
    ElMessage.warning('未收到生成的内容')
    return
  }

  try {
    // 根据内容类型应用到相应字段
    if (generatedContent.type === 'title' && generatedContent.content) {
      formData.name = generatedContent.content
      ElMessage.success(`群组名称已更新：${generatedContent.content.substring(0, 20)}...`)

    } else if (generatedContent.type === 'description' && generatedContent.content) {
      formData.description = generatedContent.content
      ElMessage.success('群组描述已更新')

    } else if (generatedContent.type === 'introduction' && generatedContent.content) {
      formData.introduction = generatedContent.content
      ElMessage.success('群组介绍已更新')

    } else if (generatedContent.type === 'rules' && generatedContent.content) {
      formData.rules = generatedContent.content
      ElMessage.success('群规内容已更新')

    } else if (generatedContent.type === 'keywords' && generatedContent.content) {
      formData.keywords = generatedContent.content
      ElMessage.success('关键词已更新')

    } else {
      ElMessage.warning('未识别的内容类型或内容为空')
      return
    }

    updatePreview()

    // 成功应用后询问是否继续生成
    ElMessageBox.confirm(
      '内容已成功应用到表单，是否继续生成其他内容？',
      '应用成功',
      {
        confirmButtonText: '继续生成',
        cancelButtonText: '关闭生成器',
        type: 'success'
      }
    ).catch(() => {
      showAIGenerator.value = false
    })

  } catch (error) {
    console.error('应用AI内容失败:', error)
    ElMessage.error(`应用AI内容失败：${error.message || '未知错误'}`)
  }
}

const handleTemplateSelected = (template) => {
  console.log('选中的模板:', template)

  if (!template) {
    ElMessage.warning('未选择模板')
    return
  }

  try {
    // 显示模板预览和确认对话框
    const templatePreview = `
📋 模板信息
名称：${template.title || '未命名模板'}
分类：${template.category || '通用'}
描述：${template.description || '无描述'}

📝 包含内容：
${template.content?.title ? '✅ 群组名称' : '❌ 群组名称'}
${template.content?.description ? '✅ 群组描述' : '❌ 群组描述'}
${template.content?.introduction ? '✅ 群组介绍' : '❌ 群组介绍'}
${template.content?.rules ? '✅ 群规内容' : '❌ 群规内容'}
${template.content?.keywords ? '✅ 关键词' : '❌ 关键词'}

⚠️ 应用模板将覆盖当前内容，是否继续？
    `.trim()

    ElMessageBox.confirm(templatePreview, '确认应用模板', {
      confirmButtonText: '应用模板',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 应用模板内容到表单
      if (template.content) {
        const templateContent = typeof template.content === 'string'
          ? JSON.parse(template.content)
          : template.content

        let appliedItems = []

        if (templateContent.title) {
          formData.name = templateContent.title
          appliedItems.push('名称')
        }
        if (templateContent.description) {
          formData.description = templateContent.description
          appliedItems.push('描述')
        }
        if (templateContent.introduction) {
          formData.introduction = templateContent.introduction
          appliedItems.push('介绍')
        }
        if (templateContent.rules) {
          formData.rules = templateContent.rules
          appliedItems.push('群规')
        }
        if (templateContent.keywords) {
          formData.keywords = templateContent.keywords
          appliedItems.push('关键词')
        }
        if (templateContent.price !== undefined) {
          formData.price = templateContent.price
          appliedItems.push('价格')
        }
        if (templateContent.category) {
          formData.category = templateContent.category
          appliedItems.push('分类')
        }

        ElMessage.success(`模板"${template.title}"已应用，包含：${appliedItems.join('、')}`)
        showTemplateLibrary.value = false
        updatePreview()
      }
    }).catch(() => {
      ElMessage.info('已取消应用模板')
    })

  } catch (error) {
    console.error('应用模板失败:', error)
    ElMessage.error(`应用模板失败：${error.message || '解析模板内容出错'}`)
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  resetForm()
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.enhanced-group-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }

  .dialog-content {
    display: flex;
    gap: 24px;
    padding: 24px;
    min-height: 600px;
    max-height: 80vh;
    overflow: hidden;

    .form-section {
      flex: 1;
      overflow-y: auto;
      padding-right: 12px;

      .enhanced-form {
        .form-card {
          margin-bottom: 24px;
          border-radius: 12px;
          border: 1px solid #e4e7ed;

          .card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #303133;

            .toggle-button {
              margin-left: auto;
              font-size: 12px;

              .el-icon {
                margin-left: 4px;
              }
            }
          }

          // AI助手卡片特殊样式
          &.ai-assistant-card {
            border: 2px solid #e1f5fe;
            background: linear-gradient(135deg, #f8fdff 0%, #e8f8ff 100%);

            .card-header {
              color: #0277bd;
            }

            .ai-assistant-content {
              .ai-tools-bar {
                margin-bottom: 20px;
                padding: 16px;
                background: #f0f9ff;
                border-radius: 8px;
                border: 1px solid #bae6fd;

                .el-button-group {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 8px;

                  .el-button {
                    margin: 0;
                    font-size: 13px;

                    .el-icon {
                      margin-right: 4px;
                    }
                  }
                }
              }

              .ai-generator-panel,
              .template-library-panel {
                margin-bottom: 20px;
                border-radius: 8px;
                overflow: hidden;
              }
            }
          }

          :deep(.el-card__body) {
            padding: 24px;
          }
        }

        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
          line-height: 1.4;
        }

        .test-tip {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          padding: 12px 16px;
          background: #fff7ed;
          border: 1px solid #fed7aa;
          border-radius: 8px;
          font-size: 13px;
          color: #9a3412;

          .el-icon {
            color: #ea580c;
            font-size: 16px;
          }
        }

        .test-result {
          color: #67c23a;
          font-weight: 500;
          font-size: 14px;
        }

        .switch-label {
          margin-left: 12px;
          font-size: 14px;
          color: #606266;
        }

        .upload-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 8px;
          text-align: center;
        }
      }
    }

    .preview-section {
      width: 400px;
      display: flex;
      flex-direction: column;

      .preview-container {
        background: white;
        border-radius: 12px;
        border: 1px solid #e4e7ed;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;

        .preview-header {
          padding: 16px 20px;
          border-bottom: 1px solid #e4e7ed;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: 600;
          color: #303133;
          flex-shrink: 0;

          .preview-actions {
            display: flex;
            gap: 8px;
            align-items: center;
          }
        }

        .preview-content {
          padding: 20px;
          flex: 1;
          overflow-y: auto;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    background: #fafafa;

    .el-button {
      margin-left: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .enhanced-group-dialog {
    :deep(.el-dialog) {
      width: 90vw;
      max-width: none;
    }

    .dialog-content {
      flex-direction: column;
      max-height: 70vh;

      .form-section {
        max-height: 50vh;
      }

      .preview-section {
        width: 100%;
        max-height: 40vh;
      }
    }
  }
}

@media (max-width: 768px) {
  .enhanced-group-dialog {
    :deep(.el-dialog) {
      width: 95vw;
      margin: 5vh auto;
    }

    .dialog-content {
      padding: 16px;
      gap: 16px;

      .form-section {
        .enhanced-form {
          .form-card {
            margin-bottom: 16px;

            :deep(.el-card__body) {
              padding: 16px;
            }
          }
        }
      }

      .preview-section {
        .preview-container {
          .preview-content {
            padding: 16px;
          }
        }
      }
    }
  }
}

/* 组件加载状态样式 */
.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.component-error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #f56c6c;
  font-size: 14px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

/* AI功能对话框样式 */
:deep(.analysis-report-dialog) {
  .el-message-box__message {
    white-space: pre-line;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.6;
  }
}

:deep(.optimization-dialog) {
  .el-message-box__message {
    white-space: pre-line;
    font-size: 14px;
    line-height: 1.6;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .enhanced-group-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }
}
</style>