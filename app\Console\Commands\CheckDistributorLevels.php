<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CommissionService;
use Illuminate\Support\Facades\Log;

/**
 * 分销商等级检查定时任务
 */
class CheckDistributorLevels extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'distributor:check-levels 
                           {--batch=50 : 每批处理的分销商数量}
                           {--force : 强制检查所有分销商，包括非活跃的}';

    /**
     * 命令描述
     */
    protected $description = '检查并自动升级分销商等级';

    protected CommissionService $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        parent::__construct();
        $this->commissionService = $commissionService;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始检查分销商等级...');
        
        $batchSize = (int) $this->option('batch');
        $force = $this->option('force');
        
        try {
            $result = $this->performLevelCheck($batchSize, $force);
            
            $this->info('分销商等级检查完成！');
            $this->displayResults($result);
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('分销商等级检查失败: ' . $e->getMessage());
            Log::error('分销商等级检查命令执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 执行等级检查
     */
    private function performLevelCheck(int $batchSize, bool $force): array
    {
        // 获取需要检查的分销商
        $query = \App\Models\User::where('is_distributor', true);
        
        if (!$force) {
            $query->where('distributor_status', 1); // 只检查活跃的分销商
        }
        
        $distributors = $query->get();
        $total = $distributors->count();
        
        if ($total === 0) {
            $this->info('没有需要检查的分销商');
            return ['checked' => 0, 'upgraded' => 0, 'errors' => []];
        }
        
        $this->info("找到 {$total} 个分销商需要检查");
        
        $results = [
            'checked' => 0,
            'upgraded' => 0,
            'errors' => [],
        ];
        
        $progressBar = $this->output->createProgressBar($total);
        $progressBar->start();
        
        // 分批处理
        foreach ($distributors->chunk($batchSize) as $batch) {
            foreach ($batch as $distributor) {
                $oldLevel = $distributor->distributor_level ?? 1;
                
                if ($this->commissionService->checkAndUpgradeDistributorLevel($distributor)) {
                    $results['checked']++;
                    
                    // 检查是否有等级变化
                    $newLevel = $distributor->fresh()->distributor_level ?? 1;
                    if ($newLevel > $oldLevel) {
                        $results['upgraded']++;
                        $this->line("\n分销商 {$distributor->name} (ID: {$distributor->id}) 等级从 LV{$oldLevel} 升级到 LV{$newLevel}");
                    }
                } else {
                    $results['errors'][] = "分销商 {$distributor->id} 等级检查失败";
                }
                
                $progressBar->advance();
            }
            
            // 批次间稍作延迟
            usleep(100000); // 0.1秒
        }
        
        $progressBar->finish();
        $this->newLine();
        
        return $results;
    }

    /**
     * 显示检查结果
     */
    private function displayResults(array $results): void
    {
        $this->info("等级检查结果:");
        $this->line("  已检查: {$results['checked']}");
        $this->line("  已升级: {$results['upgraded']}");
        $this->line("  错误数: " . count($results['errors']));
        
        if (!empty($results['errors'])) {
            $this->warn("错误详情:");
            foreach ($results['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }
        
        if ($results['upgraded'] > 0) {
            $this->info("🎉 本次检查共有 {$results['upgraded']} 个分销商获得等级升级！");
        }
    }
}