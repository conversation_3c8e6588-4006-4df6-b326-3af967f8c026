<template>
  <div class="smart-city-replacement">
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>🧠 智能城市替换配置</span>
          <el-tooltip content="配置群组标题的城市个性化展示策略" placement="top">
            <el-icon><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>

      <el-form :model="form" label-width="140px">
        <el-form-item label="城市定位功能">
          <el-switch 
            v-model="form.city_location" 
            active-text="开启" 
            inactive-text="关闭"
            @change="onCityLocationChange">
          </el-switch>
          <div class="form-tip">开启后系统会获取用户的城市信息</div>
        </el-form-item>

        <template v-if="form.city_location">
          <el-form-item label="自动城市替换">
            <el-switch 
              v-model="form.auto_city_replace" 
              active-text="开启" 
              inactive-text="关闭"
              @change="onAutoReplaceChange">
            </el-switch>
            <div class="form-tip">开启后会自动在标题中添加用户城市信息</div>
          </el-form-item>

          <el-form-item label="城市插入策略" v-if="form.auto_city_replace">
            <el-select 
              v-model="cityStrategy" 
              placeholder="选择城市插入策略"
              @change="onStrategyChange">
              <el-option
                v-for="option in strategyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value">
                <span>{{ option.label }}</span>
                <span class="option-desc">{{ option.description }}</span>
              </el-option>
            </el-select>
            <div class="form-tip">
              选择城市名称在标题中的插入方式
            </div>
          </el-form-item>
        </template>
      </el-form>
    </el-card>

    <!-- 实时预览效果 -->
    <el-card v-if="form.auto_city_replace">
      <template #header>
        <span>📱 预览效果</span>
      </template>

      <div class="preview-section">
        <el-form :inline="true" class="demo-form">
          <el-form-item label="示例标题">
            <el-input 
              v-model="demoTitle" 
              placeholder="输入群组标题"
              style="width: 200px;">
            </el-input>
          </el-form-item>
          <el-form-item label="用户城市">
            <el-select v-model="demoCity" style="width: 120px;">
              <el-option label="北京" value="北京"></el-option>
              <el-option label="上海" value="上海"></el-option>
              <el-option label="广州" value="广州"></el-option>
              <el-option label="深圳" value="深圳"></el-option>
              <el-option label="杭州" value="杭州"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <div class="preview-results">
          <div class="preview-item">
            <span class="label">原始标题：</span>
            <span class="value original">{{ demoTitle || '创业交流群' }}</span>
          </div>
          <div class="preview-item">
            <span class="label">替换效果：</span>
            <span class="value result">{{ previewResult }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 策略说明 -->
    <el-card class="mt-4">
      <template #header>
        <span>💡 策略说明</span>
      </template>

      <el-collapse>
        <el-collapse-item title="🤖 智能判断（推荐）" name="auto">
          <div class="strategy-desc">
            <p><strong>特点：</strong>系统自动分析标题长度和内容，选择最合适的插入方式</p>
            <ul>
              <li>短标题（≤4字）：使用前缀模式</li>
              <li>中等标题（5-10字）：使用自然插入</li>
              <li>长标题（>10字）：使用后缀模式</li>
            </ul>
            <div class="examples">
              <p><strong>示例：</strong></p>
              <p>"交流群" → "北京交流群"</p>
              <p>"创业者交流群" → "北京创业者交流群"</p>
              <p>"互联网产品经理学习交流分享群" → "互联网产品经理学习交流分享群(北京)"</p>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="📍 前缀模式" name="prefix">
          <div class="strategy-desc">
            <p><strong>特点：</strong>在标题前面直接添加城市名称</p>
            <p><strong>优势：</strong>简单直接，用户一眼就能看到城市信息</p>
            <div class="examples">
              <p><strong>示例：</strong></p>
              <p>"创业交流群" → "北京创业交流群"</p>
              <p>"程序员聚会" → "北京程序员聚会"</p>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="📍 后缀模式" name="suffix">
          <div class="strategy-desc">
            <p><strong>特点：</strong>在标题后面添加城市标识</p>
            <p><strong>优势：</strong>保持原标题完整性，适合长标题</p>
            <div class="examples">
              <p><strong>示例：</strong></p>
              <p>"创业交流群" → "创业交流群(北京版)"</p>
              <p>"程序员聚会" → "程序员聚会(北京版)"</p>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="🎯 自然插入" name="natural">
          <div class="strategy-desc">
            <p><strong>特点：</strong>根据中文语言习惯，在最自然的位置插入城市</p>
            <p><strong>优势：</strong>符合中文表达习惯，阅读体验最佳</p>
            <div class="examples">
              <p><strong>示例：</strong></p>
              <p>"创业交流群" → "北京创业交流群"</p>
              <p>"程序员爱好者" → "北京程序员爱好者"</p>
              <p>"学Python的群" → "北京学Python的群"</p>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="🚫 兼容模式" name="compatible">
          <div class="strategy-desc">
            <p><strong>特点：</strong>兼容原有的"xxx"占位符方式</p>
            <p><strong>使用：</strong>用户在标题中添加"xxx"，系统自动替换为城市名</p>
            <div class="examples">
              <p><strong>示例：</strong></p>
              <p>"xxx创业交流群" → "北京创业交流群"</p>
              <p>"程序员xxx聚会" → "程序员北京聚会"</p>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const form = ref({
  city_location: true,
  auto_city_replace: true,
  ...props.modelValue
})

const cityStrategy = ref(props.modelValue.city_insert_strategy || 'auto')
const demoTitle = ref('创业交流群')
const demoCity = ref('北京')

const strategyOptions = [
  {
    value: 'auto',
    label: '智能判断',
    description: '根据标题长度和内容自动选择最佳插入方式'
  },
  {
    value: 'prefix',
    label: '前缀模式',
    description: '在标题前面添加城市名称'
  },
  {
    value: 'suffix',
    label: '后缀模式',
    description: '在标题后面添加城市标识'
  },
  {
    value: 'natural',
    label: '自然插入',
    description: '根据中文语法在最自然的位置插入'
  },
  {
    value: 'none',
    label: '不插入',
    description: '保持标题原样，不添加城市信息'
  }
]

// 计算预览结果
const previewResult = computed(() => {
  if (!form.value.auto_city_replace) {
    return demoTitle.value || '创业交流群'
  }

  const title = demoTitle.value || '创业交流群'
  const city = demoCity.value

  return simulateCityReplacement(title, city, cityStrategy.value)
})

// 模拟城市替换逻辑
function simulateCityReplacement(title, city, strategy) {
  // 如果包含xxx，直接替换
  if (title.includes('xxx')) {
    return title.replace(/xxx/gi, city)
  }

  // 如果已经包含城市，不重复添加
  if (title.includes(city)) {
    return title
  }

  switch (strategy) {
    case 'prefix':
      return city + title
    case 'suffix':
      return title + '(' + city + '版)'
    case 'natural':
      return simulateNaturalInsertion(title, city)
    case 'none':
      return title
    default:
    case 'auto':
      return simulateAutoInsertion(title, city)
  }
}

function simulateNaturalInsertion(title, city) {
  const patterns = [
    { regex: /(.+)(交流群|讨论群|学习群|分享群)$/, replacement: city + '$1$2' },
    { regex: /(.+)(爱好者|发烧友|玩家|达人)$/, replacement: city + '$1$2' },
    { regex: /(学.+的群|玩.+的群)$/, replacement: city + '$1' },
    { regex: /(.+群)$/, replacement: city + '$1' },
  ]

  for (const pattern of patterns) {
    if (pattern.regex.test(title)) {
      return title.replace(pattern.regex, pattern.replacement)
    }
  }

  return city + title
}

function simulateAutoInsertion(title, city) {
  const length = title.length
  
  if (length <= 4) {
    return city + title
  } else if (length <= 10) {
    return simulateNaturalInsertion(title, city)
  } else {
    return title + '(' + city + ')'
  }
}

// 监听变化
watch(() => form.value, (newValue) => {
  emit('update:modelValue', {
    ...newValue,
    city_insert_strategy: cityStrategy.value
  })
  emit('change', newValue)
}, { deep: true })

watch(cityStrategy, (newValue) => {
  emit('update:modelValue', {
    ...form.value,
    city_insert_strategy: newValue
  })
})

function onCityLocationChange() {
  if (!form.value.city_location) {
    form.value.auto_city_replace = false
  }
}

function onAutoReplaceChange() {
  // 可以在这里添加额外的逻辑
}

function onStrategyChange() {
  // 可以在这里添加额外的逻辑
}
</script>

<style scoped>
.smart-city-replacement {
  max-width: 900px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.option-desc {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.preview-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.demo-form {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.value {
  padding: 6px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.value.original {
  background: #f5f5f5;
  color: #909399;
}

.value.result {
  background: #e1f3d8;
  color: #67c23a;
  font-weight: 500;
}

.strategy-desc {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.strategy-desc p {
  margin: 0 0 8px 0;
}

.strategy-desc ul {
  margin: 8px 0;
  padding-left: 20px;
}

.strategy-desc li {
  margin-bottom: 4px;
}

.examples {
  margin-top: 12px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.examples p {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  color: #606266;
}
</style> 