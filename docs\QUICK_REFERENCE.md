# LinkHub Pro 开发规范快速参考

## 🚀 快速开始

### **安装Git Hooks**
```bash
# 安装代码规范检查
bash scripts/setup-git-hooks.sh
```

### **运行代码检查**
```bash
# 前端代码检查
cd admin
npm run lint

# 代码格式化
npm run format
```

---

## 🚫 禁止提交的文件

### **调试文件**
```bash
❌ *-test.html
❌ *-debug.html  
❌ *-fix-*.html
❌ Mock*调试*.html
❌ admin-preview*.html
```

### **临时脚本**
```bash
❌ debug-*.js
❌ fix-*.js
❌ quick-fix.*
❌ *.bat, *.ps1
❌ *-deploy.sh
```

### **文档报告**
```bash
❌ *-report.md
❌ *-analysis.md
❌ *-修复报告.md
❌ *-检测报告.md
```

### **备份文件**
```bash
❌ *.backup
❌ *.bak
❌ *.old
❌ *-backup.*
```

---

## ✅ 命名规范

### **Vue组件**
```javascript
✅ UserProfile.vue
✅ NavigationSidebar.vue
✅ ModernRichTextEditor.vue

❌ userProfile.vue
❌ navigation-sidebar.vue
```

### **变量和函数**
```javascript
✅ const userName = 'John'
✅ const isLoading = false
✅ const handleSubmit = () => {}

❌ const user_name = 'John'
❌ const IsLoading = false
```

### **常量**
```javascript
✅ const API_BASE_URL = 'https://api.example.com'
✅ const MAX_FILE_SIZE = 1024 * 1024

❌ const apiBaseUrl = 'https://api.example.com'
```

---

## 🧹 代码清理

### **允许的调试代码**
```javascript
✅ console.error('API请求失败:', error)
✅ console.warn('配置项缺失:', configKey)
✅ console.info('用户登录成功')
```

### **禁止的调试代码**
```javascript
❌ console.log('调试信息:', data)
❌ console.log('🔍 检查数据:', response)
❌ debugger
❌ alert('测试')
```

---

## 📝 提交消息规范

### **推荐格式**
```bash
✅ feat(user): 添加用户头像上传功能
✅ fix(api): 修复登录接口错误处理
✅ docs: 更新开发规范文档
✅ style(component): 调整按钮样式
✅ refactor: 重构用户管理模块
✅ test: 添加用户注册测试用例
✅ chore: 更新依赖包版本
✅ perf: 优化图片加载性能
```

### **类型说明**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建或工具相关
- `perf`: 性能优化

---

## 🔧 Vue组件结构

### **标准结构**
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'

// 2. Props和Emits
const props = defineProps({
  modelValue: String
})
const emit = defineEmits(['update:modelValue'])

// 3. 响应式数据
const data = ref('')

// 4. 计算属性
const computedValue = computed(() => {})

// 5. 方法
const handleMethod = () => {}

// 6. 生命周期
onMounted(() => {})
</script>

<style scoped>
/* 组件样式 */
</style>
```

---

## 🚨 错误处理

### **API调用**
```javascript
✅ const fetchData = async () => {
  try {
    const response = await api.getData()
    return response.data
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('数据加载失败，请重试')
    throw error
  }
}
```

### **组件错误边界**
```vue
<script setup>
import { onErrorCaptured } from 'vue'

onErrorCaptured((error, instance, info) => {
  console.error('组件错误:', error)
  reportError(error, info)
  return false
})
</script>
```

---

## 📋 提交前检查清单

### **必检项目**
- [ ] 删除所有调试HTML文件
- [ ] 清理console.log调试输出
- [ ] 移除临时脚本和备份文件
- [ ] 检查文件命名规范
- [ ] 运行ESLint检查
- [ ] 运行测试用例
- [ ] 更新相关文档

### **代码质量**
- [ ] 组件结构清晰
- [ ] 错误处理完善
- [ ] 性能考虑充分
- [ ] 注释清晰准确
- [ ] 命名规范一致

---

## 🛠️ 常用命令

### **代码检查**
```bash
# ESLint检查
npm run lint

# ESLint自动修复
npm run lint:fix

# Prettier格式化
npm run format

# 类型检查
npm run type-check
```

### **测试命令**
```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

### **构建命令**
```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

---

## 📚 相关文档

- [完整开发规范](../DEVELOPMENT_GUIDELINES.md)
- [代码审查清单](./CODE_REVIEW_CHECKLIST.md)
- [项目README](../README.md)
- [API文档](./API.md)

---

## 🆘 常见问题

### **Q: Git提交被拒绝怎么办？**
A: 检查错误信息，按照提示清理相关文件，然后重新提交。

### **Q: ESLint报错怎么解决？**
A: 运行 `npm run lint:fix` 自动修复，或手动修改代码。

### **Q: 如何临时跳过检查？**
A: 使用 `git commit --no-verify`，但不建议经常使用。

### **Q: 如何更新开发规范？**
A: 修改相关文档后，通知团队成员重新阅读规范。

---

**快速参考版本**: v1.0  
**更新时间**: 2025-08-08
