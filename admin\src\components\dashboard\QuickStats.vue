<template>
  <div class="quick-stats-card">
    <div class="card-header">
      <h3 class="card-title">今日统计</h3>
    </div>
    <div class="stats-grid">
      <div class="stat-item" v-for="stat in stats" :key="stat.key">
        <div class="stat-icon" :style="{ background: stat.color }">
          <el-icon>
            <component :is="getIconComponent(stat.icon)" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { User, UserFilled, Tickets, Money } from '@element-plus/icons-vue'

const props = defineProps({
  stats: {
    type: Array,
    default: () => []
  }
})

const getIconComponent = (iconName) => {
  const iconMap = {
    'User': User,
    'UserFilled': UserFilled,
    'Tickets': Tickets,
    'Money': Money
  }
  return iconMap[iconName] || User
}
</script>

<style lang="scss" scoped>
.quick-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      background: #f3f4f6;
      transform: translateY(-2px);
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 2px;
      }

      .stat-label {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .quick-stats-card {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>