import{_ as e}from"./index-DtXAftX0.js";/* empty css               *//* empty css                *//* empty css                       */import{by as a,at as t,aZ as s,a_ as r,aY as l,U as i,T as n,aq as o,Q as d}from"./element-plus-h2SQQM64.js";import{P as c}from"./PageLayout-C6qH3ReN.js";import{i as u}from"./echarts-D68jitv0.js";import{r as v,L as p,e as m,n as h,k as _,l as g,E as f,z as y,t as x,D as w,u as b}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const j={class:"app-container"},D={class:"page-header"},T={class:"toolbar-container"},k={class:"metric-value"},L={class:"metric-change positive"},z={class:"metric-value"},C={class:"metric-change positive"},A={class:"metric-value"},O={class:"metric-change negative"},S={class:"metric-value"},E={class:"metric-change positive"},P=e({__name:"AnalyticsDashboard",setup(e){const P=v(null),V=v(null),U=v(null),q=v(null),G=v([]),H=v(!1),I=p({total_revenue:128530,revenue_change:15.2,total_users:8520,users_change:8.1,active_groups:128,groups_change:2.5,conversion_rate:5.8,conversion_change:1.2}),Q=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}];let R,W,Y,Z;const B=()=>{H.value=!0,d.info("正在根据日期范围加载新数据..."),setTimeout(()=>{H.value=!1},1e3)},F=()=>{d.success("报表正在后台生成，请稍后在消息中心下载。")};return m(()=>{h(()=>{R=u(P.value),W=u(V.value),Y=u(U.value),Z=u(q.value),R.setOption({tooltip:{trigger:"axis"},legend:{data:["新增用户","活跃用户"]},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:[120,132,101,134,90,230,210]},{name:"活跃用户",type:"line",data:[820,932,901,934,1290,1330,1320]}]}),W.setOption({tooltip:{trigger:"item"},legend:{top:"5%",left:"center"},series:[{name:"收入来源",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},data:[{value:1048,name:"付费群组"},{value:735,name:"活动报名"},{value:580,name:"内容打赏"},{value:484,name:"广告收入"}]}]}),Y.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",boundaryGap:[0,.01]},yAxis:{type:"category",data:["分享会","沙龙","打卡挑战"]},series:[{name:"参与人数",type:"bar",data:[88,45,152]}]}),Z.setOption({tooltip:{},radar:{indicator:[{name:"活跃度",max:100},{name:"增长率",max:100},{name:"内容质量",max:100},{name:"互动性",max:100},{name:"付费率",max:100}]},series:[{name:"群组健康度",type:"radar",data:[{value:[85,90,75,80,60],name:"平均水平"}]}]})}),window.addEventListener("resize",()=>{R?.resize(),W?.resize(),Y?.resize(),Z?.resize()})}),(e,d)=>{const u=n,v=a,p=t,m=l,h=r,H=s;return g(),_("div",j,[f(c,null,{header:y(()=>[x("div",D,[x("h1",null,[f(u,null,{default:y(()=>[f(b(o))]),_:1}),d[1]||(d[1]=w(" 数据分析与报表 ",-1))]),d[2]||(d[2]=x("p",null,"洞察社群运营关键数据，驱动业务增长。",-1))])]),default:y(()=>[x("div",T,[f(v,{modelValue:G.value,"onUpdate:modelValue":d[0]||(d[0]=e=>G.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:Q,onChange:B},null,8,["modelValue"]),f(p,{type:"primary",icon:"Download",onClick:F},{default:y(()=>d[3]||(d[3]=[w(" 导出报表 ",-1)])),_:1,__:[3]})]),f(H,{gutter:20,class:"key-metrics"},{default:y(()=>[f(h,{span:6},{default:y(()=>[f(m,{shadow:"hover",class:"metric-card"},{default:y(()=>[d[4]||(d[4]=x("div",{class:"metric-title"},"总收入",-1)),x("div",k,"¥"+i(I.total_revenue.toLocaleString()),1),x("div",L,"+"+i(I.revenue_change)+"% vs 上一周期",1)]),_:1,__:[4]})]),_:1}),f(h,{span:6},{default:y(()=>[f(m,{shadow:"hover",class:"metric-card"},{default:y(()=>[d[5]||(d[5]=x("div",{class:"metric-title"},"总用户数",-1)),x("div",z,i(I.total_users.toLocaleString()),1),x("div",C,"+"+i(I.users_change)+"%",1)]),_:1,__:[5]})]),_:1}),f(h,{span:6},{default:y(()=>[f(m,{shadow:"hover",class:"metric-card"},{default:y(()=>[d[6]||(d[6]=x("div",{class:"metric-title"},"活跃群组",-1)),x("div",A,i(I.active_groups.toLocaleString()),1),x("div",O,"-"+i(I.groups_change)+"%",1)]),_:1,__:[6]})]),_:1}),f(h,{span:6},{default:y(()=>[f(m,{shadow:"hover",class:"metric-card"},{default:y(()=>[d[7]||(d[7]=x("div",{class:"metric-title"},"付费转化率",-1)),x("div",S,i(I.conversion_rate)+"%",1),x("div",E,"+"+i(I.conversion_change)+"%",1)]),_:1,__:[7]})]),_:1})]),_:1}),f(H,{gutter:20},{default:y(()=>[f(h,{span:16},{default:y(()=>[f(m,{shadow:"never",class:"chart-card"},{header:y(()=>d[8]||(d[8]=[w("用户增长趋势",-1)])),default:y(()=>[x("div",{ref_key:"userTrendChart",ref:P,style:{height:"350px"}},null,512)]),_:1})]),_:1}),f(h,{span:8},{default:y(()=>[f(m,{shadow:"never",class:"chart-card"},{header:y(()=>d[9]||(d[9]=[w("收入来源分布",-1)])),default:y(()=>[x("div",{ref_key:"revenueSourceChart",ref:V,style:{height:"350px"}},null,512)]),_:1})]),_:1})]),_:1}),f(H,{gutter:20,style:{"margin-top":"20px"}},{default:y(()=>[f(h,{span:8},{default:y(()=>[f(m,{shadow:"never",class:"chart-card"},{header:y(()=>d[10]||(d[10]=[w("热门活动排行",-1)])),default:y(()=>[x("div",{ref_key:"topEventsChart",ref:U,style:{height:"300px"}},null,512)]),_:1})]),_:1}),f(h,{span:16},{default:y(()=>[f(m,{shadow:"never",class:"chart-card"},{header:y(()=>d[11]||(d[11]=[w("群组健康度分析",-1)])),default:y(()=>[x("div",{ref_key:"groupHealthChart",ref:q,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1})]),_:1})])}}},[["__scopeId","data-v-34c74824"]]);export{P as default};
