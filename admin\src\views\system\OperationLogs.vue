<template>
  <div class="app-container">
    <!-- 日志类型选择 -->
    <el-card class="log-type-card" style="margin-bottom: 20px;">
      <el-tabs v-model="activeLogType" @tab-change="handleLogTypeChange">
        <el-tab-pane label="操作日志" name="operation">
          <template #label>
            <span><i class="el-icon-operation"></i> 操作日志</span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="安全日志" name="security">
          <template #label>
            <span><i class="el-icon-lock"></i> 安全日志</span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="权限日志" name="permission">
          <template #label>
            <span><i class="el-icon-key"></i> 权限日志</span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="系统日志" name="system">
          <template #label>
            <span><i class="el-icon-monitor"></i> 系统日志</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 日志统计 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon total-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ logStats.total }}</div>
              <div class="stat-label">总日志数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today-icon">
              <i class="el-icon-calendar-today"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ logStats.today }}</div>
              <div class="stat-label">今日操作</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ logStats.active_users }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon error-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ logStats.errors }}</div>
              <div class="stat-label">错误操作</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作统计图表 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>📊 操作类型统计</span>
            </div>
          </template>
          <v-chart class="chart" :option="operationTypeChart" autoresize />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>📈 操作趋势</span>
            </div>
          </template>
          <v-chart class="chart" :option="operationTrendChart" autoresize />
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志筛选 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>🔍 日志筛选</span>
        </div>
      </template>
      
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="操作用户">
          <el-select v-model="queryParams.user_id" placeholder="选择用户" clearable filterable>
            <el-option v-for="user in users" :key="user.id" :label="user.nickname" :value="user.id" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作类型">
          <el-select v-model="queryParams.operation_type" placeholder="选择类型" clearable>
            <el-option label="登录" value="login" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
            <el-option label="导出" value="export" />
            <el-option label="设置" value="setting" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模块">
          <el-select v-model="queryParams.module" placeholder="选择模块" clearable>
            <el-option label="用户管理" value="user" />
            <el-option label="订单管理" value="order" />
            <el-option label="财务管理" value="finance" />
            <el-option label="分销管理" value="distribution" />
            <el-option label="系统设置" value="system" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="选择状态" clearable>
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="警告" value="warning" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="queryParams.date_range"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="IP地址">
          <el-input v-model="queryParams.ip" placeholder="IP地址" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchLogs">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
          <el-button type="success" @click="exportLogs">
            <i class="el-icon-download"></i> 导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志列表 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>📋 操作日志</span>
          <div>
            <el-button type="primary" @click="refreshLogs">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
            <el-button type="warning" @click="clearLogs">
              <i class="el-icon-delete"></i> 清理日志
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="logs" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="user" label="操作用户" width="120">
          <template #default="scope">
            <div class="user-info">
              <div class="user-name">{{ scope.row.user?.nickname || '系统' }}</div>
              <div class="user-role">{{ scope.row.user?.role_name || 'System' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="operation_type" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getOperationTypeColor(scope.row.operation_type)">
              {{ getOperationTypeName(scope.row.operation_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块" width="100">
          <template #default="scope">
            <span class="module-name">{{ getModuleName(scope.row.module) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="120" />
        <el-table-column prop="user_agent" label="用户代理" width="150" show-overflow-tooltip />
        <el-table-column prop="execution_time" label="耗时" width="80">
          <template #default="scope">
            {{ scope.row.execution_time }}ms
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="操作时间" width="160" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDetails(scope.row)">
              详情
            </el-button>
            <el-button type="danger" size="small" @click="deleteLog(scope.row)" v-if="scope.row.status === 'failed'">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.per_page"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="操作详情" v-model="detailDialog.visible" width="800px">
      <div class="log-detail" v-if="detailDialog.log">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作ID">
            {{ detailDialog.log.id }}
          </el-descriptions-item>
          <el-descriptions-item label="操作用户">
            {{ detailDialog.log.user?.nickname || '系统' }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeColor(detailDialog.log.operation_type)">
              {{ getOperationTypeName(detailDialog.log.operation_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ getModuleName(detailDialog.log.module) }}
          </el-descriptions-item>
          <el-descriptions-item label="操作描述">
            {{ detailDialog.log.description }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(detailDialog.log.status)">
              {{ getStatusName(detailDialog.log.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ detailDialog.log.ip_address }}
          </el-descriptions-item>
          <el-descriptions-item label="执行时间">
            {{ detailDialog.log.execution_time }}ms
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ detailDialog.log.created_at }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h4>用户代理</h4>
          <el-input type="textarea" :value="detailDialog.log.user_agent" readonly />
        </div>
        
        <div class="detail-section" v-if="detailDialog.log.request_data">
          <h4>请求数据</h4>
          <pre class="json-data">{{ JSON.stringify(detailDialog.log.request_data, null, 2) }}</pre>
        </div>
        
        <div class="detail-section" v-if="detailDialog.log.response_data">
          <h4>响应数据</h4>
          <pre class="json-data">{{ JSON.stringify(detailDialog.log.response_data, null, 2) }}</pre>
        </div>
        
        <div class="detail-section" v-if="detailDialog.log.error_message">
          <h4>错误信息</h4>
          <el-alert :title="detailDialog.log.error_message" type="error" show-icon />
        </div>
      </div>
    </el-dialog>

    <!-- 清理日志对话框 -->
    <el-dialog title="清理日志" v-model="cleanDialog.visible" width="500px">
      <el-form :model="cleanForm" label-width="100px">
        <el-form-item label="清理方式">
          <el-radio-group v-model="cleanForm.type">
            <el-radio label="by_date">按时间</el-radio>
            <el-radio label="by_count">按数量</el-radio>
            <el-radio label="by_status">按状态</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="清理条件" v-if="cleanForm.type === 'by_date'">
          <el-date-picker
            v-model="cleanForm.before_date"
            type="date"
            placeholder="清理此日期之前的日志"
          />
        </el-form-item>
        
        <el-form-item label="保留数量" v-if="cleanForm.type === 'by_count'">
          <el-input-number v-model="cleanForm.keep_count" :min="1000" :max="100000" />
        </el-form-item>
        
        <el-form-item label="清理状态" v-if="cleanForm.type === 'by_status'">
          <el-select v-model="cleanForm.status" placeholder="选择要清理的状态">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="警告" value="warning" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-alert
            title="注意：清理操作不可恢复，请谨慎操作！"
            type="warning"
            show-icon
            :closable="false"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cleanDialog.visible = false">取消</el-button>
          <el-button type="danger" @click="executeClean" :loading="cleaning">
            确认清理
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import VChart from "vue-echarts";

const logStats = reactive({
  total: 25687,
  today: 156,
  active_users: 45,
  errors: 12
})

const queryParams = reactive({
  page: 1,
  per_page: 20,
  user_id: '',
  operation_type: '',
  module: '',
  status: '',
  date_range: [],
  ip: ''
})

const logs = ref([
  {
    id: 1,
    user: { nickname: '管理员', role_name: '超级管理员' },
    operation_type: 'login',
    module: 'system',
    description: '用户登录系统',
    status: 'success',
    ip_address: '***********',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    execution_time: 120,
    created_at: '2024-01-01 10:00:00',
    request_data: { username: 'admin' },
    response_data: { success: true }
  },
  {
    id: 2,
    user: { nickname: '普通用户', role_name: '用户' },
    operation_type: 'create',
    module: 'user',
    description: '创建新用户',
    status: 'success',
    ip_address: '***********',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    execution_time: 350,
    created_at: '2024-01-01 09:30:00'
  }
])

const users = ref([
  { id: 1, nickname: '管理员' },
  { id: 2, nickname: '普通用户' }
])

const detailDialog = reactive({
  visible: false,
  log: null
})

const cleanDialog = reactive({
  visible: false
})

const cleanForm = reactive({
  type: 'by_date',
  before_date: null,
  keep_count: 10000,
  status: ''
})

const operationTypeChart = ref({})
const operationTrendChart = ref({})

const loading = ref(false)
const cleaning = ref(false)
const total = ref(0)
const activeLogType = ref('operation')

// 初始化图表
const initCharts = () => {
  // 操作类型统计
  operationTypeChart.value = {
    title: {
      text: '操作类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '操作类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '登录' },
          { value: 735, name: '创建' },
          { value: 580, name: '更新' },
          { value: 484, name: '删除' },
          { value: 300, name: '导出' }
        ]
      }
    ]
  }
  
  // 操作趋势
  operationTrendChart.value = {
    title: {
      text: '操作趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '操作次数',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210]
      }
    ]
  }
}

// 辅助函数
const getOperationTypeColor = (type) => {
  const colors = {
    login: 'success',
    create: 'primary',
    update: 'warning',
    delete: 'danger',
    export: 'info',
    setting: 'primary'
  }
  return colors[type] || 'info'
}

const getOperationTypeName = (type) => {
  const names = {
    login: '登录',
    create: '创建',
    update: '更新',
    delete: '删除',
    export: '导出',
    setting: '设置'
  }
  return names[type] || type
}

const getModuleName = (module) => {
  const names = {
    user: '用户管理',
    order: '订单管理',
    finance: '财务管理',
    distribution: '分销管理',
    system: '系统设置'
  }
  return names[module] || module
}

const getStatusColor = (status) => {
  const colors = {
    success: 'success',
    failed: 'danger',
    warning: 'warning'
  }
  return colors[status] || 'info'
}

const getStatusName = (status) => {
  const names = {
    success: '成功',
    failed: '失败',
    warning: '警告'
  }
  return names[status] || status
}

// 操作函数
const handleLogTypeChange = (logType) => {
  activeLogType.value = logType
  // 根据日志类型更新统计数据和日志列表
  updateLogsByType(logType)
  ElMessage.info(`切换到${getLogTypeName(logType)}`)
}

const getLogTypeName = (type) => {
  const typeNames = {
    operation: '操作日志',
    security: '安全日志',
    permission: '权限日志',
    system: '系统日志'
  }
  return typeNames[type] || '操作日志'
}

const updateLogsByType = (logType) => {
  // 模拟根据日志类型更新数据
  loading.value = true
  setTimeout(() => {
    // 这里可以调用不同的API获取不同类型的日志
    loading.value = false
  }, 500)
}

const searchLogs = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 1000)
}

const resetQuery = () => {
  Object.assign(queryParams, {
    page: 1,
    per_page: 20,
    user_id: '',
    operation_type: '',
    module: '',
    status: '',
    date_range: [],
    ip: ''
  })
  searchLogs()
}

const exportLogs = () => {
  ElMessage.success('导出任务已创建')
}

const refreshLogs = () => {
  searchLogs()
}

const clearLogs = () => {
  cleanDialog.visible = true
}

const viewDetails = (log) => {
  detailDialog.log = log
  detailDialog.visible = true
}

const deleteLog = (log) => {
  ElMessageBox.confirm(`确定要删除这条日志吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    refreshLogs()
  })
}

const executeClean = async () => {
  cleaning.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('日志清理完成')
    cleanDialog.visible = false
    refreshLogs()
  } catch (error) {
    ElMessage.error('日志清理失败')
  } finally {
    cleaning.value = false
  }
}

const handleSizeChange = (size) => {
  queryParams.per_page = size
  searchLogs()
}

const handleCurrentChange = (page) => {
  queryParams.page = page
  searchLogs()
}

onMounted(() => {
  total.value = logs.value.length
  initCharts()
})
</script>

<style scoped>
.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.total-icon { background: #409EFF; }
.today-icon { background: #67C23A; }
.user-icon { background: #E6A23C; }
.error-icon { background: #F56C6C; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart {
  height: 300px;
}

.filter-form {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.user-info {
  text-align: left;
}

.user-name {
  font-weight: bold;
  color: #303133;
}

.user-role {
  font-size: 12px;
  color: #909399;
}

.module-name {
  font-weight: 500;
  color: #606266;
}

.table-pagination {
  margin-top: 20px;
  text-align: center;
}

.log-detail {
  padding: 20px;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.json-data {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  color: #606266;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 