<template>
  <div class="enhanced-global-search-v2">
    <!-- 搜索触发器 -->
    <div class="search-trigger" @click="openSearchDialog">
      <el-icon class="search-icon"><Search /></el-icon>
      <span class="search-placeholder">搜索功能、用户、订单...</span>
      <kbd class="search-shortcut">Ctrl+K</kbd>
    </div>

    <!-- 搜索对话框 -->
    <el-dialog
      v-model="showSearchDialog"
      title="🔍 智能搜索"
      width="600px"
      :show-close="false"
      :close-on-click-modal="true"
      class="search-dialog"
      @opened="handleDialogOpened"
      @closed="handleDialogClosed"
    >
      <div class="search-content">
        <!-- 搜索输入框 -->
        <div class="search-input-container">
          <el-input
            ref="searchInputRef"
            v-model="searchQuery"
            placeholder="输入关键词搜索功能、用户、订单..."
            prefix-icon="Search"
            size="large"
            clearable
            @input="handleSearchInput"
            @keydown="handleSearchKeydown"
            @clear="handleSearchClear"
            class="search-input"
          />
        </div>

        <!-- 搜索结果区域 -->
        <div class="search-results-container">
          <!-- 推荐功能（无输入时显示） -->
          <div v-if="!searchQuery && recommendations.length > 0" class="recommendations">
            <div class="section-header">
              <el-icon><Star /></el-icon>
              <span>推荐功能</span>
            </div>
            <div class="recommendation-list">
              <div
                v-for="(item, index) in recommendations"
                :key="item.id"
                class="recommendation-item"
                :class="{ active: selectedIndex === index }"
                @click="selectResult(item)"
                @mouseenter="selectedIndex = index"
              >
                <div class="item-icon">
                  <el-icon><component :is="getIconComponent(item.icon)" /></el-icon>
                </div>
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-desc">{{ item.description }}</div>
                </div>
                <el-tag v-if="item.protected" size="small" type="success">核心</el-tag>
              </div>
            </div>
          </div>

          <!-- 搜索结果 -->
          <div v-if="searchQuery && searchResults.length > 0" class="search-results">
            <div class="results-header">
              <span>找到 {{ searchResults.length }} 个结果</span>
              <span class="search-time">{{ searchTime }}ms</span>
            </div>
            <div class="results-list">
              <div
                v-for="(result, index) in searchResults"
                :key="result.id"
                class="result-item"
                :class="{ active: selectedIndex === index }"
                @click="selectResult(result)"
                @mouseenter="selectedIndex = index"
              >
                <div class="result-icon">
                  <el-icon><component :is="getIconComponent(result.icon)" /></el-icon>
                </div>
                <div class="result-content">
                  <div class="result-title" v-html="highlightText(result.title)"></div>
                  <div class="result-path">{{ result.group || result.path }}</div>
                </div>
                <div class="result-badges">
                  <el-tag v-if="result.protected" size="small" type="success">核心</el-tag>
                  <el-tag v-if="result.recent" size="small" type="warning">最近</el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 无结果 -->
          <div v-if="searchQuery && searchResults.length === 0" class="no-results">
            <div class="no-results-icon">
              <el-icon><Search /></el-icon>
            </div>
            <div class="no-results-text">未找到相关结果</div>
            <div class="search-suggestions">
              <span>试试搜索：</span>
              <el-button
                v-for="suggestion in quickSearchTerms"
                :key="suggestion"
                text
                size="small"
                @click="searchQuery = suggestion; handleSearchInput(suggestion)"
              >
                {{ suggestion }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Search, Star, Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
  OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick, 
  Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
  InfoFilled, CreditCard, Folder, View, Bell, Plus, Lightning, Management, Promotion,
  ShoppingCart, RefreshLeft
} from '@element-plus/icons-vue'
import { getSimpleVisibleNavigationGroups, getSimpleUserQuickActions } from '@/config/simpleNavigationTest'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showSearchDialog = ref(false)
const searchQuery = ref('')
const searchResults = ref([])
const selectedIndex = ref(0)
const searchInputRef = ref(null)
const searchTime = ref(0)
const searchData = ref([])

// 计算属性
const currentUserRole = computed(() => {
  return userStore.userInfo?.role || 'user'
})

// 推荐功能（基于角色）
const recommendations = computed(() => {
  const quickActions = getSimpleUserQuickActions(currentUserRole.value)
  return quickActions.map(action => ({
    id: `rec-${action.path}`,
    title: action.title,
    description: `快速${action.title}`,
    icon: action.icon,
    path: action.path,
    type: 'recommendation',
    protected: action.path === '/community/add'
  }))
})

// 快速搜索建议
const quickSearchTerms = computed(() => {
  const baseTerms = ['群组', '用户', '订单', '数据']
  const roleTerms = {
    admin: ['系统', '监控', '权限'],
    substation: ['分站', '代理商', '财务'],
    agent: ['团队', '佣金', '绩效'],
    distributor: ['客户', '推广', '佣金'],
    group_owner: ['群组', '内容', '成员'],
    user: ['订单', '个人', '设置']
  }
  return [...baseTerms, ...(roleTerms[currentUserRole.value] || [])]
})

// 方法
const openSearchDialog = () => {
  showSearchDialog.value = true
}

const handleDialogOpened = async () => {
  await nextTick()
  searchInputRef.value?.focus()
  selectedIndex.value = 0
}

const handleDialogClosed = () => {
  searchQuery.value = ''
  searchResults.value = []
  selectedIndex.value = 0
}

const handleSearchInput = (value) => {
  const startTime = performance.now()
  
  if (!value || value.trim() === '') {
    searchResults.value = []
    selectedIndex.value = 0
    return
  }
  
  // 执行搜索
  searchResults.value = performSearch(value.trim())
  selectedIndex.value = 0
  
  const endTime = performance.now()
  searchTime.value = Math.round(endTime - startTime)
}

const handleSearchClear = () => {
  searchResults.value = []
  selectedIndex.value = 0
}

const handleSearchKeydown = (event) => {
  const totalResults = getCurrentResultsCount()
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, totalResults - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      const selectedResult = getSelectedResult()
      if (selectedResult) {
        selectResult(selectedResult)
      }
      break
    case 'Escape':
      showSearchDialog.value = false
      break
  }
}

const performSearch = (query) => {
  const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0)
  const results = []
  
  searchData.value.forEach(item => {
    let score = 0
    let matchedTerms = 0
    
    searchTerms.forEach(term => {
      if (item.title.toLowerCase().includes(term)) {
        score += 10
        matchedTerms++
      }
      if (item.description && item.description.toLowerCase().includes(term)) {
        score += 5
        matchedTerms++
      }
      if (item.path && item.path.toLowerCase().includes(term)) {
        score += 3
        matchedTerms++
      }
    })
    
    if (matchedTerms > 0) {
      // 核心功能加权
      if (item.protected) {
        score += 5
      }
      
      results.push({
        ...item,
        score,
        matchedTerms
      })
    }
  })
  
  return results
    .sort((a, b) => b.score - a.score)
    .slice(0, 20)
}

const selectResult = (result) => {
  if (!result.path) return
  
  router.push(result.path)
  showSearchDialog.value = false
  searchQuery.value = ''
  searchResults.value = []
  
  ElMessage.success(`正在跳转到 ${result.title}`)
}

const getCurrentResultsCount = () => {
  if (!searchQuery.value) {
    return recommendations.value.length
  }
  return searchResults.value.length
}

const getSelectedResult = () => {
  if (!searchQuery.value) {
    return recommendations.value[selectedIndex.value]
  }
  return searchResults.value[selectedIndex.value]
}

const getIconComponent = (iconName) => {
  const iconMap = {
    Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
    OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick,
    Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
    InfoFilled, CreditCard, Folder, View, Bell, Plus, Lightning, Management, Promotion,
    ShoppingCart, RefreshLeft
  }
  return iconMap[iconName] || Document
}

const highlightText = (text) => {
  if (!searchQuery.value || !text) return text
  
  const searchTerms = searchQuery.value.toLowerCase().split(' ').filter(term => term.length > 0)
  let highlightedText = text
  
  searchTerms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi')
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
  })
  
  return highlightedText
}

// 初始化搜索数据
const initializeSearchData = () => {
  const visibleGroups = getSimpleVisibleNavigationGroups(currentUserRole.value)
  const data = []
  
  Object.entries(visibleGroups).forEach(([groupKey, group]) => {
    group.children.forEach(child => {
      data.push({
        id: `search-${child.path}`,
        title: child.title,
        description: child.description || '',
        icon: child.icon,
        path: child.path,
        group: group.title,
        type: 'menu',
        protected: child.protected || false
      })
    })
  })
  
  searchData.value = data
}

// 全局快捷键
const handleGlobalKeydown = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    openSearchDialog()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
  initializeSearchData()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})
</script>