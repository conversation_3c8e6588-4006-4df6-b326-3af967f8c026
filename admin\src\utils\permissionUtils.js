/**
 * 权限工具函数 - 解决循环引用问题
 * 统一处理权限检查和角色相关逻辑
 */

import { roleHierarchy } from '@/config/navigation'

/**
 * 检查用户是否有权限访问指定路径
 * @param {string} path - 路由路径
 * @param {string} userRole - 用户角色
 * @returns {boolean} 是否有权限
 */
export function hasRoutePermission(path, userRole) {
  if (!userRole || !path) return false
  
  const roleConfig = roleHierarchy[userRole]
  if (!roleConfig) return false

  // 超级管理员拥有所有权限
  if (userRole === 'admin') return true

  // 根据角色层级判断权限
  const allowedPrefixes = getAllowedPrefixes(userRole)
  return allowedPrefixes.some(prefix => path.startsWith(prefix))
}

/**
 * 获取角色允许访问的路径前缀
 * @param {string} userRole - 用户角色
 * @returns {string[]} 允许的路径前缀列表
 */
export function getAllowedPrefixes(userRole) {
  const prefixes = {
    admin: ['/'],
    substation: ['/dashboard', '/user', '/community', '/orders', '/finance', '/agent', '/substation', '/distribution', '/promotion'],
    agent: ['/agent', '/user/center', '/user/profile', '/finance/commission-logs', '/promotion/links'],
    distributor: ['/distributor', '/distribution/customers', '/community/groups', '/orders/list', '/finance/commission-logs', '/promotion/links', '/user/center'],
    group_owner: ['/owner', '/community/groups', '/orders/list', '/user/center'],
    user: ['/user/center', '/user/profile', '/orders/my', '/community/my-groups']
  }
  
  return prefixes[userRole] || []
}

/**
 * 获取角色显示名称
 * @param {string} role - 角色标识
 * @returns {string} 显示名称
 */
export function getDisplayRoleName(role) {
  return getRoleDisplayName(role)
}

/**
 * 获取角色显示名称（别名，兼容旧代码）
 * @param {string} role - 角色标识
 * @returns {string} 显示名称
 */
export function getRoleDisplayName(role) {
  const displayNames = {
    admin: '超级管理员',
    substation: '分站管理员',
    agent: '代理商',
    distributor: '分销商',
    group_owner: '群主',
    user: '普通用户'
  }
  return displayNames[role] || role
}

/**
 * 检查用户是否可以查看其他用户的数据
 * @param {string} currentRole - 当前用户角色
 * @param {string} targetRole - 目标用户角色
 * @returns {boolean} 是否有权限查看
 */
export function canViewUserData(currentRole, targetRole) {
  const current = roleHierarchy[currentRole]
  if (!current) return false
  
  return current.canViewRoles.includes(targetRole)
}

/**
 * 获取用户可访问的菜单项
 * @param {string} userRole - 用户角色
 * @param {Array} allRoutes - 所有路由配置
 * @returns {Array} 过滤后的路由
 */
export function getAccessibleRoutes(userRole, allRoutes = []) {
  if (!userRole) return []
  
  return allRoutes.filter(route => {
    if (route.meta?.hidden) return false
    return hasRoutePermission(route.path, userRole)
  })
}

/**
 * 检查群组创建权限
 * @param {string} userRole - 用户角色
 * @returns {boolean} 是否有权限
 */
export function canCreateGroup(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.groupCreatePermission : false
}

/**
 * 获取用户工作台路径
 * @param {string} userRole - 用户角色
 * @returns {string} 工作台路径
 */
export function getWorkbenchPath(userRole) {
  const paths = {
    admin: '/dashboard',
    substation: '/dashboard',
    agent: '/agent/dashboard',
    distributor: '/distributor/dashboard',
    group_owner: '/owner/dashboard',
    user: '/user/center'
  }
  
  return paths[userRole] || '/user/center'
}

/**
 * 获取用户默认路由（兼容旧版导航配置）
 * @param {string} userRole - 用户角色
 * @returns {string} 默认路由路径
 */
export function getUserDefaultRoute(userRole) {
  return getWorkbenchPath(userRole)
}