import{_ as e}from"./index-DtXAftX0.js";/* empty css                     *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                *//* empty css               *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                        */import{af as l,r as a,c as t,L as s,e as d,k as u,l as i,B as o,t as r,E as n,y as _,z as c,D as p,u as m,F as v,Y as f}from"./vue-vendor-Dy164gUc.js";import{T as b,aX as h,at as g,Y as y,bv as V,aY as w,aZ as k,a_ as x,bq as q,aM as U,br as j,bt as C,bu as A,bR as z,ai as Q,_ as F,b9 as M,b8 as T,$ as R,bX as B,bs as L,bm as H,bB as O,bA as P,aw as S,am as Y,bU as G,bG as I,bS as D,bQ as E,b7 as X,bY as $,bp as Z,U as J,ay as K,Q as N}from"./element-plus-h2SQQM64.js";import{c as W}from"./community-DNWNbya4.js";import{R as ee}from"./RichTextEditor-Cp69n7mq.js";const le={class:"group-create-form"},ae={key:0,class:"page-header"},te={class:"header-content"},se={class:"header-actions"},de={class:"form-container"},ue={class:"card-header"},ie={class:"card-header"},oe={key:0},re={class:"card-header"},ne={key:0},_e=["src"],ce={key:1},pe={key:2},me={key:3},ve={key:4},fe={class:"card-header"},be={class:"card-header"},he={class:"rich-editor-container"},ge={class:"editor-actions"},ye={class:"rich-text-editor-wrapper"},Ve={class:"editor-help"},we={class:"rich-editor-container"},ke={class:"editor-actions"},xe={class:"rich-text-editor-wrapper"},qe={class:"editor-help"},Ue={class:"rich-editor-container"},je={class:"editor-actions"},Ce={class:"rich-text-editor-wrapper"},Ae={class:"editor-help"},ze={class:"card-header"},Qe={class:"card-header"},Fe={key:0},Me=["src"],Te=["src"],Re=["src"],Be={key:7,class:"dialog-footer"},Le={key:0,class:"preview-content"},He={class:"preview-header"},Oe={class:"group-title"},Pe={class:"group-stats"},Se={class:"stat-item"},Ye={class:"stat-item"},Ge={class:"stat-item"},Ie={class:"group-price"},De={class:"price-value"},Ee={key:0,class:"preview-section"},Xe={key:1,class:"preview-section"},$e=["innerHTML"],Ze={key:2,class:"preview-section"},Je=["innerHTML"],Ke={key:3,class:"preview-section"},Ne=["innerHTML"],We={class:"preview-section"},el={class:"virtual-stats"},ll={class:"stat-card"},al={class:"stat-number"},tl={class:"stat-card"},sl={class:"stat-number"},dl={class:"stat-card"},ul={class:"stat-number"},il={class:"stat-card"},ol={class:"stat-number"},rl={key:4,class:"preview-section"},nl={class:"member-avatars"},_l=["title"],cl=["src","alt"],pl={key:0,class:"more-members"},ml={key:5,class:"preview-section"},vl={class:"preview-footer"},fl={class:"dialog-footer"},bl=e({__name:"GroupCreateForm",props:{mode:{type:String,default:"dialog"},userRole:{type:String,default:"admin",validator:e=>["admin","distributor","owner"].includes(e)},defaultValues:{type:Object,default:()=>({})},hiddenFields:{type:Array,default:()=>[]},showPreview:{type:Boolean,default:!0},showTemplates:{type:Boolean,default:!0}},emits:["success","cancel"],setup(e,{emit:bl}){const hl=e,gl=bl,yl=l(),Vl=a(),wl=a(!1),kl=a(!1),xl=a(null),ql=a("北京"),Ul=a(""),jl={admin:{hiddenFields:[],defaultValues:{type:"normal",auto_city_replace:0,show_customer_service:1},permissions:["basic","paid_content","city_location","marketing","content","virtual","customer_service"]},distributor:{hiddenFields:["show_customer_service","ad_qr_code","virtual_income"],defaultValues:{type:"distribution",auto_city_replace:1,show_customer_service:0,read_count_display:"5万+",like_count:666,want_see_count:888,button_title:"立即加入分销群"},permissions:["basic","paid_content","city_location","marketing","content","virtual"]},owner:{hiddenFields:["virtual_income"],defaultValues:{type:"community",auto_city_replace:1,show_customer_service:1,read_count_display:"8万+",like_count:1500,want_see_count:1e3,button_title:"加入学习群"},permissions:["basic","paid_content","city_location","marketing","content","virtual","customer_service"]}},Cl=t(()=>jl[hl.userRole]||jl.admin),Al=s({title:"",price:0,payment_methods:["wechat","alipay"],type:"normal",status:"active",description:"",paid_content_type:"qr_code",qr_code:"",paid_images:[],paid_link:"",paid_link_desc:"",paid_document_content:"",paid_video_url:"",paid_video_title:"",paid_video_desc:"",auto_city_replace:0,city_insert_strategy:"auto",read_count_display:"10万+",like_count:888,want_see_count:666,button_title:"立即加入群聊",avatar_library:"qq",display_type:1,wx_accessible:1,group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",member_reviews:"",virtual_members:100,virtual_orders:50,virtual_income:5e3,today_views:1200,show_virtual_activity:1,show_member_avatars:1,show_member_reviews:1,show_customer_service:1,customer_service_title:"",customer_service_desc:"",customer_service_avatar:"",customer_service_qr:"",ad_qr_code:"",...hl.defaultValues,...Cl.value.defaultValues}),zl={title:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:200,message:"长度在 2 到 200 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入群组价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],type:[{required:!0,message:"请选择群组类型",trigger:"change"}]},Ql=e=>![...hl.hiddenFields,...Cl.value.hiddenFields].includes(e)&&Cl.value.permissions.includes(e),Fl=e=>{e&&!Al.title.includes("xxx")&&N.info('建议在群组名称中使用"xxx"作为城市占位符，如："xxx交流群"')},Ml=()=>{Al.title&&ql.value?(Ul.value=Al.title.replace(/xxx/g,ql.value),N.success("城市替换测试完成")):N.warning("请输入群组名称和测试城市")},Tl=t(()=>"/api/upload"),Rl=t(()=>({Authorization:`Bearer ${localStorage.getItem("token")}`})),Bl=e=>{const l=0===e.type.indexOf("image/"),a=e.size/1024/1024<2;return l?!!a||(N.error("图片大小不能超过 2MB!"),!1):(N.error("只能上传图片文件!"),!1)},Ll=e=>{Al.qr_code=e.url,N.success("二维码上传成功")},Hl=e=>{Al.paid_images.push({name:e.name,url:e.url}),N.success("图片上传成功")},Ol=e=>{Al.customer_service_avatar=e.url,N.success("头像上传成功")},Pl=e=>{Al.customer_service_qr=e.url,N.success("客服二维码上传成功")},Sl=e=>{Al.ad_qr_code=e.url,N.success("广告二维码上传成功")},Yl=a([]),Gl=e=>{let l="";switch(e){case"intro":l="<h3>🎯 群组特色</h3>\n<p>• <strong>专业交流</strong>：汇聚行业精英，分享最新资讯</p>\n<p>• <strong>资源共享</strong>：独家资料、工具、经验分享</p>\n<p>• <strong>人脉拓展</strong>：结识志同道合的朋友</p>\n<p>• <strong>持续成长</strong>：定期活动、培训、讲座</p>\n\n<h3>💎 群组价值</h3>\n<p>• 获得行业内部消息和机会</p>\n<p>• 学习最新的专业技能</p>\n<p>• 建立有价值的人脉关系</p>\n<p>• 参与高质量的讨论和交流</p>",Al.group_intro_content=l;break;case"faq":l="<div><strong>Q: 这个群主要讨论什么内容？</strong></div>\n<div>A: 我们主要分享行业资讯、专业技能、实用工具和经验心得，致力于为成员提供有价值的内容。</div>\n<br>\n<div><strong>Q: 群内有什么规则吗？</strong></div>\n<div>A: 我们倡导友善交流、互相尊重，禁止发布广告、恶意信息等。具体规则入群后会详细说明。</div>\n<br>\n<div><strong>Q: 如何参与群内活动？</strong></div>\n<div>A: 群内会定期组织线上分享、讨论活动，所有成员都可以积极参与，也欢迎主动分享有价值的内容。</div>\n<br>\n<div><strong>Q: 遇到问题如何联系管理员？</strong></div>\n<div>A: 可以在群内@管理员，或者私信联系。我们会及时回复和处理大家的问题。</div>",Al.faq_content=l;break;case"reviews":l="<div><strong>张先生</strong>：群里的资源真的很棒，学到了很多实用的技巧！⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>李女士</strong>：群主很用心，经常分享有价值的内容，强烈推荐！⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>王总</strong>：通过这个群认识了很多同行朋友，合作机会很多。⭐⭐⭐⭐⭐</div>\n<br>\n<div><strong>陈经理</strong>：群内氛围很好，大家都很乐于分享，收获满满。⭐⭐⭐⭐⭐</div>",Al.member_reviews=l}N.success("模板内容已插入")},Il=()=>{const e=(()=>{const e=["https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png","https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png","https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"],l=["张三","李四","王五","赵六","钱七","孙八","周九","吴十"];return Array.from({length:Math.min(Al.virtual_members,20)},(a,t)=>({id:t+1,name:l[t%l.length],avatar:e[t%e.length]}))})();xl.value={title:Ul.value||Al.title,price:Al.price,description:Al.description,type:Al.type,read_count_display:Al.read_count_display,like_count:Al.like_count,want_see_count:Al.want_see_count,button_title:Al.button_title,group_intro_title:Al.group_intro_title,group_intro_content:Al.group_intro_content,faq_title:Al.faq_title,faq_content:Al.faq_content,member_reviews:Al.member_reviews,virtual_members:Al.virtual_members,virtual_orders:Al.virtual_orders,virtual_income:Al.virtual_income,today_views:Al.today_views,show_customer_service:Al.show_customer_service,customer_service_title:Al.customer_service_title,customer_service_desc:Al.customer_service_desc,paid_content_type:Al.paid_content_type,qr_code:Al.qr_code,generated_members:e},kl.value=!0},Dl=()=>{"page"===hl.mode?yl.go(-1):gl("cancel")},El=()=>{Vl.value?.resetFields(),Object.assign(Al,{...hl.defaultValues,...Cl.value.defaultValues})},Xl=async()=>{try{await Vl.value.validate(),wl.value=!0;const e={...Al,user_role:hl.userRole},l=await W(e);if(200===l.code){if(N.success("群组创建成功！"),gl("success",l.data),"page"===hl.mode){const e={admin:"/community/groups",distributor:"/distributor/group-management",owner:"/owner/group-dashboard"};yl.push(e[hl.userRole]||"/community/groups")}}else N.error(l.message||"创建失败")}catch(e){console.error("创建群组失败:",e),N.error("创建失败，请重试")}finally{wl.value=!1}};return d(()=>{Object.assign(Al,{...hl.defaultValues,...Cl.value.defaultValues}),(async()=>{try{Yl.value=[{id:1,name:"商务交流模板",config:{read_count_display:"10万+",like_count:888,want_see_count:666,button_title:"立即加入商务群",group_intro_title:"商务交流群",group_intro_content:"专业的商务交流平台，拓展人脉，共享资源",virtual_members:150,virtual_orders:75}},{id:2,name:"技术分享模板",config:{read_count_display:"5万+",like_count:666,want_see_count:888,button_title:"加入技术群",group_intro_title:"技术交流群",group_intro_content:"技术大牛聚集地，分享最新技术动态",virtual_members:200,virtual_orders:100}},{id:3,name:"学习教育模板",config:{read_count_display:"8万+",like_count:1500,want_see_count:1e3,button_title:"加入学习群",group_intro_title:"学习交流群",group_intro_content:"专业的学习交流平台，共同进步成长",virtual_members:180,virtual_orders:90}}]}catch(e){console.error("获取营销模板失败:",e)}})()}),(l,a)=>{const t=b,s=g,d=U,W=q,bl=x,hl=j,gl=k,yl=A,jl=C,Cl=T,Yl=M,$l=w,Zl=L,Jl=O,Kl=H,Nl=P,Wl=Z,ea=K;return i(),u("div",le,["page"===e.mode?(i(),u("div",ae,[r("div",te,[a[52]||(a[52]=r("div",{class:"header-left"},[r("h1",{class:"page-title"},"创建群组"),r("p",{class:"page-subtitle"},"创建新的微信群组并配置完整的营销功能")],-1)),r("div",se,[n(s,{onClick:Dl},{default:c(()=>[n(t,null,{default:c(()=>[n(m(h))]),_:1}),a[49]||(a[49]=p(" 返回 ",-1))]),_:1,__:[49]}),e.showPreview?(i(),_(s,{key:0,type:"success",onClick:Il,disabled:!Al.title},{default:c(()=>[n(t,null,{default:c(()=>[n(m(y))]),_:1}),a[50]||(a[50]=p(" 预览效果 ",-1))]),_:1,__:[50]},8,["disabled"])):o("",!0),n(s,{type:"primary",onClick:Xl,loading:wl.value},{default:c(()=>[n(t,null,{default:c(()=>[n(m(V))]),_:1}),a[51]||(a[51]=p(" 创建群组 ",-1))]),_:1,__:[51]},8,["loading"])])])])):o("",!0),r("div",de,[n(Wl,{ref_key:"formRef",ref:Vl,model:Al,rules:zl,"label-width":"120px",size:"default"},{default:c(()=>[Ql("basic")?(i(),_($l,{key:0,class:"config-card",shadow:"never"},{header:c(()=>[r("div",ue,[n(t,null,{default:c(()=>[n(m(R))]),_:1}),a[53]||(a[53]=r("span",null,"基础信息",-1))])]),default:c(()=>[n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"群组名称",prop:"title"},{default:c(()=>[n(d,{modelValue:Al.title,"onUpdate:modelValue":a[0]||(a[0]=e=>Al.title=e),placeholder:"请输入群组名称，支持xxx占位符",maxlength:"200","show-word-limit":""},null,8,["modelValue"]),a[54]||(a[54]=r("div",{class:"form-tip"},' 💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市 ',-1))]),_:1,__:[54]})]),_:1}),n(bl,{span:12},{default:c(()=>[n(W,{label:"群组价格",prop:"price"},{default:c(()=>[n(hl,{modelValue:Al.price,"onUpdate:modelValue":a[1]||(a[1]=e=>Al.price=e),min:0,precision:2,placeholder:"0.00",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),Ql("payment_methods")?(i(),_(W,{key:0,label:"付款方式",prop:"payment_methods"},{default:c(()=>[n(jl,{modelValue:Al.payment_methods,"onUpdate:modelValue":a[2]||(a[2]=e=>Al.payment_methods=e)},{default:c(()=>[n(yl,{value:"wechat",border:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(z))]),_:1}),a[55]||(a[55]=p(" 微信支付 ",-1))]),_:1,__:[55]}),n(yl,{value:"alipay",border:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(Q))]),_:1}),a[56]||(a[56]=p(" 支付宝 ",-1))]),_:1,__:[56]}),n(yl,{value:"epay",border:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(F))]),_:1}),a[57]||(a[57]=p(" 易支付 ",-1))]),_:1,__:[57]})]),_:1},8,["modelValue"])]),_:1})):o("",!0),n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"群组类型",prop:"type"},{default:c(()=>[n(Yl,{modelValue:Al.type,"onUpdate:modelValue":a[3]||(a[3]=e=>Al.type=e),placeholder:"请选择群组类型",style:{width:"100%"}},{default:c(()=>[n(Cl,{label:"普通群",value:"normal"}),n(Cl,{label:"VIP群",value:"vip"}),n(Cl,{label:"分销群",value:"distribution"}),n(Cl,{label:"测试群",value:"test"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:12},{default:c(()=>[Ql("status")?(i(),_(W,{key:0,label:"发布状态",prop:"status"},{default:c(()=>[n(Yl,{modelValue:Al.status,"onUpdate:modelValue":a[4]||(a[4]=e=>Al.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:c(()=>[n(Cl,{label:"立即发布",value:"active"}),n(Cl,{label:"保存草稿",value:"draft"})]),_:1},8,["modelValue"])]),_:1})):o("",!0)]),_:1})]),_:1}),n(W,{label:"群组描述",prop:"description"},{default:c(()=>[n(d,{modelValue:Al.description,"onUpdate:modelValue":a[5]||(a[5]=e=>Al.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1})):o("",!0),Ql("city_location")?(i(),_($l,{key:1,class:"config-card",shadow:"never"},{header:c(()=>[r("div",ie,[n(t,null,{default:c(()=>[n(m(B))]),_:1}),a[58]||(a[58]=r("span",null,"城市定位",-1)),n(Zl,{modelValue:Al.auto_city_replace,"onUpdate:modelValue":a[6]||(a[6]=e=>Al.auto_city_replace=e),class:"header-switch","active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"关闭",onChange:Fl},null,8,["modelValue"])])]),default:c(()=>[Al.auto_city_replace?(i(),u("div",oe,[n(W,{label:"插入策略"},{default:c(()=>[n(Yl,{modelValue:Al.city_insert_strategy,"onUpdate:modelValue":a[7]||(a[7]=e=>Al.city_insert_strategy=e),placeholder:"选择策略",style:{width:"100%"}},{default:c(()=>[n(Cl,{label:"自动替换xxx",value:"auto"}),n(Cl,{label:"前缀插入",value:"prefix"}),n(Cl,{label:"后缀插入",value:"suffix"}),n(Cl,{label:"自然插入",value:"natural"})]),_:1},8,["modelValue"])]),_:1}),n(W,{label:"测试城市替换"},{default:c(()=>[n(gl,{gutter:12},{default:c(()=>[n(bl,{span:8},{default:c(()=>[n(d,{modelValue:ql.value,"onUpdate:modelValue":a[8]||(a[8]=e=>ql.value=e),placeholder:"输入城市名"},null,8,["modelValue"])]),_:1}),n(bl,{span:8},{default:c(()=>[n(s,{onClick:Ml},{default:c(()=>a[59]||(a[59]=[p("测试替换",-1)])),_:1,__:[59]})]),_:1}),n(bl,{span:8},{default:c(()=>[n(d,{modelValue:Ul.value,"onUpdate:modelValue":a[9]||(a[9]=e=>Ul.value=e),placeholder:"替换结果",readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])):o("",!0)]),_:1})):o("",!0),Ql("paid_content")?(i(),_($l,{key:2,class:"config-card",shadow:"never"},{header:c(()=>[r("div",re,[n(t,null,{default:c(()=>[n(m(Y))]),_:1}),a[60]||(a[60]=r("span",null,"付费后内容",-1))])]),default:c(()=>[n(W,{label:"内容类型",prop:"paid_content_type"},{default:c(()=>[n(Kl,{modelValue:Al.paid_content_type,"onUpdate:modelValue":a[10]||(a[10]=e=>Al.paid_content_type=e)},{default:c(()=>[n(Jl,{value:"qr_code"},{default:c(()=>a[61]||(a[61]=[p("入群二维码",-1)])),_:1,__:[61]}),n(Jl,{value:"image"},{default:c(()=>a[62]||(a[62]=[p("图片资源",-1)])),_:1,__:[62]}),n(Jl,{value:"link"},{default:c(()=>a[63]||(a[63]=[p("下载链接",-1)])),_:1,__:[63]}),n(Jl,{value:"document"},{default:c(()=>a[64]||(a[64]=[p("文档内容",-1)])),_:1,__:[64]}),n(Jl,{value:"video"},{default:c(()=>a[65]||(a[65]=[p("视频内容",-1)])),_:1,__:[65]})]),_:1},8,["modelValue"])]),_:1}),"qr_code"===Al.paid_content_type?(i(),u("div",ne,[n(W,{label:"入群二维码",prop:"qr_code"},{default:c(()=>[n(Nl,{class:"qr-uploader",action:Tl.value,headers:Rl.value,"show-file-list":!1,"on-success":Ll,"before-upload":Bl},{default:c(()=>[Al.qr_code?(i(),u("img",{key:0,src:Al.qr_code,class:"qr-image"},null,8,_e)):(i(),_(t,{key:1,class:"qr-uploader-icon"},{default:c(()=>[n(m(S))]),_:1}))]),_:1},8,["action","headers"]),a[66]||(a[66]=r("div",{class:"upload-tip"},"建议尺寸：400x400像素，支持jpg/png格式",-1))]),_:1,__:[66]})])):o("",!0),"image"===Al.paid_content_type?(i(),u("div",ce,[n(W,{label:"付费图片"},{default:c(()=>[n(Nl,{class:"image-uploader",action:Tl.value,headers:Rl.value,"file-list":Al.paid_images,"on-success":Hl,"before-upload":Bl,multiple:""},{default:c(()=>[n(s,{type:"primary"},{default:c(()=>[n(t,null,{default:c(()=>[n(m(S))]),_:1}),a[67]||(a[67]=p(" 上传图片 ",-1))]),_:1,__:[67]})]),_:1},8,["action","headers","file-list"])]),_:1})])):o("",!0),"link"===Al.paid_content_type?(i(),u("div",pe,[n(W,{label:"下载链接"},{default:c(()=>[n(d,{modelValue:Al.paid_link,"onUpdate:modelValue":a[11]||(a[11]=e=>Al.paid_link=e),placeholder:"请输入下载链接"},null,8,["modelValue"])]),_:1}),n(W,{label:"链接描述"},{default:c(()=>[n(d,{modelValue:Al.paid_link_desc,"onUpdate:modelValue":a[12]||(a[12]=e=>Al.paid_link_desc=e),placeholder:"请输入链接描述"},null,8,["modelValue"])]),_:1})])):o("",!0),"document"===Al.paid_content_type?(i(),u("div",me,[n(W,{label:"文档内容"},{default:c(()=>[n(d,{modelValue:Al.paid_document_content,"onUpdate:modelValue":a[13]||(a[13]=e=>Al.paid_document_content=e),type:"textarea",rows:6,placeholder:"请输入文档内容"},null,8,["modelValue"])]),_:1})])):o("",!0),"video"===Al.paid_content_type?(i(),u("div",ve,[n(W,{label:"视频链接"},{default:c(()=>[n(d,{modelValue:Al.paid_video_url,"onUpdate:modelValue":a[14]||(a[14]=e=>Al.paid_video_url=e),placeholder:"请输入视频链接"},null,8,["modelValue"])]),_:1}),n(W,{label:"视频标题"},{default:c(()=>[n(d,{modelValue:Al.paid_video_title,"onUpdate:modelValue":a[15]||(a[15]=e=>Al.paid_video_title=e),placeholder:"请输入视频标题"},null,8,["modelValue"])]),_:1}),n(W,{label:"视频描述"},{default:c(()=>[n(d,{modelValue:Al.paid_video_desc,"onUpdate:modelValue":a[16]||(a[16]=e=>Al.paid_video_desc=e),type:"textarea",rows:3,placeholder:"请输入视频描述"},null,8,["modelValue"])]),_:1})])):o("",!0)]),_:1})):o("",!0),Ql("marketing")?(i(),_($l,{key:3,class:"config-card",shadow:"never"},{header:c(()=>[r("div",fe,[n(t,null,{default:c(()=>[n(m(G))]),_:1}),a[68]||(a[68]=r("span",null,"营销展示",-1))])]),default:c(()=>[n(gl,{gutter:24},{default:c(()=>[n(bl,{span:8},{default:c(()=>[n(W,{label:"阅读数显示"},{default:c(()=>[n(d,{modelValue:Al.read_count_display,"onUpdate:modelValue":a[17]||(a[17]=e=>Al.read_count_display=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"点赞数"},{default:c(()=>[n(hl,{modelValue:Al.like_count,"onUpdate:modelValue":a[18]||(a[18]=e=>Al.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"想看数"},{default:c(()=>[n(hl,{modelValue:Al.want_see_count,"onUpdate:modelValue":a[19]||(a[19]=e=>Al.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"入群按钮文案"},{default:c(()=>[n(d,{modelValue:Al.button_title,"onUpdate:modelValue":a[20]||(a[20]=e=>Al.button_title=e),placeholder:"如：立即加入群聊"},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:12},{default:c(()=>[n(W,{label:"头像库选择"},{default:c(()=>[n(Yl,{modelValue:Al.avatar_library,"onUpdate:modelValue":a[21]||(a[21]=e=>Al.avatar_library=e),style:{width:"100%"}},{default:c(()=>[n(Cl,{label:"QQ头像库",value:"qq"}),n(Cl,{label:"微信头像库",value:"wechat"}),n(Cl,{label:"随机头像库",value:"random"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"显示类型"},{default:c(()=>[n(Yl,{modelValue:Al.display_type,"onUpdate:modelValue":a[22]||(a[22]=e=>Al.display_type=e),style:{width:"100%"}},{default:c(()=>[n(Cl,{label:"标准显示",value:1}),n(Cl,{label:"简洁显示",value:2}),n(Cl,{label:"详细显示",value:3})]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:12},{default:c(()=>[n(W,{label:"微信访问权限"},{default:c(()=>[n(Zl,{modelValue:Al.wx_accessible,"onUpdate:modelValue":a[23]||(a[23]=e=>Al.wx_accessible=e),"active-value":1,"inactive-value":0,"active-text":"允许","inactive-text":"限制"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):o("",!0),Ql("content")?(i(),_($l,{key:4,class:"config-card",shadow:"never"},{header:c(()=>[r("div",be,[n(t,null,{default:c(()=>[n(m(Y))]),_:1}),a[69]||(a[69]=r("span",null,"内容管理",-1))])]),default:c(()=>[n(W,{label:"群简介标题"},{default:c(()=>[n(d,{modelValue:Al.group_intro_title,"onUpdate:modelValue":a[24]||(a[24]=e=>Al.group_intro_title=e),placeholder:"群简介"},null,8,["modelValue"])]),_:1}),n(W,{label:"群简介内容"},{default:c(()=>[r("div",he,[r("div",ge,[n(s,{size:"small",type:"primary",onClick:a[25]||(a[25]=e=>Gl("intro"))},{default:c(()=>[n(t,null,{default:c(()=>[n(m(I))]),_:1}),a[70]||(a[70]=p(" 插入模板 ",-1))]),_:1,__:[70]}),n(s,{size:"small",onClick:a[26]||(a[26]=e=>{N.info("图片上传功能开发中，敬请期待")})},{default:c(()=>[n(t,null,{default:c(()=>[n(m(D))]),_:1}),a[71]||(a[71]=p(" 插入图片 ",-1))]),_:1,__:[71]}),n(s,{size:"small",onClick:a[27]||(a[27]=e=>Al.group_intro_content=""),type:"danger",plain:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(E))]),_:1}),a[72]||(a[72]=p(" 清空内容 ",-1))]),_:1,__:[72]})]),r("div",ye,[n(ee,{modelValue:Al.group_intro_content,"onUpdate:modelValue":a[28]||(a[28]=e=>Al.group_intro_content=e),height:200,placeholder:"详细介绍群组的价值和特色，支持富文本格式。可以添加粗体、斜体、列表、链接、图片等内容。","max-length":2e3},null,8,["modelValue"])]),r("div",Ve,[n(t,null,{default:c(()=>[n(m(R))]),_:1}),a[73]||(a[73]=r("span",null,[p("支持富文本格式，可以添加"),r("strong",null,"粗体"),p("、"),r("em",null,"斜体"),p("、列表、链接等内容")],-1))])])]),_:1}),n(W,{label:"FAQ标题"},{default:c(()=>[n(d,{modelValue:Al.faq_title,"onUpdate:modelValue":a[29]||(a[29]=e=>Al.faq_title=e),placeholder:"常见问题"},null,8,["modelValue"])]),_:1}),n(W,{label:"常见问题"},{default:c(()=>[r("div",we,[r("div",ke,[n(s,{size:"small",type:"primary",onClick:a[30]||(a[30]=e=>Gl("faq"))},{default:c(()=>[n(t,null,{default:c(()=>[n(m(I))]),_:1}),a[74]||(a[74]=p(" 插入FAQ模板 ",-1))]),_:1,__:[74]}),n(s,{size:"small",onClick:a[31]||(a[31]=e=>Al.faq_content=""),type:"danger",plain:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(E))]),_:1}),a[75]||(a[75]=p(" 清空内容 ",-1))]),_:1,__:[75]})]),r("div",xe,[n(ee,{modelValue:Al.faq_content,"onUpdate:modelValue":a[32]||(a[32]=e=>Al.faq_content=e),height:180,placeholder:"输入常见问题和答案，支持富文本格式。建议格式：Q: 问题内容 A: 答案内容","max-length":3e3},null,8,["modelValue"])]),r("div",qe,[n(t,null,{default:c(()=>[n(m(R))]),_:1}),a[76]||(a[76]=r("span",null,[p("建议格式："),r("strong",null,"Q: 问题内容"),p(),r("br"),p(),r("strong",null,"A: 答案内容"),p("，每个问答占一行")],-1))])])]),_:1}),n(W,{label:"群友评论"},{default:c(()=>[r("div",Ue,[r("div",je,[n(s,{size:"small",type:"primary",onClick:a[33]||(a[33]=e=>Gl("reviews"))},{default:c(()=>[n(t,null,{default:c(()=>[n(m(I))]),_:1}),a[77]||(a[77]=p(" 插入评价模板 ",-1))]),_:1,__:[77]}),n(s,{size:"small",onClick:a[34]||(a[34]=e=>Al.member_reviews=""),type:"danger",plain:""},{default:c(()=>[n(t,null,{default:c(()=>[n(m(E))]),_:1}),a[78]||(a[78]=p(" 清空内容 ",-1))]),_:1,__:[78]})]),r("div",Ce,[n(ee,{modelValue:Al.member_reviews,"onUpdate:modelValue":a[35]||(a[35]=e=>Al.member_reviews=e),height:160,placeholder:"输入群友评价内容，支持富文本格式。建议格式：用户名：评价内容 ⭐⭐⭐⭐⭐","max-length":2e3},null,8,["modelValue"])]),r("div",Ae,[n(t,null,{default:c(()=>[n(m(R))]),_:1}),a[79]||(a[79]=r("span",null,[p("建议格式："),r("strong",null,"用户名：评价内容 ⭐⭐⭐⭐⭐"),p("，每个评价占一行")],-1))])])]),_:1})]),_:1})):o("",!0),Ql("virtual")?(i(),_($l,{key:5,class:"config-card",shadow:"never"},{header:c(()=>[r("div",ze,[n(t,null,{default:c(()=>[n(m(X))]),_:1}),a[80]||(a[80]=r("span",null,"虚拟数据",-1))])]),default:c(()=>[n(gl,{gutter:24},{default:c(()=>[n(bl,{span:8},{default:c(()=>[n(W,{label:"虚拟成员数"},{default:c(()=>[n(hl,{modelValue:Al.virtual_members,"onUpdate:modelValue":a[36]||(a[36]=e=>Al.virtual_members=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"虚拟订单数"},{default:c(()=>[n(hl,{modelValue:Al.virtual_orders,"onUpdate:modelValue":a[37]||(a[37]=e=>Al.virtual_orders=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"虚拟收入"},{default:c(()=>[n(hl,{modelValue:Al.virtual_income,"onUpdate:modelValue":a[38]||(a[38]=e=>Al.virtual_income=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(gl,{gutter:24},{default:c(()=>[n(bl,{span:8},{default:c(()=>[n(W,{label:"今日浏览量"},{default:c(()=>[n(hl,{modelValue:Al.today_views,"onUpdate:modelValue":a[39]||(a[39]=e=>Al.today_views=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"显示虚拟活动"},{default:c(()=>[n(Zl,{modelValue:Al.show_virtual_activity,"onUpdate:modelValue":a[40]||(a[40]=e=>Al.show_virtual_activity=e),"active-value":1,"inactive-value":0,"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:8},{default:c(()=>[n(W,{label:"显示成员头像"},{default:c(()=>[n(Zl,{modelValue:Al.show_member_avatars,"onUpdate:modelValue":a[41]||(a[41]=e=>Al.show_member_avatars=e),"active-value":1,"inactive-value":0,"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(W,{label:"显示成员评价"},{default:c(()=>[n(Zl,{modelValue:Al.show_member_reviews,"onUpdate:modelValue":a[42]||(a[42]=e=>Al.show_member_reviews=e),"active-value":1,"inactive-value":0,"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])]),_:1})]),_:1})):o("",!0),Ql("customer_service")?(i(),_($l,{key:6,class:"config-card",shadow:"never"},{header:c(()=>[r("div",Qe,[n(t,null,{default:c(()=>[n(m($))]),_:1}),a[81]||(a[81]=r("span",null,"客服信息",-1)),n(Zl,{modelValue:Al.show_customer_service,"onUpdate:modelValue":a[43]||(a[43]=e=>Al.show_customer_service=e),class:"header-switch","active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"关闭"},null,8,["modelValue"])])]),default:c(()=>[Al.show_customer_service?(i(),u("div",Fe,[n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"客服标题"},{default:c(()=>[n(d,{modelValue:Al.customer_service_title,"onUpdate:modelValue":a[44]||(a[44]=e=>Al.customer_service_title=e),placeholder:"联系客服"},null,8,["modelValue"])]),_:1})]),_:1}),n(bl,{span:12},{default:c(()=>[n(W,{label:"客服描述"},{default:c(()=>[n(d,{modelValue:Al.customer_service_desc,"onUpdate:modelValue":a[45]||(a[45]=e=>Al.customer_service_desc=e),placeholder:"有问题请联系客服"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),n(gl,{gutter:24},{default:c(()=>[n(bl,{span:12},{default:c(()=>[n(W,{label:"客服头像"},{default:c(()=>[n(Nl,{class:"avatar-uploader",action:Tl.value,headers:Rl.value,"show-file-list":!1,"on-success":Ol,"before-upload":Bl},{default:c(()=>[Al.customer_service_avatar?(i(),u("img",{key:0,src:Al.customer_service_avatar,class:"avatar"},null,8,Me)):(i(),_(t,{key:1,class:"avatar-uploader-icon"},{default:c(()=>[n(m(S))]),_:1}))]),_:1},8,["action","headers"])]),_:1})]),_:1}),n(bl,{span:12},{default:c(()=>[n(W,{label:"客服二维码"},{default:c(()=>[n(Nl,{class:"qr-uploader",action:Tl.value,headers:Rl.value,"show-file-list":!1,"on-success":Pl,"before-upload":Bl},{default:c(()=>[Al.customer_service_qr?(i(),u("img",{key:0,src:Al.customer_service_qr,class:"qr-image"},null,8,Te)):(i(),_(t,{key:1,class:"qr-uploader-icon"},{default:c(()=>[n(m(S))]),_:1}))]),_:1},8,["action","headers"])]),_:1})]),_:1})]),_:1}),n(W,{label:"广告二维码"},{default:c(()=>[n(Nl,{class:"qr-uploader",action:Tl.value,headers:Rl.value,"show-file-list":!1,"on-success":Sl,"before-upload":Bl},{default:c(()=>[Al.ad_qr_code?(i(),u("img",{key:0,src:Al.ad_qr_code,class:"qr-image"},null,8,Re)):(i(),_(t,{key:1,class:"qr-uploader-icon"},{default:c(()=>[n(m(S))]),_:1}))]),_:1},8,["action","headers"]),a[82]||(a[82]=r("div",{class:"upload-tip"},"用于推广的二维码",-1))]),_:1,__:[82]})])):o("",!0)]),_:1})):o("",!0),"dialog"===e.mode?(i(),u("div",Be,[n(s,{onClick:Dl},{default:c(()=>a[83]||(a[83]=[p("取消",-1)])),_:1,__:[83]}),n(s,{onClick:El},{default:c(()=>a[84]||(a[84]=[p("重置",-1)])),_:1,__:[84]}),e.showPreview?(i(),_(s,{key:0,type:"success",onClick:Il,disabled:!Al.title},{default:c(()=>[n(t,null,{default:c(()=>[n(m(y))]),_:1}),a[85]||(a[85]=p(" 预览 ",-1))]),_:1,__:[85]},8,["disabled"])):o("",!0),n(s,{type:"primary",onClick:Xl,loading:wl.value},{default:c(()=>[n(t,null,{default:c(()=>[n(m(V))]),_:1}),a[86]||(a[86]=p(" 创建群组 ",-1))]),_:1,__:[86]},8,["loading"])])):o("",!0)]),_:1},8,["model"])]),n(ea,{modelValue:kl.value,"onUpdate:modelValue":a[48]||(a[48]=e=>kl.value=e),title:"群组完整预览",width:"800px",class:"preview-dialog"},{footer:c(()=>[r("div",fl,[n(s,{onClick:a[46]||(a[46]=e=>kl.value=!1)},{default:c(()=>a[96]||(a[96]=[p("关闭预览",-1)])),_:1,__:[96]}),n(s,{type:"primary",onClick:a[47]||(a[47]=e=>kl.value=!1)},{default:c(()=>a[97]||(a[97]=[p(" 确认效果 ",-1)])),_:1,__:[97]})])]),default:c(()=>[xl.value?(i(),u("div",Le,[r("div",He,[r("h2",Oe,J(xl.value.title),1),r("div",Pe,[r("span",Se,[n(t,null,{default:c(()=>[n(m(y))]),_:1}),p(" "+J(xl.value.read_count_display),1)]),r("span",Ye,[n(t,null,{default:c(()=>[n(m(I))]),_:1}),p(" "+J(xl.value.like_count),1)]),r("span",Ge,[n(t,null,{default:c(()=>[n(m(z))]),_:1}),p(" "+J(xl.value.want_see_count),1)])]),r("div",Ie,[a[87]||(a[87]=r("span",{class:"price-label"},"群组价格:",-1)),r("span",De,"¥"+J(xl.value.price),1)])]),xl.value.description?(i(),u("div",Ee,[a[88]||(a[88]=r("h4",null,"群组描述",-1)),r("p",null,J(xl.value.description),1)])):o("",!0),xl.value.group_intro_content?(i(),u("div",Xe,[r("h4",null,J(xl.value.group_intro_title||"群简介"),1),r("div",{class:"rich-content",innerHTML:xl.value.group_intro_content},null,8,$e)])):o("",!0),xl.value.faq_content?(i(),u("div",Ze,[r("h4",null,J(xl.value.faq_title||"常见问题"),1),r("div",{class:"rich-content",innerHTML:xl.value.faq_content},null,8,Je)])):o("",!0),xl.value.member_reviews?(i(),u("div",Ke,[a[89]||(a[89]=r("h4",null,"群友评价",-1)),r("div",{class:"rich-content",innerHTML:xl.value.member_reviews},null,8,Ne)])):o("",!0),r("div",We,[a[94]||(a[94]=r("h4",null,"群组数据",-1)),r("div",el,[r("div",ll,[r("div",al,J(xl.value.virtual_members),1),a[90]||(a[90]=r("div",{class:"stat-label"},"群成员",-1))]),r("div",tl,[r("div",sl,J(xl.value.virtual_orders),1),a[91]||(a[91]=r("div",{class:"stat-label"},"订单数",-1))]),r("div",dl,[r("div",ul,"¥"+J(xl.value.virtual_income),1),a[92]||(a[92]=r("div",{class:"stat-label"},"总收入",-1))]),r("div",il,[r("div",ol,J(xl.value.today_views),1),a[93]||(a[93]=r("div",{class:"stat-label"},"今日浏览",-1))])])]),xl.value.generated_members?(i(),u("div",rl,[a[95]||(a[95]=r("h4",null,"群成员展示",-1)),r("div",nl,[(i(!0),u(v,null,f(xl.value.generated_members.slice(0,10),e=>(i(),u("div",{key:e.id,class:"member-avatar",title:e.name},[r("img",{src:e.avatar,alt:e.name},null,8,cl)],8,_l))),128)),xl.value.generated_members.length>10?(i(),u("div",pl," +"+J(xl.value.generated_members.length-10),1)):o("",!0)])])):o("",!0),xl.value.show_customer_service?(i(),u("div",ml,[r("h4",null,J(xl.value.customer_service_title||"联系客服"),1),r("p",null,J(xl.value.customer_service_desc||"有问题请联系客服"),1)])):o("",!0),r("div",vl,[n(s,{type:"primary",size:"large",class:"join-button"},{default:c(()=>[p(J(xl.value.button_title),1)]),_:1})])])):o("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-537a81e5"]]);export{bl as G};
