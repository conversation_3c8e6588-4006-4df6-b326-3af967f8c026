<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domain_pools', function (Blueprint $table) {
            // 健康监控字段
            $table->integer('health_score')->default(100)->comment('健康分数 0-100')->after('status');
            $table->timestamp('last_check_at')->nullable()->comment('最后检查时间')->after('health_score');
            $table->integer('failure_count')->default(0)->comment('失败次数')->after('last_check_at');
            $table->boolean('is_primary')->default(false)->comment('是否为主域名')->after('failure_count');
            
            // 使用统计字段
            $table->integer('usage_count')->default(0)->comment('使用次数')->after('is_primary');
            $table->integer('max_daily_usage')->default(1000)->comment('每日最大使用次数')->after('usage_count');
            $table->integer('daily_usage_count')->default(0)->comment('今日使用次数')->after('max_daily_usage');
            $table->date('usage_date')->nullable()->comment('使用统计日期')->after('daily_usage_count');
            
            // 风险评估字段
            $table->enum('risk_level', ['low', 'medium', 'high'])->default('low')->comment('风险等级')->after('usage_date');
            $table->json('risk_factors')->nullable()->comment('风险因素')->after('risk_level');
            
            // 性能监控字段
            $table->integer('avg_response_time')->default(0)->comment('平均响应时间(ms)')->after('risk_factors');
            $table->decimal('uptime_rate', 5, 2)->default(100.00)->comment('可用率')->after('avg_response_time');
            
            // 分组和标签
            $table->string('group_tag', 50)->nullable()->comment('分组标签')->after('uptime_rate');
            $table->json('tags')->nullable()->comment('标签')->after('group_tag');
            
            // 索引优化
            $table->index(['status', 'risk_level', 'health_score'], 'idx_status_risk_health');
            $table->index(['usage_count', 'max_daily_usage'], 'idx_usage');
            $table->index('last_check_at', 'idx_last_check');
            $table->index('is_primary', 'idx_is_primary');
            $table->index('group_tag', 'idx_group_tag');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domain_pools', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_status_risk_health');
            $table->dropIndex('idx_usage');
            $table->dropIndex('idx_last_check');
            $table->dropIndex('idx_is_primary');
            $table->dropIndex('idx_group_tag');
            
            // 删除字段
            $table->dropColumn([
                'health_score', 'last_check_at', 'failure_count', 'is_primary',
                'usage_count', 'max_daily_usage', 'daily_usage_count', 'usage_date',
                'risk_level', 'risk_factors',
                'avg_response_time', 'uptime_rate',
                'group_tag', 'tags'
            ]);
        });
    }
};