<template>
  <div v-if="!item.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="resolvePath"
      :popper-class="'modern-submenu-popper'"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span class="menu-title">{{ item.meta?.title }}</span>
      </template>
      
      <sidebar-item
        v-for="child in visibleChildren"
        :key="child.path"
        :item="child"
        :base-path="resolvePath"
      />
    </el-sub-menu>
    
    <!-- 单个菜单项 -->
    <el-menu-item
      v-else
      :index="resolvePath"
      :class="itemClasses"
      @mouseover="handleItemHover"
      @click="handleItemClick"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span class="menu-title">{{ item.meta?.title }}</span>
        <transition name="badge-slide">
          <el-badge
            v-if="item.meta?.badge"
            :value="item.meta.badge"
            :type="item.meta.badgeType || 'danger'"
            class="menu-badge"
          />
        </transition>
        <el-icon v-if="item.meta?.isNew" class="new-indicator" title="新功能">
          <StarFilled />
        </el-icon>
        <el-icon v-if="item.meta?.isHot" class="hot-indicator" title="热门">
          <Promotion />
        </el-icon>
      </template>
    </el-menu-item>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { isExternal } from '@/utils/validate'
import { StarFilled, Promotion } from '@element-plus/icons-vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const route = useRoute()

// 定义事件
const emit = defineEmits(['item-click', 'item-hover'])

// 计算属性
const hasChildren = computed(() => {
  return props.item.children && props.item.children.length > 0
})

const visibleChildren = computed(() => {
  if (!props.item.children) return []
  return props.item.children.filter(child => !child.meta?.hidden)
})

const resolvePath = computed(() => {
  if (isExternal(props.item.path)) {
    return props.item.path
  }
  
  if (props.basePath) {
    return `${props.basePath}/${props.item.path}`.replace(/\/+/g, '/')
  }
  
  return props.item.path
})

const isActive = computed(() => {
  return route.path === resolvePath.value
})

// 新增方法
const handleItemClick = () => {
  emit('item-click', props.item)
}

const handleItemHover = () => {
  emit('item-hover', props.item)
}

// 新增计算属性
const hasNotification = computed(() => {
  return props.item.meta?.badge > 0
})

const itemClasses = computed(() => ({
  'is-active': isActive.value,
  'has-notification': hasNotification.value,
  'is-new': props.item.meta?.isNew,
  'is-hot': props.item.meta?.isHot,
  'is-important': props.item.meta?.important,
  'nav-transition': true
}))
</script>

<style lang="scss" scoped>
.menu-title {
  font-weight: 500;
  transition: all 0.3s ease;
}

.menu-badge {
  margin-left: auto;
}

// 子菜单样式
:deep(.el-sub-menu) {
  .el-sub-menu__title {
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }
    
    &:hover::before {
      left: 100%;
    }
  }
  
  .el-menu {
    background: rgba(0, 0, 0, 0.1);
    
    .el-menu-item {
      padding-left: 60px !important;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 40px;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
      }
      
      &:hover::before,
      &.is-active::before {
        background: #ffffff;
        transform: translateY(-50%) scale(1.2);
      }
      
      &.is-active {
        background: rgba(255, 255, 255, 0.1);
        
        &::after {
          content: '';
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #ffffff;
          border-radius: 2px;
        }
      }
    }
  }
}

// 菜单项激活状态
:deep(.el-menu-item.is-active) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 0 2px 2px 0;
  }
}

// 外部链接样式
.external-link {
  .menu-title::after {
    content: '↗';
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.6;
  }
}

// 新增动画效果
.badge-slide-enter-active,
.badge-slide-leave-active {
  transition: all 0.3s ease;
  transform-origin: right center;
}

.badge-slide-enter-from,
.badge-slide-leave-to {
  opacity: 0;
  transform: scale(0);
}

// 新功能指示器
.new-indicator {
  color: #F59E0B;
  font-size: 12px;
  margin-left: 4px;
  animation: sparkle 2s infinite;
}

.hot-indicator {
  color: #EF4444;
  font-size: 12px;
  margin-left: 4px;
  animation: pulse 2s infinite;
}

@keyframes sparkle {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.5; 
    transform: scale(1.1); 
  }
}

// 通知状态样式
.has-notification {
  .menu-title {
    font-weight: 600;
    color: var(--el-color-primary);
  }
}

// 新功能样式
.is-new {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #F59E0B, #FCD34D);
    border-radius: 2px 2px 0 0;
  }
}

// 热门功能样式
.is-hot {
  position: relative;
  
  &::after {
    content: 'HOT';
    position: absolute;
    top: -2px;
    right: 8px;
    background: #EF4444;
    color: white;
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: bold;
    animation: bounce 2s infinite;
  }
}

// 重要项目样式
.is-important {
  border-left: 3px solid var(--el-color-warning);
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.05), transparent);
}

// 增强过渡效果
.nav-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateX(2px);
    background: var(--el-fill-color-light);
    
    .menu-title {
      color: var(--el-color-primary);
    }
    
    .el-icon {
      transform: scale(1.1);
      color: var(--el-color-primary);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .menu-title {
    font-size: 14px;
  }
}
</style>

<style lang="scss">
// 全局子菜单弹出层样式
.modern-submenu-popper {
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
  
  .el-menu {
    background: transparent !important;
    border: none !important;
    
    .el-menu-item {
      color: rgba(255, 255, 255, 0.75) !important;
      transition: all 0.3s ease !important;
      border-radius: 6px !important;
      margin: 4px 8px !important;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        transform: translateX(4px) !important;
      }
      
      &.is-active {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
      }
    }
  }
}
</style>