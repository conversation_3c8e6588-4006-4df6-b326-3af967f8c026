<template>
  <div class="ai-content-generator">
    <el-card class="generator-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><Star /></el-icon>
            AI智能内容生成
          </span>
          <el-tag type="success" size="small">Beta</el-tag>
        </div>
      </template>

      <!-- 内容类型选择 -->
      <div class="content-type-selector">
        <el-radio-group v-model="selectedType" @change="handleTypeChange">
          <el-radio-button label="title">群组标题</el-radio-button>
          <el-radio-button label="description">群组描述</el-radio-button>
          <el-radio-button label="faq">FAQ问答</el-radio-button>
          <el-radio-button label="reviews">用户评论</el-radio-button>
          <el-radio-button label="intro">群组介绍</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 生成参数配置 -->
      <div class="generation-config">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行业类型">
              <el-select v-model="config.industry" placeholder="选择行业">
                <el-option label="互联网/科技" value="tech" />
                <el-option label="金融/投资" value="finance" />
                <el-option label="教育/培训" value="education" />
                <el-option label="电商/零售" value="ecommerce" />
                <el-option label="健康/医疗" value="health" />
                <el-option label="娱乐/游戏" value="entertainment" />
                <el-option label="房产/装修" value="realestate" />
                <el-option label="美食/餐饮" value="food" />
                <el-option label="旅游/出行" value="travel" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标人群">
              <el-select v-model="config.audience" placeholder="选择目标人群">
                <el-option label="学生群体" value="students" />
                <el-option label="职场新人" value="newcomers" />
                <el-option label="资深专家" value="experts" />
                <el-option label="创业者" value="entrepreneurs" />
                <el-option label="宝妈群体" value="mothers" />
                <el-option label="中老年人" value="seniors" />
                <el-option label="年轻白领" value="youngpro" />
                <el-option label="自由职业者" value="freelancers" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="内容风格">
              <el-select v-model="config.style" placeholder="选择风格">
                <el-option label="专业严谨" value="professional" />
                <el-option label="轻松幽默" value="casual" />
                <el-option label="温馨亲切" value="warm" />
                <el-option label="激励鼓舞" value="motivational" />
                <el-option label="简洁明了" value="concise" />
                <el-option label="详细全面" value="detailed" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生成数量">
              <el-input-number 
                v-model="config.count" 
                :min="1" 
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 关键词输入 -->
        <el-form-item label="关键词">
          <el-input
            v-model="config.keywords"
            placeholder="输入相关关键词，用逗号分隔"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <!-- 自定义提示词 -->
        <el-form-item label="自定义要求">
          <el-input
            v-model="config.customPrompt"
            placeholder="描述您的具体要求，如：需要包含价格信息、突出优势等"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </div>

      <!-- 生成按钮 -->
      <div class="generation-actions">
        <el-button 
          type="primary" 
          @click="generateContent" 
          :loading="generating"
          size="large"
        >
          <el-icon><Star /></el-icon>
          {{ generating ? '生成中...' : '智能生成' }}
        </el-button>
        <el-button @click="clearResults">清空结果</el-button>
      </div>

      <!-- 生成结果 -->
      <div v-if="generatedResults.length > 0" class="generation-results">
        <el-divider content-position="left">生成结果</el-divider>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in generatedResults" 
            :key="index"
            class="result-item"
          >
            <div class="result-header">
              <span class="result-index">方案 {{ index + 1 }}</span>
              <div class="result-actions">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="useResult(result)"
                >
                  使用此内容
                </el-button>
                <el-button 
                  size="small" 
                  @click="copyResult(result)"
                >
                  复制
                </el-button>
                <el-rate 
                  v-model="result.rating" 
                  size="small"
                  @change="rateResult(result, $event)"
                />
              </div>
            </div>
            <div class="result-content">
              <pre>{{ result.content }}</pre>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions">
          <el-button @click="regenerateAll">重新生成全部</el-button>
          <el-button @click="exportResults">导出结果</el-button>
        </div>
      </div>

      <!-- 历史记录 -->
      <div v-if="generationHistory.length > 0" class="generation-history">
        <el-divider content-position="left">历史记录</el-divider>
        
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in generationHistory.slice(0, 5)"
            :key="index"
            :timestamp="formatTime(history.timestamp)"
          >
            <div class="history-item">
              <div class="history-type">{{ getTypeLabel(history.type) }}</div>
              <div class="history-content">{{ history.content.substring(0, 50) }}...</div>
              <el-button 
                type="text" 
                size="small" 
                @click="reuseHistory(history)"
              >
                重新使用
              </el-button>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'

const emit = defineEmits(['content-generated'])

// 响应式数据
const selectedType = ref('title')
const generating = ref(false)
const generatedResults = ref([])
const generationHistory = ref([])

const config = reactive({
  industry: '',
  audience: '',
  style: 'professional',
  count: 3,
  keywords: '',
  customPrompt: ''
})

// 内容生成模板
const contentTemplates = {
  title: {
    tech: [
      '{city}程序员技术交流群',
      '{city}互联网从业者聚集地',
      '{city}IT精英学习成长群',
      '{city}技术大牛经验分享群'
    ],
    finance: [
      '{city}投资理财交流群',
      '{city}财富增值学习群',
      '{city}金融精英圈',
      '{city}理财规划师群'
    ],
    education: [
      '{city}学习成长交流群',
      '{city}知识分享互助群',
      '{city}教育资源共享群',
      '{city}终身学习者联盟'
    ]
  },
  description: {
    professional: '汇聚行业精英，分享前沿资讯，共同成长进步。严格筛选成员，确保高质量交流环境。',
    casual: '轻松愉快的交流氛围，大家一起聊天学习，分享生活和工作中的点点滴滴。',
    warm: '温馨的大家庭，互帮互助，共同进步。每个人都能在这里找到归属感。'
  },
  faq: [
    {
      question: '这个群主要讨论什么内容？',
      answer: '主要围绕{industry}相关话题，包括行业动态、经验分享、资源交换等。'
    },
    {
      question: '群里有什么规则吗？',
      answer: '禁止发广告、恶意刷屏，鼓励有价值的分享和讨论，营造良好的交流环境。'
    },
    {
      question: '如何更好地融入群聊？',
      answer: '主动参与讨论，分享有价值的内容，尊重其他成员，积极互动交流。'
    }
  ],
  reviews: [
    {
      username: '行业专家',
      content: '群里的分享质量很高，学到了很多实用的知识和经验。',
      rating: 5
    },
    {
      username: '资深从业者',
      content: '氛围很好，大家都很乐于分享，是个不错的学习平台。',
      rating: 5
    },
    {
      username: '新手小白',
      content: '刚入行的时候加入的，得到了很多前辈的指导和帮助。',
      rating: 4
    }
  ]
}

// 方法
const handleTypeChange = (type) => {
  // 根据类型调整配置选项
  console.log('切换内容类型:', type)
}

const generateContent = async () => {
  if (!config.industry || !config.audience) {
    ElMessage.warning('请先选择行业类型和目标人群')
    return
  }

  generating.value = true
  
  try {
    // 模拟AI生成过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const results = []
    
    for (let i = 0; i < config.count; i++) {
      const content = await generateSingleContent()
      results.push({
        id: Date.now() + i,
        content,
        rating: 0,
        type: selectedType.value
      })
    }
    
    generatedResults.value = results
    
    // 添加到历史记录
    generationHistory.value.unshift({
      timestamp: new Date(),
      type: selectedType.value,
      content: results[0].content,
      config: { ...config }
    })
    
    ElMessage.success(`成功生成 ${results.length} 个内容方案`)
    
  } catch (error) {
    ElMessage.error('生成失败，请重试')
  } finally {
    generating.value = false
  }
}

const generateSingleContent = async () => {
  const { industry, audience, style, keywords, customPrompt } = config
  
  // 基于模板和配置生成内容
  switch (selectedType.value) {
    case 'title':
      return generateTitle(industry, audience, keywords)
    case 'description':
      return generateDescription(style, industry, customPrompt)
    case 'faq':
      return generateFAQ(industry, audience)
    case 'reviews':
      return generateReviews(audience, style)
    case 'intro':
      return generateIntro(industry, audience, style)
    default:
      return '生成的内容'
  }
}

const generateTitle = (industry, audience, keywords) => {
  const templates = contentTemplates.title[industry] || contentTemplates.title.tech
  const template = templates[Math.floor(Math.random() * templates.length)]
  
  let title = template.replace('{city}', 'xxx')
  
  if (keywords) {
    const keywordList = keywords.split(',').map(k => k.trim())
    const keyword = keywordList[Math.floor(Math.random() * keywordList.length)]
    title = title.replace('程序员', keyword).replace('投资理财', keyword)
  }
  
  return title
}

const generateDescription = (style, industry, customPrompt) => {
  let baseDesc = contentTemplates.description[style] || contentTemplates.description.professional
  
  if (customPrompt) {
    baseDesc += '\n\n' + customPrompt
  }
  
  return baseDesc.replace('{industry}', getIndustryLabel(industry))
}

const generateFAQ = (industry, audience) => {
  const faqs = contentTemplates.faq.map(faq => ({
    ...faq,
    answer: faq.answer.replace('{industry}', getIndustryLabel(industry))
  }))
  
  return faqs.map(faq => `${faq.question}----${faq.answer}`).join('\n')
}

const generateReviews = (audience, style) => {
  return contentTemplates.reviews.map(review => 
    `${review.username}----${review.content}----${review.rating}`
  ).join('\n')
}

const generateIntro = (industry, audience, style) => {
  return `欢迎加入我们的${getIndustryLabel(industry)}交流群！这里汇聚了众多${getAudienceLabel(audience)}，大家可以在这里分享经验、交流心得、互相学习。我们致力于打造一个高质量的交流平台，让每个成员都能在这里收获成长。`
}

const useResult = (result) => {
  emit('content-generated', {
    type: selectedType.value,
    content: result.content
  })
  ElMessage.success('内容已应用')
}

const copyResult = async (result) => {
  // SSR-safe: Check if clipboard API is available
  if (typeof navigator === 'undefined' || !navigator.clipboard) {
    ElMessage.warning('剪贴板功能在当前环境中不可用')
    return
  }
  
  try {
    await navigator.clipboard.writeText(result.content)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const rateResult = (result, rating) => {
  result.rating = rating
  // 可以将评分数据发送到后端用于改进AI模型
}

const clearResults = () => {
  generatedResults.value = []
}

const regenerateAll = () => {
  generateContent()
}

const exportResults = () => {
  const content = generatedResults.value.map((result, index) => 
    `方案${index + 1}:\n${result.content}\n\n`
  ).join('')
  
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `AI生成内容_${new Date().toISOString().slice(0, 10)}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const reuseHistory = (history) => {
  Object.assign(config, history.config)
  selectedType.value = history.type
}

// 辅助方法
const getTypeLabel = (type) => {
  const labels = {
    title: '群组标题',
    description: '群组描述',
    faq: 'FAQ问答',
    reviews: '用户评论',
    intro: '群组介绍'
  }
  return labels[type] || type
}

const getIndustryLabel = (industry) => {
  const labels = {
    tech: '互联网科技',
    finance: '金融投资',
    education: '教育培训',
    ecommerce: '电商零售',
    health: '健康医疗',
    entertainment: '娱乐游戏',
    realestate: '房产装修',
    food: '美食餐饮',
    travel: '旅游出行',
    other: '其他行业'
  }
  return labels[industry] || industry
}

const getAudienceLabel = (audience) => {
  const labels = {
    students: '学生朋友',
    newcomers: '职场新人',
    experts: '资深专家',
    entrepreneurs: '创业者',
    mothers: '宝妈群体',
    seniors: '中老年朋友',
    youngpro: '年轻白领',
    freelancers: '自由职业者'
  }
  return labels[audience] || audience
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.ai-content-generator {
  .generator-card {
    border-radius: 12px;
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
  
  .content-type-selector {
    margin-bottom: 24px;
    text-align: center;
  }
  
  .generation-config {
    margin-bottom: 24px;
  }
  
  .generation-actions {
    text-align: center;
    margin-bottom: 24px;
    
    .el-button {
      margin: 0 8px;
    }
  }
  
  .generation-results {
    .results-list {
      .result-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        
        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .result-index {
            font-weight: 600;
            color: #3b82f6;
          }
          
          .result-actions {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
        
        .result-content {
          pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: inherit;
            margin: 0;
            color: #1e293b;
            line-height: 1.6;
          }
        }
      }
    }
    
    .batch-actions {
      text-align: center;
      margin-top: 16px;
    }
  }
  
  .generation-history {
    margin-top: 24px;
    
    .history-item {
      .history-type {
        font-weight: 600;
        color: #3b82f6;
        margin-bottom: 4px;
      }
      
      .history-content {
        color: #64748b;
        margin-bottom: 8px;
      }
    }
  }
}
</style>