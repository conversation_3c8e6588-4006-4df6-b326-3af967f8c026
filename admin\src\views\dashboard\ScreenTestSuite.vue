<template>
  <div class="screen-test-suite">
    <div class="test-header">
      <h2>📊 数据大屏显示测试套件</h2>
      <p>测试不同版本数据大屏在当前屏幕下的显示效果</p>
    </div>

    <!-- 屏幕信息 -->
    <div class="screen-info">
      <div class="info-card">
        <h3>🖥️ 屏幕信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">分辨率:</span>
            <span class="value">{{ screenInfo.width }} × {{ screenInfo.height }}</span>
          </div>
          <div class="info-item">
            <span class="label">设备像素比:</span>
            <span class="value">{{ screenInfo.devicePixelRatio }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前断点:</span>
            <span class="value breakpoint" :class="currentBreakpoint">{{ currentBreakpoint }}</span>
          </div>
          <div class="info-item">
            <span class="label">推荐版本:</span>
            <span class="value">{{ recommendedVersion }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本测试 -->
    <div class="version-tests">
      <h3>🎯 版本测试</h3>
      <div class="test-grid">
        <div 
          v-for="version in versions" 
          :key="version.key"
          class="version-card"
          :class="{ recommended: version.key === recommendedVersion }"
        >
          <div class="version-header">
            <div class="version-icon">{{ version.icon }}</div>
            <div class="version-info">
              <h4>{{ version.name }}</h4>
              <p>{{ version.description }}</p>
            </div>
            <div class="version-status" :class="version.status">
              {{ getStatusText(version.status) }}
            </div>
          </div>
          
          <div class="version-features">
            <div class="feature-list">
              <span 
                v-for="feature in version.features" 
                :key="feature"
                class="feature-tag"
              >
                {{ feature }}
              </span>
            </div>
          </div>
          
          <div class="version-actions">
            <button 
              class="test-btn"
              @click="testVersion(version)"
              :disabled="testing"
            >
              {{ testing && currentTest === version.key ? '测试中...' : '测试显示' }}
            </button>
            <button 
              class="preview-btn"
              @click="openVersion(version)"
            >
              预览
            </button>
          </div>
          
          <div v-if="testResults[version.key]" class="test-result">
            <div class="result-header">
              <span class="result-status" :class="testResults[version.key].status">
                {{ testResults[version.key].status === 'success' ? '✅' : testResults[version.key].status === 'warning' ? '⚠️' : '❌' }}
              </span>
              <span>测试结果</span>
            </div>
            <div class="result-details">
              <div v-if="testResults[version.key].issues.length > 0" class="issues">
                <strong>发现问题:</strong>
                <ul>
                  <li v-for="issue in testResults[version.key].issues" :key="issue.message">
                    {{ issue.message }}
                  </li>
                </ul>
              </div>
              <div v-if="testResults[version.key].recommendations.length > 0" class="recommendations">
                <strong>优化建议:</strong>
                <ul>
                  <li v-for="rec in testResults[version.key].recommendations" :key="rec">
                    {{ rec }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局操作 -->
    <div class="global-actions">
      <button class="action-btn primary" @click="testAllVersions" :disabled="testing">
        🔍 测试所有版本
      </button>
      <button class="action-btn" @click="autoFixAll">
        🔧 自动修复问题
      </button>
      <button class="action-btn" @click="exportReport">
        📄 导出报告
      </button>
    </div>

    <!-- 测试日志 -->
    <div class="test-logs" v-if="logs.length > 0">
      <h3>📝 测试日志</h3>
      <div class="log-container">
        <div 
          v-for="log in logs" 
          :key="log.id"
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useScreenDiagnostics, quickFix } from '@/utils/screen-diagnostics'

const router = useRouter()
const { runDiagnostics, autoFix } = useScreenDiagnostics()

// 响应式数据
const testing = ref(false)
const currentTest = ref('')
const testResults = ref({})
const logs = ref([])

// 屏幕信息
const screenInfo = ref({
  width: window.innerWidth,
  height: window.innerHeight,
  devicePixelRatio: window.devicePixelRatio || 1
})

// 当前断点
const currentBreakpoint = computed(() => {
  const width = screenInfo.value.width
  if (width >= 2560) return 'xxxxl'
  if (width >= 1920) return 'xxxl'
  if (width >= 1600) return 'xxl'
  if (width >= 1400) return 'xl'
  if (width >= 1200) return 'lg'
  if (width >= 768) return 'md'
  if (width >= 480) return 'sm'
  return 'xs'
})

// 推荐版本
const recommendedVersion = computed(() => {
  const bp = currentBreakpoint.value
  if (['xxxxl', 'xxxl'].includes(bp)) return 'ultra'
  if (['xxl', 'xl'].includes(bp)) return 'optimized'
  if (['lg', 'md'].includes(bp)) return 'enhanced'
  return 'classic'
})

// 版本配置
const versions = ref([
  {
    key: 'optimized',
    name: 'OptimizedDataScreen',
    description: '最新优化版本，完整响应式支持',
    icon: '🚀',
    path: '/data-screen/optimized',
    features: ['响应式', '自动修复', '全屏模式', '诊断工具'],
    status: 'excellent'
  },
  {
    key: 'ultra',
    name: 'UltraDataScreen',
    description: '3D动画效果，适合大屏展示',
    icon: '⚡',
    path: '/data-screen/ultra',
    features: ['3D动画', '高级视觉', '大屏优化'],
    status: 'good'
  },
  {
    key: 'enhanced',
    name: 'EnhancedDataScreen',
    description: '现代化设计，平衡性能和视觉',
    icon: '🌟',
    path: '/data-screen/enhanced',
    features: ['现代设计', '性能优化', '视觉平衡'],
    status: 'good'
  },
  {
    key: 'classic',
    name: 'DataScreen',
    description: '经典版本，稳定可靠',
    icon: '🛡️',
    path: '/data-screen',
    features: ['稳定可靠', '兼容性好', '轻量级'],
    status: 'stable'
  }
])

// 方法
const updateScreenInfo = () => {
  screenInfo.value = {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

const addLog = (message, type = 'info') => {
  logs.value.unshift({
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

const getStatusText = (status) => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    stable: '稳定',
    warning: '警告',
    error: '错误'
  }
  return texts[status] || '未知'
}

const testVersion = async (version) => {
  testing.value = true
  currentTest.value = version.key
  
  try {
    addLog(`开始测试 ${version.name}`, 'info')
    
    // 模拟测试过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 运行诊断
    const result = await runDiagnostics()
    testResults.value[version.key] = result
    
    const status = result.summary.status
    addLog(`${version.name} 测试完成 - ${getStatusText(status)}`, status)
    
    if (status === 'success') {
      ElMessage.success(`${version.name} 显示正常`)
    } else {
      ElMessage.warning(`${version.name} 发现 ${result.summary.total} 个问题`)
    }
    
  } catch (error) {
    addLog(`${version.name} 测试失败: ${error.message}`, 'error')
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
    currentTest.value = ''
  }
}

const testAllVersions = async () => {
  for (const version of versions.value) {
    await testVersion(version)
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  addLog('所有版本测试完成', 'success')
  ElMessage.success('所有版本测试完成')
}

const autoFixAll = () => {
  try {
    const fixes = quickFix()
    if (fixes.length > 0) {
      addLog(`应用了 ${fixes.length} 个自动修复`, 'success')
      ElMessage.success(`已应用 ${fixes.length} 个修复`)
    } else {
      addLog('未发现需要修复的问题', 'info')
      ElMessage.info('未发现需要修复的问题')
    }
  } catch (error) {
    addLog(`自动修复失败: ${error.message}`, 'error')
    ElMessage.error('自动修复失败')
  }
}

const openVersion = (version) => {
  window.open(version.path, '_blank')
}

const exportReport = () => {
  const report = {
    screenInfo: screenInfo.value,
    currentBreakpoint: currentBreakpoint.value,
    recommendedVersion: recommendedVersion.value,
    testResults: testResults.value,
    logs: logs.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `data-screen-test-report-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  addLog('测试报告已导出', 'success')
  ElMessage.success('测试报告已导出')
}

// 生命周期
onMounted(() => {
  updateScreenInfo()
  window.addEventListener('resize', updateScreenInfo)
  
  addLog('数据大屏测试套件已启动', 'info')
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenInfo)
})
</script>

<style scoped>
.screen-test-suite {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.test-header p {
  margin: 0;
  color: #606266;
}

.screen-info {
  margin-bottom: 32px;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  font-weight: 600;
  color: #303133;
}

.breakpoint {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.breakpoint.xxxxl { background: #722ed1; }
.breakpoint.xxxl { background: #1890ff; }
.breakpoint.xxl { background: #13c2c2; }
.breakpoint.xl { background: #52c41a; }
.breakpoint.lg { background: #faad14; }
.breakpoint.md { background: #fa8c16; }
.breakpoint.sm { background: #f5222d; }
.breakpoint.xs { background: #eb2f96; }

.version-tests h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.version-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.version-card.recommended {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
}

.version-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.version-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.version-icon {
  font-size: 24px;
}

.version-info {
  flex: 1;
}

.version-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.version-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.version-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.version-status.excellent {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.version-status.good {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.version-status.stable {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.feature-tag {
  padding: 2px 8px;
  background: #f0f0f0;
  border-radius: 12px;
  font-size: 12px;
  color: #606266;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.test-btn,
.preview-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #303133;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn:hover,
.preview-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.test-result {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #d9d9d9;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
}

.result-status.success {
  color: #52c41a;
}

.result-status.warning {
  color: #faad14;
}

.result-status.error {
  color: #f5222d;
}

.result-details ul {
  margin: 4px 0;
  padding-left: 16px;
}

.result-details li {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.global-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 32px;
}

.action-btn {
  padding: 12px 24px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #303133;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.action-btn.primary {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.test-logs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-logs h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background: #f6ffed;
}

.log-item.warning {
  background: #fffbe6;
}

.log-item.error {
  background: #fff2f0;
}

.log-time {
  color: #909399;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  color: #303133;
}

/* 响应式 */
@media (max-width: 768px) {
  .screen-test-suite {
    padding: 16px;
  }
  
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .global-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}
</style>
