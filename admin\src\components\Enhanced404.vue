<template>
  <div class="enhanced-404-page">
    <div class="content-container">
      <!-- 背景动画 -->
      <div class="background-animation">
        <div class="floating-shapes">
          <div v-for="n in 8" :key="n" class="shape" :class="`shape-${n}`"></div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="main-content">
        <!-- 404 动画图标 -->
        <div class="error-animation">
          <div class="error-number">
            <span class="number-4 number-left">4</span>
            <div class="number-0">
              <div class="search-icon">
                <el-icon><Search /></el-icon>
              </div>
            </div>
            <span class="number-4 number-right">4</span>
          </div>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">页面未找到</h1>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被移动。<br>
            让我们帮您回到正确的位置。
          </p>
        </div>

        <!-- 搜索建议 -->
        <div class="search-suggestions">
          <h3>您可能在寻找：</h3>
          <div class="suggestion-list">
            <el-button
              v-for="suggestion in suggestions"
              :key="suggestion.path"
              class="suggestion-item"
              @click="navigateTo(suggestion.path)"
            >
              <el-icon><component :is="suggestion.icon" /></el-icon>
              {{ suggestion.title }}
            </el-button>
          </div>
        </div>

        <!-- 快速导航 -->
        <div class="quick-nav">
          <h3>快速导航</h3>
          <div class="nav-grid">
            <div
              v-for="nav in quickNavItems"
              :key="nav.path"
              class="nav-item"
              @click="navigateTo(nav.path)"
            >
              <div class="nav-icon" :class="nav.color">
                <el-icon><component :is="nav.icon" /></el-icon>
              </div>
              <div class="nav-content">
                <h4>{{ nav.title }}</h4>
                <p>{{ nav.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button size="large" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
          <el-button size="large" @click="reload">
            <el-icon><Refresh /></el-icon>
            重新加载
          </el-button>
        </div>

        <!-- 联系支持 -->
        <div class="support-section">
          <p class="support-text">
            如果问题持续存在，请联系我们的技术支持团队
          </p>
          <div class="support-actions">
            <el-button text @click="reportIssue">
              <el-icon><ChatLineSquare /></el-icon>
              报告问题
            </el-button>
            <el-button text @click="contactSupport">
              <el-icon><Service /></el-icon>
              联系支持
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧装饰 -->
      <div class="side-decoration">
        <div class="decoration-item item-1">
          <el-icon><Document /></el-icon>
        </div>
        <div class="decoration-item item-2">
          <el-icon><Folder /></el-icon>
        </div>
        <div class="decoration-item item-3">
          <el-icon><Link /></el-icon>
        </div>
      </div>
    </div>

    <!-- 报告问题对话框 -->
    <el-dialog
      v-model="issueDialog.visible"
      title="报告问题"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="issueFormRef" :model="issueForm" label-width="80px">
        <el-form-item label="问题类型">
          <el-select v-model="issueForm.type" placeholder="请选择问题类型">
            <el-option label="页面无法访问" value="access" />
            <el-option label="链接失效" value="link" />
            <el-option label="权限问题" value="permission" />
            <el-option label="其他问题" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题描述">
          <el-input
            v-model="issueForm.description"
            type="textarea"
            placeholder="请详细描述您遇到的问题"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input
            v-model="issueForm.contact"
            placeholder="邮箱或电话（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="issueDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitIssue" :loading="issueDialog.loading">
          提交报告
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Search, HomeFilled, ArrowLeft, Refresh, ChatLineSquare, Service,
  Document, Folder, Link, Monitor, Comment, User, Money, Setting,
  DataLine, Share, List
} from '@element-plus/icons-vue'

const router = useRouter()

// 建议页面
const suggestions = ref([
  { title: '数据看板', path: '/dashboard', icon: 'Monitor' },
  { title: '社群管理', path: '/community', icon: 'Comment' },
  { title: '用户管理', path: '/user', icon: 'User' },
  { title: '财务管理', path: '/finance', icon: 'Money' },
  { title: '系统设置', path: '/system', icon: 'Setting' }
])

// 快速导航项目
const quickNavItems = ref([
  {
    title: '数据看板',
    description: '查看系统概览和关键指标',
    path: '/dashboard',
    icon: 'Monitor',
    color: 'primary'
  },
  {
    title: '数据大屏',
    description: '全屏展示数据分析',
    path: '/data-screen',
    icon: 'DataLine',
    color: 'success'
  },
  {
    title: '社群管理',
    description: '管理微信群组和用户',
    path: '/community',
    icon: 'Comment',
    color: 'warning'
  },
  {
    title: '分销管理',
    description: '管理分销商和推广链接',
    path: '/distribution',
    icon: 'Share',
    color: 'danger'
  },
  {
    title: '订单管理',
    description: '查看和处理订单信息',
    path: '/orders',
    icon: 'List',
    color: 'info'
  },
  {
    title: '系统设置',
    description: '配置系统参数和权限',
    path: '/system',
    icon: 'Setting',
    color: 'primary'
  }
])

// 问题报告表单
const issueDialog = reactive({
  visible: false,
  loading: false
})

const issueForm = reactive({
  type: '',
  description: '',
  contact: ''
})

const issueFormRef = ref(null)

// 方法
const navigateTo = (path) => {
  router.push(path)
}

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

const reload = () => {
  window.location.reload()
}

const reportIssue = () => {
  // 重置表单
  Object.assign(issueForm, {
    type: '',
    description: '',
    contact: ''
  })
  issueDialog.visible = true
}

const contactSupport = () => {
  // 可以跳转到支持页面或打开聊天窗口
  ElMessage.info('技术支持功能开发中，请稍后再试')
}

const submitIssue = async () => {
  try {
    if (!issueForm.type || !issueForm.description) {
      ElMessage.warning('请填写问题类型和问题描述')
      return
    }

    issueDialog.loading = true

    // TODO: 提交问题报告到后端
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('问题报告已提交，我们会尽快处理')
    issueDialog.visible = false
  } catch (error) {
    ElMessage.error('提交失败，请稍后再试')
    console.error(error)
  } finally {
    issueDialog.loading = false
  }
}

// 页面加载时的动画
onMounted(() => {
  // 添加进入动画
  setTimeout(() => {
    document.querySelector('.main-content')?.classList.add('animate-in')
  }, 100)
})
</script>

<style scoped>
.enhanced-404-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 { width: 60px; height: 60px; top: 10%; left: 10%; animation-delay: 0s; }
.shape-2 { width: 40px; height: 40px; top: 20%; right: 20%; animation-delay: -1s; }
.shape-3 { width: 80px; height: 80px; bottom: 30%; left: 15%; animation-delay: -2s; }
.shape-4 { width: 30px; height: 30px; top: 60%; right: 30%; animation-delay: -3s; }
.shape-5 { width: 50px; height: 50px; bottom: 20%; right: 10%; animation-delay: -4s; }
.shape-6 { width: 70px; height: 70px; top: 30%; left: 60%; animation-delay: -5s; }
.shape-7 { width: 35px; height: 35px; bottom: 60%; left: 70%; animation-delay: -2.5s; }
.shape-8 { width: 45px; height: 45px; top: 70%; right: 50%; animation-delay: -1.5s; }

/* 主容器 */
.content-container {
  max-width: 1000px;
  width: 100%;
  display: flex;
  position: relative;
  z-index: 10;
}

.main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  margin-right: 20px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.main-content.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 404 动画 */
.error-animation {
  text-align: center;
  margin-bottom: 40px;
}

.error-number {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.number-4 {
  font-size: 120px;
  font-weight: 800;
  color: #667eea;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
  animation: bounce 2s ease-in-out infinite;
}

.number-left {
  animation-delay: 0s;
}

.number-right {
  animation-delay: 0.5s;
}

.number-0 {
  width: 120px;
  height: 120px;
  border: 8px solid #764ba2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px;
  position: relative;
  animation: spin 3s linear infinite;
}

.search-icon {
  font-size: 40px;
  color: #764ba2;
  animation: pulse 2s ease-in-out infinite;
}

/* 错误信息 */
.error-info {
  text-align: center;
  margin-bottom: 40px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* 搜索建议 */
.search-suggestions {
  margin-bottom: 40px;
}

.search-suggestions h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.suggestion-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.suggestion-item {
  border-radius: 20px;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 快速导航 */
.quick-nav {
  margin-bottom: 40px;
}

.quick-nav h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.nav-icon.primary { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
.nav-icon.success { background: linear-gradient(135deg, #10b981 0%, #047857 100%); }
.nav-icon.warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
.nav-icon.danger { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
.nav-icon.info { background: linear-gradient(135deg, #6b7280 0%, #374151 100%); }

.nav-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.nav-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

/* 支持区域 */
.support-section {
  text-align: center;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
}

.support-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
}

.support-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 右侧装饰 */
.side-decoration {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.decoration-item {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: rgba(255, 255, 255, 0.8);
  animation: float 4s ease-in-out infinite;
}

.item-1 { animation-delay: 0s; }
.item-2 { animation-delay: -1s; }
.item-3 { animation-delay: -2s; }

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
  }

  .main-content {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .side-decoration {
    width: 100%;
    flex-direction: row;
    height: auto;
  }
}

@media (max-width: 768px) {
  .enhanced-404-page {
    padding: 16px;
  }

  .main-content {
    padding: 24px;
  }

  .number-4 {
    font-size: 80px;
  }

  .number-0 {
    width: 80px;
    height: 80px;
    margin: 0 12px;
  }

  .search-icon {
    font-size: 28px;
  }

  .error-title {
    font-size: 24px;
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .el-button {
    width: 200px;
  }

  .side-decoration {
    display: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .main-content {
    background: rgba(30, 41, 59, 0.95);
    color: #f8fafc;
  }

  .error-title {
    color: #f8fafc;
  }

  .error-description {
    color: #cbd5e1;
  }

  .quick-nav h3,
  .search-suggestions h3 {
    color: #f8fafc;
  }

  .nav-item {
    background: #334155;
  }

  .nav-item:hover {
    background: #475569;
  }

  .nav-content h4 {
    color: #f8fafc;
  }

  .nav-content p {
    color: #cbd5e1;
  }

  .support-section {
    background: #334155;
  }

  .support-text {
    color: #cbd5e1;
  }
}
</style>