import{_ as e}from"./index-DtXAftX0.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                *//* empty css               *//* empty css                       *//* empty css                  */import{aY as a,aZ as l,a_ as t,b9 as s,b8 as r,by as o,bb as u,at as d,U as i,bz as c,bh as n,bi as v,T as p,a4 as m,aR as _,Q as f}from"./element-plus-h2SQQM64.js";import{P as h}from"./PageLayout-C6qH3ReN.js";import{r as b,L as y,e as w,y as g,l as k,z as j,t as U,E as C,k as G,B as V,D as R,u as O}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const x={class:"reports-container"},z={key:0,class:"report-section"},P={class:"metric-content"},I={class:"metric-value"},T={class:"metric-change positive"},L={class:"metric-content"},N={class:"metric-value"},B={class:"metric-change positive"},D={class:"metric-content"},E={class:"metric-value"},F={class:"metric-change positive"},Q={class:"metric-content"},Y={class:"metric-value"},Z={class:"metric-change positive"},q={class:"chart-placeholder"},A={key:1,class:"report-section"},H={key:2,class:"report-section"},J={key:3,class:"report-section"},K=e({__name:"Reports",setup(e){const K=b(!1),M=b("week"),S=b([]),W=b("overview"),X=y({totalRevenue:125680.5,revenueGrowth:15.2,totalOrders:1256,ordersGrowth:8.7,totalUsers:8945,usersGrowth:12.3,totalGroups:156,groupsGrowth:6.8}),$=b([{date:"2024-08-01",newUsers:45,activeUsers:1250,retentionRate:85.2},{date:"2024-08-02",newUsers:52,activeUsers:1298,retentionRate:86.1}]),ee=b([{date:"2024-08-01",revenue:8520.5,orders:85,avgOrderValue:100.24},{date:"2024-08-02",revenue:9240.8,orders:92,avgOrderValue:100.44}]),ae=b([{name:"VIP群组",members:98,messages:1250,revenue:9800},{name:"普通群组",members:156,messages:2340,revenue:1560}]),le=e=>new Intl.NumberFormat("zh-CN").format(e),te=e=>{"custom"!==e&&(S.value=[]),re()},se=()=>{re()},re=async()=>{K.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),f.success("数据已刷新")}catch(e){f.error("刷新失败")}finally{K.value=!1}},oe=()=>{f.success("报表导出功能开发中")};return w(()=>{re()}),(e,f)=>{const b=p,y=d,w=r,ue=s,de=t,ie=o,ce=u,ne=l,ve=a,pe=c,me=v,_e=n;return k(),g(h,{title:"数据报表",subtitle:"查看详细的数据分析报表",icon:"TrendCharts",loading:K.value},{actions:j(()=>[C(y,{onClick:oe},{default:j(()=>[C(b,null,{default:j(()=>[C(O(m))]),_:1}),f[6]||(f[6]=R(" 导出报表 ",-1))]),_:1,__:[6]}),C(y,{type:"primary",onClick:re},{default:j(()=>[C(b,null,{default:j(()=>[C(O(_))]),_:1}),f[7]||(f[7]=R(" 刷新数据 ",-1))]),_:1,__:[7]})]),default:j(()=>[U("div",x,[C(ve,{class:"filter-card",shadow:"never"},{default:j(()=>[C(ne,{gutter:16,align:"middle"},{default:j(()=>[C(de,{span:6},{default:j(()=>[C(ue,{modelValue:M.value,"onUpdate:modelValue":f[0]||(f[0]=e=>M.value=e),placeholder:"选择时间范围",onChange:te},{default:j(()=>[C(w,{label:"今日",value:"today"}),C(w,{label:"昨日",value:"yesterday"}),C(w,{label:"最近7天",value:"week"}),C(w,{label:"最近30天",value:"month"}),C(w,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),"custom"===M.value?(k(),g(de,{key:0,span:10},{default:j(()=>[C(ie,{modelValue:S.value,"onUpdate:modelValue":f[1]||(f[1]=e=>S.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:se},null,8,["modelValue"])]),_:1})):V("",!0),C(de,{span:8},{default:j(()=>[C(ce,null,{default:j(()=>[C(y,{type:"overview"===W.value?"primary":"",onClick:f[2]||(f[2]=e=>W.value="overview")},{default:j(()=>f[8]||(f[8]=[R(" 概览 ",-1)])),_:1,__:[8]},8,["type"]),C(y,{type:"user"===W.value?"primary":"",onClick:f[3]||(f[3]=e=>W.value="user")},{default:j(()=>f[9]||(f[9]=[R(" 用户 ",-1)])),_:1,__:[9]},8,["type"]),C(y,{type:"finance"===W.value?"primary":"",onClick:f[4]||(f[4]=e=>W.value="finance")},{default:j(()=>f[10]||(f[10]=[R(" 财务 ",-1)])),_:1,__:[10]},8,["type"]),C(y,{type:"group"===W.value?"primary":"",onClick:f[5]||(f[5]=e=>W.value="group")},{default:j(()=>f[11]||(f[11]=[R(" 群组 ",-1)])),_:1,__:[11]},8,["type"])]),_:1})]),_:1})]),_:1})]),_:1}),"overview"===W.value?(k(),G("div",z,[C(ne,{gutter:24},{default:j(()=>[C(de,{span:6},{default:j(()=>[C(ve,{class:"metric-card"},{default:j(()=>[U("div",P,[U("div",I,i(le(X.totalRevenue)),1),f[12]||(f[12]=U("div",{class:"metric-label"},"总收入（元）",-1)),U("div",T,"+"+i(X.revenueGrowth)+"%",1)])]),_:1})]),_:1}),C(de,{span:6},{default:j(()=>[C(ve,{class:"metric-card"},{default:j(()=>[U("div",L,[U("div",N,i(le(X.totalOrders)),1),f[13]||(f[13]=U("div",{class:"metric-label"},"总订单数",-1)),U("div",B,"+"+i(X.ordersGrowth)+"%",1)])]),_:1})]),_:1}),C(de,{span:6},{default:j(()=>[C(ve,{class:"metric-card"},{default:j(()=>[U("div",D,[U("div",E,i(le(X.totalUsers)),1),f[14]||(f[14]=U("div",{class:"metric-label"},"总用户数",-1)),U("div",F,"+"+i(X.usersGrowth)+"%",1)])]),_:1})]),_:1}),C(de,{span:6},{default:j(()=>[C(ve,{class:"metric-card"},{default:j(()=>[U("div",Q,[U("div",Y,i(le(X.totalGroups)),1),f[15]||(f[15]=U("div",{class:"metric-label"},"总群组数",-1)),U("div",Z,"+"+i(X.groupsGrowth)+"%",1)])]),_:1})]),_:1})]),_:1}),C(ve,{class:"chart-card",shadow:"never"},{header:j(()=>f[16]||(f[16]=[U("h3",null,"收入趋势",-1)])),default:j(()=>[U("div",q,[C(pe,{description:"图表组件待集成"})])]),_:1})])):V("",!0),"user"===W.value?(k(),G("div",A,[C(ve,{shadow:"never"},{header:j(()=>f[17]||(f[17]=[U("h3",null,"用户分析报表",-1)])),default:j(()=>[C(_e,{data:$.value,style:{width:"100%"}},{default:j(()=>[C(me,{prop:"date",label:"日期"}),C(me,{prop:"newUsers",label:"新增用户"}),C(me,{prop:"activeUsers",label:"活跃用户"}),C(me,{prop:"retentionRate",label:"留存率"},{default:j(({row:e})=>[R(i(e.retentionRate)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})])):V("",!0),"finance"===W.value?(k(),G("div",H,[C(ve,{shadow:"never"},{header:j(()=>f[18]||(f[18]=[U("h3",null,"财务分析报表",-1)])),default:j(()=>[C(_e,{data:ee.value,style:{width:"100%"}},{default:j(()=>[C(me,{prop:"date",label:"日期"}),C(me,{prop:"revenue",label:"收入（元）"}),C(me,{prop:"orders",label:"订单数"}),C(me,{prop:"avgOrderValue",label:"客单价（元）"})]),_:1},8,["data"])]),_:1})])):V("",!0),"group"===W.value?(k(),G("div",J,[C(ve,{shadow:"never"},{header:j(()=>f[19]||(f[19]=[U("h3",null,"群组分析报表",-1)])),default:j(()=>[C(_e,{data:ae.value,style:{width:"100%"}},{default:j(()=>[C(me,{prop:"name",label:"群组名称"}),C(me,{prop:"members",label:"成员数"}),C(me,{prop:"messages",label:"消息数"}),C(me,{prop:"revenue",label:"收入（元）"})]),_:1},8,["data"])]),_:1})])):V("",!0)])]),_:1},8,["loading"])}}},[["__scopeId","data-v-9bf53583"]]);export{K as default};
