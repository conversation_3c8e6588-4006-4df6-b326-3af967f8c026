<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NavigationUsageStat;
use App\Models\NavigationSearchLog;
use App\Models\NavigationRecommendation;
use App\Services\NavigationAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 导航统计分析API控制器
 * 
 * 提供导航使用统计、性能分析和数据洞察功能
 */
class NavigationAnalyticsController extends Controller
{
    protected NavigationAnalyticsService $analyticsService;

    public function __construct(NavigationAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * 获取导航使用统计概览
     */
    public function getOverview(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:today,yesterday,week,month,quarter,year',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'compare_period' => 'boolean'
        ]);

        $period = $request->input('period', 'week');
        $domain = $request->domain;
        $comparePeriod = $request->boolean('compare_period', false);

        try {
            $cacheKey = "navigation_overview:{$period}:{$domain}:" . ($comparePeriod ? 'compare' : 'single');
            
            $overview = Cache::remember($cacheKey, 1800, function () use ($period, $domain, $comparePeriod) {
                return $this->analyticsService->getOverview($period, $domain, $comparePeriod);
            });

            return response()->json([
                'success' => true,
                'data' => $overview,
                'meta' => [
                    'period' => $period,
                    'domain' => $domain,
                    'compare_period' => $comparePeriod,
                    'generated_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计概览失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取菜单使用排行榜
     */
    public function getMenuRanking(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:today,week,month',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'metric' => 'nullable|string|in:visits,users,duration',
            'limit' => 'nullable|integer|min:10|max:100'
        ]);

        $period = $request->input('period', 'week');
        $domain = $request->domain;
        $metric = $request->input('metric', 'visits');
        $limit = $request->integer('limit', 50);

        try {
            $ranking = $this->analyticsService->getMenuRanking($period, $domain, $metric, $limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'ranking' => $ranking,
                    'summary' => $this->getRankingSummary($ranking, $metric)
                ],
                'meta' => [
                    'period' => $period,
                    'domain' => $domain,
                    'metric' => $metric,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取菜单排行失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取用户行为分析
     */
    public function getUserBehaviorAnalysis(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:week,month,quarter',
            'user_id' => 'nullable|integer|exists:users,id',
            'role' => 'nullable|string',
            'domain' => 'nullable|string|in:business,operation,analytics,system'
        ]);

        $period = $request->input('period', 'month');
        $userId = $request->user_id;
        $role = $request->role;
        $domain = $request->domain;

        try {
            $analysis = $this->analyticsService->getUserBehaviorAnalysis($period, $userId, $role, $domain);

            return response()->json([
                'success' => true,
                'data' => $analysis,
                'meta' => [
                    'period' => $period,
                    'user_id' => $userId,
                    'role' => $role,
                    'domain' => $domain
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户行为分析失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取域使用分析
     */
    public function getDomainAnalysis(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:week,month,quarter',
            'include_comparison' => 'boolean'
        ]);

        $period = $request->input('period', 'month');
        $includeComparison = $request->boolean('include_comparison', true);

        try {
            $cacheKey = "domain_analysis:{$period}:" . ($includeComparison ? 'with_comparison' : 'simple');
            
            $analysis = Cache::remember($cacheKey, 3600, function () use ($period, $includeComparison) {
                return $this->analyticsService->getDomainAnalysis($period, $includeComparison);
            });

            return response()->json([
                'success' => true,
                'data' => $analysis,
                'meta' => [
                    'period' => $period,
                    'include_comparison' => $includeComparison
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取域分析失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取搜索行为分析
     */
    public function getSearchAnalysis(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:today,week,month',
            'include_trends' => 'boolean'
        ]);

        $period = $request->input('period', 'week');
        $includeTrends = $request->boolean('include_trends', true);

        try {
            $analysis = $this->analyticsService->getSearchAnalysis($period, $includeTrends);

            return response()->json([
                'success' => true,
                'data' => $analysis,
                'meta' => [
                    'period' => $period,
                    'include_trends' => $includeTrends
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取搜索分析失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取推荐系统效果分析
     */
    public function getRecommendationAnalysis(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:week,month,quarter',
            'recommendation_type' => 'nullable|string|in:frequent,collaborative,role_based,trending'
        ]);

        $period = $request->input('period', 'month');
        $recommendationType = $request->recommendation_type;

        try {
            $analysis = $this->analyticsService->getRecommendationAnalysis($period, $recommendationType);

            return response()->json([
                'success' => true,
                'data' => $analysis,
                'meta' => [
                    'period' => $period,
                    'recommendation_type' => $recommendationType
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取推荐分析失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取实时统计数据
     */
    public function getRealTimeStats(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        try {
            $stats = $this->analyticsService->getRealTimeStats();

            return response()->json([
                'success' => true,
                'data' => $stats,
                'meta' => [
                    'timestamp' => now()->timestamp,
                    'refresh_interval' => 30 // 建议刷新间隔(秒)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取实时统计失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'period' => 'nullable|string|in:hour,day,week,month',
            'metric_type' => 'nullable|string|in:response_time,cache_hit,error_rate,all'
        ]);

        $period = $request->input('period', 'day');
        $metricType = $request->input('metric_type', 'all');

        try {
            $metrics = $this->analyticsService->getPerformanceMetrics($period, $metricType);

            return response()->json([
                'success' => true,
                'data' => $metrics,
                'meta' => [
                    'period' => $period,
                    'metric_type' => $metricType
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取性能指标失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 生成数据报告
     */
    public function generateReport(Request $request): JsonResponse
    {
        $this->authorize('generate-navigation-reports');

        $request->validate([
            'report_type' => 'required|string|in:usage,performance,user_behavior,comprehensive',
            'period' => 'required|string|in:week,month,quarter,year',
            'format' => 'nullable|string|in:json,csv,pdf',
            'include_charts' => 'boolean',
            'filters' => 'nullable|array'
        ]);

        $reportType = $request->report_type;
        $period = $request->period;
        $format = $request->input('format', 'json');
        $includeCharts = $request->boolean('include_charts', false);
        $filters = $request->filters ?? [];

        try {
            $report = $this->analyticsService->generateReport(
                $reportType,
                $period,
                $format,
                $includeCharts,
                $filters
            );

            return response()->json([
                'success' => true,
                'data' => $report,
                'meta' => [
                    'report_type' => $reportType,
                    'period' => $period,
                    'format' => $format,
                    'generated_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成报告失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 导出统计数据
     */
    public function exportData(Request $request): JsonResponse
    {
        $this->authorize('export-navigation-data');

        $request->validate([
            'data_type' => 'required|string|in:usage_stats,search_logs,user_preferences',
            'period' => 'required|string|in:week,month,quarter,year',
            'format' => 'required|string|in:csv,excel,json',
            'filters' => 'nullable|array'
        ]);

        try {
            $exportTask = $this->analyticsService->createExportTask(
                $request->data_type,
                $request->period,
                $request->format,
                $request->filters ?? [],
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => '导出任务已创建',
                'data' => [
                    'task_id' => $exportTask->id,
                    'estimated_completion' => now()->addMinutes(5)->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建导出任务失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取异常检测结果
     */
    public function getAnomalyDetection(Request $request): JsonResponse
    {
        $this->authorize('view-navigation-analytics');

        $request->validate([
            'sensitivity' => 'nullable|numeric|min:0.1|max:1.0',
            'lookback_days' => 'nullable|integer|min:7|max:90'
        ]);

        $sensitivity = $request->input('sensitivity', 0.8);
        $lookbackDays = $request->integer('lookback_days', 30);

        try {
            $anomalies = $this->analyticsService->detectAnomalies($sensitivity, $lookbackDays);

            return response()->json([
                'success' => true,
                'data' => $anomalies,
                'meta' => [
                    'sensitivity' => $sensitivity,
                    'lookback_days' => $lookbackDays,
                    'detected_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '异常检测失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    // ========== 私有辅助方法 ==========

    private function getRankingSummary(array $ranking, string $metric): array
    {
        if (empty($ranking)) {
            return [];
        }

        $values = array_column($ranking, $metric);
        
        return [
            'total_items' => count($ranking),
            'top_performer' => $ranking[0] ?? null,
            'metric_stats' => [
                'max' => max($values),
                'min' => min($values),
                'avg' => round(array_sum($values) / count($values), 2),
                'total' => array_sum($values)
            ]
        ];
    }
}