<template>
  <div class="video-uploader">
    <!-- 上传方式选择 -->
    <el-radio-group v-model="uploadType" class="upload-type-selector">
      <el-radio-button label="upload">本地上传</el-radio-button>
      <el-radio-button label="url">在线链接</el-radio-button>
    </el-radio-group>

    <!-- 本地上传 -->
    <div v-if="uploadType === 'upload'" class="upload-section">
      <el-upload
        ref="uploadRef"
        class="video-upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :accept="accept"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-progress="handleProgress"
        :show-file-list="false"
        drag
      >
        <div v-if="!videoUrl" class="upload-dragger">
          <el-icon class="upload-icon"><VideoPlay /></el-icon>
          <div class="upload-text">将视频拖到此处，或<em>点击上传</em></div>
          <div class="upload-hint">支持 MP4、AVI、MOV 格式，大小不超过 {{ maxSize }}MB</div>
        </div>
        
        <!-- 上传进度 -->
        <div v-if="uploading" class="upload-progress">
          <el-progress :percentage="uploadProgress" />
          <div class="progress-text">正在上传视频...</div>
        </div>
      </el-upload>
    </div>

    <!-- 在线链接 -->
    <div v-else class="url-section">
      <el-input
        v-model="videoUrlInput"
        placeholder="请输入视频链接（支持腾讯视频、优酷、B站等）"
        @blur="handleUrlInput"
      >
        <template #prepend>
          <el-icon><Link /></el-icon>
        </template>
        <template #append>
          <el-button @click="handleUrlInput" :loading="urlValidating">
            验证
          </el-button>
        </template>
      </el-input>
      <div class="url-tip">
        支持的视频平台：腾讯视频、优酷、爱奇艺、B站、YouTube等
      </div>
    </div>

    <!-- 视频预览 -->
    <div v-if="videoUrl" class="video-preview">
      <div class="preview-header">
        <span class="preview-title">视频预览</span>
        <div class="preview-actions">
          <el-button @click="handleEdit" size="small" type="primary" plain>
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button @click="handleRemove" size="small" type="danger" plain>
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
      
      <div class="preview-content">
        <VideoPlayer
          :src="videoUrl"
          :poster="videoPoster"
          :width="320"
          :height="180"
          controls
        />
        
        <div class="video-info">
          <div class="info-item">
            <span class="label">视频源：</span>
            <span class="value">{{ videoSource }}</span>
          </div>
          <div v-if="videoTitle" class="info-item">
            <span class="label">标题：</span>
            <span class="value">{{ videoTitle }}</span>
          </div>
          <div v-if="videoDuration" class="info-item">
            <span class="label">时长：</span>
            <span class="value">{{ videoDuration }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频设置对话框 -->
    <el-dialog
      v-model="settingsVisible"
      title="视频设置"
      width="500px"
      append-to-body
    >
      <el-form :model="videoSettings" label-width="80px">
        <el-form-item label="封面图">
          <MediaUploader
            v-model="videoSettings.poster"
            type="image"
            :limit="1"
            accept="image/*"
            list-type="picture-card"
          />
        </el-form-item>
        
        <el-form-item label="自动播放">
          <el-switch v-model="videoSettings.autoplay" />
        </el-form-item>
        
        <el-form-item label="循环播放">
          <el-switch v-model="videoSettings.loop" />
        </el-form-item>
        
        <el-form-item label="静音播放">
          <el-switch v-model="videoSettings.muted" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="settingsVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Link, Edit, Delete } from '@element-plus/icons-vue'
import VideoPlayer from './VideoPlayer.vue'
import MediaUploader from './MediaUploader.vue'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: ''
  },
  maxSize: {
    type: Number,
    default: 100 // MB
  },
  accept: {
    type: String,
    default: 'video/*'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const uploadRef = ref()
const uploadType = ref('upload')
const uploading = ref(false)
const uploadProgress = ref(0)
const videoUrlInput = ref('')
const urlValidating = ref(false)
const settingsVisible = ref(false)

const videoSettings = reactive({
  poster: '',
  autoplay: false,
  loop: false,
  muted: false
})

// 计算属性
const uploadUrl = computed(() => '/api/upload/video')

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${getToken()}`
}))

const uploadData = computed(() => ({
  type: 'video'
}))

const videoUrl = computed(() => {
  if (typeof props.modelValue === 'string') {
    return props.modelValue
  } else if (props.modelValue && props.modelValue.url) {
    return props.modelValue.url
  }
  return ''
})

const videoPoster = computed(() => {
  if (typeof props.modelValue === 'object' && props.modelValue.poster) {
    return props.modelValue.poster
  }
  return videoSettings.poster
})

const videoSource = computed(() => {
  if (!videoUrl.value) return ''
  
  if (videoUrl.value.includes('qq.com')) return '腾讯视频'
  if (videoUrl.value.includes('youku.com')) return '优酷'
  if (videoUrl.value.includes('iqiyi.com')) return '爱奇艺'
  if (videoUrl.value.includes('bilibili.com')) return 'B站'
  if (videoUrl.value.includes('youtube.com')) return 'YouTube'
  if (videoUrl.value.startsWith('http')) return '在线链接'
  return '本地上传'
})

const videoTitle = computed(() => {
  if (typeof props.modelValue === 'object' && props.modelValue.title) {
    return props.modelValue.title
  }
  return ''
})

const videoDuration = computed(() => {
  if (typeof props.modelValue === 'object' && props.modelValue.duration) {
    return formatDuration(props.modelValue.duration)
  }
  return ''
})

// 方法
const beforeUpload = (file) => {
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    ElMessage.error('请上传视频文件')
    return false
  }
  
  const isValidSize = file.size / 1024 / 1024 < props.maxSize
  if (!isValidSize) {
    ElMessage.error(`视频大小不能超过 ${props.maxSize}MB`)
    return false
  }
  
  uploading.value = true
  uploadProgress.value = 0
  return true
}

const handleProgress = (event) => {
  uploadProgress.value = Math.round(event.percent)
}

const handleSuccess = (response) => {
  uploading.value = false
  uploadProgress.value = 0
  
  if (response.success) {
    const videoData = {
      url: response.data.url,
      poster: response.data.poster || '',
      title: response.data.title || '',
      duration: response.data.duration || 0,
      ...videoSettings
    }
    
    emit('update:modelValue', videoData)
    emit('change', videoData)
    ElMessage.success('视频上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleError = () => {
  uploading.value = false
  uploadProgress.value = 0
  ElMessage.error('视频上传失败，请重试')
}

const handleUrlInput = async () => {
  if (!videoUrlInput.value.trim()) return
  
  urlValidating.value = true
  
  try {
    // 验证视频链接
    const response = await fetch('/api/video/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({ url: videoUrlInput.value })
    })
    
    const result = await response.json()
    
    if (result.success) {
      const videoData = {
        url: videoUrlInput.value,
        poster: result.data.poster || '',
        title: result.data.title || '',
        duration: result.data.duration || 0,
        ...videoSettings
      }
      
      emit('update:modelValue', videoData)
      emit('change', videoData)
      ElMessage.success('视频链接验证成功')
    } else {
      ElMessage.error(result.message || '视频链接无效')
    }
  } catch (error) {
    ElMessage.error('链接验证失败，请检查网络连接')
  } finally {
    urlValidating.value = false
  }
}

const handleEdit = () => {
  if (typeof props.modelValue === 'object') {
    Object.assign(videoSettings, props.modelValue)
  }
  settingsVisible.value = true
}

const handleRemove = () => {
  emit('update:modelValue', '')
  emit('change', '')
  videoUrlInput.value = ''
  ElMessage.success('视频已删除')
}

const saveSettings = () => {
  const currentValue = typeof props.modelValue === 'object' ? props.modelValue : { url: props.modelValue }
  const videoData = {
    ...currentValue,
    ...videoSettings
  }
  
  emit('update:modelValue', videoData)
  emit('change', videoData)
  settingsVisible.value = false
  ElMessage.success('设置已保存')
}

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (typeof newValue === 'object' && newValue) {
    Object.assign(videoSettings, newValue)
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.video-uploader {
  .upload-type-selector {
    margin-bottom: 16px;
  }
  
  .upload-section {
    .video-upload {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
    
    .upload-dragger {
      text-align: center;
      
      .upload-icon {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }
      
      .upload-text {
        font-size: 16px;
        color: #606266;
        margin-bottom: 8px;
        
        em {
          color: #409eff;
          font-style: normal;
        }
      }
      
      .upload-hint {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .upload-progress {
      padding: 20px;
      text-align: center;
      
      .progress-text {
        margin-top: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .url-section {
    .url-tip {
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
  
  .video-preview {
    margin-top: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    
    .preview-header {
      padding: 12px 16px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .preview-title {
        font-weight: 600;
        color: #303133;
      }
      
      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .preview-content {
      padding: 16px;
      display: flex;
      gap: 16px;
      
      .video-info {
        flex: 1;
        
        .info-item {
          display: flex;
          margin-bottom: 8px;
          
          .label {
            width: 60px;
            color: #909399;
            font-size: 14px;
          }
          
          .value {
            flex: 1;
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
