<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Services\CacheService;
use App\Models\SystemSetting;

/**
 * 系统健康检查命令
 * 适配宝塔环境的系统监控
 */
class SystemHealthCheck extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'system:health-check 
                           {--format=table : 输出格式 (table|json)}
                           {--save : 保存检查结果到数据库}
                           {--alert : 发送告警通知}';

    /**
     * 命令描述
     */
    protected $description = '执行系统健康检查，监控各项服务状态';

    /**
     * 缓存服务
     */
    private CacheService $cacheService;

    /**
     * 检查结果
     */
    private array $results = [];

    /**
     * 构造函数
     */
    public function __construct(CacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始系统健康检查...');
        $startTime = microtime(true);

        // 执行各项检查
        $this->checkDatabase();
        $this->checkRedis();
        $this->checkCache();
        $this->checkStorage();
        $this->checkQueue();
        $this->checkPhp();
        $this->checkNginx();
        $this->checkSystem();
        $this->checkApplication();

        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);

        // 计算总体健康分数
        $healthScore = $this->calculateHealthScore();

        // 输出结果
        $this->outputResults($duration, $healthScore);

        // 保存结果
        if ($this->option('save')) {
            $this->saveResults($healthScore, $duration);
        }

        // 发送告警
        if ($this->option('alert') && $healthScore < 80) {
            $this->sendAlert($healthScore);
        }

        return $healthScore >= 80 ? 0 : 1;
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabase(): void
    {
        $this->info('检查数据库连接...');
        
        try {
            $startTime = microtime(true);
            
            // 测试连接
            DB::connection()->getPdo();
            
            // 测试查询
            $result = DB::select('SELECT 1 as test');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 检查表数量
            $tables = DB::select('SHOW TABLES');
            $tableCount = count($tables);
            
            // 检查数据库大小
            $dbName = DB::connection()->getDatabaseName();
            $sizeResult = DB::select("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb 
                FROM information_schema.tables 
                WHERE table_schema = ?
            ", [$dbName]);
            $dbSize = $sizeResult[0]->size_mb ?? 0;

            $this->results['database'] = [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'table_count' => $tableCount,
                'size_mb' => $dbSize,
                'message' => '数据库连接正常',
                'score' => 100
            ];

        } catch (\Exception $e) {
            $this->results['database'] = [
                'status' => 'error',
                'message' => '数据库连接失败: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查Redis连接
     */
    private function checkRedis(): void
    {
        $this->info('检查Redis连接...');
        
        try {
            $startTime = microtime(true);
            
            // 测试连接
            $info = Redis::info();
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 获取内存使用情况
            $usedMemory = $info['used_memory_human'] ?? '0B';
            $maxMemory = $info['maxmemory_human'] ?? 'unlimited';
            
            // 获取连接数
            $connectedClients = $info['connected_clients'] ?? 0;
            
            // 计算命中率
            $hits = $info['keyspace_hits'] ?? 0;
            $misses = $info['keyspace_misses'] ?? 0;
            $hitRate = ($hits + $misses) > 0 ? round($hits / ($hits + $misses) * 100, 2) : 0;

            $this->results['redis'] = [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'used_memory' => $usedMemory,
                'max_memory' => $maxMemory,
                'connected_clients' => $connectedClients,
                'hit_rate' => $hitRate,
                'message' => 'Redis连接正常',
                'score' => $hitRate >= 80 ? 100 : max(50, $hitRate)
            ];

        } catch (\Exception $e) {
            $this->results['redis'] = [
                'status' => 'error',
                'message' => 'Redis连接失败: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查缓存系统
     */
    private function checkCache(): void
    {
        $this->info('检查缓存系统...');
        
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_data';
            
            $startTime = microtime(true);
            
            // 测试写入
            Cache::put($testKey, $testValue, 60);
            
            // 测试读取
            $retrieved = Cache::get($testKey);
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 清理测试数据
            Cache::forget($testKey);
            
            // 获取缓存统计
            $stats = $this->cacheService->getStats();
            
            $score = 100;
            if ($retrieved !== $testValue) {
                $score = 0;
            } elseif ($responseTime > 100) {
                $score = 70;
            } elseif ($responseTime > 50) {
                $score = 85;
            }

            $this->results['cache'] = [
                'status' => $retrieved === $testValue ? 'healthy' : 'error',
                'response_time' => $responseTime,
                'stats' => $stats,
                'message' => $retrieved === $testValue ? '缓存系统正常' : '缓存读写失败',
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['cache'] = [
                'status' => 'error',
                'message' => '缓存系统异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查存储系统
     */
    private function checkStorage(): void
    {
        $this->info('检查存储系统...');
        
        try {
            // 检查磁盘空间
            $storagePath = storage_path();
            $freeBytes = disk_free_space($storagePath);
            $totalBytes = disk_total_space($storagePath);
            
            $freeGB = round($freeBytes / (1024 * 1024 * 1024), 2);
            $totalGB = round($totalBytes / (1024 * 1024 * 1024), 2);
            $usagePercent = round((($totalBytes - $freeBytes) / $totalBytes) * 100, 2);
            
            // 测试文件写入
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'health check test';
            
            $startTime = microtime(true);
            Storage::put($testFile, $testContent);
            $retrieved = Storage::get($testFile);
            Storage::delete($testFile);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 计算分数
            $score = 100;
            if ($retrieved !== $testContent) {
                $score = 0;
            } elseif ($usagePercent > 90) {
                $score = 30;
            } elseif ($usagePercent > 80) {
                $score = 60;
            } elseif ($freeGB < 1) {
                $score = 40;
            }

            $this->results['storage'] = [
                'status' => $retrieved === $testContent ? 'healthy' : 'error',
                'free_gb' => $freeGB,
                'total_gb' => $totalGB,
                'usage_percent' => $usagePercent,
                'response_time' => $responseTime,
                'message' => $retrieved === $testContent ? '存储系统正常' : '存储读写失败',
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['storage'] = [
                'status' => 'error',
                'message' => '存储系统异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查队列系统
     */
    private function checkQueue(): void
    {
        $this->info('检查队列系统...');
        
        try {
            // 检查队列连接
            $queueSize = Redis::llen('queues:default');
            $failedJobs = Redis::llen('queues:failed');
            
            // 检查Supervisor进程（如果可用）
            $supervisorStatus = 'unknown';
            if (function_exists('exec')) {
                exec('supervisorctl status ffjq-worker:* 2>/dev/null', $output, $returnCode);
                if ($returnCode === 0) {
                    $supervisorStatus = 'running';
                } else {
                    $supervisorStatus = 'stopped';
                }
            }
            
            // 计算分数
            $score = 100;
            if ($failedJobs > 100) {
                $score = 40;
            } elseif ($failedJobs > 50) {
                $score = 60;
            } elseif ($failedJobs > 10) {
                $score = 80;
            }
            
            if ($supervisorStatus === 'stopped') {
                $score = min($score, 50);
            }

            $this->results['queue'] = [
                'status' => $score >= 60 ? 'healthy' : 'warning',
                'queue_size' => $queueSize,
                'failed_jobs' => $failedJobs,
                'supervisor_status' => $supervisorStatus,
                'message' => "队列大小: {$queueSize}, 失败任务: {$failedJobs}",
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['queue'] = [
                'status' => 'error',
                'message' => '队列系统异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查PHP环境
     */
    private function checkPhp(): void
    {
        $this->info('检查PHP环境...');
        
        try {
            $phpVersion = PHP_VERSION;
            $memoryLimit = ini_get('memory_limit');
            $maxExecutionTime = ini_get('max_execution_time');
            $uploadMaxFilesize = ini_get('upload_max_filesize');
            
            // 检查必需扩展
            $requiredExtensions = [
                'pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer',
                'xml', 'ctype', 'json', 'bcmath', 'curl', 'fileinfo', 'gd', 'redis'
            ];
            
            $missingExtensions = [];
            foreach ($requiredExtensions as $extension) {
                if (!extension_loaded($extension)) {
                    $missingExtensions[] = $extension;
                }
            }
            
            // 计算分数
            $score = 100;
            if (version_compare($phpVersion, '8.1.0', '<')) {
                $score = 60;
            }
            if (!empty($missingExtensions)) {
                $score = min($score, 40);
            }
            
            $memoryBytes = $this->parseMemoryLimit($memoryLimit);
            if ($memoryBytes < 128 * 1024 * 1024) { // 128MB
                $score = min($score, 70);
            }

            $this->results['php'] = [
                'status' => empty($missingExtensions) ? 'healthy' : 'warning',
                'version' => $phpVersion,
                'memory_limit' => $memoryLimit,
                'max_execution_time' => $maxExecutionTime,
                'upload_max_filesize' => $uploadMaxFilesize,
                'missing_extensions' => $missingExtensions,
                'message' => empty($missingExtensions) ? 'PHP环境正常' : '缺少扩展: ' . implode(', ', $missingExtensions),
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['php'] = [
                'status' => 'error',
                'message' => 'PHP环境检查异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查Nginx状态
     */
    private function checkNginx(): void
    {
        $this->info('检查Nginx状态...');
        
        try {
            $status = 'unknown';
            $version = 'unknown';
            
            if (function_exists('exec')) {
                // 检查Nginx进程
                exec('pgrep nginx', $output, $returnCode);
                if ($returnCode === 0 && !empty($output)) {
                    $status = 'running';
                } else {
                    $status = 'stopped';
                }
                
                // 获取Nginx版本
                exec('nginx -v 2>&1', $versionOutput);
                if (!empty($versionOutput[0])) {
                    preg_match('/nginx\/([0-9.]+)/', $versionOutput[0], $matches);
                    $version = $matches[1] ?? 'unknown';
                }
            }
            
            // 测试HTTP响应
            $httpStatus = 'unknown';
            $responseTime = 0;
            
            try {
                $startTime = microtime(true);
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'GET'
                    ]
                ]);
                
                $response = file_get_contents(config('app.url') . '/health', false, $context);
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                $httpStatus = $response === "healthy\n" ? 'healthy' : 'error';
            } catch (\Exception $e) {
                $httpStatus = 'error';
            }
            
            // 计算分数
            $score = 100;
            if ($status !== 'running') {
                $score = 0;
            } elseif ($httpStatus !== 'healthy') {
                $score = 50;
            } elseif ($responseTime > 1000) {
                $score = 70;
            }

            $this->results['nginx'] = [
                'status' => $status === 'running' && $httpStatus === 'healthy' ? 'healthy' : 'error',
                'process_status' => $status,
                'version' => $version,
                'http_status' => $httpStatus,
                'response_time' => $responseTime,
                'message' => $status === 'running' ? 'Nginx运行正常' : 'Nginx未运行',
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['nginx'] = [
                'status' => 'error',
                'message' => 'Nginx检查异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 检查系统资源
     */
    private function checkSystem(): void
    {
        $this->info('检查系统资源...');
        
        try {
            // CPU负载
            $loadAvg = sys_getloadavg();
            $cpuLoad = $loadAvg[0] ?? 0;
            
            // 内存使用
            $memoryUsage = 0;
            $memoryTotal = 0;
            
            if (function_exists('exec') && PHP_OS_FAMILY === 'Linux') {
                exec('free -m', $output);
                if (isset($output[1])) {
                    $memInfo = preg_split('/\s+/', $output[1]);
                    $memoryTotal = (int) ($memInfo[1] ?? 0);
                    $memoryUsed = (int) ($memInfo[2] ?? 0);
                    $memoryUsage = $memoryTotal > 0 ? round($memoryUsed / $memoryTotal * 100, 2) : 0;
                }
            }
            
            // 计算分数
            $score = 100;
            if ($cpuLoad > 2.0) {
                $score = 40;
            } elseif ($cpuLoad > 1.0) {
                $score = 70;
            }
            
            if ($memoryUsage > 90) {
                $score = min($score, 30);
            } elseif ($memoryUsage > 80) {
                $score = min($score, 60);
            }

            $this->results['system'] = [
                'status' => $score >= 60 ? 'healthy' : 'warning',
                'cpu_load' => $cpuLoad,
                'memory_usage_percent' => $memoryUsage,
                'memory_total_mb' => $memoryTotal,
                'message' => "CPU负载: {$cpuLoad}, 内存使用: {$memoryUsage}%",
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['system'] = [
                'status' => 'error',
                'message' => '系统资源检查异常: ' . $e->getMessage(),
                'score' => 50
            ];
        }
    }

    /**
     * 检查应用程序
     */
    private function checkApplication(): void
    {
        $this->info('检查应用程序...');
        
        try {
            // 检查关键表的记录数
            $userCount = DB::table('users')->count();
            $groupCount = DB::table('wechat_groups')->count();
            $orderCount = DB::table('orders')->count();
            
            // 检查今日活跃度
            $todayOrders = DB::table('orders')
                ->whereDate('created_at', today())
                ->count();
            
            // 检查错误日志
            $logPath = storage_path('logs/laravel.log');
            $errorCount = 0;
            
            if (file_exists($logPath)) {
                $logContent = file_get_contents($logPath);
                $errorCount = substr_count($logContent, '.ERROR:');
            }
            
            // 计算分数
            $score = 100;
            if ($errorCount > 100) {
                $score = 40;
            } elseif ($errorCount > 50) {
                $score = 70;
            } elseif ($errorCount > 10) {
                $score = 85;
            }

            $this->results['application'] = [
                'status' => $score >= 70 ? 'healthy' : 'warning',
                'user_count' => $userCount,
                'group_count' => $groupCount,
                'order_count' => $orderCount,
                'today_orders' => $todayOrders,
                'error_count' => $errorCount,
                'message' => "用户: {$userCount}, 群组: {$groupCount}, 订单: {$orderCount}",
                'score' => $score
            ];

        } catch (\Exception $e) {
            $this->results['application'] = [
                'status' => 'error',
                'message' => '应用程序检查异常: ' . $e->getMessage(),
                'score' => 0
            ];
        }
    }

    /**
     * 计算总体健康分数
     */
    private function calculateHealthScore(): int
    {
        $totalScore = 0;
        $count = 0;
        
        foreach ($this->results as $result) {
            $totalScore += $result['score'];
            $count++;
        }
        
        return $count > 0 ? round($totalScore / $count) : 0;
    }

    /**
     * 输出检查结果
     */
    private function outputResults(float $duration, int $healthScore): void
    {
        if ($this->option('format') === 'json') {
            $this->line(json_encode([
                'health_score' => $healthScore,
                'duration_ms' => $duration,
                'timestamp' => now()->toISOString(),
                'results' => $this->results
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            return;
        }

        // 表格输出
        $this->newLine();
        $this->info("系统健康检查完成 (耗时: {$duration}ms)");
        $this->info("总体健康分数: {$healthScore}/100");
        
        $tableData = [];
        foreach ($this->results as $component => $result) {
            $status = $result['status'];
            $statusColor = match($status) {
                'healthy' => '<fg=green>正常</>',
                'warning' => '<fg=yellow>警告</>',
                'error' => '<fg=red>错误</>',
                default => $status
            };
            
            $tableData[] = [
                $component,
                $statusColor,
                $result['score'] . '/100',
                $result['message']
            ];
        }
        
        $this->table(['组件', '状态', '分数', '消息'], $tableData);
        
        // 健康状态总结
        if ($healthScore >= 90) {
            $this->info('✅ 系统运行状态优秀');
        } elseif ($healthScore >= 80) {
            $this->comment('⚠️  系统运行状态良好，有轻微问题');
        } elseif ($healthScore >= 60) {
            $this->warn('⚠️  系统运行状态一般，需要关注');
        } else {
            $this->error('❌ 系统运行状态较差，需要立即处理');
        }
    }

    /**
     * 保存检查结果
     */
    private function saveResults(int $healthScore, float $duration): void
    {
        try {
            DB::table('system_health_logs')->insert([
                'health_score' => $healthScore,
                'duration_ms' => $duration,
                'results' => json_encode($this->results),
                'created_at' => now(),
            ]);
            
            $this->info('检查结果已保存到数据库');
        } catch (\Exception $e) {
            $this->warn('保存检查结果失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送告警通知
     */
    private function sendAlert(int $healthScore): void
    {
        try {
            // 这里可以集成邮件、短信、钉钉等通知方式
            $this->warn("系统健康分数较低: {$healthScore}/100，已发送告警通知");
        } catch (\Exception $e) {
            $this->warn('发送告警失败: ' . $e->getMessage());
        }
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit) - 1]);
        $value = (int) $memoryLimit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
}