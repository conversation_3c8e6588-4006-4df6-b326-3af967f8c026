import{_ as e}from"./index-DtXAftX0.js";/* empty css                  *//* empty css                       *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                     *//* empty css                          *//* empty css                        *//* empty css                       *//* empty css                       *//* empty css                 *//* empty css                *//* empty css               */import{p as a}from"./export-BIRLwzxN.js";import{aZ as l,a_ as t,U as s,aY as d,o,at as i,bp as n,bq as r,b9 as u,b8 as c,bm as p,bB as m,by as _,br as f,bt as b,bu as v,aM as y,bh as g,bi as k,a$ as h,bc as V,bx as w,cs as x,bs as j,ay as z,Q as C,R as U}from"./element-plus-h2SQQM64.js";import{L as D,r as M,e as Y,k as B,l as I,E as $,z as S,t as q,F as H,Y as T,y as E,D as O}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const P={class:"app-container"},J={class:"stat-item"},N={class:"stat-content"},A={class:"stat-number"},F={class:"stat-item"},G={class:"stat-content"},L={class:"stat-number"},Q={class:"stat-item"},R={class:"stat-content"},Z={class:"stat-number"},K={class:"stat-item"},W={class:"stat-content"},X={class:"stat-number"},ee={class:"export-template"},ae={class:"template-header"},le={class:"template-icon"},te={class:"template-info"},se={class:"template-name"},de={class:"template-desc"},oe={class:"template-stats"},ie={class:"stat-row"},ne={class:"stat-value"},re={class:"stat-row"},ue={class:"stat-value"},ce={class:"stat-row"},pe={class:"stat-value"},me={class:"template-actions"},_e={class:"card-header"},fe={class:"table-pagination"},be={class:"dialog-footer"},ve={key:0},ye={key:1,style:{"text-align":"center",padding:"40px"}},ge={class:"dialog-footer"},ke=e({__name:"DataExport",setup(e){const ke=D({total_exports:1245,today_exports:23,total_size:"2.5GB",pending_exports:3}),he=M([{key:"users",name:"用户数据",description:"导出所有用户的基本信息",icon:"el-icon-user",count:"12,345",size:"2.5MB",last_updated:"2024-01-01 10:00",loading:!1},{key:"orders",name:"订单数据",description:"导出所有订单记录",icon:"el-icon-goods",count:"8,765",size:"5.2MB",last_updated:"2024-01-01 09:30",loading:!1},{key:"finance",name:"财务数据",description:"导出收支明细记录",icon:"el-icon-money",count:"3,456",size:"1.8MB",last_updated:"2024-01-01 08:45",loading:!1}]),Ve=D({data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),we=M([]),xe=M([{id:1,name:"用户数据导出",data_type:"users",format:"excel",status:"completed",progress:100,file_size:"2.5MB",created_at:"2024-01-01 10:00:00"},{id:2,name:"订单数据导出",data_type:"orders",format:"csv",status:"processing",progress:65,file_size:"-",created_at:"2024-01-01 10:30:00"},{id:3,name:"财务数据导出",data_type:"finance",format:"json",status:"failed",progress:0,file_size:"-",created_at:"2024-01-01 09:15:00"}]),je=D({visible:!1,template:null}),ze=D({visible:!1,data:[],columns:[]}),Ce=D({frequency:"daily",time:null,email:"",enabled:!0}),Ue=M(!1),De=M(1),Me=M(10),Ye=M(0),Be=e=>{we.value={users:[{key:"id",label:"ID"},{key:"username",label:"用户名"},{key:"email",label:"邮箱"},{key:"phone",label:"手机号"},{key:"created_at",label:"创建时间"}],orders:[{key:"id",label:"订单ID"},{key:"user_id",label:"用户ID"},{key:"amount",label:"金额"},{key:"status",label:"状态"},{key:"created_at",label:"创建时间"}],finance:[{key:"id",label:"ID"},{key:"type",label:"类型"},{key:"amount",label:"金额"},{key:"balance",label:"余额"},{key:"created_at",label:"创建时间"}]}[e]||[],Ve.fields=we.value.map(e=>e.key)},Ie=async()=>{if(Ve.data_type){Ue.value=!0;try{await new Promise(e=>setTimeout(e,3e3)),C.success("导出任务已创建"),He()}catch(e){C.error("导出任务创建失败")}finally{Ue.value=!1}}else C.warning("请选择数据类型")},$e=()=>{Object.assign(Ve,{data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),we.value=[]},Se=async()=>{if(Ve.data_type)try{C.info("正在获取预览数据...");const e={data_type:Ve.data_type,fields:Ve.fields,filters:Ve.filters,date_range:Ve.date_range,limit:10},l=await a(e);l.data.success?(ze.visible=!0,ze.data=l.data.data,ze.columns=l.data.columns,C.success("预览数据获取成功")):C.error("预览数据获取失败")}catch(e){C.error("预览数据获取失败："+e.message)}else C.warning("请选择数据类型")},qe=e=>({pending:"待处理",processing:"处理中",completed:"已完成",failed:"失败"}[e]||e),He=()=>{C.success("任务列表已刷新")},Te=e=>{Me.value=e,He()},Ee=e=>{De.value=e,He()},Oe=()=>{C.success("定时导出设置已保存"),je.visible=!1};return Y(()=>{Ye.value=xe.value.length}),(e,a)=>{const D=t,M=l,Y=d,Pe=i,Je=c,Ne=u,Ae=r,Fe=m,Ge=p,Le=_,Qe=f,Re=v,Ze=b,Ke=y,We=n,Xe=k,ea=h,aa=V,la=g,ta=w,sa=x,da=j,oa=z;return I(),B("div",P,[$(Y,{class:"overview-card"},{header:S(()=>a[19]||(a[19]=[q("div",{class:"card-header"},[q("span",null,"📊 数据导出概览")],-1)])),default:S(()=>[$(M,{gutter:20},{default:S(()=>[$(D,{span:6},{default:S(()=>[q("div",J,[a[21]||(a[21]=q("div",{class:"stat-icon export-icon"},[q("i",{class:"el-icon-download"})],-1)),q("div",N,[q("div",A,s(ke.total_exports),1),a[20]||(a[20]=q("div",{class:"stat-label"},"总导出次数",-1))])])]),_:1}),$(D,{span:6},{default:S(()=>[q("div",F,[a[23]||(a[23]=q("div",{class:"stat-icon today-icon"},[q("i",{class:"el-icon-calendar-today"})],-1)),q("div",G,[q("div",L,s(ke.today_exports),1),a[22]||(a[22]=q("div",{class:"stat-label"},"今日导出",-1))])])]),_:1}),$(D,{span:6},{default:S(()=>[q("div",Q,[a[25]||(a[25]=q("div",{class:"stat-icon size-icon"},[q("i",{class:"el-icon-files"})],-1)),q("div",R,[q("div",Z,s(ke.total_size),1),a[24]||(a[24]=q("div",{class:"stat-label"},"总文件大小",-1))])])]),_:1}),$(D,{span:6},{default:S(()=>[q("div",K,[a[27]||(a[27]=q("div",{class:"stat-icon pending-icon"},[q("i",{class:"el-icon-loading"})],-1)),q("div",W,[q("div",X,s(ke.pending_exports),1),a[26]||(a[26]=q("div",{class:"stat-label"},"待处理任务",-1))])])]),_:1})]),_:1})]),_:1}),$(Y,{style:{"margin-top":"20px"}},{header:S(()=>a[28]||(a[28]=[q("div",{class:"card-header"},[q("span",null,"⚡ 快速导出")],-1)])),default:S(()=>[$(M,{gutter:20},{default:S(()=>[(I(!0),B(H,null,T(he.value,e=>(I(),E(D,{span:8,key:e.key},{default:S(()=>[q("div",ee,[q("div",ae,[q("div",le,[q("i",{class:o(e.icon)},null,2)]),q("div",te,[q("div",se,s(e.name),1),q("div",de,s(e.description),1)])]),q("div",oe,[q("div",ie,[a[29]||(a[29]=q("span",{class:"stat-label"},"数据量:",-1)),q("span",ne,s(e.count),1)]),q("div",re,[a[30]||(a[30]=q("span",{class:"stat-label"},"预计大小:",-1)),q("span",ue,s(e.size),1)]),q("div",ce,[a[31]||(a[31]=q("span",{class:"stat-label"},"最后更新:",-1)),q("span",pe,s(e.last_updated),1)])]),q("div",me,[$(Pe,{type:"primary",onClick:a=>(async e=>{e.loading=!0;try{await new Promise(e=>setTimeout(e,2e3)),C.success(`${e.name} 导出成功`)}catch(a){C.error(`${e.name} 导出失败`)}finally{e.loading=!1}})(e),loading:e.loading},{default:S(()=>a[32]||(a[32]=[O(" 立即导出 ",-1)])),_:2,__:[32]},1032,["onClick","loading"]),$(Pe,{type:"info",onClick:a=>(e=>{je.template=e,je.visible=!0})(e)},{default:S(()=>a[33]||(a[33]=[O(" 定时导出 ",-1)])),_:2,__:[33]},1032,["onClick"])])])]),_:2},1024))),128))]),_:1})]),_:1}),$(Y,{style:{"margin-top":"20px"}},{header:S(()=>a[34]||(a[34]=[q("div",{class:"card-header"},[q("span",null,"🎯 自定义导出")],-1)])),default:S(()=>[$(We,{model:Ve,"label-width":"120px"},{default:S(()=>[$(M,{gutter:20},{default:S(()=>[$(D,{span:12},{default:S(()=>[$(Ae,{label:"导出数据类型"},{default:S(()=>[$(Ne,{modelValue:Ve.data_type,"onUpdate:modelValue":a[0]||(a[0]=e=>Ve.data_type=e),placeholder:"请选择数据类型",onChange:Be},{default:S(()=>[$(Je,{label:"用户数据",value:"users"}),$(Je,{label:"订单数据",value:"orders"}),$(Je,{label:"财务数据",value:"finance"}),$(Je,{label:"分销数据",value:"distribution"}),$(Je,{label:"社群数据",value:"community"}),$(Je,{label:"防红数据",value:"anti-block"}),$(Je,{label:"系统日志",value:"logs"})]),_:1},8,["modelValue"])]),_:1}),$(Ae,{label:"导出格式"},{default:S(()=>[$(Ge,{modelValue:Ve.format,"onUpdate:modelValue":a[1]||(a[1]=e=>Ve.format=e)},{default:S(()=>[$(Fe,{label:"excel"},{default:S(()=>a[35]||(a[35]=[O("Excel (.xlsx)",-1)])),_:1,__:[35]}),$(Fe,{label:"csv"},{default:S(()=>a[36]||(a[36]=[O("CSV (.csv)",-1)])),_:1,__:[36]}),$(Fe,{label:"json"},{default:S(()=>a[37]||(a[37]=[O("JSON (.json)",-1)])),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),$(Ae,{label:"时间范围"},{default:S(()=>[$(Le,{modelValue:Ve.date_range,"onUpdate:modelValue":a[2]||(a[2]=e=>Ve.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),$(Ae,{label:"数据量限制"},{default:S(()=>[$(Qe,{modelValue:Ve.limit,"onUpdate:modelValue":a[3]||(a[3]=e=>Ve.limit=e),min:1,max:1e5},null,8,["modelValue"]),a[38]||(a[38]=q("span",{class:"form-tip"},"建议单次导出不超过50000条数据",-1))]),_:1,__:[38]})]),_:1}),$(D,{span:12},{default:S(()=>[$(Ae,{label:"导出字段"},{default:S(()=>[$(Ze,{modelValue:Ve.fields,"onUpdate:modelValue":a[4]||(a[4]=e=>Ve.fields=e)},{default:S(()=>[(I(!0),B(H,null,T(we.value,e=>(I(),E(Re,{key:e.key,label:e.key},{default:S(()=>[O(s(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),$(Ae,{label:"筛选条件"},{default:S(()=>[$(Ke,{type:"textarea",modelValue:Ve.filters,"onUpdate:modelValue":a[5]||(a[5]=e=>Ve.filters=e),placeholder:"请输入筛选条件（JSON格式）"},null,8,["modelValue"])]),_:1}),$(Ae,{label:"排序方式"},{default:S(()=>[$(Ne,{modelValue:Ve.sort_by,"onUpdate:modelValue":a[6]||(a[6]=e=>Ve.sort_by=e),placeholder:"选择排序字段"},{default:S(()=>[$(Je,{label:"创建时间",value:"created_at"}),$(Je,{label:"更新时间",value:"updated_at"}),$(Je,{label:"ID",value:"id"})]),_:1},8,["modelValue"]),$(Ne,{modelValue:Ve.sort_order,"onUpdate:modelValue":a[7]||(a[7]=e=>Ve.sort_order=e),placeholder:"排序方式",style:{"margin-left":"10px"}},{default:S(()=>[$(Je,{label:"升序",value:"asc"}),$(Je,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),$(Ae,{label:"导出名称"},{default:S(()=>[$(Ke,{modelValue:Ve.name,"onUpdate:modelValue":a[8]||(a[8]=e=>Ve.name=e),placeholder:"请输入导出文件名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),$(Ae,null,{default:S(()=>[$(Pe,{type:"primary",onClick:Ie,loading:Ue.value},{default:S(()=>a[39]||(a[39]=[O(" 开始导出 ",-1)])),_:1,__:[39]},8,["loading"]),$(Pe,{onClick:$e},{default:S(()=>a[40]||(a[40]=[O("重置",-1)])),_:1,__:[40]}),$(Pe,{type:"info",onClick:Se},{default:S(()=>a[41]||(a[41]=[O("预览数据",-1)])),_:1,__:[41]})]),_:1})]),_:1},8,["model"])]),_:1}),$(Y,{style:{"margin-top":"20px"}},{header:S(()=>[q("div",_e,[a[43]||(a[43]=q("span",null,"📋 导出任务列表",-1)),$(Pe,{type:"primary",onClick:He},{default:S(()=>a[42]||(a[42]=[O("刷新",-1)])),_:1,__:[42]})])]),default:S(()=>[$(la,{data:xe.value,style:{width:"100%"}},{default:S(()=>[$(Xe,{prop:"id",label:"任务ID",width:"80"}),$(Xe,{prop:"name",label:"任务名称"}),$(Xe,{prop:"data_type",label:"数据类型",width:"100"}),$(Xe,{prop:"format",label:"格式",width:"80"}),$(Xe,{prop:"status",label:"状态",width:"100"},{default:S(e=>{return[$(ea,{type:(a=e.row.status,{pending:"info",processing:"warning",completed:"success",failed:"danger"}[a]||"info")},{default:S(()=>[O(s(qe(e.row.status)),1)]),_:2},1032,["type"])];var a}),_:1}),$(Xe,{prop:"progress",label:"进度",width:"120"},{default:S(e=>[$(aa,{percentage:e.row.progress,status:"failed"===e.row.status?"exception":""},null,8,["percentage","status"])]),_:1}),$(Xe,{prop:"file_size",label:"文件大小",width:"100"}),$(Xe,{prop:"created_at",label:"创建时间",width:"160"}),$(Xe,{label:"操作",width:"200"},{default:S(e=>[$(Pe,{type:"primary",size:"small",onClick:a=>{return l=e.row,void C.success(`开始下载 ${l.name}`);var l},disabled:"completed"!==e.row.status},{default:S(()=>a[44]||(a[44]=[O(" 下载 ",-1)])),_:2,__:[44]},1032,["onClick","disabled"]),$(Pe,{type:"warning",size:"small",onClick:a=>{return l=e.row,C.info(`重试任务 ${l.name}`),l.status="pending",void(l.progress=0);var l},disabled:"failed"!==e.row.status},{default:S(()=>a[45]||(a[45]=[O(" 重试 ",-1)])),_:2,__:[45]},1032,["onClick","disabled"]),$(Pe,{type:"danger",size:"small",onClick:a=>{return l=e.row,void U.confirm(`确定要删除任务 ${l.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.success("删除成功"),He()});var l}},{default:S(()=>a[46]||(a[46]=[O(" 删除 ",-1)])),_:2,__:[46]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),q("div",fe,[$(ta,{"current-page":De.value,"onUpdate:currentPage":a[9]||(a[9]=e=>De.value=e),"page-size":Me.value,"onUpdate:pageSize":a[10]||(a[10]=e=>Me.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Ye.value,onSizeChange:Te,onCurrentChange:Ee},null,8,["current-page","page-size","total"])])]),_:1}),$(oa,{title:"定时导出设置",modelValue:je.visible,"onUpdate:modelValue":a[16]||(a[16]=e=>je.visible=e),width:"600px"},{footer:S(()=>[q("div",be,[$(Pe,{onClick:a[15]||(a[15]=e=>je.visible=!1)},{default:S(()=>a[47]||(a[47]=[O("取消",-1)])),_:1,__:[47]}),$(Pe,{type:"primary",onClick:Oe},{default:S(()=>a[48]||(a[48]=[O("保存",-1)])),_:1,__:[48]})])]),default:S(()=>[$(We,{model:Ce,"label-width":"100px"},{default:S(()=>[$(Ae,{label:"导出频率"},{default:S(()=>[$(Ne,{modelValue:Ce.frequency,"onUpdate:modelValue":a[11]||(a[11]=e=>Ce.frequency=e),placeholder:"请选择导出频率"},{default:S(()=>[$(Je,{label:"每日",value:"daily"}),$(Je,{label:"每周",value:"weekly"}),$(Je,{label:"每月",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),$(Ae,{label:"执行时间"},{default:S(()=>[$(sa,{modelValue:Ce.time,"onUpdate:modelValue":a[12]||(a[12]=e=>Ce.time=e),placeholder:"选择时间"},null,8,["modelValue"])]),_:1}),$(Ae,{label:"邮件通知"},{default:S(()=>[$(Ke,{modelValue:Ce.email,"onUpdate:modelValue":a[13]||(a[13]=e=>Ce.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),$(Ae,{label:"是否启用"},{default:S(()=>[$(da,{modelValue:Ce.enabled,"onUpdate:modelValue":a[14]||(a[14]=e=>Ce.enabled=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),$(oa,{title:"数据预览",modelValue:ze.visible,"onUpdate:modelValue":a[18]||(a[18]=e=>ze.visible=e),width:"80%",top:"5vh"},{footer:S(()=>[q("div",ge,[$(Pe,{onClick:a[17]||(a[17]=e=>ze.visible=!1)},{default:S(()=>a[51]||(a[51]=[O("关闭",-1)])),_:1,__:[51]})])]),default:S(()=>[ze.data.length>0?(I(),B("div",ve,[$(la,{data:ze.data,stripe:""},{default:S(()=>[(I(!0),B(H,null,T(ze.columns,e=>(I(),E(Xe,{key:e.key,prop:e.key,label:e.label,"show-overflow-tooltip":""},null,8,["prop","label"]))),128))]),_:1},8,["data"]),a[49]||(a[49]=q("div",{style:{"margin-top":"10px",color:"#666","font-size":"14px"}},[q("i",{class:"el-icon-info"}),O(" 预览数据仅显示前10条记录 ")],-1))])):(I(),B("div",ye,a[50]||(a[50]=[q("i",{class:"el-icon-document",style:{"font-size":"48px",color:"#ddd"}},null,-1),q("p",{style:{color:"#999","margin-top":"10px"}},"暂无数据",-1)])))]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-c8cc2d32"]]);export{ke as default};
