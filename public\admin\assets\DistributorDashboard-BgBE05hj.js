import{_ as e,u as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               */import{r as l,e as t,H as s,k as o,B as i,l as r,t as n,E as c,z as u,u as d,af as v,L as m,c as p,n as f,A as _,D as g,F as y,Y as b,y as h,C as w}from"./vue-vendor-Dy164gUc.js";import{T as C,as as k,at as x,o as D,U as F,bZ as A,Q as M,bw as V,aS as j,a$ as z,ao as E,ap as q,a_ as T,p as I,bd as P,aT as S,aZ as U,an as B,a8 as $,ai as R,a7 as L,aY as O,bm as H,bn as W,bz as Z,aR as N,aN as Y,aM as J,ab as Q,a0 as G,by as K,bh as X,bi as ee,ay as ae,ak as le,a4 as te,$ as se}from"./element-plus-h2SQQM64.js";/* empty css                                                                 */import{L as oe}from"./LineChart-CydsJ2U8.js";import{D as ie}from"./DoughnutChart-CCHHIMjz.js";import"./utils-D1VZuEZr.js";const re={key:0,class:"performance-monitor"},ne={class:"monitor-header"},ce={class:"monitor-content"},ue={class:"metric-item"},de={class:"metric-item"},ve={class:"metric-value"},me={class:"metric-item"},pe={class:"metric-item"},fe=e({__name:"PerformanceMonitor",setup(e,{expose:a}){const v=l(!1),m=l(0),p=l(0),f=l(60),_=l(0);let g=null;const y=()=>{v.value=!v.value},b=e=>e<500?"good":e<1500?"warning":"poor",h=()=>{performance.memory&&(p.value=Math.round(performance.memory.usedJSHeapSize/1024/1024))},w=()=>{const e=performance.now();setTimeout(()=>{_.value=Math.round(performance.now()-e)},500*Math.random()+200)};return t(()=>{(()=>{if(performance.timing){const e=performance.timing.navigationStart,a=performance.timing.loadEventEnd;m.value=a-e}})(),h(),(()=>{let e=0,a=performance.now();const l=t=>{e++,t>=a+1e3&&(f.value=Math.round(1e3*e/(t-a)),e=0,a=t),g=requestAnimationFrame(l)};g=requestAnimationFrame(l)})(),w();const e=setInterval(()=>{h(),w()},2e3);s(()=>{clearInterval(e),g&&cancelAnimationFrame(g)})}),a({toggleMonitor:y,showMonitor:v}),(e,a)=>{const l=C,t=x;return v.value?(r(),o("div",re,[n("div",ne,[a[0]||(a[0]=n("span",null,"性能监控",-1)),c(t,{size:"small",onClick:y},{default:u(()=>[c(l,null,{default:u(()=>[c(d(k))]),_:1})]),_:1})]),n("div",ce,[n("div",ue,[a[1]||(a[1]=n("span",{class:"metric-label"},"页面加载时间:",-1)),n("span",{class:D(["metric-value",(g=m.value,g<1e3?"good":g<3e3?"warning":"poor")])},F(m.value)+"ms",3)]),n("div",de,[a[2]||(a[2]=n("span",{class:"metric-label"},"内存使用:",-1)),n("span",ve,F(p.value)+"MB",1)]),n("div",me,[a[3]||(a[3]=n("span",{class:"metric-label"},"FPS:",-1)),n("span",{class:D(["metric-value",(s=f.value,s>=55?"good":s>=30?"warning":"poor")])},F(f.value),3)]),n("div",pe,[a[4]||(a[4]=n("span",{class:"metric-label"},"API响应:",-1)),n("span",{class:D(["metric-value",b(_.value)])},F(_.value)+"ms",3)])])])):i("",!0);var s,g}}},[["__scopeId","data-v-de8bb903"]]),_e={class:"distributor-dashboard","element-loading-text":"加载中..."},ge={class:"page-header"},ye={class:"distributor-info"},be={class:"info-content"},he={class:"status-tags"},we={class:"header-actions"},Ce=["onClick"],ke={class:"stat-content"},xe={class:"stat-value"},De={class:"stat-title"},Fe={class:"card-header"},Ae={class:"card-header"},Me={class:"customer-activities"},Ve={class:"activity-avatar"},je={class:"activity-content"},ze={class:"activity-title"},Ee={class:"activity-desc"},qe={class:"activity-time"},Te={key:0,class:"activity-value"},Ie={class:"value-amount"},Pe={key:0,class:"empty-state"},Se={class:"card-header"},Ue={class:"follow-up-list"},Be={class:"customer-info"},$e={class:"customer-name"},Re={class:"customer-level"},Le={class:"follow-up-time"},Oe={class:"follow-up-actions"},He={key:0,class:"empty-state"},We={class:"conversion-analysis"},Ze={class:"analysis-header"},Ne={class:"conversion-stats"},Ye={class:"stat-item"},Je={class:"stat-value"},Qe={class:"stat-item"},Ge={class:"stat-value"},Ke={class:"stat-item"},Xe={class:"stat-value"},ea={class:"stat-item"},aa={class:"stat-value"},la={class:"conversion-sources"},ta={key:0,class:"qr-content"},sa={class:"qr-info"},oa={class:"qr-image-container"},ia={class:"qr-placeholder"},ra={class:"qr-actions"},na={class:"qr-tips"},ca="/avatars/default.jpg",ua=e({__name:"DistributorDashboard",setup(e){const s=v(),k=a(),re=l(!1),ne=l("30d"),ce=l(!1),ue=l(!1),de=l(null),ve=l([]),me=m({totalVisits:1250,totalConversions:89,conversionRate:7.12,avgOrderValue:156.8}),pe=l([{source:"微信群",visits:450,conversions:32,rate:7.11},{source:"朋友圈",visits:380,conversions:28,rate:7.37},{source:"直接分享",visits:280,conversions:19,rate:6.79},{source:"其他渠道",visits:140,conversions:10,rate:7.14}]),ua=m({customer:!1,group:!1,copy:!1,qrcode:!1}),da=m({revenue:!1,customer:!1}),va=m({activities:!1,followUps:!1}),ma=l([]),pa=l([]),fa=l(""),_a=m({todayClicks:0,totalClicks:0}),ga=l([{key:"customers",title:"客户总数",value:0,icon:"User",color:"#409EFF",trend:0,prefix:"",suffix:""},{key:"groups",title:"活跃群组",value:0,icon:"Comment",color:"#67C23A",trend:0,prefix:"",suffix:""},{key:"commission",title:"本月佣金",value:0,icon:"Money",color:"#E6A23C",trend:0,prefix:"¥",suffix:""},{key:"conversion",title:"转化率",value:0,icon:"TrendCharts",color:"#F56C6C",trend:0,prefix:"",suffix:"%"}]),ya=l([{key:"customer-management",title:"客户管理",icon:"User",color:"#409EFF",disabled:!1,badge:0},{key:"add-customer",title:"新增客户",icon:"UserFilled",color:"#67C23A",disabled:!1},{key:"group-management",title:"群组管理",icon:"Comment",color:"#E6A23C",disabled:!1,badge:0},{key:"promotion-links",title:"推广链接",icon:"Link",color:"#409EFF",disabled:!1},{key:"commission-logs",title:"佣金查看",icon:"Money",color:"#F56C6C",disabled:!1},{key:"order-list",title:"订单查看",icon:"Tickets",color:"#909399",disabled:!1}]),ba=l({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0}]}),ha=l({labels:["A级客户","B级客户","C级客户","D级客户"],datasets:[{data:[25,45,60,26],backgroundColor:["#F56C6C","#E6A23C","#409EFF","#67C23A"],borderWidth:0,hoverOffset:4}]}),wa={responsive:!0,maintainAspectRatio:!1,interaction:{intersect:!1,mode:"index"},plugins:{legend:{display:!0,position:"top"},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"#409EFF",borderWidth:1}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"}},x:{grid:{display:!1}}}},Ca={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff"}}},ka=p(()=>k.userInfo?.distributor_code||"D"+(k.userInfo?.id||"001")),xa=p(()=>({A:"A级分销员",B:"B级分销员",C:"C级分销员",D:"D级分销员"}[k.userInfo?.distributor_level||"C"]||"C级分销员")),Da=async()=>{try{await new Promise(e=>setTimeout(e,300)),ga.value[0].value=156,ga.value[0].trend=8.2,ga.value[1].value=23,ga.value[1].trend=15.6,ga.value[2].value=8650,ga.value[2].trend=23.4,ga.value[3].value=12.5,ga.value[3].trend=-2.1,ya.value[0].badge=5,ya.value[2].badge=2,console.log("统计数据加载完成")}catch(e){console.error("加载统计数据失败:",e),M.error("加载统计数据失败")}},Fa=async()=>{da.revenue=!0;try{await new Promise(e=>setTimeout(e,500));const e={"7d":{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120,190,300,500,200,300,450]},"30d":{labels:Array.from({length:30},(e,a)=>`${a+1}日`),data:Array.from({length:30},()=>Math.floor(1e3*Math.random())+200)},"90d":{labels:["第1月","第2月","第3月"],data:[8e3,12e3,15e3]}}[ne.value];ba.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#409EFF",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]},console.log("收入图表数据加载完成")}catch(e){console.error("加载收入数据失败:",e),M.error("加载收入数据失败")}finally{da.revenue=!1}},Aa=async()=>{va.activities=!0;try{await new Promise(e=>setTimeout(e,400)),ma.value=[{id:1,title:"新客户注册",description:'客户"张三"通过推广链接注册',created_at:new Date,customer:{avatar:ca}},{id:2,title:"客户下单",description:'客户"李四"购买了VIP群组',created_at:new Date(Date.now()-36e5),value:299,customer:{avatar:ca}},{id:3,title:"客户升级",description:'客户"王五"升级为A级客户',created_at:new Date(Date.now()-72e5),customer:{avatar:ca}},{id:4,title:"佣金到账",description:"获得推广佣金奖励",created_at:new Date(Date.now()-108e5),value:150,customer:{avatar:ca}}],console.log("客户动态数据加载完成")}catch(e){console.error("加载客户动态失败:",e),M.error("加载客户动态失败")}finally{va.activities=!1}},Ma=async()=>{va.followUps=!0;try{await new Promise(e=>setTimeout(e,350)),pa.value=[{id:1,name:"王五",level:"A",level_text:"A级客户",next_follow_up:new Date(Date.now()+864e5),following:!1},{id:2,name:"赵六",level:"B",level_text:"B级客户",next_follow_up:new Date(Date.now()-36e5),following:!1},{id:3,name:"孙七",level:"C",level_text:"C级客户",next_follow_up:new Date(Date.now()+1728e5),following:!1}],console.log("待跟进客户数据加载完成")}catch(e){console.error("加载待跟进客户失败:",e),M.error("加载待跟进客户失败")}finally{va.followUps=!1}},Va=async()=>{ua.customer=!0;try{await s.push("/distribution/customers"),A.success({title:"跳转成功",message:"已跳转到客户管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}finally{ua.customer=!1}},ja=async()=>{try{await s.push("/distribution/customers?action=create")}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},za=async()=>{ua.group=!0;try{await s.push("/distributor/group-management"),A.success({title:"跳转成功",message:"已跳转到群组管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}finally{ua.group=!1}},Ea=async()=>{try{await s.push("/distributor/promotion-links"),A.success({title:"跳转成功",message:"已跳转到推广链接管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},qa=async()=>{try{await s.push("/distributor/commission-logs"),A.success({title:"跳转成功",message:"已跳转到佣金查看页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},Ta=async()=>{try{await s.push("/distributor/order-list"),A.success({title:"跳转成功",message:"已跳转到订单查看页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},Ia=async()=>{try{await s.push("/distribution/customers?tab=follow-up")}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},Pa=async()=>{ua.copy=!0;try{if(navigator.clipboard)await navigator.clipboard.writeText(fa.value),A.success({title:"复制成功",message:"推广链接已复制到剪贴板"});else{const e=document.createElement("textarea");e.value=fa.value,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),A.success({title:"复制成功",message:"推广链接已复制到剪贴板"})}}catch(e){console.error("复制失败:",e),M.error("复制失败，请手动复制")}finally{ua.copy=!1}},Sa=async()=>{ua.qrcode=!0;try{await new Promise(e=>setTimeout(e,1e3));const e={url:`https://app.example.com/invite?code=${distributorStore.distributorCode}`,code:distributorStore.distributorCode,name:distributorStore.distributorInfo.name};Ba(e),M.success("推广二维码生成成功！")}catch(e){console.error("生成二维码失败:",e),M.error("生成二维码失败，请重试")}finally{ua.qrcode=!1}},Ua=()=>{ce.value=!0},Ba=e=>{de.value=e,ue.value=!0},$a=()=>{M.success("二维码下载功能开发中")},Ra=()=>{de.value?.url&&navigator.clipboard.writeText(de.value.url).then(()=>{M.success("推广链接已复制到剪贴板")}).catch(()=>{M.error("复制失败，请手动复制")})},La=e=>{const a=new Date,l=new Date(e);return l<a?"overdue":l-a<864e5?"urgent":"normal"},Oa=e=>{const a=new Date,l=new Date(e)-a;if(l<0){return`逾期${Math.floor(Math.abs(l)/36e5)}小时`}if(l<864e5){return`${Math.floor(l/36e5)}小时后`}return`${Math.floor(l/864e5)}天后`};return t(async()=>{console.log("优化版分销员工作台开始加载..."),re.value=!0;try{await Promise.all([Da(),Fa(),Aa(),Ma()]),(()=>{const e=k.userInfo?.id||"001";fa.value=`${window.location.origin}/register?distributor=${e}`,_a.todayClicks=Math.floor(50*Math.random())+10,_a.totalClicks=Math.floor(1e3*Math.random())+500})(),await f(),console.log("优化版分销员工作台加载完成"),A.success({title:"加载完成",message:"工作台数据已全部加载完成",duration:2e3})}catch(e){console.error("页面数据加载失败:",e),M.error("页面数据加载失败，请刷新重试")}finally{re.value=!1}}),(e,a)=>{const l=j,t=z,s=C,v=x,m=T,p=U,f=O,da=W,va=H,_a=Z,ya=Y,Da=J,Ma=K,Ba=ee,Ha=X,Wa=ae,Za=V;return _((r(),o("div",_e,[c(fe,{ref:"performanceMonitor"},null,512),n("div",ge,[n("div",ye,[c(l,{size:60,src:d(k).avatar||ca},null,8,["src"]),n("div",be,[n("h2",null,F(d(k).nickname||"分销员"),1),n("p",null,"分销员ID: "+F(ka.value),1),n("div",he,[c(t,{type:"success",effect:"light"},{default:u(()=>a[5]||(a[5]=[g("活跃分销员",-1)])),_:1,__:[5]}),c(t,{type:"primary",effect:"light"},{default:u(()=>[g(F(xa.value),1)]),_:1})])])]),n("div",we,[c(v,{type:"primary",onClick:Va,loading:ua.customer},{default:u(()=>[c(s,null,{default:u(()=>[c(d(E))]),_:1}),a[6]||(a[6]=g(" 客户管理 ",-1))]),_:1,__:[6]},8,["loading"]),c(v,{onClick:za,loading:ua.group},{default:u(()=>[c(s,null,{default:u(()=>[c(d(q))]),_:1}),a[7]||(a[7]=g(" 群组管理 ",-1))]),_:1,__:[7]},8,["loading"])])]),c(p,{gutter:20,class:"stats-row"},{default:u(()=>[(r(!0),o(y,null,b(ga.value,(e,a)=>(r(),h(m,{span:6,key:a},{default:u(()=>{return[n("div",{class:"stat-card",onClick:a=>(e=>{switch(console.log("点击统计卡片:",e),e){case"customers":Va();break;case"groups":za();break;case"commission":qa();break;case"conversion":Ua()}})(e.key)},[n("div",{class:"stat-icon",style:I({backgroundColor:e.color+"20",color:e.color})},[c(s,{size:24},{default:u(()=>[(r(),h(w(e.icon)))]),_:2},1024)],4),n("div",ke,[n("div",xe,F(e.prefix)+F((l=e.value,l>=1e4?(l/1e4).toFixed(1)+"w":l>=1e3?(l/1e3).toFixed(1)+"k":l.toString()))+F(e.suffix),1),n("div",De,F(e.title),1),e.trend?(r(),o("div",{key:0,class:D(["stat-trend",(a=e.trend,a>0?"trend-up":"trend-down")])},[c(s,null,{default:u(()=>[e.trend>0?(r(),h(d(P),{key:0})):(r(),h(d(S),{key:1}))]),_:2},1024),g(" "+F(Math.abs(e.trend))+"% ",1)],2)):i("",!0)])],8,Ce)];var a,l}),_:2},1024))),128))]),_:1}),c(f,{class:"quick-actions-card"},{header:u(()=>a[8]||(a[8]=[n("span",null,"快捷操作",-1)])),default:u(()=>[c(p,{gutter:15},{default:u(()=>[c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:Va},[c(s,{class:"action-icon"},{default:u(()=>[c(d(E))]),_:1}),a[9]||(a[9]=n("span",null,"客户管理",-1))])]),_:1}),c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:ja},[c(s,{class:"action-icon"},{default:u(()=>[c(d(B))]),_:1}),a[10]||(a[10]=n("span",null,"新增客户",-1))])]),_:1}),c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:za},[c(s,{class:"action-icon"},{default:u(()=>[c(d(q))]),_:1}),a[11]||(a[11]=n("span",null,"群组管理",-1))])]),_:1}),c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:Ea},[c(s,{class:"action-icon"},{default:u(()=>[c(d($))]),_:1}),a[12]||(a[12]=n("span",null,"推广链接",-1))])]),_:1}),c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:qa},[c(s,{class:"action-icon"},{default:u(()=>[c(d(R))]),_:1}),a[13]||(a[13]=n("span",null,"佣金查看",-1))])]),_:1}),c(m,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:Ta},[c(s,{class:"action-icon"},{default:u(()=>[c(d(L))]),_:1}),a[14]||(a[14]=n("span",null,"订单查看",-1))])]),_:1})]),_:1})]),_:1}),c(p,{gutter:20,class:"charts-row"},{default:u(()=>[c(m,{span:16},{default:u(()=>[c(f,null,{header:u(()=>[n("div",Fe,[a[18]||(a[18]=n("span",null,"收入趋势",-1)),c(va,{modelValue:ne.value,"onUpdate:modelValue":a[0]||(a[0]=e=>ne.value=e),size:"small",onChange:Fa},{default:u(()=>[c(da,{value:"7d"},{default:u(()=>a[15]||(a[15]=[g("近7天",-1)])),_:1,__:[15]}),c(da,{value:"30d"},{default:u(()=>a[16]||(a[16]=[g("近30天",-1)])),_:1,__:[16]}),c(da,{value:"90d"},{default:u(()=>a[17]||(a[17]=[g("近3个月",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])])]),default:u(()=>[c(oe,{data:ba.value,options:wa,height:"300px"},null,8,["data"])]),_:1})]),_:1}),c(m,{span:8},{default:u(()=>[c(f,null,{header:u(()=>a[19]||(a[19]=[n("span",null,"客户分布",-1)])),default:u(()=>[c(ie,{data:ha.value,options:Ca,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),c(p,{gutter:20,class:"info-row"},{default:u(()=>[c(m,{span:12},{default:u(()=>[c(f,null,{header:u(()=>[n("div",Ae,[a[21]||(a[21]=n("span",null,"客户动态",-1)),c(v,{size:"small",onClick:Aa},{default:u(()=>[c(s,null,{default:u(()=>[c(d(N))]),_:1}),a[20]||(a[20]=g(" 刷新 ",-1))]),_:1,__:[20]})])]),default:u(()=>[n("div",Me,[(r(!0),o(y,null,b(ma.value,e=>{return r(),o("div",{key:e.id,class:"activity-item"},[n("div",Ve,[c(l,{size:32,src:e.customer?.avatar},null,8,["src"])]),n("div",je,[n("div",ze,F(e.title),1),n("div",Ee,F(e.description),1),n("div",qe,F((a=e.created_at,new Date(a).toLocaleString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))),1)]),e.value?(r(),o("div",Te,[n("span",Ie,"¥"+F(e.value),1)])):i("",!0)]);var a}),128)),0===ma.value.length?(r(),o("div",Pe,[c(_a,{description:"暂无客户动态"})])):i("",!0)])]),_:1})]),_:1}),c(m,{span:12},{default:u(()=>[c(f,null,{header:u(()=>[n("div",Se,[a[23]||(a[23]=n("span",null,"待跟进客户",-1)),c(ya,{value:pa.value.length,class:"follow-up-badge"},{default:u(()=>[c(v,{size:"small",onClick:Ia},{default:u(()=>a[22]||(a[22]=[g("查看全部",-1)])),_:1,__:[22]})]),_:1},8,["value"])])]),default:u(()=>[n("div",Ue,[(r(!0),o(y,null,b(pa.value,e=>{return r(),o("div",{key:e.id,class:"follow-up-item"},[n("div",Be,[n("div",$e,F(e.name),1),n("div",Re,[c(t,{type:(l=e.level,{A:"danger",B:"warning",C:"primary",D:"info"}[l]||"info"),size:"small"},{default:u(()=>[g(F(e.level_text),1)]),_:2},1032,["type"])])]),n("div",Le,[a[24]||(a[24]=n("span",{class:"time-label"},"下次跟进:",-1)),n("span",{class:D(["time-value",La(e.next_follow_up)])},F(Oa(e.next_follow_up)),3)]),n("div",Oe,[c(v,{size:"small",type:"primary",onClick:a=>(async e=>{e.following=!0;try{await new Promise(e=>setTimeout(e,800)),A.success({title:"跟进成功",message:`已为客户 ${e.name} 创建跟进任务`}),e.next_follow_up=new Date(Date.now()+6048e5)}catch(a){M.error("创建跟进任务失败")}finally{e.following=!1}})(e)},{default:u(()=>a[25]||(a[25]=[g(" 立即跟进 ",-1)])),_:2,__:[25]},1032,["onClick"])])]);var l}),128)),0===pa.value.length?(r(),o("div",He,[c(_a,{description:"暂无待跟进客户"})])):i("",!0)])]),_:1})]),_:1})]),_:1}),c(f,{class:"promotion-card"},{header:u(()=>a[26]||(a[26]=[n("span",null,"我的推广链接",-1)])),default:u(()=>[c(p,{gutter:20},{default:u(()=>[c(m,{span:16},{default:u(()=>[c(Da,{modelValue:fa.value,"onUpdate:modelValue":a[1]||(a[1]=e=>fa.value=e),placeholder:"您的专属推广链接",readonly:""},{prepend:u(()=>a[27]||(a[27]=[g("推广链接",-1)])),append:u(()=>[c(v,{onClick:Pa},{default:u(()=>[c(s,null,{default:u(()=>[c(d(Q))]),_:1}),a[28]||(a[28]=g(" 复制 ",-1))]),_:1,__:[28]})]),_:1},8,["modelValue"])]),_:1}),c(m,{span:8},{default:u(()=>[c(v,{type:"primary",onClick:Sa},{default:u(()=>[c(s,null,{default:u(()=>[c(d(G))]),_:1}),a[29]||(a[29]=g(" 生成二维码 ",-1))]),_:1,__:[29]}),c(v,{onClick:Ea},{default:u(()=>[c(s,null,{default:u(()=>[c(d(G))]),_:1}),a[30]||(a[30]=g(" 更多工具 ",-1))]),_:1,__:[30]})]),_:1})]),_:1})]),_:1}),c(Wa,{modelValue:ce.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ce.value=e),title:"转化率详情分析",width:"800px",class:"conversion-dialog"},{default:u(()=>[n("div",We,[n("div",Ze,[a[31]||(a[31]=n("h3",null,"转化率趋势分析",-1)),c(Ma,{modelValue:ve.value,"onUpdate:modelValue":a[2]||(a[2]=e=>ve.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small"},null,8,["modelValue"])]),n("div",Ne,[n("div",Ye,[n("div",Je,F(me.totalVisits),1),a[32]||(a[32]=n("div",{class:"stat-label"},"总访问量",-1))]),n("div",Qe,[n("div",Ge,F(me.totalConversions),1),a[33]||(a[33]=n("div",{class:"stat-label"},"总转化数",-1))]),n("div",Ke,[n("div",Xe,F(me.conversionRate)+"%",1),a[34]||(a[34]=n("div",{class:"stat-label"},"转化率",-1))]),n("div",ea,[n("div",aa,"¥"+F(me.avgOrderValue),1),a[35]||(a[35]=n("div",{class:"stat-label"},"平均订单价值",-1))])]),a[37]||(a[37]=n("div",{class:"conversion-chart"},[n("h4",null,"转化率趋势图"),n("div",{class:"chart-placeholder"},[n("p",null,"转化率趋势图表将在这里显示")])],-1)),n("div",la,[a[36]||(a[36]=n("h4",null,"转化来源分析",-1)),c(Ha,{data:pe.value,size:"small"},{default:u(()=>[c(Ba,{prop:"source",label:"来源"}),c(Ba,{prop:"visits",label:"访问量"}),c(Ba,{prop:"conversions",label:"转化数"}),c(Ba,{prop:"rate",label:"转化率"},{default:u(({row:e})=>[n("span",null,F(e.rate)+"%",1)]),_:1})]),_:1},8,["data"])])])]),_:1},8,["modelValue"]),c(Wa,{modelValue:ue.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ue.value=e),title:"推广二维码",width:"400px",class:"qr-dialog"},{default:u(()=>[de.value?(r(),o("div",ta,[n("div",sa,[n("h4",null,F(de.value.name)+" 的推广二维码",1),n("p",null,"推广码: "+F(de.value.code),1)]),n("div",oa,[n("div",ia,[c(s,{size:"80"},{default:u(()=>[c(d(le))]),_:1}),a[38]||(a[38]=n("p",null,"二维码图片",-1))])]),n("div",ra,[c(v,{type:"primary",onClick:$a},{default:u(()=>[c(s,null,{default:u(()=>[c(d(te))]),_:1}),a[39]||(a[39]=g(" 下载二维码 ",-1))]),_:1,__:[39]}),c(v,{onClick:Ra},{default:u(()=>[c(s,null,{default:u(()=>[c(d(Q))]),_:1}),a[40]||(a[40]=g(" 复制链接 ",-1))]),_:1,__:[40]})]),n("div",na,[n("p",null,[c(s,null,{default:u(()=>[c(d(se))]),_:1}),a[41]||(a[41]=g(" 扫描此二维码或分享链接给客户，客户通过此链接注册将自动绑定为您的下级",-1))])])])):i("",!0)]),_:1},8,["modelValue"])])),[[Za,re.value]])}}},[["__scopeId","data-v-ee07820f"]]);export{ua as default};
