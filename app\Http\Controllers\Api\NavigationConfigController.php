<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NavigationMenu;
use App\Models\UserNavigationPreference;
use App\Services\NavigationCacheService;
use App\Services\NavigationPermissionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

/**
 * 导航配置管理API控制器
 * 
 * 提供四域导航架构的完整配置管理功能
 */
class NavigationConfigController extends Controller
{
    protected NavigationCacheService $cacheService;
    protected NavigationPermissionService $permissionService;

    public function __construct(
        NavigationCacheService $cacheService,
        NavigationPermissionService $permissionService
    ) {
        $this->cacheService = $cacheService;
        $this->permissionService = $permissionService;
    }

    /**
     * 获取四域导航配置
     */
    public function getDomainConfig(Request $request): JsonResponse
    {
        $request->validate([
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'level' => 'nullable|integer|min:1|max:3',
            'include_meta' => 'boolean',
            'with_permissions' => 'boolean'
        ]);

        $user = Auth::user();
        $domain = $request->input('domain');
        $level = $request->integer('level', 3); // 默认3级菜单
        $includeMeta = $request->boolean('include_meta', true);
        $withPermissions = $request->boolean('with_permissions', true);

        try {
            $cacheKey = "navigation_domain_config:{$domain}:{$level}:{$user?->id}";
            
            $config = $this->cacheService->remember($cacheKey, 1800, function () use ($domain, $level, $user, $includeMeta, $withPermissions) {
                // 获取基础菜单树
                $query = NavigationMenu::with(['children' => function ($query) use ($level) {
                    $this->buildChildQuery($query, $level - 1);
                }])
                ->where('is_visible', true)
                ->where('parent_id', 0)
                ->orderBy('sort_order');

                if ($domain) {
                    $query->where('domain', $domain);
                }

                $menus = $query->get();

                // 应用权限过滤
                if ($user && $withPermissions) {
                    $menus = $this->permissionService->filterMenusByPermissions($menus, $user);
                }

                // 附加用户偏好数据
                if ($user) {
                    $menus = $this->attachUserPreferences($menus, $user->id);
                }

                return [
                    'menus' => $menus,
                    'domain_stats' => $this->getDomainStats($domain, $user?->id),
                    'user_context' => $user ? $this->getUserContext($user) : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $config,
                'meta' => [
                    'domain' => $domain ?? 'all',
                    'level' => $level,
                    'timestamp' => now()->timestamp,
                    'cache_key' => $cacheKey
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取导航配置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取导航菜单详情
     */
    public function getMenuDetail(Request $request, string $code): JsonResponse
    {
        try {
            $menu = NavigationMenu::with(['children', 'parent'])
                ->where('code', $code)
                ->where('is_visible', true)
                ->first();

            if (!$menu) {
                return response()->json([
                    'success' => false,
                    'message' => '菜单不存在'
                ], 404);
            }

            $user = Auth::user();

            // 检查权限
            if ($user && !$this->permissionService->hasMenuPermission($user, $menu)) {
                return response()->json([
                    'success' => false,
                    'message' => '没有访问权限'
                ], 403);
            }

            // 获取菜单统计信息
            $stats = $this->getMenuStats($code, $user?->id);

            return response()->json([
                'success' => true,
                'data' => [
                    'menu' => $menu,
                    'stats' => $stats,
                    'related_menus' => $this->getRelatedMenus($menu, 5),
                    'user_preference' => $user ? $this->getUserMenuPreference($user->id, $code) : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取菜单详情失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 创建导航菜单 (管理员权限)
     */
    public function createMenu(Request $request): JsonResponse
    {
        $this->authorize('create-navigation-menu');

        $request->validate([
            'code' => 'required|string|max:50|unique:navigation_menus,code',
            'name' => 'required|string|max:100',
            'domain' => 'required|string|in:business,operation,analytics,system',
            'icon' => 'nullable|string|max:100',
            'route' => 'nullable|string|max:200',
            'component' => 'nullable|string|max:200',
            'parent_id' => 'nullable|integer|exists:navigation_menus,id',
            'sort_order' => 'integer|min:0',
            'permission' => 'nullable|string|max:100',
            'roles' => 'nullable|array',
            'meta' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            $menu = NavigationMenu::create([
                'code' => $request->code,
                'name' => $request->name,
                'domain' => $request->domain,
                'icon' => $request->icon,
                'route' => $request->route,
                'component' => $request->component,
                'parent_id' => $request->parent_id ?? 0,
                'sort_order' => $request->integer('sort_order', 0),
                'is_visible' => true,
                'is_cacheable' => true,
                'permission' => $request->permission,
                'roles' => $request->roles,
                'meta' => $request->meta ?? []
            ]);

            // 清除相关缓存
            $this->cacheService->clearNavigationCache($request->domain);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '菜单创建成功',
                'data' => $menu
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '菜单创建失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 更新导航菜单 (管理员权限)
     */
    public function updateMenu(Request $request, string $code): JsonResponse
    {
        $this->authorize('update-navigation-menu');

        $menu = NavigationMenu::where('code', $code)->first();
        if (!$menu) {
            return response()->json(['success' => false, 'message' => '菜单不存在'], 404);
        }

        $request->validate([
            'name' => 'string|max:100',
            'icon' => 'nullable|string|max:100',
            'route' => 'nullable|string|max:200',
            'component' => 'nullable|string|max:200',
            'sort_order' => 'integer|min:0',
            'is_visible' => 'boolean',
            'permission' => 'nullable|string|max:100',
            'roles' => 'nullable|array',
            'meta' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            $menu->update($request->only([
                'name', 'icon', 'route', 'component', 'sort_order',
                'is_visible', 'permission', 'roles', 'meta'
            ]));

            // 清除相关缓存
            $this->cacheService->clearNavigationCache($menu->domain);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '菜单更新成功',
                'data' => $menu->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '菜单更新失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 删除导航菜单 (管理员权限)
     */
    public function deleteMenu(string $code): JsonResponse
    {
        $this->authorize('delete-navigation-menu');

        try {
            $menu = NavigationMenu::where('code', $code)->first();
            if (!$menu) {
                return response()->json(['success' => false, 'message' => '菜单不存在'], 404);
            }

            // 检查是否有子菜单
            if ($menu->children()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => '存在子菜单，无法删除'
                ], 400);
            }

            DB::beginTransaction();

            // 删除相关用户偏好记录
            UserNavigationPreference::where('menu_code', $code)->delete();

            $domain = $menu->domain;
            $menu->delete();

            // 清除相关缓存
            $this->cacheService->clearNavigationCache($domain);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '菜单删除成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '菜单删除失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 批量排序菜单
     */
    public function batchSort(Request $request): JsonResponse
    {
        $this->authorize('update-navigation-menu');

        $request->validate([
            'items' => 'required|array',
            'items.*.code' => 'required|string|exists:navigation_menus,code',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->items as $item) {
                NavigationMenu::where('code', $item['code'])
                    ->update(['sort_order' => $item['sort_order']]);
            }

            // 清除所有导航缓存
            $this->cacheService->clearAllNavigationCache();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '菜单排序更新成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '菜单排序失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    // ========== 私有辅助方法 ==========

    private function buildChildQuery($query, int $level): void
    {
        if ($level > 0) {
            $query->with(['children' => function ($query) use ($level) {
                $this->buildChildQuery($query, $level - 1);
            }]);
        }
        $query->where('is_visible', true)->orderBy('sort_order');
    }

    private function attachUserPreferences($menus, int $userId)
    {
        $preferences = UserNavigationPreference::where('user_id', $userId)
            ->get()
            ->keyBy('menu_code');

        return $menus->map(function ($menu) use ($preferences) {
            $pref = $preferences->get($menu->code);
            
            $menu->user_preference = $pref ? [
                'is_pinned' => in_array($menu->code, $pref->pinned_menus ?? []),
                'is_hidden' => in_array($menu->code, $pref->hidden_menus ?? []),
                'custom_order' => $pref->menu_order[$menu->code] ?? null,
                'is_quick_access' => in_array($menu->code, $pref->quick_access ?? []),
            ] : [
                'is_pinned' => false,
                'is_hidden' => false,
                'custom_order' => null,
                'is_quick_access' => false,
            ];

            // 递归处理子菜单
            if ($menu->children) {
                $menu->children = $this->attachUserPreferences($menu->children, $userId);
            }

            return $menu;
        });
    }

    private function getDomainStats(?string $domain, ?int $userId): array
    {
        $cacheKey = "domain_stats:{$domain}:{$userId}";

        return Cache::remember($cacheKey, 1800, function () use ($domain, $userId) {
            $query = NavigationMenu::query();
            
            if ($domain) {
                $query->where('domain', $domain);
            }

            return [
                'total_menus' => $query->count(),
                'visible_menus' => $query->where('is_visible', true)->count(),
                'user_accessible' => $userId ? $this->getUserAccessibleMenuCount($userId, $domain) : 0,
                'most_popular' => $this->getMostPopularMenus($domain, 5)
            ];
        });
    }

    private function getUserContext($user): array
    {
        return [
            'id' => $user->id,
            'role' => $user->role,
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'preferences_exist' => UserNavigationPreference::where('user_id', $user->id)->exists()
        ];
    }

    private function getMenuStats(string $code, ?int $userId): array
    {
        // 实现菜单统计逻辑
        return [
            'total_visits' => 0,
            'unique_visitors' => 0,
            'avg_duration' => 0,
            'last_updated' => NavigationMenu::where('code', $code)->value('updated_at')
        ];
    }

    private function getRelatedMenus($menu, int $limit): array
    {
        // 实现相关菜单推荐逻辑
        return NavigationMenu::where('domain', $menu->domain)
            ->where('code', '!=', $menu->code)
            ->where('is_visible', true)
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function getUserMenuPreference(int $userId, string $code): ?array
    {
        $preference = UserNavigationPreference::where('user_id', $userId)->first();
        
        if (!$preference) {
            return null;
        }

        return [
            'is_pinned' => in_array($code, $preference->pinned_menus ?? []),
            'is_hidden' => in_array($code, $preference->hidden_menus ?? []),
            'is_quick_access' => in_array($code, $preference->quick_access ?? []),
            'custom_order' => $preference->menu_order[$code] ?? null
        ];
    }

    private function getUserAccessibleMenuCount(int $userId, ?string $domain): int
    {
        // 实现用户可访问菜单计数逻辑
        return 0;
    }

    private function getMostPopularMenus(?string $domain, int $limit): array
    {
        // 实现热门菜单获取逻辑
        return [];
    }
}