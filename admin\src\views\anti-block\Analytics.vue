<template>
  <div class="analytics-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h1>📊 统计分析</h1>
        <p class="page-desc">防红系统的访问统计、域名分析和性能监控</p>
      </div>
      
      <div class="page-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="loadAnalytics">
        </el-date-picker>
        <el-button type="primary" @click="exportReport">
          <i class="el-icon-download"></i> 导出报告
        </el-button>
        <el-button type="success" @click="refreshData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="metrics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon total-clicks">
              <i class="el-icon-view"></i>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ formatNumber(analytics.total_clicks) }}</div>
              <div class="metric-label">总访问量</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analytics.click_trend)">
                  <i :class="getTrendIcon(analytics.click_trend)"></i>
                  {{ Math.abs(analytics.click_trend) }}%
                </span>
                <span class="trend-desc">较昨日</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon unique-visitors">
              <i class="el-icon-user"></i>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ formatNumber(analytics.unique_visitors) }}</div>
              <div class="metric-label">独立访客</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analytics.visitor_trend)">
                  <i :class="getTrendIcon(analytics.visitor_trend)"></i>
                  {{ Math.abs(analytics.visitor_trend) }}%
                </span>
                <span class="trend-desc">较昨日</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon success-rate">
              <i class="el-icon-success"></i>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ analytics.success_rate }}%</div>
              <div class="metric-label">访问成功率</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analytics.success_trend)">
                  <i :class="getTrendIcon(analytics.success_trend)"></i>
                  {{ Math.abs(analytics.success_trend) }}%
                </span>
                <span class="trend-desc">较昨日</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon response-time">
              <i class="el-icon-time"></i>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ analytics.avg_response_time }}ms</div>
              <div class="metric-label">平均响应时间</div>
              <div class="metric-trend">
                <span :class="getTrendClass(-analytics.response_trend)">
                  <i :class="getTrendIcon(-analytics.response_trend)"></i>
                  {{ Math.abs(analytics.response_trend) }}ms
                </span>
                <span class="trend-desc">较昨日</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 访问趋势图 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>📈 访问趋势</span>
              <el-radio-group v-model="trendPeriod" size="small" @change="loadTrendData">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
            <div class="chart-container">
              <div class="chart-placeholder" v-if="!trendData.length">
                <i class="el-icon-loading"></i>
                <p>加载中...</p>
              </div>
              <div v-else class="trend-chart">
                <!-- 这里应该集成图表组件，比如 ECharts -->
                <div class="chart-demo">
                  <h4>访问趋势图</h4>
                  <p>显示过去{{ trendPeriod }}的访问统计</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 域名使用分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>🌐 域名使用分布</span>
              <el-button type="text" @click="showDomainDetail">查看详情</el-button>
            </div>
            <div class="chart-container">
              <div class="domain-stats">
                <div class="domain-item" v-for="domain in domainStats" :key="domain.id">
                  <div class="domain-info">
                    <span class="domain-name">{{ domain.domain }}</span>
                    <span class="domain-type">{{ getDomainTypeName(domain.type) }}</span>
                  </div>
                  <div class="domain-metrics">
                    <div class="metric-item">
                      <span class="metric-label">使用次数</span>
                      <span class="metric-value">{{ domain.usage_count }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">成功率</span>
                      <span class="metric-value">{{ domain.success_rate }}%</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">健康度</span>
                      <el-progress 
                        :percentage="domain.health_score" 
                        :color="getHealthColor(domain.health_score)"
                        :stroke-width="6"
                        :show-text="false">
                      </el-progress>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计 -->
    <div class="details-section">
      <el-row :gutter="20">
        <!-- 来源统计 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header" class="card-header">
              <span>📱 访问来源</span>
            </div>
            <div class="stats-content">
              <div class="stat-item" v-for="source in sourceStats" :key="source.name">
                <div class="stat-info">
                  <div class="stat-name">{{ source.name }}</div>
                  <div class="stat-percent">{{ source.percentage }}%</div>
                </div>
                <div class="stat-bar">
                  <div class="stat-fill" :style="{ width: source.percentage + '%' }"></div>
                </div>
                <div class="stat-count">{{ source.count }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 地区分布 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header" class="card-header">
              <span>🌍 地区分布</span>
            </div>
            <div class="stats-content">
              <div class="stat-item" v-for="region in regionStats" :key="region.name">
                <div class="stat-info">
                  <div class="stat-name">{{ region.name }}</div>
                  <div class="stat-percent">{{ region.percentage }}%</div>
                </div>
                <div class="stat-bar">
                  <div class="stat-fill" :style="{ width: region.percentage + '%' }"></div>
                </div>
                <div class="stat-count">{{ region.count }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 时间分布 -->
        <el-col :span="8">
          <el-card class="stats-card">
            <div slot="header" class="card-header">
              <span>🕐 时间分布</span>
            </div>
            <div class="stats-content">
              <div class="time-stats">
                <div class="time-item" v-for="hour in hourlyStats" :key="hour.hour">
                  <div class="time-label">{{ hour.hour }}:00</div>
                  <div class="time-bar">
                    <div class="time-fill" :style="{ height: (hour.count / maxHourlyCount * 100) + '%' }"></div>
                  </div>
                  <div class="time-count">{{ hour.count }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 异常监控 -->
    <div class="alerts-section">
      <el-card class="alerts-card">
        <div slot="header" class="card-header">
          <span>⚠️ 异常监控</span>
          <el-button type="text" @click="showAlertSettings">告警设置</el-button>
        </div>
        
        <div class="alerts-content">
          <div class="alert-summary">
            <div class="summary-item">
              <div class="summary-icon warning">
                <i class="el-icon-warning"></i>
              </div>
              <div class="summary-content">
                <div class="summary-number">{{ alerts.warning_count }}</div>
                <div class="summary-label">警告</div>
              </div>
            </div>
            
            <div class="summary-item">
              <div class="summary-icon error">
                <i class="el-icon-error"></i>
              </div>
              <div class="summary-content">
                <div class="summary-number">{{ alerts.error_count }}</div>
                <div class="summary-label">错误</div>
              </div>
            </div>
            
            <div class="summary-item">
              <div class="summary-icon blocked">
                <i class="el-icon-remove-outline"></i>
              </div>
              <div class="summary-content">
                <div class="summary-number">{{ alerts.blocked_count }}</div>
                <div class="summary-label">封禁</div>
              </div>
            </div>
          </div>
          
          <div class="alert-list">
            <div class="alert-item" v-for="alert in recentAlerts" :key="alert.id">
              <div class="alert-icon">
                <i :class="getAlertIcon(alert.type)"></i>
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-desc">{{ alert.description }}</div>
                <div class="alert-time">{{ formatTime(alert.created_at) }}</div>
              </div>
              <div class="alert-actions">
                <el-button type="text" size="small" @click="handleAlert(alert)">
                  处理
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 域名详情对话框 -->
    <el-dialog title="域名详细统计" :visible.sync="domainDetailVisible" width="800px">
      <div class="domain-detail-content">
        <el-table :data="domainDetailStats" v-loading="domainDetailLoading">
          <el-table-column prop="domain" label="域名" width="180">
            <template slot-scope="scope">
              <span class="domain-text">{{ scope.row.domain }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="100">
            <template slot-scope="scope">
              <el-tag size="small" :type="getDomainTypeColor(scope.row.type)">
                {{ getDomainTypeName(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="usage_count" label="使用次数" width="100"></el-table-column>
          <el-table-column prop="success_count" label="成功次数" width="100"></el-table-column>
          <el-table-column prop="success_rate" label="成功率" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.success_rate }}%</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="health_score" label="健康度" width="120">
            <template slot-scope="scope">
              <el-progress 
                :percentage="scope.row.health_score" 
                :color="getHealthColor(scope.row.health_score)"
                :stroke-width="8">
              </el-progress>
            </template>
          </el-table-column>
          
          <el-table-column prop="avg_response_time" label="平均响应时间" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.avg_response_time }}ms</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 告警设置对话框 -->
    <el-dialog title="告警设置" :visible.sync="alertSettingsVisible" width="600px">
      <div class="alert-settings-content">
        <el-form :model="alertSettings" label-width="150px">
          <el-divider content-position="left">域名检测设置</el-divider>
          
          <el-form-item label="启用域名检测">
            <el-switch v-model="alertSettings.domain_check_enabled"></el-switch>
          </el-form-item>
          
          <el-form-item label="检测间隔（分钟）">
            <el-input-number 
              v-model="alertSettings.domain_check_interval" 
              :min="1" 
              :max="60"
              :disabled="!alertSettings.domain_check_enabled">
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="封禁阈值">
            <el-input-number 
              v-model="alertSettings.block_threshold" 
              :min="1" 
              :max="10"
              :disabled="!alertSettings.domain_check_enabled">
            </el-input-number>
            <span class="form-help">连续失败次数超过此值时触发告警</span>
          </el-form-item>
          
          <el-divider content-position="left">性能监控设置</el-divider>
          
          <el-form-item label="响应时间阈值（毫秒）">
            <el-input-number 
              v-model="alertSettings.response_time_threshold" 
              :min="1000" 
              :max="10000"
              :step="100">
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="成功率阈值（%）">
            <el-input-number 
              v-model="alertSettings.success_rate_threshold" 
              :min="50" 
              :max="100"
              :step="1">
            </el-input-number>
          </el-form-item>
          
          <el-divider content-position="left">通知设置</el-divider>
          
          <el-form-item label="启用通知">
            <el-switch v-model="alertSettings.notification_enabled"></el-switch>
          </el-form-item>
          
          <el-form-item label="邮件通知">
            <el-input 
              v-model="alertSettings.notification_email" 
              placeholder="请输入邮箱地址"
              :disabled="!alertSettings.notification_enabled">
            </el-input>
          </el-form-item>
          
          <el-form-item label="Webhook通知">
            <el-input 
              v-model="alertSettings.notification_webhook" 
              placeholder="请输入Webhook地址"
              :disabled="!alertSettings.notification_enabled">
            </el-input>
          </el-form-item>
          
          <el-divider content-position="left">自动切换设置</el-divider>
          
          <el-form-item label="启用自动切换">
            <el-switch v-model="alertSettings.auto_switch_enabled"></el-switch>
          </el-form-item>
          
          <el-form-item label="自动切换阈值">
            <el-input-number 
              v-model="alertSettings.auto_switch_threshold" 
              :min="1" 
              :max="10"
              :disabled="!alertSettings.auto_switch_enabled">
            </el-input-number>
            <span class="form-help">域名异常次数超过此值时自动切换</span>
          </el-form-item>
        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="alertSettingsVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAlertSettings">保存设置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getAccessStats, 
  getClickTrends, 
  getRegionStats, 
  getPlatformStats,
  getDomainList 
} from '@/api/anti-block'

export default {
  name: 'AntiBlockAnalytics',
  
  data() {
    return {
      loading: false,
      domainDetailVisible: false,
      domainDetailLoading: false,
      
      // 日期范围
      dateRange: [],
      
      // 趋势周期
      trendPeriod: '7d',
      
      // 核心指标
      analytics: {
        total_clicks: 0,
        unique_visitors: 0,
        success_rate: 0,
        avg_response_time: 0,
        click_trend: 0,
        visitor_trend: 0,
        success_trend: 0,
        response_trend: 0
      },
      
      // 图表数据
      trendData: [],
      
      // 域名统计
      domainStats: [],
      domainDetailStats: [],
      
      // 来源统计
      sourceStats: [
        { name: '微信', count: 1250, percentage: 45 },
        { name: 'QQ', count: 890, percentage: 32 },
        { name: '直接访问', count: 456, percentage: 16 },
        { name: '其他', count: 189, percentage: 7 }
      ],
      
      // 地区分布
      regionStats: [
        { name: '广东', count: 890, percentage: 32 },
        { name: '浙江', count: 678, percentage: 24 },
        { name: '江苏', count: 456, percentage: 16 },
        { name: '上海', count: 234, percentage: 8 },
        { name: '其他', count: 527, percentage: 20 }
      ],
      
      // 时间分布
      hourlyStats: [],
      
      // 异常监控
      alerts: {
        warning_count: 3,
        error_count: 1,
        blocked_count: 2
      },
      
      recentAlerts: [
        {
          id: 1,
          type: 'warning',
          title: '域名响应时间过长',
          description: 'short1.example.com 响应时间超过5秒',
          created_at: new Date()
        },
        {
          id: 2,
          type: 'error',
          title: '域名访问失败',
          description: 'short2.example.com 返回404错误',
          created_at: new Date()
        },
        {
          id: 3,
          type: 'blocked',
          title: '域名被封禁',
          description: 'short3.example.com 被微信封禁',
          created_at: new Date()
        }
      ],
      
      // 告警设置相关
      alertSettingsVisible: false,
      alertSettings: {
        domain_check_enabled: true,
        domain_check_interval: 5,
        block_threshold: 3,
        response_time_threshold: 5000,
        success_rate_threshold: 90,
        notification_enabled: true,
        notification_email: '<EMAIL>',
        notification_webhook: '',
        auto_switch_enabled: true,
        auto_switch_threshold: 3
      }
    }
  },
  
  computed: {
    maxHourlyCount() {
      return Math.max(...this.hourlyStats.map(h => h.count))
    }
  },
  
  mounted() {
    this.initDateRange()
    this.loadAnalytics()
    this.initHourlyStats()
  },
  
  methods: {
    initDateRange() {
      const today = new Date()
      const lastWeek = new Date()
      lastWeek.setDate(today.getDate() - 7)
      this.dateRange = [
        lastWeek.toISOString().split('T')[0],
        today.toISOString().split('T')[0]
      ]
    },
    
    initHourlyStats() {
      this.hourlyStats = []
      for (let i = 0; i < 24; i++) {
        this.hourlyStats.push({
          hour: i,
          count: Math.floor(Math.random() * 100) + 10
        })
      }
    },
    
    async loadAnalytics() {
      this.loading = true
      try {
        const params = {
          start_date: this.dateRange[0],
          end_date: this.dateRange[1]
        }
        
        // 加载统计数据
        const { data } = await getAccessStats(params)
        this.analytics = data
        
        // 加载域名统计
        await this.loadDomainStats()
        
        // 加载趋势数据
        await this.loadTrendData()
        
      } catch (error) {
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadDomainStats() {
      try {
        const { data } = await getDomainList({ per_page: 10 })
        this.domainStats = data.data.map(domain => ({
          ...domain,
          usage_count: Math.floor(Math.random() * 1000) + 100,
          success_rate: Math.floor(Math.random() * 20) + 80
        }))
      } catch (error) {
        console.error('加载域名统计失败')
      }
    },
    
    async loadTrendData() {
      try {
        const params = {
          period: this.trendPeriod,
          start_date: this.dateRange[0],
          end_date: this.dateRange[1]
        }
        
        const { data } = await getClickTrends(params)
        this.trendData = data.data || []
      } catch (error) {
        console.error('加载趋势数据失败')
      }
    },
    
    showDomainDetail() {
      this.domainDetailVisible = true
      this.loadDomainDetailStats()
    },
    
    async loadDomainDetailStats() {
      this.domainDetailLoading = true
      try {
        const { data } = await getDomainList({ per_page: 50 })
        this.domainDetailStats = data.data.map(domain => ({
          ...domain,
          usage_count: Math.floor(Math.random() * 1000) + 100,
          success_count: Math.floor(Math.random() * 800) + 80,
          success_rate: Math.floor(Math.random() * 20) + 80,
          avg_response_time: Math.floor(Math.random() * 2000) + 200
        }))
      } catch (error) {
        this.$message.error('加载域名详情失败')
      } finally {
        this.domainDetailLoading = false
      }
    },
    
    refreshData() {
      this.loadAnalytics()
    },
    
    exportReport() {
      // 导出报告逻辑
      this.$message.success('报告导出中...')
    },
    
    showAlertSettings() {
      this.alertSettingsVisible = true
      this.loadAlertSettings()
    },
    
    loadAlertSettings() {
      // 加载当前告警设置
      this.alertSettings = {
        domain_check_enabled: true,
        domain_check_interval: 5,
        block_threshold: 3,
        response_time_threshold: 5000,
        success_rate_threshold: 90,
        notification_enabled: true,
        notification_email: '<EMAIL>',
        notification_webhook: '',
        auto_switch_enabled: true,
        auto_switch_threshold: 3
      }
    },
    
    saveAlertSettings() {
      // 保存告警设置到后端
      this.$message.loading('正在保存设置...')
      
      // 模拟API调用
      setTimeout(() => {
        this.$message.success('告警设置保存成功')
        this.alertSettingsVisible = false
      }, 1000)
    },
    
    handleAlert(alert) {
      this.$message.success('告警处理成功')
    },
    
    // 辅助方法
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'W'
      }
      return num.toString()
    },
    
    getTrendClass(trend) {
      if (trend > 0) return 'trend-up'
      if (trend < 0) return 'trend-down'
      return 'trend-stable'
    },
    
    getTrendIcon(trend) {
      if (trend > 0) return 'el-icon-top'
      if (trend < 0) return 'el-icon-bottom'
      return 'el-icon-minus'
    },
    
    getDomainTypeName(type) {
      const types = {
        'redirect': '短链接',
        'landing': '中转页',
        'api': 'API服务'
      }
      return types[type] || type
    },
    
    getDomainTypeColor(type) {
      const colors = {
        'redirect': 'primary',
        'landing': 'success',
        'api': 'warning'
      }
      return colors[type] || ''
    },
    
    getHealthColor(score) {
      if (score >= 90) return '#67c23a'
      if (score >= 80) return '#409eff'
      if (score >= 60) return '#e6a23c'
      return '#f56c6c'
    },
    
    getAlertIcon(type) {
      const icons = {
        'warning': 'el-icon-warning',
        'error': 'el-icon-error',
        'blocked': 'el-icon-remove-outline'
      }
      return icons[type] || 'el-icon-info'
    },
    
    formatTime(time) {
      if (!time) return '-'
      return this.$dayjs(time).format('MM-DD HH:mm')
    }
  }
}
</script>

<style lang="scss" scoped>
.analytics-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .page-title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }
    
    .page-desc {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .page-actions {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

.metrics-overview {
  margin-bottom: 30px;
  
  .metric-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    
    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 24px;
        color: white;
      }
      
      &.total-clicks {
        background: linear-gradient(45deg, #409eff, #67c23a);
      }
      
      &.unique-visitors {
        background: linear-gradient(45deg, #67c23a, #e6a23c);
      }
      
      &.success-rate {
        background: linear-gradient(45deg, #e6a23c, #f56c6c);
      }
      
      &.response-time {
        background: linear-gradient(45deg, #f56c6c, #409eff);
      }
    }
    
    .metric-content {
      flex: 1;
      
      .metric-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .metric-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .metric-trend {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 12px;
        
        .trend-up { color: #67c23a; }
        .trend-down { color: #f56c6c; }
        .trend-stable { color: #909399; }
        
        .trend-desc {
          color: #909399;
        }
      }
    }
  }
}

.charts-section {
  margin-bottom: 30px;
  
  .chart-card {
    height: 400px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .chart-container {
      height: 320px;
      
      .chart-placeholder {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        
        i {
          font-size: 24px;
          margin-bottom: 10px;
        }
      }
      
      .chart-demo {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        
        h4 {
          margin: 0 0 10px 0;
        }
        
        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
  
  .domain-stats {
    .domain-item {
      display: flex;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #ebeef5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .domain-info {
        flex: 1;
        
        .domain-name {
          display: block;
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 14px;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .domain-type {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .domain-metrics {
        display: flex;
        gap: 20px;
        align-items: center;
        
        .metric-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          min-width: 80px;
          
          .metric-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }
          
          .metric-value {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }
        }
      }
    }
  }
}

.details-section {
  margin-bottom: 30px;
  
  .stats-card {
    height: 350px;
    
    .stats-content {
      height: 280px;
      overflow-y: auto;
      
      .stat-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        
        .stat-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-right: 10px;
          
          .stat-name {
            font-size: 14px;
            color: #303133;
          }
          
          .stat-percent {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .stat-bar {
          width: 100px;
          height: 4px;
          background: #f0f0f0;
          border-radius: 2px;
          margin-right: 10px;
          
          .stat-fill {
            height: 100%;
            background: #409eff;
            border-radius: 2px;
            transition: width 0.3s ease;
          }
        }
        
        .stat-count {
          font-size: 12px;
          color: #606266;
          min-width: 40px;
          text-align: right;
        }
      }
    }
  }
  
  .time-stats {
    display: flex;
    align-items: end;
    gap: 5px;
    height: 200px;
    padding: 20px 0;
    
    .time-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
      
      .time-label {
        font-size: 10px;
        color: #909399;
        margin-bottom: 5px;
      }
      
      .time-bar {
        flex: 1;
        width: 20px;
        background: #f0f0f0;
        border-radius: 2px;
        position: relative;
        margin-bottom: 5px;
        
        .time-fill {
          position: absolute;
          bottom: 0;
          width: 100%;
          background: #409eff;
          border-radius: 2px;
          transition: height 0.3s ease;
        }
      }
      
      .time-count {
        font-size: 10px;
        color: #606266;
      }
    }
  }
}

.alerts-section {
  .alerts-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .alerts-content {
      .alert-summary {
        display: flex;
        gap: 30px;
        margin-bottom: 20px;
        
        .summary-item {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .summary-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            
            i {
              font-size: 18px;
              color: white;
            }
            
            &.warning {
              background: #e6a23c;
            }
            
            &.error {
              background: #f56c6c;
            }
            
            &.blocked {
              background: #909399;
            }
          }
          
          .summary-content {
            .summary-number {
              font-size: 20px;
              font-weight: bold;
              color: #303133;
            }
            
            .summary-label {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
      
      .alert-list {
        .alert-item {
          display: flex;
          align-items: center;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 4px;
          margin-bottom: 10px;
          
          .alert-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            background: #e6a23c;
            
            i {
              font-size: 14px;
              color: white;
            }
          }
          
          .alert-content {
            flex: 1;
            
            .alert-title {
              font-size: 14px;
              color: #303133;
              margin-bottom: 4px;
            }
            
            .alert-desc {
              font-size: 12px;
              color: #606266;
              margin-bottom: 4px;
            }
            
            .alert-time {
              font-size: 12px;
              color: #909399;
            }
          }
          
          .alert-actions {
            margin-left: 15px;
          }
        }
      }
    }
  }
}

.domain-text {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
}

.alert-settings-content {
  .form-help {
    color: #909399;
    font-size: 12px;
    margin-left: 8px;
  }
}
</style> 