<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DomainPool;
use App\Models\User;

/**
 * 域名池种子数据
 */
class DomainPoolSeeder extends Seeder
{
    /**
     * 运行种子数据
     */
    public function run(): void
    {
        // 获取第一个管理员用户作为创建者
        $adminUser = User::where('role', 'admin')->first();
        
        if (!$adminUser) {
            // 如果没有管理员用户，创建一个
            $adminUser = User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'status' => 'active'
            ]);
        }

        $domainPools = [
            [
                'name' => '默认域名池',
                'description' => '系统默认的域名池，包含主要的备用域名',
                'status' => 'active',
                'domains' => [
                    'backup1.example.com',
                    'backup2.example.com',
                    'backup3.example.com',
                    'mirror1.example.com',
                    'mirror2.example.com'
                ],
                'check_interval' => 10,
                'check_method' => 'http',
                'timeout' => 10,
                'retry_times' => 3,
                'switch_strategy' => 'immediate',
                'switch_delay' => 0,
                'total_domains' => 5,
                'active_domains' => 5,
                'blocked_domains' => 0,
                'creator_id' => $adminUser->id
            ],
            [
                'name' => '高速域名池',
                'description' => '高速访问的域名池，适用于重要群组',
                'status' => 'active',
                'domains' => [
                    'fast1.example.com',
                    'fast2.example.com',
                    'fast3.example.com',
                    'cdn1.example.com',
                    'cdn2.example.com',
                    'cdn3.example.com'
                ],
                'check_interval' => 5,
                'check_method' => 'http',
                'timeout' => 5,
                'retry_times' => 2,
                'switch_strategy' => 'immediate',
                'switch_delay' => 0,
                'total_domains' => 6,
                'active_domains' => 6,
                'blocked_domains' => 0,
                'creator_id' => $adminUser->id
            ],
            [
                'name' => '备用域名池',
                'description' => '备用域名池，用于应急切换',
                'status' => 'active',
                'domains' => [
                    'emergency1.example.com',
                    'emergency2.example.com',
                    'emergency3.example.com',
                    'standby1.example.com',
                    'standby2.example.com'
                ],
                'check_interval' => 15,
                'check_method' => 'ping',
                'timeout' => 15,
                'retry_times' => 3,
                'switch_strategy' => 'delayed',
                'switch_delay' => 5,
                'total_domains' => 5,
                'active_domains' => 4,
                'blocked_domains' => 1,
                'creator_id' => $adminUser->id
            ],
            [
                'name' => '测试域名池',
                'description' => '用于测试的域名池，开发和测试环境使用',
                'status' => 'inactive',
                'domains' => [
                    'test1.example.com',
                    'test2.example.com',
                    'dev1.example.com',
                    'dev2.example.com'
                ],
                'check_interval' => 30,
                'check_method' => 'http',
                'timeout' => 20,
                'retry_times' => 1,
                'switch_strategy' => 'manual',
                'switch_delay' => 0,
                'total_domains' => 4,
                'active_domains' => 2,
                'blocked_domains' => 2,
                'creator_id' => $adminUser->id
            ],
            [
                'name' => '国际域名池',
                'description' => '国际访问优化的域名池',
                'status' => 'active',
                'domains' => [
                    'global1.example.com',
                    'global2.example.com',
                    'intl1.example.com',
                    'intl2.example.com',
                    'world1.example.com'
                ],
                'check_interval' => 20,
                'check_method' => 'http',
                'timeout' => 30,
                'retry_times' => 3,
                'switch_strategy' => 'delayed',
                'switch_delay' => 10,
                'total_domains' => 5,
                'active_domains' => 3,
                'blocked_domains' => 2,
                'creator_id' => $adminUser->id
            ]
        ];

        foreach ($domainPools as $poolData) {
            DomainPool::create($poolData);
        }

        $this->command->info('域名池种子数据创建完成！');
    }
}