<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Models\PromotionLink;
use App\Services\PromotionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * 推广管理控制器
 */
class PromotionController extends Controller
{
    protected PromotionService $promotionService;

    public function __construct(PromotionService $promotionService)
    {
        $this->promotionService = $promotionService;
    }

    /**
     * 获取推广链接列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = PromotionLink::with(['group', 'creator']);

        // 按群组筛选
        if ($request->filled('group_id')) {
            $query->where('group_id', $request->group_id);
        }

        // 按创建者筛选
        if ($request->filled('created_by')) {
            $query->where('created_by', $request->created_by);
        }

        // 按状态筛选
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active();
                    break;
                case 'expired':
                    $query->expired();
                    break;
                case 'disabled':
                    $query->where('is_active', false);
                    break;
            }
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%")
                  ->orWhereHas('group', function ($gq) use ($search) {
                      $gq->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $links = $query->paginate($request->get('per_page', 15));

        return $this->success([
            'links' => $links->items(),
            'pagination' => [
                'current_page' => $links->currentPage(),
                'last_page' => $links->lastPage(),
                'per_page' => $links->perPage(),
                'total' => $links->total()
            ]
        ]);
    }

    /**
     * 创建推广链接
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|exists:wechat_groups,id',
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'tracking_params' => 'nullable|array',
            'tracking_params.source' => 'nullable|string|max:50',
            'tracking_params.utm_campaign' => 'nullable|string|max:100',
            'tracking_params.utm_medium' => 'nullable|string|max:50',
            'tracking_params.utm_source' => 'nullable|string|max:50',
            'tracking_params.utm_content' => 'nullable|string|max:100'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($request->group_id);
            
            // 生成推广链接
            $promotionData = $this->promotionService->generatePromotionLink(
                $group,
                $request->tracking_params ?? []
            );

            // 创建推广链接记录
            $link = PromotionLink::create([
                'group_id' => $group->id,
                'short_code' => basename($promotionData['short_url']),
                'original_url' => $promotionData['original_url'],
                'title' => $request->title ?? $promotionData['share_title'],
                'description' => $request->description ?? $promotionData['share_text'],
                'expires_at' => $request->expires_at ?? now()->addMonths(6),
                'tracking_params' => $request->tracking_params,
                'created_by' => auth()->id()
            ]);

            return $this->success([
                'link' => $link->load(['group', 'creator']),
                'promotion_data' => $promotionData
            ], '推广链接创建成功');

        } catch (\Exception $e) {
            return $this->error('创建推广链接失败：' . $e->getMessage());
        }
    }

    /**
     * 获取推广链接详情
     */
    public function show(PromotionLink $promotionLink): JsonResponse
    {
        $promotionLink->load(['group', 'creator']);
        
        return $this->success([
            'link' => $promotionLink,
            'stats' => $promotionLink->getStats(),
            'click_trend' => $promotionLink->getClickTrend()
        ]);
    }

    /**
     * 更新推广链接
     */
    public function update(Request $request, PromotionLink $promotionLink): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'nullable|boolean',
            'tracking_params' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $promotionLink->update($request->only([
                'title',
                'description',
                'expires_at',
                'is_active',
                'tracking_params'
            ]));

            return $this->success(
                $promotionLink->load(['group', 'creator']),
                '推广链接更新成功'
            );

        } catch (\Exception $e) {
            return $this->error('更新推广链接失败：' . $e->getMessage());
        }
    }

    /**
     * 删除推广链接
     */
    public function destroy(PromotionLink $promotionLink): JsonResponse
    {
        try {
            $promotionLink->delete();
            return $this->success(null, '推广链接删除成功');
        } catch (\Exception $e) {
            return $this->error('删除推广链接失败：' . $e->getMessage());
        }
    }

    /**
     * 批量操作推广链接
     */
    public function batchAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:enable,disable,delete,extend_expiry',
            'link_ids' => 'required|array|min:1',
            'link_ids.*' => 'exists:promotion_links,id',
            'extend_days' => 'required_if:action,extend_expiry|integer|min:1|max:365'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $links = PromotionLink::whereIn('id', $request->link_ids)->get();
            $successCount = 0;

            foreach ($links as $link) {
                switch ($request->action) {
                    case 'enable':
                        if ($link->enable()) {
                            $successCount++;
                        }
                        break;
                    case 'disable':
                        if ($link->disable()) {
                            $successCount++;
                        }
                        break;
                    case 'delete':
                        if ($link->delete()) {
                            $successCount++;
                        }
                        break;
                    case 'extend_expiry':
                        if ($link->extendExpiry($request->extend_days)) {
                            $successCount++;
                        }
                        break;
                }
            }

            return $this->success([
                'total' => count($links),
                'success_count' => $successCount,
                'failed_count' => count($links) - $successCount
            ], "批量操作完成，成功处理 {$successCount} 个链接");

        } catch (\Exception $e) {
            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }

    /**
     * 生成推广素材
     */
    public function generateMaterials(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|exists:wechat_groups,id',
            'materials' => 'nullable|array',
            'materials.*' => 'in:qr_codes,posters,share_texts,promotion_links'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($request->group_id);
            
            $materials = $this->promotionService->generatePromotionMaterials(
                $group,
                $request->only(['materials'])
            );

            return $this->success($materials, '推广素材生成成功');

        } catch (\Exception $e) {
            return $this->error('生成推广素材失败：' . $e->getMessage());
        }
    }

    /**
     * 获取推广统计
     */
    public function getStats(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'nullable|exists:wechat_groups,id',
            'date_range' => 'nullable|array|size:2',
            'date_range.*' => 'date'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $query = PromotionLink::query();

            if ($request->filled('group_id')) {
                $query->where('group_id', $request->group_id);
                $group = WechatGroup::findOrFail($request->group_id);
                $groupStats = $this->promotionService->getPromotionStats($group);
            }

            if ($request->filled('date_range')) {
                $query->whereBetween('created_at', $request->date_range);
            }

            // 基础统计
            $totalLinks = $query->count();
            $activeLinks = $query->active()->count();
            $expiredLinks = $query->expired()->count();
            $totalClicks = $query->sum('click_count');

            // 热门链接
            $popularLinks = $query->popular()
                ->with(['group', 'creator'])
                ->limit(10)
                ->get();

            // 最近创建的链接
            $recentLinks = $query->recent()
                ->with(['group', 'creator'])
                ->limit(10)
                ->get();

            $stats = [
                'overview' => [
                    'total_links' => $totalLinks,
                    'active_links' => $activeLinks,
                    'expired_links' => $expiredLinks,
                    'disabled_links' => $totalLinks - $activeLinks - $expiredLinks,
                    'total_clicks' => $totalClicks,
                    'avg_clicks_per_link' => $totalLinks > 0 ? round($totalClicks / $totalLinks, 2) : 0
                ],
                'popular_links' => $popularLinks,
                'recent_links' => $recentLinks
            ];

            if (isset($groupStats)) {
                $stats['group_stats'] = $groupStats;
            }

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 复制推广链接
     */
    public function duplicate(PromotionLink $promotionLink, Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $overrides = array_filter($request->only([
                'title',
                'description',
                'expires_at'
            ]));

            $newLink = $promotionLink->duplicate($overrides);

            return $this->success(
                $newLink->load(['group', 'creator']),
                '推广链接复制成功'
            );

        } catch (\Exception $e) {
            return $this->error('复制推广链接失败：' . $e->getMessage());
        }
    }

    /**
     * 重新生成短码
     */
    public function regenerateShortCode(PromotionLink $promotionLink): JsonResponse
    {
        try {
            $promotionLink->regenerateShortCode();

            return $this->success(
                $promotionLink->fresh(['group', 'creator']),
                '短码重新生成成功'
            );

        } catch (\Exception $e) {
            return $this->error('重新生成短码失败：' . $e->getMessage());
        }
    }

    /**
     * 获取即将过期的链接
     */
    public function getExpiringLinks(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        
        $expiringLinks = PromotionLink::getExpiringLinks($days);

        return $this->success([
            'expiring_links' => $expiringLinks,
            'count' => $expiringLinks->count(),
            'days' => $days
        ]);
    }

    /**
     * 清理过期链接
     */
    public function cleanupExpired(): JsonResponse
    {
        try {
            $deletedCount = PromotionLink::cleanupExpiredLinks();

            return $this->success([
                'deleted_count' => $deletedCount
            ], "成功清理 {$deletedCount} 个过期链接");

        } catch (\Exception $e) {
            return $this->error('清理过期链接失败：' . $e->getMessage());
        }
    }
}