<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\User;
use App\Models\Order;
use App\Models\GroupMember;
use App\Models\GroupTemplate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

/**
 * 群组服务类
 */
class GroupService
{
    /**
     * 获取群组列表
     */
    public function getGroupList(array $params): array
    {
        $query = WechatGroup::with(['creator', 'category'])
            ->withCount(['members', 'orders']);

        // 搜索
        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 分类筛选
        if (!empty($params['category'])) {
            $query->where('category', $params['category']);
        }

        // 状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 价格范围筛选
        if (!empty($params['price_range'])) {
            $this->applyPriceRangeFilter($query, $params['price_range']);
        }

        // 日期范围筛选
        if (!empty($params['date_range']) && count($params['date_range']) === 2) {
            $query->whereBetween('created_at', $params['date_range']);
        }

        // 排序
        $sortBy = $params['sort_by'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        $result = $query->paginate($perPage);

        return [
            'data' => $result->items(),
            'total' => $result->total(),
            'per_page' => $result->perPage(),
            'current_page' => $result->currentPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 创建群组
     */
    public function createGroup(array $data): WechatGroup
    {
        // 处理城市定位标题
        if ($data['city_location'] && $data['city_insert_strategy'] !== 'none') {
            $data['title_template'] = $data['title'];
        }

        // 处理富媒体内容
        $data['rich_content'] = $this->processRichContent($data);

        // 处理营销配置
        $data['marketing_config'] = $this->processMarketingConfig($data);

        // 处理支付配置
        $data['payment_config'] = $this->processPaymentConfig($data);

        // 处理分销配置
        $data['distribution_config'] = $this->processDistributionConfig($data);

        // 处理防红配置
        $data['anti_block_config'] = $this->processAntiBlockConfig($data);

        // 设置创建者
        $data['creator_id'] = auth()->id();
        $data['status'] = $data['auto_publish'] ? 'active' : 'draft';

        // 生成唯一标识
        $data['slug'] = $this->generateUniqueSlug($data['title']);

        $group = WechatGroup::create($data);

        // 处理媒体文件
        if (!empty($data['media_images'])) {
            $this->processMediaImages($group, $data['media_images']);
        }

        // 生成默认二维码（如果没有上传）
        if (empty($data['qr_code'])) {
            $this->generateDefaultQrCode($group);
        }

        return $group->load(['creator', 'category']);
    }

    /**
     * 获取群组详情
     */
    public function getGroupDetail(int $id): ?WechatGroup
    {
        return WechatGroup::with([
            'creator',
            'category',
            'members' => function ($query) {
                $query->with('user')->latest()->limit(10);
            },
            'orders' => function ($query) {
                $query->with('user')->latest()->limit(10);
            }
        ])
        ->withCount(['members', 'orders'])
        ->find($id);
    }

    /**
     * 更新群组
     */
    public function updateGroup(int $id, array $data): ?WechatGroup
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return null;
        }

        // 处理更新数据
        if (isset($data['city_location']) && $data['city_location'] && $data['city_insert_strategy'] !== 'none') {
            $data['title_template'] = $data['title'] ?? $group->title;
        }

        if (isset($data['rich_content'])) {
            $data['rich_content'] = $this->processRichContent($data);
        }

        if (isset($data['marketing_config'])) {
            $data['marketing_config'] = $this->processMarketingConfig($data);
        }

        if (isset($data['payment_config'])) {
            $data['payment_config'] = $this->processPaymentConfig($data);
        }

        if (isset($data['distribution_config'])) {
            $data['distribution_config'] = $this->processDistributionConfig($data);
        }

        if (isset($data['anti_block_config'])) {
            $data['anti_block_config'] = $this->processAntiBlockConfig($data);
        }

        // 处理媒体文件更新
        if (isset($data['media_images'])) {
            $this->processMediaImages($group, $data['media_images']);
        }

        $group->update($data);

        return $group->load(['creator', 'category']);
    }

    /**
     * 删除群组
     */
    public function deleteGroup(int $id): bool
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return false;
        }

        // 删除相关文件
        $this->deleteGroupFiles($group);

        // 软删除群组
        return $group->delete();
    }

    /**
     * 批量删除群组
     */
    public function batchDeleteGroups(array $ids): int
    {
        $groups = WechatGroup::whereIn('id', $ids)->get();
        
        $deletedCount = 0;
        foreach ($groups as $group) {
            if ($this->deleteGroup($group->id)) {
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * 切换群组状态
     */
    public function toggleGroupStatus(int $id, string $status): ?WechatGroup
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return null;
        }

        $group->update(['status' => $status]);

        return $group;
    }

    /**
     * 获取群组统计数据
     */
    public function getGroupStats(int $id): array
    {
        $group = WechatGroup::with(['members', 'orders'])->find($id);
        
        if (!$group) {
            return [];
        }

        $stats = [
            'basic_info' => [
                'id' => $group->id,
                'title' => $group->title,
                'status' => $group->status,
                'created_at' => $group->created_at
            ],
            'member_stats' => [
                'total_members' => $group->members_count ?? 0,
                'active_members' => $group->members()->where('status', 'active')->count(),
                'new_members_today' => $group->members()->whereDate('created_at', today())->count(),
                'member_growth_rate' => $this->calculateMemberGrowthRate($group)
            ],
            'order_stats' => [
                'total_orders' => $group->orders_count ?? 0,
                'paid_orders' => $group->orders()->where('status', 'paid')->count(),
                'total_revenue' => $group->orders()->where('status', 'paid')->sum('amount'),
                'orders_today' => $group->orders()->whereDate('created_at', today())->count(),
                'conversion_rate' => $this->calculateConversionRate($group)
            ],
            'engagement_stats' => [
                'page_views' => $this->getPageViews($group->id),
                'unique_visitors' => $this->getUniqueVisitors($group->id),
                'bounce_rate' => $this->getBounceRate($group->id),
                'avg_session_duration' => $this->getAvgSessionDuration($group->id)
            ]
        ];

        return $stats;
    }

    /**
     * 获取总体统计数据
     */
    public function getOverallStats(): array
    {
        $cacheKey = 'group_overall_stats';
        
        return Cache::remember($cacheKey, 300, function () {
            return [
                'total_groups' => WechatGroup::count(),
                'active_groups' => WechatGroup::where('status', 'active')->count(),
                'total_members' => GroupMember::count(),
                'total_revenue' => Order::where('status', 'paid')->sum('amount'),
                'today_orders' => Order::whereDate('created_at', today())->count(),
                'conversion_rate' => $this->calculateOverallConversionRate(),
                'popular_categories' => $this->getPopularCategories(),
                'revenue_trend' => $this->getRevenueTrend(),
                'member_growth_trend' => $this->getMemberGrowthTrend()
            ];
        });
    }

    /**
     * 获取群组成员列表
     */
    public function getGroupMembers(int $groupId, array $params): array
    {
        $query = GroupMember::with('user')
            ->where('group_id', $groupId);

        // 搜索
        if (!empty($params['search'])) {
            $search = $params['search'];
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('nickname', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // 分页
        $perPage = $params['per_page'] ?? 15;
        $result = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return [
            'data' => $result->items(),
            'total' => $result->total(),
            'per_page' => $result->perPage(),
            'current_page' => $result->currentPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 移除群组成员
     */
    public function removeGroupMember(int $groupId, int $userId): bool
    {
        $member = GroupMember::where('group_id', $groupId)
            ->where('user_id', $userId)
            ->first();

        if (!$member) {
            return false;
        }

        return $member->delete();
    }

    /**
     * 获取群组订单列表
     */
    public function getGroupOrders(int $groupId, array $params): array
    {
        $query = Order::with('user')
            ->where('group_id', $groupId);

        // 状态筛选
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 日期范围筛选
        if (!empty($params['date_range']) && count($params['date_range']) === 2) {
            $query->whereBetween('created_at', $params['date_range']);
        }

        // 分页
        $perPage = $params['per_page'] ?? 15;
        $result = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return [
            'data' => $result->items(),
            'total' => $result->total(),
            'per_page' => $result->perPage(),
            'current_page' => $result->currentPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 生成预览
     */
    public function generatePreview(array $data): array
    {
        $preview = [
            'title' => $data['title'],
            'description' => $data['description'],
            'price' => $data['price'],
            'cover_image' => $data['cover_image'] ?? '/default-group-avatar.png',
            'formatted_price' => $data['price'] == 0 ? '免费' : '¥' . number_format($data['price'], 2),
            'city_replaced_title' => $this->applyCityReplacement(
                $data['title'],
                '北京', // 示例城市
                $data['city_insert_strategy'] ?? 'auto'
            )
        ];

        return $preview;
    }

    /**
     * 生成推广海报
     */
    public function generatePoster(int $groupId, array $options = []): ?string
    {
        $group = WechatGroup::find($groupId);
        
        if (!$group) {
            return null;
        }

        $template = $options['template'] ?? 'default';
        $includeQr = $options['include_qr'] ?? true;
        $customText = $options['custom_text'] ?? '';

        // 生成海报逻辑
        $posterPath = $this->createPosterImage($group, $template, $includeQr, $customText);

        return $posterPath;
    }

    /**
     * 复制群组
     */
    public function duplicateGroup(int $id): ?WechatGroup
    {
        $originalGroup = WechatGroup::find($id);
        
        if (!$originalGroup) {
            return null;
        }

        $data = $originalGroup->toArray();
        
        // 移除不需要复制的字段
        unset($data['id'], $data['created_at'], $data['updated_at'], $data['deleted_at']);
        
        // 修改标题
        $data['title'] = $data['title'] . ' (副本)';
        $data['slug'] = $this->generateUniqueSlug($data['title']);
        $data['status'] = 'draft';
        $data['creator_id'] = auth()->id();

        return WechatGroup::create($data);
    }

    /**
     * 导出群组数据
     */
    public function exportGroupData(int $id, string $format = 'excel'): ?string
    {
        $group = WechatGroup::with(['members.user', 'orders.user'])->find($id);
        
        if (!$group) {
            return null;
        }

        // 根据格式导出数据
        switch ($format) {
            case 'excel':
                return $this->exportToExcel($group);
            case 'csv':
                return $this->exportToCsv($group);
            case 'pdf':
                return $this->exportToPdf($group);
            default:
                return null;
        }
    }

    /**
     * 测试城市替换
     */
    public function testCityReplacement(string $title, string $city, string $strategy): string
    {
        return $this->applyCityReplacement($title, $city, $strategy);
    }

    /**
     * 获取推荐设置
     */
    public function getRecommendedSettings(string $category): array
    {
        $recommendations = [
            'technology' => [
                'recommended_price' => 29.9,
                'recommended_member_limit' => 500,
                'recommended_button_title' => '加入技术群',
                'recommended_description_template' => '专业的技术交流群，分享最新技术动态和经验。',
                'recommended_tags' => ['技术', '编程', '开发', '交流']
            ],
            'finance' => [
                'recommended_price' => 99.9,
                'recommended_member_limit' => 200,
                'recommended_button_title' => '加入理财群',
                'recommended_description_template' => '专业的投资理财交流群，分享投资经验和市场分析。',
                'recommended_tags' => ['理财', '投资', '股票', '基金']
            ],
            'education' => [
                'recommended_price' => 19.9,
                'recommended_member_limit' => 300,
                'recommended_button_title' => '加入学习群',
                'recommended_description_template' => '优质的学习交流群，共同进步，共同成长。',
                'recommended_tags' => ['学习', '教育', '成长', '知识']
            ],
            // 其他分类...
        ];

        return $recommendations[$category] ?? [
            'recommended_price' => 9.9,
            'recommended_member_limit' => 500,
            'recommended_button_title' => '立即加入',
            'recommended_description_template' => '优质的交流群，欢迎加入。',
            'recommended_tags' => ['交流', '分享']
        ];
    }

    /**
     * 获取群组分析数据
     */
    public function getGroupAnalytics(int $groupId, array $params): array
    {
        // 实现分析数据获取逻辑
        return [
            'page_views' => $this->getPageViewsAnalytics($groupId, $params),
            'member_growth' => $this->getMemberGrowthAnalytics($groupId, $params),
            'order_analytics' => $this->getOrderAnalytics($groupId, $params),
            'conversion_funnel' => $this->getConversionFunnel($groupId, $params)
        ];
    }

    // 私有方法

    /**
     * 应用价格范围筛选
     */
    private function applyPriceRangeFilter($query, string $priceRange): void
    {
        switch ($priceRange) {
            case 'free':
                $query->where('price', 0);
                break;
            case '0-50':
                $query->whereBetween('price', [0.01, 50]);
                break;
            case '50-100':
                $query->whereBetween('price', [50.01, 100]);
                break;
            case '100+':
                $query->where('price', '>', 100);
                break;
        }
    }

    /**
     * 处理富媒体内容
     */
    private function processRichContent(array $data): array
    {
        return [
            'intro' => [
                'title' => $data['group_intro_title'] ?? '',
                'content' => $data['group_intro_content'] ?? ''
            ],
            'faq' => [
                'title' => $data['faq_title'] ?? '',
                'content' => $data['faq_content'] ?? ''
            ],
            'reviews' => $data['member_reviews'] ?? '',
            'media' => [
                'images' => $data['media_images'] ?? [],
                'video' => $data['promo_video'] ?? '',
                'audio' => $data['audio_intro'] ?? ''
            ]
        ];
    }

    /**
     * 处理营销配置
     */
    private function processMarketingConfig(array $data): array
    {
        return [
            'display' => [
                'read_count' => $data['read_count_display'] ?? '',
                'like_count' => $data['like_count'] ?? 0,
                'want_see_count' => $data['want_see_count'] ?? 0,
                'virtual_members' => $data['virtual_members'] ?? 0,
                'virtual_orders' => $data['virtual_orders'] ?? 0
            ],
            'limited_offer' => [
                'enabled' => $data['enable_limited_offer'] ?? false,
                'price' => $data['offer_price'] ?? 0,
                'end_time' => $data['offer_end_time'] ?? null
            ],
            'urgency' => [
                'enabled' => $data['enable_urgency'] ?? false,
                'remaining_slots' => $data['remaining_slots'] ?? 0,
                'show_countdown' => $data['show_countdown'] ?? false
            ],
            'social_proof' => [
                'enabled' => $data['enable_social_proof'] ?? false,
                'recent_join_text' => $data['recent_join_text'] ?? ''
            ]
        ];
    }

    /**
     * 处理支付配置
     */
    private function processPaymentConfig(array $data): array
    {
        return [
            'methods' => $data['payment_methods'] ?? [],
            'expire_minutes' => $data['order_expire_minutes'] ?? 30,
            'success_redirect_url' => $data['success_redirect_url'] ?? ''
        ];
    }

    /**
     * 处理分销配置
     */
    private function processDistributionConfig(array $data): array
    {
        return [
            'enabled' => $data['enable_distribution'] ?? false,
            'commission_rates' => [
                'level_1' => $data['commission_rate_1'] ?? 0,
                'level_2' => $data['commission_rate_2'] ?? 0,
                'level_3' => $data['commission_rate_3'] ?? 0
            ],
            'settlement' => $data['commission_settlement'] ?? 'daily'
        ];
    }

    /**
     * 处理防红配置
     */
    private function processAntiBlockConfig(array $data): array
    {
        return [
            'enabled' => $data['enable_anti_block'] ?? false,
            'domain_pool_id' => $data['domain_pool_id'] ?? null,
            'check_frequency' => $data['check_frequency'] ?? '10',
            'switch_strategy' => $data['switch_strategy'] ?? 'immediate'
        ];
    }

    /**
     * 生成唯一标识
     */
    private function generateUniqueSlug(string $title): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (WechatGroup::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * 处理媒体图片
     */
    private function processMediaImages(WechatGroup $group, array $images): void
    {
        // 处理媒体图片上传和关联
        foreach ($images as $image) {
            // 保存图片关联关系
        }
    }

    /**
     * 生成默认二维码
     */
    private function generateDefaultQrCode(WechatGroup $group): void
    {
        // 生成默认二维码逻辑
        $qrCodePath = "qrcodes/group_{$group->id}.png";
        // 实际生成二维码的代码
        $group->update(['qr_code' => $qrCodePath]);
    }

    /**
     * 删除群组文件
     */
    private function deleteGroupFiles(WechatGroup $group): void
    {
        // 删除相关文件
        if ($group->cover_image) {
            Storage::delete($group->cover_image);
        }
        if ($group->qr_code) {
            Storage::delete($group->qr_code);
        }
    }

    /**
     * 应用城市替换
     */
    private function applyCityReplacement(string $title, string $city, string $strategy): string
    {
        switch ($strategy) {
            case 'auto':
                if (strpos($title, 'xxx') !== false) {
                    return str_replace('xxx', $city, $title);
                } else {
                    return $city . $title;
                }
            case 'prefix':
                return $city . $title;
            case 'suffix':
                return $title . $city;
            case 'natural':
                return str_replace('xxx', $city, $title);
            case 'none':
            default:
                return $title;
        }
    }

    /**
     * 计算成员增长率
     */
    private function calculateMemberGrowthRate(WechatGroup $group): float
    {
        // 实现成员增长率计算逻辑
        return 0.0;
    }

    /**
     * 计算转化率
     */
    private function calculateConversionRate(WechatGroup $group): float
    {
        // 实现转化率计算逻辑
        return 0.0;
    }

    /**
     * 计算总体转化率
     */
    private function calculateOverallConversionRate(): float
    {
        // 实现总体转化率计算逻辑
        return 0.0;
    }

    /**
     * 获取页面浏览量
     */
    private function getPageViews(int $groupId): int
    {
        // 实现页面浏览量获取逻辑
        return 0;
    }

    /**
     * 获取独立访客数
     */
    private function getUniqueVisitors(int $groupId): int
    {
        // 实现独立访客数获取逻辑
        return 0;
    }

    /**
     * 获取跳出率
     */
    private function getBounceRate(int $groupId): float
    {
        // 实现跳出率获取逻辑
        return 0.0;
    }

    /**
     * 获取平均会话时长
     */
    private function getAvgSessionDuration(int $groupId): int
    {
        // 实现平均会话时长获取逻辑
        return 0;
    }

    /**
     * 获取热门分类
     */
    private function getPopularCategories(): array
    {
        // 实现热门分类获取逻辑
        return [];
    }

    /**
     * 获取收入趋势
     */
    private function getRevenueTrend(): array
    {
        // 实现收入趋势获取逻辑
        return [];
    }

    /**
     * 获取成员增长趋势
     */
    private function getMemberGrowthTrend(): array
    {
        // 实现成员增长趋势获取逻辑
        return [];
    }

    /**
     * 创建海报图片
     */
    private function createPosterImage(WechatGroup $group, string $template, bool $includeQr, string $customText): string
    {
        // 实现海报生成逻辑
        return '';
    }

    /**
     * 导出到Excel
     */
    private function exportToExcel(WechatGroup $group): string
    {
        // 实现Excel导出逻辑
        return '';
    }

    /**
     * 导出到CSV
     */
    private function exportToCsv(WechatGroup $group): string
    {
        // 实现CSV导出逻辑
        return '';
    }

    /**
     * 导出到PDF
     */
    private function exportToPdf(WechatGroup $group): string
    {
        // 实现PDF导出逻辑
        return '';
    }

    /**
     * 获取页面浏览分析
     */
    private function getPageViewsAnalytics(int $groupId, array $params): array
    {
        // 实现页面浏览分析逻辑
        return [];
    }

    /**
     * 获取成员增长分析
     */
    private function getMemberGrowthAnalytics(int $groupId, array $params): array
    {
        // 实现成员增长分析逻辑
        return [];
    }

    /**
     * 获取订单分析
     */
    private function getOrderAnalytics(int $groupId, array $params): array
    {
        // 实现订单分析逻辑
        return [];
    }

    /**
     * 获取转化漏斗
     */
    private function getConversionFunnel(int $groupId, array $params): array
    {
        // 实现转化漏斗分析逻辑
        return [];
    }
}