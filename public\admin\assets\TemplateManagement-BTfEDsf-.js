import{_ as e,k as l}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                        *//* empty css                  *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css                *//* empty css                     */import{r as a,L as t,c as d,e as o,k as u,l as i,t as r,E as s,A as n,z as _,D as c,u as m,a3 as p,F as f,Y as b,y as v,B as g}from"./vue-vendor-Dy164gUc.js";import{Q as V,T as y,aw as h,at as w,aR as k,bp as U,bq as x,aM as C,b9 as j,b8 as z,aY as q,bw as E,U as B,bm as S,bn as A,bh as O,bi as R,bS as T,a$ as $,bs as I,bV as K,aZ as L,a_ as M,o as P,Y as Q,ac as Y,bQ as Z,ao as F,bW as J,bx as N,bA as W,aU as D,br as G,ay as H,bk as X,bl as ee,R as le}from"./element-plus-h2SQQM64.js";import{f as ae}from"./format-3eU4VJ9V.js";import{i as te,j as de,t as oe,k as ue,l as ie,m as re,n as se}from"./community-DNWNbya4.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const ne={class:"template-management"},_e={class:"page-header"},ce={class:"header-actions"},me={class:"table-header"},pe={class:"table-title"},fe={class:"table-actions"},be={class:"template-info"},ve={class:"template-cover"},ge=["src"],Ve={class:"template-details"},ye={class:"template-name"},he={class:"template-code"},we={class:"template-desc"},ke={key:1,class:"card-view"},Ue={class:"card-cover"},xe=["src"],Ce={key:1,class:"no-image"},je={class:"card-overlay"},ze={class:"card-content"},qe={class:"card-header"},Ee={class:"card-title"},Be={class:"card-badges"},Se={class:"card-desc"},Ae={class:"card-stats"},Oe={class:"stat-item"},Re={class:"stat-item"},Te={class:"pagination-wrapper"},$e=["src"],Ie={class:"custom-fields-config"},Ke={class:"dialog-footer"},Le={class:"template-preview"},Me={class:"preview-header"},Pe={class:"preview-content"},Qe={key:0,class:"preview-description"},Ye=e({__name:"TemplateManagement",setup(e){const Ye=a(!1),Ze=a(!1),Fe=a([]),Je=a(0),Ne=a([]),We=a({}),De=a("table"),Ge=t({page:1,per_page:20,keyword:"",category:"",is_preset:null,is_active:null}),He=t({visible:!1,title:"",isEdit:!1}),Xe=t({visible:!1,loading:!1,data:{}}),el=t({template_name:"",category:"",description:"",cover_image:"",template_data:{title:"",description:"",price:0,member_limit:500,virtual_members:0,virtual_orders:0,virtual_income:0,faq_content:"",member_reviews:""},custom_fields_config:[],sort_order:0,read_count:"10万+",like_count:3659,want_see_count:665,button_title:"加入群，学习更多副业知识",avatar_library:"default",group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",user_reviews:"",customer_service_qr:"",ad_image:"",extra_title1:"",extra_content1:"",extra_title2:"",extra_content2:""}),ll={template_name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],category:[{required:!0,message:"请选择模板分类",trigger:"change"}],"template_data.title":[{required:!0,message:"请输入群名称",trigger:"blur"}],"template_data.description":[{required:!0,message:"请输入群描述",trigger:"blur"}],"template_data.price":[{required:!0,message:"请输入入群价格",trigger:"blur"}]},al=a(null),tl=a("/api/v1/group-templates/upload-cover"),dl=a({Authorization:`Bearer ${l()}`}),ol=d(()=>JSON.parse(localStorage.getItem("user")||"{}"));async function ul(){Ye.value=!0;try{const e=await te(Ge);Fe.value=e.data.data,Je.value=e.data.total}catch(e){V.error("获取模板列表失败")}finally{Ye.value=!1}}function il(){Object.assign(Ge,{page:1,per_page:20,keyword:"",category:"",is_preset:null,is_active:null}),ul()}function rl(){ul()}function sl(){He.title="新建模板",He.isEdit=!1,He.visible=!0,Object.assign(el,{template_name:"",category:"",description:"",cover_image:"",template_data:{title:"",description:"",price:0,member_limit:500,virtual_members:0,virtual_orders:0,virtual_income:0,faq_content:"",member_reviews:""},custom_fields_config:[],sort_order:0})}async function nl(e){if(fl(e)){He.title="编辑模板",He.isEdit=!0,He.visible=!0;try{const l=await ue(e.id);Object.assign(el,l.data)}catch(l){V.error("加载模板详情失败")}}else V.warning("您无权编辑此模板")}async function _l(){al.value&&await al.value.validate(async e=>{if(e){Ze.value=!0;try{He.isEdit?(await re(el.id,el),V.success("更新模板成功")):(await se(el),V.success("创建模板成功")),He.visible=!1,ul()}catch(l){V.error(He.isEdit?"更新模板失败":"创建模板失败")}finally{Ze.value=!1}}})}async function cl(e){if(bl(e))try{await le.confirm(`确定要删除模板 "${e.template_name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ie(e.id),V.success("删除模板成功"),ul()}catch(l){"cancel"!==l&&V.error("删除模板失败")}else V.warning("您无权删除此模板")}function ml(e){Xe.data=e,Xe.visible=!0}function pl(){el.custom_fields_config.push({key:"",type:"text",label:"",default_value:"",placeholder:""})}function fl(e){return"admin"===ol.value.role||e.created_by===ol.value.id&&!e.is_preset}function bl(e){return!e.is_preset&&("admin"===ol.value.role||e.created_by===ol.value.id)}function vl(e){return{preset:"",custom:"info",business:"warning",education:"success",entertainment:"danger",technology:"primary"}[e]||"info"}function gl(e){Ne.value=e.map(e=>e.id)}function Vl(e){const l=e.type.startsWith("image/"),a=e.size/1024/1024<2;return l?!!a||(V.error("图片大小不能超过 2MB!"),!1):(V.error("只能上传图片文件!"),!1)}function yl(e){200===e.code?(el.cover_image=e.data.url,V.success("上传成功")):V.error("上传失败")}return o(()=>{ul(),async function(){try{const e=await de();We.value=e.data}catch(e){V.error("获取分类列表失败")}}()}),(e,l)=>{const a=y,t=w,d=C,o=x,le=z,te=j,de=U,ue=q,ie=A,re=S,se=R,Ne=$,ol=I,hl=K,wl=O,kl=M,Ul=L,xl=N,Cl=W,jl=D,zl=G,ql=H,El=ee,Bl=X,Sl=E;return i(),u("div",ne,[r("div",_e,[l[41]||(l[41]=r("div",{class:"header-title"},[r("h2",null,"📋 群组模板管理"),r("p",null,"管理群组模板，提高群组创建效率")],-1)),r("div",ce,[s(t,{type:"primary",onClick:sl},{default:_(()=>[s(a,null,{default:_(()=>[s(m(h))]),_:1}),l[39]||(l[39]=c(" 新建模板 ",-1))]),_:1,__:[39]}),s(t,{onClick:rl},{default:_(()=>[s(a,null,{default:_(()=>[s(m(k))]),_:1}),l[40]||(l[40]=c(" 刷新 ",-1))]),_:1,__:[40]})])]),s(ue,{class:"filter-card"},{default:_(()=>[s(de,{inline:!0,model:Ge,class:"filter-form"},{default:_(()=>[s(o,{label:"关键词"},{default:_(()=>[s(d,{modelValue:Ge.keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>Ge.keyword=e),placeholder:"模板名称/描述/代码",clearable:"",style:{width:"200px"},onClear:ul,onKeyup:p(ul,["enter"])},null,8,["modelValue"])]),_:1}),s(o,{label:"分类"},{default:_(()=>[s(te,{modelValue:Ge.category,"onUpdate:modelValue":l[1]||(l[1]=e=>Ge.category=e),placeholder:"全部分类",clearable:"",style:{width:"150px"}},{default:_(()=>[(i(!0),u(f,null,b(We.value,(e,l)=>(i(),v(le,{key:l,label:e,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(o,{label:"类型"},{default:_(()=>[s(te,{modelValue:Ge.is_preset,"onUpdate:modelValue":l[2]||(l[2]=e=>Ge.is_preset=e),placeholder:"全部类型",clearable:"",style:{width:"120px"}},{default:_(()=>[s(le,{label:"预设模板",value:!0}),s(le,{label:"自定义模板",value:!1})]),_:1},8,["modelValue"])]),_:1}),s(o,{label:"状态"},{default:_(()=>[s(te,{modelValue:Ge.is_active,"onUpdate:modelValue":l[3]||(l[3]=e=>Ge.is_active=e),placeholder:"全部状态",clearable:"",style:{width:"120px"}},{default:_(()=>[s(le,{label:"启用",value:!0}),s(le,{label:"禁用",value:!1})]),_:1},8,["modelValue"])]),_:1}),s(o,null,{default:_(()=>[s(t,{type:"primary",onClick:ul},{default:_(()=>l[42]||(l[42]=[c("查询",-1)])),_:1,__:[42]}),s(t,{onClick:il},{default:_(()=>l[43]||(l[43]=[c("重置",-1)])),_:1,__:[43]})]),_:1})]),_:1},8,["model"])]),_:1}),n((i(),v(ue,{class:"table-card"},{default:_(()=>[r("div",me,[r("div",pe,[r("span",null,"模板列表 ("+B(Je.value)+")",1)]),r("div",fe,[s(re,{modelValue:De.value,"onUpdate:modelValue":l[4]||(l[4]=e=>De.value=e),size:"small"},{default:_(()=>[s(ie,{label:"table"},{default:_(()=>l[44]||(l[44]=[c("列表",-1)])),_:1,__:[44]}),s(ie,{label:"card"},{default:_(()=>l[45]||(l[45]=[c("卡片",-1)])),_:1,__:[45]})]),_:1},8,["modelValue"])])]),"table"===De.value?(i(),v(wl,{key:0,data:Fe.value,style:{width:"100%"},onSelectionChange:gl},{default:_(()=>[s(se,{type:"selection",width:"55"}),s(se,{label:"模板信息","min-width":"200"},{default:_(e=>[r("div",be,[r("div",ve,[e.row.cover_image_url?(i(),u("img",{key:0,src:e.row.cover_image_url},null,8,ge)):(i(),v(a,{key:1},{default:_(()=>[s(m(T))]),_:1}))]),r("div",Ve,[r("div",ye,B(e.row.template_name),1),r("div",he,B(e.row.template_code),1),r("div",we,B(e.row.description||"No description"),1)])])]),_:1}),s(se,{label:"分类",width:"120"},{default:_(e=>[s(Ne,{type:vl(e.row.category)},{default:_(()=>[c(B(e.row.category_name),1)]),_:2},1032,["type"])]),_:1}),s(se,{label:"类型",width:"100"},{default:_(e=>[s(Ne,{type:e.row.is_preset?"warning":"info"},{default:_(()=>[c(B(e.row.is_preset?"预设":"自定义"),1)]),_:2},1032,["type"])]),_:1}),s(se,{label:"状态",width:"80"},{default:_(e=>[s(ol,{modelValue:e.row.is_active,"onUpdate:modelValue":l=>e.row.is_active=l,onChange:l=>async function(e){try{await oe(e.id,e.is_active),V.success((e.is_active?"启用":"禁用")+"模板成功")}catch(l){e.is_active=!e.is_active,V.error("操作失败")}}(e.row),disabled:e.row.is_preset},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),s(se,{label:"使用次数",width:"100"},{default:_(e=>[s(hl,{type:"primary"},{default:_(()=>[c(B(e.row.usage_count),1)]),_:2},1024)]),_:1}),s(se,{label:"创建者",width:"120"},{default:_(e=>[c(B(e.row.creator?.username||"系统"),1)]),_:1}),s(se,{label:"创建时间",width:"160"},{default:_(e=>[c(B(m(ae)(e.row.created_at)),1)]),_:1}),s(se,{label:"操作",width:"200",fixed:"right"},{default:_(e=>[s(t,{size:"small",onClick:l=>ml(e.row)},{default:_(()=>l[46]||(l[46]=[c("预览",-1)])),_:2,__:[46]},1032,["onClick"]),s(t,{size:"small",onClick:l=>nl(e.row),disabled:!fl(e.row)},{default:_(()=>l[47]||(l[47]=[c("编辑",-1)])),_:2,__:[47]},1032,["onClick","disabled"]),s(t,{size:"small",onClick:l=>{return a=e.row,Object.assign(el,{...a,template_name:a.template_name+" (副本)",id:void 0}),He.title="复制模板",He.isEdit=!1,void(He.visible=!0);var a}},{default:_(()=>l[48]||(l[48]=[c("复制",-1)])),_:2,__:[48]},1032,["onClick"]),s(t,{size:"small",type:"danger",onClick:l=>cl(e.row),disabled:!bl(e.row)},{default:_(()=>l[49]||(l[49]=[c(" 删除 ",-1)])),_:2,__:[49]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])):(i(),u("div",ke,[s(Ul,{gutter:20},{default:_(()=>[(i(!0),u(f,null,b(Fe.value,e=>(i(),v(kl,{span:6,key:e.id},{default:_(()=>[r("div",{class:P(["template-card",{"template-disabled":!e.is_active}])},[r("div",Ue,[e.cover_image_url?(i(),u("img",{key:0,src:e.cover_image_url},null,8,xe)):(i(),u("div",Ce,[s(a,null,{default:_(()=>[s(m(T))]),_:1})])),r("div",je,[s(t,{size:"small",circle:"",onClick:l=>ml(e)},{default:_(()=>[s(a,null,{default:_(()=>[s(m(Q))]),_:1})]),_:2},1032,["onClick"]),s(t,{size:"small",circle:"",onClick:l=>nl(e),disabled:!fl(e)},{default:_(()=>[s(a,null,{default:_(()=>[s(m(Y))]),_:1})]),_:2},1032,["onClick","disabled"]),s(t,{size:"small",circle:"",type:"danger",onClick:l=>cl(e),disabled:!bl(e)},{default:_(()=>[s(a,null,{default:_(()=>[s(m(Z))]),_:1})]),_:2},1032,["onClick","disabled"])])]),r("div",ze,[r("div",qe,[r("h4",Ee,B(e.template_name),1),r("div",Be,[s(Ne,{size:"small",type:e.is_preset?"warning":"info"},{default:_(()=>[c(B(e.is_preset?"预设":"自定义"),1)]),_:2},1032,["type"]),s(Ne,{size:"small",type:vl(e.category)},{default:_(()=>[c(B(e.category_name),1)]),_:2},1032,["type"])])]),r("p",Se,B(e.description||"暂无描述"),1),r("div",Ae,[r("span",Oe,[s(a,null,{default:_(()=>[s(m(F))]),_:1}),c(" 使用 "+B(e.usage_count)+" 次 ",1)]),r("span",Re,[s(a,null,{default:_(()=>[s(m(J))]),_:1}),c(" "+B(m(ae)(e.created_at)),1)])])])],2)]),_:2},1024))),128))]),_:1})])),r("div",Te,[s(xl,{"current-page":Ge.page,"onUpdate:currentPage":l[5]||(l[5]=e=>Ge.page=e),"page-size":Ge.per_page,"onUpdate:pageSize":l[6]||(l[6]=e=>Ge.per_page=e),"page-sizes":[10,20,50,100],total:Je.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ul,onCurrentChange:ul},null,8,["current-page","page-size","total"])])]),_:1})),[[Sl,Ye.value]]),s(ql,{title:He.title,modelValue:He.visible,"onUpdate:modelValue":l[35]||(l[35]=e=>He.visible=e),width:"900px","close-on-click-modal":!1},{footer:_(()=>[r("div",Ke,[s(t,{onClick:l[34]||(l[34]=e=>He.visible=!1)},{default:_(()=>l[57]||(l[57]=[c("取消",-1)])),_:1,__:[57]}),s(t,{type:"primary",onClick:_l,loading:Ze.value},{default:_(()=>l[58]||(l[58]=[c("保存",-1)])),_:1,__:[58]},8,["loading"])])]),default:_(()=>[n((i(),v(de,{model:el,rules:ll,ref_key:"formRef",ref:al,"label-width":"120px"},{default:_(()=>[s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"模板名称",prop:"template_name"},{default:_(()=>[s(d,{modelValue:el.template_name,"onUpdate:modelValue":l[7]||(l[7]=e=>el.template_name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"模板分类",prop:"category"},{default:_(()=>[s(te,{modelValue:el.category,"onUpdate:modelValue":l[8]||(l[8]=e=>el.category=e),placeholder:"请选择分类",style:{width:"100%"}},{default:_(()=>[(i(!0),u(f,null,b(We.value,(e,l)=>(i(),v(le,{key:l,label:e,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(o,{label:"模板描述"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.description,"onUpdate:modelValue":l[9]||(l[9]=e=>el.description=e),placeholder:"请输入模板描述",rows:3},null,8,["modelValue"])]),_:1}),s(o,{label:"封面图片"},{default:_(()=>[s(Cl,{action:tl.value,headers:dl.value,"on-success":yl,"before-upload":Vl,"show-file-list":!1,class:"cover-uploader"},{default:_(()=>[el.cover_image?(i(),u("img",{key:0,src:el.cover_image,class:"cover-image"},null,8,$e)):(i(),v(a,{key:1,class:"cover-uploader-icon"},{default:_(()=>[s(m(h))]),_:1}))]),_:1},8,["action","headers"])]),_:1}),s(jl,{"content-position":"left"},{default:_(()=>l[50]||(l[50]=[c("基础配置",-1)])),_:1,__:[50]}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"群名称",prop:"template_data.title"},{default:_(()=>[s(d,{modelValue:el.template_data.title,"onUpdate:modelValue":l[10]||(l[10]=e=>el.template_data.title=e),placeholder:"支持变量：{{city}}、{{username}}"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"入群价格",prop:"template_data.price"},{default:_(()=>[s(zl,{modelValue:el.template_data.price,"onUpdate:modelValue":l[11]||(l[11]=e=>el.template_data.price=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(o,{label:"群描述",prop:"template_data.description"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.template_data.description,"onUpdate:modelValue":l[12]||(l[12]=e=>el.template_data.description=e),placeholder:"支持变量：{{city}}、{{username}}",rows:4},null,8,["modelValue"])]),_:1}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"成员上限"},{default:_(()=>[s(zl,{modelValue:el.template_data.member_limit,"onUpdate:modelValue":l[13]||(l[13]=e=>el.template_data.member_limit=e),min:10,max:2e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"排序权重"},{default:_(()=>[s(zl,{modelValue:el.sort_order,"onUpdate:modelValue":l[14]||(l[14]=e=>el.sort_order=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(jl,{"content-position":"left"},{default:_(()=>l[51]||(l[51]=[c("营销配置",-1)])),_:1,__:[51]}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:8},{default:_(()=>[s(o,{label:"虚拟成员数"},{default:_(()=>[s(zl,{modelValue:el.template_data.virtual_members,"onUpdate:modelValue":l[15]||(l[15]=e=>el.template_data.virtual_members=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:8},{default:_(()=>[s(o,{label:"虚拟订单数"},{default:_(()=>[s(zl,{modelValue:el.template_data.virtual_orders,"onUpdate:modelValue":l[16]||(l[16]=e=>el.template_data.virtual_orders=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:8},{default:_(()=>[s(o,{label:"虚拟收益"},{default:_(()=>[s(zl,{modelValue:el.template_data.virtual_income,"onUpdate:modelValue":l[17]||(l[17]=e=>el.template_data.virtual_income=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:8},{default:_(()=>[s(o,{label:"阅读数显示"},{default:_(()=>[s(d,{modelValue:el.read_count,"onUpdate:modelValue":l[18]||(l[18]=e=>el.read_count=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:8},{default:_(()=>[s(o,{label:"点赞数"},{default:_(()=>[s(zl,{modelValue:el.like_count,"onUpdate:modelValue":l[19]||(l[19]=e=>el.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:8},{default:_(()=>[s(o,{label:"想看数"},{default:_(()=>[s(zl,{modelValue:el.want_see_count,"onUpdate:modelValue":l[20]||(l[20]=e=>el.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"按键名称"},{default:_(()=>[s(d,{modelValue:el.button_title,"onUpdate:modelValue":l[21]||(l[21]=e=>el.button_title=e),placeholder:"如：加入群，学习更多副业知识"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"头像库类型"},{default:_(()=>[s(te,{modelValue:el.avatar_library,"onUpdate:modelValue":l[22]||(l[22]=e=>el.avatar_library=e),placeholder:"选择头像库",style:{width:"100%"}},{default:_(()=>[s(le,{label:"默认头像",value:"default"}),s(le,{label:"商务头像",value:"business"}),s(le,{label:"交友头像",value:"dating"}),s(le,{label:"征婚头像",value:"marriage"}),s(le,{label:"健身头像",value:"fitness"}),s(le,{label:"家庭头像",value:"family"}),s(le,{label:"扩列头像",value:"qq"}),s(le,{label:"综合头像",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(jl,{"content-position":"left"},{default:_(()=>l[52]||(l[52]=[c("内容区块配置",-1)])),_:1,__:[52]}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"群简介标题"},{default:_(()=>[s(d,{modelValue:el.group_intro_title,"onUpdate:modelValue":l[23]||(l[23]=e=>el.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"常见问题标题"},{default:_(()=>[s(d,{modelValue:el.faq_title,"onUpdate:modelValue":l[24]||(l[24]=e=>el.faq_title=e),placeholder:"如：常见问题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(o,{label:"群简介内容"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.group_intro_content,"onUpdate:modelValue":l[25]||(l[25]=e=>el.group_intro_content=e),placeholder:"输入群简介内容",rows:3},null,8,["modelValue"])]),_:1}),s(o,{label:"FAQ内容"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.faq_content,"onUpdate:modelValue":l[26]||(l[26]=e=>el.faq_content=e),placeholder:"格式：问题----答案（每行一个）",rows:4},null,8,["modelValue"])]),_:1}),s(o,{label:"用户评论"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.user_reviews,"onUpdate:modelValue":l[27]||(l[27]=e=>el.user_reviews=e),placeholder:"格式：用户名----评论内容----评分（每行一个）",rows:4},null,8,["modelValue"])]),_:1}),s(jl,{"content-position":"left"},{default:_(()=>l[53]||(l[53]=[c("素材配置",-1)])),_:1,__:[53]}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"客服二维码"},{default:_(()=>[s(d,{modelValue:el.customer_service_qr,"onUpdate:modelValue":l[28]||(l[28]=e=>el.customer_service_qr=e),placeholder:"客服二维码URL"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"广告图片"},{default:_(()=>[s(d,{modelValue:el.ad_image,"onUpdate:modelValue":l[29]||(l[29]=e=>el.ad_image=e),placeholder:"广告图片URL"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(Ul,{gutter:20},{default:_(()=>[s(kl,{span:12},{default:_(()=>[s(o,{label:"扩展区块1标题"},{default:_(()=>[s(d,{modelValue:el.extra_title1,"onUpdate:modelValue":l[30]||(l[30]=e=>el.extra_title1=e),placeholder:"扩展区块1标题"},null,8,["modelValue"])]),_:1})]),_:1}),s(kl,{span:12},{default:_(()=>[s(o,{label:"扩展区块2标题"},{default:_(()=>[s(d,{modelValue:el.extra_title2,"onUpdate:modelValue":l[31]||(l[31]=e=>el.extra_title2=e),placeholder:"扩展区块2标题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(o,{label:"扩展区块1内容"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.extra_content1,"onUpdate:modelValue":l[32]||(l[32]=e=>el.extra_content1=e),placeholder:"扩展区块1内容",rows:3},null,8,["modelValue"])]),_:1}),s(o,{label:"扩展区块2内容"},{default:_(()=>[s(d,{type:"textarea",modelValue:el.extra_content2,"onUpdate:modelValue":l[33]||(l[33]=e=>el.extra_content2=e),placeholder:"扩展区块2内容",rows:3},null,8,["modelValue"])]),_:1}),s(jl,{"content-position":"left"},{default:_(()=>l[54]||(l[54]=[c("自定义字段配置",-1)])),_:1,__:[54]}),r("div",Ie,[(i(!0),u(f,null,b(el.custom_fields_config,(e,a)=>(i(),u("div",{key:a,class:"custom-field-item"},[s(Ul,{gutter:10},{default:_(()=>[s(kl,{span:4},{default:_(()=>[s(d,{modelValue:e.key,"onUpdate:modelValue":l=>e.key=l,placeholder:"字段名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(kl,{span:3},{default:_(()=>[s(te,{modelValue:e.type,"onUpdate:modelValue":l=>e.type=l,placeholder:"类型"},{default:_(()=>[s(le,{label:"文本",value:"text"}),s(le,{label:"数字",value:"number"}),s(le,{label:"选择",value:"select"}),s(le,{label:"开关",value:"switch"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(kl,{span:4},{default:_(()=>[s(d,{modelValue:e.label,"onUpdate:modelValue":l=>e.label=l,placeholder:"显示名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(kl,{span:4},{default:_(()=>[s(d,{modelValue:e.default_value,"onUpdate:modelValue":l=>e.default_value=l,placeholder:"默认值"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(kl,{span:6},{default:_(()=>[s(d,{modelValue:e.placeholder,"onUpdate:modelValue":l=>e.placeholder=l,placeholder:"提示文本"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(kl,{span:3},{default:_(()=>[s(t,{type:"danger",size:"small",onClick:e=>function(e){el.custom_fields_config.splice(e,1)}(a)},{default:_(()=>l[55]||(l[55]=[c("删除",-1)])),_:2,__:[55]},1032,["onClick"])]),_:2},1024)]),_:2},1024)]))),128)),s(t,{type:"primary",size:"small",onClick:pl},{default:_(()=>l[56]||(l[56]=[c("添加自定义字段",-1)])),_:1,__:[56]})])]),_:1},8,["model"])),[[Sl,Ze.value]])]),_:1},8,["title","modelValue"]),s(ql,{title:"模板预览",modelValue:Xe.visible,"onUpdate:modelValue":l[38]||(l[38]=e=>Xe.visible=e),width:"600px"},{footer:_(()=>[s(t,{onClick:l[36]||(l[36]=e=>Xe.visible=!1)},{default:_(()=>l[60]||(l[60]=[c("关闭",-1)])),_:1,__:[60]}),s(t,{type:"primary",onClick:l[37]||(l[37]=e=>function(e){this.$router.push({path:"/community/groups/create",query:{template_id:e.id}})}(Xe.data))},{default:_(()=>l[61]||(l[61]=[c("使用模板",-1)])),_:1,__:[61]})]),default:_(()=>[n((i(),u("div",Le,[r("div",Me,[r("h3",null,B(Xe.data.template_name),1),r("p",null,B(Xe.data.description),1)]),r("div",Pe,[s(Bl,{column:2,border:""},{default:_(()=>[s(El,{label:"群名称"},{default:_(()=>[c(B(Xe.data.template_data?.title),1)]),_:1}),s(El,{label:"入群价格"},{default:_(()=>[c(" ¥"+B(Xe.data.template_data?.price),1)]),_:1}),s(El,{label:"成员上限"},{default:_(()=>[c(B(Xe.data.template_data?.member_limit),1)]),_:1}),s(El,{label:"虚拟成员"},{default:_(()=>[c(B(Xe.data.template_data?.virtual_members),1)]),_:1}),s(El,{label:"分类"},{default:_(()=>[c(B(Xe.data.category_name),1)]),_:1}),s(El,{label:"使用次数"},{default:_(()=>[c(B(Xe.data.usage_count),1)]),_:1})]),_:1}),Xe.data.template_data?.description?(i(),u("div",Qe,[l[59]||(l[59]=r("h4",null,"群描述",-1)),r("p",null,B(Xe.data.template_data.description),1)])):g("",!0)])])),[[Sl,Xe.loading]])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b70ea7f1"]]);export{Ye as default};
