<template>
  <el-dialog
    title="编辑群组"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
    class="group-edit-dialog"
  >
    <el-form :model="form" :rules="rules" ref="editForm" label-width="120px">
      <el-tabs v-model="activeTab" type="card">
        <!-- 基础信息 -->
        <el-tab-pane label="基础信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入群组名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组副标题" prop="subtitle">
                <el-input v-model="form.subtitle" placeholder="请输入群组副标题" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群组价格" prop="price">
            <el-input-number 
              v-model="form.price" 
              :min="0" 
              :precision="2"
              placeholder="请输入群组价格"
              style="width: 200px;"
            />
            <span style="margin-left: 10px;">元</span>
          </el-form-item>

          <el-form-item label="群组描述" prop="description">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入群组描述"
            />
          </el-form-item>

          <el-form-item label="群组介绍" prop="group_intro">
            <el-input 
              v-model="form.group_intro" 
              type="textarea" 
              :rows="4"
              placeholder="请输入详细的群组介绍"
            />
          </el-form-item>

          <el-form-item label="群组状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="active">正常</el-radio>
              <el-radio label="inactive">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="群组封面">
            <el-upload
              class="cover-uploader"
              :action="uploadCoverUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleCoverSuccess"
              :before-upload="beforeCoverUpload"
            >
              <img v-if="form.cover_image" :src="form.cover_image" class="cover-image">
              <i v-else class="el-icon-plus cover-uploader-icon"></i>
            </el-upload>
            <div class="upload-tip">建议尺寸：400x300像素，支持jpg/png格式，大小不超过2MB</div>
          </el-form-item>

          <el-form-item label="群二维码" prop="qr_code" required>
            <el-upload
              class="qr-uploader"
              :action="uploadQRUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleQRSuccess"
              :before-upload="beforeQRUpload"
            >
              <img v-if="form.qr_code" :src="form.qr_code" class="qr-image">
              <i v-else class="el-icon-plus qr-uploader-icon"></i>
            </el-upload>
            <div class="upload-tip">请上传微信群二维码，支持jpg/png格式</div>
          </el-form-item>
        </el-tab-pane>

        <!-- 营销设置 -->
        <el-tab-pane label="营销设置" name="marketing">
          <h3>虚拟数据展示</h3>
          <el-divider></el-divider>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="红包数量">
                <el-input v-model="form.red_packet_count" placeholder="如：10万+" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="点赞数">
                <el-input-number v-model="form.like_count" :min="0" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="消息数">
                <el-input-number v-model="form.message_count" :min="0" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="虚拟成员数">
                <el-input-number v-model="form.virtual_member_count" :min="0" style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="虚拟总收入">
                <el-input-number 
                  v-model="form.virtual_total_income" 
                  :min="0" 
                  :precision="2"
                  style="width: 100%;" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="虚拟订单数">
                <el-input-number v-model="form.virtual_order_count" :min="0" style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>

          <h3>显示设置</h3>
          <el-divider></el-divider>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="显示虚拟数据">
                <el-switch v-model="form.show_virtual_data"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="自动增长数据">
                <el-switch v-model="form.auto_increase_count"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示成员头像">
                <el-switch v-model="form.show_member_avatars"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="显示用户评价">
                <el-switch v-model="form.show_member_reviews"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示实时统计">
                <el-switch v-model="form.show_real_time_stats"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="自动城市替换">
                <el-switch v-model="form.auto_city_replace"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="营销标签">
            <el-input 
              v-model="form.marketing_tags" 
              placeholder="用逗号分隔多个标签，如：热门,推荐,限时优惠"
            />
          </el-form-item>
        </el-tab-pane>

        <!-- 内容管理 -->
        <el-tab-pane label="内容管理" name="content">
          <el-form-item label="快捷内容">
            <el-input 
              v-model="form.quick_content" 
              type="textarea" 
              :rows="4"
              placeholder="快捷回复内容，支持变量：{city}、{time}等"
            />
          </el-form-item>

          <el-form-item label="群友内容">
            <el-input 
              v-model="form.member_content" 
              type="textarea" 
              :rows="4"
              placeholder="群友展示内容，用于营销展示"
            />
          </el-form-item>

          <el-form-item label="广告内容">
            <el-input 
              v-model="form.ad_content" 
              type="textarea" 
              :rows="4"
              placeholder="广告推广内容"
            />
          </el-form-item>

          <el-form-item label="常见问题">
            <div class="faq-editor">
              <el-button size="small" @click="addFAQ" style="margin-bottom: 10px;">
                <i class="el-icon-plus"></i> 添加问题
              </el-button>
              <div v-for="(faq, index) in faqList" :key="index" class="faq-item">
                <el-row :gutter="10">
                  <el-col :span="10">
                    <el-input v-model="faq.question" placeholder="问题" />
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="faq.answer" placeholder="回答" />
                  </el-col>
                  <el-col :span="2">
                    <el-button size="small" type="danger" @click="removeFAQ(index)">删除</el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="用户评价">
            <div class="review-editor">
              <el-button size="small" @click="addReview" style="margin-bottom: 10px;">
                <i class="el-icon-plus"></i> 添加评价
              </el-button>
              <div v-for="(review, index) in reviewList" :key="index" class="review-item">
                <el-row :gutter="10">
                  <el-col :span="6">
                    <el-input v-model="review.username" placeholder="用户名" />
                  </el-col>
                  <el-col :span="4">
                    <el-rate v-model="review.rating" :max="5"></el-rate>
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="review.content" placeholder="评价内容" />
                  </el-col>
                  <el-col :span="2">
                    <el-button size="small" type="danger" @click="removeReview(index)">删除</el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form-item>
        </el-tab-pane>

        <!-- 高级设置 -->
        <el-tab-pane label="高级设置" name="advanced">
          <h3>访问控制</h3>
          <el-divider></el-divider>
          
          <el-form-item label="仅限微信浏览器">
            <el-switch v-model="form.wechat_browser_only"></el-switch>
            <div class="form-tip">开启后只能在微信内置浏览器中访问</div>
          </el-form-item>

          <el-form-item label="域名保护">
            <el-switch v-model="form.domain_protection"></el-switch>
            <div class="form-tip">开启后限制访问域名，防止恶意盗链</div>
          </el-form-item>

          <el-form-item label="错误时跳转">
            <el-switch v-model="form.redirect_on_error"></el-switch>
            <div class="form-tip">访问异常时自动跳转到主页</div>
          </el-form-item>

          <h3>客服设置</h3>
          <el-divider></el-divider>

          <el-form-item label="客服链接">
            <el-input v-model="form.customer_service_url" placeholder="客服网页链接" />
          </el-form-item>

          <el-form-item label="客服二维码">
            <el-upload
              class="service-qr-uploader"
              :action="uploadQRUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleServiceQRSuccess"
              :before-upload="beforeQRUpload"
            >
              <img v-if="form.customer_service_qr" :src="form.customer_service_qr" class="service-qr-image">
              <i v-else class="el-icon-plus service-qr-uploader-icon"></i>
            </el-upload>
            <div class="upload-tip">上传客服微信二维码</div>
          </el-form-item>

          <h3>自定义消息</h3>
          <el-divider></el-divider>

          <el-form-item label="欢迎消息">
            <el-input 
              v-model="form.welcome_message" 
              type="textarea" 
              :rows="3"
              placeholder="用户进入页面时显示的欢迎消息"
            />
          </el-form-item>

          <el-form-item label="群规说明">
            <el-input 
              v-model="form.group_rules" 
              type="textarea" 
              :rows="4"
              placeholder="群规则和注意事项"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'GroupEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    group: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'basic',
      saving: false,
      
      form: {
        title: '',
        subtitle: '',
        description: '',
        price: 0,
        cover_image: '',
        qr_code: '',
        group_intro: '',
        status: 'active',
        
        // 营销字段
        red_packet_count: '10万+',
        like_count: 324,
        message_count: 341,
        virtual_member_count: 500,
        virtual_total_income: 0,
        virtual_order_count: 100,
        
        // 显示设置
        show_virtual_data: true,
        auto_increase_count: false,
        show_member_avatars: true,
        show_member_reviews: true,
        show_real_time_stats: true,
        auto_city_replace: false,
        
        // 内容字段
        quick_content: '',
        member_content: '',
        ad_content: '',
        faq_content: '',
        member_reviews: '',
        marketing_tags: '',
        
        // 高级设置
        wechat_browser_only: false,
        domain_protection: false,
        redirect_on_error: false,
        customer_service_url: '',
        customer_service_qr: '',
        welcome_message: '',
        group_rules: '',
      },
      
      rules: {
        title: [
          { required: true, message: '请输入群组名称', trigger: 'blur' },
          { max: 100, message: '名称长度不能超过100个字符', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入群组价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
        ],
        qr_code: [
          { required: true, message: '请上传群二维码', trigger: 'blur' }
        ]
      },
      
      faqList: [],
      reviewList: [],
      
      uploadCoverUrl: '/api/owner/groups/upload-cover',
      uploadQRUrl: '/api/owner/groups/upload-qr',
      uploadHeaders: {
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      }
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal && this.group.id) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      // 初始化表单数据
      Object.keys(this.form).forEach(key => {
        if (this.group[key] !== undefined) {
          this.form[key] = this.group[key]
        }
      })
      
      // 解析FAQ数据
      try {
        this.faqList = this.group.faq_content ? JSON.parse(this.group.faq_content) : []
      } catch (e) {
        this.faqList = []
      }
      
      // 解析评价数据
      try {
        this.reviewList = this.group.member_reviews ? JSON.parse(this.group.member_reviews) : []
      } catch (e) {
        this.reviewList = []
      }
    },

    handleClose() {
      this.$emit('close')
    },

    async handleSave() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            // 序列化FAQ和评价数据
            this.form.faq_content = JSON.stringify(this.faqList)
            this.form.member_reviews = JSON.stringify(this.reviewList)
            
            await this.$api.put(`/owner/groups/${this.group.id}`, this.form)
            this.$message.success('群组更新成功')
            this.$emit('success')
          } catch (error) {
            this.$message.error('保存失败：' + (error.response?.data?.error || error.message))
          } finally {
            this.saving = false
          }
        }
      })
    },

    // 上传相关方法
    beforeCoverUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    handleCoverSuccess(response) {
      this.form.cover_image = response.url
      this.$message.success('封面上传成功')
    },

    beforeQRUpload(file) {
      return this.beforeCoverUpload(file)
    },

    handleQRSuccess(response) {
      this.form.qr_code = response.url
      this.$message.success('二维码上传成功')
    },

    handleServiceQRSuccess(response) {
      this.form.customer_service_qr = response.url
      this.$message.success('客服二维码上传成功')
    },

    // FAQ管理
    addFAQ() {
      this.faqList.push({
        question: '',
        answer: ''
      })
    },

    removeFAQ(index) {
      this.faqList.splice(index, 1)
    },

    // 评价管理
    addReview() {
      this.reviewList.push({
        username: '',
        rating: 5,
        content: ''
      })
    },

    removeReview(index) {
      this.reviewList.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.group-edit-dialog ::v-deep .el-dialog {
  max-height: 90vh;
  overflow-y: auto;
}

.cover-uploader, .qr-uploader, .service-qr-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cover-uploader {
  width: 200px;
  height: 150px;
}

.qr-uploader, .service-qr-uploader {
  width: 120px;
  height: 120px;
}

.cover-uploader:hover, .qr-uploader:hover, .service-qr-uploader:hover {
  border-color: #409eff;
}

.cover-uploader-icon, .qr-uploader-icon, .service-qr-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  line-height: 150px;
  text-align: center;
}

.qr-uploader-icon, .service-qr-uploader-icon {
  line-height: 120px;
}

.cover-image {
  width: 200px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.qr-image, .service-qr-image {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.faq-editor, .review-editor {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.faq-item, .review-item {
  margin-bottom: 10px;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>