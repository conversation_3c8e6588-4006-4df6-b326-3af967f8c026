<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\CommissionLog;
use App\Services\AnalyticsServiceMerged as AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 管理员控制器
 * 提供管理员专用的数据和功能接口
 */
class AdminController extends Controller
{
    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->middleware('auth:api');
        $this->middleware('role:admin');
        $this->analyticsService = $analyticsService;
    }

    /**
     * 管理员仪表板数据
     */
    public function dashboard(Request $request)
    {
        try {
            $cacheKey = 'admin_dashboard_' . now()->format('Y-m-d-H');
            
            $data = Cache::remember($cacheKey, 1800, function () {
                return [
                    'overview' => $this->getOverviewStats(),
                    'trends' => $this->getTrendData(),
                    'recent_activities' => $this->getRecentActivities(),
                    'system_status' => $this->getSystemStatus(),
                    'alerts' => $this->getSystemAlerts(),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => '仪表板数据获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '仪表板数据获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取概览统计数据
     */
    private function getOverviewStats(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        // 今日数据
        $todayStats = [
            'users' => User::whereDate('created_at', $today)->count(),
            'orders' => Order::whereDate('created_at', $today)->count(),
            'revenue' => Order::whereDate('created_at', $today)
                            ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'groups' => WechatGroup::whereDate('created_at', $today)->count(),
        ];

        // 昨日数据
        $yesterdayStats = [
            'users' => User::whereDate('created_at', $yesterday)->count(),
            'orders' => Order::whereDate('created_at', $yesterday)->count(),
            'revenue' => Order::whereDate('created_at', $yesterday)
                            ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'groups' => WechatGroup::whereDate('created_at', $yesterday)->count(),
        ];

        // 计算增长率
        $growthRates = [];
        foreach ($todayStats as $key => $todayValue) {
            $yesterdayValue = $yesterdayStats[$key] ?? 0;
            if ($yesterdayValue > 0) {
                $growthRates[$key] = round((($todayValue - $yesterdayValue) / $yesterdayValue) * 100, 2);
            } else {
                $growthRates[$key] = $todayValue > 0 ? 100 : 0;
            }
        }

        // 总计数据
        $totalStats = [
            'users' => User::count(),
            'orders' => Order::count(),
            'revenue' => Order::where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'groups' => WechatGroup::count(),
            'active_users' => User::where('status', User::STATUS_ACTIVE)->count(),
            'paid_orders' => Order::where('status', Order::STATUS_PAID_INT)->count(),
            'active_groups' => WechatGroup::where('status', 1)->count(),
            'total_commission' => CommissionLog::where('status', 'settled')->sum('amount'),
        ];

        return [
            'today' => $todayStats,
            'yesterday' => $yesterdayStats,
            'growth_rates' => $growthRates,
            'totals' => $totalStats,
        ];
    }

    /**
     * 获取趋势数据
     */
    private function getTrendData(): array
    {
        $days = 30;
        $endDate = Carbon::today();
        $startDate = $endDate->copy()->subDays($days - 1);

        // 用户注册趋势
        $userTrend = User::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 订单趋势
        $orderTrend = Order::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 群组创建趋势
        $groupTrend = WechatGroup::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 填充缺失日期
        $trends = [];
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            
            $trends[] = [
                'date' => $dateStr,
                'users' => $userTrend->get($dateStr)->count ?? 0,
                'orders' => $orderTrend->get($dateStr)->count ?? 0,
                'revenue' => $orderTrend->get($dateStr)->revenue ?? 0,
                'groups' => $groupTrend->get($dateStr)->count ?? 0,
            ];
            
            $currentDate->addDay();
        }

        return $trends;
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities(): array
    {
        $activities = [];

        // 最近注册用户
        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get(['id', 'username', 'nickname', 'role', 'created_at']);

        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_register',
                'title' => '新用户注册',
                'description' => "用户 {$user->nickname} ({$user->username}) 注册成功",
                'time' => $user->created_at,
                'icon' => 'user-plus',
                'color' => 'success',
            ];
        }

        // 最近订单
        $recentOrders = Order::with('user', 'wechatGroup')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order_created',
                'title' => '新订单创建',
                'description' => "订单 {$order->order_no} 金额 ¥{$order->amount}",
                'time' => $order->created_at,
                'icon' => 'shopping-cart',
                'color' => 'primary',
            ];
        }

        // 最近群组
        $recentGroups = WechatGroup::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentGroups as $group) {
            $activities[] = [
                'type' => 'group_created',
                'title' => '新群组创建',
                'description' => "群组 {$group->title} 创建成功",
                'time' => $group->created_at,
                'icon' => 'users',
                'color' => 'info',
            ];
        }

        // 按时间排序
        usort($activities, function ($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus(): array
    {
        return [
            'database' => $this->checkDatabaseStatus(),
            'cache' => $this->checkCacheStatus(),
            'storage' => $this->checkStorageStatus(),
            'queue' => $this->checkQueueStatus(),
            'performance' => $this->getPerformanceMetrics(),
        ];
    }

    /**
     * 检查数据库状态
     */
    private function checkDatabaseStatus(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'connections' => $this->getDatabaseConnections(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 检查缓存状态
     */
    private function checkCacheStatus(): array
    {
        try {
            $testKey = 'system_health_check_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);

            return [
                'status' => $retrieved === $testValue ? 'healthy' : 'error',
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 检查存储状态
     */
    private function checkStorageStatus(): array
    {
        $storagePath = storage_path();
        $totalSpace = disk_total_space($storagePath);
        $freeSpace = disk_free_space($storagePath);
        $usedSpace = $totalSpace - $freeSpace;
        $usagePercent = round(($usedSpace / $totalSpace) * 100, 2);

        return [
            'status' => $usagePercent < 90 ? 'healthy' : 'warning',
            'total_space' => $this->formatBytes($totalSpace),
            'free_space' => $this->formatBytes($freeSpace),
            'used_space' => $this->formatBytes($usedSpace),
            'usage_percent' => $usagePercent,
        ];
    }

    /**
     * 检查队列状态
     */
    private function checkQueueStatus(): array
    {
        try {
            // 这里可以检查队列连接状态
            return [
                'status' => 'healthy',
                'driver' => config('queue.default'),
                'pending_jobs' => 0, // 可以从队列驱动获取实际数据
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取性能指标
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->parseSize(ini_get('memory_limit')),
            ],
            'execution_time' => round(microtime(true) - LARAVEL_START, 4),
            'included_files' => count(get_included_files()),
        ];
    }

    /**
     * 获取系统警告
     */
    private function getSystemAlerts(): array
    {
        $alerts = [];

        // 检查磁盘空间
        $storageStatus = $this->checkStorageStatus();
        if ($storageStatus['usage_percent'] > 80) {
            $alerts[] = [
                'type' => 'warning',
                'title' => '磁盘空间不足',
                'message' => "磁盘使用率已达到 {$storageStatus['usage_percent']}%",
                'action' => '清理日志文件或扩展存储空间',
            ];
        }

        // 检查待处理订单
        $pendingOrders = Order::where('status', Order::STATUS_PENDING_INT)
            ->where('created_at', '<', now()->subHours(24))
            ->count();

        if ($pendingOrders > 0) {
            $alerts[] = [
                'type' => 'info',
                'title' => '待处理订单',
                'message' => "有 {$pendingOrders} 个订单超过24小时未支付",
                'action' => '检查订单状态或设置自动取消',
            ];
        }

        // 检查异常用户
        $suspiciousUsers = User::where('created_at', '>', now()->subDay())
            ->whereHas('orders', function ($query) {
                $query->where('amount', '>', 1000);
            })
            ->count();

        if ($suspiciousUsers > 0) {
            $alerts[] = [
                'type' => 'warning',
                'title' => '异常用户活动',
                'message' => "发现 {$suspiciousUsers} 个新用户有大额交易",
                'action' => '审查用户活动和交易记录',
            ];
        }

        return $alerts;
    }

    /**
     * 获取数据库连接数
     */
    private function getDatabaseConnections(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return $result[0]->Value ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 解析内存大小
     */
    private function parseSize(string $size): int
    {
        $unit = strtolower(substr($size, -1));
        $value = (int) $size;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * 获取管理员操作统计
     */
    public function getOperationStats(Request $request)
    {
        try {
            $period = $request->input('period', '7d');
            $startDate = match($period) {
                '1d' => now()->startOfDay(),
                '7d' => now()->subDays(7),
                '30d' => now()->subDays(30),
                '90d' => now()->subDays(90),
                default => now()->subDays(7)
            };

            $stats = [
                'user_operations' => [
                    'created' => User::where('created_at', '>=', $startDate)->count(),
                    'updated' => User::where('updated_at', '>=', $startDate)
                                   ->where('updated_at', '!=', DB::raw('created_at'))->count(),
                    'deleted' => User::onlyTrashed()->where('deleted_at', '>=', $startDate)->count(),
                ],
                'order_operations' => [
                    'created' => Order::where('created_at', '>=', $startDate)->count(),
                    'paid' => Order::where('paid_at', '>=', $startDate)->count(),
                    'refunded' => Order::where('status', Order::STATUS_REFUNDED_INT)
                                      ->where('updated_at', '>=', $startDate)->count(),
                ],
                'group_operations' => [
                    'created' => WechatGroup::where('created_at', '>=', $startDate)->count(),
                    'updated' => WechatGroup::where('updated_at', '>=', $startDate)
                                           ->where('updated_at', '!=', DB::raw('created_at'))->count(),
                ],
                'system_health' => $this->getSystemStatus(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 清理系统缓存
     */
    public function clearCache(Request $request)
    {
        try {
            $cacheTypes = $request->input('types', ['all']);
            $cleared = [];

            foreach ($cacheTypes as $type) {
                switch ($type) {
                    case 'config':
                        \Artisan::call('config:clear');
                        $cleared[] = '配置缓存';
                        break;
                    case 'route':
                        \Artisan::call('route:clear');
                        $cleared[] = '路由缓存';
                        break;
                    case 'view':
                        \Artisan::call('view:clear');
                        $cleared[] = '视图缓存';
                        break;
                    case 'cache':
                        \Artisan::call('cache:clear');
                        $cleared[] = '应用缓存';
                        break;
                    case 'all':
                    default:
                        \Artisan::call('optimize:clear');
                        $cleared[] = '所有缓存';
                        break;
                }
            }

            return response()->json([
                'success' => true,
                'message' => '缓存清理成功',
                'data' => [
                    'cleared' => $cleared,
                    'timestamp' => now()->toDateTimeString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '缓存清理失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 系统优化
     */
    public function optimizeSystem(Request $request)
    {
        try {
            $operations = [];

            // 优化配置
            \Artisan::call('config:cache');
            $operations[] = '配置缓存优化';

            // 优化路由
            \Artisan::call('route:cache');
            $operations[] = '路由缓存优化';

            // 优化视图
            \Artisan::call('view:cache');
            $operations[] = '视图缓存优化';

            // 优化自动加载
            \Artisan::call('optimize');
            $operations[] = '自动加载优化';

            return response()->json([
                'success' => true,
                'message' => '系统优化完成',
                'data' => [
                    'operations' => $operations,
                    'timestamp' => now()->toDateTimeString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统优化失败: ' . $e->getMessage(),
            ], 500);
        }
    }
}