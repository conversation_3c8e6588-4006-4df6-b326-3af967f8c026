<template>
  <div 
    class="stat-card" 
    :class="[`stat-card--${color}`, { 'stat-card--clickable': clickable }]"
    @click="handleClick"
  >
    <!-- 背景装饰 -->
    <div class="stat-card__bg">
      <div class="bg-pattern"></div>
      <div class="bg-gradient"></div>
    </div>

    <!-- 卡片内容 -->
    <div class="stat-card__content">
      <!-- 图标区域 -->
      <div class="stat-card__icon">
        <div class="icon-wrapper">
          <el-icon>
            <component :is="icon" />
          </el-icon>
        </div>
      </div>

      <!-- 数据区域 -->
      <div class="stat-card__data">
        <div class="stat-value">
          <span class="value-number">{{ formattedValue }}</span>
          <span v-if="unit" class="value-unit">{{ unit }}</span>
        </div>
        <div class="stat-title">{{ title }}</div>
      </div>

      <!-- 趋势指示器 -->
      <div v-if="change !== undefined" class="stat-card__trend">
        <div class="trend-indicator" :class="trendClass">
          <el-icon class="trend-icon">
            <CaretTop v-if="trend === 'up'" />
            <CaretBottom v-if="trend === 'down'" />
            <Minus v-if="trend === 'flat'" />
          </el-icon>
          <span class="trend-value">{{ Math.abs(change) }}%</span>
        </div>
        <div class="trend-label">较昨日</div>
      </div>
    </div>

    <!-- 悬停效果 -->
    <div class="stat-card__hover-effect"></div>

    <!-- 点击波纹效果 -->
    <div class="stat-card__ripple" ref="rippleRef"></div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { CaretTop, CaretBottom, Minus } from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  change: {
    type: Number,
    default: undefined
  },
  trend: {
    type: String,
    default: 'flat',
    validator: (value) => ['up', 'down', 'flat'].includes(value)
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'info', 'danger'].includes(value)
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const rippleRef = ref(null)

// 计算属性
const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    if (props.value >= 10000) {
      return (props.value / 10000).toFixed(1) + 'W'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'K'
    }
    return props.value.toLocaleString()
  }
  return props.value
})

const trendClass = computed(() => {
  return {
    'trend--up': props.trend === 'up',
    'trend--down': props.trend === 'down',
    'trend--flat': props.trend === 'flat'
  }
})

// 方法
const handleClick = (event) => {
  if (!props.clickable) return

  // 创建波纹效果
  createRipple(event)
  
  emit('click')
}

const createRipple = (event) => {
  const ripple = rippleRef.value
  if (!ripple) return

  const rect = event.currentTarget.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2

  ripple.style.width = ripple.style.height = size + 'px'
  ripple.style.left = x + 'px'
  ripple.style.top = y + 'px'
  ripple.classList.add('ripple-active')

  setTimeout(() => {
    ripple.classList.remove('ripple-active')
  }, 600)
}
</script>

<style lang="scss" scoped>
.stat-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 140px;

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);

    .stat-card__hover-effect {
      opacity: 1;
    }

    .stat-card__icon .icon-wrapper {
      transform: scale(1.1) rotate(5deg);
    }

    .bg-pattern {
      transform: scale(1.1) rotate(10deg);
    }
  }

  &--clickable {
    cursor: pointer;

    &:active {
      transform: translateY(-4px) scale(0.98);
    }
  }

  // 颜色主题
  &--primary {
    .stat-card__icon .icon-wrapper {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    }

    .bg-gradient {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
    }

    &::before {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    }
  }

  &--success {
    .stat-card__icon .icon-wrapper {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
    }

    .bg-gradient {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    }

    &::before {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
  }

  &--warning {
    .stat-card__icon .icon-wrapper {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
    }

    .bg-gradient {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    }

    &::before {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
  }

  &--info {
    .stat-card__icon .icon-wrapper {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
    }

    .bg-gradient {
      background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%);
    }

    &::before {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
  }

  &--danger {
    .stat-card__icon .icon-wrapper {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
    }

    .bg-gradient {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    }

    &::before {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
  }

  // 顶部装饰条
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    opacity: 0.8;
    z-index: 2;
  }

  // 背景装饰
  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .bg-pattern {
      position: absolute;
      top: -20px;
      right: -20px;
      width: 80px;
      height: 80px;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      transition: all 0.4s ease;
    }

    .bg-gradient {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.6;
    }
  }

  // 卡片内容
  &__content {
    position: relative;
    z-index: 3;
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  // 图标区域
  &__icon {
    .icon-wrapper {
      width: 48px;
      height: 48px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: inherit;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::after {
        opacity: 1;
      }
    }
  }

  // 数据区域
  &__data {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 16px 0;

    .stat-value {
      display: flex;
      align-items: baseline;
      margin-bottom: 8px;

      .value-number {
        font-size: 32px;
        font-weight: 700;
        color: #1e293b;
        line-height: 1;
        transition: all 0.3s ease;
      }

      .value-unit {
        font-size: 16px;
        font-weight: 600;
        color: #64748b;
        margin-left: 4px;
      }
    }

    .stat-title {
      font-size: 14px;
      font-weight: 600;
      color: #64748b;
      line-height: 1.2;
    }
  }

  // 趋势指示器
  &__trend {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .trend-indicator {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 600;

      .trend-icon {
        margin-right: 4px;
        font-size: 14px;
      }

      &.trend--up {
        background: rgba(16, 185, 129, 0.1);
        color: #10b981;
      }

      &.trend--down {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
      }

      &.trend--flat {
        background: rgba(100, 116, 139, 0.1);
        color: #64748b;
      }
    }

    .trend-label {
      font-size: 12px;
      color: #94a3b8;
      font-weight: 500;
    }
  }

  // 悬停效果
  &__hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
  }

  // 波纹效果
  &__ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    z-index: 4;
    pointer-events: none;

    &.ripple-active {
      animation: ripple 0.6s ease-out;
    }
  }
}

// 波纹动画
@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stat-card {
    height: 120px;

    &__content {
      padding: 20px;
    }

    &__icon .icon-wrapper {
      width: 40px;
      height: 40px;
      font-size: 18px;
    }

    &__data {
      margin: 12px 0;

      .stat-value .value-number {
        font-size: 24px;
      }

      .stat-title {
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .stat-card {
    height: 100px;

    &__content {
      padding: 16px;
    }

    &__icon .icon-wrapper {
      width: 36px;
      height: 36px;
      font-size: 16px;
    }

    &__data {
      margin: 8px 0;

      .stat-value .value-number {
        font-size: 20px;
      }

      .stat-title {
        font-size: 12px;
      }
    }

    &__trend {
      .trend-indicator {
        padding: 2px 6px;
        font-size: 10px;

        .trend-icon {
          font-size: 12px;
        }
      }

      .trend-label {
        font-size: 10px;
      }
    }
  }
}
</style>