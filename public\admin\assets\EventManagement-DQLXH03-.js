import{_ as e}from"./index-DtXAftX0.js";/* empty css                 *//* empty css               *//* empty css                *//* empty css               *//* empty css                    */import{bp as a,bq as t,aM as l,bm as s,bn as n,by as i,aZ as o,a_ as u,br as d,bw as m,at as r,ay as p,Q as c,bj as _,bg as f,aY as v,a$ as g,U as b,T as y,b6 as h,ao as V,ai as j,bb as w,az as x,aT as k,aB as D,aC as T,bS as U,ab as C,bQ as O,bz as Z,aa as z,R as $}from"./element-plus-h2SQQM64.js";import{P as S}from"./PageLayout-C6qH3ReN.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                       *//* empty css                       *//* empty css                        */import{r as Y,c as q,L as E,d as I,y as J,l as M,z as N,A as B,E as L,D as P,t as R,e as A,k as F,B as H,F as Q,Y as G,u as K}from"./vue-vendor-Dy164gUc.js";import{f as W}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";const X={class:"dialog-footer"},ee=e({__name:"EventEditDialog",props:{modelValue:Boolean,eventData:{type:Object,default:null}},emits:["update:modelValue","success"],setup(e,{emit:_}){const f=e,v=_,g=Y(null),b=Y(!1),y=Y(!1),h=q(()=>!!f.eventData),V=E({name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""}),j={name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],type:[{required:!0,message:"请选择活动类型",trigger:"change"}],time_range:[{required:!0,message:"请选择活动时间",trigger:"change"}]};I(()=>f.eventData,e=>{if(e){const a=JSON.parse(JSON.stringify(e));a.time_range=[a.start_time,a.end_time],Object.assign(V,a)}else Object.assign(V,{name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""})});const w=()=>{g.value.resetFields(),Object.assign(V,{name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""})},x=()=>{g.value.validate(e=>{if(e){y.value=!0;const e={...V,start_time:V.time_range[0],end_time:V.time_range[1]};delete e.time_range,setTimeout(()=>{y.value=!1,c.success(h.value?"活动更新成功":"活动创建成功"),v("success",e),v("update:modelValue",!1)},500)}})};return(c,_)=>{const f=l,v=t,k=n,D=s,T=i,U=d,C=u,O=o,Z=a,z=r,$=p,S=m;return M(),J($,{"model-value":e.modelValue,title:h.value?"编辑活动":"创建新活动",width:"700px","onUpdate:modelValue":_[8]||(_[8]=e=>c.$emit("update:modelValue",e)),onClose:w},{footer:N(()=>[R("span",X,[L(z,{onClick:_[7]||(_[7]=e=>c.$emit("update:modelValue",!1))},{default:N(()=>_[12]||(_[12]=[P("取消",-1)])),_:1,__:[12]}),L(z,{type:"primary",onClick:x,loading:y.value},{default:N(()=>_[13]||(_[13]=[P(" 确定 ",-1)])),_:1,__:[13]},8,["loading"])])]),default:N(()=>[B((M(),J(Z,{ref_key:"formRef",ref:g,model:V,rules:j,"label-width":"100px"},{default:N(()=>[L(v,{label:"活动名称",prop:"name"},{default:N(()=>[L(f,{modelValue:V.name,"onUpdate:modelValue":_[0]||(_[0]=e=>V.name=e),placeholder:"请输入活动名称"},null,8,["modelValue"])]),_:1}),L(v,{label:"活动类型",prop:"type"},{default:N(()=>[L(D,{modelValue:V.type,"onUpdate:modelValue":_[1]||(_[1]=e=>V.type=e)},{default:N(()=>[L(k,{label:"online"},{default:N(()=>_[9]||(_[9]=[P("线上活动",-1)])),_:1,__:[9]}),L(k,{label:"offline"},{default:N(()=>_[10]||(_[10]=[P("线下活动",-1)])),_:1,__:[10]}),L(k,{label:"check-in"},{default:N(()=>_[11]||(_[11]=[P("打卡挑战",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),L(v,{label:"活动时间",prop:"time_range"},{default:N(()=>[L(T,{modelValue:V.time_range,"onUpdate:modelValue":_[2]||(_[2]=e=>V.time_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"100%"},"value-format":"YYYY-MM-DDTHH:mm:ssZ"},null,8,["modelValue"])]),_:1}),L(O,{gutter:20},{default:N(()=>[L(C,{span:12},{default:N(()=>[L(v,{label:"参与费用",prop:"fee"},{default:N(()=>[L(U,{modelValue:V.fee,"onUpdate:modelValue":_[3]||(_[3]=e=>V.fee=e),precision:2,min:0,placeholder:"0表示免费",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),L(C,{span:12},{default:N(()=>[L(v,{label:"人数限制",prop:"max_participants"},{default:N(()=>[L(U,{modelValue:V.max_participants,"onUpdate:modelValue":_[4]||(_[4]=e=>V.max_participants=e),min:0,placeholder:"0表示不限制",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),L(v,{label:"活动封面",prop:"cover_image"},{default:N(()=>[L(f,{modelValue:V.cover_image,"onUpdate:modelValue":_[5]||(_[5]=e=>V.cover_image=e),placeholder:"请输入封面图片URL"},null,8,["modelValue"])]),_:1}),L(v,{label:"活动详情",prop:"description"},{default:N(()=>[L(f,{type:"textarea",rows:4,modelValue:V.description,"onUpdate:modelValue":_[6]||(_[6]=e=>V.description=e),placeholder:"请输入详细的活动介绍..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[S,b.value]])]),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-f63857e5"]]),ae={class:"app-container"},te={class:"page-header"},le={class:"toolbar-container"},se={class:"event-list-container"},ne={class:"event-image-wrapper"},ie=["src"],oe={class:"event-info"},ue={class:"event-title"},de={class:"event-time"},me={class:"event-stats"},re={class:"event-actions"},pe=e({__name:"EventManagement",setup(e){const a=Y(!1),t=Y(!1),l=Y("all"),s=Y([]),n=Y(null),i=q(()=>{const e=new Date;return"all"===l.value?s.value:s.value.filter(a=>{const t=new Date(a.start_time),s=new Date(a.end_time);return"ongoing"===l.value?t<=e&&s>=e:"upcoming"===l.value?t>e:"finished"===l.value&&s<e})}),d=e=>{const a=new Date,t=new Date(e.start_time),l=new Date(e.end_time);return l<a?{text:"已结束",type:"info"}:t<=a&&l>=a?{text:"进行中",type:"success"}:t>a?{text:"未开始",type:"warning"}:{text:"未知",type:"info"}},m=()=>{a.value=!0,setTimeout(()=>{s.value=[{id:1,name:"AI技术应用线上分享会",type:"online",start_time:"2024-06-10T19:00:00Z",end_time:"2024-06-10T21:00:00Z",participants_count:88,max_participants:200,fee:0,cover_image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500",status:"ongoing"},{id:2,name:"产品经理线下交流沙龙",type:"offline",start_time:"2024-06-15T14:00:00Z",end_time:"2024-06-15T17:00:00Z",participants_count:45,max_participants:50,fee:99,cover_image:"https://images.unsplash.com/photo-1556761175-5973dc0f32e7?w=500",status:"upcoming"},{id:3,name:"30天健身打卡挑战",type:"check-in",start_time:"2024-05-01T00:00:00Z",end_time:"2024-05-30T23:59:59Z",participants_count:152,max_participants:200,fee:19.9,cover_image:"https://images.unsplash.com/photo-1541534401786-20772b48a48c?w=500",status:"finished"}],a.value=!1},500)},p=()=>{},E=()=>{n.value=null,t.value=!0},I=e=>{const{action:a,event:t}=e;"poster"===a?c.success(`正在为活动 "${t.name}" 生成推广海报...`):"copy"===a?(c.success(`活动 "${t.name}" 已复制`),m()):"delete"===a&&$.confirm(`确定要删除活动 "${t.name}" 吗？`,"警告",{type:"warning"}).then(()=>{s.value=s.value.filter(e=>e.id!==t.id),c.success("活动删除成功")}).catch(()=>{})},B=()=>{m()};return A(()=>{m()}),(e,a)=>{const s=y,m=r,$=f,Y=_,q=g,A=w,X=T,pe=D,ce=x,_e=v,fe=u,ve=o,ge=Z;return M(),F("div",ae,[L(S,null,{header:N(()=>[R("div",te,[R("h1",null,[L(s,null,{default:N(()=>[L(K(z))]),_:1}),a[2]||(a[2]=P(" 活动与营销管理 ",-1))]),a[3]||(a[3]=R("p",null,"创建、管理并推广您的社群活动，提升用户参与度和活跃度。",-1))])]),default:N(()=>[R("div",le,[L(m,{type:"primary",icon:"Plus",onClick:E},{default:N(()=>a[4]||(a[4]=[P(" 创建新活动 ",-1)])),_:1,__:[4]}),L(Y,{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),onTabClick:p,class:"event-tabs"},{default:N(()=>[L($,{label:"全部活动",name:"all"}),L($,{label:"进行中",name:"ongoing"}),L($,{label:"未开始",name:"upcoming"}),L($,{label:"已结束",name:"finished"})]),_:1},8,["modelValue"])]),R("div",se,[L(ve,{gutter:20},{default:N(()=>[(M(!0),F(Q,null,G(i.value,e=>(M(),J(fe,{span:8,key:e.id,class:"event-col"},{default:N(()=>[L(_e,{class:"event-card",shadow:"hover"},{default:N(()=>[R("div",ne,[R("img",{src:e.cover_image,class:"event-image"},null,8,ie),L(q,{class:"event-status-tag",type:d(e).type},{default:N(()=>[P(b(d(e).text),1)]),_:2},1032,["type"])]),R("div",oe,[R("h3",ue,b(e.name),1),R("p",de,[L(s,null,{default:N(()=>[L(K(h))]),_:1}),P(" "+b(K(W)(e.start_time))+" - "+b(K(W)(e.end_time)),1)]),R("div",me,[R("span",null,[L(s,null,{default:N(()=>[L(K(V))]),_:1}),P(" "+b(e.participants_count)+" / "+b(e.max_participants)+" 人",1)]),R("span",null,[L(s,null,{default:N(()=>[L(K(j))]),_:1}),P(" ¥"+b(e.fee),1)])])]),R("div",re,[L(A,null,{default:N(()=>[L(m,{type:"primary",size:"small",onClick:a=>(e=>{n.value=JSON.parse(JSON.stringify(e)),t.value=!0})(e)},{default:N(()=>a[5]||(a[5]=[P("编辑",-1)])),_:2,__:[5]},1032,["onClick"]),L(m,{type:"success",size:"small",onClick:a=>(e=>{c.info(`查看活动 "${e.name}" 的数据分析...`)})(e)},{default:N(()=>a[6]||(a[6]=[P("数据",-1)])),_:2,__:[6]},1032,["onClick"])]),_:2},1024),L(ce,{onCommand:I},{dropdown:N(()=>[L(pe,null,{default:N(()=>[L(X,{command:{action:"poster",event:e}},{default:N(()=>[L(s,null,{default:N(()=>[L(K(U))]),_:1}),a[8]||(a[8]=P("生成海报 ",-1))]),_:2,__:[8]},1032,["command"]),L(X,{command:{action:"copy",event:e}},{default:N(()=>[L(s,null,{default:N(()=>[L(K(C))]),_:1}),a[9]||(a[9]=P("复制活动 ",-1))]),_:2,__:[9]},1032,["command"]),L(X,{command:{action:"delete",event:e},divided:""},{default:N(()=>[L(s,null,{default:N(()=>[L(K(O))]),_:1}),a[10]||(a[10]=P("删除活动 ",-1))]),_:2,__:[10]},1032,["command"])]),_:2},1024)]),default:N(()=>[L(m,{size:"small"},{default:N(()=>[a[7]||(a[7]=P(" 更多",-1)),L(s,{class:"el-icon--right"},{default:N(()=>[L(K(k))]),_:1})]),_:1,__:[7]})]),_:2},1024)])]),_:2},1024)]),_:2},1024))),128))]),_:1}),0===i.value.length?(M(),J(ge,{key:0,description:"暂无活动"})):H("",!0)])]),_:1}),L(ee,{modelValue:t.value,"onUpdate:modelValue":a[1]||(a[1]=e=>t.value=e),"event-data":n.value,onSuccess:B},null,8,["modelValue","event-data"])])}}},[["__scopeId","data-v-2fe96726"]]);export{pe as default};
