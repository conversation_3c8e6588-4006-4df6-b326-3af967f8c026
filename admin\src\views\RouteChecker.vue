<template>
  <div class="route-checker">
    <div class="page-header">
      <h1>🔍 路由检测工具</h1>
      <p>自动化检测所有路由的可访问性和组件加载状态</p>
    </div>

    <div class="checker-controls">
      <el-button 
        type="primary" 
        @click="runCheck" 
        :loading="checking"
        size="large"
      >
        <el-icon><Search /></el-icon>
        开始检测
      </el-button>
      
      <el-button 
        v-if="results" 
        @click="downloadReport"
        size="large"
      >
        <el-icon><Download /></el-icon>
        下载报告
      </el-button>
    </div>

    <!-- 检测进度 -->
    <div v-if="checking" class="progress-section">
      <el-progress 
        :percentage="progress" 
        :status="progress === 100 ? 'success' : ''"
      />
      <p class="progress-text">正在检测路由... {{ currentRoute }}</p>
    </div>

    <!-- 检测结果摘要 -->
    <div v-if="results && !checking" class="results-summary">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-number">{{ results.summary.total }}</div>
              <div class="summary-label">总路由数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card success">
            <div class="summary-item">
              <div class="summary-number">{{ results.summary.success }}</div>
              <div class="summary-label">成功加载</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card error">
            <div class="summary-item">
              <div class="summary-number">{{ results.summary.errors }}</div>
              <div class="summary-label">加载失败</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-number">{{ results.summary.successRate }}%</div>
              <div class="summary-label">成功率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细结果 -->
    <div v-if="results && !checking" class="results-detail">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="所有路由" name="all">
          <el-table :data="results.results" style="width: 100%">
            <el-table-column prop="path" label="路由路径" width="300" />
            <el-table-column prop="name" label="路由名称" width="200" />
            <el-table-column label="状态" width="120">
              <template #default="scope">
                <el-tag 
                  :type="getStatusType(scope.row.status)"
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="loadTime" label="加载时间(ms)" width="120" />
            <el-table-column label="错误信息">
              <template #default="scope">
                <span v-if="scope.row.error" class="error-text">
                  {{ scope.row.error }}
                </span>
                <span v-else class="success-text">无</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="失败路由" name="failed">
          <el-table :data="failedRoutes" style="width: 100%">
            <el-table-column prop="path" label="路由路径" width="300" />
            <el-table-column prop="name" label="路由名称" width="200" />
            <el-table-column label="状态" width="120">
              <template #default="scope">
                <el-tag type="danger" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="错误信息">
              <template #default="scope">
                <span class="error-text">{{ scope.row.error }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import { routeChecker } from '@/utils/routeChecker'

// 响应式数据
const checking = ref(false)
const progress = ref(0)
const currentRoute = ref('')
const results = ref(null)
const activeTab = ref('all')

// 计算属性
const failedRoutes = computed(() => {
  if (!results.value) return []
  return results.value.results.filter(r => r.status !== 'success')
})

// 方法
const runCheck = async () => {
  checking.value = true
  progress.value = 0
  currentRoute.value = ''
  
  try {
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += Math.random() * 10
      }
    }, 200)
    
    const checkResults = await routeChecker.checkAllRoutes()
    
    clearInterval(progressInterval)
    progress.value = 100
    
    results.value = checkResults
    
    ElMessage.success(`检测完成！成功: ${checkResults.summary.success}, 失败: ${checkResults.summary.errors}`)
    
  } catch (error) {
    ElMessage.error('检测过程中发生错误: ' + error.message)
  } finally {
    checking.value = false
  }
}

const downloadReport = () => {
  if (!results.value) return
  
  const report = routeChecker.generateReport()
  const blob = new Blob([report], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `route-check-report-${new Date().toISOString().slice(0, 10)}.md`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('报告已下载')
}

const getStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'component_error': return 'danger'
    case 'no_component': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'success': return '成功'
    case 'component_error': return '组件错误'
    case 'no_component': return '无组件'
    case 'error': return '错误'
    default: return '未知'
  }
}
</script>

<style lang="scss" scoped>
.route-checker {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
  
  h1 {
    font-size: 28px;
    margin-bottom: 10px;
    color: #303133;
  }
  
  p {
    color: #606266;
    font-size: 16px;
  }
}

.checker-controls {
  text-align: center;
  margin-bottom: 30px;
  
  .el-button {
    margin: 0 10px;
  }
}

.progress-section {
  margin-bottom: 30px;
  
  .progress-text {
    text-align: center;
    margin-top: 10px;
    color: #606266;
  }
}

.results-summary {
  margin-bottom: 30px;
  
  .summary-card {
    text-align: center;
    
    &.success {
      border-color: #67c23a;
    }
    
    &.error {
      border-color: #f56c6c;
    }
  }
  
  .summary-item {
    .summary-number {
      font-size: 32px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 5px;
    }
    
    .summary-label {
      color: #606266;
      font-size: 14px;
    }
  }
}

.results-detail {
  .error-text {
    color: #f56c6c;
    font-size: 12px;
  }
  
  .success-text {
    color: #67c23a;
  }
}
</style>
