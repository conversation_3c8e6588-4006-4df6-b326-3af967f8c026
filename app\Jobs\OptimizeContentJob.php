<?php

namespace App\Jobs;

use App\Models\WechatGroup;
use App\Services\ContentServiceMerged;
use App\Services\ContentOptimizationService;
use App\Events\ContentOptimizationCompleted;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 内容优化任务
 * 执行自动化内容优化操作
 */
class OptimizeContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5分钟超时
    public int $tries = 3; // 最多重试3次
    public int $backoff = 60; // 重试间隔60秒

    public function __construct(
        public readonly array $task
    ) {}

    /**
     * 执行任务
     */
    public function handle(
        ContentServiceMerged $advancedService,
        ContentOptimizationService $optimizationService
    ): void {
        $startTime = microtime(true);
        
        try {
            Log::info('开始执行内容优化任务', [
                'task_id' => $this->task['task_id'],
                'group_id' => $this->task['group_id'] ?? null,
                'optimization_types' => $this->task['optimization_types'] ?? [],
            ]);

            // 获取群组
            $group = WechatGroup::find($this->task['group_id']);
            if (!$group) {
                throw new Exception('群组不存在: ' . $this->task['group_id']);
            }

            // 执行优化
            $results = $this->executeOptimization($group, $advancedService, $optimizationService);
            
            // 记录优化结果
            $this->recordOptimizationResults($group, $results);
            
            // 计算执行时间
            $executionTime = microtime(true) - $startTime;
            
            // 触发完成事件
            event(new ContentOptimizationCompleted($group, $results, $executionTime));
            
            Log::info('内容优化任务完成', [
                'task_id' => $this->task['task_id'],
                'group_id' => $group->id,
                'execution_time' => round($executionTime, 2),
                'improvements' => $results['summary'] ?? [],
            ]);

        } catch (Exception $e) {
            $this->handleOptimizationFailure($e);
            throw $e; // 重新抛出异常以触发重试机制
        }
    }

    /**
     * 执行优化操作
     */
    private function executeOptimization(
        WechatGroup $group,
        ContentServiceMerged $advancedService,
        ContentOptimizationService $optimizationService
    ): array {
        $results = [
            'group_id' => $group->id,
            'optimizations_applied' => [],
            'before_scores' => [],
            'after_scores' => [],
            'summary' => [],
        ];

        // 获取优化前的质量分数
        $beforeAnalysis = $advancedService->autoOptimizeContent($group);
        $results['before_scores'] = $beforeAnalysis;

        $optimizationTypes = $this->task['optimization_types'] ?? ['title', 'description', 'price', 'seo'];

        foreach ($optimizationTypes as $type) {
            try {
                $optimization = $this->executeSpecificOptimization($group, $type, $advancedService, $optimizationService);
                $results['optimizations_applied'][] = $optimization;
                
                Log::debug('优化类型完成', [
                    'group_id' => $group->id,
                    'optimization_type' => $type,
                    'success' => $optimization['success'],
                ]);
                
            } catch (Exception $e) {
                Log::warning('优化类型失败', [
                    'group_id' => $group->id,
                    'optimization_type' => $type,
                    'error' => $e->getMessage(),
                ]);
                
                $results['optimizations_applied'][] = [
                    'type' => $type,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        // 获取优化后的质量分数
        $afterAnalysis = $advancedService->autoOptimizeContent($group);
        $results['after_scores'] = $afterAnalysis;

        // 生成优化摘要
        $results['summary'] = $this->generateOptimizationSummary($results);

        return $results;
    }

    /**
     * 执行特定类型的优化
     */
    private function executeSpecificOptimization(
        WechatGroup $group,
        string $type,
        ContentServiceMerged $advancedService,
        ContentOptimizationService $optimizationService
    ): array {
        return match ($type) {
            'title' => $this->optimizeTitle($group, $advancedService),
            'description' => $this->optimizeDescription($group, $advancedService),
            'price' => $this->optimizePrice($group, $optimizationService),
            'seo' => $this->optimizeSEO($group, $optimizationService),
            'tags' => $this->optimizeTags($group, $advancedService),
            'images' => $this->optimizeImages($group),
            default => throw new Exception('不支持的优化类型: ' . $type),
        };
    }

    /**
     * 优化标题
     */
    private function optimizeTitle(WechatGroup $group, ContentServiceMerged $service): array
    {
        $originalTitle = $group->title;
        
        // 使用AI服务优化标题
        $optimization = $service->autoOptimizeContent($group);
        
        if (isset($optimization['optimizations']['title']) && $optimization['auto_apply_safe']) {
            $optimizedTitle = $optimization['optimizations']['title']['optimized_title'];
            
            // 应用优化
            $group->update(['title' => $optimizedTitle]);
            
            return [
                'type' => 'title',
                'success' => true,
                'original_value' => $originalTitle,
                'optimized_value' => $optimizedTitle,
                'improvement_score' => $optimization['optimizations']['title']['improvement_score'] ?? 0,
                'confidence' => $optimization['optimizations']['title']['confidence'] ?? 0,
            ];
        }

        return [
            'type' => 'title',
            'success' => false,
            'reason' => '自动优化不安全或无改进建议',
        ];
    }

    /**
     * 优化描述
     */
    private function optimizeDescription(WechatGroup $group, ContentServiceMerged $service): array
    {
        $originalDescription = $group->description;
        
        $optimization = $service->autoOptimizeContent($group);
        
        if (isset($optimization['optimizations']['description']) && $optimization['auto_apply_safe']) {
            $optimizedDescription = $optimization['optimizations']['description']['optimized_description'];
            
            $group->update(['description' => $optimizedDescription]);
            
            return [
                'type' => 'description',
                'success' => true,
                'original_value' => $originalDescription,
                'optimized_value' => $optimizedDescription,
                'readability_score' => $optimization['optimizations']['description']['readability_score'] ?? 0,
                'seo_score' => $optimization['optimizations']['description']['seo_score'] ?? 0,
            ];
        }

        return [
            'type' => 'description',
            'success' => false,
            'reason' => '自动优化不安全或无改进建议',
        ];
    }

    /**
     * 优化价格
     */
    private function optimizePrice(WechatGroup $group, ContentOptimizationService $service): array
    {
        $originalPrice = $group->price;
        
        $comprehensive = $service->getComprehensiveOptimization($group);
        $priceOptimization = $comprehensive['price_optimization'] ?? null;
        
        if ($priceOptimization && isset($priceOptimization['current_analysis']['recommended_price'])) {
            $recommendedPrice = $priceOptimization['current_analysis']['recommended_price'];
            $confidence = $priceOptimization['current_analysis']['confidence'] ?? 'low';
            
            // 只有在高置信度时才自动应用价格优化
            if ($confidence === 'high' && abs($recommendedPrice - $originalPrice) / $originalPrice > 0.05) {
                $group->update(['price' => $recommendedPrice]);
                
                return [
                    'type' => 'price',
                    'success' => true,
                    'original_value' => $originalPrice,
                    'optimized_value' => $recommendedPrice,
                    'confidence' => $confidence,
                    'expected_impact' => $priceOptimization['current_analysis']['adjustment_reason'] ?? '',
                ];
            }
        }

        return [
            'type' => 'price',
            'success' => false,
            'reason' => '价格优化置信度不足或变化幅度过小',
        ];
    }

    /**
     * 优化SEO
     */
    private function optimizeSEO(WechatGroup $group, ContentOptimizationService $service): array
    {
        $comprehensive = $service->getComprehensiveOptimization($group);
        $seoOptimization = $comprehensive['seo_optimization'] ?? null;
        
        if ($seoOptimization) {
            $improvements = [];
            
            // 优化关键词（如果群组模型支持）
            if (isset($seoOptimization['keywords']) && method_exists($group, 'updateKeywords')) {
                $group->updateKeywords($seoOptimization['keywords']);
                $improvements[] = 'keywords';
            }
            
            // 优化URL结构（如果支持）
            if (isset($seoOptimization['url_structure']) && method_exists($group, 'updateSlug')) {
                $group->updateSlug($seoOptimization['url_structure']['seo_friendly']);
                $improvements[] = 'url_structure';
            }
            
            return [
                'type' => 'seo',
                'success' => !empty($improvements),
                'improvements' => $improvements,
                'seo_score' => $seoOptimization['seo_score'] ?? 0,
            ];
        }

        return [
            'type' => 'seo',
            'success' => false,
            'reason' => '无SEO优化建议',
        ];
    }

    /**
     * 优化标签
     */
    private function optimizeTags(WechatGroup $group, ContentServiceMerged $service): array
    {
        $optimization = $service->autoOptimizeContent($group);
        
        if (isset($optimization['optimizations']['tags'])) {
            $tagOptimization = $optimization['optimizations']['tags'];
            $recommendedTags = $tagOptimization['recommended_tags'] ?? [];
            
            // 如果群组模型支持标签，更新标签
            if (method_exists($group, 'syncTags') && !empty($recommendedTags)) {
                $group->syncTags($recommendedTags);
                
                return [
                    'type' => 'tags',
                    'success' => true,
                    'recommended_tags' => $recommendedTags,
                    'seo_value' => $tagOptimization['seo_value'] ?? 0,
                    'trending_score' => $tagOptimization['trending_score'] ?? 0,
                ];
            }
        }

        return [
            'type' => 'tags',
            'success' => false,
            'reason' => '无标签优化建议或模型不支持标签',
        ];
    }

    /**
     * 优化图片
     */
    private function optimizeImages(WechatGroup $group): array
    {
        $improvements = [];
        
        // 检查封面图片
        if (empty($group->cover_image)) {
            $improvements[] = '建议上传封面图片';
        }
        
        // 检查二维码
        if (empty($group->qr_code)) {
            $improvements[] = '建议上传群二维码';
        }
        
        return [
            'type' => 'images',
            'success' => false, // 图片优化通常需要人工干预
            'suggestions' => $improvements,
            'reason' => '图片优化需要人工上传',
        ];
    }

    /**
     * 生成优化摘要
     */
    private function generateOptimizationSummary(array $results): array
    {
        $successful = array_filter($results['optimizations_applied'], fn($opt) => $opt['success']);
        $failed = array_filter($results['optimizations_applied'], fn($opt) => !$opt['success']);
        
        $beforeScore = $results['before_scores']['estimated_improvement'] ?? 0;
        $afterScore = $results['after_scores']['estimated_improvement'] ?? 0;
        
        return [
            'total_optimizations' => count($results['optimizations_applied']),
            'successful_optimizations' => count($successful),
            'failed_optimizations' => count($failed),
            'improvement_score' => $afterScore - $beforeScore,
            'optimization_types' => array_column($successful, 'type'),
            'overall_success_rate' => count($results['optimizations_applied']) > 0 ? 
                (count($successful) / count($results['optimizations_applied'])) * 100 : 0,
        ];
    }

    /**
     * 记录优化结果
     */
    private function recordOptimizationResults(WechatGroup $group, array $results): void
    {
        // 保存到数据库
        \DB::table('content_optimization_history')->insert([
            'group_id' => $group->id,
            'user_id' => $group->user_id,
            'optimization_type' => 'auto_batch',
            'before_data' => json_encode($results['before_scores']),
            'after_data' => json_encode($results['after_scores']),
            'suggestions_applied' => json_encode($results['optimizations_applied']),
            'score_before' => $results['before_scores']['estimated_improvement'] ?? 0,
            'score_after' => $results['after_scores']['estimated_improvement'] ?? 0,
            'performance_metrics' => json_encode($results['summary']),
            'created_at' => now(),
        ]);
    }

    /**
     * 处理优化失败
     */
    private function handleOptimizationFailure(Exception $e): void
    {
        Log::error('内容优化任务失败', [
            'task_id' => $this->task['task_id'],
            'group_id' => $this->task['group_id'] ?? null,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'attempt' => $this->attempts(),
        ]);
    }

    /**
     * 任务失败时的处理
     */
    public function failed(Exception $exception): void
    {
        Log::error('内容优化任务最终失败', [
            'task_id' => $this->task['task_id'],
            'group_id' => $this->task['group_id'] ?? null,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
        
        // 可以在这里发送失败通知给用户
        // event(new ContentOptimizationFailed($this->task, $exception));
    }
}