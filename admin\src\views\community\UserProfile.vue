<template>
  <el-drawer
    :model-value="visible"
    :title="`用户画像 - ${userData.nickname}`"
    direction="rtl"
    size="50%"
    @update:model-value="$emit('update:visible', $event)"
    :before-close="handleClose"
  >
    <div class="user-profile-container" v-loading="loading">
      <!-- 1. 基本信息与活跃度 -->
      <el-card class="profile-section" shadow="never">
        <template #header>
          <div class="card-header">
            <span><el-icon><User /></el-icon> 基本信息与活跃度</span>
          </div>
        </template>
        <div class="basic-info">
          <el-avatar :size="80" :src="userData.avatar" />
          <div class="info-text">
            <h2>{{ userData.nickname }} <el-tag size="small">{{ userData.role || '普通成员' }}</el-tag></h2>
            <p>ID: {{ userData.id }} | 加入时间: {{ formatDate(userData.joined_at) }}</p>
            <div class="tags">
              <el-tag v-for="tag in userData.tags" :key="tag" class="profile-tag">{{ tag }}</el-tag>
            </div>
          </div>
        </div>
        <el-divider />
        <el-row :gutter="20" class="activity-stats">
          <el-col :span="6"><div class="stat-item"><strong>{{ activityData.login_days_30d }}</strong><span>近30日登录</span></div></el-col>
          <el-col :span="6"><div class="stat-item"><strong>{{ activityData.post_count }}</strong><span>累计发言</span></div></el-col>
          <el-col :span="6"><div class="stat-item"><strong>{{ activityData.avg_online_time }}h</strong><span>日均在线</span></div></el-col>
          <el-col :span="6"><div class="stat-item"><strong>{{ activityData.last_active }}</strong><span>最后活跃</span></div></el-col>
        </el-row>
      </el-card>

      <!-- 2. 内容偏好与互动行为 -->
      <el-card class="profile-section" shadow="never">
        <template #header>
          <div class="card-header">
            <span><el-icon><ChatDotRound /></el-icon> 内容偏好与互动</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>互动行为统计</h4>
            <ul class="interaction-list">
              <li><el-icon><Pointer /></el-icon> 点赞数: {{ interactionData.likes_given }}</li>
              <li><el-icon><Star /></el-icon> 收藏数: {{ interactionData.favorites_given }}</li>
              <li><el-icon><Share /></el-icon> 分享数: {{ interactionData.shares_given }}</li>
            </ul>
          </el-col>
          <el-col :span="12">
            <h4>常参与的话题</h4>
            <div class="topic-tags">
              <el-tag v-for="topic in interactionData.popular_topics" :key="topic" type="success" class="topic-tag">{{ topic }}</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 3. 消费能力与转化路径 -->
      <el-card class="profile-section" shadow="never">
        <template #header>
          <div class="card-header">
            <span><el-icon><Money /></el-icon> 消费能力与转化</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8"><div class="stat-item"><strong>¥{{ consumptionData.total_spent.toFixed(2) }}</strong><span>累计消费</span></div></el-col>
          <el-col :span="8"><div class="stat-item"><strong>{{ consumptionData.order_count }}</strong><span>订单数</span></div></el-col>
          <el-col :span="8"><div class="stat-item"><strong>¥{{ consumptionData.avg_order_value.toFixed(2) }}</strong><span>客单价</span></div></el-col>
        </el-row>
        <el-divider />
        <h4>转化路径分析</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in consumptionData.conversion_path"
            :key="index"
            :timestamp="formatDate(activity.timestamp)"
            :type="activity.type === 'order' ? 'primary' : 'success'"
          >
            {{ activity.description }}
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 4. 社交网络与影响力 -->
      <el-card class="profile-section" shadow="never">
        <template #header>
          <div class="card-header">
            <span><el-icon><Promotion /></el-icon> 社交网络与影响力</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8"><div class="stat-item"><strong>{{ socialData.invited_users }}</strong><span>邀请好友数</span></div></el-col>
          <el-col :span="8"><div class="stat-item"><strong>{{ socialData.downline_users }}</strong><span>下线成员数</span></div></el-col>
          <el-col :span="8"><div class="stat-item"><strong>¥{{ socialData.commission_earned.toFixed(2) }}</strong><span>赚取佣金</span></div></el-col>
        </el-row>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { User, ChatDotRound, Money, Promotion, Pointer, Star, Share } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/format'

const props = defineProps({
  visible: Boolean,
  userId: {
    type: [Number, String],
    default: null,
  },
})

const emit = defineEmits(['update:visible'])

const loading = ref(false)
const userData = ref({})
const activityData = ref({})
const interactionData = ref({})
const consumptionData = ref({})
const socialData = ref({})

const fetchUserProfile = async (id) => {
  if (!id) return
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    userData.value = {
      id: id,
      nickname: '张三',
      avatar: 'https://i.pravatar.cc/150?u=zhangsan',
      role: '核心用户',
      joined_at: '2024-01-15T10:30:00Z',
      tags: ['技术控', '早期用户', '高价值'],
    }
    activityData.value = {
      login_days_30d: 25,
      post_count: 188,
      avg_online_time: 2.5,
      last_active: '2小时前',
    }
    interactionData.value = {
      likes_given: 512,
      favorites_given: 98,
      shares_given: 45,
      popular_topics: ['AI技术', '产品设计', '创业心得'],
    }
    consumptionData.value = {
      total_spent: 1280.50,
      order_count: 15,
      avg_order_value: 85.37,
      conversion_path: [
        { timestamp: '2024-05-10T10:00:00Z', description: '通过分享链接进入落地页' },
        { timestamp: '2024-05-10T10:05:00Z', description: '浏览了 "AI大模型" 课程' },
        { timestamp: '2024-05-11T14:20:00Z', description: '下单购买 "AI大模型" 课程', type: 'order' },
        { timestamp: '2024-05-20T18:30:00Z', description: '复购 "产品设计" 课程', type: 'order' },
      ],
    }
    socialData.value = {
      invited_users: 12,
      downline_users: 5,
      commission_earned: 350.80,
    }
    loading.value = false
  }, 800)
}

watch(() => props.userId, (newId) => {
  fetchUserProfile(newId)
})

const handleClose = () => {
  emit('update:visible', false)
}

onMounted(() => {
  if (props.userId) {
    fetchUserProfile(props.userId)
  }
})
</script>

<style lang="scss" scoped>
.user-profile-container {
  padding: 0 20px;
}

.profile-section {
  margin-bottom: 20px;
  .card-header {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.basic-info {
  display: flex;
  align-items: center;
  gap: 20px;
  .info-text {
    h2 {
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    p {
      color: #64748b;
      margin: 0 0 10px 0;
      font-size: 14px;
    }
    .tags .profile-tag {
      margin-right: 5px;
    }
  }
}

.activity-stats, .interaction-list {
  .stat-item, li {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background-color: #f8fafc;
    padding: 15px;
    border-radius: 8px;
    strong {
      font-size: 20px;
      margin-bottom: 5px;
      color: #1e293b;
    }
    span, .el-icon {
      font-size: 14px;
      color: #475569;
    }
  }
}

.interaction-list {
  list-style: none;
  padding: 0;
  li {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px 15px;
    strong {
      font-size: 16px;
    }
  }
}

.topic-tags {
  .topic-tag {
    margin: 5px;
  }
}
</style>