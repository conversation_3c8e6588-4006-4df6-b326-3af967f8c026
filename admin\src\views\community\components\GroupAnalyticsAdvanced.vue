<template>
  <div class="group-analytics-advanced">
    <!-- 实时数据概览 -->
    <div class="realtime-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card realtime">
            <div class="metric-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ realtimeData.currentVisitors }}</div>
              <div class="metric-label">实时访客</div>
              <div class="metric-trend">
                <span :class="getTrendClass(realtimeData.visitorTrend)">
                  {{ realtimeData.visitorTrend > 0 ? '+' : '' }}{{ realtimeData.visitorTrend }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card conversion">
            <div class="metric-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ analyticsData.conversion_rate }}%</div>
              <div class="metric-label">转化率</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analyticsData.conversion_trend)">
                  {{ analyticsData.conversion_trend > 0 ? '+' : '' }}{{ analyticsData.conversion_trend }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card revenue">
            <div class="metric-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatNumber(analyticsData.total_revenue) }}</div>
              <div class="metric-label">总收入</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analyticsData.revenue_trend)">
                  {{ analyticsData.revenue_trend > 0 ? '+' : '' }}{{ analyticsData.revenue_trend }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card engagement">
            <div class="metric-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ analyticsData.avg_stay_time }}s</div>
              <div class="metric-label">平均停留</div>
              <div class="metric-trend">
                <span :class="getTrendClass(analyticsData.engagement_trend)">
                  {{ analyticsData.engagement_trend > 0 ? '+' : '' }}{{ analyticsData.engagement_trend }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- A/B测试管理 -->
    <el-card class="ab-test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><TrendCharts /></el-icon>
            A/B测试管理
          </span>
          <el-button type="primary" size="small" @click="createABTest">
            <el-icon><Plus /></el-icon>
            创建测试
          </el-button>
        </div>
      </template>

      <div class="ab-tests-list">
        <div v-for="test in abTests" :key="test.id" class="ab-test-item">
          <div class="test-header">
            <div class="test-info">
              <h4 class="test-name">{{ test.name }}</h4>
              <el-tag :type="getTestStatusType(test.status)" size="small">
                {{ getTestStatusText(test.status) }}
              </el-tag>
            </div>
            <div class="test-actions">
              <el-button size="small" @click="viewTestResults(test)">查看结果</el-button>
              <el-button size="small" @click="stopTest(test)" v-if="test.status === 'running'">
                停止测试
              </el-button>
            </div>
          </div>
          
          <div class="test-variants">
            <el-row :gutter="16">
              <el-col :span="12" v-for="variant in test.variants" :key="variant.id">
                <div class="variant-card">
                  <div class="variant-header">
                    <span class="variant-name">{{ variant.name }}</span>
                    <span class="variant-traffic">{{ variant.traffic }}% 流量</span>
                  </div>
                  <div class="variant-metrics">
                    <div class="metric">
                      <span class="metric-label">转化率:</span>
                      <span class="metric-value">{{ variant.conversion_rate }}%</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">访客数:</span>
                      <span class="metric-value">{{ variant.visitors }}</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">转化数:</span>
                      <span class="metric-value">{{ variant.conversions }}</span>
                    </div>
                  </div>
                  <div class="variant-preview">
                    <el-button size="small" @click="previewVariant(variant)">预览</el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <div class="test-progress">
            <div class="progress-info">
              <span>测试进度: {{ test.progress }}%</span>
              <span>剩余时间: {{ test.remaining_time }}</span>
            </div>
            <el-progress :percentage="test.progress" :stroke-width="6" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 用户行为分析 -->
    <el-card class="behavior-analysis-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><User /></el-icon>
            用户行为分析
          </span>
          <el-radio-group v-model="behaviorTimeRange" size="small">
            <el-radio-button label="1d">今天</el-radio-button>
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <!-- 用户流程漏斗 -->
          <div class="behavior-section">
            <h4 class="section-title">用户转化漏斗</h4>
            <div class="funnel-chart">
              <div v-for="(step, index) in conversionFunnel" :key="index" class="funnel-step">
                <div class="step-bar" :style="{ width: step.percentage + '%' }">
                  <span class="step-label">{{ step.name }}</span>
                  <span class="step-value">{{ step.count }} ({{ step.percentage }}%)</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <!-- 热力图 -->
          <div class="behavior-section">
            <h4 class="section-title">页面热力图</h4>
            <div class="heatmap-container">
              <div class="heatmap-placeholder">
                <el-icon><Location /></el-icon>
                <p>点击热力图数据</p>
                <el-button size="small" @click="generateHeatmap">生成热力图</el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 用户路径分析 -->
      <div class="user-path-analysis">
        <h4 class="section-title">用户路径分析</h4>
        <div class="path-flow">
          <div v-for="(path, index) in userPaths" :key="index" class="path-item">
            <div class="path-steps">
              <div v-for="(step, stepIndex) in path.steps" :key="stepIndex" class="path-step">
                <span class="step-name">{{ step.name }}</span>
                <el-icon v-if="stepIndex < path.steps.length - 1"><ArrowRight /></el-icon>
              </div>
            </div>
            <div class="path-metrics">
              <span class="path-users">{{ path.users }} 用户</span>
              <span class="path-percentage">{{ path.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 智能优化建议 -->
    <el-card class="optimization-suggestions" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><MagicStick /></el-icon>
            智能优化建议
          </span>
          <el-button size="small" @click="refreshSuggestions">
            <el-icon><Refresh /></el-icon>
            刷新建议
          </el-button>
        </div>
      </template>

      <div class="suggestions-list">
        <div v-for="suggestion in optimizationSuggestions" :key="suggestion.id" class="suggestion-item">
          <div class="suggestion-header">
            <div class="suggestion-priority" :class="suggestion.priority">
              {{ getPriorityText(suggestion.priority) }}
            </div>
            <div class="suggestion-impact">
              预期提升: {{ suggestion.expected_improvement }}
            </div>
          </div>
          <div class="suggestion-content">
            <h5 class="suggestion-title">{{ suggestion.title }}</h5>
            <p class="suggestion-description">{{ suggestion.description }}</p>
          </div>
          <div class="suggestion-actions">
            <el-button type="primary" size="small" @click="applySuggestion(suggestion)">
              应用建议
            </el-button>
            <el-button size="small" @click="dismissSuggestion(suggestion)">
              忽略
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View, TrendCharts, Money, Timer, Plus, User,
  Location, ArrowRight, MagicStick, Refresh
} from '@element-plus/icons-vue'

const props = defineProps({
  groupId: {
    type: [Number, String],
    required: true
  }
})

// 响应式数据
const behaviorTimeRange = ref('7d')
const realtimeUpdateTimer = ref(null)

const realtimeData = reactive({
  currentVisitors: 23,
  visitorTrend: 12.5
})

const analyticsData = reactive({
  conversion_rate: 15.6,
  conversion_trend: 8.2,
  total_revenue: 8960,
  revenue_trend: 15.3,
  avg_stay_time: 145,
  engagement_trend: -2.1
})

const abTests = ref([
  {
    id: 1,
    name: '标题文案A/B测试',
    status: 'running',
    progress: 65,
    remaining_time: '3天',
    variants: [
      {
        id: 1,
        name: '版本A（原版）',
        traffic: 50,
        conversion_rate: 12.3,
        visitors: 1250,
        conversions: 154
      },
      {
        id: 2,
        name: '版本B（优化版）',
        traffic: 50,
        conversion_rate: 15.8,
        visitors: 1180,
        conversions: 186
      }
    ]
  }
])

const conversionFunnel = ref([
  { name: '页面访问', count: 10000, percentage: 100 },
  { name: '查看详情', count: 6500, percentage: 65 },
  { name: '点击加入', count: 2800, percentage: 28 },
  { name: '完成支付', count: 1560, percentage: 15.6 }
])

const userPaths = ref([
  {
    steps: [
      { name: '首页' },
      { name: '群组详情' },
      { name: '支付页面' },
      { name: '支付成功' }
    ],
    users: 856,
    percentage: 45.2
  },
  {
    steps: [
      { name: '首页' },
      { name: '群组详情' },
      { name: '离开' }
    ],
    users: 623,
    percentage: 32.8
  }
])

const optimizationSuggestions = ref([
  {
    id: 1,
    priority: 'high',
    title: '优化群组标题',
    description: '当前标题的点击率较低，建议使用更具吸引力的词汇，如"限时优惠"、"专家指导"等。',
    expected_improvement: '+25% 点击率',
    type: 'title'
  },
  {
    id: 2,
    priority: 'medium',
    title: '调整价格策略',
    description: '数据显示用户对当前价格敏感度较高，建议测试阶梯定价或限时折扣。',
    expected_improvement: '+18% 转化率',
    type: 'pricing'
  }
])

// 方法
const getTrendClass = (trend) => {
  return trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-neutral'
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'W'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getTestStatusType = (status) => {
  const types = {
    running: 'success',
    completed: 'info',
    paused: 'warning',
    draft: 'info'
  }
  return types[status] || 'info'
}

const getTestStatusText = (status) => {
  const texts = {
    running: '进行中',
    completed: '已完成',
    paused: '已暂停',
    draft: '草稿'
  }
  return texts[status] || status
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return texts[priority] || priority
}

const createABTest = () => {
  ElMessage.info('A/B测试创建功能开发中')
}

const viewTestResults = (test) => {
  ElMessage.info(`查看测试结果: ${test.name}`)
}

const stopTest = (test) => {
  ElMessage.info(`停止测试: ${test.name}`)
}

const previewVariant = (variant) => {
  ElMessage.info(`预览变体: ${variant.name}`)
}

const generateHeatmap = () => {
  ElMessage.info('生成热力图功能开发中')
}

const refreshSuggestions = () => {
  ElMessage.success('优化建议已刷新')
}

const applySuggestion = (suggestion) => {
  ElMessage.success(`已应用建议: ${suggestion.title}`)
}

const dismissSuggestion = (suggestion) => {
  const index = optimizationSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index !== -1) {
    optimizationSuggestions.value.splice(index, 1)
    ElMessage.info('建议已忽略')
  }
}

// 实时数据更新
const startRealtimeUpdates = () => {
  realtimeUpdateTimer.value = setInterval(() => {
    // 模拟实时数据更新
    realtimeData.currentVisitors += Math.floor(Math.random() * 5) - 2
    realtimeData.currentVisitors = Math.max(0, realtimeData.currentVisitors)
  }, 5000)
}

const stopRealtimeUpdates = () => {
  if (realtimeUpdateTimer.value) {
    clearInterval(realtimeUpdateTimer.value)
    realtimeUpdateTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  startRealtimeUpdates()
})

onUnmounted(() => {
  stopRealtimeUpdates()
})
</script>

<style lang="scss" scoped>
.group-analytics-advanced {
  .realtime-overview {
    margin-bottom: 24px;
    
    .metric-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }
      
      .metric-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        color: white;
      }
      
      .metric-content {
        flex: 1;
        
        .metric-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }
        
        .metric-label {
          font-size: 14px;
          color: #64748b;
          margin-bottom: 4px;
        }
        
        .metric-trend {
          font-size: 12px;
          font-weight: 500;
          
          .trend-up {
            color: #10b981;
          }
          
          .trend-down {
            color: #ef4444;
          }
          
          .trend-neutral {
            color: #64748b;
          }
        }
      }
      
      &.realtime .metric-icon {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      }
      
      &.conversion .metric-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }
      
      &.revenue .metric-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }
      
      &.engagement .metric-icon {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
    }
  }
  
  .ab-test-card,
  .behavior-analysis-card,
  .optimization-suggestions {
    margin-bottom: 24px;
    
    :deep(.el-card__header) {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #1e293b;
        }
      }
    }
  }
  
  .ab-tests-list {
    .ab-test-item {
      margin-bottom: 24px;
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      
      .test-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .test-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .test-name {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
          }
        }
      }
      
      .test-variants {
        margin-bottom: 16px;
        
        .variant-card {
          padding: 16px;
          background: white;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          
          .variant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .variant-name {
              font-weight: 600;
              color: #1e293b;
            }
            
            .variant-traffic {
              font-size: 12px;
              color: #64748b;
            }
          }
          
          .variant-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 12px;
            
            .metric {
              text-align: center;
              
              .metric-label {
                display: block;
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
              }
              
              .metric-value {
                font-weight: 600;
                color: #1e293b;
              }
            }
          }
        }
      }
      
      .test-progress {
        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;
          color: #64748b;
        }
      }
    }
  }
  
  .behavior-section {
    margin-bottom: 24px;
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }
    
    .funnel-chart {
      .funnel-step {
        margin-bottom: 8px;
        
        .step-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          color: white;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    
    .heatmap-container {
      .heatmap-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        background: #f8fafc;
        border: 2px dashed #e2e8f0;
        border-radius: 8px;
        color: #64748b;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 12px;
        }
        
        p {
          margin: 0 0 12px 0;
        }
      }
    }
  }
  
  .user-path-analysis {
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }
    
    .path-flow {
      .path-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        margin-bottom: 12px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        
        .path-steps {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .path-step {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .step-name {
              padding: 4px 8px;
              background: white;
              border-radius: 4px;
              font-size: 12px;
              color: #1e293b;
            }
          }
        }
        
        .path-metrics {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;
          
          .path-users {
            font-weight: 600;
            color: #1e293b;
          }
          
          .path-percentage {
            font-size: 12px;
            color: #64748b;
          }
        }
      }
    }
  }
  
  .suggestions-list {
    .suggestion-item {
      padding: 20px;
      margin-bottom: 16px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      
      .suggestion-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .suggestion-priority {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          
          &.high {
            background: #fef2f2;
            color: #dc2626;
          }
          
          &.medium {
            background: #fffbeb;
            color: #d97706;
          }
          
          &.low {
            background: #f0fdf4;
            color: #16a34a;
          }
        }
        
        .suggestion-impact {
          font-size: 14px;
          color: #10b981;
          font-weight: 500;
        }
      }
      
      .suggestion-content {
        margin-bottom: 16px;
        
        .suggestion-title {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }
        
        .suggestion-description {
          margin: 0;
          color: #64748b;
          line-height: 1.6;
        }
      }
      
      .suggestion-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}
</style>