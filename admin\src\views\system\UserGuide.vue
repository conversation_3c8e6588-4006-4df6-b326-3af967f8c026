<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="guide-header">
      <h1>📚 系统使用指南</h1>
              <p class="guide-subtitle">完整的功能介绍和操作指南，帮助您快速上手晨鑫流量变现系统</p>
    </div>

    <!-- 快速导航 -->
    <el-card class="nav-card" style="margin-bottom: 20px">
      <template #header>
        <div class="card-header">
          <span>🧭 快速导航</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="(section, index) in guideSections" :key="index">
          <div class="nav-item" @click="scrollToSection(section.id)">
            <div class="nav-icon">
              <i :class="section.icon"></i>
            </div>
            <div class="nav-content">
              <h4>{{ section.title }}</h4>
              <p>{{ section.description }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 系统概览 -->
    <el-card class="guide-section" id="system-overview">
      <template #header>
        <div class="card-header">
          <span>🎯 系统概览</span>
        </div>
      </template>
      
      <div class="section-content">
        <div class="overview-content">
                        <h3>什么是晨鑫流量变现系统？</h3>
                      <p>晨鑫流量变现系统是一个功能强大的智能社群营销与多级分销平台，整合了防红系统、用户管理、财务管理、数据分析等核心功能。</p>
          
          <h4>🚀 核心功能</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="el-icon-shield"></i>
                </div>
                <h5>智能防红系统</h5>
                <p>自动检测域名状态，智能切换可用域名，确保链接永不失效</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="el-icon-s-promotion"></i>
                </div>
                <h5>多级分销系统</h5>
                <p>支持多级分销，自动计算佣金，实时结算分成</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="el-icon-data-analysis"></i>
                </div>
                <h5>数据分析中心</h5>
                <p>详细的数据统计和分析，帮助您做出明智的决策</p>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 用户管理指南 -->
    <el-card class="guide-section" id="user-management">
      <template #header>
        <div class="card-header">
          <span>👥 用户管理</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>用户管理功能</h3>
        
        <div class="guide-step">
          <h4>📋 用户列表</h4>
          <p>查看和管理所有用户信息，支持筛选、搜索、导出等操作。</p>
          
          <div class="step-actions">
            <el-tag type="primary">基础操作</el-tag>
            <ul>
              <li>查看用户详细信息</li>
              <li>编辑用户资料</li>
              <li>设置用户状态（启用/禁用）</li>
              <li>批量操作用户</li>
              <li>导出用户数据</li>
            </ul>
          </div>
        </div>
        
        <div class="guide-step">
          <h4>📊 用户分析</h4>
          <p>提供详细的用户分析报告，包括用户增长、活跃度、地域分布等。</p>
          
          <div class="step-actions">
            <el-tag type="success">分析功能</el-tag>
            <ul>
              <li>用户增长趋势分析</li>
              <li>用户活跃度统计</li>
              <li>地域分布分析</li>
              <li>设备类型统计</li>
              <li>用户行为分析</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 分销管理指南 -->
    <el-card class="guide-section" id="distribution-management">
      <template #header>
        <div class="card-header">
          <span>💰 分销管理</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>分销系统使用指南</h3>
        
        <div class="guide-step">
          <h4>🏢 分销组管理</h4>
          <p>创建和管理分销组，设置不同的佣金比例和权限。</p>
          
          <div class="step-process">
            <el-steps :active="3" align-center>
              <el-step title="创建分销组" description="设置分销组基本信息"></el-step>
              <el-step title="配置佣金比例" description="设置各级佣金分成比例"></el-step>
              <el-step title="添加分销商" description="邀请用户加入分销组"></el-step>
              <el-step title="监控业绩" description="实时查看分销业绩"></el-step>
            </el-steps>
          </div>
        </div>
        
        <div class="guide-step">
          <h4>👨‍💼 分销商管理</h4>
          <p>管理分销商信息，查看分销业绩，处理提现申请。</p>
          
          <div class="step-actions">
            <el-tag type="warning">管理功能</el-tag>
            <ul>
              <li>查看分销商详细信息</li>
              <li>分销业绩统计</li>
              <li>佣金结算记录</li>
              <li>提现申请处理</li>
              <li>分销商等级管理</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 财务管理指南 -->
    <el-card class="guide-section" id="finance-management">
      <template #header>
        <div class="card-header">
          <span>💳 财务管理</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>财务管理功能</h3>
        
        <div class="guide-step">
          <h4>📈 财务概览</h4>
          <p>实时查看收支情况，掌握平台财务状况。</p>
          
          <div class="financial-metrics">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="metric-item">
                  <div class="metric-icon income">
                    <i class="el-icon-money"></i>
                  </div>
                  <div class="metric-info">
                    <h5>总收入</h5>
                    <p>平台总收入统计</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="metric-item">
                  <div class="metric-icon expense">
                    <i class="el-icon-wallet"></i>
                  </div>
                  <div class="metric-info">
                    <h5>总支出</h5>
                    <p>平台总支出统计</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="metric-item">
                  <div class="metric-icon profit">
                    <i class="el-icon-trophy"></i>
                  </div>
                  <div class="metric-info">
                    <h5>净利润</h5>
                    <p>收支差额统计</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="metric-item">
                  <div class="metric-icon balance">
                    <i class="el-icon-coin"></i>
                  </div>
                  <div class="metric-info">
                    <h5>可用余额</h5>
                    <p>平台可用资金</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <div class="guide-step">
          <h4>💸 提现管理</h4>
          <p>处理用户提现申请，管理提现流程。</p>
          
          <div class="step-process">
            <el-steps :active="2" align-center>
              <el-step title="提现申请" description="用户发起提现申请"></el-step>
              <el-step title="审核处理" description="管理员审核提现申请"></el-step>
              <el-step title="资金处理" description="处理提现资金"></el-step>
              <el-step title="完成提现" description="用户收到提现资金"></el-step>
            </el-steps>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 防红系统指南 -->
    <el-card class="guide-section" id="anti-block-system">
      <template #header>
        <div class="card-header">
          <span>🛡️ 防红系统</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>防红系统使用指南</h3>
        
        <div class="guide-step">
          <h4>🌐 域名池管理</h4>
          <p>管理域名池，配置域名检测策略，确保链接稳定可用。</p>
          
          <div class="domain-config">
            <h5>域名配置要求：</h5>
            <el-alert
              title="重要提醒"
              type="warning"
              description="所有域名必须已备案、已解析、已配置SSL证书，否则可能影响系统正常运行"
              show-icon
              :closable="false"
            />
            
            <div class="config-tips">
              <div class="tip-item">
                <el-tag type="primary">短链接域名</el-tag>
                <span>用于生成短链接，建议配置3-5个</span>
              </div>
              <div class="tip-item">
                <el-tag type="success">中转页域名</el-tag>
                <span>用于中转页面，建议配置2-3个</span>
              </div>
              <div class="tip-item">
                <el-tag type="info">API服务域名</el-tag>
                <span>用于API接口，建议配置1-2个</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="guide-step">
          <h4>🔗 短链接管理</h4>
          <p>创建和管理短链接，查看链接访问统计。</p>
          
          <div class="step-actions">
            <el-tag type="success">功能特色</el-tag>
            <ul>
              <li>自动选择最佳域名</li>
              <li>实时监控链接状态</li>
              <li>智能故障转移</li>
              <li>详细访问统计</li>
              <li>批量管理操作</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 系统设置指南 -->
    <el-card class="guide-section" id="system-settings">
      <template #header>
        <div class="card-header">
          <span>⚙️ 系统设置</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>系统设置与维护</h3>
        
        <div class="guide-step">
          <h4>🔧 基础设置</h4>
          <p>配置系统基本参数，如网站信息、支付配置等。</p>
          
          <div class="settings-categories">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="setting-category">
                  <h5>网站配置</h5>
                  <ul>
                    <li>网站名称</li>
                    <li>网站描述</li>
                    <li>联系方式</li>
                    <li>备案信息</li>
                  </ul>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="setting-category">
                  <h5>支付配置</h5>
                  <ul>
                    <li>支付宝配置</li>
                    <li>微信支付配置</li>
                    <li>银行卡配置</li>
                    <li>提现设置</li>
                  </ul>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="setting-category">
                  <h5>通知配置</h5>
                  <ul>
                    <li>邮件通知</li>
                    <li>短信通知</li>
                    <li>站内消息</li>
                    <li>推送设置</li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <div class="guide-step">
          <h4>📊 系统监控</h4>
          <p>实时监控系统运行状况，及时发现和解决问题。</p>
          
          <div class="monitoring-features">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="monitor-item">
                  <div class="monitor-icon">
                    <i class="el-icon-monitor"></i>
                  </div>
                  <div class="monitor-info">
                    <h5>服务器监控</h5>
                    <p>CPU、内存、磁盘使用情况</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="monitor-item">
                  <div class="monitor-icon">
                    <i class="el-icon-connection"></i>
                  </div>
                  <div class="monitor-info">
                    <h5>网络监控</h5>
                    <p>网络流量、连接状态</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 常见问题 -->
    <el-card class="guide-section" id="faq">
      <template #header>
        <div class="card-header">
          <span>❓ 常见问题</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>常见问题解答</h3>
        
        <el-collapse v-model="activeNames">
          <el-collapse-item title="如何添加新域名到防红系统？" name="1">
            <div class="faq-content">
              <p>1. 确保域名已备案、已解析、已配置SSL证书</p>
              <p>2. 进入"防红系统 - 域名管理"页面</p>
              <p>3. 点击"添加域名"按钮</p>
              <p>4. 填写域名信息和配置参数</p>
              <p>5. 系统会自动检测域名状态</p>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="如何处理用户提现申请？" name="2">
            <div class="faq-content">
              <p>1. 进入"财务管理 - 提现管理"页面</p>
              <p>2. 查看待审核的提现申请</p>
              <p>3. 核实用户身份和提现信息</p>
              <p>4. 点击"批准"或"拒绝"按钮</p>
              <p>5. 批准后系统会自动处理资金</p>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="系统出现异常怎么办？" name="3">
            <div class="faq-content">
              <p>1. 查看"系统管理 - 系统监控"页面</p>
              <p>2. 检查系统日志了解具体错误</p>
              <p>3. 尝试重启相关服务</p>
              <p>4. 清理系统缓存</p>
              <p>5. 如问题仍未解决，请联系技术支持</p>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="如何设置分销佣金比例？" name="4">
            <div class="faq-content">
              <p>1. 进入"分销管理 - 分销组管理"页面</p>
              <p>2. 选择要设置的分销组</p>
              <p>3. 点击"编辑"按钮</p>
              <p>4. 在佣金设置中配置各级分成比例</p>
              <p>5. 保存设置后立即生效</p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 联系支持 -->
    <el-card class="guide-section" id="contact-support">
      <template #header>
        <div class="card-header">
          <span>📞 联系支持</span>
        </div>
      </template>
      
      <div class="section-content">
        <h3>获取帮助</h3>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="support-item">
              <div class="support-icon">
                <i class="el-icon-service"></i>
              </div>
              <div class="support-info">
                <h4>在线客服</h4>
                <p>工作时间：9:00-18:00</p>
                <el-button type="primary" size="small">联系客服</el-button>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="support-item">
              <div class="support-icon">
                <i class="el-icon-message"></i>
              </div>
              <div class="support-info">
                <h4>邮件支持</h4>
                <p><EMAIL></p>
                <el-button type="success" size="small">发送邮件</el-button>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="support-item">
              <div class="support-icon">
                <i class="el-icon-phone"></i>
              </div>
              <div class="support-info">
                <h4>电话支持</h4>
                <p>************</p>
                <el-button type="warning" size="small">拨打电话</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const activeNames = ref(['1'])

const guideSections = ref([
  {
    id: 'system-overview',
    title: '系统概览',
    description: '了解系统功能和架构',
    icon: 'el-icon-info'
  },
  {
    id: 'user-management',
    title: '用户管理',
    description: '用户信息管理和分析',
    icon: 'el-icon-user'
  },
  {
    id: 'distribution-management',
    title: '分销管理',
    description: '分销系统配置和管理',
    icon: 'el-icon-s-promotion'
  },
  {
    id: 'finance-management',
    title: '财务管理',
    description: '财务数据和提现管理',
    icon: 'el-icon-money'
  },
  {
    id: 'anti-block-system',
    title: '防红系统',
    description: '域名池和短链接管理',
    icon: 'el-icon-shield'
  },
  {
    id: 'system-settings',
    title: '系统设置',
    description: '系统配置和监控',
    icon: 'el-icon-setting'
  }
])

// 方法
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style lang="scss" scoped>
.guide-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    font-size: 32px;
    color: #303133;
    margin-bottom: 10px;
  }
  
  .guide-subtitle {
    font-size: 16px;
    color: #606266;
    margin: 0;
  }
}

.nav-card {
  .nav-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .nav-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      color: white;
      font-size: 20px;
    }
    
    .nav-content {
      flex: 1;
      
      h4 {
        margin: 0 0 5px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.guide-section {
  margin-bottom: 20px;
  
  .section-content {
    padding: 20px;
    
    h3 {
      color: #303133;
      margin-bottom: 20px;
      font-size: 24px;
    }
    
    h4 {
      color: #303133;
      margin: 20px 0 10px 0;
      font-size: 18px;
    }
    
    h5 {
      color: #303133;
      margin: 15px 0 8px 0;
      font-size: 16px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
    
    ul {
      margin-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }
}

.feature-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  
  .feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #409eff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
  }
  
  h5 {
    margin-bottom: 10px;
    color: #303133;
  }
  
  p {
    color: #606266;
    font-size: 14px;
  }
}

.guide-step {
  margin-bottom: 30px;
  
  .step-actions {
    margin-top: 15px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
    
    ul {
      margin-top: 10px;
    }
  }
  
  .step-process {
    margin-top: 20px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
  }
}

.financial-metrics {
  margin-top: 20px;
  
  .metric-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    
    .metric-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 20px;
      color: white;
      
      &.income { background: #67c23a; }
      &.expense { background: #f56c6c; }
      &.profit { background: #409eff; }
      &.balance { background: #e6a23c; }
    }
    
    .metric-info {
      flex: 1;
      
      h5 {
        margin: 0 0 5px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.domain-config {
  margin-top: 20px;
  
  h5 {
    margin-bottom: 15px;
    color: #303133;
  }
  
  .config-tips {
    margin-top: 15px;
    
    .tip-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .el-tag {
        margin-right: 10px;
      }
    }
  }
}

.settings-categories {
  margin-top: 20px;
  
  .setting-category {
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    
    h5 {
      margin-bottom: 15px;
      color: #303133;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }
}

.monitoring-features {
  margin-top: 20px;
  
  .monitor-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    
    .monitor-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 20px;
      color: white;
    }
    
    .monitor-info {
      flex: 1;
      
      h5 {
        margin: 0 0 5px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.faq-content {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
  
  p {
    margin-bottom: 8px;
    color: #606266;
  }
}

.support-item {
  text-align: center;
  padding: 30px 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  
  .support-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #409eff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
  }
  
  .support-info {
    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
    
    p {
      margin-bottom: 15px;
      color: #606266;
    }
  }
}
</style> 