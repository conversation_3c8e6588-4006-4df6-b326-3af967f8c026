<?php

namespace App\Http\Middleware;

use Illuminate\Http\Middleware\TrustProxies as Middleware;
use Illuminate\Http\Request;

/**
 * 代理信任中间件
 * 配置信任的代理服务器
 */
class TrustProxies extends Middleware
{
    /**
     * 这个应用信任的代理服务器
     */
    protected $proxies;

    /**
     * 用于检测代理的请求头
     */
    protected $headers =
        Request::HEADER_X_FORWARDED_FOR |
        Request::HEADER_X_FORWARDED_HOST |
        Request::HEADER_X_FORWARDED_PORT |
        Request::HEADER_X_FORWARDED_PROTO |
        Request::HEADER_X_FORWARDED_AWS_ELB;
} 