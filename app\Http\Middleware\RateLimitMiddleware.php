<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

/**
 * 智能API限流中间件
 * 提供多种限流策略和防护机制
 */
class RateLimitMiddleware
{
    /**
     * 默认限流规则
     */
    private $defaultRules = [
        'guest' => [
            'requests' => 100,
            'per_minutes' => 60,
        ],
        'user' => [
            'requests' => 1000,
            'per_minutes' => 60,
        ],
        'admin' => [
            'requests' => 5000,
            'per_minutes' => 60,
        ],
    ];

    /**
     * 特殊路由限流规则
     */
    private $routeRules = [
        'api/login' => [
            'requests' => 5,
            'per_minutes' => 15,
            'block_duration' => 300, // 5分钟
        ],
        'api/register' => [
            'requests' => 3,
            'per_minutes' => 60,
            'block_duration' => 1800, // 30分钟
        ],
        'api/forgot-password' => [
            'requests' => 3,
            'per_minutes' => 60,
            'block_duration' => 1800,
        ],
        'api/orders' => [
            'requests' => 100,
            'per_minutes' => 60,
        ],
        'api/export' => [
            'requests' => 10,
            'per_minutes' => 60,
        ],
    ];

    /**
     * 可疑行为检测规则
     */
    private $suspiciousRules = [
        'rapid_requests' => [
            'threshold' => 50,
            'window' => 60,
            'action' => 'temporary_block',
        ],
        'failed_auth' => [
            'threshold' => 10,
            'window' => 300,
            'action' => 'auth_block',
        ],
        'error_rate' => [
            'threshold' => 20,
            'window' => 300,
            'action' => 'warning',
        ],
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $clientId = $this->getClientId($request);
        $route = $request->path();
        
        // 检查是否在黑名单中
        if ($this->isBlocked($clientId)) {
            return $this->blockedResponse($clientId);
        }
        
        // 检查可疑行为
        $this->checkSuspiciousActivity($clientId, $request);
        
        // 获取限流规则
        $rule = $this->getRule($request, $route);
        
        // 执行限流检查
        $rateLimitResult = $this->checkRateLimit($clientId, $route, $rule);
        
        if (!$rateLimitResult['allowed']) {
            return $this->rateLimitExceededResponse($rateLimitResult);
        }
        
        // 记录请求
        $this->recordRequest($clientId, $request);
        
        // 执行请求
        $response = $next($request);
        
        // 记录响应
        $this->recordResponse($clientId, $request, $response);
        
        // 添加限流头信息
        $this->addRateLimitHeaders($response, $rateLimitResult);
        
        return $response;
    }

    /**
     * 获取客户端ID
     */
    private function getClientId(Request $request): string
    {
        $user = Auth::user();
        
        if ($user) {
            return "user:{$user->id}";
        }
        
        return "ip:{$request->ip()}";
    }

    /**
     * 检查是否被阻止
     */
    private function isBlocked(string $clientId): bool
    {
        $blockKey = "rate_limit:blocked:{$clientId}";
        return Cache::has($blockKey);
    }

    /**
     * 检查可疑活动
     */
    private function checkSuspiciousActivity(string $clientId, Request $request): void
    {
        $this->checkRapidRequests($clientId);
        $this->checkFailedAuth($clientId, $request);
        $this->checkErrorRate($clientId);
    }

    /**
     * 检查快速请求
     */
    private function checkRapidRequests(string $clientId): void
    {
        $key = "suspicious:rapid:{$clientId}";
        $rule = $this->suspiciousRules['rapid_requests'];
        
        $count = Cache::get($key, 0);
        
        if ($count >= $rule['threshold']) {
            $this->handleSuspiciousActivity($clientId, 'rapid_requests', $rule);
        }
        
        Cache::put($key, $count + 1, $rule['window']);
    }

    /**
     * 检查认证失败
     */
    private function checkFailedAuth(string $clientId, Request $request): void
    {
        // 只检查认证相关的路由
        if (!$this->isAuthRoute($request->path())) {
            return;
        }
        
        $key = "suspicious:auth:{$clientId}";
        $rule = $this->suspiciousRules['failed_auth'];
        
        $count = Cache::get($key, 0);
        
        if ($count >= $rule['threshold']) {
            $this->handleSuspiciousActivity($clientId, 'failed_auth', $rule);
        }
    }

    /**
     * 检查错误率
     */
    private function checkErrorRate(string $clientId): void
    {
        $key = "suspicious:error:{$clientId}";
        $rule = $this->suspiciousRules['error_rate'];
        
        $errors = Cache::get($key, 0);
        
        if ($errors >= $rule['threshold']) {
            $this->handleSuspiciousActivity($clientId, 'error_rate', $rule);
        }
    }

    /**
     * 处理可疑活动
     */
    private function handleSuspiciousActivity(string $clientId, string $type, array $rule): void
    {
        Log::warning("Suspicious activity detected", [
            'client_id' => $clientId,
            'type' => $type,
            'rule' => $rule,
            'timestamp' => now(),
        ]);

        switch ($rule['action']) {
            case 'temporary_block':
                $this->blockClient($clientId, 3600); // 1小时
                break;
            case 'auth_block':
                $this->blockClient($clientId, 7200); // 2小时
                break;
            case 'warning':
                $this->sendWarningNotification($clientId, $type);
                break;
        }
    }

    /**
     * 阻止客户端
     */
    private function blockClient(string $clientId, int $duration): void
    {
        $blockKey = "rate_limit:blocked:{$clientId}";
        Cache::put($blockKey, [
            'blocked_at' => now(),
            'duration' => $duration,
            'reason' => 'suspicious_activity',
        ], $duration);
    }

    /**
     * 发送警告通知
     */
    private function sendWarningNotification(string $clientId, string $type): void
    {
        // 这里可以集成邮件、短信、钉钉等通知方式
        Log::warning("Rate limit warning notification", [
            'client_id' => $clientId,
            'type' => $type,
            'timestamp' => now(),
        ]);
    }

    /**
     * 检查是否是认证路由
     */
    private function isAuthRoute(string $path): bool
    {
        $authRoutes = ['login', 'register', 'forgot-password', 'reset-password'];
        
        foreach ($authRoutes as $route) {
            if (strpos($path, $route) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取限流规则
     */
    private function getRule(Request $request, string $route): array
    {
        // 检查特定路由规则
        foreach ($this->routeRules as $pattern => $rule) {
            if (strpos($route, str_replace('api/', '', $pattern)) !== false) {
                return $rule;
            }
        }
        
        // 获取用户角色规则
        $user = Auth::user();
        if ($user) {
            $role = $user->role ?? 'user';
            return $this->defaultRules[$role] ?? $this->defaultRules['user'];
        }
        
        return $this->defaultRules['guest'];
    }

    /**
     * 检查限流
     */
    private function checkRateLimit(string $clientId, string $route, array $rule): array
    {
        $key = "rate_limit:{$clientId}:{$route}";
        $globalKey = "rate_limit:global:{$clientId}";
        
        $requests = intval($rule['requests']);
        $perMinutes = intval($rule['per_minutes']);
        $windowSeconds = $perMinutes * 60;
        
        // 检查特定路由限流
        $routeCount = $this->getRequestCount($key, $windowSeconds);
        
        // 检查全局限流
        $globalCount = $this->getRequestCount($globalKey, $windowSeconds);
        $globalLimit = $requests * 2; // 全局限制是路由限制的2倍
        
        if ($routeCount >= $requests) {
            return [
                'allowed' => false,
                'reason' => 'route_limit_exceeded',
                'limit' => $requests,
                'remaining' => 0,
                'reset_time' => now()->addSeconds($windowSeconds),
            ];
        }
        
        if ($globalCount >= $globalLimit) {
            return [
                'allowed' => false,
                'reason' => 'global_limit_exceeded',
                'limit' => $globalLimit,
                'remaining' => 0,
                'reset_time' => now()->addSeconds($windowSeconds),
            ];
        }
        
        // 增加计数
        $this->incrementRequestCount($key, $windowSeconds);
        $this->incrementRequestCount($globalKey, $windowSeconds);
        
        return [
            'allowed' => true,
            'limit' => $requests,
            'remaining' => max(0, $requests - $routeCount - 1),
            'reset_time' => now()->addSeconds($windowSeconds),
        ];
    }

    /**
     * 获取请求计数
     */
    private function getRequestCount(string $key, int $windowSeconds): int
    {
        if (config('cache.default') === 'redis') {
            return $this->getRedisRequestCount($key, $windowSeconds);
        }
        
        return Cache::get($key, 0);
    }

    /**
     * 使用Redis滑动窗口计数
     */
    private function getRedisRequestCount(string $key, int $windowSeconds): int
    {
        $redis = Redis::connection();
        $now = time();
        $windowStart = $now - $windowSeconds;
        
        // 清理过期记录
        $redis->zremrangebyscore($key, 0, $windowStart);
        
        // 获取当前窗口内的计数
        return $redis->zcard($key);
    }

    /**
     * 增加请求计数
     */
    private function incrementRequestCount(string $key, int $windowSeconds): void
    {
        if (config('cache.default') === 'redis') {
            $this->incrementRedisRequestCount($key, $windowSeconds);
        } else {
            Cache::increment($key);
            Cache::expire($key, $windowSeconds);
        }
    }

    /**
     * 使用Redis增加请求计数
     */
    private function incrementRedisRequestCount(string $key, int $windowSeconds): void
    {
        $redis = Redis::connection();
        $now = time();
        $score = $now + mt_rand(1, 1000) / 1000; // 添加微秒精度
        
        // 添加当前请求
        $redis->zadd($key, $score, $score);
        
        // 设置过期时间
        $redis->expire($key, $windowSeconds);
    }

    /**
     * 记录请求
     */
    private function recordRequest(string $clientId, Request $request): void
    {
        $key = "rate_limit:requests:{$clientId}";
        $data = [
            'timestamp' => now(),
            'method' => $request->method(),
            'path' => $request->path(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
        ];
        
        // 只保留最近50个请求记录
        $requests = Cache::get($key, []);
        $requests[] = $data;
        
        if (count($requests) > 50) {
            $requests = array_slice($requests, -50);
        }
        
        Cache::put($key, $requests, 3600); // 1小时
    }

    /**
     * 记录响应
     */
    private function recordResponse(string $clientId, Request $request, $response): void
    {
        $statusCode = $response->status();
        
        // 记录错误响应
        if ($statusCode >= 400) {
            $errorKey = "suspicious:error:{$clientId}";
            $rule = $this->suspiciousRules['error_rate'];
            
            $count = Cache::get($errorKey, 0);
            Cache::put($errorKey, $count + 1, $rule['window']);
        }
        
        // 记录认证失败
        if ($statusCode === 401 && $this->isAuthRoute($request->path())) {
            $authKey = "suspicious:auth:{$clientId}";
            $rule = $this->suspiciousRules['failed_auth'];
            
            $count = Cache::get($authKey, 0);
            Cache::put($authKey, $count + 1, $rule['window']);
        }
    }

    /**
     * 添加限流头信息
     */
    private function addRateLimitHeaders($response, array $rateLimitResult): void
    {
        $response->headers->set('X-RateLimit-Limit', $rateLimitResult['limit']);
        $response->headers->set('X-RateLimit-Remaining', $rateLimitResult['remaining']);
        $response->headers->set('X-RateLimit-Reset', $rateLimitResult['reset_time']->timestamp);
    }

    /**
     * 返回被阻止的响应
     */
    private function blockedResponse(string $clientId): JsonResponse
    {
        $blockInfo = Cache::get("rate_limit:blocked:{$clientId}");
        
        Log::warning("Blocked client attempted access", [
            'client_id' => $clientId,
            'block_info' => $blockInfo,
            'timestamp' => now(),
        ]);
        
        return response()->json([
            'code' => 429,
            'message' => '您的IP已被临时封禁，请稍后再试',
            'data' => [
                'blocked_at' => $blockInfo['blocked_at'] ?? null,
                'reason' => $blockInfo['reason'] ?? 'unknown',
                'retry_after' => $blockInfo['duration'] ?? 3600,
            ],
        ], 429);
    }

    /**
     * 返回限流超出响应
     */
    private function rateLimitExceededResponse(array $rateLimitResult): JsonResponse
    {
        Log::info("Rate limit exceeded", [
            'reason' => $rateLimitResult['reason'],
            'limit' => $rateLimitResult['limit'],
            'timestamp' => now(),
        ]);
        
        $retryAfter = $rateLimitResult['reset_time']->timestamp - now()->timestamp;
        
        return response()->json([
            'code' => 429,
            'message' => '请求过于频繁，请稍后再试',
            'data' => [
                'reason' => $rateLimitResult['reason'],
                'limit' => $rateLimitResult['limit'],
                'retry_after' => $retryAfter,
                'reset_time' => $rateLimitResult['reset_time']->toISOString(),
            ],
        ], 429)->header('Retry-After', $retryAfter);
    }

    /**
     * 清理过期数据
     */
    public function cleanup(): void
    {
        if (config('cache.default') === 'redis') {
            $redis = Redis::connection();
            $pattern = 'rate_limit:*';
            $keys = $redis->keys($pattern);
            
            foreach ($keys as $key) {
                if ($redis->ttl($key) < 0) {
                    $redis->del($key);
                }
            }
        }
    }
} 