/**
 * 优化后的导航分组配置
 * 基于业务逻辑重新组织菜单结构，提高用户体验
 */

// 优化后的导航分组结构
export const optimizedNavigationGroups = {
  // 数据中心 - 所有数据相关功能
  dataCenter: {
    title: '数据中心',
    icon: 'DataBoard',
    order: 1,
    description: '数据分析、统计报表、实时监控',
    children: [
      {
        path: '/dashboard',
        title: '数据看板',
        icon: 'Monitor',
        description: '核心业务数据总览'
      },
      {
        path: '/data-screen',
        title: '数据大屏',
        icon: 'DataLine',
        description: '全屏数据展示'
      },
      {
        path: '/analytics',
        title: '数据分析',
        icon: 'TrendCharts',
        description: '深度数据分析报告'
      },
      {
        path: '/reports',
        title: '统计报表',
        icon: 'Document',
        description: '各类业务统计报表'
      }
    ]
  },

  // 业务管理 - 核心业务功能
  businessManagement: {
    title: '业务管理',
    icon: 'Management',
    order: 2,
    description: '社群、订单、内容等核心业务管理',
    children: [
      {
        path: '/community',
        title: '社群管理',
        icon: 'Comment',
        description: '群组管理、模板配置、营销活动',
        children: [
          { path: '/community/groups', title: '社群列表', icon: 'UserFilled' },
          { path: '/community/add-enhanced', title: '创建群组', icon: 'Plus' },
          { path: '/community/templates', title: '模板管理', icon: 'Document' },
          { path: '/community/marketing', title: '营销配置', icon: 'Promotion' },
          { path: '/community/analytics', title: '数据分析', icon: 'DataLine' }
        ]
      },
      {
        path: '/orders',
        title: '订单管理',
        icon: 'ShoppingCart',
        description: '订单处理、支付管理、售后服务',
        children: [
          { path: '/orders/list', title: '订单列表', icon: 'List' },
          { path: '/orders/refunds', title: '退款管理', icon: 'RefreshLeft' },
          { path: '/orders/analytics', title: '订单分析', icon: 'DataAnalysis' }
        ]
      },
      {
        path: '/content',
        title: '内容管理',
        icon: 'Document',
        description: '内容审核、模板管理、素材库',
        children: [
          { path: '/content/moderation', title: '内容审核', icon: 'ShieldCheck' },
          { path: '/content/templates', title: '内容模板', icon: 'DocumentCopy' },
          { path: '/content/materials', title: '素材库', icon: 'Picture' }
        ]
      }
    ]
  },

  // 分销体系 - 代理商和分销员管理
  distributionSystem: {
    title: '分销体系',
    icon: 'Share',
    order: 3,
    description: '代理商、分销员、客户关系管理',
    children: [
      {
        path: '/agents',
        title: '代理商管理',
        icon: 'Avatar',
        description: '代理商层级、申请审核、绩效管理',
        children: [
          { path: '/agents/list', title: '代理商列表', icon: 'UserFilled' },
          { path: '/agents/applications', title: '申请审核', icon: 'DocumentChecked' },
          { path: '/agents/hierarchy', title: '层级管理', icon: 'Connection' },
          { path: '/agents/performance', title: '绩效分析', icon: 'TrendCharts' }
        ]
      },
      {
        path: '/distributors',
        title: '分销员管理',
        icon: 'User',
        description: '分销员管理、客户分配、业绩跟踪',
        children: [
          { path: '/distributors/list', title: '分销员列表', icon: 'UserFilled' },
          { path: '/distributors/customers', title: '客户管理', icon: 'UserFilled' },
          { path: '/distributors/performance', title: '业绩统计', icon: 'DataAnalysis' }
        ]
      },
      {
        path: '/customers',
        title: '客户管理',
        icon: 'UserFilled',
        description: '客户信息、标签管理、行为分析',
        children: [
          { path: '/customers/list', title: '客户列表', icon: 'User' },
          { path: '/customers/tags', title: '标签管理', icon: 'PriceTag' },
          { path: '/customers/analytics', title: '行为分析', icon: 'DataLine' }
        ]
      }
    ]
  },

  // 财务中心 - 所有财务相关功能
  financeCenter: {
    title: '财务中心',
    icon: 'Money',
    order: 4,
    description: '财务总览、佣金管理、支付配置',
    children: [
      {
        path: '/finance/overview',
        title: '财务总览',
        icon: 'DataBoard',
        description: '收入支出、利润分析、财务报表'
      },
      {
        path: '/finance/commission',
        title: '佣金管理',
        icon: 'Coin',
        description: '佣金计算、发放记录、提现管理',
        children: [
          { path: '/finance/commission/rules', title: '佣金规则', icon: 'Setting' },
          { path: '/finance/commission/logs', title: '佣金记录', icon: 'List' },
          { path: '/finance/commission/withdrawals', title: '提现管理', icon: 'Money' }
        ]
      },
      {
        path: '/admin/payment-settings',
        title: '支付管理',
        icon: 'CreditCard',
        description: '支付配置、交易记录、对账管理',
        children: [
          { path: '/admin/payment-settings', title: '支付配置', icon: 'Setting' },
          { path: '/admin/payment-orders', title: '交易记录', icon: 'List' },
          { path: '/admin/payment-logs', title: '支付日志', icon: 'DocumentChecked' }
        ]
      }
    ]
  },

  // 营销推广 - 营销工具和推广功能
  marketingPromotion: {
    title: '营销推广',
    icon: 'Promotion',
    order: 5,
    description: '推广链接、营销活动、防红系统',
    children: [
      {
        path: '/promotion/links',
        title: '推广链接',
        icon: 'Link',
        description: '推广链接生成、统计分析'
      },
      {
        path: '/promotion/campaigns',
        title: '营销活动',
        icon: 'Present',
        description: '活动策划、效果跟踪'
      },
      {
        path: '/anti-red',
        title: '防红系统',
        icon: 'Shield',
        description: '链接防红、域名管理、安全检测'
      }
    ]
  },

  // 用户中心 - 用户管理功能
  userCenter: {
    title: '用户中心',
    icon: 'User',
    order: 6,
    description: '用户管理、权限配置、个人中心',
    children: [
      {
        path: '/users',
        title: '用户管理',
        icon: 'UserFilled',
        description: '用户列表、信息管理、状态控制',
        children: [
          { path: '/users/list', title: '用户列表', icon: 'User' },
          { path: '/users/roles', title: '角色管理', icon: 'Avatar' },
          { path: '/users/permissions', title: '权限配置', icon: 'Lock' }
        ]
      },
      {
        path: '/user/center',
        title: '个人中心',
        icon: 'UserFilled',
        description: '个人信息、账户设置'
      }
    ]
  },

  // 系统管理 - 系统配置和管理功能
  systemManagement: {
    title: '系统管理',
    icon: 'Setting',
    order: 7,
    description: '系统配置、安全管理、日志监控',
    children: [
      {
        path: '/system/settings',
        title: '系统设置',
        icon: 'Tools',
        description: '基础配置、参数设置'
      },
      {
        path: '/system/security',
        title: '安全管理',
        icon: 'Lock',
        description: '安全策略、访问控制'
      },
      {
        path: '/system/logs',
        title: '日志管理',
        icon: 'Document',
        description: '操作日志、系统日志、错误日志'
      },
      {
        path: '/system/monitor',
        title: '系统监控',
        icon: 'Monitor',
        description: '性能监控、资源使用情况'
      }
    ]
  }
}

// 角色专属导航配置
export const roleSpecificNavigation = {
  admin: {
    visibleGroups: ['dataCenter', 'businessManagement', 'distributionSystem', 'financeCenter', 'marketingPromotion', 'userCenter', 'systemManagement'],
    defaultGroup: 'dataCenter'
  },
  substation: {
    visibleGroups: ['dataCenter', 'businessManagement', 'distributionSystem', 'financeCenter', 'userCenter'],
    defaultGroup: 'dataCenter'
  },
  agent: {
    visibleGroups: ['distributionSystem', 'financeCenter', 'marketingPromotion'],
    defaultGroup: 'distributionSystem',
    customWorkbench: '/agents/dashboard'
  },
  distributor: {
    visibleGroups: ['businessManagement', 'distributionSystem', 'financeCenter', 'marketingPromotion'],
    defaultGroup: 'distributionSystem',
    customWorkbench: '/distributors/dashboard'
  },
  group_owner: {
    visibleGroups: ['businessManagement', 'userCenter'],
    defaultGroup: 'businessManagement',
    customWorkbench: '/community/my-groups'
  },
  user: {
    visibleGroups: ['userCenter'],
    defaultGroup: 'userCenter',
    customWorkbench: '/user/center'
  }
}

// 快捷操作配置
export const quickActions = {
  global: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '数据大屏', path: '/data-screen', icon: 'DataBoard', color: '#67c23a' },
    { title: '用户管理', path: '/users/list', icon: 'User', color: '#e6a23c' },
    { title: '财务总览', path: '/finance/overview', icon: 'Money', color: '#f56c6c' }
  ],
  admin: [
    { title: '系统监控', path: '/system/monitor', icon: 'Monitor', color: '#909399' },
    { title: '安全管理', path: '/system/security', icon: 'Lock', color: '#f56c6c' }
  ],
  agent: [
    { title: '代理商申请', path: '/agents/applications', icon: 'DocumentChecked', color: '#409eff' },
    { title: '绩效分析', path: '/agents/performance', icon: 'TrendCharts', color: '#67c23a' }
  ],
  distributor: [
    { title: '客户管理', path: '/distributors/customers', icon: 'UserFilled', color: '#e6a23c' },
    { title: '推广链接', path: '/promotion/links', icon: 'Link', color: '#409eff' }
  ]
}

// 搜索配置
export const searchConfig = {
  // 可搜索的菜单项
  searchableMenus: [],
  // 搜索快捷键
  shortcuts: {
    globalSearch: 'Ctrl+K',
    toggleSidebar: 'Ctrl+B',
    quickCreate: 'Ctrl+N'
  },
  // 搜索分类
  categories: [
    { key: 'menus', title: '菜单功能', icon: 'Menu' },
    { key: 'users', title: '用户', icon: 'User' },
    { key: 'groups', title: '群组', icon: 'UserFilled' },
    { key: 'orders', title: '订单', icon: 'ShoppingCart' },
    { key: 'content', title: '内容', icon: 'Document' }
  ]
}

// 初始化搜索菜单数据
export function initializeSearchableMenus() {
  const searchableMenus = []
  
  Object.entries(optimizedNavigationGroups).forEach(([groupKey, group]) => {
    // 添加分组标题
    searchableMenus.push({
      id: `group-${groupKey}`,
      title: group.title,
      description: group.description,
      type: 'group',
      icon: group.icon,
      path: null,
      keywords: [group.title, group.description]
    })
    
    // 添加子菜单
    group.children.forEach(child => {
      searchableMenus.push({
        id: `menu-${child.path}`,
        title: child.title,
        description: child.description || '',
        type: 'menu',
        icon: child.icon,
        path: child.path,
        group: group.title,
        keywords: [child.title, child.description || '', group.title]
      })
      
      // 添加子子菜单
      if (child.children) {
        child.children.forEach(subChild => {
          searchableMenus.push({
            id: `submenu-${subChild.path}`,
            title: subChild.title,
            description: `${child.title} - ${subChild.title}`,
            type: 'submenu',
            icon: subChild.icon,
            path: subChild.path,
            parent: child.title,
            group: group.title,
            keywords: [subChild.title, child.title, group.title]
          })
        })
      }
    })
  })
  
  searchConfig.searchableMenus = searchableMenus
  return searchableMenus
}
