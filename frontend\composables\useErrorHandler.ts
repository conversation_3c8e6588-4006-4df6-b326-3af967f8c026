/**
 * 错误处理 Composable
 * 提供统一的错误处理机制
 */

export interface ErrorInfo {
  code?: string | number
  message: string
  type: 'network' | 'validation' | 'auth' | 'server' | 'business' | 'unknown'
  details?: any
}

export const useErrorHandler = () => {
  const toast = useNuxtApp().$toast

  /**
   * 处理API错误
   */
  const handleApiError = (error: any): ErrorInfo => {
    let errorInfo: ErrorInfo = {
      message: '操作失败，请重试',
      type: 'unknown'
    }

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      errorInfo.code = status

      switch (status) {
        case 400:
          errorInfo = {
            code: status,
            message: data?.message || '请求参数错误',
            type: 'validation',
            details: data?.errors
          }
          break
        case 401:
          errorInfo = {
            code: status,
            message: '登录已过期，请重新登录',
            type: 'auth'
          }
          // 清理认证状态
          const authStore = useAuthStore()
          authStore.clearAuth()
          navigateTo('/login')
          break
        case 403:
          errorInfo = {
            code: status,
            message: '权限不足，无法访问',
            type: 'auth'
          }
          break
        case 404:
          errorInfo = {
            code: status,
            message: '请求的资源不存在',
            type: 'server'
          }
          break
        case 422:
          errorInfo = {
            code: status,
            message: data?.message || '输入数据验证失败',
            type: 'validation',
            details: data?.errors
          }
          break
        case 429:
          errorInfo = {
            code: status,
            message: '请求过于频繁，请稍后再试',
            type: 'server'
          }
          break
        case 500:
          errorInfo = {
            code: status,
            message: '服务器内部错误，请稍后重试',
            type: 'server'
          }
          break
        case 502:
          errorInfo = {
            code: status,
            message: '网关错误，请稍后重试',
            type: 'server'
          }
          break
        case 503:
          errorInfo = {
            code: status,
            message: '服务暂时不可用，请稍后重试',
            type: 'server'
          }
          break
        default:
          errorInfo = {
            code: status,
            message: data?.message || `请求失败 (${status})`,
            type: 'server'
          }
      }
    } else if (error.request) {
      // 网络错误
      errorInfo = {
        message: '网络连接失败，请检查网络设置',
        type: 'network'
      }
    } else {
      // 请求配置错误
      errorInfo = {
        message: error.message || '请求配置错误',
        type: 'unknown'
      }
    }

    return errorInfo
  }

  /**
   * 处理业务逻辑错误
   */
  const handleBusinessError = (error: any): ErrorInfo => {
    return {
      message: error.message || '业务处理失败',
      type: 'business',
      details: error.details
    }
  }

  /**
   * 处理验证错误
   */
  const handleValidationError = (errors: Record<string, string[]>): ErrorInfo => {
    const firstError = Object.values(errors)[0]?.[0]
    return {
      message: firstError || '表单验证失败',
      type: 'validation',
      details: errors
    }
  }

  /**
   * 显示错误消息
   */
  const showError = (errorInfo: ErrorInfo) => {
    // 根据错误类型选择不同的显示方式
    switch (errorInfo.type) {
      case 'validation':
        if (toast) {
          toast.error(errorInfo.message, {
            duration: 5000
          })
        } else {
          console.error('Validation Error:', errorInfo.message)
        }
        break
      case 'auth':
        if (toast) {
          toast.warning(errorInfo.message, {
            duration: 3000
          })
        } else {
          console.warn('Auth Error:', errorInfo.message)
        }
        break
      case 'network':
        if (toast) {
          toast.error(errorInfo.message, {
            duration: 8000
          })
        } else {
          console.error('Network Error:', errorInfo.message)
        }
        break
      default:
        if (toast) {
          toast.error(errorInfo.message, {
            duration: 5000
          })
        } else {
          console.error('Error:', errorInfo.message)
        }
    }
  }

  /**
   * 统一错误处理入口
   */
  const handleError = (error: any, showToast = true): ErrorInfo => {
    let errorInfo: ErrorInfo

    if (error.response || error.request) {
      // API错误
      errorInfo = handleApiError(error)
    } else if (error.type === 'business') {
      // 业务错误
      errorInfo = handleBusinessError(error)
    } else if (error.errors) {
      // 验证错误
      errorInfo = handleValidationError(error.errors)
    } else {
      // 其他错误
      errorInfo = {
        message: error.message || '未知错误',
        type: 'unknown'
      }
    }

    // 记录错误日志
    console.error('Error handled:', errorInfo)

    // 显示错误消息
    if (showToast) {
      showError(errorInfo)
    }

    return errorInfo
  }

  /**
   * 创建业务错误
   */
  const createBusinessError = (message: string, details?: any): Error => {
    const error = new Error(message) as any
    error.type = 'business'
    error.details = details
    return error
  }

  return {
    handleError,
    handleApiError,
    handleBusinessError,
    handleValidationError,
    showError,
    createBusinessError
  }
}
