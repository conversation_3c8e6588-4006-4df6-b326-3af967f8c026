import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 用户偏好设置存储
 * 管理用户的个性化配置和界面偏好
 */
export const usePreferencesStore = defineStore('preferences', () => {
  // 状态定义
  const preferences = ref({
    // 界面设置
    interface: {
      theme: 'auto', // light, dark, auto
      primaryColor: '#3b82f6',
      sidebarPosition: 'left', // left, right
      compactMode: false,
      animationsEnabled: true,
      language: 'zh-CN'
    },
    
    // 功能设置
    features: {
      quickActions: [], // 用户自定义快速操作
      dashboardWidgets: [], // 用户自定义仪表板组件
      navigationPinned: [], // 用户固定的导航项
      searchFilters: [], // 用户自定义搜索过滤器
      notificationSettings: {
        desktop: true,
        email: false,
        sms: false,
        groupCreationAlerts: true // 群组创建相关通知
      }
    },
    
    // 工作流设置
    workflows: {
      customShortcuts: [], // 自定义快捷键
      automationRules: [], // 自动化规则
      templatePreferences: [], // 模板偏好
      dataViewPreferences: {
        pageSize: 20,
        sortOrder: 'desc',
        defaultView: 'table'
      }
    },
    
    // AI设置
    ai: {
      recommendationsEnabled: true,
      learningEnabled: true,
      personalizedContent: true,
      smartNotifications: true
    },
    
    // 性能设置
    performance: {
      enableAnimations: true,
      lazyLoading: true,
      cacheEnabled: true,
      prefetchEnabled: true
    }
  })

  // 用户行为数据
  const behaviorData = ref({
    // 功能使用统计
    featureUsage: {},
    // 页面访问统计
    pageViews: {},
    // 搜索历史
    searchHistory: [],
    // 操作时间统计
    operationTimes: {},
    // 错误统计
    errorCounts: {}
  })

  // AI推荐数据
  const aiRecommendations = ref([])
  
  // 个性化数据
  const personalizationData = ref({
    // 用户兴趣标签
    interests: [],
    // 使用模式
    usagePatterns: {},
    // 偏好权重
    preferenceWeights: {},
    // 学习数据
    learningData: {}
  })

  // 计算属性
  const currentTheme = computed(() => {
    if (preferences.value.interface.theme === 'auto') {
      // 根据系统时间自动切换
      const hour = new Date().getHours()
      return hour >= 6 && hour < 18 ? 'light' : 'dark'
    }
    return preferences.value.interface.theme
  })

  const isCompactMode = computed(() => preferences.value.interface.compactMode)
  
  const primaryColor = computed(() => preferences.value.interface.primaryColor)
  
  const enabledNotifications = computed(() => {
    return Object.entries(preferences.value.features.notificationSettings)
      .filter(([key, value]) => value)
      .map(([key]) => key)
  })

  // 获取个性化快速操作
  const personalizedQuickActions = computed(() => {
    const defaultActions = [
      {
        id: 'create_group',
        title: '创建群组',
        description: '快速创建新的微信群组',
        icon: 'ChatDotRound',
        iconClass: 'text-blue-500',
        protected: true, // 群组创建功能保护
        priority: 1,
        category: 'core'
      },
      {
        id: 'user_management',
        title: '用户管理',
        description: '管理系统用户和权限',
        icon: 'User',
        iconClass: 'text-green-500',
        priority: 2,
        category: 'management'
      },
      {
        id: 'data_analysis',
        title: '数据分析',
        description: '查看业务数据和统计报表',
        icon: 'DataAnalysis',
        iconClass: 'text-purple-500',
        priority: 3,
        category: 'analytics'
      }
    ]

    // 合并用户自定义和默认操作
    const customActions = preferences.value.features.quickActions || []
    const mergedActions = [...defaultActions, ...customActions]
    
    // 根据使用频率和AI推荐排序
    return mergedActions.sort((a, b) => {
      // 保护功能优先
      if (a.protected && !b.protected) return -1
      if (!a.protected && b.protected) return 1
      
      // 根据使用频率排序
      const aUsage = behaviorData.value.featureUsage[a.id] || 0
      const bUsage = behaviorData.value.featureUsage[b.id] || 0
      
      return bUsage - aUsage
    })
  })

  // 获取个性化仪表板组件
  const personalizedDashboardWidgets = computed(() => {
    const defaultWidgets = [
      {
        id: 'stats_overview',
        title: '数据概览',
        component: 'StatsOverviewWidget',
        size: 'large',
        priority: 1,
        config: { showTrends: true }
      },
      {
        id: 'group_activity',
        title: '群组活动',
        component: 'GroupActivityWidget',
        size: 'medium',
        priority: 2,
        config: { limit: 10, showGroupCreation: true } // 突出显示群组创建
      }
    ]

    const customWidgets = preferences.value.features.dashboardWidgets || []
    return [...defaultWidgets, ...customWidgets].sort((a, b) => a.priority - b.priority)
  })

  // 方法定义
  
  /**
   * 加载用户偏好设置
   */
  const loadUserPreferences = async () => {
    try {
      // 从localStorage加载本地设置
      const localPrefs = localStorage.getItem('user_preferences')
      if (localPrefs) {
        const parsed = JSON.parse(localPrefs)
        preferences.value = { ...preferences.value, ...parsed }
      }

      // 尝试从服务器加载云端设置（如果API可用）
      try {
        const response = await fetch('/api/user/preferences')
        if (response.ok) {
          const serverPrefs = await response.json()
          preferences.value = { ...preferences.value, ...serverPrefs.data }
        }
      } catch (apiError) {
        console.log('API不可用，使用本地设置')
      }

      // 加载行为数据
      await loadBehaviorData()
      
      // 加载AI推荐（如果API可用）
      try {
        await loadAIRecommendations()
      } catch (aiError) {
        console.log('AI推荐API不可用，使用默认推荐')
      }
      
    } catch (error) {
      console.log('加载用户偏好设置失败，使用默认设置:', error)
    }
  }

  /**
   * 保存用户偏好设置
   */
  const saveUserPreferences = async () => {
    try {
      // 保存到localStorage
      localStorage.setItem('user_preferences', JSON.stringify(preferences.value))

      // 保存到服务器
      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences.value)
      })

      if (!response.ok) {
        throw new Error('保存偏好设置失败')
      }

      return true
    } catch (error) {
      console.error('保存用户偏好设置失败:', error)
      return false
    }
  }

  /**
   * 更新界面设置
   */
  const updateInterfaceSettings = (settings) => {
    preferences.value.interface = { ...preferences.value.interface, ...settings }
    saveUserPreferences()
  }

  /**
   * 保存快速操作设置
   */
  const saveQuickActions = (actions) => {
    // 确保群组创建功能始终存在
    const hasGroupCreation = actions.some(action => action.id === 'create_group')
    if (!hasGroupCreation) {
      actions.unshift({
        id: 'create_group',
        title: '创建群组',
        description: '快速创建新的微信群组',
        icon: 'ChatDotRound',
        iconClass: 'text-blue-500',
        protected: true
      })
    }

    preferences.value.features.quickActions = actions
    saveUserPreferences()
  }

  /**
   * 保存仪表板组件设置
   */
  const saveDashboardWidgets = (widgets) => {
    preferences.value.features.dashboardWidgets = widgets
    saveUserPreferences()
  }

  /**
   * 记录功能使用
   */
  const recordFeatureUsage = (featureId) => {
    if (!behaviorData.value.featureUsage[featureId]) {
      behaviorData.value.featureUsage[featureId] = 0
    }
    behaviorData.value.featureUsage[featureId]++
    
    // 特别记录群组创建功能使用
    if (featureId === 'create_group') {
      recordGroupCreationUsage()
    }
    
    // 异步保存行为数据
    saveBehaviorData()
  }

  /**
   * 记录群组创建功能使用
   */
  const recordGroupCreationUsage = () => {
    const timestamp = Date.now()
    if (!behaviorData.value.groupCreationHistory) {
      behaviorData.value.groupCreationHistory = []
    }
    behaviorData.value.groupCreationHistory.push(timestamp)
    
    // 只保留最近100次记录
    if (behaviorData.value.groupCreationHistory.length > 100) {
      behaviorData.value.groupCreationHistory = behaviorData.value.groupCreationHistory.slice(-100)
    }
  }

  /**
   * 记录页面访问
   */
  const recordPageView = (pagePath) => {
    if (!behaviorData.value.pageViews[pagePath]) {
      behaviorData.value.pageViews[pagePath] = 0
    }
    behaviorData.value.pageViews[pagePath]++
    saveBehaviorData()
  }

  /**
   * 记录搜索行为
   */
  const recordSearch = (query, results) => {
    const searchRecord = {
      query,
      resultsCount: results.length,
      timestamp: Date.now(),
      hasGroupCreation: results.some(r => r.id === 'create_group')
    }
    
    behaviorData.value.searchHistory.unshift(searchRecord)
    
    // 只保留最近50次搜索
    if (behaviorData.value.searchHistory.length > 50) {
      behaviorData.value.searchHistory = behaviorData.value.searchHistory.slice(0, 50)
    }
    
    saveBehaviorData()
  }

  /**
   * 加载行为数据
   */
  const loadBehaviorData = async () => {
    try {
      const localData = localStorage.getItem('user_behavior_data')
      if (localData) {
        behaviorData.value = { ...behaviorData.value, ...JSON.parse(localData) }
      }
    } catch (error) {
      console.error('加载行为数据失败:', error)
    }
  }

  /**
   * 保存行为数据
   */
  const saveBehaviorData = () => {
    try {
      localStorage.setItem('user_behavior_data', JSON.stringify(behaviorData.value))
    } catch (error) {
      console.error('保存行为数据失败:', error)
    }
  }

  /**
   * 加载AI推荐
   */
  const loadAIRecommendations = async () => {
    try {
      if (!preferences.value.ai.recommendationsEnabled) return

      // 尝试从API加载推荐
      try {
        const response = await fetch('/api/ai/recommendations')
        if (response.ok) {
          const data = await response.json()
          aiRecommendations.value = data.recommendations || []
          return
        }
      } catch (apiError) {
        console.log('AI推荐API不可用，使用默认推荐')
      }

      // 如果API不可用，使用默认推荐
      aiRecommendations.value = [
        {
          id: 'ai_group_creation',
          title: '智能群组创建助手',
          description: '基于您的使用习惯，推荐创建产品交流群',
          icon: 'ChatDotRound',
          confidence: 0.92,
          action: () => console.log('跳转到群组创建页面')
        },
        {
          id: 'ai_user_analysis',
          title: '用户行为分析',
          description: 'AI发现您经常使用数据分析功能，建议查看详细报表',
          icon: 'DataAnalysis',
          confidence: 0.87,
          action: () => console.log('跳转到数据分析页面')
        }
      ]
    } catch (error) {
      console.log('加载AI推荐失败，使用空推荐:', error)
      aiRecommendations.value = []
    }
  }

  /**
   * 获取个性化推荐
   */
  const getPersonalizedRecommendations = () => {
    const recommendations = []
    
    // 基于使用频率的推荐
    const leastUsedFeatures = Object.entries(behaviorData.value.featureUsage)
      .sort(([,a], [,b]) => a - b)
      .slice(0, 3)
      .map(([featureId]) => featureId)
    
    leastUsedFeatures.forEach(featureId => {
      recommendations.push({
        id: `feature_${featureId}`,
        type: 'feature_suggestion',
        title: `尝试使用${featureId}功能`,
        description: '这个功能可能对您有帮助',
        confidence: 0.7
      })
    })

    // 群组创建功能推荐（如果使用频率低）
    const groupCreationUsage = behaviorData.value.featureUsage['create_group'] || 0
    if (groupCreationUsage < 5) {
      recommendations.unshift({
        id: 'group_creation_suggestion',
        type: 'core_feature_suggestion',
        title: '创建您的第一个群组',
        description: '群组创建是平台的核心功能，快来体验吧！',
        confidence: 0.9,
        priority: 'high'
      })
    }

    return recommendations
  }

  /**
   * 重置偏好设置
   */
  const resetPreferences = () => {
    preferences.value = {
      interface: {
        theme: 'auto',
        primaryColor: '#3b82f6',
        sidebarPosition: 'left',
        compactMode: false,
        animationsEnabled: true,
        language: 'zh-CN'
      },
      features: {
        quickActions: [],
        dashboardWidgets: [],
        navigationPinned: [],
        searchFilters: [],
        notificationSettings: {
          desktop: true,
          email: false,
          sms: false,
          groupCreationAlerts: true
        }
      },
      workflows: {
        customShortcuts: [],
        automationRules: [],
        templatePreferences: [],
        dataViewPreferences: {
          pageSize: 20,
          sortOrder: 'desc',
          defaultView: 'table'
        }
      },
      ai: {
        recommendationsEnabled: true,
        learningEnabled: true,
        personalizedContent: true,
        smartNotifications: true
      },
      performance: {
        enableAnimations: true,
        lazyLoading: true,
        cacheEnabled: true,
        prefetchEnabled: true
      }
    }
    
    saveUserPreferences()
  }

  /**
   * 导出用户数据
   */
  const exportUserData = () => {
    const exportData = {
      preferences: preferences.value,
      behaviorData: behaviorData.value,
      exportTime: new Date().toISOString(),
      version: '1.0'
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `user_preferences_${Date.now()}.json`
    a.click()
    
    URL.revokeObjectURL(url)
  }

  /**
   * 导入用户数据
   */
  const importUserData = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const importData = JSON.parse(e.target.result)
          
          if (importData.preferences) {
            preferences.value = { ...preferences.value, ...importData.preferences }
          }
          
          if (importData.behaviorData) {
            behaviorData.value = { ...behaviorData.value, ...importData.behaviorData }
          }
          
          saveUserPreferences()
          saveBehaviorData()
          
          resolve(true)
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file)
    })
  }

  // 返回store接口
  return {
    // 状态
    preferences,
    behaviorData,
    aiRecommendations,
    personalizationData,
    
    // 计算属性
    currentTheme,
    isCompactMode,
    primaryColor,
    enabledNotifications,
    personalizedQuickActions,
    personalizedDashboardWidgets,
    
    // 方法
    loadUserPreferences,
    saveUserPreferences,
    updateInterfaceSettings,
    saveQuickActions,
    saveDashboardWidgets,
    recordFeatureUsage,
    recordPageView,
    recordSearch,
    loadAIRecommendations,
    getPersonalizedRecommendations,
    resetPreferences,
    exportUserData,
    importUserData
  }
})