<template>
  <div class="mobile-tab-bar" :class="{ 'safe-area': hasSafeArea }">
    <div class="tab-bar-container">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="handleTabClick(tab.key)"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
      >
        <div class="tab-icon">
          <Icon
            :name="activeTab === tab.key ? tab.activeIcon : tab.icon"
            :size="20"
            :color="activeTab === tab.key ? activeColor : inactiveColor"
          />
        </div>
        <span class="tab-label">{{ tab.label }}</span>
        <div v-if="tab.badge" class="tab-badge">
          {{ tab.badge > 99 ? '99+' : tab.badge }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

interface TabItem {
  key: string
  label: string
  icon: string
  activeIcon: string
  path?: string
  badge?: number
}

interface Props {
  tabs: TabItem[]
  activeTab: string
  activeColor?: string
  inactiveColor?: string
  hasSafeArea?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  activeColor: '#1890ff',
  inactiveColor: '#999',
  hasSafeArea: true
})

const emit = defineEmits<{
  tabChange: [key: string]
}>()

const router = useRouter()
const touchStartTime = ref(0)

const handleTabClick = (key: string) => {
  emit('tabChange', key)
  
  const tab = props.tabs.find(t => t.key === key)
  if (tab?.path) {
    router.push(tab.path)
  }
}

const handleTouchStart = (event: TouchEvent) => {
  touchStartTime.value = Date.now()
  const target = event.currentTarget as HTMLElement
  target.style.transform = 'scale(0.95)'
}

const handleTouchEnd = (event: TouchEvent) => {
  const target = event.currentTarget as HTMLElement
  target.style.transform = 'scale(1)'
  
  // 防止长按触发
  if (Date.now() - touchStartTime.value > 500) {
    event.preventDefault()
  }
}
</script>

<style scoped>
.mobile-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: transform 0.3s ease;
}

.mobile-tab-bar.safe-area {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.tab-bar-container {
  display: flex;
  height: 50px;
  align-items: center;
  justify-content: space-around;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-icon {
  margin-bottom: 2px;
  transition: all 0.2s ease;
}

.tab-item.active .tab-icon {
  transform: translateY(-2px);
}

.tab-label {
  font-size: 12px;
  line-height: 1;
  transition: all 0.2s ease;
}

.tab-item.active .tab-label {
  font-weight: 500;
}

.tab-badge {
  position: absolute;
  top: 2px;
  right: calc(50% - 15px);
  background: #ff4d4f;
  color: #fff;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  transform: scale(0.8);
}

/* 隐藏动画 */
.mobile-tab-bar.hidden {
  transform: translateY(100%);
}

/* 适配横屏 */
@media (max-height: 400px) and (orientation: landscape) {
  .mobile-tab-bar {
    display: none;
  }
}
</style>