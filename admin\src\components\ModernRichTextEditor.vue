<template>
  <div class="modern-rich-editor" :class="{ 'fullscreen': isFullscreen }">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <!-- 文本格式 -->
      <div class="toolbar-group">
        <button 
          type="button"
          class="toolbar-btn"
          :class="{ active: formatStates.bold }"
          @mousedown.prevent
          @click="toggleFormat('bold')"
          title="粗体 (Ctrl+B)"
        >
          <strong>B</strong>
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          :class="{ active: formatStates.italic }"
          @mousedown.prevent
          @click="toggleFormat('italic')"
          title="斜体 (Ctrl+I)"
        >
          <em>I</em>
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          :class="{ active: formatStates.underline }"
          @mousedown.prevent
          @click="toggleFormat('underline')"
          title="下划线 (Ctrl+U)"
        >
          <u>U</u>
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="toggleFormat('strikethrough')"
          title="删除线"
        >
          <s>S</s>
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 字体大小和颜色 -->
      <div class="toolbar-group">
        <select class="font-size-select" @change="changeFontSize($event)" title="字体大小">
          <option value="">字号</option>
          <option value="12px">12px</option>
          <option value="14px">14px</option>
          <option value="16px">16px</option>
          <option value="18px">18px</option>
          <option value="20px">20px</option>
          <option value="24px">24px</option>
          <option value="28px">28px</option>
        </select>
        <input 
          type="color" 
          class="color-picker" 
          @change="changeTextColor($event)"
          title="文字颜色"
          value="#000000"
        />
        <input 
          type="color" 
          class="color-picker" 
          @change="changeBackgroundColor($event)"
          title="背景颜色"
          value="#ffffff"
        />
      </div>

      <div class="toolbar-divider"></div>

      <!-- 对齐方式 -->
      <div class="toolbar-group">
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="setAlignment('left')"
          title="左对齐"
        >
          ⬅
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="setAlignment('center')"
          title="居中对齐"
        >
          ↔
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="setAlignment('right')"
          title="右对齐"
        >
          ➡
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <div class="toolbar-group">
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="toggleList('ul')"
          title="无序列表"
        >
          •
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="toggleList('ol')"
          title="有序列表"
        >
          1.
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 插入功能 -->
      <div class="toolbar-group">
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="insertLink"
          title="插入链接"
        >
          🔗
        </button>
        <button
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="showImageOptions"
          title="插入图片"
        >
          🖼️
        </button>
        <button
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="insertVideo"
          title="插入视频"
        >
          🎬
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="insertEmoji"
          title="插入表情"
        >
          😊
        </button>
      </div>

      <div class="toolbar-divider"></div>

      <!-- 其他功能 -->
      <div class="toolbar-group">
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="clearFormat"
          title="清除格式"
        >
          🧹
        </button>
        <button 
          type="button"
          class="toolbar-btn"
          @mousedown.prevent
          @click="toggleFullscreen"
          title="全屏编辑"
        >
          ⛶
        </button>
      </div>
    </div>
    
    <!-- 编辑区域 -->
    <div 
      ref="editorRef"
      class="editor-content"
      :style="{ minHeight: height + 'px' }"
      contenteditable="true"
      @input="handleInput"
      @compositionstart="handleCompositionStart"
      @compositionupdate="handleCompositionUpdate"
      @compositionend="handleCompositionEnd"
      @focus="handleFocus"
      @blur="handleBlur"
      @paste="handlePaste"
      @keydown="handleKeydown"
      @mouseup="updateFormatStates"
      @keyup="updateFormatStates"
      :data-placeholder="placeholder"
    ></div>
    
    <!-- 底部状态栏 -->
    <div class="editor-footer">
      <div class="editor-stats">
        <span class="word-count">字数: {{ wordCount }}</span>
        <span class="char-count">字符: {{ charCount }}</span>
        <span v-if="maxLength > 0" class="limit-info">
          / {{ maxLength }}
          <span v-if="wordCount > maxLength" class="over-limit">超出限制</span>
        </span>
      </div>
      <div class="editor-actions">
        <button type="button" class="action-btn" @click="clearAll" title="清空内容">
          清空
        </button>
      </div>
    </div>

    <!-- 表情选择器 -->
    <div v-if="showEmojiPicker" class="emoji-picker" @click.stop>
      <div class="emoji-header">
        <span>选择表情</span>
        <button @click="showEmojiPicker = false">×</button>
      </div>
      <div class="emoji-grid">
        <span
          v-for="emoji in commonEmojis"
          :key="emoji"
          class="emoji-item"
          @click="insertEmojiChar(emoji)"
        >
          {{ emoji }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 200
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  maxLength: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur'])

// 响应式数据
const editorRef = ref(null)
const content = ref('')
const isComposing = ref(false)
const isFocused = ref(false)
const isFullscreen = ref(false)
const showEmojiPicker = ref(false)
const savedRange = ref(null)

// 格式状态
const formatStates = ref({
  bold: false,
  italic: false,
  underline: false
})

// 常用表情
const commonEmojis = [
  '😊', '😂', '🤣', '😍', '🥰', '😘', '😗', '😙', '😚', '😋',
  '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳',
  '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖',
  '👍', '👎', '👌', '🤝', '👏', '🙌', '👐', '🤲', '🙏', '✍️',
  '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔'
]

// 计算属性
const wordCount = computed(() => {
  const text = editorRef.value?.innerText || ''
  return text.length
})

const charCount = computed(() => {
  const html = content.value || ''
  return html.length
})

// 保存和恢复光标位置
const saveSelection = () => {
  const selection = window.getSelection()
  if (selection.rangeCount > 0) {
    savedRange.value = selection.getRangeAt(0).cloneRange()
  }
}

const restoreSelection = () => {
  if (savedRange.value) {
    const selection = window.getSelection()
    selection.removeAllRanges()
    selection.addRange(savedRange.value)
  }
}

// 获取当前选区或光标位置
const getCurrentRange = () => {
  const selection = window.getSelection()
  if (selection.rangeCount > 0) {
    return selection.getRangeAt(0)
  }
  return null
}

// 设置光标位置到末尾
const setCursorToEnd = () => {
  if (editorRef.value) {
    const range = document.createRange()
    const selection = window.getSelection()
    range.selectNodeContents(editorRef.value)
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

// 中文输入处理
const handleCompositionStart = () => {
  isComposing.value = true
  saveSelection()
}

const handleCompositionUpdate = () => {
  // 输入过程中不做处理
}

const handleCompositionEnd = () => {
  isComposing.value = false
  // 延迟处理，确保输入完成
  nextTick(() => {
    handleInput()
  })
}

// 输入处理
const handleInput = () => {
  if (isComposing.value) return

  const html = editorRef.value?.innerHTML || ''
  content.value = html

  // 发射事件
  emit('update:modelValue', html)
  emit('change', html)

  // 检查字数限制
  if (props.maxLength > 0 && wordCount.value > props.maxLength) {
    ElMessage.warning(`内容长度不能超过 ${props.maxLength} 字`)
  }
}

// 焦点处理
const handleFocus = () => {
  isFocused.value = true
  emit('focus')
}

const handleBlur = () => {
  isFocused.value = false
  saveSelection()
  emit('blur')
}

// 粘贴处理
const handlePaste = (e) => {
  e.preventDefault()
  const text = e.clipboardData.getData('text/plain')
  insertText(text)
}

// 键盘快捷键
const handleKeydown = (e) => {
  if (e.ctrlKey || e.metaKey) {
    switch (e.key) {
      case 'b':
        e.preventDefault()
        toggleFormat('bold')
        break
      case 'i':
        e.preventDefault()
        toggleFormat('italic')
        break
      case 'u':
        e.preventDefault()
        toggleFormat('underline')
        break
      case 'z':
        if (e.shiftKey) {
          e.preventDefault()
          redo()
        } else {
          e.preventDefault()
          undo()
        }
        break
      case 'y':
        e.preventDefault()
        redo()
        break
    }
  }

  // 更新格式状态
  setTimeout(updateFormatStates, 10)
}

// 更新格式状态
const updateFormatStates = () => {
  if (!editorRef.value || isComposing.value) return

  try {
    formatStates.value.bold = document.queryCommandState('bold')
    formatStates.value.italic = document.queryCommandState('italic')
    formatStates.value.underline = document.queryCommandState('underline')
  } catch (error) {
    // 忽略错误
  }
}

// 切换格式
const toggleFormat = (format) => {
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand(format, false, null)
    updateFormatStates()
    handleInput()
  } catch (error) {
    console.warn('Format command failed:', format, error)
  }
}

// 设置对齐方式
const setAlignment = (align) => {
  if (!editorRef.value) return

  editorRef.value.focus()

  const alignCommands = {
    left: 'justifyLeft',
    center: 'justifyCenter',
    right: 'justifyRight',
    justify: 'justifyFull'
  }

  try {
    document.execCommand(alignCommands[align], false, null)
    handleInput()
  } catch (error) {
    console.warn('Alignment command failed:', align, error)
  }
}

// 切换列表
const toggleList = (listType) => {
  if (!editorRef.value) return

  editorRef.value.focus()

  const command = listType === 'ul' ? 'insertUnorderedList' : 'insertOrderedList'

  try {
    document.execCommand(command, false, null)
    handleInput()
  } catch (error) {
    console.warn('List command failed:', listType, error)
  }
}

// 改变字体大小
const changeFontSize = (event) => {
  const size = event.target.value
  if (!size || !editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('fontSize', false, '7')
    const fontElements = editorRef.value.querySelectorAll('font[size="7"]')
    fontElements.forEach(el => {
      el.removeAttribute('size')
      el.style.fontSize = size
    })
    handleInput()
  } catch (error) {
    console.warn('Font size command failed:', error)
  }

  event.target.value = ''
}

// 改变文字颜色
const changeTextColor = (event) => {
  const color = event.target.value
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('foreColor', false, color)
    handleInput()
  } catch (error) {
    console.warn('Text color command failed:', error)
  }
}

// 改变背景颜色
const changeBackgroundColor = (event) => {
  const color = event.target.value
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('backColor', false, color)
    handleInput()
  } catch (error) {
    console.warn('Background color command failed:', error)
  }
}

// 插入文本
const insertText = (text) => {
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('insertText', false, text)
    handleInput()
  } catch (error) {
    // Fallback method
    const selection = window.getSelection()
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(document.createTextNode(text))
      range.collapse(false)
      handleInput()
    }
  }
}

// 插入链接
const insertLink = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入链接地址', '插入链接', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+/,
      inputErrorMessage: '请输入有效的链接地址'
    })

    if (url && editorRef.value) {
      editorRef.value.focus()
      restoreSelection()

      try {
        document.execCommand('createLink', false, url)
        handleInput()
      } catch (error) {
        console.warn('Link insertion failed:', error)
      }
    }
  } catch (error) {
    // 用户取消
  }
}

// 显示图片选项
const showImageOptions = async () => {
  try {
    const { value: option } = await ElMessageBox.confirm(
      '请选择插入图片的方式',
      '插入图片',
      {
        confirmButtonText: '上传图片',
        cancelButtonText: '图片链接',
        distinguishCancelAndClose: true,
        type: 'info'
      }
    )
    // 用户点击确定（上传图片）
    uploadImage()
  } catch (action) {
    if (action === 'cancel') {
      // 用户点击取消（图片链接）
      insertImageByUrl()
    }
    // 用户点击关闭按钮，不做任何操作
  }
}

// 上传图片
const uploadImage = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        ElMessage.error('请选择图片文件')
        return
      }

      // 检查文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        ElMessage.error('图片大小不能超过5MB')
        return
      }

      // 显示加载提示
      const loadingMessage = ElMessage({
        message: '正在处理图片...',
        type: 'info',
        duration: 0
      })

      // 创建FileReader读取文件
      const reader = new FileReader()
      reader.onload = (event) => {
        loadingMessage.close()
        const base64 = event.target.result
        insertImageToEditor(base64)
      }
      reader.onerror = () => {
        loadingMessage.close()
        ElMessage.error('图片读取失败')
      }
      reader.readAsDataURL(file)
    }
  }
  input.click()
}

// 通过URL插入图片
const insertImageByUrl = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入图片地址', '插入图片', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,
      inputErrorMessage: '请输入有效的图片地址'
    })

    if (url) {
      insertImageToEditor(url)
    }
  } catch (error) {
    // 用户取消
  }
}

// 插入图片到编辑器
const insertImageToEditor = (src) => {
  if (!editorRef.value) return

  try {
    editorRef.value.focus()

    // 创建图片HTML
    const imgHtml = `<img src="${src}" style="max-width: 100%; height: auto; display: block; margin: 10px 0;" alt="插入的图片" />`

    // 使用execCommand插入HTML
    if (document.queryCommandSupported('insertHTML')) {
      document.execCommand('insertHTML', false, imgHtml)
    } else {
      // 备用方案：直接插入到编辑器末尾
      const currentContent = editorRef.value.innerHTML
      editorRef.value.innerHTML = currentContent + imgHtml
    }

    // 触发输入事件
    handleInput()

    ElMessage.success('图片插入成功')
  } catch (error) {
    console.warn('Image insertion failed:', error)
    ElMessage.error('图片插入失败')
  }
}

// 插入视频
const insertVideo = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入视频地址或嵌入代码', '插入视频', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '支持视频URL或iframe嵌入代码'
    })

    if (url && editorRef.value) {
      editorRef.value.focus()
      restoreSelection()

      try {
        let videoElement

        // 检查是否是iframe代码
        if (url.includes('<iframe') || url.includes('iframe')) {
          // 直接插入iframe代码
          const div = document.createElement('div')
          div.innerHTML = url
          div.style.margin = '10px 0'

          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(div)
            range.collapse(false)
          }
        } else {
          // 创建video元素
          videoElement = document.createElement('video')
          videoElement.src = url
          videoElement.controls = true
          videoElement.style.maxWidth = '100%'
          videoElement.style.height = 'auto'
          videoElement.style.display = 'block'
          videoElement.style.margin = '10px 0'

          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(videoElement)
            range.collapse(false)
          }
        }

        handleInput()
      } catch (error) {
        console.warn('Video insertion failed:', error)
        ElMessage.error('视频插入失败')
      }
    }
  } catch (error) {
    // 用户取消
  }
}

// 插入表情
const insertEmoji = () => {
  saveSelection()
  showEmojiPicker.value = !showEmojiPicker.value
}

// 插入表情字符
const insertEmojiChar = (emoji) => {
  showEmojiPicker.value = false

  if (editorRef.value) {
    editorRef.value.focus()
    restoreSelection()
    insertText(emoji)
  }
}

// 清除格式
const clearFormat = () => {
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('removeFormat', false, null)
    handleInput()
  } catch (error) {
    console.warn('Clear format failed:', error)
  }
}

// 撤销
const undo = () => {
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('undo', false, null)
    handleInput()
  } catch (error) {
    console.warn('Undo failed:', error)
  }
}

// 重做
const redo = () => {
  if (!editorRef.value) return

  editorRef.value.focus()

  try {
    document.execCommand('redo', false, null)
    handleInput()
  } catch (error) {
    console.warn('Redo failed:', error)
  }
}

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 清空内容
const clearAll = () => {
  if (editorRef.value) {
    editorRef.value.innerHTML = ''
    content.value = ''
    emit('update:modelValue', '')
    emit('change', '')
    editorRef.value.focus()
  }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value && !isComposing.value) {
    content.value = newValue || ''
    if (editorRef.value) {
      editorRef.value.innerHTML = content.value
    }
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (editorRef.value) {
    // 初始化内容
    editorRef.value.innerHTML = props.modelValue || ''
    content.value = props.modelValue || ''

    // 设置占位符样式
    updatePlaceholder()
  }
})

// 更新占位符
const updatePlaceholder = () => {
  if (!editorRef.value) return

  const isEmpty = !editorRef.value.innerText.trim()

  if (isEmpty && !isFocused.value) {
    editorRef.value.classList.add('empty')
  } else {
    editorRef.value.classList.remove('empty')
  }
}

// 监听焦点变化更新占位符
watch([isFocused, content], () => {
  updatePlaceholder()
})

// 点击外部关闭表情选择器
const handleClickOutside = (e) => {
  if (showEmojiPicker.value && !e.target.closest('.emoji-picker')) {
    showEmojiPicker.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.modern-rich-editor {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: #ffffff;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-rich-editor:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.modern-rich-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
}

/* 工具栏样式 */
.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  flex-wrap: wrap;
  gap: 8px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: #f0f2f5;
  border-color: #c0c4cc;
}

.toolbar-btn.active {
  background: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

/* 下拉菜单样式 */
:deep(.el-dropdown) {
  .toolbar-btn {
    margin-right: 0;
  }
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #dcdfe6;
  margin: 0 4px;
}

.font-size-select {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #ffffff;
  font-size: 12px;
  cursor: pointer;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  background: none;
}

/* 编辑区域样式 */
.editor-content {
  padding: 16px;
  min-height: 200px;
  max-height: 600px;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 14px;
  color: #303133;
  outline: none;
  position: relative;
}

.editor-content.empty::before {
  content: attr(data-placeholder);
  position: absolute;
  top: 16px;
  left: 16px;
  color: #c0c4cc;
  pointer-events: none;
  font-size: 14px;
}

.fullscreen .editor-content {
  max-height: calc(100vh - 120px);
}

/* 编辑器内容样式 */
.editor-content h1, .editor-content h2, .editor-content h3,
.editor-content h4, .editor-content h5, .editor-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.editor-content p {
  margin: 8px 0;
}

.editor-content ul, .editor-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.editor-content li {
  margin: 4px 0;
}

.editor-content a {
  color: #409eff;
  text-decoration: underline;
}

.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.editor-content blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  font-style: italic;
}

/* 底部状态栏 */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.editor-stats {
  display: flex;
  gap: 16px;
}

.over-limit {
  color: #f56c6c;
  font-weight: 600;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f0f2f5;
  border-color: #c0c4cc;
}

/* 表情选择器 */
.emoji-picker {
  position: absolute;
  top: 60px;
  right: 16px;
  width: 280px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.emoji-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.emoji-header button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #909399;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-header button:hover {
  color: #303133;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.emoji-item:hover {
  background: #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 12px;
    gap: 4px;
  }

  .toolbar-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .toolbar-group {
    gap: 2px;
  }

  .editor-content {
    padding: 12px;
    font-size: 13px;
  }

  .emoji-picker {
    width: 240px;
    right: 8px;
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 打印样式 */
@media print {
  .editor-toolbar,
  .editor-footer,
  .emoji-picker {
    display: none !important;
  }

  .modern-rich-editor {
    border: none;
    box-shadow: none;
  }

  .editor-content {
    padding: 0;
  }
}
</style>
