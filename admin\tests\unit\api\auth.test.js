/**
 * Auth API Service Unit Tests
 * 测试认证API服务的所有功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ElMessage } from 'element-plus'
import * as authApi from '@/api/auth'

// 模拟请求工具
const mockRequest = vi.fn()
vi.mock('@/utils/request', () => ({
  default: mockRequest
}))

describe('Auth API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockRequest.mockClear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Authentication', () => {
    describe('login', () => {
      it('should send login request with correct parameters', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              token: 'test-token',
              user: { id: 1, username: 'testuser' }
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const loginData = {
          username: 'testuser',
          password: 'password123'
        }

        const result = await authApi.login(loginData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/login',
          method: 'post',
          data: loginData
        })
        expect(result).toEqual(mockResponse)
      })

      it('should handle login request failure', async () => {
        const error = new Error('Login failed')
        mockRequest.mockRejectedValue(error)

        const loginData = {
          username: 'testuser',
          password: 'wrongpass'
        }

        await expect(authApi.login(loginData)).rejects.toThrow('Login failed')
      })

      it('should handle empty credentials', async () => {
        const emptyData = { username: '', password: '' }
        mockRequest.mockResolvedValue({ data: { success: false } })

        await authApi.login(emptyData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/login',
          method: 'post',
          data: emptyData
        })
      })
    })

    describe('logout', () => {
      it('should send logout request', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.logout()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/logout',
          method: 'post'
        })
        expect(result).toEqual(mockResponse)
      })

      it('should handle logout failure', async () => {
        const error = new Error('Logout failed')
        mockRequest.mockRejectedValue(error)

        await expect(authApi.logout()).rejects.toThrow('Logout failed')
      })
    })

    describe('refreshToken', () => {
      it('should send refresh token request', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: { token: 'new-token' }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.refreshToken()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/refresh',
          method: 'post'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('getInfo', () => {
      it('should get user info', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              user: {
                id: 1,
                username: 'testuser',
                role: 'admin'
              }
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getInfo()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/user',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('getUserInfo', () => {
      it('should get user info (alias method)', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              user: {
                id: 1,
                username: 'testuser'
              }
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getUserInfo()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/user',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Profile Management', () => {
    describe('updateProfile', () => {
      it('should update user profile', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const profileData = {
          nickname: 'New Nickname',
          email: '<EMAIL>',
          phone: '1234567890'
        }

        const result = await authApi.updateProfile(profileData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/profile',
          method: 'put',
          data: profileData
        })
        expect(result).toEqual(mockResponse)
      })

      it('should handle profile update failure', async () => {
        const error = new Error('Update failed')
        mockRequest.mockRejectedValue(error)

        const profileData = { nickname: 'Test' }

        await expect(authApi.updateProfile(profileData)).rejects.toThrow('Update failed')
      })
    })

    describe('changePassword', () => {
      it('should change password', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const passwordData = {
          current_password: 'oldpass',
          new_password: 'newpass',
          confirm_password: 'newpass'
        }

        const result = await authApi.changePassword(passwordData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/password',
          method: 'put',
          data: passwordData
        })
        expect(result).toEqual(mockResponse)
      })

      it('should validate password change data', async () => {
        const invalidData = {
          current_password: 'old',
          new_password: 'new',
          confirm_password: 'different'
        }
        mockRequest.mockResolvedValue({ data: { success: false } })

        await authApi.changePassword(invalidData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/password',
          method: 'put',
          data: invalidData
        })
      })
    })
  })

  describe('Permissions Management', () => {
    describe('getUserPermissions', () => {
      it('should get user permissions', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              permissions: ['user_management', 'order_view', 'dashboard']
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getUserPermissions()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/permissions',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('checkPermission', () => {
      it('should check specific permission', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: { hasPermission: true }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const permission = 'user_management'
        const result = await authApi.checkPermission(permission)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/check-permission',
          method: 'post',
          data: { permission }
        })
        expect(result).toEqual(mockResponse)
      })

      it('should handle permission check failure', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: { hasPermission: false }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const permission = 'unauthorized_action'
        const result = await authApi.checkPermission(permission)

        expect(result.data.data.hasPermission).toBe(false)
      })
    })
  })

  describe('Security Settings', () => {
    describe('getSecuritySettings', () => {
      it('should get security settings', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              twoFactorEnabled: false,
              loginNotifications: true,
              sessionTimeout: 24
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getSecuritySettings()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/security-settings',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('updateSecuritySettings', () => {
      it('should update security settings', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const settings = {
          loginNotifications: false,
          sessionTimeout: 12
        }

        const result = await authApi.updateSecuritySettings(settings)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/security-settings',
          method: 'put',
          data: settings
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('Two-Factor Authentication', () => {
      describe('enableTwoFactor', () => {
        it('should enable 2FA', async () => {
          const mockResponse = {
            data: {
              success: true,
              data: {
                qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANS...',
                backupCodes: ['123456', '789012']
              }
            }
          }
          mockRequest.mockResolvedValue(mockResponse)

          const twoFactorData = {
            password: 'currentpass',
            code: '123456'
          }

          const result = await authApi.enableTwoFactor(twoFactorData)

          expect(mockRequest).toHaveBeenCalledWith({
            url: '/admin/auth/2fa/enable',
            method: 'post',
            data: twoFactorData
          })
          expect(result).toEqual(mockResponse)
        })
      })

      describe('disableTwoFactor', () => {
        it('should disable 2FA', async () => {
          const mockResponse = { data: { success: true } }
          mockRequest.mockResolvedValue(mockResponse)

          const disableData = {
            password: 'currentpass',
            code: '123456'
          }

          const result = await authApi.disableTwoFactor(disableData)

          expect(mockRequest).toHaveBeenCalledWith({
            url: '/admin/auth/2fa/disable',
            method: 'post',
            data: disableData
          })
          expect(result).toEqual(mockResponse)
        })
      })

      describe('verifyTwoFactor', () => {
        it('should verify 2FA code', async () => {
          const mockResponse = {
            data: {
              success: true,
              data: { verified: true }
            }
          }
          mockRequest.mockResolvedValue(mockResponse)

          const verifyData = {
            code: '123456'
          }

          const result = await authApi.verifyTwoFactor(verifyData)

          expect(mockRequest).toHaveBeenCalledWith({
            url: '/admin/auth/2fa/verify',
            method: 'post',
            data: verifyData
          })
          expect(result).toEqual(mockResponse)
        })
      })
    })
  })

  describe('Login Logs', () => {
    describe('getLoginLogs', () => {
      it('should get login logs with pagination', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              logs: [
                {
                  id: 1,
                  login_time: '2024-01-15 10:30:00',
                  ip_address: '*************',
                  user_agent: 'Mozilla/5.0...',
                  success: true
                }
              ],
              pagination: {
                current_page: 1,
                total_pages: 5,
                total_records: 50
              }
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const params = {
          page: 1,
          limit: 10,
          start_date: '2024-01-01',
          end_date: '2024-01-31'
        }

        const result = await authApi.getLoginLogs(params)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/login-logs',
          method: 'get',
          params
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('getSecurityLogs', () => {
      it('should get security logs', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              logs: [
                {
                  id: 1,
                  event_type: 'password_change',
                  timestamp: '2024-01-15 10:30:00',
                  ip_address: '*************',
                  details: 'Password changed successfully'
                }
              ]
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const params = {
          event_type: 'password_change',
          limit: 20
        }

        const result = await authApi.getSecurityLogs(params)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/security-logs',
          method: 'get',
          params
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Session Management', () => {
    describe('getActiveSessions', () => {
      it('should get active sessions', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              sessions: [
                {
                  id: 'session-1',
                  created_at: '2024-01-15 09:00:00',
                  last_activity: '2024-01-15 10:30:00',
                  ip_address: '*************',
                  user_agent: 'Chrome/120.0',
                  is_current: true
                },
                {
                  id: 'session-2',
                  created_at: '2024-01-14 15:00:00',
                  last_activity: '2024-01-14 18:30:00',
                  ip_address: '*************',
                  user_agent: 'Firefox/121.0',
                  is_current: false
                }
              ]
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getActiveSessions()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/sessions',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('terminateSession', () => {
      it('should terminate specific session', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const sessionId = 'session-123'
        const result = await authApi.terminateSession(sessionId)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/sessions/${sessionId}`,
          method: 'delete'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('terminateAllSessions', () => {
      it('should terminate all sessions except current', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: { terminated_count: 3 }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.terminateAllSessions()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/sessions/all',
          method: 'delete'
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('API Key Management', () => {
    describe('getApiKeys', () => {
      it('should get API keys', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              keys: [
                {
                  id: 1,
                  name: 'Mobile App API',
                  key: 'ak_test_1234567890abcdef',
                  created_at: '2024-01-01 00:00:00',
                  last_used: '2024-01-15 10:30:00',
                  permissions: ['read', 'write']
                }
              ]
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getApiKeys()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/api-keys',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('createApiKey', () => {
      it('should create new API key', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              key: {
                id: 2,
                name: 'New API Key',
                key: 'ak_test_new1234567890',
                permissions: ['read']
              }
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const keyData = {
          name: 'New API Key',
          permissions: ['read'],
          expires_at: '2025-01-15'
        }

        const result = await authApi.createApiKey(keyData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/api-keys',
          method: 'post',
          data: keyData
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('updateApiKey', () => {
      it('should update API key', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const keyId = 1
        const updateData = {
          name: 'Updated API Key',
          permissions: ['read', 'write']
        }

        const result = await authApi.updateApiKey(keyId, updateData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/api-keys/${keyId}`,
          method: 'put',
          data: updateData
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('deleteApiKey', () => {
      it('should delete API key', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const keyId = 1
        const result = await authApi.deleteApiKey(keyId)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/api-keys/${keyId}`,
          method: 'delete'
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Password Reset', () => {
    describe('sendResetEmail', () => {
      it('should send password reset email', async () => {
        const mockResponse = {
          data: {
            success: true,
            message: 'Password reset email sent'
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const email = '<EMAIL>'
        const result = await authApi.sendResetEmail(email)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/password/reset-email',
          method: 'post',
          data: { email }
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('resetPassword', () => {
      it('should reset password with token', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const resetData = {
          token: 'reset-token-123',
          email: '<EMAIL>',
          password: 'newpassword',
          password_confirmation: 'newpassword'
        }

        const result = await authApi.resetPassword(resetData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/password/reset',
          method: 'post',
          data: resetData
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Account Management', () => {
    describe('lockAccount', () => {
      it('should lock user account', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const userId = 123
        const reason = 'Suspicious activity detected'

        const result = await authApi.lockAccount(userId, reason)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/accounts/${userId}/lock`,
          method: 'post',
          data: { reason }
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('unlockAccount', () => {
      it('should unlock user account', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const userId = 123
        const result = await authApi.unlockAccount(userId)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/accounts/${userId}/unlock`,
          method: 'post'
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Device Management', () => {
    describe('getTrustedDevices', () => {
      it('should get trusted devices', async () => {
        const mockResponse = {
          data: {
            success: true,
            data: {
              devices: [
                {
                  id: 1,
                  device_name: 'iPhone 12',
                  device_type: 'mobile',
                  added_at: '2024-01-01 00:00:00',
                  last_used: '2024-01-15 10:30:00'
                }
              ]
            }
          }
        }
        mockRequest.mockResolvedValue(mockResponse)

        const result = await authApi.getTrustedDevices()

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/trusted-devices',
          method: 'get'
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('addTrustedDevice', () => {
      it('should add trusted device', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const deviceData = {
          device_name: 'Work Laptop',
          device_fingerprint: 'fp_abc123'
        }

        const result = await authApi.addTrustedDevice(deviceData)

        expect(mockRequest).toHaveBeenCalledWith({
          url: '/admin/auth/trusted-devices',
          method: 'post',
          data: deviceData
        })
        expect(result).toEqual(mockResponse)
      })
    })

    describe('removeTrustedDevice', () => {
      it('should remove trusted device', async () => {
        const mockResponse = { data: { success: true } }
        mockRequest.mockResolvedValue(mockResponse)

        const deviceId = 1
        const result = await authApi.removeTrustedDevice(deviceId)

        expect(mockRequest).toHaveBeenCalledWith({
          url: `/admin/auth/trusted-devices/${deviceId}`,
          method: 'delete'
        })
        expect(result).toEqual(mockResponse)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle network timeout', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.code = 'ECONNABORTED'
      mockRequest.mockRejectedValue(timeoutError)

      await expect(authApi.login({ username: 'test', password: 'test' }))
        .rejects.toThrow('Request timeout')
    })

    it('should handle server errors', async () => {
      const serverError = new Error('Internal Server Error')
      serverError.response = { status: 500 }
      mockRequest.mockRejectedValue(serverError)

      await expect(authApi.login({ username: 'test', password: 'test' }))
        .rejects.toThrow('Internal Server Error')
    })

    it('should handle malformed responses', async () => {
      mockRequest.mockResolvedValue('not-json-response')

      const result = await authApi.login({ username: 'test', password: 'test' })
      expect(result).toBe('not-json-response')
    })
  })
})