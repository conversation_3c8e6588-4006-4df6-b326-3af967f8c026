import{_ as e,u as a,g as t,f as s,c as l}from"./index-DtXAftX0.js";/* empty css                    *//* empty css               *//* empty css                  *//* empty css                *//* empty css               */import{af as i,r as n,c,e as r,k as o,l as d,E as u,z as v,D as p,t as m,u as g,B as f,F as h,Y as y,y as _,ae as k,C as b,d as w,H as C,m as D,n as x,L as A,a6 as S,ah as z}from"./vue-vendor-Dy164gUc.js";import{U as $,at as I,aY as T,aZ as U,a_ as M,a$ as V,T as N,ao as H,b0 as R,b1 as E,aw as P,b2 as j,o as L,am as O,ax as W,Q as q,aS as F,X as J,aN as G,aa as B,a0 as K,a6 as Q,ak as Y,aA as X,aB as Z,aC as ee,az as ae,ay as te,as as se,p as le,b3 as ie,b4 as ne,aL as ce,aM as re,b5 as oe,b6 as de,an as ue,a7 as ve,ai as pe,ae as me,b7 as ge,aR as fe,b8 as he,b9 as ye,ba as _e,bb as ke,bc as be,bd as we,aT as Ce,be as De,bf as xe,$ as Ae}from"./element-plus-h2SQQM64.js";/* empty css                 */import{c as Se,g as ze,a as $e}from"./dataPermission-K5RJ0Kz8.js";import"./utils-D1VZuEZr.js";const Ie={class:"navigation-test-panel"},Te={class:"test-content"},Ue={class:"card-header"},Me={class:"status-info"},Ve={class:"status-item"},Ne={class:"status-item"},He={class:"status-item"},Re={class:"card-header"},Ee={class:"role-switch-content"},Pe={class:"role-buttons"},je={key:0,class:"role-info"},Le={class:"card-header"},Oe={class:"group-creation-content"},We={class:"creation-tests"},qe={class:"test-buttons"},Fe={class:"card-header"},Je={class:"navigation-content"},Ge={class:"navigation-stats"},Be={class:"stat-item"},Ke={class:"stat-number"},Qe={class:"stat-item"},Ye={class:"stat-number"},Xe={class:"stat-item"},Ze={class:"stat-number"},ea={class:"route-list"},aa={class:"routes"},ta={class:"card-header"},sa={class:"log-content"},la={class:"log-list"},ia={class:"log-time"},na={class:"log-message"},ca={__name:"NavigationTestPanel",setup(e){const l=i(),k=a(),b=n(!1),w=n(!1),C=n(""),D=n([]),x=[{key:"admin",name:"管理员"},{key:"substation",name:"分站管理员"},{key:"agent",name:"代理商"},{key:"distributor",name:"分销员"},{key:"group_owner",name:"群主"},{key:"user",name:"普通用户"}],A=c(()=>k.userInfo?.role||"user"),S=c(()=>{const e=Se(A.value);return{title:e?"✅ 群组创建功能可用":"❌ 群组创建功能不可用",type:e?"success":"error",description:`当前角色 ${t(A.value)} ${e?"拥有":"没有"}群组创建权限`}}),z=c(()=>{try{const e=l.options.routes.filter(e=>!(!e||"string"!=typeof e.path)&&("/login"!==e.path&&"/"!==e.path&&!e.meta?.hidden)),a=JSON.parse(JSON.stringify(e)),t=s(a,A.value);return B("info",`成功计算可见路由: ${t.length} 个`),t.length}catch(e){return B("error",`计算可见路由失败: ${e.message}`),console.error("路由计算错误详情:",e),0}}),F=c(()=>{try{const e=l.options.routes.filter(e=>!(!e||"string"!=typeof e.path)&&("/login"!==e.path&&"/"!==e.path&&!e.meta?.hidden)),a=JSON.parse(JSON.stringify(e));return s(a,A.value).slice(0,8)}catch(e){return B("error",`计算示例路由失败: ${e.message}`),[]}}),J=c(()=>{let e=0;return Se(A.value)&&(e+=1),e+=1,e+=1,e}),G=c(()=>({admin:5,substation:4,agent:4,distributor:4,group_owner:4,user:3}[A.value]||3)),B=(e,a)=>{D.value.unshift({type:e,message:a,time:new Date}),D.value.length>20&&(D.value=D.value.slice(0,20))},K=()=>{if(B("info","测试群组创建功能"),!Se(A.value))return B("error","当前角色没有群组创建权限"),void q.error("当前角色没有群组创建权限");try{l.push("/community/add"),B("success","成功导航到群组创建页面"),q.success("群组创建功能测试通过")}catch(e){B("error",`导航到群组创建页面失败: ${e.message}`),q.error("群组创建功能测试失败")}},Q=()=>{B("info","测试导航访问权限");const e=["/dashboard","/community/groups","/user/list","/finance/dashboard"];let a=0;e.forEach(e=>{try{a++,B("success",`路由 ${e} 访问权限正常`)}catch(t){B("error",`路由 ${e} 访问权限异常: ${t.message}`)}}),q.success(`导航权限测试完成，${a}/${e.length} 个路由可访问`)},Y=()=>{D.value=[],q.success("测试日志已清空")};return r(()=>{B("info","导航测试面板已加载"),B("info",`当前用户角色: ${t(A.value)}`);const e=localStorage.getItem("current-test-role");e&&e!==A.value&&B("info",`检测到保存的测试角色: ${t(e)}`),Se(A.value)?B("success","群组创建权限检测通过"):B("warning","群组创建权限检测失败"),B("info",`数据权限范围: ${ze(A.value)}`),B("info",`财务权限范围: ${$e(A.value)}`)}),(e,a)=>{const s=I,l=N,i=V,n=M,c=U,r=T,X=E,Z=W;return d(),o("div",Ie,[u(s,{type:"primary",onClick:a[0]||(a[0]=e=>b.value=!b.value),class:"test-toggle-btn",icon:b.value?"Hide":"View"},{default:v(()=>[p($(b.value?"隐藏测试面板":"显示导航测试面板"),1)]),_:1},8,["icon"]),u(Z,{modelValue:b.value,"onUpdate:modelValue":a[1]||(a[1]=e=>b.value=e),title:"🧪 导航系统和权限测试面板",direction:"rtl",size:"60%","with-header":!0},{default:v(()=>[m("div",Te,[u(r,{class:"status-card",shadow:"never"},{header:v(()=>[m("div",Ue,[u(l,null,{default:v(()=>[u(g(H))]),_:1}),a[2]||(a[2]=m("span",null,"当前状态",-1))])]),default:v(()=>[m("div",Me,[u(c,{gutter:16},{default:v(()=>[u(n,{span:8},{default:v(()=>{return[m("div",Ve,[a[3]||(a[3]=m("div",{class:"status-label"},"用户角色",-1)),u(i,{type:(e=A.value,{admin:"danger",substation:"warning",agent:"primary",distributor:"success",group_owner:"info",user:"default"}[e]||"default"),size:"large"},{default:v(()=>[p($(g(t)(A.value)),1)]),_:1},8,["type"])])];var e}),_:1}),u(n,{span:8},{default:v(()=>[m("div",Ne,[a[4]||(a[4]=m("div",{class:"status-label"},"群组创建权限",-1)),u(i,{type:g(Se)(A.value)?"success":"danger",size:"large"},{default:v(()=>[p($(g(Se)(A.value)?"✅ 允许":"❌ 禁止"),1)]),_:1},8,["type"])])]),_:1}),u(n,{span:8},{default:v(()=>[m("div",He,[a[5]||(a[5]=m("div",{class:"status-label"},"数据权限范围",-1)),u(i,{type:"info",size:"large"},{default:v(()=>[p($(g(ze)(A.value)),1)]),_:1})])]),_:1})]),_:1})])]),_:1}),u(r,{class:"role-test-card",shadow:"never"},{header:v(()=>[m("div",Re,[u(l,null,{default:v(()=>[u(g(R))]),_:1}),a[6]||(a[6]=m("span",null,"角色切换测试",-1))])]),default:v(()=>[m("div",Ee,[m("div",Pe,[(d(),o(h,null,y(x,e=>u(s,{key:e.key,type:A.value===e.key?"primary":"default",onClick:a=>(async e=>{if(!w.value){w.value=!0,C.value=e,B("info",`开始切换角色到: ${t(e)}`);try{await new Promise(e=>setTimeout(e,800));const a={id:"preview-user",username:"admin",nickname:`${t(e)} (预览)`,name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:e,roles:[e],permissions:"admin"===e?["*"]:[e]};k.setUserInfo(a),localStorage.setItem("preview-user-info",JSON.stringify(a)),localStorage.setItem("preview-mode","true"),localStorage.setItem("current-test-role",e),B("success",`角色切换成功: ${t(e)}`),q.success(`已切换到 ${t(e)}，页面即将刷新`),setTimeout(()=>{window.location.reload()},1500)}catch(a){B("error",`角色切换失败: ${a.message}`),q.error("角色切换失败")}finally{w.value=!1,C.value=""}}})(e.key),loading:w.value&&C.value===e.key,size:"small"},{default:v(()=>[p($(e.name),1)]),_:2},1032,["type","onClick","loading"])),64))]),A.value?(d(),o("div",je,[m("p",null,[a[7]||(a[7]=m("strong",null,"当前角色：",-1)),p($(g(t)(A.value)),1)]),m("p",null,[a[8]||(a[8]=m("strong",null,"数据权限：",-1)),p($(g(ze)(A.value)),1)]),m("p",null,[a[9]||(a[9]=m("strong",null,"财务权限：",-1)),p($(g($e)(A.value)),1)])])):f("",!0)])]),_:1}),u(r,{class:"group-creation-card",shadow:"never"},{header:v(()=>[m("div",Le,[u(l,null,{default:v(()=>[u(g(P))]),_:1}),a[10]||(a[10]=m("span",null,"群组创建功能测试",-1))])]),default:v(()=>[m("div",Oe,[u(X,{title:S.value.title,type:S.value.type,description:S.value.description,"show-icon":"",closable:!1},null,8,["title","type","description"]),m("div",We,[a[13]||(a[13]=m("h4",null,"测试群组创建访问：",-1)),m("div",qe,[u(s,{type:"success",onClick:K,disabled:!g(Se)(A.value)},{default:v(()=>[u(l,null,{default:v(()=>[u(g(P))]),_:1}),a[11]||(a[11]=p(" 测试创建群组 ",-1))]),_:1,__:[11]},8,["disabled"]),u(s,{type:"primary",onClick:Q},{default:v(()=>[u(l,null,{default:v(()=>[u(g(j))]),_:1}),a[12]||(a[12]=p(" 测试导航访问 ",-1))]),_:1,__:[12]})])])])]),_:1}),u(r,{class:"navigation-card",shadow:"never"},{header:v(()=>[m("div",Fe,[u(l,null,{default:v(()=>[u(g(j))]),_:1}),a[14]||(a[14]=m("span",null,"导航权限测试",-1))])]),default:v(()=>[m("div",Je,[m("div",Ge,[u(c,{gutter:16},{default:v(()=>[u(n,{span:8},{default:v(()=>[m("div",Be,[m("div",Ke,$(z.value),1),a[15]||(a[15]=m("div",{class:"stat-label"},"可访问路由",-1))])]),_:1}),u(n,{span:8},{default:v(()=>[m("div",Qe,[m("div",Ye,$(J.value),1),a[16]||(a[16]=m("div",{class:"stat-label"},"群组创建入口",-1))])]),_:1}),u(n,{span:8},{default:v(()=>[m("div",Xe,[m("div",Ze,$(G.value),1),a[17]||(a[17]=m("div",{class:"stat-label"},"快速操作",-1))])]),_:1})]),_:1})]),m("div",ea,[a[18]||(a[18]=m("h4",null,"可访问的主要路由：",-1)),m("div",aa,[(d(!0),o(h,null,y(F.value,e=>(d(),_(i,{key:e.path,type:e.path.includes("community/add")?"success":"default",class:"route-tag"},{default:v(()=>[p($(e.meta?.title||e.path),1)]),_:2},1032,["type"]))),128))])])])]),_:1}),u(r,{class:"log-card",shadow:"never"},{header:v(()=>[m("div",ta,[u(l,null,{default:v(()=>[u(g(O))]),_:1}),a[20]||(a[20]=m("span",null,"测试日志",-1)),u(s,{size:"small",onClick:Y},{default:v(()=>a[19]||(a[19]=[p("清空",-1)])),_:1,__:[19]})])]),default:v(()=>[m("div",sa,[m("div",la,[(d(!0),o(h,null,y(D.value.slice(0,10),(e,a)=>{return d(),o("div",{key:a,class:L(["log-item",e.type])},[m("span",ia,$((t=e.time,t.toLocaleTimeString())),1),m("span",na,$(e.message),1)],2);var t}),128))])])]),_:1})])]),_:1},8,["modelValue"])])}}},ra=e(ca,[["__scopeId","data-v-2cbec75f"]]),oa=k("preferences",()=>{const e=n({interface:{theme:"auto",primaryColor:"#3b82f6",sidebarPosition:"left",compactMode:!1,animationsEnabled:!0,language:"zh-CN"},features:{quickActions:[],dashboardWidgets:[],navigationPinned:[],searchFilters:[],notificationSettings:{desktop:!0,email:!1,sms:!1,groupCreationAlerts:!0}},workflows:{customShortcuts:[],automationRules:[],templatePreferences:[],dataViewPreferences:{pageSize:20,sortOrder:"desc",defaultView:"table"}},ai:{recommendationsEnabled:!0,learningEnabled:!0,personalizedContent:!0,smartNotifications:!0},performance:{enableAnimations:!0,lazyLoading:!0,cacheEnabled:!0,prefetchEnabled:!0}}),a=n({featureUsage:{},pageViews:{},searchHistory:[],operationTimes:{},errorCounts:{}}),t=n([]),s=n({interests:[],usagePatterns:{},preferenceWeights:{},learningData:{}}),l=c(()=>{if("auto"===e.value.interface.theme){const e=(new Date).getHours();return e>=6&&e<18?"light":"dark"}return e.value.interface.theme}),i=c(()=>e.value.interface.compactMode),r=c(()=>e.value.interface.primaryColor),o=c(()=>Object.entries(e.value.features.notificationSettings).filter(([e,a])=>a).map(([e])=>e)),d=c(()=>[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0,priority:1,category:"core"},{id:"user_management",title:"用户管理",description:"管理系统用户和权限",icon:"User",iconClass:"text-green-500",priority:2,category:"management"},{id:"data_analysis",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",priority:3,category:"analytics"},...e.value.features.quickActions||[]].sort((e,t)=>{if(e.protected&&!t.protected)return-1;if(!e.protected&&t.protected)return 1;const s=a.value.featureUsage[e.id]||0;return(a.value.featureUsage[t.id]||0)-s})),u=c(()=>[{id:"stats_overview",title:"数据概览",component:"StatsOverviewWidget",size:"large",priority:1,config:{showTrends:!0}},{id:"group_activity",title:"群组活动",component:"GroupActivityWidget",size:"medium",priority:2,config:{limit:10,showGroupCreation:!0}},...e.value.features.dashboardWidgets||[]].sort((e,a)=>e.priority-a.priority)),v=async()=>{try{localStorage.setItem("user_preferences",JSON.stringify(e.value));if(!(await fetch("/api/user/preferences",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e.value)})).ok)throw new Error("保存偏好设置失败");return!0}catch(a){return console.error("保存用户偏好设置失败:",a),!1}},p=()=>{const e=Date.now();a.value.groupCreationHistory||(a.value.groupCreationHistory=[]),a.value.groupCreationHistory.push(e),a.value.groupCreationHistory.length>100&&(a.value.groupCreationHistory=a.value.groupCreationHistory.slice(-100))},m=async()=>{try{const e=localStorage.getItem("user_behavior_data");e&&(a.value={...a.value,...JSON.parse(e)})}catch(e){console.error("加载行为数据失败:",e)}},g=()=>{try{localStorage.setItem("user_behavior_data",JSON.stringify(a.value))}catch(e){console.error("保存行为数据失败:",e)}},f=async()=>{try{if(!e.value.ai.recommendationsEnabled)return;try{const e=await fetch("/api/ai/recommendations");if(e.ok){const a=await e.json();return void(t.value=a.recommendations||[])}}catch(a){console.log("AI推荐API不可用，使用默认推荐")}t.value=[{id:"ai_group_creation",title:"智能群组创建助手",description:"基于您的使用习惯，推荐创建产品交流群",icon:"ChatDotRound",confidence:.92,action:()=>console.log("跳转到群组创建页面")},{id:"ai_user_analysis",title:"用户行为分析",description:"AI发现您经常使用数据分析功能，建议查看详细报表",icon:"DataAnalysis",confidence:.87,action:()=>console.log("跳转到数据分析页面")}]}catch(s){console.log("加载AI推荐失败，使用空推荐:",s),t.value=[]}};return{preferences:e,behaviorData:a,aiRecommendations:t,personalizationData:s,currentTheme:l,isCompactMode:i,primaryColor:r,enabledNotifications:o,personalizedQuickActions:d,personalizedDashboardWidgets:u,loadUserPreferences:async()=>{try{const s=localStorage.getItem("user_preferences");if(s){const a=JSON.parse(s);e.value={...e.value,...a}}try{const a=await fetch("/api/user/preferences");if(a.ok){const t=await a.json();e.value={...e.value,...t.data}}}catch(a){console.log("API不可用，使用本地设置")}await m();try{await f()}catch(t){console.log("AI推荐API不可用，使用默认推荐")}}catch(s){console.log("加载用户偏好设置失败，使用默认设置:",s)}},saveUserPreferences:v,updateInterfaceSettings:a=>{e.value.interface={...e.value.interface,...a},v()},saveQuickActions:a=>{a.some(e=>"create_group"===e.id)||a.unshift({id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0}),e.value.features.quickActions=a,v()},saveDashboardWidgets:a=>{e.value.features.dashboardWidgets=a,v()},recordFeatureUsage:e=>{a.value.featureUsage[e]||(a.value.featureUsage[e]=0),a.value.featureUsage[e]++,"create_group"===e&&p(),g()},recordPageView:e=>{a.value.pageViews[e]||(a.value.pageViews[e]=0),a.value.pageViews[e]++,g()},recordSearch:(e,t)=>{const s={query:e,resultsCount:t.length,timestamp:Date.now(),hasGroupCreation:t.some(e=>"create_group"===e.id)};a.value.searchHistory.unshift(s),a.value.searchHistory.length>50&&(a.value.searchHistory=a.value.searchHistory.slice(0,50)),g()},loadAIRecommendations:f,getPersonalizedRecommendations:()=>{const e=[];Object.entries(a.value.featureUsage).sort(([,e],[,a])=>e-a).slice(0,3).map(([e])=>e).forEach(a=>{e.push({id:`feature_${a}`,type:"feature_suggestion",title:`尝试使用${a}功能`,description:"这个功能可能对您有帮助",confidence:.7})});return(a.value.featureUsage.create_group||0)<5&&e.unshift({id:"group_creation_suggestion",type:"core_feature_suggestion",title:"创建您的第一个群组",description:"群组创建是平台的核心功能，快来体验吧！",confidence:.9,priority:"high"}),e},resetPreferences:()=>{e.value={interface:{theme:"auto",primaryColor:"#3b82f6",sidebarPosition:"left",compactMode:!1,animationsEnabled:!0,language:"zh-CN"},features:{quickActions:[],dashboardWidgets:[],navigationPinned:[],searchFilters:[],notificationSettings:{desktop:!0,email:!1,sms:!1,groupCreationAlerts:!0}},workflows:{customShortcuts:[],automationRules:[],templatePreferences:[],dataViewPreferences:{pageSize:20,sortOrder:"desc",defaultView:"table"}},ai:{recommendationsEnabled:!0,learningEnabled:!0,personalizedContent:!0,smartNotifications:!0},performance:{enableAnimations:!0,lazyLoading:!0,cacheEnabled:!0,prefetchEnabled:!0}},v()},exportUserData:()=>{const t={preferences:e.value,behaviorData:a.value,exportTime:(new Date).toISOString(),version:"1.0"},s=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),l=URL.createObjectURL(s),i=document.createElement("a");i.href=l,i.download=`user_preferences_${Date.now()}.json`,i.click(),URL.revokeObjectURL(l)},importUserData:t=>new Promise((s,l)=>{const i=new FileReader;i.onload=t=>{try{const l=JSON.parse(t.target.result);l.preferences&&(e.value={...e.value,...l.preferences}),l.behaviorData&&(a.value={...a.value,...l.behaviorData}),v(),g(),s(!0)}catch(i){l(i)}},i.onerror=()=>l(new Error("文件读取失败")),i.readAsText(t)})}}),da={class:"intelligent-workbench"},ua={class:"workbench-header"},va={class:"user-greeting"},pa={class:"greeting-content"},ma={class:"greeting-title"},ga={class:"greeting-subtitle"},fa={class:"user-avatar"},ha={class:"user-status"},ya={key:0,class:"ai-recommendation-banner"},_a={class:"banner-icon"},ka={class:"banner-content"},ba={class:"quick-actions-section"},wa={class:"section-header"},Ca={class:"quick-actions-grid"},Da=["onClick"],xa={class:"action-icon"},Aa={class:"action-content"},Sa={key:0,class:"action-stats"},za={class:"stat-item"},$a={key:0,class:"action-badge"},Ia={class:"dashboard-widgets-section"},Ta={class:"section-header"},Ua={class:"widget-header"},Ma={class:"widget-content"},Va={class:"activity-section"},Na={class:"activity-card"},Ha={class:"card-header"},Ra={class:"activity-list"},Ea={class:"activity-icon"},Pa={class:"activity-content"},ja={class:"activity-title"},La={class:"activity-time"},Oa={key:0,class:"activity-action"},Wa={class:"reminders-card"},qa={class:"card-header"},Fa={class:"reminders-list"},Ja={class:"reminder-icon"},Ga={class:"reminder-content"},Ba={class:"reminder-title"},Ka={class:"reminder-desc"},Qa={class:"reminder-actions"},Ya={class:"widget-gallery"},Xa=["onClick"],Za={class:"widget-preview"},et={class:"widget-info"},at={class:"customize-content"},tt={class:"available-actions"},st={class:"actions-list"},lt=["onClick"],it={class:"selected-actions"},nt={class:"actions-list"},ct=e({__name:"IntelligentWorkbench",setup(e){const t=i(),s=a(),k=oa(),w=n(!1),C=n(!1),D=n(null),x=c(()=>s.userInfo||{name:"用户",avatar:"",role:"user"}),A=c(()=>{const e={admin:{title:"管理员智能控制台",description:"系统全局管理和智能监控中心"},substation:{title:"分站智能管理中心",description:"分站运营和团队协作智能助手"},agent:{title:"代理商智能工作台",description:"团队管理和业绩分析智能平台"},distributor:{title:"分销员智能助手",description:"客户管理和销售优化智能工具"},group_owner:{title:"群主智能运营台",description:"群组内容和成员管理智能中心"},user:{title:"个人智能助手",description:"个人信息和订单管理智能平台"}};return e[x.value.role]||e.user}),S=c(()=>k.aiRecommendations||[{id:1,type:"feature_suggestion",title:"智能群组创建助手",description:"基于您的使用习惯，推荐使用群组批量管理功能",message:"基于您的使用习惯，推荐使用群组批量管理功能",action:"navigate",target:"/groups/batch-manage",confidence:.85}]),z=c(()=>{const e=x.value?.role||"user",a=(l(e)||[]).map(e=>({id:e.path.replace(/\//g,"_"),title:e.title,description:T(e.title),icon:e.icon,iconClass:H(e.icon),protected:"创建群组"===e.title,stats:{usage:Math.floor(200*Math.random())+50},action:()=>t.push(e.path)}));return a.find(e=>"创建群组"===e.title)||a.unshift({id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"Plus",iconClass:"text-blue-500",protected:!0,stats:{usage:156},action:()=>t.push("/community/add")}),a}),T=e=>({"创建群组":"快速创建新的微信群组","支付设置":"配置支付渠道和参数","防红配置":"配置防封系统和域名管理","用户管理":"管理系统用户和权限","代理商管理":"管理代理商和团队","数据大屏":"查看全屏数据展示","系统监控":"监控系统运行状态"}[e]||`管理${e}相关功能`),H=e=>({Plus:"text-blue-500",CreditCard:"text-green-500",Shield:"text-orange-500",User:"text-purple-500",Avatar:"text-red-500",DataBoard:"text-indigo-500",Monitor:"text-gray-500"}[e]||"text-gray-500"),R=n([{id:"stats_overview",title:"数据概览",component:"StatsOverviewWidget",size:"large",config:{showTrends:!0},data:{}},{id:"recent_groups",title:"最近群组",component:"RecentGroupsWidget",size:"medium",config:{limit:5},data:{}},{id:"performance_chart",title:"性能监控",component:"PerformanceChartWidget",size:"medium",config:{timeRange:"7d"},data:{}}]),E=n([{id:"todo_list",title:"待办事项",description:"管理您的任务和提醒",icon:"Folder",component:"TodoListWidget"},{id:"system_monitor",title:"系统监控",description:"实时系统状态监控",icon:"Monitor",component:"SystemMonitorWidget"}]),j=n([{id:1,title:'创建了新群组"产品交流群"',time:new Date(Date.now()-18e5),icon:"ChatDotRound",iconClass:"text-blue-500",actionable:!0,actionText:"查看"},{id:2,title:"处理了5个用户申请",time:new Date(Date.now()-72e5),icon:"User",iconClass:"text-green-500"}]),O=n([{id:1,title:"群组活跃度下降",description:"有3个群组本周活跃度下降超过20%",priority:"high",action:"view_groups"},{id:2,title:"系统更新可用",description:"新版本包含性能优化和安全更新",priority:"medium",action:"update_system"}]),W=c(()=>O.value.filter(e=>"high"===e.priority).length),q=n([{id:"file_management",title:"文件管理",icon:"Folder",action:()=>t.push("/files")}]),le=()=>{const e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},ie=()=>{C.value=!0},ne=()=>{console.log("自定义仪表板")},ce=e=>{const[a,t]=e.split("-");console.log(`Widget action: ${a} for ${t}`)},re=(e,a)=>{const t=R.value.find(a=>a.id===e);t&&(t.data={...t.data,...a})},oe=()=>{t.push("/activities")},de=()=>{console.log("自定义提醒设置")},ue=()=>{k.saveQuickActions(z.value),C.value=!1},ve=e=>{const a=new Date-e,t=Math.floor(a/6e4),s=Math.floor(a/36e5);return t<60?`${t}分钟前`:s<24?`${s}小时前`:e.toLocaleDateString()};return r(()=>{s.userInfo||s.enterPreviewMode();try{k.loadUserPreferences()}catch(e){console.log("用户偏好设置加载失败，使用默认设置")}}),(e,a)=>{const s=F,l=N,i=G,n=I,c=V,r=ee,k=Z,T=ae,H=M,pe=U,me=te;return d(),o("div",da,[m("div",ua,[m("div",va,[m("div",pa,[m("h2",ma,$(le())+"，"+$(x.value?.name||"用户")+"！ ",1),m("p",ga,$(A.value.description),1)]),m("div",fa,[u(s,{size:60,src:x.value?.avatar},{default:v(()=>[p($(x.value?.name?.charAt(0)||"U"),1)]),_:1},8,["src"]),m("div",ha,[u(i,{value:W.value,max:99,class:"notification-badge"},{default:v(()=>[u(l,null,{default:v(()=>[u(g(J))]),_:1})]),_:1},8,["value"])])])]),S.value.length>0?(d(),o("div",ya,[m("div",_a,[u(l,null,{default:v(()=>[u(g(B))]),_:1})]),m("div",ka,[a[5]||(a[5]=m("h4",null,"🤖 AI智能推荐",-1)),m("p",null,$(S.value[0].message||"基于您的使用习惯，为您推荐相关功能"),1)]),u(n,{type:"primary",size:"small",onClick:a[0]||(a[0]=e=>{var a;"navigate"===(a=S.value[0]).action&&t.push(a.target)})},{default:v(()=>a[6]||(a[6]=[p(" 立即体验 ",-1)])),_:1,__:[6]})])):f("",!0)]),m("div",ba,[m("div",wa,[a[8]||(a[8]=m("h3",null,"⚡ 快速操作",-1)),u(n,{text:"",onClick:ie},{default:v(()=>[u(l,null,{default:v(()=>[u(g(K))]),_:1}),a[7]||(a[7]=p(" 自定义 ",-1))]),_:1,__:[7]})]),m("div",Ca,[(d(!0),o(h,null,y(z.value,e=>(d(),o("div",{key:e.id,class:L(["quick-action-card",{"protected-action":e.protected}]),onClick:a=>(e=>{e.action&&e.action()})(e)},[m("div",xa,[u(l,{class:L(e.iconClass)},{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1032,["class"])]),m("div",Aa,[m("h4",null,$(e.title),1),m("p",null,$(e.description),1),e.stats?(d(),o("div",Sa,[m("span",za,[u(l,null,{default:v(()=>[u(g(Q))]),_:1}),p(" "+$(e.stats.usage)+"次使用 ",1)])])):f("",!0)]),e.protected?(d(),o("div",$a,[u(c,{type:"warning",size:"small"},{default:v(()=>a[9]||(a[9]=[p("核心功能",-1)])),_:1,__:[9]})])):f("",!0)],10,Da))),128))])]),m("div",Ia,[m("div",Ta,[a[11]||(a[11]=m("h3",null,"📊 个人仪表板",-1)),u(n,{text:"",onClick:ne},{default:v(()=>[u(l,null,{default:v(()=>[u(g(Y))]),_:1}),a[10]||(a[10]=p(" 布局设置 ",-1))]),_:1,__:[10]})]),m("div",{class:"widgets-grid",ref_key:"widgetsContainer",ref:D},[(d(!0),o(h,null,y(R.value,e=>(d(),o("div",{key:e.id,class:L(["widget-card",`widget-${e.size}`])},[m("div",Ua,[m("h4",null,$(e.title),1),u(T,{onCommand:ce},{dropdown:v(()=>[u(k,null,{default:v(()=>[u(r,{command:`refresh-${e.id}`},{default:v(()=>a[12]||(a[12]=[p("刷新",-1)])),_:2,__:[12]},1032,["command"]),u(r,{command:`settings-${e.id}`},{default:v(()=>a[13]||(a[13]=[p("设置",-1)])),_:2,__:[13]},1032,["command"]),u(r,{command:`remove-${e.id}`,divided:""},{default:v(()=>a[14]||(a[14]=[p("移除",-1)])),_:2,__:[14]},1032,["command"])]),_:2},1024)]),default:v(()=>[u(l,{class:"widget-menu"},{default:v(()=>[u(g(X))]),_:1})]),_:2},1024)]),m("div",Ma,[(d(),_(b(e.component),{config:e.config,data:e.data,onUpdate:re},null,40,["config","data"]))])],2))),128)),m("div",{class:"add-widget-card",onClick:a[1]||(a[1]=e=>w.value=!0)},[u(l,{class:"add-icon"},{default:v(()=>[u(g(P))]),_:1}),a[15]||(a[15]=m("p",null,"添加组件",-1))])],512)]),m("div",Va,[u(pe,{gutter:24},{default:v(()=>[u(H,{span:12},{default:v(()=>[m("div",Na,[m("div",Ha,[a[17]||(a[17]=m("h3",null,"🕒 最近活动",-1)),u(n,{text:"",size:"small",onClick:oe},{default:v(()=>a[16]||(a[16]=[p("查看全部",-1)])),_:1,__:[16]})]),m("div",Ra,[(d(!0),o(h,null,y(j.value,e=>(d(),o("div",{key:e.id,class:"activity-item"},[m("div",Ea,[u(l,{class:L(e.iconClass)},{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1032,["class"])]),m("div",Pa,[m("p",ja,$(e.title),1),m("p",La,$(ve(e.time)),1)]),e.actionable?(d(),o("div",Oa,[u(n,{size:"small",text:"",onClick:a=>(e=>{console.log("处理活动:",e)})(e)},{default:v(()=>[p($(e.actionText),1)]),_:2},1032,["onClick"])])):f("",!0)]))),128))])])]),_:1}),u(H,{span:12},{default:v(()=>[m("div",Wa,[m("div",qa,[a[19]||(a[19]=m("h3",null,"💡 智能提醒",-1)),u(n,{text:"",size:"small",onClick:de},{default:v(()=>a[18]||(a[18]=[p("设置",-1)])),_:1,__:[18]})]),m("div",Fa,[(d(!0),o(h,null,y(O.value,e=>(d(),o("div",{key:e.id,class:L(["reminder-item",`reminder-${e.priority}`])},[m("div",Ja,[u(l,null,{default:v(()=>[u(g(J))]),_:1})]),m("div",Ga,[m("p",Ba,$(e.title),1),m("p",Ka,$(e.description),1)]),m("div",Qa,[u(n,{size:"small",onClick:a=>(e=>{console.log("处理提醒:",e)})(e)},{default:v(()=>a[20]||(a[20]=[p(" 处理 ",-1)])),_:2,__:[20]},1032,["onClick"]),u(n,{size:"small",text:"",onClick:a=>(e=>{const a=O.value.findIndex(a=>a.id===e);a>-1&&O.value.splice(a,1)})(e.id)},{default:v(()=>a[21]||(a[21]=[p(" 忽略 ",-1)])),_:2,__:[21]},1032,["onClick"])])],2))),128))])])]),_:1})]),_:1})]),u(me,{modelValue:w.value,"onUpdate:modelValue":a[2]||(a[2]=e=>w.value=e),title:"添加仪表板组件",width:"600px",class:"add-widget-dialog"},{default:v(()=>[m("div",Ya,[(d(!0),o(h,null,y(E.value,e=>(d(),o("div",{key:e.id,class:"widget-option",onClick:a=>(e=>{const a={id:`${e.id}_${Date.now()}`,title:e.title,component:e.component,size:"medium",config:{},data:{}};R.value.push(a),w.value=!1})(e)},[m("div",Za,[u(l,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)]),m("div",et,[m("h4",null,$(e.title),1),m("p",null,$(e.description),1)])],8,Xa))),128))])]),_:1},8,["modelValue"]),u(me,{modelValue:C.value,"onUpdate:modelValue":a[4]||(a[4]=e=>C.value=e),title:"自定义快速操作",width:"700px",class:"customize-dialog"},{footer:v(()=>[u(n,{onClick:a[3]||(a[3]=e=>C.value=!1)},{default:v(()=>a[24]||(a[24]=[p("取消",-1)])),_:1,__:[24]}),u(n,{type:"primary",onClick:ue},{default:v(()=>a[25]||(a[25]=[p("保存设置",-1)])),_:1,__:[25]})]),default:v(()=>[m("div",at,[m("div",tt,[a[22]||(a[22]=m("h4",null,"可用操作",-1)),m("div",st,[(d(!0),o(h,null,y(q.value,e=>(d(),o("div",{key:e.id,class:"action-item",onClick:a=>(e=>{const a=q.value.findIndex(a=>a.id===e.id);a>-1&&(q.value.splice(a,1),z.value.push(e))})(e)},[u(l,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024),m("span",null,$(e.title),1)],8,lt))),128))])]),m("div",it,[a[23]||(a[23]=m("h4",null,"已选操作",-1)),m("div",nt,[(d(!0),o(h,null,y(z.value,e=>(d(),o("div",{key:e.id,class:"action-item selected"},[u(l,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024),m("span",null,$(e.title),1),u(l,{class:"remove-icon",onClick:a=>(e=>{const a=z.value.findIndex(a=>a.id===e);if(a>-1){const e=z.value.splice(a,1)[0];q.value.push(e)}})(e.id)},{default:v(()=>[u(g(se))]),_:2},1032,["onClick"])]))),128))])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-fccaab6a"]]),rt={class:"default-item"},ot={key:0,class:"loading-indicator"},dt={key:1,class:"no-more-indicator"},ut={key:2,class:"empty-state"},vt=e({__name:"VirtualScrollList",props:{items:{type:Array,default:()=>[]},itemHeight:{type:Number,default:60},containerHeight:{type:Number,default:400},bufferSize:{type:Number,default:5},keyField:{type:String,default:"id"},infiniteScroll:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},hasMore:{type:Boolean,default:!0},loadMoreThreshold:{type:Number,default:100}},emits:["loadMore","scroll","itemClick"],setup(e,{expose:a,emit:t}){const s=e,l=t,i=n(null),p=n(0),_=c(()=>s.items.length*s.itemHeight),k=c(()=>Math.ceil(s.containerHeight/s.itemHeight)+2*s.bufferSize),b=c(()=>{const e=Math.floor(p.value/s.itemHeight)-s.bufferSize;return Math.max(0,e)}),A=c(()=>{const e=b.value+k.value;return Math.min(s.items.length,e)}),S=c(()=>s.items.slice(b.value,A.value)),z=c(()=>b.value*s.itemHeight),I=e=>{const a=e.target;if(p.value=a.scrollTop,l("scroll",{scrollTop:p.value,scrollHeight:a.scrollHeight,clientHeight:a.clientHeight}),s.infiniteScroll&&s.hasMore&&!s.loading){a.scrollHeight-a.scrollTop-a.clientHeight<=s.loadMoreThreshold&&l("loadMore")}},T=(e,a)=>"object"==typeof e&&e[s.keyField]?e[s.keyField]:a,U=e=>{if(i.value){const a=e*s.itemHeight;i.value.scrollTop=a,p.value=a}},M=()=>{i.value&&(i.value.scrollTop=i.value.scrollHeight)};let V=null;const H=e=>{V&&clearTimeout(V),V=setTimeout(()=>{I(e)},16)};return w(()=>s.items.length,()=>{x(()=>{if(i.value){const{scrollTop:e,scrollHeight:a,clientHeight:t}=i.value;a-e-t<50&&s.infiniteScroll&&x(()=>{M()})}})}),a({scrollToIndex:U,scrollToTop:()=>{U(0)},scrollToBottom:M,getVisibleRange:()=>({start:b.value,end:A.value})}),r(()=>{i.value&&i.value.addEventListener("scroll",H,{passive:!0})}),C(()=>{i.value&&i.value.removeEventListener("scroll",H),V&&clearTimeout(V)}),(a,t)=>{const s=N;return d(),o("div",{class:"virtual-scroll-list",style:le({height:e.containerHeight+"px"}),onScroll:I,ref_key:"containerRef",ref:i},[m("div",{class:"scroll-content",style:le({height:_.value+"px",paddingTop:z.value+"px"})},[(d(!0),o(h,null,y(S.value,(t,s)=>(d(),o("div",{key:T(t,b.value+s),class:"scroll-item",style:le({height:e.itemHeight+"px"})},[D(a.$slots,"default",{item:t,index:b.value+s,isVisible:!0},()=>[m("div",rt,$(t),1)],!0)],4))),128))],4),e.loading?(d(),o("div",ot,[u(s,{class:"is-loading"},{default:v(()=>[u(g(ie))]),_:1}),t[0]||(t[0]=m("span",null,"加载中...",-1))])):f("",!0),!e.hasMore&&e.items.length>0?(d(),o("div",dt,t[1]||(t[1]=[m("span",null,"没有更多数据了",-1)]))):f("",!0),0!==e.items.length||e.loading?f("",!0):(d(),o("div",ut,[u(s,{class:"empty-icon"},{default:v(()=>[u(g(ne))]),_:1}),t[2]||(t[2]=m("p",null,"暂无数据",-1))]))],36)}}},[["__scopeId","data-v-ce92c55f"]]),pt={class:"ai-enhanced-search"},mt={class:"dialog-header"},gt={class:"header-title"},ft={class:"header-actions"},ht={class:"search-content"},yt={class:"search-input-container"},_t={class:"input-actions"},kt={key:0,class:"search-suggestions"},bt={key:0,class:"ai-analysis"},wt={class:"analysis-indicator"},Ct={class:"search-results-container"},Dt={key:0,class:"recommendations"},xt={key:0,class:"recommendation-section"},At={class:"section-header"},St=["onClick"],zt={class:"item-icon ai-icon"},$t={class:"item-content"},It={class:"item-title"},Tt={class:"item-desc"},Ut={key:0,class:"ai-confidence"},Mt={class:"item-badge"},Vt={class:"recommendation-section"},Nt={class:"section-header"},Ht=["onClick"],Rt={class:"item-content"},Et={class:"item-title"},Pt={class:"item-desc"},jt={key:0,class:"item-badge"},Lt={key:1,class:"recommendation-section"},Ot={class:"section-header"},Wt=["onClick"],qt={class:"item-icon"},Ft={class:"item-content"},Jt={class:"item-title"},Gt={class:"item-time"},Bt={key:1,class:"search-results"},Kt={class:"results-header"},Qt={class:"search-time"},Yt={key:0,class:"ai-explanation"},Xt={class:"explanation-header"},Zt=["onClick"],es={class:"result-content"},as=["innerHTML"],ts={class:"result-path"},ss={key:0,class:"result-description"},ls={class:"result-badges"},is={key:2,class:"ai-score"},ns={key:2,class:"no-results"},cs={class:"no-results-icon"},rs={class:"search-suggestions"},os={key:0,class:"voice-indicator"},ds=e({__name:"AIEnhancedSearch",setup(e){const t=i(),s=a(),l=oa(),k=n(!1),D=n(""),A=n(null),S=n(0),z=n(!1),T=n(!1),U=n(0),M=n(""),R=n([{id:"ai_group_creation",title:"智能群组创建助手",description:"基于您的历史数据，AI建议创建产品交流群",icon:"ChatDotRound",confidence:.92,action:()=>t.push("/groups/create?ai=true")},{id:"ai_user_analysis",title:"用户行为分析",description:"AI发现用户活跃度异常，建议查看详细分析",icon:"DataAnalysis",confidence:.87,action:()=>t.push("/analytics/users?ai=true")}]),E=n(["创建群组","用户管理","数据分析","订单处理","系统设置"]),P=n([]),j=c(()=>s.currentUser),O=c(()=>[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",protected:!0,path:"/groups/create",action:()=>t.push("/groups/create")},{id:"user_management",title:"用户管理",description:"管理系统用户和权限",icon:"User",iconClass:"text-green-500",path:"/users",action:()=>t.push("/users")},{id:"data_analysis",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",path:"/analytics",action:()=>t.push("/analytics")}].filter(e=>{if("create_group"===e.id)return!0;const a=j.value?.role;return"admin"===a||!("substation"!==a||!["user_management","data_analysis"].includes(e.id))})),W=c(()=>{const e=l.behaviorData.featureUsage||{};return Object.entries(e).sort(([,e],[,a])=>a-e).slice(0,5).map(([e,a])=>({id:e,title:pe(e),icon:me(e),lastUsed:new Date(Date.now()-864e5*Math.random())}))}),F=()=>{k.value=!0,x(()=>{A.value?.focus()})},J=()=>{k.value=!1,D.value="",S.value=0,M.value=""},G=()=>{D.value="",S.value=0,P.value=[],M.value=""},K=async e=>{if(!e.trim())return P.value=[],void(M.value="");z.value=!0;const a=Date.now();try{await new Promise(e=>setTimeout(e,300));const t=await Q(e);P.value=t,U.value=Date.now()-a,M.value=X(e,t),l.recordSearch(e,t)}catch(t){console.error("搜索失败:",t)}finally{z.value=!1}},Q=async e=>{const a=e.toLowerCase(),t=[];return[{id:"create_group",title:"创建群组",description:"快速创建新的微信群组",icon:"ChatDotRound",iconClass:"text-blue-500",path:"/groups/create",protected:!0,keywords:["创建","群组","微信","新建","group","create"],category:"core"},{id:"manage_users",title:"用户管理",description:"管理系统用户和权限设置",icon:"User",iconClass:"text-green-500",path:"/users",keywords:["用户","管理","权限","user","manage"],category:"management"},{id:"analytics",title:"数据分析",description:"查看业务数据和统计报表",icon:"DataAnalysis",iconClass:"text-purple-500",path:"/analytics",keywords:["数据","分析","统计","报表","analytics","data"],category:"analytics"}].forEach(s=>{let l=0;s.title.toLowerCase().includes(a)&&(l+=10),s.description.toLowerCase().includes(a)&&(l+=5),s.keywords.forEach(e=>{e.toLowerCase().includes(a)&&(l+=3)}),s.path.toLowerCase().includes(a)&&(l+=2),s.protected&&(l+=5);const i=Y(e,s);l+=i,l>0&&t.push({...s,aiScore:Math.min(l/20,1),matchScore:l})}),t.sort((e,a)=>a.matchScore-e.matchScore)},Y=(e,a)=>{let t=0;return Object.entries({"创建":["群组","新建","添加"],"管理":["用户","设置","配置"],"分析":["数据","统计","报表"],"群组":["创建","管理","设置"]}).forEach(([s,l])=>{e.includes(s)&&l.forEach(e=>{(a.title.includes(e)||a.description.includes(e))&&(t+=2)})}),t},X=(e,a)=>{if(0===a.length)return`AI未能理解查询"${e}"，请尝试使用更具体的关键词。`;const t=a[0],s=Math.round(100*t.aiScore);return`AI理解您想要"${e}"，推荐使用"${t.title}"功能（匹配度${s}%）。`},Z=e=>{const a=D.value?P.value.length:R.value.length+O.value.length;switch(e.key){case"ArrowDown":e.preventDefault(),S.value=Math.min(S.value+1,a-1);break;case"ArrowUp":e.preventDefault(),S.value=Math.max(S.value-1,0);break;case"Enter":e.preventDefault(),ee();break;case"Escape":e.preventDefault(),J()}},ee=()=>{if(D.value&&P.value.length>0){const e=P.value[S.value];e&&ae(e)}else if(!D.value){const e=[...R.value,...O.value][S.value];e&&(e.action?e.action():ae(e))}},ae=e=>{l.recordFeatureUsage(e.id),e.action?e.action():e.path&&t.push(e.path),J()},le=()=>{T.value?ue():ne()},ne=()=>{if("webkitSpeechRecognition"in window||"SpeechRecognition"in window){T.value=!0;const e=new(window.SpeechRecognition||window.webkitSpeechRecognition);e.lang="zh-CN",e.continuous=!1,e.interimResults=!1,e.onresult=e=>{const a=e.results[0][0].transcript;D.value=a,K(a),T.value=!1},e.onerror=()=>{T.value=!1},e.onend=()=>{T.value=!1},e.start()}else q.warning("您的浏览器不支持语音识别功能")},ue=()=>{T.value=!1},ve=(e,a)=>{if(!a)return e;const t=new RegExp(`(${a})`,"gi");return e.replace(t,"<mark>$1</mark>")},pe=e=>({create_group:"创建群组",manage_users:"用户管理",analytics:"数据分析",orders:"订单管理",settings:"系统设置"}[e]||e),me=e=>({create_group:"ChatDotRound",manage_users:"User",analytics:"DataAnalysis",orders:"ShoppingCart",settings:"Setting"}[e]||"Document"),ge=e=>{const a=new Date-e,t=Math.floor(a/6e4),s=Math.floor(a/36e5);return t<60?`${t}分钟前`:s<24?`${s}小时前`:e.toLocaleDateString()},fe=e=>{(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),F())};return r(()=>{document.addEventListener("keydown",fe)}),C(()=>{document.removeEventListener("keydown",fe)}),w(k,e=>{e&&(S.value=0)}),(e,a)=>{const t=N,s=I,l=re,i=V,n=te;return d(),o("div",pt,[m("div",{class:"search-trigger",onClick:F},[u(t,{class:"search-icon"},{default:v(()=>[u(g(ce))]),_:1}),a[5]||(a[5]=m("span",{class:"search-placeholder"},"AI智能搜索...",-1)),a[6]||(a[6]=m("div",{class:"search-shortcuts"},[m("kbd",null,"Ctrl"),p(" + "),m("kbd",null,"K")],-1))]),u(n,{modelValue:k.value,"onUpdate:modelValue":a[4]||(a[4]=e=>k.value=e),"show-close":!1,"close-on-click-modal":!1,width:"700px",class:"search-dialog","append-to-body":""},{header:v(()=>[m("div",mt,[m("div",gt,[u(t,null,{default:v(()=>[u(g(B))]),_:1}),a[7]||(a[7]=m("span",null,"AI智能搜索",-1))]),m("div",ft,[u(s,{text:"",onClick:le,class:L({active:T.value})},{default:v(()=>[u(t,null,{default:v(()=>[u(g(oe))]),_:1})]),_:1},8,["class"]),u(s,{text:"",onClick:J},{default:v(()=>[u(t,null,{default:v(()=>[u(g(se))]),_:1})]),_:1})])])]),default:v(()=>[m("div",ht,[m("div",yt,[u(l,{modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=e=>D.value=e),ref_key:"searchInputRef",ref:A,placeholder:"输入搜索内容，支持自然语言查询...",size:"large",class:"search-input",onInput:K,onKeydown:Z},{prefix:v(()=>[u(t,null,{default:v(()=>[u(g(ce))]),_:1})]),suffix:v(()=>[m("div",_t,[D.value?(d(),_(s,{key:0,text:"",onClick:G,class:"clear-btn"},{default:v(()=>[u(t,null,{default:v(()=>[u(g(se))]),_:1})]),_:1})):f("",!0),u(s,{text:"",onClick:le,class:L([{active:T.value},"voice-btn"])},{default:v(()=>[u(t,null,{default:v(()=>[u(g(oe))]),_:1})]),_:1},8,["class"])])]),_:1},8,["modelValue"]),!D.value&&E.value.length>0?(d(),o("div",kt,[(d(!0),o(h,null,y(E.value,e=>(d(),_(i,{key:e,size:"small",class:"suggestion-tag",onClick:a=>D.value=e},{default:v(()=>[p($(e),1)]),_:2},1032,["onClick"]))),128))])):f("",!0)]),z.value?(d(),o("div",bt,[m("div",wt,[u(t,{class:"is-loading"},{default:v(()=>[u(g(ie))]),_:1}),a[8]||(a[8]=m("span",null,"AI正在分析您的查询...",-1))])])):f("",!0),m("div",Ct,[D.value||z.value?D.value&&!z.value?(d(),o("div",Bt,[m("div",Kt,[m("span",null,"找到 "+$(P.value.length)+" 个结果",1),m("span",Qt,$(U.value)+"ms",1)]),M.value?(d(),o("div",Yt,[m("div",Xt,[u(t,null,{default:v(()=>[u(g(B))]),_:1}),a[14]||(a[14]=m("span",null,"AI理解",-1))]),m("p",null,$(M.value),1)])):f("",!0),u(vt,{items:P.value,"item-height":80,"container-height":300,class:"results-list"},{default:v(({item:e,index:s})=>[m("div",{class:L(["result-item",{active:S.value===s,protected:e.protected,"ai-recommended":e.aiScore>.8}]),onClick:a=>ae(e)},[m("div",{class:L(["result-icon",e.iconClass])},[u(t,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],2),m("div",es,[m("div",{class:"result-title",innerHTML:ve(e.title,D.value)},null,8,as),m("div",ts,$(e.path),1),e.description?(d(),o("div",ss,$(e.description),1)):f("",!0)]),m("div",ls,[e.protected?(d(),_(i,{key:0,type:"warning",size:"small"},{default:v(()=>a[15]||(a[15]=[p("核心",-1)])),_:1,__:[15]})):f("",!0),e.aiScore>.8?(d(),_(i,{key:1,type:"success",size:"small"},{default:v(()=>a[16]||(a[16]=[p("AI推荐",-1)])),_:1,__:[16]})):f("",!0),e.aiScore?(d(),o("div",is,$(Math.round(100*e.aiScore))+"%匹配 ",1)):f("",!0)])],10,Zt)]),_:1},8,["items"])])):D.value&&0===P.value.length&&!z.value?(d(),o("div",ns,[m("div",cs,[u(t,null,{default:v(()=>[u(g(ce))]),_:1})]),a[21]||(a[21]=m("div",{class:"no-results-text"},"未找到相关结果",-1)),m("div",rs,[a[20]||(a[20]=m("p",null,"您可以尝试：",-1)),u(s,{size:"small",onClick:a[1]||(a[1]=e=>D.value="创建群组")},{default:v(()=>a[17]||(a[17]=[p("创建群组",-1)])),_:1,__:[17]}),u(s,{size:"small",onClick:a[2]||(a[2]=e=>D.value="用户管理")},{default:v(()=>a[18]||(a[18]=[p("用户管理",-1)])),_:1,__:[18]}),u(s,{size:"small",onClick:a[3]||(a[3]=e=>D.value="数据分析")},{default:v(()=>a[19]||(a[19]=[p("数据分析",-1)])),_:1,__:[19]})])])):f("",!0):(d(),o("div",Dt,[R.value.length>0?(d(),o("div",xt,[m("div",At,[u(t,null,{default:v(()=>[u(g(B))]),_:1}),a[9]||(a[9]=m("span",null,"AI为您推荐",-1))]),(d(!0),o(h,null,y(R.value,(e,s)=>(d(),o("div",{key:e.id,class:L(["recommendation-item",{active:S.value===s}]),onClick:a=>(e=>{e.action&&e.action(),J()})(e)},[m("div",zt,[u(t,null,{default:v(()=>[(d(),_(b(e.icon||"MagicStick")))]),_:2},1024)]),m("div",$t,[m("div",It,$(e.title),1),m("div",Tt,$(e.description),1),e.confidence?(d(),o("div",Ut,[m("span",null,"AI置信度: "+$(Math.round(100*e.confidence))+"%",1)])):f("",!0)]),m("div",Mt,[u(i,{type:"info",size:"small"},{default:v(()=>a[10]||(a[10]=[p("AI推荐",-1)])),_:1,__:[10]})])],10,St))),128))])):f("",!0),m("div",Vt,[m("div",Nt,[u(t,null,{default:v(()=>[u(g(H))]),_:1}),a[11]||(a[11]=m("span",null,"为您推荐",-1))]),(d(!0),o(h,null,y(O.value,(e,s)=>(d(),o("div",{key:e.id,class:L(["recommendation-item",{active:S.value===R.value.length+s,protected:e.protected}]),onClick:a=>ae(e)},[m("div",{class:L(["item-icon",e.iconClass])},[u(t,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],2),m("div",Rt,[m("div",Et,$(e.title),1),m("div",Pt,$(e.description),1)]),e.protected?(d(),o("div",jt,[u(i,{type:"warning",size:"small"},{default:v(()=>a[12]||(a[12]=[p("核心功能",-1)])),_:1,__:[12]})])):f("",!0)],10,Ht))),128))]),W.value.length>0?(d(),o("div",Lt,[m("div",Ot,[u(t,null,{default:v(()=>[u(g(de))]),_:1}),a[13]||(a[13]=m("span",null,"最近使用",-1))]),(d(!0),o(h,null,y(W.value,(e,a)=>(d(),o("div",{key:e.id,class:"recommendation-item recent-item",onClick:a=>ae(e)},[m("div",qt,[u(t,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)]),m("div",Ft,[m("div",Jt,$(e.title),1),m("div",Gt,$(ge(e.lastUsed)),1)])],8,Wt))),128))])):f("",!0)]))])])]),_:1},8,["modelValue"]),T.value?(d(),o("div",os,[a[23]||(a[23]=m("div",{class:"voice-animation"},[m("div",{class:"wave"}),m("div",{class:"wave"}),m("div",{class:"wave"})],-1)),a[24]||(a[24]=m("p",null,"正在听取您的语音...",-1)),u(s,{onClick:ue},{default:v(()=>a[22]||(a[22]=[p("停止",-1)])),_:1,__:[22]})])):f("",!0)])}}},[["__scopeId","data-v-29c9aae4"]]),us={class:"modern-dashboard"},vs={class:"stage-three-showcase"},ps={class:"feature-demo"},ms={class:"feature-demo"},gs={class:"welcome-banner"},fs={class:"banner-content"},hs={class:"welcome-text"},ys={class:"welcome-title"},_s={class:"welcome-subtitle"},ks={class:"banner-actions"},bs={class:"metrics-grid"},ws={class:"metric-content"},Cs={class:"metric-value"},Ds={class:"metric-label"},xs={class:"metric-chart"},As={class:"dashboard-content"},Ss={class:"content-left"},zs={class:"chart-card"},$s={class:"card-header"},Is={class:"card-actions"},Ts={class:"chart-container"},Us={class:"activity-card"},Ms={class:"activity-heatmap"},Vs={class:"heatmap-grid"},Ns=["title"],Hs={class:"orders-card"},Rs={class:"card-header"},Es={class:"orders-list"},Ps=["onClick"],js={class:"order-avatar"},Ls={class:"order-info"},Os={class:"order-title"},Ws={class:"order-meta"},qs={class:"order-amount"},Fs={class:"popular-groups-card"},Js={class:"card-header"},Gs={class:"groups-ranking"},Bs=["onClick"],Ks={class:"group-info"},Qs={class:"group-name"},Ys={class:"group-stats"},Xs={class:"member-count"},Zs={class:"revenue"},el={class:"group-trend"},al={class:"trend-value"},tl={class:"recent-activities-card"},sl={class:"card-header"},ll={class:"activity-filters"},il={class:"activities-timeline"},nl={class:"timeline-content"},cl={class:"activity-header"},rl={class:"activity-user"},ol={class:"activity-action"},dl={class:"activity-target"},ul={class:"activity-time"},vl={class:"content-right"},pl={class:"quick-stats-card"},ml={class:"stats-grid"},gl={class:"stat-content"},fl={class:"stat-value"},hl={class:"stat-label"},yl={class:"notifications-card"},_l={class:"card-header"},kl={class:"notifications-list"},bl={class:"notification-content"},wl={class:"notification-title"},Cl={class:"notification-time"},Dl={class:"notification-actions"},xl={class:"system-status-card"},Al={class:"status-metrics"},Sl={class:"status-item"},zl={class:"status-progress"},$l={class:"status-value"},Il={class:"status-item"},Tl={class:"status-progress"},Ul={class:"status-value"},Ml={class:"status-item"},Vl={class:"status-progress"},Nl={class:"status-value"},Hl={class:"status-item"},Rl={class:"status-progress"},El={class:"status-value"},Pl={class:"quick-actions-card"},jl={class:"actions-grid"},Ll=["onClick"],Ol={class:"action-label"},Wl={class:"quick-actions-content"},ql={class:"category-title"},Fl={class:"category-actions"},Jl=["onClick"],Gl={class:"action-info"},Bl={class:"action-name"},Kl={class:"action-desc"},Ql=e({__name:"ModernDashboard",setup(e){const t=i(),s=a(),l=n(!1),f=n("today"),k=n(null),w=n(!1),C=c(()=>(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})),D=()=>{const e=(new Date).getHours();return e<6?"夜深了，注意休息":e<12?"早上好":e<18?"下午好":"晚上好"},V=A([{key:"users",label:"总用户数",value:12580,change:"+12.5%",changeType:"positive",icon:ue,iconClass:"user-icon",chartGradient:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"orders",label:"总订单数",value:8964,change:"+8.2%",changeType:"positive",icon:ve,iconClass:"order-icon",chartGradient:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"revenue",label:"总收入",value:256780,change:"+15.3%",changeType:"positive",icon:pe,iconClass:"revenue-icon",chartGradient:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"conversion",label:"转化率",value:68.5,change:"-2.1%",changeType:"negative",icon:Q,iconClass:"conversion-icon",chartGradient:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"}]),R=A({cpu:45,memory:62,disk:78,latency:23}),E=A([{key:"todayUsers",label:"今日新增用户",value:156,icon:H,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"todayOrders",label:"今日订单",value:89,icon:ve,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"todayRevenue",label:"今日收入",value:"¥12,580",icon:pe,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"activeUsers",label:"在线用户",value:1,icon:ue,color:"linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"}]),j=A([{id:1,groupName:"高端投资理财群",userName:"张三",userAvatar:"/avatars/user1.jpg",amount:299,status:"paid",createdAt:new Date(Date.now()-3e5)},{id:2,groupName:"股票交流群",userName:"李四",userAvatar:"/avatars/user2.jpg",amount:199,status:"pending",createdAt:new Date(Date.now()-9e5)},{id:3,groupName:"创业交流群",userName:"王五",userAvatar:"/avatars/user3.jpg",amount:99,status:"paid",createdAt:new Date(Date.now()-18e5)}]),J=A([{id:1,title:"系统维护通知",type:"warning",read:!1,createdAt:new Date(Date.now()-6e5)},{id:2,title:"新用户注册",type:"info",read:!1,createdAt:new Date(Date.now()-12e5)},{id:3,title:"订单支付成功",type:"success",read:!0,createdAt:new Date(Date.now()-21e5)}]),B=A([{key:"addUser",label:"添加用户",icon:H,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>t.push("/user/add")},{key:"createGroup",label:"创建群组",icon:me,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>t.push("/community/groups/add")},{key:"viewReports",label:"查看报表",icon:Q,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>t.push("/dashboard/reports")},{key:"systemSettings",label:"系统设置",icon:K,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>t.push("/system/settings")}]),Y=A([{name:"用户管理",actions:[{key:"addUser",label:"添加用户",description:"快速添加新用户到系统",icon:H,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>t.push("/user/add")},{key:"userAnalytics",label:"用户分析",description:"查看用户行为和统计数据",icon:ge,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>t.push("/user/analytics")}]},{name:"内容管理",actions:[{key:"createGroup",label:"创建群组",description:"创建新的微信群组",icon:me,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>t.push("/community/groups/add")},{key:"contentManage",label:"内容管理",description:"管理群组内容和模板",icon:O,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>t.push("/content/management")}]}]),X=A([]),Z=A([{id:1,name:"高端投资理财群",memberCount:2580,revenue:125600,trendType:"up",trendValue:"+15.2%"},{id:2,name:"股票交流群",memberCount:1890,revenue:89400,trendType:"up",trendValue:"+8.7%"},{id:3,name:"创业交流群",memberCount:1456,revenue:67200,trendType:"down",trendValue:"-2.3%"},{id:4,name:"技术分享群",memberCount:1234,revenue:45600,trendType:"up",trendValue:"+5.1%"},{id:5,name:"职场发展群",memberCount:987,revenue:32100,trendType:"flat",trendValue:"0%"}]),ee=n("all"),ae=A([{key:"all",label:"全部"},{key:"user",label:"用户"},{key:"order",label:"订单"},{key:"system",label:"系统"}]),se=A([{id:1,userName:"张三",action:"加入了",target:"高端投资理财群",type:"user",createdAt:new Date(Date.now()-12e4)},{id:2,userName:"李四",action:"完成了订单支付",target:"¥299",type:"order",createdAt:new Date(Date.now()-48e4)},{id:3,userName:"系统",action:"自动备份了",target:"数据库",type:"system",createdAt:new Date(Date.now()-9e5)},{id:4,userName:"王五",action:"创建了",target:"新的群组",type:"user",createdAt:new Date(Date.now()-15e5)},{id:5,userName:"赵六",action:"申请了",target:"提现 ¥1200",type:"order",createdAt:new Date(Date.now()-21e5)}]),ie=c(()=>"all"===ee.value?se:se.filter(e=>e.type===ee.value)),ne=e=>{const a=new Date-e,t=Math.floor(a/6e4),s=Math.floor(a/36e5),l=Math.floor(a/864e5);return t<1?"刚刚":t<60?`${t}分钟前`:s<24?`${s}小时前`:`${l}天前`},ce=()=>{q.success("数据已刷新")},re=()=>{t.push("/system/notifications")},oe=e=>{e.action&&(e.action(),l.value=!1)},de=e=>{const a=["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #f093fb 0%, #f5576c 100%)","linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)","linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)","linear-gradient(135deg, #fa709a 0%, #fee140 100%)","linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)","linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)","linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)","linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)"];let t=0;for(let s=0;s<e.length;s++)t=e.charCodeAt(s)+((t<<5)-t);return a[Math.abs(t)%a.length]};return r(()=>{(()=>{const e=[],a=new Date;for(let t=364;t>=0;t--){const s=new Date(a.getTime()-24*t*60*60*1e3),l=Math.floor(20*Math.random());let i="none";l>15?i="high":l>8?i="medium":l>3&&(i="low"),e.push({date:s.toLocaleDateString(),count:l,level:i})}X.splice(0,X.length,...e)})(),x(()=>{})}),(e,a)=>{const i=I,n=M,c=U,r=T,x=W,A=N,H=he,O=ye,K=F,Q=S("Minus"),se=ke,ue=G,ve=be,pe=te;return d(),o("div",us,[u(ra),m("div",vs,[u(r,{class:"showcase-card",shadow:"never"},{header:v(()=>a[7]||(a[7]=[m("div",{class:"showcase-header"},[m("h3",null,"🚀 第三阶段：个性化功能和性能优化"),m("p",null,"AI驱动的智能工作台和极致性能体验")],-1)])),default:v(()=>[u(c,{gutter:24},{default:v(()=>[u(n,{span:12},{default:v(()=>[m("div",ps,[a[8]||(a[8]=m("h4",null,"🤖 智能工作台",-1)),a[9]||(a[9]=m("p",null,"AI驱动的个性化工作台，智能推荐和自适应界面",-1)),u(i,{type:"primary",onClick:a[0]||(a[0]=e=>w.value=!w.value)},{default:v(()=>[p($(w.value?"隐藏":"体验")+"智能工作台 ",1)]),_:1})])]),_:1}),u(n,{span:12},{default:v(()=>[m("div",ms,[a[10]||(a[10]=m("h4",null,"🔍 AI增强搜索",-1)),a[11]||(a[11]=m("p",null,"语义搜索、语音识别、智能推荐",-1)),u(ds)])]),_:1})]),_:1})]),_:1}),u(x,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value=e),title:"🤖 智能工作台",direction:"rtl",size:"80%",class:"workbench-drawer"},{default:v(()=>[u(ct)]),_:1},8,["modelValue"])]),m("div",gs,[m("div",fs,[m("div",hs,[m("h1",ys," 欢迎回来，"+$(g(s).nickname||"管理员")+"！ ",1),m("p",_s," 今天是 "+$(C.value)+"，"+$(D()),1)]),m("div",ks,[u(i,{type:"primary",class:"modern-btn primary",onClick:a[2]||(a[2]=e=>l.value=!0)},{default:v(()=>[u(A,null,{default:v(()=>[u(g(P))]),_:1}),a[12]||(a[12]=p(" 快速操作 ",-1))]),_:1,__:[12]}),u(i,{class:"modern-btn secondary",onClick:ce},{default:v(()=>[u(A,null,{default:v(()=>[u(g(fe))]),_:1}),a[13]||(a[13]=p(" 刷新数据 ",-1))]),_:1,__:[13]})])]),a[14]||(a[14]=m("div",{class:"banner-decoration"},[m("div",{class:"decoration-circle circle-1"}),m("div",{class:"decoration-circle circle-2"}),m("div",{class:"decoration-circle circle-3"})],-1))]),m("div",bs,[(d(!0),o(h,null,y(V,e=>{return d(),o("div",{class:"metric-card",key:e.key},[m("div",{class:L(["metric-icon",e.iconClass])},[u(A,{size:24},{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],2),m("div",ws,[m("div",Cs,$((a=e.value,a>=1e4?(a/1e4).toFixed(1)+"w":a.toLocaleString())),1),m("div",Ds,$(e.label),1),m("div",{class:L(["metric-change",e.changeType])},[u(A,{size:12},{default:v(()=>["positive"===e.changeType?(d(),_(g(we),{key:0})):(d(),_(g(Ce),{key:1}))]),_:2},1024),p(" "+$(e.change),1)],2)]),m("div",xs,[m("div",{class:"mini-chart",style:le({background:e.chartGradient})},null,4)])]);var a}),128))]),m("div",As,[m("div",Ss,[m("div",zs,[m("div",$s,[a[15]||(a[15]=m("h3",{class:"card-title"},"实时数据概览",-1)),m("div",Is,[u(O,{modelValue:f.value,"onUpdate:modelValue":a[3]||(a[3]=e=>f.value=e),size:"small",class:"time-selector"},{default:v(()=>[u(H,{label:"今日",value:"today"}),u(H,{label:"本周",value:"week"}),u(H,{label:"本月",value:"month"}),u(H,{label:"本年",value:"year"})]),_:1},8,["modelValue"])])]),m("div",Ts,[m("div",{ref_key:"mainChart",ref:k,class:"main-chart"},null,512)])]),m("div",Us,[a[16]||(a[16]=z('<div class="card-header" data-v-b1077dd7><h3 class="card-title" data-v-b1077dd7>用户活动热力图</h3><div class="activity-legend" data-v-b1077dd7><span class="legend-item" data-v-b1077dd7><div class="legend-color low" data-v-b1077dd7></div> 低活跃 </span><span class="legend-item" data-v-b1077dd7><div class="legend-color medium" data-v-b1077dd7></div> 中活跃 </span><span class="legend-item" data-v-b1077dd7><div class="legend-color high" data-v-b1077dd7></div> 高活跃 </span></div></div>',1)),m("div",Ms,[m("div",Vs,[(d(!0),o(h,null,y(X,(e,a)=>(d(),o("div",{key:a,class:L(["heatmap-cell",e.level]),title:`${e.date}: ${e.count} 活跃用户`},null,10,Ns))),128))])])]),m("div",Hs,[m("div",Rs,[a[18]||(a[18]=m("h3",{class:"card-title"},"最新订单",-1)),u(i,{text:"",onClick:a[4]||(a[4]=a=>e.$router.push("/orders/list"))},{default:v(()=>[a[17]||(a[17]=p(" 查看全部 ",-1)),u(A,null,{default:v(()=>[u(g(_e))]),_:1})]),_:1,__:[17]})]),m("div",Es,[(d(!0),o(h,null,y(j,e=>{return d(),o("div",{key:e.id,class:"order-item",onClick:a=>{return s=e.id,void t.push(`/orders/detail/${s}`);var s}},[m("div",js,[u(K,{size:40,src:e.userAvatar,style:le({background:de(e.userName),color:"white",fontWeight:"600"})},{default:v(()=>{return[p($((a=e.userName,a?/[\u4e00-\u9fa5]/.test(a)?a.slice(-1):a.charAt(0).toUpperCase():"?")),1)];var a}),_:2},1032,["src","style"])]),m("div",Ls,[m("div",Os,$(e.groupName),1),m("div",Ws,$(e.userName)+" · "+$(ne(e.createdAt)),1)]),m("div",qs," ¥"+$(e.amount),1),m("div",{class:L(["order-status",e.status])},$((a=e.status,{paid:"已支付",pending:"待支付",cancelled:"已取消",refunded:"已退款"}[a]||"未知")),3)],8,Ps);var a}),128))])]),m("div",Fs,[m("div",Js,[a[20]||(a[20]=m("h3",{class:"card-title"},"热门群组排行",-1)),u(i,{text:"",onClick:a[5]||(a[5]=a=>e.$router.push("/community/groups"))},{default:v(()=>[a[19]||(a[19]=p(" 查看全部 ",-1)),u(A,null,{default:v(()=>[u(g(_e))]),_:1})]),_:1,__:[19]})]),m("div",Gs,[(d(!0),o(h,null,y(Z,(e,s)=>(d(),o("div",{key:e.id,class:"ranking-item",onClick:a=>{return s=e.id,void t.push(`/community/groups/detail/${s}`);var s}},[m("div",{class:L(["ranking-number",{"top-three":s<3}])},$(s+1),3),m("div",Ks,[m("div",Qs,$(e.name),1),m("div",Ys,[m("span",Xs,$(e.memberCount)+"人",1),a[21]||(a[21]=m("span",{class:"separator"},"·",-1)),m("span",Zs,"¥"+$(e.revenue),1)])]),m("div",el,[m("div",{class:L(["trend-icon",e.trendType])},[u(A,null,{default:v(()=>["up"===e.trendType?(d(),_(g(we),{key:0})):"down"===e.trendType?(d(),_(g(Ce),{key:1})):(d(),_(Q,{key:2}))]),_:2},1024)],2),m("span",al,$(e.trendValue),1)])],8,Bs))),128))])]),m("div",tl,[m("div",sl,[a[22]||(a[22]=m("h3",{class:"card-title"},"最新动态",-1)),m("div",ll,[u(se,{size:"small"},{default:v(()=>[(d(!0),o(h,null,y(ae,e=>(d(),_(i,{key:e.key,type:ee.value===e.key?"primary":"",onClick:a=>ee.value=e.key},{default:v(()=>[p($(e.label),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})])]),m("div",il,[(d(!0),o(h,null,y(ie.value,e=>(d(),o("div",{key:e.id,class:"timeline-item"},[m("div",{class:L(["timeline-dot",e.type])},null,2),m("div",nl,[m("div",cl,[m("span",rl,$(e.userName),1),m("span",ol,$(e.action),1),m("span",dl,$(e.target),1)]),m("div",ul,$(ne(e.createdAt)),1)])]))),128))])])]),m("div",vl,[m("div",pl,[a[23]||(a[23]=m("div",{class:"card-header"},[m("h3",{class:"card-title"},"今日统计")],-1)),m("div",ml,[(d(!0),o(h,null,y(E,e=>(d(),o("div",{class:"stat-item",key:e.key},[m("div",{class:"stat-icon",style:le({background:e.color})},[u(A,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],4),m("div",gl,[m("div",fl,$(e.value),1),m("div",hl,$(e.label),1)])]))),128))])]),m("div",yl,[m("div",_l,[a[25]||(a[25]=m("h3",{class:"card-title"},"通知中心",-1)),u(ue,{value:J.length,hidden:0===J.length},{default:v(()=>[u(i,{text:"",onClick:re},{default:v(()=>a[24]||(a[24]=[p(" 查看全部 ",-1)])),_:1,__:[24]})]),_:1},8,["value","hidden"])]),m("div",kl,[(d(!0),o(h,null,y(J.slice(0,5),e=>(d(),o("div",{key:e.id,class:L(["notification-item",{unread:!e.read}])},[m("div",{class:L(["notification-icon",e.type])},[u(A,null,{default:v(()=>{return[(d(),_(b((a=e.type,{warning:De,info:Ae,success:xe,error:De}[a]||Ae))))];var a}),_:2},1024)],2),m("div",bl,[m("div",wl,$(e.title),1),m("div",Cl,$(ne(e.createdAt)),1)]),m("div",Dl,[u(i,{text:"",size:"small",onClick:a=>(e=>{const a=J.find(a=>a.id===e);a&&(a.read=!0,q.success("已标记为已读"))})(e.id)},{default:v(()=>a[26]||(a[26]=[p(" 标记已读 ",-1)])),_:2,__:[26]},1032,["onClick"])])],2))),128))])]),m("div",xl,[a[31]||(a[31]=m("div",{class:"card-header"},[m("h3",{class:"card-title"},"系统状态"),m("div",{class:"status-indicator online"},[m("div",{class:"status-dot"}),p(" 运行正常 ")])],-1)),m("div",Al,[m("div",Sl,[a[27]||(a[27]=m("div",{class:"status-label"},"CPU 使用率",-1)),m("div",zl,[u(ve,{percentage:R.cpu,"show-text":!1},null,8,["percentage"]),m("span",$l,$(R.cpu)+"%",1)])]),m("div",Il,[a[28]||(a[28]=m("div",{class:"status-label"},"内存使用率",-1)),m("div",Tl,[u(ve,{percentage:R.memory,"show-text":!1,color:"#10b981"},null,8,["percentage"]),m("span",Ul,$(R.memory)+"%",1)])]),m("div",Ml,[a[29]||(a[29]=m("div",{class:"status-label"},"磁盘使用率",-1)),m("div",Vl,[u(ve,{percentage:R.disk,"show-text":!1,color:"#f59e0b"},null,8,["percentage"]),m("span",Nl,$(R.disk)+"%",1)])]),m("div",Hl,[a[30]||(a[30]=m("div",{class:"status-label"},"网络延迟",-1)),m("div",Rl,[m("span",El,$(R.latency)+"ms",1)])])])]),m("div",Pl,[a[32]||(a[32]=m("div",{class:"card-header"},[m("h3",{class:"card-title"},"快捷操作")],-1)),m("div",jl,[(d(!0),o(h,null,y(B,e=>(d(),o("div",{key:e.key,class:"action-item",onClick:a=>oe(e)},[m("div",{class:"action-icon",style:le({background:e.color})},[u(A,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],4),m("div",Ol,$(e.label),1)],8,Ll))),128))])])])]),u(pe,{modelValue:l.value,"onUpdate:modelValue":a[6]||(a[6]=e=>l.value=e),title:"快速操作",width:"600px",class:"quick-actions-dialog"},{default:v(()=>[m("div",Wl,[(d(!0),o(h,null,y(Y,e=>(d(),o("div",{class:"action-category",key:e.name},[m("h4",ql,$(e.name),1),m("div",Fl,[(d(!0),o(h,null,y(e.actions,e=>(d(),o("div",{key:e.key,class:"category-action-item",onClick:a=>oe(e)},[m("div",{class:"action-icon",style:le({background:e.color})},[u(A,null,{default:v(()=>[(d(),_(b(e.icon)))]),_:2},1024)],4),m("div",Gl,[m("div",Bl,$(e.label),1),m("div",Kl,$(e.description),1)])],8,Jl))),128))])]))),128))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b1077dd7"]]);export{Ql as default};
