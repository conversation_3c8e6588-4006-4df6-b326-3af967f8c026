import{_ as e,k as a}from"./index-DtXAftX0.js";/* empty css                  *//* empty css                    */import{an as l,r as t,c as s,k as r,l as o,E as u,z as c,t as i,B as d,u as p,J as n,D as v}from"./vue-vendor-Dy164gUc.js";import{o as m,bc as g,T as f,aw as y,V as _,at as h,ac as k,bQ as w,bA as b,ay as x,R as z,Q as j}from"./element-plus-h2SQQM64.js";const V={class:"avatar-upload-container"},B=["src"],S={key:1,class:"upload-progress"},A={key:2,class:"avatar-placeholder"},C={key:3,class:"avatar-overlay"},P={class:"overlay-actions"},G={class:"avatar-preview"},M=["src"],N=e({__name:"AvatarUpload",props:{modelValue:{type:String,default:""},size:{type:Number,default:100},maxSize:{type:Number,default:2},enablePreview:{type:Boolean,default:!0}},emits:["update:modelValue","success","error"],setup(e,{emit:N}){l(a=>({c4eae940:`${e.size}px`}));const T=e,$=N,E=t(!1),I=t(0),J=t(!1),Q=s({get:()=>T.modelValue,set:e=>$("update:modelValue",e)}),U=s(()=>"/api/upload/avatar"),q=s(()=>({Authorization:`Bearer ${a()}`})),D=e=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(e.type))return j.error("头像只能是 JPG、PNG、GIF 或 WebP 格式!"),!1;return e.size/1024/1024<T.maxSize?(E.value=!0,I.value=0,!0):(j.error(`头像大小不能超过 ${T.maxSize}MB!`),!1)},F=e=>{I.value=Math.round(e.loaded/e.total*100)},R=e=>{E.value=!1,I.value=0,e.success?(Q.value=e.data.url,j.success("头像上传成功!"),$("success",e.data)):(j.error(e.message||"头像上传失败!"),$("error",e))},W=e=>{E.value=!1,I.value=0,console.error("头像上传失败:",e),j.error("头像上传失败!"),$("error",e)},H=()=>{const e=document.querySelector('.avatar-uploader input[type="file"]');e&&e.click()},K=()=>{z.confirm("确定要删除当前头像吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Q.value="",$("update:modelValue",""),j.success("头像已删除")}).catch(()=>{})},L=()=>{if(Q.value){const e=document.createElement("a");e.href=Q.value,e.download="avatar.jpg",e.click()}};return(a,l)=>{const t=g,s=f,z=h,j=_,N=b,T=x;return o(),r("div",V,[u(N,{class:"avatar-uploader",action:U.value,headers:q.value,"show-file-list":!1,"on-success":R,"on-error":W,"before-upload":D,"on-progress":F,accept:"image/jpeg,image/jpg,image/png,image/gif,image/webp"},{default:c(()=>[i("div",{class:m(["avatar-wrapper",{uploading:E.value}])},[Q.value&&!E.value?(o(),r("img",{key:0,src:Q.value,class:"avatar",alt:"头像"},null,8,B)):E.value?(o(),r("div",S,[u(t,{type:"circle",percentage:I.value,width:Math.min(e.size-20,80),"stroke-width":4},null,8,["percentage","width"]),l[2]||(l[2]=i("div",{class:"progress-text"},"上传中...",-1))])):(o(),r("div",A,[u(s,{class:"avatar-uploader-icon"},{default:c(()=>[u(p(y))]),_:1}),l[3]||(l[3]=i("div",{class:"upload-text"},"点击上传头像",-1))])),Q.value&&!E.value?(o(),r("div",C,[i("div",P,[u(j,{content:"更换头像",placement:"top"},{default:c(()=>[u(z,{type:"primary",size:"small",circle:"",onClick:n(H,["stop"])},{default:c(()=>[u(s,null,{default:c(()=>[u(p(k))]),_:1})]),_:1})]),_:1}),u(j,{content:"删除头像",placement:"top"},{default:c(()=>[u(z,{type:"danger",size:"small",circle:"",onClick:n(K,["stop"])},{default:c(()=>[u(s,null,{default:c(()=>[u(p(w))]),_:1})]),_:1})]),_:1})])])):d("",!0)],2)]),_:1},8,["action","headers"]),u(T,{modelValue:J.value,"onUpdate:modelValue":l[1]||(l[1]=e=>J.value=e),title:"头像预览",width:"400px",center:""},{footer:c(()=>[u(z,{onClick:l[0]||(l[0]=e=>J.value=!1)},{default:c(()=>l[4]||(l[4]=[v("关闭",-1)])),_:1,__:[4]}),u(z,{type:"primary",onClick:L},{default:c(()=>l[5]||(l[5]=[v("下载",-1)])),_:1,__:[5]})]),default:c(()=>[i("div",G,[i("img",{src:Q.value,alt:"头像预览"},null,8,M)])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b86ade83"]]);export{N as A};
