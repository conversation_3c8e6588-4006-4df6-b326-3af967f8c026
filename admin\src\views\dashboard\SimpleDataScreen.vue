<template>
  <div class="simple-data-screen">
    <!-- 大屏头部 -->
    <div class="screen-header">
      <div class="header-left">
        <div class="logo">
          <div class="logo-icon">📊</div>
          <span class="logo-text">数据中心</span>
        </div>
      </div>
      <div class="header-center">
        <h1 class="screen-title">实时运营数据大屏</h1>
      </div>
      <div class="header-right">
        <div class="current-time">{{ currentTime }}</div>
        <div class="refresh-info">
          <span class="refresh-dot"></span>
          实时更新
        </div>
        <div class="header-actions">
          <el-button @click="exportData" size="small">
            导出数据
          </el-button>
          <el-button @click="refreshData" size="small" :loading="isRefreshing">
            刷新数据
          </el-button>
          <el-button @click="openFullscreen" type="primary" size="small">
            全屏显示
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标区域 -->
    <div class="metrics-section">
      <div class="metrics-grid">
        <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-icon" :style="{ background: metric.color }">
            {{ metric.icon }}
          </div>
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-change" :class="{ positive: metric.change > 0, negative: metric.change < 0 }">
              {{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>收入趋势</h3>
              <div class="chart-controls">
                <el-select v-model="selectedPeriod" size="small">
                  <el-option label="今日" value="1d" />
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                </el-select>
              </div>
            </div>
            <div class="chart-content">
              <div class="revenue-chart">
                <div class="chart-data">
                  <div class="data-point" v-for="point in revenueData" :key="point.time">
                    <div class="data-bar" :style="{ height: point.percentage + '%' }"></div>
                    <div class="data-label">{{ point.label }}</div>
                    <div class="data-value">¥{{ point.value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>用户分布</h3>
            </div>
            <div class="chart-content">
              <div class="user-distribution">
                <div class="distribution-item" v-for="item in userDistribution" :key="item.type">
                  <div class="distribution-icon" :style="{ background: item.color }">
                    {{ item.icon }}
                  </div>
                  <div class="distribution-info">
                    <div class="distribution-label">{{ item.label }}</div>
                    <div class="distribution-value">{{ item.count }}人</div>
                    <div class="distribution-percentage">{{ item.percentage }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 第二行：社群活跃度和转化漏斗 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>社群活跃度排行</h3>
              <div class="chart-controls">
                <el-select v-model="selectedActivityPeriod" size="small">
                  <el-option label="今日" value="today" />
                  <el-option label="本周" value="week" />
                  <el-option label="本月" value="month" />
                </el-select>
              </div>
            </div>
            <div class="chart-content">
              <div class="activity-ranking">
                <div class="ranking-item" v-for="(group, index) in groupActivity" :key="group.name">
                  <div class="ranking-number" :class="{
                    'top-1': index === 0,
                    'top-2': index === 1,
                    'top-3': index === 2
                  }">
                    {{ index + 1 }}
                  </div>
                  <div class="ranking-info">
                    <div class="ranking-name">{{ group.name }}</div>
                    <div class="ranking-stats">
                      <span class="stat-item">
                        <span class="stat-label">成员</span>
                        <span class="stat-value">{{ group.members }}</span>
                      </span>
                      <span class="stat-item">
                        <span class="stat-label">活跃</span>
                        <span class="stat-value active">{{ group.active }}</span>
                      </span>
                    </div>
                  </div>
                  <div class="ranking-rate">
                    <div class="rate-circle" :style="{
                      background: `conic-gradient(var(--primary-500) ${group.rate * 3.6}deg, var(--gray-200) 0deg)`
                    }">
                      <div class="rate-inner">{{ group.rate }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>转化漏斗分析</h3>
              <div class="chart-controls">
                <span class="conversion-summary">总转化率: {{ finalConversionRate }}%</span>
              </div>
            </div>
            <div class="chart-content">
              <div class="funnel-chart">
                <div class="funnel-item" v-for="(item, index) in conversionFunnel" :key="item.stage">
                  <div class="funnel-stage">
                    <div class="funnel-bar" :style="{
                      width: item.percentage + '%',
                      background: `linear-gradient(135deg,
                        hsl(${220 - index * 15}, 70%, ${60 + index * 5}%),
                        hsl(${220 - index * 15}, 70%, ${50 + index * 5}%))`
                    }">
                      <div class="funnel-content">
                        <span class="funnel-label">{{ item.stage }}</span>
                        <span class="funnel-value">{{ item.count }}</span>
                      </div>
                    </div>
                    <div class="funnel-meta">
                      <span class="funnel-percentage">{{ item.percentage }}%</span>
                      <span class="funnel-conversion" v-if="index > 0">
                        转化率: {{ Math.round((item.count.replace(',', '') / conversionFunnel[index-1].count.replace(',', '')) * 100) }}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 第三行：地域分布地图 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :span="24">
          <div class="chart-card map-card">
            <div class="chart-header">
              <h3>用户地域分布</h3>
              <div class="chart-controls">
                <div class="map-legend">
                  <span class="legend-item">
                    <span class="legend-color light"></span>
                    <span class="legend-text">1-100人</span>
                  </span>
                  <span class="legend-item">
                    <span class="legend-color medium"></span>
                    <span class="legend-text">101-300人</span>
                  </span>
                  <span class="legend-item">
                    <span class="legend-color heavy"></span>
                    <span class="legend-text">300+人</span>
                  </span>
                </div>
              </div>
            </div>
            <div class="chart-content">
              <div class="china-map">
                <div class="map-container">
                  <!-- 科技风格中国地图 -->
                  <svg viewBox="0 0 800 600" class="map-svg">
                    <!-- 地图背景 -->
                    <defs>
                      <radialGradient id="mapBg" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:rgba(15,23,42,0.8);stop-opacity:1" />
                        <stop offset="100%" style="stop-color:rgba(30,41,59,0.9);stop-opacity:1" />
                      </radialGradient>
                      <filter id="glow">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge>
                          <feMergeNode in="coloredBlur"/>
                          <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                      </filter>
                      <linearGradient id="heatGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                        <stop offset="50%" style="stop-color:#06b6d4;stop-opacity:0.5" />
                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.7" />
                      </linearGradient>
                    </defs>

                    <rect width="800" height="600" fill="url(#mapBg)" />

                    <!-- 中国地图轮廓（简化版） -->
                    <g class="china-outline">
                      <path d="M200,150 L250,120 L300,130 L350,140 L400,135 L450,145 L500,160 L550,180 L580,220 L590,260 L580,300 L570,340 L550,380 L520,420 L480,450 L440,470 L400,480 L360,475 L320,465 L280,450 L240,430 L200,400 L170,360 L160,320 L155,280 L160,240 L170,200 L180,170 Z"
                            fill="rgba(59, 130, 246, 0.1)"
                            stroke="rgba(59, 130, 246, 0.5)"
                            stroke-width="2"
                            class="china-border" />
                    </g>

                    <!-- 热力图效果区域 -->
                    <g class="heat-zones">
                      <!-- 北京热力区 -->
                      <circle cx="400" cy="180" r="40" fill="url(#heatGradient)" opacity="0.6" class="heat-zone">
                        <animate attributeName="r" values="35;45;35" dur="3s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
                      </circle>

                      <!-- 上海热力区 -->
                      <circle cx="480" cy="280" r="35" fill="url(#heatGradient)" opacity="0.5" class="heat-zone">
                        <animate attributeName="r" values="30;40;30" dur="2.5s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
                      </circle>

                      <!-- 广深热力区 -->
                      <ellipse cx="430" cy="390" rx="25" ry="20" fill="url(#heatGradient)" opacity="0.4" class="heat-zone">
                        <animate attributeName="rx" values="20;30;20" dur="2s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2s" repeatCount="indefinite"/>
                      </ellipse>
                    </g>

                    <!-- 城市标记点 -->
                    <g class="city-markers">
                      <!-- 北京 -->
                      <circle cx="400" cy="180" r="8" :fill="getRegionColor('北京')" class="city-marker" @click="selectRegion('北京')" filter="url(#glow)">
                        <title>北京: {{ getRegionData('北京').count }}人</title>
                        <animate attributeName="r" values="6;10;6" dur="2s" repeatCount="indefinite"/>
                      </circle>
                      <text x="400" y="165" text-anchor="middle" class="city-label">北京</text>

                      <!-- 上海 -->
                      <circle cx="480" cy="280" r="7" :fill="getRegionColor('上海')" class="city-marker" @click="selectRegion('上海')" filter="url(#glow)">
                        <title>上海: {{ getRegionData('上海').count }}人</title>
                        <animate attributeName="r" values="5;9;5" dur="1.8s" repeatCount="indefinite"/>
                      </circle>
                      <text x="480" y="265" text-anchor="middle" class="city-label">上海</text>

                      <!-- 广州 -->
                      <circle cx="420" cy="380" r="6" :fill="getRegionColor('广州')" class="city-marker" @click="selectRegion('广州')" filter="url(#glow)">
                        <title>广州: {{ getRegionData('广州').count }}人</title>
                        <animate attributeName="r" values="4;8;4" dur="1.5s" repeatCount="indefinite"/>
                      </circle>
                      <text x="420" y="365" text-anchor="middle" class="city-label">广州</text>

                      <!-- 深圳 -->
                      <circle cx="440" cy="400" r="6" :fill="getRegionColor('深圳')" class="city-marker" @click="selectRegion('深圳')" filter="url(#glow)">
                        <title>深圳: {{ getRegionData('深圳').count }}人</title>
                        <animate attributeName="r" values="4;8;4" dur="1.3s" repeatCount="indefinite"/>
                      </circle>
                      <text x="440" y="415" text-anchor="middle" class="city-label">深圳</text>

                      <!-- 杭州 -->
                      <circle cx="460" cy="260" r="5" :fill="getRegionColor('杭州')" class="city-marker" @click="selectRegion('杭州')" filter="url(#glow)">
                        <title>杭州: {{ getRegionData('杭州').count }}人</title>
                        <animate attributeName="r" values="3;7;3" dur="1.2s" repeatCount="indefinite"/>
                      </circle>
                      <text x="460" y="245" text-anchor="middle" class="city-label">杭州</text>

                      <!-- 成都 -->
                      <circle cx="320" cy="300" r="5" :fill="getRegionColor('成都')" class="city-marker" @click="selectRegion('成都')" filter="url(#glow)">
                        <title>成都: {{ getRegionData('成都').count }}人</title>
                        <animate attributeName="r" values="3;7;3" dur="1.1s" repeatCount="indefinite"/>
                      </circle>
                      <text x="320" y="285" text-anchor="middle" class="city-label">成都</text>
                    </g>

                    <!-- 连接线动画 -->
                    <g class="connection-lines">
                      <line x1="400" y1="180" x2="480" y2="280" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1" class="connection-line">
                        <animate attributeName="stroke-opacity" values="0.1;0.5;0.1" dur="3s" repeatCount="indefinite"/>
                      </line>
                      <line x1="480" y1="280" x2="430" y2="390" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1" class="connection-line">
                        <animate attributeName="stroke-opacity" values="0.1;0.5;0.1" dur="2.5s" repeatCount="indefinite"/>
                      </line>
                    </g>
                  </svg>
                </div>

                <!-- 地域数据详情 -->
                <div class="region-details">
                  <div class="region-stats">
                    <div class="stats-header">
                      <h4>{{ selectedRegion || '全国' }}用户统计</h4>
                    </div>
                    <div class="stats-grid">
                      <div class="stat-card" v-for="region in regionData" :key="region.name">
                        <div class="stat-icon" :style="{ background: getRegionColor(region.name) }">
                          {{ region.icon }}
                        </div>
                        <div class="stat-info">
                          <div class="stat-name">{{ region.name }}</div>
                          <div class="stat-count">{{ region.count }}人</div>
                          <div class="stat-growth" :data-negative="region.growth < 0">
                            {{ region.growth > 0 ? '+' : '' }}{{ region.growth }}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 实时数据滚动区域 -->
    <div class="realtime-section">
      <div class="realtime-header">
        <h3>实时数据流</h3>
      </div>
      <div class="realtime-content">
        <div class="realtime-item" v-for="item in realtimeData" :key="item.id">
          <div class="realtime-time">{{ item.time }}</div>
          <div class="realtime-event">{{ item.event }}</div>
          <div class="realtime-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const currentTime = ref('')
const selectedPeriod = ref('7d')
const selectedActivityPeriod = ref('today')
const selectedRegion = ref('')
const isRefreshing = ref(false)

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'revenue',
    label: '今日收入',
    value: '¥25,680',
    change: 12.5,
    icon: '💰',
    color: 'var(--gradient-primary)'
  },
  {
    key: 'users',
    label: '活跃用户',
    value: '1,234',
    change: 8.3,
    icon: '👥',
    color: 'var(--gradient-success)'
  },
  {
    key: 'groups',
    label: '活跃社群',
    value: '56',
    change: -2.1,
    icon: '🏘️',
    color: 'var(--gradient-warning)'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: '12.8%',
    change: 5.7,
    icon: '📈',
    color: 'var(--primary-500)'
  }
])

// 收入趋势数据
const revenueData = ref([
  { time: '00:00', label: '00', value: '2.1k', percentage: 30 },
  { time: '04:00', label: '04', value: '1.8k', percentage: 25 },
  { time: '08:00', label: '08', value: '3.2k', percentage: 45 },
  { time: '12:00', label: '12', value: '4.5k', percentage: 65 },
  { time: '16:00', label: '16', value: '5.8k', percentage: 85 },
  { time: '20:00', label: '20', value: '3.9k', percentage: 55 },
  { time: '24:00', label: '24', value: '2.7k', percentage: 40 }
])

// 用户分布数据
const userDistribution = ref([
  { type: 'vip', label: 'VIP用户', count: 156, percentage: 12.6, icon: '👑', color: 'var(--gradient-primary)' },
  { type: 'premium', label: '付费用户', count: 423, percentage: 34.3, icon: '💎', color: 'var(--gradient-success)' },
  { type: 'active', label: '活跃用户', count: 567, percentage: 46.0, icon: '🔥', color: 'var(--gradient-warning)' },
  { type: 'new', label: '新用户', count: 88, percentage: 7.1, icon: '🌟', color: 'var(--primary-500)' }
])

// 社群活跃度数据
const groupActivity = ref([
  { name: '前端开发群', members: 1234, active: 89, rate: 72 },
  { name: '产品经理群', members: 567, active: 45, rate: 79 },
  { name: '设计师联盟', members: 890, active: 67, rate: 75 },
  { name: '创业者俱乐部', members: 345, active: 23, rate: 67 },
  { name: 'AI技术群', members: 678, active: 56, rate: 83 }
])

// 转化漏斗数据
const conversionFunnel = ref([
  { stage: '访问', count: '10,000', percentage: 100 },
  { stage: '注册', count: '3,200', percentage: 80 },
  { stage: '激活', count: '2,400', percentage: 60 },
  { stage: '付费', count: '1,600', percentage: 40 },
  { stage: '复购', count: '800', percentage: 20 }
])

// 地域分布数据
const regionData = ref([
  { name: '北京', count: 456, percentage: 85, growth: 12.5, icon: '🏛️', level: 'heavy' },
  { name: '上海', count: 389, percentage: 72, growth: 8.3, icon: '🏙️', level: 'heavy' },
  { name: '广州', count: 234, percentage: 43, growth: -2.1, icon: '🌸', level: 'medium' },
  { name: '深圳', count: 198, percentage: 37, growth: 15.7, icon: '🏢', level: 'medium' },
  { name: '杭州', count: 167, percentage: 31, growth: 6.8, icon: '🌊', level: 'medium' },
  { name: '成都', count: 134, percentage: 25, growth: 4.2, icon: '🐼', level: 'light' }
])

// 计算最终转化率
const finalConversionRate = computed(() => {
  const first = parseInt(conversionFunnel.value[0].count.replace(',', ''))
  const last = parseInt(conversionFunnel.value[conversionFunnel.value.length - 1].count.replace(',', ''))
  return Math.round((last / first) * 100)
})

// 实时数据
const realtimeData = ref([
  { id: 1, time: '16:58:23', event: '新用户注册', value: '张三', type: 'user' },
  { id: 2, time: '16:58:15', event: '订单支付', value: '¥299', type: 'payment' },
  { id: 3, time: '16:58:08', event: '社群加入', value: '前端交流群', type: 'group' },
  { id: 4, time: '16:57:55', event: '佣金结算', value: '¥156', type: 'commission' },
  { id: 5, time: '16:57:42', event: '新用户注册', value: '李四', type: 'user' }
])

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 打开全屏模式
const openFullscreen = () => {
  router.push('/fullscreen-data-screen')
}

// 刷新数据
const refreshData = async () => {
  isRefreshing.value = true

  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新核心指标
    coreMetrics.value.forEach(metric => {
      const change = (Math.random() - 0.5) * 20 // -10% 到 +10% 的随机变化
      metric.change = Math.round(change * 10) / 10

      // 根据变化更新数值
      if (metric.key === 'revenue') {
        const baseValue = 25680
        const newValue = Math.round(baseValue * (1 + change / 100))
        metric.value = `¥${newValue.toLocaleString()}`
      } else if (metric.key === 'users') {
        const baseValue = 1234
        const newValue = Math.round(baseValue * (1 + change / 100))
        metric.value = newValue.toLocaleString()
      } else if (metric.key === 'groups') {
        const baseValue = 56
        const newValue = Math.round(baseValue * (1 + change / 100))
        metric.value = newValue.toString()
      } else if (metric.key === 'conversion') {
        const baseValue = 12.8
        const newValue = Math.round((baseValue * (1 + change / 100)) * 10) / 10
        metric.value = `${newValue}%`
      }
    })

    // 更新收入趋势数据
    revenueData.value.forEach(point => {
      point.percentage = Math.max(10, Math.min(90, point.percentage + (Math.random() - 0.5) * 20))
    })

    // 更新社群活跃度
    groupActivity.value.forEach(group => {
      group.rate = Math.max(50, Math.min(95, group.rate + (Math.random() - 0.5) * 10))
      group.active = Math.round(group.members * group.rate / 100)
    })

    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

// 导出数据
const exportData = () => {
  try {
    const exportData = {
      exportTime: new Date().toLocaleString('zh-CN'),
      coreMetrics: coreMetrics.value,
      revenueData: revenueData.value,
      userDistribution: userDistribution.value,
      groupActivity: groupActivity.value,
      conversionFunnel: conversionFunnel.value,
      regionData: regionData.value,
      realtimeData: realtimeData.value
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `数据大屏导出_${new Date().toISOString().slice(0, 10)}.json`
    link.click()

    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

// 获取地域数据
const getRegionData = (regionName) => {
  return regionData.value.find(region => region.name === regionName) || { count: 0 }
}

// 获取地域颜色
const getRegionColor = (regionName) => {
  const region = getRegionData(regionName)
  const colorMap = {
    'heavy': '#3b82f6',    // 蓝色 - 高密度
    'medium': '#06b6d4',   // 青色 - 中密度
    'light': '#8b5cf6'     // 紫色 - 低密度
  }
  return colorMap[region.level] || '#64748b'
}

// 选择地域
const selectRegion = (regionName) => {
  selectedRegion.value = selectedRegion.value === regionName ? '' : regionName
  ElMessage.info(`选择了${regionName}地区`)
}

// 模拟实时数据更新
const updateRealtimeData = () => {
  const events = ['新用户注册', '订单支付', '社群加入', '佣金结算', '推广点击']
  const values = ['张三', '李四', '王五', '¥299', '¥156', '¥88', '前端交流群', '产品经理群']
  
  const newItem = {
    id: Date.now(),
    time: new Date().toLocaleTimeString('zh-CN'),
    event: events[Math.floor(Math.random() * events.length)],
    value: values[Math.floor(Math.random() * values.length)]
  }
  
  realtimeData.value.unshift(newItem)
  if (realtimeData.value.length > 10) {
    realtimeData.value.pop()
  }
}

let timeInterval = null
let dataInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  dataInterval = setInterval(updateRealtimeData, 3000)
})

onUnmounted(() => {
  if (timeInterval) clearInterval(timeInterval)
  if (dataInterval) clearInterval(dataInterval)
})
</script>

<style scoped>
.simple-data-screen {
  min-height: 100vh;
  background:
    radial-gradient(ellipse at top, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #e2e8f0;
  padding: var(--spacing-xl);
  overflow-x: hidden;
  position: relative;
}

.simple-data-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundPulse 8s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.screen-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  font-size: var(--font-size-2xl);
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
}

.screen-title {
  margin: 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  background: linear-gradient(135deg, #60a5fa, #34d399, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(96, 165, 250, 0.5);
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  from { filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.5)); }
  to { filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.8)); }
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 科技风格按钮 */
.header-actions :deep(.el-button) {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #60a5fa;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.header-actions :deep(.el-button::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s ease;
}

.header-actions :deep(.el-button:hover) {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #93c5fd;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.header-actions :deep(.el-button:hover::before) {
  left: 100%;
}

.header-actions :deep(.el-button--primary) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8));
  border: 1px solid rgba(59, 130, 246, 0.6);
  color: white;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.header-actions :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(139, 92, 246, 1));
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.6);
}

.current-time {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: #10b981;
  text-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
}

.refresh-dot {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #10b981, #059669);
  border-radius: 50%;
  box-shadow:
    0 0 10px rgba(16, 185, 129, 0.6),
    0 0 20px rgba(16, 185, 129, 0.3);
  animation: techPulse 2s infinite;
}

@keyframes techPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow:
      0 0 10px rgba(16, 185, 129, 0.6),
      0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
    box-shadow:
      0 0 15px rgba(16, 185, 129, 0.8),
      0 0 30px rgba(16, 185, 129, 0.5);
  }
}

.metrics-section {
  margin-bottom: var(--spacing-2xl);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.metric-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: iconRotate 4s linear infinite;
}

@keyframes iconRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.metric-content {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.metric-label {
  font-size: 12px; /* 固定字体大小防止溢出 */
  color: #94a3b8;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-value {
  font-size: 20px; /* 调整字体大小防止溢出 */
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  color: #e2e8f0;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-change {
  font-size: 10px; /* 调整字体大小防止溢出 */
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-change.positive {
  color: #10b981;
  text-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.metric-change.negative {
  color: #ef4444;
  text-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
}

.charts-section {
  margin-bottom: var(--spacing-2xl);
}

.chart-card {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: var(--spacing-xl);
  height: 300px;
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.chart-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, #e2e8f0, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
}

.chart-content {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
}

.chart-placeholder p {
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-md);
  color: var(--text-secondary);
}

/* 收入趋势图表样式 */
.revenue-chart {
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-data {
  display: flex;
  align-items: end;
  gap: var(--spacing-lg);
  height: 200px;
}

.data-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.data-bar {
  width: 24px;
  background: var(--primary-500);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  min-height: 20px;
  transition: all var(--transition-normal);
}

.data-bar:hover {
  background: var(--primary-600);
}

.data-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.data-value {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 用户分布样式 */
.user-distribution {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 100%;
  justify-content: center;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.distribution-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: white;
}

.distribution-info {
  flex: 1;
}

.distribution-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.distribution-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.distribution-percentage {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 社群活跃度排行样式 */
.activity-ranking {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
  justify-content: center;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.ranking-item:hover {
  background: var(--gray-100);
  transform: translateX(4px);
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  background: var(--gray-300);
  color: white;
}

.ranking-number.top-1 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #b45309;
}

.ranking-number.top-2 {
  background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
  color: #374151;
}

.ranking-number.top-3 {
  background: linear-gradient(135deg, #cd7f32, #d97706);
  color: white;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.ranking-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.stat-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.stat-value.active {
  color: var(--success-600);
}

.ranking-rate {
  display: flex;
  align-items: center;
}

.rate-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rate-inner {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

/* 转化漏斗样式 */
.funnel-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
  justify-content: center;
}

.funnel-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.funnel-stage {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.funnel-bar {
  height: 36px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  color: white;
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.funnel-bar:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.funnel-content {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.funnel-label {
  font-weight: var(--font-weight-semibold);
}

.funnel-value {
  font-weight: var(--font-weight-bold);
}

.funnel-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 100px;
}

.funnel-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.funnel-conversion {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.conversion-summary {
  font-size: var(--font-size-sm);
  color: var(--success-600);
  font-weight: var(--font-weight-semibold);
}

/* 地图卡片样式 */
.map-card {
  height: 500px;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.map-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6, #3b82f6);
  background-size: 200% 100%;
  animation: borderFlow 3s linear infinite;
}

@keyframes borderFlow {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 0%; }
}

.map-legend {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.light {
  background: #8b5cf6;
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
}

.legend-color.medium {
  background: #06b6d4;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.legend-color.heavy {
  background: #3b82f6;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

.legend-text {
  font-size: var(--font-size-xs);
  color: #94a3b8;
  text-shadow: 0 0 5px rgba(148, 163, 184, 0.3);
}

/* 中国地图样式 */
.china-map {
  display: flex;
  height: 100%;
  gap: var(--spacing-xl);
}

.map-container {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-svg {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-lg);
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(15, 23, 42, 0.5);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.2),
    inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.china-border {
  filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5));
  transition: all var(--transition-normal);
}

.china-border:hover {
  stroke: rgba(59, 130, 246, 0.8);
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.8));
}

.heat-zone {
  pointer-events: none;
}

.city-marker {
  cursor: pointer;
  transition: all var(--transition-normal);
  stroke: rgba(255, 255, 255, 0.8);
  stroke-width: 1;
}

.city-marker:hover {
  transform: scale(1.3);
  stroke-width: 2;
  stroke: rgba(255, 255, 255, 1);
}

.city-label {
  font-size: 10px;
  fill: #e2e8f0;
  font-weight: var(--font-weight-bold);
  pointer-events: none;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
  font-family: 'Arial', sans-serif;
}

.connection-line {
  pointer-events: none;
}

/* 地域详情样式 */
.region-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.region-stats {
  height: 100%;
}

.stats-header h4 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: calc(100% - 40px);
  overflow-y: auto;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover {
  background: rgba(30, 41, 59, 0.8);
  transform: translateX(4px);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.stat-card:hover::before {
  left: 100%;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3));
  border: 1px solid rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.stat-info {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.stat-name {
  font-size: 12px; /* 固定字体大小防止溢出 */
  font-weight: var(--font-weight-semibold);
  color: #94a3b8;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-count {
  font-size: 16px; /* 调整字体大小防止溢出 */
  font-weight: var(--font-weight-bold);
  color: #e2e8f0;
  margin-bottom: 2px;
  text-shadow: 0 0 10px rgba(226, 232, 240, 0.3);
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-growth {
  font-size: 10px; /* 调整字体大小防止溢出 */
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-growth {
  color: #10b981;
  text-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.stat-growth[data-negative="true"] {
  color: #ef4444;
  text-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
}

.realtime-section {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-xl);
}

.realtime-header h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.realtime-content {
  max-height: 200px;
  overflow-y: auto;
}

.realtime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--gray-100);
  animation: slideIn 0.5s ease;
}

.realtime-item:last-child {
  border-bottom: none;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.realtime-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  min-width: 80px;
}

.realtime-event {
  flex: 1;
  margin: 0 var(--spacing-lg);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.realtime-value {
  font-weight: var(--font-weight-semibold);
  min-width: 80px;
  text-align: right;
  color: var(--text-primary);
}

@media (max-width: 1200px) {
  .china-map {
    flex-direction: column;
  }

  .map-container {
    flex: none;
    height: 300px;
  }

  .region-details {
    flex: none;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  .simple-data-screen {
    padding: var(--spacing-lg);
  }

  .screen-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
    padding: var(--spacing-lg);
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .screen-title {
    font-size: var(--font-size-xl);
  }

  .chart-card {
    height: auto;
    min-height: 250px;
  }

  .chart-content {
    height: auto;
    min-height: 190px;
  }

  .chart-placeholder {
    font-size: var(--font-size-2xl);
  }

  .map-card {
    height: auto;
    min-height: 400px;
  }

  .china-map {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .map-container {
    height: 250px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .ranking-stats {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .funnel-stage {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .funnel-meta {
    min-width: auto;
    flex-direction: row;
    gap: var(--spacing-md);
  }
}
</style>
