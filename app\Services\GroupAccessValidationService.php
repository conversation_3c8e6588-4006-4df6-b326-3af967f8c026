<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\User;
use App\Models\Substation;
use App\Models\GroupAccessLog;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 群组访问验证服务
 * 处理群组访问权限验证、防封检测、访问统计等功能
 */
class GroupAccessValidationService
{
    /**
     * 验证群组访问权限（基于源码包逻辑）
     */
    public function validateGroupAccess(int $groupId, string $domain = null): array
    {
        try {
            // 1. 检查群组是否存在
            $group = WechatGroup::find($groupId);
            if (!$group) {
                return $this->createErrorResponse('群组不存在', 'https://www.baidu.com/s?ie=UTF-8&wd=群组不存在');
            }

            // 2. 检查群组状态
            if ($group->status != WechatGroup::STATUS_ACTIVE) {
                $message = $group->status == WechatGroup::STATUS_DISABLED ? '群组被禁用' : '群组已满';
                return $this->createErrorResponse($message, "https://www.baidu.com/s?ie=UTF-8&wd={$message}");
            }

            // 3. 检查分销员状态
            $distributor = $group->user;
            if (!$distributor || $distributor->status != 'active') {
                return $this->createErrorResponse('分销商不存在或被禁用', 'https://www.baidu.com/s?ie=UTF-8&wd=分销商不可用');
            }

            // 4. 检查分站状态
            $substation = $group->substation;
            if (!$substation || $substation->status != 'active') {
                $message = !$substation ? '分站不存在' : 
                          ($substation->status == 'expired' ? '分站已到期' : '分站被禁用');
                return $this->createErrorResponse($message, "https://www.baidu.com/s?ie=UTF-8&wd={$message}");
            }

            // 5. 检查域名匹配（如果提供了域名）
            if ($domain && $substation->domain && $domain !== $substation->domain) {
                return $this->createErrorResponse('不存在的分站域名', 'https://www.baidu.com/s?ie=UTF-8&wd=域名错误');
            }

            // 6. 检查点卡余额（如果启用了扣费）
            if (isset($substation->deduction_rate) && $substation->deduction_rate > 0) {
                $requiredBalance = $group->price * ($substation->deduction_rate / 100);
                
                // 检查二级分站扣费
                if ($substation->parent_id) {
                    $parentSubstation = Substation::find($substation->parent_id);
                    if ($parentSubstation && isset($parentSubstation->deduction_rate) && $parentSubstation->deduction_rate > 0) {
                        $requiredBalance += $group->price * ($parentSubstation->deduction_rate / 100);
                    }
                }
                
                if (isset($substation->balance) && $substation->balance < $requiredBalance) {
                    return $this->createErrorResponse('点卡不足，请先充值', 'https://www.baidu.com/s?ie=UTF-8&wd=点卡不足');
                }
            }

            // 7. 检查支付功能
            $paymentChannels = $group->getAvailablePaymentMethods();
            if (empty($paymentChannels)) {
                return $this->createErrorResponse('分销商未开启支付功能', 'https://www.baidu.com/s?ie=UTF-8&wd=支付功能未开启');
            }

            // 8. 所有验证通过
            return [
                'valid' => true,
                'group' => $group,
                'distributor' => $distributor,
                'substation' => $substation,
                'payment_channels' => $paymentChannels,
            ];

        } catch (\Exception $e) {
            Log::error('群组访问验证失败', [
                'group_id' => $groupId,
                'domain' => $domain,
                'error' => $e->getMessage(),
            ]);

            return $this->createErrorResponse('系统错误', 'https://www.baidu.com/s?ie=UTF-8&wd=系统维护中');
        }
    }

    /**
     * 生成加密的群组访问链接
     */
    public function generateSecureGroupLink(WechatGroup $group): string
    {
        $timestamp = time();
        $token = md5($group->id . $timestamp . config('app.key'));
        
        $baseUrl = optional($group->substation)->domain ?? config('app.url');
        
        return "https://{$baseUrl}/group/{$group->id}?t={$token}&time={$timestamp}";
    }

    /**
     * 验证访问令牌
     */
    public function validateAccessToken(int $groupId, string $token, int $timestamp): bool
    {
        // 检查时间戳是否过期（24小时）
        if (time() - $timestamp > 86400) {
            return false;
        }

        // 验证令牌
        $expectedToken = md5($groupId . $timestamp . config('app.key'));
        
        return hash_equals($expectedToken, $token);
    }

    /**
     * 记录访问日志
     */
    public function logAccess(int $groupId, array $validationResult, array $browserInfo = []): void
    {
        try {
            $ipLocationService = app(IPLocationService::class);
            $browserService = app(BrowserDetectionService::class);
            
            $logData = [
                'group_id' => $groupId,
                'visitor_ip' => request()->ip(),
                'city' => $ipLocationService->getCity(request()->ip()),
                'browser_type' => $browserService->getBrowserType(),
                'user_agent' => request()->header('User-Agent'),
                'referer' => request()->header('Referer'),
                'access_result' => $validationResult['valid'] ? 'success' : 'failed',
                'failure_reason' => $validationResult['message'] ?? null,
                'access_time' => now(),
            ];

            GroupAccessLog::create($logData);
        } catch (\Exception $e) {
            Log::warning('记录访问日志失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 检查访问频率限制
     */
    public function checkRateLimit(string $ip, int $groupId): bool
    {
        $cacheKey = "access_rate_limit:{$ip}:{$groupId}";
        $attempts = Cache::get($cacheKey, 0);
        
        // 每分钟最多访问10次
        if ($attempts >= 10) {
            return false;
        }
        
        Cache::put($cacheKey, $attempts + 1, 60);
        return true;
    }

    /**
     * 检查IP是否被封禁
     */
    public function isIpBlocked(string $ip): bool
    {
        // 检查IP黑名单
        $blockedIps = Cache::remember('blocked_ips', 3600, function () {
            // 这里可以从数据库或配置文件读取封禁IP列表
            return config('security.blocked_ips', []);
        });

        return in_array($ip, $blockedIps);
    }

    /**
     * 获取访问统计
     */
    public function getAccessStats(int $groupId, int $days = 7): array
    {
        $stats = Cache::remember("access_stats:{$groupId}:{$days}", 1800, function () use ($groupId, $days) {
            $logs = GroupAccessLog::where('group_id', $groupId)
                ->where('created_at', '>=', now()->subDays($days))
                ->get();

            return [
                'total_visits' => $logs->count(),
                'unique_visitors' => $logs->unique('visitor_ip')->count(),
                'success_rate' => $logs->count() > 0 ? 
                    round(($logs->where('access_result', 'success')->count() / $logs->count()) * 100, 2) : 0,
                'top_cities' => $logs->groupBy('city')->map->count()->sortDesc()->take(5)->toArray(),
                'browser_distribution' => $logs->groupBy('browser_type')->map->count()->sortDesc()->toArray(),
                'hourly_distribution' => $this->getHourlyDistribution($logs),
                'daily_trend' => $this->getDailyTrend($logs, $days),
            ];
        });

        return $stats;
    }

    /**
     * 获取小时分布
     */
    private function getHourlyDistribution($logs): array
    {
        $hourlyStats = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $hourlyStats[$hour] = $logs->filter(function ($log) use ($hour) {
                return $log->created_at->hour == $hour;
            })->count();
        }
        
        return $hourlyStats;
    }

    /**
     * 获取每日趋势
     */
    private function getDailyTrend($logs, int $days): array
    {
        $dailyStats = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyStats[$date] = $logs->filter(function ($log) use ($date) {
                return $log->created_at->format('Y-m-d') == $date;
            })->count();
        }
        
        return $dailyStats;
    }

    /**
     * 创建错误响应
     */
    private function createErrorResponse(string $message, string $redirectUrl): array
    {
        return [
            'valid' => false,
            'message' => $message,
            'redirect_url' => $redirectUrl,
        ];
    }

    /**
     * 批量验证群组访问权限
     */
    public function batchValidateAccess(array $groupIds): array
    {
        $results = [];
        
        foreach ($groupIds as $groupId) {
            $results[$groupId] = $this->validateGroupAccess($groupId);
        }
        
        return $results;
    }

    /**
     * 获取群组访问报告
     */
    public function getAccessReport(int $groupId, string $period = '7days'): array
    {
        $days = match($period) {
            '1day' => 1,
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            default => 7
        };

        $stats = $this->getAccessStats($groupId, $days);
        $group = WechatGroup::find($groupId);

        return [
            'group_info' => [
                'id' => $group->id,
                'title' => $group->title,
                'price' => $group->price,
                'status' => $group->status_name,
            ],
            'period' => $period,
            'stats' => $stats,
            'recommendations' => $this->generateRecommendations($stats),
        ];
    }

    /**
     * 生成优化建议
     */
    private function generateRecommendations(array $stats): array
    {
        $recommendations = [];

        // 成功率建议
        if ($stats['success_rate'] < 80) {
            $recommendations[] = [
                'type' => 'success_rate',
                'message' => '访问成功率较低，建议检查群组配置和支付设置',
                'priority' => 'high'
            ];
        }

        // 访问量建议
        if ($stats['total_visits'] < 100) {
            $recommendations[] = [
                'type' => 'traffic',
                'message' => '访问量较少，建议加强推广或优化群组标题',
                'priority' => 'medium'
            ];
        }

        // 浏览器兼容性建议
        $wechatRatio = ($stats['browser_distribution']['wechat'] ?? 0) / max($stats['total_visits'], 1);
        if ($wechatRatio > 0.7) {
            $recommendations[] = [
                'type' => 'browser',
                'message' => '微信访问占比较高，建议优化微信浏览器体验',
                'priority' => 'medium'
            ];
        }

        return $recommendations;
    }

    /**
     * 检查群组是否需要防封处理
     */
    public function needsAntiBlockProtection(WechatGroup $group): bool
    {
        // 检查最近访问失败率
        $recentLogs = GroupAccessLog::where('group_id', $group->id)
            ->where('created_at', '>=', now()->subHours(1))
            ->get();

        if ($recentLogs->count() > 10) {
            $failureRate = $recentLogs->where('access_result', 'failed')->count() / $recentLogs->count();
            if ($failureRate > 0.5) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取防封建议
     */
    public function getAntiBlockSuggestions(WechatGroup $group): array
    {
        $suggestions = [];

        // 检查域名健康状态
        if ($group->substation && $group->substation->domain) {
            $domainPool = \App\Models\DomainPool::where('domain', $group->substation->domain)->first();
            if ($domainPool && $domainPool->health_score < 60) {
                $suggestions[] = [
                    'type' => 'domain',
                    'message' => '当前域名健康分数较低，建议更换域名',
                    'action' => 'change_domain'
                ];
            }
        }

        // 检查微信访问限制
        if ($group->wx_accessible == 1) {
            $wechatFailures = GroupAccessLog::where('group_id', $group->id)
                ->where('browser_type', 'wechat')
                ->where('access_result', 'failed')
                ->where('created_at', '>=', now()->subDays(1))
                ->count();

            if ($wechatFailures > 10) {
                $suggestions[] = [
                    'type' => 'wechat_access',
                    'message' => '微信访问失败较多，建议启用微信访问限制',
                    'action' => 'restrict_wechat'
                ];
            }
        }

        return $suggestions;
    }
}