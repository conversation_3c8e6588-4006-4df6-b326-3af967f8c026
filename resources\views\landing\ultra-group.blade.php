<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title data-geo-replace>{{ $group->title ?? '加载中...' }}</title>
    <meta name="description" content="{{ $group->description ?? '' }}">
    <meta name="keywords" content="微信群,{{ $group->category_name ?? '' }},社群">
    
    {{-- Open Graph Meta Tags --}}
    <meta property="og:title" content="{{ $group->title }}">
    <meta property="og:description" content="{{ $group->description }}">
    <meta property="og:image" content="{{ $group->avatar_url }}">
    <meta property="og:type" content="website">
    
    {{-- 预加载关键资源 --}}
    <link rel="preconnect" href="https://restapi.amap.com">
    <link rel="preconnect" href="https://api.map.baidu.com">
    <link rel="preconnect" href="https://cdn.bootcdn.net">
    
    {{-- jQuery --}}
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
    {{-- 高德地图API（用于定位） --}}
    <script>
        window._AMapSecurityConfig = {
            securityJsCode: '{{ config("services.amap.security_code", "61f55187b6feeb3794ccbe8fba390442") }}'
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key={{ config('services.amap.key', 'fdc6f731641c83601f4ffeb3dca10cd6') }}"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 定位加载提示 */
        .location-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 9999;
            transition: transform 0.3s ease;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .location-loading.hidden {
            transform: translateY(-100%);
        }
        
        /* 微信引导层 */
        .wechat-guide {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 99999;
            display: none;
        }
        
        .wechat-guide.show {
            display: block;
        }
        
        .wechat-guide img {
            position: absolute;
            top: 10px;
            right: 20px;
            width: 200px;
        }
        
        .wechat-guide p {
            position: absolute;
            top: 220px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            font-size: 18px;
        }
        
        /* 头部区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 20px;
            background: white;
            border-radius: 20px 20px 0 0;
        }
        
        .group-avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            margin: 0 auto 15px;
            border: 3px solid rgba(255,255,255,0.3);
            object-fit: cover;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        .group-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .group-subtitle {
            font-size: 14px;
            opacity: 0.95;
            margin-bottom: 20px;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        /* 内容区域 */
        .content {
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #667eea;
        }
        
        /* 虚拟成员展示 */
        .members-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        
        .member-item {
            text-align: center;
        }
        
        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-bottom: 5px;
            object-fit: cover;
        }
        
        .member-name {
            font-size: 12px;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 虚拟评论 */
        .comment-list {
            margin-top: 15px;
        }
        
        .comment-item {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .comment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
        }
        
        .comment-content {
            flex: 1;
        }
        
        .comment-user {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .comment-text {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 8px;
        }
        
        .comment-actions {
            font-size: 12px;
            color: #999;
        }
        
        .comment-like {
            color: #ff6b6b;
            margin-right: 15px;
        }
        
        /* 加入按钮 */
        .join-button {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .join-button:active {
            opacity: 0.9;
        }
        
        .button-text {
            margin-right: 10px;
        }
        
        .button-price {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 16px;
        }
        
        /* 群简介 */
        .group-intro {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.8;
            color: #555;
        }
        
        /* 提示信息 */
        .tips {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 5px;
            padding: 10px;
            margin: 15px 0;
            font-size: 13px;
            color: #856404;
        }
        
        /* 倒计时提示 */
        .countdown-banner {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        /* 底部占位 */
        .bottom-space {
            height: 80px;
        }
        
        /* 加载动画 */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            animation: pulse 1.5s ease-in-out infinite;
        }
    </style>
</head>
<body>
    {{-- 定位加载提示 --}}
    <div class="location-loading" id="locationLoading">
        <span class="loading">正在获取您的位置信息...</span>
    </div>
    
    {{-- 微信引导层 --}}
    <div class="wechat-guide" id="wechatGuide">
        <img src="/images/open-browser.png" alt="点击右上角在浏览器打开">
        <p>请点击右上角 ··· 选择"在浏览器打开"</p>
    </div>
    
    <div class="container">
        {{-- 群组头部 --}}
        <div class="header">
            <img src="{{ $group->avatar_url ?? '/images/default-group.png' }}" alt="群头像" class="group-avatar">
            <h1 class="group-title" data-city-replace>
                {{ $group->title }}
            </h1>
            <p class="group-subtitle">{{ $group->subtitle ?? '高质量社群，等你加入' }}</p>
            
            <div class="stats-row">
                <div class="stat-item">
                    <span class="stat-number">{{ $virtualStats['total_members'] ?? rand(100, 500) }}</span>
                    <span class="stat-label">群成员</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ $virtualStats['today_joined'] ?? rand(5, 20) }}</span>
                    <span class="stat-label">今日加入</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ $virtualStats['read_count'] ?? '10万+' }}</span>
                    <span class="stat-label">阅读量</span>
                </div>
            </div>
        </div>
        
        {{-- 倒计时提示 --}}
        <div class="countdown-banner">
            <span id="countdown">限时优惠，仅剩 <strong>23:59:59</strong></span>
        </div>
        
        <div class="content">
            {{-- 群简介 --}}
            @if($group->description)
            <div class="section">
                <div class="section-title">群简介</div>
                <div class="group-intro">
                    {!! nl2br(e($group->description)) !!}
                </div>
            </div>
            @endif
            
            {{-- 虚拟成员展示 --}}
            <div class="section">
                <div class="section-title">群成员 ({{ count($virtualMembers) }})</div>
                <div class="members-grid">
                    @foreach(array_slice($virtualMembers, 0, 15) as $member)
                    <div class="member-item">
                        <img src="{{ $member['avatar'] }}" alt="{{ $member['nickname'] }}" class="member-avatar">
                        <div class="member-name">{{ $member['nickname'] }}</div>
                    </div>
                    @endforeach
                </div>
                @if(count($virtualMembers) > 15)
                <p style="text-align: center; color: #999; font-size: 12px; margin-top: 10px;">
                    还有{{ count($virtualMembers) - 15 }}位成员...
                </p>
                @endif
            </div>
            
            {{-- 群组内容 --}}
            @if($group->content)
            <div class="section">
                <div class="section-title">群组特色</div>
                <div class="group-intro">
                    {!! $group->content !!}
                </div>
            </div>
            @endif
            
            {{-- 虚拟评论 --}}
            <div class="section">
                <div class="section-title">群友评价</div>
                <div class="comment-list">
                    @foreach($virtualComments as $comment)
                    <div class="comment-item">
                        <img src="{{ $comment['avatar'] }}" alt="" class="comment-avatar">
                        <div class="comment-content">
                            <div class="comment-user">{{ $comment['nickname'] }}</div>
                            <div class="comment-text">{{ $comment['content'] }}</div>
                            <div class="comment-actions">
                                <span class="comment-like">👍 {{ $comment['likes'] }}</span>
                                <span>{{ $comment['time'] ?? '刚刚' }}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            
            {{-- 温馨提示 --}}
            <div class="tips">
                💡 温馨提示：群组名额有限，请尽快加入。支付后自动拉群，请保持微信在线。
            </div>
        </div>
        
        {{-- 底部占位 --}}
        <div class="bottom-space"></div>
        
        {{-- 加入按钮 --}}
        <button class="join-button" id="joinButton">
            <span class="button-text">立即加入群聊</span>
            <span class="button-price">¥{{ $group->price }}</span>
        </button>
    </div>
    
    <script>
    // 超级定位系统（整合ThinkPHP的逻辑）
    class UltraLocationSystem {
        constructor() {
            this.city = null;
            this.loading = layer.open({ type: 3, shadeClose: false, content: '定位中...' });
        }
        
        async init() {
            try {
                // 1. 尝试从Cookie读取（兼容ThinkPHP的curcity2）
                this.city = this.getCookie('curcity2');
                
                if (!this.city) {
                    // 2. 使用高德地图定位
                    await this.getLocationFromAmap();
                }
                
                // 3. 如果还是没有，使用百度API
                if (!this.city) {
                    await this.getLocationFromBaidu();
                }
                
                // 4. 更新页面内容
                this.updatePageContent();
                
            } catch (error) {
                console.error('定位失败:', error);
                this.city = '全国';
                this.updatePageContent();
            } finally {
                // 5. 隐藏加载提示
                this.hideLoading();
                if (this.loading) layer.close(this.loading);
            }
        }
        
        async getLocationFromAmap() {
            return new Promise((resolve) => {
                AMap.plugin(['AMap.Geolocation', 'AMap.Geocoder'], () => {
                    const geolocation = new AMap.Geolocation({
                        enableHighAccuracy: true,
                        timeout: 4000,
                        maximumAge: 0
                    });
                    
                    geolocation.getCurrentPosition((status, result) => {
                        if (status === 'complete' && result.position) {
                            const geocoder = new AMap.Geocoder();
                            geocoder.getAddress(result.position, (status, result) => {
                                if (status === 'complete' && result.regeocode) {
                                    this.city = result.regeocode.addressComponent.city;
                                    this.city = this.city.replace('市', '');
                                    this.setCookie('curcity2', this.city, 600);
                                }
                                resolve();
                            });
                        } else {
                            resolve();
                        }
                    });
                });
            });
        }
        
        async getLocationFromBaidu() {
            try {
                const response = await fetch('/api/location/current');
                const data = await response.json();
                if (data.success && data.city) {
                    this.city = data.city.replace('市', '');
                    this.setCookie('curcity2', this.city, 600);
                }
            } catch (e) {
                console.error('Baidu API failed:', e);
            }
        }
        
        updatePageContent() {
            const city = this.city || '您所在城市';
            const title = "{{ $group->title }}";
            
            // 替换所有占位符（兼容ThinkPHP的xxx）
            document.querySelectorAll('[data-geo-replace]').forEach(el => {
                let content = el.innerHTML;
                content = content.replace(/xxx/gi, city);
                content = content.replace(/\{city\}/gi, city);
                content = content.replace(/定位中\.\.\./g, city);
                el.innerHTML = content;
            });
            
            // 更新页面标题
            document.title = title.replace(/xxx/gi, city);
        }
        
        hideLoading() {
            const loader = document.getElementById('locationLoading');
            if (loader) {
                loader.classList.add('hidden');
                setTimeout(() => loader.remove(), 300);
            }
        }
        
        getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) {
                return parts.pop().split(';').shift();
            }
            return null;
        }
        
        setCookie(name, value, seconds) {
            const expires = new Date(Date.now() + seconds * 1000);
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }
    }
    
    // 防检测系统（借鉴ThinkPHP的wxonoff逻辑）
    class AntiDetectionSystem {
        constructor() {
            this.isWechat = /MicroMessenger/i.test(navigator.userAgent);
            this.isQQ = /QQ\//i.test(navigator.userAgent);
            this.wxonoff = {{ $group->anti_block_config['wxonoff'] ?? 2 }};
        }
        
        init() {
            // ThinkPHP兼容：wxonoff = 2 表示微信内不能打开
            if (this.wxonoff === 2 && (this.isWechat || this.isQQ)) {
                this.showBrowserGuide();
                return false;
            }
            
            // 添加防调试保护
            this.addAntiDebug();
            
            return true;
        }
        
        showBrowserGuide() {
            const guide = document.getElementById('wechatGuide');
            if (guide) {
                guide.classList.add('show');
            }
        }
        
        addAntiDebug() {
            // 禁用右键
            document.addEventListener('contextmenu', e => e.preventDefault());
            
            // 禁用F12和开发者工具快捷键
            document.addEventListener('keydown', e => {
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                    (e.ctrlKey && e.shiftKey && e.key === 'J') ||
                    (e.ctrlKey && e.key === 'U')) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }
    
    // 倒计时系统
    class CountdownSystem {
        constructor() {
            this.endTime = new Date();
            this.endTime.setHours(23, 59, 59);
        }
        
        start() {
            this.update();
            setInterval(() => this.update(), 1000);
        }
        
        update() {
            const now = new Date();
            const diff = this.endTime - now;
            
            if (diff <= 0) {
                this.endTime = new Date();
                this.endTime.setDate(this.endTime.getDate() + 1);
                this.endTime.setHours(23, 59, 59);
            }
            
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            const countdown = document.getElementById('countdown');
            if (countdown) {
                countdown.innerHTML = `限时优惠，仅剩 <strong>${timeString}</strong>`;
            }
        }
    }
    
    // 支付处理
    function handleJoinClick() {
        // 触发支付流程
        window.location.href = "{{ route('payment.create', ['group_id' => $group->id]) }}";
    }
    
    // 初始化所有系统
    document.addEventListener('DOMContentLoaded', function() {
        // 1. 初始化防检测系统
        const antiDetection = new AntiDetectionSystem();
        if (!antiDetection.init()) {
            return; // 如果在微信中且需要引导，停止执行
        }
        
        // 2. 初始化定位系统
        const locationSystem = new UltraLocationSystem();
        locationSystem.init();
        
        // 3. 初始化倒计时
        const countdown = new CountdownSystem();
        countdown.start();
        
        // 4. 绑定加入按钮
        const joinButton = document.getElementById('joinButton');
        if (joinButton) {
            joinButton.addEventListener('click', handleJoinClick);
        }
        
        // 5. 随机弹出加入提示（借鉴ThinkPHP）
        const names = ["李先生", "王女士", "张先生", "刘女士", "陈先生"];
        setInterval(() => {
            const name = names[Math.floor(Math.random() * names.length)];
            const tip = `${name}*** 刚刚支付了{{ $group->price }}元入群`;
            console.log(tip); // 实际项目中可以显示为toast提示
        }, 5000);
    });
    </script>
    
    {{-- Layer弹层库（用于提示） --}}
    <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/mobile/layer.js"></script>
</body>
</html>