<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB; // Added for DB facade
use App\Models\BalanceLog; // Added for BalanceLog model

class UserController extends Controller
{
    /**
     * 获取用户列表
     */
    public function index(Request $request)
    {
        $query = User::with(['parent', 'substation']);
        
        // 搜索条件
        if ($request->filled('username')) {
            $query->where('username', 'like', '%' . $request->username . '%');
        }
        
        if ($request->filled('role')) {
            $roles = explode(',', $request->role);
            $query->whereIn('role', $roles);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $users = $query->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }
    
    /**
     * 创建用户
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|unique:users|max:50',
            'password' => 'required|min:6',
            'nickname' => 'required|max:50',
            'email' => 'nullable|email|unique:users',
            'phone' => 'nullable|regex:/^1[3-9]\d{9}$/',
            'role' => 'required|in:admin,substation,distributor,user',
            'parent_id' => 'nullable|exists:users,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $user = User::create([
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'nickname' => $request->nickname,
            'email' => $request->email,
            'phone' => $request->phone,
            'role' => $request->role,
            'parent_id' => $request->parent_id,
            'status' => 1,
            'balance' => 0
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '用户创建成功'
        ]);
    }
    
    /**
     * 获取用户详情
     */
    public function show($id)
    {
        $user = User::with(['parent', 'children', 'substation'])->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }
    
    /**
     * 更新用户
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'username' => [
                'required',
                'max:50',
                Rule::unique('users')->ignore($user->id)
            ],
            'nickname' => 'required|max:50',
            'email' => [
                'nullable',
                'email',
                Rule::unique('users')->ignore($user->id)
            ],
            'phone' => 'nullable|regex:/^1[3-9]\d{9}$/',
            'role' => 'required|in:admin,substation,distributor,user',
            'parent_id' => 'nullable|exists:users,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $user->update($request->only([
            'username', 'nickname', 'email', 'phone', 'role', 'parent_id'
        ]));
        
        return response()->json([
            'success' => true,
            'data' => $user,
            'message' => '用户更新成功'
        ]);
    }
    
    /**
     * 删除用户
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        
        // 检查是否有下级用户
        if ($user->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该用户有下级用户，无法删除'
            ], 400);
        }
        
        $user->delete();
        
        return response()->json([
            'success' => true,
            'message' => '用户删除成功'
        ]);
    }
    
    /**
     * 更新用户状态
     */
    public function updateStatus(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:1,2'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $user->update(['status' => $request->status]);
        
        return response()->json([
            'success' => true,
            'message' => '用户状态更新成功'
        ]);
    }
    
    /**
     * 调整用户余额 (已重构，增加事务和日志)
     */
    public function adjustBalance(Request $request, $id)
    {
        $data = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:add,subtract',
            'remark' => 'required|string|max:255'
        ]);

        $user = User::findOrFail($id);
        $amount = $data['amount'];
        $operator = $request->user();

        try {
            DB::transaction(function () use ($user, $amount, $data, $operator) {
                $balanceBefore = $user->balance;

                if ($data['type'] === 'add') {
                    $user->balance = bcadd(strval($balanceBefore), strval($amount), 2);
                    $balanceAfter = $user->balance;
                } else {
                    if ($balanceBefore < $amount) {
                        throw new \Exception('用户余额不足。');
                    }
                    $user->balance = bcsub(strval($balanceBefore), strval($amount), 2);
                    $balanceAfter = $user->balance;
                }

                $user->save();

                BalanceLog::create([
                    'user_id' => $user->id,
                    'operator_id' => $operator->id,
                    'amount' => ($data['type'] === 'add' ? $amount : -$amount),
                    'balance_before' => $balanceBefore,
                    'balance_after' => $balanceAfter,
                    'type' => 'system_adjust', // 类型定义为后台调整
                    'remark' => $data['remark'],
                ]);
            });
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => '余额调整成功'
        ]);
    }
    
    /**
     * 重置用户密码
     */
    public function resetPassword(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'password' => 'required|min:6'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $user->update([
            'password' => Hash::make($request->password)
        ]);
        
        return response()->json([
            'success' => true,
            'message' => '密码重置成功'
        ]);
    }
    
    /**
     * 获取用户下级
     */
    public function getChildren($id)
    {
        $user = User::findOrFail($id);
        $children = $user->children()->with('children')->get();
        
        return response()->json([
            'success' => true,
            'data' => $children
        ]);
    }
    
    /**
     * 获取用户统计数据
     */
    public function getStats($id)
    {
        $user = User::findOrFail($id);
        
        $stats = [
            'total_orders' => $user->orders()->count(),
            'total_amount' => $user->orders()->sum('total_amount'),
            'total_commission' => $user->commissionLogs()->sum('amount'),
            'children_count' => $user->children()->count(),
            'direct_children_count' => $user->children()->where('parent_id', $id)->count(),
            'monthly_orders' => $user->orders()->whereMonth('created_at', now()->month)->count(),
            'monthly_amount' => $user->orders()->whereMonth('created_at', now()->month)->sum('total_amount'),
            'monthly_commission' => $user->commissionLogs()->whereMonth('created_at', now()->month)->sum('amount'),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 获取用户统计数据 (别名方法，供前端调用)
     */
    public function getUserStats($id)
    {
        return $this->getStats($id);
    }
    
    /**
     * 获取用户余额信息
     */
    public function getBalance(Request $request)
    {
        $user = auth()->user();
        
        return response()->json([
            'success' => true,
            'data' => [
                'balance' => $user->balance,
                'frozen_balance' => $user->frozen_balance,
                'available_balance' => $user->getAvailableBalance(),
                'withdraw_total' => $user->withdraw_total,
            ]
        ]);
    }
    
    /**
     * 获取交易记录
     */
    public function getTransactions(Request $request)
    {
        $user = auth()->user();
        
        $query = $user->commissionLogs();
        
        // 筛选条件
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        if ($request->filled('date_range')) {
            $dates = explode(',', $request->date_range);
            if (count($dates) === 2) {
                $query->whereBetween('created_at', [$dates[0], $dates[1]]);
            }
        }
        
        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }
    
    /**
     * 提现申请
     */
    public function withdraw(Request $request)
    {
        $user = auth()->user();
        
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'bank_name' => 'required|string|max:100',
            'bank_account' => 'required|string|max:50',
            'account_name' => 'required|string|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        // 检查余额是否足够
        if ($request->amount > $user->getAvailableBalance()) {
            return response()->json([
                'success' => false,
                'message' => '余额不足'
            ], 400);
        }
        
        // 创建提现记录
        $withdraw = \App\Models\WithdrawRecord::create([
            'user_id' => $user->id,
            'amount' => $request->amount,
            'bank_name' => $request->bank_name,
            'bank_account' => $request->bank_account,
            'account_name' => $request->account_name,
            'status' => 'pending',
        ]);
        
        // 冻结余额
        $user->freezeBalance($request->amount);
        
        return response()->json([
            'success' => true,
            'message' => '提现申请提交成功',
            'data' => $withdraw
        ]);
    }
    
    /**
     * 获取提现记录
     */
    public function getWithdrawRecords(Request $request)
    {
        $user = auth()->user();
        
        $query = \App\Models\WithdrawRecord::where('user_id', $user->id);
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $records = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => $records
        ]);
    }
    
    /**
     * 获取提现申请列表（管理员）
     */
    public function getWithdrawRequests(Request $request)
    {
        $query = \App\Models\WithdrawRecord::with('user');
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $requests = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => $requests
        ]);
    }
    
    /**
     * 审核通过提现
     */
    public function approveWithdraw(Request $request, $id)
    {
        $withdraw = \App\Models\WithdrawRecord::findOrFail($id);
        
        if ($withdraw->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => '该提现申请已处理'
            ], 400);
        }
        
        $user = $withdraw->user;
        
        // 扣除冻结余额
        $user->unfreezeBalance($withdraw->amount);
        $user->withdraw_total += $withdraw->amount;
        $user->save();
        
        // 更新提现状态
        $withdraw->update([
            'status' => 'approved',
            'processed_at' => now(),
            'processed_by' => auth()->id(),
            'remark' => $request->remark,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => '提现审核通过'
        ]);
    }
    
    /**
     * 拒绝提现
     */
    public function rejectWithdraw(Request $request, $id)
    {
        $withdraw = \App\Models\WithdrawRecord::findOrFail($id);
        
        if ($withdraw->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => '该提现申请已处理'
            ], 400);
        }
        
        $user = $withdraw->user;
        
        // 解冻余额
        $user->unfreezeBalance($withdraw->amount);
        
        // 更新提现状态
        $withdraw->update([
            'status' => 'rejected',
            'processed_at' => now(),
            'processed_by' => auth()->id(),
            'remark' => $request->remark,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => '提现申请已拒绝'
        ]);
    }
    
    /**
     * 获取用户余额调整记录
     */
    public function getBalanceLogs(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $logs = BalanceLog::where('user_id', $user->id)->latest()->paginate(15);
        return response()->json(['success' => true, 'data' => $logs]);
    }

    // ==================== 用户个人信息管理方法 ====================
    
    /**
     * 获取用户个人资料
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'username' => $user->username,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'role' => $user->role,
                'status' => $user->status,
                'balance' => $user->balance ?? 0,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ]
        ]);
    }

    /**
     * 更新用户个人资料
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $user->id,
            'phone' => 'sometimes|string|max:20|unique:users,phone,' . $user->id,
            'avatar' => 'sometimes|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only(['name', 'email', 'phone', 'avatar']));

        return response()->json([
            'success' => true,
            'message' => '资料更新成功',
            'data' => $user->fresh()
        ]);
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => '当前密码错误'
            ], 422);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => '密码修改成功'
        ]);
    }

    /**
     * 获取用户订单列表
     */
    public function orders(Request $request)
    {
        $user = $request->user();
        
        $query = $user->orders()->with(['wechatGroup:id,title']);

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 时间范围过滤
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }

    /**
     * 获取用户群组列表
     */
    public function groups(Request $request)
    {
        $user = $request->user();
        
        $query = $user->wechatGroups()->withCount('orders')->withSum('orders', 'amount');

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $groups
        ]);
    }

    /**
     * 获取用户统计数据
     */
    public function statistics(Request $request)
    {
        $user = $request->user();
        
        $stats = [
            'total_orders' => $user->orders()->count(),
            'paid_orders' => $user->orders()->where('status', 'paid')->count(),
            'total_spent' => $user->orders()->where('status', 'paid')->sum('amount'),
            'total_groups' => $user->wechatGroups()->count(),
            'active_groups' => $user->wechatGroups()->where('status', 1)->count(),
            'balance' => $user->balance ?? 0,
        ];

        // 如果是分销商，添加分销统计
        if ($user->is_distributor) {
            $stats['total_commission'] = $user->commissionLogs()->sum('amount');
            $stats['pending_commission'] = $user->commissionLogs()->where('status', 'pending')->sum('amount');
            $stats['settled_commission'] = $user->commissionLogs()->where('status', 'settled')->sum('amount');
            $stats['direct_children'] = $user->children()->where('is_distributor', true)->count();
        }

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 上传头像
     */
    public function uploadAvatar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $filename = time() . '_' . $user->id . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('avatars', $filename, 'public');
            
            $user->update([
                'avatar' => '/storage/' . $path
            ]);

            return response()->json([
                'success' => true,
                'message' => '头像上传成功',
                'data' => [
                    'avatar' => $user->avatar
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => '头像上传失败'
        ], 422);
    }
} 