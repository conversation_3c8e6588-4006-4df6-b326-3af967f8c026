/* 管理后台现代化样式 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --admin-primary: #667eea;
  --admin-primary-light: #764ba2;
  --admin-primary-dark: #5a67d8;
  --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 辅助色 */
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-error: #ef4444;
  --admin-info: #3b82f6;
  
  /* 中性色 */
  --admin-white: #ffffff;
  --admin-gray-50: #f9fafb;
  --admin-gray-100: #f3f4f6;
  --admin-gray-200: #e5e7eb;
  --admin-gray-300: #d1d5db;
  --admin-gray-400: #9ca3af;
  --admin-gray-500: #6b7280;
  --admin-gray-600: #4b5563;
  --admin-gray-700: #374151;
  --admin-gray-800: #1f2937;
  --admin-gray-900: #111827;
  
  /* 背景 */
  --admin-bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --admin-bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --admin-bg-glass: rgba(255, 255, 255, 0.25);
  --admin-bg-glass-border: rgba(255, 255, 255, 0.18);
  
  /* 阴影 */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --admin-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 圆角 */
  --admin-radius-sm: 0.25rem;
  --admin-radius: 0.5rem;
  --admin-radius-md: 0.75rem;
  --admin-radius-lg: 1rem;
  --admin-radius-xl: 1.5rem;
  --admin-radius-2xl: 2rem;
  
  /* 过渡 */
  --admin-transition: 300ms ease-in-out;
  --admin-transition-fast: 150ms ease-in-out;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--admin-gradient);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Element Plus 组件样式覆盖 */

/* 卡片样式 */
.el-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-lg);
  transition: all var(--admin-transition);
  overflow: hidden;
}

.el-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-xl);
}

.el-card__header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.el-card__body {
  padding: 1.5rem;
}

/* 按钮样式 */
.el-button {
  border-radius: var(--admin-radius-lg);
  font-weight: 500;
  transition: all var(--admin-transition);
  position: relative;
  overflow: hidden;
}

.el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.el-button:hover::before {
  left: 100%;
}

.el-button--primary {
  background: var(--admin-gradient);
  border-color: transparent;
  box-shadow: var(--admin-shadow-md);
}

.el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
}

.el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: transparent;
}

.el-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: transparent;
}

.el-button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-color: transparent;
}

/* 输入框样式 */
.el-input__wrapper {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  transition: all var(--admin-transition);
}

.el-input__wrapper:hover {
  border-color: var(--admin-primary);
  box-shadow: var(--admin-shadow);
}

.el-input__wrapper.is-focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 表格样式 */
.el-table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--admin-radius-xl);
  overflow: hidden;
  box-shadow: var(--admin-shadow-lg);
}

.el-table__header-wrapper {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.el-table th.el-table__cell {
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
  color: var(--admin-gray-700);
}

.el-table td.el-table__cell {
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.el-table__row:hover {
  background: rgba(102, 126, 234, 0.05);
}

/* 分页样式 */
.el-pagination {
  justify-content: center;
  margin-top: 2rem;
}

.el-pagination .el-pager li {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--admin-radius);
  margin: 0 0.25rem;
  transition: all var(--admin-transition);
}

.el-pagination .el-pager li:hover {
  background: var(--admin-primary);
  color: white;
  transform: translateY(-1px);
}

.el-pagination .el-pager li.is-active {
  background: var(--admin-gradient);
  color: white;
  border-color: transparent;
}

/* 标签样式 */
.el-tag {
  border-radius: var(--admin-radius-lg);
  border: none;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.el-tag--primary {
  background: rgba(102, 126, 234, 0.1);
  color: var(--admin-primary);
}

.el-tag--success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--admin-success);
}

.el-tag--warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--admin-warning);
}

.el-tag--danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--admin-error);
}

/* 对话框样式 */
.el-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-2xl);
  box-shadow: var(--admin-shadow-2xl);
}

.el-dialog__header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: var(--admin-radius-2xl) var(--admin-radius-2xl) 0 0;
}

/* 抽屉样式 */
.el-drawer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

/* 菜单样式 */
.el-menu {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: none;
}

.el-menu-item {
  border-radius: var(--admin-radius-lg);
  margin: 0.25rem 0.5rem;
  transition: all var(--admin-transition);
}

.el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-xl);
}

.el-dropdown-menu__item {
  transition: all var(--admin-transition);
}

.el-dropdown-menu__item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--admin-primary);
}

/* 通知样式 */
.el-notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-xl);
}

.el-message {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-lg);
}

/* 自定义组件样式 */

/* 现代化卡片 */
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-lg);
  transition: all var(--admin-transition);
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--admin-shadow-2xl);
}

/* 渐变卡片 */
.gradient-card {
  background: var(--admin-gradient);
  color: white;
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-lg);
  transition: all var(--admin-transition);
  position: relative;
  overflow: hidden;
}

.gradient-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--admin-transition);
}

.gradient-card:hover::before {
  opacity: 1;
}

.gradient-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--admin-shadow-2xl);
}

/* 统计卡片 */
.stat-card {
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--admin-gradient);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: var(--admin-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  color: var(--admin-gray-600);
  font-weight: 500;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.125rem;
}

/* 工具栏 */
.toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--admin-shadow);
}

/* 搜索框 */
.search-box {
  position: relative;
  max-width: 400px;
}

.search-box .el-input__wrapper {
  padding-left: 3rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray-400);
  z-index: 10;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--admin-radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--admin-success);
}

.status-indicator.offline {
  background: rgba(239, 68, 68, 0.1);
  color: var(--admin-error);
}

.status-indicator.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--admin-warning);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--admin-radius);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--admin-gray-500);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--admin-gray-700);
}

.empty-description {
  margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .page-header {
    padding: 1.5rem 0;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--admin-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--admin-primary-dark);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* 延迟动画 */
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }

/* 工具类 */
.text-gradient {
  background: var(--admin-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.backdrop-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.glass-border {
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}/* 数据可
视化组件样式 */
.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: all var(--admin-transition);
}

.chart-container:hover {
  transform: translateY(-4px);
  box-shadow: var(--admin-shadow-xl);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-gray-800);
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-body {
  position: relative;
  min-height: 300px;
}

/* 数据表格增强样式 */
.enhanced-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--admin-radius-xl);
  overflow: hidden;
}

.enhanced-table th {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: var(--admin-gray-700);
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.enhanced-table th:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--admin-gradient);
  transform: scaleX(0);
  transition: transform var(--admin-transition);
}

.enhanced-table th:hover:after {
  transform: scaleX(1);
}

.enhanced-table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  color: var(--admin-gray-700);
}

.enhanced-table tr:last-child td {
  border-bottom: none;
}

.enhanced-table tr:hover td {
  background: rgba(102, 126, 234, 0.05);
}

/* 可排序表头 */
.sortable-header {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-icon {
  opacity: 0.5;
  transition: all var(--admin-transition);
}

.sortable-header:hover .sort-icon {
  opacity: 1;
}

.sortable-header.sorted-asc .sort-icon,
.sortable-header.sorted-desc .sort-icon {
  opacity: 1;
  color: var(--admin-primary);
}

/* 高级表单样式 */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--admin-shadow-lg);
}

.form-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-gray-800);
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--admin-gray-700);
}

.form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--admin-gray-500);
}

.form-error {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--admin-error);
}

/* 高级开关 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--admin-gray-300);
  transition: var(--admin-transition);
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: var(--admin-transition);
  border-radius: 50%;
  box-shadow: var(--admin-shadow-sm);
}

input:checked + .toggle-slider {
  background: var(--admin-gradient);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* 仪表板卡片 */
.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-xl);
  padding: 1.5rem;
  box-shadow: var(--admin-shadow-lg);
  transition: all var(--admin-transition);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--admin-shadow-xl);
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-gray-800);
}

.dashboard-card-icon {
  width: 40px;
  height: 40px;
  background: var(--admin-gradient);
  border-radius: var(--admin-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.dashboard-card-body {
  flex: 1;
}

.dashboard-card-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--admin-gray-800);
  margin-bottom: 0.5rem;
}

.dashboard-card-label {
  font-size: 0.875rem;
  color: var(--admin-gray-600);
  margin-bottom: 1rem;
}

.dashboard-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-card-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.trend-up {
  color: var(--admin-success);
}

.trend-down {
  color: var(--admin-error);
}

/* 高级步骤条 */
.steps-container {
  display: flex;
  margin-bottom: 2rem;
}

.step-item {
  flex: 1;
  text-align: center;
  padding: 1rem;
  position: relative;
}

.step-item:not(:last-child):after {
  content: '';
  position: absolute;
  top: 2.5rem;
  right: 0;
  width: calc(100% - 4rem);
  height: 2px;
  background: var(--admin-gray-300);
  z-index: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--admin-gray-300);
  color: var(--admin-gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin: 0 auto 1rem;
  position: relative;
  z-index: 2;
  transition: all var(--admin-transition);
}

.step-title {
  font-weight: 500;
  color: var(--admin-gray-600);
  margin-bottom: 0.5rem;
  transition: all var(--admin-transition);
}

.step-description {
  font-size: 0.875rem;
  color: var(--admin-gray-500);
}

.step-item.active .step-number {
  background: var(--admin-gradient);
  color: white;
  box-shadow: var(--admin-shadow-md);
}

.step-item.active .step-title {
  color: var(--admin-gray-800);
  font-weight: 600;
}

.step-item.completed .step-number {
  background: var(--admin-success);
  color: white;
}

.step-item.completed:not(:last-child):after {
  background: var(--admin-success);
}

/* 高级文件上传 */
.file-upload {
  background: rgba(255, 255, 255, 0.5);
  border: 2px dashed var(--admin-gray-300);
  border-radius: var(--admin-radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all var(--admin-transition);
  cursor: pointer;
}

.file-upload:hover {
  border-color: var(--admin-primary);
  background: rgba(255, 255, 255, 0.8);
}

.file-upload-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: 1rem;
}

.file-upload-title {
  font-weight: 600;
  color: var(--admin-gray-700);
  margin-bottom: 0.5rem;
}

.file-upload-hint {
  font-size: 0.875rem;
  color: var(--admin-gray-500);
}

.file-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
}

.file-preview-item {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.file-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-preview-remove {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-error);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
}

.file-preview-remove:hover {
  background: var(--admin-error);
  color: white;
}

/* 高级标签输入 */
.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--admin-radius-lg);
  min-height: 3rem;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--admin-gradient);
  color: white;
  border-radius: var(--admin-radius-full);
  font-size: 0.875rem;
}

.tag-remove {
  cursor: pointer;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all var(--admin-transition-fast);
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.5);
}

.tags-input-field {
  flex: 1;
  min-width: 100px;
  border: none;
  outline: none;
  background: transparent;
  padding: 0.25rem;
  color: var(--admin-gray-700);
}

/* 高级通知组件 */
.notification-center {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.notification-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--admin-radius-lg);
  padding: 1rem;
  box-shadow: var(--admin-shadow-lg);
  display: flex;
  gap: 1rem;
  animation: slideInRight 0.3s ease-out;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--admin-success);
}

.notification-icon.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--admin-error);
}

.notification-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--admin-warning);
}

.notification-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--admin-info);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: var(--admin-gray-800);
  margin-bottom: 0.25rem;
}

.notification-message {
  font-size: 0.875rem;
  color: var(--admin-gray-600);
}

.notification-close {
  align-self: flex-start;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-gray-500);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--admin-gray-700);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 暗色模式支持 */
.dark-mode {
  --admin-gray-50: #18191a;
  --admin-gray-100: #242526;
  --admin-gray-200: #3a3b3c;
  --admin-gray-300: #4e4f50;
  --admin-gray-400: #6a6b6c;
  --admin-gray-500: #8a8b8c;
  --admin-gray-600: #a8a9aa;
  --admin-gray-700: #c5c6c7;
  --admin-gray-800: #e4e5e6;
  --admin-gray-900: #f5f6f7;
  
  --admin-bg-glass: rgba(0, 0, 0, 0.25);
  --admin-bg-glass-border: rgba(255, 255, 255, 0.1);
}

.dark-mode .el-card,
.dark-mode .modern-card {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .el-input__wrapper {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .el-table {
  background: rgba(0, 0, 0, 0.3);
}

.dark-mode .el-table th.el-table__cell {
  background: rgba(0, 0, 0, 0.2);
  color: var(--admin-gray-700);
}

.dark-mode .el-table td.el-table__cell {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .el-table__row:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* 打印样式优化 */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .el-card,
  .modern-card,
  .dashboard-card,
  .chart-container,
  .form-section {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    background: white !important;
    break-inside: avoid;
  }
  
  .el-button,
  .modern-btn {
    background: #f0f0f0 !important;
    color: black !important;
    border: 1px solid #ddd !important;
  }
  
  .no-print {
    display: none !important;
  }
}