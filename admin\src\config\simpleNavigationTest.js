/**
 * 简化导航测试配置
 * 用于测试和验证导航系统的基本功能
 */

import { roleHierarchy } from './navigation'

// 简化的导航分组配置（用于测试）
export const simpleNavigationGroups = {
  // 工作台 - 所有角色都可访问
  workbench: {
    title: '工作台',
    icon: 'Monitor',
    order: 1,
    description: '个人工作区和数据分析',
    children: [
      {
        path: '/dashboard',
        title: '数据看板',
        icon: 'DataLine',
        description: '查看核心业务数据',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/data-screen',
        title: '数据大屏',
        icon: 'DataBoard',
        description: '全屏数据展示',
        roles: ['admin', 'substation', 'agent', 'distributor']
      },
      {
        path: '/data-screen/reports',
        title: '报表中心',
        icon: 'Document',
        description: '各类业务报表',
        roles: ['admin', 'substation', 'agent']
      }
    ]
  },

  // 核心业务 - 重点保护群组创建功能
  business: {
    title: '核心业务',
    icon: 'Management',
    order: 2,
    description: '社群管理和订单处理',
    children: [
      {
        path: '/community/groups',
        title: '社群管理',
        icon: 'Comment',
        description: '管理所有社群',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/community/add-enhanced',
        title: '创建群组',
        icon: 'Plus',
        description: '创建新的微信群组',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
        protected: true // 🔒 标记为受保护的核心功能
      },
      {
        path: '/orders/list',
        title: '订单管理',
        icon: 'ShoppingCart',
        description: '查看和管理订单',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/orders/analytics',
        title: '订单分析',
        icon: 'DataAnalysis',
        description: '订单数据统计分析',
        roles: ['admin', 'substation', 'agent']
      }
    ]
  },

  // 分销管理 - 分销相关功能
  distribution: {
    title: '分销管理',
    icon: 'Share',
    order: 3,
    description: '分销网络和推广管理',
    children: [
      {
        path: '/agent/list',
        title: '代理商管理',
        icon: 'Avatar',
        description: '管理代理商',
        roles: ['admin', 'substation']
      },
      {
        path: '/distribution/distributors',
        title: '分销员管理',
        icon: 'User',
        description: '管理分销员',
        roles: ['admin', 'substation', 'agent']
      },
      {
        path: '/substation/list',
        title: '分站管理',
        icon: 'OfficeBuilding',
        description: '管理平台分站，配置分站权限',
        roles: ['admin']
      },
      {
        path: '/promotion/links',
        title: '推广链接',
        icon: 'Link',
        description: '管理推广链接',
        roles: ['admin', 'substation', 'agent', 'distributor']
      }
    ]
  },

  // 财务管理 - 财务相关功能
  finance: {
    title: '财务管理',
    icon: 'Money',
    order: 4,
    description: '财务数据和佣金管理',
    children: [
      {
        path: '/finance/dashboard',
        title: '财务总览',
        icon: 'DataBoard',
        description: '查看财务数据',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner']
      },
      {
        path: '/finance/commission-logs',
        title: '佣金记录',
        icon: 'Medal',
        description: '查看佣金记录',
        roles: ['admin', 'substation', 'agent', 'distributor']
      },
      {
        path: '/finance/withdraw',
        title: '提现管理',
        icon: 'Upload',
        description: '处理提现申请和记录',
        roles: ['admin', 'substation', 'agent', 'distributor']
      },
      {
        path: '/finance/transactions',
        title: '交易记录',
        icon: 'List',
        description: '查看所有交易记录',
        roles: ['admin', 'substation', 'agent']
      }
    ]
  },

  // 用户权限 - 用户和权限管理
  userPermission: {
    title: '用户权限',
    icon: 'User',
    order: 5,
    description: '用户管理和权限控制',
    children: [
      {
        path: '/user/list',
        title: '用户管理',
        icon: 'UserFilled',
        description: '管理系统用户',
        roles: ['admin', 'substation']
      },
      {
        path: '/permission/roles',
        title: '角色管理',
        icon: 'Avatar',
        description: '管理用户角色和权限',
        roles: ['admin', 'substation']
      }
    ]
  },

  // 系统配置 - 系统设置和业务配置
  systemConfig: {
    title: '系统配置',
    icon: 'Setting',
    order: 6,
    description: '系统设置和业务配置',
    children: [
      {
        path: '/system/settings',
        title: '基础设置',
        icon: 'Tools',
        description: '系统基础配置',
        roles: ['admin', 'substation']
      },
      {
        path: '/payment',
        title: '支付设置',
        icon: 'CreditCard',
        description: '支付渠道配置和参数设置',
        roles: ['admin', 'substation']
      },
      {
        path: '/anti-block',
        title: '防红配置',
        icon: 'Shield',
        description: '防封系统配置和域名管理',
        roles: ['admin', 'substation']
      }
    ]
  },

  // 系统监控 - 日志和安全管理
  systemMonitor: {
    title: '系统监控',
    icon: 'Monitor',
    order: 7,
    description: '系统监控和安全管理',
    children: [
      {
        path: '/system/operation-logs',
        title: '操作日志',
        icon: 'Document',
        description: '查看系统操作日志',
        roles: ['admin', 'substation']
      },
      {
        path: '/security/management',
        title: '安全管理',
        icon: 'Lock',
        description: '系统安全设置和监控',
        roles: ['admin']
      }
    ]
  },

  // 个人中心 - 所有角色都可访问
  profile: {
    title: '个人中心',
    icon: 'User',
    order: 6,
    description: '个人信息和设置',
    children: [
      {
        path: '/user/center',
        title: '个人资料',
        icon: 'User',
        description: '查看和编辑个人信息',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      },
      {
        path: '/user/profile',
        title: '账户设置',
        icon: 'Setting',
        description: '账户相关设置',
        roles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
      }
    ]
  }
}

// 角色专属快速操作（简化版）
export const simpleQuickActions = {
  admin: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '分站管理', path: '/substation/list', icon: 'OfficeBuilding', color: '#67c23a' },
    { title: '支付设置', path: '/payment', icon: 'CreditCard', color: '#e6a23c' },
    { title: '防红配置', path: '/anti-block', icon: 'Shield', color: '#f56c6c' }
  ],
  substation: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '支付设置', path: '/payment', icon: 'CreditCard', color: '#67c23a' },
    { title: '防红配置', path: '/anti-block', icon: 'Shield', color: '#e6a23c' },
    { title: '代理商管理', path: '/agent/list', icon: 'Avatar', color: '#f56c6c' }
  ],
  agent: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '分销员管理', path: '/distribution/distributors', icon: 'User', color: '#67c23a' },
    { title: '佣金记录', path: '/finance/commission-logs', icon: 'Money', color: '#e6a23c' },
    { title: '推广链接', path: '/promotion/links', icon: 'Link', color: '#f56c6c' }
  ],
  distributor: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '推广链接', path: '/promotion/links', icon: 'Link', color: '#67c23a' },
    { title: '佣金记录', path: '/finance/commission-logs', icon: 'Money', color: '#e6a23c' },
    { title: '我的群组', path: '/community/groups', icon: 'Comment', color: '#f56c6c' }
  ],
  group_owner: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '我的群组', path: '/community/groups', icon: 'Comment', color: '#67c23a' },
    { title: '订单管理', path: '/orders/list', icon: 'ShoppingCart', color: '#e6a23c' },
    { title: '财务数据', path: '/finance/dashboard', icon: 'Money', color: '#f56c6c' }
  ],
  user: [
    { title: '创建群组', path: '/community/add-enhanced', icon: 'Plus', color: '#409eff' },
    { title: '我的订单', path: '/orders/list', icon: 'ShoppingCart', color: '#67c23a' },
    { title: '个人中心', path: '/user/center', icon: 'User', color: '#e6a23c' }
  ]
}

/**
 * 根据用户角色获取可见的简化导航分组
 * @param {string} userRole - 用户角色
 * @returns {Object} 过滤后的导航分组
 */
export function getSimpleVisibleNavigationGroups(userRole) {
  console.log('🧪 简化导航 - 获取角色导航:', userRole)
  
  if (!userRole) {
    console.log('⚠️ 简化导航 - 用户角色为空')
    return {}
  }

  const visibleGroups = {}
  
  Object.entries(simpleNavigationGroups).forEach(([groupKey, group]) => {
    console.log(`📁 简化导航 - 处理分组 ${groupKey}:`, group.title)
    
    // 过滤子菜单
    const filteredChildren = group.children.filter(child => {
      const hasPermission = !child.roles || child.roles.includes(userRole)
      console.log(`  📄 子菜单 ${child.title}: ${hasPermission ? '✅' : '❌'}`)
      return hasPermission
    })

    // 如果有可访问的子菜单，则包含这个分组
    if (filteredChildren.length > 0) {
      visibleGroups[groupKey] = {
        ...group,
        children: filteredChildren
      }
      console.log(`✅ 简化导航 - 分组 ${groupKey} 包含 ${filteredChildren.length} 个子菜单`)
    } else {
      console.log(`❌ 简化导航 - 分组 ${groupKey} 无可访问子菜单`)
    }
  })

  console.log('🎯 简化导航 - 最终可见分组:', Object.keys(visibleGroups))
  return visibleGroups
}

/**
 * 获取用户的简化快速操作
 * @param {string} userRole - 用户角色
 * @returns {Array} 快速操作列表
 */
export function getSimpleUserQuickActions(userRole) {
  return simpleQuickActions[userRole] || simpleQuickActions.user
}

/**
 * 检查用户是否可以访问指定路径
 * @param {string} path - 路径
 * @param {string} userRole - 用户角色
 * @returns {boolean}
 */
export function canAccessPath(path, userRole) {
  // 遍历所有分组和子菜单，检查权限
  for (const group of Object.values(simpleNavigationGroups)) {
    for (const child of group.children) {
      if (child.path === path) {
        return !child.roles || child.roles.includes(userRole)
      }
    }
  }
  
  // 如果没有找到对应的路径配置，默认允许访问
  return true
}

/**
 * 获取群组创建功能的所有访问入口
 * @param {string} userRole - 用户角色
 * @returns {Array} 访问入口列表
 */
export function getGroupCreationEntries(userRole) {
  const entries = []
  
  // 1. 导航菜单入口
  const businessGroup = simpleNavigationGroups.business
  const createGroupMenu = businessGroup.children.find(child => child.path === '/community/add-enhanced')
  if (createGroupMenu && (!createGroupMenu.roles || createGroupMenu.roles.includes(userRole))) {
    entries.push({
      type: 'navigation',
      title: '导航菜单 - 创建群组',
      path: createGroupMenu.path,
      location: '核心业务 > 创建群组'
    })
  }
  
  // 2. 快速操作入口
  const quickActions = getSimpleUserQuickActions(userRole)
  const createGroupAction = quickActions.find(action => action.path === '/community/add-enhanced')
  if (createGroupAction) {
    entries.push({
      type: 'quick_action',
      title: '快速操作 - 创建群组',
      path: createGroupAction.path,
      location: '快速操作面板'
    })
  }
  
  // 3. 工作台入口（如果实现了工作台）
  entries.push({
    type: 'workbench',
    title: '工作台 - 创建群组',
    path: '/community/add-enhanced',
    location: '个人工作台'
  })
  
  return entries
}

/**
 * 验证群组创建功能的完整性
 * @param {string} userRole - 用户角色
 * @returns {Object} 验证结果
 */
export function validateGroupCreationAccess(userRole) {
  const entries = getGroupCreationEntries(userRole)
  const canAccess = canAccessPath('/community/add-enhanced', userRole)
  
  return {
    userRole,
    canAccess,
    entryCount: entries.length,
    entries,
    isProtected: entries.some(entry => entry.type === 'navigation'),
    hasQuickAction: entries.some(entry => entry.type === 'quick_action'),
    hasWorkbenchAccess: entries.some(entry => entry.type === 'workbench'),
    validation: {
      passed: canAccess && entries.length >= 2, // 至少要有2个入口
      issues: []
    }
  }
}

// 导出验证函数，用于测试
export function runGroupCreationValidation() {
  const roles = ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user']
  const results = {}
  
  roles.forEach(role => {
    results[role] = validateGroupCreationAccess(role)
  })
  
  console.log('🔒 群组创建功能完整性验证结果:')
  console.table(Object.entries(results).map(([role, result]) => ({
    角色: role,
    可访问: result.canAccess ? '✅' : '❌',
    入口数量: result.entryCount,
    有导航入口: result.isProtected ? '✅' : '❌',
    有快速操作: result.hasQuickAction ? '✅' : '❌',
    验证通过: result.validation.passed ? '✅' : '❌'
  })))
  
  return results
}