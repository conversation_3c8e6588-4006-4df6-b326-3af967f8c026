<template>
  <el-drawer
    :model-value="visible"
    title="群组分析"
    size="60%"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="analytics-content">
      <!-- 基础统计 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ groupData.memberCount || 0 }}</div>
              <div class="stat-label">总成员数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ groupData.monthlyActiveUsers || 0 }}</div>
              <div class="stat-label">月活跃用户</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ groupData.dailyMessages || 0 }}</div>
              <div class="stat-label">日均消息数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ ((groupData.memberCount || 0) / (groupData.maxMembers || 500) * 100).toFixed(1) }}%</div>
              <div class="stat-label">群组满员率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 趋势图表 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">成员增长趋势</span>
            </template>
            <div class="chart-placeholder">
              <div class="placeholder-content">
                <el-icon size="48"><TrendCharts /></el-icon>
                <p>成员增长趋势图</p>
                <p class="placeholder-note">图表组件加载中...</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">活跃度分析</span>
            </template>
            <div class="chart-placeholder">
              <div class="placeholder-content">
                <el-icon size="48"><TrendCharts /></el-icon>
                <p>活跃度分析图</p>
                <p class="placeholder-note">图表组件加载中...</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-card style="margin-top: 20px;" shadow="never">
        <template #header>
          <span>详细数据</span>
        </template>
        <el-table :data="analyticsData" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="newMembers" label="新增成员" width="100" />
          <el-table-column prop="activeMembers" label="活跃成员" width="100" />
          <el-table-column prop="messages" label="消息数" width="100" />
          <el-table-column prop="engagement" label="参与度" width="100">
            <template #default="{ row }">
              {{ row.engagement }}%
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { TrendCharts } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: [Number, String],
    required: true
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 模拟分析数据
const analyticsData = ref([
  { date: '2024-01-01', newMembers: 12, activeMembers: 156, messages: 234, engagement: 78.5 },
  { date: '2024-01-02', newMembers: 8, activeMembers: 162, messages: 198, engagement: 82.1 },
  { date: '2024-01-03', newMembers: 15, activeMembers: 171, messages: 267, engagement: 85.3 },
  { date: '2024-01-04', newMembers: 6, activeMembers: 168, messages: 189, engagement: 79.8 },
  { date: '2024-01-05', newMembers: 11, activeMembers: 175, messages: 245, engagement: 88.2 }
])

const handleClose = () => {
  emit('update:visible', false)
}

watch(() => props.visible, (val) => {
  if (val) {
    console.log('群组分析打开，群组ID:', props.groupId)
    // 这里可以加载真实的分析数据
  }
})
</script>

<style lang="scss" scoped>
.analytics-content {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  
  .stat-item {
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #606266;
    }
  }
}

.chart-card {
  .chart-title {
    font-weight: 500;
    color: #303133;
  }
}

.chart-placeholder {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 4px;
  
  .placeholder-content {
    text-align: center;
    color: #909399;
    
    p {
      margin: 8px 0;
    }
    
    .placeholder-note {
      font-size: 12px;
      color: #c0c4cc;
    }
  }
}
</style>
