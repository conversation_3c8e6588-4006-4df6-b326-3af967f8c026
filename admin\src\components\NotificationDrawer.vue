<template>
  <el-drawer
    v-model="visible"
    title="通知中心"
    direction="rtl"
    size="400px"
    :before-close="handleClose"
  >
    <div class="notification-content">
      <div class="notification-header">
        <h3>最新通知</h3>
        <el-button text @click="markAllRead">全部已读</el-button>
      </div>
      
      <div class="notification-list">
        <div v-for="item in notifications" :key="item.id" class="notification-item">
          <div class="notification-icon">
            <el-icon><Bell /></el-icon>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ item.title }}</div>
            <div class="notification-message">{{ item.message }}</div>
            <div class="notification-time">{{ formatTime(item.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Bell } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const notifications = ref([
  {
    id: 1,
    title: '系统通知',
    message: '系统将于今晚进行维护升级',
    time: new Date(),
    read: false
  },
  {
    id: 2,
    title: '订单提醒',
    message: '您有新的订单需要处理',
    time: new Date(Date.now() - 3600000),
    read: false
  },
  {
    id: 3,
    title: '安全提醒',
    message: '检测到异常登录，请注意账户安全',
    time: new Date(Date.now() - 7200000),
    read: true
  }
])

const handleClose = () => {
  visible.value = false
}

const markAllRead = () => {
  notifications.value.forEach(item => {
    item.read = true
  })
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}
</script>

<style scoped>
.notification-content {
  padding: 20px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notification-header h3 {
  margin: 0;
  color: #1f2937;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notification-item {
  display: flex;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.notification-icon {
  margin-right: 12px;
  color: #3b82f6;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.notification-message {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.notification-time {
  color: #9ca3af;
  font-size: 12px;
}
</style>