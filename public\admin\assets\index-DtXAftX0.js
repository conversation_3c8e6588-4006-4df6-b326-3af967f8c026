const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-DME2sbOQ.js","assets/vue-vendor-Dy164gUc.js","assets/element-plus-h2SQQM64.js","assets/utils-D1VZuEZr.js","assets/Login-rSoNIJ5o.css","assets/DataScreenFullscreen-k4Mj_VsY.js","assets/DataScreen-DhgDTN83.js","assets/LineChart-CydsJ2U8.js","assets/LineChart-OVmPlT36.css","assets/DoughnutChart-CCHHIMjz.js","assets/DoughnutChart-BIJ7x2iS.css","assets/echarts-D68jitv0.js","assets/DataScreen-CyXPUa6t.css","assets/DataScreenFullscreen-DClPWAwn.css","assets/TestPage-CWqo3qky.js","assets/TestPage-DGSIHe7Z.css","assets/TestPreview-fptg9nyv.js","assets/TestPreview-BvIb6TDM.css","assets/ModernDashboard-C2luKjm5.js","assets/dataPermission-K5RJ0Kz8.js","assets/ModernDashboard-DDe2NHmS.css","assets/el-progress-Dw9yTa91.css","assets/el-tag-DljBBxJR.css","assets/el-select-CvzM3W2w.css","assets/el-card-fwQOLwdi.css","assets/el-col-Ds2mGN2S.css","assets/el-alert-G57rL0jl.css","assets/RouteChecker-BEmmehKW.js","assets/RouteChecker-D0WcVVkE.css","assets/el-tab-pane-DTGC0oAx.css","assets/el-table-column-CKoPG0Y8.css","assets/el-checkbox-DIj50LEB.css","assets/TestNavigationSystem-Dm1PGOpS.js","assets/TestNavigationSystem-BAIyMPlo.css","assets/el-radio-group-BzMpJalG.css","assets/el-radio-button-CSkroacn.css","assets/el-descriptions-item-o9ObloqJ.css","assets/SecurityManagement-CGdTK9uD.js","assets/PageLayout-C6qH3ReN.js","assets/PageLayout-DahFmBjs.css","assets/SecurityManagement-Bl5ckS82.css","assets/el-loading-DLSpKYce.css","assets/el-pagination-BNQcHhjS.css","assets/el-checkbox-group-D_6SYB2i.css","assets/el-form-item-DCFsf57O.css","assets/el-switch-B5lTGWdM.css","assets/el-input-number-DUUPPWGj.css","assets/Reports-DsJJSTOu.js","assets/Reports-BWfI56SW.css","assets/el-empty-D4ZqTl4F.css","assets/el-date-picker-Db-ufUiu.css","assets/GroupList-BbuRdOiN.js","assets/community-DNWNbya4.js","assets/chunk-KZPPZA2C-BZQYgWVq.js","assets/RichTextEditor-Cp69n7mq.js","assets/RichTextEditor-DLPamswZ.css","assets/GroupCreateForm-CUdmzNwa.js","assets/GroupCreateForm-C7RORJ8e.css","assets/el-upload-q8uObtwj.css","assets/el-radio-BuDgLcOG.css","assets/UserProfile-D2NIrp8S.js","assets/format-3eU4VJ9V.js","assets/UserProfile-g2JeI23d.css","assets/el-timeline-item-BvbJTz1y.css","assets/ImageUpload-B-U8MD1C.js","assets/ImageUpload-BNDQ9u7E.css","assets/GroupList-B00ieJD1.css","assets/el-collapse-item-BqS7tZDP.css","assets/AutoRules-CWt1k6gy.js","assets/AutoRules-BsKmJ0KW.css","assets/EventManagement-DQLXH03-.js","assets/EventManagement-CRdY7go3.css","assets/ContentModeration-CU-oCkcY.js","assets/ContentModeration-DirNfgnc.css","assets/AnalyticsDashboard-BvRdOuXV.js","assets/AnalyticsDashboard-DU4O3ZOQ.css","assets/TemplateManagement-BTfEDsf-.js","assets/TemplateManagement-jzrs_Ea7.css","assets/el-text-3XkjT9nK.css","assets/GroupAdd-B_U7JokZ.js","assets/GroupAdd-DiLk6Bx5.css","assets/GroupDetail-COgWOPNL.js","assets/GroupDetail-BgYVefpA.css","assets/GroupMarketing-BBbU3HBr.js","assets/GroupMarketing-Bqn6qKUk.css","assets/DistributorList-j8gR_afM.js","assets/index-ByaD-6N-.js","assets/index-BJ8EPzFa.css","assets/DistributorList-kPRLj1LG.css","assets/DistributorDetail-COlSGvZy.js","assets/export-BIRLwzxN.js","assets/DistributorDetail-clmkuS2f.css","assets/CustomerManagement-DwUSC7Jy.js","assets/StatCard-u_ssO_Ky.js","assets/StatCard-DduPrYa0.css","assets/CustomerManagement-ZcoHEbfI.css","assets/DistributorDashboard-BgBE05hj.js","assets/DistributorDashboard-B4MKn6W_.css","assets/GroupManagement-CAyMWdI1.js","assets/GroupManagement-DBz5lfWH.css","assets/PromotionLinks-B6Udr01T.js","assets/browser-DJkR4j8n.js","assets/PromotionLinks-EJvbIL5R.css","assets/el-color-picker-DOhQXICb.css","assets/el-slider-DtISwLyR.css","assets/CommissionLogs-Db3gssb8.js","assets/CommissionLogs-DoBL2dkA.css","assets/el-link-B58a4a3I.css","assets/OwnerDashboard-DjREXJvn.js","assets/OwnerDashboard-ClLN1HOK.css","assets/FinanceDashboard-TY2uYZgR.js","assets/finance-DBah1Ldq.js","assets/FinanceDashboard-B9JuvMSB.css","assets/CommissionLog-DJp26Fcb.js","assets/CommissionLog-CZOsge_q.css","assets/TransactionList-lZ25UXGY.js","assets/TransactionList-ucktdfU6.css","assets/WithdrawManage-DzYCQgKD.js","assets/WithdrawManage-CKkTsuI2.css","assets/UserCenter-CRdrJPs1.js","assets/AvatarUpload-Kj8d-M_w.js","assets/AvatarUpload-CBsjY5bz.css","assets/user-CJhH85FQ.js","assets/UserCenter-BjMbhTPN.css","assets/UserList-DrGy0qXH.js","assets/UserList-GVax6hjZ.css","assets/Profile-BafzYgsS.js","assets/Profile-BCJjq1Q7.css","assets/UserAnalytics-D0myNp49.js","assets/UserAnalytics-DcrgRIrd.css","assets/UserAdd-8HTfxXmt.js","assets/UserAdd-CbaRtsZo.css","assets/SubstationList-DY8d-49p.js","assets/substation-C0LtbWrR.js","assets/SubstationList-DmpJiNG1.css","assets/SubstationFinance-CTst0Re9.js","assets/SubstationFinance-CpwpKSs-.css","assets/SubstationAnalytics-BQ7nSvwF.js","assets/SubstationAnalytics-BfoEr8Yi.css","assets/AgentDashboard-beFXBMxJ.js","assets/agent-BTWzqVJ0.js","assets/AgentDashboard-D5SIhOOb.css","assets/AgentList-BQ8fvjk9.js","assets/AgentList-PQ-LlCtf.css","assets/AgentApplications-CPaWnJt1.js","assets/AgentApplications-BWOZLAsg.css","assets/AgentHierarchy-5PGpY888.js","assets/AgentHierarchy-BBcsk2pC.css","assets/el-tree-C2sTlbKd.css","assets/AgentPerformance-B-YH0X2Y.js","assets/AgentPerformance-DvqYonSd.css","assets/Dashboard-BXPz0Lwi.js","assets/anti-block-CmiVNzQG.js","assets/Dashboard-D23GJ8pj.css","assets/EnhancedDashboard-BMvFXyI9.js","assets/EnhancedDashboard-BJd09IXY.css","assets/DomainList-CHC0CEE5.js","assets/DomainList-g-IIA-LC.css","assets/ShortLinkList-BEKG29v-.js","assets/ShortLinkList-CtVaR-6d.css","assets/Analytics-Co9tHakN.js","assets/Analytics-W_bWcuzF.css","assets/RoleManagement-DL_jeR3P.js","assets/RoleManagement-BfZu-UsL.css","assets/PermissionManagement-C8MSMQ5l.js","assets/PermissionManagement-N7Br9Fr1.css","assets/LinkManagement-Da1w7byV.js","assets/promotion-DjIFk3EX.js","assets/LinkManagement-DOudxbf9.css","assets/LandingPages-HgxUj6Ac.js","assets/LandingPages-CZ9hcL7r.css","assets/Analytics-CWzSItOu.js","assets/Analytics-BZLK6Iji.css","assets/OrderList-lH8zHgmD.js","assets/OrderList-Owwndhdh.css","assets/OrderAnalytics-JJLEbY_q.js","assets/OrderAnalytics-BLQIG9Tf.css","assets/OrderDetail-DBHrLlIK.js","assets/OrderDetail-B_Ybh9fJ.css","assets/Settings-TS2r7zta.js","assets/Settings-nOs0s1jF.css","assets/el-time-picker-B4D4rMOz.css","assets/DataExport-lcVYex3I.js","assets/DataExport-Bvx1OpEI.css","assets/Notifications-CkmmtTak.js","assets/Notifications-DZ_bUbQR.css","assets/OperationLogs-CMX9wsE0.js","assets/OperationLogs-DAst9zpi.css","assets/FunctionTest-CwLNuy48.js","assets/FunctionTest-A4X9tfUv.css","assets/UserGuide-pa1A2KqB.js","assets/UserGuide-CKL1aRCv.css","assets/FileManagement-DlQl42nE.js","assets/FileManagement-DRIuEPz8.css","assets/PaymentSettings-Bn2FAfcI.js","assets/payment-BistKFiU.js","assets/PaymentSettings-BaPKRisT.css","assets/PaymentOrders-CYBaaLwO.js","assets/PaymentOrders-O3f0dFDV.css","assets/PaymentRefunds--OptUzKN.js","assets/PaymentRefunds-BNuT-4jI.css","assets/ErrorPage-KpwDoSeT.js","assets/ErrorPage-Da804C4y.css"])))=>i.map(i=>d[i]);
import{a6 as e,k as t,l as o,E as n,ae as i,r as a,c as s,af as r,y as l,z as c,t as d,B as u,C as m,T as p,F as h,Y as _,e as g,H as f,u as b,D as v,ag as y,d as k,ah as w,ai as E,aj as D,ak as L,ac as A,al as P}from"./vue-vendor-Dy164gUc.js";import{a as S,b as T,N as I}from"./utils-D1VZuEZr.js";import{Q as C,R as O,S as R,o as M,T as V,U as j,V as U,W as x,X as B,Y as $,Z as q,_ as F,$ as G,a0 as N,a1 as W,a2 as K,a3 as z,a4 as H,a5 as J,a6 as Q,a7 as X,a8 as Y,a9 as Z,aa as ee,ab as te,ac as oe,ad as ne,ae as ie,af as ae,ag as se,ah as re,ai as le,aj as ce,ak as de,al as ue,am as me,an as pe,ao as he,ap as _e,aq as ge,ar as fe,as as be,at as ve,p as ye,au as ke,av as we,aw as Ee,ax as De,ay as Le,az as Ae,aA as Pe,aB as Se,aC as Te,aD as Ie,aE as Ce,aF as Oe,aG as Re,aH as Me,aI as Ve,aJ as je,aK as Ue,aL as xe,aM as Be,aN as $e,aO as qe,aP as Fe,aQ as Ge,aR as Ne,aS as We,aT as Ke,aU as ze,aV as He,aW as Je}from"./element-plus-h2SQQM64.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Qe={},Xe=function(e,t,o){let n=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),o=e?.nonce||e?.getAttribute("nonce");n=Promise.allSettled(t.map(e=>{if((e=function(e){return"/admin/"+e}(e))in Qe)return;Qe[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script"),i.crossOrigin="",i.href=e,o&&i.setAttribute("nonce",o),document.head.appendChild(i),t?new Promise((t,o)=>{i.addEventListener("load",t),i.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then(t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)})},Ye={id:"app"},Ze={__name:"App",setup:i=>(console.log("晨鑫流量变现系统 管理后台启动"),(i,a)=>{const s=e("router-view");return o(),t("div",Ye,[n(s)])})},et="Admin-Token";function tt(){return S.get(et)}function ot(e){return S.set(et,e,{expires:7})}function nt(){S.remove(et),S.remove("Admin-Refresh-Token")}const it=T.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});it.interceptors.request.use(e=>{const t=tt();return t&&(e.headers.Authorization=`Bearer ${t}`),"get"===e.method&&(e.params={...e.params,_t:Date.now()}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),it.interceptors.response.use(e=>{const t=e.data;return 200!==t.code&&0!==t.code?(C({message:t.message||"请求失败",type:"error",duration:5e3}),50008!==t.code&&50012!==t.code&&50014!==t.code||O.confirm("你已被登出，可以取消继续留在该页面，或者重新登录","确定登出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{nt(),location.reload()}),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{console.error("响应拦截器错误:",e);let t="请求失败";if(e.response){const{status:o,data:n}=e.response;switch(o){case 400:t=n.message||"请求参数错误";break;case 401:t="未授权，请重新登录",nt(),an.push("/login");break;case 403:t="拒绝访问";break;case 404:t="请求地址出错";break;case 408:t="请求超时";break;case 500:t="服务器内部错误";break;case 501:t="服务未实现";break;case 502:t="网关错误";break;case 503:t="服务不可用";break;case 504:t="网关超时";break;case 505:t="HTTP版本不受支持";break;default:t=n.message||`连接错误${o}`}}else t=e.request?"网络连接异常，请检查网络":e.message||"请求配置错误";return C({message:t,type:"error",duration:5e3}),Promise.reject(e)});const at=()=>it({url:"/admin/auth/user",method:"get"}),st=e=>it({url:"/admin/auth/profile",method:"put",data:e}),rt=e=>it({url:"/admin/auth/password",method:"put",data:e}),lt=i("user",()=>{const e=a(tt()),t=a(null),o=s(()=>t.value?.roles||[]),n=s(()=>t.value?.nickname||""),i=s(()=>t.value?.avatar||""),r=s(()=>t.value?.role||""),l=s(()=>"admin"===r.value),c=s(()=>"substation"===r.value),d=s(()=>"agent"===r.value),u=s(()=>"distributor"===r.value),m=s(()=>"group_owner"===r.value),p=s(()=>"user"===r.value),h=s(()=>{try{const e=localStorage.getItem("sessionInfo");return e?JSON.parse(e):null}catch{return null}}),_=s(()=>{if(!e.value||!h.value)return!1;const t=new Date(h.value.loginTime);return new Date-t<864e5}),g=()=>{window.autoLogoutTimer&&clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=setTimeout(()=>{f()},864e5)},f=async()=>{try{await it({url:"/admin/auth/logout",method:"post"})}catch(e){}finally{b(),t.value=null,localStorage.removeItem("sessionInfo"),window.autoLogoutTimer&&(clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=null)}},b=()=>{nt(),e.value=""};return{token:e,userInfo:t,roles:o,nickname:n,avatar:i,userRole:r,sessionInfo:h,isSessionValid:_,isAdmin:l,isSubstation:c,isAgent:d,isDistributor:u,isGroupOwner:m,isUser:p,login:async o=>{try{const n=await(e=>it({url:"/admin/auth/login",method:"post",data:e}))(o),i=n.data;if(i.success){ot(i.data.token),e.value=i.data.token,t.value=i.data.user;const o={loginTime:(new Date).toISOString(),userAgent:navigator.userAgent,role:i.data.user.role,userId:i.data.user.id,username:i.data.user.username};return localStorage.setItem("sessionInfo",JSON.stringify(o)),g(),i}throw new Error(i.message||"登录失败")}catch(n){if(console.error("登录失败:",n),n.response){const e=n.response.status,t=n.response.data?.message||n.message;switch(e){case 401:throw new Error("用户名或密码错误");case 403:throw new Error("账户已被禁用或权限不足");case 429:throw new Error("登录尝试过于频繁，请稍后再试");case 500:throw new Error("服务器内部错误，请稍后重试");default:throw new Error(t||"登录失败")}}throw n}},getUserInfo:async()=>{try{const e=(await at()).data;if(e.success)return t.value=e.data.user,e;throw new Error(e.message||"获取用户信息失败")}catch(e){throw console.error("获取用户信息失败:",e),e}},logout:f,resetToken:b,setToken:t=>{ot(t),e.value=t},setUserInfo:e=>{t.value=e},setupAutoLogout:g,hasPermission:e=>{if(!t.value)return!1;if(l.value)return!0;return({substation:["user_management","agent_management","order_management","group_management","finance_view"],agent:["team_management","commission_view","performance_view","application_management"],distributor:["customer_management","group_management","promotion_management","commission_view"],group_owner:["group_management","content_management","template_management"],user:["profile_management","order_view"]}[r.value]||[]).includes(e)},hasRouteAccess:e=>{if(!t.value)return!1;if(l.value)return!0;try{const{checkMenuPermission:t}=require("@/config/navigation");return t({path:e},r.value)}catch{return!1}},getUserDefaultRoute:()=>{try{const{getUserDefaultRoute:e}=require("@/config/navigation");return e(r.value)}catch{return"/dashboard"}},enterPreviewMode:()=>{const o="preview-mode-token-"+Date.now();ot(o),e.value=o,t.value={id:1,username:"admin",nickname:"超级管理员 (预览)",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]},console.log("🎭 预览模式已激活，用户信息已设置")}}}),ct=Object.freeze(Object.defineProperty({__proto__:null,useUserStore:lt},Symbol.toStringTag,{value:"Module"})),dt={admin:{level:0,name:"超级管理员",canViewRoles:["admin","substation","agent","distributor","group_owner","user"],dataScope:"all",dashboardScope:"global",financeScope:"all_finance",groupCreatePermission:!0,dataExportPermission:!0},substation:{level:1,name:"分站管理员",canViewRoles:["substation","agent","distributor","group_owner","user"],dataScope:"substation_and_below",dashboardScope:"substation",financeScope:"substation_finance",groupCreatePermission:!0,dataExportPermission:!0},agent:{level:2,name:"代理商",canViewRoles:["agent","distributor","group_owner","user"],dataScope:"agent_and_below",dashboardScope:"agent_team",financeScope:"agent_commission",groupCreatePermission:!0,dataExportPermission:!1},distributor:{level:3,name:"分销员",canViewRoles:["distributor","group_owner","user"],dataScope:"distributor_and_below",dashboardScope:"distributor_personal",financeScope:"distributor_commission",groupCreatePermission:!0,dataExportPermission:!1},group_owner:{level:4,name:"群主",canViewRoles:["group_owner","user"],dataScope:"group_owner_and_below",dashboardScope:"group_owner_groups",financeScope:"group_owner_income",groupCreatePermission:!0,dataExportPermission:!1},user:{level:5,name:"普通用户",canViewRoles:["user"],dataScope:"self_only",dashboardScope:"user_personal",financeScope:"user_consumption",groupCreatePermission:!0,dataExportPermission:!1}};function ut(e,t){const o=dt[e];return!!o&&o.canViewRoles.includes(t)}const mt={admin:{allowedRoutes:["*"],defaultRoute:"/dashboard",workbench:"/dashboard"},substation:{allowedRoutes:["/dashboard","/user","/community","/orders","/finance","/agent","/substation","/distribution/distributors","/promotion"],defaultRoute:"/dashboard",workbench:"/dashboard"},agent:{allowedRoutes:["/agent/dashboard","/agent/list","/agent/applications","/agent/commission","/agent/hierarchy","/agent/performance","/user/center","/user/profile","/finance/commission-logs","/promotion/links"],defaultRoute:"/agent/dashboard",workbench:"/agent/dashboard"},distributor:{allowedRoutes:["/distributor/dashboard","/distribution/customers","/community/groups","/orders/list","/finance/commission-logs","/promotion/links","/user/center","/user/profile"],defaultRoute:"/distributor/dashboard",workbench:"/distributor/dashboard"},group_owner:{allowedRoutes:["/owner/dashboard","/community/groups","/content/management","/content/templates","/user/center","/user/profile","/orders/list"],defaultRoute:"/owner/dashboard",workbench:"/owner/dashboard"},user:{allowedRoutes:["/user/center","/user/profile","/orders/my","/community/my-groups"],defaultRoute:"/user/center",workbench:"/user/center"}};function pt(e,t){if(!t||!e)return!1;const o=mt[t];if(!o)return!1;if(o.allowedRoutes.includes("*"))return!0;let n;if("string"==typeof e)n=e;else if(e&&"string"==typeof e.path)n=e.path;else{if(!e||"string"!=typeof e.name)return!1;n=`/${e.name.toLowerCase()}`}return"string"==typeof n&&o.allowedRoutes.some(e=>e===n||!!n.startsWith(e+"/"))}function ht(e,t){return t&&e?e.filter(e=>!e.meta?.hidden&&(!!pt(e,t)&&!(e.children&&e.children.length>0&&(e.children=ht(e.children,t),0===e.children.length&&e.redirect)))):[]}const _t={admin:"超级管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"};function gt(e){return _t[e]||"未知角色"}const ft=Object.freeze(Object.defineProperty({__proto__:null,canViewUserData:ut,checkMenuPermission:pt,filterRoutesByRole:ht,getRoleDisplayName:gt,getUserDefaultRoute:function(e){const t=mt[e];return t?.defaultRoute||"/user/center"},roleDisplayNames:_t,roleHierarchy:dt,roleNavigationConfig:mt},Symbol.toStringTag,{value:"Module"})),bt=(e,t)=>{const o=e.__vccOpts||e;for(const[n,i]of t)o[n]=i;return o},vt={class:"enhanced-menu-item"},yt={class:"menu-item-content"},kt={key:1,class:"default-icon"},wt={key:0,class:"menu-title"},Et={key:0,class:"menu-badge"},Dt={key:1,class:"menu-shortcut"},Lt={key:2,class:"notification-dot"},At={class:"menu-item-content"},Pt={key:1,class:"default-icon"},St={key:0,class:"menu-title"},Tt={key:0,class:"menu-badge"},It={key:1,class:"notification-dot"},Ct={__name:"ModernMenuItem",props:{item:{type:Object,required:!0},basePath:{type:String,default:""},collapsed:{type:Boolean,default:!1}},setup(i){const a=i,g=r();lt();const f=s(()=>a.item.children&&a.item.children.length>0),b=s(()=>a.item.path.startsWith("/")?a.item.path:`${a.basePath}/${a.item.path}`.replace(/\/+/g,"/")),v=()=>{a.item.meta?.external?window.open(a.item.path,"_blank"):g.push(b.value)},y=()=>({Monitor:"dashboard-icon",DataLine:"datascreen-icon",User:"user-icon",UserFilled:"user-icon",Comment:"community-icon",Money:"finance-icon",Tickets:"order-icon",Setting:"system-icon",DataAnalysis:"analytics-icon",Connection:"network-icon",Document:"document-icon",Lock:"security-icon"}[a.item.meta?.icon]||"default-icon"),k=()=>({Monitor:fe,DataLine:ge,Comment:_e,User:he,UserFilled:pe,Document:me,Share:ue,Grid:de,Avatar:ce,Money:le,List:re,OfficeBuilding:se,Lock:ae,Connection:ie,TrendCharts:Q,Tools:ne,Edit:oe,DocumentCopy:te,MagicStick:ee,Key:Z,Link:Y,Tickets:X,DataAnalysis:Q,Upload:J,Download:H,Goods:z,Medal:K,Cpu:W,Setting:N,InfoFilled:G,CreditCard:F,Folder:q,View:$,Bell:B}[a.item.meta?.icon]||fe),w=()=>["/system/notifications","/orders/list","/user/list"].includes(a.item.path);return(a,s)=>{const r=V,g=R,E=U,D=e("ModernMenuItem",!0),L=x;return o(),t("div",vt,[f.value?(o(),l(E,{key:1,content:i.item.meta?.title,placement:"right",disabled:!i.collapsed,"show-after":500},{default:c(()=>[n(L,{index:b.value,class:"menu-item-group"},{title:c(()=>[d("div",At,[d("div",{class:M(["menu-icon",y()])},[i.item.meta?.icon?(o(),l(r,{key:0},{default:c(()=>[(o(),l(m(k())))]),_:1})):(o(),t("div",Pt,s[3]||(s[3]=[d("div",{class:"icon-dot"},null,-1)])))],2),n(p,{name:"menu-text"},{default:c(()=>[i.collapsed?u("",!0):(o(),t("span",St,j(i.item.meta?.title),1))]),_:1}),i.item.meta?.badge&&!i.collapsed?(o(),t("div",Tt,j(i.item.meta.badge),1)):u("",!0),w()&&!i.collapsed?(o(),t("div",It)):u("",!0)])]),default:c(()=>[(o(!0),t(h,null,_(i.item.children,e=>(o(),l(D,{key:e.path,item:e,"base-path":b.value,collapsed:i.collapsed,class:"submenu-item"},null,8,["item","base-path","collapsed"]))),128))]),_:1},8,["index"])]),_:1},8,["content","disabled"])):(o(),l(E,{key:0,content:i.item.meta?.title,placement:"right",disabled:!i.collapsed,"show-after":500},{default:c(()=>[n(g,{index:b.value,class:"menu-item-single",onClick:v},{default:c(()=>[d("div",yt,[d("div",{class:M(["menu-icon",y()])},[i.item.meta?.icon?(o(),l(r,{key:0},{default:c(()=>[(o(),l(m(k())))]),_:1})):(o(),t("div",kt,s[0]||(s[0]=[d("div",{class:"icon-dot"},null,-1)])))],2),n(p,{name:"menu-text"},{default:c(()=>[i.collapsed?u("",!0):(o(),t("span",wt,j(i.item.meta?.title),1))]),_:1}),i.item.meta?.badge&&!i.collapsed?(o(),t("div",Et,j(i.item.meta.badge),1)):u("",!0),i.item.meta?.shortcut&&!i.collapsed?(o(),t("div",Dt,j(i.item.meta.shortcut),1)):u("",!0),w()&&!i.collapsed?(o(),t("div",Lt)):u("",!0)]),s[1]||(s[1]=d("div",{class:"menu-item-bg"},null,-1)),s[2]||(s[2]=d("div",{class:"menu-item-indicator"},null,-1))]),_:1,__:[1,2]},8,["index"])]),_:1},8,["content","disabled"]))])}}},Ot=bt(Ct,[["__scopeId","data-v-154748a4"]]),Rt={dataCenter:{title:"数据中心",icon:"DataBoard",color:"#409eff",routes:["/dashboard","/data-screen"]},businessManagement:{title:"业务管理",icon:"Management",color:"#67c23a",routes:["/community","/orders","/content"]},distributionSystem:{title:"分销体系",icon:"Share",color:"#e6a23c",routes:["/distribution","/distributor","/agent"]},financeCenter:{title:"财务中心",icon:"Money",color:"#f56c6c",routes:["/finance","/payment","/withdrawal"]},userCenter:{title:"用户中心",icon:"User",color:"#909399",routes:["/user","/users"]},systemManagement:{title:"系统管理",icon:"Setting",color:"#606266",routes:["/system","/security","/logs"]}},Mt=[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff",description:"快速创建新的社群"},{title:"数据大屏",path:"/data-screen",icon:"DataLine",color:"#67c23a",description:"查看实时数据大屏"},{title:"用户管理",path:"/users/list",icon:"User",color:"#e6a23c",description:"管理系统用户"},{title:"财务总览",path:"/finance/overview",icon:"Money",color:"#f56c6c",description:"查看财务数据"}];const Vt=[{id:"dashboard",title:"数据看板",path:"/dashboard",icon:"Monitor",keywords:["数据","看板","统计","图表"]},{id:"data-screen",title:"数据大屏",path:"/data-screen",icon:"DataLine",keywords:["大屏","数据","可视化","监控"]},{id:"community",title:"社群管理",path:"/community",icon:"Comment",keywords:["社群","群组","管理","微信"]},{id:"community-add",title:"创建群组",path:"/community/add",icon:"Plus",keywords:["创建","新建","群组","社群"]},{id:"orders",title:"订单管理",path:"/orders",icon:"ShoppingCart",keywords:["订单","交易","支付","商品"]},{id:"users",title:"用户管理",path:"/users",icon:"User",keywords:["用户","会员","客户","管理"]},{id:"finance",title:"财务管理",path:"/finance",icon:"Money",keywords:["财务","佣金","收入","提现"]},{id:"distribution",title:"分销管理",path:"/distribution",icon:"Share",keywords:["分销","代理","推广","佣金"]}];const jt={class:"navigation-enhancer"},Ut={key:0,class:"quick-actions-panel"},xt={class:"panel-header"},Bt={class:"quick-actions-grid"},$t=["onClick"],qt={class:"action-icon"},Ft={class:"action-content"},Gt={class:"action-title"},Nt={class:"action-description"},Wt={key:1,class:"quick-actions-trigger"},Kt={__name:"NavigationEnhancer",props:{collapsed:{type:Boolean,default:!1}},setup(e){const i=r(),p=lt(),v=a(!1),y=s(()=>function(e){return{admin:Mt,substation:Mt.filter(e=>!e.path.includes("/system")),agent:Mt.filter(e=>["/community/add","/finance/overview"].includes(e.path)),distributor:Mt.filter(e=>["/community/add","/users/list"].includes(e.path)),group_owner:Mt.filter(e=>"/community/add"===e.path),user:[]}[e]||[]}(p.userInfo?.role||"user")),k={Plus:Ee,DataLine:ge,User:he,Money:le,Monitor:fe,Comment:_e,ShoppingCart:we,Share:ue},w=()=>{setTimeout(()=>{const e=document.querySelector(".sidebar-menu");if(!e)return;const t=e.querySelectorAll(".el-menu-item, .el-sub-menu");let o=null;t.forEach((e,t)=>{const n=e.getAttribute("index")||e.querySelector(".el-menu-item")?.getAttribute("index");if(!n)return;let i=null;for(const[o,a]of Object.entries(Rt))if(a.routes.some(e=>n.startsWith(e))){i=o;break}if(i&&i!==o){const t=Rt[i],n=document.createElement("div");n.className="nav-group-separator",n.innerHTML=`\n          <div class="group-line" style="background: linear-gradient(90deg, ${t.color}22, ${t.color}88, ${t.color}22)"></div>\n          <div class="group-label" style="color: ${t.color}">\n            <span class="group-title">${t.title}</span>\n          </div>\n        `,e.parentNode.insertBefore(n,e),o=i}})},100)};return g(()=>{w();const e=i.afterEach(()=>{document.querySelectorAll(".nav-group-separator").forEach(e=>e.remove()),w()});f(()=>{e()})}),(a,s)=>{const r=V,p=ve;return o(),t("div",jt,[!e.collapsed&&v.value?(o(),t("div",Ut,[d("div",xt,[s[2]||(s[2]=d("span",{class:"panel-title"},"快捷操作",-1)),n(p,{text:"",size:"small",onClick:s[0]||(s[0]=e=>v.value=!1),class:"close-btn"},{default:c(()=>[n(r,null,{default:c(()=>[n(b(be))]),_:1})]),_:1})]),d("div",Bt,[(o(!0),t(h,null,_(y.value,e=>(o(),t("div",{key:e.path,class:"quick-action-item",style:ye({"--action-color":e.color}),onClick:t=>(e=>{i.push(e.path),v.value=!1})(e)},[d("div",qt,[n(r,null,{default:c(()=>{return[(o(),l(m((t=e.icon,k[t]||fe))))];var t}),_:2},1024)]),d("div",Ft,[d("div",Gt,j(e.title),1),d("div",Nt,j(e.description),1)])],12,$t))),128))])])):u("",!0),e.collapsed||v.value?u("",!0):(o(),t("div",Wt,[n(p,{type:"primary",size:"small",onClick:s[1]||(s[1]=e=>v.value=!0),class:"trigger-btn"},{default:c(()=>[n(r,null,{default:c(()=>[n(b(ke))]),_:1}),s[3]||(s[3]=d("span",null,"快捷操作",-1))]),_:1,__:[3]})]))])}}},zt=bt(Kt,[["__scopeId","data-v-424527e1"]]),Ht={class:"notification-content"},Jt={class:"notification-header"},Qt={class:"notification-list"},Xt={class:"notification-icon"},Yt={class:"notification-content"},Zt={class:"notification-title"},eo={class:"notification-message"},to={class:"notification-time"},oo=bt({__name:"NotificationDrawer",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:i}){const r=e,u=i,m=s({get:()=>r.modelValue,set:e=>u("update:modelValue",e)}),p=a([{id:1,title:"系统通知",message:"系统将于今晚进行维护升级",time:new Date,read:!1},{id:2,title:"订单提醒",message:"您有新的订单需要处理",time:new Date(Date.now()-36e5),read:!1},{id:3,title:"安全提醒",message:"检测到异常登录，请注意账户安全",time:new Date(Date.now()-72e5),read:!0}]),g=()=>{m.value=!1},f=()=>{p.value.forEach(e=>{e.read=!0})},y=e=>{const t=new Date-e,o=Math.floor(t/6e4),n=Math.floor(t/36e5),i=Math.floor(t/864e5);return o<1?"刚刚":o<60?`${o}分钟前`:n<24?`${n}小时前`:`${i}天前`};return(e,i)=>{const a=ve,s=V,r=De;return o(),l(r,{modelValue:m.value,"onUpdate:modelValue":i[0]||(i[0]=e=>m.value=e),title:"通知中心",direction:"rtl",size:"400px","before-close":g},{default:c(()=>[d("div",Ht,[d("div",Jt,[i[2]||(i[2]=d("h3",null,"最新通知",-1)),n(a,{text:"",onClick:f},{default:c(()=>i[1]||(i[1]=[v("全部已读",-1)])),_:1,__:[1]})]),d("div",Qt,[(o(!0),t(h,null,_(p.value,e=>(o(),t("div",{key:e.id,class:"notification-item"},[d("div",Xt,[n(s,null,{default:c(()=>[n(b(B))]),_:1})]),d("div",Yt,[d("div",Zt,j(e.title),1),d("div",eo,j(e.message),1),d("div",to,j(y(e.time)),1)])]))),128))])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-fb61e5ca"]]),no=bt({__name:"ShortcutHelp",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:t}){const i=e,a=t,r=s({get:()=>i.modelValue,set:e=>a("update:modelValue",e)}),u=()=>{r.value=!1};return(e,t)=>{const i=ve,a=Le;return o(),l(a,{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=e=>r.value=e),title:"快捷键帮助",width:"600px","before-close":u},{footer:c(()=>[n(i,{onClick:u},{default:c(()=>t[1]||(t[1]=[v("关闭",-1)])),_:1,__:[1]})]),default:c(()=>[t[2]||(t[2]=d("div",{class:"shortcut-help"},[d("div",{class:"shortcut-section"},[d("h4",null,"导航快捷键"),d("div",{class:"shortcut-list"},[d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"Ctrl"),v(" + "),d("kbd",null,"B")]),d("div",{class:"shortcut-desc"},"切换侧边栏")]),d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"Ctrl"),v(" + "),d("kbd",null,"K")]),d("div",{class:"shortcut-desc"},"打开搜索")]),d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"F11")]),d("div",{class:"shortcut-desc"},"全屏切换")])])]),d("div",{class:"shortcut-section"},[d("h4",null,"功能快捷键"),d("div",{class:"shortcut-list"},[d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"Ctrl"),v(" + "),d("kbd",null,"S")]),d("div",{class:"shortcut-desc"},"保存当前页面")]),d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"Ctrl"),v(" + "),d("kbd",null,"R")]),d("div",{class:"shortcut-desc"},"刷新页面")]),d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"Esc")]),d("div",{class:"shortcut-desc"},"关闭弹窗")])])]),d("div",{class:"shortcut-section"},[d("h4",null,"帮助快捷键"),d("div",{class:"shortcut-list"},[d("div",{class:"shortcut-item"},[d("div",{class:"shortcut-keys"},[d("kbd",null,"?")]),d("div",{class:"shortcut-desc"},"显示快捷键帮助")])])])],-1))]),_:1,__:[2]},8,["modelValue"])}}},[["__scopeId","data-v-27c1e945"]]),io={workbench:{title:"工作台",icon:"Monitor",order:1,description:"个性化工作区，快速访问常用功能",children:[{path:"/workbench/personal",title:"个人工作台",icon:"User",description:"根据角色定制的个人工作区",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/workbench/quick-actions",title:"快速操作",icon:"Lightning",description:"常用操作快捷入口",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/workbench/recent",title:"最近访问",icon:"Clock",description:"最近使用的功能和页面",roles:["admin","substation","agent","distributor","group_owner","user"]}]},dataCenter:{title:"数据中心",icon:"DataBoard",order:2,description:"数据分析、统计报表、实时监控",children:[{path:"/dashboard",title:"实时看板",icon:"Monitor",description:"核心业务数据总览",roles:["admin","substation","agent","distributor","group_owner","user"],permissionScope:"dashboard"},{path:"/data-screen",title:"数据大屏",icon:"DataLine",description:"全屏数据展示",roles:["admin","substation","agent","distributor","group_owner","user"],permissionScope:"dashboard"},{path:"/analytics/business",title:"业务分析",icon:"TrendCharts",description:"深度业务数据分析",roles:["admin","substation","agent"],permissionScope:"analytics"},{path:"/reports/comprehensive",title:"综合报表",icon:"Document",description:"各类业务统计报表",roles:["admin","substation"],permissionScope:"reports"}]},coreBusiness:{title:"核心业务",icon:"Management",order:3,description:"社群运营、订单处理、客户服务等核心业务",children:[{path:"/business/community",title:"社群运营",icon:"Comment",description:"群组管理、内容管理、营销活动",roles:["admin","substation","agent","distributor","group_owner"],children:[{path:"/community/groups",title:"群组管理",icon:"UserFilled",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/community/add",title:"创建群组",icon:"Plus",roles:["admin","substation","agent","distributor","group_owner","user"],protected:!0},{path:"/community/templates",title:"内容模板",icon:"DocumentCopy",roles:["admin","substation","group_owner"]},{path:"/community/marketing",title:"营销活动",icon:"Promotion",roles:["admin","substation","agent","distributor"]}]},{path:"/business/orders",title:"订单中心",icon:"ShoppingCart",description:"订单管理、支付处理、售后服务",roles:["admin","substation","agent","distributor","group_owner","user"],children:[{path:"/orders/list",title:"订单管理",icon:"List",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/orders/payments",title:"支付管理",icon:"CreditCard",roles:["admin","substation"]},{path:"/orders/refunds",title:"售后服务",icon:"RefreshLeft",roles:["admin","substation","group_owner"]}]},{path:"/business/customers",title:"客户服务",icon:"UserFilled",description:"客户管理、服务记录、满意度调查",roles:["admin","substation","agent","distributor"],children:[{path:"/customers/list",title:"客户管理",icon:"User",roles:["admin","substation","agent","distributor"]},{path:"/customers/service",title:"服务记录",icon:"Document",roles:["admin","substation","agent"]},{path:"/customers/feedback",title:"满意度调查",icon:"Star",roles:["admin","substation"]}]}]},distributionNetwork:{title:"分销网络",icon:"Share",order:4,description:"分销体系、推广营销、佣金结算",children:[{path:"/distribution/hierarchy",title:"分销体系",icon:"Connection",description:"代理商管理、分销员管理、层级关系",roles:["admin","substation","agent"],children:[{path:"/agents/list",title:"代理商管理",icon:"Avatar",roles:["admin","substation"]},{path:"/distributors/list",title:"分销员管理",icon:"User",roles:["admin","substation","agent"]},{path:"/distribution/relationships",title:"层级关系",icon:"Connection",roles:["admin","substation","agent"]}]},{path:"/distribution/marketing",title:"推广营销",icon:"Promotion",description:"推广链接、营销工具、效果分析",roles:["admin","substation","agent","distributor"],children:[{path:"/promotion/links",title:"推广链接",icon:"Link",roles:["admin","substation","agent","distributor"]},{path:"/promotion/tools",title:"营销工具",icon:"Tools",roles:["admin","substation","agent"]},{path:"/promotion/analytics",title:"效果分析",icon:"DataAnalysis",roles:["admin","substation","agent","distributor"]}]},{path:"/distribution/commission",title:"佣金结算",icon:"Money",description:"佣金规则、结算记录、提现管理",roles:["admin","substation","agent","distributor"],children:[{path:"/finance/commission/rules",title:"佣金规则",icon:"Setting",roles:["admin","substation"]},{path:"/finance/commission/logs",title:"结算记录",icon:"List",roles:["admin","substation","agent","distributor"]},{path:"/finance/withdrawals",title:"提现管理",icon:"Upload",roles:["admin","substation","agent","distributor"]}]},{path:"/distribution/substation",title:"分站管理",icon:"OfficeBuilding",description:"多站点运营管理、财务分析、数据监控",roles:["admin"],children:[{path:"/substation/list",title:"分站列表",icon:"List",roles:["admin"],description:"查看和管理所有分站"},{path:"/substation/finance",title:"分站财务",icon:"Money",roles:["admin"],description:"分站收入统计、佣金结算、财务报表"},{path:"/substation/analytics",title:"分站分析",icon:"DataAnalysis",roles:["admin"],description:"分站运营数据分析、业绩对比"}]}]},systemManagement:{title:"系统管理",icon:"Setting",order:5,description:"用户权限、系统配置、运维监控",children:[{path:"/system/users",title:"用户权限",icon:"Lock",description:"用户管理、角色管理、权限配置",roles:["admin","substation"],children:[{path:"/users/list",title:"用户管理",icon:"User",roles:["admin","substation"]},{path:"/permission/roles",title:"角色管理",icon:"UserFilled",roles:["admin","substation"]},{path:"/permission/permissions",title:"权限配置",icon:"Key",roles:["admin"]}]},{path:"/system/config",title:"系统配置",icon:"Tools",description:"基础设置、支付配置、安全设置",roles:["admin","substation"],children:[{path:"/system/settings",title:"基础设置",icon:"Setting",roles:["admin","substation"]},{path:"/payment",title:"支付设置",icon:"CreditCard",roles:["admin","substation"],description:"支付渠道配置、支付参数设置"},{path:"/anti-block",title:"防红配置",icon:"Shield",roles:["admin","substation"],description:"防封系统配置、域名管理"},{path:"/security/management",title:"安全设置",icon:"Lock",roles:["admin"]}]},{path:"/system/operations",title:"运维监控",icon:"Monitor",description:"系统监控、日志管理、数据备份",roles:["admin"],children:[{path:"/system/monitor",title:"系统监控",icon:"Monitor",roles:["admin"]},{path:"/system/logs",title:"日志管理",icon:"Document",roles:["admin"]},{path:"/system/backup",title:"数据备份",icon:"Folder",roles:["admin"]}]}]}},ao={admin:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork","systemManagement"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"分站管理",path:"/substation/list",icon:"OfficeBuilding",color:"#67c23a"},{title:"支付设置",path:"/payment",icon:"CreditCard",color:"#e6a23c"},{title:"防红配置",path:"/anti-block",icon:"Shield",color:"#f56c6c"}]},substation:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork","systemManagement"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"支付设置",path:"/payment",icon:"CreditCard",color:"#67c23a"},{title:"防红配置",path:"/anti-block",icon:"Shield",color:"#e6a23c"},{title:"代理商管理",path:"/agent/list",icon:"Avatar",color:"#f56c6c"}]},agent:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"团队管理",path:"/distributors/list",icon:"User",color:"#67c23a"},{title:"佣金查看",path:"/finance/commission/logs",icon:"Money",color:"#e6a23c"},{title:"推广分析",path:"/promotion/analytics",icon:"TrendCharts",color:"#f56c6c"}]},distributor:{visibleGroups:["workbench","dataCenter","coreBusiness","distributionNetwork"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"客户管理",path:"/customers/list",icon:"UserFilled",color:"#67c23a"},{title:"推广链接",path:"/promotion/links",icon:"Link",color:"#e6a23c"},{title:"佣金记录",path:"/finance/commission/logs",icon:"Money",color:"#f56c6c"}]},group_owner:{visibleGroups:["workbench","dataCenter","coreBusiness"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"我的群组",path:"/community/groups",icon:"Comment",color:"#67c23a"},{title:"内容模板",path:"/community/templates",icon:"DocumentCopy",color:"#e6a23c"},{title:"群组数据",path:"/dashboard",icon:"DataLine",color:"#f56c6c"}]},user:{visibleGroups:["workbench","dataCenter","coreBusiness"],defaultGroup:"workbench",customWorkbench:"/workbench/personal",quickActions:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"我的订单",path:"/orders/list",icon:"Tickets",color:"#67c23a"},{title:"个人中心",path:"/user/center",icon:"User",color:"#e6a23c"}]}};function so(e){const t=ao[e];return t?.quickActions||[]}const ro={workbench:{title:"工作台",icon:"Monitor",order:1,description:"个人工作区和数据分析",children:[{path:"/dashboard",title:"数据看板",icon:"DataLine",description:"查看核心业务数据",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/data-screen",title:"数据大屏",icon:"DataBoard",description:"全屏数据展示",roles:["admin","substation","agent","distributor"]},{path:"/data-screen/reports",title:"报表中心",icon:"Document",description:"各类业务报表",roles:["admin","substation","agent"]}]},business:{title:"核心业务",icon:"Management",order:2,description:"社群管理和订单处理",children:[{path:"/community/groups",title:"社群管理",icon:"Comment",description:"管理所有社群",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/community/add",title:"创建群组",icon:"Plus",description:"创建新的微信群组",roles:["admin","substation","agent","distributor","group_owner","user"],protected:!0},{path:"/orders/list",title:"订单管理",icon:"ShoppingCart",description:"查看和管理订单",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/orders/analytics",title:"订单分析",icon:"DataAnalysis",description:"订单数据统计分析",roles:["admin","substation","agent"]}]},distribution:{title:"分销管理",icon:"Share",order:3,description:"分销网络和推广管理",children:[{path:"/agent/list",title:"代理商管理",icon:"Avatar",description:"管理代理商",roles:["admin","substation"]},{path:"/distribution/distributors",title:"分销员管理",icon:"User",description:"管理分销员",roles:["admin","substation","agent"]},{path:"/substation/list",title:"分站管理",icon:"OfficeBuilding",description:"管理平台分站，配置分站权限",roles:["admin"]},{path:"/promotion/links",title:"推广链接",icon:"Link",description:"管理推广链接",roles:["admin","substation","agent","distributor"]}]},finance:{title:"财务管理",icon:"Money",order:4,description:"财务数据和佣金管理",children:[{path:"/finance/dashboard",title:"财务总览",icon:"DataBoard",description:"查看财务数据",roles:["admin","substation","agent","distributor","group_owner"]},{path:"/finance/commission-logs",title:"佣金记录",icon:"Medal",description:"查看佣金记录",roles:["admin","substation","agent","distributor"]},{path:"/finance/withdraw",title:"提现管理",icon:"Upload",description:"处理提现申请和记录",roles:["admin","substation","agent","distributor"]},{path:"/finance/transactions",title:"交易记录",icon:"List",description:"查看所有交易记录",roles:["admin","substation","agent"]}]},userPermission:{title:"用户权限",icon:"User",order:5,description:"用户管理和权限控制",children:[{path:"/user/list",title:"用户管理",icon:"UserFilled",description:"管理系统用户",roles:["admin","substation"]},{path:"/permission/roles",title:"角色管理",icon:"Avatar",description:"管理用户角色和权限",roles:["admin","substation"]}]},systemConfig:{title:"系统配置",icon:"Setting",order:6,description:"系统设置和业务配置",children:[{path:"/system/settings",title:"基础设置",icon:"Tools",description:"系统基础配置",roles:["admin","substation"]},{path:"/payment",title:"支付设置",icon:"CreditCard",description:"支付渠道配置和参数设置",roles:["admin","substation"]},{path:"/anti-block",title:"防红配置",icon:"Shield",description:"防封系统配置和域名管理",roles:["admin","substation"]}]},systemMonitor:{title:"系统监控",icon:"Monitor",order:7,description:"系统监控和安全管理",children:[{path:"/system/operation-logs",title:"操作日志",icon:"Document",description:"查看系统操作日志",roles:["admin","substation"]},{path:"/security/management",title:"安全管理",icon:"Lock",description:"系统安全设置和监控",roles:["admin"]}]},profile:{title:"个人中心",icon:"User",order:6,description:"个人信息和设置",children:[{path:"/user/center",title:"个人资料",icon:"User",description:"查看和编辑个人信息",roles:["admin","substation","agent","distributor","group_owner","user"]},{path:"/user/profile",title:"账户设置",icon:"Setting",description:"账户相关设置",roles:["admin","substation","agent","distributor","group_owner","user"]}]}},lo={admin:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"分站管理",path:"/substation/list",icon:"OfficeBuilding",color:"#67c23a"},{title:"支付设置",path:"/payment",icon:"CreditCard",color:"#e6a23c"},{title:"防红配置",path:"/anti-block",icon:"Shield",color:"#f56c6c"}],substation:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"支付设置",path:"/payment",icon:"CreditCard",color:"#67c23a"},{title:"防红配置",path:"/anti-block",icon:"Shield",color:"#e6a23c"},{title:"代理商管理",path:"/agent/list",icon:"Avatar",color:"#f56c6c"}],agent:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"分销员管理",path:"/distribution/distributors",icon:"User",color:"#67c23a"},{title:"佣金记录",path:"/finance/commission-logs",icon:"Money",color:"#e6a23c"},{title:"推广链接",path:"/promotion/links",icon:"Link",color:"#f56c6c"}],distributor:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"推广链接",path:"/promotion/links",icon:"Link",color:"#67c23a"},{title:"佣金记录",path:"/finance/commission-logs",icon:"Money",color:"#e6a23c"},{title:"我的群组",path:"/community/groups",icon:"Comment",color:"#f56c6c"}],group_owner:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"我的群组",path:"/community/groups",icon:"Comment",color:"#67c23a"},{title:"订单管理",path:"/orders/list",icon:"ShoppingCart",color:"#e6a23c"},{title:"财务数据",path:"/finance/dashboard",icon:"Money",color:"#f56c6c"}],user:[{title:"创建群组",path:"/community/add",icon:"Plus",color:"#409eff"},{title:"我的订单",path:"/orders/list",icon:"ShoppingCart",color:"#67c23a"},{title:"个人中心",path:"/user/center",icon:"User",color:"#e6a23c"}]};function co(e){if(console.log("🧪 简化导航 - 获取角色导航:",e),!e)return console.log("⚠️ 简化导航 - 用户角色为空"),{};const t={};return Object.entries(ro).forEach(([o,n])=>{console.log(`📁 简化导航 - 处理分组 ${o}:`,n.title);const i=n.children.filter(t=>{const o=!t.roles||t.roles.includes(e);return console.log(`  📄 子菜单 ${t.title}: ${o?"✅":"❌"}`),o});i.length>0?(t[o]={...n,children:i},console.log(`✅ 简化导航 - 分组 ${o} 包含 ${i.length} 个子菜单`)):console.log(`❌ 简化导航 - 分组 ${o} 无可访问子菜单`)}),console.log("🎯 简化导航 - 最终可见分组:",Object.keys(t)),t}function uo(e){return lo[e]||lo.user}function mo(e){const t=function(e){const t=[],o=ro.business.children.find(e=>"/community/add"===e.path);!o||o.roles&&!o.roles.includes(e)||t.push({type:"navigation",title:"导航菜单 - 创建群组",path:o.path,location:"核心业务 > 创建群组"});const n=uo(e).find(e=>"/community/add"===e.path);return n&&t.push({type:"quick_action",title:"快速操作 - 创建群组",path:n.path,location:"快速操作面板"}),t.push({type:"workbench",title:"工作台 - 创建群组",path:"/community/add",location:"个人工作台"}),t}(e),o=function(e,t){for(const o of Object.values(ro))for(const n of o.children)if(n.path===e)return!n.roles||n.roles.includes(t);return!0}("/community/add",e);return{userRole:e,canAccess:o,entryCount:t.length,entries:t,isProtected:t.some(e=>"navigation"===e.type),hasQuickAction:t.some(e=>"quick_action"===e.type),hasWorkbenchAccess:t.some(e=>"workbench"===e.type),validation:{passed:o&&t.length>=2,issues:[]}}}function po(){const e={};return["admin","substation","agent","distributor","group_owner","user"].forEach(t=>{e[t]=mo(t)}),console.log("🔒 群组创建功能完整性验证结果:"),console.table(Object.entries(e).map(([e,t])=>({"角色":e,"可访问":t.canAccess?"✅":"❌","入口数量":t.entryCount,"有导航入口":t.isProtected?"✅":"❌","有快速操作":t.hasQuickAction?"✅":"❌","验证通过":t.validation.passed?"✅":"❌"}))),e}const ho={class:"modern-admin-layout"},_o={class:"logo-section"},go={class:"logo-container"},fo={key:0,class:"logo-text"},bo={key:0,class:"user-profile-card"},vo={class:"user-avatar"},yo=["src"],ko={class:"user-info"},wo={class:"user-name"},Eo={class:"user-role"},Do={class:"user-actions"},Lo={class:"navigation-menu"},Ao={class:"sidebar-footer"},Po={key:0,class:"system-status"},So={class:"top-header"},To={class:"header-left"},Io={class:"header-right"},Co={class:"search-container"},Oo={class:"search-content"},Ro={key:0,class:"search-results"},Mo=["onClick","onMouseenter"],Vo={class:"result-icon"},jo={class:"result-content"},Uo={class:"result-title"},xo={class:"result-path"},Bo={key:1,class:"search-empty"},$o={class:"header-actions"},qo={class:"user-menu"},Fo={class:"user-trigger"},Go={class:"username"},No={class:"user-info-header"},Wo={class:"user-details"},Ko={class:"name"},zo={class:"role"},Ho={class:"page-content"},Jo={__name:"ModernLayout",setup(i){const D=y(),L=r(),A=lt(),P=a(!1),S=a(""),T=a(!1),I=a(!1),R=a([]),U=a(0),x=a(null),$=a(!1),q=a(3),F=a(!1),G=s(()=>D.path),W=s(()=>D.matched.filter(e=>e.meta&&e.meta.title&&"/dashboard"!==e.path)),K=s(()=>{const e=A.userInfo?.role;if(console.log("🔍 导航过滤调试:",{userInfo:A.userInfo,userRole:e,hasToken:!!A.token}),!e)return console.log("⚠️ 用户角色为空，返回空路由"),[];console.log("🧪 尝试使用简化导航配置测试");try{const t=co(e);if(console.log("🎯 简化导航分组:",Object.keys(t)),Object.keys(t).length>0){const e=[];if(Object.entries(t).forEach(([t,o])=>{console.log(`📁 处理简化分组 ${t}:`,o);const n={path:`/${t}`,meta:{title:o.title,icon:o.icon,order:o.order},children:[]};o.children&&o.children.length>0&&o.children.forEach(e=>{const t={path:e.path,meta:{title:e.title,icon:e.icon,description:e.description,protected:e.protected}};n.children.push(t)}),e.push(n)}),e.sort((e,t)=>(e.meta.order||999)-(t.meta.order||999)),console.log("✅ 简化导航路由:",e.map(e=>({path:e.path,title:e.meta?.title,children:e.children?.length||0}))),e.length>0)return console.log("🎉 使用简化导航配置成功！"),e}}catch(n){}try{const t=function(e){const t=ao[e];if(!t)return{};const o={};return t.visibleGroups.forEach(t=>{io[t]&&(o[t]=function(e,t){const o={...e};return o.children&&(o.children=o.children.filter(e=>!(e.roles&&!e.roles.includes(t)||(e.children&&(e.children=e.children.filter(e=>!(e.roles&&!e.roles.includes(t)||(e.children&&(e.children=e.children.filter(e=>!e.roles||e.roles.includes(t))),0)))),0)))),o}(io[t],e))}),o}(e);if(console.log("🎯 可见导航分组:",Object.keys(t)),Object.keys(t).length>0){const e=[];if(Object.entries(t).forEach(([t,o])=>{console.log(`📁 处理分组 ${t}:`,o);const n={path:`/${t}`,meta:{title:o.title,icon:o.icon,order:o.order},children:[]};o.children&&o.children.length>0&&o.children.forEach(e=>{const t={path:e.path,meta:{title:e.title,icon:e.icon,description:e.description,protected:e.protected}};e.children&&e.children.length>0&&(t.children=e.children.map(e=>{const t={path:e.path,meta:{title:e.title,icon:e.icon,description:e.description}};return e.children&&e.children.length>0&&(t.children=e.children.map(e=>({path:e.path,meta:{title:e.title,icon:e.icon,description:e.description}}))),t})),n.children.push(t)}),e.push(n)}),e.sort((e,t)=>(e.meta.order||999)-(t.meta.order||999)),console.log("✅ 优化后的导航路由:",e.map(e=>({path:e.path,title:e.meta?.title,children:e.children?.length||0}))),e.length>0)return e}}catch(n){}const t=L.options.routes.filter(e=>"/login"!==e.path&&"/404"!==e.path&&"/403"!==e.path&&"/"!==e.path&&!e.meta?.hidden);console.log("📋 使用原始路由配置:",t.map(e=>({path:e.path,title:e.meta?.title})));const o=ht(t,e);return console.log("✅ 过滤后的路由:",o.map(e=>({path:e.path,title:e.meta?.title}))),o}),z=()=>{P.value=!P.value},H=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},J=()=>{F.value=!F.value,document.documentElement.classList.toggle("dark",F.value)},Q=()=>{window.location.reload()},X=()=>{I.value=!0,setTimeout(()=>{x.value?.focus()},100)},Y=e=>{R.value=function(e){if(!e||0===e.trim().length)return[];const t=e.toLowerCase().trim();return Vt.filter(e=>e.title.toLowerCase().includes(t)||e.keywords.some(e=>e.toLowerCase().includes(t))).slice(0,8)}(e),U.value=0},Z=e=>{switch(e.key){case"ArrowDown":e.preventDefault(),U.value=Math.min(U.value+1,R.value.length-1);break;case"ArrowUp":e.preventDefault(),U.value=Math.max(U.value-1,0);break;case"Enter":e.preventDefault(),R.value[U.value]&&ee(R.value[U.value]);break;case"Escape":I.value=!1}},ee=e=>{L.push(e.path),I.value=!1,S.value="",R.value=[]},te=e=>{(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),X())},oe=()=>{O.confirm("确定要退出登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await A.logout(),C.success("退出登录成功"),L.push("/login")}catch(e){C.error("退出登录失败")}})},ne=e=>{(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),document.querySelector(".search-input input")?.focus()),(e.ctrlKey||e.metaKey)&&"b"===e.key&&(e.preventDefault(),z()),"F11"===e.key&&(e.preventDefault(),H()),"?"!==e.key||e.ctrlKey||e.metaKey||($.value=!0)};return k(()=>A.userInfo,e=>{console.log("👤 用户信息变化:",e),e&&console.log("✅ 用户信息已设置，角色:",e.role)},{immediate:!0,deep:!0}),k(()=>localStorage.getItem("preview-mode"),e=>{console.log("🎭 预览模式状态变化:",e),"true"!==e||A.userInfo||(console.log("⚠️ 预览模式已启用但用户信息未设置，尝试手动设置"),A.setUserInfo({id:"preview-user",username:"admin",nickname:"超级管理员 (预览)",name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]}))},{immediate:!0}),g(()=>{document.addEventListener("keydown",ne),document.addEventListener("keydown",te);const e="true"===localStorage.getItem("preview-mode");console.log("🔍 组件挂载时检查:",{isPreviewMode:e,hasUserInfo:!!A.userInfo,userRole:A.userInfo?.role})}),f(()=>{document.removeEventListener("keydown",ne),document.removeEventListener("keydown",te)}),(i,a)=>{const s=V,r=ve,g=Te,f=Se,y=Ae,k=Ce,D=Oe,L=Ve,C=Ue,O=Be,te=Le,ne=$e,ie=We,ae=ze,se=e("router-view");return o(),t("div",ho,[d("aside",{class:M(["modern-sidebar",{collapsed:P.value}])},[d("div",_o,[d("div",go,[a[11]||(a[11]=w('<div class="logo-icon" data-v-e6b357fc><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-e6b357fc><defs data-v-e6b357fc><linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%" data-v-e6b357fc><stop offset="0%" style="stop-color:#3b82f6;" data-v-e6b357fc></stop><stop offset="100%" style="stop-color:#8b5cf6;" data-v-e6b357fc></stop></linearGradient></defs><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="url(#logoGradient)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" data-v-e6b357fc></path></svg></div>',1)),n(p,{name:"fade"},{default:c(()=>[P.value?u("",!0):(o(),t("div",fo,a[10]||(a[10]=[d("h1",null,"LinkHub Pro",-1),d("p",null,"晨鑫流量变现系统",-1)])))]),_:1})])]),n(p,{name:"slide-fade"},{default:c(()=>[P.value?u("",!0):(o(),t("div",bo,[d("div",vo,[d("img",{src:b(A).avatar||"/default-avatar.png",alt:"用户头像"},null,8,yo),a[12]||(a[12]=d("div",{class:"status-indicator online"},null,-1))]),d("div",ko,[d("div",wo,j(b(A).nickname||"管理员"),1),d("div",Eo,j(b(gt)(b(A).userInfo?.role)),1)]),d("div",Do,[n(y,{trigger:"click"},{dropdown:c(()=>[n(f,null,{default:c(()=>[n(g,{onClick:a[0]||(a[0]=e=>i.$router.push("/user/profile"))},{default:c(()=>[n(s,null,{default:c(()=>[n(b(he))]),_:1}),a[13]||(a[13]=v(" 个人资料 ",-1))]),_:1,__:[13]}),n(g,{onClick:a[1]||(a[1]=e=>i.$router.push("/user/settings"))},{default:c(()=>[n(s,null,{default:c(()=>[n(b(N))]),_:1}),a[14]||(a[14]=v(" 账户设置 ",-1))]),_:1,__:[14]}),n(g,{divided:"",onClick:oe},{default:c(()=>[n(s,null,{default:c(()=>[n(b(Ie))]),_:1}),a[15]||(a[15]=v(" 退出登录 ",-1))]),_:1,__:[15]})]),_:1})]),default:c(()=>[n(r,{text:"",class:"user-menu-btn"},{default:c(()=>[n(s,null,{default:c(()=>[n(b(Pe))]),_:1})]),_:1})]),_:1})])]))]),_:1}),d("nav",Lo,[n(D,{class:"menu-scrollbar"},{default:c(()=>[n(k,{"default-active":G.value,collapse:P.value,"unique-opened":!0,"background-color":"transparent","text-color":"rgba(255, 255, 255, 0.8)","active-text-color":"#ffffff",class:"sidebar-menu",router:""},{default:c(()=>[(o(!0),t(h,null,_(K.value,e=>(o(),l(Ot,{key:e.path,item:e,"base-path":e.path,collapsed:P.value},null,8,["item","base-path","collapsed"]))),128))]),_:1},8,["default-active","collapse"]),n(zt,{collapsed:P.value},null,8,["collapsed"])]),_:1})]),d("div",Ao,[n(p,{name:"fade"},{default:c(()=>[P.value?u("",!0):(o(),t("div",Po,a[16]||(a[16]=[d("div",{class:"status-item"},[d("div",{class:"status-dot success"}),d("span",null,"系统运行正常")],-1),d("div",{class:"version-info"},"v2.0.1",-1)])))]),_:1})])],2),d("main",{class:M(["main-content",{expanded:P.value}])},[d("header",So,[d("div",To,[n(r,{text:"",class:"collapse-btn",onClick:z},{default:c(()=>[n(s,{size:"20"},{default:c(()=>[P.value?(o(),l(b(Me),{key:1})):(o(),l(b(Re),{key:0}))]),_:1})]),_:1}),n(C,{separator:"/",class:"breadcrumb-nav"},{default:c(()=>[n(L,{to:{path:"/dashboard"}},{default:c(()=>[n(s,null,{default:c(()=>[n(b(je))]),_:1}),a[17]||(a[17]=v(" 控制台 ",-1))]),_:1,__:[17]}),(o(!0),t(h,null,_(W.value,e=>(o(),l(L,{key:e.path},{default:c(()=>[v(j(e.meta.title),1)]),_:2},1024))),128))]),_:1})]),d("div",Io,[d("div",Co,[d("div",{class:"search-trigger",onClick:X},[n(s,{class:"search-icon"},{default:c(()=>[n(b(xe))]),_:1}),a[18]||(a[18]=d("span",{class:"search-placeholder"},"搜索功能、用户、订单...",-1)),a[19]||(a[19]=d("kbd",{class:"search-shortcut"},"Ctrl+K",-1))]),n(te,{modelValue:I.value,"onUpdate:modelValue":a[3]||(a[3]=e=>I.value=e),title:"全局搜索",width:"500px","show-close":!1,"close-on-click-modal":!0,class:"search-dialog"},{default:c(()=>[d("div",Oo,[n(O,{ref_key:"searchInputRef",ref:x,modelValue:S.value,"onUpdate:modelValue":a[2]||(a[2]=e=>S.value=e),placeholder:"输入关键词搜索...","prefix-icon":"Search",size:"large",onInput:Y,onKeydown:Z},null,8,["modelValue"]),R.value.length>0?(o(),t("div",Ro,[(o(!0),t(h,null,_(R.value,(e,i)=>(o(),t("div",{key:e.id,class:M(["search-result-item",{active:U.value===i}]),onClick:t=>ee(e),onMouseenter:e=>U.value=i},[d("div",Vo,[n(s,null,{default:c(()=>{return[(o(),l(m((t=e.icon,{Monitor:"Monitor",DataLine:"DataLine",Comment:"Comment",Plus:"Plus",ShoppingCart:"ShoppingCart",User:"User",Money:"Money",Share:"Share"}[t]||"Document"))))];var t}),_:2},1024)]),d("div",jo,[d("div",Uo,j(e.title),1),d("div",xo,j(e.path),1)])],42,Mo))),128))])):S.value&&0===R.value.length?(o(),t("div",Bo,[n(s,null,{default:c(()=>[n(b(xe))]),_:1}),a[20]||(a[20]=d("span",null,"未找到相关结果",-1))])):u("",!0)])]),_:1},8,["modelValue"])]),d("div",$o,[n(ne,{value:q.value,hidden:0===q.value},{default:c(()=>[n(r,{text:"",class:"action-btn",onClick:a[4]||(a[4]=e=>T.value=!0)},{default:c(()=>[n(s,null,{default:c(()=>[n(b(B))]),_:1})]),_:1})]),_:1},8,["value","hidden"]),n(r,{text:"",class:"action-btn",onClick:H},{default:c(()=>[n(s,null,{default:c(()=>[n(b(qe))]),_:1})]),_:1}),n(r,{text:"",class:"action-btn",onClick:J},{default:c(()=>[n(s,null,{default:c(()=>[F.value?(o(),l(b(Fe),{key:0})):(o(),l(b(Ge),{key:1}))]),_:1})]),_:1}),n(r,{text:"",class:"action-btn",onClick:Q},{default:c(()=>[n(s,null,{default:c(()=>[n(b(Ne))]),_:1})]),_:1})]),d("div",qo,[n(y,{trigger:"click"},{dropdown:c(()=>[n(f,{class:"user-dropdown-menu"},{default:c(()=>[d("div",No,[n(ie,{src:b(A).avatar,size:"large"},{default:c(()=>[n(s,null,{default:c(()=>[n(b(he))]),_:1})]),_:1},8,["src"]),d("div",Wo,[d("div",Ko,j(b(A).nickname||"管理员"),1),d("div",zo,j(b(gt)(b(A).userInfo?.role)),1)])]),n(ae,{style:{margin:"12px 0"}}),n(g,{onClick:a[5]||(a[5]=e=>i.$router.push("/user/profile"))},{default:c(()=>[n(s,null,{default:c(()=>[n(b(he))]),_:1}),a[21]||(a[21]=v(" 个人中心 ",-1))]),_:1,__:[21]}),n(g,{onClick:a[6]||(a[6]=e=>i.$router.push("/user/settings"))},{default:c(()=>[n(s,null,{default:c(()=>[n(b(N))]),_:1}),a[22]||(a[22]=v(" 账户设置 ",-1))]),_:1,__:[22]}),n(g,{onClick:a[7]||(a[7]=e=>i.$router.push("/system/help"))},{default:c(()=>[n(s,null,{default:c(()=>[n(b(He))]),_:1}),a[23]||(a[23]=v(" 帮助中心 ",-1))]),_:1,__:[23]}),n(g,{divided:"",onClick:oe},{default:c(()=>[n(s,null,{default:c(()=>[n(b(Ie))]),_:1}),a[24]||(a[24]=v(" 退出登录 ",-1))]),_:1,__:[24]})]),_:1})]),default:c(()=>[d("div",Fo,[n(ie,{src:b(A).avatar,size:"small"},{default:c(()=>[n(s,null,{default:c(()=>[n(b(he))]),_:1})]),_:1},8,["src"]),d("span",Go,j(b(A).nickname||"管理员"),1),n(s,{class:"dropdown-icon"},{default:c(()=>[n(b(Ke))]),_:1})])]),_:1})])])]),d("div",Ho,[n(se,null,{default:c(({Component:e,route:t})=>[n(p,{name:"page-transition",mode:"out-in"},{default:c(()=>[(o(),l(E,null,[(o(),l(m(e),{key:t.path}))],1024))]),_:2},1024)]),_:1})])],2),n(oo,{modelValue:T.value,"onUpdate:modelValue":a[8]||(a[8]=e=>T.value=e)},null,8,["modelValue"]),n(no,{modelValue:$.value,"onUpdate:modelValue":a[9]||(a[9]=e=>$.value=e)},null,8,["modelValue"])])}}},Qo=bt(Jo,[["__scopeId","data-v-e6b357fc"]]),Xo={MAX_IDLE_TIME:9e5};class Yo{constructor(){this.lastActivity=Date.now(),this.sessionTimer=null,this.idleTimer=null,this.isActive=!0,this.initSessionMonitoring()}initSessionMonitoring(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.addEventListener(e,this.updateActivity.bind(this),!0)}),this.startSessionCheck(),this.startIdleCheck()}updateActivity(){this.lastActivity=Date.now(),this.isActive=!0}startSessionCheck(){this.sessionTimer=setInterval(()=>{const e=localStorage.getItem("admin_token");if(e)try{1e3*JSON.parse(atob(e.split(".")[1])).exp<Date.now()&&this.handleSessionExpired()}catch(t){console.error("Token解析失败:",t),this.handleSessionExpired()}else this.handleSessionExpired()},6e4)}startIdleCheck(){this.idleTimer=setInterval(()=>{Date.now()-this.lastActivity>Xo.MAX_IDLE_TIME&&this.isActive&&this.handleIdleTimeout()},3e4)}handleSessionExpired(){this.cleanup(),O.alert("您的登录会话已过期，请重新登录","会话过期",{confirmButtonText:"重新登录",type:"warning",showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{this.logout()})}handleIdleTimeout(){this.isActive=!1,O.confirm("检测到您已长时间未操作，是否继续保持登录？","空闲提醒",{confirmButtonText:"继续使用",cancelButtonText:"退出登录",type:"warning"}).then(()=>{this.updateActivity()}).catch(()=>{this.logout()})}logout(){localStorage.removeItem("admin_token"),localStorage.removeItem("admin_user"),localStorage.removeItem("admin_permissions"),localStorage.removeItem("admin_roles"),an.push("/login")}getCurrentSession(){const e=localStorage.getItem("admin_token");if(!e)return null;try{const t=JSON.parse(atob(e.split(".")[1]));return{id:t.jti||"session_"+Date.now(),userId:t.sub,username:t.username,exp:t.exp,iat:t.iat,isValid:1e3*t.exp>Date.now()}}catch(t){return console.error("解析会话信息失败:",t),null}}cleanup(){this.sessionTimer&&(clearInterval(this.sessionTimer),this.sessionTimer=null),this.idleTimer&&(clearInterval(this.idleTimer),this.idleTimer=null)}destroy(){this.cleanup();["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.removeEventListener(e,this.updateActivity.bind(this),!0)})}}const Zo=new Yo,en=(e,t,o,n={})=>{const i={type:"session",username:e,action:t,sessionId:o,details:n,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent};console.log("Session Log:",i)},tn=(e,t,o={})=>{const n={type:"login",username:e,success:t,details:o,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,ip:"unknown"};console.log("Login Log:",n)},on=Object.freeze(Object.defineProperty({__proto__:null,SECURITY_CONFIG:Xo,SessionManager:Yo,logLogin:tn,logSession:en,sessionManager:Zo},Symbol.toStringTag,{value:"Module"})),nn=[{path:"/login",name:"Login",component:()=>Xe(()=>import("./Login-DME2sbOQ.js"),__vite__mapDeps([0,1,2,3,4])),meta:{title:"登录",hidden:!0}},{path:"/",redirect:"/dashboard"},{path:"/fullscreen-data-screen",name:"FullscreenDataScreen",component:()=>Xe(()=>import("./DataScreenFullscreen-k4Mj_VsY.js"),__vite__mapDeps([5,1,2,6,7,8,9,10,11,3,12,13])),meta:{title:"数据大屏",hidden:!0,fullscreen:!0}},{path:"/test",name:"TestPage",component:()=>Xe(()=>import("./TestPage-CWqo3qky.js"),__vite__mapDeps([14,1,2,3,15])),meta:{title:"测试页面",hidden:!0}},{path:"/test-preview",name:"TestPreview",component:()=>Xe(()=>import("./TestPreview-fptg9nyv.js"),__vite__mapDeps([16,1,2,3,17])),meta:{title:"预览测试页面",hidden:!0}},{path:"/dashboard-direct",name:"DashboardDirect",component:()=>Xe(()=>import("./ModernDashboard-C2luKjm5.js"),__vite__mapDeps([18,1,2,19,3,20,21,22,23,24,25,26])),meta:{title:"直接仪表板",hidden:!0}},{path:"/route-checker",name:"RouteChecker",component:()=>Xe(()=>import("./RouteChecker-BEmmehKW.js"),__vite__mapDeps([27,2,1,3,28,29,30,31,22,25,24,21])),meta:{title:"路由检测工具",hidden:!0}},{path:"/test-navigation",name:"TestNavigationSystem",component:()=>Xe(()=>import("./TestNavigationSystem-Dm1PGOpS.js"),__vite__mapDeps([32,1,2,19,3,33,30,31,22,26,34,35,24,36])),meta:{title:"导航系统测试",hidden:!0}},{path:"/dashboard",component:Qo,meta:{title:"数据看板",icon:"Monitor"},children:[{path:"",name:"Dashboard",component:()=>Xe(()=>import("./ModernDashboard-C2luKjm5.js"),__vite__mapDeps([18,1,2,19,3,20,21,22,23,24,25,26])),meta:{title:"数据看板",icon:"Monitor"}},{path:"/security",name:"SecurityManagement",component:()=>Xe(()=>import("./SecurityManagement-CGdTK9uD.js"),__vite__mapDeps([37,2,1,38,39,3,40,41,42,22,23,30,31,24,29,43,44,45,46,25])),meta:{title:"安全管理",requiresAuth:!0}}]},{path:"/data-screen",component:Qo,meta:{title:"数据大屏",icon:"DataLine"},children:[{path:"",name:"DataScreen",component:()=>Xe(()=>import("./DataScreen-DhgDTN83.js"),__vite__mapDeps([6,7,1,2,8,9,10,11,3,12])),meta:{title:"数据大屏",icon:"DataLine"}},{path:"reports",name:"DashboardReports",component:()=>Xe(()=>import("./Reports-DsJJSTOu.js"),__vite__mapDeps([47,2,1,38,39,3,48,30,31,22,49,24,25,50,23])),meta:{title:"数据报表",icon:"DataAnalysis"}}]},{path:"/community",component:Qo,redirect:"/community/groups",meta:{title:"社群管理",icon:"Comment"},children:[{path:"groups",name:"GroupList",component:()=>Xe(()=>import("./GroupList-BbuRdOiN.js"),__vite__mapDeps([51,1,2,52,53,54,55,56,57,44,58,21,34,59,45,24,22,23,43,31,25,46,60,61,3,62,41,63,64,65,66,42,30,26,49,35,50,67])),meta:{title:"社群列表",icon:"UserFilled"}},{path:"rules",name:"AutoRules",component:()=>Xe(()=>import("./AutoRules-CWt1k6gy.js"),__vite__mapDeps([68,1,2,38,39,61,3,69,41,24,30,31,45,22,44,23])),meta:{title:"自动化规则",icon:"MagicStick"}},{path:"events",name:"EventManagement",component:()=>Xe(()=>import("./EventManagement-DQLXH03-.js"),__vite__mapDeps([70,2,1,38,39,61,3,71,49,25,24,22,29,41,44,46,50,34,35])),meta:{title:"活动管理",icon:"Ticket"}},{path:"moderation",name:"ContentModeration",component:()=>Xe(()=>import("./ContentModeration-CU-oCkcY.js"),__vite__mapDeps([72,1,2,38,39,61,3,73,41,42,22,23,30,31,29])),meta:{title:"内容审核",icon:"ShieldCheck"}},{path:"analytics",name:"CommunityAnalytics",component:()=>Xe(()=>import("./AnalyticsDashboard-BvRdOuXV.js"),__vite__mapDeps([74,2,1,38,39,11,3,75,25,24,50])),meta:{title:"数据分析",icon:"DataLine"}},{path:"members/:id",name:"UserProfile",component:()=>Xe(()=>import("./UserProfile-D2NIrp8S.js"),__vite__mapDeps([60,2,1,61,3,62,41,63,24,25,22])),meta:{title:"用户画像",hidden:!0}},{path:"templates",name:"TemplateManagement",component:()=>Xe(()=>import("./TemplateManagement-BTfEDsf-.js"),__vite__mapDeps([76,1,2,61,52,53,3,77,41,36,46,58,21,42,22,23,25,30,31,78,45,34,35,24,44])),meta:{title:"模板管理",icon:"Document"}},{path:"add",name:"GroupAdd",component:()=>Xe(()=>import("./GroupAdd-B_U7JokZ.js"),__vite__mapDeps([79,1,2,52,53,54,55,3,80,44,45,24,58,21,34,59,22,23,43,31,25,46])),meta:{title:"创建群组",icon:"Plus"}},{path:"detail/:id",name:"GroupDetail",component:()=>Xe(()=>import("./GroupDetail-COgWOPNL.js"),__vite__mapDeps([81,1,2,38,39,3,82,24,25,22])),meta:{title:"群组详情",icon:"View",hidden:!0}},{path:"marketing",name:"GroupMarketing",component:()=>Xe(()=>import("./GroupMarketing-BBbU3HBr.js"),__vite__mapDeps([83,2,1,3,84,41,34,59,29,46,42,22,23,30,31,24,44])),meta:{title:"营销配置",icon:"Promotion"}}]},{path:"/distribution",component:Qo,redirect:"/distribution/groups",name:"Distribution",meta:{title:"分销管理",icon:"Share"},children:[{path:"distributors",name:"DistributorList",component:()=>Xe(()=>import("./DistributorList-j8gR_afM.js"),__vite__mapDeps([85,1,2,86,87,42,22,23,3,88,41,30,31,45,25,24,44])),meta:{title:"分销商管理",icon:"UserFilled"}},{path:"detail/:id",name:"DistributorDetail",component:()=>Xe(()=>import("./DistributorDetail-COlSGvZy.js"),__vite__mapDeps([89,1,2,90,3,91,41,44,34,59,22,23,30,31,24])),meta:{title:"分销员详情",icon:"User",hidden:!0}},{path:"customers",name:"CustomerManagement",component:()=>Xe(()=>import("./CustomerManagement-DwUSC7Jy.js"),__vite__mapDeps([92,2,1,93,94,3,95,41,67,26,50,36,44,42,22,23,30,31,24,25])),meta:{title:"客户管理",icon:"UserFilled"}}]},{path:"/distributor",component:Qo,redirect:"/distributor/dashboard",name:"Distributor",meta:{title:"分销员工作台",icon:"User",roles:["distributor"]},children:[{path:"dashboard",name:"DistributorDashboard",component:()=>Xe(()=>import("./DistributorDashboard-BgBE05hj.js"),__vite__mapDeps([96,1,2,7,8,9,10,3,97,41,30,31,22,50,49,34,35,24,25,94])),meta:{title:"工作台",icon:"Monitor",roles:["distributor"]}},{path:"group-management",name:"DistributorGroupManagement",component:()=>Xe(()=>import("./GroupManagement-CAyMWdI1.js"),__vite__mapDeps([98,2,1,56,52,53,54,55,57,44,58,21,34,59,45,24,22,23,43,31,25,46,3,99,41,42,30])),meta:{title:"群组管理",icon:"Comment",roles:["distributor"]}},{path:"promotion-links",name:"DistributorPromotionLinks",component:()=>Xe(()=>import("./PromotionLinks-B6Udr01T.js"),__vite__mapDeps([100,2,1,101,3,102,41,44,50,42,22,23,30,31,24,25,26,46,103,104,36])),meta:{title:"推广链接",icon:"Link",roles:["distributor"]}},{path:"commission-logs",name:"DistributorCommissionLogs",component:()=>Xe(()=>import("./CommissionLogs-Db3gssb8.js"),__vite__mapDeps([105,2,1,7,8,3,106,41,36,42,22,23,30,31,107,50,24,34,35,25])),meta:{title:"佣金查看",icon:"Money",roles:["distributor"]}}]},{path:"/owner",component:Qo,redirect:"/owner/dashboard",name:"Owner",meta:{title:"群主工作台",icon:"Comment",roles:["group_owner"]},children:[{path:"dashboard",name:"OwnerDashboard",component:()=>Xe(()=>import("./OwnerDashboard-DjREXJvn.js"),__vite__mapDeps([108,1,2,93,94,7,8,9,10,3,109,67,29,26,30,31,22,44,46,23,49,34,35,24,25])),meta:{title:"群主工作台",icon:"Monitor",roles:["group_owner"]}}]},{path:"/finance",component:Qo,redirect:"/finance/dashboard",name:"Finance",meta:{title:"财务管理",icon:"Money"},children:[{path:"dashboard",name:"FinanceDashboard",component:()=>Xe(()=>import("./FinanceDashboard-TY2uYZgR.js"),__vite__mapDeps([110,1,2,7,8,11,111,53,61,3,112,67,26,22,30,31,25,24])),meta:{title:"财务总览",icon:"DataLine"}},{path:"commission-logs",name:"CommissionLog",component:()=>Xe(()=>import("./CommissionLog-DJp26Fcb.js"),__vite__mapDeps([113,1,2,111,53,90,61,3,114,41,46,42,22,23,30,31,44,50,25,24])),meta:{title:"佣金明细",icon:"Medal"}},{path:"transactions",name:"TransactionList",component:()=>Xe(()=>import("./TransactionList-lZ25UXGY.js"),__vite__mapDeps([115,1,2,111,53,86,87,42,22,23,3,116,41,36,30,31,25,24,44,50])),meta:{title:"交易记录",icon:"Goods"}},{path:"withdraw",name:"WithdrawManage",component:()=>Xe(()=>import("./WithdrawManage-DzYCQgKD.js"),__vite__mapDeps([117,1,2,90,61,3,118,41,26,36,42,22,23,30,31,44,50,46,25,24])),meta:{title:"提现管理",icon:"Upload"}}]},{path:"/user",component:Qo,redirect:"/user/center",name:"User",meta:{title:"用户管理",icon:"User"},children:[{path:"center",name:"UserCenter",component:()=>Xe(()=>import("./UserCenter-CRdrJPs1.js"),__vite__mapDeps([119,2,1,93,94,7,8,120,121,58,21,122,53,3,123,44,34,35,49,24,25,22])),meta:{title:"用户中心",icon:"User"}},{path:"list",name:"UserList",component:()=>Xe(()=>import("./UserList-DrGy0qXH.js"),__vite__mapDeps([124,1,2,3,125,41,42,22,23,30,31,24,44,34,59])),meta:{title:"用户列表",icon:"UserFilled"}},{path:"profile",name:"Profile",component:()=>Xe(()=>import("./Profile-BafzYgsS.js"),__vite__mapDeps([126,120,1,2,121,58,21,3,127,25,24,44,22])),meta:{title:"个人资料",icon:"Avatar"}},{path:"analytics",name:"UserAnalytics",component:()=>Xe(()=>import("./UserAnalytics-D0myNp49.js"),__vite__mapDeps([128,11,1,2,3,129,44,22,23,50,34,35,25,24])),meta:{title:"用户分析",icon:"DataAnalysis"}},{path:"add",name:"UserAdd",component:()=>Xe(()=>import("./UserAdd-8HTfxXmt.js"),__vite__mapDeps([130,1,2,3,131,22,23,34,59,24,44,25])),meta:{title:"添加用户",icon:"Plus"}}]},{path:"/substation",component:Qo,redirect:"/substation/list",name:"Substation",meta:{title:"分站管理",icon:"OfficeBuilding"},children:[{path:"list",name:"SubstationList",component:()=>Xe(()=>import("./SubstationList-DY8d-49p.js"),__vite__mapDeps([132,1,2,133,53,122,86,87,42,22,23,3,134,41,67,29,26,46,30,31,25,44,24])),meta:{title:"分站列表",icon:"List"}},{path:"finance",name:"SubstationFinance",component:()=>Xe(()=>import("./SubstationFinance-CTst0Re9.js"),__vite__mapDeps([135,2,1,93,94,7,8,9,10,133,53,3,136,41,44,22,23,50,42,30,31,24,34,35,25])),meta:{title:"分站财务",icon:"Money"}},{path:"analytics",name:"SubstationAnalytics",component:()=>Xe(()=>import("./SubstationAnalytics-BQ7nSvwF.js"),__vite__mapDeps([137,38,2,1,39,3,138,41,24,42,22,23,30,31])),meta:{title:"分站分析",icon:"DataAnalysis"}}]},{path:"/agent",component:Qo,redirect:"/agent/dashboard",name:"Agent",meta:{title:"代理商管理",icon:"Avatar"},children:[{path:"dashboard",name:"AgentDashboard",component:()=>Xe(()=>import("./AgentDashboard-beFXBMxJ.js"),__vite__mapDeps([139,1,2,93,94,7,8,9,10,140,101,3,141,67,29,26,30,31,22,34,35,24,25])),meta:{title:"代理商工作台",icon:"Monitor"}},{path:"list",name:"AgentList",component:()=>Xe(()=>import("./AgentList-BQ8fvjk9.js"),__vite__mapDeps([142,2,1,93,94,140,3,143,41,50,46,34,59,42,22,23,30,31,24,44,25])),meta:{title:"代理商列表",icon:"List"}},{path:"applications",name:"AgentApplications",component:()=>Xe(()=>import("./AgentApplications-CPaWnJt1.js"),__vite__mapDeps([144,2,1,93,94,140,3,145,41,36,42,22,23,30,31,26,24,44,25])),meta:{title:"申请管理",icon:"Document"}},{path:"hierarchy",name:"AgentHierarchy",component:()=>Xe(()=>import("./AgentHierarchy-5PGpY888.js"),__vite__mapDeps([146,2,1,3,147,41,29,44,22,31,59,23,24,30,148,78,34,35])),meta:{title:"代理商层级",icon:"Connection"}},{path:"performance",name:"AgentPerformance",component:()=>Xe(()=>import("./AgentPerformance-B-YH0X2Y.js"),__vite__mapDeps([149,38,2,1,39,3,150,41,24,42,22,23,30,31])),meta:{title:"绩效分析",icon:"TrendCharts"}}]},{path:"/anti-block",component:Qo,redirect:"/anti-block/dashboard",name:"AntiBlock",meta:{title:"防红系统",icon:"Tools"},children:[{path:"dashboard",name:"AntiBlockDashboard",component:()=>Xe(()=>import("./Dashboard-BXPz0Lwi.js"),__vite__mapDeps([151,152,2,1,3,153,26,44,104,46,22,23,30,31,21,24,25])),meta:{title:"系统概览",icon:"DataLine"}},{path:"enhanced",name:"AntiBlockEnhanced",component:()=>Xe(()=>import("./EnhancedDashboard-BMvFXyI9.js"),__vite__mapDeps([154,2,1,11,3,155,41,36,44,46,24,42,22,23,30,31,21,25])),meta:{title:"增强管理",icon:"Monitor"}},{path:"domains",name:"DomainList",component:()=>Xe(()=>import("./DomainList-CHC0CEE5.js"),__vite__mapDeps([156,1,2,152,86,87,42,22,23,3,157,41,46,34,59,30,31,25,24,44])),meta:{title:"域名管理",icon:"Connection"}},{path:"short-links",name:"ShortLinkList",component:()=>Xe(()=>import("./ShortLinkList-BEKG29v-.js"),__vite__mapDeps([158,152,2,1,3,159,41,26,29,42,22,23,30,31,44,50,24])),meta:{title:"短链接管理",icon:"Link"}},{path:"analytics",name:"AntiBlockAnalytics",component:()=>Xe(()=>import("./Analytics-Co9tHakN.js"),__vite__mapDeps([160,152,2,1,3,161,41,44,46,45,30,31,22,21,24,34,35,25,50])),meta:{title:"统计分析",icon:"DataAnalysis"}}]},{path:"/permission",component:Qo,redirect:"/permission/roles",name:"Permission",meta:{title:"权限管理",icon:"Lock"},children:[{path:"roles",name:"RoleManagement",component:()=>Xe(()=>import("./RoleManagement-DL_jeR3P.js"),__vite__mapDeps([162,1,2,3,163,41,42,22,23,30,31,24,44,34,59,148,78])),meta:{title:"角色管理",icon:"UserFilled"}},{path:"permissions",name:"PermissionManagement",component:()=>Xe(()=>import("./PermissionManagement-C8MSMQ5l.js"),__vite__mapDeps([164,38,2,1,39,3,165,41,24,42,22,23,30,31])),meta:{title:"权限配置",icon:"Key"}}]},{path:"/promotion",component:Qo,redirect:"/promotion/links",name:"Promotion",meta:{title:"推广管理",icon:"Share"},children:[{path:"links",name:"PromotionLinks",component:()=>Xe(()=>import("./LinkManagement-Da1w7byV.js"),__vite__mapDeps([166,1,2,93,94,167,61,3,168,41,24,42,22,23,30,31,107,25,44,34,59,46,50,35,26,43])),meta:{title:"推广链接",icon:"Link"}},{path:"landing-pages",name:"LandingPages",component:()=>Xe(()=>import("./LandingPages-HgxUj6Ac.js"),__vite__mapDeps([169,1,2,167,61,3,170,41,42,22,23,30,31,34,35,24,50,25,44,45,46,103,59,43,21])),meta:{title:"落地页管理",icon:"Document"}},{path:"analytics",name:"PromotionAnalytics",component:()=>Xe(()=>import("./Analytics-CWzSItOu.js"),__vite__mapDeps([171,38,2,1,39,3,172,41,24,42,22,23,30,31])),meta:{title:"推广分析",icon:"DataAnalysis"}}]},{path:"/orders",component:Qo,redirect:"/orders/list",name:"Orders",meta:{title:"订单管理",icon:"Tickets"},children:[{path:"list",name:"OrderList",component:()=>Xe(()=>import("./OrderList-lH8zHgmD.js"),__vite__mapDeps([173,1,2,93,94,61,3,174,41,24,42,22,23,30,31,25,50,36,26,44,34,59,46,63])),meta:{title:"订单列表",icon:"List"}},{path:"analytics",name:"OrderAnalytics",component:()=>Xe(()=>import("./OrderAnalytics-JJLEbY_q.js"),__vite__mapDeps([175,38,2,1,39,3,176,41,24,42,22,23,30,31])),meta:{title:"订单分析",icon:"DataAnalysis"}},{path:"detail/:id",name:"OrderDetail",component:()=>Xe(()=>import("./OrderDetail-DBHrLlIK.js"),__vite__mapDeps([177,1,2,38,39,3,178,63,30,31,22,24,25])),meta:{title:"订单详情",icon:"View",hidden:!0}}]},{path:"/system",component:Qo,redirect:"/system/settings",name:"System",meta:{title:"系统管理",icon:"Setting"},children:[{path:"settings",name:"SystemSettings",component:()=>Xe(()=>import("./Settings-TS2r7zta.js"),__vite__mapDeps([179,1,2,64,65,58,21,61,3,180,25,46,43,31,45,107,44,24,41,42,22,23,30,50,181])),meta:{title:"系统设置",icon:"Tools"}},{path:"export",name:"DataExport",component:()=>Xe(()=>import("./DataExport-lcVYex3I.js"),__vite__mapDeps([182,90,2,1,3,183,45,181,42,22,23,30,31,21,44,43,46,50,34,59,24,25])),meta:{title:"数据导出",icon:"Download"}},{path:"notifications",name:"SystemNotifications",component:()=>Xe(()=>import("./Notifications-CkmmtTak.js"),__vite__mapDeps([184,11,1,2,3,185,44,50,43,31,34,59,42,22,23,30,25,24])),meta:{title:"通知管理",icon:"Bell"}},{path:"operation-logs",name:"OperationLogs",component:()=>Xe(()=>import("./OperationLogs-CMX9wsE0.js"),__vite__mapDeps([186,11,1,2,3,187,41,46,34,59,26,36,42,22,23,30,31,44,50,25,24,29])),meta:{title:"操作日志",icon:"Document"}},{path:"function-test",name:"FunctionTest",component:()=>Xe(()=>import("./FunctionTest-CwLNuy48.js"),__vite__mapDeps([188,2,1,3,189,25,22,23,24,26])),meta:{title:"功能测试",icon:"Cpu"}},{path:"user-guide",name:"UserGuide",component:()=>Xe(()=>import("./UserGuide-pa1A2KqB.js"),__vite__mapDeps([190,2,1,3,191,67,26,22,24,25])),meta:{title:"使用指南",icon:"InfoFilled"}},{path:"file-management",name:"FileManagement",component:()=>Xe(()=>import("./FileManagement-DlQl42nE.js"),__vite__mapDeps([192,38,2,1,39,3,193,41,24,42,22,23,30,31])),meta:{title:"文件管理",icon:"Folder"}}]},{path:"/payment",component:Qo,redirect:"/payment/settings",name:"Payment",meta:{title:"支付管理",icon:"CreditCard"},children:[{path:"settings",name:"PaymentSettings",component:()=>Xe(()=>import("./PaymentSettings-Bn2FAfcI.js"),__vite__mapDeps([194,2,1,38,39,195,3,196,25,22,24,29,46,43,31,58,21,44,23,45])),meta:{title:"支付设置",icon:"Setting"}},{path:"orders",name:"PaymentOrders",component:()=>Xe(()=>import("./PaymentOrders-CYBaaLwO.js"),__vite__mapDeps([197,1,2,38,39,195,3,198,41,42,22,23,30,31,24,50,25,36,44,46])),meta:{title:"支付订单",icon:"Tickets"}},{path:"refunds",name:"PaymentRefunds",component:()=>Xe(()=>import("./PaymentRefunds--OptUzKN.js"),__vite__mapDeps([199,2,1,38,39,195,3,200,41,36,42,22,23,30,31,24,44,50])),meta:{title:"退款管理",icon:"RefreshLeft"}}]},{path:"/security",component:Qo,redirect:"/security/management",name:"Security",meta:{title:"安全管理",icon:"Lock"},children:[{path:"management",name:"SecurityManagement",component:()=>Xe(()=>import("./SecurityManagement-CGdTK9uD.js"),__vite__mapDeps([37,2,1,38,39,3,40,41,42,22,23,30,31,24,29,43,44,45,46,25])),meta:{title:"安全管理",icon:"Shield"}}]},{path:"/error/:type?",name:"ErrorPage",component:()=>Xe(()=>import("./ErrorPage-KpwDoSeT.js"),__vite__mapDeps([201,1,2,3,202,67])),meta:{title:"错误页面",hidden:!0}},{path:"/403",redirect:"/error/403"},{path:"/:pathMatch(.*)*",name:"NotFound",redirect:"/error/404"}],an=D({history:L("/admin/"),routes:nn});an.beforeEach(async(e,t,o)=>{I.start();const n=lt(),i=n.token;if("true"===new URLSearchParams(window.location.search).get("preview")||"true"===localStorage.getItem("preview-mode")){console.log("🎭 启用预览模式：",e.path);const t=`preview-mode-token-${Date.now()}`;let i;n.setToken(t);const s=localStorage.getItem("preview-user-info"),r=localStorage.getItem("current-test-role");if(s)try{i=JSON.parse(s),console.log("🔄 从localStorage恢复用户信息:",i)}catch(a){i=null}if(!i){const e=r||"admin";i={id:"preview-user",username:"admin",nickname:`${gt(e)} (预览)`,name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:e,roles:[e],permissions:"admin"===e?["*"]:[e]},console.log("🆕 使用默认用户信息:",i)}return n.setUserInfo(i),localStorage.setItem("preview-mode","true"),console.log("✅ 预览模式用户信息已设置:",i),void o()}if(i&&i.startsWith("preview-mode-token-"))return console.log("🎭 预览模式路由守卫：允许访问",e.path),void o();if(i)if("/login"===e.path)o({path:"/dashboard"});else{if(!n.userInfo)try{await n.getUserInfo()}catch(a){return await n.logout(),void o(`/login?redirect=${e.path}`)}o()}else"/login"!==e.path?o(`/login?redirect=${e.path}`):o()}),an.afterEach(()=>{I.done()});const sn=T.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});sn.interceptors.request.use(e=>{const t=lt().token;return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求错误:",e),Promise.reject(e))),sn.interceptors.response.use(e=>{const{data:t}=e;return void 0!==t.code?200===t.code||0===t.code?t:(C.error(t.message||"请求失败"),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{if(console.error("响应错误:",e),e.response){const{status:t,data:o}=e.response;switch(t){case 401:C.error("登录已过期，请重新登录");lt().logout(),an.push("/login");break;case 403:C.error("没有权限访问");break;case 404:C.error("请求的资源不存在");break;case 422:const e=o.errors;if(e){const t=Object.values(e)[0];C.error(Array.isArray(t)?t[0]:t)}else C.error(o.message||"请求参数错误");break;case 500:C.error("服务器内部错误");break;default:C.error(o.message||`请求失败 (${t})`)}}else e.request?C.error("网络连接失败，请检查网络"):C.error("请求配置错误");return Promise.reject(e)});const rn={install(e){e.config.globalProperties.$api=sn,e.provide("api",sn),console.log("✅ API插件已安装")}},ln={mounted(e,t){const{value:o}=t,n=lt(),i=n.userInfo?.role;if(o&&o.length>0){(Array.isArray(o)?o.includes(i):o===i)||(e.style.display="none")}},updated(e,t){const{value:o}=t,n=lt(),i=n.userInfo?.role;if(o&&o.length>0){const t=Array.isArray(o)?o.includes(i):o===i;e.style.display=t?"":"none"}}},cn={mounted(e,t){const{value:o}=t,n=lt(),i=n.userInfo?.permissions||[];if(o&&o.length>0){(Array.isArray(o)?o.some(e=>i.includes(e)||i.includes("*")):i.includes(o)||i.includes("*"))||(e.style.display="none")}},updated(e,t){const{value:o}=t,n=lt(),i=n.userInfo?.permissions||[];if(o&&o.length>0){const t=Array.isArray(o)?o.some(e=>i.includes(e)||i.includes("*")):i.includes(o)||i.includes("*");e.style.display=t?"":"none"}}};window.addEventListener("error",e=>{(e.error?.message?.includes("QBMiniVideo")||e.error?.message?.includes("jstProcess")||e.error?.message?.includes("__jstcache"))&&e.preventDefault()});try{localStorage.setItem("__test__","test"),localStorage.removeItem("__test__")}catch(mn){}const dn="true"===new URLSearchParams(window.location.search).get("preview")||"true"===localStorage.getItem("preview-mode");dn&&(console.log("🎭 预览模式已启用"),localStorage.setItem("preview-mode","true"));const un=A(Ze);for(const[pn,hn]of Object.entries(Je))un.component(pn,hn);if(un.use(P()),un.use(an),un.use(rn),function(e){e.directive("role",ln),e.directive("permission",cn),console.log("✅ 权限指令已安装")}(un),!dn)try{Xe(async()=>{const{sessionManager:e,logSession:t}=await Promise.resolve().then(()=>on);return{sessionManager:e,logSession:t}},void 0).then(({sessionManager:e,logSession:t})=>{Xe(async()=>{const{useUserStore:e}=await Promise.resolve().then(()=>ct);return{useUserStore:e}},void 0).then(({useUserStore:o})=>{window.addEventListener("sessionExpired",()=>{const n=o();C.warning({message:"会话已过期，请重新登录",duration:5e3}),t(n.userInfo?.username||"unknown","session_expired",e.getCurrentSession()?.id,"自动会话过期检测"),n.logout().then(()=>{an.push("/login")})}),document.addEventListener("visibilitychange",()=>{!document.hidden&&e.getCurrentSession()&&e.updateActivity()});let n=null;const i=()=>{n||(n=setTimeout(()=>{e.getCurrentSession()&&e.updateActivity(),n=null},3e4))};["mousedown","mousemove","keypress","scroll","touchstart","click"].forEach(e=>{document.addEventListener(e,i,{passive:!0})})})})}catch(mn){}un.config.errorHandler=(e,t,o)=>{console.error("Vue应用错误:",e,o),C.error("应用出现错误，请刷新页面重试")},window.addEventListener("unhandledrejection",e=>{console.error("未处理的Promise错误:",e.reason),e.preventDefault()});try{un.mount("#app"),console.log("✅ 应用挂载成功")}catch(mn){console.error("❌ 应用挂载失败:",mn)}export{bt as _,Xe as a,en as b,so as c,co as d,uo as e,ht as f,gt as g,po as h,dt as i,ut as j,tt as k,tn as l,sn as m,it as n,at as o,st as p,rt as q,an as r,Zo as s,pt as t,lt as u,mo as v,ft as w};
