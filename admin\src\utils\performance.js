// 性能监控和错误处理服务
// admin/src/utils/performance.js

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isBrowser = typeof window !== 'undefined'
    if (this.isBrowser) {
      this.init()
    }
  }

  init() {
    if (!this.isBrowser) return

    // 监听页面加载性能
    if (window.performance) {
      this.observePageLoad()
      this.observeResourceTiming()
      this.observeLongTasks()
    }

    // 监听内存使用
    this.observeMemoryUsage()
    
    // 监听网络状态
    this.observeNetworkStatus()
  }

  // 页面加载性能监控
  observePageLoad() {
    if (!this.isBrowser) return
    
    window.addEventListener('load', () => {
      setTimeout(() => {
        const timing = window.performance.timing
        const metrics = {
          // DNS查询时间
          dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
          // TCP连接时间
          tcpTime: timing.connectEnd - timing.connectStart,
          // 请求时间
          requestTime: timing.responseEnd - timing.requestStart,
          // 解析DOM时间
          domTime: timing.domComplete - timing.domLoading,
          // 白屏时间
          whiteScreenTime: timing.responseStart - timing.navigationStart,
          // 首屏时间
          firstScreenTime: timing.loadEventEnd - timing.navigationStart,
          // 页面完全加载时间
          loadTime: timing.loadEventEnd - timing.navigationStart
        }
        
        this.recordMetric('pageLoad', metrics)
        this.reportMetrics('pageLoad', metrics)
      }, 0)
    })
  }

  // 资源加载性能监控
  observeResourceTiming() {
    if (!this.isBrowser || !window.PerformanceObserver) return
    
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.duration > 1000) { // 超过1秒的资源加载
          this.recordMetric('slowResource', {
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize,
            type: entry.initiatorType
          })
        }
      })
    })
    
    observer.observe({ entryTypes: ['resource'] })
    this.observers.push(observer)
  }

  // 长任务监控
  observeLongTasks() {
    if (!this.isBrowser || !window.PerformanceObserver) return
    
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        this.recordMetric('longTask', {
          duration: entry.duration,
          startTime: entry.startTime,
          name: entry.name
        })
      })
    })
    
    try {
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    } catch (e) {
      console.warn('Long task monitoring not supported')
    }
  }

  // 内存使用监控
  observeMemoryUsage() {
    if (!this.isBrowser || !window.performance || !('memory' in window.performance)) return
    
    setInterval(() => {
      const memory = window.performance.memory
      const memoryInfo = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      }
      
      this.recordMetric('memory', memoryInfo)
      
      // 内存使用过高警告
      if (memoryInfo.usage > 90) {
        this.reportMetrics('memoryWarning', memoryInfo)
      }
    }, 30000) // 30秒检查一次
  }

  // 网络状态监控
  observeNetworkStatus() {
    if (!this.isBrowser || !navigator || !('connection' in navigator)) return
    
    const connection = navigator.connection
    
    const updateNetworkInfo = () => {
      const networkInfo = {
        type: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
      
      this.recordMetric('network', networkInfo)
    }
    
    updateNetworkInfo()
    connection.addEventListener('change', updateNetworkInfo)
  }

  // API请求性能监控
  monitorApiRequest(url, startTime, endTime, status, size) {
    const duration = endTime - startTime
    const metric = {
      url,
      duration,
      status,
      size,
      timestamp: Date.now()
    }
    
    this.recordMetric('apiRequest', metric)
    
    // 慢请求报告
    if (duration > 3000) {
      this.reportMetrics('slowApi', metric)
    }
  }

  // 组件渲染性能监控
  monitorComponentRender(componentName, renderTime) {
    const metric = {
      component: componentName,
      renderTime,
      timestamp: Date.now()
    }
    
    this.recordMetric('componentRender', metric)
    
    // 渲染时间过长警告
    if (renderTime > 100) {
      this.reportMetrics('slowRender', metric)
    }
  }

  // 记录指标
  recordMetric(type, data) {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, [])
    }
    
    const metrics = this.metrics.get(type)
    metrics.push({
      ...data,
      timestamp: Date.now()
    })
    
    // 只保留最近的100条记录
    if (metrics.length > 100) {
      metrics.shift()
    }
  }

  // 获取指标
  getMetrics(type) {
    return this.metrics.get(type) || []
  }

  // 获取性能报告
  getPerformanceReport() {
    const report = {}
    
    for (const [type, metrics] of this.metrics) {
      if (metrics.length > 0) {
        report[type] = {
          count: metrics.length,
          latest: metrics[metrics.length - 1],
          average: this.calculateAverage(metrics, 'duration') || 0
        }
      }
    }
    
    return report
  }

  // 计算平均值
  calculateAverage(metrics, field) {
    if (!metrics.length) return 0
    
    const values = metrics.map(m => m[field]).filter(v => typeof v === 'number')
    if (!values.length) return 0
    
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  // 上报指标
  reportMetrics(type, data) {
    if (!this.isBrowser) return
    
    // 只在生产环境上报
    if (process.env.NODE_ENV === 'production') {
      // 发送到监控系统
      fetch('/api/v1/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          data,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: Date.now()
        })
      }).catch(err => {
        console.warn('Failed to report metrics:', err)
      })
    } else {
      console.log(`[Performance] ${type}:`, data)
    }
  }

  // 清理资源
  destroy() {
    if (!this.isBrowser) return
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

/**
 * 错误监控工具
 */
export class ErrorMonitor {
  constructor() {
    this.errors = []
    this.isBrowser = typeof window !== 'undefined'
    if (this.isBrowser) {
      this.init()
    }
  }

  init() {
    if (!this.isBrowser) return
    
    // 监听JavaScript错误
    window.addEventListener('error', this.handleError.bind(this))
    
    // 监听Promise rejection
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this))
    
    // 监听Vue错误
    this.setupVueErrorHandler()
  }

  handleError(event) {
    if (!this.isBrowser) return
    
    const error = {
      type: 'javascript',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error ? event.error.stack : '',
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }
    
    this.recordError(error)
    this.reportError(error)
  }

  handlePromiseRejection(event) {
    if (!this.isBrowser) return
    
    const error = {
      type: 'promise',
      message: event.reason.message || event.reason,
      stack: event.reason.stack || '',
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }
    
    this.recordError(error)
    this.reportError(error)
  }

  setupVueErrorHandler() {
    if (!this.isBrowser) return
    
    // 在Vue应用中设置错误处理器
    if (window.Vue) {
      window.Vue.config.errorHandler = (err, vm, info) => {
        const error = {
          type: 'vue',
          message: err.message,
          stack: err.stack,
          componentInfo: info,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent
        }
        
        this.recordError(error)
        this.reportError(error)
      }
    }
  }

  // 记录错误
  recordError(error) {
    this.errors.push(error)
    
    // 只保留最近的50个错误
    if (this.errors.length > 50) {
      this.errors.shift()
    }
  }

  // 获取错误列表
  getErrors() {
    return this.errors
  }

  // 上报错误
  reportError(error) {
    if (!this.isBrowser) return
    
    // 过滤掉一些不重要的错误
    if (this.shouldIgnoreError(error)) {
      return
    }
    
    if (process.env.NODE_ENV === 'production') {
      // 发送到错误监控系统
      fetch('/api/v1/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(error)
      }).catch(err => {
        console.warn('Failed to report error:', err)
      })
    } else {
      console.error('[Error Monitor]', error)
    }
  }

  // 判断是否应该忽略错误
  shouldIgnoreError(error) {
    const ignoreMessages = [
      'Script error',
      'Non-Error promise rejection captured',
      'ResizeObserver loop limit exceeded',
      'Network request failed'
    ]
    
    return ignoreMessages.some(msg => 
      error.message && error.message.includes(msg)
    )
  }

  // 清理错误记录
  clearErrors() {
    this.errors = []
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const errorMonitor = new ErrorMonitor()

// Vue插件
export const PerformancePlugin = {
  install(app) {
    // 添加全局属性
    app.config.globalProperties.$performance = performanceMonitor
    app.config.globalProperties.$errorMonitor = errorMonitor
    
    // 监听路由变化
    app.mixin({
      beforeRouteEnter(to, from, next) {
        const startTime = Date.now()
        next(vm => {
          const endTime = Date.now()
          performanceMonitor.monitorApiRequest(
            `route:${to.path}`,
            startTime,
            endTime,
            200,
            0
          )
        })
      }
    })
    
    // 组件渲染性能监控
    app.mixin({
      beforeMount() {
        this._renderStart = Date.now()
      },
      mounted() {
        if (this._renderStart) {
          const renderTime = Date.now() - this._renderStart
          performanceMonitor.monitorComponentRender(
            this.$options.name || 'Anonymous',
            renderTime
          )
        }
      }
    })
  }
}

// 导出默认配置
export default {
  performanceMonitor,
  errorMonitor,
  PerformancePlugin
}