<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 域名池表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('domain_pools', function (Blueprint $table) {
            $table->id();
            
            // 基础信息
            $table->string('name', 100)->comment('域名池名称');
            $table->text('description')->nullable()->comment('描述');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            
            // 域名列表 (JSON)
            $table->json('domains')->comment('域名列表');
            
            // 检测配置
            $table->integer('check_interval')->default(10)->comment('检测间隔(分钟)');
            $table->string('check_method', 20)->default('http')->comment('检测方式');
            $table->integer('timeout')->default(10)->comment('超时时间(秒)');
            $table->integer('retry_times')->default(3)->comment('重试次数');
            
            // 切换策略
            $table->enum('switch_strategy', ['immediate', 'delayed', 'manual'])->default('immediate')->comment('切换策略');
            $table->integer('switch_delay')->default(0)->comment('切换延迟(分钟)');
            
            // 统计数据
            $table->integer('total_domains')->default(0)->comment('总域名数');
            $table->integer('active_domains')->default(0)->comment('可用域名数');
            $table->integer('blocked_domains')->default(0)->comment('被封域名数');
            $table->timestamp('last_check_at')->nullable()->comment('最后检测时间');
            
            // 使用统计
            $table->integer('group_count')->default(0)->comment('使用群组数');
            $table->integer('switch_count')->default(0)->comment('切换次数');
            
            // 创建者
            $table->unsignedBigInteger('creator_id')->comment('创建者ID');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index(['status', 'created_at']);
            $table->index('creator_id');
            $table->index('last_check_at');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_pools');
    }
};