<template>
  <el-dialog
    v-model="visible"
    title="申请退款"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="refundForm"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="订单号">
        <el-input v-model="order.order_no" disabled />
      </el-form-item>
      
      <el-form-item label="订单金额">
        <el-input v-model="orderAmount" disabled />
      </el-form-item>
      
      <el-form-item label="退款金额" prop="amount">
        <el-input-number
          v-model="refundForm.amount"
          :min="0.01"
          :max="order.amount"
          :precision="2"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="退款原因" prop="reason">
        <el-select v-model="refundForm.reason" placeholder="请选择退款原因" style="width: 100%">
          <el-option label="用户申请退款" value="user_request" />
          <el-option label="商品质量问题" value="quality_issue" />
          <el-option label="服务问题" value="service_issue" />
          <el-option label="系统错误" value="system_error" />
          <el-option label="其他原因" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="退款说明" prop="description">
        <el-input
          v-model="refundForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入退款说明..."
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认退款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  order: Object
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const formRef = ref()
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const orderAmount = computed(() => {
  return props.order?.amount ? `¥${props.order.amount.toFixed(2)}` : '¥0.00'
})

const refundForm = reactive({
  amount: 0,
  reason: '',
  description: ''
})

const rules = {
  amount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '退款金额必须大于0.01', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请选择退款原因', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入退款说明', trigger: 'blur' },
    { min: 10, message: '退款说明至少10个字符', trigger: 'blur' }
  ]
}

watch(() => props.order, (newOrder) => {
  if (newOrder) {
    refundForm.amount = newOrder.amount || 0
    refundForm.reason = ''
    refundForm.description = ''
  }
}, { immediate: true })

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    emit('confirm', {
      ...refundForm,
      order_id: props.order.id
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>