<template>
  <div class="metrics-grid">
    <div class="metric-card" v-for="metric in metrics" :key="metric.key">
      <div class="metric-icon">
        <el-icon :size="32" :color="metric.color">
          <component :is="metric.icon" />
        </el-icon>
      </div>
      <div class="metric-content">
        <div class="metric-value">{{ metric.value }}</div>
        <div class="metric-label">{{ metric.label }}</div>
        <div class="metric-change" :class="metric.changeType">
          <el-icon :size="12">
            <component :is="metric.changeType === 'increase' ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
          {{ metric.change }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  User, 
  Link, 
  Money, 
  TrendCharts,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const metrics = computed(() => [
  {
    key: 'users',
    label: '总用户数',
    value: props.data.totalUsers || '12,345',
    change: '+12.5%',
    changeType: 'increase',
    icon: User,
    color: '#409eff'
  },
  {
    key: 'links',
    label: '短链接数',
    value: props.data.totalLinks || '8,976',
    change: '+8.2%',
    changeType: 'increase',
    icon: Link,
    color: '#67c23a'
  },
  {
    key: 'revenue',
    label: '今日收入',
    value: props.data.todayRevenue || '¥23,456',
    change: '+15.3%',
    changeType: 'increase',
    icon: Money,
    color: '#e6a23c'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: props.data.conversionRate || '3.2%',
    change: '-0.5%',
    changeType: 'decrease',
    icon: TrendCharts,
    color: '#f56c6c'
  }
])
</script>

<style lang="scss" scoped>
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.metric-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;

  &.increase {
    color: #67c23a;
  }

  &.decrease {
    color: #f56c6c;
  }
}
</style>
