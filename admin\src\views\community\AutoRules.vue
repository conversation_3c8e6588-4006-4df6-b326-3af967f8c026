<template>
  <div class="app-container">
    <PageLayout>
      <template #header>
        <div class="page-header">
          <h1>
            <el-icon><Setting /></el-icon>
            自动化规则管理
          </h1>
          <p>创建和管理社群的自动化规则，提高管理效率。</p>
        </div>
      </template>

      <div class="toolbar-container">
        <el-button type="primary" icon="Plus" @click="handleCreateRule">
          创建新规则
        </el-button>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索规则名称"
          style="width: 250px;"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button icon="Search" @click="handleSearch" />
          </template>
        </el-input>
      </div>

      <el-card class="rules-list-card">
        <template #header>
          <div class="card-header">
            <span>规则列表</span>
          </div>
        </template>
        <el-table :data="filteredRules" v-loading="loading" style="width: 100%">
          <el-table-column prop="name" label="规则名称" width="200" />
          <el-table-column label="规则类型" width="180">
            <template #default="{ row }">
              <el-tag :type="getRuleTypeInfo(row.type).type">
                {{ getRuleTypeInfo(row.type).text }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="目标群组">
            <template #default="{ row }">
              <span v-if="row.target_group_ids === 'all'">所有群组</span>
              <span v-else>{{ row.target_group_ids.length }} 个群组</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="handleEditRule(row)">编辑</el-button>
              <el-button link type="danger" size="small" @click="handleDeleteRule(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </PageLayout>

    <!-- 创建/编辑规则对话框 -->
    <RuleEditDialog
      v-model="dialogVisible"
      :rule-data="currentRule"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Plus, Search } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import RuleEditDialog from './components/RuleEditDialog.vue' // 需要创建的子组件
import { formatDate } from '@/utils/format'

const loading = ref(false)
const dialogVisible = ref(false)
const searchKeyword = ref('')
const rules = ref([])
const currentRule = ref(null)

const filteredRules = computed(() => {
  if (!searchKeyword.value) {
    return rules.value
  }
  return rules.value.filter(rule =>
    rule.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const getRuleTypeInfo = (type) => {
  const map = {
    keyword_reply: { text: '关键词自动回复', type: 'primary' },
    new_member_welcome: { text: '新成员欢迎语', type: 'success' },
    auto_kick: { text: '违规自动踢人', type: 'danger' },
    scheduled_message: { text: '定时发送消息', type: 'warning' },
  }
  return map[type] || { text: '未知类型', type: 'info' }
}

const fetchRules = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    rules.value = [
      { id: 1, name: '回复"入群"关键词', type: 'keyword_reply', target_group_ids: 'all', enabled: true, created_at: '2024-05-20T10:00:00Z', config: { keyword: '入群', reply_content: '欢迎加入，请先阅读群规！' } },
      { id: 2, name: '新成员进群欢迎', type: 'new_member_welcome', target_group_ids: [101, 102], enabled: true, created_at: '2024-05-21T11:30:00Z', config: { message: '欢迎新朋友 @{memberName}！' } },
      { id: 3, name: '禁止发送广告链接', type: 'auto_kick', target_group_ids: 'all', enabled: false, created_at: '2024-05-22T14:00:00Z', config: { kick_reason: '发送广告链接', trigger: 'contains_url' } },
      { id: 4, name: '每日晚报', type: 'scheduled_message', target_group_ids: [101], enabled: true, created_at: '2024-05-23T18:00:00Z', config: { cron: '0 20 * * *', message: '【每日晚报】...' } },
    ]
    loading.value = false
  }, 500)
}

const handleSearch = () => {
  // 前端筛选，无需额外操作
}

const handleCreateRule = () => {
  currentRule.value = null
  dialogVisible.value = true
}

const handleEditRule = (rule) => {
  currentRule.value = JSON.parse(JSON.stringify(rule))
  dialogVisible.value = true
}

const handleDeleteRule = (rule) => {
  ElMessageBox.confirm(`确定要删除规则 "${rule.name}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 调用API删除
    rules.value = rules.value.filter(r => r.id !== rule.id)
    ElMessage.success('规则删除成功')
  }).catch(() => {})
}

const handleStatusChange = (rule) => {
  // 调用API更新状态
  ElMessage.success(`规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`)
}

const handleSuccess = (newRuleData) => {
  if (currentRule.value) {
    // 编辑
    const index = rules.value.findIndex(r => r.id === newRuleData.id)
    if (index !== -1) {
      rules.value[index] = newRuleData
    }
  } else {
    // 创建
    rules.value.unshift({ ...newRuleData, id: Date.now() }) // 模拟ID
  }
  fetchRules() // 重新加载数据
}

onMounted(() => {
  fetchRules()
})
</script>

<style lang="scss" scoped>
.page-header {
  h1 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }
  p {
    color: #64748b;
    font-size: 14px;
    margin-top: 0;
  }
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rules-list-card {
  .card-header {
    font-weight: 600;
  }
}
</style>