/* 响应式断点系统 - 移动优先 */

/* 基础变量定义 */
:root {
  /* 断点值 */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* 响应式间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* 响应式字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}

/* 响应式工具类 - 移动优先 */

/* 超小屏幕 (xs < 576px) - 默认样式 */
.container {
  width: 100%;
  padding-left: var(--spacing-sm);
  padding-right: var(--spacing-sm);
  margin-left: auto;
  margin-right: auto;
}

/* 小屏幕 (sm ≥ 576px) */
@media (min-width: 576px) {
  .container {
    max-width: var(--container-sm);
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }
  
  .grid-sm-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-sm-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-sm-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-sm-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  
  .hidden-sm { display: none !important; }
  .block-sm { display: block !important; }
  .flex-sm { display: flex !important; }
  
  .text-sm-sm { font-size: var(--text-sm); }
  .text-sm-base { font-size: var(--text-base); }
  .text-sm-lg { font-size: var(--text-lg); }
}

/* 中等屏幕 (md ≥ 768px) */
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
  
  .grid-md-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-md-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-md-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-md-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-md-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .hidden-md { display: none !important; }
  .block-md { display: block !important; }
  .flex-md { display: flex !important; }
  
  .text-md-sm { font-size: var(--text-sm); }
  .text-md-base { font-size: var(--text-base); }
  .text-md-lg { font-size: var(--text-lg); }
  .text-md-xl { font-size: var(--text-xl); }
}

/* 大屏幕 (lg ≥ 992px) */
@media (min-width: 992px) {
  .container {
    max-width: var(--container-lg);
  }
  
  .grid-lg-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-lg-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-lg-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-lg-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-lg-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .grid-lg-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .hidden-lg { display: none !important; }
  .block-lg { display: block !important; }
  .flex-lg { display: flex !important; }
  
  .text-lg-sm { font-size: var(--text-sm); }
  .text-lg-base { font-size: var(--text-base); }
  .text-lg-lg { font-size: var(--text-lg); }
  .text-lg-xl { font-size: var(--text-xl); }
  .text-lg-2xl { font-size: var(--text-2xl); }
}

/* 超大屏幕 (xl ≥ 1200px) */
@media (min-width: 1200px) {
  .container {
    max-width: var(--container-xl);
  }
  
  .grid-xl-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-xl-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-xl-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-xl-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-xl-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .grid-xl-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .hidden-xl { display: none !important; }
  .block-xl { display: block !important; }
  .flex-xl { display: flex !important; }
  
  .text-xl-sm { font-size: var(--text-sm); }
  .text-xl-base { font-size: var(--text-base); }
  .text-xl-lg { font-size: var(--text-lg); }
  .text-xl-xl { font-size: var(--text-xl); }
  .text-xl-2xl { font-size: var(--text-2xl); }
  .text-xl-3xl { font-size: var(--text-3xl); }
}

/* 超大屏幕 (xxl ≥ 1400px) */
@media (min-width: 1400px) {
  .container {
    max-width: var(--container-xxl);
  }
  
  .grid-xxl-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-xxl-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-xxl-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-xxl-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-xxl-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .grid-xxl-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  
  .hidden-xxl { display: none !important; }
  .block-xxl { display: block !important; }
  .flex-xxl { display: flex !important; }
  
  .text-xxl-sm { font-size: var(--text-sm); }
  .text-xxl-base { font-size: var(--text-base); }
  .text-xxl-lg { font-size: var(--text-lg); }
  .text-xxl-xl { font-size: var(--text-xl); }
  .text-xxl-2xl { font-size: var(--text-2xl); }
  .text-xxl-3xl { font-size: var(--text-3xl); }
  .text-xxl-4xl { font-size: var(--text-4xl); }
}

/* 容器查询支持 */
@container (min-width: 300px) {
  .container-responsive {
    padding: var(--spacing-sm);
  }
}

@container (min-width: 500px) {
  .container-responsive {
    padding: var(--spacing-md);
  }
}

@container (min-width: 700px) {
  .container-responsive {
    padding: var(--spacing-lg);
  }
}

/* 响应式间距工具类 */
.spacing-xs { margin: var(--spacing-xs); }
.spacing-sm { margin: var(--spacing-sm); }
.spacing-md { margin: var(--spacing-md); }
.spacing-lg { margin: var(--spacing-lg); }
.spacing-xl { margin: var(--spacing-xl); }
.spacing-xxl { margin: var(--spacing-xxl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }
.p-xxl { padding: var(--spacing-xxl); }

/* 响应式布局工具 */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* 响应式隐藏工具 */
@media (max-width: 575.98px) {
  .hidden-xs-down { display: none !important; }
}

@media (max-width: 767.98px) {
  .hidden-sm-down { display: none !important; }
}

@media (max-width: 991.98px) {
  .hidden-md-down { display: none !important; }
}

@media (max-width: 1199.98px) {
  .hidden-lg-down { display: none !important; }
}

@media (max-width: 1399.98px) {
  .hidden-xl-down { display: none !important; }
}