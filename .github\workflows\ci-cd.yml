name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PHP_VERSION: '8.2'
  NODE_VERSION: '18'
  MYSQL_VERSION: '8.0'
  REDIS_VERSION: '7'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: ffjq_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: pdo, pdo_mysql, redis, bcmath, gd, zip, intl, mbstring
        coverage: xdebug
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Cache PHP dependencies
      uses: actions/cache@v3
      with:
        path: ~/.composer/cache/files
        key: dependencies-php-${{ hashFiles('composer.lock') }}
        restore-keys: dependencies-php-
    
    - name: Install PHP dependencies
      run: composer install --prefer-dist --no-progress --no-suggest
    
    - name: Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate
        php artisan config:cache
    
    - name: Run database migrations
      run: php artisan migrate --seed
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: ffjq_test
        DB_USERNAME: root
        DB_PASSWORD: root
    
    - name: Install frontend dependencies
      run: |
        cd admin && npm install
        cd ../frontend && npm install
    
    - name: Build frontend assets
      run: |
        cd admin && npm run build
        cd ../frontend && npm run build
    
    - name: Run PHP tests
      run: |
        vendor/bin/phpunit --coverage-text --coverage-clover=coverage.xml
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: ffjq_test
        DB_USERNAME: root
        DB_PASSWORD: root
    
    - name: Run code analysis
      run: |
        vendor/bin/phpstan analyse --memory-limit=2G
        vendor/bin/psalm --show-info=true
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: true

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Run security audit
      run: |
        composer audit
        vendor/bin/enlightn
    
    - name: Run SAST scan
      uses: github/super-linter@v4
      env:
        DEFAULT_BRANCH: main
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_ALL_CODEBASE: false
        VALIDATE_PHP: true
        VALIDATE_JAVASCRIPT_ES: true
        VALIDATE_CSS: true

  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/ffjq:latest
          ${{ secrets.DOCKER_USERNAME }}/ffjq:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to staging
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /var/www/ffjq
          git pull origin main
          docker-compose down
          docker-compose pull
          docker-compose up -d
          docker-compose exec -T app php artisan migrate --force
          docker-compose exec -T app php artisan config:cache
          docker-compose exec -T app php artisan route:cache
          docker-compose exec -T app php artisan view:cache
    
    - name: Run smoke tests
      run: |
        curl -f ${{ secrets.STAGING_URL }}/health || exit 1
        curl -f ${{ secrets.STAGING_URL }}/api/health || exit 1
    
    - name: Deploy to production
      if: success()
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /var/www/ffjq
          git pull origin main
          docker-compose down
          docker-compose pull
          docker-compose up -d
          docker-compose exec -T app php artisan migrate --force
          docker-compose exec -T app php artisan config:cache
          docker-compose exec -T app php artisan route:cache
          docker-compose exec -T app php artisan view:cache
    
    - name: Notify deployment
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  performance:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install dependencies
      run: npm install -g artillery
    
    - name: Run performance tests
      run: |
        artillery run tests/performance/load-test.yml
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: artillery_report_*.json