/**
 * 验证工具函数
 */

// 验证邮箱
export function isEmail(email) {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

// 验证手机号
export function isPhone(phone) {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}

// 验证身份证号
export function isIdCard(idCard) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return reg.test(idCard)
}

// 验证URL
export function isUrl(url) {
  const reg = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i
  return reg.test(url)
}

// 验证外部链接
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

// 验证IP地址
export function isIP(ip) {
  const reg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return reg.test(ip)
}

// 验证端口号
export function isPort(port) {
  const reg = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
  return reg.test(port)
}

// 验证密码强度
export function isStrongPassword(password) {
  // 至少8位，包含大小写字母、数字和特殊字符
  const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
  return reg.test(password)
}

// 验证用户名
export function isUsername(username) {
  // 4-20位，字母、数字、下划线
  const reg = /^[a-zA-Z0-9_]{4,20}$/
  return reg.test(username)
}

// 验证中文姓名
export function isChineseName(name) {
  const reg = /^[\u4e00-\u9fa5]{2,10}$/
  return reg.test(name)
}

// 验证银行卡号
export function isBankCard(cardNo) {
  const reg = /^[1-9]\d{12,18}$/
  return reg.test(cardNo)
}

// 验证数字
export function isNumber(value) {
  return !isNaN(value) && isFinite(value)
}

// 验证正整数
export function isPositiveInteger(value) {
  const reg = /^[1-9]\d*$/
  return reg.test(value)
}

// 验证非负整数
export function isNonNegativeInteger(value) {
  const reg = /^\d+$/
  return reg.test(value)
}

// 验证小数
export function isDecimal(value, decimals = 2) {
  const reg = new RegExp(`^\\d+(\\.\\d{1,${decimals}})?$`)
  return reg.test(value)
}

// 验证日期格式
export function isDate(date) {
  return date instanceof Date && !isNaN(date.getTime())
}

// 验证日期字符串
export function isDateString(dateString) {
  const reg = /^\d{4}-\d{2}-\d{2}$/
  return reg.test(dateString) && !isNaN(Date.parse(dateString))
}

// 验证时间字符串
export function isTimeString(timeString) {
  const reg = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/
  return reg.test(timeString)
}

// 验证颜色值
export function isColor(color) {
  const reg = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return reg.test(color)
}

// 验证JSON字符串
export function isJSON(str) {
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}

// 验证空值
export function isEmpty(value) {
  return value === null || value === undefined || value === '' || 
         (Array.isArray(value) && value.length === 0) ||
         (typeof value === 'object' && Object.keys(value).length === 0)
}

// 验证文件类型
export function isFileType(file, types) {
  if (!file || !file.type) return false
  return types.some(type => file.type.includes(type))
}

// 验证文件大小
export function isFileSize(file, maxSize) {
  if (!file || !file.size) return false
  return file.size <= maxSize
}