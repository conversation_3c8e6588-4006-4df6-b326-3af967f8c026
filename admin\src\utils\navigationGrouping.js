/**
 * 轻量级导航分组工具
 * 不改变现有路由结构，只是为导航添加视觉分组
 */

// 导航分组配置 - 基于现有路由路径
export const navigationGroups = {
  dataCenter: {
    title: '数据中心',
    icon: 'DataBoard',
    color: '#409eff',
    routes: ['/dashboard', '/data-screen']
  },
  businessManagement: {
    title: '业务管理', 
    icon: 'Management',
    color: '#67c23a',
    routes: ['/community', '/orders', '/content']
  },
  distributionSystem: {
    title: '分销体系',
    icon: 'Share',
    color: '#e6a23c',
    routes: ['/distribution', '/distributor', '/agent']
  },
  financeCenter: {
    title: '财务中心',
    icon: 'Money',
    color: '#f56c6c',
    routes: ['/finance', '/payment', '/withdrawal']
  },
  userCenter: {
    title: '用户中心',
    icon: 'User',
    color: '#909399',
    routes: ['/user', '/users']
  },
  systemManagement: {
    title: '系统管理',
    icon: 'Setting',
    color: '#606266',
    routes: ['/system', '/security', '/logs']
  }
}

/**
 * 根据路由路径获取所属分组
 * @param {string} routePath - 路由路径
 * @returns {Object|null} 分组信息
 */
export function getRouteGroup(routePath) {
  for (const [groupKey, group] of Object.entries(navigationGroups)) {
    if (group.routes.some(route => routePath.startsWith(route))) {
      return {
        key: groupKey,
        ...group
      }
    }
  }
  return null
}

/**
 * 为路由添加分组信息
 * @param {Array} routes - 路由数组
 * @returns {Array} 带分组信息的路由数组
 */
export function addGroupInfoToRoutes(routes) {
  return routes.map(route => {
    const group = getRouteGroup(route.path)
    return {
      ...route,
      group: group
    }
  })
}

/**
 * 按分组组织路由
 * @param {Array} routes - 路由数组
 * @returns {Object} 按分组组织的路由对象
 */
export function organizeRoutesByGroup(routes) {
  const groupedRoutes = {}
  
  // 初始化分组
  Object.keys(navigationGroups).forEach(groupKey => {
    groupedRoutes[groupKey] = {
      ...navigationGroups[groupKey],
      routes: []
    }
  })
  
  // 将路由分配到对应分组
  routes.forEach(route => {
    const group = getRouteGroup(route.path)
    if (group) {
      groupedRoutes[group.key].routes.push(route)
    } else {
      // 未分组的路由放到系统管理中
      groupedRoutes.systemManagement.routes.push(route)
    }
  })
  
  // 移除空分组
  Object.keys(groupedRoutes).forEach(groupKey => {
    if (groupedRoutes[groupKey].routes.length === 0) {
      delete groupedRoutes[groupKey]
    }
  })
  
  return groupedRoutes
}

/**
 * 获取分组的CSS类名
 * @param {string} groupKey - 分组键
 * @returns {string} CSS类名
 */
export function getGroupClassName(groupKey) {
  return `nav-group-${groupKey}`
}

/**
 * 生成分组分隔符HTML
 * @param {Object} group - 分组信息
 * @returns {string} HTML字符串
 */
export function createGroupSeparator(group) {
  return `
    <div class="nav-group-separator" data-group="${group.key}">
      <div class="group-line" style="background-color: ${group.color}"></div>
      <div class="group-label" style="color: ${group.color}">
        <i class="el-icon-${group.icon}"></i>
        <span>${group.title}</span>
      </div>
    </div>
  `
}

// 快捷操作配置
export const quickActions = [
  {
    title: '创建群组',
    path: '/community/add-enhanced',
    icon: 'Plus',
    color: '#409eff',
    description: '快速创建新的社群'
  },
  {
    title: '数据大屏',
    path: '/data-screen',
    icon: 'DataLine', 
    color: '#67c23a',
    description: '查看实时数据大屏'
  },
  {
    title: '用户管理',
    path: '/users/list',
    icon: 'User',
    color: '#e6a23c',
    description: '管理系统用户'
  },
  {
    title: '财务总览',
    path: '/finance/overview',
    icon: 'Money',
    color: '#f56c6c',
    description: '查看财务数据'
  }
]

/**
 * 根据用户角色过滤快捷操作
 * @param {string} userRole - 用户角色
 * @returns {Array} 过滤后的快捷操作
 */
export function getQuickActionsByRole(userRole) {
  const roleActions = {
    admin: quickActions,
    substation: quickActions.filter(action => 
      !action.path.includes('/system')
    ),
    agent: quickActions.filter(action => 
      ['/community/add', '/finance/overview'].includes(action.path)
    ),
    distributor: quickActions.filter(action => 
      ['/community/add', '/users/list'].includes(action.path)
    ),
    group_owner: quickActions.filter(action => 
      action.path === '/community/add'
    ),
    user: []
  }
  
  return roleActions[userRole] || []
}

/**
 * 搜索配置
 */
export const searchConfig = {
  placeholder: '搜索功能、用户、订单...',
  shortcuts: {
    open: 'Ctrl+K',
    close: 'Escape'
  },
  categories: [
    { key: 'menus', title: '菜单功能', icon: 'Menu' },
    { key: 'users', title: '用户', icon: 'User' },
    { key: 'groups', title: '群组', icon: 'UserFilled' },
    { key: 'orders', title: '订单', icon: 'ShoppingCart' }
  ]
}

/**
 * 简单的搜索数据
 */
export const searchableItems = [
  { id: 'dashboard', title: '数据看板', path: '/dashboard', icon: 'Monitor', keywords: ['数据', '看板', '统计', '图表'] },
  { id: 'data-screen', title: '数据大屏', path: '/data-screen', icon: 'DataLine', keywords: ['大屏', '数据', '可视化', '监控'] },
  { id: 'community', title: '社群管理', path: '/community', icon: 'Comment', keywords: ['社群', '群组', '管理', '微信'] },
  { id: 'community-add', title: '创建群组', path: '/community/add', icon: 'Plus', keywords: ['创建', '新建', '群组', '社群'] },
  { id: 'orders', title: '订单管理', path: '/orders', icon: 'ShoppingCart', keywords: ['订单', '交易', '支付', '商品'] },
  { id: 'users', title: '用户管理', path: '/users', icon: 'User', keywords: ['用户', '会员', '客户', '管理'] },
  { id: 'finance', title: '财务管理', path: '/finance', icon: 'Money', keywords: ['财务', '佣金', '收入', '提现'] },
  { id: 'distribution', title: '分销管理', path: '/distribution', icon: 'Share', keywords: ['分销', '代理', '推广', '佣金'] }
]

/**
 * 执行搜索
 * @param {string} query - 搜索关键词
 * @returns {Array} 搜索结果
 */
export function performSearch(query) {
  if (!query || query.trim().length === 0) {
    return []
  }
  
  const searchTerm = query.toLowerCase().trim()
  
  return searchableItems.filter(item => {
    return item.title.toLowerCase().includes(searchTerm) ||
           item.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
  }).slice(0, 8) // 限制结果数量
}
