/**
 * 城市替换组合式函数
 * 用于在客户端落地页动态替换群组信息中的城市变量
 */

import { ref, computed, onMounted } from 'vue'
import clientLocationService from '@/utils/clientLocationService'

export function useCityReplacement() {
  const isLocationLoading = ref(false)
  const userCity = ref('北京')
  const isLocationDetected = ref(false)

  /**
   * 初始化城市检测
   */
  const initializeCityDetection = async () => {
    if (isLocationDetected.value) return

    isLocationLoading.value = true
    try {
      const detectedCity = await clientLocationService.initialize()
      userCity.value = detectedCity
      isLocationDetected.value = true
      
      console.log('用户城市检测完成:', detectedCity)
    } catch (error) {
      console.warn('城市检测失败:', error)
      userCity.value = '北京'
    } finally {
      isLocationLoading.value = false
    }
  }

  /**
   * 替换群组数据中的城市变量
   * @param {Object} groupData 群组数据
   * @returns {Object} 替换后的群组数据
   */
  const replaceGroupCityVariables = (groupData) => {
    if (!groupData) return groupData
    
    return clientLocationService.replaceCityInObject(groupData)
  }

  /**
   * 替换文本中的城市变量
   * @param {string} text 文本
   * @returns {string} 替换后的文本
   */
  const replaceCityInText = (text) => {
    return clientLocationService.replaceCityInText(text)
  }

  /**
   * 获取当前用户城市
   * @returns {string} 城市名称
   */
  const getCurrentUserCity = () => {
    return userCity.value
  }

  /**
   * 手动设置用户城市
   * @param {string} city 城市名称
   */
  const setUserCity = (city) => {
    userCity.value = city
    clientLocationService.setCity(city)
  }

  /**
   * 创建带城市替换的计算属性
   * @param {Function} dataGetter 获取原始数据的函数
   * @returns {ComputedRef} 替换后的数据
   */
  const createCityReplacedComputed = (dataGetter) => {
    return computed(() => {
      const originalData = dataGetter()
      if (!isLocationDetected.value) {
        return originalData
      }
      return replaceGroupCityVariables(originalData)
    })
  }

  /**
   * 为群组列表替换城市变量
   * @param {Array} groupList 群组列表
   * @returns {Array} 替换后的群组列表
   */
  const replaceGroupListCityVariables = (groupList) => {
    if (!Array.isArray(groupList) || !isLocationDetected.value) {
      return groupList
    }
    
    return groupList.map(group => replaceGroupCityVariables(group))
  }

  // 自动初始化
  onMounted(() => {
    initializeCityDetection()
  })

  return {
    // 状态
    isLocationLoading,
    userCity,
    isLocationDetected,
    
    // 方法
    initializeCityDetection,
    replaceGroupCityVariables,
    replaceCityInText,
    getCurrentUserCity,
    setUserCity,
    createCityReplacedComputed,
    replaceGroupListCityVariables
  }
}

/**
 * 全局城市替换工具函数
 * 可以在任何地方使用，不依赖组合式API
 */
export const globalCityReplacer = {
  /**
   * 快速替换文本中的城市变量
   * @param {string} text 文本
   * @param {string} city 城市名称，如果不提供则使用检测到的城市
   * @returns {string} 替换后的文本
   */
  replaceText(text, city = null) {
    if (typeof text !== 'string') return text
    
    const targetCity = city || clientLocationService.getCurrentCity()
    return text.replace(/\{\{city\}\}/g, targetCity)
  },

  /**
   * 快速替换对象中的城市变量
   * @param {Object} obj 对象
   * @param {string} city 城市名称，如果不提供则使用检测到的城市
   * @returns {Object} 替换后的对象
   */
  replaceObject(obj, city = null) {
    if (!obj || typeof obj !== 'object') return obj
    
    const targetCity = city || clientLocationService.getCurrentCity()
    const originalCity = clientLocationService.getCurrentCity()
    
    // 临时设置城市
    if (city) {
      clientLocationService.setCity(city)
    }
    
    const result = clientLocationService.replaceCityInObject(obj)
    
    // 恢复原城市
    if (city) {
      clientLocationService.setCity(originalCity)
    }
    
    return result
  },

  /**
   * 获取当前检测到的城市
   * @returns {string}
   */
  getCurrentCity() {
    return clientLocationService.getCurrentCity()
  },

  /**
   * 初始化城市检测（用于非Vue组件）
   * @returns {Promise<string>}
   */
  async initialize() {
    return await clientLocationService.initialize()
  }
}

export default useCityReplacement
