<template>
  <div class="responsive-table-container">
    <!-- 移动端卡片视图 -->
    <div v-if="isMobile" class="mobile-cards">
      <div 
        v-for="(row, index) in data" 
        :key="index"
        class="mobile-card"
        @click="$emit('row-click', row)"
      >
        <div class="card-header">
          <span class="card-title">{{ getCardTitle(row) }}</span>
          <div class="card-status" :class="getStatusClass(row)">{{ getStatusText(row) }}</div>
        </div>
        <div class="card-content">
          <div 
            v-for="col in visibleColumns" 
            :key="col.key"
            class="card-row"
          >
            <span class="card-label">{{ col.title }}:</span>
            <span class="card-value">{{ formatCell(row[col.key], col) }}</span>
          </div>
        </div>
        <div class="card-actions">
          <button 
            v-for="action in actions" 
            :key="action.key"
            class="card-action-btn"
            :class="action.class"
            @click.stop="handleAction(action, row)"
          >
            <i :class="action.icon"></i>
            <span>{{ action.text }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 桌面端表格视图 -->
    <div v-else class="desktop-table-wrapper">
      <table class="responsive-table">
        <thead>
          <tr>
            <th 
              v-for="col in columns" 
              :key="col.key"
              :class="{ 'sortable': col.sortable }"
              @click="col.sortable && handleSort(col.key)"
            >
              <span>{{ col.title }}</span>
              <i 
                v-if="col.sortable"
                class="sort-icon"
                :class="getSortClass(col.key)"
              ></i>
            </th>
            <th v-if="actions.length" class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="(row, index) in sortedData" 
            :key="index"
            @click="$emit('row-click', row)"
            class="table-row"
          >
            <td 
              v-for="col in columns" 
              :key="col.key"
              :class="getCellClass(col, row)"
            >
              <slot 
                :name="`cell-${col.key}`" 
                :value="row[col.key]" 
                :row="row"
                :index="index"
              >
                <span>{{ formatCell(row[col.key], col) }}</span>
              </slot>
            </td>
            <td v-if="actions.length" class="actions-cell">
              <div class="actions-wrapper">
                <button 
                  v-for="action in actions" 
                  :key="action.key"
                  class="table-action-btn"
                  :class="action.class"
                  :title="action.text"
                  @click.stop="handleAction(action, row)"
                >
                  <i :class="action.icon"></i>
                </button>
              </div>
            </td>
          </tr>
          
          <tr v-if="!data.length" class="empty-row">
            <td :colspan="columns.length + (actions.length ? 1 : 0)" class="empty-cell">
              <div class="empty-state">
                <i class="icon-empty"></i>
                <p>{{ emptyText }}</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && total > 0" class="pagination-wrapper">
      <div class="pagination-info">
        共 {{ total }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
      </div>
      
      <div class="pagination-actions">
        <button 
          class="pagination-btn"
          :disabled="currentPage <= 1"
          @click="$emit('page-change', currentPage - 1)"
        >
          <i class="icon-prev"></i>
          上一页
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in visiblePages" 
            :key="page"
            class="page-btn"
            :class="{ active: page === currentPage }"
            @click="$emit('page-change', page)"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="pagination-btn"
          :disabled="currentPage >= totalPages"
          @click="$emit('page-change', currentPage + 1)"
        >
          下一页
          <i class="icon-next"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'ResponsiveTable',
  props: {
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    },
    actions: {
      type: Array,
      default: () => []
    },
    titleKey: {
      type: String,
      default: 'name'
    },
    statusKey: {
      type: String,
      default: 'status'
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    }
  },
  emits: ['row-click', 'action', 'page-change', 'sort-change'],
  setup(props, { emit }) {
    const isMobile = ref(false)
    const sortKey = ref('')
    const sortOrder = ref('asc')
    
    const visibleColumns = computed(() => {
      return props.columns.filter(col => !col.hidden)
    })
    
    const sortedData = computed(() => {
      if (!sortKey.value) return props.data
      
      return [...props.data].sort((a, b) => {
        const aVal = a[sortKey.value]
        const bVal = b[sortKey.value]
        
        if (sortOrder.value === 'asc') {
          return aVal > bVal ? 1 : -1
        } else {
          return aVal < bVal ? 1 : -1
        }
      })
    })
    
    const totalPages = computed(() => {
      return Math.ceil(props.total / props.pageSize)
    })
    
    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, props.currentPage - 2)
      const end = Math.min(totalPages.value, props.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })
    
    const checkMobile = () => {
      isMobile.value = window.innerWidth < 768
    }
    
    const getCardTitle = (row) => {
      return row[props.titleKey] || '未命名'
    }
    
    const getStatusClass = (row) => {
      const status = row[props.statusKey]
      return `status-${status}`
    }
    
    const getStatusText = (row) => {
      const status = row[props.statusKey]
      const statusMap = {
        active: '活跃',
        inactive: '禁用',
        pending: '待审核',
        deleted: '已删除'
      }
      return statusMap[status] || status
    }
    
    const formatCell = (value, column) => {
      if (column.formatter) {
        return column.formatter(value)
      }
      
      if (column.type === 'date') {
        return new Date(value).toLocaleDateString()
      }
      
      if (column.type === 'money') {
        return `¥${Number(value).toFixed(2)}`
      }
      
      return value || '-'
    }
    
    const getCellClass = (column, row) => {
      const classes = [`cell-${column.key}`]
      
      if (column.align) {
        classes.push(`text-${column.align}`)
      }
      
      if (column.type) {
        classes.push(`type-${column.type}`)
      }
      
      return classes
    }
    
    const handleSort = (key) => {
      if (sortKey.value === key) {
        sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortKey.value = key
        sortOrder.value = 'asc'
      }
      
      emit('sort-change', { key: sortKey.value, order: sortOrder.value })
    }
    
    const getSortClass = (key) => {
      if (sortKey.value !== key) return 'icon-sort-default'
      return sortOrder.value === 'asc' ? 'icon-sort-asc' : 'icon-sort-desc'
    }
    
    const handleAction = (action, row) => {
      emit('action', { action, row })
    }
    
    onMounted(() => {
      checkMobile()
      window.addEventListener('resize', checkMobile)
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', checkMobile)
    })
    
    return {
      isMobile,
      visibleColumns,
      sortedData,
      totalPages,
      visiblePages,
      getCardTitle,
      getStatusClass,
      getStatusText,
      formatCell,
      getCellClass,
      handleSort,
      getSortClass,
      handleAction
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 移动端卡片样式
.mobile-cards {
  padding: 16px;
}

.mobile-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
  
  &:active {
    transform: scale(0.98);
    background-color: #f9f9f9;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.card-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.status-active {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  &.status-inactive {
    background-color: #fff2e8;
    color: #ff7a45;
  }
  
  &.status-pending {
    background-color: #fff7e6;
    color: #faad14;
  }
  
  &.status-deleted {
    background-color: #fff1f0;
    color: #ff4d4f;
  }
}

.card-content {
  margin-bottom: 12px;
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  white-space: nowrap;
}

.card-value {
  font-size: 14px;
  color: #262626;
  text-align: right;
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.card-action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &.primary {
    border-color: #1890ff;
    color: #1890ff;
  }
  
  &.danger {
    border-color: #ff4d4f;
    color: #ff4d4f;
  }
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// 桌面端表格样式
.desktop-table-wrapper {
  overflow-x: auto;
}

.responsive-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  
  th,
  td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
  }
  
  th {
    background-color: #fafafa;
    font-weight: 600;
    color: #262626;
    
    &.sortable {
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
  
  .table-row {
    transition: background-color 0.2s;
    cursor: pointer;
    
    &:hover {
      background-color: #fafafa;
    }
  }
  
  .actions-cell {
    text-align: center;
    white-space: nowrap;
  }
  
  .actions-wrapper {
    display: flex;
    gap: 4px;
    justify-content: center;
  }
  
  .table-action-btn {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.primary {
      border-color: #1890ff;
      color: #1890ff;
    }
    
    &.danger {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }
  
  .empty-row {
    .empty-cell {
      text-align: center;
      padding: 48px 16px;
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }
}

// 文本对齐
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 数据类型特殊样式
.type-money {
  font-family: 'Arial', sans-serif;
  font-weight: 600;
  color: #52c41a;
}

.type-date {
  font-size: 13px;
  color: #666;
}

// 分页样式
.pagination-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  gap: 16px;
  
  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
  }
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn,
.page-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
  
  &:not(:disabled):hover {
    border-color: #1890ff;
    color: #1890ff;
  }
  
  &.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
  }
}

.page-numbers {
  display: flex;
  gap: 4px;
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .card-action-btn,
  .table-action-btn {
    min-height: 44px;
    padding: 8px 16px;
  }
  
  .pagination-btn,
  .page-btn {
    min-height: 44px;
    padding: 8px 16px;
  }
}

// 响应式滚动优化
@media (max-width: 767px) {
  .responsive-table {
    th,
    td {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
}
</style>