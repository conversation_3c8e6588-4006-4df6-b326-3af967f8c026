import{r as e,_ as s}from"./index-DtXAftX0.js";/* empty css                    *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                *//* empty css                    */import{T as a,aL as t,at as r,a4 as l,bc as u,U as o,a_ as n,aY as c,aZ as m,bg as i,bh as d,bi as p,a$ as h,bj as v,Q as f}from"./element-plus-h2SQQM64.js";import{r as y,c as _,k as g,l as b,t as w,B as $,E as k,y as R,z as j,D as x,u as E}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const L=new class{constructor(){this.results=[],this.errors=[]}async checkAllRoutes(){console.log("🔍 开始路由检测..."),this.results=[],this.errors=[];const e=this.getAllRoutes();for(const s of e)await this.checkRoute(s);return{results:this.results,errors:this.errors,summary:this.generateSummary()}}getAllRoutes(){const s=[],a=(e,t="")=>{e.forEach(e=>{if(e.path&&!e.meta?.hidden){const a=this.resolvePath(t,e.path);s.push({path:a,name:e.name,component:e.component,meta:e.meta,parent:t})}if(e.children){const s=this.resolvePath(t,e.path);a(e.children,s)}})};return a(e.options.routes),s}async checkRoute(e){const s={path:e.path,name:e.name,status:"unknown",error:null,loadTime:0,componentExists:!1,hasErrors:!1};try{const t=Date.now();if(e.component)if("function"==typeof e.component)try{await e.component();s.componentExists=!0,s.status="success"}catch(a){s.componentExists=!1,s.status="component_error",s.error=a.message,this.errors.push({path:e.path,type:"component_load_error",error:a.message})}else s.componentExists=!0,s.status="success";else s.status="no_component";s.loadTime=Date.now()-t}catch(a){s.status="error",s.error=a.message,this.errors.push({path:e.path,type:"route_error",error:a.message})}this.results.push(s),console.log(`✓ 检测路由: ${e.path} - ${s.status}`)}resolvePath(e,s){return s.startsWith("/")?s:e?`${e}/${s}`.replace(/\/+/g,"/"):`/${s}`}generateSummary(){const e=this.results.length,s=this.results.filter(e=>"success"===e.status).length;return{total:e,success:s,errors:this.results.filter(e=>"success"!==e.status).length,successRate:e>0?(s/e*100).toFixed(2):0,avgLoadTime:this.results.reduce((e,s)=>e+s.loadTime,0)/e}}generateReport(){const e=this.generateSummary();let s="# 路由检测报告\n\n";s+="## 📊 检测摘要\n\n",s+=`- **总路由数**: ${e.total}\n`,s+=`- **成功加载**: ${e.success}\n`,s+=`- **加载失败**: ${e.errors}\n`,s+=`- **成功率**: ${e.successRate}%\n`,s+=`- **平均加载时间**: ${e.avgLoadTime.toFixed(2)}ms\n\n`;const a=this.results.filter(e=>"success"===e.status);a.length>0&&(s+=`## ✅ 成功加载的路由 (${a.length})\n\n`,a.forEach(e=>{s+=`- \`${e.path}\` - ${e.name||"未命名"}\n`}),s+="\n");const t=this.results.filter(e=>"success"!==e.status);return t.length>0&&(s+=`## ❌ 加载失败的路由 (${t.length})\n\n`,t.forEach(e=>{s+=`- \`${e.path}\` - ${e.status}\n`,e.error&&(s+=`  - 错误: ${e.error}\n`)}),s+="\n"),this.errors.length>0&&(s+="## 🔍 错误详情\n\n",this.errors.forEach(e=>{s+=`### ${e.path}\n`,s+=`- **类型**: ${e.type}\n`,s+=`- **错误**: ${e.error}\n\n`})),s}},T={class:"route-checker"},U={class:"checker-controls"},z={key:0,class:"progress-section"},S={class:"progress-text"},A={key:1,class:"results-summary"},D={class:"summary-item"},I={class:"summary-number"},C={class:"summary-item"},O={class:"summary-number"},P={class:"summary-item"},V={class:"summary-number"},B={class:"summary-item"},F={class:"summary-number"},M={key:2,class:"results-detail"},Q={key:0,class:"error-text"},W={key:1,class:"success-text"},Y={class:"error-text"},Z=s({__name:"RouteChecker",setup(e){const s=y(!1),Z=y(0),q=y(""),G=y(null),H=y("all"),J=_(()=>G.value?G.value.results.filter(e=>"success"!==e.status):[]),K=async()=>{s.value=!0,Z.value=0,q.value="";try{const e=setInterval(()=>{Z.value<90&&(Z.value+=10*Math.random())},200),s=await L.checkAllRoutes();clearInterval(e),Z.value=100,G.value=s,f.success(`检测完成！成功: ${s.summary.success}, 失败: ${s.summary.errors}`)}catch(e){f.error("检测过程中发生错误: "+e.message)}finally{s.value=!1}},N=()=>{if(!G.value)return;const e=L.generateReport(),s=new Blob([e],{type:"text/markdown"}),a=URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download=`route-check-report-${(new Date).toISOString().slice(0,10)}.md`,t.click(),URL.revokeObjectURL(a),f.success("报告已下载")},X=e=>{switch(e){case"success":return"success";case"component_error":case"error":return"danger";case"no_component":return"warning";default:return"info"}},ee=e=>{switch(e){case"success":return"成功";case"component_error":return"组件错误";case"no_component":return"无组件";case"error":return"错误";default:return"未知"}};return(e,f)=>{const y=a,_=r,L=u,se=c,ae=n,te=m,re=p,le=h,ue=d,oe=i,ne=v;return b(),g("div",T,[f[7]||(f[7]=w("div",{class:"page-header"},[w("h1",null,"🔍 路由检测工具"),w("p",null,"自动化检测所有路由的可访问性和组件加载状态")],-1)),w("div",U,[k(_,{type:"primary",onClick:K,loading:s.value,size:"large"},{default:j(()=>[k(y,null,{default:j(()=>[k(E(t))]),_:1}),f[1]||(f[1]=x(" 开始检测 ",-1))]),_:1,__:[1]},8,["loading"]),G.value?(b(),R(_,{key:0,onClick:N,size:"large"},{default:j(()=>[k(y,null,{default:j(()=>[k(E(l))]),_:1}),f[2]||(f[2]=x(" 下载报告 ",-1))]),_:1,__:[2]})):$("",!0)]),s.value?(b(),g("div",z,[k(L,{percentage:Z.value,status:100===Z.value?"success":""},null,8,["percentage","status"]),w("p",S,"正在检测路由... "+o(q.value),1)])):$("",!0),G.value&&!s.value?(b(),g("div",A,[k(te,{gutter:20},{default:j(()=>[k(ae,{span:6},{default:j(()=>[k(se,{class:"summary-card"},{default:j(()=>[w("div",D,[w("div",I,o(G.value.summary.total),1),f[3]||(f[3]=w("div",{class:"summary-label"},"总路由数",-1))])]),_:1})]),_:1}),k(ae,{span:6},{default:j(()=>[k(se,{class:"summary-card success"},{default:j(()=>[w("div",C,[w("div",O,o(G.value.summary.success),1),f[4]||(f[4]=w("div",{class:"summary-label"},"成功加载",-1))])]),_:1})]),_:1}),k(ae,{span:6},{default:j(()=>[k(se,{class:"summary-card error"},{default:j(()=>[w("div",P,[w("div",V,o(G.value.summary.errors),1),f[5]||(f[5]=w("div",{class:"summary-label"},"加载失败",-1))])]),_:1})]),_:1}),k(ae,{span:6},{default:j(()=>[k(se,{class:"summary-card"},{default:j(()=>[w("div",B,[w("div",F,o(G.value.summary.successRate)+"%",1),f[6]||(f[6]=w("div",{class:"summary-label"},"成功率",-1))])]),_:1})]),_:1})]),_:1})])):$("",!0),G.value&&!s.value?(b(),g("div",M,[k(ne,{modelValue:H.value,"onUpdate:modelValue":f[0]||(f[0]=e=>H.value=e)},{default:j(()=>[k(oe,{label:"所有路由",name:"all"},{default:j(()=>[k(ue,{data:G.value.results,style:{width:"100%"}},{default:j(()=>[k(re,{prop:"path",label:"路由路径",width:"300"}),k(re,{prop:"name",label:"路由名称",width:"200"}),k(re,{label:"状态",width:"120"},{default:j(e=>[k(le,{type:X(e.row.status),size:"small"},{default:j(()=>[x(o(ee(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),k(re,{prop:"loadTime",label:"加载时间(ms)",width:"120"}),k(re,{label:"错误信息"},{default:j(e=>[e.row.error?(b(),g("span",Q,o(e.row.error),1)):(b(),g("span",W,"无"))]),_:1})]),_:1},8,["data"])]),_:1}),k(oe,{label:"失败路由",name:"failed"},{default:j(()=>[k(ue,{data:J.value,style:{width:"100%"}},{default:j(()=>[k(re,{prop:"path",label:"路由路径",width:"300"}),k(re,{prop:"name",label:"路由名称",width:"200"}),k(re,{label:"状态",width:"120"},{default:j(e=>[k(le,{type:"danger",size:"small"},{default:j(()=>[x(o(ee(e.row.status)),1)]),_:2},1024)]),_:1}),k(re,{label:"错误信息"},{default:j(e=>[w("span",Y,o(e.row.error),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])])):$("",!0)])}}},[["__scopeId","data-v-d24fb311"]]);export{Z as default};
