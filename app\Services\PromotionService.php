<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\PromotionLink;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * 推广服务类
 * 处理推广链接生成、二维码生成等功能
 */
class PromotionService
{
    /**
     * 为群组生成推广链接
     */
    public function generatePromotionLink(WechatGroup $group, array $params = []): array
    {
        // 基础推广链接
        $baseUrl = route('group.landing', ['slug' => $group->slug]);
        
        // 添加追踪参数
        $trackingParams = array_filter([
            'source' => $params['source'] ?? 'promotion',
            'utm_campaign' => $params['utm_campaign'] ?? null,
            'utm_medium' => $params['utm_medium'] ?? 'link',
            'utm_source' => $params['utm_source'] ?? 'system',
            'utm_content' => $params['utm_content'] ?? null,
            'ref' => $params['ref'] ?? null
        ]);
        
        $promotionUrl = $baseUrl;
        if (!empty($trackingParams)) {
            $promotionUrl .= '?' . http_build_query($trackingParams);
        }
        
        // 生成短链接
        $shortUrl = $this->generateShortUrl($promotionUrl, $group);
        
        // 生成二维码
        $qrCodeUrl = $this->generateQRCode($promotionUrl, $group);
        
        // 生成推广海报
        $posterUrl = $this->generatePromotionPoster($group, $promotionUrl);
        
        return [
            'original_url' => $promotionUrl,
            'short_url' => $shortUrl,
            'qr_code_url' => $qrCodeUrl,
            'poster_url' => $posterUrl,
            'tracking_params' => $trackingParams,
            'share_text' => $this->generateShareText($group),
            'share_title' => $this->generateShareTitle($group)
        ];
    }

    /**
     * 生成短链接
     */
    public function generateShortUrl(string $originalUrl, WechatGroup $group): string
    {
        // 生成短码
        $shortCode = $this->generateShortCode();
        
        // 保存到数据库
        PromotionLink::create([
            'group_id' => $group->id,
            'short_code' => $shortCode,
            'original_url' => $originalUrl,
            'created_by' => auth()->id(),
            'expires_at' => now()->addMonths(6) // 6个月后过期
        ]);
        
        return url('/s/' . $shortCode);
    }

    /**
     * 生成二维码
     */
    public function generateQRCode(string $url, WechatGroup $group, array $options = []): string
    {
        $size = $options['size'] ?? 300;
        $margin = $options['margin'] ?? 2;
        $format = $options['format'] ?? 'png';
        
        // 生成二维码
        $qrCode = QrCode::format($format)
            ->size($size)
            ->margin($margin)
            ->errorCorrection('M')
            ->generate($url);
        
        // 保存到存储
        $filename = 'qrcodes/group_' . $group->id . '_' . time() . '.' . $format;
        Storage::disk('public')->put($filename, $qrCode);
        
        return Storage::disk('public')->url($filename);
    }

    /**
     * 生成推广海报
     */
    public function generatePromotionPoster(WechatGroup $group, string $promotionUrl): string
    {
        // 海报模板配置
        $posterConfig = [
            'width' => 750,
            'height' => 1334,
            'background' => '#ffffff',
            'template' => 'default'
        ];
        
        // 生成海报
        $posterPath = $this->createPosterImage($group, $promotionUrl, $posterConfig);
        
        return $posterPath;
    }

    /**
     * 批量生成推广素材
     */
    public function generatePromotionMaterials(WechatGroup $group, array $options = []): array
    {
        $materials = [];
        
        // 生成不同尺寸的二维码
        $qrSizes = [200, 300, 500];
        foreach ($qrSizes as $size) {
            $materials['qr_codes'][$size] = $this->generateQRCode(
                route('group.landing', ['slug' => $group->slug]),
                $group,
                ['size' => $size]
            );
        }
        
        // 生成不同样式的海报
        $posterStyles = ['default', 'modern', 'simple'];
        foreach ($posterStyles as $style) {
            $materials['posters'][$style] = $this->generatePromotionPoster(
                $group,
                route('group.landing', ['slug' => $group->slug])
            );
        }
        
        // 生成分享文案
        $materials['share_texts'] = $this->generateShareTexts($group);
        
        // 生成推广链接
        $materials['promotion_links'] = $this->generateMultiplePromotionLinks($group);
        
        return $materials;
    }

    /**
     * 解析短链接
     */
    public function resolveShortUrl(string $shortCode): ?array
    {
        $link = PromotionLink::where('short_code', $shortCode)
            ->where('expires_at', '>', now())
            ->first();
        
        if (!$link) {
            return null;
        }
        
        // 增加访问次数
        $link->increment('click_count');
        
        return [
            'original_url' => $link->original_url,
            'group_id' => $link->group_id,
            'created_at' => $link->created_at
        ];
    }

    /**
     * 获取推广统计数据
     */
    public function getPromotionStats(WechatGroup $group): array
    {
        $links = PromotionLink::where('group_id', $group->id)->get();
        
        return [
            'total_links' => $links->count(),
            'total_clicks' => $links->sum('click_count'),
            'active_links' => $links->where('expires_at', '>', now())->count(),
            'expired_links' => $links->where('expires_at', '<=', now())->count(),
            'top_performing_links' => $links->sortByDesc('click_count')->take(5)->values(),
            'recent_links' => $links->sortByDesc('created_at')->take(10)->values()
        ];
    }

    // 私有方法

    /**
     * 生成短码
     */
    private function generateShortCode(): string
    {
        do {
            $shortCode = Str::random(6);
        } while (PromotionLink::where('short_code', $shortCode)->exists());
        
        return $shortCode;
    }

    /**
     * 创建海报图片
     */
    private function createPosterImage(WechatGroup $group, string $url, array $config): string
    {
        // 这里使用GD库或Imagick创建海报
        // 为了简化，返回一个模拟的海报URL
        $filename = 'posters/group_' . $group->id . '_' . time() . '.jpg';
        
        // 创建基础海报
        $poster = $this->createBasicPoster($group, $url, $config);
        
        // 保存海报
        Storage::disk('public')->put($filename, $poster);
        
        return Storage::disk('public')->url($filename);
    }

    /**
     * 创建基础海报
     */
    private function createBasicPoster(WechatGroup $group, string $url, array $config): string
    {
        $width = $config['width'];
        $height = $config['height'];
        
        // 创建画布
        $image = imagecreatetruecolor($width, $height);
        
        // 设置背景色
        $backgroundColor = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $backgroundColor);
        
        // 添加标题
        $titleColor = imagecolorallocate($image, 51, 51, 51);
        $title = mb_substr($group->title, 0, 20); // 限制标题长度
        
        // 这里应该使用TTF字体，简化处理
        imagestring($image, 5, 50, 100, $title, $titleColor);
        
        // 添加描述
        $descColor = imagecolorallocate($image, 102, 102, 102);
        $description = mb_substr($group->description, 0, 50);
        imagestring($image, 3, 50, 150, $description, $descColor);
        
        // 添加价格
        $priceColor = imagecolorallocate($image, 255, 107, 107);
        $priceText = $group->price > 0 ? '¥' . number_format($group->price, 2) : '免费加入';
        imagestring($image, 5, 50, 200, $priceText, $priceColor);
        
        // 输出图片
        ob_start();
        imagejpeg($image, null, 90);
        $imageData = ob_get_contents();
        ob_end_clean();
        
        imagedestroy($image);
        
        return $imageData;
    }

    /**
     * 生成分享文案
     */
    private function generateShareText(WechatGroup $group): string
    {
        $templates = [
            '🔥 发现一个超棒的{category}群组：{title}！{price_text}，快来加入我们吧！',
            '💎 推荐一个优质{category}社群：{title}，{price_text}，值得拥有！',
            '✨ {title} - 专业的{category}交流群，{price_text}，等你来！',
            '🎯 {category}爱好者必看！{title}群组{price_text}，机会难得！'
        ];
        
        $template = $templates[array_rand($templates)];
        
        return str_replace([
            '{category}',
            '{title}',
            '{price_text}'
        ], [
            $group->category_name,
            $group->title,
            $group->price > 0 ? '仅需¥' . number_format($group->price, 2) : '免费加入'
        ], $template);
    }

    /**
     * 生成分享标题
     */
    private function generateShareTitle(WechatGroup $group): string
    {
        return $group->title . ' - ' . $group->category_name . '交流群';
    }

    /**
     * 生成多种分享文案
     */
    private function generateShareTexts(WechatGroup $group): array
    {
        return [
            'short' => mb_substr($this->generateShareText($group), 0, 50) . '...',
            'medium' => $this->generateShareText($group),
            'long' => $this->generateShareText($group) . ' 加入我们，一起学习成长！🚀',
            'formal' => '诚邀您加入' . $group->title . '，这是一个专业的' . $group->category_name . '交流平台。',
            'casual' => '嘿！发现了个不错的群组：' . $group->title . '，一起来玩吧！😄'
        ];
    }

    /**
     * 生成多个推广链接
     */
    private function generateMultiplePromotionLinks(WechatGroup $group): array
    {
        $sources = [
            'wechat' => '微信分享',
            'qq' => 'QQ分享',
            'weibo' => '微博分享',
            'douyin' => '抖音分享',
            'xiaohongshu' => '小红书分享'
        ];
        
        $links = [];
        foreach ($sources as $source => $name) {
            $links[$source] = $this->generatePromotionLink($group, [
                'source' => $source,
                'utm_medium' => 'social',
                'utm_source' => $source
            ]);
        }
        
        return $links;
    }
}