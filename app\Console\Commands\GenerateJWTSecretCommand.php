<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateJWTSecretCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jwt:generate-secret {--force : Force the operation to run when in production}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate JWT secret key';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $key = $this->generateRandomKey();

        if (!$this->setKeyInEnvironmentFile($key)) {
            $this->error('Unable to set JWT secret key in environment file.');
            return false;
        }

        $this->info('JWT secret key generated successfully.');
        $this->line('<comment>JWT_SECRET:</comment> ' . $key);

        return true;
    }

    /**
     * Generate a random key for the JWT secret.
     */
    protected function generateRandomKey(): string
    {
        return base64_encode(random_bytes(32));
    }

    /**
     * Set the JWT secret key in the environment file.
     */
    protected function setKeyInEnvironmentFile(string $key): bool
    {
        $currentKey = config('jwt.secret');

        if (strlen($currentKey) !== 0 && (!$this->option('force'))) {
            if (!$this->confirm('JWT secret key already exists. Do you want to override it?')) {
                return false;
            }
        }

        if (!$this->writeNewEnvironmentFileWith($key)) {
            return false;
        }

        return true;
    }

    /**
     * Write a new environment file with the given key.
     */
    protected function writeNewEnvironmentFileWith(string $key): bool
    {
        $envFile = $this->laravel->environmentFilePath();

        if (!file_exists($envFile)) {
            $this->error('.env file not found.');
            return false;
        }

        $content = file_get_contents($envFile);

        if (str_contains($content, 'JWT_SECRET=')) {
            $content = preg_replace(
                '/JWT_SECRET=.*/',
                'JWT_SECRET=' . $key,
                $content
            );
        } else {
            $content .= PHP_EOL . 'JWT_SECRET=' . $key;
        }

        if (file_put_contents($envFile, $content) === false) {
            $this->error('Unable to write to .env file.');
            return false;
        }

        return true;
    }
} 