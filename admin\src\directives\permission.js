// 权限指令 - 基于新4域架构的权限控制
// admin/src/directives/permission.js

import { useUserStore } from '@/stores/user'
import { DomainPermissionChecker } from '@/utils/domainPermissions'

/**
 * 权限指令 - 支持4域架构权限检查
 * 
 * 使用方式：
 * v-permission="'domain:business-analytics'"
 * v-permission="'module:data-dashboard'"  
 * v-permission="'page:/dashboard'"
 * v-permission="{ domain: 'business-analytics', module: 'data-dashboard' }"
 */
const permissionDirective = {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el, binding) {
  const permission = binding.value
  
  if (!permission) {
    console.warn('权限指令缺少权限配置')
    return
  }

  const userStore = useUserStore()
  const userRole = userStore.userInfo?.role
  
  if (!userRole) {
    hideElement(el)
    return
  }

  const checker = new DomainPermissionChecker(userRole)
  let hasPermission = false

  // 字符串格式权限检查
  if (typeof permission === 'string') {
    hasPermission = checkStringPermission(permission, checker)
  }
  // 对象格式权限检查
  else if (typeof permission === 'object') {
    hasPermission = checkObjectPermission(permission, checker)
  }
  // 兼容旧的权限数组格式
  else if (Array.isArray(permission)) {
    const userPermissions = userStore.userInfo?.permissions || []
    hasPermission = permission.some(perm => 
      userPermissions.includes(perm) || userPermissions.includes('*')
    )
  }

  if (!hasPermission) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

function checkStringPermission(permission, checker) {
  const [type, value] = permission.split(':')
  
  switch (type) {
    case 'domain':
      return checker.canAccessDomain(value)
    case 'module':
      return checker.canAccessModule(value)
    case 'page':
      return checker.canAccessPage(value)
    default:
      console.warn(`未知权限类型: ${type}`)
      return false
  }
}

function checkObjectPermission(permission, checker) {
  // 检查域权限
  if (permission.domain && !checker.canAccessDomain(permission.domain)) {
    return false
  }

  // 检查模块权限
  if (permission.module && !checker.canAccessModule(permission.module)) {
    return false
  }

  // 检查页面权限
  if (permission.page && !checker.canAccessPage(permission.page)) {
    return false
  }

  // 检查角色权限
  if (permission.roles && Array.isArray(permission.roles)) {
    if (!permission.roles.includes(checker.userRole)) {
      return false
    }
  }

  return true
}

// 角色指令 - 增强版本
const roleDirective = {
  mounted(el, binding) {
    checkRole(el, binding)
  },
  
  updated(el, binding) {
    checkRole(el, binding)
  }
}

function checkRole(el, binding) {
  const { value } = binding
  const userStore = useUserStore()
  const userRole = userStore.userInfo?.role

  if (!value) return

  const roles = Array.isArray(value) ? value : [value]
  const hasRole = roles.includes(userRole)

  if (!hasRole) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

/**
 * 权限按钮指令 - 不仅隐藏还禁用按钮
 * 
 * 使用方式：
 * v-permission-button="'domain:business-analytics'"
 */
const permissionButtonDirective = {
  mounted(el, binding) {
    checkPermissionButton(el, binding)
  },
  
  updated(el, binding) {
    checkPermissionButton(el, binding)
  }
}

function checkPermissionButton(el, binding) {
  const permission = binding.value
  
  if (!permission) {
    return
  }

  const userStore = useUserStore()
  const userRole = userStore.userInfo?.role
  
  if (!userRole) {
    disableElement(el)
    return
  }

  const checker = new DomainPermissionChecker(userRole)
  let hasPermission = false

  if (typeof permission === 'string') {
    hasPermission = checkStringPermission(permission, checker)
  } else if (typeof permission === 'object') {
    hasPermission = checkObjectPermission(permission, checker)
  }

  if (!hasPermission) {
    disableElement(el)
  } else {
    enableElement(el)
  }
}

function hideElement(el) {
  if (!el._originalDisplay) {
    el._originalDisplay = el.style.display
  }
  el.style.display = 'none'
}

function showElement(el) {
  if (el._originalDisplay !== undefined) {
    el.style.display = el._originalDisplay
  } else {
    el.style.display = ''
  }
}

function disableElement(el) {
  el.disabled = true
  el.style.opacity = '0.5'
  el.style.cursor = 'not-allowed'
  el.title = '您没有权限执行此操作'
}

function enableElement(el) {
  el.disabled = false
  el.style.opacity = ''
  el.style.cursor = ''
  el.title = ''
}

// 安装权限指令
export function setupPermissionDirectives(app) {
  app.directive('role', roleDirective)
  app.directive('permission', permissionDirective)
  app.directive('permission-button', permissionButtonDirective)
  
  console.log('✅ 4域架构权限指令已安装')
}

export { roleDirective, permissionDirective, permissionButtonDirective }