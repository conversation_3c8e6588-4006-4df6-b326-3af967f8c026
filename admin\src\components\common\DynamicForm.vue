<template>
  <div class="dynamic-form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :size="size"
      :disabled="disabled"
      class="modern-form"
      @submit.prevent="handleSubmit"
    >
      <!-- 表单项循环 -->
      <template v-for="field in formFields" :key="field.prop">
        <!-- 分组标题 -->
        <div v-if="field.type === 'group'" class="form-group">
          <div class="group-title">
            <i :class="field.icon" v-if="field.icon"></i>
            {{ field.label }}
          </div>
          <div class="group-description" v-if="field.description">
            {{ field.description }}
          </div>
        </div>
        
        <!-- 分割线 -->
        <el-divider v-else-if="field.type === 'divider'" :content-position="field.position || 'center'">
          {{ field.label }}
        </el-divider>
        
        <!-- 普通表单项 -->
        <el-form-item
          v-else
          :prop="field.prop"
          :label="field.label"
          :required="field.required"
          :class="getFormItemClass(field)"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input' || !field.type"
            v-model="formData[field.prop]"
            :type="field.inputType || 'text'"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :clearable="field.clearable !== false"
            :show-password="field.showPassword"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            @input="handleFieldChange(field, $event)"
            @blur="handleFieldBlur(field, $event)"
          >
            <template #prefix v-if="field.prefixIcon">
              <i :class="field.prefixIcon"></i>
            </template>
            <template #suffix v-if="field.suffixIcon">
              <i :class="field.suffixIcon"></i>
            </template>
            <template #prepend v-if="field.prepend">
              {{ field.prepend }}
            </template>
            <template #append v-if="field.append">
              {{ field.append }}
            </template>
          </el-input>
          
          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.prop]"
            type="textarea"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :rows="field.rows || 4"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            @input="handleFieldChange(field, $event)"
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.prop]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            :precision="field.precision"
            :disabled="field.disabled"
            :controls-position="field.controlsPosition"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :multiple="field.multiple"
            :filterable="field.filterable"
            :remote="field.remote"
            :remote-method="field.remoteMethod"
            :loading="field.loading"
            @change="handleFieldChange(field, $event)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>
          
          <!-- 级联选择器 -->
          <el-cascader
            v-else-if="field.type === 'cascader'"
            v-model="formData[field.prop]"
            :options="field.options"
            :props="field.cascaderProps"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :filterable="field.filterable"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            :picker-options="field.pickerOptions"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 时间选择器 -->
          <el-time-picker
            v-else-if="field.type === 'time'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue"
            :inactive-value="field.inactiveValue"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            @change="handleFieldChange(field, $event)"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            @change="handleFieldChange(field, $event)"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 滑块 -->
          <el-slider
            v-else-if="field.type === 'slider'"
            v-model="formData[field.prop]"
            :min="field.min || 0"
            :max="field.max || 100"
            :step="field.step || 1"
            :disabled="field.disabled"
            :show-input="field.showInput"
            :show-stops="field.showStops"
            :range="field.range"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 评分 -->
          <el-rate
            v-else-if="field.type === 'rate'"
            v-model="formData[field.prop]"
            :max="field.max || 5"
            :disabled="field.disabled"
            :allow-half="field.allowHalf"
            :show-text="field.showText"
            :texts="field.texts"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="field.type === 'color'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :show-alpha="field.showAlpha"
            :color-format="field.colorFormat"
            @change="handleFieldChange(field, $event)"
          />
          
          <!-- 文件上传 -->
          <el-upload
            v-else-if="field.type === 'upload'"
            :action="field.action"
            :headers="field.headers"
            :data="field.data"
            :name="field.name || 'file'"
            :multiple="field.multiple"
            :accept="field.accept"
            :limit="field.limit"
            :file-list="formData[field.prop] || []"
            :disabled="field.disabled"
            :list-type="field.listType || 'text'"
            :auto-upload="field.autoUpload !== false"
            :before-upload="field.beforeUpload"
            :on-success="(response, file, fileList) => handleUploadSuccess(field, response, file, fileList)"
            :on-error="(error, file, fileList) => handleUploadError(field, error, file, fileList)"
            :on-remove="(file, fileList) => handleUploadRemove(field, file, fileList)"
          >
            <el-button v-if="field.listType !== 'picture-card'" type="primary">
              <i class="el-icon-upload"></i> {{ field.uploadText || '点击上传' }}
            </el-button>
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          
          <!-- 自定义插槽 -->
          <slot
            v-else-if="field.type === 'slot'"
            :name="field.slotName"
            :field="field"
            :value="formData[field.prop]"
            :form-data="formData"
          ></slot>
          
          <!-- 字段描述 -->
          <div v-if="field.description" class="field-description">
            {{ field.description }}
          </div>
        </el-form-item>
      </template>
      
      <!-- 表单操作按钮 -->
      <el-form-item v-if="showActions" class="form-actions">
        <el-button
          v-for="action in actions"
          :key="action.key"
          :type="action.type || 'default'"
          :size="action.size || size"
          :loading="action.loading"
          :disabled="action.disabled"
          @click="handleAction(action)"
        >
          {{ action.label }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// Props 定义
const props = defineProps({
  // 表单字段配置
  fields: {
    type: Array,
    required: true
  },
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({})
  },
  // 表单规则
  rules: {
    type: Object,
    default: () => ({})
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '120px'
  },
  // 标签位置
  labelPosition: {
    type: String,
    default: 'right'
  },
  // 表单尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 操作按钮配置
  actions: {
    type: Array,
    default: () => [
      { key: 'submit', label: '提交', type: 'primary' },
      { key: 'reset', label: '重置', type: 'default' }
    ]
  }
})

// Emits 定义
const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'field-change',
  'field-blur',
  'action',
  'upload-success',
  'upload-error',
  'upload-remove'
])

// 响应式数据
const formRef = ref()
const formData = ref({})
const formRules = ref({})
const formFields = ref([])

// 计算属性
const computedFormData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.modelValue, (newVal) => {
  formData.value = { ...newVal }
}, { immediate: true, deep: true })

watch(() => props.fields, (newVal) => {
  formFields.value = newVal
  generateRules()
}, { immediate: true, deep: true })

watch(() => props.rules, (newVal) => {
  formRules.value = { ...newVal }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  generateRules()
})

// 方法
const generateRules = () => {
  const rules = { ...props.rules }
  
  props.fields.forEach(field => {
    if (field.required && !rules[field.prop]) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
    
    if (field.rules) {
      rules[field.prop] = field.rules
    }
  })
  
  formRules.value = rules
}

const getFormItemClass = (field) => {
  return [
    `form-item-${field.type}`,
    {
      'form-item-required': field.required,
      'form-item-disabled': field.disabled,
      'form-item-readonly': field.readonly
    }
  ]
}

const handleFieldChange = (field, value) => {
  formData.value[field.prop] = value
  emit('update:modelValue', formData.value)
  emit('field-change', field, value, formData.value)
  
  // 执行字段联动
  if (field.onChange) {
    field.onChange(value, formData.value, field)
  }
}

const handleFieldBlur = (field, event) => {
  emit('field-blur', field, event.target.value, formData.value)
  
  if (field.onBlur) {
    field.onBlur(event.target.value, formData.value, field)
  }
}

const handleAction = (action) => {
  if (action.key === 'submit') {
    handleSubmit()
  } else if (action.key === 'reset') {
    handleReset()
  } else {
    emit('action', action, formData.value)
  }
}

const handleSubmit = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      emit('submit', formData.value)
    } else {
      ElMessage.error('请检查表单填写是否正确')
    }
  })
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

const handleUploadSuccess = (field, response, file, fileList) => {
  formData.value[field.prop] = fileList
  emit('update:modelValue', formData.value)
  emit('upload-success', field, response, file, fileList)
}

const handleUploadError = (field, error, file, fileList) => {
  emit('upload-error', field, error, file, fileList)
  ElMessage.error('文件上传失败')
}

const handleUploadRemove = (field, file, fileList) => {
  formData.value[field.prop] = fileList
  emit('update:modelValue', formData.value)
  emit('upload-remove', field, file, fileList)
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  validateField: (prop) => formRef.value?.validateField(prop),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>

<style scoped lang="scss">
.dynamic-form-container {
  .modern-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
  }
}

.form-group {
  margin: var(--spacing-2xl) 0 var(--spacing-xl) 0;
  
  &:first-child {
    margin-top: 0;
  }
}

.group-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  
  i {
    color: var(--primary-500);
  }
}

.group-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
}

.field-description {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
}

.form-actions {
  margin-top: var(--spacing-3xl);
  text-align: center;
  
  :deep(.el-form-item__content) {
    justify-content: center;
    gap: var(--spacing-md);
  }
}

// 表单项样式优化
:deep(.el-form-item) {
  margin-bottom: var(--spacing-xl);
  
  .el-form-item__label {
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
  }
  
  .el-form-item__content {
    .el-input,
    .el-select,
    .el-cascader,
    .el-date-editor,
    .el-time-picker {
      width: 100%;
    }
    
    .el-input__inner,
    .el-textarea__inner {
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      transition: all var(--transition-fast);
      
      &:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
    }
    
    .el-button {
      border-radius: var(--radius-lg);
      font-weight: var(--font-weight-medium);
      transition: all var(--transition-fast);
      
      &.el-button--primary {
        background: var(--gradient-primary);
        border: none;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--shadow-lg);
        }
      }
    }
  }
  
  &.is-error {
    .el-input__inner,
    .el-textarea__inner {
      border-color: var(--error-500);
      
      &:focus {
        border-color: var(--error-500);
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
      }
    }
  }
  
  &.is-success {
    .el-input__inner,
    .el-textarea__inner {
      border-color: var(--success-500);
      
      &:focus {
        border-color: var(--success-500);
        box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dynamic-form-container {
    .modern-form {
      padding: var(--spacing-lg);
    }
  }
  
  :deep(.el-form) {
    .el-form-item {
      .el-form-item__label {
        text-align: left;
        padding-right: 0;
        margin-bottom: var(--spacing-sm);
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
