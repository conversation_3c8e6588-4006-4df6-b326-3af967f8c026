<?php

namespace App\Http\Controllers;

use App\Models\LandingPage;
use App\Models\WechatGroup;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;

/**
 * 落地页展示控制器
 * 用于处理落地页的前端展示和跳转
 */
class LandingPageViewController extends Controller
{
    /**
     * 显示落地页
     */
    public function show(Request $request, int $pageId): View
    {
        $page = LandingPage::where('id', $pageId)
            ->where('status', 1)
            ->firstOrFail();

        // 增加使用次数
        $page->incrementUseCount();

        // 获取用户信息（如果有推荐人）
        $referrer = null;
        if ($request->has('ref')) {
            $referrer = User::find($request->get('ref'));
        }

        // 获取群组信息（如果是群组页面）
        $group = null;
        if ($request->has('group_id')) {
            $group = WechatGroup::find($request->get('group_id'));
        }

        // 检测是否为微信浏览器
        $isWechat = $this->isWechatBrowser($request);

        // 获取目标URL
        $targetUrl = $this->getTargetUrl($request, $page, $group, $referrer);

        return view('landing.page', compact('page', 'referrer', 'group', 'isWechat', 'targetUrl'));
    }

    /**
     * 预览落地页
     */
    public function preview(Request $request, int $pageId): View
    {
        $page = LandingPage::findOrFail($pageId);

        // 模拟数据
        $referrer = null;
        $group = null;
        $isWechat = true;
        $targetUrl = '#';

        return view('landing.page', compact('page', 'referrer', 'group', 'isWechat', 'targetUrl'));
    }

    /**
     * 群组落地页
     */
    public function group(Request $request, int $groupId): View
    {
        $group = WechatGroup::where('id', $groupId)
            ->where('status', 1)
            ->firstOrFail();

        // 获取群组类型的落地页模板
        $page = LandingPage::where('page_type', LandingPage::TYPE_GROUP)
            ->where('status', 1)
            ->first();

        // 如果没有找到群组类型的模板，使用默认模板
        if (!$page) {
            $page = $this->createDefaultGroupPage();
        }

        // 获取推荐人信息
        $referrer = null;
        if ($request->has('ref')) {
            $referrer = User::find($request->get('ref'));
        }

        // 检测是否为微信浏览器
        $isWechat = $this->isWechatBrowser($request);

        // 目标URL就是群组的二维码或支付页面
        $targetUrl = $group->price > 0 
            ? route('group.payment', ['group' => $group->id, 'ref' => $request->get('ref')])
            : $group->qr_code_url;

        return view('landing.group', compact('page', 'group', 'referrer', 'isWechat', 'targetUrl'));
    }

    /**
     * 邀请落地页
     */
    public function invite(Request $request): View
    {
        // 获取邀请人信息
        $referrer = null;
        if ($request->has('ref')) {
            $referrer = User::find($request->get('ref'));
        }

        // 获取邀请类型的落地页模板
        $page = LandingPage::where('page_type', LandingPage::TYPE_INVITE)
            ->where('status', 1)
            ->first();

        // 如果没有找到邀请类型的模板，使用默认模板
        if (!$page) {
            $page = $this->createDefaultInvitePage();
        }

        // 检测是否为微信浏览器
        $isWechat = $this->isWechatBrowser($request);

        // 目标URL是注册页面
        $targetUrl = route('register', ['ref' => $request->get('ref')]);

        return view('landing.invite', compact('page', 'referrer', 'isWechat', 'targetUrl'));
    }

    /**
     * 检测是否为微信浏览器
     */
    private function isWechatBrowser(Request $request): bool
    {
        $userAgent = $request->userAgent();
        return str_contains($userAgent, 'MicroMessenger');
    }

    /**
     * 获取目标URL
     */
    private function getTargetUrl(Request $request, LandingPage $page, ?WechatGroup $group, ?User $referrer): string
    {
        // 如果有群组，返回群组相关URL
        if ($group) {
            return $group->price > 0 
                ? route('group.payment', ['group' => $group->id, 'ref' => $referrer?->id])
                : ($group->qr_code_url ?? '#');
        }

        // 根据页面类型返回不同的URL
        switch ($page->page_type) {
            case LandingPage::TYPE_INVITE:
                return route('register', ['ref' => $referrer?->id]);
            case LandingPage::TYPE_PAYMENT:
                return route('payment.index', ['ref' => $referrer?->id]);
            case LandingPage::TYPE_GENERAL:
            default:
                return route('home', ['ref' => $referrer?->id]);
        }
    }

    /**
     * 创建默认群组页面
     */
    private function createDefaultGroupPage(): LandingPage
    {
        return new LandingPage([
            'page_name' => '默认群组页面',
            'page_type' => LandingPage::TYPE_GROUP,
            'page_content' => $this->getDefaultGroupPageContent(),
            'page_config' => LandingPage::getDefaultConfig(LandingPage::TYPE_GROUP),
            'status' => 1,
        ]);
    }

    /**
     * 创建默认邀请页面
     */
    private function createDefaultInvitePage(): LandingPage
    {
        return new LandingPage([
            'page_name' => '默认邀请页面',
            'page_type' => LandingPage::TYPE_INVITE,
            'page_content' => $this->getDefaultInvitePageContent(),
            'page_config' => LandingPage::getDefaultConfig(LandingPage::TYPE_INVITE),
            'status' => 1,
        ]);
    }

    /**
     * 获取默认群组页面内容
     */
    private function getDefaultGroupPageContent(): string
    {
        return '
            <div class="group-intro">
                <h2>{{group.title}}</h2>
                <p>{{group.description}}</p>
                <div class="group-info">
                    <p>成员数量：{{group.current_members}}/{{group.member_limit}}</p>
                    <p>入群费用：{{group.price}}元</p>
                </div>
            </div>
        ';
    }

    /**
     * 获取默认邀请页面内容
     */
    private function getDefaultInvitePageContent(): string
    {
        return '
            <div class="invite-intro">
                <h2>邀请您加入</h2>
                <p>{{referrer.name}}邀请您加入我们的平台</p>
                <div class="invite-benefits">
                    <ul>
                        <li>免费注册，立即获得推广权限</li>
                        <li>多级分销，轻松赚取佣金</li>
                        <li>专业团队，全程指导</li>
                    </ul>
                </div>
            </div>
        ';
    }
} 