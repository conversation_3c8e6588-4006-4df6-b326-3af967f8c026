/**
 * 数据权限控制工具（第一阶段优化版）
 * 实现层级化的数据访问权限控制，确保群组创建功能完整性
 */

import { useUserStore } from '@/stores/user'
import { roleHierarchy, canViewUserData, getViewableRoles } from '@/config/navigation'

/**
 * 检查群组创建权限（保护核心功能）
 * @param {string} userRole - 用户角色
 * @returns {boolean}
 */
export function canCreateGroup(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.groupCreatePermission : false
}

/**
 * 检查数据导出权限
 * @param {string} userRole - 用户角色
 * @returns {boolean}
 */
export function canExportData(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.dataExportPermission : false
}

/**
 * 获取用户的数据大屏权限范围
 * @param {string} userRole - 用户角色
 * @returns {string}
 */
export function getDashboardScope(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.dashboardScope : 'user_personal'
}

/**
 * 获取用户的财务数据权限范围
 * @param {string} userRole - 用户角色
 * @returns {string}
 */
export function getFinanceScope(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.financeScope : 'user_consumption'
}

/**
 * 数据过滤器 - 根据用户角色过滤数据
 * @param {Array} data - 原始数据数组
 * @param {string} currentUserRole - 当前用户角色
 * @param {string} dataUserRoleField - 数据中用户角色字段名，默认为 'role'
 * @param {string} dataUserIdField - 数据中用户ID字段名，默认为 'user_id'
 * @returns {Array} 过滤后的数据
 */
export function filterDataByPermission(data, currentUserRole, dataUserRoleField = 'role', dataUserIdField = 'user_id') {
  if (!data || !Array.isArray(data)) return []
  
  const userStore = useUserStore()
  const currentUserId = userStore.userInfo?.id
  
  // 超级管理员可以查看所有数据
  if (currentUserRole === 'admin') {
    return data
  }
  
  const viewableRoles = getViewableRoles(currentUserRole)
  
  return data.filter(item => {
    const itemRole = item[dataUserRoleField]
    const itemUserId = item[dataUserIdField]
    
    // 检查角色权限
    if (!viewableRoles.includes(itemRole)) {
      return false
    }
    
    // 如果是同级用户，只能查看自己的数据（除了管理员角色）
    if (itemRole === currentUserRole && currentUserRole !== 'admin') {
      return itemUserId === currentUserId
    }
    
    return true
  })
}

/**
 * 构建数据查询的权限条件
 * @param {string} userRole - 用户角色
 * @param {number} userId - 用户ID
 * @returns {Object} 查询条件对象
 */
export function buildDataPermissionQuery(userRole, userId) {
  const viewableRoles = getViewableRoles(userRole)
  
  const query = {
    roles: viewableRoles,
    includeSubordinates: true
  }
  
  // 如果不是管理员，添加用户层级限制
  if (userRole !== 'admin') {
    query.hierarchyUserId = userId
    query.maxLevel = roleHierarchy[userRole]?.level || 5
  }
  
  return query
}

/**
 * 检查是否可以访问特定用户的数据
 * @param {string} currentUserRole - 当前用户角色
 * @param {number} currentUserId - 当前用户ID
 * @param {string} targetUserRole - 目标用户角色
 * @param {number} targetUserId - 目标用户ID
 * @returns {boolean}
 */
export function canAccessUserData(currentUserRole, currentUserId, targetUserRole, targetUserId) {
  // 超级管理员可以访问所有数据
  if (currentUserRole === 'admin') {
    return true
  }
  
  // 用户可以访问自己的数据
  if (currentUserId === targetUserId) {
    return true
  }
  
  // 检查角色层级权限
  return canViewUserData(currentUserRole, targetUserRole)
}

/**
 * 数据大屏权限过滤器
 * @param {Object} dashboardData - 仪表板数据
 * @param {string} userRole - 用户角色
 * @param {number} userId - 用户ID
 * @returns {Object} 过滤后的仪表板数据
 */
export function filterDashboardData(dashboardData, userRole, userId) {
  if (!dashboardData) return {}
  
  const filteredData = { ...dashboardData }
  const viewableRoles = getViewableRoles(userRole)
  
  // 过滤用户统计数据
  if (filteredData.userStats) {
    filteredData.userStats = filterStatsByRoles(filteredData.userStats, viewableRoles)
  }
  
  // 过滤订单数据
  if (filteredData.orderStats) {
    filteredData.orderStats = filterStatsByRoles(filteredData.orderStats, viewableRoles)
  }
  
  // 过滤群组数据
  if (filteredData.groupStats) {
    filteredData.groupStats = filterStatsByRoles(filteredData.groupStats, viewableRoles)
  }
  
  // 过滤财务数据
  if (filteredData.financeStats) {
    filteredData.financeStats = filterFinanceDataByRole(filteredData.financeStats, userRole, userId)
  }
  
  // 过滤实时活动数据
  if (filteredData.realtimeActivities) {
    filteredData.realtimeActivities = filterDataByPermission(
      filteredData.realtimeActivities, 
      userRole, 
      'user_role', 
      'user_id'
    )
  }
  
  return filteredData
}

/**
 * 根据角色过滤统计数据
 * @param {Object} stats - 统计数据
 * @param {Array} viewableRoles - 可查看的角色列表
 * @returns {Object} 过滤后的统计数据
 */
function filterStatsByRoles(stats, viewableRoles) {
  const filteredStats = {}
  
  Object.keys(stats).forEach(key => {
    if (key === 'total' || viewableRoles.some(role => key.includes(role))) {
      filteredStats[key] = stats[key]
    }
  })
  
  return filteredStats
}

/**
 * 根据角色过滤财务数据
 * @param {Object} financeData - 财务数据
 * @param {string} userRole - 用户角色
 * @param {number} userId - 用户ID
 * @returns {Object} 过滤后的财务数据
 */
function filterFinanceDataByRole(financeData, userRole, userId) {
  const filteredData = { ...financeData }
  
  // 根据角色限制财务数据的可见性
  switch (userRole) {
    case 'admin':
      // 管理员可以看到所有财务数据
      break
    case 'substation':
      // 分站管理员只能看到本分站的财务数据
      filteredData.scope = 'substation'
      filteredData.userId = userId
      break
    case 'agent':
      // 代理商只能看到自己团队的佣金数据
      filteredData.scope = 'agent_team'
      filteredData.userId = userId
      break
    case 'distributor':
      // 分销员只能看到自己的佣金数据
      filteredData.scope = 'self'
      filteredData.userId = userId
      break
    case 'group_owner':
      // 群主只能看到自己群组的收入数据
      filteredData.scope = 'group_owner'
      filteredData.userId = userId
      break
    case 'user':
      // 普通用户只能看到自己的消费数据
      filteredData.scope = 'user_consumption'
      filteredData.userId = userId
      break
  }
  
  return filteredData
}

/**
 * API请求权限中间件
 * 在API请求中自动添加权限参数
 * @param {Object} requestConfig - 请求配置
 * @param {string} userRole - 用户角色
 * @param {number} userId - 用户ID
 * @returns {Object} 增强后的请求配置
 */
export function enhanceRequestWithPermission(requestConfig, userRole, userId) {
  const enhancedConfig = { ...requestConfig }
  
  // 添加权限查询参数
  const permissionQuery = buildDataPermissionQuery(userRole, userId)
  
  if (!enhancedConfig.params) {
    enhancedConfig.params = {}
  }
  
  enhancedConfig.params = {
    ...enhancedConfig.params,
    ...permissionQuery
  }
  
  return enhancedConfig
}

/**
 * 检查是否可以执行特定操作
 * @param {string} action - 操作类型 (create, update, delete, view)
 * @param {string} resource - 资源类型 (user, group, order, etc.)
 * @param {string} userRole - 用户角色
 * @param {Object} targetData - 目标数据对象
 * @returns {boolean}
 */
export function canPerformAction(action, resource, userRole, targetData = {}) {
  const userStore = useUserStore()
  const currentUserId = userStore.userInfo?.id
  
  // 超级管理员可以执行所有操作
  if (userRole === 'admin') {
    return true
  }
  
  // 根据资源类型和操作类型检查权限
  switch (resource) {
    case 'user':
      return canManageUser(action, userRole, currentUserId, targetData)
    case 'group':
      return canManageGroup(action, userRole, currentUserId, targetData)
    case 'order':
      return canManageOrder(action, userRole, currentUserId, targetData)
    case 'finance':
      return canViewFinance(action, userRole, currentUserId, targetData)
    default:
      return false
  }
}

// 辅助函数：检查用户管理权限
function canManageUser(action, userRole, currentUserId, targetData) {
  const targetRole = targetData.role
  const targetUserId = targetData.id
  
  if (action === 'view') {
    return canViewUserData(userRole, targetRole)
  }
  
  // 只有管理员和分站管理员可以创建/修改/删除用户
  if (['create', 'update', 'delete'].includes(action)) {
    return ['admin', 'substation'].includes(userRole)
  }
  
  return false
}

// 辅助函数：检查群组管理权限
function canManageGroup(action, userRole, currentUserId, targetData) {
  const groupOwnerId = targetData.user_id || targetData.owner_id
  
  if (action === 'view') {
    return canViewUserData(userRole, targetData.owner_role || 'user')
  }
  
  if (action === 'create') {
    // 所有角色都可以创建群组（保护群组创建功能）
    return true
  }
  
  if (['update', 'delete'].includes(action)) {
    // 只有群组所有者或更高级别的管理员可以修改/删除
    return currentUserId === groupOwnerId || ['admin', 'substation'].includes(userRole)
  }
  
  return false
}

// 辅助函数：检查订单管理权限
function canManageOrder(action, userRole, currentUserId, targetData) {
  const orderUserId = targetData.user_id
  const orderUserRole = targetData.user_role || 'user'
  
  if (action === 'view') {
    return canViewUserData(userRole, orderUserRole) || currentUserId === orderUserId
  }
  
  // 订单通常只能查看，不能修改
  return false
}

// 辅助函数：检查财务查看权限
function canViewFinance(action, userRole, currentUserId, targetData) {
  if (action !== 'view') return false
  
  const financeUserId = targetData.user_id
  const financeUserRole = targetData.user_role || 'user'
  
  return canViewUserData(userRole, financeUserRole) || currentUserId === financeUserId
}
