import{_ as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                      *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               */import{a_ as e,aZ as l,aY as t,bm as o,bn as s,at as n,T as r,am as u,ai as d,a4 as i,bh as p,bi as _,U as c,a$ as m,bw as f,bx as v,bp as b,bq as g,by as h,b9 as y,b8 as C,ay as w,Q as j,R as x}from"./element-plus-h2SQQM64.js";import{S as k}from"./StatCard-u_ssO_Ky.js";import{L as V}from"./LineChart-CydsJ2U8.js";import{D as F}from"./DoughnutChart-CCHHIMjz.js";import{g as z,a as A,b as U}from"./substation-C0LtbWrR.js";import{r as D,L as E,c as S,e as L,k as Z,l as M,t as P,E as R,z as Y,D as q,u as N,A as T,y as $}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */import"./chunk-KZPPZA2C-BZQYgWVq.js";const I={class:"substation-finance"},J={class:"action-bar"},K={class:"amount"},O={class:"amount"},Q={class:"pagination"},W=a({__name:"SubstationFinance",setup(a){const W=D(!1),B=D("month"),G=D(1),H=D(20),X=D(!1),aa=D(!1),ea=D({}),la=D([]),ta=D({data:[],total:0}),oa=E({start_date:"",end_date:"",format:"array"}),sa=S(()=>{const a=ea.value.trends||[];return{labels:a.map(a=>a.date),datasets:[{label:"收入",data:a.map(a=>a.revenue),borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4},{label:"利润",data:a.map(a=>a.profit),borderColor:"#67C23A",backgroundColor:"rgba(103, 194, 58, 0.1)",tension:.4}]}}),na=S(()=>{const a=(ea.value.commission||{}).commission_by_level||[];return{labels:a.map(a=>a.commission_level),datasets:[{data:a.map(a=>a.total_commission),backgroundColor:["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399"]}]}}),ra={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},ua={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},da=async()=>{try{W.value=!0;const a=await z({period:B.value});ea.value=a.data,la.value=a.data.top_agents||[]}catch(a){j.error("加载财务统计失败")}finally{W.value=!1}},ia=async()=>{try{const a=await A({page:G.value,limit:H.value});ta.value=a.data}catch(a){j.error("加载结算记录失败")}},pa=()=>{da()},_a=a=>{H.value=a,ia()},ca=a=>{G.value=a,ia()},ma=()=>{X.value=!0;const a=new Date;oa.start_date=new Date(a.getFullYear(),a.getMonth(),1),oa.end_date=new Date(a.getFullYear(),a.getMonth()+1,0)},fa=async()=>{try{aa.value=!0;await U({start_date:ga(oa.start_date),end_date:ga(oa.end_date),format:oa.format});"excel"===oa.format?j.success("报表生成成功，正在下载..."):j.success("报表生成成功"),X.value=!1}catch(a){j.error("生成报表失败")}finally{aa.value=!1}},va=async()=>{try{await x.confirm("确定要进行批量结算吗？","确认操作",{type:"warning"}),j.info("请先选择要结算的记录")}catch(a){}},ba=()=>{j.info("导出功能开发中...")},ga=a=>a?new Date(a).toLocaleString("zh-CN"):"",ha=a=>({pending:"待结算",confirmed:"已结算",cancelled:"已取消"}[a]||"未知");return L(()=>{da(),ia()}),(a,x)=>{const z=e,A=l,U=s,D=o,E=t,S=r,L=n,da=_,ia=p,ya=m,Ca=v,wa=h,ja=g,xa=C,ka=y,Va=b,Fa=w,za=f;return M(),Z("div",I,[x[25]||(x[25]=P("div",{class:"page-header"},[P("h2",null,"分站财务管理"),P("p",null,"管理分站的财务数据、佣金结算和收入统计")],-1)),R(A,{gutter:20,class:"stats-row"},{default:Y(()=>[R(z,{span:6},{default:Y(()=>[R(k,{title:"总收入",value:ea.value.overview?.total_revenue||0,icon:"Money",color:"#67C23A",suffix:"元"},null,8,["value"])]),_:1}),R(z,{span:6},{default:Y(()=>[R(k,{title:"总佣金",value:ea.value.overview?.total_commission||0,icon:"Coin",color:"#E6A23C",suffix:"元"},null,8,["value"])]),_:1}),R(z,{span:6},{default:Y(()=>[R(k,{title:"净利润",value:ea.value.overview?.net_profit||0,icon:"TrendCharts",color:"#409EFF",suffix:"元"},null,8,["value"])]),_:1}),R(z,{span:6},{default:Y(()=>[R(k,{title:"活跃用户",value:ea.value.overview?.active_users||0,icon:"User",color:"#F56C6C",suffix:"人"},null,8,["value"])]),_:1})]),_:1}),P("div",J,[R(A,{gutter:20},{default:Y(()=>[R(z,{span:12},{default:Y(()=>[R(E,null,{header:Y(()=>x[8]||(x[8]=[P("span",null,"时间筛选",-1)])),default:Y(()=>[R(D,{modelValue:B.value,"onUpdate:modelValue":x[0]||(x[0]=a=>B.value=a),onChange:pa},{default:Y(()=>[R(U,{label:"today"},{default:Y(()=>x[9]||(x[9]=[q("今日",-1)])),_:1,__:[9]}),R(U,{label:"week"},{default:Y(()=>x[10]||(x[10]=[q("本周",-1)])),_:1,__:[10]}),R(U,{label:"month"},{default:Y(()=>x[11]||(x[11]=[q("本月",-1)])),_:1,__:[11]}),R(U,{label:"quarter"},{default:Y(()=>x[12]||(x[12]=[q("本季度",-1)])),_:1,__:[12]}),R(U,{label:"year"},{default:Y(()=>x[13]||(x[13]=[q("本年",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),R(z,{span:12},{default:Y(()=>[R(E,null,{header:Y(()=>x[14]||(x[14]=[P("span",null,"快捷操作",-1)])),default:Y(()=>[R(L,{type:"primary",onClick:ma},{default:Y(()=>[R(S,null,{default:Y(()=>[R(N(u))]),_:1}),x[15]||(x[15]=q(" 生成报表 ",-1))]),_:1,__:[15]}),R(L,{type:"success",onClick:va},{default:Y(()=>[R(S,null,{default:Y(()=>[R(N(d))]),_:1}),x[16]||(x[16]=q(" 批量结算 ",-1))]),_:1,__:[16]}),R(L,{type:"info",onClick:ba},{default:Y(()=>[R(S,null,{default:Y(()=>[R(N(i))]),_:1}),x[17]||(x[17]=q(" 导出数据 ",-1))]),_:1,__:[17]})]),_:1})]),_:1})]),_:1})]),R(A,{gutter:20,class:"charts-row"},{default:Y(()=>[R(z,{span:12},{default:Y(()=>[R(E,null,{header:Y(()=>x[18]||(x[18]=[P("span",null,"收入趋势",-1)])),default:Y(()=>[R(V,{data:sa.value,options:ra,height:"300px"},null,8,["data"])]),_:1})]),_:1}),R(z,{span:12},{default:Y(()=>[R(E,null,{header:Y(()=>x[19]||(x[19]=[P("span",null,"佣金分布",-1)])),default:Y(()=>[R(F,{data:na.value,options:ua,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),R(E,{class:"top-agents-card"},{header:Y(()=>x[20]||(x[20]=[P("span",null,"顶级代理商排行",-1)])),default:Y(()=>[R(ia,{data:la.value,stripe:""},{default:Y(()=>[R(da,{prop:"agent.agent_name",label:"代理商名称"}),R(da,{prop:"agent.agent_code",label:"代理商编码"}),R(da,{prop:"total_commission",label:"总佣金",sortable:""},{default:Y(({row:a})=>[P("span",K,"¥"+c(a.total_commission),1)]),_:1}),R(da,{prop:"order_count",label:"订单数量",sortable:""}),R(da,{label:"操作"},{default:Y(({row:a})=>[R(L,{size:"small",onClick:e=>{return l=a.agent.id,void j.info(`查看代理商 ${l} 详情`);var l}},{default:Y(()=>x[21]||(x[21]=[q(" 查看详情 ",-1)])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),R(E,{class:"settlement-records"},{header:Y(()=>x[22]||(x[22]=[P("span",null,"结算记录",-1)])),default:Y(()=>[T((M(),$(ia,{data:ta.value.data,stripe:""},{default:Y(()=>[R(da,{prop:"agent.agent_name",label:"代理商"}),R(da,{prop:"order.order_no",label:"订单号"}),R(da,{prop:"commission_amount",label:"佣金金额"},{default:Y(({row:a})=>[P("span",O,"¥"+c(a.commission_amount),1)]),_:1}),R(da,{prop:"settled_at",label:"结算时间"},{default:Y(({row:a})=>[q(c(ga(a.settled_at)),1)]),_:1}),R(da,{prop:"status",label:"状态"},{default:Y(({row:a})=>{return[R(ya,{type:(e=a.status,{pending:"warning",confirmed:"success",cancelled:"danger"}[e]||"info")},{default:Y(()=>[q(c(ha(a.status)),1)]),_:2},1032,["type"])];var e}),_:1})]),_:1},8,["data"])),[[za,W.value]]),P("div",Q,[R(Ca,{"current-page":G.value,"onUpdate:currentPage":x[1]||(x[1]=a=>G.value=a),"page-size":H.value,"onUpdate:pageSize":x[2]||(x[2]=a=>H.value=a),total:ta.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_a,onCurrentChange:ca},null,8,["current-page","page-size","total"])])]),_:1}),R(Fa,{modelValue:X.value,"onUpdate:modelValue":x[7]||(x[7]=a=>X.value=a),title:"生成财务报表",width:"500px"},{footer:Y(()=>[R(L,{onClick:x[6]||(x[6]=a=>X.value=!1)},{default:Y(()=>x[23]||(x[23]=[q("取消",-1)])),_:1,__:[23]}),R(L,{type:"primary",onClick:fa,loading:aa.value},{default:Y(()=>x[24]||(x[24]=[q(" 生成报表 ",-1)])),_:1,__:[24]},8,["loading"])]),default:Y(()=>[R(Va,{model:oa,"label-width":"100px"},{default:Y(()=>[R(ja,{label:"开始日期"},{default:Y(()=>[R(wa,{modelValue:oa.start_date,"onUpdate:modelValue":x[3]||(x[3]=a=>oa.start_date=a),type:"date",placeholder:"选择开始日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),R(ja,{label:"结束日期"},{default:Y(()=>[R(wa,{modelValue:oa.end_date,"onUpdate:modelValue":x[4]||(x[4]=a=>oa.end_date=a),type:"date",placeholder:"选择结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),R(ja,{label:"报表格式"},{default:Y(()=>[R(ka,{modelValue:oa.format,"onUpdate:modelValue":x[5]||(x[5]=a=>oa.format=a),style:{width:"100%"}},{default:Y(()=>[R(xa,{label:"在线查看",value:"array"}),R(xa,{label:"Excel文件",value:"excel"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-134a2421"]]);export{W as default};
