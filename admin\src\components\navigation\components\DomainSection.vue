<!-- 域区块组件 -->
<template>
  <div class="domain-section" :class="domainClasses">
    <!-- 域标题头部 -->
    <div 
      class="domain-header"
      @click="handleDomainToggle"
      :title="collapsed ? domain.title : ''"
    >
      <div class="domain-icon-container">
        <div class="domain-icon" :style="{ color: domain.color }">
          <el-icon><component :is="domain.icon" /></el-icon>
        </div>
        <div class="icon-glow" :style="{ background: `${domain.color}20` }"></div>
      </div>
      
      <div class="domain-content" v-if="!collapsed">
        <div class="domain-info">
          <h3 class="domain-title">{{ domain.title }}</h3>
          <p class="domain-description">{{ domain.description }}</p>
        </div>
        
        <div class="domain-meta">
          <div class="module-count">{{ domain.modules.length }} 个功能</div>
          <div class="expand-indicator" :class="{ 'expanded': expanded }">
            <el-icon>
              <ArrowDown />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 域模块列表 -->
    <CollapseTransition>
      <div v-show="expanded || collapsed" class="domain-modules">
        <ModuleItem
          v-for="module in visibleModules"
          :key="module.key"
          :module="module"
          :collapsed="collapsed"
          :active="isModuleActive(module)"
          @click="handleModuleClick"
          @badge-update="handleBadgeUpdate"
        />
        
        <!-- 更多模块按钮 -->
        <div 
          v-if="hasMoreModules && !showAllModules" 
          class="more-modules-btn"
          @click="showAllModules = true"
        >
          <el-icon><MoreFilled /></el-icon>
          <span>显示更多 ({{ hiddenModulesCount }})</span>
        </div>
      </div>
    </CollapseTransition>

    <!-- 域操作菜单 -->
    <div v-if="!collapsed && expanded" class="domain-actions">
      <el-button
        v-for="action in domainActions"
        :key="action.key"
        :icon="action.icon"
        :type="action.type || 'text'"
        size="small"
        @click="handleDomainAction(action)"
      >
        {{ action.label }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowDown, MoreFilled } from '@element-plus/icons-vue'
import ModuleItem from './ModuleItem.vue'
import CollapseTransition from '@/components/transitions/CollapseTransition.vue'

const props = defineProps({
  domain: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  active: {
    type: Boolean,
    default: false
  },
  expanded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle', 'module-click', 'badge-update', 'domain-action'])

const route = useRoute()
const showAllModules = ref(false)
const maxVisibleModules = 6

// 计算属性
const domainClasses = computed(() => ({
  'domain-collapsed': props.collapsed,
  'domain-active': props.active,
  'domain-expanded': props.expanded
}))

const visibleModules = computed(() => {
  if (showAllModules.value || props.collapsed) {
    return props.domain.modules
  }
  return props.domain.modules.slice(0, maxVisibleModules)
})

const hasMoreModules = computed(() => {
  return props.domain.modules.length > maxVisibleModules
})

const hiddenModulesCount = computed(() => {
  return Math.max(0, props.domain.modules.length - maxVisibleModules)
})

const domainActions = computed(() => {
  // 根据域类型和用户权限动态生成操作按钮
  const actions = []
  
  switch (props.domain.key) {
    case 'business-core':
      actions.push(
        { key: 'quick-create', label: '快速创建', icon: 'Plus', type: 'primary' },
        { key: 'batch-manage', label: '批量管理', icon: 'Operation' }
      )
      break
    case 'operation-management':
      actions.push(
        { key: 'system-check', label: '系统检查', icon: 'Monitor' },
        { key: 'maintenance', label: '维护模式', icon: 'Tools' }
      )
      break
    case 'data-analytics':
      actions.push(
        { key: 'export-data', label: '数据导出', icon: 'Download' },
        { key: 'create-report', label: '生成报表', icon: 'Document' }
      )
      break
    case 'system-config':
      actions.push(
        { key: 'backup', label: '系统备份', icon: 'FolderOpened' },
        { key: 'restore', label: '系统还原', icon: 'RefreshLeft' }
      )
      break
  }
  
  return actions
})

// 方法
const handleDomainToggle = () => {
  if (props.collapsed) return
  emit('toggle', props.domain.key)
}

const handleModuleClick = (module) => {
  emit('module-click', module)
}

const handleBadgeUpdate = (moduleKey, count) => {
  emit('badge-update', moduleKey, count)
}

const handleDomainAction = (action) => {
  emit('domain-action', {
    domain: props.domain.key,
    action: action.key,
    data: action
  })
}

const isModuleActive = (module) => {
  const currentPath = route.path
  
  if (module.path === currentPath) return true
  
  if (module.children) {
    return module.children.some(child => child.path === currentPath)
  }
  
  return false
}

// 监听展开状态变化，重置显示更多状态
watch(() => props.expanded, (newValue) => {
  if (!newValue) {
    showAllModules.value = false
  }
})
</script>

<style lang="scss" scoped>
.domain-section {
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      var(--domain-color, #6366f1) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }
  
  &.domain-active {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--domain-color, #6366f1);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
    
    &::before {
      opacity: 1;
    }
  }
  
  &.domain-expanded {
    background: rgba(255, 255, 255, 0.95);
    
    .domain-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }
  }
  
  &.domain-collapsed {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 8px;
    margin-bottom: 8px;
    
    .domain-header {
      padding: 12px;
      justify-content: center;
    }
    
    .domain-icon-container {
      margin-right: 0;
    }
  }
}

.domain-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background: rgba(0, 0, 0, 0.02);
    
    .icon-glow {
      opacity: 0.8;
      transform: scale(1.2);
    }
  }
}

.domain-icon-container {
  position: relative;
  margin-right: 16px;
  
  .domain-icon {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    
    .el-icon {
      font-size: 20px;
    }
  }
  
  .icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
  }
}

.domain-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.domain-info {
  flex: 1;
  
  .domain-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
    line-height: 1.3;
  }
  
  .domain-description {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
  }
}

.domain-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .module-count {
    font-size: 11px;
    color: #9ca3af;
    background: rgba(156, 163, 175, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
  }
  
  .expand-indicator {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: transform 0.3s ease;
    
    &.expanded {
      transform: rotate(180deg);
    }
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.domain-modules {
  padding: 0 12px 12px 12px;
}

.more-modules-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  margin: 8px 8px 0 8px;
  background: rgba(99, 102, 241, 0.05);
  border: 1px dashed rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  color: #6366f1;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.5);
  }
  
  .el-icon {
    font-size: 14px;
  }
}

.domain-actions {
  display: flex;
  gap: 8px;
  padding: 12px 20px 16px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(0, 0, 0, 0.01);
  
  .el-button {
    font-size: 11px;
    height: 28px;
    padding: 0 12px;
    
    &:not(.el-button--primary) {
      color: #6b7280;
      border-color: rgba(107, 114, 128, 0.3);
      
      &:hover {
        color: #6366f1;
        border-color: rgba(99, 102, 241, 0.5);
        background: rgba(99, 102, 241, 0.05);
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .domain-section.domain-collapsed {
    .domain-icon-container .domain-icon {
      width: 36px;
      height: 36px;
      
      .el-icon {
        font-size: 16px;
      }
    }
  }
}
</style>