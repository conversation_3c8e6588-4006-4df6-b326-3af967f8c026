<template>
  <div class="template-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-title">
        <h2>📋 群组模板管理</h2>
        <p>管理群组模板，提高群组创建效率</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建模板
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="关键词">
          <el-input 
            v-model="queryParams.keyword" 
            placeholder="模板名称/描述/代码" 
            clearable 
            style="width: 200px"
            @clear="getList"
            @keyup.enter="getList"
          />
        </el-form-item>

        <el-form-item label="分类">
          <el-select v-model="queryParams.category" placeholder="全部分类" clearable style="width: 150px">
            <el-option 
              v-for="(name, code) in categories" 
              :key="code" 
              :label="name" 
              :value="code" 
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select v-model="queryParams.is_preset" placeholder="全部类型" clearable style="width: 120px">
            <el-option label="预设模板" :value="true" />
            <el-option label="自定义模板" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="queryParams.is_active" placeholder="全部状态" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 模板列表 -->
    <el-card class="table-card" v-loading="loading">
      <div class="table-header">
        <div class="table-title">
          <span>模板列表 ({{ total }})</span>
        </div>
        <div class="table-actions">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="table">列表</el-radio-button>
            <el-radio-button label="card">卡片</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 表格视图 -->
      <el-table 
        v-if="viewMode === 'table'" 
        :data="templateList" 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="模板信息" min-width="200">
          <template #default="scope">
            <div class="template-info">
              <div class="template-cover">
                <img v-if="scope.row.cover_image_url" :src="scope.row.cover_image_url" />
                <el-icon v-else><Picture /></el-icon>
              </div>
              <div class="template-details">
                <div class="template-name">{{ scope.row.template_name }}</div>
                <div class="template-code">{{ scope.row.template_code }}</div>
                <div class="template-desc">{{ scope.row.description || 'No description' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分类" width="120">
          <template #default="scope">
            <el-tag :type="getCategoryTagType(scope.row.category)">
              {{ scope.row.category_name }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_preset ? 'warning' : 'info'">
              {{ scope.row.is_preset ? '预设' : '自定义' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-switch 
              v-model="scope.row.is_active" 
              @change="toggleStatus(scope.row)"
              :disabled="scope.row.is_preset"
            />
          </template>
        </el-table-column>

        <el-table-column label="使用次数" width="100">
          <template #default="scope">
            <el-text type="primary">{{ scope.row.usage_count }}</el-text>
          </template>
        </el-table-column>

        <el-table-column label="创建者" width="120">
          <template #default="scope">
            {{ scope.row.creator?.username || '系统' }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewTemplate(scope.row)">预览</el-button>
            <el-button size="small" @click="editTemplate(scope.row)" :disabled="!canEdit(scope.row)">编辑</el-button>
            <el-button size="small" @click="copyTemplateToForm(scope.row)">复制</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDeleteTemplate(scope.row)"
              :disabled="!canDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="6" v-for="template in templateList" :key="template.id">
            <div class="template-card" :class="{ 'template-disabled': !template.is_active }">
              <div class="card-cover">
                <img v-if="template.cover_image_url" :src="template.cover_image_url" />
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                </div>
                <div class="card-overlay">
                  <el-button size="small" circle @click="viewTemplate(template)">
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button size="small" circle @click="editTemplate(template)" :disabled="!canEdit(template)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" circle type="danger" @click="handleDeleteTemplate(template)" :disabled="!canDelete(template)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="card-content">
                <div class="card-header">
                  <h4 class="card-title">{{ template.template_name }}</h4>
                  <div class="card-badges">
                    <el-tag size="small" :type="template.is_preset ? 'warning' : 'info'">
                      {{ template.is_preset ? '预设' : '自定义' }}
                    </el-tag>
                    <el-tag size="small" :type="getCategoryTagType(template.category)">
                      {{ template.category_name }}
                    </el-tag>
                  </div>
                </div>
                <p class="card-desc">{{ template.description || '暂无描述' }}</p>
                <div class="card-stats">
                  <span class="stat-item">
                    <el-icon><User /></el-icon>
                    使用 {{ template.usage_count }} 次
                  </span>
                  <span class="stat-item">
                    <el-icon><Calendar /></el-icon>
                    {{ formatDate(template.created_at) }}
                  </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.per_page"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </el-card>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog 
      :title="dialog.title" 
      v-model="dialog.visible" 
      width="900px" 
      :close-on-click-modal="false"
    >
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        label-width="120px"
        v-loading="formLoading"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="template_name">
              <el-input v-model="form.template_name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
                <el-option 
                  v-for="(name, code) in categories" 
                  :key="code" 
                  :label="name" 
                  :value="code" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="模板描述">
          <el-input 
            type="textarea" 
            v-model="form.description" 
            placeholder="请输入模板描述"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="封面图片">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleCoverUploadSuccess"
            :before-upload="beforeCoverUpload"
            :show-file-list="false"
            class="cover-uploader"
          >
            <img v-if="form.cover_image" :src="form.cover_image" class="cover-image" />
            <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <!-- 模板基础配置 -->
        <el-divider content-position="left">基础配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="群名称" prop="template_data.title">
              <el-input v-model="form.template_data.title" placeholder="支持变量：{{city}}、{{username}}" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入群价格" prop="template_data.price">
              <el-input-number 
                v-model="form.template_data.price" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="群描述" prop="template_data.description">
          <el-input 
            type="textarea" 
            v-model="form.template_data.description" 
            placeholder="支持变量：{{city}}、{{username}}"
            :rows="4"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成员上限">
              <el-input-number 
                v-model="form.template_data.member_limit" 
                :min="10" 
                :max="2000" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序权重">
              <el-input-number 
                v-model="form.sort_order" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 营销配置 -->
        <el-divider content-position="left">营销配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="虚拟成员数">
              <el-input-number 
                v-model="form.template_data.virtual_members" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="虚拟订单数">
              <el-input-number 
                v-model="form.template_data.virtual_orders" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="虚拟收益">
              <el-input-number 
                v-model="form.template_data.virtual_income" 
                :min="0" 
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 营销展示数据 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="阅读数显示">
              <el-input 
                v-model="form.read_count" 
                placeholder="如：10万+"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="点赞数">
              <el-input-number 
                v-model="form.like_count" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="想看数">
              <el-input-number 
                v-model="form.want_see_count" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="按键名称">
              <el-input 
                v-model="form.button_title" 
                placeholder="如：加入群，学习更多副业知识"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="头像库类型">
              <el-select v-model="form.avatar_library" placeholder="选择头像库" style="width: 100%">
                <el-option label="默认头像" value="default" />
                <el-option label="商务头像" value="business" />
                <el-option label="交友头像" value="dating" />
                <el-option label="征婚头像" value="marriage" />
                <el-option label="健身头像" value="fitness" />
                <el-option label="家庭头像" value="family" />
                <el-option label="扩列头像" value="qq" />
                <el-option label="综合头像" value="za" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 内容区块配置 -->
        <el-divider content-position="left">内容区块配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="群简介标题">
              <el-input v-model="form.group_intro_title" placeholder="如：群简介" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="常见问题标题">
              <el-input v-model="form.faq_title" placeholder="如：常见问题" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="群简介内容">
          <el-input 
            type="textarea" 
            v-model="form.group_intro_content" 
            placeholder="输入群简介内容"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="FAQ内容">
          <el-input 
            type="textarea" 
            v-model="form.faq_content" 
            placeholder="格式：问题----答案（每行一个）"
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="用户评论">
          <el-input 
            type="textarea" 
            v-model="form.user_reviews" 
            placeholder="格式：用户名----评论内容----评分（每行一个）"
            :rows="4"
          />
        </el-form-item>

        <!-- 素材配置 -->
        <el-divider content-position="left">素材配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客服二维码">
              <el-input v-model="form.customer_service_qr" placeholder="客服二维码URL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="广告图片">
              <el-input v-model="form.ad_image" placeholder="广告图片URL" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="扩展区块1标题">
              <el-input v-model="form.extra_title1" placeholder="扩展区块1标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展区块2标题">
              <el-input v-model="form.extra_title2" placeholder="扩展区块2标题" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="扩展区块1内容">
          <el-input 
            type="textarea" 
            v-model="form.extra_content1" 
            placeholder="扩展区块1内容"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="扩展区块2内容">
          <el-input 
            type="textarea" 
            v-model="form.extra_content2" 
            placeholder="扩展区块2内容"
            :rows="3"
          />
        </el-form-item>

        <!-- 自定义字段配置 -->
        <el-divider content-position="left">自定义字段配置</el-divider>
        
        <div class="custom-fields-config">
          <div v-for="(field, index) in form.custom_fields_config" :key="index" class="custom-field-item">
            <el-row :gutter="10">
              <el-col :span="4">
                <el-input v-model="field.key" placeholder="字段名" />
              </el-col>
              <el-col :span="3">
                <el-select v-model="field.type" placeholder="类型">
                  <el-option label="文本" value="text" />
                  <el-option label="数字" value="number" />
                  <el-option label="选择" value="select" />
                  <el-option label="开关" value="switch" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input v-model="field.label" placeholder="显示名称" />
              </el-col>
              <el-col :span="4">
                <el-input v-model="field.default_value" placeholder="默认值" />
              </el-col>
              <el-col :span="6">
                <el-input v-model="field.placeholder" placeholder="提示文本" />
              </el-col>
              <el-col :span="3">
                <el-button type="danger" size="small" @click="removeCustomField(index)">删除</el-button>
              </el-col>
            </el-row>
          </div>
          <el-button type="primary" size="small" @click="addCustomField">添加自定义字段</el-button>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate" :loading="formLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog title="模板预览" v-model="previewDialog.visible" width="600px">
      <div class="template-preview" v-loading="previewDialog.loading">
        <div class="preview-header">
          <h3>{{ previewDialog.data.template_name }}</h3>
          <p>{{ previewDialog.data.description }}</p>
        </div>
        
        <div class="preview-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="群名称">
              {{ previewDialog.data.template_data?.title }}
            </el-descriptions-item>
            <el-descriptions-item label="入群价格">
              ¥{{ previewDialog.data.template_data?.price }}
            </el-descriptions-item>
            <el-descriptions-item label="成员上限">
              {{ previewDialog.data.template_data?.member_limit }}
            </el-descriptions-item>
            <el-descriptions-item label="虚拟成员">
              {{ previewDialog.data.template_data?.virtual_members }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              {{ previewDialog.data.category_name }}
            </el-descriptions-item>
            <el-descriptions-item label="使用次数">
              {{ previewDialog.data.usage_count }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="previewDialog.data.template_data?.description" class="preview-description">
            <h4>群描述</h4>
            <p>{{ previewDialog.data.template_data.description }}</p>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="previewDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="useTemplate(previewDialog.data)">使用模板</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Picture, View, Edit, Delete, User, Calendar } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import { formatDate } from '@/utils/format'

// API 接口
import {
  getTemplates,
  getTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateCategories,
  toggleTemplateStatus
} from '@/api/community'

// ========== 状态管理 ==========
const loading = ref(false)
const formLoading = ref(false)
const templateList = ref([])
const total = ref(0)
const selectedIds = ref([])
const categories = ref({})
const viewMode = ref('table')

const queryParams = reactive({
  page: 1,
  per_page: 20,
  keyword: '',
  category: '',
  is_preset: null,
  is_active: null
})

const dialog = reactive({
  visible: false,
  title: '',
  isEdit: false
})

const previewDialog = reactive({
  visible: false,
  loading: false,
  data: {}
})

const form = reactive({
  template_name: '',
  category: '',
  description: '',
  cover_image: '',
  template_data: {
    title: '',
    description: '',
    price: 0,
    member_limit: 500,
    virtual_members: 0,
    virtual_orders: 0,
    virtual_income: 0,
    faq_content: '',
    member_reviews: ''
  },
  custom_fields_config: [],
  sort_order: 0,
  // 营销展示字段
  read_count: '10万+',
  like_count: 3659,
  want_see_count: 665,
  button_title: '加入群，学习更多副业知识',
  avatar_library: 'default',
  // 内容区块字段
  group_intro_title: '群简介',
  group_intro_content: '',
  faq_title: '常见问题',
  faq_content: '',
  user_reviews: '',
  // 素材相关字段
  customer_service_qr: '',
  ad_image: '',
  extra_title1: '',
  extra_content1: '',
  extra_title2: '',
  extra_content2: ''
})

const rules = {
  template_name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择模板分类', trigger: 'change' }
  ],
  'template_data.title': [
    { required: true, message: '请输入群名称', trigger: 'blur' }
  ],
  'template_data.description': [
    { required: true, message: '请输入群描述', trigger: 'blur' }
  ],
  'template_data.price': [
    { required: true, message: '请输入入群价格', trigger: 'blur' }
  ]
}

const formRef = ref(null)

// 上传配置
const uploadUrl = ref(`${import.meta.env.VITE_API_BASE_URL}/group-templates/upload-cover`)
const uploadHeaders = ref({
  Authorization: `Bearer ${getToken()}`
})

// ========== 计算属性 ==========
const currentUser = computed(() => {
  // 获取当前用户信息
  return JSON.parse(localStorage.getItem('user') || '{}')
})

// ========== 生命周期 ==========
onMounted(() => {
  getList()
  getCategories()
})

// ========== 方法 ==========
async function getList() {
  loading.value = true
  try {
    const response = await getTemplates(queryParams)
    templateList.value = response.data.data
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

async function getCategories() {
  try {
    const response = await getTemplateCategories()
    categories.value = response.data
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  }
}

function resetQuery() {
  Object.assign(queryParams, {
    page: 1,
    per_page: 20,
    keyword: '',
    category: '',
    is_preset: null,
    is_active: null
  })
  getList()
}

function refreshData() {
  getList()
}

function showCreateDialog() {
  dialog.title = '新建模板'
  dialog.isEdit = false
  dialog.visible = true
  resetForm()
}

function resetForm() {
  Object.assign(form, {
    template_name: '',
    category: '',
    description: '',
    cover_image: '',
    template_data: {
      title: '',
      description: '',
      price: 0,
      member_limit: 500,
      virtual_members: 0,
      virtual_orders: 0,
      virtual_income: 0,
      faq_content: '',
      member_reviews: ''
    },
    custom_fields_config: [],
    sort_order: 0
  })
}

async function editTemplate(template) {
  if (!canEdit(template)) {
    ElMessage.warning('您无权编辑此模板')
    return
  }
  
  dialog.title = '编辑模板'
  dialog.isEdit = true
  dialog.visible = true
  
  // 加载模板详情
  try {
    const response = await getTemplate(template.id)
    Object.assign(form, response.data)
  } catch (error) {
    ElMessage.error('加载模板详情失败')
  }
}

async function saveTemplate() {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    formLoading.value = true
    try {
      if (dialog.isEdit) {
        await updateTemplate(form.id, form)
        ElMessage.success('更新模板成功')
      } else {
        await createTemplate(form)
        ElMessage.success('创建模板成功')
      }
      
      dialog.visible = false
      getList()
    } catch (error) {
      ElMessage.error(dialog.isEdit ? '更新模板失败' : '创建模板失败')
    } finally {
      formLoading.value = false
    }
  })
}

async function handleDeleteTemplate(template) {
  if (!canDelete(template)) {
    ElMessage.warning('您无权删除此模板')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.template_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteTemplate(template.id)
    ElMessage.success('删除模板成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模板失败')
    }
  }
}

async function toggleStatus(template) {
  try {
    await toggleTemplateStatus(template.id, template.is_active)
    ElMessage.success(`${template.is_active ? '启用' : '禁用'}模板成功`)
  } catch (error) {
    template.is_active = !template.is_active // 恢复状态
    ElMessage.error('操作失败')
  }
}

function viewTemplate(template) {
  previewDialog.data = template
  previewDialog.visible = true
}

function copyTemplateToForm(template) {
  Object.assign(form, {
    ...template,
    template_name: template.template_name + ' (副本)',
    id: undefined
  })
  dialog.title = '复制模板'
  dialog.isEdit = false
  dialog.visible = true
}

function useTemplate(template) {
  // 跳转到群组创建页面，并传递模板ID
  this.$router.push({
    path: '/community/groups/create',
    query: { template_id: template.id }
  })
}

// 自定义字段管理
function addCustomField() {
  form.custom_fields_config.push({
    key: '',
    type: 'text',
    label: '',
    default_value: '',
    placeholder: ''
  })
}

function removeCustomField(index) {
  form.custom_fields_config.splice(index, 1)
}

// 权限检查
function canEdit(template) {
  if (currentUser.value.role === 'admin') return true
  return template.created_by === currentUser.value.id && !template.is_preset
}

function canDelete(template) {
  if (template.is_preset) return false
  if (currentUser.value.role === 'admin') return true
  return template.created_by === currentUser.value.id
}

// 工具函数
function getCategoryTagType(category) {
  const types = {
    preset: '',
    custom: 'info',
    business: 'warning',
    education: 'success',
    entertainment: 'danger',
    technology: 'primary'
  }
  return types[category] || 'info'
}

function handleSelectionChange(selection) {
  selectedIds.value = selection.map(item => item.id)
}

// 上传处理
function beforeCoverUpload(file) {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

function handleCoverUploadSuccess(response) {
  if (response.code === 200) {
    form.cover_image = response.data.url
    ElMessage.success('上传成功')
  } else {
    ElMessage.error('上传失败')
  }
}
</script>

<style scoped>
.template-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-weight: 500;
  color: #303133;
}

.template-info {
  display: flex;
  align-items: center;
}

.template-cover {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-details {
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.template-code {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #606266;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-view {
  margin: 20px 0;
}

.template-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.template-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-disabled {
  opacity: 0.6;
}

.card-cover {
  position: relative;
  height: 160px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.card-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #c0c4cc;
  font-size: 40px;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .card-overlay {
  opacity: 1;
}

.card-content {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.card-badges {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.card-desc {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cover-uploader {
  width: 120px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-uploader:hover {
  border-color: #409eff;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.custom-fields-config {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
}

.custom-field-item {
  margin-bottom: 12px;
}

.custom-field-item:last-child {
  margin-bottom: 16px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.template-preview {
  padding: 16px;
}

.preview-header {
  margin-bottom: 20px;
  text-align: center;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.preview-header p {
  margin: 0;
  color: #606266;
}

.preview-description {
  margin-top: 20px;
}

.preview-description h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.preview-description p {
  margin: 0;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #606266;
}
</style> 