<template>
  <div class="china-map-chart">
    <!-- 地图头部 -->
    <div class="map-header">
      <h3 class="map-title">
        <i class="el-icon-location"></i>
        全国用户分布
      </h3>
      <div class="map-controls">
        <button 
          v-for="type in dataTypes"
          :key="type.key"
          class="data-type-btn"
          :class="{ active: selectedDataType === type.key }"
          @click="switchDataType(type.key)"
        >
          {{ type.label }}
        </button>
      </div>
    </div>

    <!-- 地图内容 -->
    <div class="map-content">
      <div class="province-grid">
        <div 
          v-for="(province, index) in provinceData"
          :key="province.name"
          class="province-item"
          :style="{ 
            background: getProvinceColor(province.value),
            animationDelay: (index * 0.05) + 's'
          }"
          @click="selectProvince(province)"
          @mouseenter="showTooltip(province, $event)"
          @mouseleave="hideTooltip"
        >
          <div class="province-name">{{ province.name }}</div>
          <div class="province-value">{{ formatNumber(province.value) }}</div>
          <div class="province-growth" :class="province.growth > 0 ? 'positive' : 'negative'">
            <i :class="province.growth > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ Math.abs(province.growth) }}%
          </div>
        </div>
      </div>
      
      <!-- 数据图例 -->
      <div class="map-legend">
        <div class="legend-title">数据密度</div>
        <div class="legend-items">
          <div class="legend-item">
            <div class="legend-color" style="background: #4ade80"></div>
            <span>较少 (0-500)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #fbbf24"></div>
            <span>一般 (501-1000)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #f87171"></div>
            <span>较多 (1001-2000)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #dc2626"></div>
            <span>很多 (2000+)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具提示 -->
    <div v-if="showTooltipFlag" class="province-tooltip" :style="tooltipStyle">
      <div class="tooltip-header">{{ selectedProvince?.name }}</div>
      <div class="tooltip-content">
        <div class="tooltip-item">
          <span>用户数量:</span>
          <span>{{ formatNumber(selectedProvince?.value || 0) }}</span>
        </div>
        <div class="tooltip-item">
          <span>增长率:</span>
          <span :class="selectedProvince?.growth > 0 ? 'positive' : 'negative'">
            {{ selectedProvince?.growth > 0 ? '+' : '' }}{{ selectedProvince?.growth }}%
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const selectedDataType = ref('users')
const selectedProvince = ref(null)
const showTooltipFlag = ref(false)
const tooltipStyle = ref({})

// 数据类型配置
const dataTypes = ref([
  { key: 'users', label: '用户数' },
  { key: 'orders', label: '订单数' },
  { key: 'revenue', label: '收入' }
])

// 省份数据
const provinceData = ref([
  { name: '北京', value: 2580, growth: 12.5 },
  { name: '上海', value: 2340, growth: 8.3 },
  { name: '广东', value: 3200, growth: 15.2 },
  { name: '浙江', value: 1890, growth: 6.7 },
  { name: '江苏', value: 2100, growth: 9.8 },
  { name: '山东', value: 1750, growth: 4.2 },
  { name: '河南', value: 1420, growth: -2.1 },
  { name: '四川', value: 1680, growth: 7.9 },
  { name: '湖北', value: 1350, growth: 3.4 },
  { name: '湖南', value: 1280, growth: 5.6 },
  { name: '安徽', value: 1150, growth: 2.8 },
  { name: '河北', value: 1320, growth: 1.9 },
  { name: '江西', value: 980, growth: 4.5 },
  { name: '山西', value: 890, growth: -1.2 },
  { name: '辽宁', value: 1100, growth: 0.8 },
  { name: '福建', value: 1050, growth: 6.3 },
  { name: '陕西', value: 920, growth: 3.7 },
  { name: '黑龙江', value: 780, growth: -3.5 },
  { name: '广西', value: 850, growth: 2.1 },
  { name: '云南', value: 720, growth: 4.8 },
  { name: '贵州', value: 650, growth: 8.9 },
  { name: '吉林', value: 580, growth: -1.8 },
  { name: '重庆', value: 890, growth: 11.2 },
  { name: '天津', value: 760, growth: 3.9 }
])

// 方法
const formatNumber = (num) => {
  if (num >= 10000) return (num / 10000).toFixed(1) + 'w'
  return num.toLocaleString()
}

const getProvinceColor = (value) => {
  if (value >= 2000) return 'linear-gradient(135deg, #dc2626, #ef4444)' // 很多 - 红色
  if (value >= 1000) return 'linear-gradient(135deg, #f87171, #fca5a5)' // 较多 - 橙色
  if (value >= 500) return 'linear-gradient(135deg, #fbbf24, #fcd34d)'  // 一般 - 黄色
  return 'linear-gradient(135deg, #4ade80, #6ee7b7)' // 较少 - 绿色
}

const switchDataType = (type) => {
  selectedDataType.value = type
  console.log('切换数据类型:', type)
}

const selectProvince = (province) => {
  console.log('选中省份:', province.name)
  selectedProvince.value = province
}

const showTooltip = (province, event) => {
  selectedProvince.value = province
  showTooltipFlag.value = true
  
  const rect = event.target.getBoundingClientRect()
  tooltipStyle.value = {
    display: 'block',
    position: 'fixed',
    left: rect.right + 10 + 'px',
    top: rect.top + 'px',
    zIndex: 10000
  }
}

const hideTooltip = () => {
  showTooltipFlag.value = false
  selectedProvince.value = null
}

// 生命周期
onMounted(() => {
  console.log('ChinaMapChart 组件已挂载，省份数据:', provinceData.value.length)
})
</script>

<style scoped>
.china-map-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: hidden;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.map-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.map-controls {
  display: flex;
  gap: 8px;
}

.data-type-btn {
  padding: 4px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.data-type-btn.active,
.data-type-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.5);
  color: #ffffff;
}

.map-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.province-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  flex: 1;
}

.province-item {
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.province-item:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.3);
}

.province-name {
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.province-value {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.province-growth {
  font-size: 11px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.province-growth.positive {
  color: #4ade80;
}

.province-growth.negative {
  color: #f87171;
}

.map-legend {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.legend-title {
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 8px;
  font-weight: 600;
}

.legend-items {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.province-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 10000;
  pointer-events: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.tooltip-header {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.tooltip-item span:first-child {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

.tooltip-item span:last-child {
  font-weight: 600;
  color: #ffffff;
}

.positive {
  color: #4ade80;
}

.negative {
  color: #f87171;
}
</style>
