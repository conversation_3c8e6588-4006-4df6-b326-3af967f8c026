<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\WechatGroup;
use App\Services\CommissionService;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 订单控制器 (V3.0 - 用户与管理员双功能版)
 * <AUTHOR>
 * @date 2024-07-26
 */
class OrderController extends Controller
{
    protected $paymentService;
    protected $commissionService;

    public function __construct(PaymentService $paymentService, CommissionService $commissionService)
    {
        $this->middleware('auth:api')->except(['create', 'paymentNotify']);
        $this->paymentService = $paymentService;
        $this->commissionService = $commissionService;
    }

    /**
     * 获取订单列表 (为用户和管理员重构)
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // 基础查询, 始终加载关联的群组信息
        $query = Order::with('wechatGroup');

        // 普通用户只能看自己的订单
        if ($user->isUser()) {
            $query->where('user_id', $user->id);
        }
        // 如果是管理员, 可以查看所有, 此处不做限制, 由路由中间件控制

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        // 群组筛选
        if ($request->filled('group_id')) {
            $query->where('wechat_group_id', $request->group_id);
        }

        // 关键词搜索 (订单号或群组名称)
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_no', 'like', "%{$search}%")
                  ->orWhereHas('wechatGroup', function ($q_group) use ($search) {
                      $q_group->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->latest()->paginate($request->input('limit', 10));

        return response()->json(['code' => 0, 'data' => $orders]);
    }
    
    /**
     * 获取当前用户的订单统计数据
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        $stats = Order::where('user_id', $user->id)
            ->selectRaw("
                COUNT(*) as total,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as paid,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled
            ", [Order::STATUS_PENDING_INT, Order::STATUS_PAID_INT, Order::STATUS_CANCELLED_INT])
            ->first();
            
        return response()->json(['code' => 0, 'data' => $stats]);
    }

    /**
     * 获取用户下过单的群组列表 (用于筛选)
     */
    public function getAvailableGroups(Request $request)
    {
        $user = $request->user();
        $groupIds = Order::where('user_id', $user->id)->distinct()->pluck('wechat_group_id');
        $groups = WechatGroup::whereIn('id', $groupIds)->select('id', 'name')->get();

        return response()->json(['code' => 0, 'data' => $groups]);
    }
    
    /**
     * 用户取消订单
     */
    public function cancel(Request $request, Order $order)
    {
        $this->authorize('cancel', $order);

        if ($order->status !== Order::STATUS_PENDING) {
            return response()->json(['code' => 400, 'message' => '只有待支付的订单才能取消'], 400);
        }

        $order->status = Order::STATUS_CANCELLED;
        $order->save();

        return response()->json(['code' => 0, 'message' => '订单已取消']);
    }

    /**
     * 获取订单详情
     */
    public function show(Request $request, Order $order)
    {
        $this->authorize('view', $order);
        $order->load('wechatGroup', 'user');
        return response()->json(['code' => 0, 'data' => $order]);
    }
    
    /**
     * 创建订单并获取支付信息
     */
    public function create(Request $request, $groupId)
    {
        $group = WechatGroup::findOrFail($groupId);
        if (!$group->isJoinable()) {
            return response()->json(['success' => false, 'message' => '群组不可用或已满员'], 400);
        }

        if ($group->isFree()) {
            return response()->json(['success' => true, 'message' => '此群组为免费群，请直接扫码加入', 'data' => ['is_free' => true, 'qr_code' => $group->qr_code_url]]);
        }
        
        $order = new Order();
        $order->fill($request->only(['customer_name', 'customer_phone', 'customer_wechat']));
        $order->wechat_group_id = $group->id;
        $order->substation_id = $group->substation_id;
        $order->user_id = $group->user_id; // 订单归属于群主
        $order->amount = $group->price;
        $order->status = Order::STATUS_PENDING;
        $order->customer_ip = $request->ip();
        $order->expired_at = now()->addMinutes(30);
        $order->save();
        
        try {
            // 从请求中获取支付渠道ID，如果未提供则使用默认
            $channelId = $request->input('channel_id');
            $paymentInfo = $this->paymentService->getPaymentInfo($order, $channelId);
            
            // 更新订单，关联支付网关订单号
            if (isset($paymentInfo['payment_no'])) {
                 $order->payment_no = $paymentInfo['payment_no'];
                 $order->save();
            }

            return response()->json([
                'code' => 0,
                'message' => '订单创建成功，请支付',
                'data' => [
                    'order_no' => $order->order_no,
                    'amount' => $order->amount,
                    'payment_info' => $paymentInfo['pay_url'] ?? ($paymentInfo['qr_code'] ?? null), // 返回支付URL或二维码
                    'expired_at' => $order->expired_at,
                ],
            ], 201);

        } catch (\Exception $e) {
            Log::error("创建订单 {$order->order_no} 的支付信息失败", ['error' => $e->getMessage()]);
            return response()->json(['code' => 500, 'message' => $e->getMessage()], 500);
        }
    }
    
    /**
     * 支付回调通知
     */
    public function paymentNotify(Request $request, $method)
    {
        Log::info("{$method} 支付回调接收", $request->all());

        // 根据不同支付方式处理回调
        switch ($method) {
            case 'wechat':
                return $this->handleWechatCallback($request);
            case 'alipay':
                return $this->handleAlipayCallback($request);
            case 'payoreo':
                return $this->handlePayoreoCallback($request);
            default:
                Log::warning('不支持的支付方式回调', ['method' => $method]);
                return response('Unsupported payment method', 400);
        }
    }

    /**
     * 处理微信支付回调
     */
    private function handleWechatCallback(Request $request)
    {
        $orderNo = $request->input('out_trade_no');
        if (!$orderNo) {
            Log::warning('微信支付回调缺少订单号');
            return response('Missing order number', 400);
        }

        $order = Order::where('order_no', $orderNo)->first();
        if (!$order) {
            Log::warning('微信支付回调订单不存在', ['order_no' => $orderNo]);
            return response('Order not found', 404);
        }

        if ($order->status === Order::STATUS_PAID) {
            return response('success'); // 已处理，直接返回成功
        }

        // 验证微信支付签名
        $signature = $request->input('sign');
        if (!$this->paymentService->verifyCallback('wechat', $request->all(), $signature)) {
            Log::warning('微信支付回调签名验证失败', ['order_no' => $orderNo]);
            return response('Invalid signature', 400);
        }

        // 验证金额
        if ($order->amount != $request->input('total_fee') / 100) { // 微信金额单位是分
            Log::warning('微信支付回调金额不匹配', [
                'order_no' => $orderNo,
                'order_amount' => $order->amount,
                'callback_amount' => $request->input('total_fee') / 100
            ]);
            return response('Amount mismatch', 400);
        }

        return $this->processPaymentSuccess($order, 'wechat', [
            'transaction_id' => $request->input('transaction_id'),
            'trade_type' => $request->input('trade_type'),
            'bank_type' => $request->input('bank_type'),
        ]);
    }

    /**
     * 处理支付宝回调
     */
    private function handleAlipayCallback(Request $request)
    {
        $orderNo = $request->input('out_trade_no');
        if (!$orderNo) {
            Log::warning('支付宝回调缺少订单号');
            return response('Missing order number', 400);
        }

        $order = Order::where('order_no', $orderNo)->first();
        if (!$order) {
            Log::warning('支付宝回调订单不存在', ['order_no' => $orderNo]);
            return response('Order not found', 404);
        }

        if ($order->status === Order::STATUS_PAID) {
            return response('success'); // 已处理，直接返回成功
        }

        // 验证支付宝签名
        $signature = $request->input('sign');
        if (!$this->paymentService->verifyCallback('alipay', $request->all(), $signature)) {
            Log::warning('支付宝回调签名验证失败', ['order_no' => $orderNo]);
            return response('Invalid signature', 400);
        }

        // 验证订单状态
        if ($request->input('trade_status') !== 'TRADE_SUCCESS') {
            Log::warning('支付宝回调订单状态异常', [
                'order_no' => $orderNo,
                'trade_status' => $request->input('trade_status')
            ]);
            return response('Trade status error', 400);
        }

        // 验证金额
        if ($order->amount != floatval($request->input('total_amount'))) {
            Log::warning('支付宝回调金额不匹配', [
                'order_no' => $orderNo,
                'order_amount' => $order->amount,
                'callback_amount' => $request->input('total_amount')
            ]);
            return response('Amount mismatch', 400);
        }

        return $this->processPaymentSuccess($order, 'alipay', [
            'trade_no' => $request->input('trade_no'),
            'buyer_id' => $request->input('buyer_id'),
            'buyer_logon_id' => $request->input('buyer_logon_id'),
        ]);
    }

    /**
     * 处理易支付回调
     */
    private function handlePayoreoCallback(Request $request)
    {
        $orderNo = $request->input('out_trade_no');
        if (!$orderNo) {
            Log::warning('易支付回调缺少订单号');
            return response('fail'); // 易支付要求返回fail
        }

        $order = Order::where('order_no', $orderNo)->first();
        if (!$order) {
            Log::warning('易支付回调订单不存在', ['order_no' => $orderNo]);
            return response('fail');
        }

        if ($order->status === Order::STATUS_PAID) {
            return response('success'); // 已处理，直接返回成功
        }

        // 验证易支付签名
        $signature = $request->input('sign');
        if (!$this->paymentService->verifyCallback('payoreo', $request->all(), $signature)) {
            Log::warning('易支付回调签名验证失败', ['order_no' => $orderNo]);
            return response('fail');
        }

        // 验证订单状态
        if ($request->input('trade_status') !== '1') { // 易支付成功状态是1
            Log::warning('易支付回调订单状态异常', [
                'order_no' => $orderNo,
                'trade_status' => $request->input('trade_status')
            ]);
            return response('fail');
        }

        // 验证金额
        if ($order->amount != floatval($request->input('money'))) {
            Log::warning('易支付回调金额不匹配', [
                'order_no' => $orderNo,
                'order_amount' => $order->amount,
                'callback_amount' => $request->input('money')
            ]);
            return response('fail');
        }

        return $this->processPaymentSuccess($order, 'payoreo', [
            'trade_no' => $request->input('trade_no'),
            'type' => $request->input('type'),
            'pid' => $request->input('pid'),
        ]);
    }

    /**
     * 处理支付成功的通用逻辑
     */
    private function processPaymentSuccess(Order $order, string $method, array $extraData = [])
    {
        DB::beginTransaction();
        try {
            // 标记订单为已支付
            $order->markAsPaid($method, $extraData['trade_no'] ?? $extraData['transaction_id'] ?? 'N/A', $extraData);
            
            // 处理支付成功后的业务逻辑（佣金计算等）
            $order->processPaidOrder();
            
            DB::commit();
            
            Log::info("订单 {$order->order_no} 支付成功处理完毕", [
                'method' => $method,
                'amount' => $order->amount,
                'extra_data' => $extraData
            ]);
            
            return response('success');
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('支付回调处理异常', [
                'order_no' => $order->order_no,
                'method' => $method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 根据支付方式返回不同的错误响应
            $errorResponse = $method === 'payoreo' ? 'fail' : 'Internal Server Error';
            return response($errorResponse, 500);
        }
    }

    /**
     * 更新订单状态 (管理员操作) - 已移至 Admin/OrderController
     */
    /*
    public function updateStatus(Request $request, $id)
    {
        $order = Order::findOrFail($id);
        $this->authorize('update', $order);
        
        $validated = $request->validate(['status' => 'required|in:pending,paid,cancelled,refunded']);
        $newStatus = $validated['status'];
        $originalStatus = $order->status;

        if ($newStatus === $originalStatus) {
            return response()->json(['success' => true, 'message' => '状态未变更', 'data' => $order]);
        }
        
        DB::beginTransaction();
        try {
            $order->status = $newStatus;
            $order->save();
            
            // 如果管理员手动将订单从未支付更新为已支付，则触发后续业务
            if ($originalStatus === Order::STATUS_PENDING && $newStatus === Order::STATUS_PAID) {
                // 如果没有支付时间，则补充
                if(is_null($order->paid_at)) {
                    $order->paid_at = now();
                }
                // 手动标记为后台操作
                $order->payment_method = 'manual'; 
                $order->save();

                $order->processPaidOrder();
                Log::info("管理员手动将订单 {$order->order_no} 标记为已支付并处理业务。");
            }
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("管理员更新订单 {$id} 状态失败", ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => '更新失败，发生内部错误'], 500);
        }

        return response()->json(['success' => true, 'message' => '状态更新成功', 'data' => $order]);
    }
    */

    /**
     * 订单退款处理
     */
    public function refund(Request $request, Order $order)
    {
        $this->authorize('update', $order);
        
        // 验证订单状态
        if ($order->status !== Order::STATUS_PAID_INT) {
            return response()->json([
                'success' => false,
                'message' => '只有已支付的订单才能申请退款'
            ], 422);
        }
        
        // 验证退款请求
        $request->validate([
            'refund_reason' => 'required|string|max:500',
            'refund_amount' => 'sometimes|numeric|min:0.01|max:' . $order->amount,
        ]);
        
        $refundAmount = $request->input('refund_amount', $order->amount);
        
        DB::beginTransaction();
        try {
            // 调用支付服务处理退款
            $refundResult = $this->paymentService->processRefund($order, [
                'refund_amount' => $refundAmount,
                'refund_reason' => $request->refund_reason,
                'operator_id' => $request->user()->id,
            ]);
            
            if ($refundResult['success']) {
                // 更新订单状态
                $order->update([
                    'status' => Order::STATUS_REFUNDED_INT,
                    'refund_amount' => $refundAmount,
                    'refund_reason' => $request->refund_reason,
                    'refunded_at' => now(),
                    'refund_no' => $refundResult['refund_no'] ?? null,
                ]);
                
                // 记录退款日志
                Log::info('订单退款成功', [
                    'order_no' => $order->order_no,
                    'refund_amount' => $refundAmount,
                    'refund_reason' => $request->refund_reason,
                    'operator_id' => $request->user()->id,
                ]);
                
                // 如果有佣金，需要扣回
                if ($order->commissionLogs()->exists()) {
                    $this->commissionService->reverseCommission($order);
                }
                
                DB::commit();
                
                return response()->json([
                    'success' => true,
                    'message' => '退款申请提交成功',
                    'data' => [
                        'order_no' => $order->order_no,
                        'refund_amount' => $refundAmount,
                        'refund_no' => $refundResult['refund_no'] ?? null,
                    ]
                ]);
            } else {
                DB::rollback();
                return response()->json([
                    'success' => false,
                    'message' => $refundResult['message'] ?? '退款处理失败'
                ], 500);
            }
            
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('订单退款异常', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '退款处理异常，请稍后重试'
            ], 500);
        }
    }

    /**
     * 获取订单统计数据 (管理员用)
     */
    public function getOrderStats(Request $request)
    {
        $user = $request->user();
        $today = now()->format('Y-m-d');
        
        $stats = Order::selectRaw("
            COUNT(*) as total,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as paid,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as refunded,
            SUM(CASE WHEN status = ? THEN amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN status = ? AND DATE(created_at) = ? THEN amount ELSE 0 END) as today_amount,
            SUM(CASE WHEN DATE(created_at) = ? THEN 1 ELSE 0 END) as today_orders
        ", [
            Order::STATUS_PENDING_INT, 
            Order::STATUS_PAID_INT, 
            Order::STATUS_CANCELLED_INT, 
            Order::STATUS_REFUNDED_INT,
            Order::STATUS_PAID_INT,
            Order::STATUS_PAID_INT,
            $today,
            $today
        ])->first();
        
        return response()->json([
            'code' => 0,
            'data' => $stats
        ]);
    }

    /**
     * 批量处理订单
     */
    public function batchProcess(Request $request)
    {
        $data = $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'integer|exists:orders,id',
            'action' => 'required|in:cancel,refund,delete',
            'reason' => 'nullable|string|max:255'
        ]);

        $orderIds = $data['order_ids'];
        $action = $data['action'];
        $reason = $data['reason'] ?? '';

        DB::beginTransaction();
        try {
            $orders = Order::whereIn('id', $orderIds)->get();
            $processedCount = 0;

            foreach ($orders as $order) {
                switch ($action) {
                    case 'cancel':
                        if ($order->status === Order::STATUS_PENDING) {
                            $order->status = Order::STATUS_CANCELLED;
                            $order->remark = $reason;
                            $order->save();
                            $processedCount++;
                        }
                        break;
                        
                    case 'refund':
                        if ($order->status === Order::STATUS_PAID) {
                            $order->status = Order::STATUS_REFUNDED;
                            $order->remark = $reason;
                            $order->save();
                            
                            // 这里可以添加实际的退款逻辑
                            $processedCount++;
                        }
                        break;
                        
                    case 'delete':
                        if (in_array($order->status, [Order::STATUS_CANCELLED, Order::STATUS_REFUNDED])) {
                            $order->delete();
                            $processedCount++;
                        }
                        break;
                }
            }

            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => "成功处理 {$processedCount} 个订单"
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("批量处理订单失败: " . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '批量处理失败'
            ], 500);
        }
    }

    /**
     * 导出订单数据
     */
    public function exportOrders(Request $request)
    {
        try {
            $query = Order::with(['wechatGroup', 'user']);
            
            // 应用筛选条件
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }
            
            $orders = $query->orderBy('created_at', 'desc')->get();
            
            // 创建CSV内容
            $csvContent = "订单号,群组名称,用户,金额,状态,创建时间,支付时间\n";
            
            foreach ($orders as $order) {
                $csvContent .= implode(',', [
                    $order->order_no,
                    '"' . ($order->wechatGroup->name ?? '未知群组') . '"',
                    '"' . ($order->user->name ?? '未知用户') . '"',
                    $order->amount,
                    $order->status,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->paid_at ? $order->paid_at->format('Y-m-d H:i:s') : '未支付'
                ]) . "\n";
            }
            
            $filename = 'orders_export_' . now()->format('Y_m_d_H_i_s') . '.csv';
            
            return response($csvContent, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ]);
            
        } catch (\Exception $e) {
            Log::error("导出订单失败: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '导出失败'
            ], 500);
        }
    }

    /**
     * 获取订单完整列表 (管理员用)
     */
    public function getFullOrderList(Request $request)
    {
        $query = Order::with(['wechatGroup', 'user']);
        
        // 应用筛选条件
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->filled('group_id')) {
            $query->where('wechat_group_id', $request->group_id);
        }
        
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_no', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }
        
        $orders = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('limit', 15));
        
        return response()->json([
            'code' => 0,
            'data' => $orders
        ]);
    }

    /**
     * 订单退款
     */
    public function refund(Request $request, $id)
    {
        $order = Order::findOrFail($id);
        
        if ($order->status !== Order::STATUS_PAID) {
            return response()->json([
                'code' => 400,
                'message' => '只有已支付的订单才能退款'
            ], 400);
        }
        
        $data = $request->validate([
            'reason' => 'required|string|max:255'
        ]);
        
        DB::beginTransaction();
        try {
            $order->status = Order::STATUS_REFUNDED;
            $order->refund_reason = $data['reason'];
            $order->refunded_at = now();
            $order->save();
            
            // 这里可以添加实际的退款逻辑
            // 比如调用支付平台的退款API
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '退款成功'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("订单退款失败: " . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '退款失败'
            ], 500);
        }
    }

    // ==================== 缺失的路由方法 ====================
    
    /**
     * 创建订单
     */
    public function store(Request $request)
    {
        $request->validate([
            'wechat_group_id' => 'required|exists:wechat_groups,id',
            'payment_method' => 'sometimes|string|in:wechat,alipay,bank',
        ]);

        $user = $request->user();
        $group = WechatGroup::findOrFail($request->wechat_group_id);

        // 检查群组状态
        if ($group->status !== 1) {
            return response()->json([
                'code' => 400,
                'message' => '群组已关闭，无法下单'
            ], 400);
        }

        // 检查是否已经购买过
        $existingOrder = Order::where('user_id', $user->id)
            ->where('wechat_group_id', $group->id)
            ->where('status', Order::STATUS_PAID)
            ->first();

        if ($existingOrder) {
            return response()->json([
                'code' => 400,
                'message' => '您已经购买过该群组'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $order = Order::create([
                'order_no' => 'ORD' . date('YmdHis') . rand(1000, 9999),
                'user_id' => $user->id,
                'wechat_group_id' => $group->id,
                'amount' => $group->price,
                'status' => Order::STATUS_PENDING,
                'payment_method' => $request->payment_method ?? 'wechat',
            ]);

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '订单创建成功',
                'data' => $order->load('wechatGroup:id,title,price')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("订单创建失败: " . $e->getMessage());

            return response()->json([
                'code' => 500,
                'message' => '订单创建失败'
            ], 500);
        }
    }

    /**
     * 更新订单
     */
    public function update(Request $request, $id)
    {
        $order = Order::findOrFail($id);
        
        // 权限检查
        $this->authorize('update', $order);

        $request->validate([
            'payment_method' => 'sometimes|string|in:wechat,alipay,bank',
            'remark' => 'sometimes|string|max:500',
        ]);

        // 只有待支付的订单才能更新
        if ($order->status !== Order::STATUS_PENDING) {
            return response()->json([
                'code' => 400,
                'message' => '只有待支付的订单才能修改'
            ], 400);
        }

        $order->update($request->only(['payment_method', 'remark']));

        return response()->json([
            'code' => 0,
            'message' => '订单更新成功',
            'data' => $order->fresh()
        ]);
    }

    /**
     * 删除订单
     */
    public function destroy($id)
    {
        $order = Order::findOrFail($id);
        
        // 权限检查
        $this->authorize('delete', $order);

        // 只有待支付或已取消的订单才能删除
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_CANCELLED])) {
            return response()->json([
                'code' => 400,
                'message' => '只有待支付或已取消的订单才能删除'
            ], 400);
        }

        $order->delete();

        return response()->json([
            'code' => 0,
            'message' => '订单删除成功'
        ]);
    }

    /**
     * 支付订单
     */
    public function pay(Request $request, $id)
    {
        $order = Order::findOrFail($id);
        
        // 权限检查
        $this->authorize('pay', $order);

        if ($order->status !== Order::STATUS_PENDING) {
            return response()->json([
                'code' => 400,
                'message' => '订单状态不正确，无法支付'
            ], 400);
        }

        $request->validate([
            'payment_method' => 'required|string|in:wechat,alipay,bank',
        ]);

        DB::beginTransaction();
        try {
            // 更新支付方式
            $order->update([
                'payment_method' => $request->payment_method
            ]);

            // 这里应该调用支付服务创建支付订单
            // 暂时返回模拟的支付信息
            $paymentData = [
                'order_no' => $order->order_no,
                'amount' => $order->amount,
                'payment_method' => $order->payment_method,
                'payment_url' => url('/payment/' . $order->order_no),
                'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            ];

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '支付订单创建成功',
                'data' => $paymentData
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("支付订单创建失败: " . $e->getMessage());

            return response()->json([
                'code' => 500,
                'message' => '支付订单创建失败'
            ], 500);
        }
    }

    // ==================== 管理员方法 ====================
    
    /**
     * 管理员获取订单列表
     */
    public function adminIndex(Request $request)
    {
        $query = Order::with(['user:id,name,username', 'wechatGroup:id,title']);

        // 搜索过滤
        if ($request->filled('order_no')) {
            $query->where('order_no', 'like', '%' . $request->order_no . '%');
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // 时间范围过滤
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'code' => 0,
            'data' => $orders
        ]);
    }

    /**
     * 管理员查看订单详情
     */
    public function adminShow($id)
    {
        $order = Order::with(['user:id,name,username,phone', 'wechatGroup:id,title,price'])
            ->findOrFail($id);

        return response()->json([
            'code' => 0,
            'data' => $order
        ]);
    }

    /**
     * 管理员更新订单
     */
    public function adminUpdate(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        $request->validate([
            'status' => 'sometimes|in:pending,paid,cancelled,refunded',
            'remark' => 'sometimes|string|max:500',
        ]);

        $order->update($request->only(['status', 'remark']));

        return response()->json([
            'code' => 0,
            'message' => '订单更新成功',
            'data' => $order->fresh()
        ]);
    }

    /**
     * 管理员删除订单
     */
    public function adminDestroy($id)
    {
        $order = Order::findOrFail($id);

        // 检查订单状态
        if ($order->status === Order::STATUS_PAID) {
            return response()->json([
                'code' => 400,
                'message' => '已支付的订单无法删除'
            ], 400);
        }

        $order->delete();

        return response()->json([
            'code' => 0,
            'message' => '订单删除成功'
        ]);
    }

    /**
     * 获取完整订单列表
     */
    public function getFullOrderList(Request $request)
    {
        return $this->adminIndex($request);
    }

    /**
     * 获取订单统计数据
     */
    public function getOrderStats(Request $request)
    {
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', Order::STATUS_PENDING)->count(),
            'paid_orders' => Order::where('status', Order::STATUS_PAID)->count(),
            'cancelled_orders' => Order::where('status', Order::STATUS_CANCELLED)->count(),
            'refunded_orders' => Order::where('status', Order::STATUS_REFUNDED)->count(),
            'total_amount' => Order::where('status', Order::STATUS_PAID)->sum('amount'),
            'today_orders' => Order::whereDate('created_at', today())->count(),
            'today_amount' => Order::whereDate('created_at', today())->where('status', Order::STATUS_PAID)->sum('amount'),
        ];

        return response()->json([
            'code' => 0,
            'data' => $stats
        ]);
    }

    /**
     * 批量处理订单
     */
    public function batchProcess(Request $request)
    {
        $request->validate([
            'action' => 'required|in:cancel,delete,refund',
            'ids' => 'required|array',
            'ids.*' => 'exists:orders,id',
        ]);

        $orders = Order::whereIn('id', $request->ids);

        DB::beginTransaction();
        try {
            switch ($request->action) {
                case 'cancel':
                    $orders->where('status', Order::STATUS_PENDING)->update(['status' => Order::STATUS_CANCELLED]);
                    $message = '批量取消成功';
                    break;
                    
                case 'delete':
                    $orders->whereIn('status', [Order::STATUS_PENDING, Order::STATUS_CANCELLED])->delete();
                    $message = '批量删除成功';
                    break;
                    
                case 'refund':
                    $orders->where('status', Order::STATUS_PAID)->update([
                        'status' => Order::STATUS_REFUNDED,
                        'refunded_at' => now(),
                        'refund_reason' => '管理员批量退款'
                    ]);
                    $message = '批量退款成功';
                    break;
            }

            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("批量处理订单失败: " . $e->getMessage());

            return response()->json([
                'code' => 500,
                'message' => '批量处理失败'
            ], 500);
        }
    }

    /**
     * 导出订单数据
     */
    public function exportOrders(Request $request)
    {
        // 这里应该实现订单导出逻辑
        // 暂时返回成功响应
        return response()->json([
            'code' => 0,
            'message' => '导出任务已创建',
            'data' => [
                'task_id' => 'export_' . time(),
                'download_url' => '/exports/orders_' . date('Y-m-d') . '.xlsx'
            ]
        ]);
    }

    /**
     * 获取分站订单列表
     */
    public function substationOrders(Request $request)
    {
        $user = $request->user();
        
        if (!$user->substation_id) {
            return response()->json([
                'code' => 403,
                'message' => '您不属于任何分站'
            ], 403);
        }

        $query = Order::whereHas('user', function($q) use ($user) {
            $q->where('substation_id', $user->substation_id);
        })->with(['user:id,name,username', 'wechatGroup:id,title']);

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'code' => 0,
            'data' => $orders
        ]);
    }
} 