import{_ as e}from"./index-DtXAftX0.js";/* empty css                      *//* empty css               *//* empty css                  */import{bx as a}from"./element-plus-h2SQQM64.js";import{c as t,k as l,l as i,E as p}from"./vue-vendor-Dy164gUc.js";const o={class:"pagination-container"},s=e({__name:"index",props:{total:{type:Number,required:!0,default:0},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:()=>[10,20,30,50]},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},small:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],setup(e,{emit:s}){const n=e,u=s,r=t({get:()=>n.page,set(e){u("update:page",e)}}),d=t({get:()=>n.limit,set(e){u("update:limit",e)}}),g=e=>{u("pagination",{page:r.value,limit:e})},m=e=>{u("pagination",{page:e,limit:d.value})};return(t,s)=>{const n=a;return i(),l("div",o,[p(n,{"current-page":r.value,"onUpdate:currentPage":s[0]||(s[0]=e=>r.value=e),"page-size":d.value,"onUpdate:pageSize":s[1]||(s[1]=e=>d.value=e),"page-sizes":e.pageSizes,total:e.total,layout:e.layout,background:e.background,small:e.small,disabled:e.disabled,onSizeChange:g,onCurrentChange:m},null,8,["current-page","page-size","page-sizes","total","layout","background","small","disabled"])])}}},[["__scopeId","data-v-961e4b3f"]]);export{s as P};
