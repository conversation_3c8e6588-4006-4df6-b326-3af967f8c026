<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建群组"
    width="70%"
    :before-close="handleClose"
    class="group-create-dialog"
  >
    <GroupCreateSteps
      user-role="owner"
      :default-values="ownerDefaults"
      @success="handleCreateSuccess"
      @cancel="handleCreateCancel"
    />


  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import GroupCreateSteps from '@/components/GroupCreateSteps.vue'

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = ref(props.visible)

// 群主专用默认配置
const ownerDefaults = {
  type: 'community',
  auto_city_replace: 1,
  read_count_display: '8万+',
  like_count: 1500,
  want_see_count: 1000,
  button_title: '加入学习群',
  avatar_library: 'qq',
  show_customer_service: 1
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleCreateSuccess = (groupData) => {
  emit('success', groupData)
  dialogVisible.value = false
}

const handleCreateCancel = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.group-create-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .group-create-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }
}
</style>
