<template>
  <div class="navigation-user-panel" :class="{ collapsed }">
    <!-- 分隔线 -->
    <div class="panel-divider"></div>
    
    <!-- 用户信息区域 -->
    <div class="user-section">
      <!-- 用户头像和信息 -->
      <div class="user-info" @click="toggleUserMenu">
        <div class="user-avatar-container">
          <el-avatar 
            :size="collapsed ? 36 : 44" 
            :src="user?.avatar" 
            class="user-avatar"
          >
            <el-icon><UserFilled /></el-icon>
          </el-avatar>
          
          <!-- 在线状态指示器 -->
          <div class="online-status" :class="onlineStatus"></div>
        </div>
        
        <transition name="user-details">
          <div v-if="!collapsed" class="user-details">
            <div class="user-name">{{ user?.nickname || user?.username || '管理员' }}</div>
            <div class="user-role" :style="{ background: roleConfig.background, color: roleConfig.color }">
              {{ roleConfig.label }}
            </div>
            <div class="user-status">
              <span class="status-dot" :class="onlineStatus"></span>
              {{ onlineStatusText }}
            </div>
          </div>
        </transition>
        
        <transition name="chevron">
          <el-icon v-if="!collapsed" class="chevron-icon" :class="{ rotated: userMenuOpen }">
            <ArrowUp />
          </el-icon>
        </transition>
      </div>
      
      <!-- 用户菜单 -->
      <transition name="user-menu">
        <div v-if="userMenuOpen && !collapsed" class="user-menu">
          <div class="menu-item" @click="handleProfile">
            <el-icon><User /></el-icon>
            <span>个人资料</span>
          </div>
          <div class="menu-item" @click="handleSettings">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </div>
          <div class="menu-item" @click="handleHelp">
            <el-icon><QuestionFilled /></el-icon>
            <span>帮助中心</span>
          </div>
          <div class="menu-divider"></div>
          <div class="menu-item logout" @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            <span>退出登录</span>
          </div>
        </div>
      </transition>
    </div>
    
    <!-- 收缩状态的快捷操作 -->
    <div v-if="collapsed" class="collapsed-actions">
      <el-tooltip content="个人资料" placement="right">
        <div class="action-button" @click="handleProfile">
          <el-icon><User /></el-icon>
        </div>
      </el-tooltip>
      
      <el-tooltip content="退出登录" placement="right">
        <div class="action-button logout" @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
        </div>
      </el-tooltip>
    </div>
    
    <!-- 系统信息（仅在展开状态显示） -->
    <transition name="system-info">
      <div v-if="!collapsed" class="system-info">
        <div class="info-item">
          <span class="info-label">系统版本</span>
          <span class="info-value">v{{ systemVersion }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">在线时长</span>
          <span class="info-value">{{ onlineTime }}</span>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import { 
  UserFilled, User, Setting, QuestionFilled, SwitchButton, ArrowUp 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  user: {
    type: Object,
    default: () => ({})
  },
  collapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['logout', 'profile', 'settings', 'help'])

// Data
const userMenuOpen = ref(false)
const onlineStatus = ref('online') // online, away, busy, offline
const systemVersion = ref('2.1.0')
const sessionStartTime = ref(Date.now())
const onlineTime = ref('00:00')

// 计算属性
const roleConfig = computed(() => {
  const role = props.user?.role || 'admin'
  const configs = {
    admin: {
      label: '超级管理员',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: '#ffffff'
    },
    substation: {
      label: '分站管理员',
      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      color: '#ffffff'
    },
    agent: {
      label: '代理商',
      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      color: '#ffffff'
    },
    distributor: {
      label: '分销员',
      background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      color: '#ffffff'
    },
    group_owner: {
      label: '群主',
      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      color: '#ffffff'
    },
    user: {
      label: '普通用户',
      background: '#f0f2f5',
      color: '#606266'
    }
  }
  return configs[role] || configs.admin
})

const onlineStatusText = computed(() => {
  const statusMap = {
    online: '在线',
    away: '离开',
    busy: '忙碌',
    offline: '离线'
  }
  return statusMap[onlineStatus.value] || '未知'
})

// 定时器
let onlineTimeTimer = null

// Methods
const toggleUserMenu = () => {
  if (!props.collapsed) {
    userMenuOpen.value = !userMenuOpen.value
  }
}

const handleProfile = () => {
  emit('profile')
  userMenuOpen.value = false
}

const handleSettings = () => {
  emit('settings')
  userMenuOpen.value = false
}

const handleHelp = () => {
  emit('help')
  userMenuOpen.value = false
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定退出',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'modern-message-box'
      }
    )
    
    emit('logout')
  } catch (error) {
    // 用户取消
  }
}

// 更新在线时长
const updateOnlineTime = () => {
  const now = Date.now()
  const diff = now - sessionStartTime.value
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  onlineTime.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  // 启动在线时长计时器
  onlineTimeTimer = setInterval(updateOnlineTime, 60000) // 每分钟更新
  updateOnlineTime()
  
  // 模拟状态变化
  setTimeout(() => {
    onlineStatus.value = 'online'
  }, 1000)
  
  // 点击外部关闭菜单
  const handleClickOutside = (event) => {
    if (!event.target.closest('.navigation-user-panel')) {
      userMenuOpen.value = false
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  return () => {
    document.removeEventListener('click', handleClickOutside)
  }
})

onUnmounted(() => {
  if (onlineTimeTimer) {
    clearInterval(onlineTimeTimer)
  }
})
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.navigation-user-panel {
  border-top: 1px solid #f0f2ff;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), transparent);
  transition: all 0.3s ease;
  
  .panel-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
    margin: 0 16px;
  }
  
  // 用户信息区域
  .user-section {
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 20px 16px;
      cursor: pointer;
      border-radius: 12px;
      margin: 16px 8px 0;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(102, 126, 234, 0.08);
        transform: translateY(-1px);
      }
      
      .user-avatar-container {
        position: relative;
        
        .user-avatar {
          border: 3px solid rgba(102, 126, 234, 0.2);
          transition: all 0.3s ease;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          
          &:hover {
            border-color: #667eea;
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
          }
        }
        
        .online-status {
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 2px solid white;
          
          &.online {
            background: #10b981;
            animation: status-pulse 2s infinite;
          }
          
          &.away {
            background: #f59e0b;
          }
          
          &.busy {
            background: #ef4444;
          }
          
          &.offline {
            background: #6b7280;
          }
        }
      }
      
      .user-details {
        flex: 1;
        min-width: 0;
        
        .user-name {
          font-size: 15px;
          font-weight: 600;
          color: #303133;
          line-height: 1.3;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .user-role {
          display: inline-block;
          font-size: 11px;
          font-weight: 600;
          padding: 3px 8px;
          border-radius: 12px;
          margin-bottom: 4px;
          text-transform: uppercase;
          letter-spacing: 0.3px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .user-status {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 11px;
          color: #909399;
          
          .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            
            &.online {
              background: #10b981;
            }
            
            &.away {
              background: #f59e0b;
            }
            
            &.busy {
              background: #ef4444;
            }
            
            &.offline {
              background: #6b7280;
            }
          }
        }
      }
      
      .chevron-icon {
        color: #909399;
        transition: all 0.3s ease;
        
        &.rotated {
          transform: rotate(180deg);
          color: #667eea;
        }
      }
    }
    
    // 用户菜单
    .user-menu {
      margin: 0 8px 16px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid #f0f2ff;
      overflow: hidden;
      
      .menu-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        font-size: 13px;
        font-weight: 500;
        color: #606266;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: linear-gradient(90deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.03));
          color: #667eea;
          transform: translateX(2px);
        }
        
        &.logout {
          color: #ef4444;
          
          &:hover {
            background: linear-gradient(90deg, rgba(239, 68, 68, 0.08), rgba(239, 68, 68, 0.03));
            color: #ef4444;
          }
        }
        
        .el-icon {
          font-size: 14px;
        }
      }
      
      .menu-divider {
        height: 1px;
        background: linear-gradient(90deg, transparent, #e4e7ed, transparent);
        margin: 4px 0;
      }
    }
  }
  
  // 收缩状态的快捷操作
  .collapsed-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px 8px;
    align-items: center;
    
    .action-button {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      
      &:hover {
        background: rgba(102, 126, 234, 0.2);
        transform: scale(1.05);
      }
      
      &.logout {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        
        &:hover {
          background: rgba(239, 68, 68, 0.2);
        }
      }
    }
  }
  
  // 系统信息
  .system-info {
    padding: 16px;
    margin: 0 8px 16px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.03));
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 11px;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        color: #909399;
      }
      
      .info-value {
        color: #606266;
        font-weight: 600;
      }
    }
  }
  
  // 收缩状态
  &.collapsed {
    .panel-divider {
      margin: 0 8px;
    }
    
    .user-section .user-info {
      justify-content: center;
      padding: 16px 8px;
      margin: 12px 4px 0;
    }
  }
}

// 动画定义
@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

// 过渡动画
.user-details-enter-active,
.user-details-leave-active {
  transition: all 0.3s ease;
}

.user-details-enter-from,
.user-details-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.chevron-enter-active,
.chevron-leave-active {
  transition: all 0.3s ease;
}

.chevron-enter-from,
.chevron-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.user-menu-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-menu-leave-active {
  transition: all 0.2s ease;
}

.user-menu-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.user-menu-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

.system-info-enter-active,
.system-info-leave-active {
  transition: all 0.3s ease;
}

.system-info-enter-from,
.system-info-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

// 响应式设计
@media (max-width: 768px) {
  .navigation-user-panel {
    .user-section .user-info {
      padding: 16px 12px;
      
      .user-details {
        .user-name {
          font-size: 14px;
        }
        
        .user-role {
          font-size: 10px;
          padding: 2px 6px;
        }
        
        .user-status {
          font-size: 10px;
        }
      }
    }
    
    .user-menu .menu-item {
      padding: 10px 12px;
      font-size: 12px;
    }
    
    .system-info {
      padding: 12px;
      
      .info-item {
        font-size: 10px;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .navigation-user-panel {
    border-top-color: #000;
    
    .user-section .user-info {
      .user-details {
        .user-name {
          color: #000;
          font-weight: 800;
        }
        
        .user-status {
          color: #000;
        }
      }
      
      &:hover {
        background: rgba(0, 0, 0, 0.1);
        border: 1px solid #000;
      }
    }
    
    .user-menu {
      border-color: #000;
      
      .menu-item {
        color: #000;
        
        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 暗色主题
@media (prefers-color-scheme: dark) {
  .navigation-user-panel {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(17, 24, 39, 0.5));
    border-top-color: #374151;
    
    .user-section .user-info {
      .user-details {
        .user-name {
          color: #e5e7eb;
        }
        
        .user-status {
          color: #9ca3af;
        }
      }
      
      &:hover {
        background: rgba(102, 126, 234, 0.15);
      }
    }
    
    .user-menu {
      background: #1f2937;
      border-color: #374151;
      
      .menu-item {
        color: #d1d5db;
        
        &:hover {
          background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.08));
          color: #8b9aff;
        }
      }
    }
    
    .system-info {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(17, 24, 39, 0.8));
      border-color: rgba(102, 126, 234, 0.2);
      
      .info-item {
        .info-label {
          color: #9ca3af;
        }
        
        .info-value {
          color: #d1d5db;
        }
      }
    }
  }
}

// 动画偏好
@media (prefers-reduced-motion: reduce) {
  .navigation-user-panel {
    * {
      animation: none !important;
      transition: none !important;
    }
  }
}
</style>