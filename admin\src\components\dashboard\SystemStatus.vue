<template>
  <div class="system-status-card">
    <div class="card-header">
      <h3 class="card-title">系统状态</h3>
      <div class="status-indicator online">
        <div class="status-dot"></div>
        运行正常
      </div>
    </div>
    <div class="status-metrics">
      <div class="status-item">
        <div class="status-label">CPU 使用率</div>
        <div class="status-progress">
          <el-progress :percentage="status.cpu" :show-text="false" />
          <span class="status-value">{{ status.cpu }}%</span>
        </div>
      </div>
      <div class="status-item">
        <div class="status-label">内存使用率</div>
        <div class="status-progress">
          <el-progress :percentage="status.memory" :show-text="false" color="#10b981" />
          <span class="status-value">{{ status.memory }}%</span>
        </div>
      </div>
      <div class="status-item">
        <div class="status-label">磁盘使用率</div>
        <div class="status-progress">
          <el-progress :percentage="status.disk" :show-text="false" color="#f59e0b" />
          <span class="status-value">{{ status.disk }}%</span>
        </div>
      </div>
      <div class="status-item">
        <div class="status-label">网络延迟</div>
        <div class="status-progress">
          <span class="status-value">{{ status.latency }}ms</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: Object,
    default: () => ({
      cpu: 45,
      memory: 62,
      disk: 78,
      latency: 23
    })
  }
})
</script>

<style lang="scss" scoped>
.system-status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      font-weight: 500;

      &.online {
        color: #10b981;

        .status-dot {
          width: 8px;
          height: 8px;
          background: #10b981;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .status-metrics {
    .status-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .status-label {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .status-progress {
        display: flex;
        align-items: center;
        gap: 12px;

        :deep(.el-progress) {
          flex: 1;

          .el-progress-bar__outer {
            background: #f3f4f6;
            border-radius: 6px;
          }

          .el-progress-bar__inner {
            border-radius: 6px;
          }
        }

        .status-value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;
          min-width: 40px;
          text-align: right;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>