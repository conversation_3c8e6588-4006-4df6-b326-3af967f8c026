import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     *//* empty css                       */import{aY as a,bp as l,bq as t,aM as r,b9 as u,b8 as s,by as d,at as o,T as n,aL as p,aR as i,bh as c,bi as m,U as _,a$ as f,Y as v,bv as b,as as g,bw as h,bx as y,a4 as k,bk as w,bl as j,ay as V,Q as z}from"./element-plus-h2SQQM64.js";import{P as C}from"./PageLayout-C6qH3ReN.js";import{f as x,h as U,p as Y}from"./payment-BistKFiU.js";import{r as D,L as R,e as M,k as P,l as q,E as L,z as O,t as F,D as S,u as $,A,y as B,B as E}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const I={class:"payment-refunds"},Q={class:"filter-section"},T={class:"table-section"},G={class:"card-header"},H={class:"header-actions"},J={class:"amount"},K={class:"pagination-wrapper"},N={key:0,class:"refund-detail"},W={class:"amount"},X={class:"refund-reason"},Z={key:0,class:"refund-remark"},ee=e({__name:"PaymentRefunds",setup(e){const ee=D(!1),ae=D(!1),le=D(!1),te=D(!1),re=D(!1),ue=D("approve"),se=D(null),de=R({refund_no:"",order_no:"",status:"",date_range:[]}),oe=R({remark:""}),ne=D([]),pe=R({current:1,size:20,total:0}),ie=async()=>{ee.value=!0;try{const e={page:pe.current,size:pe.size,...de},a=await x(e);ne.value=a.data.list,pe.total=a.data.total}catch(e){ne.value=[{id:1,refund_no:"RF202401010001",order_no:"ORD202401010001",amount:99,reason:"商品质量问题",status:"pending",payment_method:"alipay",applicant:"张三",created_at:"2024-01-01 10:00:00",processed_at:null,remark:""},{id:2,refund_no:"RF202401010002",order_no:"ORD202401010002",amount:199,reason:"不喜欢商品",status:"completed",payment_method:"wechat",applicant:"李四",created_at:"2024-01-01 11:00:00",processed_at:"2024-01-01 12:00:00",remark:"已处理完成"}],pe.total=2}finally{ee.value=!1}},ce=()=>{pe.current=1,ie()},me=()=>{Object.assign(de,{refund_no:"",order_no:"",status:"",date_range:[]}),ce()},_e=e=>{pe.size=e,ie()},fe=e=>{pe.current=e,ie()},ve=async()=>{if(oe.remark.trim()){le.value=!0;try{await Y(se.value.id,{action:ue.value,remark:oe.remark}),z.success(`退款${"approve"===ue.value?"通过":"拒绝"}成功`),re.value=!1,ie()}catch(e){z.error("处理失败："+e.message)}finally{le.value=!1}}else z.warning("请输入处理备注")},be=async()=>{ae.value=!0;try{await U(de),z.success("导出成功")}catch(e){z.error("导出失败："+e.message)}finally{ae.value=!1}},ge=e=>({pending:"warning",processing:"primary",completed:"success",rejected:"danger"}[e]||"info"),he=e=>({pending:"待处理",processing:"处理中",completed:"已完成",rejected:"已拒绝"}[e]||"未知"),ye=e=>({alipay:"支付宝",wechat:"微信支付",easypay:"易支付",bank:"银行卡"}[e]||"未知");return M(()=>{ie()}),(e,z)=>{const x=r,U=t,Y=s,D=u,R=d,M=n,ie=o,ke=l,we=a,je=m,Ve=f,ze=c,Ce=y,xe=j,Ue=w,Ye=V,De=h;return q(),P("div",I,[L(C,{title:"退款管理",subtitle:"管理支付退款申请和处理"},{default:O(()=>[F("div",Q,[L(we,{class:"filter-card"},{default:O(()=>[L(ke,{model:de,inline:""},{default:O(()=>[L(U,{label:"退款单号"},{default:O(()=>[L(x,{modelValue:de.refund_no,"onUpdate:modelValue":z[0]||(z[0]=e=>de.refund_no=e),placeholder:"请输入退款单号",clearable:""},null,8,["modelValue"])]),_:1}),L(U,{label:"订单号"},{default:O(()=>[L(x,{modelValue:de.order_no,"onUpdate:modelValue":z[1]||(z[1]=e=>de.order_no=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),L(U,{label:"退款状态"},{default:O(()=>[L(D,{modelValue:de.status,"onUpdate:modelValue":z[2]||(z[2]=e=>de.status=e),placeholder:"请选择状态",clearable:""},{default:O(()=>[L(Y,{label:"待处理",value:"pending"}),L(Y,{label:"处理中",value:"processing"}),L(Y,{label:"已完成",value:"completed"}),L(Y,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),L(U,{label:"申请时间"},{default:O(()=>[L(R,{modelValue:de.date_range,"onUpdate:modelValue":z[3]||(z[3]=e=>de.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),L(U,null,{default:O(()=>[L(ie,{type:"primary",onClick:ce,loading:ee.value},{default:O(()=>[L(M,null,{default:O(()=>[L($(p))]),_:1}),z[10]||(z[10]=S(" 搜索 ",-1))]),_:1,__:[10]},8,["loading"]),L(ie,{onClick:me},{default:O(()=>[L(M,null,{default:O(()=>[L($(i))]),_:1}),z[11]||(z[11]=S(" 重置 ",-1))]),_:1,__:[11]})]),_:1})]),_:1},8,["model"])]),_:1})]),F("div",T,[L(we,{class:"table-card"},{header:O(()=>[F("div",G,[z[13]||(z[13]=F("h3",null,"退款列表",-1)),F("div",H,[L(ie,{onClick:be,loading:ae.value},{default:O(()=>[L(M,null,{default:O(()=>[L($(k))]),_:1}),z[12]||(z[12]=S(" 导出 ",-1))]),_:1,__:[12]},8,["loading"])])])]),default:O(()=>[A((q(),B(ze,{data:ne.value,stripe:""},{default:O(()=>[L(je,{prop:"refund_no",label:"退款单号",width:"180"}),L(je,{prop:"order_no",label:"订单号",width:"180"}),L(je,{prop:"amount",label:"退款金额",width:"120"},{default:O(({row:e})=>[F("span",J,"¥"+_(e.amount),1)]),_:1}),L(je,{prop:"reason",label:"退款原因",width:"200","show-overflow-tooltip":""}),L(je,{prop:"status",label:"状态",width:"100"},{default:O(({row:e})=>[L(Ve,{type:ge(e.status)},{default:O(()=>[S(_(he(e.status)),1)]),_:2},1032,["type"])]),_:1}),L(je,{prop:"payment_method",label:"支付方式",width:"120"},{default:O(({row:e})=>[L(Ve,{size:"small"},{default:O(()=>[S(_(ye(e.payment_method)),1)]),_:2},1024)]),_:1}),L(je,{prop:"applicant",label:"申请人",width:"120"}),L(je,{prop:"created_at",label:"申请时间",width:"160"}),L(je,{label:"操作",width:"200",fixed:"right"},{default:O(({row:e})=>[L(ie,{size:"small",onClick:a=>{return l=e,se.value=l,void(te.value=!0);var l}},{default:O(()=>[L(M,null,{default:O(()=>[L($(v))]),_:1}),z[14]||(z[14]=S(" 查看 ",-1))]),_:2,__:[14]},1032,["onClick"]),"pending"===e.status?(q(),B(ie,{key:0,size:"small",type:"success",onClick:a=>{return l=e,se.value=l,ue.value="approve",oe.remark="",void(re.value=!0);var l}},{default:O(()=>[L(M,null,{default:O(()=>[L($(b))]),_:1}),z[15]||(z[15]=S(" 通过 ",-1))]),_:2,__:[15]},1032,["onClick"])):E("",!0),"pending"===e.status?(q(),B(ie,{key:1,size:"small",type:"danger",onClick:a=>{return l=e,se.value=l,ue.value="reject",oe.remark="",void(re.value=!0);var l}},{default:O(()=>[L(M,null,{default:O(()=>[L($(g))]),_:1}),z[16]||(z[16]=S(" 拒绝 ",-1))]),_:2,__:[16]},1032,["onClick"])):E("",!0)]),_:1})]),_:1},8,["data"])),[[De,ee.value]]),F("div",K,[L(Ce,{"current-page":pe.current,"onUpdate:currentPage":z[4]||(z[4]=e=>pe.current=e),"page-size":pe.size,"onUpdate:pageSize":z[5]||(z[5]=e=>pe.size=e),total:pe.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_e,onCurrentChange:fe},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1}),L(Ye,{modelValue:te.value,"onUpdate:modelValue":z[6]||(z[6]=e=>te.value=e),title:"退款详情",width:"800px"},{default:O(()=>[se.value?(q(),P("div",N,[L(Ue,{column:2,border:""},{default:O(()=>[L(xe,{label:"退款单号"},{default:O(()=>[S(_(se.value.refund_no),1)]),_:1}),L(xe,{label:"订单号"},{default:O(()=>[S(_(se.value.order_no),1)]),_:1}),L(xe,{label:"退款金额"},{default:O(()=>[F("span",W,"¥"+_(se.value.amount),1)]),_:1}),L(xe,{label:"支付方式"},{default:O(()=>[S(_(ye(se.value.payment_method)),1)]),_:1}),L(xe,{label:"退款状态"},{default:O(()=>[L(Ve,{type:ge(se.value.status)},{default:O(()=>[S(_(he(se.value.status)),1)]),_:1},8,["type"])]),_:1}),L(xe,{label:"申请人"},{default:O(()=>[S(_(se.value.applicant),1)]),_:1}),L(xe,{label:"申请时间"},{default:O(()=>[S(_(se.value.created_at),1)]),_:1}),L(xe,{label:"处理时间"},{default:O(()=>[S(_(se.value.processed_at||"未处理"),1)]),_:1})]),_:1}),F("div",X,[z[17]||(z[17]=F("h4",null,"退款原因",-1)),F("p",null,_(se.value.reason),1)]),se.value.remark?(q(),P("div",Z,[z[18]||(z[18]=F("h4",null,"处理备注",-1)),F("p",null,_(se.value.remark),1)])):E("",!0)])):E("",!0)]),_:1},8,["modelValue"]),L(Ye,{modelValue:re.value,"onUpdate:modelValue":z[9]||(z[9]=e=>re.value=e),title:"approve"===ue.value?"通过退款":"拒绝退款",width:"500px"},{footer:O(()=>[L(ie,{onClick:z[8]||(z[8]=e=>re.value=!1)},{default:O(()=>z[19]||(z[19]=[S("取消",-1)])),_:1,__:[19]}),L(ie,{type:"approve"===ue.value?"success":"danger",onClick:ve,loading:le.value},{default:O(()=>[S(" 确认"+_("approve"===ue.value?"通过":"拒绝"),1)]),_:1},8,["type","loading"])]),default:O(()=>[L(ke,{model:oe,"label-width":"80px"},{default:O(()=>[L(U,{label:"处理备注",required:""},{default:O(()=>[L(x,{modelValue:oe.remark,"onUpdate:modelValue":z[7]||(z[7]=e=>oe.remark=e),type:"textarea",rows:4,placeholder:"approve"===ue.value?"请输入通过原因":"请输入拒绝原因"},null,8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-8d6eff59"]]);export{ee as default};
