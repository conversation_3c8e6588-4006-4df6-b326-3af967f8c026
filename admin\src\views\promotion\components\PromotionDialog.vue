<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑推广链接' : '创建推广链接'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联群组" prop="group_id">
            <el-select
              v-model="form.group_id"
              placeholder="选择群组"
              style="width: 100%"
              filterable
              @change="handleGroupChange"
            >
              <el-option
                v-for="group in groups"
                :key="group.id"
                :label="group.title"
                :value="group.id"
              >
                <div class="group-option">
                  <span class="group-title">{{ group.title }}</span>
                  <span class="group-category">{{ group.category_name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="链接标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="输入链接标题"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="链接描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="输入链接描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="过期时间" prop="expires_at">
        <el-date-picker
          v-model="form.expires_at"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 追踪参数 -->
      <el-card class="tracking-params-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>追踪参数</span>
            <el-tooltip content="用于统计分析链接来源和效果">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="来源标识">
              <el-input
                v-model="form.tracking_params.source"
                placeholder="如：wechat, qq, weibo"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="媒介类型">
              <el-select
                v-model="form.tracking_params.utm_medium"
                placeholder="选择媒介类型"
                style="width: 100%"
              >
                <el-option label="社交媒体" value="social" />
                <el-option label="邮件" value="email" />
                <el-option label="短信" value="sms" />
                <el-option label="广告" value="ad" />
                <el-option label="直接链接" value="link" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称">
              <el-input
                v-model="form.tracking_params.utm_campaign"
                placeholder="如：春节推广、新用户活动"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内容标识">
              <el-input
                v-model="form.tracking_params.utm_content"
                placeholder="如：banner1, text_link"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 预览区域 -->
      <el-card v-if="selectedGroup" class="preview-card" shadow="never">
        <template #header>
          <span>群组预览</span>
        </template>
        <div class="group-preview">
          <div class="group-info">
            <h4>{{ selectedGroup.title }}</h4>
            <p class="group-desc">{{ selectedGroup.description }}</p>
            <div class="group-meta">
              <el-tag size="small" type="info">{{ selectedGroup.category_name }}</el-tag>
              <span class="group-price">
                {{ selectedGroup.price > 0 ? '¥' + selectedGroup.price : '免费' }}
              </span>
              <span class="group-members">{{ selectedGroup.member_count }}/{{ selectedGroup.member_limit }}人</span>
            </div>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { usePromotionStore } from '@/stores/promotion'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  link: {
    type: Object,
    default: null
  },
  groups: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 状态管理
const promotionStore = usePromotionStore()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  group_id: '',
  title: '',
  description: '',
  expires_at: '',
  tracking_params: {
    source: '',
    utm_medium: 'link',
    utm_campaign: '',
    utm_content: ''
  }
})

// 表单验证规则
const rules = {
  group_id: [
    { required: true, message: '请选择关联群组', trigger: 'change' }
  ],
  title: [
    { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ],
  expires_at: [
    { required: true, message: '请选择过期时间', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.link)

const selectedGroup = computed(() => {
  if (!form.group_id) return null
  return props.groups.find(group => group.id === form.group_id)
})

// 方法
const initForm = () => {
  if (props.link) {
    // 编辑模式
    Object.assign(form, {
      group_id: props.link.group_id,
      title: props.link.title || '',
      description: props.link.description || '',
      expires_at: props.link.expires_at,
      tracking_params: {
        source: props.link.tracking_params?.source || '',
        utm_medium: props.link.tracking_params?.utm_medium || 'link',
        utm_campaign: props.link.tracking_params?.utm_campaign || '',
        utm_content: props.link.tracking_params?.utm_content || ''
      }
    })
  } else {
    // 创建模式
    resetForm()
    // 设置默认过期时间为6个月后
    const defaultExpiry = new Date()
    defaultExpiry.setMonth(defaultExpiry.getMonth() + 6)
    form.expires_at = defaultExpiry.toISOString().slice(0, 19).replace('T', ' ')
  }
}

const resetForm = () => {
  Object.assign(form, {
    group_id: '',
    title: '',
    description: '',
    expires_at: '',
    tracking_params: {
      source: '',
      utm_medium: 'link',
      utm_campaign: '',
      utm_content: ''
    }
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleGroupChange = (groupId) => {
  const group = props.groups.find(g => g.id === groupId)
  if (group && !form.title) {
    // 自动填充标题
    form.title = `${group.title} - 推广链接`
  }
  if (group && !form.description) {
    // 自动填充描述
    form.description = `加入${group.title}，${group.description || '一起交流学习'}`
  }
}

const disabledDate = (time) => {
  // 不能选择今天之前的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const submitData = {
      ...form,
      tracking_params: Object.fromEntries(
        Object.entries(form.tracking_params).filter(([key, value]) => value)
      )
    }
    
    if (isEdit.value) {
      await promotionStore.updatePromotionLink(props.link.id, submitData)
      ElMessage.success('推广链接更新成功')
    } else {
      await promotionStore.createPromotionLink(submitData)
      ElMessage.success('推广链接创建成功')
    }
    
    emit('success')
    
  } catch (error) {
    if (error.errors) {
      // 显示验证错误
      const firstError = Object.values(error.errors)[0]
      ElMessage.error(Array.isArray(firstError) ? firstError[0] : firstError)
    } else {
      ElMessage.error(error.message || '操作失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      nextTick(() => {
        initForm()
      })
    }
  }
)
</script>

<style scoped>
.group-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-title {
  font-weight: 600;
  color: #303133;
}

.group-category {
  font-size: 12px;
  color: #909399;
}

.tracking-params-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-card {
  margin-top: 20px;
}

.group-preview {
  padding: 10px 0;
}

.group-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.group-desc {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.group-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
}

.group-price {
  color: #f56c6c;
  font-weight: 600;
}

.group-members {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>