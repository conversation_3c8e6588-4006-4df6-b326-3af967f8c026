import request from '@/utils/request'
import { mockSubstationAPI } from './mock/substation'

const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' || 
                (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL)

// --- 分站管理 ---
export function getSubstationList(params) {
  if (useMock) return mockSubstationAPI.getSubstationList(params)
  return request({ url: '/admin/substations', method: 'get', params })
}

export function getSubstationDetail(id) {
  if (useMock) return mockSubstationAPI.getSubstationDetail(id)
  return request({ url: `/admin/substations/${id}`, method: 'get' })
}

export function createSubstation(data) {
  if (useMock) return mockSubstationAPI.createSubstation(data)
  return request({ url: '/admin/substations', method: 'post', data })
}

export function updateSubstation(id, data) {
  if (useMock) return mockSubstationAPI.updateSubstation(id, data)
  return request({ url: `/admin/substations/${id}`, method: 'put', data })
}

export function deleteSubstation(id) {
  if (useMock) return mockSubstationAPI.deleteSubstation(id)
  return request({ url: `/admin/substations/${id}`, method: 'delete' })
}

export function renewSubstation(id, data) {
  if (useMock) return mockSubstationAPI.renewSubstation(id, data)
  return request({ url: `/admin/substations/${id}/renew`, method: 'post', data })
}

export function updateSubstationStatus(id, status) {
  if (useMock) return mockSubstationAPI.updateSubstationStatus(id, status)
  return request({ url: `/admin/substations/${id}/status`, method: 'put', data: { status } })
}

// --- 分站财务 ---
export function getSubstationFinanceStats(params) {
  if (useMock) return mockSubstationAPI.getSubstationFinanceStats(params)
  return request({ url: '/admin/substation-finance/stats', method: 'get', params })
}

export function getSubstationSettlementRecords(params) {
  if (useMock) return mockSubstationAPI.getSubstationSettlementRecords(params)
  return request({ url: '/admin/substation-finance/settlements', method: 'get', params })
}

export function generateSubstationFinanceReport(data) {
  if (useMock) return mockSubstationAPI.generateSubstationFinanceReport(data)
  return request({ url: '/admin/substation-finance/report', method: 'post', data })
}

export function batchSettleCommissions(data) {
  if (useMock) return mockSubstationAPI.batchSettleCommissions(data)
  return request({ url: '/admin/substation-finance/batch-settle', method: 'post', data })
}