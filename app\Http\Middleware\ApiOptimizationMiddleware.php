<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Services\CacheServiceMerged;

/**
 * API性能优化中间件
 * 
 * 功能：
 * 1. 响应缓存
 * 2. 数据压缩
 * 3. 响应时间记录
 * 4. 限流保护
 */
class ApiOptimizationMiddleware
{
    /**
     * 可缓存的API端点
     */
    protected $cacheableEndpoints = [
        'dashboard/stats',
        'users/profile',
        'orders/statistics',
        'distribution/groups',
        'system/settings',
    ];

    /**
     * 缓存时间配置（秒）
     */
    protected $cacheTtl = [
        'dashboard/stats' => 600,      // 10分钟
        'users/profile' => 1800,       // 30分钟
        'orders/statistics' => 300,    // 5分钟
        'distribution/groups' => 900,  // 15分钟
        'system/settings' => 3600,     // 1小时
    ];

    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        
        // 只处理GET请求的缓存
        if ($request->isMethod('GET')) {
            $cachedResponse = $this->getCachedResponse($request);
            if ($cachedResponse) {
                return $this->addPerformanceHeaders($cachedResponse, $startTime, true);
            }
        }

        $response = $next($request);

        // 记录响应时间
        $responseTime = (microtime(true) - $startTime) * 1000;
        
        // 缓存响应
        if ($request->isMethod('GET') && $response->isSuccessful()) {
            $this->cacheResponse($request, $response);
        }

        // 压缩响应
        if ($this->shouldCompress($request, $response)) {
            $response = $this->compressResponse($response);
        }

        return $this->addPerformanceHeaders($response, $startTime, false, $responseTime);
    }

    /**
     * 获取缓存的响应
     */
    protected function getCachedResponse(Request $request)
    {
        $endpoint = $this->getEndpointFromRequest($request);
        
        if (!$this->shouldCache($endpoint)) {
            return null;
        }

        $cacheKey = $this->getCacheKey($request);
        $cachedData = Cache::get($cacheKey);
        
        if ($cachedData) {
            return response()->json($cachedData)
                ->header('X-Cache-Status', 'HIT')
                ->header('X-Cache-Key', $cacheKey);
        }

        return null;
    }

    /**
     * 缓存响应
     */
    protected function cacheResponse(Request $request, $response)
    {
        $endpoint = $this->getEndpointFromRequest($request);
        
        if (!$this->shouldCache($endpoint)) {
            return;
        }

        $cacheKey = $this->getCacheKey($request);
        $ttl = $this->cacheTtl[$endpoint] ?? 300;
        
        try {
            $data = json_decode($response->getContent(), true);
            
            // 只缓存成功的响应
            if ($data && isset($data['success']) && $data['success']) {
                Cache::put($cacheKey, $data, $ttl);
                
                // 记录缓存统计
                app(CacheServiceMerged::class)->cacheApiResponse($endpoint, $request->all(), $data);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to cache API response: ' . $e->getMessage());
        }
    }

    /**
     * 是否应该缓存
     */
    protected function shouldCache($endpoint)
    {
        return in_array($endpoint, $this->cacheableEndpoints);
    }

    /**
     * 获取端点路径
     */
    protected function getEndpointFromRequest(Request $request)
    {
        $path = $request->path();
        
        // 移除API版本前缀
        if (strpos($path, 'api/v1/') === 0) {
            $path = substr($path, 7);
        }
        
        return $path;
    }

    /**
     * 生成缓存键
     */
    protected function getCacheKey(Request $request)
    {
        $endpoint = $this->getEndpointFromRequest($request);
        $params = $request->all();
        $user = auth()->user();
        
        // 包含用户ID以支持用户级缓存
        $keyData = [
            'endpoint' => $endpoint,
            'params' => $params,
            'user_id' => $user ? $user->id : 'guest',
            'user_role' => $user ? $user->role : 'guest',
        ];
        
        return 'api_cache:' . md5(serialize($keyData));
    }

    /**
     * 是否应该压缩响应
     */
    protected function shouldCompress(Request $request, $response)
    {
        // 检查客户端是否支持压缩
        $acceptEncoding = $request->header('Accept-Encoding', '');
        
        if (!str_contains($acceptEncoding, 'gzip')) {
            return false;
        }

        // 检查响应类型
        $contentType = $response->headers->get('Content-Type', '');
        $compressibleTypes = [
            'application/json',
            'text/html',
            'text/plain',
            'text/css',
            'application/javascript',
        ];

        foreach ($compressibleTypes as $type) {
            if (str_contains($contentType, $type)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 压缩响应
     */
    protected function compressResponse($response)
    {
        $content = $response->getContent();
        
        // 只压缩大于1KB的内容
        if (strlen($content) > 1024) {
            $compressed = gzencode($content, 6);
            
            if ($compressed !== false) {
                $response->setContent($compressed);
                $response->headers->set('Content-Encoding', 'gzip');
                $response->headers->set('Content-Length', strlen($compressed));
            }
        }

        return $response;
    }

    /**
     * 添加性能头部
     */
    protected function addPerformanceHeaders($response, $startTime, $fromCache = false, $responseTime = null)
    {
        if (!$responseTime) {
            $responseTime = (microtime(true) - $startTime) * 1000;
        }

        $response->headers->set('X-Response-Time', round($responseTime, 2) . 'ms');
        $response->headers->set('X-Cache-Status', $fromCache ? 'HIT' : 'MISS');
        $response->headers->set('X-Server-Time', now()->toISOString());
        
        // 添加安全和性能头部
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        
        // 性能建议头部
        if ($responseTime > 1000) {
            $response->headers->set('X-Performance-Warning', 'slow-response');
        }

        return $response;
    }
}