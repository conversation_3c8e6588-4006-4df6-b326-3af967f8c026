#!/usr/bin/env node

const net = require('net');
const { exec } = require('child_process');

// 要检查的端口列表
const portsToCheck = [3000, 3001, 5173, 8000, 8080];

console.log('🔍 检查端口占用情况...\n');

// 检查端口是否被占用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve({ port, available: true });
      });
      server.close();
    });
    
    server.on('error', () => {
      resolve({ port, available: false });
    });
  });
}

// 获取占用端口的进程信息
function getPortProcess(port) {
  return new Promise((resolve) => {
    // Windows
    if (process.platform === 'win32') {
      exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve(null);
          return;
        }
        
        const lines = stdout.trim().split('\n');
        const line = lines.find(l => l.includes(`0.0.0.0:${port}`) || l.includes(`127.0.0.1:${port}`));
        
        if (line) {
          const pid = line.trim().split(/\s+/).pop();
          exec(`tasklist /FI "PID eq ${pid}" /FO CSV`, (error, stdout) => {
            if (!error && stdout) {
              const lines = stdout.trim().split('\n');
              if (lines.length > 1) {
                const processInfo = lines[1].split(',')[0].replace(/"/g, '');
                resolve(`PID: ${pid}, 进程: ${processInfo}`);
              } else {
                resolve(`PID: ${pid}`);
              }
            } else {
              resolve(`PID: ${pid}`);
            }
          });
        } else {
          resolve(null);
        }
      });
    } else {
      // macOS/Linux
      exec(`lsof -ti:${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve(null);
          return;
        }
        
        const pid = stdout.trim();
        exec(`ps -p ${pid} -o comm=`, (error, stdout) => {
          if (!error && stdout) {
            resolve(`PID: ${pid}, 进程: ${stdout.trim()}`);
          } else {
            resolve(`PID: ${pid}`);
          }
        });
      });
    }
  });
}

// 主检查函数
async function checkAllPorts() {
  console.log('端口检查结果:');
  console.log('=====================================');
  
  for (const port of portsToCheck) {
    const result = await checkPort(port);
    
    if (result.available) {
      console.log(`✅ 端口 ${port}: 可用`);
    } else {
      console.log(`❌ 端口 ${port}: 被占用`);
      
      const processInfo = await getPortProcess(port);
      if (processInfo) {
        console.log(`   占用进程: ${processInfo}`);
      }
    }
  }
  
  console.log('=====================================\n');
  
  // 推荐可用端口
  const availablePorts = [];
  for (const port of portsToCheck) {
    const result = await checkPort(port);
    if (result.available) {
      availablePorts.push(port);
    }
  }
  
  if (availablePorts.length > 0) {
    console.log('💡 建议使用的可用端口:');
    availablePorts.forEach(port => {
      console.log(`   - ${port}`);
    });
  } else {
    console.log('⚠️  常用端口都被占用，建议使用其他端口');
  }
  
  console.log('\n🔧 解决方案:');
  console.log('1. 如果端口3001被占用，可以修改 vite.config.js 中的端口设置');
  console.log('2. 或者停止占用端口的进程');
  console.log('3. 使用 npm run dev -- --port 3000 指定其他端口');
}

// 运行检查
checkAllPorts().catch(console.error);