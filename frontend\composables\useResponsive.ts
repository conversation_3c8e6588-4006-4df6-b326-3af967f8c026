import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式断点定义
export const BREAKPOINTS = {
  xs: 0,      // 手机竖屏
  sm: 576,    // 手机横屏
  md: 768,    // 平板竖屏
  lg: 992,    // 平板横屏/小屏笔记本
  xl: 1200,   // 桌面显示器
  xxl: 1400,  // 大屏显示器
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// 响应式组合式函数
export function useResponsive() {
  const width = ref(typeof window !== 'undefined' ? window.innerWidth : 0)
  const height = ref(typeof window !== 'undefined' ? window.innerHeight : 0)
  const isHydrated = ref(false)

  // 当前断点计算
  const currentBreakpoint = computed<Breakpoint>(() => {
    if (!isHydrated.value) return 'lg' // SSR 默认
    
    const w = width.value
    if (w >= BREAKPOINTS.xxl) return 'xxl'
    if (w >= BREAKPOINTS.xl) return 'xl'
    if (w >= BREAKPOINTS.lg) return 'lg'
    if (w >= BREAKPOINTS.md) return 'md'
    if (w >= BREAKPOINTS.sm) return 'sm'
    return 'xs'
  })

  // 断点判断工具
  const isMobile = computed(() => ['xs', 'sm'].includes(currentBreakpoint.value))
  const isTablet = computed(() => currentBreakpoint.value === 'md')
  const isDesktop = computed(() => ['lg', 'xl', 'xxl'].includes(currentBreakpoint.value))
  const isTouch = computed(() => 'ontouchstart' in window)

  // 响应式工具类
  const responsive = computed(() => ({
    isXs: currentBreakpoint.value === 'xs',
    isSm: currentBreakpoint.value === 'sm',
    isMd: currentBreakpoint.value === 'md',
    isLg: currentBreakpoint.value === 'lg',
    isXl: currentBreakpoint.value === 'xl',
    isXxl: currentBreakpoint.value === 'xxl',
    isMobile: isMobile.value,
    isTablet: isTablet.value,
    isDesktop: isDesktop.value,
    isTouch: isTouch.value,
  }))

  // 响应式间距
  const spacing = computed(() => {
    const spacings = {
      xs: { xs: 8, sm: 12, md: 16, lg: 20, xl: 24 },
      sm: { xs: 10, sm: 14, md: 18, lg: 22, xl: 26 },
      md: { xs: 12, sm: 16, md: 20, lg: 24, xl: 28 },
      lg: { xs: 14, sm: 18, md: 22, lg: 26, xl: 30 },
      xl: { xs: 16, sm: 20, md: 24, lg: 28, xl: 32 },
      xxl: { xs: 18, sm: 22, md: 26, lg: 30, xl: 36 },
    }
    return spacings[currentBreakpoint.value]
  })

  // 响应式字体大小
  const fontSize = computed(() => {
    const sizes = {
      xs: { xs: 12, sm: 14, md: 16, lg: 18, xl: 20 },
      sm: { xs: 12, sm: 14, md: 16, lg: 18, xl: 20 },
      md: { xs: 13, sm: 15, md: 17, lg: 19, xl: 21 },
      lg: { xs: 14, sm: 16, md: 18, lg: 20, xl: 22 },
      xl: { xs: 14, sm: 16, md: 18, lg: 20, xl: 22 },
      xxl: { xs: 15, sm: 17, md: 19, lg: 21, xl: 23 },
    }
    return sizes[currentBreakpoint.value]
  })

  // 窗口大小变化监听
  const updateSize = () => {
    if (typeof window !== 'undefined') {
      width.value = window.innerWidth
      height.value = window.innerHeight
    }
  }

  onMounted(() => {
    isHydrated.value = true
    updateSize()
    window.addEventListener('resize', updateSize)
    window.addEventListener('orientationchange', updateSize)
  })

  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', updateSize)
      window.removeEventListener('orientationchange', updateSize)
    }
  })

  return {
    width,
    height,
    currentBreakpoint,
    responsive,
    spacing,
    fontSize,
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
    BREAKPOINTS,
  }
}