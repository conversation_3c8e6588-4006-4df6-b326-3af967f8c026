import request from '@/utils/request'

// ========== 增强的社群管理API ==========

// 批量操作群组成员
export function batchManageMembers(groupId, action, memberIds, data = {}) {
  return request({
    url: `/admin/groups/${groupId}/members/batch`,
    method: 'post',
    data: {
      action, // 'add', 'remove', 'mute', 'unmute', 'promote', 'demote'
      member_ids: memberIds,
      ...data
    }
  })
}

// 获取群组活跃度分析
export function getGroupActivityAnalysis(groupId, timeRange = '7d') {
  return request({
    url: `/admin/groups/${groupId}/activity-analysis`,
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 获取群组收益分析
export function getGroupRevenueAnalysis(groupId, timeRange = '30d') {
  return request({
    url: `/admin/groups/${groupId}/revenue-analysis`,
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 获取群组成员增长趋势
export function getGroupMemberGrowth(groupId, timeRange = '30d') {
  return request({
    url: `/admin/groups/${groupId}/member-growth`,
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 智能推荐群组设置
export function getGroupRecommendations(groupId) {
  return request({
    url: `/admin/groups/${groupId}/recommendations`,
    method: 'get'
  })
}

// 群组健康度检测
export function checkGroupHealth(groupId) {
  return request({
    url: `/admin/groups/${groupId}/health-check`,
    method: 'post'
  })
}

// 自动化群组管理规则
export function getAutomationRules(groupId) {
  return request({
    url: `/admin/groups/${groupId}/automation-rules`,
    method: 'get'
  })
}

export function createAutomationRule(groupId, ruleData) {
  return request({
    url: `/admin/groups/${groupId}/automation-rules`,
    method: 'post',
    data: ruleData
  })
}

export function updateAutomationRule(groupId, ruleId, ruleData) {
  return request({
    url: `/admin/groups/${groupId}/automation-rules/${ruleId}`,
    method: 'put',
    data: ruleData
  })
}

export function deleteAutomationRule(groupId, ruleId) {
  return request({
    url: `/admin/groups/${groupId}/automation-rules/${ruleId}`,
    method: 'delete'
  })
}

// ========== 内容审核增强API ==========

// 智能内容审核
export function intelligentContentReview(contentId) {
  return request({
    url: `/admin/content/${contentId}/intelligent-review`,
    method: 'post'
  })
}

// 批量内容审核
export function batchReviewContent(contentIds, action, reason = '') {
  return request({
    url: '/admin/content/batch-review',
    method: 'post',
    data: {
      content_ids: contentIds,
      action, // 'approve', 'reject', 'flag'
      reason
    }
  })
}

// 获取内容审核统计
export function getContentReviewStats(timeRange = '7d') {
  return request({
    url: '/admin/content/review-stats',
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 获取敏感词库
export function getSensitiveWords() {
  return request({
    url: '/admin/content/sensitive-words',
    method: 'get'
  })
}

// 更新敏感词库
export function updateSensitiveWords(words) {
  return request({
    url: '/admin/content/sensitive-words',
    method: 'put',
    data: { words }
  })
}

// 获取审核规则
export function getReviewRules() {
  return request({
    url: '/admin/content/review-rules',
    method: 'get'
  })
}

// 创建审核规则
export function createReviewRule(ruleData) {
  return request({
    url: '/admin/content/review-rules',
    method: 'post',
    data: ruleData
  })
}

// 更新审核规则
export function updateReviewRule(ruleId, ruleData) {
  return request({
    url: `/admin/content/review-rules/${ruleId}`,
    method: 'put',
    data: ruleData
  })
}

// 删除审核规则
export function deleteReviewRule(ruleId) {
  return request({
    url: `/admin/content/review-rules/${ruleId}`,
    method: 'delete'
  })
}

// ========== 模板管理增强API ==========

// 模板版本管理
export function getTemplateVersions(templateId) {
  return request({
    url: `/admin/group-templates/${templateId}/versions`,
    method: 'get'
  })
}

export function createTemplateVersion(templateId, versionData) {
  return request({
    url: `/admin/group-templates/${templateId}/versions`,
    method: 'post',
    data: versionData
  })
}

export function restoreTemplateVersion(templateId, versionId) {
  return request({
    url: `/admin/group-templates/${templateId}/versions/${versionId}/restore`,
    method: 'post'
  })
}

// 模板使用统计
export function getTemplateUsageStats(templateId, timeRange = '30d') {
  return request({
    url: `/admin/group-templates/${templateId}/usage-stats`,
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 模板效果分析
export function getTemplateEffectAnalysis(templateId) {
  return request({
    url: `/admin/group-templates/${templateId}/effect-analysis`,
    method: 'get'
  })
}

// 模板推荐
export function getRecommendedTemplates(category = '', limit = 10) {
  return request({
    url: '/admin/group-templates/recommendations',
    method: 'get',
    params: { category, limit }
  })
}

// 模板预览生成
export function generateTemplatePreview(templateId, previewData = {}) {
  return request({
    url: `/admin/group-templates/${templateId}/preview`,
    method: 'post',
    data: previewData
  })
}

// ========== 数据分析和报表API ==========

// 获取社群总览数据
export function getCommunityOverview(timeRange = '30d') {
  return request({
    url: '/admin/community/overview',
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 获取群组排行榜
export function getGroupRanking(type = 'revenue', limit = 20) {
  return request({
    url: '/admin/community/group-ranking',
    method: 'get',
    params: { type, limit } // type: 'revenue', 'members', 'activity', 'growth'
  })
}

// 获取用户行为分析
export function getUserBehaviorAnalysis(timeRange = '30d') {
  return request({
    url: '/admin/community/user-behavior',
    method: 'get',
    params: { time_range: timeRange }
  })
}

// 生成社群报表
export function generateCommunityReport(reportType, timeRange, options = {}) {
  return request({
    url: '/admin/community/reports/generate',
    method: 'post',
    data: {
      report_type: reportType, // 'overview', 'revenue', 'user_activity', 'content_analysis'
      time_range: timeRange,
      ...options
    }
  })
}

// 获取报表列表
export function getReportList(params = {}) {
  return request({
    url: '/admin/community/reports',
    method: 'get',
    params
  })
}

// 下载报表
export function downloadReport(reportId) {
  return request({
    url: `/admin/community/reports/${reportId}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

// ========== 实时监控API ==========

// 获取实时群组状态
export function getRealTimeGroupStatus() {
  return request({
    url: '/admin/community/real-time/groups',
    method: 'get'
  })
}

// 获取实时用户活动
export function getRealTimeUserActivity() {
  return request({
    url: '/admin/community/real-time/user-activity',
    method: 'get'
  })
}

// 获取实时收益数据
export function getRealTimeRevenue() {
  return request({
    url: '/admin/community/real-time/revenue',
    method: 'get'
  })
}

// 获取系统告警
export function getSystemAlerts() {
  return request({
    url: '/admin/community/alerts',
    method: 'get'
  })
}

// 处理系统告警
export function handleSystemAlert(alertId, action) {
  return request({
    url: `/admin/community/alerts/${alertId}`,
    method: 'put',
    data: { action } // 'acknowledge', 'resolve', 'ignore'
  })
}