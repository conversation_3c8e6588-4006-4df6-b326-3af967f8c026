<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源加载检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .check-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            background: #f6ffed;
            border-left-color: #52c41a;
        }
        .check-item.error {
            background: #fff2f0;
            border-left-color: #ff4d4f;
        }
        .check-item.loading {
            background: #e6f7ff;
            border-left-color: #1890ff;
        }
        .status-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        .check-details {
            flex: 1;
        }
        .check-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .check-url {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .summary {
            margin-top: 30px;
            padding: 20px;
            background: #fafafa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 资源加载检查</h1>
            <p>检查所有可能导致404错误的资源</p>
            <button class="btn" onclick="runAllChecks()">开始检查</button>
            <button class="btn" onclick="clearResults()">清除结果</button>
        </div>

        <div id="checkResults"></div>
        
        <div id="summary" class="summary" style="display: none;">
            <h3>检查摘要</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        const checks = [
            {
                name: 'Favicon 图标',
                url: '/favicon.ico',
                type: 'image'
            },
            {
                name: '主应用入口',
                url: '/',
                type: 'html'
            },
            {
                name: 'Vue 主文件',
                url: '/src/main.js',
                type: 'script'
            },
            {
                name: '测试预览页面',
                url: '/test-preview.html',
                type: 'html'
            },
            {
                name: '简化预览页面',
                url: '/preview-simple.html',
                type: 'html'
            },
            {
                name: 'API 健康检查',
                url: '/api/health',
                type: 'api'
            },
            {
                name: 'API v1 基础路径',
                url: '/api/v1/',
                type: 'api'
            },
            {
                name: 'Vite 开发服务器',
                url: '/@vite/client',
                type: 'script'
            }
        ];

        async function checkResource(check) {
            const resultDiv = document.getElementById(`check-${check.name.replace(/\s+/g, '-')}`);
            
            // 设置加载状态
            resultDiv.className = 'check-item loading';
            resultDiv.querySelector('.status-icon').textContent = '⏳';
            
            try {
                const response = await fetch(check.url, {
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    resultDiv.className = 'check-item success';
                    resultDiv.querySelector('.status-icon').textContent = '✅';
                    return { success: true, status: response.status };
                } else {
                    resultDiv.className = 'check-item error';
                    resultDiv.querySelector('.status-icon').textContent = '❌';
                    return { success: false, status: response.status };
                }
            } catch (error) {
                resultDiv.className = 'check-item error';
                resultDiv.querySelector('.status-icon').textContent = '❌';
                return { success: false, error: error.message };
            }
        }

        function createCheckItem(check) {
            return `
                <div id="check-${check.name.replace(/\s+/g, '-')}" class="check-item">
                    <div class="status-icon">⏳</div>
                    <div class="check-details">
                        <div class="check-name">${check.name}</div>
                        <div class="check-url">${check.url}</div>
                    </div>
                </div>
            `;
        }

        async function runAllChecks() {
            const resultsDiv = document.getElementById('checkResults');
            const summaryDiv = document.getElementById('summary');
            
            // 创建检查项目
            resultsDiv.innerHTML = checks.map(createCheckItem).join('');
            
            // 运行所有检查
            const results = [];
            for (const check of checks) {
                const result = await checkResource(check);
                results.push({ check, result });
                
                // 添加延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            // 显示摘要
            const successCount = results.filter(r => r.result.success).length;
            const totalCount = results.length;
            
            document.getElementById('summaryContent').innerHTML = `
                <p><strong>检查完成:</strong> ${successCount}/${totalCount} 项通过</p>
                <p><strong>成功率:</strong> ${Math.round(successCount/totalCount*100)}%</p>
                ${successCount < totalCount ? '<p style="color: #ff4d4f;"><strong>发现问题:</strong> 部分资源无法访问</p>' : '<p style="color: #52c41a;"><strong>状态:</strong> 所有资源正常</p>'}
                
                <h4>失败的资源:</h4>
                <ul>
                    ${results.filter(r => !r.result.success).map(r => 
                        `<li>${r.check.name} (${r.check.url}) - ${r.result.status || r.result.error}</li>`
                    ).join('') || '<li>无</li>'}
                </ul>
                
                <h4>建议解决方案:</h4>
                <ul>
                    ${getRecommendations(results)}
                </ul>
            `;
            
            summaryDiv.style.display = 'block';
        }

        function getRecommendations(results) {
            const recommendations = [];
            
            const failedChecks = results.filter(r => !r.result.success);
            
            if (failedChecks.some(r => r.check.url === '/favicon.ico')) {
                recommendations.push('<li>检查 public/favicon.ico 文件是否存在</li>');
            }
            
            if (failedChecks.some(r => r.check.url.startsWith('/api'))) {
                recommendations.push('<li>后端API服务可能未启动，这是正常的（使用Mock数据）</li>');
            }
            
            if (failedChecks.some(r => r.check.url === '/src/main.js')) {
                recommendations.push('<li>检查Vite开发服务器是否正常运行</li>');
            }
            
            if (failedChecks.some(r => r.check.type === 'html')) {
                recommendations.push('<li>检查HTML文件是否在public目录中</li>');
            }
            
            if (recommendations.length === 0) {
                recommendations.push('<li>所有资源正常，无需额外操作</li>');
            }
            
            return recommendations.join('');
        }

        function clearResults() {
            document.getElementById('checkResults').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
        }

        // 页面加载时显示当前信息
        document.addEventListener('DOMContentLoaded', function() {
            const info = `
                <div style="background: #e6f7ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                    <h4>当前环境信息</h4>
                    <p><strong>访问地址:</strong> ${window.location.href}</p>
                    <p><strong>端口:</strong> ${window.location.port || '80'}</p>
                    <p><strong>协议:</strong> ${window.location.protocol}</p>
                    <p><strong>主机:</strong> ${window.location.hostname}</p>
                    <p><strong>时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
                </div>
            `;
            
            document.querySelector('.header').insertAdjacentHTML('afterend', info);
        });
    </script>
</body>
</html>