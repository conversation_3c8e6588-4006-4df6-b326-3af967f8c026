/**
 * 类型检查工具
 * 提供运行时类型检查和验证功能
 */

/**
 * 基础类型检查
 */
export const is = {
  string: (value) => typeof value === 'string',
  number: (value) => typeof value === 'number' && !isNaN(value),
  boolean: (value) => typeof value === 'boolean',
  function: (value) => typeof value === 'function',
  object: (value) => value !== null && typeof value === 'object' && !Array.isArray(value),
  array: (value) => Array.isArray(value),
  null: (value) => value === null,
  undefined: (value) => value === undefined,
  empty: (value) => {
    if (value === null || value === undefined) return true
    if (typeof value === 'string') return value.trim() === ''
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'object') return Object.keys(value).length === 0
    return false
  },
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return is.string(value) && emailRegex.test(value)
  },
  url: (value) => {
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },
  phone: (value) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return is.string(value) && phoneRegex.test(value)
  }
}

/**
 * 类型断言
 */
export class TypeAssertion {
  constructor(value, name = 'value') {
    this.value = value
    this.name = name
  }

  string(message) {
    if (!is.string(this.value)) {
      throw new TypeError(message || `${this.name} must be a string`)
    }
    return this
  }

  number(message) {
    if (!is.number(this.value)) {
      throw new TypeError(message || `${this.name} must be a number`)
    }
    return this
  }

  boolean(message) {
    if (!is.boolean(this.value)) {
      throw new TypeError(message || `${this.name} must be a boolean`)
    }
    return this
  }

  object(message) {
    if (!is.object(this.value)) {
      throw new TypeError(message || `${this.name} must be an object`)
    }
    return this
  }

  array(message) {
    if (!is.array(this.value)) {
      throw new TypeError(message || `${this.name} must be an array`)
    }
    return this
  }

  notEmpty(message) {
    if (is.empty(this.value)) {
      throw new TypeError(message || `${this.name} cannot be empty`)
    }
    return this
  }

  email(message) {
    if (!is.email(this.value)) {
      throw new TypeError(message || `${this.name} must be a valid email`)
    }
    return this
  }

  url(message) {
    if (!is.url(this.value)) {
      throw new TypeError(message || `${this.name} must be a valid URL`)
    }
    return this
  }

  phone(message) {
    if (!is.phone(this.value)) {
      throw new TypeError(message || `${this.name} must be a valid phone number`)
    }
    return this
  }

  oneOf(values, message) {
    if (!values.includes(this.value)) {
      throw new TypeError(message || `${this.name} must be one of: ${values.join(', ')}`)
    }
    return this
  }

  min(minValue, message) {
    if (is.number(this.value) && this.value < minValue) {
      throw new TypeError(message || `${this.name} must be at least ${minValue}`)
    }
    if (is.string(this.value) && this.value.length < minValue) {
      throw new TypeError(message || `${this.name} must be at least ${minValue} characters`)
    }
    if (is.array(this.value) && this.value.length < minValue) {
      throw new TypeError(message || `${this.name} must have at least ${minValue} items`)
    }
    return this
  }

  max(maxValue, message) {
    if (is.number(this.value) && this.value > maxValue) {
      throw new TypeError(message || `${this.name} must be at most ${maxValue}`)
    }
    if (is.string(this.value) && this.value.length > maxValue) {
      throw new TypeError(message || `${this.name} must be at most ${maxValue} characters`)
    }
    if (is.array(this.value) && this.value.length > maxValue) {
      throw new TypeError(message || `${this.name} must have at most ${maxValue} items`)
    }
    return this
  }

  pattern(regex, message) {
    if (is.string(this.value) && !regex.test(this.value)) {
      throw new TypeError(message || `${this.name} does not match the required pattern`)
    }
    return this
  }
}

/**
 * 创建类型断言
 */
export function assert(value, name) {
  return new TypeAssertion(value, name)
}

/**
 * 对象结构验证
 */
export function validateSchema(obj, schema) {
  const errors = []

  function validateProperty(value, rules, path) {
    for (const rule of rules) {
      try {
        if (typeof rule === 'function') {
          rule(value)
        } else if (typeof rule === 'object') {
          const { type, required, validator, message } = rule
          
          if (required && is.empty(value)) {
            throw new Error(message || `${path} is required`)
          }
          
          if (!is.empty(value) && type) {
            if (!is[type](value)) {
              throw new Error(message || `${path} must be of type ${type}`)
            }
          }
          
          if (!is.empty(value) && validator) {
            validator(value)
          }
        }
      } catch (error) {
        errors.push({
          path,
          message: error.message,
          value
        })
      }
    }
  }

  function traverse(obj, schema, currentPath = '') {
    for (const [key, rules] of Object.entries(schema)) {
      const path = currentPath ? `${currentPath}.${key}` : key
      const value = obj[key]
      
      if (Array.isArray(rules)) {
        validateProperty(value, rules, path)
      } else if (typeof rules === 'object' && !Array.isArray(rules)) {
        if (is.object(value)) {
          traverse(value, rules, path)
        } else {
          errors.push({
            path,
            message: `${path} must be an object`,
            value
          })
        }
      }
    }
  }

  traverse(obj, schema)

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * API参数验证装饰器
 */
export function validateParams(schema) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      const [params] = args
      const validation = validateSchema(params, schema)
      
      if (!validation.valid) {
        const errorMessage = validation.errors.map(e => e.message).join('; ')
        throw new Error(`Parameter validation failed: ${errorMessage}`)
      }
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 常用验证规则
 */
export const rules = {
  required: { required: true },
  string: { type: 'string' },
  number: { type: 'number' },
  boolean: { type: 'boolean' },
  array: { type: 'array' },
  object: { type: 'object' },
  email: { 
    type: 'string',
    validator: (value) => assert(value, 'email').email()
  },
  url: {
    type: 'string', 
    validator: (value) => assert(value, 'url').url()
  },
  phone: {
    type: 'string',
    validator: (value) => assert(value, 'phone').phone()
  },
  minLength: (min) => ({
    type: 'string',
    validator: (value) => assert(value, 'value').min(min)
  }),
  maxLength: (max) => ({
    type: 'string',
    validator: (value) => assert(value, 'value').max(max)
  }),
  range: (min, max) => ({
    type: 'number',
    validator: (value) => assert(value, 'value').min(min).max(max)
  }),
  oneOf: (values) => ({
    validator: (value) => assert(value, 'value').oneOf(values)
  })
}

/**
 * 表单验证助手
 */
export function createFormValidator(schema) {
  return {
    validate(formData) {
      return validateSchema(formData, schema)
    },
    
    validateField(fieldName, value) {
      if (!schema[fieldName]) {
        return { valid: true, errors: [] }
      }
      
      return validateSchema({ [fieldName]: value }, { [fieldName]: schema[fieldName] })
    },
    
    getFieldRules(fieldName) {
      return schema[fieldName] || []
    }
  }
}

/**
 * 安全的数据迭代工具
 */
export const safeIterate = {
  /**
   * 安全的数组迭代
   * @param {any} data - 要迭代的数据
   * @param {Function} callback - 回调函数
   * @param {any} defaultValue - 默认值
   * @returns {Array} 处理后的数组
   */
  array: (data, callback, defaultValue = []) => {
    if (!is.array(data)) {
      console.warn('safeIterate.array: 期望数组但收到:', typeof data, data)
      return defaultValue
    }
    try {
      return data.map(callback)
    } catch (error) {
      console.error('safeIterate.array: 迭代错误:', error)
      return defaultValue
    }
  },

  /**
   * 安全的对象迭代
   * @param {any} data - 要迭代的数据
   * @param {Function} callback - 回调函数
   * @param {any} defaultValue - 默认值
   * @returns {Array} 处理后的数组
   */
  object: (data, callback, defaultValue = []) => {
    if (!is.object(data)) {
      console.warn('safeIterate.object: 期望对象但收到:', typeof data, data)
      return defaultValue
    }
    try {
      return Object.entries(data).map(callback)
    } catch (error) {
      console.error('safeIterate.object: 迭代错误:', error)
      return defaultValue
    }
  },

  /**
   * 确保数据是数组
   * @param {any} data - 要检查的数据
   * @param {Array} defaultValue - 默认值
   * @returns {Array} 安全的数组
   */
  ensureArray: (data, defaultValue = []) => {
    if (is.array(data)) return data
    if (data === null || data === undefined) return defaultValue
    console.warn('ensureArray: 期望数组但收到:', typeof data, data)
    return defaultValue
  }
}
