import{n as t}from"./index-DtXAftX0.js";const a={getMy:()=>t({url:"/admin/agent/my",method:"get"}),getMyStats:()=>t({url:"/api/v1/admin/agent/my-stats",method:"get"}),getList:a=>t({url:"/admin/agent/list",method:"get",params:a}),getDetail:a=>t({url:`/admin/agent/${a}`,method:"get"}),create:a=>t({url:"/admin/agent",method:"post",data:a}),update:(a,e)=>t({url:`/admin/agent/${a}`,method:"put",data:e}),delete:a=>t({url:`/admin/agent/${a}`,method:"delete"}),getTeamData:()=>t({url:"/admin/agent/team",method:"get"}),getCommissionData:a=>t({url:"/admin/agent/commission",method:"get",params:a})},e={getStats:()=>t({url:"/admin/agent-application/stats",method:"get"}),getList:a=>t({url:"/admin/agent-application/list",method:"get",params:a}),getDetail:a=>t({url:`/admin/agent-application/${a}`,method:"get"}),review:(a,e)=>t({url:`/admin/agent-application/${a}/review`,method:"post",data:e}),batchReview:a=>t({url:"/admin/agent-application/batch-review",method:"post",data:a})};export{a,e as b};
