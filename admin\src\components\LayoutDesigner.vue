<template>
  <div class="layout-designer">
    <!-- 设计器头部 -->
    <div class="designer-header">
      <div class="header-title">
        <el-icon><Grid /></el-icon>
        <span>落地页布局设计</span>
      </div>
      <div class="header-actions">
        <el-button @click="resetLayout" size="small" type="warning" plain>
          <el-icon><RefreshRight /></el-icon>
          重置布局
        </el-button>
        <el-button @click="previewLayout" size="small" type="primary" plain>
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
      </div>
    </div>

    <!-- 布局区域 -->
    <div class="layout-area">
      <!-- 左侧组件库 -->
      <div class="component-library">
        <div class="library-title">可用组件</div>
        <div class="component-list">
          <div
            v-for="component in availableComponents"
            :key="component.id"
            class="component-item"
            :class="{ disabled: !component.available }"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-icon">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <div class="component-info">
              <div class="component-name">{{ component.name }}</div>
              <div class="component-desc">{{ component.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间设计区域 -->
      <div class="design-area">
        <div class="design-canvas">
          <div class="canvas-header">
            <span class="canvas-title">落地页预览</span>
            <div class="canvas-tools">
              <el-button-group size="small">
                <el-button :type="viewMode === 'mobile' ? 'primary' : ''" @click="viewMode = 'mobile'">
                  <el-icon><Iphone /></el-icon>
                </el-button>
                <el-button :type="viewMode === 'tablet' ? 'primary' : ''" @click="viewMode = 'tablet'">
                  <el-icon><Monitor /></el-icon>
                </el-button>
                <el-button :type="viewMode === 'desktop' ? 'primary' : ''" @click="viewMode = 'desktop'">
                  <el-icon><Monitor /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <div 
            class="canvas-content"
            :class="`view-${viewMode}`"
            @drop="handleDrop"
            @dragover="handleDragOver"
          >
            <Sortable
              v-model="layoutSections"
              :options="{
                group: 'layout',
                animation: 200,
                ghostClass: 'ghost'
              }"
              class="section-list"
              @change="handleSectionChange"
            >
              <template #item="{ element: section, index }">
                <div
                  class="layout-section"
                  :class="{ 
                    active: activeSection === section.id,
                    hidden: !section.visible 
                  }"
                  @click="selectSection(section.id)"
                >
                  <div class="section-header">
                    <div class="section-info">
                      <el-icon><component :is="getSectionIcon(section.type)" /></el-icon>
                      <span class="section-name">{{ section.name }}</span>
                    </div>
                    <div class="section-controls">
                      <el-switch
                        v-model="section.visible"
                        size="small"
                        @change="updateSection(section.id, { visible: section.visible })"
                      />
                      <el-button
                        @click.stop="removeSection(section.id)"
                        size="small"
                        type="danger"
                        text
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  
                  <div class="section-content">
                    <component
                      :is="getSectionComponent(section.type)"
                      :section="section"
                      :preview="true"
                      @update="updateSection(section.id, $event)"
                    />
                  </div>
                </div>
              </template>
            </Sortable>
            
            <!-- 空状态 -->
            <div v-if="layoutSections.length === 0" class="empty-canvas">
              <el-icon class="empty-icon"><Grid /></el-icon>
              <div class="empty-text">从左侧拖拽组件到此处开始设计</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <div class="panel-title">属性设置</div>
        
        <div v-if="activeSection" class="property-content">
          <el-form :model="activeSectionData" label-width="80px" size="small">
            <!-- 基础属性 -->
            <el-form-item label="组件名称">
              <el-input
                v-model="activeSectionData.name"
                @input="updateActiveSection({ name: activeSectionData.name })"
              />
            </el-form-item>
            
            <el-form-item label="显示状态">
              <el-switch
                v-model="activeSectionData.visible"
                @change="updateActiveSection({ visible: activeSectionData.visible })"
              />
            </el-form-item>
            
            <!-- 样式属性 -->
            <el-divider content-position="left">样式设置</el-divider>
            
            <el-form-item label="背景色">
              <el-color-picker
                v-model="activeSectionData.style.backgroundColor"
                @change="updateSectionStyle('backgroundColor', activeSectionData.style.backgroundColor)"
              />
            </el-form-item>
            
            <el-form-item label="内边距">
              <el-input-number
                v-model="activeSectionData.style.padding"
                :min="0"
                :max="100"
                @change="updateSectionStyle('padding', activeSectionData.style.padding)"
              />
            </el-form-item>
            
            <el-form-item label="外边距">
              <el-input-number
                v-model="activeSectionData.style.margin"
                :min="0"
                :max="100"
                @change="updateSectionStyle('margin', activeSectionData.style.margin)"
              />
            </el-form-item>
            
            <!-- 组件特定属性 -->
            <component
              :is="getSectionPropertyComponent(activeSectionData.type)"
              :section="activeSectionData"
              @update="updateActiveSection"
            />
          </el-form>
        </div>
        
        <div v-else class="no-selection">
          <el-icon class="no-selection-icon"><InfoFilled /></el-icon>
          <div class="no-selection-text">请选择一个组件进行编辑</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Grid, RefreshRight, View, Delete, InfoFilled,
  Picture, Document, VideoPlay, User, Iphone, Monitor
} from '@element-plus/icons-vue'
import { Sortable } from 'sortablejs-vue3'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      sections: []
    })
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const viewMode = ref('mobile')
const activeSection = ref(null)
const layoutSections = ref([])

// 可用组件库
const availableComponents = ref([
  {
    id: 'banner',
    name: '顶部横幅',
    description: '展示主要信息的横幅区域',
    icon: 'Picture',
    type: 'banner',
    available: true
  },
  {
    id: 'info',
    name: '基础信息',
    description: '群组名称、价格等基础信息',
    icon: 'Document',
    type: 'info',
    available: true
  },
  {
    id: 'content',
    name: '详细介绍',
    description: '富文本内容展示区域',
    icon: 'Document',
    type: 'content',
    available: true
  },
  {
    id: 'gallery',
    name: '图片展示',
    description: '多图片轮播展示',
    icon: 'Picture',
    type: 'gallery',
    available: true
  },
  {
    id: 'video',
    name: '视频播放',
    description: '视频内容播放区域',
    icon: 'VideoPlay',
    type: 'video',
    available: true
  },
  {
    id: 'members',
    name: '成员展示',
    description: '虚拟成员头像展示',
    icon: 'User',
    type: 'members',
    available: true
  }
])

// 当前选中组件的数据
const activeSectionData = computed(() => {
  if (!activeSection.value) return null
  return layoutSections.value.find(section => section.id === activeSection.value)
})

// 方法
const handleDragStart = (event, component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))
  addSection(componentData)
}

const addSection = (componentData) => {
  const newSection = {
    id: `${componentData.type}_${Date.now()}`,
    type: componentData.type,
    name: componentData.name,
    visible: true,
    order: layoutSections.value.length + 1,
    style: {
      backgroundColor: '#ffffff',
      padding: 20,
      margin: 0
    },
    config: getDefaultConfig(componentData.type)
  }
  
  layoutSections.value.push(newSection)
  selectSection(newSection.id)
  updateModelValue()
}

const getDefaultConfig = (type) => {
  const configs = {
    banner: { height: 200, showOverlay: true },
    info: { showPrice: true, showDescription: true },
    content: { maxHeight: 300 },
    gallery: { columns: 3, showThumbnails: true },
    video: { autoplay: false, controls: true },
    members: { maxCount: 12, showCount: true }
  }
  return configs[type] || {}
}

const selectSection = (sectionId) => {
  activeSection.value = sectionId
}

const updateSection = (sectionId, updates) => {
  const section = layoutSections.value.find(s => s.id === sectionId)
  if (section) {
    Object.assign(section, updates)
    updateModelValue()
  }
}

const updateActiveSection = (updates) => {
  if (activeSection.value) {
    updateSection(activeSection.value, updates)
  }
}

const updateSectionStyle = (property, value) => {
  if (activeSection.value) {
    const section = layoutSections.value.find(s => s.id === activeSection.value)
    if (section) {
      section.style[property] = value
      updateModelValue()
    }
  }
}

const removeSection = (sectionId) => {
  const index = layoutSections.value.findIndex(s => s.id === sectionId)
  if (index > -1) {
    layoutSections.value.splice(index, 1)
    if (activeSection.value === sectionId) {
      activeSection.value = null
    }
    updateModelValue()
  }
}

const handleSectionChange = () => {
  updateModelValue()
}

const resetLayout = () => {
  layoutSections.value = []
  activeSection.value = null
  updateModelValue()
  ElMessage.success('布局已重置')
}

const previewLayout = () => {
  emit('preview', {
    sections: layoutSections.value,
    viewMode: viewMode.value
  })
}

const getSectionIcon = (type) => {
  const icons = {
    banner: 'Picture',
    info: 'Document',
    content: 'Document',
    gallery: 'Picture',
    video: 'VideoPlay',
    members: 'User'
  }
  return icons[type] || 'Document'
}

const getSectionComponent = (type) => {
  // 返回对应的预览组件
  return `Section${type.charAt(0).toUpperCase() + type.slice(1)}Preview`
}

const getSectionPropertyComponent = (type) => {
  // 返回对应的属性编辑组件
  return `Section${type.charAt(0).toUpperCase() + type.slice(1)}Properties`
}

const updateModelValue = () => {
  const newValue = {
    sections: layoutSections.value,
    viewMode: viewMode.value
  }
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 初始化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.sections) {
    layoutSections.value = [...newValue.sections]
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.layout-designer {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  
  .designer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .layout-area {
    display: flex;
    height: 600px;
    
    .component-library {
      width: 200px;
      border-right: 1px solid #e4e7ed;
      background: #fafafa;
      
      .library-title {
        padding: 12px 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
      }
      
      .component-list {
        padding: 8px;
        
        .component-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          margin-bottom: 4px;
          border-radius: 6px;
          cursor: grab;
          transition: all 0.3s;
          
          &:hover {
            background: #e6f7ff;
          }
          
          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
          
          .component-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #409eff;
          }
          
          .component-info {
            flex: 1;
            
            .component-name {
              font-size: 12px;
              font-weight: 500;
              color: #303133;
            }
            
            .component-desc {
              font-size: 10px;
              color: #909399;
              margin-top: 2px;
            }
          }
        }
      }
    }
    
    .design-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .design-canvas {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .canvas-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e4e7ed;
          background: white;
          
          .canvas-title {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .canvas-content {
          flex: 1;
          padding: 20px;
          background: #f5f7fa;
          overflow-y: auto;
          
          &.view-mobile {
            max-width: 375px;
            margin: 0 auto;
          }
          
          &.view-tablet {
            max-width: 768px;
            margin: 0 auto;
          }
          
          .section-list {
            min-height: 100%;
          }
          
          .layout-section {
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              border-color: #409eff;
            }
            
            &.active {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
            
            &.hidden {
              opacity: 0.5;
            }
            
            .section-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f5f7fa;
              border-bottom: 1px solid #e4e7ed;
              
              .section-info {
                display: flex;
                align-items: center;
                gap: 6px;
                
                .section-name {
                  font-size: 12px;
                  font-weight: 500;
                  color: #303133;
                }
              }
              
              .section-controls {
                display: flex;
                align-items: center;
                gap: 8px;
              }
            }
            
            .section-content {
              padding: 16px;
              min-height: 60px;
            }
          }
          
          .empty-canvas {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            color: #909399;
            
            .empty-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }
            
            .empty-text {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .property-panel {
      width: 280px;
      border-left: 1px solid #e4e7ed;
      background: #fafafa;
      
      .panel-title {
        padding: 12px 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
      }
      
      .property-content {
        padding: 16px;
        overflow-y: auto;
        height: calc(100% - 45px);
      }
      
      .no-selection {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #909399;
        
        .no-selection-icon {
          font-size: 32px;
          margin-bottom: 12px;
        }
        
        .no-selection-text {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
