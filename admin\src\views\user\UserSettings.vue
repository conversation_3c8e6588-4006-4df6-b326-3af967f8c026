<template>
  <div class="user-settings-container">
    <div class="settings-header">
      <div class="header-content">
        <el-avatar 
          :size="80" 
          :src="userStore.userInfo?.avatar || defaultAvatar"
          class="user-avatar"
        />
        <div class="user-info">
          <h1 class="user-name">{{ userStore.userInfo?.nickname || userStore.userInfo?.username || '用户' }}</h1>
          <p class="user-email">{{ userStore.userInfo?.email || '未设置邮箱' }}</p>
          <el-tag type="info" size="small">
            {{ getRoleDisplayName(userStore.userInfo?.role) }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="settings-content">
      <el-tabs 
        v-model="activeTab" 
        class="settings-tabs"
        tab-position="left"
      >
        <!-- 个人资料标签页 -->
        <el-tab-pane label="个人资料" name="profile">
          <ProfileSettings ref="profileRef" />
        </el-tab-pane>

        <!-- 偏好设置标签页 -->
        <el-tab-pane label="偏好设置" name="preferences">
          <PreferenceSettings ref="preferencesRef" />
        </el-tab-pane>

        <!-- 安全设置标签页 -->
        <el-tab-pane label="安全设置" name="security">
          <SecuritySettings ref="securityRef" />
        </el-tab-pane>

        <!-- 隐私设置标签页 -->
        <el-tab-pane label="隐私设置" name="privacy">
          <PrivacySettings ref="privacyRef" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import ProfileSettings from './components/ProfileSettings.vue'
import PreferenceSettings from './components/PreferenceSettings.vue'
import SecuritySettings from './components/SecuritySettings.vue'
import PrivacySettings from './components/PrivacySettings.vue'

const userStore = useUserStore()
const activeTab = ref('profile')

// 引用各个设置组件
const profileRef = ref(null)
const preferencesRef = ref(null)
const securityRef = ref(null)
const privacyRef = ref(null)

const defaultAvatar = '/default-avatar.png'

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  const roleMap = {
    'admin': '管理员',
    'distributor': '分销商',
    'group_owner': '群主',
    'user': '普通用户'
  }
  return roleMap[role] || role || '未知角色'
}

// 页面加载时获取用户信息
onMounted(async () => {
  try {
    if (!userStore.userInfo) {
      await userStore.getUserInfo()
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
    console.error('Failed to load user info:', error)
  }
})
</script>

<style scoped>
.user-settings-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 30px;
  color: white;
  position: relative;
  overflow: hidden;
}

.settings-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
  50% { transform: translate(-20px, -20px) rotate(180deg); }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.user-avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-info h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.user-info p {
  margin: 0 0 12px 0;
  opacity: 0.9;
  font-size: 16px;
}

.settings-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.settings-tabs :deep(.el-tabs__header) {
  margin-right: 0;
  background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
}

.settings-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.settings-tabs :deep(.el-tabs__active-bar) {
  background-color: #667eea;
}

.settings-tabs :deep(.el-tabs__item) {
  padding: 0 30px;
  height: 50px;
  line-height: 50px;
  transition: all 0.3s ease;
}

.settings-tabs :deep(.el-tabs__item:hover) {
  color: #667eea;
  background-color: rgba(102, 126, 234, 0.1);
}

.settings-tabs :deep(.el-tabs__item.is-active) {
  color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .user-settings-container {
    padding: 10px;
  }
  
  .settings-header {
    padding: 30px 20px;
    margin-bottom: 20px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .user-info h1 {
    font-size: 24px;
  }
  
  .settings-tabs {
    height: auto;
  }
  
  .settings-tabs :deep(.el-tabs__header) {
    margin-right: 0;
    margin-bottom: 20px;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .settings-tabs :deep(.el-tabs__nav) {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .settings-tabs :deep(.el-tabs__item) {
    flex-shrink: 0;
    padding: 0 20px;
  }
}

@media screen and (max-width: 480px) {
  .settings-header {
    padding: 20px;
  }
  
  .user-info h1 {
    font-size: 20px;
  }
  
  .user-info p {
    font-size: 14px;
  }
}
</style>