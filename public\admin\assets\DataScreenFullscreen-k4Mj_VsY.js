import{_ as s}from"./index-DtXAftX0.js";import{af as a,k as t,l as e,t as r,E as o,z as n,u as l}from"./vue-vendor-Dy164gUc.js";import{aX as c,T as u}from"./element-plus-h2SQQM64.js";import i from"./DataScreen-DhgDTN83.js";import"./utils-D1VZuEZr.js";import"./LineChart-CydsJ2U8.js";import"./DoughnutChart-CCHHIMjz.js";import"./echarts-D68jitv0.js";const m={class:"fullscreen-data-screen"},p=s({__name:"DataScreenFullscreen",setup(s){const p=a(),d=()=>{p.go(-1)};return(s,a)=>{const p=u;return e(),t("div",m,[r("div",{class:"back-button",onClick:d},[o(p,null,{default:n(()=>[o(l(c))]),_:1}),a[0]||(a[0]=r("span",null,"返回",-1))]),o(i)])}}},[["__scopeId","data-v-1a1a586c"]]);export{p as default};
