/**
 * 权限验证服务
 * 提供统一的权限验证和角色管理功能
 */

import { PERMISSION_CONFIG, ROUTE_CONFIG } from '@/config'
import { hasRoutePermission, canViewUserData, canCreateGroup } from '@/utils/permissionUtils'

class PermissionService {
  constructor() {
    this.permissions = new Map()
    this.roleCache = new Map()
    this.permissionCache = new Map()
    this.init()
  }

  /**
   * 初始化权限服务
   */
  init() {
    // 清理过期缓存
    this.cleanupCache()
    
    // 设置定时清理
    setInterval(() => {
      this.cleanupCache()
    }, PERMISSION_CONFIG.CACHE_TIME)
  }

  /**
   * 获取用户权限
   */
  async getUserPermissions(userRole) {
    if (!userRole) return []
    
    // 检查缓存
    const cacheKey = `permissions_${userRole}`
    const cached = this.permissionCache.get(cacheKey)
    if (cached && cached.expire > Date.now()) {
      return cached.data
    }

    // 根据角色获取权限
    const permissions = this.generateRolePermissions(userRole)
    
    // 缓存权限
    this.permissionCache.set(cacheKey, {
      data: permissions,
      expire: Date.now() + PERMISSION_CONFIG.PERMISSION_EXPIRE
    })

    return permissions
  }

  /**
   * 生成角色权限
   */
  generateRolePermissions(userRole) {
    const roleConfig = PERMISSION_CONFIG.ROLES[userRole]
    if (!roleConfig) return []

    const permissions = []

    // 基础权限
    if (roleConfig.basePermissions) {
      permissions.push(...roleConfig.basePermissions)
    }

    // 菜单权限
    if (roleConfig.allowedMenus) {
      permissions.push(...roleConfig.allowedMenus.map(menu => `menu:${menu}`))
    }

    // 操作权限
    if (roleConfig.allowedOperations) {
      permissions.push(...roleConfig.allowedOperations.map(op => `op:${op}`))
    }

    // 数据权限
    if (roleConfig.dataPermissions) {
      permissions.push(...roleConfig.dataPermissions.map(data => `data:${data}`))
    }

    return permissions
  }

  /**
   * 检查用户是否有特定权限
   */
  async hasPermission(userRole, permission) {
    if (!userRole || !permission) return false
    
    // 超级管理员拥有所有权限
    if (userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE) return true

    const permissions = await this.getUserPermissions(userRole)
    return permissions.includes(permission)
  }

  /**
   * 检查用户是否有多个权限（全部匹配）
   */
  async hasAllPermissions(userRole, permissions) {
    if (!userRole || !Array.isArray(permissions)) return false
    
    // 超级管理员拥有所有权限
    if (userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE) return true

    const userPermissions = await this.getUserPermissions(userRole)
    return permissions.every(permission => userPermissions.includes(permission))
  }

  /**
   * 检查用户是否有任意权限（部分匹配）
   */
  async hasAnyPermission(userRole, permissions) {
    if (!userRole || !Array.isArray(permissions)) return false
    
    // 超级管理员拥有所有权限
    if (userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE) return true

    const userPermissions = await this.getUserPermissions(userRole)
    return permissions.some(permission => userPermissions.includes(permission))
  }

  /**
   * 检查路由权限
   */
  async hasRouteAccess(userRole, routePath) {
    if (!userRole || !routePath) return false
    
    // 检查白名单
    if (ROUTE_CONFIG.WHITE_LIST.includes(routePath)) return true
    
    // 使用权限工具函数
    return hasRoutePermission(routePath, userRole)
  }

  /**
   * 检查菜单权限
   */
  async filterAccessibleMenus(userRole, menus) {
    if (!userRole || !Array.isArray(menus)) return []
    
    const filteredMenus = []
    
    for (const menu of menus) {
      const accessibleMenu = await this.processMenuPermissions(userRole, menu)
      if (accessibleMenu) {
        filteredMenus.push(accessibleMenu)
      }
    }

    return filteredMenus
  }

  /**
   * 处理菜单权限
   */
  async processMenuPermissions(userRole, menu) {
    // 如果没有设置权限要求，直接返回
    if (!menu.meta?.roles && !menu.meta?.permissions) {
      return menu
    }

    // 检查角色权限
    if (menu.meta?.roles) {
      const hasRole = menu.meta.roles.includes(userRole)
      if (!hasRole) return null
    }

    // 检查具体权限
    if (menu.meta?.permissions) {
      const hasPermission = await this.hasAnyPermission(userRole, menu.meta.permissions)
      if (!hasPermission) return null
    }

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      const children = await this.filterAccessibleMenus(userRole, menu.children)
      if (children.length === 0) return null
      
      return {
        ...menu,
        children
      }
    }

    return menu
  }

  /**
   * 检查数据访问权限
   */
  async canAccessData(userRole, dataType, dataOwner) {
    if (!userRole || !dataType) return false
    
    // 超级管理员可以访问所有数据
    if (userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE) return true

    // 检查数据权限
    const dataPermission = `data:${dataType}`
    const hasDataPermission = await this.hasPermission(userRole, dataPermission)
    
    if (!hasDataPermission) return false

    // 检查数据所有权
    if (dataOwner) {
      return canViewUserData(userRole, dataOwner)
    }

    return true
  }

  /**
   * 检查操作权限
   */
  async canPerformOperation(userRole, operation, context = {}) {
    if (!userRole || !operation) return false
    
    // 超级管理员可以执行所有操作
    if (userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE) return true

    const operationPermission = `op:${operation}`
    const hasOperationPermission = await this.hasPermission(userRole, operationPermission)
    
    if (!hasOperationPermission) return false

    // 上下文相关权限检查
    if (context.targetRole) {
      return canViewUserData(userRole, context.targetRole)
    }

    return true
  }

  /**
   * 检查群组创建权限
   */
  async canCreateGroups(userRole, groupType = null) {
    if (!userRole) return false
    
    // 使用权限工具函数
    const basePermission = canCreateGroup(userRole)
    
    if (!basePermission) return false

    // 检查群组类型权限
    if (groupType) {
      const groupPermission = `group:create:${groupType}`
      return await this.hasPermission(userRole, groupPermission)
    }

    return true
  }

  /**
   * 获取用户可访问的路由
   */
  async getAccessibleRoutes(userRole, allRoutes) {
    if (!userRole || !Array.isArray(allRoutes)) return []
    
    const accessibleRoutes = []
    
    for (const route of allRoutes) {
      if (await this.hasRouteAccess(userRole, route.path)) {
        const accessibleRoute = { ...route }
        
        // 处理子路由
        if (route.children && route.children.length > 0) {
          accessibleRoute.children = await this.getAccessibleRoutes(userRole, route.children)
        }
        
        accessibleRoutes.push(accessibleRoute)
      }
    }

    return accessibleRoutes
  }

  /**
   * 获取用户权限摘要
   */
  async getPermissionSummary(userRole) {
    if (!userRole) return null
    
    const permissions = await this.getUserPermissions(userRole)
    const roleConfig = PERMISSION_CONFIG.ROLES[userRole]
    
    return {
      role: userRole,
      displayName: roleConfig?.displayName || userRole,
      permissions: permissions.length,
      menuCount: roleConfig?.allowedMenus?.length || 0,
      operationCount: roleConfig?.allowedOperations?.length || 0,
      dataAccess: roleConfig?.dataPermissions || [],
      isSuperAdmin: userRole === PERMISSION_CONFIG.SUPER_ADMIN_ROLE
    }
  }

  /**
   * 验证权限配置
   */
  async validatePermissions(userRole, requiredPermissions) {
    const results = {}
    
    for (const permission of requiredPermissions) {
      results[permission] = await this.hasPermission(userRole, permission)
    }

    return results
  }

  /**
   * 获取权限建议
   */
  async getPermissionSuggestions(userRole, missingPermissions) {
    if (!userRole || !Array.isArray(missingPermissions)) return []
    
    const suggestions = []
    const roleConfig = PERMISSION_CONFIG.ROLES[userRole]
    
    missingPermissions.forEach(permission => {
      const suggestion = this.generatePermissionSuggestion(permission, roleConfig)
      if (suggestion) {
        suggestions.push(suggestion)
      }
    })

    return suggestions
  }

  /**
   * 生成权限建议
   */
  generatePermissionSuggestion(permission, roleConfig) {
    const parts = permission.split(':')
    const type = parts[0]
    const action = parts[1]
    const resource = parts[2]

    return {
      permission,
      type,
      action,
      resource,
      suggestion: `${roleConfig?.displayName || '当前角色'} 需要 ${action} ${resource || '权限'} 的权限`
    }
  }

  /**
   * 清理过期的缓存
   */
  cleanupCache() {
    const now = Date.now()
    
    // 清理权限缓存
    for (const [key, value] of this.permissionCache.entries()) {
      if (value.expire <= now) {
        this.permissionCache.delete(key)
      }
    }

    // 清理角色缓存
    for (const [key, value] of this.roleCache.entries()) {
      if (value.expire <= now) {
        this.roleCache.delete(key)
      }
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.permissionCache.clear()
    this.roleCache.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      permissionCache: this.permissionCache.size,
      roleCache: this.roleCache.size,
      totalCached: this.permissionCache.size + this.roleCache.size
    }
  }

  /**
   * 导出权限配置
   */
  exportPermissionConfig(userRole) {
    const config = {
      role: userRole,
      permissions: [],
      timestamp: new Date().toISOString()
    }

    // 这里可以添加导出逻辑
    return config
  }
}

// 创建单例实例
const permissionService = new PermissionService()

// 快捷方法
export const checkPermission = (userRole, permission) => permissionService.hasPermission(userRole, permission)
export const checkRouteAccess = (userRole, routePath) => permissionService.hasRouteAccess(userRole, routePath)
export const getUserPermissions = (userRole) => permissionService.getUserPermissions(userRole)

export default permissionService