/* 富文本编辑器修复样式 */

/* 确保富文本编辑器容器正确显示 */
.rich-editor-container {
  width: 100%;
  
  .editor-actions {
    margin-bottom: 8px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .rich-text-wrapper {
    position: relative;
    width: 100%;
    
    /* 确保富文本编辑器正确显示 */
    .rich-text-editor {
      width: 100% !important;
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px !important;
      background: #fff !important;
      
      /* 工具栏样式修复 */
      .editor-toolbar {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        padding: 8px 12px !important;
        border-bottom: 1px solid #e4e7ed !important;
        background: #f5f7fa !important;
        gap: 8px !important;
        flex-wrap: wrap !important;
        min-height: 40px !important;
        
        .el-button-group {
          display: flex !important;
          
          .el-button {
            padding: 4px 8px !important;
            border: 1px solid #dcdfe6 !important;
            background: #fff !important;
            color: #606266 !important;
            font-size: 12px !important;
            min-height: 28px !important;
            
            &:hover {
              color: #409eff !important;
              border-color: #c6e2ff !important;
              background: #ecf5ff !important;
            }
            
            &.is-active {
              color: #409eff !important;
              border-color: #409eff !important;
              background: #ecf5ff !important;
            }
          }
        }
        
        /* 工具栏按钮图标 */
        .el-icon {
          font-size: 14px !important;
        }
        
        /* 分隔线 */
        .toolbar-divider {
          width: 1px;
          height: 20px;
          background: #e4e7ed;
          margin: 0 4px;
        }
      }
      
      /* 编辑区域样式修复 */
      .editor-content {
        min-height: 200px !important;
        padding: 12px !important;
        outline: none !important;
        line-height: 1.6 !important;
        font-size: 14px !important;
        color: #606266 !important;
        border: none !important;
        resize: vertical !important;
        
        &:focus {
          outline: none !important;
        }
        
        /* 富文本内容样式 */
        p {
          margin: 8px 0;
          line-height: 1.6;
        }
        
        h1, h2, h3, h4, h5, h6 {
          margin: 12px 0 8px 0;
          font-weight: 600;
        }
        
        ul, ol {
          margin: 8px 0;
          padding-left: 20px;
          
          li {
            margin: 4px 0;
          }
        }
        
        blockquote {
          margin: 8px 0;
          padding: 8px 16px;
          border-left: 4px solid #409eff;
          background: #f0f9ff;
          color: #606266;
        }
        
        code {
          padding: 2px 4px;
          background: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-radius: 2px;
          font-family: 'Courier New', monospace;
          font-size: 12px;
        }
        
        pre {
          margin: 8px 0;
          padding: 12px;
          background: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow-x: auto;
          
          code {
            padding: 0;
            background: none;
            border: none;
          }
        }
        
        a {
          color: #409eff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          margin: 8px 0;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 8px 0;
          
          th, td {
            padding: 8px 12px;
            border: 1px solid #e4e7ed;
            text-align: left;
          }
          
          th {
            background: #f5f7fa;
            font-weight: 600;
          }
        }
      }
      
      /* 编辑器底部 */
      .editor-footer {
        padding: 8px 12px !important;
        border-top: 1px solid #e4e7ed !important;
        background: #f5f7fa !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        font-size: 12px !important;
        color: #909399 !important;
        
        .word-count {
          color: #909399;
        }
        
        .editor-status {
          display: flex;
          gap: 8px;
          
          .status-item {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
    }
  }
  
  .editor-tips {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
    
    .el-icon {
      font-size: 14px;
    }
  }
}

/* 在标签页中的富文本编辑器 */
.el-tabs {
  .rich-editor-container {
    margin-top: 8px;
    
    .rich-text-wrapper {
      .rich-text-editor {
        .editor-toolbar {
          visibility: visible !important;
          opacity: 1 !important;
          display: flex !important;
        }
      }
    }
  }
}

/* FAQ编辑器特殊样式 */
.faq-editor {
  .rich-text-wrapper {
    .rich-text-editor {
      .editor-content {
        font-family: inherit;
        
        /* FAQ格式提示样式 */
        &::before {
          content: "提示：使用 Q: 开头表示问题，A: 开头表示答案";
          display: block;
          font-size: 12px;
          color: #c0c4cc;
          margin-bottom: 8px;
          font-style: italic;
        }
        
        &:focus::before {
          display: none;
        }
        
        &:not(:empty)::before {
          display: none;
        }
      }
    }
  }
  
  .faq-preview {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #303133;
      font-weight: 600;
    }
    
    .faq-item {
      margin-bottom: 12px;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
      
      .faq-question {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
        
        &::before {
          content: "Q: ";
          color: #409eff;
          font-weight: 700;
        }
      }
      
      .faq-answer {
        color: #606266;
        padding-left: 16px;
        line-height: 1.5;
        
        &::before {
          content: "A: ";
          color: #67c23a;
          font-weight: 600;
        }
      }
    }
  }
}

/* 评价编辑器特殊样式 */
.reviews-editor {
  .rich-text-wrapper {
    .rich-text-editor {
      .editor-content {
        /* 评价格式提示样式 */
        &::before {
          content: "提示：格式如 张三：这个群组很不错，学到了很多东西 ⭐⭐⭐⭐⭐";
          display: block;
          font-size: 12px;
          color: #c0c4cc;
          margin-bottom: 8px;
          font-style: italic;
        }
        
        &:focus::before {
          display: none;
        }
        
        &:not(:empty)::before {
          display: none;
        }
      }
    }
  }
  
  .reviews-preview {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #303133;
      font-weight: 600;
    }
    
    .review-item {
      margin-bottom: 12px;
      padding: 12px;
      background: white;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      
      .review-content {
        margin-bottom: 8px;
        color: #303133;
        line-height: 1.5;
      }
      
      .review-rating {
        display: flex;
        align-items: center;
        
        .el-rate {
          margin-right: 8px;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-editor-container {
    .editor-actions {
      flex-direction: column;
      gap: 4px;
      
      .el-button {
        width: 100%;
        justify-content: center;
      }
    }
    
    .rich-text-wrapper {
      .rich-text-editor {
        .editor-toolbar {
          padding: 6px 8px !important;
          
          .el-button-group {
            .el-button {
              padding: 3px 6px !important;
              font-size: 11px !important;
            }
          }
        }
        
        .editor-content {
          padding: 8px !important;
          font-size: 13px !important;
        }
        
        .editor-footer {
          padding: 6px 8px !important;
          font-size: 11px !important;
        }
      }
    }
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .rich-editor-container {
    .rich-text-wrapper {
      .rich-text-editor {
        border-color: #4c4d4f !important;
        background: #2d2d2d !important;
        
        .editor-toolbar {
          background: #363636 !important;
          border-bottom-color: #4c4d4f !important;
          
          .el-button-group {
            .el-button {
              background: #2d2d2d !important;
              border-color: #4c4d4f !important;
              color: #cccccc !important;
              
              &:hover {
                color: #409eff !important;
                border-color: #409eff !important;
                background: #1a1a1a !important;
              }
            }
          }
        }
        
        .editor-content {
          background: #2d2d2d !important;
          color: #cccccc !important;
          
          &::placeholder {
            color: #8c8c8c !important;
          }
        }
        
        .editor-footer {
          background: #363636 !important;
          border-top-color: #4c4d4f !important;
          color: #8c8c8c !important;
        }
      }
    }
    
    .editor-tips {
      color: #8c8c8c !important;
    }
  }
  
  .faq-editor,
  .reviews-editor {
    .faq-preview,
    .reviews-preview {
      background: #363636 !important;
      border-color: #4c4d4f !important;
      
      h4 {
        color: #cccccc !important;
      }
      
      .faq-item,
      .review-item {
        background: #2d2d2d !important;
        border-color: #4c4d4f !important;
        
        .faq-question,
        .review-content {
          color: #cccccc !important;
        }
        
        .faq-answer {
          color: #b3b3b3 !important;
        }
      }
    }
  }
}

/* 打印样式 */
@media print {
  .rich-editor-container {
    .editor-actions {
      display: none !important;
    }
    
    .rich-text-wrapper {
      .rich-text-editor {
        border: none !important;
        
        .editor-toolbar,
        .editor-footer {
          display: none !important;
        }
        
        .editor-content {
          padding: 0 !important;
          min-height: auto !important;
        }
      }
    }
    
    .editor-tips {
      display: none !important;
    }
  }
}