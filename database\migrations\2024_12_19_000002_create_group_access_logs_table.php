<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_access_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('wechat_group_id')->comment('群组ID');
            $table->string('visitor_ip', 45)->comment('访问者IP');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->string('referer')->nullable()->comment('来源页面');
            $table->string('detected_city', 50)->nullable()->comment('检测到的城市');
            $table->string('browser_type', 20)->nullable()->comment('浏览器类型');
            $table->boolean('is_wechat_browser')->default(false)->comment('是否微信浏览器');
            $table->string('access_domain', 100)->nullable()->comment('访问域名');
            $table->enum('access_result', ['success', 'blocked', 'error'])->comment('访问结果');
            $table->string('block_reason')->nullable()->comment('阻止原因');
            $table->json('extra_data')->nullable()->comment('额外数据');
            $table->timestamps();

            // 索引
            $table->index(['wechat_group_id', 'created_at'], 'idx_group_time');
            $table->index(['visitor_ip', 'created_at'], 'idx_ip_time');
            $table->index(['detected_city', 'created_at'], 'idx_city_time');
            $table->index(['access_result', 'created_at'], 'idx_result_time');
            $table->index('is_wechat_browser', 'idx_wechat_browser');
            
            // 外键
            $table->foreign('wechat_group_id')->references('id')->on('wechat_groups')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_access_logs');
    }
};