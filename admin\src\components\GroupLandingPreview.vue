<template>
  <div class="group-landing-preview">
    <div class="preview-phone">
      <div class="phone-frame">
        <div class="phone-screen">
          <!-- 1. 顶部海报区域 -->
          <div class="banner-section">
            <div 
              v-if="groupData.banner_image" 
              class="banner-image"
              :style="{ backgroundImage: `url(${groupData.banner_image})` }"
            ></div>
            <div v-else class="banner-default">
              <div class="banner-placeholder">
                <el-icon><Picture /></el-icon>
                <span>顶部海报</span>
              </div>
            </div>
          </div>

          <!-- 2. 群组名称区域 -->
          <div class="group-name-section">
            <h1 class="group-name">
              {{ displayGroupName }}
            </h1>
            <div class="group-meta">
              <span class="category">{{ getCategoryText(groupData.category) }}</span>
              <span class="price">{{ getPriceText() }}</span>
            </div>
          </div>

          <!-- 3. 群组头像区域 -->
          <div class="group-avatar-section">
            <div class="avatar-container">
              <img 
                v-if="groupData.avatar" 
                :src="groupData.avatar" 
                alt="群组头像"
                class="group-avatar"
              />
              <div v-else class="avatar-placeholder">
                <el-icon><User /></el-icon>
              </div>
            </div>
            <div class="group-info">
              <div class="group-description">{{ groupData.description || '暂无描述' }}</div>
              <div class="group-stats">
                <span>👥 {{ groupData.current_members || 0 }}/{{ groupData.max_members || 500 }}</span>
                <span v-if="groupData.virtual_members > 0">📊 {{ groupData.virtual_members }}人已关注</span>
              </div>
            </div>
          </div>

          <!-- 4. 虚拟成员头像区域 -->
          <div v-if="groupData.avatar_library && groupData.avatar_library.length > 0" class="members-section">
            <div class="members-title">群组成员</div>
            <div class="members-avatars">
              <div 
                v-for="(avatar, index) in groupData.avatar_library.slice(0, 8)" 
                :key="index"
                class="member-avatar"
              >
                <img :src="avatar" :alt="`成员${index + 1}`" />
              </div>
              <div v-if="groupData.avatar_library.length > 8" class="more-members">
                +{{ groupData.avatar_library.length - 8 }}
              </div>
            </div>
          </div>

          <!-- 5. 群组内容介绍区域 -->
          <div class="content-section">
            <!-- 富文本内容 -->
            <div v-if="groupData.rich_content" class="rich-content" v-html="groupData.rich_content"></div>
            
            <!-- 群组介绍 -->
            <div v-if="groupData.introduction" class="introduction">
              <h3>群组介绍</h3>
              <p>{{ groupData.introduction }}</p>
            </div>
            
            <!-- 展示图片 -->
            <div v-if="groupData.gallery_images && groupData.gallery_images.length > 0" class="gallery-section">
              <div class="gallery-grid">
                <div 
                  v-for="(image, index) in groupData.gallery_images.slice(0, 6)" 
                  :key="index"
                  class="gallery-item"
                >
                  <img :src="image" :alt="`图片${index + 1}`" />
                </div>
              </div>
            </div>
            
            <!-- 介绍视频 -->
            <div v-if="groupData.intro_video" class="video-section">
              <div class="video-placeholder">
                <el-icon><VideoPlay /></el-icon>
                <span>介绍视频</span>
              </div>
            </div>
            
            <!-- 群组公告 -->
            <div v-if="groupData.announcement" class="announcement-section">
              <h3>群组公告</h3>
              <div class="announcement-content" v-html="groupData.announcement"></div>
            </div>
            
            <!-- 群规 -->
            <div v-if="groupData.rules" class="rules-section">
              <h3>群规</h3>
              <p>{{ groupData.rules }}</p>
            </div>
          </div>

          <!-- 6. 底部操作区域 -->
          <div class="action-section">
            <div class="action-container">
              <div class="group-owner" v-if="groupData.owner_name">
                <img
                  v-if="groupData.owner_avatar"
                  :src="groupData.owner_avatar"
                  alt="群主头像"
                  class="owner-avatar"
                />
                <div v-else class="owner-avatar-placeholder">
                  <el-icon><User /></el-icon>
                </div>
                <span class="owner-name">群主：{{ groupData.owner_name }}</span>
              </div>

              <el-button type="primary" size="large" class="join-button">
                {{ getJoinButtonText() }}
              </el-button>
            </div>
          </div>

          <!-- 7. 付费后内容区域（仅在有付费内容时显示） -->
          <div v-if="hasPaidContent" class="paid-content-section">
            <div class="paid-content-container">
              <div class="paid-success-header">
                <el-icon class="success-icon"><Check /></el-icon>
                <h3 class="success-title">{{ groupData.paid_success_title || '付费成功' }}</h3>
              </div>

              <!-- 富媒体内容块 -->
              <div
                v-for="(block, index) in groupData.paid_content_blocks"
                :key="block.id || index"
                class="content-block"
              >
                <!-- 富文本内容 -->
                <div v-if="block.type === 'richtext' && block.content" class="richtext-block">
                  <div class="richtext-content" v-html="block.content"></div>
                </div>

                <!-- 图片内容 -->
                <div v-else-if="block.type === 'images' && block.content && block.content.length > 0" class="images-block">
                  <div class="images-grid">
                    <div
                      v-for="(image, imgIndex) in block.content.slice(0, 6)"
                      :key="imgIndex"
                      class="image-item"
                    >
                      <img :src="image" :alt="`图片${imgIndex + 1}`" />
                    </div>
                    <div v-if="block.content.length > 6" class="more-images">
                      +{{ block.content.length - 6 }}
                    </div>
                  </div>
                </div>

                <!-- 文档内容 -->
                <div v-else-if="block.type === 'documents' && block.content && block.content.length > 0" class="documents-block">
                  <h4 v-if="block.title" class="block-title">{{ block.title }}</h4>
                  <div class="documents-list">
                    <div
                      v-for="(doc, docIndex) in block.content"
                      :key="docIndex"
                      class="document-item"
                    >
                      <el-icon class="doc-icon"><Folder /></el-icon>
                      <span class="doc-name">{{ getFileName(doc) }}</span>
                      <el-icon class="download-icon"><Download /></el-icon>
                    </div>
                  </div>
                </div>

                <!-- 视频内容 -->
                <div v-else-if="block.type === 'video' && block.content" class="video-block">
                  <h4 v-if="block.title" class="block-title">{{ block.title }}</h4>
                  <div v-if="block.videoType === 'upload'" class="video-player">
                    <div class="video-placeholder">
                      <el-icon class="video-icon"><VideoPlay /></el-icon>
                      <span>视频内容</span>
                    </div>
                  </div>
                  <div v-else class="video-embed">
                    <div class="embed-placeholder">
                      <el-icon class="video-icon"><VideoPlay /></el-icon>
                      <span>嵌入视频</span>
                    </div>
                  </div>
                </div>

                <!-- 网站链接 -->
                <div v-else-if="block.type === 'links' && block.content" class="links-block">
                  <div class="link-item">
                    <div class="link-header">
                      <el-icon class="link-icon"><Link /></el-icon>
                      <h4 class="link-title">{{ block.title || '相关链接' }}</h4>
                    </div>
                    <p v-if="block.description" class="link-description">{{ block.description }}</p>
                    <div class="link-url">{{ block.content }}</div>
                  </div>
                </div>

                <!-- 二维码 -->
                <div v-else-if="block.type === 'qrcode' && block.content" class="qrcode-block">
                  <h4 v-if="block.title" class="block-title">{{ block.title }}</h4>
                  <div class="qrcode-container">
                    <img :src="block.content" alt="二维码" class="qrcode-image" />
                    <p v-if="block.description" class="qrcode-description">{{ block.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Picture, User, VideoPlay, Check, Folder, Download, Link } from '@element-plus/icons-vue'

const props = defineProps({
  groupData: {
    type: Object,
    default: () => ({})
  },
  testCity: {
    type: String,
    default: '北京'
  }
})

// 计算属性
const displayGroupName = computed(() => {
  // 直接使用传入的群组名称，因为在父组件中已经处理了城市替换
  return props.groupData.name || '群组名称'
})

const hasPaidContent = computed(() => {
  const { paid_content_blocks } = props.groupData
  return !!(paid_content_blocks && paid_content_blocks.length > 0)
})

// 方法
const getCategoryText = (category) => {
  const categoryMap = {
    startup: '创业交流',
    finance: '投资理财',
    tech: '科技互联网',
    education: '教育培训',
    life: '生活服务',
    other: '其他'
  }
  return categoryMap[category] || '未分类'
}

const getPriceText = () => {
  const price = props.groupData.price
  if (price === 0 || price === undefined) {
    return '免费'
  }
  return `¥${price}`
}

const getJoinButtonText = () => {
  const { price, free_button_text, paid_button_text, join_button_text } = props.groupData

  if (price === 0 || price === undefined) {
    return free_button_text || '免费加入'
  } else {
    return paid_button_text || '付费进群'
  }
}

const getFileName = (filePath) => {
  if (typeof filePath === 'string') {
    return filePath.split('/').pop() || '文档'
  }
  return '文档'
}
</script>

<style lang="scss" scoped>
.group-landing-preview {
  display: flex;
  justify-content: center;
  padding: 20px;

  .preview-phone {
    .phone-frame {
      width: 320px;
      height: 600px;
      background: #000;
      border-radius: 20px;
      padding: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      .phone-screen {
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 12px;
        overflow-y: auto;
        overflow-x: hidden;

        // 1. 顶部海报区域
        .banner-section {
          height: 120px;
          position: relative;

          .banner-image {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
          }

          .banner-default {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;

            .banner-placeholder {
              display: flex;
              flex-direction: column;
              align-items: center;
              color: white;
              opacity: 0.8;

              .el-icon {
                font-size: 24px;
                margin-bottom: 8px;
              }

              span {
                font-size: 12px;
              }
            }
          }
        }

        // 2. 群组名称区域
        .group-name-section {
          padding: 16px;
          background: white;
          border-bottom: 1px solid #f0f0f0;

          .group-name {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 8px 0;
            line-height: 1.4;
          }

          .group-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;

            .category {
              color: #409eff;
              background: #ecf5ff;
              padding: 2px 8px;
              border-radius: 12px;
            }

            .price {
              color: #f56c6c;
              font-weight: 600;
            }
          }
        }

        // 3. 群组头像区域
        .group-avatar-section {
          padding: 16px;
          background: white;
          border-bottom: 1px solid #f0f0f0;
          display: flex;
          gap: 12px;

          .avatar-container {
            flex-shrink: 0;

            .group-avatar {
              width: 60px;
              height: 60px;
              border-radius: 12px;
              object-fit: cover;
            }

            .avatar-placeholder {
              width: 60px;
              height: 60px;
              border-radius: 12px;
              background: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #c0c4cc;

              .el-icon {
                font-size: 24px;
              }
            }
          }

          .group-info {
            flex: 1;

            .group-description {
              font-size: 14px;
              color: #606266;
              line-height: 1.5;
              margin-bottom: 8px;
            }

            .group-stats {
              display: flex;
              gap: 12px;
              font-size: 12px;
              color: #909399;
            }
          }
        }

        // 4. 虚拟成员头像区域
        .members-section {
          padding: 16px;
          background: white;
          border-bottom: 1px solid #f0f0f0;

          .members-title {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
          }

          .members-avatars {
            display: flex;
            gap: 8px;
            align-items: center;

            .member-avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              overflow: hidden;
              border: 2px solid #fff;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .more-members {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10px;
              color: #909399;
              border: 2px solid #fff;
            }
          }
        }

        // 5. 群组内容介绍区域
        .content-section {
          background: white;

          .rich-content, .introduction, .announcement-section, .rules-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;

            h3 {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin: 0 0 12px 0;
            }

            p {
              font-size: 14px;
              color: #606266;
              line-height: 1.6;
              margin: 0;
            }
          }

          .gallery-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;

            .gallery-grid {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 8px;

              .gallery-item {
                aspect-ratio: 1;
                border-radius: 8px;
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
            }
          }

          .video-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;

            .video-placeholder {
              height: 120px;
              background: #f5f7fa;
              border-radius: 8px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              color: #909399;

              .el-icon {
                font-size: 32px;
                margin-bottom: 8px;
              }
            }
          }
        }

        // 6. 底部操作区域
        .action-section {
          padding: 16px;
          background: white;
          border-top: 1px solid #f0f0f0;

          .action-container {
            .group-owner {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 16px;
              font-size: 12px;
              color: #909399;

              .owner-avatar {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                object-fit: cover;
              }

              .owner-avatar-placeholder {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: #f5f7fa;
                display: flex;
                align-items: center;
                justify-content: center;

                .el-icon {
                  font-size: 12px;
                }
              }
            }

            .join-button {
              width: 100%;
              height: 44px;
              border-radius: 22px;
              font-size: 16px;
              font-weight: 600;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;

              &:hover {
                opacity: 0.9;
              }
            }
          }
        }

        // 7. 付费后内容区域
        .paid-content-section {
          padding: 20px 16px;
          background: #f8fffe;
          border-top: 2px solid #67c23a;

          .paid-content-container {
            .paid-success-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 16px;

              .success-icon {
                color: #67c23a;
                font-size: 20px;
              }

              .success-title {
                color: #67c23a;
                font-size: 16px;
                font-weight: 600;
                margin: 0;
              }
            }

            .content-block {
              margin-bottom: 16px;
              background: white;
              border-radius: 8px;
              padding: 12px;
              border: 1px solid #e4e7ed;

              &:last-child {
                margin-bottom: 0;
              }

              .block-title {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
                margin: 0 0 12px 0;
              }

              // 富文本内容
              .richtext-block {
                .richtext-content {
                  font-size: 14px;
                  line-height: 1.6;
                  color: #606266;

                  :deep(p) {
                    margin: 0 0 8px 0;

                    &:last-child {
                      margin-bottom: 0;
                    }
                  }

                  :deep(img) {
                    max-width: 100%;
                    height: auto;
                    border-radius: 4px;
                  }
                }
              }

              // 图片内容
              .images-block {
                .images-grid {
                  display: grid;
                  grid-template-columns: repeat(3, 1fr);
                  gap: 4px;

                  .image-item {
                    aspect-ratio: 1;
                    border-radius: 4px;
                    overflow: hidden;

                    img {
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                    }
                  }

                  .more-images {
                    aspect-ratio: 1;
                    background: #f5f7fa;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    color: #909399;
                  }
                }
              }

              // 文档内容
              .documents-block {
                .documents-list {
                  .document-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 12px;
                    background: #f5f7fa;
                    border-radius: 6px;
                    margin-bottom: 8px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .doc-icon {
                      color: #409eff;
                      font-size: 16px;
                    }

                    .doc-name {
                      flex: 1;
                      font-size: 12px;
                      color: #606266;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }

                    .download-icon {
                      color: #67c23a;
                      font-size: 14px;
                    }
                  }
                }
              }

              // 视频内容
              .video-block {
                .video-player, .video-embed {
                  .video-placeholder, .embed-placeholder {
                    height: 120px;
                    background: #f5f7fa;
                    border-radius: 6px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: #909399;

                    .video-icon {
                      font-size: 32px;
                      margin-bottom: 8px;
                    }

                    span {
                      font-size: 12px;
                    }
                  }
                }
              }

              // 网站链接
              .links-block {
                .link-item {
                  .link-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;

                    .link-icon {
                      color: #409eff;
                      font-size: 16px;
                    }

                    .link-title {
                      font-size: 14px;
                      font-weight: 600;
                      color: #303133;
                      margin: 0;
                    }
                  }

                  .link-description {
                    font-size: 12px;
                    color: #909399;
                    margin: 0 0 8px 0;
                    line-height: 1.4;
                  }

                  .link-url {
                    font-size: 12px;
                    color: #409eff;
                    background: #ecf5ff;
                    padding: 6px 8px;
                    border-radius: 4px;
                    word-break: break-all;
                  }
                }
              }

              // 二维码
              .qrcode-block {
                text-align: center;

                .qrcode-container {
                  .qrcode-image {
                    width: 120px;
                    height: 120px;
                    border-radius: 8px;
                    border: 1px solid #e4e7ed;
                  }

                  .qrcode-description {
                    font-size: 12px;
                    color: #909399;
                    margin: 8px 0 0 0;
                    line-height: 1.4;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 滚动条样式 */
.phone-screen::-webkit-scrollbar {
  width: 2px;
}

.phone-screen::-webkit-scrollbar-track {
  background: transparent;
}

.phone-screen::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 1px;
}
</style>
