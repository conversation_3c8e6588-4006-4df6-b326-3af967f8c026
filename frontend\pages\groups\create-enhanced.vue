<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- 顶部导航 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <NuxtLink to="/groups" class="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              返回群组管理
            </NuxtLink>
          </div>
          <div class="flex items-center space-x-4">
            <!-- 保存状态指示器 -->
            <div v-if="autoSaveStatus" class="flex items-center text-sm text-gray-500">
              <svg v-if="autoSaveStatus === 'saving'" class="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else-if="autoSaveStatus === 'saved'" class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              {{ autoSaveStatus === 'saving' ? '保存中...' : '已保存' }}
            </div>
            
            <button @click="saveAsDraft" :disabled="saving" class="text-gray-600 hover:text-gray-900 disabled:opacity-50 transition-colors">
              {{ saving ? '保存中...' : '保存草稿' }}
            </button>
            <button @click="previewGroup" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
              预览效果
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 转化率优化助手 -->
      <EnhancedGroupCreator 
        v-model:form-data="formData"
        class="mb-8"
      />

      <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
        <!-- 主要创建区域 -->
        <div class="xl:col-span-2">
          <!-- 进度指示器 -->
          <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h1 class="text-2xl font-bold text-gray-900">创建精品社群</h1>
              <div class="text-sm text-gray-500">
                第 {{ currentStep + 1 }} 步，共 {{ steps.length }} 步
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <div 
                v-for="(step, index) in steps" 
                :key="index"
                class="flex items-center"
              >
                <div 
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300',
                    index <= currentStep 
                      ? 'bg-blue-600 text-white shadow-lg' 
                      : 'bg-gray-200 text-gray-600'
                  ]"
                >
                  <svg v-if="index < currentStep" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <div 
                  v-if="index < steps.length - 1"
                  :class="[
                    'w-16 h-1 mx-2 transition-all duration-300',
                    index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  ]"
                ></div>
              </div>
            </div>
            
            <div class="mt-4">
              <h3 class="font-medium text-gray-900">{{ steps[currentStep].title }}</h3>
              <p class="text-sm text-gray-600">{{ steps[currentStep].description }}</p>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <!-- 步骤1：基础信息 -->
            <div v-if="currentStep === 0" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">群组基础信息</h2>
                <p class="text-gray-600">设置您的群组基本信息，这些信息将展示给潜在用户</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                  <SmartFormField
                    v-model="formData.title"
                    label="群组名称"
                    :required="true"
                    :max-length="50"
                    :min-length="5"
                    placeholder="例如：北京创业交流群、上海副业赚钱群"
                    :suggestions="titleSuggestions"
                    help-text="💡 提示：使用'xxx'作为城市占位符，系统会自动替换为用户所在城市"
                    :optimization-rules="[
                      (value) => ({
                        tip: value.length >= 5 ? '标题长度合适，有助于提升转化率' : '建议标题长度至少5个字符',
                        impact: value.length >= 5 ? '+0.8%' : ''
                      })
                    ]"
                    @optimization-change="handleOptimizationChange"
                  />
                </div>

                <div>
                  <SmartFormField
                    v-model="formData.category"
                    label="群组分类"
                    :required="true"
                    field-type="select"
                    placeholder="请选择分类"
                  >
                    <option value="">请选择分类</option>
                    <option v-for="cat in categories" :key="cat.value" :value="cat.value">
                      {{ cat.label }}
                    </option>
                  </SmartFormField>
                </div>

                <div>
                  <SmartFormField
                    v-model="formData.price"
                    label="入群价格"
                    :required="true"
                    type="number"
                    placeholder="0.00"
                    help-text="建议价格：9.9-99.9元"
                    :optimization-rules="[
                      (value) => ({
                        tip: value >= 9.9 && value <= 99.9 ? '价格设置合理，转化率较高' : '建议价格设置在9.9-99.9元之间',
                        impact: value >= 9.9 && value <= 99.9 ? '+1.2%' : ''
                      })
                    ]"
                  />
                </div>

                <div class="md:col-span-2">
                  <SmartFormField
                    v-model="formData.description"
                    label="群组描述"
                    :required="true"
                    field-type="textarea"
                    :max-length="500"
                    placeholder="详细描述您的群组价值，例如：提供什么服务、解决什么问题、有什么独特优势..."
                    help-text="清晰的描述能提高转化率"
                    :optimization-rules="[
                      (value) => ({
                        tip: value.length >= 50 ? '描述详细，有助于用户了解价值' : '建议描述至少50个字符，详细说明群组价值',
                        impact: value.length >= 50 ? '+0.6%' : ''
                      })
                    ]"
                  />
                </div>

                <div>
                  <SmartFormField
                    v-model="formData.member_limit"
                    label="群成员上限"
                    type="number"
                    placeholder="200"
                    help-text="建议设置为100-500人"
                  />
                </div>

                <div>
                  <SmartFormField
                    v-model="formData.current_members"
                    label="当前成员数"
                    type="number"
                    placeholder="0"
                    help-text="显示的初始成员数，建议设置为上限的30-70%"
                    :optimization-rules="[
                      (value) => {
                        const ratio = value / (formData.member_limit || 200)
                        return {
                          tip: ratio >= 0.3 && ratio <= 0.7 ? '成员数设置合理，营造活跃氛围' : '建议设置为群上限的30-70%',
                          impact: ratio >= 0.3 && ratio <= 0.7 ? '+0.5%' : ''
                        }
                      }
                    ]"
                  />
                </div>
              </div>
            </div>

            <!-- 步骤2：营销优化 -->
            <div v-if="currentStep === 1" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">营销转化优化</h2>
                <p class="text-gray-600">设置吸引用户的营销元素，提高转化率</p>
              </div>

              <!-- 社会证明设置 -->
              <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">社会证明设置</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <SmartFormField
                      v-model="formData.read_count_display"
                      label="阅读数显示"
                      placeholder="8万+"
                      help-text="显示浏览量增加信任度"
                    />
                  </div>
                  <div>
                    <SmartFormField
                      v-model="formData.like_count"
                      label="点赞数"
                      type="number"
                      placeholder="1500"
                      help-text="点赞数体现受欢迎程度"
                    />
                  </div>
                  <div>
                    <SmartFormField
                      v-model="formData.want_see_count"
                      label="想看数"
                      type="number"
                      placeholder="1000"
                      help-text="想看数表示用户兴趣"
                    />
                  </div>
                </div>
              </div>

              <!-- 按钮和展示设置 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <SmartFormField
                    v-model="formData.button_title"
                    label="入群按钮文案"
                    placeholder="立即加入群聊"
                    :suggestions="buttonSuggestions"
                    help-text="吸引人的按钮文案能提升点击率"
                  />
                </div>

                <div>
                  <SmartFormField
                    v-model="formData.avatar_library"
                    label="头像库选择"
                    field-type="select"
                  >
                    <option value="default">默认头像库</option>
                    <option value="business">商务头像库</option>
                    <option value="dating">交友头像库</option>
                    <option value="education">教育头像库</option>
                  </SmartFormField>
                </div>
              </div>

              <!-- 紧迫感设置 -->
              <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">紧迫感营销</h3>
                <div class="space-y-4">
                  <label class="flex items-center">
                    <input
                      v-model="formData.show_limited_time"
                      type="checkbox"
                      class="mr-3 w-4 h-4 text-blue-600"
                    />
                    <span class="text-sm font-medium">显示限时优惠</span>
                  </label>
                  
                  <div v-if="formData.show_limited_time" class="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                    <div>
                      <SmartFormField
                        v-model="formData.original_price"
                        label="原价"
                        type="number"
                        placeholder="99.00"
                        help-text="设置原价显示优惠力度"
                      />
                    </div>
                    <div>
                      <SmartFormField
                        v-model="formData.discount_end_time"
                        label="优惠截止时间"
                        type="datetime-local"
                        help-text="设置优惠截止时间增加紧迫感"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤3：内容配置 -->
            <div v-if="currentStep === 2" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">内容与服务</h2>
                <p class="text-gray-600">配置群组介绍、FAQ和用户评价，增强信任度</p>
              </div>

              <div class="space-y-6">
                <!-- 群组介绍 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">群组详细介绍</h3>
                  <div class="space-y-4">
                    <div>
                      <SmartFormField
                        v-model="formData.group_intro_title"
                        label="介绍标题"
                        placeholder="群简介"
                        help-text="简洁明了的介绍标题"
                      />
                    </div>
                    <div>
                      <SmartFormField
                        v-model="formData.group_intro_content"
                        label="详细内容"
                        field-type="textarea"
                        :max-length="1000"
                        placeholder="详细介绍群组的价值、服务内容、群规等..."
                        help-text="详细的介绍能让用户更好地了解群组价值"
                      />
                    </div>
                  </div>
                </div>

                <!-- FAQ配置 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">常见问题</h3>
                    <button
                      @click="addFAQ"
                      class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-700"
                    >
                      + 添加问题
                    </button>
                  </div>
                  
                  <div class="space-y-4">
                    <div
                      v-for="(faq, index) in formData.faqs"
                      :key="index"
                      class="bg-gray-50 rounded-lg p-4"
                    >
                      <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">问题 {{ index + 1 }}</span>
                        <button
                          @click="removeFAQ(index)"
                          class="text-red-600 hover:text-red-800 text-sm"
                        >
                          删除
                        </button>
                      </div>
                      <div class="space-y-3">
                        <SmartFormField
                          v-model="faq.question"
                          placeholder="输入问题"
                          help-text="常见问题能减少用户疑虑"
                        />
                        <SmartFormField
                          v-model="faq.answer"
                          field-type="textarea"
                          placeholder="输入答案"
                          help-text="详细回答用户关心的问题"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 用户评价 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">用户评价</h3>
                    <button
                      @click="addReview"
                      class="bg-green-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-green-700"
                    >
                      + 添加评价
                    </button>
                  </div>
                  
                  <div class="space-y-4">
                    <div
                      v-for="(review, index) in formData.reviews"
                      :key="index"
                      class="bg-gray-50 rounded-lg p-4"
                    >
                      <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">评价 {{ index + 1 }}</span>
                        <button
                          @click="removeReview(index)"
                          class="text-red-600 hover:text-red-800 text-sm"
                        >
                          删除
                        </button>
                      </div>
                      <div class="space-y-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <SmartFormField
                            v-model="review.author"
                            placeholder="用户名"
                            help-text="真实的用户名增加可信度"
                          />
                          <SmartFormField
                            v-model="review.rating"
                            field-type="select"
                          >
                            <option value="5">⭐⭐⭐⭐⭐ 5分</option>
                            <option value="4">⭐⭐⭐⭐ 4分</option>
                            <option value="3">⭐⭐⭐ 3分</option>
                          </SmartFormField>
                        </div>
                        <SmartFormField
                          v-model="review.content"
                          field-type="textarea"
                          placeholder="输入评价内容"
                          help-text="具体的评价内容更有说服力"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤4：付费内容 -->
            <div v-if="currentStep === 3" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">付费后内容</h2>
                <p class="text-gray-600">设置用户付费后能获得的内容或服务</p>
              </div>

              <div class="space-y-6">
                <!-- 内容类型选择 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-3">内容类型</label>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <label
                      v-for="type in contentTypes"
                      :key="type.value"
                      class="relative flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50"
                      :class="formData.paid_content_type === type.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
                    >
                      <input
                        v-model="formData.paid_content_type"
                        type="radio"
                        :value="type.value"
                        class="sr-only"
                      />
                      <div class="text-center">
                        <div class="text-2xl mb-2">{{ type.icon }}</div>
                        <div class="text-sm font-medium">{{ type.label }}</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- 二维码上传 -->
                <div v-if="formData.paid_content_type === 'qr_code'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">微信群二维码</h3>
                  <div class="space-y-4">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <div class="mt-4">
                        <label class="cursor-pointer">
                          <span class="mt-2 block text-sm font-medium text-gray-900">
                            点击上传群二维码
                          </span>
                          <input type="file" class="sr-only" accept="image/*" @change="handleQRUpload" />
                        </label>
                      </div>
                      <p class="mt-2 text-xs text-gray-500">PNG, JPG, GIF 最大 10MB</p>
                    </div>
                  </div>
                </div>

                <!-- 文本内容 -->
                <div v-if="formData.paid_content_type === 'text'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">文本内容</h3>
                  <SmartFormField
                    v-model="formData.paid_text_content"
                    field-type="textarea"
                    :max-length="2000"
                    placeholder="输入用户付费后能看到的文本内容..."
                    help-text="提供有价值的文本内容，如教程、资料等"
                  />
                </div>

                <!-- 链接内容 -->
                <div v-if="formData.paid_content_type === 'link'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">链接内容</h3>
                  <div class="space-y-4">
                    <SmartFormField
                      v-model="formData.paid_link_title"
                      label="链接标题"
                      placeholder="例如：专属资料下载"
                      help-text="描述链接内容的标题"
                    />
                    <SmartFormField
                      v-model="formData.paid_link_url"
                      label="链接地址"
                      placeholder="https://example.com"
                      help-text="用户付费后可以访问的链接"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤导航按钮 -->
            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
              <button
                v-if="currentStep > 0"
                @click="previousStep"
                class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                上一步
              </button>
              <div v-else></div>
              
              <button
                v-if="currentStep < steps.length - 1"
                @click="nextStep"
                :disabled="!canProceedToNext"
                class="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                下一步
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
              <button
                v-else
                @click="submitForm"
                :disabled="!canSubmit || submitting"
                class="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {{ submitting ? '创建中...' : '创建群组' }}
                <svg v-if="!submitting" class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="xl:col-span-2">
          <!-- 实时预览 -->
          <div class="bg-white rounded-xl shadow-sm p-6 mb-6 sticky top-24">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">实时预览</h3>
            <GroupPreview :form-data="formData" />
          </div>

          <!-- 转化率仪表盘 -->
          <ConversionDashboard :form-data="formData" />
        </div>
      </div>
    </div>

    <!-- 预览模态框 -->
    <GroupPreviewModal
      v-if="showPreviewModal"
      :form-data="formData"
      @close="showPreviewModal = false"
    />
  </div>
</template>
<script s
etup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// 页面元数据
definePageMeta({
  title: '创建精品社群 - 增强版',
  description: '使用AI优化的群组创建工具，提升转化率'
})

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const saving = ref(false)
const submitting = ref(false)
const autoSaveStatus = ref('')
const showPreviewModal = ref(false)

// 表单数据
const formData = ref({
  title: '',
  category: '',
  price: 0,
  description: '',
  member_limit: 200,
  current_members: 0,
  read_count_display: '8万+',
  like_count: 1500,
  want_see_count: 1000,
  button_title: '立即加入群聊',
  avatar_library: 'default',
  show_limited_time: false,
  original_price: 0,
  discount_end_time: '',
  group_intro_title: '群简介',
  group_intro_content: '',
  faqs: [],
  reviews: [],
  paid_content_type: 'qr_code',
  paid_text_content: '',
  paid_link_title: '',
  paid_link_url: ''
})

// 步骤配置
const steps = ref([
  {
    title: '基础信息',
    description: '设置群组的基本信息和定价'
  },
  {
    title: '营销优化',
    description: '配置社会证明和紧迫感营销'
  },
  {
    title: '内容配置',
    description: '添加详细介绍、FAQ和用户评价'
  },
  {
    title: '付费内容',
    description: '设置用户付费后获得的内容'
  }
])

// 分类选项
const categories = ref([
  { value: 'business', label: '商务交流' },
  { value: 'education', label: '教育学习' },
  { value: 'lifestyle', label: '生活方式' },
  { value: 'investment', label: '投资理财' },
  { value: 'technology', label: '科技互联网' },
  { value: 'health', label: '健康养生' },
  { value: 'entertainment', label: '娱乐休闲' },
  { value: 'other', label: '其他' }
])

// 标题建议
const titleSuggestions = computed(() => {
  if (!formData.value.category) return []
  
  const suggestions = {
    business: [
      '商务精英交流群',
      '创业项目对接群',
      '商业资源共享群',
      '企业家私董会'
    ],
    education: [
      '学霸养成计划群',
      '知识变现实战群',
      '技能提升加速群',
      '在线学习交流群'
    ],
    lifestyle: [
      '品质生活分享群',
      '时尚潮流交流群',
      '美食探店群',
      '旅行攻略分享群'
    ],
    investment: [
      '投资理财交流群',
      '股票基金讨论群',
      '财富增值学习群',
      '理财规划指导群'
    ]
  }
  
  return suggestions[formData.value.category] || []
})

// 按钮文案建议
const buttonSuggestions = ref([
  '立即加入群聊',
  '马上进群',
  '点击入群',
  '加入我们',
  '立即参与',
  '现在加入',
  '抢先入群',
  '限时加入'
])

// 内容类型选项
const contentTypes = ref([
  { value: 'qr_code', label: '微信群二维码', icon: '📱' },
  { value: 'text', label: '文本内容', icon: '📝' },
  { value: 'link', label: '链接内容', icon: '🔗' },
  { value: 'file', label: '文件下载', icon: '📁' }
])

// 计算属性
const canProceedToNext = computed(() => {
  switch (currentStep.value) {
    case 0:
      return formData.value.title && 
             formData.value.category && 
             formData.value.price > 0 && 
             formData.value.description
    case 1:
      return true // 营销优化步骤是可选的
    case 2:
      return true // 内容配置步骤是可选的
    case 3:
      return formData.value.paid_content_type
    default:
      return false
  }
})

const canSubmit = computed(() => {
  return formData.value.title && 
         formData.value.category && 
         formData.value.price > 0 && 
         formData.value.description &&
         formData.value.paid_content_type
})

// 方法
const nextStep = () => {
  if (currentStep.value < steps.value.length - 1) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const addFAQ = () => {
  formData.value.faqs.push({
    question: '',
    answer: ''
  })
}

const removeFAQ = (index) => {
  formData.value.faqs.splice(index, 1)
}

const addReview = () => {
  formData.value.reviews.push({
    author: '',
    rating: '5',
    content: ''
  })
}

const removeReview = (index) => {
  formData.value.reviews.splice(index, 1)
}

const handleQRUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 这里应该上传文件到服务器
    console.log('上传二维码文件:', file)
    // 模拟上传成功
    formData.value.qr_code_url = URL.createObjectURL(file)
  }
}

const handleOptimizationChange = (data) => {
  // 处理优化建议变化
  console.log('优化建议变化:', data)
}

const saveAsDraft = async () => {
  saving.value = true
  autoSaveStatus.value = 'saving'
  
  try {
    // 这里应该调用API保存草稿
    await new Promise(resolve => setTimeout(resolve, 1000))
    autoSaveStatus.value = 'saved'
    
    setTimeout(() => {
      autoSaveStatus.value = ''
    }, 2000)
  } catch (error) {
    console.error('保存草稿失败:', error)
  } finally {
    saving.value = false
  }
}

const previewGroup = () => {
  showPreviewModal.value = true
}

const submitForm = async () => {
  if (!canSubmit.value) return
  
  submitting.value = true
  
  try {
    // 这里应该调用API创建群组
    console.log('提交表单数据:', formData.value)
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 创建成功后跳转
    router.push('/groups')
  } catch (error) {
    console.error('创建群组失败:', error)
  } finally {
    submitting.value = false
  }
}

// 自动保存功能
let autoSaveTimer = null
const startAutoSave = () => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  
  autoSaveTimer = setTimeout(() => {
    if (formData.value.title || formData.value.description) {
      saveAsDraft()
    }
  }, 30000) // 30秒后自动保存
}

// 监听表单数据变化，触发自动保存
watch(formData, () => {
  startAutoSave()
}, { deep: true })

// 页面加载时初始化
onMounted(() => {
  // 可以在这里加载草稿数据
  console.log('增强版群组创建页面已加载')
})

// 页面离开时清理定时器
onUnmounted(() => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
})
</script>

<style scoped>
/* 自定义样式 */
.step-indicator {
  @apply transition-all duration-300;
}

.step-content {
  @apply transition-all duration-500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.xl\\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .xl\\:col-span-2 {
    grid-column: span 1 / span 1;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-content > div {
  animation: slideIn 0.3s ease-out;
}

/* 优化提示样式 */
.optimization-tip {
  @apply bg-blue-50 border-l-4 border-blue-400 p-3 rounded;
}

.conversion-impact {
  @apply text-green-600 font-medium;
}

/* 表单验证样式 */
.field-valid {
  @apply border-green-300 bg-green-50;
}

.field-invalid {
  @apply border-red-300 bg-red-50;
}

.field-warning {
  @apply border-yellow-300 bg-yellow-50;
}
</style>