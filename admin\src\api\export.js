import api from './index'

// 数据导出相关API
export const previewExportData = (type, params) => {
  return api.get(`/admin/exports/preview/${type}`, { params })
}

export const exportUsers = (params) => {
  return api.get('/admin/users/export', { params })
}

export const exportGroups = (params) => {
  return api.get('/admin/groups/export', { params })
}

export const exportOrders = (params) => {
  return api.get('/admin/orders/export', { params })
}

export const exportDistributors = (params) => {
  return api.get('/admin/distributors/export', { params })
}

export const exportCommissions = (params) => {
  return api.get('/admin/commissions/export', { params })
}

export const exportWithdraws = (params) => {
  return api.get('/admin/withdrawals/export', { params })
}

export const exportTransactions = (params) => {
  return api.get('/admin/finance/transactions/export', { params })
}

export const exportStatistics = (params) => {
  return api.get('/admin/statistics/export', { params })
}

export const getExportTasks = (params) => {
  return api.get('/admin/exports', { params })
}

export const deleteExportTask = (id) => {
  return api.delete(`/admin/exports/${id}`)
}

export const cleanupExportTasks = () => {
  return api.post('/admin/exports/cleanup')
}