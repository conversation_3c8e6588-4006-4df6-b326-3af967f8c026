const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DtXAftX0.js","assets/vue-vendor-Dy164gUc.js","assets/element-plus-h2SQQM64.js","assets/utils-D1VZuEZr.js","assets/index-DETnwR_y.css","assets/permission-BsO7Yg_T.js"])))=>i.map(i=>d[i]);
import{_ as e,u as a,l as s,a as r,s as t,b as o}from"./index-DtXAftX0.js";import{af as i,r as n,L as l,c as d,e as u,n as v,H as c,k as p,l as f,ah as m,t as g,J as w,B as h,A as y,a4 as b,a3 as x,am as k,_}from"./vue-vendor-Dy164gUc.js";import{U as E,o as R,Q as C}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const B={class:"login-page"},M={class:"login-container"},U={class:"login-card"},z={key:0,class:"error-alert"},A={class:"form-fields"},I={class:"form-group"},L={class:"input-wrapper"},V={key:0,class:"field-error"},$={class:"form-group"},j={class:"input-wrapper"},P=["type"],q={key:0,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},D={key:1,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},H={key:0,class:"field-error"},T={class:"form-options"},K={class:"remember-me"},O=["disabled"],S={key:0,class:"loading-spinner"},Z=e({__name:"Login",setup(e){const Z=i(),F=a(),J=n(),N=n(),Q=n(),W=n(!1),G=n(!1);n(!1);const X=n(!1),Y=n(!1);n(!1);const ee=()=>{const e=window.innerHeight,a=window.innerWidth;Y.value=e<800||a<480},ae=()=>{ee()},se=l({username:"",password:""});l({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"},{validator:(e,a,s)=>{if(!a)return void s();const r=a.trim();if(r.includes("@")){if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return void s(new Error("邮箱格式不正确"))}else{if(!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(r))return void s(new Error("用户名只能包含字母、数字、下划线或中文"))}s()},trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:128,message:"密码长度在 6 到 128 个字符",trigger:"blur"},{validator:(e,a,s)=>{if(!a)return void s();const r=/[a-zA-Z]/.test(a),t=/[0-9]/.test(a);if(!r||!t)return void s(new Error("密码必须包含字母和数字"));/[<>'"&]/.test(a)?s(new Error("密码包含不安全字符")):s()},trigger:"blur"}]}),d(()=>G.value?"text":"password");const re=()=>{W.value=!0,C.info("正在进入预览模式...");try{F.enterPreviewMode(),setTimeout(()=>{C.success("欢迎来到预览模式！"),Z.push("/dashboard"),W.value=!1},500)}catch(e){console.error("进入预览模式失败:",e),C.error("无法进入预览模式，请稍后重试。"),W.value=!1}},te=async()=>{try{await J.value.validate(),W.value=!0;const e=await F.login(se),a=e.data?.user?.role||F.userInfo?.role,s=e.data?.user?.nickname||e.data?.user?.username||"用户",r={admin:"欢迎回来，超级管理员！",substation:"欢迎回来，分站管理员！",agent:"欢迎回来，代理商！",distributor:"欢迎回来，分销员！",group_owner:"欢迎回来，群主！",user:"欢迎回来！"};C.success({message:`${r[a]||"登录成功！"} ${s}`,type:"success",duration:3e3}),await oe(a)}catch(e){console.error("登录失败:",e);const a={"Network Error":"网络连接失败，请检查网络设置",timeout:"请求超时，请稍后重试",Unauthorized:"用户名或密码错误",Forbidden:"账户已被禁用，请联系管理员"},r=a[e.code]||a[e.message]||e.message||"登录失败，请检查用户名和密码";C.error({message:r,type:"error",duration:5e3}),s(se.username,!1,r)}finally{W.value=!1}},oe=async e=>{try{const{getUserDefaultRoute:a}=await r(async()=>{const{getUserDefaultRoute:e}=await import("./index-DtXAftX0.js").then(e=>e.w);return{getUserDefaultRoute:e}},__vite__mapDeps([0,1,2,3,4])),i=Z.currentRoute.value.query.redirect;if(i){const{checkRoutePermission:a}=await r(async()=>{const{checkRoutePermission:e}=await import("./permission-BsO7Yg_T.js");return{checkRoutePermission:e}},__vite__mapDeps([5,0,1,2,3,4]));if(a({path:i,meta:{}},e))return void Z.push(i);C.warning({message:"您没有权限访问请求的页面，已跳转到默认页面",duration:4e3})}const n=a(e),l=t.createSession({id:F.userInfo?.id,username:se.username,role:e});s(se.username,!0,`重定向到: ${n}`),o(se.username,"login",l.id,`角色: ${e}, 重定向: ${n}`),Z.push(n)}catch(a){console.error("重定向失败:",a),Z.push("/dashboard")}};u(()=>{ee(),window.addEventListener("resize",ae),""===se.username?v(()=>{N.value.focus()}):v(()=>{Q.value.focus()})}),c(()=>{window.removeEventListener("resize",ae)});const ie=()=>{C.info("请联系系统管理员重置密码")},ne=n({username:"",password:""}),le=n(""),de=e=>{ne.value[e]&&(ne.value[e]=""),le.value&&(le.value="")};return(e,a)=>(f(),p("div",B,[a[16]||(a[16]=m('<div class="background-decoration" data-v-64173f62><div class="floating-shapes" data-v-64173f62><div class="shape shape-1" data-v-64173f62></div><div class="shape shape-2" data-v-64173f62></div><div class="shape shape-3" data-v-64173f62></div><div class="shape shape-4" data-v-64173f62></div><div class="shape shape-5" data-v-64173f62></div></div><div class="gradient-overlay" data-v-64173f62></div></div>',1)),g("div",M,[g("div",U,[a[15]||(a[15]=m('<div class="login-header" data-v-64173f62><div class="logo-section" data-v-64173f62><div class="logo-icon" data-v-64173f62><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-64173f62><path d="M13 10V3L4 14h7v7l9-11h-7z" fill="currentColor" data-v-64173f62></path></svg></div><div class="logo-text" data-v-64173f62><h1 data-v-64173f62>晨鑫流量变现系统</h1><p data-v-64173f62>智能社群营销与多级分销平台</p></div></div><div class="welcome-section" data-v-64173f62><h2 data-v-64173f62>管理员登录</h2><p data-v-64173f62>欢迎回来，请登录您的管理员账户</p><div class="status-indicator" data-v-64173f62><div class="status-dot" data-v-64173f62></div><span data-v-64173f62>系统运行正常</span></div></div></div>',1)),g("form",{class:"login-form",onSubmit:w(te,["prevent"])},[le.value?(f(),p("div",z,[a[6]||(a[6]=m('<div class="error-icon" data-v-64173f62><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" data-v-64173f62><circle cx="12" cy="12" r="10" data-v-64173f62></circle><line x1="15" y1="9" x2="9" y2="15" data-v-64173f62></line><line x1="9" y1="9" x2="15" y2="15" data-v-64173f62></line></svg></div>',1)),g("span",null,E(le.value),1)])):h("",!0),g("div",A,[g("div",I,[a[8]||(a[8]=g("label",{for:"username",class:"form-label"},"用户名或邮箱",-1)),g("div",L,[a[7]||(a[7]=g("div",{class:"input-icon"},[g("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[g("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),g("circle",{cx:"12",cy:"7",r:"4"})])],-1)),y(g("input",{id:"username",ref_key:"usernameRef",ref:N,"onUpdate:modelValue":a[0]||(a[0]=e=>se.username=e),type:"text",required:"",class:R(["modern-input",{"input-error":ne.value.username}]),placeholder:"请输入用户名或邮箱",autofocus:"",onInput:a[1]||(a[1]=e=>de("username")),onKeyup:x(te,["enter"])},null,34),[[b,se.username]])]),ne.value.username?(f(),p("p",V,E(ne.value.username),1)):h("",!0)]),g("div",$,[a[12]||(a[12]=g("label",{for:"password",class:"form-label"},"密码",-1)),g("div",j,[a[11]||(a[11]=m('<div class="input-icon" data-v-64173f62><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" data-v-64173f62><rect x="3" y="11" width="18" height="11" rx="2" ry="2" data-v-64173f62></rect><circle cx="12" cy="16" r="1" data-v-64173f62></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" data-v-64173f62></path></svg></div>',1)),y(g("input",{id:"password",ref_key:"passwordRef",ref:Q,"onUpdate:modelValue":a[2]||(a[2]=e=>se.password=e),type:G.value?"text":"password",required:"",class:R(["modern-input",{"input-error":ne.value.password}]),placeholder:"请输入密码",onInput:a[3]||(a[3]=e=>de("password")),onKeyup:x(te,["enter"])},null,42,P),[[k,se.password]]),g("button",{type:"button",onClick:a[4]||(a[4]=e=>G.value=!G.value),class:"password-toggle"},[G.value?(f(),p("svg",D,a[10]||(a[10]=[g("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"},null,-1),g("line",{x1:"1",y1:"1",x2:"23",y2:"23"},null,-1)]))):(f(),p("svg",q,a[9]||(a[9]=[g("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"},null,-1),g("circle",{cx:"12",cy:"12",r:"3"},null,-1)])))])]),ne.value.password?(f(),p("p",H,E(ne.value.password),1)):h("",!0)])]),g("div",T,[g("div",K,[y(g("input",{id:"remember","onUpdate:modelValue":a[5]||(a[5]=e=>X.value=e),type:"checkbox",class:"modern-checkbox"},null,512),[[_,X.value]]),a[13]||(a[13]=g("label",{for:"remember",class:"checkbox-label"},"记住我",-1))]),g("a",{href:"#",class:"forgot-password",onClick:w(ie,["prevent"])}," 忘记密码？ ")]),g("button",{type:"submit",disabled:W.value,class:"modern-login-button"},[W.value?(f(),p("div",S,a[14]||(a[14]=[g("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor"},[g("path",{d:"M21 12a9 9 0 11-6.219-8.56"})],-1)]))):h("",!0),g("span",null,E(W.value?"登录中...":"立即登录管理后台"),1)],8,O),g("button",{type:"button",onClick:re,class:"preview-button"}," 🚀 进入预览模式 ")],32)])])]))}},[["__scopeId","data-v-64173f62"]]);export{Z as default};
