/**
 * API配置和Mock数据管理
 * 解决开发环境中的API连接问题
 */

// 检测是否为开发环境
const isDev = import.meta.env.DEV
const enableMock = import.meta.env.VITE_ENABLE_MOCK === 'true'

// API基础配置
export const apiConfig = {
  // 根据环境自动选择API地址
  baseURL: isDev && enableMock 
    ? '/mock-api' // 使用Mock API
    : import.meta.env.VITE_API_BASE_URL || '/api/v1',
  
  timeout: 30000,
  
  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
}

// Mock API响应数据
export const mockResponses = {
  // 用户管理相关
  'GET /api/v1/users': {
    code: 200,
    message: '获取成功',
    data: {
      list: [
        {
          id: 1,
          username: 'admin',
          realName: '系统管理员',
          email: '<EMAIL>',
          phone: '13800138000',
          role: 'admin',
          status: 'active',
          avatar: '',
          created_at: '2024-01-01 10:00:00',
          last_login_at: '2024-08-06 15:30:00'
        },
        {
          id: 2,
          username: 'zhangsan',
          realName: '张三',
          email: '<EMAIL>',
          phone: '13800138001',
          role: 'agent',
          status: 'active',
          avatar: '',
          created_at: '2024-02-15 14:20:00',
          last_login_at: '2024-08-06 12:15:00'
        }
      ],
      total: 2,
      page: 1,
      size: 20
    }
  },

  'POST /api/v1/users': {
    code: 200,
    message: '用户创建成功',
    data: {
      id: Date.now(),
      username: 'new_user',
      realName: '新用户',
      email: '<EMAIL>',
      phone: '13800138999',
      role: 'user',
      status: 'active',
      created_at: new Date().toISOString()
    }
  },

  'PUT /api/v1/users/:id': {
    code: 200,
    message: '用户更新成功',
    data: null
  },

  'DELETE /api/v1/users/:id': {
    code: 200,
    message: '用户删除成功',
    data: null
  },

  // 角色管理相关
  'GET /api/v1/roles': {
    code: 200,
    message: '获取成功',
    data: {
      list: [
        {
          id: 1,
          name: 'admin',
          display_name: '超级管理员',
          description: '拥有系统所有权限，可以管理所有功能模块',
          permissions_count: 45,
          users_count: 3,
          is_system: true,
          status: 'active',
          created_at: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          name: 'agent',
          display_name: '代理商',
          description: '管理下级分销员，拥有团队管理权限',
          permissions_count: 25,
          users_count: 23,
          is_system: true,
          status: 'active',
          created_at: '2024-02-01 09:30:00'
        }
      ],
      total: 2,
      page: 1,
      size: 20
    }
  },

  'POST /api/v1/roles': {
    code: 200,
    message: '角色创建成功',
    data: {
      id: Date.now(),
      name: 'new_role',
      display_name: '新角色',
      description: '新创建的角色',
      is_system: false,
      status: 'active',
      created_at: new Date().toISOString()
    }
  },

  'PUT /api/v1/roles/:id': {
    code: 200,
    message: '角色更新成功',
    data: null
  },

  'DELETE /api/v1/roles/:id': {
    code: 200,
    message: '角色删除成功',
    data: null
  },

  // 权限配置相关
  'GET /api/v1/roles/:id/permissions': {
    code: 200,
    message: '获取成功',
    data: {
      permissions: [
        'dashboard.view',
        'user.list',
        'user.create',
        'community.list',
        'finance.dashboard'
      ]
    }
  },

  'PUT /api/v1/roles/:id/permissions': {
    code: 200,
    message: '权限配置保存成功',
    data: null
  },

  // 认证相关
  'POST /api/v1/auth/login': {
    code: 200,
    message: '登录成功',
    data: {
      token: 'mock_token_' + Date.now(),
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        name: '系统管理员',
        role: 'admin',
        avatar: '',
        permissions: ['*']
      }
    }
  },

  'GET /api/v1/auth/user': {
    code: 200,
    message: '获取成功',
    data: {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      name: '系统管理员',
      role: 'admin',
      avatar: '',
      permissions: ['*']
    }
  },

  'POST /api/v1/auth/logout': {
    code: 200,
    message: '退出成功',
    data: null
  },

  // 健康检查
  'GET /api/v1/health': {
    code: 200,
    message: '系统运行正常',
    data: {
      status: 'ok',
      timestamp: Date.now(),
      version: '1.0.0'
    }
  }
}

// Mock API拦截器
export function setupMockApi() {
  if (!isDev || !enableMock) {
    return
  }

  console.log('🎭 启用Mock API模式')

  // 拦截fetch请求
  const originalFetch = window.fetch
  window.fetch = async function(url, options = {}) {
    const method = options.method || 'GET'
    let mockKey = `${method.toUpperCase()} ${url}`

    // 处理相对路径URL
    if (url.startsWith('/api/')) {
      mockKey = `${method.toUpperCase()} ${url}`
    }

    // 检查是否有对应的Mock响应
    if (mockResponses[mockKey]) {
      console.log(`🎯 Mock API拦截: ${mockKey}`)

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))

      const mockData = mockResponses[mockKey]
      return new Response(JSON.stringify(mockData), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }
    
    // 如果没有Mock数据，尝试原始请求
    try {
      return await originalFetch(url, options)
    } catch (error) {
      console.warn(`⚠️ API请求失败，返回默认响应: ${url}`)
      
      // 返回默认成功响应
      return new Response(JSON.stringify({
        code: 200,
        message: '操作成功',
        data: null
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }
  }
}

// 检查API连接状态
export async function checkApiConnection() {
  try {
    // 在Mock模式下，直接返回true
    if (isDev && enableMock) {
      console.log('🎭 Mock模式下跳过API连接检查')
      return true
    }

    const response = await fetch('/api/v1/health', {
      method: 'GET',
      timeout: 5000
    })

    if (response.ok) {
      console.log('✅ API连接正常')
      return true
    } else {
      console.warn('⚠️ API连接异常，状态码:', response.status)
      return false
    }
  } catch (error) {
    console.warn('⚠️ API连接失败:', error.message)
    return false
  }
}

// 自动配置API模式
export async function autoConfigureApi() {
  if (isDev) {
    const isApiAvailable = await checkApiConnection()
    
    if (!isApiAvailable && enableMock) {
      console.log('🔄 API不可用，自动启用Mock模式')
      setupMockApi()
    } else if (isApiAvailable) {
      console.log('✅ 使用真实API')
    } else {
      console.warn('⚠️ API不可用且Mock未启用')
    }
  }
}

export default apiConfig