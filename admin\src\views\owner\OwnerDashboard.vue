<template>
  <div class="owner-dashboard">
    <div class="page-header">
      <div class="owner-info">
        <el-avatar :size="60" :src="userStore.avatar" />
        <div class="info-content">
          <h2>{{ userStore.nickname || '群主' }}</h2>
          <p>群主ID: {{ userStore.userInfo?.owner_code || 'O' + userStore.userInfo?.id }}</p>
          <div class="status-tags">
            <el-tag type="success">认证群主</el-tag>
            <el-tag type="primary">{{ levelText }}</el-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="info" @click="showHelpDialog = true">
          <el-icon><QuestionFilled /></el-icon>
          功能说明
        </el-button>
        <el-button type="primary" @click="createGroup">
          <el-icon><Plus /></el-icon>
          创建群组
        </el-button>
        <el-button @click="goToGroupManagement">
          <el-icon><Comment /></el-icon>
          群组管理
        </el-button>
      </div>
    </div>

    <!-- 核心数据统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="管理群组"
          :value="stats.total_groups || 0"
          icon="Comment"
          color="#409EFF"
          :trend="stats.group_growth_rate"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="群组成员"
          :value="stats.total_members || 0"
          icon="User"
          color="#67C23A"
          :trend="stats.member_growth_rate"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="本月收入"
          :value="stats.monthly_income || 0"
          icon="Money"
          color="#E6A23C"
          prefix="¥"
          :trend="stats.income_trend"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="活跃度"
          :value="stats.activity_rate || 0"
          icon="TrendCharts"
          color="#F56C6C"
          suffix="%"
        />
      </el-col>
    </el-row>

    <!-- 快捷操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="action-item" @click="createGroup">
            <el-icon class="action-icon"><Plus /></el-icon>
            <span>创建群组</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToGroupManagement">
            <el-icon class="action-icon"><Comment /></el-icon>
            <span>群组管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToContentManagement">
            <el-icon class="action-icon"><Edit /></el-icon>
            <span>内容管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTemplates">
            <el-icon class="action-icon"><DocumentCopy /></el-icon>
            <span>模板库</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToAnalytics">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <span>数据分析</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToSettings">
            <el-icon class="action-icon"><Setting /></el-icon>
            <span>群组设置</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>群组活跃度趋势</span>
              <el-radio-group v-model="activityPeriod" size="small" @change="loadActivityData">
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近3个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <LineChart
            :data="activityChartData"
            :options="chartOptions"
            height="300px"
          />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>群组类型分布</span>
          </template>
          <DoughnutChart
            :data="groupTypeData"
            :options="doughnutOptions"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 群组管理和最新动态 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>我的群组</span>
              <el-button size="small" @click="goToGroupManagement">查看全部</el-button>
            </div>
          </template>
          <div class="group-list">
            <div v-for="group in myGroups" :key="group.id" class="group-item">
              <div class="group-avatar">
                <el-avatar :size="40" :src="group.cover_image" />
              </div>
              <div class="group-info">
                <div class="group-name">{{ group.title }}</div>
                <div class="group-meta">
                  <span class="member-count">{{ group.member_count }}人</span>
                  <span class="group-type">{{ group.type_text }}</span>
                </div>
              </div>
              <div class="group-status">
                <el-tag :type="getGroupStatusColor(group.status)" size="small">
                  {{ group.status_text }}
                </el-tag>
              </div>
              <div class="group-actions">
                <el-button size="small" @click="manageGroup(group)">
                  管理
                </el-button>
              </div>
            </div>
            <div v-if="myGroups.length === 0" class="empty-state">
              <el-empty description="暂无群组" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新动态</span>
              <el-button size="small" @click="loadRecentActivities">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="activities-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <el-icon :color="getActivityColor(activity.type)">
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
              </div>
              <div class="activity-value" v-if="activity.value">
                <span class="value-amount">{{ activity.value }}</span>
              </div>
            </div>
            <div v-if="recentActivities.length === 0" class="empty-state">
              <el-empty description="暂无动态" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 内容管理快捷入口 -->
    <el-card class="content-management-card">
      <template #header>
        <span>内容管理</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="content-section">
            <h4>群组公告</h4>
            <p>管理群组公告和重要通知</p>
            <el-button @click="goToAnnouncements">管理公告</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="content-section">
            <h4>内容模板</h4>
            <p>使用和管理群组内容模板</p>
            <el-button @click="goToTemplates">模板库</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="content-section">
            <h4>素材管理</h4>
            <p>管理图片、视频等素材资源</p>
            <el-button @click="goToMaterials">素材库</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 创建群组对话框 -->
    <el-dialog v-model="showCreateGroupDialog" title="创建群组" width="600px">
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="群组名称" prop="title">
          <el-input v-model="groupForm.title" placeholder="请输入群组名称" />
        </el-form-item>
        <el-form-item label="群组类型" prop="type">
          <el-select v-model="groupForm.type" placeholder="请选择群组类型">
            <el-option label="免费群组" value="free" />
            <el-option label="付费群组" value="paid" />
            <el-option label="VIP群组" value="vip" />
          </el-select>
        </el-form-item>
        <el-form-item label="群组价格" prop="price" v-if="groupForm.type !== 'free'">
          <el-input-number
            v-model="groupForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入价格"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="群组描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入群组描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateGroup" :loading="creating">
          创建群组
        </el-button>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="群主工作台功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>👑 功能概述</h3>
          <p>群主工作台是您管理微信群组的专业平台，提供群组创建、成员管理、内容发布、数据分析等全方位功能，帮助您高效运营群组，实现流量变现。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Comment /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>群组管理</h4>
                  <p>创建、编辑、删除群组，设置群组属性和权限</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>成员管理</h4>
                  <p>管理群组成员，设置管理员，处理加群申请</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Edit /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>内容管理</h4>
                  <p>发布群公告，管理群内容，使用内容模板</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>数据分析</h4>
                  <p>查看群组活跃度、成员增长、收入统计等数据</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>收益管理</h4>
                  <p>查看群组收入，设置付费门槛，管理收益分成</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>群组设置</h4>
                  <p>配置群规则、自动回复、入群验证等功能</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 群组类型说明 -->
        <div class="help-section">
          <h3>📊 群组类型说明</h3>
          <el-table :data="groupTypes" style="width: 100%">
            <el-table-column prop="type" label="群组类型" width="120">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="类型说明" />
            <el-table-column prop="features" label="主要特点" />
            <el-table-column prop="suitable" label="适用场景" />
          </el-table>
        </div>

        <!-- 群主等级体系 -->
        <div class="help-section">
          <h3>🏆 群主等级体系</h3>
          <el-table :data="ownerLevels" style="width: 100%">
            <el-table-column prop="level" label="等级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="等级名称" width="120" />
            <el-table-column prop="requirements" label="升级条件" />
            <el-table-column prop="benefits" label="专属权益" />
          </el-table>
        </div>

        <!-- 运营技巧 -->
        <div class="help-section">
          <h3>💡 群组运营技巧</h3>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="内容运营" name="content">
              <div class="tips-content">
                <h4>📝 内容策略建议</h4>
                <ul>
                  <li><strong>定期发布</strong>：保持群内活跃度，建议每日至少发布1-2条有价值内容</li>
                  <li><strong>内容多样化</strong>：结合文字、图片、视频等多种形式，提升用户体验</li>
                  <li><strong>互动引导</strong>：通过提问、投票等方式引导成员参与讨论</li>
                  <li><strong>专业分享</strong>：分享行业知识、经验心得，建立专业形象</li>
                  <li><strong>及时回复</strong>：积极回应成员问题，维护良好的群氛围</li>
                </ul>
                <el-alert type="success" :closable="false" style="margin-top: 15px;">
                  💡 建议：使用内容模板功能，提前准备优质内容，提高发布效率
                </el-alert>
              </div>
            </el-tab-pane>
            <el-tab-pane label="成员管理" name="member">
              <div class="tips-content">
                <h4>👥 成员管理策略</h4>
                <ul>
                  <li><strong>入群审核</strong>：设置入群验证，筛选优质成员</li>
                  <li><strong>群规制定</strong>：明确群规则，维护群秩序</li>
                  <li><strong>活跃激励</strong>：对活跃成员给予奖励，提升参与度</li>
                  <li><strong>分层管理</strong>：设置管理员，分工协作管理群组</li>
                  <li><strong>定期清理</strong>：清理不活跃成员，保持群质量</li>
                </ul>
                <el-alert type="info" :closable="false" style="margin-top: 15px;">
                  💡 提示：合理控制群成员数量，一般建议单群不超过500人
                </el-alert>
              </div>
            </el-tab-pane>
            <el-tab-pane label="变现策略" name="monetization">
              <div class="tips-content">
                <h4>💰 变现方式推荐</h4>
                <ul>
                  <li><strong>付费入群</strong>：设置入群费用，筛选付费用户</li>
                  <li><strong>VIP服务</strong>：提供专属服务，收取会员费</li>
                  <li><strong>产品推广</strong>：推广相关产品，获取佣金收入</li>
                  <li><strong>知识付费</strong>：分享专业知识，收取学费</li>
                  <li><strong>广告合作</strong>：接受广告投放，获取广告费</li>
                </ul>
                <el-alert type="warning" :closable="false" style="margin-top: 15px;">
                  ⚠️ 注意：变现要适度，过度商业化可能影响群活跃度
                </el-alert>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何创建群组？" name="create-group">
              <div class="guide-content">
                <ol>
                  <li>点击页面右上角的"创建群组"按钮</li>
                  <li>填写群组基本信息（名称、类型、描述）</li>
                  <li>如果是付费群组，设置入群价格</li>
                  <li>点击"创建群组"完成创建</li>
                  <li>创建成功后可在"我的群组"中查看和管理</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：群组名称要简洁明了，描述要突出群价值和特色
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何查看群组数据？" name="view-data">
              <div class="guide-content">
                <ol>
                  <li>在工作台首页查看核心数据统计卡片</li>
                  <li>查看"群组活跃度趋势"图表了解活跃情况</li>
                  <li>在"群组类型分布"图表中查看群组结构</li>
                  <li>点击"数据分析"进入详细分析页面</li>
                  <li>可按时间段筛选查看不同期间的数据</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：数据每小时更新一次，可实时了解群组运营状况
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何管理群组内容？" name="content-management">
              <div class="guide-content">
                <ol>
                  <li>点击"内容管理"进入内容管理页面</li>
                  <li>在"群组公告"中发布重要通知</li>
                  <li>使用"内容模板"快速创建标准化内容</li>
                  <li>在"素材管理"中上传和管理图片、视频等素材</li>
                  <li>设置自动回复和欢迎语</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 技巧：善用模板功能，可以大大提高内容发布效率
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何设置群组规则？" name="group-settings">
              <div class="guide-content">
                <ol>
                  <li>点击"群组设置"进入设置页面</li>
                  <li>在"基础设置"中配置群基本信息</li>
                  <li>在"入群设置"中配置入群验证和审核</li>
                  <li>在"群规管理"中设置群规则和违规处理</li>
                  <li>在"自动化设置"中配置自动回复等功能</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 提醒：群规要明确具体，便于执行和管理
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="群组收入如何结算？" name="faq1">
              <p>群组收入采用T+1结算模式，即今日产生的收入将在明日到账。您可以在收益管理中查看详细的收入明细和提现记录。</p>
            </el-collapse-item>
            <el-collapse-item title="如何提升群组活跃度？" name="faq2">
              <p>建议：1）定期发布有价值的内容；2）组织群内活动和讨论；3）及时回应成员问题；4）设置活跃奖励机制；5）邀请行业专家分享。</p>
            </el-collapse-item>
            <el-collapse-item title="付费群组如何定价？" name="faq3">
              <p>定价建议根据群价值、目标用户、市场行情等因素综合考虑。一般建议：入门群10-50元，专业群50-200元，高端群200-500元。</p>
            </el-collapse-item>
            <el-collapse-item title="群组被封怎么办？" name="faq4">
              <p>如果群组被微信封禁，请及时联系客服处理。平时要注意：1）遵守微信群规；2）避免发布违规内容；3）不要频繁拉人；4）定期备份重要信息。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { 
  Plus, Comment, Edit, DocumentCopy, TrendCharts, Setting,
  User, Money, Refresh, QuestionFilled
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const activityPeriod = ref('30d')
const showCreateGroupDialog = ref(false)
const showHelpDialog = ref(false)
const creating = ref(false)
const stats = ref({})
const myGroups = ref([])
const recentActivities = ref([])

// 帮助对话框相关数据
const activeTab = ref('content')
const activeGuides = ref(['create-group'])
const activeFAQ = ref([])

// 群组类型说明数据
const groupTypes = ref([
  {
    type: '免费群组',
    color: 'success',
    description: '完全免费加入，无门槛限制',
    features: '基础功能、群公告、文件分享',
    suitable: '兴趣交流、学习讨论、社区建设'
  },
  {
    type: '付费群组',
    color: 'primary',
    description: '需要支付一定费用才能加入',
    features: '专属内容、优质服务、定期活动',
    suitable: '专业培训、知识付费、高质量交流'
  },
  {
    type: 'VIP群组',
    color: 'warning',
    description: '高端付费群组，提供顶级服务',
    features: '一对一指导、独家资源、优先支持',
    suitable: '高端咨询、深度服务、精英圈层'
  }
])

// 群主等级数据
const ownerLevels = ref([
  {
    level: '标准群主',
    color: 'info',
    name: '新手群主',
    requirements: '完成实名认证，创建首个群组',
    benefits: '基础功能、新手指导、社区支持'
  },
  {
    level: '高级群主',
    color: 'primary',
    name: '进阶群主',
    requirements: '管理群组≥3个，总成员≥200人，月收入≥1000元',
    benefits: '高级功能、数据分析、营销工具'
  },
  {
    level: 'VIP群主',
    color: 'warning',
    name: '专业群主',
    requirements: '管理群组≥10个，总成员≥1000人，月收入≥5000元',
    benefits: '专属客服、定制功能、优先更新'
  },
  {
    level: '金牌群主',
    color: 'danger',
    name: '顶级群主',
    requirements: '管理群组≥20个，总成员≥5000人，月收入≥20000元',
    benefits: '专属经理、品牌合作、年度奖励'
  }
])

const groupForm = reactive({
  title: '',
  type: '',
  price: 0,
  description: ''
})

const groupRules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入群组描述', trigger: 'blur' }
  ]
}

const groupFormRef = ref()

// 图表数据
const activityChartData = ref({
  labels: [],
  datasets: [{
    label: '群组活跃度',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const groupTypeData = ref({
  labels: ['免费群组', '付费群组', 'VIP群组'],
  datasets: [{
    data: [0, 0, 0],
    backgroundColor: ['#67C23A', '#409EFF', '#F56C6C']
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

// 计算属性
const levelText = computed(() => {
  const level = userStore.userInfo?.owner_level || 'standard'
  const levelMap = {
    'standard': '标准群主',
    'premium': '高级群主',
    'vip': 'VIP群主'
  }
  return levelMap[level] || '标准群主'
})

// 方法
const loadStats = async () => {
  try {
    // 模拟数据加载
    stats.value = {
      total_groups: 8,
      total_members: 1256,
      monthly_income: 12580,
      activity_rate: 78.5,
      group_growth_rate: 12.5,
      member_growth_rate: 25.8,
      income_trend: 18.6
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadActivityData = async () => {
  try {
    // 模拟活跃度数据
    const mockData = {
      '7d': {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [65, 78, 82, 75, 88, 92, 85]
      },
      '30d': {
        labels: Array.from({length: 30}, (_, i) => `${i+1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 40) + 60)
      },
      '90d': {
        labels: ['1月', '2月', '3月'],
        data: [75, 82, 78]
      }
    }
    
    const data = mockData[activityPeriod.value]
    activityChartData.value = {
      labels: data.labels,
      datasets: [{
        label: '群组活跃度',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      }]
    }
  } catch (error) {
    ElMessage.error('加载活跃度数据失败')
  }
}

const loadMyGroups = async () => {
  try {
    // 模拟群组数据
    myGroups.value = [
      {
        id: 1,
        title: '技术交流群',
        cover_image: '',
        member_count: 156,
        type_text: '免费群组',
        status: 'active',
        status_text: '正常'
      },
      {
        id: 2,
        title: 'VIP学习群',
        cover_image: '',
        member_count: 89,
        type_text: 'VIP群组',
        status: 'active',
        status_text: '正常'
      }
    ]
  } catch (error) {
    ElMessage.error('加载群组数据失败')
  }
}

const loadRecentActivities = async () => {
  try {
    // 模拟活动数据
    recentActivities.value = [
      {
        id: 1,
        type: 'member',
        title: '新成员加入',
        description: '技术交流群新增5名成员',
        created_at: new Date(),
        value: '+5'
      },
      {
        id: 2,
        type: 'income',
        title: '收入到账',
        description: 'VIP学习群收入299元',
        created_at: new Date(Date.now() - 3600000),
        value: '¥299'
      }
    ]
  } catch (error) {
    ElMessage.error('加载活动数据失败')
  }
}

// 导航方法
const createGroup = () => {
  showCreateGroupDialog.value = true
}

const goToGroupManagement = () => {
  router.push('/community/groups')
}

const goToContentManagement = () => {
  router.push('/content/management')
}

const goToTemplates = () => {
  router.push('/content/templates')
}

const goToAnalytics = () => {
  router.push('/community/analytics')
}

const goToSettings = () => {
  router.push('/community/settings')
}

const goToAnnouncements = () => {
  router.push('/content/announcements')
}

const goToMaterials = () => {
  router.push('/content/materials')
}

const manageGroup = (group) => {
  router.push(`/community/groups/${group.id}`)
}

const handleCreateGroup = async () => {
  try {
    await groupFormRef.value.validate()
    creating.value = true
    
    // 模拟创建群组
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('群组创建成功')
    showCreateGroupDialog.value = false
    loadMyGroups()
    loadStats()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建群组失败')
    }
  } finally {
    creating.value = false
  }
}

// 工具方法
const getGroupStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'warning',
    'suspended': 'danger'
  }
  return colors[status] || 'info'
}

const getActivityColor = (type) => {
  const colors = {
    'member': '#67C23A',
    'income': '#409EFF',
    'content': '#E6A23C'
  }
  return colors[type] || '#909399'
}

const getActivityIcon = (type) => {
  const icons = {
    'member': 'User',
    'income': 'Money',
    'content': 'Edit'
  }
  return icons[type] || 'InfoFilled'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadStats()
  loadActivityData()
  loadMyGroups()
  loadRecentActivities()
})
</script>

<style scoped>
.owner-dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.owner-info {
  display: flex;
  align-items: center;
}

.info-content {
  margin-left: 15px;
}

.info-content h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.info-content p {
  margin: 0 0 8px 0;
  color: #909399;
}

.status-tags {
  display: flex;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.quick-actions-card {
  margin-bottom: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e4e7ed;
  background: white;
}

.action-item:hover {
  background: #f5f7fa;
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.action-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
}

.action-item span {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.charts-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-row {
  margin-bottom: 20px;
}

.group-list,
.activities-list {
  max-height: 400px;
  overflow-y: auto;
}

.group-item,
.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.group-item:last-child,
.activity-item:last-child {
  border-bottom: none;
}

.group-avatar {
  margin-right: 12px;
}

.group-info,
.activity-content {
  flex: 1;
}

.group-name,
.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.group-meta,
.activity-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.group-meta span {
  margin-right: 10px;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.activity-icon {
  margin-right: 12px;
}

.activity-value {
  text-align: right;
}

.value-amount {
  font-weight: bold;
  color: #67C23A;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.content-management-card {
  margin-bottom: 20px;
}

.content-section {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.content-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.content-section p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .owner-dashboard {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .owner-info {
    flex-direction: column;
    text-align: center;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .action-item {
    padding: 15px;
  }
  
  .charts-row .el-col {
    margin-bottom: 20px;
  }
  
  .group-item,
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>