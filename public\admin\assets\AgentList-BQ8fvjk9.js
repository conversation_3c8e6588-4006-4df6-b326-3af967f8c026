import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                       *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     *//* empty css               */import{a_ as a,aZ as l,bp as t,bq as n,aM as o,b9 as s,b8 as r,at as u,T as d,aL as i,aR as p,aw as _,aY as m,bh as c,bi as v,a$ as g,U as f,o as b,az as y,aT as h,aB as w,aC as V,bw as k,bx as j,bm as C,bB as x,br as U,by as z,ay as q,Q as S,R as $}from"./element-plus-h2SQQM64.js";import{S as A}from"./StatCard-u_ssO_Ky.js";import{a as D}from"./agent-BTWzqVJ0.js";import{r as F,L,e as B,k as E,l as O,t as R,E as M,z as N,D as T,u as Y,A as I,y as P,B as Q,F as X,Y as Z}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const G={class:"agent-list"},H={key:0,class:"no-commission"},J={key:1,class:"commission-rate"},K={class:"amount"},W={key:0,class:"permanent"},ee={key:2,class:"no-expiry"},ae={class:"pagination"},le=e({__name:"AgentList",setup(e){const le=F(!1),te=F(!1),ne=F(1),oe=F(20),se=F(!1),re=F({}),ue=F({data:[],total:0}),de=F([]),ie=L({keyword:"",agent_level:"",agent_type:"",status:""}),pe=L({user_id:"",agent_name:"",agent_level:"platform",agent_type:"individual",commission_rate:10,validity_period:"year",custom_end_date:"",remark:""}),_e={user_id:[{required:!0,message:"请选择关联用户",trigger:"change"}],agent_name:[{required:!0,message:"请输入代理商名称",trigger:"blur"}],agent_level:[{required:!0,message:"请选择代理商等级",trigger:"change"}],agent_type:[{required:!0,message:"请选择代理商类型",trigger:"change"}],commission_rate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],validity_period:[{required:!0,message:"请选择有效期",trigger:"change"}]},me=F(),ce=async()=>{try{const e=await D.getStats();re.value=e.data}catch(e){S.error("加载统计数据失败")}},ve=async()=>{try{le.value=!0;const e={page:ne.value,limit:oe.value,...ie},a=await D.getList(e);ue.value=a.data}catch(e){S.error("加载代理商列表失败")}finally{le.value=!1}},ge=()=>{ne.value=1,ve()},fe=()=>{Object.keys(ie).forEach(e=>{ie[e]=""}),ge()},be=e=>{oe.value=e,ve()},ye=e=>{ne.value=e,ve()},he=()=>{se.value=!0,we()},we=async()=>{de.value=[{id:1,name:"张三",username:"zhangsan"},{id:2,name:"李四",username:"lisi"}]},Ve=async()=>{try{await me.value.validate(),te.value=!0,await D.create(pe),S.success("代理商创建成功"),se.value=!1,ve(),ce()}catch(e){!1!==e&&S.error("创建代理商失败")}finally{te.value=!1}},ke=({action:e,row:a})=>{switch(e){case"edit":S.info(`编辑代理商 ${a.agent_name}`);break;case"renew":S.info(`续费代理商 ${a.agent_name}`);break;case"status":S.info(`管理代理商 ${a.agent_name} 状态`);break;case"delete":je(a)}},je=async e=>{try{await $.confirm(`确定要删除代理商 ${e.agent_name} 吗？`,"确认删除",{type:"warning"}),await D.delete(e.id),S.success("删除成功"),ve(),ce()}catch(a){"cancel"!==a&&S.error("删除失败")}},Ce=e=>({active:"正常",inactive:"未激活",suspended:"暂停",expired:"已过期"}[e]||"未知"),xe=e=>({individual:"个人代理",enterprise:"企业代理",channel:"渠道代理"}[e]||"未知"),Ue=e=>{const a=new Date,l=new Date(e),t=Math.ceil((l-a)/864e5);return t<0?"expired":t<=7?"expiring-soon":"valid"};return B(()=>{ce(),ve()}),(e,$)=>{const D=a,F=l,L=o,B=n,ce=r,ve=s,we=d,je=u,ze=t,qe=m,Se=v,$e=g,Ae=V,De=w,Fe=y,Le=c,Be=j,Ee=x,Oe=C,Re=U,Me=z,Ne=q,Te=k;return O(),E("div",G,[$[31]||($[31]=R("div",{class:"page-header"},[R("h2",null,"代理商管理"),R("p",null,"管理平台代理商，包括代理商信息、状态管理和绩效分析")],-1)),M(F,{gutter:20,class:"stats-row"},{default:N(()=>[M(D,{span:6},{default:N(()=>[M(A,{title:"总代理商",value:re.value.total_agents||0,icon:"Avatar",color:"#409EFF"},null,8,["value"])]),_:1}),M(D,{span:6},{default:N(()=>[M(A,{title:"活跃代理商",value:re.value.active_agents||0,icon:"Check",color:"#67C23A"},null,8,["value"])]),_:1}),M(D,{span:6},{default:N(()=>[M(A,{title:"平台代理商",value:re.value.platform_agents||0,icon:"Star",color:"#E6A23C"},null,8,["value"])]),_:1}),M(D,{span:6},{default:N(()=>[M(A,{title:"分站代理商",value:re.value.substation_agents||0,icon:"OfficeBuilding",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),M(qe,{class:"search-card"},{default:N(()=>[M(ze,{model:ie,inline:""},{default:N(()=>[M(B,{label:"关键词"},{default:N(()=>[M(L,{modelValue:ie.keyword,"onUpdate:modelValue":$[0]||($[0]=e=>ie.keyword=e),placeholder:"搜索代理商名称或编码",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),M(B,{label:"代理商等级"},{default:N(()=>[M(ve,{modelValue:ie.agent_level,"onUpdate:modelValue":$[1]||($[1]=e=>ie.agent_level=e),placeholder:"选择等级",clearable:""},{default:N(()=>[M(ce,{label:"平台代理商",value:"platform"}),M(ce,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),M(B,{label:"代理商类型"},{default:N(()=>[M(ve,{modelValue:ie.agent_type,"onUpdate:modelValue":$[2]||($[2]=e=>ie.agent_type=e),placeholder:"选择类型",clearable:""},{default:N(()=>[M(ce,{label:"个人代理",value:"individual"}),M(ce,{label:"企业代理",value:"enterprise"}),M(ce,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),M(B,{label:"状态"},{default:N(()=>[M(ve,{modelValue:ie.status,"onUpdate:modelValue":$[3]||($[3]=e=>ie.status=e),placeholder:"选择状态",clearable:""},{default:N(()=>[M(ce,{label:"正常",value:"active"}),M(ce,{label:"未激活",value:"inactive"}),M(ce,{label:"暂停",value:"suspended"}),M(ce,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])]),_:1}),M(B,null,{default:N(()=>[M(je,{type:"primary",onClick:ge},{default:N(()=>[M(we,null,{default:N(()=>[M(Y(i))]),_:1}),$[16]||($[16]=T(" 搜索 ",-1))]),_:1,__:[16]}),M(je,{onClick:fe},{default:N(()=>[M(we,null,{default:N(()=>[M(Y(p))]),_:1}),$[17]||($[17]=T(" 重置 ",-1))]),_:1,__:[17]}),M(je,{type:"success",onClick:he},{default:N(()=>[M(we,null,{default:N(()=>[M(Y(_))]),_:1}),$[18]||($[18]=T(" 新增代理商 ",-1))]),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),_:1}),M(qe,{class:"agents-table"},{header:N(()=>$[19]||($[19]=[R("span",null,"代理商列表",-1)])),default:N(()=>[I((O(),P(Le,{data:ue.value.data,stripe:""},{default:N(()=>[M(Se,{prop:"agent_code",label:"代理商编码"}),M(Se,{prop:"agent_name",label:"代理商名称"}),M(Se,{prop:"user.username",label:"关联用户"}),M(Se,{prop:"agent_level_label",label:"代理商等级"},{default:N(({row:e})=>{return[M($e,{type:(a=e.agent_level,"platform"===a?"primary":"success")},{default:N(()=>[T(f("platform"===e.agent_level?"平台代理商":"分站代理商"),1)]),_:2},1032,["type"])];var a}),_:1}),M(Se,{prop:"agent_type_label",label:"代理商类型"},{default:N(({row:e})=>{return[M($e,{type:(a=e.agent_type,{individual:"primary",enterprise:"success",channel:"warning"}[a]||"info")},{default:N(()=>[T(f(xe(e.agent_type)),1)]),_:2},1032,["type"])];var a}),_:1}),M(Se,{prop:"commission_display",label:"佣金设置"},{default:N(({row:e})=>[e.no_commission?(O(),E("span",H,"不抽佣")):(O(),E("span",J,f(e.commission_rate)+"%",1))]),_:1}),M(Se,{prop:"total_commission",label:"总佣金"},{default:N(({row:e})=>[R("span",K,"¥"+f(e.total_commission||0),1)]),_:1}),M(Se,{prop:"status_label",label:"状态"},{default:N(({row:e})=>{return[M($e,{type:(a=e.status,{active:"success",inactive:"info",suspended:"warning",expired:"danger"}[a]||"info")},{default:N(()=>[T(f(Ce(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),M(Se,{prop:"validity_display",label:"有效期"},{default:N(({row:e})=>{return[e.is_permanent?(O(),E("span",W,"永久有效")):e.end_date?(O(),E("span",{key:1,class:b(Ue(e.end_date))},f((a=e.end_date,a?new Date(a).toLocaleDateString("zh-CN"):"")),3)):(O(),E("span",ee,"未设置"))];var a}),_:1}),M(Se,{label:"操作",width:"200"},{default:N(({row:e})=>[M(je,{size:"small",onClick:a=>{return l=e,void S.info(`查看代理商 ${l.agent_name} 详情`);var l}},{default:N(()=>$[20]||($[20]=[T(" 查看详情 ",-1)])),_:2,__:[20]},1032,["onClick"]),M(Fe,{onCommand:ke,trigger:"click"},{dropdown:N(()=>[M(De,null,{default:N(()=>[M(Ae,{command:{action:"edit",row:e}},{default:N(()=>$[22]||($[22]=[T("编辑",-1)])),_:2,__:[22]},1032,["command"]),e.is_permanent?Q("",!0):(O(),P(Ae,{key:0,command:{action:"renew",row:e}},{default:N(()=>$[23]||($[23]=[T("续费",-1)])),_:2,__:[23]},1032,["command"])),M(Ae,{command:{action:"status",row:e}},{default:N(()=>$[24]||($[24]=[T("状态管理",-1)])),_:2,__:[24]},1032,["command"]),M(Ae,{command:{action:"delete",row:e},divided:""},{default:N(()=>$[25]||($[25]=[T("删除",-1)])),_:2,__:[25]},1032,["command"])]),_:2},1024)]),default:N(()=>[M(je,{size:"small",type:"primary"},{default:N(()=>[$[21]||($[21]=T(" 更多操作",-1)),M(we,{class:"el-icon--right"},{default:N(()=>[M(Y(h))]),_:1})]),_:1,__:[21]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Te,le.value]]),R("div",ae,[M(Be,{"current-page":ne.value,"onUpdate:currentPage":$[4]||($[4]=e=>ne.value=e),"page-size":oe.value,"onUpdate:pageSize":$[5]||($[5]=e=>oe.value=e),total:ue.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:ye},null,8,["current-page","page-size","total"])])]),_:1}),M(Ne,{modelValue:se.value,"onUpdate:modelValue":$[15]||($[15]=e=>se.value=e),title:"新增代理商",width:"600px"},{footer:N(()=>[M(je,{onClick:$[14]||($[14]=e=>se.value=!1)},{default:N(()=>$[29]||($[29]=[T("取消",-1)])),_:1,__:[29]}),M(je,{type:"primary",onClick:Ve,loading:te.value},{default:N(()=>$[30]||($[30]=[T(" 确认创建 ",-1)])),_:1,__:[30]},8,["loading"])]),default:N(()=>[M(ze,{model:pe,rules:_e,ref_key:"createFormRef",ref:me,"label-width":"120px"},{default:N(()=>[M(B,{label:"关联用户",prop:"user_id"},{default:N(()=>[M(ve,{modelValue:pe.user_id,"onUpdate:modelValue":$[6]||($[6]=e=>pe.user_id=e),placeholder:"选择用户",filterable:""},{default:N(()=>[(O(!0),E(X,null,Z(de.value,e=>(O(),P(ce,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),M(B,{label:"代理商名称",prop:"agent_name"},{default:N(()=>[M(L,{modelValue:pe.agent_name,"onUpdate:modelValue":$[7]||($[7]=e=>pe.agent_name=e),placeholder:"请输入代理商名称"},null,8,["modelValue"])]),_:1}),M(B,{label:"代理商等级",prop:"agent_level"},{default:N(()=>[M(Oe,{modelValue:pe.agent_level,"onUpdate:modelValue":$[8]||($[8]=e=>pe.agent_level=e)},{default:N(()=>[M(Ee,{label:"platform"},{default:N(()=>$[26]||($[26]=[T("平台代理商",-1)])),_:1,__:[26]}),M(Ee,{label:"substation"},{default:N(()=>$[27]||($[27]=[T("分站代理商",-1)])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),M(B,{label:"代理商类型",prop:"agent_type"},{default:N(()=>[M(ve,{modelValue:pe.agent_type,"onUpdate:modelValue":$[9]||($[9]=e=>pe.agent_type=e),placeholder:"选择类型"},{default:N(()=>[M(ce,{label:"个人代理",value:"individual"}),M(ce,{label:"企业代理",value:"enterprise"}),M(ce,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),M(B,{label:"佣金比例",prop:"commission_rate"},{default:N(()=>[M(Re,{modelValue:pe.commission_rate,"onUpdate:modelValue":$[10]||($[10]=e=>pe.commission_rate=e),min:0,max:100,precision:2,"controls-position":"right"},null,8,["modelValue"]),$[28]||($[28]=R("span",{style:{"margin-left":"10px"}},"%",-1))]),_:1,__:[28]}),M(B,{label:"有效期",prop:"validity_period"},{default:N(()=>[M(ve,{modelValue:pe.validity_period,"onUpdate:modelValue":$[11]||($[11]=e=>pe.validity_period=e),placeholder:"选择有效期"},{default:N(()=>[M(ce,{label:"1周",value:"week"}),M(ce,{label:"1个月",value:"month"}),M(ce,{label:"3个月",value:"quarter"}),M(ce,{label:"6个月",value:"half_year"}),M(ce,{label:"1年",value:"year"}),M(ce,{label:"永久",value:"permanent"}),M(ce,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),"custom"===pe.validity_period?(O(),P(B,{key:0,label:"自定义结束日期"},{default:N(()=>[M(Me,{modelValue:pe.custom_end_date,"onUpdate:modelValue":$[12]||($[12]=e=>pe.custom_end_date=e),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]),_:1})):Q("",!0),M(B,{label:"备注"},{default:N(()=>[M(L,{modelValue:pe.remark,"onUpdate:modelValue":$[13]||($[13]=e=>pe.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0b5b1e3f"]]);export{le as default};
