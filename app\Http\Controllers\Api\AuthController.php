<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use OpenApi\Annotations as OA;
use App\Models\User;
use App\Services\JWTService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 认证控制器
 * 处理用户登录、注册、权限验证等
 */
class AuthController extends Controller
{
    protected $jwtService;

    public function __construct(JWTService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * 用户登录
     * 
     * @OA\Post(
     *     path="/api/v1/login",
     *     tags={"Authentication"},
     *     summary="用户登录",
     *     description="支持用户名或邮箱登录",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"username","password"},
     *             @OA\Property(property="username", type="string", example="admin", description="用户名或邮箱"),
     *             @OA\Property(property="password", type="string", example="password123", description="密码")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="登录成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="登录成功"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."),
     *                 @OA\Property(
     *                     property="user",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="username", type="string", example="admin"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="role", type="string", example="admin")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="认证失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="用户不存在"),
     *             @OA\Property(property="data", type="null")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="验证失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="用户名/邮箱不能为空"),
     *             @OA\Property(property="data", type="null")
     *         )
     *     )
     * )
     */
    public function login(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'username' => 'required|string',
                'password' => 'required|string|min:6',
            ], [
                'username.required' => '用户名/邮箱不能为空',
                'password.required' => '密码不能为空',
                'password.min' => '密码长度不能少于6位',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null,
                ], 422);
            }

            $credentials = $request->only('username', 'password');
            
            // 检查用户是否存在 - 支持用户名或邮箱登录
            $user = User::where('username', $credentials['username'])
                       ->orWhere('email', $credentials['username'])
                       ->first();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '用户不存在',
                    'data' => null,
                ], 401);
            }

            // 检查用户状态
            if (!$user->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => '账户已被禁用或已过期',
                    'data' => null,
                ], 401);
            }

            // 验证密码
            if (!Hash::check($credentials['password'], $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => '密码错误',
                    'data' => null,
                ], 401);
            }

            // 检查JWT密钥是否配置
            if (!$this->jwtService->checkJWTSecret()) {
                Log::error('JWT secret not configured properly');
                return response()->json([
                    'success' => false,
                    'message' => '系统配置错误，请联系管理员',
                    'data' => null,
                ], 500);
            }

            // 生成JWT Token
            $token = $this->jwtService->generateToken($user);
            
            if (!$token) {
                Log::error('Failed to generate JWT token for user: ' . $user->id);
                return response()->json([
                    'success' => false,
                    'message' => '登录失败，请稍后重试',
                    'data' => null,
                ], 500);
            }

            // 更新登录信息
            $user->updateLastLogin($request->ip());

            return response()->json([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => $user->load('parent') // 同时加载上级信息
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Login error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '登录失败，请稍后重试',
                'data' => null,
            ], 500);
        }
    }

    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'username' => 'required|string|max:50|unique:users,username',
                'email' => 'nullable|email|max:100|unique:users,email',
                'phone' => 'nullable|string|max:20|unique:users,phone',
                'password' => 'required|string|min:6|confirmed',
                'nickname' => 'nullable|string|max:50',
                'invite_code' => 'nullable|string|exists:users,invite_code',
            ], [
                'username.required' => '用户名不能为空',
                'username.unique' => '用户名已存在',
                'email.email' => '邮箱格式不正确',
                'email.unique' => '邮箱已存在',
                'phone.unique' => '手机号已存在',
                'password.required' => '密码不能为空',
                'password.min' => '密码长度不能少于6位',
                'password.confirmed' => '两次输入的密码不一致',
                'invite_code.exists' => '邀请码不存在',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null,
                ], 422);
            }

            // 获取邀请人信息
            $inviter = null;
            if ($request->invite_code) {
                $inviter = User::where('invite_code', $request->invite_code)->first();
            }

            // 创建用户
            $user = User::create([
                'username' => $request->username,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'nickname' => $request->nickname ?: $request->username,
                'role' => User::ROLE_USER,
                'status' => User::STATUS_ACTIVE,
                'parent_id' => $inviter ? $inviter->id : null,
            ]);

            // 生成邀请码
            $user->generateInviteCode();

            // 注册成功后直接登录：生成JWT Token
            $token = $this->jwtService->generateToken($user);
            if (!$token) {
                 Log::error('Failed to generate JWT token for new user: ' . $user->id);
                 // 即使token生成失败，也应该返回成功注册的信息
            }

            return response()->json([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'token' => $token,
                    'user' => $user,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Registration error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '注册失败，请稍后重试',
                'data' => null,
            ], 500);
        }
    }

    /**
     * 获取用户信息
     */
    public function me()
    {
        try {
            $user = auth()->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '用户未登录',
                    'data' => null,
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $user->load('parent') // 返回用户信息时也加载上级信息
            ]);

        } catch (\Exception $e) {
            Log::error('Get user info error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '获取用户信息失败',
                'data' => null,
            ], 500);
        }
    }

    /**
     * Get user specific stats for profile page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserStats(Request $request)
    {
        $user = $request->user();

        $stats = [
            'groups' => $user->wechatGroups()->count(),
            'orders' => $user->orders()->count(),
            'income' => (float)$user->commissionLogs()->sum('amount'),
            'referrals' => $user->children()->count(),
        ];

        return response()->json(['code' => 0, 'data' => $stats]);
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        try {
            $this->jwtService->invalidateToken();

            return response()->json([
                'success' => true,
                'message' => '登出成功',
                'data' => null,
            ]);

        } catch (\Exception $e) {
            Log::error('Logout error: ' . $e->getMessage());
            return response()->json([
                'success' => true,
                'message' => '登出成功', // 即使出错也返回成功，因为客户端会删除token
                'data' => null,
            ]);
        }
    }

    /**
     * 刷新令牌
     */
    public function refresh()
    {
        try {
            $newToken = $this->jwtService->refreshToken();
            
            if (!$newToken) {
                return response()->json([
                    'success' => false,
                    'message' => '令牌刷新失败',
                    'data' => null,
                ], 401);
            }

            $user = auth()->user();

            return response()->json([
                'success' => true,
                'message' => '令牌刷新成功',
                'data' => $this->jwtService->createTokenResponse($newToken, $user),
            ]);

        } catch (\Exception $e) {
            Log::error('Token refresh error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '令牌刷新失败',
                'data' => null,
            ], 401);
        }
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:6|confirmed',
            ], [
                'current_password.required' => '当前密码不能为空',
                'new_password.required' => '新密码不能为空',
                'new_password.min' => '新密码长度不能少于6位',
                'new_password.confirmed' => '两次输入的新密码不一致',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null,
                ], 422);
            }

            $user = auth()->user();

            // 验证当前密码
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => '当前密码错误',
                    'data' => null,
                ], 422);
            }

            // 更新密码
            $user->password = Hash::make($request->new_password);
            $user->save();

            return response()->json([
                'success' => true,
                'message' => '密码修改成功',
                'data' => null,
            ]);

        } catch (\Exception $e) {
            Log::error('Change password error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '密码修改失败，请稍后重试',
                'data' => null,
            ], 500);
        }
    }

    /**
     * 更新用户资料
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = auth()->user();

            $validator = Validator::make($request->all(), [
                'email' => 'nullable|email|max:100|unique:users,email,' . $user->id,
                'phone' => 'nullable|string|max:20|unique:users,phone,' . $user->id,
                'nickname' => 'nullable|string|max:50',
                'avatar' => 'nullable|string|max:255',
            ], [
                'email.email' => '邮箱格式不正确',
                'email.unique' => '邮箱已存在',
                'phone.unique' => '手机号已存在',
                'nickname.max' => '昵称长度不能超过50字符',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null,
                ], 422);
            }

            // 更新用户信息
            $user->fill($request->only(['email', 'phone', 'nickname', 'avatar']));
            $user->save();

            return response()->json([
                'success' => true,
                'message' => '资料更新成功',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'nickname' => $user->nickname,
                        'avatar' => $user->avatar,
                        'role' => $user->role,
                        'role_name' => $user->role_name,
                        'status' => $user->status,
                        'status_name' => $user->status_name,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Update profile error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '资料更新失败，请稍后重试',
                'data' => null,
            ], 500);
        }
    }
} 