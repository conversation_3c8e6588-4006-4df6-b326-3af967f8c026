<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\WechatGroup;
use App\Models\User;
use App\Models\Order;
use App\Models\GroupTemplate;

class GroupOwnerController extends Controller
{
    /**
     * 群主创建微信群
     */
    public function createGroup(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:100',
            'subtitle' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'qr_code' => 'required|string',
            'cover_image' => 'nullable|string',
            'template_id' => 'nullable|exists:group_templates,id',
            
            // 营销字段
            'red_packet_count' => 'nullable|string|max:20',
            'like_count' => 'nullable|integer|min:0',
            'message_count' => 'nullable|integer|min:0',
            'virtual_member_count' => 'nullable|integer|min:0',
            'virtual_total_income' => 'nullable|numeric|min:0',
            'virtual_order_count' => 'nullable|integer|min:0',
            
            // 群介绍和内容
            'group_intro' => 'nullable|string',
            'quick_content' => 'nullable|string',
            'member_content' => 'nullable|string',
            'ad_content' => 'nullable|string',
            'faq_content' => 'nullable|string',
            'member_reviews' => 'nullable|string',
            
            // 显示设置
            'show_virtual_data' => 'nullable|boolean',
            'auto_increase_count' => 'nullable|boolean',
            'show_member_avatars' => 'nullable|boolean',
            'show_member_reviews' => 'nullable|boolean',
            'show_real_time_stats' => 'nullable|boolean',
            
            // 营销设置
            'marketing_tags' => 'nullable|string',
            'welcome_message' => 'nullable|string',
            'group_rules' => 'nullable|string',
            'customer_service_url' => 'nullable|string',
            'customer_service_qr' => 'nullable|string',
            
            // 高级设置
            'wechat_browser_only' => 'nullable|boolean',
            'domain_protection' => 'nullable|boolean',
            'redirect_on_error' => 'nullable|boolean',
            'auto_city_replace' => 'nullable|boolean',
        ]);

        $user = $request->user();

        DB::beginTransaction();
        try {
            $groupData = [
                'user_id' => $user->id,
                'distributor_id' => $user->is_distributor ? $user->id : $user->parent_id,
                'title' => $request->title,
                'subtitle' => $request->subtitle,
                'description' => $request->description,
                'price' => $request->price,
                'qr_code' => $request->qr_code,
                'cover_image' => $request->cover_image,
                'template_id' => $request->template_id,
                'status' => 'active',
                
                // 营销字段
                'red_packet_count' => $request->red_packet_count ?: '10万+',
                'like_count' => $request->like_count ?: 324,
                'message_count' => $request->message_count ?: 341,
                'virtual_member_count' => $request->virtual_member_count ?: 500,
                'virtual_total_income' => $request->virtual_total_income ?: 0,
                'virtual_order_count' => $request->virtual_order_count ?: 100,
                
                // 内容字段
                'group_intro' => $request->group_intro,
                'quick_content' => $request->quick_content,
                'member_content' => $request->member_content,
                'ad_content' => $request->ad_content,
                'faq_content' => $request->faq_content,
                'member_reviews' => $request->member_reviews,
                
                // 显示设置
                'show_virtual_data' => $request->show_virtual_data ?? true,
                'auto_increase_count' => $request->auto_increase_count ?? false,
                'show_member_avatars' => $request->show_member_avatars ?? true,
                'show_member_reviews' => $request->show_member_reviews ?? true,
                'show_real_time_stats' => $request->show_real_time_stats ?? true,
                
                // 营销设置
                'marketing_tags' => $request->marketing_tags,
                'welcome_message' => $request->welcome_message,
                'group_rules' => $request->group_rules,
                'customer_service_url' => $request->customer_service_url,
                'customer_service_qr' => $request->customer_service_qr,
                
                // 高级设置
                'wechat_browser_only' => $request->wechat_browser_only ?? false,
                'domain_protection' => $request->domain_protection ?? false,
                'redirect_on_error' => $request->redirect_on_error ?? false,
                'auto_city_replace' => $request->auto_city_replace ?? false,
            ];

            $group = WechatGroup::create($groupData);

            DB::commit();

            return response()->json([
                'message' => '微信群创建成功',
                'group' => $group->load(['template', 'distributor'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => '创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取群主的群组列表
     */
    public function getOwnerGroups(Request $request)
    {
        $user = $request->user();
        
        $query = WechatGroup::where('user_id', $user->id)
            ->with(['template', 'distributor', 'orders'])
            ->withCount('orders')
            ->withSum('orders', 'amount');

        // 搜索过滤
        if ($request->search) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // 模板过滤
        if ($request->template_id) {
            $query->where('template_id', $request->template_id);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        // 为每个群组添加统计信息
        $groups->getCollection()->transform(function ($group) {
            $group->stats = [
                'total_orders' => $group->orders_count,
                'total_income' => $group->orders_sum_amount ?: 0,
                'today_orders' => $group->orders()->whereDate('created_at', today())->count(),
                'today_income' => $group->orders()->whereDate('created_at', today())->sum('amount'),
                'conversion_rate' => $this->calculateConversionRate($group),
                'avg_order_amount' => $group->orders_count > 0 ? ($group->orders_sum_amount / $group->orders_count) : 0,
            ];
            return $group;
        });

        return response()->json($groups);
    }

    /**
     * 群组详细信息
     */
    public function getGroupDetail(Request $request, $id)
    {
        $user = $request->user();
        
        $group = WechatGroup::where('user_id', $user->id)
            ->with(['template', 'distributor', 'orders.user'])
            ->withCount('orders')
            ->withSum('orders', 'amount')
            ->findOrFail($id);

        // 统计数据
        $stats = [
            'total_orders' => $group->orders_count,
            'total_income' => $group->orders_sum_amount ?: 0,
            'today_orders' => $group->orders()->whereDate('created_at', today())->count(),
            'today_income' => $group->orders()->whereDate('created_at', today())->sum('amount'),
            'week_orders' => $group->orders()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'week_income' => $group->orders()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->sum('amount'),
            'conversion_rate' => $this->calculateConversionRate($group),
            'avg_order_amount' => $group->orders_count > 0 ? ($group->orders_sum_amount / $group->orders_count) : 0,
        ];

        // 最近订单
        $recent_orders = $group->orders()
            ->with('user:id,name,username')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 每日统计（最近30天）
        $daily_stats = DB::table('orders')
            ->where('wechat_group_id', $id)
            ->where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(amount) as income')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return response()->json([
            'group' => $group,
            'stats' => $stats,
            'recent_orders' => $recent_orders,
            'daily_stats' => $daily_stats,
        ]);
    }

    /**
     * 更新群组信息
     */
    public function updateGroup(Request $request, $id)
    {
        $user = $request->user();
        $group = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        $request->validate([
            'title' => 'sometimes|required|string|max:100',
            'subtitle' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'price' => 'sometimes|required|numeric|min:0',
            'qr_code' => 'sometimes|required|string',
            'cover_image' => 'nullable|string',
            'status' => 'sometimes|required|in:active,inactive',
        ]);

        $group->update($request->all());

        return response()->json([
            'message' => '群组更新成功',
            'group' => $group->load(['template', 'distributor'])
        ]);
    }

    /**
     * 上传群二维码
     */
    public function uploadQRCode(Request $request)
    {
        $request->validate([
            'qr_code' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            $file = $request->file('qr_code');
            $fileName = 'qrcodes/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public', $fileName);
            
            $url = Storage::url($fileName);

            return response()->json([
                'message' => '二维码上传成功',
                'url' => $url,
                'path' => $fileName
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => '上传失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 上传群封面图片
     */
    public function uploadCoverImage(Request $request)
    {
        $request->validate([
            'cover_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            $file = $request->file('cover_image');
            $fileName = 'covers/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public', $fileName);
            
            $url = Storage::url($fileName);

            return response()->json([
                'message' => '封面图片上传成功',
                'url' => $url,
                'path' => $fileName
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => '上传失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取可用模板
     */
    public function getAvailableTemplates(Request $request)
    {
        $user = $request->user();
        
        $query = GroupTemplate::where('status', 'active');

        // 如果用户是分销员，检查模板权限
        if ($user->is_distributor) {
            $query->where(function($q) use ($user) {
                $q->where('is_public', true)
                  ->orWhereJsonContains('allowed_distributors', $user->id)
                  ->orWhereJsonContains('allowed_distribution_groups', $user->distribution_group_id);
            });
        } else {
            // 普通用户只能使用公开模板
            $query->where('is_public', true);
        }

        $templates = $query->get();

        return response()->json($templates);
    }

    /**
     * 使用模板创建群组
     */
    public function createGroupFromTemplate(Request $request, $templateId)
    {
        $user = $request->user();
        $template = GroupTemplate::findOrFail($templateId);

        // 检查模板权限
        if (!$template->is_public && $user->is_distributor && 
            !in_array($user->id, $template->allowed_distributors ?? []) &&
            !in_array($user->distribution_group_id, $template->allowed_distribution_groups ?? [])) {
            return response()->json(['error' => '无权使用此模板'], 403);
        }

        $request->validate([
            'title' => 'required|string|max:100',
            'price' => 'required|numeric|min:0',
            'qr_code' => 'required|string',
        ]);

        DB::beginTransaction();
        try {
            $groupData = [
                'user_id' => $user->id,
                'distributor_id' => $user->is_distributor ? $user->id : $user->parent_id,
                'title' => $request->title,
                'subtitle' => $template->subtitle,
                'description' => $template->description,
                'price' => $request->price,
                'qr_code' => $request->qr_code,
                'cover_image' => $template->image,
                'template_id' => $template->id,
                'status' => 'active',
                
                // 从模板复制营销数据
                'red_packet_count' => $template->marketing_data['red_packet_count'] ?? '10万+',
                'like_count' => $template->marketing_data['like_count'] ?? 324,
                'message_count' => $template->marketing_data['message_count'] ?? 341,
                'virtual_member_count' => $template->marketing_data['virtual_member_count'] ?? 500,
                'virtual_total_income' => $template->marketing_data['virtual_total_income'] ?? 0,
                'virtual_order_count' => $template->marketing_data['virtual_order_count'] ?? 100,
                
                // 从模板复制内容
                'group_intro' => $template->content['group_intro'] ?? '',
                'quick_content' => $template->content['quick_content'] ?? '',
                'member_content' => $template->content['member_content'] ?? '',
                'ad_content' => $template->content['ad_content'] ?? '',
                'faq_content' => $template->content['faq_content'] ?? '',
                'member_reviews' => $template->content['member_reviews'] ?? '',
            ];

            $group = WechatGroup::create($groupData);

            DB::commit();

            return response()->json([
                'message' => '基于模板创建群组成功',
                'group' => $group->load(['template', 'distributor'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => '创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 删除群组
     */
    public function deleteGroup(Request $request, $id)
    {
        $user = $request->user();
        $group = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        // 检查是否有订单
        if ($group->orders()->count() > 0) {
            return response()->json(['error' => '存在订单记录，无法删除'], 400);
        }

        $group->delete();

        return response()->json(['message' => '群组删除成功']);
    }

    /**
     * 复制群组
     */
    public function duplicateGroup(Request $request, $id)
    {
        $user = $request->user();
        $originalGroup = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        $newGroup = $originalGroup->replicate();
        $newGroup->title = $originalGroup->title . ' (副本)';
        $newGroup->qr_code = ''; // 需要重新设置二维码
        $newGroup->created_at = now();
        $newGroup->updated_at = now();
        $newGroup->save();

        return response()->json([
            'message' => '群组复制成功',
            'group' => $newGroup->load(['template', 'distributor'])
        ]);
    }

    /**
     * 批量操作群组
     */
    public function batchOperation(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'group_ids' => 'required|array',
            'group_ids.*' => 'exists:wechat_groups,id',
        ]);

        $user = $request->user();
        $groups = WechatGroup::where('user_id', $user->id)
            ->whereIn('id', $request->group_ids);

        switch ($request->action) {
            case 'activate':
                $groups->update(['status' => 'active']);
                $message = '群组已批量启用';
                break;
                
            case 'deactivate':
                $groups->update(['status' => 'inactive']);
                $message = '群组已批量禁用';
                break;
                
            case 'delete':
                // 检查是否有订单
                $hasOrders = $groups->whereHas('orders')->exists();
                if ($hasOrders) {
                    return response()->json(['error' => '存在订单记录的群组无法删除'], 400);
                }
                $groups->delete();
                $message = '群组已批量删除';
                break;
        }

        return response()->json(['message' => $message]);
    }

    // 私有方法
    private function calculateConversionRate($group)
    {
        // 这里可以根据实际的访问统计数据计算转化率
        // 暂时返回模拟数据
        $visits = rand(100, 1000);
        $orders = $group->orders_count ?? 0;
        
        return $visits > 0 ? round(($orders / $visits) * 100, 2) : 0;
    }

    /**
     * 获取群主统计数据
     */
    public function getStatistics(Request $request)
    {
        $user = $request->user();
        
        // 基础统计
        $totalGroups = WechatGroup::where('user_id', $user->id)->count();
        $activeGroups = WechatGroup::where('user_id', $user->id)->where('status', 1)->count();
        
        // 订单统计
        $totalOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->count();
        
        $paidOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where('status', 'paid')->count();
        
        $totalRevenue = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where('status', 'paid')->sum('amount');
        
        // 今日统计
        $todayOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->whereDate('created_at', today())->count();
        
        $todayRevenue = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->whereDate('created_at', today())->where('status', 'paid')->sum('amount');
        
        // 本月统计
        $thisMonthOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->whereMonth('created_at', now()->month)->count();
        
        $thisMonthRevenue = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->whereMonth('created_at', now()->month)->where('status', 'paid')->sum('amount');
        
        // 转化率
        $conversionRate = $totalOrders > 0 ? round(($paidOrders / $totalOrders) * 100, 2) : 0;
        
        $statistics = [
            'overview' => [
                'total_groups' => $totalGroups,
                'active_groups' => $activeGroups,
                'total_orders' => $totalOrders,
                'paid_orders' => $paidOrders,
                'total_revenue' => $totalRevenue,
                'conversion_rate' => $conversionRate,
            ],
            'today' => [
                'orders' => $todayOrders,
                'revenue' => $todayRevenue,
            ],
            'this_month' => [
                'orders' => $thisMonthOrders,
                'revenue' => $thisMonthRevenue,
            ],
            'top_groups' => WechatGroup::where('user_id', $user->id)
                ->withCount('orders')
                ->withSum('orders', 'amount')
                ->orderBy('orders_count', 'desc')
                ->limit(5)
                ->get(['id', 'title', 'price', 'status']),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }
}