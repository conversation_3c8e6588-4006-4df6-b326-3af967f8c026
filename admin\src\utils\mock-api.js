// 模拟API响应，用于开发环境
export const mockApiResponses = {
  // 用户认证相关
  '/admin/auth/user': {
    code: 200,
    message: 'success',
    data: {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      name: '系统管理员',
      avatar: '/avatars/admin.jpg',
      roles: ['admin'],
      permissions: [
        'system.view', 'system.edit', 'users.view', 'users.edit',
        'groups.view', 'groups.edit', 'orders.view', 'orders.edit'
      ],
      created_at: '2024-01-01T00:00:00Z',
      last_login_at: new Date().toISOString()
    }
  },

  '/admin/auth/login': {
    code: 200,
    message: '登录成功',
    data: {
      token: 'mock_token_' + Date.now(),
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        name: '系统管理员',
        avatar: '/avatars/admin.jpg',
        roles: ['admin'],
        permissions: [
          'system.view', 'system.edit', 'users.view', 'users.edit',
          'groups.view', 'groups.edit', 'orders.view', 'orders.edit'
        ]
      }
    }
  },

  '/admin/auth/logout': {
    code: 200,
    message: '退出成功',
    data: null
  },

  '/admin/auth/refresh': {
    code: 200,
    message: '刷新成功',
    data: {
      token: 'mock_token_' + Date.now()
    }
  },

  // 防红系统API - 使用简单路径格式
  '/admin/anti-block/stats': {
    code: 200,
    message: 'success',
    data: {
      overview: {
        total_domains: 15,
        healthy_domains: 12,
        warning_domains: 2,
        blocked_domains: 1,
        total_short_links: 1248,
        active_short_links: 1156,
        total_clicks: 45678,
        today_clicks: 892
      },
      domain_health: {
        excellent: 8,  // 90-100分
        good: 4,       // 80-89分
        warning: 2,    // 60-79分
        poor: 1        // 0-59分
      },
      recent_activity: [
        {
          id: 1,
          type: 'domain_check',
          domain: 'safe-domain-1.com',
          status: 'healthy',
          health_score: 95,
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          type: 'short_link_created',
          short_code: 'A6X8Y9Z0',
          original_url: 'https://example.com/landing/group/1',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        }
      ],
      performance_trend: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
        datasets: [
          {
            label: '点击量',
            data: [45, 78, 156, 234, 189, 123],
            borderColor: '#409eff',
            backgroundColor: 'rgba(64, 158, 255, 0.1)'
          },
          {
            label: '转化量',
            data: [12, 23, 45, 67, 54, 34],
            borderColor: '#67c23a',
            backgroundColor: 'rgba(103, 194, 58, 0.1)'
          }
        ]
      }
    }
  },

  '/admin/anti-block/domains': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          domain: 'safe-domain-1.com',
          domain_type: 'redirect',
          health_score: 95,
          status: 'normal',
          last_check_time: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 156,
          today_clicks: 234,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          domain: 'safe-domain-2.com',
          domain_type: 'landing',
          health_score: 88,
          status: 'normal',
          last_check_time: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 89,
          today_clicks: 167,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          id: 3,
          domain: 'api-domain.com',
          domain_type: 'api',
          health_score: 92,
          status: 'normal',
          last_check_time: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 45,
          today_clicks: 78,
          created_at: '2024-01-03T00:00:00Z'
        }
      ],
      total: 3,
      current_page: 1,
      per_page: 20,
      total_pages: 1
    }
  },

  '/admin/anti-block/short-links': {
    code: 200,
    message: 'success',
    data: {
      short_links: [
        {
          id: 1,
          short_code: 'A6X8Y9Z0',
          full_url: 'https://t.cn/A6X8Y9Z0',
          original_url: 'https://example.com/landing/group/1',
          domain: 'safe-domain-1.com',
          link_type: 'promotion',
          status: 'active',
          click_count: 234,
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          last_click_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 1,
        total_pages: 1
      }
    }
  },

  // 群组相关API
  'GET:/admin/groups': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          title: '北京同城交友群',
          price: 9.9,
          description: '北京地区同城交友，真实靠谱',
          qr_code: '/images/qr1.jpg',
          status: 1,
          auto_city_replace: 1,
          read_count_display: '10万+',
          like_count: 888,
          want_see_count: 666,
          button_title: '立即加入群聊',
          avatar_library: 'qq',
          display_type: 1,
          wx_accessible: 1,
          virtual_members: 156,
          virtual_orders: 89,
          virtual_income: 2580.50,
          today_views: 1200,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          title: '上海商务交流群',
          price: 19.9,
          description: '上海商务人士交流平台',
          qr_code: '/images/qr2.jpg',
          status: 1,
          auto_city_replace: 1,
          read_count_display: '8万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '加入商务群',
          avatar_library: 'za',
          display_type: 1,
          wx_accessible: 1,
          virtual_members: 203,
          virtual_orders: 156,
          virtual_income: 4580.80,
          today_views: 1800,
          created_at: '2024-01-02T00:00:00Z'
        }
      ],
      total: 2,
      per_page: 20,
      current_page: 1
    }
  },

  // 创建群组
  'POST:/admin/groups': {
    code: 200,
    message: '群组创建成功！推广链接和落地页已自动生成',
    data: {
      id: Date.now(),
      title: '新创建的群组',
      price: 0,
      status: 1,
      created_at: new Date().toISOString(),

      // 付费后内容配置
      paid_content_type: 'qr_code',
      paid_content: {
        qr_code: null, // 入群二维码（用户上传）
        images: [],
        link: null,
        link_desc: null,
        document_content: null,
        video_url: null,
        video_title: null,
        video_desc: null
      },

      // 落地页信息
      landing_page: {
        id: Date.now() + 1,
        url: `https://landing.example.com/group/${Date.now()}`,
        preview_url: `https://preview.landing.example.com/group/${Date.now()}`,
        template_id: 1,
        status: 'active',
        created_at: new Date().toISOString()
      },

      // 推广链接和推广二维码（用于营销推广）
      promotion_links: [
        {
          id: Date.now() + 2,
          type: 'primary',
          name: '主推广链接',
          url: `https://promo.example.com/g/${Date.now()}`,
          short_url: `https://t.cn/${Math.random().toString(36).substr(2, 8)}`,
          // 推广二维码 - 扫码后跳转到落地页
          promotion_qr_code: `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=https://promo.example.com/g/${Date.now()}`,
          clicks: 0,
          conversions: 0,
          created_at: new Date().toISOString()
        }
      ],

      // 支付配置
      payment_config: {
        methods: ['wechat', 'alipay'],
        merchant_id: 'MOCK_MERCHANT_' + Date.now(),
        callback_url: `https://api.example.com/callback/payment/${Date.now()}`
      },

      // 防红系统配置
      anti_block_config: {
        enabled: true,
        domain_rotation: true,
        backup_domains: [
          `backup1.example.com`,
          `backup2.example.com`
        ],
        detection_interval: 300 // 5分钟检测一次
      }
    }
  },

  // 分类列表
  '/admin/categories': {
    code: 200,
    message: 'success',
    data: {
      data: [
        { id: 1, name: '同城交友', slug: 'dating' },
        { id: 2, name: '商务交流', slug: 'business' },
        { id: 3, name: '学习教育', slug: 'education' },
        { id: 4, name: '兴趣爱好', slug: 'hobby' }
      ]
    }
  },

  // 微信群组列表（营销配置页面使用）
  'GET:/api/v1/wechat-groups': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          title: '北京商务精英交流群',
          price: 99.00,
          avatar_library: 'qq',
          display_type: 1,
          virtual_members: 328,
          virtual_orders: 89,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: 2,
          title: '上海副业赚钱交流群',
          price: 58.00,
          avatar_library: 'default',
          display_type: 1,
          virtual_members: 267,
          virtual_orders: 156,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-10T14:30:00Z'
        },
        {
          id: 3,
          title: '深圳学习成长群',
          price: 29.00,
          avatar_library: 'qq',
          display_type: 2,
          virtual_members: 145,
          virtual_orders: 67,
          wx_accessible: 0,
          auto_city_replace: 0,
          created_at: '2024-01-20T09:15:00Z'
        },
        {
          id: 4,
          title: '广州健身运动群',
          price: 39.00,
          avatar_library: 'default',
          display_type: 1,
          virtual_members: 189,
          virtual_orders: 78,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-25T16:45:00Z'
        },
        {
          id: 5,
          title: '杭州创业交流群',
          price: 88.00,
          avatar_library: 'qq',
          display_type: 1,
          virtual_members: 234,
          virtual_orders: 123,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-12T11:20:00Z'
        }
      ],
      total: 5,
      current_page: 1,
      per_page: 10,
      last_page: 1
    }
  },

  // 微信群组列表（备用路径）
  'GET:/wechat-groups': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          title: '北京商务精英交流群',
          price: 99.00,
          avatar_library: 'qq',
          display_type: 1,
          virtual_members: 328,
          virtual_orders: 89,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: 2,
          title: '上海副业赚钱交流群',
          price: 58.00,
          avatar_library: 'default',
          display_type: 1,
          virtual_members: 267,
          virtual_orders: 156,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-10T14:30:00Z'
        },
        {
          id: 3,
          title: '深圳学习成长群',
          price: 29.00,
          avatar_library: 'qq',
          display_type: 2,
          virtual_members: 145,
          virtual_orders: 67,
          wx_accessible: 0,
          auto_city_replace: 0,
          created_at: '2024-01-20T09:15:00Z'
        },
        {
          id: 4,
          title: '广州健身运动群',
          price: 39.00,
          avatar_library: 'default',
          display_type: 1,
          virtual_members: 189,
          virtual_orders: 78,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-25T16:45:00Z'
        },
        {
          id: 5,
          title: '杭州创业交流群',
          price: 88.00,
          avatar_library: 'qq',
          display_type: 1,
          virtual_members: 234,
          virtual_orders: 123,
          wx_accessible: 1,
          auto_city_replace: 1,
          created_at: '2024-01-12T11:20:00Z'
        }
      ],
      total: 5,
      current_page: 1,
      per_page: 10,
      last_page: 1
    }
  },

  // 微信群组列表（兼容旧路径）
  'GET:/admin/wechat-groups': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          title: '北京同城交友群',
          price: 9.9,
          description: '北京地区同城交友，真实靠谱',
          qr_code: '/images/qr1.jpg',
          status: 1,
          auto_city_replace: 1,
          read_count_display: '10万+',
          like_count: 888,
          want_see_count: 666,
          button_title: '立即加入群聊',
          avatar_library: 'qq',
          display_type: 1,
          wx_accessible: 1,
          virtual_members: 156,
          virtual_orders: 89,
          virtual_income: 2580.50,
          today_views: 1200,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          title: '上海商务交流群',
          price: 19.9,
          description: '上海商务人士交流平台',
          qr_code: '/images/qr2.jpg',
          status: 1,
          auto_city_replace: 1,
          read_count_display: '8万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '加入商务群',
          avatar_library: 'za',
          display_type: 1,
          wx_accessible: 1,
          virtual_members: 203,
          virtual_orders: 156,
          virtual_income: 4580.80,
          today_views: 1800,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          id: 3,
          title: '深圳科技创业群',
          price: 29.9,
          description: '深圳科技创业者交流平台',
          qr_code: '/images/qr3.jpg',
          status: 1,
          auto_city_replace: 1,
          read_count_display: '12万+',
          like_count: 1500,
          want_see_count: 1200,
          button_title: '加入创业群',
          avatar_library: 'qq',
          display_type: 1,
          wx_accessible: 1,
          virtual_members: 289,
          virtual_orders: 198,
          virtual_income: 6890.20,
          today_views: 2100,
          created_at: '2024-01-03T00:00:00Z'
        }
      ],
      total: 3,
      per_page: 20,
      current_page: 1
    }
  },

  // 营销配置相关API
  'GET:/admin/marketing/config': {
    code: 200,
    message: 'success',
    data: {
      basic: {
        read_count_display: '10万+',
        like_count: 888,
        want_see_count: 666,
        button_title: '立即加入群聊',
        avatar_library: 'qq',
        wx_accessible: 1,
        display_type: 1
      },
      content: {
        group_intro_title: '群简介',
        group_intro_content: '这是一个优质的交流群',
        faq_title: '常见问题',
        faq_content: '问题1----答案1\n问题2----答案2',
        member_reviews: '评论1----10\n评论2----8'
      },
      service: {
        show_customer_service: 2,
        customer_service_title: 'VIP专属客服',
        customer_service_desc: '有问题请联系客服',
        customer_service_avatar: '/avatars/service.jpg',
        customer_service_qr: '/qr/service.jpg',
        ad_qr_code: '/qr/ad.jpg'
      },
      advanced: {
        auto_city_replace: 1,
        city_insert_strategy: 'auto',
        virtual_members: 156,
        virtual_orders: 89,
        virtual_income: 2580.50,
        today_views: 1200
      }
    }
  },

  // 营销模板列表（营销配置页面使用）
  'GET:/api/v1/marketing-templates': {
    code: 200,
    message: 'success',
    data: [
      {
        id: 1,
        name: '商务交流模板',
        description: '适用于商务人士交流的营销模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '社交娱乐模板',
        description: '适用于社交娱乐的营销模板',
        config: {
          read_count_display: '3万+',
          like_count: 800,
          want_see_count: 600,
          button_title: '加入娱乐群',
          group_intro_title: '娱乐交流群',
          group_intro_content: '轻松愉快的社交环境，分享生活乐趣',
          virtual_members: 120,
          virtual_orders: 60
        },
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '学习教育模板',
        description: '适用于学习教育的营销模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        },
        created_at: '2024-01-03T00:00:00Z'
      },
      {
        id: 4,
        name: '副业赚钱模板',
        description: '适用于副业创业的营销模板',
        config: {
          read_count_display: '12万+',
          like_count: 3659,
          want_see_count: 665,
          button_title: '加入群，学习更多副业知识',
          group_intro_title: '副业赚钱交流群',
          group_intro_content: '分享副业项目，交流赚钱经验，实现财务自由',
          virtual_members: 267,
          virtual_orders: 156
        },
        created_at: '2024-01-04T00:00:00Z'
      },
      {
        id: 5,
        name: '健身运动模板',
        description: '适用于健身运动的营销模板',
        config: {
          read_count_display: '6万+',
          like_count: 2234,
          want_see_count: 445,
          button_title: '加入群，一起健身运动',
          group_intro_title: '健身运动交流群',
          group_intro_content: '分享健身经验，制定运动计划，一起变得更健康',
          virtual_members: 189,
          virtual_orders: 78
        },
        created_at: '2024-01-05T00:00:00Z'
      }
    ]
  },

  // 营销模板列表（备用路径）
  'GET:/marketing-templates': {
    code: 200,
    message: 'success',
    data: [
      {
        id: 1,
        name: '商务交流模板',
        description: '适用于商务人士交流的营销模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '社交娱乐模板',
        description: '适用于社交娱乐的营销模板',
        config: {
          read_count_display: '3万+',
          like_count: 800,
          want_see_count: 600,
          button_title: '加入娱乐群',
          group_intro_title: '娱乐交流群',
          group_intro_content: '轻松愉快的社交环境，分享生活乐趣',
          virtual_members: 120,
          virtual_orders: 60
        },
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '学习教育模板',
        description: '适用于学习教育的营销模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        },
        created_at: '2024-01-03T00:00:00Z'
      },
      {
        id: 4,
        name: '副业赚钱模板',
        description: '适用于副业创业的营销模板',
        config: {
          read_count_display: '12万+',
          like_count: 3659,
          want_see_count: 665,
          button_title: '加入群，学习更多副业知识',
          group_intro_title: '副业赚钱交流群',
          group_intro_content: '分享副业项目，交流赚钱经验，实现财务自由',
          virtual_members: 267,
          virtual_orders: 156
        },
        created_at: '2024-01-04T00:00:00Z'
      },
      {
        id: 5,
        name: '健身运动模板',
        description: '适用于健身运动的营销模板',
        config: {
          read_count_display: '6万+',
          like_count: 2234,
          want_see_count: 445,
          button_title: '加入群，一起健身运动',
          group_intro_title: '健身运动交流群',
          group_intro_content: '分享健身经验，制定运动计划，一起变得更健康',
          virtual_members: 189,
          virtual_orders: 78
        },
        created_at: '2024-01-05T00:00:00Z'
      }
    ]
  },

  // 营销模板列表（兼容旧路径）
  'GET:/admin/marketing-templates': {
    code: 200,
    message: 'success',
    data: [
      {
        id: 1,
        name: '商务交流模板',
        description: '适用于商务人士交流的营销模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '社交娱乐模板',
        description: '适用于社交娱乐的营销模板',
        config: {
          read_count_display: '10万+',
          like_count: 2000,
          want_see_count: 1500,
          button_title: '快来聊天吧',
          group_intro_title: '欢乐聊天群',
          group_intro_content: '轻松愉快的聊天环境，结识更多朋友',
          virtual_members: 200,
          virtual_orders: 120
        },
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '学习教育模板',
        description: '适用于学习教育的营销模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        },
        created_at: '2024-01-03T00:00:00Z'
      },
      {
        id: 4,
        name: '副业赚钱模板',
        description: '适用于副业创业的营销模板',
        config: {
          read_count_display: '12万+',
          like_count: 3659,
          want_see_count: 665,
          button_title: '加入群，学习更多副业知识',
          group_intro_title: '副业赚钱交流群',
          group_intro_content: '分享副业项目，交流赚钱经验，实现财务自由',
          virtual_members: 267,
          virtual_orders: 156
        },
        created_at: '2024-01-04T00:00:00Z'
      },
      {
        id: 5,
        name: '健身运动模板',
        description: '适用于健身运动的营销模板',
        config: {
          read_count_display: '6万+',
          like_count: 2234,
          want_see_count: 445,
          button_title: '加入群，一起健身运动',
          group_intro_title: '健身运动交流群',
          group_intro_content: '分享健身经验，制定运动计划，一起变得更健康',
          virtual_members: 189,
          virtual_orders: 78
        },
        created_at: '2024-01-05T00:00:00Z'
      }
    ]
  },

  'GET:/admin/marketing/templates': {
    code: 200,
    message: 'success',
    data: [
      {
        id: 1,
        name: '商务交流模板',
        description: '适用于商务人士交流的营销模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '社交娱乐模板',
        description: '适用于社交娱乐的营销模板',
        config: {
          read_count_display: '10万+',
          like_count: 2000,
          want_see_count: 1500,
          button_title: '快来聊天吧',
          group_intro_title: '欢乐聊天群',
          group_intro_content: '轻松愉快的聊天环境，结识更多朋友',
          virtual_members: 200,
          virtual_orders: 120
        },
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        name: '学习教育模板',
        description: '适用于学习教育的营销模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        },
        created_at: '2024-01-03T00:00:00Z'
      }
    ]
  },

  // 系统设置相关
  '/admin/system/settings': {
    code: 200,
    message: 'success',
    data: {
      site_name: '晨鑫流量变现系统',
      site_description: '智能社群营销与多级分销平台',
      site_logo: '/logo.png',
      site_favicon: '/favicon.ico',
      admin_email: '<EMAIL>',
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      maintenance_mode: false,
      registration_enabled: true,
      email_verification: true,
      sms_verification: false,
      max_upload_size: 10,
      allowed_file_types: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
      cache_enabled: true,
      cache_ttl: 3600,
      debug_mode: false,
      log_level: 'info'
    }
  },

  // 文件上传API
  'POST:/api/upload': (req) => {
    // 模拟上传延迟
    return new Promise((resolve) => {
      setTimeout(() => {
        const fileType = req.body?.type || 'image';
        let mockUrl;

        // 根据文件类型生成不同的Mock URL
        switch (fileType) {
          case 'image':
            mockUrl = `https://picsum.photos/400/300?random=${Math.random()}`;
            break;
          case 'video':
            mockUrl = `https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4`;
            break;
          default:
            mockUrl = `https://picsum.photos/400/300?random=${Math.random()}`;
        }

        resolve({
          success: true,
          message: '上传成功',
          data: {
            url: mockUrl,
            filename: `uploaded_${fileType}_${Date.now()}.jpg`,
            size: Math.floor(Math.random() * 2000000) + 100000, // 100KB - 2MB
            type: fileType === 'image' ? 'image/jpeg' : 'video/mp4',
            upload_time: new Date().toISOString()
          }
        });
      }, 1000 + Math.random() * 2000); // 1-3秒延迟模拟真实上传
    });
  },

  // 系统信息
  '/admin/system/info': {
    code: 200,
    message: 'success',
    data: {
      php_version: '8.2.9',
      laravel_version: '10.x',
      db_response_time: 15,
      memory_usage: 45,
      disk_usage: 32,
      cache_status: 'healthy',
      server_time: new Date().toISOString(),
      uptime: '15天 8小时 32分钟'
    }
  },

  // 健康检查
  '/admin/system/health': {
    code: 200,
    message: 'success',
    data: {
      components: {
        php_environment: {
          status: 'pass',
          message: 'PHP环境配置正常',
          details: {
            version: '8.2.9',
            extensions: ['pdo', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json']
          }
        },
        database: {
          status: 'pass',
          message: '数据库连接正常',
          details: {
            type: 'MySQL',
            version: '8.0.33',
            response_time: '15ms'
          }
        },
        cache: {
          status: 'pass',
          message: '缓存系统运行正常',
          details: {
            driver: 'Redis',
            status: 'connected'
          }
        },
        storage: {
          status: 'pass',
          message: '存储系统正常',
          details: {
            disk_usage: '32%',
            available_space: '68GB'
          }
        }
      }
    }
  },

  // 代理商信息
  '/admin/agent/my': {
    code: 200,
    message: 'success',
    data: {
      id: 1,
      agent_code: 'AG001',
      agent_level: 'advanced',
      agent_level_text: '高级代理',
      agent_type: 'individual',
      agent_type_text: '个人代理',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      status: 'active',
      created_at: '2024-01-01T00:00:00Z'
    }
  },

  // 代理商统计
  '/admin/agent/my-stats': {
    code: 200,
    message: 'success',
    data: {
      total_users: 156,
      total_commission: 12580.50,
      monthly_commission: 3250.80,
      child_agents_count: 8,
      user_growth_rate: 15.6,
      commission_growth_rate: 23.4
    }
  },

  // 支付配置
  '/admin/system/payment/config': {
    code: 200,
    message: 'success',
    data: {
      alipay: {
        enabled: false,
        app_id: '',
        private_key: '',
        public_key: '',
        gateway: 'https://openapi.alipay.com/gateway.do',
        notify_url: '',
        return_url: ''
      },
      wechat: {
        enabled: false,
        app_id: '',
        mch_id: '',
        key: '',
        cert_path: '',
        notify_url: ''
      },
      easypay: {
        enabled: true,
        pid: 'EP123456',
        key: '****示例密钥****',
        api_url: 'https://api.easypay.com',
        notify_url: 'https://yourdomain.com/api/payment/easypay/notify',
        return_url: 'https://yourdomain.com/payment/success'
      },
      bank: {
        enabled: false,
        supported_banks: ['ICBC', 'ABC', 'BOC', 'CCB'],
        fee_rate: 0.6,
        min_amount: 0.01,
        max_amount: 50000
      }
    }
  },

  // 支付统计
  '/admin/system/payment/stats': {
    code: 200,
    message: 'success',
    data: {
      total_amount: 1234567.89,
      total_orders: 5678,
      success_rate: 98.5,
      avg_time: 2.3
    }
  },

  // 支付方式切换
  '/admin/system/payment/alipay/toggle': {
    code: 200,
    message: 'success',
    data: { enabled: true }
  },
  '/admin/system/payment/wechat/toggle': {
    code: 200,
    message: 'success',
    data: { enabled: true }
  },
  '/admin/system/payment/easypay/toggle': {
    code: 200,
    message: 'success',
    data: { enabled: true }
  },
  '/admin/system/payment/bank/toggle': {
    code: 200,
    message: 'success',
    data: { enabled: true }
  },

  // 支付通道测试
  '/admin/system/payment/alipay/test': {
    code: 200,
    message: 'success',
    data: {
      test_result: 'success',
      test_time: new Date().toISOString(),
      response_time: '1.23s',
      test_order_no: `test_alipay_${Date.now()}`
    }
  },
  '/admin/system/payment/wechat/test': {
    code: 200,
    message: 'success',
    data: {
      test_result: 'success',
      test_time: new Date().toISOString(),
      response_time: '0.98s',
      test_order_no: `test_wechat_${Date.now()}`
    }
  },
  '/admin/system/payment/easypay/test': {
    code: 200,
    message: 'success',
    data: {
      test_result: 'success',
      test_time: new Date().toISOString(),
      response_time: '1.56s',
      test_order_no: `test_easypay_${Date.now()}`
    }
  },

  // 支付配置更新
  'PUT:/admin/system/payment/config': {
    code: 200,
    message: '支付配置更新成功',
    data: { updated: true }
  },

  // 安全设置更新
  'PUT:/admin/system/payment/security': {
    code: 200,
    message: '安全设置更新成功',
    data: { updated: true }
  },

  // 营销配置相关API（具体群组）
  'GET:/admin/groups/1/marketing-config': {
    code: 200,
    message: 'success',
    data: {
      basic: {
        read_count_display: '10万+',
        like_count: 888,
        want_see_count: 666,
        button_title: '立即加入群聊',
        avatar_library: 'qq',
        wx_accessible: 1,
        display_type: 1
      },
      content: {
        group_intro_title: '群简介',
        group_intro_content: '这是一个优质的交流群',
        faq_title: '常见问题',
        faq_content: '问题1----答案1\n问题2----答案2',
        member_reviews: '评论1----10\n评论2----8'
      },
      service: {
        show_customer_service: 2,
        customer_service_title: 'VIP专属客服',
        customer_service_desc: '有问题请联系客服',
        customer_service_avatar: '/avatars/service.jpg',
        customer_service_qr: '/qr/service.jpg',
        ad_qr_code: '/qr/ad.jpg'
      },
      advanced: {
        auto_city_replace: 1,
        city_insert_strategy: 'auto',
        virtual_members: 156,
        virtual_orders: 89,
        virtual_income: 2580.50,
        today_views: 1200
      }
    }
  },

  // 更新营销配置
  'PUT:/admin/groups/1/marketing-config': {
    code: 200,
    message: '营销配置更新成功',
    data: { success: true }
  },

  // 群组预览
  'GET:/admin/groups/1/preview': {
    code: 200,
    message: 'success',
    data: {
      title: '北京同城交友群',
      price: 9.9,
      read_count_display: '10万+',
      like_count: 888,
      want_see_count: 666,
      button_title: '立即加入群聊',
      group_intro_title: '群简介',
      group_intro_content: '这是一个优质的交流群',
      virtual_members: [
        {
          nickname: '最美的太阳花',
          avatar: '/face/qq/1.jpg',
          join_time: '2024-01-01 10:30:00'
        },
        {
          nickname: '孤海的浪漫',
          avatar: '/face/qq/2.jpg',
          join_time: '2024-01-01 11:15:00'
        },
        {
          nickname: '薰衣草',
          avatar: '/face/qq/3.jpg',
          join_time: '2024-01-01 12:00:00'
        }
      ]
    }
  },

  // 城市定位测试
  'POST:/admin/groups/1/test-city': {
    code: 200,
    message: 'success',
    data: {
      original_title: '北京同城交友群',
      replaced_title: '上海同城交友群',
      strategy_name: '智能判断',
      test_city: '上海'
    }
  },

  // 生成虚拟成员
  'POST:/admin/groups/1/virtual-members': {
    code: 200,
    message: 'success',
    data: [
      {
        nickname: '最美的太阳花',
        avatar: '/face/qq/1.jpg',
        join_time: '2024-01-01 10:30:00'
      },
      {
        nickname: '孤海的浪漫',
        avatar: '/face/qq/2.jpg',
        join_time: '2024-01-01 11:15:00'
      },
      {
        nickname: '薰衣草',
        avatar: '/face/qq/3.jpg',
        join_time: '2024-01-01 12:00:00'
      }
    ]
  },

  // 批量应用模板
  'POST:/admin/groups/batch-marketing': {
    code: 200,
    message: '批量配置应用成功',
    data: { success: true, affected_count: 3 }
  },

  // 获取付费后内容（需要验证支付状态）
  'GET:/api/v1/groups/1/paid-content': {
    code: 200,
    message: 'success',
    data: {
      group_id: 1,
      group_title: '北京商务精英交流群',
      paid_content_type: 'qr_code',
      content: {
        qr_code: 'https://example.com/qr-codes/group-1-entry.png',
        qr_code_desc: '请使用微信扫描二维码加入群组',
        access_instructions: [
          '1. 扫描上方二维码',
          '2. 添加群主微信',
          '3. 发送付费凭证截图',
          '4. 等待群主邀请入群'
        ]
      },
      payment_verified: true,
      access_time: new Date().toISOString()
    }
  },

  // 获取付费后内容 - 图片类型
  'GET:/api/v1/groups/2/paid-content': {
    code: 200,
    message: 'success',
    data: {
      group_id: 2,
      group_title: '专业资料分享群',
      paid_content_type: 'image',
      content: {
        images: [
          'https://example.com/resources/image1.jpg',
          'https://example.com/resources/image2.jpg',
          'https://example.com/resources/image3.jpg'
        ],
        image_desc: '专业学习资料图片合集',
        download_instructions: '长按图片保存到相册'
      },
      payment_verified: true,
      access_time: new Date().toISOString()
    }
  },

  // 获取付费后内容 - 链接类型
  'GET:/api/v1/groups/3/paid-content': {
    code: 200,
    message: 'success',
    data: {
      group_id: 3,
      group_title: '资源下载群',
      paid_content_type: 'link',
      content: {
        download_link: 'https://example.com/downloads/premium-resources.zip',
        link_desc: '专业资料包下载',
        file_size: '156MB',
        file_count: '50个文件',
        access_instructions: [
          '1. 点击下载链接',
          '2. 输入提取码：8888',
          '3. 下载完成后请及时保存'
        ]
      },
      payment_verified: true,
      access_time: new Date().toISOString()
    }
  },

  // 未付费用户访问付费内容
  'GET:/api/v1/groups/999/paid-content': {
    code: 403,
    message: '请先完成支付才能查看内容',
    data: {
      payment_required: true,
      payment_url: '/payment/order-123456',
      group_title: '测试群组',
      price: 99.00
    }
  },

  // 生成防红推广链接
  'POST:/api/admin/groups/1/promotion-link': {
    code: 200,
    message: '防红推广链接生成成功',
    data: {
      group_id: 1,
      original_url: 'https://example.com/landing/group/1',
      anti_block_url: 'https://safe-domain-1.com/g/abc123',
      short_url: 'https://t.cn/A6X8Y9Z0',
      qr_code_url: 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=https%3A//t.cn/A6X8Y9Z0',
      anti_block_enabled: true,
      short_link_enabled: true,
      domain_info: {
        domain: 'safe-domain-1.com',
        health_score: 95,
        risk_level: 'low',
        last_check: new Date().toISOString()
      },
      short_link_info: {
        short_code: 'A6X8Y9Z0',
        click_count: 0,
        expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString() // 6个月后过期
      },
      created_at: new Date().toISOString()
    }
  },

  // 生成防红推广链接（群组2）
  'POST:/api/admin/groups/2/promotion-link': {
    code: 200,
    message: '防红推广链接生成成功',
    data: {
      group_id: 2,
      original_url: 'https://example.com/landing/group/2',
      anti_block_url: 'https://safe-domain-2.com/g/def456',
      short_url: 'https://t.cn/B7Y9Z1A2',
      qr_code_url: 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=https%3A//t.cn/B7Y9Z1A2',
      anti_block_enabled: true,
      short_link_enabled: true,
      domain_info: {
        domain: 'safe-domain-2.com',
        health_score: 88,
        risk_level: 'low',
        last_check: new Date().toISOString()
      },
      short_link_info: {
        short_code: 'B7Y9Z1A2',
        click_count: 0,
        expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      created_at: new Date().toISOString()
    }
  },

  // 防红系统不可用的情况
  'POST:/api/admin/groups/999/promotion-link': {
    code: 500,
    message: '防红系统暂时不可用',
    data: {
      group_id: 999,
      original_url: 'https://example.com/landing/group/999',
      anti_block_url: null,
      short_url: null,
      anti_block_enabled: false,
      short_link_enabled: false,
      error: 'No available domains in pool'
    }
  },

  // 通用防红推广链接生成（支持任意群组ID）
  'POST:/api/admin/groups/:id/promotion-link': (url, options) => {
    const groupId = url.split('/')[4] // 提取群组ID
    const domains = [
      'safe-domain-1.com',
      'safe-domain-2.com',
      'safe-domain-3.com',
      'backup-domain-1.com',
      'backup-domain-2.com'
    ]
    const selectedDomain = domains[Math.floor(Math.random() * domains.length)]
    const shortCode = Math.random().toString(36).substr(2, 8).toUpperCase()

    return {
      code: 200,
      message: '防红推广链接生成成功',
      data: {
        group_id: parseInt(groupId),
        original_url: `https://example.com/landing/group/${groupId}`,
        anti_block_url: `https://${selectedDomain}/g/${shortCode.toLowerCase()}`,
        short_url: `https://t.cn/${shortCode}`,
        qr_code_url: `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=https%3A//t.cn/${shortCode}`,
        anti_block_enabled: true,
        short_link_enabled: true,
        domain_info: {
          domain: selectedDomain,
          health_score: Math.floor(Math.random() * 20) + 80, // 80-100之间的健康分数
          risk_level: 'low',
          last_check: new Date().toISOString()
        },
        short_link_info: {
          short_code: shortCode,
          click_count: 0,
          expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        created_at: new Date().toISOString()
      }
    }
  },

  // 防红系统统计数据
  'GET:/api/v1/admin/anti-block/stats': {
    code: 200,
    message: 'success',
    data: {
      overview: {
        total_domains: 15,
        healthy_domains: 12,
        warning_domains: 2,
        blocked_domains: 1,
        total_short_links: 1248,
        active_short_links: 1156,
        total_clicks: 45678,
        today_clicks: 892
      },
      domain_health: {
        excellent: 8,  // 90-100分
        good: 4,       // 80-89分
        warning: 2,    // 60-79分
        poor: 1        // 0-59分
      },
      recent_activity: [
        {
          id: 1,
          type: 'domain_check',
          domain: 'safe-domain-1.com',
          status: 'healthy',
          health_score: 95,
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          type: 'short_link_created',
          short_code: 'A6X8Y9Z0',
          original_url: 'https://example.com/landing/group/1',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          type: 'domain_warning',
          domain: 'backup-domain-1.com',
          status: 'warning',
          health_score: 75,
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        }
      ],
      performance_trend: {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
        datasets: [
          {
            label: '点击量',
            data: [45, 78, 156, 234, 189, 123],
            borderColor: '#409eff',
            backgroundColor: 'rgba(64, 158, 255, 0.1)'
          },
          {
            label: '转化量',
            data: [12, 23, 45, 67, 54, 34],
            borderColor: '#67c23a',
            backgroundColor: 'rgba(103, 194, 58, 0.1)'
          }
        ]
      }
    }
  },

  // 防红域名列表
  'GET:/api/v1/admin/anti-block/domains': {
    code: 200,
    message: 'success',
    data: {
      data: [
        {
          id: 1,
          domain: 'safe-domain-1.com',
          domain_type: 'primary',
          health_score: 95,
          status: 'active',
          last_check_time: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 156,
          today_clicks: 234,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          domain: 'safe-domain-2.com',
          domain_type: 'backup',
          health_score: 88,
          status: 'active',
          last_check_time: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 89,
          today_clicks: 167,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          id: 3,
          domain: 'safe-domain-3.com',
          domain_type: 'primary',
          health_score: 92,
          status: 'active',
          last_check_time: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          risk_level: 'low',
          total_links: 203,
          today_clicks: 345,
          created_at: '2024-01-03T00:00:00Z'
        },
        {
          id: 4,
          domain: 'backup-domain-1.com',
          domain_type: 'backup',
          health_score: 75,
          status: 'warning',
          last_check_time: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          risk_level: 'medium',
          total_links: 45,
          today_clicks: 67,
          created_at: '2024-01-04T00:00:00Z'
        },
        {
          id: 5,
          domain: 'backup-domain-2.com',
          domain_type: 'backup',
          health_score: 45,
          status: 'blocked',
          last_check_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          risk_level: 'high',
          total_links: 23,
          today_clicks: 12,
          created_at: '2024-01-05T00:00:00Z'
        }
      ],
      total: 5,
      current_page: 1,
      per_page: 20,
      total_pages: 1
    }
  },

  // 单个域名检测
  'POST:/api/v1/admin/anti-block/domains/1/check': {
    code: 200,
    message: '域名检测完成',
    data: {
      domain_id: 1,
      domain: 'safe-domain-1.com',
      health_score: 96,
      status: 'active',
      risk_level: 'low',
      check_time: new Date().toISOString(),
      check_details: {
        dns_resolution: 'ok',
        http_response: 'ok',
        ssl_certificate: 'ok',
        blacklist_check: 'clean',
        response_time: 156
      }
    }
  },

  // 短链接列表
  'GET:/api/v1/admin/anti-block/short-links': {
    code: 200,
    message: 'success',
    data: {
      short_links: [
        {
          id: 1,
          short_code: 'A6X8Y9Z0',
          full_url: 'https://t.cn/A6X8Y9Z0',
          original_url: 'https://example.com/landing/group/1',
          domain: 'safe-domain-1.com',
          link_type: 'promotion',
          status: 'active',
          click_count: 234,
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          last_click_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          short_code: 'B7Y9Z1A2',
          full_url: 'https://t.cn/B7Y9Z1A2',
          original_url: 'https://example.com/landing/group/2',
          domain: 'safe-domain-2.com',
          link_type: 'promotion',
          status: 'active',
          click_count: 167,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          last_click_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          short_code: 'C8Z0A3B4',
          full_url: 'https://t.cn/C8Z0A3B4',
          original_url: 'https://example.com/landing/group/3',
          domain: 'safe-domain-3.com',
          link_type: 'promotion',
          status: 'active',
          click_count: 89,
          created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          last_click_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 3,
        total_pages: 1
      }
    }
  },

  // 批量域名检测
  'POST:/api/v1/admin/anti-block/domains/batch-check': {
    code: 200,
    message: '批量检测完成',
    data: {
      total: 3,
      success: 2,
      failed: 1,
      results: [
        {
          domain_id: 1,
          domain: 'safe-domain-1.com',
          health_score: 95,
          status: 'active',
          success: true
        },
        {
          domain_id: 2,
          domain: 'safe-domain-2.com',
          health_score: 88,
          status: 'active',
          success: true
        },
        {
          domain_id: 3,
          domain: 'backup-domain-1.com',
          health_score: 0,
          status: 'blocked',
          success: false,
          error: 'Domain blocked'
        }
      ]
    }
  },

  // 添加域名
  'POST:/api/v1/admin/anti-block/domains': {
    code: 200,
    message: '域名添加成功',
    data: {
      id: Date.now(),
      domain: 'new-domain.com',
      domain_type: 'backup',
      health_score: 100,
      status: 'active',
      created_at: new Date().toISOString()
    }
  },

  // 更新域名
  'PUT:/api/v1/admin/anti-block/domains/1': {
    code: 200,
    message: '域名更新成功',
    data: {
      id: 1,
      domain: 'updated-domain.com',
      domain_type: 'primary',
      health_score: 95,
      status: 'active',
      updated_at: new Date().toISOString()
    }
  },

  // 删除域名
  'DELETE:/api/v1/admin/anti-block/domains/1': {
    code: 200,
    message: '域名删除成功',
    data: { success: true }
  },

  // 批量删除域名
  'POST:/api/v1/admin/anti-block/domains/batch-delete': {
    code: 200,
    message: '批量删除成功',
    data: {
      deleted_count: 3,
      success: true
    }
  },

  // 创建短链接
  'POST:/api/v1/admin/anti-block/short-links': {
    code: 200,
    message: '短链接创建成功',
    data: {
      id: Date.now(),
      short_code: Math.random().toString(36).substr(2, 8).toUpperCase(),
      full_url: 'https://t.cn/' + Math.random().toString(36).substr(2, 8).toUpperCase(),
      original_url: 'https://example.com/test',
      domain: 'safe-domain-1.com',
      status: 'active',
      created_at: new Date().toISOString()
    }
  },

  // 删除短链接
  'DELETE:/api/v1/admin/anti-block/short-links/1': {
    code: 200,
    message: '短链接删除成功',
    data: { success: true }
  },

  // 批量删除短链接
  'POST:/api/v1/admin/anti-block/short-links/batch-delete': {
    code: 200,
    message: '批量删除成功',
    data: {
      deleted_count: 5,
      success: true
    }
  },

  // 访问统计
  'GET:/api/v1/admin/anti-block/access-stats': {
    code: 200,
    message: 'success',
    data: {
      total_clicks: 45678,
      today_clicks: 892,
      yesterday_clicks: 756,
      this_week_clicks: 5234,
      this_month_clicks: 23456,
      conversion_rate: 12.5,
      top_links: [
        {
          short_code: 'A6X8Y9Z0',
          clicks: 234,
          conversions: 29
        },
        {
          short_code: 'B7Y9Z1A2',
          clicks: 167,
          conversions: 21
        }
      ]
    }
  },

  // 访问日志
  'GET:/api/v1/admin/anti-block/access-logs': {
    code: 200,
    message: 'success',
    data: {
      logs: [
        {
          id: 1,
          short_code: 'A6X8Y9Z0',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
          referer: 'https://weixin.qq.com/',
          region: '北京',
          access_time: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          short_code: 'B7Y9Z1A2',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Android 10; Mobile)',
          referer: 'https://weixin.qq.com/',
          region: '上海',
          access_time: new Date(Date.now() - 10 * 60 * 1000).toISOString()
        }
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 2,
        total_pages: 1
      }
    }
  },

  // 点击趋势
  'GET:/api/v1/admin/anti-block/click-trends': {
    code: 200,
    message: 'success',
    data: {
      labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
      datasets: [
        {
          label: '点击量',
          data: [45, 78, 156, 234, 189, 123],
          borderColor: '#409eff'
        }
      ]
    }
  },

  // 地区统计
  'GET:/api/v1/admin/anti-block/region-stats': {
    code: 200,
    message: 'success',
    data: {
      regions: [
        { name: '北京', clicks: 1234, percentage: 25.6 },
        { name: '上海', clicks: 987, percentage: 20.4 },
        { name: '广州', clicks: 756, percentage: 15.7 },
        { name: '深圳', clicks: 654, percentage: 13.6 },
        { name: '其他', clicks: 1189, percentage: 24.7 }
      ]
    }
  },

  // 平台统计
  'GET:/api/v1/admin/anti-block/platform-stats': {
    code: 200,
    message: 'success',
    data: {
      platforms: [
        { name: '微信', clicks: 2345, percentage: 48.6 },
        { name: 'QQ', clicks: 1234, percentage: 25.6 },
        { name: '浏览器', clicks: 987, percentage: 20.4 },
        { name: '其他', clicks: 254, percentage: 5.4 }
      ]
    }
  },

  // 生成二维码
  'POST:/api/v1/admin/anti-block/qrcode': {
    code: 200,
    message: '二维码生成成功',
    data: {
      qr_code_url: 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=https%3A//t.cn/A6X8Y9Z0',
      short_url: 'https://t.cn/A6X8Y9Z0'
    }
  },

  // 支付渠道管理API
  '/admin/payment/channels': {
    code: 200,
    message: 'success',
    data: {
      channels: [
        {
          id: 1,
          name: '微信支付',
          type: 'wechat',
          status: 'active',
          app_id: 'wx1234567890abcdef',
          merchant_id: '1234567890',
          fee_rate: 0.6,
          daily_limit: 50000,
          single_limit: 5000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          name: '支付宝',
          type: 'alipay',
          status: 'active',
          app_id: '2021001234567890',
          merchant_id: '2088123456789012',
          fee_rate: 0.55,
          daily_limit: 100000,
          single_limit: 10000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 3,
          name: '银联支付',
          type: 'unionpay',
          status: 'inactive',
          app_id: 'up1234567890',
          merchant_id: '123456789012345',
          fee_rate: 0.8,
          daily_limit: 30000,
          single_limit: 3000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        }
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 3,
        total_pages: 1
      }
    }
  },

  'GET:/api/v1/admin/payment/channels': {
    code: 200,
    message: 'success',
    data: {
      channels: [
        {
          id: 1,
          name: '微信支付',
          type: 'wechat',
          status: 'active',
          app_id: 'wx1234567890abcdef',
          merchant_id: '1234567890',
          fee_rate: 0.6,
          daily_limit: 50000,
          single_limit: 5000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          name: '支付宝',
          type: 'alipay',
          status: 'active',
          app_id: '2021001234567890',
          merchant_id: '2088123456789012',
          fee_rate: 0.55,
          daily_limit: 100000,
          single_limit: 10000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        }
      ],
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 2,
        total_pages: 1
      }
    }
  },

  // 通用的群组营销配置（支持任意群组ID）
  'GET:/admin/groups/*/marketing-config': {
    code: 200,
    message: 'success',
    data: {
      basic: {
        read_count_display: '10万+',
        like_count: 888,
        want_see_count: 666,
        button_title: '立即加入群聊',
        avatar_library: 'qq',
        wx_accessible: 1,
        display_type: 1
      },
      content: {
        group_intro_title: '群简介',
        group_intro_content: '这是一个优质的交流群',
        faq_title: '常见问题',
        faq_content: '问题1----答案1\n问题2----答案2',
        member_reviews: '评论1----10\n评论2----8'
      },
      service: {
        show_customer_service: 2,
        customer_service_title: 'VIP专属客服',
        customer_service_desc: '有问题请联系客服',
        customer_service_avatar: '/avatars/service.jpg',
        customer_service_qr: '/qr/service.jpg',
        ad_qr_code: '/qr/ad.jpg'
      },
      advanced: {
        auto_city_replace: 1,
        city_insert_strategy: 'auto',
        virtual_members: 156,
        virtual_orders: 89,
        virtual_income: 2580.50,
        today_views: 1200
      }
    }
  },

}

// 拦截API请求并返回模拟数据
export const setupMockApi = () => {
  // 如果是开发环境且没有后端服务，使用模拟数据
  if (import.meta.env.DEV) {
    // 拦截axios请求
    import('axios').then(({ default: axios }) => {
      // 添加请求拦截器来处理Mock API
      axios.interceptors.request.use(
        async (config) => {
          // 检查是否是API请求
          if (config.url && (config.url.includes('/api/v1') || config.url.includes('/api/'))) {
            let apiPath, method;

            if (config.url.includes('/api/v1')) {
              apiPath = config.url.replace(/.*\/api\/v1/, '').split('?')[0]
            } else {
              apiPath = config.url.replace(/.*\/api/, '').split('?')[0]
            }

            method = (config.method || 'GET').toUpperCase()



            // 检查是否有对应的Mock API响应
            const mockKey = `${method}:${apiPath}`
            if (mockApiResponses[mockKey]) {
              let mockResponse = mockApiResponses[mockKey]

              // 如果是函数，执行它
              if (typeof mockResponse === 'function') {
                try {
                  mockResponse = await mockResponse(config)
                } catch (error) {
                  console.error('Mock API函数执行错误:', error)
                  mockResponse = { success: false, message: 'Mock API执行错误' }
                }
              }

              // 返回Mock响应
              return Promise.reject({
                response: {
                  data: mockResponse,
                  status: 200,
                  statusText: 'OK'
                },
                config,
                isMockResponse: true
              })
            }

            // 特殊处理防红系统API
            if (apiPath.includes('/admin/anti-block/')) {

              // 直接返回防红系统Mock数据
              const antiBlockMockData = {
                '/admin/anti-block/stats': {
                  code: 200,
                  message: 'success',
                  data: {
                    domain_stats: {
                      total: 15,
                      active: 12,
                      abnormal: 2,
                      blocked: 1
                    },
                    link_stats: {
                      total: 1248,
                      active: 1156,
                      today_created: 23,
                      today_clicks: 892,
                      total_clicks: 45678
                    },
                    system_health: {
                      score: 85,
                      status: 'good'
                    },
                    domain_health: {
                      excellent: 8,
                      good: 4,
                      warning: 2,
                      poor: 1
                    },
                    recent_activity: [
                      {
                        id: 1,
                        type: 'domain_check',
                        domain: 'safe-domain-1.com',
                        status: 'healthy',
                        health_score: 95,
                        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
                      }
                    ]
                  }
                }
              }

              const mockResponse = antiBlockMockData[apiPath]
              if (mockResponse) {

                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 300))

                // 直接返回Mock响应
                config.adapter = () => {
                  return Promise.resolve({
                    data: mockResponse,
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: config
                  })
                }

                return config
              }
            }

            // 尝试匹配带方法的路径，如果没有则尝试不带方法的路径
            const methodPath = `${method}:${apiPath}`
            const fullMethodPath = `${method}:/api/v1${apiPath}`
            let mockResponse = mockApiResponses[methodPath] || mockApiResponses[apiPath] || mockApiResponses[fullMethodPath]

            console.log(`🔍 尝试匹配路径:`)
            console.log(`  - methodPath: ${methodPath}`)
            console.log(`  - apiPath: ${apiPath}`)
            console.log(`  - fullMethodPath: ${fullMethodPath}`)
            console.log('🔍 找到Mock响应:', !!mockResponse)

            // 如果没有找到，打印所有可用的防红系统路径
            if (!mockResponse) {
              const antiBlockPaths = Object.keys(mockApiResponses).filter(k => k.includes('anti-block'))
              console.log('🔍 可用的防红系统路径:', antiBlockPaths)
              console.log('🔍 所有Mock路径数量:', Object.keys(mockApiResponses).length)
            }

            // 如果没有直接匹配，尝试通配符匹配
            if (!mockResponse) {
              for (const [pattern, response] of Object.entries(mockApiResponses)) {
                // 处理通配符路径，如 /admin/groups/*/marketing-config
                if (pattern.includes('*')) {
                  const regex = new RegExp('^' + pattern.replace(/\*/g, '[^/]+') + '$')
                  if (regex.test(methodPath) || regex.test(apiPath)) {
                    mockResponse = response
                    break
                  }
                }
              }
            }

            if (mockResponse) {
              console.log(`🔄 Mock API Response for: ${method} ${apiPath}`)

              // 模拟网络延迟
              await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))

              // 处理函数类型的Mock响应
              let responseData = mockResponse
              if (typeof mockResponse === 'function') {
                try {
                  responseData = mockResponse(config.url, config)
                  console.log('🔄 执行函数类型Mock响应')
                } catch (error) {
                  console.error('🔄 执行Mock函数失败:', error)
                  responseData = { code: 500, message: 'Mock函数执行失败' }
                }
              }

              // 直接返回Mock响应，不发送真实请求
              config.adapter = () => {
                return Promise.resolve({
                  data: responseData,
                  status: 200,
                  statusText: 'OK',
                  headers: {},
                  config: config
                })
              }
            }
          }

          return config
        },
        (error) => {
          return Promise.reject(error)
        }
      )
      
      // 添加响应拦截器
      axios.interceptors.response.use(
        (response) => {
          return response
        },
        (error) => {
          // 检查是否是Mock响应
          if (error.isMockResponse) {
            return Promise.resolve(error.response)
          }

          console.error('API请求错误:', error)
          return Promise.reject(error)
        }
      )
    })
    
    // 同时也拦截fetch请求（以防万一）
    const originalFetch = window.fetch
    
    window.fetch = async (url, options = {}) => {
      // 检查是否是API请求
      if (typeof url === 'string' && (url.includes('/api/v1') || url.includes('/api/'))) {
        let apiPath;

        if (url.includes('/api/v1')) {
          apiPath = url.replace(/.*\/api\/v1/, '').split('?')[0]
        } else {
          apiPath = url.replace(/.*\/api/, '').split('?')[0]
        }

        const method = (options.method || 'GET').toUpperCase()
        const mockKey = `${method}:${apiPath}`

        if (mockApiResponses[mockKey]) {
          let mockResponse = mockApiResponses[mockKey]

          // 如果是函数，执行它
          if (typeof mockResponse === 'function') {
            try {
              mockResponse = await mockResponse({ url, ...options })
            } catch (error) {
              console.error('Mock API函数执行错误:', error)
              mockResponse = { success: false, message: 'Mock API执行错误' }
            }
          }

          // 模拟网络延迟
          await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))

          return new Response(JSON.stringify(mockResponse), {
            status: 200,
            headers: {
              'Content-Type': 'application/json'
            }
          })
        }
      }
      
      // 对于非API请求或未配置的API，使用原始fetch
      return originalFetch(url, options)
    }
  }
}

export default {
  mockApiResponses,
  setupMockApi
}