<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Models\User;
use App\Http\Resources\WechatGroupResource;
use App\Http\Resources\WechatGroupCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 优化的微信群控制器
 * 解决N+1查询问题，提升性能
 */
class OptimizedWechatGroupController extends Controller
{
    /**
     * 获取群组列表（优化版）
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 20), 100);
        $cacheKey = 'groups_list:' . md5(serialize($request->all())) . ':page:' . $request->get('page', 1);
        
        // 使用缓存提升性能
        $result = Cache::remember($cacheKey, 600, function () use ($request, $perPage) {
            $query = WechatGroup::query()
                ->with([
                    'user:id,username,nickname,avatar',
                    'substation:id,name,domain',
                ])
                ->withCount([
                    'orders as total_orders_count',
                    'orders as paid_orders_count' => function ($query) {
                        $query->where('status', \App\Models\Order::STATUS_PAID_INT);
                    }
                ])
                ->select([
                    'id', 'user_id', 'substation_id', 'title', 'subtitle', 
                    'description', 'price', 'current_members', 'max_members',
                    'status', 'view_count', 'total_earnings', 'created_at',
                    'qr_code', 'cover_image', 'city_location', 'sort_order'
                ]);

            // 应用筛选条件
            $this->applyFilters($query, $request);

            // 排序
            $this->applySorting($query, $request);

            return $query->paginate($perPage);
        });

        return $this->success([
            'groups' => WechatGroupResource::collection($result->items()),
            'pagination' => [
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
                'has_more' => $result->hasMorePages(),
            ]
        ]);
    }

    /**
     * 获取群组详情（优化版）
     */
    public function show(Request $request, int $id): JsonResponse
    {
        $cacheKey = "group_detail:{$id}";
        
        $group = Cache::remember($cacheKey, 1800, function () use ($id) {
            return WechatGroup::with([
                'user:id,username,nickname,avatar,role',
                'substation:id,name,domain,status',
                'orders' => function ($query) {
                    $query->select('id', 'wechat_group_id', 'status', 'amount', 'created_at')
                          ->where('status', \App\Models\Order::STATUS_PAID_INT)
                          ->latest()
                          ->limit(10);
                }
            ])
            ->withCount([
                'orders as total_orders_count',
                'orders as paid_orders_count' => function ($query) {
                    $query->where('status', \App\Models\Order::STATUS_PAID_INT);
                }
            ])
            ->findOrFail($id);
        });

        // 增加浏览量（异步处理，不影响响应速度）
        $this->incrementViewCountAsync($group);

        // 获取统计数据
        $stats = $group->getCachedStats();

        return $this->success([
            'group' => new WechatGroupResource($group),
            'stats' => $stats,
            'recent_orders' => $group->orders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'amount' => $order->amount,
                    'created_at' => $order->created_at->format('Y-m-d H:i:s'),
                ];
            }),
        ]);
    }

    /**
     * 获取用户的群组列表（优化版）
     */
    public function userGroups(Request $request, int $userId): JsonResponse
    {
        $perPage = min($request->get('per_page', 20), 50);
        $cacheKey = "user_groups:{$userId}:page:" . $request->get('page', 1);
        
        $result = Cache::remember($cacheKey, 900, function () use ($userId, $perPage, $request) {
            $query = WechatGroup::where('user_id', $userId)
                ->withCount([
                    'orders as total_orders_count',
                    'orders as paid_orders_count' => function ($query) {
                        $query->where('status', \App\Models\Order::STATUS_PAID_INT);
                    }
                ])
                ->with('substation:id,name')
                ->select([
                    'id', 'user_id', 'substation_id', 'title', 'price',
                    'current_members', 'max_members', 'status', 'view_count',
                    'total_earnings', 'created_at', 'updated_at'
                ]);

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->get('status'));
            }

            return $query->orderBy('created_at', 'desc')->paginate($perPage);
        });

        return $this->success([
            'groups' => WechatGroupResource::collection($result->items()),
            'pagination' => [
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
            ]
        ]);
    }

    /**
     * 批量获取群组统计数据
     */
    public function batchStats(Request $request): JsonResponse
    {
        $groupIds = $request->get('group_ids', []);
        
        if (empty($groupIds) || count($groupIds) > 50) {
            return $this->error('群组ID数量必须在1-50之间');
        }

        $cacheKey = 'batch_stats:' . md5(implode(',', $groupIds));
        
        $stats = Cache::remember($cacheKey, 1800, function () use ($groupIds) {
            // 使用单次查询获取所有群组的统计数据
            $results = DB::table('orders')
                ->whereIn('wechat_group_id', $groupIds)
                ->selectRaw('
                    wechat_group_id,
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN status = ? THEN 1 END) as paid_orders,
                    SUM(CASE WHEN status = ? THEN amount ELSE 0 END) as total_income,
                    COUNT(CASE WHEN status = ? AND DATE(created_at) = CURDATE() THEN 1 END) as today_orders,
                    SUM(CASE WHEN status = ? AND DATE(created_at) = CURDATE() THEN amount ELSE 0 END) as today_income
                ', [
                    \App\Models\Order::STATUS_PAID_INT,
                    \App\Models\Order::STATUS_PAID_INT,
                    \App\Models\Order::STATUS_PAID_INT,
                    \App\Models\Order::STATUS_PAID_INT
                ])
                ->groupBy('wechat_group_id')
                ->get()
                ->keyBy('wechat_group_id');

            // 格式化结果
            $formattedStats = [];
            foreach ($groupIds as $groupId) {
                $stat = $results->get($groupId);
                $formattedStats[$groupId] = [
                    'total_orders' => $stat ? (int) $stat->total_orders : 0,
                    'paid_orders' => $stat ? (int) $stat->paid_orders : 0,
                    'total_income' => $stat ? (float) $stat->total_income : 0,
                    'today_orders' => $stat ? (int) $stat->today_orders : 0,
                    'today_income' => $stat ? (float) $stat->today_income : 0,
                ];
            }

            return $formattedStats;
        });

        return $this->success(['stats' => $stats]);
    }

    /**
     * 获取热门群组（优化版）
     */
    public function hotGroups(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 50);
        $cacheKey = "hot_groups:limit:{$limit}";
        
        $groups = Cache::remember($cacheKey, 3600, function () use ($limit) {
            return WechatGroup::with([
                'user:id,username,nickname,avatar',
                'substation:id,name'
            ])
            ->withCount([
                'orders as recent_orders_count' => function ($query) {
                    $query->where('status', \App\Models\Order::STATUS_PAID_INT)
                          ->where('created_at', '>=', now()->subDays(7));
                }
            ])
            ->where('status', WechatGroup::STATUS_ACTIVE)
            ->having('recent_orders_count', '>=', 5)
            ->orderBy('recent_orders_count', 'desc')
            ->orderBy('view_count', 'desc')
            ->limit($limit)
            ->get();
        });

        return $this->success([
            'groups' => WechatGroupResource::collection($groups)
        ]);
    }

    /**
     * 搜索群组（优化版）
     */
    public function search(Request $request): JsonResponse
    {
        $keyword = $request->get('keyword');
        $perPage = min($request->get('per_page', 20), 50);
        
        if (empty($keyword) || mb_strlen($keyword) < 2) {
            return $this->error('搜索关键词至少2个字符');
        }

        $cacheKey = 'search_groups:' . md5($keyword . $perPage . $request->get('page', 1));
        
        $result = Cache::remember($cacheKey, 600, function () use ($keyword, $perPage) {
            return WechatGroup::with([
                'user:id,username,nickname',
                'substation:id,name'
            ])
            ->withCount('orders as total_orders_count')
            ->where('status', WechatGroup::STATUS_ACTIVE)
            ->where(function ($query) use ($keyword) {
                $query->where('title', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%")
                      ->orWhere('subtitle', 'like', "%{$keyword}%");
            })
            ->orderBy('view_count', 'desc')
            ->orderBy('total_earnings', 'desc')
            ->paginate($perPage);
        });

        return $this->success([
            'groups' => WechatGroupResource::collection($result->items()),
            'pagination' => [
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
            ]
        ]);
    }

    /**
     * 应用筛选条件
     */
    private function applyFilters($query, Request $request): void
    {
        // 状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        } else {
            $query->where('status', WechatGroup::STATUS_ACTIVE);
        }

        // 价格筛选
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // 分站筛选
        if ($request->has('substation_id')) {
            $query->where('substation_id', $request->get('substation_id'));
        }

        // 用户筛选
        if ($request->has('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        // 城市定位筛选
        if ($request->has('city_location')) {
            $query->where('city_location', $request->get('city_location'));
        }

        // 关键词搜索
        if ($request->has('keyword')) {
            $keyword = $request->get('keyword');
            $query->where(function ($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
    }

    /**
     * 应用排序
     */
    private function applySorting($query, Request $request): void
    {
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $allowedSorts = [
            'created_at', 'updated_at', 'price', 'view_count',
            'total_earnings', 'current_members', 'sort_order'
        ];

        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        } else {
            // 默认排序：优先级 > 浏览量 > 创建时间
            $query->orderBy('sort_order', 'desc')
                  ->orderBy('view_count', 'desc')
                  ->orderBy('created_at', 'desc');
        }
    }

    /**
     * 异步增加浏览量
     */
    private function incrementViewCountAsync(WechatGroup $group): void
    {
        // 使用队列异步处理，避免影响响应速度
        dispatch(function () use ($group) {
            $group->incrementViewCount();
            
            // 清除相关缓存
            Cache::forget("group_detail:{$group->id}");
            Cache::forget("group_stats:{$group->id}");
        })->afterResponse();
    }

    /**
     * 清除群组相关缓存
     */
    public function clearCache(int $groupId): JsonResponse
    {
        $this->authorize('admin');

        $cacheKeys = [
            "group_detail:{$groupId}",
            "group_stats:{$groupId}",
            "user_groups:{$groupId}",
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // 清除列表缓存（使用标签）
        Cache::tags(['groups'])->flush();

        return $this->success(['message' => '缓存清除成功']);
    }

    /**
     * 获取群组性能统计
     */
    public function performanceStats(): JsonResponse
    {
        $this->authorize('admin');

        $stats = Cache::remember('group_performance_stats', 3600, function () {
            return [
                'total_groups' => WechatGroup::count(),
                'active_groups' => WechatGroup::where('status', WechatGroup::STATUS_ACTIVE)->count(),
                'groups_with_orders' => WechatGroup::has('orders')->count(),
                'avg_orders_per_group' => round(
                    \App\Models\Order::count() / max(WechatGroup::count(), 1), 2
                ),
                'top_performing_groups' => WechatGroup::withCount('orders')
                    ->orderBy('orders_count', 'desc')
                    ->limit(10)
                    ->pluck('title', 'id'),
                'cache_hit_rate' => $this->calculateCacheHitRate(),
            ];
        });

        return $this->success(['stats' => $stats]);
    }

    /**
     * 计算缓存命中率
     */
    private function calculateCacheHitRate(): float
    {
        // 这里可以集成Redis统计信息
        // 暂时返回模拟数据
        return 85.6;
    }
}