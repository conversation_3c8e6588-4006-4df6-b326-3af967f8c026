/**
 * 路由检测工具
 * 用于自动化检测所有路由的可访问性和组件加载状态
 */

import router from '@/router'

export class RouteChecker {
  constructor() {
    this.results = []
    this.errors = []
  }

  /**
   * 检测所有路由
   */
  async checkAllRoutes() {
    console.log('🔍 开始路由检测...')
    this.results = []
    this.errors = []

    const routes = this.getAllRoutes()
    
    for (const route of routes) {
      await this.checkRoute(route)
    }

    return {
      results: this.results,
      errors: this.errors,
      summary: this.generateSummary()
    }
  }

  /**
   * 获取所有路由（包括嵌套路由）
   */
  getAllRoutes() {
    const routes = []
    
    const extractRoutes = (routeList, parentPath = '') => {
      routeList.forEach(route => {
        if (route.path && !route.meta?.hidden) {
          const fullPath = this.resolvePath(parentPath, route.path)
          routes.push({
            path: fullPath,
            name: route.name,
            component: route.component,
            meta: route.meta,
            parent: parentPath
          })
        }
        
        if (route.children) {
          const currentPath = this.resolvePath(parentPath, route.path)
          extractRoutes(route.children, currentPath)
        }
      })
    }

    extractRoutes(router.options.routes)
    return routes
  }

  /**
   * 检测单个路由
   */
  async checkRoute(route) {
    const result = {
      path: route.path,
      name: route.name,
      status: 'unknown',
      error: null,
      loadTime: 0,
      componentExists: false,
      hasErrors: false
    }

    try {
      const startTime = Date.now()
      
      // 检测组件是否存在
      if (route.component) {
        if (typeof route.component === 'function') {
          // 动态导入的组件
          try {
            const component = await route.component()
            result.componentExists = true
            result.status = 'success'
          } catch (error) {
            result.componentExists = false
            result.status = 'component_error'
            result.error = error.message
            this.errors.push({
              path: route.path,
              type: 'component_load_error',
              error: error.message
            })
          }
        } else {
          // 直接导入的组件
          result.componentExists = true
          result.status = 'success'
        }
      } else {
        result.status = 'no_component'
      }

      result.loadTime = Date.now() - startTime
      
    } catch (error) {
      result.status = 'error'
      result.error = error.message
      this.errors.push({
        path: route.path,
        type: 'route_error',
        error: error.message
      })
    }

    this.results.push(result)
    console.log(`✓ 检测路由: ${route.path} - ${result.status}`)
  }

  /**
   * 解析路径
   */
  resolvePath(parent, path) {
    if (path.startsWith('/')) {
      return path
    }
    if (!parent) {
      return `/${path}`
    }
    return `${parent}/${path}`.replace(/\/+/g, '/')
  }

  /**
   * 生成检测摘要
   */
  generateSummary() {
    const total = this.results.length
    const success = this.results.filter(r => r.status === 'success').length
    const errors = this.results.filter(r => r.status !== 'success').length
    
    return {
      total,
      success,
      errors,
      successRate: total > 0 ? (success / total * 100).toFixed(2) : 0,
      avgLoadTime: this.results.reduce((sum, r) => sum + r.loadTime, 0) / total
    }
  }

  /**
   * 生成详细报告
   */
  generateReport() {
    const summary = this.generateSummary()
    
    let report = `# 路由检测报告\n\n`
    report += `## 📊 检测摘要\n\n`
    report += `- **总路由数**: ${summary.total}\n`
    report += `- **成功加载**: ${summary.success}\n`
    report += `- **加载失败**: ${summary.errors}\n`
    report += `- **成功率**: ${summary.successRate}%\n`
    report += `- **平均加载时间**: ${summary.avgLoadTime.toFixed(2)}ms\n\n`

    // 成功的路由
    const successRoutes = this.results.filter(r => r.status === 'success')
    if (successRoutes.length > 0) {
      report += `## ✅ 成功加载的路由 (${successRoutes.length})\n\n`
      successRoutes.forEach(route => {
        report += `- \`${route.path}\` - ${route.name || '未命名'}\n`
      })
      report += `\n`
    }

    // 失败的路由
    const failedRoutes = this.results.filter(r => r.status !== 'success')
    if (failedRoutes.length > 0) {
      report += `## ❌ 加载失败的路由 (${failedRoutes.length})\n\n`
      failedRoutes.forEach(route => {
        report += `- \`${route.path}\` - ${route.status}\n`
        if (route.error) {
          report += `  - 错误: ${route.error}\n`
        }
      })
      report += `\n`
    }

    // 错误详情
    if (this.errors.length > 0) {
      report += `## 🔍 错误详情\n\n`
      this.errors.forEach(error => {
        report += `### ${error.path}\n`
        report += `- **类型**: ${error.type}\n`
        report += `- **错误**: ${error.error}\n\n`
      })
    }

    return report
  }
}

// 导出单例
export const routeChecker = new RouteChecker()
