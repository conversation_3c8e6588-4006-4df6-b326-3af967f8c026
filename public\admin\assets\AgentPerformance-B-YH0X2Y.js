import{_ as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import{P as e}from"./PageLayout-C6qH3ReN.js";import{aY as t,bh as s,bi as l,a$ as r,U as i,at as n,bw as o,bx as c,Q as d,R as u}from"./element-plus-h2SQQM64.js";import{r as p,L as m,e as _,k as f,l as g,E as v,z as y,t as b,A as h,y as w,D as z}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const j={class:"page-container"},x={class:"content-wrapper"},C={class:"card-body"},k={class:"pagination-wrapper"},P=a({__name:"AgentPerformance",setup(a){const P=p(!1),T=p([]),U=m({current:1,size:20,total:0}),$=()=>{I()},A=()=>{d.info("新增功能待实现")},B=a=>{U.size=a,I()},D=a=>{U.current=a,I()},I=async()=>{P.value=!0;try{await new Promise(a=>setTimeout(a,1e3)),T.value=[{id:1,name:"示例数据1",status:"active",created_at:"2024-01-01 12:00:00"},{id:2,name:"示例数据2",status:"inactive",created_at:"2024-01-02 12:00:00"}],U.total=2}catch(a){console.error("加载数据失败:",a),d.error("加载数据失败")}finally{P.value=!1}};return _(()=>{I()}),(a,p)=>{const m=n,_=l,L=r,S=s,E=c,Q=t,R=o;return g(),f("div",j,[v(e,{title:"代理业绩统计",subtitle:"查看代理商业绩数据和排名",loading:P.value},{actions:y(()=>[v(m,{class:"modern-btn secondary",onClick:$},{default:y(()=>p[2]||(p[2]=[b("i",{class:"el-icon-refresh"},null,-1),z(" 刷新数据 ",-1)])),_:1,__:[2]}),v(m,{class:"modern-btn primary",onClick:A},{default:y(()=>p[3]||(p[3]=[b("i",{class:"el-icon-plus"},null,-1),z(" 新增 ",-1)])),_:1,__:[3]})]),default:y(()=>[b("div",x,[v(Q,{class:"modern-card"},{default:y(()=>[p[6]||(p[6]=b("div",{class:"card-header"},[b("h3",null,"代理业绩统计"),b("p",{class:"text-muted"},"查看代理商业绩数据和排名")],-1)),b("div",C,[h((g(),w(S,{data:T.value,style:{width:"100%"},class:"modern-table"},{default:y(()=>[v(_,{prop:"id",label:"ID",width:"80"}),v(_,{prop:"name",label:"名称","min-width":"150"}),v(_,{prop:"status",label:"状态",width:"100"},{default:y(({row:a})=>[v(L,{type:"active"===a.status?"success":"info",size:"small"},{default:y(()=>[z(i("active"===a.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),v(_,{prop:"created_at",label:"创建时间",width:"180"}),v(_,{label:"操作",width:"200",fixed:"right"},{default:y(({row:a})=>[v(m,{type:"primary",size:"small",onClick:e=>(a=>{d.info(`编辑功能待实现: ${a.name}`)})(a)},{default:y(()=>p[4]||(p[4]=[z(" 编辑 ",-1)])),_:2,__:[4]},1032,["onClick"]),v(m,{type:"danger",size:"small",onClick:e=>(async a=>{try{await u.confirm(`确定要删除 "${a.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.success("删除成功"),I()}catch{d.info("已取消删除")}})(a)},{default:y(()=>p[5]||(p[5]=[z(" 删除 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[R,P.value]]),b("div",k,[v(E,{"current-page":U.current,"onUpdate:currentPage":p[0]||(p[0]=a=>U.current=a),"page-size":U.size,"onUpdate:pageSize":p[1]||(p[1]=a=>U.size=a),total:U.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:B,onCurrentChange:D},null,8,["current-page","page-size","total"])])])]),_:1,__:[6]})])]),_:1},8,["loading"])])}}},[["__scopeId","data-v-de350bc0"]]);export{P as default};
