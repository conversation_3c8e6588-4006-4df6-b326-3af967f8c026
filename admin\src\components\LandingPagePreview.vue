<template>
  <div class="landing-page-preview">
    <div
      class="preview-container"
      :class="[
        `view-${viewMode}`,
        `layout-${groupData.layout_style || 'card'}`
      ]"
    >
      <!-- 动态渲染布局组件 -->
      <div
        v-for="section in visibleSections"
        :key="section.id"
        class="preview-section"
        :class="[
          `section-${section.type}`,
          `layout-${groupData.layout_style || 'card'}`
        ]"
        :style="getSectionStyle(section)"
      >
        <component
          :is="getSectionComponent(section.type)"
          :section="section"
          :group-data="groupData"
          :layout-style="groupData.layout_style || 'card'"
          :preview="true"
        />
      </div>
      
      <!-- 空状态 -->
      <div v-if="visibleSections.length === 0" class="empty-preview">
        <el-icon class="empty-icon"><Document /></el-icon>
        <div class="empty-text">暂无内容</div>
        <div class="empty-hint">请在左侧添加内容模块</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Document } from '@element-plus/icons-vue'

// 导入各个预览组件
import BannerPreview from './preview/BannerPreview.vue'
import InfoPreview from './preview/InfoPreview.vue'
import ContentPreview from './preview/ContentPreview.vue'
import GalleryPreview from './preview/GalleryPreview.vue'
import VideoPreview from './preview/VideoPreview.vue'
import MembersPreview from './preview/MembersPreview.vue'

const props = defineProps({
  groupData: {
    type: Object,
    default: () => ({})
  },
  layoutConfig: {
    type: Object,
    default: () => ({
      sections: []
    })
  },
  viewMode: {
    type: String,
    default: 'mobile'
  }
})

// 计算属性
const visibleSections = computed(() => {
  if (!props.layoutConfig.sections) return []
  
  return props.layoutConfig.sections
    .filter(section => section.visible)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
})

// 方法
const getSectionComponent = (type) => {
  const componentMap = {
    banner: BannerPreview,
    info: InfoPreview,
    content: ContentPreview,
    gallery: GalleryPreview,
    video: VideoPreview,
    members: MembersPreview
  }
  
  return componentMap[type] || InfoPreview
}

const getSectionStyle = (section) => {
  const style = section.style || {}
  
  return {
    backgroundColor: style.backgroundColor || '#ffffff',
    padding: `${style.padding || 20}px`,
    margin: `${style.margin || 0}px 0`,
    ...style
  }
}
</script>

<style lang="scss" scoped>
.landing-page-preview {
  .preview-container {
    background: #f5f7fa;
    border-radius: 8px;
    overflow: hidden;
    min-height: 400px;
    
    &.view-mobile {
      max-width: 375px;
      margin: 0 auto;
    }
    
    &.view-tablet {
      max-width: 768px;
      margin: 0 auto;
    }
    
    &.view-desktop {
      max-width: 100%;
    }
    
    .preview-section {
      background: white;
      border-bottom: 1px solid #e4e7ed;

      &:last-child {
        border-bottom: none;
      }

      &.section-banner {
        border-radius: 8px 8px 0 0;
      }

      // 卡片布局样式
      &.layout-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e4e7ed;
        border-bottom: none;

        &:last-child {
          margin-bottom: 0;
        }
      }

      // 列表布局样式
      &.layout-list {
        border-left: 4px solid #409eff;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 8px;
      }

      // 现代布局样式
      &.layout-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        margin-bottom: 20px;
        border-bottom: none;
      }
    }
    
    .empty-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #909399;
      
      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      .empty-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .empty-hint {
        font-size: 14px;
      }
    }
  }
}
</style>
