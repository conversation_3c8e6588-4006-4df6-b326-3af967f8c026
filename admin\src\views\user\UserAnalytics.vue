<template>
  <div class="app-container">
    <!-- 用户概览统计 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card total-users">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-data">
              <div class="stat-number">{{ userStats.total_users.toLocaleString() }}</div>
              <div class="stat-label">总用户数</div>
              <div class="stat-change">
                <span class="change-text">本月新增 {{ userStats.new_this_month }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card active-users">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-data">
              <div class="stat-number">{{ userStats.active_users.toLocaleString() }}</div>
              <div class="stat-label">活跃用户</div>
              <div class="stat-change">
                <span class="change-text">活跃率 {{ userStats.active_rate }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card vip-users">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-trophy"></i>
            </div>
            <div class="stat-data">
              <div class="stat-number">{{ userStats.vip_users.toLocaleString() }}</div>
              <div class="stat-label">VIP用户</div>
              <div class="stat-change">
                <span class="change-text">占比 {{ userStats.vip_rate }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card avg-value">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-data">
              <div class="stat-number">¥{{ userStats.avg_user_value }}</div>
              <div class="stat-label">用户均值</div>
              <div class="stat-change">
                <span class="change-text">LTV ¥{{ userStats.lifetime_value }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户分析图表 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>📈 用户增长趋势</span>
              <el-radio-group v-model="growthPeriod" size="small" @change="updateGrowthChart">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <v-chart class="chart" :option="growthChartOptions" autoresize />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🎯 用户活跃度</span>
            </div>
          </template>
          <v-chart class="chart" :option="activityChartOptions" autoresize />
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户分布分析 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🌍 地域分布</span>
            </div>
          </template>
          <div class="region-list">
            <div class="region-item" v-for="region in regionData" :key="region.name">
              <div class="region-info">
                <span class="region-name">{{ region.name }}</span>
                <span class="region-count">{{ region.count }}人</span>
              </div>
              <div class="region-bar">
                <div class="region-progress" :style="{ width: region.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>👥 用户等级分布</span>
            </div>
          </template>
          <v-chart class="chart" :option="levelChartOptions" autoresize />
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>📱 设备类型</span>
            </div>
          </template>
          <div class="device-stats">
            <div class="device-item" v-for="device in deviceData" :key="device.type">
              <div class="device-icon" :class="device.type">
                <i :class="device.icon"></i>
              </div>
              <div class="device-info">
                <div class="device-name">{{ device.name }}</div>
                <div class="device-count">{{ device.count }}人</div>
                <div class="device-percent">{{ device.percentage }}%</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户行为分析 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>🔍 用户行为分析</span>
          <div>
            <el-date-picker
              v-model="behaviorDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="updateBehaviorData"
            />
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="behavior-card">
            <h4>🚀 功能使用排行</h4>
            <div class="feature-list">
              <div class="feature-item" v-for="(feature, index) in featureUsage" :key="feature.name">
                <div class="feature-rank">{{ index + 1 }}</div>
                <div class="feature-info">
                  <div class="feature-name">{{ feature.name }}</div>
                  <div class="feature-usage">{{ feature.usage }}次使用</div>
                </div>
                <div class="feature-bar">
                  <div class="feature-progress" :style="{ width: feature.percentage + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="behavior-card">
            <h4>⏰ 活跃时段分析</h4>
            <v-chart class="mini-chart" :option="timeAnalysisChart" autoresize />
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="behavior-card">
            <h4>📊 用户留存分析</h4>
            <div class="retention-table">
              <div class="retention-header">
                <span>时期</span>
                <span>新增用户</span>
                <span>次日留存</span>
                <span>7日留存</span>
              </div>
              <div class="retention-row" v-for="retention in retentionData" :key="retention.date">
                <span>{{ retention.date }}</span>
                <span>{{ retention.new_users }}</span>
                <span class="retention-rate">{{ retention.day1_retention }}%</span>
                <span class="retention-rate">{{ retention.day7_retention }}%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 用户画像分析 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>👤 用户画像分析</span>
          <el-button type="primary" @click="showUserSegmentDialog">创建用户分群</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="portrait-section">
            <h4>📈 消费能力分布</h4>
            <v-chart class="chart" :option="consumptionChart" autoresize />
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="portrait-section">
            <h4>🎂 年龄段分布</h4>
            <div class="age-distribution">
              <div class="age-item" v-for="age in ageData" :key="age.range">
                <div class="age-range">{{ age.range }}</div>
                <div class="age-bar">
                  <div class="age-progress" :style="{ width: age.percentage + '%', background: age.color }"></div>
                </div>
                <div class="age-percent">{{ age.percentage }}%</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 用户分群对话框 -->
    <el-dialog title="创建用户分群" v-model="segmentDialog.visible" width="800px">
      <el-form :model="segmentForm" label-width="100px">
        <el-form-item label="分群名称">
          <el-input v-model="segmentForm.name" placeholder="请输入分群名称" />
        </el-form-item>
        
        <el-form-item label="分群条件">
          <div class="segment-conditions">
            <div class="condition-item" v-for="(condition, index) in segmentForm.conditions" :key="index">
              <el-select v-model="condition.field" placeholder="选择字段">
                <el-option label="注册时间" value="register_time" />
                <el-option label="最后登录" value="last_login" />
                <el-option label="消费金额" value="consumption" />
                <el-option label="用户等级" value="level" />
                <el-option label="地域" value="region" />
              </el-select>
              
              <el-select v-model="condition.operator" placeholder="条件">
                <el-option label="等于" value="=" />
                <el-option label="大于" value=">" />
                <el-option label="小于" value="<" />
                <el-option label="包含" value="in" />
              </el-select>
              
              <el-input v-model="condition.value" placeholder="值" />
              
              <el-button type="danger" @click="removeCondition(index)" icon="el-icon-delete" circle />
            </div>
            
            <el-button type="primary" @click="addCondition" icon="el-icon-plus">添加条件</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="预览结果">
          <div class="segment-preview">
            <span>预计匹配用户数：{{ segmentPreview.count }}</span>
            <span>占总用户比例：{{ segmentPreview.percentage }}%</span>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="segmentDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="createSegment">创建分群</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import VChart from "vue-echarts"

const userStats = reactive({
  total_users: 12543,
  new_this_month: 1245,
  active_users: 8976,
  active_rate: 71.5,
  vip_users: 456,
  vip_rate: 3.6,
  avg_user_value: 1256.78,
  lifetime_value: 3456.78
})

const regionData = ref([
  { name: '北京', count: 2543, percentage: 85 },
  { name: '上海', count: 2134, percentage: 71 },
  { name: '广州', count: 1876, percentage: 63 },
  { name: '深圳', count: 1654, percentage: 55 },
  { name: '其他', count: 4336, percentage: 45 }
])

const deviceData = ref([
  { type: 'mobile', name: '手机', icon: 'el-icon-mobile-phone', count: 8543, percentage: 68 },
  { type: 'desktop', name: '电脑', icon: 'el-icon-monitor', count: 3210, percentage: 26 },
  { type: 'tablet', name: '平板', icon: 'el-icon-tablet', count: 790, percentage: 6 }
])

const featureUsage = ref([
  { name: '用户登录', usage: 15643, percentage: 100 },
  { name: '查看商品', usage: 12453, percentage: 80 },
  { name: '下单购买', usage: 8976, percentage: 57 },
  { name: '分享推广', usage: 6543, percentage: 42 },
  { name: '提现申请', usage: 3456, percentage: 22 }
])

const retentionData = ref([
  { date: '2024-01-01', new_users: 123, day1_retention: 85, day7_retention: 45 },
  { date: '2024-01-02', new_users: 156, day1_retention: 78, day7_retention: 42 },
  { date: '2024-01-03', new_users: 134, day1_retention: 82, day7_retention: 48 }
])

const ageData = ref([
  { range: '18-25', percentage: 35, color: '#409EFF' },
  { range: '26-35', percentage: 42, color: '#67C23A' },
  { range: '36-45', percentage: 18, color: '#E6A23C' },
  { range: '46+', percentage: 5, color: '#F56C6C' }
])

const growthPeriod = ref('30d')
const behaviorDateRange = ref([])

const growthChartOptions = ref({})
const activityChartOptions = ref({})
const levelChartOptions = ref({})
const timeAnalysisChart = ref({})
const consumptionChart = ref({})

const segmentDialog = reactive({
  visible: false
})

const segmentForm = reactive({
  name: '',
  conditions: [
    { field: '', operator: '', value: '' }
  ]
})

const segmentPreview = reactive({
  count: 0,
  percentage: 0
})

// 初始化图表
const initCharts = () => {
  updateGrowthChart()
  
  // 用户活跃度
  activityChartOptions.value = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户活跃度',
        type: 'pie',
        radius: ['30%', '70%'],
        data: [
          { value: 6543, name: '高活跃' },
          { value: 2433, name: '中活跃' },
          { value: 3567, name: '低活跃' }
        ]
      }
    ]
  }
  
  // 用户等级分布
  levelChartOptions.value = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户等级',
        type: 'pie',
        radius: '60%',
        data: [
          { value: 8543, name: '普通用户' },
          { value: 3210, name: '银牌用户' },
          { value: 790, name: '金牌用户' },
          { value: 200, name: 'VIP用户' }
        ]
      }
    ]
  }
  
  // 活跃时段分析
  timeAnalysisChart.value = {
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [120, 200, 150, 800, 700, 500],
        type: 'line',
        smooth: true
      }
    ]
  }
  
  // 消费能力分布
  consumptionChart.value = {
    xAxis: {
      type: 'category',
      data: ['0-100', '100-500', '500-1000', '1000-5000', '5000+']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [1200, 3400, 2800, 1600, 400],
        type: 'bar'
      }
    ]
  }
}

// 更新增长图表
const updateGrowthChart = () => {
  const days = growthPeriod.value === '7d' ? 7 : growthPeriod.value === '30d' ? 30 : 90
  const dates = []
  const newUsers = []
  const totalUsers = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
    newUsers.push(Math.floor(Math.random() * 100) + 50)
    totalUsers.push(Math.floor(Math.random() * 200) + 100)
  }
  
  growthChartOptions.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增用户', '活跃用户']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增用户',
        type: 'line',
        data: newUsers,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '活跃用户',
        type: 'line',
        data: totalUsers,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
}

// 更新行为数据
const updateBehaviorData = () => {
  ElMessage.success('行为数据已更新')
}

// 用户分群相关
const showUserSegmentDialog = () => {
  segmentDialog.visible = true
}

const addCondition = () => {
  segmentForm.conditions.push({ field: '', operator: '', value: '' })
}

const removeCondition = (index) => {
  segmentForm.conditions.splice(index, 1)
}

const createSegment = () => {
  ElMessage.success('用户分群创建成功')
  segmentDialog.visible = false
}

onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.total-users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.active-users {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.vip-users {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.avg-value {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
}

.stat-data {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.change-text {
  font-size: 12px;
  opacity: 0.8;
}

.chart {
  height: 300px;
}

.mini-chart {
  height: 200px;
}

.region-list {
  padding: 10px 0;
}

.region-item {
  margin-bottom: 15px;
}

.region-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.region-name {
  font-weight: 500;
  color: #303133;
}

.region-count {
  color: #909399;
  font-size: 14px;
}

.region-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.region-progress {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 3px;
  transition: width 0.3s;
}

.device-stats {
  padding: 10px 0;
}

.device-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.device-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
  color: white;
}

.device-icon.mobile { background: #409EFF; }
.device-icon.desktop { background: #67C23A; }
.device-icon.tablet { background: #E6A23C; }

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 3px;
}

.device-count {
  color: #909399;
  font-size: 14px;
}

.device-percent {
  color: #409EFF;
  font-weight: bold;
  font-size: 14px;
}

.behavior-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  height: 350px;
}

.behavior-card h4 {
  margin-bottom: 15px;
  color: #303133;
}

.feature-list {
  max-height: 280px;
  overflow-y: auto;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.feature-rank {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}

.feature-info {
  flex: 1;
  margin-right: 10px;
}

.feature-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 3px;
}

.feature-usage {
  color: #909399;
  font-size: 12px;
}

.feature-bar {
  width: 60px;
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.feature-progress {
  height: 100%;
  background: #409EFF;
  border-radius: 3px;
  transition: width 0.3s;
}

.retention-table {
  font-size: 14px;
}

.retention-header {
  display: grid;
  grid-template-columns: 1fr 80px 80px 80px;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
  color: #303133;
}

.retention-row {
  display: grid;
  grid-template-columns: 1fr 80px 80px 80px;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f2f5;
}

.retention-rate {
  color: #409EFF;
  font-weight: 500;
}

.portrait-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.portrait-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.age-distribution {
  padding: 10px 0;
}

.age-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.age-range {
  width: 60px;
  font-weight: 500;
  color: #303133;
}

.age-bar {
  flex: 1;
  height: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
  margin: 0 10px;
}

.age-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.age-percent {
  width: 40px;
  text-align: right;
  color: #909399;
  font-size: 14px;
}

.segment-conditions {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.segment-preview {
  padding: 10px;
  background: #f0f2f5;
  border-radius: 6px;
  color: #606266;
}

.segment-preview span {
  margin-right: 20px;
}
</style> 