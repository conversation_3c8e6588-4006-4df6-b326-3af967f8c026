import{_ as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                         *//* empty css                *//* empty css               *//* empty css               */import{aY as e,aS as s,U as l,a$ as t,aU as n,aZ as i,a_ as u,T as d,ao as r,bT as o,bG as p,al as v,bR as c,bI as _,bJ as m,ai as f,bU as g,bw as h,ax as y}from"./element-plus-h2SQQM64.js";import{f as b}from"./format-3eU4VJ9V.js";import{r as j,d as I,e as k,y as T,l as w,z as x,A as Z,k as z,E as F,t as U,D as A,u as $,F as D,Y as S}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const Y={class:"user-profile-container"},B={class:"card-header"},E={class:"basic-info"},G={class:"info-text"},J={class:"tags"},N={class:"stat-item"},P={class:"stat-item"},R={class:"stat-item"},V={class:"stat-item"},q={class:"card-header"},C={class:"interaction-list"},H={class:"topic-tags"},K={class:"card-header"},L={class:"stat-item"},M={class:"stat-item"},O={class:"stat-item"},Q={class:"card-header"},W={class:"stat-item"},X={class:"stat-item"},aa={class:"stat-item"},ea=a({__name:"UserProfile",props:{visible:Boolean,userId:{type:[Number,String],default:null}},emits:["update:visible"],setup(a,{emit:ea}){const sa=a,la=ea,ta=j(!1),na=j({}),ia=j({}),ua=j({}),da=j({}),ra=j({}),oa=async a=>{a&&(ta.value=!0,setTimeout(()=>{na.value={id:a,nickname:"张三",avatar:"https://i.pravatar.cc/150?u=zhangsan",role:"核心用户",joined_at:"2024-01-15T10:30:00Z",tags:["技术控","早期用户","高价值"]},ia.value={login_days_30d:25,post_count:188,avg_online_time:2.5,last_active:"2小时前"},ua.value={likes_given:512,favorites_given:98,shares_given:45,popular_topics:["AI技术","产品设计","创业心得"]},da.value={total_spent:1280.5,order_count:15,avg_order_value:85.37,conversion_path:[{timestamp:"2024-05-10T10:00:00Z",description:"通过分享链接进入落地页"},{timestamp:"2024-05-10T10:05:00Z",description:'浏览了 "AI大模型" 课程'},{timestamp:"2024-05-11T14:20:00Z",description:'下单购买 "AI大模型" 课程',type:"order"},{timestamp:"2024-05-20T18:30:00Z",description:'复购 "产品设计" 课程',type:"order"}]},ra.value={invited_users:12,downline_users:5,commission_earned:350.8},ta.value=!1},800))};I(()=>sa.userId,a=>{oa(a)});const pa=()=>{la("update:visible",!1)};return k(()=>{sa.userId&&oa(sa.userId)}),(j,I)=>{const k=d,ea=s,sa=t,la=n,oa=u,va=i,ca=e,_a=m,ma=_,fa=y,ga=h;return w(),T(fa,{"model-value":a.visible,title:`用户画像 - ${na.value.nickname}`,direction:"rtl",size:"50%","onUpdate:modelValue":I[0]||(I[0]=a=>j.$emit("update:visible",a)),"before-close":pa},{default:x(()=>[Z((w(),z("div",Y,[F(ca,{class:"profile-section",shadow:"never"},{header:x(()=>[U("div",B,[U("span",null,[F(k,null,{default:x(()=>[F($(r))]),_:1}),I[1]||(I[1]=A(" 基本信息与活跃度",-1))])])]),default:x(()=>[U("div",E,[F(ea,{size:80,src:na.value.avatar},null,8,["src"]),U("div",G,[U("h2",null,[A(l(na.value.nickname)+" ",1),F(sa,{size:"small"},{default:x(()=>[A(l(na.value.role||"普通成员"),1)]),_:1})]),U("p",null,"ID: "+l(na.value.id)+" | 加入时间: "+l($(b)(na.value.joined_at)),1),U("div",J,[(w(!0),z(D,null,S(na.value.tags,a=>(w(),T(sa,{key:a,class:"profile-tag"},{default:x(()=>[A(l(a),1)]),_:2},1024))),128))])])]),F(la),F(va,{gutter:20,class:"activity-stats"},{default:x(()=>[F(oa,{span:6},{default:x(()=>[U("div",N,[U("strong",null,l(ia.value.login_days_30d),1),I[2]||(I[2]=U("span",null,"近30日登录",-1))])]),_:1}),F(oa,{span:6},{default:x(()=>[U("div",P,[U("strong",null,l(ia.value.post_count),1),I[3]||(I[3]=U("span",null,"累计发言",-1))])]),_:1}),F(oa,{span:6},{default:x(()=>[U("div",R,[U("strong",null,l(ia.value.avg_online_time)+"h",1),I[4]||(I[4]=U("span",null,"日均在线",-1))])]),_:1}),F(oa,{span:6},{default:x(()=>[U("div",V,[U("strong",null,l(ia.value.last_active),1),I[5]||(I[5]=U("span",null,"最后活跃",-1))])]),_:1})]),_:1})]),_:1}),F(ca,{class:"profile-section",shadow:"never"},{header:x(()=>[U("div",q,[U("span",null,[F(k,null,{default:x(()=>[F($(c))]),_:1}),I[6]||(I[6]=A(" 内容偏好与互动",-1))])])]),default:x(()=>[F(va,{gutter:20},{default:x(()=>[F(oa,{span:12},{default:x(()=>[I[7]||(I[7]=U("h4",null,"互动行为统计",-1)),U("ul",C,[U("li",null,[F(k,null,{default:x(()=>[F($(o))]),_:1}),A(" 点赞数: "+l(ua.value.likes_given),1)]),U("li",null,[F(k,null,{default:x(()=>[F($(p))]),_:1}),A(" 收藏数: "+l(ua.value.favorites_given),1)]),U("li",null,[F(k,null,{default:x(()=>[F($(v))]),_:1}),A(" 分享数: "+l(ua.value.shares_given),1)])])]),_:1,__:[7]}),F(oa,{span:12},{default:x(()=>[I[8]||(I[8]=U("h4",null,"常参与的话题",-1)),U("div",H,[(w(!0),z(D,null,S(ua.value.popular_topics,a=>(w(),T(sa,{key:a,type:"success",class:"topic-tag"},{default:x(()=>[A(l(a),1)]),_:2},1024))),128))])]),_:1,__:[8]})]),_:1})]),_:1}),F(ca,{class:"profile-section",shadow:"never"},{header:x(()=>[U("div",K,[U("span",null,[F(k,null,{default:x(()=>[F($(f))]),_:1}),I[9]||(I[9]=A(" 消费能力与转化",-1))])])]),default:x(()=>[F(va,{gutter:20},{default:x(()=>[F(oa,{span:8},{default:x(()=>[U("div",L,[U("strong",null,"¥"+l(da.value.total_spent.toFixed(2)),1),I[10]||(I[10]=U("span",null,"累计消费",-1))])]),_:1}),F(oa,{span:8},{default:x(()=>[U("div",M,[U("strong",null,l(da.value.order_count),1),I[11]||(I[11]=U("span",null,"订单数",-1))])]),_:1}),F(oa,{span:8},{default:x(()=>[U("div",O,[U("strong",null,"¥"+l(da.value.avg_order_value.toFixed(2)),1),I[12]||(I[12]=U("span",null,"客单价",-1))])]),_:1})]),_:1}),F(la),I[13]||(I[13]=U("h4",null,"转化路径分析",-1)),F(ma,null,{default:x(()=>[(w(!0),z(D,null,S(da.value.conversion_path,(a,e)=>(w(),T(_a,{key:e,timestamp:$(b)(a.timestamp),type:"order"===a.type?"primary":"success"},{default:x(()=>[A(l(a.description),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1,__:[13]}),F(ca,{class:"profile-section",shadow:"never"},{header:x(()=>[U("div",Q,[U("span",null,[F(k,null,{default:x(()=>[F($(g))]),_:1}),I[14]||(I[14]=A(" 社交网络与影响力",-1))])])]),default:x(()=>[F(va,{gutter:20},{default:x(()=>[F(oa,{span:8},{default:x(()=>[U("div",W,[U("strong",null,l(ra.value.invited_users),1),I[15]||(I[15]=U("span",null,"邀请好友数",-1))])]),_:1}),F(oa,{span:8},{default:x(()=>[U("div",X,[U("strong",null,l(ra.value.downline_users),1),I[16]||(I[16]=U("span",null,"下线成员数",-1))])]),_:1}),F(oa,{span:8},{default:x(()=>[U("div",aa,[U("strong",null,"¥"+l(ra.value.commission_earned.toFixed(2)),1),I[17]||(I[17]=U("span",null,"赚取佣金",-1))])]),_:1})]),_:1})]),_:1})])),[[ga,ta.value]])]),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-feccc22c"]]);export{ea as default};
