<template>
  <div class="help-center">
    <!-- Page Header -->
    <div class="help-header">
      <div class="header-content">
        <div class="header-text">
          <h1>帮助中心</h1>
          <p>快速找到您需要的答案和指南</p>
        </div>
        <div class="search-section">
          <el-input
            v-model="searchQuery"
            placeholder="搜索帮助内容..."
            prefix-icon="Search"
            size="large"
            class="help-search"
            @input="handleSearch"
          />
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <div class="action-card" v-for="action in quickActions" :key="action.id" @click="handleQuickAction(action)">
        <div class="action-icon">
          <el-icon :size="24"><component :is="action.icon" /></el-icon>
        </div>
        <div class="action-content">
          <h3>{{ action.title }}</h3>
          <p>{{ action.description }}</p>
        </div>
        <div class="action-arrow">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="help-content">
      <!-- Categories -->
      <div class="help-sidebar">
        <div class="category-list">
          <div class="category-title">内容分类</div>
          <div
            v-for="category in filteredCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <el-icon><component :is="category.icon" /></el-icon>
            <span>{{ category.name }}</span>
            <el-badge :value="category.count" class="category-badge" />
          </div>
        </div>
      </div>

      <!-- Articles -->
      <div class="help-articles">
        <div class="articles-header">
          <h2>{{ getCurrentCategoryName() }}</h2>
          <div class="articles-meta">
            <span>{{ filteredArticles.length }} 篇文章</span>
          </div>
        </div>
        
        <div class="articles-grid">
          <div
            v-for="article in filteredArticles"
            :key="article.id"
            class="article-card"
            @click="openArticle(article)"
          >
            <div class="article-header">
              <div class="article-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="article-meta">
                <span class="article-category">{{ getCategoryName(article.categoryId) }}</span>
                <span class="article-date">{{ formatDate(article.updatedAt) }}</span>
              </div>
            </div>
            
            <h3 class="article-title">{{ article.title }}</h3>
            <p class="article-excerpt">{{ article.excerpt }}</p>
            
            <div class="article-footer">
              <div class="article-tags">
                <el-tag v-for="tag in article.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
              <div class="article-stats">
                <span><el-icon><View /></el-icon> {{ article.views }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredArticles.length === 0" class="empty-state">
          <el-empty description="没有找到相关帮助内容">
            <template #image>
              <el-icon :size="64" color="#d3d3d3"><DocumentRemove /></el-icon>
            </template>
            <el-button type="primary" @click="clearSearch">清除搜索条件</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- Article Dialog -->
    <el-dialog
      v-model="showArticleDialog"
      :title="selectedArticle?.title"
      width="80%"
      max-width="800px"
      destroy-on-close
    >
      <div v-if="selectedArticle" class="article-content">
        <div class="article-meta-info">
          <el-tag>{{ getCategoryName(selectedArticle.categoryId) }}</el-tag>
          <span class="article-update-time">最后更新: {{ formatDate(selectedArticle.updatedAt) }}</span>
        </div>
        
        <div class="article-body" v-html="selectedArticle.content"></div>
        
        <div class="article-actions">
          <el-button @click="likeArticle(selectedArticle)">
            <el-icon><Like /></el-icon>
            有帮助 ({{ selectedArticle.likes }})
          </el-button>
          <el-button @click="shareArticle(selectedArticle)">
            <el-icon><Share /></el-icon>
            分享
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- Contact Support -->
    <div class="contact-support">
      <div class="support-card">
        <div class="support-content">
          <h3>需要更多帮助？</h3>
          <p>如果您没有找到所需的答案，请联系我们的技术支持团队</p>
        </div>
        <div class="support-actions">
          <el-button type="primary" @click="contactSupport">
            <el-icon><Service /></el-icon>
            联系技术支持
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, ArrowRight, Document, View, Like, Share, Service,
  Monitor, UserFilled, Setting, Wallet, Shield, QuestionFilled,
  DocumentRemove
} from '@element-plus/icons-vue'
import { formatDate } from '@/utils/format'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const showArticleDialog = ref(false)
const selectedArticle = ref(null)

// 快速操作
const quickActions = ref([
  {
    id: 'getting-started',
    title: '快速入门',
    description: '了解系统基本功能和操作流程',
    icon: 'Monitor'
  },
  {
    id: 'user-guide', 
    title: '用户指南',
    description: '详细的功能使用说明和最佳实践',
    icon: 'UserFilled'
  },
  {
    id: 'system-settings',
    title: '系统设置',
    description: '系统配置和管理相关帮助',
    icon: 'Setting'
  },
  {
    id: 'payment-help',
    title: '支付问题',
    description: '支付配置和故障排除指南',
    icon: 'Wallet'
  }
])

// 分类数据
const categories = ref([
  { id: 'all', name: '全部', icon: 'Document', count: 0 },
  { id: 'getting-started', name: '快速入门', icon: 'Monitor', count: 5 },
  { id: 'user-management', name: '用户管理', icon: 'UserFilled', count: 8 },
  { id: 'payment', name: '支付管理', icon: 'Wallet', count: 6 },
  { id: 'system', name: '系统设置', icon: 'Setting', count: 10 },
  { id: 'security', name: '安全相关', icon: 'Shield', count: 4 },
  { id: 'troubleshooting', name: '故障排除', icon: 'QuestionFilled', count: 7 }
])

// 帮助文章数据
const articles = ref([
  {
    id: 1,
    title: '系统首次登录和设置指南',
    excerpt: '了解如何首次登录系统，进行基本设置和配置管理员账户...',
    content: `
      <h2>首次登录步骤</h2>
      <ol>
        <li>使用默认管理员账户登录系统</li>
        <li>立即修改默认密码，确保账户安全</li>
        <li>完善管理员个人信息</li>
        <li>配置系统基本设置</li>
      </ol>
      
      <h2>基本设置配置</h2>
      <p>系统设置包括以下几个方面：</p>
      <ul>
        <li><strong>站点信息</strong>：配置网站名称、描述等基本信息</li>
        <li><strong>邮件设置</strong>：配置SMTP服务用于发送通知邮件</li>
        <li><strong>支付配置</strong>：设置支付通道和相关参数</li>
        <li><strong>安全设置</strong>：配置密码策略、登录限制等</li>
      </ul>
      
      <h2>注意事项</h2>
      <div class="warning">
        <p>⚠️ 首次登录后请务必：</p>
        <ul>
          <li>修改默认管理员密码</li>
          <li>启用二次验证（如果可用）</li>
          <li>定期备份系统数据</li>
        </ul>
      </div>
    `,
    categoryId: 'getting-started',
    tags: ['新手', '设置', '安全'],
    views: 1250,
    likes: 45,
    updatedAt: '2024-01-15'
  },
  {
    id: 2,
    title: '用户账户管理完整指南',
    excerpt: '学习如何创建、编辑、禁用用户账户，以及角色权限的分配...',
    content: `
      <h2>用户账户创建</h2>
      <p>创建新用户账户的步骤：</p>
      <ol>
        <li>进入"用户管理"页面</li>
        <li>点击"添加用户"按钮</li>
        <li>填写用户基本信息</li>
        <li>分配用户角色和权限</li>
        <li>设置账户状态</li>
      </ol>
      
      <h2>角色权限管理</h2>
      <p>系统支持以下角色类型：</p>
      <ul>
        <li><strong>超级管理员</strong>：拥有所有权限</li>
        <li><strong>管理员</strong>：拥有大部分管理权限</li>
        <li><strong>分站管理员</strong>：管理特定分站</li>
        <li><strong>代理商</strong>：推广和代理功能</li>
        <li><strong>分销员</strong>：基础分销功能</li>
        <li><strong>普通用户</strong>：基本功能使用</li>
      </ul>
    `,
    categoryId: 'user-management',
    tags: ['用户', '权限', '角色'],
    views: 890,
    likes: 32,
    updatedAt: '2024-01-18'
  },
  {
    id: 3,
    title: '支付系统配置和故障排除',
    excerpt: '详细的支付渠道配置步骤，以及常见支付问题的解决方案...',
    content: `
      <h2>支付渠道配置</h2>
      <p>系统支持多种支付方式的配置：</p>
      
      <h3>微信支付配置</h3>
      <ol>
        <li>获取微信商户号和API密钥</li>
        <li>在系统中添加微信支付渠道</li>
        <li>配置商户信息和证书</li>
        <li>测试支付功能</li>
      </ol>
      
      <h3>支付宝配置</h3>
      <ol>
        <li>申请支付宝商户账户</li>
        <li>获取应用ID和私钥</li>
        <li>配置公钥信息</li>
        <li>设置回调地址</li>
      </ol>
      
      <h2>常见问题排除</h2>
      <div class="troubleshooting">
        <h4>支付失败</h4>
        <ul>
          <li>检查支付渠道配置是否正确</li>
          <li>确认商户账户状态正常</li>
          <li>检查网络连接和防火墙设置</li>
        </ul>
        
        <h4>回调异常</h4>
        <ul>
          <li>检查回调地址配置</li>
          <li>确认服务器可正常接收回调</li>
          <li>查看支付日志获取详细信息</li>
        </ul>
      </div>
    `,
    categoryId: 'payment',
    tags: ['支付', '配置', '故障'],
    views: 2100,
    likes: 78,
    updatedAt: '2024-01-20'
  }
  // 可以添加更多文章...
])

// 计算属性
const filteredCategories = computed(() => {
  const allCategory = categories.value[0]
  allCategory.count = articles.value.length
  return categories.value
})

const filteredArticles = computed(() => {
  let result = articles.value

  // 分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter(article => article.categoryId === selectedCategory.value)
  }

  // 搜索筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(article => 
      article.title.toLowerCase().includes(query) ||
      article.excerpt.toLowerCase().includes(query) ||
      article.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const handleSearch = () => {
  // 搜索时重置分类为全部
  selectedCategory.value = 'all'
}

const handleQuickAction = (action) => {
  selectedCategory.value = action.id
  searchQuery.value = ''
}

const selectCategory = (categoryId) => {
  selectedCategory.value = categoryId
  searchQuery.value = ''
}

const getCurrentCategoryName = () => {
  const category = categories.value.find(cat => cat.id === selectedCategory.value)
  return category ? category.name : '全部'
}

const getCategoryName = (categoryId) => {
  const category = categories.value.find(cat => cat.id === categoryId)
  return category ? category.name : '未分类'
}

const openArticle = (article) => {
  selectedArticle.value = article
  showArticleDialog.value = true
  // 增加浏览量
  article.views++
}

const likeArticle = (article) => {
  article.likes++
  ElMessage.success('感谢您的反馈！')
}

const shareArticle = (article) => {
  // 分享功能实现
  ElMessage.info('分享功能开发中...')
}

const contactSupport = () => {
  ElMessage.info('技术支持联系方式：<EMAIL>')
}

const clearSearch = () => {
  searchQuery.value = ''
  selectedCategory.value = 'all'
}

onMounted(() => {
  // 页面加载时的初始化操作
  console.log('帮助中心加载完成')
})
</script>

<style lang="scss" scoped>
.help-center {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;

  @media (max-width: 768px) {
    padding: 16px;
  }
}

// 页面头部
.help-header {
  background: white;
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    padding: 24px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 24px;
    }
  }

  .header-text {
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    p {
      font-size: 1.1rem;
      color: #64748b;
      margin: 0;
    }
  }

  .search-section {
    flex-shrink: 0;
    width: 400px;

    @media (max-width: 768px) {
      width: 100%;
    }

    .help-search {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover, &.is-focus {
          border-color: #3b82f6;
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
        }
      }
    }
  }
}

// 快速操作
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 32px;

  .action-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      margin-right: 16px;
    }

    .action-content {
      flex: 1;

      h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 4px 0;
      }

      p {
        color: #64748b;
        margin: 0;
        font-size: 0.9rem;
      }
    }

    .action-arrow {
      color: #94a3b8;
      transition: transform 0.3s ease;
    }

    &:hover .action-arrow {
      transform: translateX(4px);
    }
  }
}

// 主要内容
.help-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
}

// 侧边栏
.help-sidebar {
  background: white;
  border-radius: 12px;
  padding: 24px;
  height: fit-content;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

  @media (max-width: 1024px) {
    order: 2;
  }

  .category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
  }

  .category-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;

    &:hover {
      background: #f8fafc;
    }

    &.active {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      color: white;

      .category-badge :deep(.el-badge__content) {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }
    }

    .el-icon {
      margin-right: 8px;
    }

    span {
      flex: 1;
      font-size: 0.9rem;
    }

    .category-badge {
      :deep(.el-badge__content) {
        background: #e2e8f0;
        color: #64748b;
        border: none;
      }
    }
  }
}

// 文章列表
.help-articles {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

  @media (max-width: 1024px) {
    order: 1;
  }

  .articles-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }

    .articles-meta {
      color: #64748b;
      font-size: 0.9rem;
    }
  }

  .articles-grid {
    display: grid;
    gap: 16px;

    .article-card {
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
      }

      .article-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .article-icon {
          width: 32px;
          height: 32px;
          background: #f1f5f9;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #3b82f6;
        }

        .article-meta {
          display: flex;
          gap: 12px;
          font-size: 0.8rem;
          color: #64748b;

          .article-category {
            background: #e2e8f0;
            padding: 2px 8px;
            border-radius: 4px;
          }
        }
      }

      .article-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 8px 0;
        line-height: 1.4;
      }

      .article-excerpt {
        color: #64748b;
        line-height: 1.6;
        margin: 0 0 16px 0;
      }

      .article-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .article-tags {
          display: flex;
          gap: 6px;
        }

        .article-stats {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #94a3b8;
          font-size: 0.8rem;
        }
      }
    }
  }
}

// 文章内容样式
.article-content {
  .article-meta-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;

    .article-update-time {
      color: #64748b;
      font-size: 0.9rem;
    }
  }

  .article-body {
    line-height: 1.8;
    color: #374151;

    :deep(h2) {
      font-size: 1.4rem;
      font-weight: 600;
      color: #1e293b;
      margin: 32px 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e2e8f0;
    }

    :deep(h3) {
      font-size: 1.2rem;
      font-weight: 600;
      color: #1e293b;
      margin: 24px 0 12px 0;
    }

    :deep(h4) {
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;
      margin: 20px 0 8px 0;
    }

    :deep(p) {
      margin-bottom: 16px;
    }

    :deep(ul), :deep(ol) {
      margin: 16px 0;
      padding-left: 24px;

      li {
        margin-bottom: 8px;
      }
    }

    :deep(.warning) {
      background: #fef3cd;
      border: 1px solid #f59e0b;
      border-radius: 8px;
      padding: 16px;
      margin: 20px 0;

      p {
        margin: 0 0 8px 0;
        color: #92400e;
        font-weight: 600;
      }

      ul {
        margin: 0;
        color: #92400e;
      }
    }

    :deep(.troubleshooting) {
      background: #f0f9ff;
      border: 1px solid #0ea5e9;
      border-radius: 8px;
      padding: 16px;
      margin: 20px 0;

      h4 {
        color: #0c4a6e;
        margin-top: 16px;

        &:first-child {
          margin-top: 0;
        }
      }

      ul {
        color: #0c4a6e;
      }
    }
  }

  .article-actions {
    margin-top: 32px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
  }
}

// 联系支持
.contact-support {
  margin-top: 32px;

  .support-card {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 16px;
    padding: 32px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: 20px;
    }

    .support-content {
      h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      p {
        margin: 0;
        opacity: 0.9;
      }
    }

    .support-actions {
      .el-button {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.25);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;

  :deep(.el-empty__description p) {
    color: #94a3b8;
    font-size: 1rem;
  }
}
</style>