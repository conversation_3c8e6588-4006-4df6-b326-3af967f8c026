<template>
  <el-dialog
    :model-value="visible"
    title="角色用户"
    width="800px"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="role-users">
      <div class="role-info">
        <h4>角色：{{ roleData.name }}</h4>
        <p>当前角色下共有 {{ userList.length }} 个用户</p>
      </div>
      
      <el-divider />
      
      <div class="toolbar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" @click="handleAddUser">
          <el-icon><Plus /></el-icon>
          添加用户
        </el-button>
      </div>
      
      <el-table
        :data="filteredUserList"
        style="width: 100%; margin-top: 16px"
        v-loading="loading"
      >
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column label="用户信息" width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :src="row.avatar" size="small">
                {{ row.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="email">{{ row.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="joinTime" label="加入时间" width="160" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveUser(row)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const loading = ref(false)
const searchKeyword = ref('')

// 模拟用户数据
const userList = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    phone: '138****1234',
    avatar: '',
    status: 'active',
    joinTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    username: 'editor',
    email: '<EMAIL>',
    phone: '139****5678',
    avatar: '',
    status: 'active',
    joinTime: '2024-01-02 14:30:00'
  },
  {
    id: 3,
    username: 'viewer',
    email: '<EMAIL>',
    phone: '137****9012',
    avatar: '',
    status: 'inactive',
    joinTime: '2024-01-03 09:15:00'
  }
])

const filteredUserList = computed(() => {
  if (!searchKeyword.value) {
    return userList.value
  }
  return userList.value.filter(user => 
    user.username.includes(searchKeyword.value) ||
    user.email.includes(searchKeyword.value) ||
    user.phone.includes(searchKeyword.value)
  )
})

watch(() => props.visible, (val) => {
  if (val) {
    loadUserList()
  }
})

const loadUserList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    console.log('加载角色用户列表:', props.roleData)
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

const handleAddUser = () => {
  ElMessage.info('添加用户功能待实现')
}

const handleRemoveUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要将用户 "${user.username}" 从角色 "${props.roleData.name}" 中移除吗？`,
      '确认移除',
      {
        type: 'warning'
      }
    )
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 从列表中移除
    const index = userList.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      userList.value.splice(index, 1)
    }
    
    ElMessage.success('移除成功')
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

const handleClose = () => {
  emit('update:visible', false)
  searchKeyword.value = ''
}
</script>

<style lang="scss" scoped>
.role-users {
  .role-info {
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      .username {
        font-weight: 500;
        color: #303133;
      }
      
      .email {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
