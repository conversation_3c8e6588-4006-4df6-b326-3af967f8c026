import{_ as a}from"./index-DtXAftX0.js";/* empty css                         *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                *//* empty css               */import{af as e,ag as l,r as t,L as s,e as n,y as r,l as d,z as i,k as o,B as u,E as c,t as p,D as m,F as f,Y as _,u as h}from"./vue-vendor-Dy164gUc.js";import{aY as v,aZ as g,a_ as b,U as y,a$ as w,bh as k,bi as j,bI as A,bJ as x,at as T,T as N,aX as P,c0 as Z,Q as z}from"./element-plus-h2SQQM64.js";import{P as D}from"./PageLayout-C6qH3ReN.js";import"./utils-D1VZuEZr.js";const I={key:0,class:"order-detail"},q={class:"card-header"},C={class:"info-item"},F={class:"info-item"},L={class:"amount"},M={class:"info-item"},O={class:"info-item"},V={class:"info-item"},$={class:"info-item"},S={class:"info-item"},U={class:"info-item"},Y={class:"info-item"},B={class:"log-content"},E={class:"log-action"},J={class:"log-operator"},K={key:0,class:"log-remark"},Q=a({__name:"OrderDetail",setup(a){e();const Q=l(),R=t(!0),X=s({id:"",orderNo:"",amount:0,status:"",paymentMethod:"",createdAt:"",paidAt:"",completedAt:"",user:{username:"",phone:"",email:""},items:[],logs:[]}),G=a=>({pending:"待支付",paid:"已支付",completed:"已完成",cancelled:"已取消",refunded:"已退款"}[a]||"未知"),H=a=>a?new Date(a).toLocaleString("zh-CN"):"-",W=()=>{window.print()};return n(()=>{(async()=>{try{R.value=!0,await new Promise(a=>setTimeout(a,1e3)),Object.assign(X,{id:Q.params.id,orderNo:"ORD202408020001",amount:99,status:"paid",paymentMethod:"wechat",createdAt:"2024-08-02T10:30:00Z",paidAt:"2024-08-02T10:35:00Z",completedAt:null,user:{username:"张三",phone:"13800138000",email:"<EMAIL>"},items:[{id:1,name:"VIP群组入群费",sku:"VIP-001",price:99,quantity:1}],logs:[{id:1,action:"订单创建",operator:"系统",createdAt:"2024-08-02T10:30:00Z",remark:"用户下单成功"},{id:2,action:"支付成功",operator:"系统",createdAt:"2024-08-02T10:35:00Z",remark:"微信支付成功"}]})}catch(a){z.error("加载订单详情失败"),console.error(a)}finally{R.value=!1}})()}),(a,e)=>{const l=N,t=T,s=w,n=b,z=g,Q=v,aa=j,ea=k,la=x,ta=A;return d(),r(D,{title:`订单详情 #${X.orderNo||""}`,subtitle:"查看订单详细信息",icon:"View",loading:R.value},{actions:i(()=>[c(t,{onClick:e[0]||(e[0]=e=>a.$router.go(-1))},{default:i(()=>[c(l,null,{default:i(()=>[c(h(P))]),_:1}),e[1]||(e[1]=m(" 返回 ",-1))]),_:1,__:[1]}),c(t,{type:"primary",onClick:W},{default:i(()=>[c(l,null,{default:i(()=>[c(h(Z))]),_:1}),e[2]||(e[2]=m(" 打印订单 ",-1))]),_:1,__:[2]})]),default:i(()=>[R.value?u("",!0):(d(),o("div",I,[c(Q,{class:"info-card",shadow:"never"},{header:i(()=>{return[p("div",q,[e[3]||(e[3]=p("h3",null,"订单信息",-1)),c(s,{type:(a=X.status,{pending:"warning",paid:"success",completed:"success",cancelled:"danger",refunded:"info"}[a]||""),size:"large"},{default:i(()=>[m(y(G(X.status)),1)]),_:1},8,["type"])])];var a}),default:i(()=>[c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",C,[e[4]||(e[4]=p("label",null,"订单号：",-1)),p("span",null,y(X.orderNo),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",F,[e[5]||(e[5]=p("label",null,"订单金额：",-1)),p("span",L,"¥"+y(X.amount),1)])]),_:1}),c(n,{span:8},{default:i(()=>{return[p("div",M,[e[6]||(e[6]=p("label",null,"支付方式：",-1)),p("span",null,y((a=X.paymentMethod,{wechat:"微信支付",alipay:"支付宝",bank:"银行卡",balance:"余额支付"}[a]||"未知")),1)])];var a}),_:1})]),_:1}),c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",O,[e[7]||(e[7]=p("label",null,"创建时间：",-1)),p("span",null,y(H(X.createdAt)),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",V,[e[8]||(e[8]=p("label",null,"支付时间：",-1)),p("span",null,y(H(X.paidAt)),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",$,[e[9]||(e[9]=p("label",null,"完成时间：",-1)),p("span",null,y(H(X.completedAt)),1)])]),_:1})]),_:1})]),_:1}),c(Q,{class:"user-card",shadow:"never"},{header:i(()=>e[10]||(e[10]=[p("div",{class:"card-header"},[p("h3",null,"用户信息")],-1)])),default:i(()=>[c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",S,[e[11]||(e[11]=p("label",null,"用户名：",-1)),p("span",null,y(X.user.username),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",U,[e[12]||(e[12]=p("label",null,"手机号：",-1)),p("span",null,y(X.user.phone),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",Y,[e[13]||(e[13]=p("label",null,"邮箱：",-1)),p("span",null,y(X.user.email),1)])]),_:1})]),_:1})]),_:1}),c(Q,{class:"items-card",shadow:"never"},{header:i(()=>e[14]||(e[14]=[p("div",{class:"card-header"},[p("h3",null,"商品信息")],-1)])),default:i(()=>[c(ea,{data:X.items,style:{width:"100%"}},{default:i(()=>[c(aa,{prop:"name",label:"商品名称"}),c(aa,{prop:"sku",label:"SKU"}),c(aa,{prop:"price",label:"单价",width:"120"},{default:i(({row:a})=>[m(" ¥"+y(a.price),1)]),_:1}),c(aa,{prop:"quantity",label:"数量",width:"80"}),c(aa,{label:"小计",width:"120"},{default:i(({row:a})=>[m(" ¥"+y((a.price*a.quantity).toFixed(2)),1)]),_:1})]),_:1},8,["data"])]),_:1}),c(Q,{class:"logs-card",shadow:"never"},{header:i(()=>e[15]||(e[15]=[p("div",{class:"card-header"},[p("h3",null,"操作记录")],-1)])),default:i(()=>[c(ta,null,{default:i(()=>[(d(!0),o(f,null,_(X.logs,a=>(d(),r(la,{key:a.id,timestamp:H(a.createdAt),placement:"top"},{default:i(()=>[p("div",B,[p("div",E,y(a.action),1),p("div",J,"操作人："+y(a.operator),1),a.remark?(d(),o("div",K,y(a.remark),1)):u("",!0)])]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]))]),_:1},8,["title","loading"])}}},[["__scopeId","data-v-af268548"]]);export{Q as default};
