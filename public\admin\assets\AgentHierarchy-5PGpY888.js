import{_ as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                    *//* empty css                     *//* empty css               *//* empty css                    *//* empty css                 *//* empty css                  *//* empty css                *//* empty css                        *//* empty css                *//* empty css                *//* empty css                       *//* empty css                        */import{T as e,aR as l,at as t,aw as s,aj as n,U as i,ae as d,a6 as o,ai as c,cf as r,aS as u,a$ as m,Y as _,az as v,cg as p,aB as g,aC as f,ac as h,b0 as b,ch as y,ci as w,bh as k,bi as V,ba as j,aN as C,aT as x,bw as z,bm as A,bn as U,aY as T,bp as $,bq as q,aM as D,b9 as L,b8 as B,cj as P,ay as Y,bj as E,bg as R,Q as F,R as M}from"./element-plus-h2SQQM64.js";import{r as N,L as O,c as S,e as H,k as I,l as K,t as Q,E as G,z as J,D as W,u as X,y as Z,A as aa,F as ea,Y as la,B as ta}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const sa={class:"agent-hierarchy-container"},na={class:"page-header"},ia={class:"header-content"},da={class:"header-actions"},oa={class:"stats-grid"},ca={class:"stat-card"},ra={class:"stat-icon"},ua={class:"stat-content"},ma={class:"stat-value"},_a={class:"stat-card"},va={class:"stat-icon"},pa={class:"stat-content"},ga={class:"stat-value"},fa={class:"stat-card"},ha={class:"stat-icon"},ba={class:"stat-content"},ya={class:"stat-value"},wa={class:"stat-card"},ka={class:"stat-icon"},Va={class:"stat-content"},ja={class:"stat-value"},Ca={class:"card-header"},xa={class:"header-left"},za={class:"header-actions"},Aa={key:0,class:"tree-view"},Ua={class:"tree-node"},Ta={class:"node-info"},$a={class:"node-details"},qa={class:"node-name"},Da={class:"node-meta"},La={class:"node-code"},Ba={class:"node-stats"},Pa={class:"stat-item"},Ya={class:"stat-value"},Ea={class:"stat-item"},Ra={class:"stat-value"},Fa={class:"node-actions"},Ma={key:1,class:"table-view"},Na={class:"agent-info"},Oa={class:"agent-details"},Sa={class:"agent-name"},Ha={class:"agent-code"},Ia={class:"level-path"},Ka={class:"commission-amount"},Qa={class:"dialog-footer"},Ga={key:0,class:"agent-detail"},Ja={class:"detail-header"},Wa={class:"agent-info"},Xa={class:"agent-code"},Za={class:"info-grid"},ae={class:"info-item"},ee={class:"info-item"},le={class:"info-item"},te={class:"info-item"},se={class:"info-item"},ne={class:"info-item"},ie={class:"performance-stats"},de={class:"stat-row"},oe={class:"stat-item"},ce={class:"stat-value"},re={class:"stat-item"},ue={class:"stat-value"},me={class:"stat-item"},_e={class:"stat-value"},ve={class:"stat-item"},pe={class:"stat-value"},ge=a({__name:"AgentHierarchy",setup(a){const ge=N(!1),fe=N("tree"),he=N(!1),be=N(!1),ye=N(!1),we=N(!1),ke=N(!1),Ve=N(!1),je=N(!1),Ce=N(null),xe=N("basic"),ze=N([]),Ae=O({totalAgents:0,directChildren:0,maxLevel:0,totalCommission:0}),Ue={children:"children",label:"agent_name"},Te={value:"id",label:"agent_name",children:"children"},$e=O({agent_name:"",phone:"",email:"",agent_level:"",parent_id:null,remark:""}),qe=N({agent_name:"",phone:"",email:"",agent_level:"",remark:""}),De=N({agent_id:null,new_parent_id:null,reason:""}),Le={agent_name:[{required:!0,message:"请输入代理商姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],agent_level:[{required:!0,message:"请选择代理等级",trigger:"change"}]},Be=N(),Pe=S(()=>{const a=(e,l=1,t=[])=>{let s=[];return e.forEach(e=>{const n=[...t,e.agent_name],i={...e,level:l,level_path:n};s.push(i),e.children&&e.children.length>0&&(s=s.concat(a(e.children,l+1,n)))}),s};return a(ze.value)}),Ye=S(()=>ze.value),Ee=async()=>{await Ge()},Re=()=>{Fe(),he.value=!0},Fe=()=>{Object.assign($e,{agent_name:"",phone:"",email:"",agent_level:"",parent_id:null,remark:""}),Be.value?.clearValidate()},Me=async()=>{try{await Be.value.validate(),je.value=!0,await new Promise(a=>setTimeout(a,1e3)),F.success("添加代理商成功"),he.value=!1,await Ge()}catch(a){console.error("添加代理商失败:",a),!1!==a&&F.error("添加代理商失败")}finally{je.value=!1}},Ne=a=>{Ce.value=a,xe.value="basic",be.value=!0},Oe=a=>{Fe(),$e.parent_id=[a.id],he.value=!0},Se=async({action:a,data:e})=>{switch(a){case"edit":l=e,Ce.value=l,qe.value={agent_name:l.agent_name,phone:l.phone,email:l.email,agent_level:l.agent_level,remark:l.remark||""},ye.value=!0;break;case"commission":(a=>{Ce.value=a,we.value=!0,loadAgentCommissionData(a.id)})(e);break;case"performance":(a=>{Ce.value=a,ke.value=!0,loadAgentPerformanceData(a.id)})(e);break;case"transfer":(a=>{Ce.value=a,De.value={agent_id:a.id,new_parent_id:null,reason:""},Ve.value=!0})(e);break;case"disable":await He(e,"inactive");break;case"enable":await He(e,"active")}var l},He=async(a,e)=>{try{const l="active"===e?"启用":"停用";await M.confirm(`确定要${l}代理商 "${a.agent_name}" 吗？`,`确认${l}`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(a=>setTimeout(a,500)),F.success(`${l}成功`),await Ge()}catch{F.info("已取消操作")}},Ie=a=>({junior:"info",intermediate:"warning",senior:"success",gold:"danger"}[a]||"info"),Ke=a=>({junior:"初级代理",intermediate:"中级代理",senior:"高级代理",gold:"金牌代理"}[a]||"未知等级"),Qe=a=>a?new Date(a).toLocaleDateString("zh-CN"):"-",Ge=async()=>{ge.value=!0;try{await new Promise(a=>setTimeout(a,1e3)),ze.value=[{id:1,agent_name:"张三",agent_code:"A001",agent_level:"gold",phone:"13800138001",email:"<EMAIL>",avatar:"",status:"active",children_count:2,total_commission:15e3,month_commission:3e3,total_orders:150,month_orders:25,team_count:8,created_at:"2024-01-01",children:[{id:2,agent_name:"李四",agent_code:"A002",agent_level:"senior",phone:"13800138002",email:"<EMAIL>",avatar:"",status:"active",children_count:1,total_commission:8e3,month_commission:1500,total_orders:80,month_orders:12,team_count:3,created_at:"2024-01-15",children:[{id:3,agent_name:"王五",agent_code:"A003",agent_level:"intermediate",phone:"13800138003",email:"<EMAIL>",avatar:"",status:"active",children_count:0,total_commission:3e3,month_commission:800,total_orders:30,month_orders:8,team_count:1,created_at:"2024-02-01",children:[]}]},{id:4,agent_name:"赵六",agent_code:"A004",agent_level:"junior",phone:"13800138004",email:"<EMAIL>",avatar:"",status:"inactive",children_count:0,total_commission:1200,month_commission:200,total_orders:15,month_orders:3,team_count:1,created_at:"2024-02-15",children:[]}]}];const a=(a=>{let e=0,l=0,t=0,s=0;const n=(a,i=1)=>{a.forEach(a=>{e++,s+=a.total_commission||0,t=Math.max(t,i),1===i&&(l+=a.children_count||0),a.children&&a.children.length>0&&n(a.children,i+1)})};return n(a),{totalAgents:e,directChildren:l,maxLevel:t,totalCommission:s}})(ze.value);Object.assign(Ae,a)}catch(a){console.error("加载层级数据失败:",a),F.error("加载数据失败")}finally{ge.value=!1}};return H(()=>{Ge()}),(a,F)=>{const M=e,N=t,O=U,S=A,H=u,ye=m,we=f,ke=g,Ve=v,qe=r,De=V,Fe=C,He=k,Ge=T,Je=D,We=q,Xe=B,Ze=L,al=P,el=$,ll=Y,tl=R,sl=E,nl=z;return K(),I("div",sa,[Q("div",na,[Q("div",ia,[F[13]||(F[13]=Q("div",{class:"header-left"},[Q("h1",{class:"page-title"},"团队层级管理"),Q("p",{class:"page-subtitle"},"管理代理商团队结构和层级关系")],-1)),Q("div",da,[G(N,{onClick:Ee,loading:ge.value},{default:J(()=>[G(M,null,{default:J(()=>[G(X(l))]),_:1}),F[11]||(F[11]=W(" 刷新数据 ",-1))]),_:1,__:[11]},8,["loading"]),G(N,{type:"primary",onClick:Re},{default:J(()=>[G(M,null,{default:J(()=>[G(X(s))]),_:1}),F[12]||(F[12]=W(" 添加下级代理 ",-1))]),_:1,__:[12]})])])]),Q("div",oa,[Q("div",ca,[Q("div",ra,[G(M,null,{default:J(()=>[G(X(n))]),_:1})]),Q("div",ua,[Q("div",ma,i(Ae.totalAgents),1),F[14]||(F[14]=Q("div",{class:"stat-label"},"团队总人数",-1))])]),Q("div",_a,[Q("div",va,[G(M,null,{default:J(()=>[G(X(d))]),_:1})]),Q("div",pa,[Q("div",ga,i(Ae.directChildren),1),F[15]||(F[15]=Q("div",{class:"stat-label"},"直属下级",-1))])]),Q("div",fa,[Q("div",ha,[G(M,null,{default:J(()=>[G(X(o))]),_:1})]),Q("div",ba,[Q("div",ya,i(Ae.maxLevel),1),F[16]||(F[16]=Q("div",{class:"stat-label"},"最大层级",-1))])]),Q("div",wa,[Q("div",ka,[G(M,null,{default:J(()=>[G(X(c))]),_:1})]),Q("div",Va,[Q("div",ja,"¥"+i(Ae.totalCommission),1),F[17]||(F[17]=Q("div",{class:"stat-label"},"团队总佣金",-1))])])]),G(Ge,{class:"hierarchy-card",shadow:"never"},{header:J(()=>[Q("div",Ca,[Q("div",xa,[G(M,null,{default:J(()=>[G(X(d))]),_:1}),F[18]||(F[18]=Q("span",null,"团队层级结构",-1))]),Q("div",za,[G(S,{modelValue:fe.value,"onUpdate:modelValue":F[0]||(F[0]=a=>fe.value=a),size:"small"},{default:J(()=>[G(O,{value:"tree"},{default:J(()=>F[19]||(F[19]=[W("树形视图",-1)])),_:1,__:[19]}),G(O,{value:"table"},{default:J(()=>F[20]||(F[20]=[W("表格视图",-1)])),_:1,__:[20]})]),_:1},8,["modelValue"])])])]),default:J(()=>["tree"===fe.value?(K(),I("div",Aa,[G(qe,{ref:"hierarchyTree",data:ze.value,props:Ue,"expand-on-click-node":!1,"default-expand-all":!1,"node-key":"id",class:"hierarchy-tree"},{default:J(({node:a,data:e})=>[Q("div",Ua,[Q("div",Ta,[G(H,{size:32,src:e.avatar,class:"node-avatar"},{default:J(()=>[W(i(e.agent_name?.charAt(0)),1)]),_:2},1032,["src"]),Q("div",$a,[Q("div",qa,i(e.agent_name),1),Q("div",Da,[G(ye,{type:Ie(e.agent_level),size:"small"},{default:J(()=>[W(i(Ke(e.agent_level)),1)]),_:2},1032,["type"]),Q("span",La,i(e.agent_code),1)])])]),Q("div",Ba,[Q("div",Pa,[F[21]||(F[21]=Q("span",{class:"stat-label"},"下级:",-1)),Q("span",Ya,i(e.children_count||0),1)]),Q("div",Ea,[F[22]||(F[22]=Q("span",{class:"stat-label"},"佣金:",-1)),Q("span",Ra,"¥"+i(e.total_commission||0),1)])]),Q("div",Fa,[G(N,{size:"small",type:"primary",link:"",onClick:a=>Ne(e)},{default:J(()=>[G(M,null,{default:J(()=>[G(X(_))]),_:1}),F[23]||(F[23]=W(" 详情 ",-1))]),_:2,__:[23]},1032,["onClick"]),G(N,{size:"small",type:"success",link:"",onClick:a=>Oe(e)},{default:J(()=>[G(M,null,{default:J(()=>[G(X(s))]),_:1}),F[24]||(F[24]=W(" 添加下级 ",-1))]),_:2,__:[24]},1032,["onClick"]),G(Ve,{onCommand:Se,trigger:"click"},{dropdown:J(()=>[G(ke,null,{default:J(()=>[G(we,{command:{action:"edit",data:e}},{default:J(()=>[G(M,null,{default:J(()=>[G(X(h))]),_:1}),F[26]||(F[26]=W(" 编辑信息 ",-1))]),_:2,__:[26]},1032,["command"]),G(we,{command:{action:"commission",data:e}},{default:J(()=>[G(M,null,{default:J(()=>[G(X(c))]),_:1}),F[27]||(F[27]=W(" 佣金管理 ",-1))]),_:2,__:[27]},1032,["command"]),G(we,{command:{action:"performance",data:e}},{default:J(()=>[G(M,null,{default:J(()=>[G(X(o))]),_:1}),F[28]||(F[28]=W(" 业绩分析 ",-1))]),_:2,__:[28]},1032,["command"]),G(we,{command:{action:"transfer",data:e},divided:""},{default:J(()=>[G(M,null,{default:J(()=>[G(X(b))]),_:1}),F[29]||(F[29]=W(" 转移代理 ",-1))]),_:2,__:[29]},1032,["command"]),"active"===e.status?(K(),Z(we,{key:0,command:{action:"disable",data:e}},{default:J(()=>[G(M,null,{default:J(()=>[G(X(y))]),_:1}),F[30]||(F[30]=W(" 停用代理 ",-1))]),_:2,__:[30]},1032,["command"])):(K(),Z(we,{key:1,command:{action:"enable",data:e}},{default:J(()=>[G(M,null,{default:J(()=>[G(X(w))]),_:1}),F[31]||(F[31]=W(" 启用代理 ",-1))]),_:2,__:[31]},1032,["command"]))]),_:2},1024)]),default:J(()=>[G(N,{size:"small",type:"info",link:""},{default:J(()=>[G(M,null,{default:J(()=>[G(X(p))]),_:1}),F[25]||(F[25]=W(" 更多 ",-1))]),_:1,__:[25]})]),_:2},1024)])])]),_:1},8,["data"])])):(K(),I("div",Ma,[aa((K(),Z(He,{data:Pe.value,class:"hierarchy-table"},{default:J(()=>[G(De,{prop:"agent_name",label:"代理商","min-width":"200"},{default:J(({row:a})=>[Q("div",Na,[G(H,{size:32,src:a.avatar},{default:J(()=>[W(i(a.agent_name?.charAt(0)),1)]),_:2},1032,["src"]),Q("div",Oa,[Q("div",Sa,i(a.agent_name),1),Q("div",Ha,i(a.agent_code),1)])])]),_:1}),G(De,{prop:"level_path",label:"层级路径","min-width":"150"},{default:J(({row:a})=>[Q("div",Ia,[(K(!0),I(ea,null,la(a.level_path,(e,l)=>(K(),I("span",{key:l,class:"level-item"},[W(i(e)+" ",1),l<a.level_path.length-1?(K(),Z(M,{key:0},{default:J(()=>[G(X(j))]),_:1})):ta("",!0)]))),128))])]),_:1}),G(De,{prop:"agent_level",label:"代理等级",width:"120"},{default:J(({row:a})=>[G(ye,{type:Ie(a.agent_level),size:"small"},{default:J(()=>[W(i(Ke(a.agent_level)),1)]),_:2},1032,["type"])]),_:1}),G(De,{prop:"children_count",label:"下级数量",width:"100",align:"center"},{default:J(({row:a})=>[G(Fe,{value:a.children_count||0,max:99,class:"children-badge"},{default:J(()=>[G(M,null,{default:J(()=>[G(X(n))]),_:1})]),_:2},1032,["value"])]),_:1}),G(De,{prop:"total_commission",label:"累计佣金",width:"120",align:"right"},{default:J(({row:a})=>[Q("span",Ka,"¥"+i(a.total_commission||0),1)]),_:1}),G(De,{prop:"status",label:"状态",width:"100",align:"center"},{default:J(({row:a})=>[G(ye,{type:"active"===a.status?"success":"danger",size:"small"},{default:J(()=>[W(i("active"===a.status?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),G(De,{prop:"created_at",label:"加入时间",width:"120"},{default:J(({row:a})=>[W(i(Qe(a.created_at)),1)]),_:1}),G(De,{label:"操作",width:"200",fixed:"right"},{default:J(({row:a})=>[G(N,{size:"small",type:"primary",link:"",onClick:e=>Ne(a)},{default:J(()=>F[32]||(F[32]=[W(" 详情 ",-1)])),_:2,__:[32]},1032,["onClick"]),G(N,{size:"small",type:"success",link:"",onClick:e=>Oe(a)},{default:J(()=>F[33]||(F[33]=[W(" 添加下级 ",-1)])),_:2,__:[33]},1032,["onClick"]),G(Ve,{onCommand:Se,trigger:"click"},{dropdown:J(()=>[G(ke,null,{default:J(()=>[G(we,{command:{action:"edit",data:a}},{default:J(()=>F[35]||(F[35]=[W("编辑",-1)])),_:2,__:[35]},1032,["command"]),G(we,{command:{action:"commission",data:a}},{default:J(()=>F[36]||(F[36]=[W("佣金",-1)])),_:2,__:[36]},1032,["command"]),G(we,{command:{action:"performance",data:a}},{default:J(()=>F[37]||(F[37]=[W("业绩",-1)])),_:2,__:[37]},1032,["command"])]),_:2},1024)]),default:J(()=>[G(N,{size:"small",type:"info",link:""},{default:J(()=>[F[34]||(F[34]=W(" 更多",-1)),G(M,null,{default:J(()=>[G(X(x))]),_:1})]),_:1,__:[34]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[nl,ge.value]])]))]),_:1}),G(ll,{modelValue:he.value,"onUpdate:modelValue":F[8]||(F[8]=a=>he.value=a),title:"添加下级代理",width:"600px",class:"add-agent-dialog"},{footer:J(()=>[Q("div",Qa,[G(N,{onClick:F[7]||(F[7]=a=>he.value=!1)},{default:J(()=>F[38]||(F[38]=[W("取消",-1)])),_:1,__:[38]}),G(N,{type:"primary",onClick:Me,loading:je.value},{default:J(()=>F[39]||(F[39]=[W(" 确定添加 ",-1)])),_:1,__:[39]},8,["loading"])])]),default:J(()=>[G(el,{model:$e,rules:Le,ref_key:"addFormRef",ref:Be,"label-width":"100px"},{default:J(()=>[G(We,{label:"代理商姓名",prop:"agent_name"},{default:J(()=>[G(Je,{modelValue:$e.agent_name,"onUpdate:modelValue":F[1]||(F[1]=a=>$e.agent_name=a),placeholder:"请输入代理商姓名"},null,8,["modelValue"])]),_:1}),G(We,{label:"手机号码",prop:"phone"},{default:J(()=>[G(Je,{modelValue:$e.phone,"onUpdate:modelValue":F[2]||(F[2]=a=>$e.phone=a),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),G(We,{label:"邮箱地址",prop:"email"},{default:J(()=>[G(Je,{modelValue:$e.email,"onUpdate:modelValue":F[3]||(F[3]=a=>$e.email=a),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),G(We,{label:"代理等级",prop:"agent_level"},{default:J(()=>[G(Ze,{modelValue:$e.agent_level,"onUpdate:modelValue":F[4]||(F[4]=a=>$e.agent_level=a),placeholder:"请选择代理等级"},{default:J(()=>[G(Xe,{label:"初级代理",value:"junior"}),G(Xe,{label:"中级代理",value:"intermediate"}),G(Xe,{label:"高级代理",value:"senior"}),G(Xe,{label:"金牌代理",value:"gold"})]),_:1},8,["modelValue"])]),_:1}),G(We,{label:"上级代理",prop:"parent_id"},{default:J(()=>[G(al,{modelValue:$e.parent_id,"onUpdate:modelValue":F[5]||(F[5]=a=>$e.parent_id=a),options:Ye.value,props:Te,placeholder:"请选择上级代理",clearable:""},null,8,["modelValue","options"])]),_:1}),G(We,{label:"备注信息"},{default:J(()=>[G(Je,{modelValue:$e.remark,"onUpdate:modelValue":F[6]||(F[6]=a=>$e.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),G(ll,{modelValue:be.value,"onUpdate:modelValue":F[10]||(F[10]=a=>be.value=a),title:"代理商详情",width:"800px",class:"detail-dialog"},{default:J(()=>[Ce.value?(K(),I("div",Ga,[Q("div",Ja,[G(H,{size:80,src:Ce.value.avatar},{default:J(()=>[W(i(Ce.value.agent_name?.charAt(0)),1)]),_:1},8,["src"]),Q("div",Wa,[Q("h3",null,i(Ce.value.agent_name),1),Q("p",Xa,"代理编号: "+i(Ce.value.agent_code),1),G(ye,{type:Ie(Ce.value.agent_level)},{default:J(()=>[W(i(Ke(Ce.value.agent_level)),1)]),_:1},8,["type"])])]),G(sl,{modelValue:xe.value,"onUpdate:modelValue":F[9]||(F[9]=a=>xe.value=a),class:"detail-tabs"},{default:J(()=>[G(tl,{label:"基本信息",name:"basic"},{default:J(()=>[Q("div",Za,[Q("div",ae,[F[40]||(F[40]=Q("label",null,"手机号码:",-1)),Q("span",null,i(Ce.value.phone||"-"),1)]),Q("div",ee,[F[41]||(F[41]=Q("label",null,"邮箱地址:",-1)),Q("span",null,i(Ce.value.email||"-"),1)]),Q("div",le,[F[42]||(F[42]=Q("label",null,"加入时间:",-1)),Q("span",null,i(Qe(Ce.value.created_at)),1)]),Q("div",te,[F[43]||(F[43]=Q("label",null,"状态:",-1)),G(ye,{type:"active"===Ce.value.status?"success":"danger",size:"small"},{default:J(()=>[W(i("active"===Ce.value.status?"正常":"停用"),1)]),_:1},8,["type"])]),Q("div",se,[F[44]||(F[44]=Q("label",null,"直属下级:",-1)),Q("span",null,i(Ce.value.children_count||0)+"人",1)]),Q("div",ne,[F[45]||(F[45]=Q("label",null,"团队总人数:",-1)),Q("span",null,i(Ce.value.team_count||0)+"人",1)])])]),_:1}),G(tl,{label:"业绩统计",name:"performance"},{default:J(()=>[Q("div",ie,[Q("div",de,[Q("div",oe,[Q("div",ce,"¥"+i(Ce.value.total_commission||0),1),F[46]||(F[46]=Q("div",{class:"stat-label"},"累计佣金",-1))]),Q("div",re,[Q("div",ue,"¥"+i(Ce.value.month_commission||0),1),F[47]||(F[47]=Q("div",{class:"stat-label"},"本月佣金",-1))]),Q("div",me,[Q("div",_e,i(Ce.value.total_orders||0),1),F[48]||(F[48]=Q("div",{class:"stat-label"},"累计订单",-1))]),Q("div",ve,[Q("div",pe,i(Ce.value.month_orders||0),1),F[49]||(F[49]=Q("div",{class:"stat-label"},"本月订单",-1))])])])]),_:1}),G(tl,{label:"团队结构",name:"team"},{default:J(()=>F[50]||(F[50]=[Q("div",{class:"team-structure"},[Q("p",null,"团队层级结构图表将在这里显示")],-1)])),_:1,__:[50]})]),_:1},8,["modelValue"])])):ta("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-56d95fb9"]]);export{ge as default};
