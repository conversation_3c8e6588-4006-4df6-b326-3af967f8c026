<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" @submit.prevent="handleQuery">
        <el-form-item label="域名类型">
          <el-select v-model="queryParams.domain_type" placeholder="全部类型" clearable>
            <el-option label="短链接域名" value="redirect"></el-option>
            <el-option label="中转页域名" value="landing"></el-option>
            <el-option label="API服务域名" value="api"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="域名状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option label="正常" value="normal"></el-option>
            <el-option label="异常" value="error"></el-option>
            <el-option label="封禁" value="banned"></el-option>
            <el-option label="停用" value="stopped"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="域名搜索">
          <el-input v-model="queryParams.domain" placeholder="输入域名关键词" clearable @keyup.enter="handleQuery"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" :disabled="single" @click="handleUpdate(selectedDomains[0])">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
       <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-magic-stick" :disabled="multiple" @click="handleBatchCheck">批量检测</el-button>
      </el-col>
    </el-row>

    <!-- 域名列表 -->
    <el-card>
      <el-table v-loading="loading" :data="domainList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="域名" prop="domain" width="250" />
        <el-table-column label="类型" prop="domain_type" width="120">
          <template #default="scope">
            <el-tag :type="getDomainTypeColor(scope.row.domain_type)">{{ getDomainTypeName(scope.row.domain_type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="优先级" prop="priority" width="100" />
        <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        <el-table-column label="创建时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
             <el-button link type="warning" icon="el-icon-magic-stick" @click="handleSingleCheck(scope.row)">检测</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改域名对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="domainFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入域名，如 domain.com" />
        </el-form-item>
        <el-form-item label="类型" prop="domain_type">
          <el-select v-model="form.domain_type" placeholder="请选择域名类型">
            <el-option label="短链接域名" value="redirect"></el-option>
            <el-option label="中转页域名" value="landing"></el-option>
            <el-option label="API服务域名" value="api"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="normal">正常</el-radio>
            <el-radio label="stopped">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="form.priority" :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDomains, addDomain, updateDomain, deleteDomain, checkDomains, batchDeleteDomains } from '@/api/anti-block';
import Pagination from '@/components/Pagination/index.vue';

// =========== State ===========
const loading = ref(true);
const selectedDomains = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const domainList = ref([]);
const domainFormRef = ref(null);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 10,
    domain: undefined,
    domain_type: undefined,
    status: undefined,
  },
  dialog: {
    visible: false,
    title: ''
  },
  form: {},
  rules: {
    domain: [{ required: true, message: "域名不能为空", trigger: "blur" }],
    domain_type: [{ required: true, message: "域名类型不能为空", trigger: "change" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
    priority: [{ required: true, message: "优先级不能为空", trigger: "blur" }],
  }
});

const { queryParams, dialog, form, rules } = toRefs(data);

// =========== Helpers ===========
const getDomainTypeName = (type) => ({ redirect: '短链接', landing: '中转页', api: 'API服务' }[type] || '未知');
const getDomainTypeColor = (type) => ({ redirect: 'success', landing: 'primary', api: 'warning' }[type] || 'info');
const getStatusName = (status) => ({ normal: '正常', error: '异常', banned: '封禁', stopped: '停用' }[status] || '未知');
const getStatusColor = (status) => ({ normal: 'success', error: 'danger', banned: 'danger', stopped: 'info' }[status] || 'warning');

// =========== Lifecycle ===========
onMounted(() => {
  getList();
});

// =========== API Calls ===========
async function getList() {
  loading.value = true;
  try {
    const response = await getDomains(queryParams.value);
    domainList.value = response.data.data;
    total.value = response.data.total;
  } catch (error) {
    ElMessage.error('获取域名列表失败');
  } finally {
    loading.value = false;
  }
}

// =========== Event Handlers ===========
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

function resetQuery() {
  queryParams.value = {
    page: 1,
    limit: 10,
    domain: undefined,
    domain_type: undefined,
    status: undefined,
  };
  handleQuery();
}

function handleSelectionChange(selection) {
  selectedDomains.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function resetForm() {
  form.value = {
    domain: undefined,
    domain_type: 'redirect',
    status: 'normal',
    priority: 10,
    remark: undefined
  };
  if (domainFormRef.value) {
    domainFormRef.value.resetFields();
  }
}

function handleAdd() {
  resetForm();
  dialog.value.visible = true;
  dialog.value.title = "添加域名";
}

function handleUpdate(row) {
  resetForm();
  const domain = row || selectedDomains.value[0];
  form.value = { ...domain };
  dialog.value.visible = true;
  dialog.value.title = "修改域名";
}

async function handleDelete(row) {
  const ids = row ? [row.id] : selectedDomains.value.map(item => item.id);
  await ElMessageBox.confirm('是否确认删除选中的域名?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
  });
  try {
    await batchDeleteDomains({ ids });
    getList();
    ElMessage.success("删除成功");
  } catch(e) {
    ElMessage.error("删除失败");
  }
}

async function handleSingleCheck(row) {
    loading.value = true;
    try {
        await checkDomains({ ids: [row.id] });
        ElMessage.success("检测任务已提交");
        getList();
    } catch(e) {
        ElMessage.error("检测失败");
    } finally {
        loading.value = false;
    }
}

async function handleBatchCheck() {
    const ids = selectedDomains.value.map(item => item.id);
    loading.value = true;
    try {
        await checkDomains({ ids });
        ElMessage.success("批量检测任务已提交");
        getList();
    } catch(e) {
        ElMessage.error("批量检测失败");
    } finally {
        loading.value = false;
    }
}


function cancel() {
  dialog.value.visible = false;
  resetForm();
}

async function submitForm() {
  await domainFormRef.value.validate();
  const isUpdate = !!form.value.id;
  try {
    if (isUpdate) {
      await updateDomain(form.value.id, form.value);
      ElMessage.success("修改成功");
    } else {
      await addDomain(form.value);
      ElMessage.success("新增成功");
    }
    dialog.value.visible = false;
    getList();
  } catch (error) {
    ElMessage.error("操作失败");
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-card {
  margin-bottom: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style> 