<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建群组模板表
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('group_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_code')->unique()->comment('模板代码');
            $table->string('template_name')->comment('模板名称');
            $table->string('category')->comment('模板分类');
            $table->text('description')->nullable()->comment('模板描述');
            $table->string('cover_image')->nullable()->comment('封面图片');
            $table->json('template_data')->nullable()->comment('模板数据');
            $table->json('custom_fields_config')->nullable()->comment('自定义字段配置');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('is_preset')->default(false)->comment('是否预设模板');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            
            // 营销展示字段
            $table->string('read_count', 20)->default('10万+')->comment('阅读数显示');
            $table->integer('like_count')->default(0)->comment('点赞数');
            $table->integer('want_see_count')->default(0)->comment('想看数');
            $table->string('button_title', 120)->nullable()->comment('按键名称');
            
            // 内容区块字段
            $table->string('group_intro_title')->nullable()->comment('群介绍标题');
            $table->text('group_intro_content')->nullable()->comment('群介绍内容');
            $table->string('faq_title')->nullable()->comment('常见问题标题');
            $table->text('faq_content')->nullable()->comment('常见问题内容');
            $table->text('user_reviews')->nullable()->comment('用户评价');
            
            // 素材相关字段
            $table->string('customer_service_qr')->nullable()->comment('客服二维码');
            $table->string('ad_image')->nullable()->comment('广告图片');
            $table->string('avatar_library', 50)->default('default')->comment('头像库类型');
            
            // 扩展内容字段
            $table->string('extra_title1')->nullable()->comment('扩展标题1');
            $table->text('extra_content1')->nullable()->comment('扩展内容1');
            $table->string('extra_title2')->nullable()->comment('扩展标题2');
            $table->text('extra_content2')->nullable()->comment('扩展内容2');
            
            // JSON格式存储复杂数据
            $table->json('ad_images')->nullable()->comment('广告图片集合');
            $table->json('marketing_config')->nullable()->comment('营销配置');
            
            $table->timestamps();
            
            // 索引
            $table->index(['category', 'is_active']);
            $table->index(['is_preset', 'sort_order']);
            $table->index(['created_by']);
            $table->index(['usage_count']);
            
            // 外键约束
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('group_templates');
    }
};
