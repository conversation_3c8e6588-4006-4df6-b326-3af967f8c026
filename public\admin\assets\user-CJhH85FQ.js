import"./index-DtXAftX0.js";import{f as e}from"./chunk-KZPPZA2C-BZQYgWVq.js";const r=Array.from({length:150},(r,t)=>({id:t+1,username:e.internet.userName(),name:e.person.fullName(),email:e.internet.email(),phone:e.phone.number(),avatar:e.image.avatar(),role:e.helpers.arrayElement(["user","agent","group_owner"]),status:e.helpers.arrayElement([0,1]),vip_level:e.helpers.arrayElement([0,1,2,3,4,5]),balance:e.finance.amount(0,1e4,2),points:e.number.int({min:0,max:1e5}),created_at:e.date.past({years:2}),last_login_at:e.date.recent(),bio:e.lorem.paragraph()})),t=Array.from({length:500},(r,t)=>({id:t+1,user_id:e.number.int({min:1,max:150}),order_no:e.string.uuid(),amount:e.finance.amount(10,500,2),status:e.helpers.arrayElement(["pending","paid","cancelled","refunded"]),created_at:e.date.past({years:1}),wechat_group:{title:e.lorem.words(3)}})),a=Array.from({length:1e3},(r,t)=>({id:t+1,user_id:e.number.int({min:1,max:150}),type:e.helpers.arrayElement(["add","subtract"]),points:e.number.int({min:10,max:1e3}),description:e.lorem.sentence(),created_at:e.date.past({years:1})})),n=()=>Promise.resolve({code:0,data:r[0],message:"成功"}),s=()=>Promise.resolve({code:0,data:{total_orders:e.number.int({min:50,max:200}),total_spent:e.finance.amount(5e3,5e4,2),points:r[0].points,coupons:e.number.int({min:0,max:20})},message:"成功"}),o=e=>{const{limit:a=5}=e,n=t.filter(e=>e.user_id===r[0].id);return Promise.resolve({code:0,data:n.slice(0,a),message:"成功"})},i=e=>{const{limit:t=5}=e,n=a.filter(e=>e.user_id===r[0].id);return Promise.resolve({code:0,data:n.slice(0,t),message:"成功"})},m=r=>{const{period:t="month"}=r,a=Array.from({length:30},(e,r)=>`Day ${r+1}`),n=Array.from({length:30},()=>e.finance.amount(0,1e3,2));return Promise.resolve({code:0,data:{summary:{total_amount:n.reduce((e,r)=>e+parseFloat(r),0).toFixed(2),total_orders:e.number.int({min:10,max:50}),avg_amount:(n.reduce((e,r)=>e+parseFloat(r),0)/n.length).toFixed(2),max_amount:Math.max(...n).toFixed(2)},chart:{labels:a,data:n}},message:"成功"})},l=e=>{const t=r.findIndex(e=>e.id===r[0].id);return-1!==t&&(r[t]={...r[t],...e}),Promise.resolve({code:0,data:null,message:"更新成功"})},u=e=>{const{page:t=1,limit:a=10}=e,n=(t-1)*a,s=t*a;return Promise.resolve({code:0,data:{list:r.slice(n,s),total:r.length},message:"成功"})};function d(){return n()}function c(){return s()}function p(e){return o(e)}function g(e){return i(e)}function f(e){return m(e)}function h(e){return l(e)}function _(e){return u(e)}export{c as a,p as b,g as c,f as d,_ as e,d as g,h as u};
