<template>
  <div class="simple-payment-settings">
    <div class="page-header">
      <h1>支付设置</h1>
      <p>管理系统支付配置和支付方式</p>
    </div>

    <div class="content">
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <h3>支付方式配置</h3>
            <el-button type="primary" @click="saveSettings">
              <el-icon><Check /></el-icon>
              保存设置
            </el-button>
          </div>
        </template>

        <el-tabs v-model="activeTab" class="payment-tabs">
          <!-- 支付宝配置 -->
          <el-tab-pane label="支付宝" name="alipay">
            <div class="payment-config">
              <div class="config-header">
                <div class="header-info">
                  <el-icon class="payment-icon"><CreditCard /></el-icon>
                  <div class="payment-info">
                    <h4>支付宝支付</h4>
                    <p>接入支付宝官方支付接口</p>
                  </div>
                </div>
                <el-switch v-model="config.alipay.enabled" size="large" />
              </div>

              <div v-if="config.alipay.enabled" class="config-form">
                <el-form :model="config.alipay" label-width="120px">
                  <el-form-item label="应用ID">
                    <el-input v-model="config.alipay.app_id" placeholder="请输入支付宝应用ID" />
                  </el-form-item>
                  <el-form-item label="商户号">
                    <el-input v-model="config.alipay.merchant_id" placeholder="请输入商户号" />
                  </el-form-item>
                  <el-form-item label="费率(%)">
                    <el-input-number v-model="config.alipay.fee_rate" :min="0" :max="10" :precision="2" />
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

          <!-- 微信支付配置 -->
          <el-tab-pane label="微信支付" name="wechat">
            <div class="payment-config">
              <div class="config-header">
                <div class="header-info">
                  <el-icon class="payment-icon"><ChatDotRound /></el-icon>
                  <div class="payment-info">
                    <h4>微信支付</h4>
                    <p>接入微信官方支付接口</p>
                  </div>
                </div>
                <el-switch v-model="config.wechat.enabled" size="large" />
              </div>

              <div v-if="config.wechat.enabled" class="config-form">
                <el-form :model="config.wechat" label-width="120px">
                  <el-form-item label="应用ID">
                    <el-input v-model="config.wechat.app_id" placeholder="请输入微信应用ID" />
                  </el-form-item>
                  <el-form-item label="商户号">
                    <el-input v-model="config.wechat.merchant_id" placeholder="请输入商户号" />
                  </el-form-item>
                  <el-form-item label="费率(%)">
                    <el-input-number v-model="config.wechat.fee_rate" :min="0" :max="10" :precision="2" />
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 支付统计 -->
      <el-card class="stats-card">
        <template #header>
          <h3>支付统计</h3>
        </template>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalAmount }}</div>
            <div class="stat-label">总交易金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, CreditCard, ChatDotRound } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('alipay')

const config = reactive({
  alipay: {
    enabled: true,
    app_id: '2021001234567890',
    merchant_id: '2088123456789012',
    fee_rate: 0.6
  },
  wechat: {
    enabled: true,
    app_id: 'wx1234567890abcdef',
    merchant_id: '1234567890',
    fee_rate: 0.6
  }
})

const stats = reactive({
  totalAmount: '¥1,234,567.89',
  totalOrders: '12,345',
  successRate: '99.8'
})

// 方法
const saveSettings = () => {
  ElMessage.success('支付设置保存成功')
}

onMounted(() => {
  console.log('简化支付设置页面已加载')
})
</script>

<style scoped>
.simple-payment-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-icon {
  font-size: 24px;
  color: #409eff;
}

.payment-info h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
}

.payment-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.config-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
}
</style>
