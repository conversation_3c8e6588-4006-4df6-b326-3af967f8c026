/**
 * 角色导航配置
 * 定义不同角色用户可以访问的菜单和路由
 */

// 角色层级定义 - 用于数据访问权限控制（优化版）
export const roleHierarchy = {
  admin: {
    level: 0,
    name: '超级管理员',
    canViewRoles: ['admin', 'substation', 'agent', 'distributor', 'group_owner', 'user'],
    dataScope: 'all', // 可查看所有数据
    dashboardScope: 'global', // 全局数据大屏权限
    financeScope: 'all_finance', // 所有财务数据
    groupCreatePermission: true, // 群组创建权限
    dataExportPermission: true // 数据导出权限
  },
  substation: {
    level: 1,
    name: '分站管理员',
    canViewRoles: ['substation', 'agent', 'distributor', 'group_owner', 'user'],
    dataScope: 'substation_and_below', // 可查看本级别及下级数据
    dashboardScope: 'substation', // 分站级数据大屏权限
    financeScope: 'substation_finance', // 分站财务数据
    groupCreatePermission: true, // 群组创建权限
    dataExportPermission: true // 数据导出权限
  },
  agent: {
    level: 2,
    name: '代理商',
    canViewRoles: ['agent', 'distributor', 'group_owner', 'user'],
    dataScope: 'agent_and_below', // 可查看本级别及下级数据
    dashboardScope: 'agent_team', // 代理商团队数据大屏权限
    financeScope: 'agent_commission', // 代理商佣金数据
    groupCreatePermission: true, // 群组创建权限（保护）
    dataExportPermission: false // 限制数据导出
  },
  distributor: {
    level: 3,
    name: '分销员',
    canViewRoles: ['distributor', 'group_owner', 'user'],
    dataScope: 'distributor_and_below', // 可查看本级别及下级数据
    dashboardScope: 'distributor_personal', // 分销员个人数据大屏权限
    financeScope: 'distributor_commission', // 分销员佣金数据
    groupCreatePermission: true, // 群组创建权限（保护）
    dataExportPermission: false // 限制数据导出
  },
  group_owner: {
    level: 4,
    name: '群主',
    canViewRoles: ['group_owner', 'user'],
    dataScope: 'group_owner_and_below', // 可查看自己和普通用户数据
    dashboardScope: 'group_owner_groups', // 群主群组数据大屏权限
    financeScope: 'group_owner_income', // 群主收入数据
    groupCreatePermission: true, // 群组创建权限（保护）
    dataExportPermission: false // 限制数据导出
  },
  user: {
    level: 5,
    name: '普通用户',
    canViewRoles: ['user'],
    dataScope: 'self_only', // 只能查看自己的数据
    dashboardScope: 'user_personal', // 个人数据大屏权限
    financeScope: 'user_consumption', // 个人消费数据
    groupCreatePermission: true, // 群组创建权限（保护）
    dataExportPermission: false // 限制数据导出
  }
}

// 数据访问权限检查函数
export function canViewUserData(currentUserRole, targetUserRole) {
  const currentRole = roleHierarchy[currentUserRole]
  if (!currentRole) return false

  return currentRole.canViewRoles.includes(targetUserRole)
}

// 获取用户可查看的角色列表
export function getViewableRoles(userRole) {
  const role = roleHierarchy[userRole]
  return role ? role.canViewRoles : []
}

// 角色导航权限配置
export const roleNavigationConfig = {
  // 超级管理员 - 拥有所有权限
  admin: {
    allowedRoutes: ['*'], // * 表示所有路由
    defaultRoute: '/admin/dashboard',
    workbench: '/admin/dashboard'
  },

  // 分站管理员 - 管理本分站相关功能
  substation: {
    allowedRoutes: [
      '/admin/dashboard',
      '/admin/users',
      '/admin/groups',
      '/admin/orders',
      '/admin/finance',
      '/admin/agents',
      '/admin/substations',
      '/admin/distributors',
      '/admin/promotion'
    ],
    defaultRoute: '/admin/dashboard',
    workbench: '/admin/dashboard'
  },
  
  // 代理商 - 代理商相关功能
  agent: {
    allowedRoutes: [
      '/admin/agents',
      '/admin/agents',
      '/admin/agents',
      '/admin/commission-logs',
      '/admin/agents',
      '/admin/agent-performance',
      '/admin/users',
      '/admin/users',
      '/admin/commission-logs',
      '/admin/promotion'
    ],
    defaultRoute: '/admin/agents',
    workbench: '/admin/agents'
  },
  
  // 分销员 - 分销和客户管理功能
  distributor: {
    allowedRoutes: [
      '/admin/distributors',
      '/admin/distributors',
      '/admin/groups',
      '/admin/orders',
      '/admin/commission-logs',
      '/admin/promotion',
      '/admin/users',
      '/admin/users'
    ],
    defaultRoute: '/admin/distributors',
    workbench: '/admin/distributors'
  },

  // 群主 - 群组管理功能
  group_owner: {
    allowedRoutes: [
      '/admin/groups',
      '/admin/groups',
      '/admin/templates',
      '/admin/templates',
      '/admin/users',
      '/admin/users',
      '/admin/orders'
    ],
    defaultRoute: '/admin/groups',
    workbench: '/admin/groups'
  },

  // 普通用户 - 基础功能
  user: {
    allowedRoutes: [
      '/admin/users',
      '/admin/users',
      '/admin/orders',
      '/admin/groups'
    ],
    defaultRoute: '/admin/users',
    workbench: '/admin/users'
  }
}

// 角色专属工作台配置
export const roleWorkbenchConfig = {
  admin: {
    title: '管理员控制台',
    description: '系统全局管理和监控',
    quickActions: [
      { name: '用户管理', path: '/admin/users', icon: 'User' },
      { name: '系统监控', path: '/admin/system-monitor', icon: 'Monitor' },
      { name: '财务总览', path: '/admin/finance', icon: 'Money' },
      { name: '数据分析', path: '/admin/dashboard', icon: 'DataAnalysis' },
      { name: '支付设置', path: '/admin/payment-settings', icon: 'CreditCard' }
    ]
  },
  
  substation: {
    title: '分站管理中心',
    description: '分站运营和用户管理',
    quickActions: [
      { name: '分站用户', path: '/user/list', icon: 'User' },
      { name: '代理商管理', path: '/agent/list', icon: 'Avatar' },
      { name: '分站财务', path: '/substation/finance', icon: 'Money' },
      { name: '权限配置', path: '/substation/permissions', icon: 'Lock' }
    ]
  },
  
  agent: {
    title: '代理商工作台',
    description: '团队管理和业绩分析',
    quickActions: [
      { name: '我的团队', path: '/agent/hierarchy', icon: 'Connection' },
      { name: '佣金管理', path: '/agent/commission', icon: 'Money' },
      { name: '绩效分析', path: '/agent/performance', icon: 'TrendCharts' },
      { name: '申请管理', path: '/agent/applications', icon: 'Document' }
    ]
  },
  
  distributor: {
    title: '分销员工作台',
    description: '客户管理和群组运营',
    quickActions: [
      { name: '客户管理', path: '/distribution/customers', icon: 'User' },
      { name: '我的群组', path: '/community/groups', icon: 'Comment' },
      { name: '推广链接', path: '/promotion/links', icon: 'Link' },
      { name: '佣金查看', path: '/finance/commission-logs', icon: 'Money' }
    ]
  },
  
  group_owner: {
    title: '群主工作台',
    description: '群组内容和成员管理',
    quickActions: [
      { name: '我的群组', path: '/community/groups', icon: 'Comment' },
      { name: '内容管理', path: '/content/management', icon: 'Edit' },
      { name: '群组统计', path: '/community/analytics', icon: 'DataAnalysis' },
      { name: '模板管理', path: '/content/templates', icon: 'DocumentCopy' }
    ]
  },
  
  user: {
    title: '个人中心',
    description: '个人信息和订单管理',
    quickActions: [
      { name: '我的订单', path: '/orders/my', icon: 'Tickets' },
      { name: '我的群组', path: '/community/my-groups', icon: 'Comment' },
      { name: '个人资料', path: '/user/profile', icon: 'User' },
      { name: '账户设置', path: '/user/settings', icon: 'Setting' }
    ]
  }
}

// 菜单权限检查函数
export function checkMenuPermission(route, userRole) {
  if (!userRole || !route) return false
  
  const config = roleNavigationConfig[userRole]
  if (!config) return false
  
  // 管理员拥有所有权限
  if (config.allowedRoutes.includes('*')) return true
  
  // 安全地获取路由路径
  let routePath
  if (typeof route === 'string') {
    routePath = route
  } else if (route && typeof route.path === 'string') {
    routePath = route.path
  } else if (route && typeof route.name === 'string') {
    // 如果没有path但有name，尝试使用name
    routePath = `/${route.name.toLowerCase()}`
  } else {
    // 无法获取有效路径，返回false
    console.warn('无法从路由对象获取有效路径:', route)
    return false
  }
  
  // 确保routePath是字符串
  if (typeof routePath !== 'string') {
    console.warn('路由路径不是字符串类型:', routePath, typeof routePath)
    return false
  }
  
  // 检查路由是否在允许列表中
  return config.allowedRoutes.some(allowedRoute => {
    // 精确匹配
    if (allowedRoute === routePath) return true
    
    // 前缀匹配（支持子路由）
    if (routePath.startsWith(allowedRoute + '/')) return true
    
    return false
  })
}

// 获取用户默认路由
export function getUserDefaultRoute(userRole) {
  const config = roleNavigationConfig[userRole]
  return config?.defaultRoute || '/user/center'
}

// 获取用户工作台配置
export function getUserWorkbenchConfig(userRole) {
  return roleWorkbenchConfig[userRole] || roleWorkbenchConfig.user
}

// 过滤路由菜单
export function filterRoutesByRole(routes, userRole) {
  if (!userRole || !routes) return []
  
  return routes.filter(route => {
    // 跳过隐藏的路由
    if (route.meta?.hidden) return false
    
    // 检查路由权限
    if (!checkMenuPermission(route, userRole)) return false
    
    // 递归过滤子路由
    if (route.children && route.children.length > 0) {
      route.children = filterRoutesByRole(route.children, userRole)
      // 如果所有子路由都被过滤掉，则隐藏父路由
      if (route.children.length === 0 && route.redirect) {
        return false
      }
    }
    
    return true
  })
}

// 角色显示名称映射
export const roleDisplayNames = {
  admin: '超级管理员',
  substation: '分站管理员', 
  agent: '代理商',
  distributor: '分销员',
  group_owner: '群主',
  user: '普通用户'
}

// 获取角色显示名称
export function getRoleDisplayName(role) {
  return roleDisplayNames[role] || '未知角色'
}