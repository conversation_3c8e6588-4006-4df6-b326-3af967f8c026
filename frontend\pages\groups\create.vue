<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- 顶部导航 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <NuxtLink to="/groups" class="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              返回群组管理
            </NuxtLink>
          </div>
          <div class="flex items-center space-x-4">
            <!-- 保存状态指示器 -->
            <div v-if="autoSaveStatus" class="flex items-center text-sm text-gray-500">
              <svg v-if="autoSaveStatus === 'saving'" class="animate-spin w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else-if="autoSaveStatus === 'saved'" class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              {{ autoSaveStatus === 'saving' ? '保存中...' : '已保存' }}
            </div>
            
            <button @click="saveAsDraft" :disabled="saving" class="text-gray-600 hover:text-gray-900 disabled:opacity-50 transition-colors">
              {{ saving ? '保存中...' : '保存草稿' }}
            </button>
            <button @click="previewGroup" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
              预览效果
            </button>
            
            <!-- 转化率优化提示 -->
            <div class="relative">
              <button @click="showOptimizationTips = !showOptimizationTips" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </button>
              
              <!-- 优化建议弹窗 -->
              <div v-if="showOptimizationTips" class="absolute right-0 top-12 w-80 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-50">
                <div class="flex items-center justify-between mb-3">
                  <h3 class="font-semibold text-gray-900">💡 转化率优化建议</h3>
                  <button @click="showOptimizationTips = false" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
                <div class="space-y-2 text-sm">
                  <div v-for="tip in optimizationTips" :key="tip.id" class="flex items-start space-x-2">
                    <div :class="['w-2 h-2 rounded-full mt-1.5', tip.completed ? 'bg-green-500' : 'bg-orange-500']"></div>
                    <span :class="tip.completed ? 'text-green-700' : 'text-gray-700'">{{ tip.text }}</span>
                  </div>
                </div>
                <div class="mt-3 pt-3 border-t border-gray-200">
                  <div class="flex items-center justify-between text-xs text-gray-500">
                    <span>转化率预估</span>
                    <span class="font-semibold text-blue-600">{{ estimatedConversionRate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 主要创建区域 -->
        <div class="lg:col-span-2">
          <!-- 进度指示器 -->
          <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h1 class="text-2xl font-bold text-gray-900">创建精品社群</h1>
              <div class="text-sm text-gray-500">
                第 {{ currentStep + 1 }} 步，共 {{ steps.length }} 步
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <div 
                v-for="(step, index) in steps" 
                :key="index"
                class="flex items-center"
              >
                <div 
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                    index <= currentStep 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  ]"
                >
                  {{ index + 1 }}
                </div>
                <div 
                  v-if="index < steps.length - 1"
                  :class="[
                    'w-16 h-1 mx-2',
                    index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  ]"
                ></div>
              </div>
            </div>
            
            <div class="mt-4">
              <h3 class="font-medium text-gray-900">{{ steps[currentStep].title }}</h3>
              <p class="text-sm text-gray-600">{{ steps[currentStep].description }}</p>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <!-- 步骤1：基础信息 -->
            <div v-if="currentStep === 0" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">群组基础信息</h2>
                <p class="text-gray-600">设置您的群组基本信息，这些信息将展示给潜在用户</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    群组名称 <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <input
                      v-model="formData.title"
                      type="text"
                      :class="[
                        'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors',
                        validationErrors.title ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      ]"
                      placeholder="例如：北京创业交流群、上海副业赚钱群"
                      maxlength="50"
                    />
                    <!-- 智能建议按钮 -->
                    <button
                      v-if="smartSuggestions.title.length > 0"
                      @click="showTitleSuggestions = !showTitleSuggestions"
                      class="absolute right-3 top-3 text-blue-600 hover:text-blue-800"
                      type="button"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                    </button>
                  </div>
                  
                  <!-- 验证错误提示 -->
                  <div v-if="validationErrors.title" class="mt-1 text-sm text-red-600 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    {{ validationErrors.title }}
                  </div>
                  
                  <!-- 智能建议下拉 -->
                  <div v-if="showTitleSuggestions && smartSuggestions.title.length > 0" class="mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10">
                    <div class="text-xs font-medium text-gray-700 mb-2">💡 智能建议：</div>
                    <div class="space-y-1">
                      <button
                        v-for="suggestion in smartSuggestions.title"
                        :key="suggestion"
                        @click="formData.title = suggestion; showTitleSuggestions = false"
                        class="block w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded transition-colors"
                      >
                        {{ suggestion }}
                      </button>
                    </div>
                  </div>
                  
                  <div class="mt-2 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <p class="text-xs text-gray-500">
                        💡 提示：使用"xxx"作为城市占位符，系统会自动替换为用户所在城市
                      </p>
                      <!-- 转化率影响指示器 -->
                      <div v-if="formData.title.length >= 5" class="flex items-center text-xs text-green-600">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        转化率 +0.8%
                      </div>
                    </div>
                    <span :class="['text-xs', formData.title.length > 40 ? 'text-orange-500' : 'text-gray-400']">
                      {{ formData.title.length }}/50
                    </span>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    群组分类 <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="formData.category"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">请选择分类</option>
                    <option v-for="cat in categories" :key="cat.value" :value="cat.value">
                      {{ cat.label }}
                    </option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    入群价格 <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <span class="absolute left-3 top-3 text-gray-500">¥</span>
                    <input
                      v-model.number="formData.price"
                      type="number"
                      min="0"
                      step="0.01"
                      class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0.00"
                    />
                  </div>
                  <p class="text-xs text-gray-500 mt-1">建议价格：9.9-99.9元</p>
                </div>

                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    群组描述 <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    v-model="formData.description"
                    rows="4"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="详细描述您的群组价值，例如：提供什么服务、解决什么问题、有什么独特优势..."
                    maxlength="500"
                  ></textarea>
                  <div class="mt-2 flex justify-between">
                    <p class="text-xs text-gray-500">清晰的描述能提高转化率</p>
                    <span class="text-xs text-gray-400">{{ formData.description.length }}/500</span>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">群成员上限</label>
                  <input
                    v-model.number="formData.member_limit"
                    type="number"
                    min="10"
                    max="500"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="200"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">当前成员数</label>
                  <input
                    v-model.number="formData.current_members"
                    type="number"
                    min="0"
                    :max="formData.member_limit"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                  />
                  <p class="text-xs text-gray-500 mt-1">显示的初始成员数，建议设置为上限的30-70%</p>
                </div>
              </div>
            </div>

            <!-- 步骤2：营销优化 -->
            <div v-if="currentStep === 1" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">营销转化优化</h2>
                <p class="text-gray-600">设置吸引用户的营销元素，提高转化率</p>
              </div>

              <!-- 社会证明设置 -->
              <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">社会证明设置</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">阅读数显示</label>
                    <input
                      v-model="formData.read_count_display"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="8万+"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">点赞数</label>
                    <input
                      v-model.number="formData.like_count"
                      type="number"
                      min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="1500"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">想看数</label>
                    <input
                      v-model.number="formData.want_see_count"
                      type="number"
                      min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="1000"
                    />
                  </div>
                </div>
              </div>

              <!-- 按钮和展示设置 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">入群按钮文案</label>
                  <input
                    v-model="formData.button_title"
                    type="text"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="立即加入群聊"
                  />
                  <div class="mt-2 flex flex-wrap gap-2">
                    <button 
                      v-for="suggestion in buttonSuggestions" 
                      :key="suggestion"
                      @click="formData.button_title = suggestion"
                      class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded hover:bg-gray-200"
                    >
                      {{ suggestion }}
                    </button>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">头像库选择</label>
                  <select
                    v-model="formData.avatar_library"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="default">默认头像库</option>
                    <option value="business">商务头像库</option>
                    <option value="dating">交友头像库</option>
                    <option value="education">教育头像库</option>
                  </select>
                </div>
              </div>

              <!-- 紧迫感设置 -->
              <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">紧迫感营销</h3>
                <div class="space-y-4">
                  <label class="flex items-center">
                    <input
                      v-model="formData.show_limited_time"
                      type="checkbox"
                      class="mr-3 w-4 h-4 text-blue-600"
                    />
                    <span class="text-sm font-medium">显示限时优惠</span>
                  </label>
                  
                  <div v-if="formData.show_limited_time" class="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">原价</label>
                      <input
                        v-model.number="formData.original_price"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="99.00"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">优惠截止时间</label>
                      <input
                        v-model="formData.discount_end_time"
                        type="datetime-local"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤3：内容配置 -->
            <div v-if="currentStep === 2" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">内容与服务</h2>
                <p class="text-gray-600">配置群组介绍、FAQ和用户评价，增强信任度</p>
              </div>

              <div class="space-y-6">
                <!-- 群组介绍 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">群组详细介绍</h3>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">介绍标题</label>
                      <input
                        v-model="formData.group_intro_title"
                        type="text"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="群简介"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">详细内容</label>
                      <textarea
                        v-model="formData.group_intro_content"
                        rows="6"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="详细介绍群组的价值、服务内容、群规等..."
                        maxlength="1000"
                      ></textarea>
                      <p class="text-xs text-gray-500 mt-1">{{ formData.group_intro_content.length }}/1000</p>
                    </div>
                  </div>
                </div>

                <!-- FAQ配置 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">常见问题</h3>
                    <button
                      @click="addFAQ"
                      class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-700"
                    >
                      + 添加问题
                    </button>
                  </div>
                  
                  <div class="space-y-4">
                    <div
                      v-for="(faq, index) in formData.faqs"
                      :key="index"
                      class="bg-gray-50 rounded-lg p-4"
                    >
                      <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">问题 {{ index + 1 }}</span>
                        <button
                          @click="removeFAQ(index)"
                          class="text-red-600 hover:text-red-800 text-sm"
                        >
                          删除
                        </button>
                      </div>
                      <div class="space-y-3">
                        <input
                          v-model="faq.question"
                          type="text"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          placeholder="输入问题"
                        />
                        <textarea
                          v-model="faq.answer"
                          rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          placeholder="输入答案"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 用户评价 -->
                <div class="border border-gray-200 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">用户评价</h3>
                    <button
                      @click="addReview"
                      class="bg-green-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-green-700"
                    >
                      + 添加评价
                    </button>
                  </div>
                  
                  <div class="space-y-4">
                    <div
                      v-for="(review, index) in formData.reviews"
                      :key="index"
                      class="bg-gray-50 rounded-lg p-4"
                    >
                      <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">评价 {{ index + 1 }}</span>
                        <button
                          @click="removeReview(index)"
                          class="text-red-600 hover:text-red-800 text-sm"
                        >
                          删除
                        </button>
                      </div>
                      <div class="space-y-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <input
                            v-model="review.author"
                            type="text"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            placeholder="用户名"
                          />
                          <select
                            v-model="review.rating"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="5">⭐⭐⭐⭐⭐ 5分</option>
                            <option value="4">⭐⭐⭐⭐ 4分</option>
                            <option value="3">⭐⭐⭐ 3分</option>
                          </select>
                        </div>
                        <textarea
                          v-model="review.content"
                          rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          placeholder="输入评价内容"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤4：付费内容 -->
            <div v-if="currentStep === 3" class="space-y-6">
              <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                  <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">付费后内容</h2>
                <p class="text-gray-600">设置用户付费后能获得的内容或服务</p>
              </div>

              <div class="space-y-6">
                <!-- 内容类型选择 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-3">内容类型</label>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <label
                      v-for="type in contentTypes"
                      :key="type.value"
                      class="relative flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50"
                      :class="formData.paid_content_type === type.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
                    >
                      <input
                        v-model="formData.paid_content_type"
                        type="radio"
                        :value="type.value"
                        class="sr-only"
                      />
                      <div class="text-center">
                        <div class="text-2xl mb-2">{{ type.icon }}</div>
                        <div class="text-sm font-medium">{{ type.label }}</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- 二维码上传 -->
                <div v-if="formData.paid_content_type === 'qr_code'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">入群二维码</h3>
                  <div class="flex items-center space-x-6">
                    <div class="flex-shrink-0">
                      <div class="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                        <img v-if="formData.qr_code" :src="formData.qr_code" class="w-full h-full object-cover rounded-lg" />
                        <div v-else class="text-center">
                          <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                          </svg>
                          <p class="text-sm text-gray-500">上传二维码</p>
                        </div>
                      </div>
                    </div>
                    <div class="flex-1">
                      <input
                        v-model="formData.qr_code"
                        type="url"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入二维码图片URL"
                      />
                      <p class="text-sm text-gray-500 mt-2">
                        💡 建议上传到图床后填入链接，或使用我们的上传服务
                      </p>
                    </div>
                  </div>
                </div>

                <!-- 其他内容类型的配置 -->
                <div v-if="formData.paid_content_type === 'link'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">下载链接</h3>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">链接地址</label>
                      <input
                        v-model="formData.paid_link"
                        type="url"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="https://example.com/download"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">链接描述</label>
                      <input
                        v-model="formData.paid_link_desc"
                        type="text"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="例如：独家资料包下载"
                      />
                    </div>
                  </div>
                </div>

                <div v-if="formData.paid_content_type === 'document'" class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">文档内容</h3>
                  <textarea
                    v-model="formData.paid_document_content"
                    rows="8"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="输入付费后显示的文档内容..."
                    maxlength="2000"
                  ></textarea>
                  <p class="text-xs text-gray-500 mt-1">{{ formData.paid_document_content.length }}/2000</p>
                </div>
              </div>
            </div>

            <!-- 导航按钮 -->
            <div class="flex justify-between pt-8 border-t border-gray-200">
              <button
                v-if="currentStep > 0"
                @click="prevStep"
                class="flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                上一步
              </button>
              <div v-else></div>

              <button
                v-if="currentStep < steps.length - 1"
                @click="nextStep"
                :disabled="!canProceed"
                class="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一步
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>

              <button
                v-else
                @click="createGroup"
                :disabled="creating || !canProceed"
                class="flex items-center px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="creating" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ creating ? '创建中...' : '创建群组' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 实时预览区域 -->
        <div class="lg:col-span-1">
          <div class="sticky top-24">
            <GroupPreview :form-data="formData" />
          </div>
        </div>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <GroupPreviewModal 
      v-if="showPreviewModal" 
      :form-data="formData" 
      @close="showPreviewModal = false" 
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useApi } from '~/composables/useApi'
import { useErrorHandler } from '~/composables/useErrorHandler'
import { debounce } from 'lodash-es'

definePageMeta({ 
  middleware: 'auth',
  title: '创建群组'
})

const router = useRouter()
const { wechatGroups: groupsApi } = useApi()
const { handleError } = useErrorHandler()

// 响应式数据
const currentStep = ref(0)
const creating = ref(false)
const saving = ref(false)
const showPreviewModal = ref(false)
const showOptimizationTips = ref(false)
const autoSaveStatus = ref('')

// 转化率优化相关
const optimizationTips = ref([
  { id: 1, text: '设置吸引人的群组名称', completed: false },
  { id: 2, text: '添加详细的群组描述', completed: false },
  { id: 3, text: '设置合理的价格区间', completed: false },
  { id: 4, text: '添加社会证明元素', completed: false },
  { id: 5, text: '配置用户评价', completed: false },
  { id: 6, text: '设置紧迫感营销', completed: false },
  { id: 7, text: '优化按钮文案', completed: false },
  { id: 8, text: '添加FAQ解答疑虑', completed: false }
])

// 表单验证错误
const validationErrors = ref({})

// 智能建议
const smartSuggestions = ref({
  title: [],
  description: [],
  price: null,
  buttonText: []
})

// UI状态控制
const showTitleSuggestions = ref(false)

// 步骤配置
const steps = ref([
  { title: '基础信息', description: '设置群组基本信息' },
  { title: '营销优化', description: '配置转化率优化元素' },
  { title: '内容配置', description: '设置介绍、FAQ和评价' },
  { title: '付费内容', description: '配置付费后的内容' }
])

// 分类选项
const categories = ref([
  { label: '商务合作', value: 'business' },
  { label: '创业交流', value: 'entrepreneurship' },
  { label: '扩列交友', value: 'dating' },
  { label: '婚恋相亲', value: 'marriage' },
  { label: '学习教育', value: 'education' },
  { label: '健身健康', value: 'health' },
  { label: '育儿教育', value: 'parenting' },
  { label: '投资理财', value: 'investment' }
])

// 按钮文案建议
const buttonSuggestions = ref([
  '立即加入群聊',
  '马上进群学习',
  '加入学习群',
  '进群获取资源',
  '立即入群交流',
  '加入VIP群'
])

// 内容类型选项
const contentTypes = ref([
  { value: 'qr_code', label: '入群二维码', icon: '📱' },
  { value: 'link', label: '下载链接', icon: '🔗' },
  { value: 'document', label: '文档内容', icon: '📄' },
  { value: 'video', label: '视频内容', icon: '🎥' }
])

// 表单数据
const formData = reactive({
  // 基础信息
  title: '',
  category: '',
  price: 9.9,
  description: '',
  member_limit: 200,
  current_members: 0,
  
  // 营销优化
  read_count_display: '8万+',
  like_count: 1500,
  want_see_count: 1000,
  button_title: '立即加入群聊',
  avatar_library: 'default',
  show_limited_time: false,
  original_price: 0,
  discount_end_time: '',
  
  // 内容配置
  group_intro_title: '群简介',
  group_intro_content: '',
  faqs: [
    { question: '', answer: '' }
  ],
  reviews: [
    { author: '', content: '', rating: 5 }
  ],
  
  // 付费内容
  paid_content_type: 'qr_code',
  qr_code: '',
  paid_link: '',
  paid_link_desc: '',
  paid_document_content: '',
  
  // 其他设置
  status: 'active',
  payment_methods: ['wechat', 'alipay']
})

// 计算属性
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return formData.title && formData.category && formData.price > 0 && formData.description
    case 1:
      return true // 营销优化步骤都是可选的
    case 2:
      return true // 内容配置步骤都是可选的
    case 3:
      if (formData.paid_content_type === 'qr_code') {
        return formData.qr_code
      }
      if (formData.paid_content_type === 'link') {
        return formData.paid_link
      }
      if (formData.paid_content_type === 'document') {
        return formData.paid_document_content
      }
      return true
    default:
      return true
  }
})

// 转化率预估计算
const estimatedConversionRate = computed(() => {
  let baseRate = 2.5 // 基础转化率
  let bonus = 0
  
  // 根据完成的优化项目增加转化率
  optimizationTips.value.forEach(tip => {
    if (tip.completed) {
      bonus += 0.8
    }
  })
  
  // 价格因素
  if (formData.price >= 9.9 && formData.price <= 99.9) {
    bonus += 1.2
  }
  
  // 社会证明因素
  if (formData.like_count > 1000) bonus += 0.5
  if (formData.want_see_count > 500) bonus += 0.5
  
  // 紧迫感因素
  if (formData.show_limited_time) bonus += 1.5
  
  // 内容丰富度
  if (formData.reviews.filter(r => r.content).length >= 3) bonus += 0.8
  if (formData.faqs.filter(f => f.question && f.answer).length >= 3) bonus += 0.6
  
  return Math.min(Math.round((baseRate + bonus) * 10) / 10, 15.0)
})

// 表单验证
const validateStep = (step) => {
  const errors = {}
  
  switch (step) {
    case 0:
      if (!formData.title) errors.title = '请输入群组名称'
      if (!formData.category) errors.category = '请选择群组分类'
      if (!formData.price || formData.price <= 0) errors.price = '请设置合理的价格'
      if (!formData.description) errors.description = '请输入群组描述'
      if (formData.title.length < 5) errors.title = '群组名称至少需要5个字符'
      if (formData.description.length < 20) errors.description = '群组描述至少需要20个字符'
      break
    case 3:
      if (formData.paid_content_type === 'qr_code' && !formData.qr_code) {
        errors.qr_code = '请上传入群二维码'
      }
      if (formData.paid_content_type === 'link' && !formData.paid_link) {
        errors.paid_link = '请输入下载链接'
      }
      if (formData.paid_content_type === 'document' && !formData.paid_document_content) {
        errors.paid_document_content = '请输入文档内容'
      }
      break
  }
  
  validationErrors.value = errors
  return Object.keys(errors).length === 0
}

// 智能建议生成
const generateSmartSuggestions = () => {
  // 根据分类生成标题建议
  if (formData.category) {
    const titleSuggestions = {
      'business': ['商务合作交流群', '创业项目对接群', '商业资源共享群'],
      'entrepreneurship': ['创业者交流群', '副业赚钱群', '创业项目分享群'],
      'dating': ['同城交友群', '扩列聊天群', '兴趣爱好交流群'],
      'education': ['学习交流群', '知识分享群', '技能提升群']
    }
    smartSuggestions.value.title = titleSuggestions[formData.category] || []
  }
  
  // 根据价格生成描述建议
  if (formData.price) {
    if (formData.price <= 19.9) {
      smartSuggestions.value.description = [
        '超值入群价格，物超所值的学习体验',
        '低门槛高价值，人人都能参与的优质社群'
      ]
    } else if (formData.price <= 99.9) {
      smartSuggestions.value.description = [
        '精品社群，专业指导，助你快速成长',
        '高质量交流环境，结识志同道合的朋友'
      ]
    }
  }
}

// 自动保存功能
const autoSave = debounce(async () => {
  if (!formData.title) return
  
  autoSaveStatus.value = 'saving'
  try {
    await groupsApi.saveDraft(formData)
    autoSaveStatus.value = 'saved'
    setTimeout(() => {
      autoSaveStatus.value = ''
    }, 2000)
  } catch (error) {
    autoSaveStatus.value = ''
    console.error('自动保存失败:', error)
  }
}, 3000)

// 更新优化提示状态
const updateOptimizationTips = () => {
  optimizationTips.value[0].completed = formData.title.length >= 5
  optimizationTips.value[1].completed = formData.description.length >= 20
  optimizationTips.value[2].completed = formData.price >= 9.9 && formData.price <= 99.9
  optimizationTips.value[3].completed = formData.like_count > 0 || formData.want_see_count > 0
  optimizationTips.value[4].completed = formData.reviews.filter(r => r.content).length >= 2
  optimizationTips.value[5].completed = formData.show_limited_time
  optimizationTips.value[6].completed = formData.button_title !== '立即加入群聊'
  optimizationTips.value[7].completed = formData.faqs.filter(f => f.question && f.answer).length >= 2
}

// 方法
const nextStep = () => {
  if (!validateStep(currentStep.value)) {
    return
  }
  
  if (canProceed.value && currentStep.value < steps.value.length - 1) {
    currentStep.value++
    // 生成下一步的智能建议
    generateSmartSuggestions()
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const addFAQ = () => {
  formData.faqs.push({ question: '', answer: '' })
}

const removeFAQ = (index) => {
  if (formData.faqs.length > 1) {
    formData.faqs.splice(index, 1)
  }
}

const addReview = () => {
  const defaultReviews = [
    { author: '张同学', content: '群里的资源很丰富，学到了很多实用的知识！', rating: 5 },
    { author: '李老师', content: '群主很负责，经常分享干货，值得推荐！', rating: 5 },
    { author: '王经理', content: '在这里认识了很多志同道合的朋友，收获满满。', rating: 5 }
  ]
  
  const randomReview = defaultReviews[Math.floor(Math.random() * defaultReviews.length)]
  formData.reviews.push({ ...randomReview })
}

const removeReview = (index) => {
  if (formData.reviews.length > 1) {
    formData.reviews.splice(index, 1)
  }
}

// 智能填充功能
const smartFillFAQs = () => {
  const commonFAQs = {
    'business': [
      { question: '这个群主要讨论什么内容？', answer: '主要分享商务合作机会、行业资讯和创业经验交流。' },
      { question: '群里有什么资源？', answer: '定期分享行业报告、商业计划书模板、合作项目信息等。' }
    ],
    'education': [
      { question: '群里会提供什么学习资料？', answer: '定期分享学习资料、在线课程推荐、学习方法指导等。' },
      { question: '有专业老师指导吗？', answer: '群内有经验丰富的老师定期答疑解惑，提供学习建议。' }
    ]
  }
  
  const faqs = commonFAQs[formData.category] || []
  faqs.forEach(faq => {
    if (formData.faqs.length < 5) {
      formData.faqs.push({ ...faq })
    }
  })
}

// 价格优化建议
const getPriceOptimizationTip = () => {
  if (formData.price < 9.9) {
    return '建议价格设置在9.9-19.9元之间，既能体现价值又不会过高'
  } else if (formData.price > 99.9) {
    return '价格较高，建议添加更多价值说明和社会证明'
  }
  return null
}

const saveAsDraft = async () => {
  if (saving.value) return
  
  saving.value = true
  try {
    const response = await groupsApi.saveDraft(formData)
    if (response.success) {
      // 显示成功提示
      ElMessage.success('草稿保存成功')
    }
  } catch (error) {
    handleError(error)
    console.error('保存草稿失败:', error)
  } finally {
    saving.value = false
  }
}

const previewGroup = () => {
  showPreviewModal.value = true
}

const createGroup = async () => {
  if (!canProceed.value || creating.value) return
  
  // 最终验证
  if (!validateStep(currentStep.value)) {
    return
  }
  
  creating.value = true
  try {
    // 处理FAQ和评价数据
    const processedData = {
      ...formData,
      faq_content: formData.faqs
        .filter(faq => faq.question && faq.answer)
        .map(faq => `${faq.question}----${faq.answer}`)
        .join('\n'),
      user_reviews: formData.reviews
        .filter(review => review.content)
        .map(review => `${review.content}----${review.rating}`)
        .join('\n')
    }
    
    const response = await groupsApi.create(processedData)
    if (response.success) {
      // 创建成功，跳转到群组详情页
      ElMessage.success('群组创建成功！')
      router.push(`/groups/${response.data.id}`)
    }
  } catch (error) {
    handleError(error)
    console.error('创建群组失败:', error)
  } finally {
    creating.value = false
  }
}

// 加载草稿数据
const loadDraft = async () => {
  try {
    const response = await groupsApi.getDraft()
    if (response.success && response.data) {
      Object.assign(formData, response.data)
      ElMessage.info('已加载上次保存的草稿')
    }
  } catch (error) {
    console.error('加载草稿失败:', error)
  }
}

// 监听表单变化，自动保存和更新优化提示
watch(formData, () => {
  updateOptimizationTips()
  autoSave()
}, { deep: true })

// 监听分类变化，生成智能建议
watch(() => formData.category, () => {
  generateSmartSuggestions()
})

// 页面加载时的初始化
onMounted(async () => {
  // 加载草稿数据
  await loadDraft()
  
  // 初始化优化提示
  updateOptimizationTips()
  
  // 生成初始建议
  generateSmartSuggestions()
  
  // 设置默认的限时优惠时间（24小时后）
  if (!formData.discount_end_time) {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    formData.discount_end_time = tomorrow.toISOString().slice(0, 16)
  }
})
</script>

<style scoped>
/* 自定义样式 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>