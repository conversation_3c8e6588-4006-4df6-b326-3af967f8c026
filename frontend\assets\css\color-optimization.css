/* 配色优化专用样式 - 解决白色背景上白色文字问题 */
/* ================================================== */

/* 1. 全局文字颜色优化 */
/* =================== */

/* 确保所有文字在白色背景上可读 */
.bg-white,
.bg-gray-50,
.bg-gray-100,
[style*="background: white"],
[style*="background-color: white"],
[style*="background: #fff"],
[style*="background-color: #fff"],
[style*="background: #ffffff"],
[style*="background-color: #ffffff"] {
  color: #1f2937 !important; /* 深灰色文字 */
}

/* 修复白色背景上的白色文字 */
.bg-white .text-white,
.bg-gray-50 .text-white,
.bg-gray-100 .text-white {
  color: #1f2937 !important;
}

/* 修复浅色背景上的浅色文字 */
.bg-white .text-gray-50,
.bg-white .text-gray-100,
.bg-white .text-gray-200 {
  color: #374151 !important;
}

/* 2. 卡片组件优化 */
/* =============== */

.card,
.modern-card,
.bg-white.rounded-lg,
.bg-white.shadow {
  background: white !important;
  color: #1f2937 !important;
}

.card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
.modern-card h1, .modern-card h2, .modern-card h3, .modern-card h4, .modern-card h5, .modern-card h6 {
  color: #111827 !important;
}

.card p,
.modern-card p {
  color: #374151 !important;
}

/* 3. 统计卡片优化 */
/* =============== */

.stat-card,
.stats-card,
.dashboard-card {
  background: white !important;
  color: #1f2937 !important;
}

.stat-value,
.stats-value {
  color: #111827 !important;
  font-weight: 700;
}

.stat-label,
.stats-label {
  color: #6b7280 !important;
}

/* 4. 表单元素优化 */
/* =============== */

.input,
.form-input,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  background: white !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

.input::placeholder,
.form-input::placeholder,
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
}

label {
  color: #374151 !important;
  font-weight: 500;
}

/* 5. 按钮优化 */
/* =========== */

.btn-primary,
.modern-btn-primary,
.bg-primary-600,
.bg-blue-600 {
  background: #2563eb !important;
  color: white !important;
}

.btn-secondary,
.modern-btn-secondary {
  background: white !important;
  color: #374151 !important;
  border: 1px solid #d1d5db !important;
}

.btn-secondary:hover,
.modern-btn-secondary:hover {
  background: #f9fafb !important;
  color: #1f2937 !important;
}

/* Element UI 按钮优化 */
.el-button {
  color: #374151 !important;
}

.el-button--primary {
  background: #2563eb !important;
  color: white !important;
  border-color: #2563eb !important;
}

.el-button--success {
  background: #059669 !important;
  color: white !important;
  border-color: #059669 !important;
}

.el-button--warning {
  background: #d97706 !important;
  color: white !important;
  border-color: #d97706 !important;
}

.el-button--danger {
  background: #dc2626 !important;
  color: white !important;
  border-color: #dc2626 !important;
}

.el-button--info {
  background: #2563eb !important;
  color: white !important;
  border-color: #2563eb !important;
}

/* 6. 表格优化 */
/* =========== */

.table,
table {
  background: white !important;
  color: #1f2937 !important;
}

.table th,
table th {
  background: #f9fafb !important;
  color: #374151 !important;
  font-weight: 600;
}

.table td,
table td {
  color: #1f2937 !important;
}

.table tbody tr:hover,
table tbody tr:hover {
  background: #f9fafb !important;
}

/* 7. 导航和菜单优化 */
/* ================= */

.nav-item {
  color: rgba(255, 255, 255, 0.9) !important;
}

.nav-item:hover {
  color: white !important;
}

.dropdown-menu {
  background: white !important;
  color: #1f2937 !important;
  border: 1px solid #e5e7eb !important;
}

.dropdown-item {
  color: #374151 !important;
}

.dropdown-item:hover {
  background: #f9fafb !important;
  color: #1f2937 !important;
}

/* 8. 模态框优化 */
/* ============= */

.modal,
.modal-content {
  background: white !important;
  color: #1f2937 !important;
}

.modal-header {
  color: #111827 !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.modal-body {
  color: #374151 !important;
}

.modal-footer {
  border-top: 1px solid #e5e7eb !important;
}

/* 9. 状态颜色优化 */
/* =============== */

.text-success,
.text-green-500,
.text-green-600 {
  color: #059669 !important;
}

.text-error,
.text-red-500,
.text-red-600 {
  color: #dc2626 !important;
}

.text-warning,
.text-yellow-500,
.text-yellow-600 {
  color: #d97706 !important;
}

.text-info,
.text-blue-500,
.text-blue-600 {
  color: #2563eb !important;
}

/* 10. 链接优化 */
/* ============ */

a {
  color: #2563eb !important;
}

a:hover {
  color: #1d4ed8 !important;
}

/* 11. 面包屑导航优化 */
/* ================== */

.breadcrumb {
  color: #6b7280 !important;
}

.breadcrumb a {
  color: #2563eb !important;
}

.breadcrumb .active {
  color: #1f2937 !important;
}

/* 12. 分页器优化 */
/* ============== */

.pagination {
  color: #374151 !important;
}

.pagination .page-link {
  color: #374151 !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
}

.pagination .page-link:hover {
  color: #1f2937 !important;
  background: #f9fafb !important;
}

.pagination .active .page-link {
  background: #2563eb !important;
  color: white !important;
  border-color: #2563eb !important;
}

/* 13. 特殊情况修复 */
/* ================ */

/* 修复可能的内联样式问题 */
[style*="color: white"] {
  color: #1f2937 !important;
}

[style*="color: #fff"] {
  color: #1f2937 !important;
}

[style*="color: #ffffff"] {
  color: #1f2937 !important;
}

/* 修复透明背景上的白色文字 */
.bg-transparent .text-white {
  color: white !important; /* 保持透明背景上的白色文字 */
}

/* 修复渐变背景上的文字 */
.bg-gradient-to-r .text-white,
.bg-gradient-to-l .text-white,
.bg-gradient-to-t .text-white,
.bg-gradient-to-b .text-white,
[style*="background: linear-gradient"] .text-white,
[style*="background-image: linear-gradient"] .text-white {
  color: white !important; /* 保持渐变背景上的白色文字 */
}

/* 14. 响应式优化 */
/* ============== */

@media (max-width: 768px) {
  .card,
  .modern-card {
    padding: 1rem !important;
  }
  
  .stat-value,
  .stats-value {
    font-size: 1.5rem !important;
  }
  
  .btn,
  .modern-btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
  }
}

/* 15. 打印样式优化 */
/* ================ */

@media print {
  * {
    color: black !important;
    background: white !important;
  }
  
  .btn,
  .modern-btn {
    border: 1px solid black !important;
  }
}
