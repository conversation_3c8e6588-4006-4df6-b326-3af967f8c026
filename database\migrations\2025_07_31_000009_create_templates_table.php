<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建模板表
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('模板名称');
            $table->string('code')->unique()->comment('模板代码');
            $table->string('type')->comment('模板类型');
            $table->string('category')->nullable()->comment('模板分类');
            $table->text('description')->nullable()->comment('模板描述');
            $table->string('cover_image')->nullable()->comment('封面图片');
            $table->json('template_data')->nullable()->comment('模板数据');
            $table->json('metadata')->nullable()->comment('元数据');
            $table->json('tags')->nullable()->comment('标签');
            $table->string('source')->nullable()->comment('来源');
            $table->boolean('is_public')->default(false)->comment('是否公开');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->decimal('success_rate', 5, 2)->default(0)->comment('成功率');
            $table->decimal('rating', 3, 1)->default(0)->comment('评分');
            $table->decimal('price', 10, 2)->default(0)->comment('价格');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->integer('version')->default(1)->comment('版本号');
            $table->unsignedBigInteger('parent_template_id')->nullable()->comment('父模板ID');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['type', 'category']);
            $table->index(['is_public', 'is_active']);
            $table->index(['created_by']);
            $table->index(['parent_template_id']);
            $table->unique(['code']);
            
            // 外键约束
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_template_id')->references('id')->on('templates')->onDelete('set null');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('templates');
    }
};
