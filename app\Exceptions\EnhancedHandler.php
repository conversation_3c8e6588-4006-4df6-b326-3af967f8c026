<?php

namespace App\Exceptions;

use App\Services\ErrorHandlerService;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Throwable;

/**
 * 增强的异常处理器
 * 提供统一的错误处理、格式化的错误响应和详细的错误日志
 */
class EnhancedHandler extends ExceptionHandler
{
    /**
     * 不需要记录的异常类型
     */
    protected $dontReport = [
        ValidationException::class,
        AuthenticationException::class,
        AuthorizationException::class,
    ];

    /**
     * 注册异常处理回调
     */
    public function register()
    {
        // 自定义渲染回调
        $this->renderable(function (Throwable $e, $request) {
            return $this->handleException($e, $request);
        });

        // 自定义报告回调
        $this->reportable(function (Throwable $e) {
            ErrorHandlerService::handle($e);
        });
    }

    /**
     * 处理异常
     */
    protected function handleException(Throwable $exception, $request)
    {
        // API请求返回JSON格式
        if ($request->wantsJson() || $request->is('api/*')) {
            return $this->handleApiException($exception, $request);
        }

        // Web请求返回视图
        return $this->handleWebException($exception, $request);
    }

    /**
     * 处理API异常
     */
    protected function handleApiException(Throwable $exception, Request $request)
    {
        $statusCode = $this->getStatusCode($exception);
        $error = $this->getErrorResponse($exception, $statusCode);

        return response()-json($error, $statusCode);
    }

    /**
     * 处理Web异常
     */
    protected function handleWebException(Throwable $exception, Request $request)
    {
        $statusCode = $this->getStatusCode($exception);

        // 自定义错误页面
        if (view()-exists("errors.{$statusCode}")) {
            return response()-view("errors.{$statusCode}", [], $statusCode);
        }

        // 默认错误页面
        return response()-view('errors.generic', [
            'message' => $this->getUserFriendlyMessage($exception),
            'status_code' => $statusCode,
        ], $statusCode);
    }

    /**
     * 获取HTTP状态码
     */
    protected function getStatusCode(Throwable $exception): int
    {
        if ($exception instanceof HttpException) {
            return $exception->getStatusCode();
        }

        if ($exception instanceof ValidationException) {
            return 422;
        }

        if ($exception instanceof AuthenticationException) {
            return 401;
        }

        if ($exception instanceof AuthorizationException) {
            return 403;
        }

        return 500;
    }

    /**
     * 获取错误响应数据
     */
    protected function getErrorResponse(Throwable $exception, int $statusCode): array
    {
        $error = [
            'success' => false,
            'code' => $statusCode,
            'message' => $this->getUserFriendlyMessage($exception),
            'timestamp' => now()-toISOString(),
            'path' => request()-path(),
        ];

        // 添加详细错误信息（开发环境）
        if (config('app.debug')) {
            $error['debug'] = [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => explode("\n", $exception->getTraceAsString()),
            ];
        }

        // 验证错误特殊处理
        if ($exception instanceof ValidationException) {
            $error['errors'] = $exception->errors();
        }

        return $error;
    }

    /**
     * 获取用户友好的错误消息
     */
    protected function getUserFriendlyMessage(Throwable $exception): string
    {
        if ($exception instanceof ValidationException) {
            return '输入数据验证失败';
        }

        if ($exception instanceof AuthenticationException) {
            return '请先登录';
        }

        if ($exception instanceof AuthorizationException) {
            return '权限不足';
        }

        if ($exception instanceof NotFoundHttpException) {
            return '请求的资源不存在';
        }

        if ($exception instanceof MethodNotAllowedHttpException) {
            return '请求方法不被允许';
        }

        if (config('app.debug')) {
            return $exception->getMessage();
        }

        return '系统繁忙，请稍后重试';
    }

    /**
     * 自定义验证异常响应
     */
    protected function invalidJson($request, ValidationException $exception)
    {
        return response()-json([
            'success' => false,
            'code' => 422,
            'message' => '输入数据验证失败',
            'errors' => $exception->errors(),
            'timestamp' => now()-toISOString(),
        ], 422);
    }

    /**
     * 自定义认证异常响应
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->wantsJson() || $request->is('api/*')) {
            return response()-json([
                'success' => false,
                'code' => 401,
                'message' => '请先登录',
                'timestamp' => now()-toISOString(),
            ], 401);
        }

        return parent::unauthenticated($request, $exception);
    }

    /**
     * 自定义权限异常响应
     */
    protected function prepareException(Throwable $e)
    {
        if ($e instanceof AuthorizationException) {
            return $e;
        }

        return parent::prepareException($e);
    }

    /**
     * 记录异常
     */
    public function report(Throwable $exception)
    {
        // 忽略不需要报告的异常
        if ($this->shouldntReport($exception)) {
            return;
        }

        // 使用自定义错误处理服务
        ErrorHandlerService::handle($exception);

        parent::report($exception);
    }

    /**
     * 渲染异常
     */
    public function render($request, Throwable $exception)
    {
        return $this->handleException($exception, $request);
    }

    /**
     * 获取错误统计
     */
    public static function getErrorStatistics(): array
    {
        return ErrorHandlerService::getErrorStats();
    }

    /**
     * 获取最近错误
     */
    public static function getRecentErrors(int $limit = 10): array
    {
        return ErrorHandlerService::getRecentErrors($limit);
    }

    /**
     * 清理错误日志
     */
    public static function cleanupErrorLogs(int $days = 30): void
    {
        ErrorHandlerService::cleanupOldLogs($days);
    }
}