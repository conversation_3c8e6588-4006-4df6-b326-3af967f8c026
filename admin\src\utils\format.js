/**
 * 格式化日期时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {number} decimals 小数位数
 * @param {string} symbol 货币符号
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoney(amount, decimals = 2, symbol = '¥') {
  if (amount === null || amount === undefined || isNaN(amount)) return `${symbol}0.00`
  
  const num = Number(amount)
  return `${symbol}${num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })}`
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, decimals = 0) {
  if (num === null || num === undefined || isNaN(num)) return '0'
  
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化百分比
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(num, decimals = 2) {
  if (num === null || num === undefined || isNaN(num)) return '0%'
  
  return (Number(num) * 100).toFixed(decimals) + '%'
}

/**
 * 格式化相对时间
 * @param {string|Date} date 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < week) {
    return Math.floor(diff / day) + '天前'
  } else if (diff < month) {
    return Math.floor(diff / week) + '周前'
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前'
  } else {
    return Math.floor(diff / year) + '年前'
  }
}

/**
 * 格式化手机号
 * @param {string} phone 手机号
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone) {
  if (!phone) return ''
  
  const phoneStr = String(phone).replace(/\D/g, '')
  if (phoneStr.length === 11) {
    return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  return phone
}

/**
 * 格式化身份证号
 * @param {string} idCard 身份证号
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard(idCard) {
  if (!idCard) return ''
  
  const idStr = String(idCard)
  if (idStr.length === 18) {
    return idStr.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  } else if (idStr.length === 15) {
    return idStr.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
  }
  return idCard
}

/**
 * 格式化银行卡号
 * @param {string} cardNo 银行卡号
 * @returns {string} 格式化后的银行卡号
 */
export function formatBankCard(cardNo) {
  if (!cardNo) return ''
  
  const cardStr = String(cardNo).replace(/\D/g, '')
  if (cardStr.length >= 16) {
    return cardStr.replace(/(\d{4})\d*(\d{4})/, '$1****$2')
  }
  return cardNo
}

/**
 * 格式化邮箱
 * @param {string} email 邮箱
 * @returns {string} 格式化后的邮箱
 */
export function formatEmail(email) {
  if (!email) return ''
  
  const emailStr = String(email)
  const atIndex = emailStr.indexOf('@')
  if (atIndex > 0) {
    const username = emailStr.substring(0, atIndex)
    const domain = emailStr.substring(atIndex)
    
    if (username.length <= 3) {
      return username + '***' + domain
    } else {
      return username.substring(0, 3) + '***' + domain
    }
  }
  return email
}

/**
 * 截断文本
 * @param {string} text 文本
 * @param {number} length 长度
 * @param {string} suffix 后缀
 * @returns {string} 截断后的文本
 */
export function truncateText(text, length = 50, suffix = '...') {
  if (!text) return ''
  
  const textStr = String(text)
  if (textStr.length <= length) return textStr
  
  return textStr.substring(0, length) + suffix
}

/**
 * 格式化状态文本
 * @param {string} status 状态值
 * @param {Object} statusMap 状态映射
 * @returns {string} 状态文本
 */
export function formatStatus(status, statusMap = {}) {
  return statusMap[status] || status || '未知'
}

/**
 * 格式化数组为字符串
 * @param {Array} arr 数组
 * @param {string} separator 分隔符
 * @returns {string} 字符串
 */
export function formatArray(arr, separator = ', ') {
  if (!Array.isArray(arr)) return ''
  return arr.join(separator)
}

/**
 * 格式化布尔值
 * @param {boolean} value 布尔值
 * @param {string} trueText 真值文本
 * @param {string} falseText 假值文本
 * @returns {string} 格式化后的文本
 */
export function formatBoolean(value, trueText = '是', falseText = '否') {
  return value ? trueText : falseText
}

/**
 * 格式化JSON
 * @param {Object} obj JSON对象
 * @param {number} space 缩进空格数
 * @returns {string} 格式化后的JSON字符串
 */
export function formatJSON(obj, space = 2) {
  try {
    return JSON.stringify(obj, null, space)
  } catch (error) {
    return String(obj)
  }
}

/**
 * 格式化URL参数
 * @param {Object} params 参数对象
 * @returns {string} URL参数字符串
 */
export function formatUrlParams(params) {
  if (!params || typeof params !== 'object') return ''
  
  const searchParams = new URLSearchParams()
  Object.keys(params).forEach(key => {
    const value = params[key]
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  return searchParams.toString()
}

/**
 * 格式化时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date, format = 'HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return formatDate(date, format)
}
