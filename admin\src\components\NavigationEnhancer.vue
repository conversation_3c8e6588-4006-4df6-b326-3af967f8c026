<template>
  <div class="navigation-enhancer">
    <!-- 分组分隔符会通过JavaScript动态插入到现有导航中 -->
    
    <!-- 快捷操作面板 -->
    <div class="quick-actions-panel" v-if="!collapsed && showQuickActions">
      <div class="panel-header">
        <span class="panel-title">快捷操作</span>
        <el-button 
          text 
          size="small" 
          @click="showQuickActions = false"
          class="close-btn"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="quick-actions-grid">
        <div
          v-for="action in currentQuickActions"
          :key="action.path"
          class="quick-action-item"
          :style="{ '--action-color': action.color }"
          @click="navigateToAction(action)"
        >
          <div class="action-icon">
            <el-icon><component :is="getIconComponent(action.icon)" /></el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">{{ action.title }}</div>
            <div class="action-description">{{ action.description }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作触发按钮 -->
    <div class="quick-actions-trigger" v-if="!collapsed && !showQuickActions">
      <el-button 
        type="primary" 
        size="small" 
        @click="showQuickActions = true"
        class="trigger-btn"
      >
        <el-icon><Lightning /></el-icon>
        <span>快捷操作</span>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  Close, Lightning, Plus, DataLine, User, Money,
  Monitor, Comment, ShoppingCart, Share
} from '@element-plus/icons-vue'
import { 
  navigationGroups, 
  getQuickActionsByRole,
  addGroupInfoToRoutes,
  createGroupSeparator
} from '@/utils/navigationGrouping'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showQuickActions = ref(false)

// 计算属性
const currentQuickActions = computed(() => {
  const userRole = userStore.userInfo?.role || 'user'
  return getQuickActionsByRole(userRole)
})

// 图标组件映射
const iconComponents = {
  Plus, DataLine, User, Money, Monitor, Comment, ShoppingCart, Share
}

// 方法
const getIconComponent = (iconName) => {
  return iconComponents[iconName] || Monitor
}

const navigateToAction = (action) => {
  router.push(action.path)
  showQuickActions.value = false
}

// 为现有导航添加分组分隔符
const enhanceExistingNavigation = () => {
  // 等待DOM渲染完成
  setTimeout(() => {
    const menuElement = document.querySelector('.sidebar-menu')
    if (!menuElement) return
    
    const menuItems = menuElement.querySelectorAll('.el-menu-item, .el-sub-menu')
    let currentGroup = null
    
    menuItems.forEach((item, index) => {
      const routePath = item.getAttribute('index') || item.querySelector('.el-menu-item')?.getAttribute('index')
      if (!routePath) return
      
      // 确定当前菜单项属于哪个分组
      let itemGroup = null
      for (const [groupKey, group] of Object.entries(navigationGroups)) {
        if (group.routes.some(route => routePath.startsWith(route))) {
          itemGroup = groupKey
          break
        }
      }
      
      // 如果分组发生变化，插入分组分隔符
      if (itemGroup && itemGroup !== currentGroup) {
        const groupInfo = navigationGroups[itemGroup]
        const separator = document.createElement('div')
        separator.className = 'nav-group-separator'
        separator.innerHTML = `
          <div class="group-line" style="background: linear-gradient(90deg, ${groupInfo.color}22, ${groupInfo.color}88, ${groupInfo.color}22)"></div>
          <div class="group-label" style="color: ${groupInfo.color}">
            <span class="group-title">${groupInfo.title}</span>
          </div>
        `
        
        // 插入分隔符
        item.parentNode.insertBefore(separator, item)
        currentGroup = itemGroup
      }
    })
  }, 100)
}

// 生命周期
onMounted(() => {
  enhanceExistingNavigation()
  
  // 监听路由变化，重新增强导航
  const unwatch = router.afterEach(() => {
    // 清除现有分隔符
    document.querySelectorAll('.nav-group-separator').forEach(el => el.remove())
    // 重新添加分隔符
    enhanceExistingNavigation()
  })
  
  onUnmounted(() => {
    unwatch()
  })
})
</script>

<style scoped>
.navigation-enhancer {
  position: relative;
}

.quick-actions-panel {
  position: absolute;
  bottom: 80px;
  left: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.close-btn {
  color: rgba(255, 255, 255, 0.6);
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.action-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--action-color, #409eff);
  border-radius: 6px;
  margin-right: 12px;
  color: white;
  font-size: 16px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.action-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.2;
}

.quick-actions-trigger {
  position: absolute;
  bottom: 20px;
  left: 16px;
  right: 16px;
}

.trigger-btn {
  width: 100%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border: none;
  border-radius: 8px;
  height: 36px;
  font-size: 13px;
  font-weight: 500;
}

.trigger-btn:hover {
  background: linear-gradient(135deg, #337ecc, #529b2e);
}

/* 全局样式 - 分组分隔符 */
:global(.nav-group-separator) {
  margin: 12px 16px 8px 16px;
  padding: 0;
}

:global(.nav-group-separator .group-line) {
  height: 1px;
  margin-bottom: 8px;
  border-radius: 1px;
}

:global(.nav-group-separator .group-label) {
  display: flex;
  align-items: center;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

:global(.nav-group-separator .group-title) {
  margin-left: 4px;
}

/* 折叠状态下隐藏分组标签 */
:global(.modern-sidebar.collapsed .nav-group-separator .group-label) {
  display: none;
}

:global(.modern-sidebar.collapsed .nav-group-separator .group-line) {
  margin: 8px 12px;
}
</style>
