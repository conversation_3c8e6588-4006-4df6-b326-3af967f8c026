<!-- 智能面包屑导航组件 -->
<!-- admin/src/components/navigation/SmartBreadcrumb.vue -->

<template>
  <div class="smart-breadcrumb">
    <div class="breadcrumb-path">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/dashboard' }">
          <el-icon><HomeFilled /></el-icon>
          <span>控制台</span>
        </el-breadcrumb-item>
        
        <!-- 域级导航 -->
        <el-breadcrumb-item 
          v-if="domainInfo"
          :to="domainInfo.path"
        >
          <el-icon><component :is="domainInfo.icon" /></el-icon>
          <span>{{ domainInfo.title }}</span>
        </el-breadcrumb-item>

        <!-- 模块级导航 -->
        <el-breadcrumb-item 
          v-if="moduleInfo"
          :to="moduleInfo.path"
        >
          <el-icon><component :is="moduleInfo.icon" /></el-icon>
          <span>{{ moduleInfo.title }}</span>
        </el-breadcrumb-item>

        <!-- 页面级导航 -->
        <el-breadcrumb-item 
          v-for="(item, index) in pageBreadcrumbs" 
          :key="index"
          :to="index < pageBreadcrumbs.length - 1 ? { path: item.path } : null"
        >
          <el-icon v-if="item.icon"><component :is="item.icon" /></el-icon>
          <span>{{ item.title }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 页面操作快捷按钮 -->
    <div class="page-actions" v-if="pageActions.length > 0">
      <el-button-group size="small">
        <el-tooltip
          v-for="action in pageActions"
          :key="action.key"
          :content="action.tooltip || action.title"
          placement="bottom"
        >
          <el-button 
            :type="action.type || 'default'"
            :icon="action.icon"
            @click="action.handler"
            :disabled="action.disabled"
          >
            {{ action.showText ? action.title : '' }}
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>

    <!-- 页面帮助提示 -->
    <div class="page-help" v-if="pageHelp">
      <el-tooltip :content="pageHelp" placement="bottom-end">
        <el-button text :icon="QuestionFilled" size="small" />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  NAVIGATION_DOMAINS, 
  getRouteDomainInfo,
  ROUTE_DOMAIN_MAPPING 
} from '@/config/navigation-domains'
import {
  HomeFilled,
  QuestionFilled,
  Plus,
  Edit,
  Delete,
  Refresh,
  Download,
  Upload,
  View,
  Setting
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 计算域信息
const domainInfo = computed(() => {
  const routeInfo = getRouteDomainInfo(route.path)
  if (!routeInfo) return null

  // 查找对应的域
  for (const domain of Object.values(NAVIGATION_DOMAINS)) {
    if (domain.key === routeInfo.domainKey) {
      return {
        title: domain.title,
        icon: domain.icon,
        path: '/' + domain.key // 生成域首页路径
      }
    }
  }
  return null
})

// 计算模块信息
const moduleInfo = computed(() => {
  const routeInfo = getRouteDomainInfo(route.path)
  if (!routeInfo || !domainInfo.value) return null

  // 查找对应的模块
  for (const domain of Object.values(NAVIGATION_DOMAINS)) {
    if (domain.key === routeInfo.domainKey) {
      for (const module of Object.values(domain.modules)) {
        if (module.key === routeInfo.moduleKey) {
          return {
            title: module.title,
            icon: module.icon,
            path: module.path
          }
        }
      }
    }
  }
  return null
})

// 页面级面包屑
const pageBreadcrumbs = computed(() => {
  const breadcrumbs = []
  const matched = route.matched.filter(item => 
    item.meta && 
    item.meta.title && 
    item.path !== '/dashboard' &&
    !isDomainOrModule(item.path)
  )

  matched.forEach(item => {
    breadcrumbs.push({
      title: item.meta.title,
      icon: item.meta.icon,
      path: item.path
    })
  })

  return breadcrumbs
})

// 检查路径是否为域或模块级别
const isDomainOrModule = (path) => {
  return ROUTE_DOMAIN_MAPPING[path] !== undefined
}

// 页面操作按钮配置
const pageActions = computed(() => {
  const actions = []
  const currentPath = route.path

  // 根据不同页面配置不同的操作按钮
  if (currentPath.includes('/community/groups')) {
    actions.push(
      { key: 'add', title: '创建群组', icon: Plus, type: 'primary', handler: () => router.push('/community/add-enhanced') },
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() },
      { key: 'export', title: '导出', icon: Download, handler: handleExport }
    )
  } else if (currentPath.includes('/user/list')) {
    actions.push(
      { key: 'add', title: '添加用户', icon: Plus, type: 'primary', handler: () => router.push('/user/add') },
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() },
      { key: 'export', title: '导出', icon: Download, handler: handleExport }
    )
  } else if (currentPath.includes('/orders')) {
    actions.push(
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() },
      { key: 'export', title: '导出订单', icon: Download, handler: handleExport }
    )
  } else if (currentPath.includes('/finance')) {
    actions.push(
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() },
      { key: 'export', title: '导出财务数据', icon: Download, handler: handleExport },
      { key: 'settings', title: '财务设置', icon: Setting, handler: () => router.push('/finance/settings') }
    )
  } else if (currentPath.includes('/system')) {
    actions.push(
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() },
      { key: 'backup', title: '系统备份', icon: Upload, handler: handleBackup }
    )
  }

  // 通用操作按钮
  if (actions.length === 0) {
    actions.push(
      { key: 'refresh', title: '刷新', icon: Refresh, handler: () => window.location.reload() }
    )
  }

  return actions
})

// 页面帮助信息
const pageHelp = computed(() => {
  const helpTexts = {
    '/community/groups': '在这里管理所有的微信群组，可以创建、编辑和查看群组详情。',
    '/user/list': '管理系统中的所有用户，包括查看用户信息、编辑用户资料等。',
    '/orders/list': '查看和管理所有订单信息，包括订单状态跟踪和处理。',
    '/finance/dashboard': '查看财务概况，包括收入、支出和利润分析。',
    '/system/settings': '配置系统基础设置，包括网站信息、功能开关等。'
  }
  
  return helpTexts[route.path] || null
})

// 操作处理函数
const handleExport = () => {
  // 实现导出功能
  console.log('执行导出操作')
  // 这里可以根据当前页面类型调用不同的导出API
}

const handleBackup = () => {
  // 实现系统备份功能
  console.log('执行系统备份')
}
</script>

<style lang="scss" scoped>
.smart-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid var(--el-border-color-light);
  min-height: 52px;

  .breadcrumb-path {
    flex: 1;
    min-width: 0;
  }

  :deep(.el-breadcrumb) {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--el-text-color-regular);
        font-weight: 400;
        
        &:hover {
          color: var(--el-color-primary);
        }

        .el-icon {
          font-size: 14px;
        }
      }

      &:last-child {
        .el-breadcrumb__inner {
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }

    .el-breadcrumb__separator {
      color: var(--el-text-color-placeholder);
      margin: 0 8px;
    }
  }

  .page-actions {
    margin-left: 16px;
    flex-shrink: 0;

    .el-button-group {
      .el-button {
        border-color: var(--el-border-color);
        
        &:hover {
          border-color: var(--el-color-primary);
        }
      }
    }
  }

  .page-help {
    margin-left: 8px;
    flex-shrink: 0;

    .el-button {
      color: var(--el-text-color-secondary);
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .smart-breadcrumb {
    padding: 8px 16px;
    flex-wrap: wrap;
    gap: 8px;

    .breadcrumb-path {
      width: 100%;
      order: 1;
    }

    .page-actions {
      order: 2;
      margin-left: 0;
    }

    .page-help {
      order: 3;
      margin-left: 0;
    }

    :deep(.el-breadcrumb) {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          span {
            display: none;
          }
          
          .el-icon {
            display: inline-flex;
          }
        }
        
        &:last-child {
          .el-breadcrumb__inner span {
            display: inline;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .smart-breadcrumb {
    .page-actions {
      :deep(.el-button-group) {
        .el-button {
          padding: 8px;
          
          span {
            display: none;
          }
        }
      }
    }
  }
}
</style>