<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Psr\SimpleCache\CacheInterface;

/**
 * 导航系统缓存服务
 * 
 * 提供高效的多层缓存策略和缓存管理功能
 */
class NavigationCacheService
{
    // 缓存前缀
    const PREFIX = 'navigation:';
    
    // 缓存TTL配置 (秒)
    const TTL_CONFIG = [
        'navigation_tree' => 3600,      // 导航树结构 - 1小时
        'user_preferences' => 1800,     // 用户偏好 - 30分钟
        'permissions' => 3600,          // 权限数据 - 1小时
        'search_results' => 900,        // 搜索结果 - 15分钟
        'recommendations' => 1800,      // 推荐数据 - 30分钟
        'analytics' => 1800,            // 统计数据 - 30分钟
        'popular_queries' => 3600,      // 热门搜索 - 1小时
        'real_time_stats' => 60,        // 实时统计 - 1分钟
        'session' => 7200,              // 会话数据 - 2小时
    ];

    // 缓存标签
    const TAGS = [
        'navigation' => 'nav_structure',
        'user' => 'user_data',
        'permissions' => 'permissions',
        'search' => 'search_data',
        'analytics' => 'analytics_data',
    ];

    /**
     * 记住缓存
     */
    public function remember(string $key, int $ttl, callable $callback, array $tags = [])
    {
        $cacheKey = $this->buildKey($key);
        
        if ($this->isTaggingSupported() && !empty($tags)) {
            return Cache::tags($tags)->remember($cacheKey, $ttl, $callback);
        }
        
        return Cache::remember($cacheKey, $ttl, $callback);
    }

    /**
     * 获取缓存
     */
    public function get(string $key, $default = null)
    {
        return Cache::get($this->buildKey($key), $default);
    }

    /**
     * 设置缓存
     */
    public function put(string $key, $value, ?int $ttl = null, array $tags = []): bool
    {
        $cacheKey = $this->buildKey($key);
        $ttl = $ttl ?? $this->getDefaultTtl($key);
        
        if ($this->isTaggingSupported() && !empty($tags)) {
            return Cache::tags($tags)->put($cacheKey, $value, $ttl);
        }
        
        return Cache::put($cacheKey, $value, $ttl);
    }

    /**
     * 删除缓存
     */
    public function forget(string $key): bool
    {
        return Cache::forget($this->buildKey($key));
    }

    /**
     * 清除导航树缓存
     */
    public function clearNavigationCache(?string $domain = null): void
    {
        if ($domain) {
            $patterns = [
                "navigation_tree:{$domain}:*",
                "navigation_domain_config:{$domain}:*",
                "domain_stats:{$domain}:*"
            ];
        } else {
            $patterns = [
                'navigation_tree:*',
                'navigation_domain_config:*',
                'domain_stats:*',
                'navigation_overview:*'
            ];
        }

        $this->forgetByPatterns($patterns);

        // 清除带标签的缓存
        if ($this->isTaggingSupported()) {
            Cache::tags([self::TAGS['navigation']])->flush();
        }
    }

    /**
     * 清除用户导航缓存
     */
    public function clearUserNavigationCache(int $userId): void
    {
        $patterns = [
            "navigation_tree:*:{$userId}",
            "navigation_domain_config:*:{$userId}",
            "user_nav_stats:{$userId}",
            "user_navigation_report:{$userId}:*",
            "user_recommendations:{$userId}:*",
            "personalized_nav:{$userId}:*",
            "menu_permission:{$userId}:*",
            "user_accessible_domains:{$userId}",
            "user_domain_permission:{$userId}:*",
            "user_role_permissions:{$userId}"
        ];

        $this->forgetByPatterns($patterns);

        // 清除带标签的用户缓存
        if ($this->isTaggingSupported()) {
            Cache::tags([self::TAGS['user'], "user:{$userId}"])->flush();
        }
    }

    /**
     * 清除用户统计缓存
     */
    public function clearUserStatsCache(int $userId): void
    {
        $patterns = [
            "user_nav_stats:{$userId}",
            "user_navigation_report:{$userId}:*",
            "user_search_stats:{$userId}:*"
        ];

        $this->forgetByPatterns($patterns);
    }

    /**
     * 清除搜索相关缓存
     */
    public function clearSearchCache(?string $query = null, ?int $userId = null): void
    {
        if ($query && $userId) {
            $patterns = [
                "search_suggestions:{$query}:*:{$userId}:*",
                "user_search_stats:{$userId}:*"
            ];
        } elseif ($query) {
            $patterns = [
                "search_suggestions:{$query}:*",
                "popular_search_queries:*"
            ];
        } else {
            $patterns = [
                'search_suggestions:*',
                'popular_search_queries:*',
                'user_search_stats:*'
            ];
        }

        $this->forgetByPatterns($patterns);

        if ($this->isTaggingSupported()) {
            Cache::tags([self::TAGS['search']])->flush();
        }
    }

    /**
     * 清除权限相关缓存
     */
    public function clearPermissionCache(?int $userId = null): void
    {
        if ($userId) {
            $patterns = [
                "menu_permission:{$userId}:*",
                "user_accessible_domains:{$userId}",
                "user_domain_permission:{$userId}:*",
                "user_role_permissions:{$userId}"
            ];
        } else {
            $patterns = [
                'menu_permission:*',
                'user_accessible_domains:*',
                'user_domain_permission:*',
                'user_role_permissions:*',
                'navigation_permission_matrix'
            ];
        }

        $this->forgetByPatterns($patterns);

        if ($this->isTaggingSupported()) {
            Cache::tags([self::TAGS['permissions']])->flush();
        }
    }

    /**
     * 清除推荐相关缓存
     */
    public function clearRecommendationCache(?int $userId = null): void
    {
        if ($userId) {
            $patterns = [
                "nav_recommendations:{$userId}:*",
                "user_recommendations:{$userId}:*",
                "personalized_nav:{$userId}:*"
            ];
        } else {
            $patterns = [
                'nav_recommendations:*',
                'user_recommendations:*',
                'personalized_nav:*',
                'role_nav_recommendations:*',
                'trending_nav_recommendations:*'
            ];
        }

        $this->forgetByPatterns($patterns);
    }

    /**
     * 清除分析相关缓存
     */
    public function clearAnalyticsCache(?string $type = null): void
    {
        $patterns = $type ? [
            "{$type}_analysis:*",
            "navigation_overview:*:{$type}*"
        ] : [
            '*_analysis:*',
            'navigation_overview:*',
            'domain_analysis:*',
            'menu_ranking:*'
        ];

        $this->forgetByPatterns($patterns);

        if ($this->isTaggingSupported()) {
            Cache::tags([self::TAGS['analytics']])->flush();
        }
    }

    /**
     * 清除所有导航相关缓存
     */
    public function clearAllNavigationCache(): void
    {
        $this->clearNavigationCache();
        $this->clearSearchCache();
        $this->clearPermissionCache();
        $this->clearRecommendationCache();
        $this->clearAnalyticsCache();

        if ($this->isTaggingSupported()) {
            Cache::tags(array_values(self::TAGS))->flush();
        }
    }

    /**
     * 预热缓存
     */
    public function warmupCache(array $domains = ['business', 'operation', 'analytics', 'system']): void
    {
        dispatch(function () use ($domains) {
            // 预热导航树
            foreach ($domains as $domain) {
                $this->remember("navigation_tree:{$domain}:guest", self::TTL_CONFIG['navigation_tree'], function () use ($domain) {
                    // 这里应该调用实际的数据获取逻辑
                    return [];
                }, [self::TAGS['navigation']]);
            }

            // 预热热门搜索
            $this->remember('popular_search_queries:all:week:20', self::TTL_CONFIG['popular_queries'], function () {
                // 这里应该调用实际的热门搜索获取逻辑
                return [];
            }, [self::TAGS['search']]);

            // 预热统计数据
            $this->remember('navigation_overview:week:all:single', self::TTL_CONFIG['analytics'], function () {
                // 这里应该调用实际的统计数据获取逻辑
                return [];
            }, [self::TAGS['analytics']]);
        })->afterResponse();
    }

    /**
     * 获取缓存统计信息
     */
    public function getCacheStats(): array
    {
        try {
            if (config('cache.default') === 'redis') {
                return $this->getRedisStats();
            }
            
            return [
                'type' => config('cache.default'),
                'status' => 'active',
                'hits' => 'not_available',
                'misses' => 'not_available',
                'memory_usage' => 'not_available'
            ];
        } catch (\Exception $e) {
            return [
                'type' => config('cache.default'),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 批量设置缓存
     */
    public function putMany(array $data, ?int $ttl = null, array $tags = []): bool
    {
        $success = true;
        
        foreach ($data as $key => $value) {
            if (!$this->put($key, $value, $ttl, $tags)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * 批量获取缓存
     */
    public function getMany(array $keys): array
    {
        $results = [];
        
        foreach ($keys as $key) {
            $results[$key] = $this->get($key);
        }
        
        return $results;
    }

    /**
     * 原子递增
     */
    public function increment(string $key, int $value = 1): int
    {
        return Cache::increment($this->buildKey($key), $value);
    }

    /**
     * 原子递减
     */
    public function decrement(string $key, int $value = 1): int
    {
        return Cache::decrement($this->buildKey($key), $value);
    }

    /**
     * 获取并删除缓存
     */
    public function pull(string $key, $default = null)
    {
        return Cache::pull($this->buildKey($key), $default);
    }

    /**
     * 永久缓存
     */
    public function forever(string $key, $value, array $tags = []): bool
    {
        $cacheKey = $this->buildKey($key);
        
        if ($this->isTaggingSupported() && !empty($tags)) {
            return Cache::tags($tags)->forever($cacheKey, $value);
        }
        
        return Cache::forever($cacheKey, $value);
    }

    // ========== 私有方法 ==========

    private function buildKey(string $key): string
    {
        return self::PREFIX . $key;
    }

    private function getDefaultTtl(string $key): int
    {
        foreach (self::TTL_CONFIG as $pattern => $ttl) {
            if (str_contains($key, $pattern)) {
                return $ttl;
            }
        }
        
        return 3600; // 默认1小时
    }

    private function forgetByPatterns(array $patterns): void
    {
        foreach ($patterns as $pattern) {
            if (config('cache.default') === 'redis') {
                $this->forgetRedisPattern($pattern);
            } else {
                // 对于不支持模式匹配的缓存驱动，只能逐个删除
                Cache::forget($this->buildKey($pattern));
            }
        }
    }

    private function forgetRedisPattern(string $pattern): void
    {
        try {
            $redis = Redis::connection();
            $keys = $redis->keys($this->buildKey($pattern));
            
            if (!empty($keys)) {
                $redis->del($keys);
            }
        } catch (\Exception $e) {
            // 记录日志但不抛出异常
            logger()->warning("Failed to clear Redis pattern: {$pattern}", [
                'error' => $e->getMessage()
            ]);
        }
    }

    private function isTaggingSupported(): bool
    {
        return in_array(config('cache.default'), ['redis', 'memcached']);
    }

    private function getRedisStats(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();
            
            return [
                'type' => 'redis',
                'status' => 'active',
                'memory_usage' => $info['used_memory_human'] ?? 'unknown',
                'connected_clients' => $info['connected_clients'] ?? 0,
                'total_commands_processed' => $info['total_commands_processed'] ?? 0,
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info),
                'navigation_keys' => $this->countNavigationKeys()
            ];
        } catch (\Exception $e) {
            return [
                'type' => 'redis',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function calculateHitRate(array $info): float
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;
        
        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }

    private function countNavigationKeys(): int
    {
        try {
            $redis = Redis::connection();
            return count($redis->keys($this->buildKey('*')));
        } catch (\Exception $e) {
            return 0;
        }
    }
}