import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                         *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                     *//* empty css                *//* empty css                  */import{r as l,L as a,e as t,k as n,l as s,t as o,E as i,z as u,u as d,D as r,F as c,Y as p,a3 as m,A as _,y as v,G as f,B as h,C as g}from"./vue-vendor-Dy164gUc.js";import{d as b,u as y,c as V,r as k}from"./substation-C0LtbWrR.js";import{e as w}from"./user-CJhH85FQ.js";import{ag as x,T as C,a4 as U,at as j,aV as I,aw as S,aM as D,b9 as T,b8 as A,aL as q,c9 as $,aY as z,bp as L,bq as P,aZ as E,a_ as R,bh as B,bi as N,U as F,a$ as O,bw as Y,br as M,ay as G,bj as H,bg as J,b1 as K,a0 as Z,_ as Q,ao as W,b6 as X,ar as ee,bK as le,bL as ae,Q as te,R as ne,cd as se,p as oe,o as ie}from"./element-plus-h2SQQM64.js";import{P as ue}from"./index-ByaD-6N-.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";/* empty css                      */const de={class:"modern-substation-list"},re={class:"page-header"},ce={class:"header-content"},pe={class:"header-left"},me={class:"header-icon"},_e={class:"header-actions"},ve={class:"stats-section"},fe={class:"stats-container"},he={class:"stat-content"},ge={class:"stat-value"},be={class:"stat-label"},ye={class:"filter-section"},Ve={class:"filter-content"},ke={class:"filter-left"},we={class:"filter-right"},xe={class:"dialog-footer"},Ce={class:"dialog-footer"},Ue={style:{display:"flex","justify-content":"space-between","align-items":"center"}},je={class:"form-tip"},Ie={class:"dialog-footer"},Se={class:"help-content"},De={class:"help-section"},Te={class:"feature-item"},Ae={class:"feature-icon"},qe={class:"feature-item"},$e={class:"feature-icon"},ze={class:"feature-item"},Le={class:"feature-icon"},Pe={class:"feature-item"},Ee={class:"feature-icon"},Re={class:"feature-item"},Be={class:"feature-icon"},Ne={class:"feature-item"},Fe={class:"feature-icon"},Oe={class:"help-section"},Ye={class:"help-section"},Me={class:"commission-guide"},Ge={class:"guide-item"},He={class:"help-section"},Je={class:"guide-content"},Ke={class:"guide-content"},Ze={class:"guide-content"},Qe={class:"guide-content"},We={class:"help-section"},Xe=e({__name:"SubstationList",setup(e){const Xe=l(!0),el=l([]);l([]);const ll=l(0),al=l(!1),tl=l([]),nl=l([]),sl=l(!1),ol=a({keyword:"",status:"",region:""}),il=a({page:1,size:20}),ul=l([{key:"total",label:"总分站数",value:12,icon:"OfficeBuilding",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"CaretTop",change:"+3"},{key:"active",label:"运营中",value:9,icon:"CircleCheckFilled",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"CaretTop",change:"+2"},{key:"revenue",label:"总收益",value:"¥45,678",icon:"Money",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"CaretTop",change:"+15.8%"},{key:"users",label:"总用户数",value:2456,icon:"User",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"CaretTop",change:"+128"}]),dl=l(["create-substation"]),rl=l([]),cl=l([{status:"正常",color:"success",description:"分站正常运行，所有功能可用",features:"无限制",action:"保持现状，定期监控"},{status:"禁用",color:"info",description:"分站被管理员暂停使用",features:"用户无法访问，管理员可登录",action:"检查禁用原因，解决后重新启用"},{status:"过期",color:"danger",description:"分站使用期限已到期",features:"所有功能停用，数据保留",action:"联系客户续费或备份数据"}]),pl=l([{type:"新建分站",range:"5%-10%",reason:"吸引新客户，建立合作关系"},{type:"标准分站",range:"10%-15%",reason:"平衡收益与成本，维持正常运营"},{type:"高级分站",range:"15%-20%",reason:"提供更多服务，获得更高收益"},{type:"企业分站",range:"8%-12%",reason:"大客户优惠，长期合作考虑"}]),ml=a({page:1,per_page:10,name:void 0,domain:void 0,status:void 0}),_l=a({visible:!1,title:""}),vl=a({visible:!1,substationName:"",currentExpireDate:""}),fl=a({visible:!1,title:"",substationId:null}),hl=l("payoreo"),gl=l([]),bl=a({}),yl=l(!1),Vl=l(!1),kl=l(),wl=l(),xl=l({}),Cl=l({months:null}),Ul=a({name:[{required:!0,message:"分站名称不能为空",trigger:"blur"}],domain:[{required:!0,message:"分站域名不能为空",trigger:"blur"}],user_id:[{required:!0,message:"管理员不能为空",trigger:"change"}],commission_rate:[{required:!0,message:"抽成比例不能为空",trigger:"blur"}],expire_months:[{required:!0,message:"有效期不能为空",trigger:"change"}]}),jl=a({months:[{required:!0,message:"续费时长不能为空",trigger:"change"}]}),Il=a({config_name:[{required:!0,message:"配置名称不能为空",trigger:"blur"}],api_url:[{required:!0,message:"API地址不能为空",trigger:"blur"},{type:"url",message:"请输入正确的URL格式",trigger:"blur"}],pid:[{required:!0,message:"商户ID不能为空",trigger:"blur"}],key:[{required:!0,message:"商户秘钥不能为空",trigger:"blur"}]}),Sl={async fetchSubstationList(){Xe.value=!0;try{await new Promise(e=>setTimeout(e,800)),el.value=[{id:1,name:"北京分站",domain:"beijing.example.com",manager:"张三",phone:"13800138001",email:"<EMAIL>",status:1,userCount:1256,revenue:125800,todayRevenue:3200,groupCount:45,region:"华北",address:"北京市朝阳区建国门外大街1号",created_at:"2024-01-15",expire_at:"2025-01-15",lastLoginAt:"2024-03-15 14:30:00",commission_rate:.1,user:{nickname:"张三"},avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:2,name:"上海分站",domain:"shanghai.example.com",manager:"李四",phone:"13800138002",email:"<EMAIL>",status:1,userCount:2134,revenue:189e3,todayRevenue:4500,groupCount:67,region:"华东",address:"上海市浦东新区陆家嘴环路1000号",created_at:"2024-01-20",expire_at:"2025-01-20",lastLoginAt:"2024-03-15 16:45:00",commission_rate:.12,user:{nickname:"李四"},avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:3,name:"广州分站",domain:"guangzhou.example.com",manager:"王五",phone:"13800138003",email:"<EMAIL>",status:2,userCount:789,revenue:56e3,todayRevenue:0,groupCount:23,region:"华南",address:"广州市天河区珠江新城花城大道5号",created_at:"2024-02-01",expire_at:"2025-02-01",lastLoginAt:"2024-03-10 09:20:00",commission_rate:.08,user:{nickname:"王五"},avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:4,name:"深圳分站",domain:"shenzhen.example.com",manager:"赵六",phone:"13800138004",email:"<EMAIL>",status:1,userCount:1567,revenue:234500,todayRevenue:5600,groupCount:89,region:"华南",address:"深圳市南山区科技园南区深南大道9988号",created_at:"2024-01-10",expire_at:"2025-01-10",lastLoginAt:"2024-03-15 18:20:00",commission_rate:.15,user:{nickname:"赵六"},avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}],ll.value=4,Sl.updateStats()}catch(e){te.error("获取分站列表失败"),console.error("获取分站列表错误:",e)}finally{Xe.value=!1}},updateStats(){const e=el.value.filter(e=>1===e.status).length,l=el.value.reduce((e,l)=>e+l.revenue,0),a=el.value.reduce((e,l)=>e+l.userCount,0);ul.value[0].value=el.value.length,ul.value[1].value=e,ul.value[2].value=`¥${(l/1e4).toFixed(1)}万`,ul.value[3].value=a},handleSearch(){il.page=1,Sl.fetchSubstationList(),te.success("搜索完成")},handleReset(){Object.assign(ol,{keyword:"",status:"",region:""}),Sl.handleSearch()},async handleExport(){try{te.info("正在导出数据..."),await new Promise(e=>setTimeout(e,2e3)),te.success("导出成功")}catch(e){te.error("导出失败")}}},Dl=async()=>{await Sl.fetchSubstationList()},Tl=()=>{ml.page=1,Dl()},Al=()=>{ml.page=1,ml.name=void 0,ml.domain=void 0,ml.status=void 0,Tl()},ql=async()=>{await Rl(),xl.value={commission_rate:.1,expire_months:12},_l.title="新增分站",_l.visible=!0},$l=e=>{const l=e.id?[e.id]:tl.value;ne.confirm(`是否确认删除ID为"${l.join(",")}"的分站？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{for(const e of l)await b(e);Dl(),te.success("删除成功")}).catch(()=>{})},zl=e=>{tl.value=e.map(e=>e.id),al.value=e.length>0},Ll=()=>{_l.visible=!1,kl.value.resetFields()},Pl=async()=>{await kl.value.validate(),xl.value.id?(await y(xl.value.id,xl.value),te.success("修改成功")):(await V(xl.value),te.success("新增成功")),_l.visible=!1,Dl()},El=async()=>{await wl.value.validate(),await k(Cl.value.substation_id,Cl.value.months),te.success("续费成功"),vl.visible=!1,Dl()},Rl=async()=>{const{data:e}=await w({role:"substation",per_page:1e3});nl.value=e.data},Bl=e=>({1:"正常",2:"禁用",3:"过期"}[e]),Nl=async e=>{try{const e=await fetch("/api/v1/api/v1/payment-channels/user-channels",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(e.ok){const l=await e.json();l.success&&(gl.value=l.data,l.data.length>0&&(hl.value=l.data[0].channel_code))}}catch(l){console.error("获取支付通道失败:",l),te.error("获取支付通道失败")}},Fl=()=>{gl.value.forEach(e=>{bl[e.channel_code]||(bl[e.channel_code]={config_name:`${e.channel_name}配置`,api_url:"payoreo"===e.channel_code?"https://api.payoreo.com":"",pid:"",key:"",notify_url:"",return_url:""}),e.has_config&&Ol(e.channel_code)})},Ol=async e=>{try{const l=await fetch(`/api/v1/api/v1/payment-channels/user-config/${e}`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(l.ok){const a=await l.json();a.success&&a.data&&(bl[e]={...bl[e],...a.data.config_data})}}catch(l){console.error("加载支付配置失败:",l)}};t(()=>{Sl.fetchSubstationList()});const Yl=()=>Sl.handleSearch(),Ml=()=>Sl.handleReset(),Gl=()=>Sl.handleExport();return(e,l)=>{const a=C,t=j,b=D,y=A,V=T,k=z,w=P,ne=L,tl=R,il=E,Sl=N,Hl=O,Jl=B,Kl=M,Zl=G,Ql=K,Wl=J,Xl=H,ea=ae,la=le,aa=Y;return s(),n("div",de,[o("div",re,[o("div",ce,[o("div",pe,[o("div",me,[i(a,{size:"24"},{default:u(()=>[i(d(x))]),_:1})]),l[25]||(l[25]=o("div",{class:"header-text"},[o("h1",null,"分站管理"),o("p",null,"管理平台分站，配置分站权限，监控分站运营状况")],-1))]),o("div",_e,[i(t,{onClick:Gl,class:"action-btn secondary"},{default:u(()=>[i(a,null,{default:u(()=>[i(d(U))]),_:1}),l[26]||(l[26]=r(" 导出数据 ",-1))]),_:1,__:[26]}),i(t,{onClick:l[0]||(l[0]=e=>sl.value=!0),class:"action-btn secondary"},{default:u(()=>[i(a,null,{default:u(()=>[i(d(I))]),_:1}),l[27]||(l[27]=r(" 功能说明 ",-1))]),_:1,__:[27]}),i(t,{type:"primary",onClick:ql,class:"action-btn primary"},{default:u(()=>[i(a,null,{default:u(()=>[i(d(S))]),_:1}),l[28]||(l[28]=r(" 新增分站 ",-1))]),_:1,__:[28]})])])]),o("div",ve,[o("div",fe,[(s(!0),n(c,null,p(ul.value,e=>(s(),n("div",{class:"stat-card",key:e.key},[o("div",{class:"stat-icon",style:oe({background:e.color})},[i(a,{size:"20"},{default:u(()=>[(s(),v(g(e.icon)))]),_:2},1024)],4),o("div",he,[o("div",ge,F(e.value),1),o("div",be,F(e.label),1)]),o("div",{class:ie(["stat-trend",e.trend])},[i(a,{size:"14"},{default:u(()=>[(s(),v(g(e.trendIcon)))]),_:2},1024),o("span",null,F(e.change),1)],2)]))),128))])]),o("div",ye,[i(k,{class:"filter-card",shadow:"never"},{default:u(()=>[o("div",Ve,[o("div",ke,[i(b,{modelValue:ol.keyword,"onUpdate:modelValue":l[1]||(l[1]=e=>ol.keyword=e),placeholder:"搜索分站名称、域名、管理员","prefix-icon":"Search",clearable:"",class:"search-input",onKeyup:m(Yl,["enter"])},null,8,["modelValue"]),i(V,{modelValue:ol.status,"onUpdate:modelValue":l[2]||(l[2]=e=>ol.status=e),placeholder:"分站状态",clearable:"",class:"filter-select"},{default:u(()=>[i(y,{label:"全部状态",value:""}),i(y,{label:"正常运营",value:"1"}),i(y,{label:"已禁用",value:"2"}),i(y,{label:"已过期",value:"3"})]),_:1},8,["modelValue"]),i(V,{modelValue:ol.region,"onUpdate:modelValue":l[3]||(l[3]=e=>ol.region=e),placeholder:"所属区域",clearable:"",class:"filter-select"},{default:u(()=>[i(y,{label:"全部区域",value:""}),i(y,{label:"华北地区",value:"north"}),i(y,{label:"华东地区",value:"east"}),i(y,{label:"华南地区",value:"south"}),i(y,{label:"西部地区",value:"west"})]),_:1},8,["modelValue"])]),o("div",we,[i(t,{onClick:Yl,type:"primary",class:"search-btn"},{default:u(()=>[i(a,null,{default:u(()=>[i(d(q))]),_:1}),l[29]||(l[29]=r(" 搜索 ",-1))]),_:1,__:[29]}),i(t,{onClick:Ml,class:"reset-btn"},{default:u(()=>[i(a,null,{default:u(()=>[i(d($))]),_:1}),l[30]||(l[30]=r(" 重置 ",-1))]),_:1,__:[30]})])])]),_:1})]),i(k,null,{default:u(()=>[i(ne,{inline:!0,model:ml,class:"filter-container"},{default:u(()=>[i(w,{label:"分站名称"},{default:u(()=>[i(b,{modelValue:ml.name,"onUpdate:modelValue":l[4]||(l[4]=e=>ml.name=e),placeholder:"请输入分站名称",clearable:""},null,8,["modelValue"])]),_:1}),i(w,{label:"域名"},{default:u(()=>[i(b,{modelValue:ml.domain,"onUpdate:modelValue":l[5]||(l[5]=e=>ml.domain=e),placeholder:"请输入域名",clearable:""},null,8,["modelValue"])]),_:1}),i(w,{label:"状态"},{default:u(()=>[i(V,{modelValue:ml.status,"onUpdate:modelValue":l[6]||(l[6]=e=>ml.status=e),placeholder:"请选择状态",clearable:""},{default:u(()=>[i(y,{label:"正常",value:1}),i(y,{label:"禁用",value:2}),i(y,{label:"过期",value:3})]),_:1},8,["modelValue"])]),_:1}),i(w,null,{default:u(()=>[i(t,{type:"primary",icon:"Search",onClick:Tl},{default:u(()=>l[31]||(l[31]=[r("搜索",-1)])),_:1,__:[31]}),i(t,{icon:"Refresh",onClick:Al},{default:u(()=>l[32]||(l[32]=[r("重置",-1)])),_:1,__:[32]})]),_:1})]),_:1},8,["model"]),i(il,{gutter:10,class:"mb-2"},{default:u(()=>[i(tl,{span:1.5},{default:u(()=>[i(t,{type:"primary",plain:"",icon:"Plus",onClick:ql},{default:u(()=>l[33]||(l[33]=[r("新增分站",-1)])),_:1,__:[33]})]),_:1}),i(tl,{span:1.5},{default:u(()=>[i(t,{type:"danger",plain:"",icon:"Delete",disabled:!al.value,onClick:$l},{default:u(()=>l[34]||(l[34]=[r("删除",-1)])),_:1,__:[34]},8,["disabled"])]),_:1})]),_:1}),_((s(),v(Jl,{data:el.value,onSelectionChange:zl},{default:u(()=>[i(Sl,{type:"selection",width:"55",align:"center"}),i(Sl,{label:"ID",prop:"id",width:"80"}),i(Sl,{label:"分站名称",prop:"name","show-overflow-tooltip":""}),i(Sl,{label:"域名",prop:"domain","show-overflow-tooltip":""}),i(Sl,{label:"管理员",prop:"user.nickname"}),i(Sl,{label:"抽成比例",align:"center"},{default:u(e=>[r(F((100*e.row.commission_rate).toFixed(1))+"% ",1)]),_:1}),i(Sl,{label:"状态",align:"center"},{default:u(e=>{return[i(Hl,{type:(l=e.row.status,{1:"success",2:"info",3:"danger"}[l])},{default:u(()=>[r(F(Bl(e.row.status)),1)]),_:2},1032,["type"])];var l}),_:1}),i(Sl,{label:"到期时间",prop:"expire_at"}),i(Sl,{label:"创建时间",prop:"created_at"}),i(Sl,{label:"操作",width:"280",align:"center"},{default:u(e=>[i(t,{type:"primary",link:"",icon:"Edit",onClick:l=>(async e=>{await Rl(),xl.value={...e},_l.title="编辑分站",_l.visible=!0})(e.row)},{default:u(()=>l[35]||(l[35]=[r("编辑",-1)])),_:2,__:[35]},1032,["onClick"]),i(t,{type:"warning",link:"",icon:"CreditCard",onClick:l=>(async e=>{fl.title=`${e.name} - 支付配置`,fl.substationId=e.id,fl.visible=!0,await Nl(e.id),Fl()})(e.row)},{default:u(()=>l[36]||(l[36]=[r("支付配置",-1)])),_:2,__:[36]},1032,["onClick"]),i(t,{type:"success",link:"",icon:"Clock",onClick:l=>{return a=e.row,vl.substationName=a.name,vl.currentExpireDate=a.expire_at,Cl.value={substation_id:a.id,current_expire_date:a.expire_at,months:null},void(vl.visible=!0);var a}},{default:u(()=>l[37]||(l[37]=[r("续费",-1)])),_:2,__:[37]},1032,["onClick"]),i(t,{type:"danger",link:"",icon:"Delete",onClick:l=>$l(e.row)},{default:u(()=>l[38]||(l[38]=[r("删除",-1)])),_:2,__:[38]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[aa,Xe.value]]),_(i(ue,{total:ll.value,page:ml.page,"onUpdate:page":l[7]||(l[7]=e=>ml.page=e),limit:ml.per_page,"onUpdate:limit":l[8]||(l[8]=e=>ml.per_page=e),onPagination:Dl},null,8,["total","page","limit"]),[[f,ll.value>0]])]),_:1}),i(Zl,{title:_l.title,modelValue:_l.visible,"onUpdate:modelValue":l[15]||(l[15]=e=>_l.visible=e),width:"600px","append-to-body":""},{footer:u(()=>[o("div",xe,[i(t,{onClick:Ll},{default:u(()=>l[41]||(l[41]=[r("取 消",-1)])),_:1,__:[41]}),i(t,{type:"primary",onClick:Pl},{default:u(()=>l[42]||(l[42]=[r("确 定",-1)])),_:1,__:[42]})])]),default:u(()=>[i(ne,{ref_key:"formRef",ref:kl,model:xl.value,rules:Ul,"label-width":"100px"},{default:u(()=>[i(w,{label:"分站名称",prop:"name"},{default:u(()=>[i(b,{modelValue:xl.value.name,"onUpdate:modelValue":l[9]||(l[9]=e=>xl.value.name=e),placeholder:"请输入分站名称"},null,8,["modelValue"])]),_:1}),i(w,{label:"分站域名",prop:"domain"},{default:u(()=>[i(b,{modelValue:xl.value.domain,"onUpdate:modelValue":l[10]||(l[10]=e=>xl.value.domain=e),placeholder:"请输入分站域名"},{append:u(()=>l[39]||(l[39]=[r(".linkhub.pro",-1)])),_:1},8,["modelValue"])]),_:1}),i(w,{label:"管理员",prop:"user_id"},{default:u(()=>[i(V,{modelValue:xl.value.user_id,"onUpdate:modelValue":l[11]||(l[11]=e=>xl.value.user_id=e),placeholder:"请选择管理员",filterable:""},{default:u(()=>[(s(!0),n(c,null,p(nl.value,e=>(s(),v(y,{key:e.id,label:`${e.nickname}(${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(w,{label:"抽成比例",prop:"commission_rate"},{default:u(()=>[i(Kl,{modelValue:xl.value.commission_rate,"onUpdate:modelValue":l[12]||(l[12]=e=>xl.value.commission_rate=e),precision:3,step:.001,min:0,max:1},null,8,["modelValue"]),l[40]||(l[40]=o("span",{class:"ml-2"},"（0-1之间的小数，如0.1表示10%）",-1))]),_:1,__:[40]}),i(w,{label:"有效期",prop:"expire_months"},{default:u(()=>[i(V,{modelValue:xl.value.expire_months,"onUpdate:modelValue":l[13]||(l[13]=e=>xl.value.expire_months=e),placeholder:"请选择有效期"},{default:u(()=>[i(y,{label:"1个月",value:1}),i(y,{label:"3个月",value:3}),i(y,{label:"6个月",value:6}),i(y,{label:"12个月",value:12}),i(y,{label:"24个月",value:24}),i(y,{label:"36个月",value:36})]),_:1},8,["modelValue"])]),_:1}),i(w,{label:"描述",prop:"description"},{default:u(()=>[i(b,{type:"textarea",modelValue:xl.value.description,"onUpdate:modelValue":l[14]||(l[14]=e=>xl.value.description=e),placeholder:"请输入分站描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),i(Zl,{title:"分站续费",modelValue:vl.visible,"onUpdate:modelValue":l[18]||(l[18]=e=>vl.visible=e),width:"500px","append-to-body":""},{footer:u(()=>[o("div",Ce,[i(t,{onClick:l[17]||(l[17]=e=>vl.visible=!1)},{default:u(()=>l[43]||(l[43]=[r("取 消",-1)])),_:1,__:[43]}),i(t,{type:"primary",onClick:El},{default:u(()=>l[44]||(l[44]=[r("确 定",-1)])),_:1,__:[44]})])]),default:u(()=>[i(ne,{ref_key:"renewFormRef",ref:wl,model:Cl.value,rules:jl,"label-width":"100px"},{default:u(()=>[i(w,{label:"分站名称"},{default:u(()=>[i(b,{value:vl.substationName,readonly:""},null,8,["value"])]),_:1}),i(w,{label:"当前到期时间"},{default:u(()=>[i(b,{value:vl.currentExpireDate,readonly:""},null,8,["value"])]),_:1}),i(w,{label:"续费时长",prop:"months"},{default:u(()=>[i(V,{modelValue:Cl.value.months,"onUpdate:modelValue":l[16]||(l[16]=e=>Cl.value.months=e),placeholder:"请选择续费时长"},{default:u(()=>[i(y,{label:"1个月",value:1}),i(y,{label:"3个月",value:3}),i(y,{label:"6个月",value:6}),i(y,{label:"12个月",value:12}),i(y,{label:"24个月",value:24})]),_:1},8,["modelValue"])]),_:1}),Cl.value.months?(s(),v(w,{key:0,label:"续费后到期时间"},{default:u(()=>[i(b,{value:Cl.value.months&&Cl.value.current_expire_date?se(Cl.value.current_expire_date).add(Cl.value.months,"month").format("YYYY-MM-DD HH:mm:ss"):"",readonly:""},null,8,["value"])]),_:1})):h("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),i(Zl,{title:fl.title,modelValue:fl.visible,"onUpdate:modelValue":l[21]||(l[21]=e=>fl.visible=e),width:"800px","append-to-body":""},{footer:u(()=>[o("div",Ie,[i(t,{onClick:l[20]||(l[20]=e=>fl.visible=!1)},{default:u(()=>l[50]||(l[50]=[r("关 闭",-1)])),_:1,__:[50]})])]),default:u(()=>[i(Xl,{modelValue:hl.value,"onUpdate:modelValue":l[19]||(l[19]=e=>hl.value=e),type:"card"},{default:u(()=>[(s(!0),n(c,null,p(gl.value,e=>(s(),v(Wl,{key:e.channel_code,label:e.channel_name,name:e.channel_code},{default:u(()=>[i(k,{shadow:"never",style:{"margin-bottom":"20px"}},{header:u(()=>[o("div",Ue,[o("span",null,F(e.channel_name)+"配置",1),o("div",null,[e.has_config?(s(),v(Hl,{key:0,type:e.test_status?"success":"warning"},{default:u(()=>[r(F(e.test_status?"已测试通过":"未测试"),1)]),_:2},1032,["type"])):(s(),v(Hl,{key:1,type:"info"},{default:u(()=>l[45]||(l[45]=[r("未配置",-1)])),_:1,__:[45]}))])])]),default:u(()=>[i(ne,{ref_for:!0,ref:`paymentForm_${e.channel_code}`,model:bl[e.channel_code],rules:Il,"label-width":"120px"},{default:u(()=>["payoreo"===e.channel_code?(s(),n(c,{key:0},[i(w,{label:"配置名称",prop:"config_name"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].config_name,"onUpdate:modelValue":l=>bl[e.channel_code].config_name=l,placeholder:"请输入配置名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(w,{label:"API地址",prop:"api_url"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].api_url,"onUpdate:modelValue":l=>bl[e.channel_code].api_url=l,placeholder:"https://api.payoreo.com"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(w,{label:"商户ID",prop:"pid"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].pid,"onUpdate:modelValue":l=>bl[e.channel_code].pid=l,placeholder:"请输入易支付商户ID"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(w,{label:"商户秘钥",prop:"key"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].key,"onUpdate:modelValue":l=>bl[e.channel_code].key=l,type:"password","show-password":"",placeholder:"请输入易支付商户秘钥"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),i(w,{label:"异步通知地址"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].notify_url,"onUpdate:modelValue":l=>bl[e.channel_code].notify_url=l,placeholder:"留空则使用系统默认"},null,8,["modelValue","onUpdate:modelValue"]),o("div",je,"系统默认: "+F(`${window.location.origin}/api/v1/payment/notify/payoreo`),1)]),_:2},1024),i(w,{label:"同步返回地址"},{default:u(()=>[i(b,{modelValue:bl[e.channel_code].return_url,"onUpdate:modelValue":l=>bl[e.channel_code].return_url=l,placeholder:"留空则使用系统默认"},null,8,["modelValue","onUpdate:modelValue"]),l[46]||(l[46]=o("div",{class:"form-tip"},"用户支付成功后的跳转页面",-1))]),_:2,__:[46]},1024)],64)):(s(),v(Ql,{key:1,title:`${e.channel_name}配置功能开发中`,type:"info","show-icon":"",closable:!1},null,8,["title"])),i(w,null,{default:u(()=>[i(t,{type:"primary",onClick:l=>(async e=>{try{yl.value=!0;const l=await fetch("/api/v1/api/v1/payment-channels/user-config",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"},body:JSON.stringify({channel_code:e,config_name:bl[e].config_name,config_data:bl[e]})});if(l.ok){const e=await l.json();e.success?(te.success("支付配置保存成功"),await Nl(fl.substationId)):te.error(e.message||"保存失败")}else te.error("保存失败")}catch(l){console.error("保存支付配置失败:",l),te.error("保存失败")}finally{yl.value=!1}})(e.channel_code),loading:yl.value},{default:u(()=>l[47]||(l[47]=[r(" 保存配置 ",-1)])),_:2,__:[47]},1032,["onClick","loading"]),i(t,{type:"success",onClick:l=>(async e=>{try{Vl.value=!0;const l=await fetch("/api/v1/api/v1/payment-channels/test-config",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"},body:JSON.stringify({channel_code:e})});if(l.ok){const e=await l.json();e.success?(te.success("配置测试通过"),await Nl(fl.substationId)):te.error(e.message||"测试失败")}else te.error("测试失败")}catch(l){console.error("测试支付配置失败:",l),te.error("测试失败")}finally{Vl.value=!1}})(e.channel_code),loading:Vl.value,disabled:!e.has_config},{default:u(()=>l[48]||(l[48]=[r(" 测试配置 ",-1)])),_:2,__:[48]},1032,["onClick","loading","disabled"]),e.has_config?(s(),v(t,{key:0,type:"info",onClick:l=>Ol(e.channel_code)},{default:u(()=>l[49]||(l[49]=[r(" 重新加载 ",-1)])),_:2,__:[49]},1032,["onClick"])):h("",!0)]),_:2},1024)]),_:2},1032,["model","rules"])]),_:2},1024)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),i(Zl,{modelValue:sl.value,"onUpdate:modelValue":l[24]||(l[24]=e=>sl.value=e),title:"分站管理功能说明",width:"1000px",class:"help-dialog"},{default:u(()=>[o("div",Se,[l[76]||(l[76]=o("div",{class:"help-section"},[o("h3",null,"🏢 功能概述"),o("p",null,"分站管理系统是平台多站点运营的核心功能，支持创建和管理多个独立的分站点，每个分站拥有独立的域名、管理员和配置，实现平台的规模化扩展和区域化运营。")],-1)),o("div",De,[l[57]||(l[57]=o("h3",null,"🚀 核心功能模块",-1)),i(il,{gutter:20},{default:u(()=>[i(tl,{span:8},{default:u(()=>[o("div",Te,[o("div",Ae,[i(a,null,{default:u(()=>[i(d(S))]),_:1})]),l[51]||(l[51]=o("div",{class:"feature-content"},[o("h4",null,"分站创建"),o("p",null,"创建新的分站点，配置基本信息和管理权限")],-1))])]),_:1}),i(tl,{span:8},{default:u(()=>[o("div",qe,[o("div",$e,[i(a,null,{default:u(()=>[i(d(Z))]),_:1})]),l[52]||(l[52]=o("div",{class:"feature-content"},[o("h4",null,"分站配置"),o("p",null,"设置分站域名、抽成比例、有效期等参数")],-1))])]),_:1}),i(tl,{span:8},{default:u(()=>[o("div",ze,[o("div",Le,[i(a,null,{default:u(()=>[i(d(Q))]),_:1})]),l[53]||(l[53]=o("div",{class:"feature-content"},[o("h4",null,"支付配置"),o("p",null,"为分站配置独立的支付通道和参数")],-1))])]),_:1}),i(tl,{span:8},{default:u(()=>[o("div",Pe,[o("div",Ee,[i(a,null,{default:u(()=>[i(d(W))]),_:1})]),l[54]||(l[54]=o("div",{class:"feature-content"},[o("h4",null,"管理员管理"),o("p",null,"指定分站管理员，分配管理权限")],-1))])]),_:1}),i(tl,{span:8},{default:u(()=>[o("div",Re,[o("div",Be,[i(a,null,{default:u(()=>[i(d(X))]),_:1})]),l[55]||(l[55]=o("div",{class:"feature-content"},[o("h4",null,"续费管理"),o("p",null,"管理分站有效期，处理续费申请")],-1))])]),_:1}),i(tl,{span:8},{default:u(()=>[o("div",Ne,[o("div",Fe,[i(a,null,{default:u(()=>[i(d(ee))]),_:1})]),l[56]||(l[56]=o("div",{class:"feature-content"},[o("h4",null,"状态监控"),o("p",null,"实时监控分站运行状态和业务数据")],-1))])]),_:1})]),_:1})]),o("div",Oe,[l[58]||(l[58]=o("h3",null,"📊 分站状态说明",-1)),i(Jl,{data:cl.value,style:{width:"100%"}},{default:u(()=>[i(Sl,{prop:"status",label:"状态",width:"100"},{default:u(({row:e})=>[i(Hl,{type:e.color},{default:u(()=>[r(F(e.status),1)]),_:2},1032,["type"])]),_:1}),i(Sl,{prop:"description",label:"状态描述"}),i(Sl,{prop:"features",label:"功能限制"}),i(Sl,{prop:"action",label:"处理建议"})]),_:1},8,["data"])]),o("div",Ye,[l[61]||(l[61]=o("h3",null,"💰 抽成比例设置指南",-1)),o("div",Me,[l[60]||(l[60]=o("div",{class:"guide-item"},[o("h4",null,"🔸 抽成比例说明"),o("p",null,"抽成比例是平台从分站收入中提取的分成比例，用小数表示（如0.1表示10%）"),o("div",{class:"commission-examples"},[o("div",{class:"example-item"},[o("span",{class:"example-label"},"示例1："),o("span",null,"设置0.05，表示平台抽成5%，分站保留95%")]),o("div",{class:"example-item"},[o("span",{class:"example-label"},"示例2："),o("span",null,"设置0.15，表示平台抽成15%，分站保留85%")])])],-1)),o("div",Ge,[l[59]||(l[59]=o("h4",null,"🔸 推荐设置范围",-1)),i(Jl,{data:pl.value,size:"small"},{default:u(()=>[i(Sl,{prop:"type",label:"分站类型",width:"120"}),i(Sl,{prop:"range",label:"推荐比例",width:"120"}),i(Sl,{prop:"reason",label:"设置理由"})]),_:1},8,["data"])])])]),l[77]||(l[77]=o("div",{class:"help-section"},[o("h3",null,"💳 支付配置指南"),o("div",{class:"payment-guide"},[o("div",{class:"guide-step"},[o("h4",null,"📋 配置步骤"),o("ol",null,[o("li",null,'点击分站列表中的"支付配置"按钮'),o("li",null,"选择要配置的支付通道（如易支付）"),o("li",null,"填写支付通道的配置信息"),o("li",null,"测试配置是否正确"),o("li",null,"保存配置并启用")])]),o("div",{class:"guide-step"},[o("h4",null,"⚙️ 易支付配置说明"),o("div",{class:"config-fields"},[o("div",{class:"field-item"},[o("strong",null,"API地址："),r("易支付平台的API接口地址 ")]),o("div",{class:"field-item"},[o("strong",null,"商户ID："),r("在易支付平台注册的商户标识 ")]),o("div",{class:"field-item"},[o("strong",null,"商户秘钥："),r("用于签名验证的密钥，请妥善保管 ")]),o("div",{class:"field-item"},[o("strong",null,"通知地址："),r("支付成功后的异步通知地址 ")]),o("div",{class:"field-item"},[o("strong",null,"返回地址："),r("支付成功后用户跳转的页面 ")])])])])],-1)),o("div",He,[l[70]||(l[70]=o("h3",null,"📝 操作指南",-1)),i(la,{modelValue:dl.value,"onUpdate:modelValue":l[22]||(l[22]=e=>dl.value=e)},{default:u(()=>[i(ea,{title:"如何创建新分站？",name:"create-substation"},{default:u(()=>[o("div",Je,[l[63]||(l[63]=o("ol",null,[o("li",null,'点击页面右上角的"新增分站"按钮'),o("li",null,[r("填写分站基本信息： "),o("ul",null,[o("li",null,"分站名称：建议使用有意义的名称"),o("li",null,"分站域名：输入二级域名（系统自动添加后缀）"),o("li",null,"管理员：选择负责该分站的管理员"),o("li",null,"抽成比例：设置平台分成比例"),o("li",null,"有效期：选择分站的使用期限")])]),o("li",null,"填写分站描述（可选）"),o("li",null,'点击"确定"完成创建')],-1)),i(Ql,{type:"info",closable:!1},{default:u(()=>l[62]||(l[62]=[r(" 💡 提示：域名创建后需要进行DNS解析配置才能正常访问 ",-1)])),_:1,__:[62]})])]),_:1}),i(ea,{title:"如何配置分站支付？",name:"payment-config"},{default:u(()=>[o("div",Ke,[l[65]||(l[65]=o("ol",null,[o("li",null,"在分站列表中找到目标分站"),o("li",null,'点击"支付配置"按钮'),o("li",null,"选择支付通道标签页"),o("li",null,[r("填写支付配置信息： "),o("ul",null,[o("li",null,"配置名称：便于识别的名称"),o("li",null,"API地址：支付平台的接口地址"),o("li",null,"商户信息：商户ID和密钥"),o("li",null,"回调地址：通知和返回地址")])]),o("li",null,'点击"测试配置"验证设置'),o("li",null,'测试通过后点击"保存配置"')],-1)),i(Ql,{type:"warning",closable:!1},{default:u(()=>l[64]||(l[64]=[r(" ⚠️ 注意：商户密钥等敏感信息请妥善保管，不要泄露 ",-1)])),_:1,__:[64]})])]),_:1}),i(ea,{title:"如何为分站续费？",name:"renew-substation"},{default:u(()=>[o("div",Ze,[l[67]||(l[67]=o("ol",null,[o("li",null,"在分站列表中找到需要续费的分站"),o("li",null,'点击"续费"按钮'),o("li",null,"查看当前到期时间"),o("li",null,"选择续费时长（1-24个月）"),o("li",null,"确认续费后的到期时间"),o("li",null,'点击"确定"完成续费')],-1)),i(Ql,{type:"success",closable:!1},{default:u(()=>l[66]||(l[66]=[r(" ✅ 说明：续费后分站状态会自动更新，有效期延长 ",-1)])),_:1,__:[66]})])]),_:1}),i(ea,{title:"如何管理分站状态？",name:"manage-status"},{default:u(()=>[o("div",Qe,[l[69]||(l[69]=o("ol",null,[o("li",null,"在分站列表中查看各分站的状态"),o("li",null,"正常状态：分站正常运行，所有功能可用"),o("li",null,"禁用状态：分站被暂停，用户无法访问"),o("li",null,"过期状态：分站已过期，需要续费才能恢复"),o("li",null,"可以通过编辑功能修改分站状态")],-1)),i(Ql,{type:"info",closable:!1},{default:u(()=>l[68]||(l[68]=[r(" 💡 建议：定期检查分站状态，及时处理过期和异常情况 ",-1)])),_:1,__:[68]})])]),_:1})]),_:1},8,["modelValue"])]),l[78]||(l[78]=o("div",{class:"help-section"},[o("h3",null,"💡 最佳实践建议"),o("div",{class:"best-practices"},[o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"🎯"),o("div",{class:"practice-content"},[o("h4",null,"合理规划分站"),o("p",null,"根据业务需求和地域特点合理规划分站数量和分布，避免过度分散或集中")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"👥"),o("div",{class:"practice-content"},[o("h4",null,"选择合适管理员"),o("p",null,"为每个分站选择有经验、负责任的管理员，确保分站正常运营")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"💰"),o("div",{class:"practice-content"},[o("h4",null,"合理设置抽成"),o("p",null,"根据分站规模、运营成本和市场情况合理设置抽成比例，平衡各方利益")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"🔒"),o("div",{class:"practice-content"},[o("h4",null,"加强安全管理"),o("p",null,"定期检查分站安全状况，及时更新配置，防范安全风险")])])])],-1)),o("div",We,[l[75]||(l[75]=o("h3",null,"❓ 常见问题",-1)),i(la,{modelValue:rl.value,"onUpdate:modelValue":l[23]||(l[23]=e=>rl.value=e)},{default:u(()=>[i(ea,{title:"分站域名如何解析？",name:"faq1"},{default:u(()=>l[71]||(l[71]=[o("p",null,"分站创建后，需要在DNS服务商处添加CNAME记录，将分站域名指向主站域名。具体操作请联系技术支持。",-1)])),_:1,__:[71]}),i(ea,{title:"抽成比例可以随时修改吗？",name:"faq2"},{default:u(()=>l[72]||(l[72]=[o("p",null,"可以的。管理员可以随时编辑分站信息修改抽成比例，修改后立即生效，影响后续的收入分成计算。",-1)])),_:1,__:[72]}),i(ea,{title:"分站过期后数据会丢失吗？",name:"faq3"},{default:u(()=>l[73]||(l[73]=[o("p",null,"分站过期后数据不会立即删除，但用户无法访问。建议在过期前及时续费，或联系管理员备份重要数据。",-1)])),_:1,__:[73]}),i(ea,{title:"如何批量管理多个分站？",name:"faq4"},{default:u(()=>l[74]||(l[74]=[o("p",null,"可以使用表格上方的批量操作功能，选中多个分站后进行批量删除等操作。更多批量功能正在开发中。",-1)])),_:1,__:[74]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-9ec4e6f5"]]);export{Xe as default};
