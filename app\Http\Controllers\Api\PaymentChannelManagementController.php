<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentChannel;
use App\Services\PaymentChannelManagementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 支付通道管理控制器（系统管理员专用）
 * 处理支付通道的启用/禁用和权限授权
 */
class PaymentChannelManagementController extends Controller
{
    protected PaymentChannelManagementService $channelManagementService;

    public function __construct(PaymentChannelManagementService $channelManagementService)
    {
        $this->channelManagementService = $channelManagementService;
    }

    /**
     * 获取所有支付通道状态（仅系统管理员）
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            if (!$this->isSystemAdmin()) {
                return $this->error('只有系统管理员可以管理支付通道', 403);
            }

            $channels = PaymentChannel::with(['paymentConfigs' => function ($query) {
                $query->select('channel_code', 'owner_type', 'owner_id', 'status', 'test_status');
            }])->get();

            $result = $channels->map(function ($channel) {
                return [
                    'id' => $channel->id,
                    'channel_code' => $channel->channel_code,
                    'channel_name' => $channel->channel_name,
                    'channel_icon' => $channel->channel_icon,
                    'description' => $channel->description,
                    'status' => $channel->status,
                    'fee_rate' => $channel->fee_rate,
                    'min_amount' => $channel->min_amount,
                    'max_amount' => $channel->max_amount,
                    'sort_order' => $channel->sort_order,
                    'config_count' => $channel->paymentConfigs->count(),
                    'active_config_count' => $channel->paymentConfigs->where('status', true)->count(),
                    'created_at' => $channel->created_at->toDateTimeString(),
                    'updated_at' => $channel->updated_at->toDateTimeString(),
                ];
            });

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error('获取支付通道列表失败', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('获取支付通道列表失败：' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用支付通道
     */
    public function toggleStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'channel_code' => 'required|string|exists:payment_channels,channel_code',
                'status' => 'required|boolean',
                'reason' => 'string|max:500',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $result = $this->channelManagementService->toggleChannelStatus(
                $request->input('channel_code'),
                $request->input('status'),
                auth()->id()
            );

            if ($result['success']) {
                return $this->success($result, $result['message']);
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            Log::error('切换支付通道状态失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('操作失败：' . $e->getMessage());
        }
    }

    /**
     * 批量授权支付通道权限
     */
    public function grantPermissions(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'permissions' => 'required|array|min:1',
                'permissions.*.user_type' => 'required|string|in:user,distributor,substation',
                'permissions.*.user_id' => 'required|integer|min:1',
                'permissions.*.channel_code' => 'required|string|exists:payment_channels,channel_code',
                'permissions.*.permission_type' => 'required|string|in:use,config,manage',
                'permissions.*.expires_at' => 'nullable|date|after:now',
                'permissions.*.remark' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $result = $this->channelManagementService->grantChannelPermissions(
                $request->input('permissions'),
                auth()->id()
            );

            if ($result['success']) {
                return $this->success($result, $result['message']);
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            Log::error('批量授权支付通道权限失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('授权失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付通道使用统计
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'string|in:today,week,month,year',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $period = $request->input('period', 'month');
            $stats = $this->channelManagementService->getChannelUsageStatistics(auth()->id(), $period);

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取支付通道统计失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取通道配置用户列表
     */
    public function getChannelUsers(Request $request, string $channelCode): JsonResponse
    {
        try {
            // 验证管理员权限
            if (!$this->isSystemAdmin()) {
                return $this->error('只有系统管理员可以查看通道用户', 403);
            }

            $channel = PaymentChannel::where('channel_code', $channelCode)->first();
            if (!$channel) {
                return $this->error('支付通道不存在');
            }

            // 获取配置了此通道的用户
            $configs = \App\Models\PaymentConfig::where('channel_code', $channelCode)
                ->with(['owner' => function ($query) {
                    $query->select('id', 'name', 'email', 'phone');
                }])
                ->get();

            // 获取有权限的用户
            $permissions = \App\Models\PaymentPermission::where('channel_code', $channelCode)
                ->valid()
                ->get();

            $result = [
                'channel_info' => [
                    'channel_code' => $channel->channel_code,
                    'channel_name' => $channel->channel_name,
                    'status' => $channel->status,
                ],
                'configured_users' => $configs->map(function ($config) {
                    return [
                        'config_id' => $config->id,
                        'owner_type' => $config->owner_type,
                        'owner_id' => $config->owner_id,
                        'config_name' => $config->config_name,
                        'status' => $config->status,
                        'test_status' => $config->test_status,
                        'test_time' => $config->test_time?->toDateTimeString(),
                        'created_at' => $config->created_at->toDateTimeString(),
                    ];
                }),
                'authorized_users' => $permissions->map(function ($permission) {
                    return [
                        'permission_id' => $permission->id,
                        'user_type' => $permission->user_type,
                        'user_id' => $permission->user_id,
                        'permission_type' => $permission->permission_type,
                        'permission_type_name' => $permission->permission_type_name,
                        'status' => $permission->status,
                        'granted_at' => $permission->granted_at?->toDateTimeString(),
                        'expires_at' => $permission->expires_at?->toDateTimeString(),
                        'remark' => $permission->remark,
                    ];
                }),
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error('获取通道用户列表失败', [
                'channel_code' => $channelCode,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('获取用户列表失败：' . $e->getMessage());
        }
    }

    /**
     * 更新支付通道基本信息
     */
    public function updateChannel(Request $request, int $channelId): JsonResponse
    {
        try {
            // 验证管理员权限
            if (!$this->isSystemAdmin()) {
                return $this->error('只有系统管理员可以修改通道信息', 403);
            }

            $validator = Validator::make($request->all(), [
                'channel_name' => 'string|max:100',
                'description' => 'string|max:500',
                'fee_rate' => 'numeric|min:0|max:100',
                'min_amount' => 'numeric|min:0',
                'max_amount' => 'numeric|min:0',
                'sort_order' => 'integer|min:0',
                'channel_icon' => 'string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $channel = PaymentChannel::findOrFail($channelId);
            
            $updateData = [];
            foreach (['channel_name', 'description', 'fee_rate', 'min_amount', 'max_amount', 'sort_order', 'channel_icon'] as $field) {
                if ($request->has($field)) {
                    $updateData[$field] = $request->input($field);
                }
            }

            if (!empty($updateData)) {
                $channel->update($updateData);
                
                Log::info('更新支付通道信息', [
                    'channel_id' => $channelId,
                    'channel_code' => $channel->channel_code,
                    'updated_fields' => array_keys($updateData),
                    'admin_id' => auth()->id()
                ]);
            }

            return $this->success($channel->fresh(), '通道信息更新成功');

        } catch (\Exception $e) {
            Log::error('更新支付通道信息失败', [
                'channel_id' => $channelId,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取系统支付通道概览
     */
    public function overview(): JsonResponse
    {
        try {
            // 验证管理员权限
            if (!$this->isSystemAdmin()) {
                return $this->error('只有系统管理员可以查看系统概览', 403);
            }

            $overview = [
                'total_channels' => PaymentChannel::count(),
                'active_channels' => PaymentChannel::where('status', 1)->count(),
                'inactive_channels' => PaymentChannel::where('status', 0)->count(),
                'total_configs' => \App\Models\PaymentConfig::count(),
                'active_configs' => \App\Models\PaymentConfig::where('status', true)->count(),
                'tested_configs' => \App\Models\PaymentConfig::where('test_status', true)->count(),
                'total_permissions' => \App\Models\PaymentPermission::count(),
                'active_permissions' => \App\Models\PaymentPermission::valid()->count(),
                'expiring_permissions' => \App\Models\PaymentPermission::expiringSoon()->count(),
            ];

            // 获取各通道的配置统计
            $channelStats = PaymentChannel::withCount([
                'paymentConfigs',
                'paymentConfigs as active_configs_count' => function ($query) {
                    $query->where('status', true);
                },
                'paymentConfigs as tested_configs_count' => function ($query) {
                    $query->where('test_status', true);
                }
            ])->get()->map(function ($channel) {
                return [
                    'channel_code' => $channel->channel_code,
                    'channel_name' => $channel->channel_name,
                    'status' => $channel->status,
                    'total_configs' => $channel->payment_configs_count,
                    'active_configs' => $channel->active_configs_count,
                    'tested_configs' => $channel->tested_configs_count,
                ];
            });

            $overview['channel_stats'] = $channelStats;

            return $this->success($overview);

        } catch (\Exception $e) {
            Log::error('获取支付系统概览失败', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id()
            ]);

            return $this->error('获取系统概览失败：' . $e->getMessage());
        }
    }

    /**
     * 检查是否为系统管理员
     */
    private function isSystemAdmin(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole('admin');
    }
}