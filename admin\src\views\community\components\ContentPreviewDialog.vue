<template>
  <el-dialog
    v-model="dialogVisible"
    title="内容预览"
    width="800px"
    :destroy-on-close="true"
    class="content-preview-dialog"
  >
    <div class="preview-container">
      <!-- 预览模式切换 -->
      <div class="preview-header">
        <el-radio-group v-model="previewMode" size="small">
          <el-radio-button label="desktop">桌面端</el-radio-button>
          <el-radio-button label="mobile">移动端</el-radio-button>
        </el-radio-group>
        <div class="preview-actions">
          <el-button size="small" @click="refreshPreview">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" @click="exportPreview">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      
      <!-- 预览内容 -->
      <div class="preview-content" :class="{ 'mobile-view': previewMode === 'mobile' }">
        <div class="group-preview">
          <!-- 群组基本信息 -->
          <div class="group-header">
            <div class="group-avatar">
              <el-avatar :src="groupData.avatar" :size="60">
                <el-icon><Comment /></el-icon>
              </el-avatar>
            </div>
            <div class="group-info">
              <h3 class="group-name">{{ groupData.name || '群组名称' }}</h3>
              <div class="group-meta">
                <span class="group-price">
                  {{ groupData.price > 0 ? `¥${groupData.price}` : '免费' }}
                </span>
                <span class="group-members">
                  {{ groupData.member_count || 0 }}/{{ groupData.max_members || 500 }}人
                </span>
              </div>
            </div>
          </div>
          
          <!-- 群组介绍 -->
          <div v-if="contentData.show_intro && contentData.group_intro_content" class="content-section">
            <h4 class="section-title">{{ contentData.group_intro_title || '群简介' }}</h4>
            <div class="section-content" v-html="contentData.group_intro_content"></div>
          </div>
          
          <!-- FAQ区块 -->
          <div v-if="contentData.show_faq && faqList.length > 0" class="content-section">
            <h4 class="section-title">{{ contentData.faq_title || '常见问题' }}</h4>
            <div class="faq-content">
              <!-- 折叠面板样式 -->
              <el-collapse v-if="contentData.faq_style === 'collapse'" accordion>
                <el-collapse-item 
                  v-for="(item, index) in faqList" 
                  :key="index"
                  :title="item.question"
                  :name="index"
                >
                  <div class="faq-answer">{{ item.answer }}</div>
                </el-collapse-item>
              </el-collapse>
              
              <!-- 列表样式 -->
              <div v-else-if="contentData.faq_style === 'list'" class="faq-list">
                <div v-for="(item, index) in faqList" :key="index" class="faq-item">
                  <div class="faq-question">
                    <span class="faq-index">Q{{ index + 1 }}</span>
                    {{ item.question }}
                  </div>
                  <div class="faq-answer">
                    <span class="answer-label">A:</span>
                    {{ item.answer }}
                  </div>
                </div>
              </div>
              
              <!-- 卡片样式 -->
              <div v-else class="faq-cards">
                <el-card v-for="(item, index) in faqList" :key="index" class="faq-card" shadow="hover">
                  <template #header>
                    <div class="faq-question">{{ item.question }}</div>
                  </template>
                  <div class="faq-answer">{{ item.answer }}</div>
                </el-card>
              </div>
            </div>
          </div>
          
          <!-- 用户评论 -->
          <div v-if="contentData.show_reviews && reviewsList.length > 0" class="content-section">
            <h4 class="section-title">{{ contentData.reviews_title || '用户评价' }}</h4>
            <div class="reviews-content">
              <!-- 卡片样式 -->
              <div v-if="contentData.reviews_style === 'card'" class="reviews-cards">
                <div 
                  v-for="(review, index) in displayReviews" 
                  :key="index" 
                  class="review-card"
                >
                  <div class="review-header">
                    <el-avatar :src="review.avatar" :size="32">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div class="review-user">
                      <div class="username">{{ review.username }}</div>
                      <el-rate v-model="review.rating" disabled size="small" />
                    </div>
                    <div class="review-time">{{ formatTime(review.created_at) }}</div>
                  </div>
                  <div class="review-content">{{ review.content }}</div>
                </div>
              </div>
              
              <!-- 列表样式 -->
              <div v-else-if="contentData.reviews_style === 'list'" class="reviews-list">
                <div 
                  v-for="(review, index) in displayReviews" 
                  :key="index" 
                  class="review-item"
                >
                  <div class="review-left">
                    <el-avatar :src="review.avatar" :size="40">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                  </div>
                  <div class="review-right">
                    <div class="review-meta">
                      <span class="username">{{ review.username }}</span>
                      <el-rate v-model="review.rating" disabled size="small" />
                      <span class="review-time">{{ formatTime(review.created_at) }}</span>
                    </div>
                    <div class="review-content">{{ review.content }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 轮播样式 -->
              <div v-else class="reviews-carousel">
                <el-carousel height="120px" :autoplay="true" :interval="3000">
                  <el-carousel-item v-for="(review, index) in displayReviews" :key="index">
                    <div class="carousel-review">
                      <div class="review-content">"{{ review.content }}"</div>
                      <div class="review-author">
                        <span class="username">—— {{ review.username }}</span>
                        <el-rate v-model="review.rating" disabled size="small" />
                      </div>
                    </div>
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </div>
          
          <!-- 扩展内容区块 -->
          <div v-if="contentData.show_extra" class="content-section">
            <div v-if="contentData.extra_title1 && contentData.extra_content1" class="extra-block">
              <h4 class="section-title">{{ contentData.extra_title1 }}</h4>
              <div class="section-content" v-html="contentData.extra_content1"></div>
            </div>
            
            <div v-if="contentData.extra_title2 && contentData.extra_content2" class="extra-block">
              <h4 class="section-title">{{ contentData.extra_title2 }}</h4>
              <div class="section-content" v-html="contentData.extra_content2"></div>
            </div>
          </div>
          
          <!-- 素材展示 -->
          <div v-if="contentData.customer_service_qr || contentData.ad_image?.length > 0" class="content-section">
            <h4 class="section-title">相关素材</h4>
            <div class="materials-content">
              <div v-if="contentData.customer_service_qr" class="material-item">
                <div class="material-label">客服二维码</div>
                <img :src="contentData.customer_service_qr" alt="客服二维码" class="qr-code" />
              </div>
              
              <div v-if="contentData.ad_image?.length > 0" class="material-item">
                <div class="material-label">广告图片</div>
                <div class="ad-images">
                  <img 
                    v-for="(img, index) in contentData.ad_image" 
                    :key="index"
                    :src="img" 
                    alt="广告图片" 
                    class="ad-image"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 加入群组按钮 -->
          <div class="join-section">
            <el-button type="primary" size="large" class="join-button">
              <el-icon><Plus /></el-icon>
              立即加入群组
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleApplyContent">应用内容</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Download,
  Comment,
  User,
  Plus
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  contentData: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'apply-content'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const previewMode = ref('desktop')

// 计算属性
const faqList = computed(() => {
  if (!props.contentData.faq_content) return []
  
  const items = props.contentData.faq_content.split('\n').filter(line => line.includes('----'))
  return items.map(item => {
    const [question, answer] = item.split('----')
    return { question: question?.trim() || '', answer: answer?.trim() || '' }
  })
})

const reviewsList = computed(() => {
  if (!props.contentData.user_reviews) return []
  
  const items = props.contentData.user_reviews.split('\n').filter(line => line.includes('----'))
  return items.map(item => {
    const parts = item.split('----')
    return {
      username: parts[0]?.trim() || '',
      content: parts[1]?.trim() || '',
      rating: parseInt(parts[2]) || 5,
      avatar: parts[3]?.trim() || '',
      created_at: parts[4]?.trim() || new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
  })
})

const displayReviews = computed(() => {
  const count = props.contentData.reviews_count || 6
  return reviewsList.value.slice(0, count)
})

// 方法
const refreshPreview = () => {
  ElMessage.success('预览已刷新')
}

const exportPreview = () => {
  // 导出预览内容为HTML或PDF
  ElMessage.info('导出功能开发中')
}

const handleApplyContent = () => {
  emit('apply-content', props.contentData)
  dialogVisible.value = false
}

const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.content-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.preview-container {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    
    .preview-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .preview-content {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
    
    &.mobile-view {
      max-width: 375px;
      margin: 0 auto;
      background: #f5f5f5;
      border-radius: 20px;
      padding: 20px 16px;
    }
  }
}

.group-preview {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  
  .group-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .group-info {
      flex: 1;
      
      .group-name {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
      }
      
      .group-meta {
        display: flex;
        gap: 16px;
        font-size: 14px;
        opacity: 0.9;
        
        .group-price {
          font-weight: 600;
          color: #fbbf24;
        }
      }
    }
  }
  
  .content-section {
    padding: 24px 20px;
    border-bottom: 1px solid #f1f5f9;
    
    &:last-child {
      border-bottom: none;
    }
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border-radius: 2px;
        margin-right: 8px;
        vertical-align: middle;
      }
    }
    
    .section-content {
      color: #475569;
      line-height: 1.6;
      
      :deep(p) {
        margin: 0 0 12px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 8px 0;
      }
    }
  }
  
  // FAQ样式
  .faq-content {
    .faq-list {
      .faq-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #f8fafc;
        border-radius: 8px;
        border-left: 4px solid #3b82f6;
        
        .faq-question {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 8px;
          
          .faq-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 600;
          }
        }
        
        .faq-answer {
          color: #64748b;
          line-height: 1.6;
          
          .answer-label {
            font-weight: 600;
            color: #f59e0b;
            margin-right: 4px;
          }
        }
      }
    }
    
    .faq-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
      
      .faq-card {
        .faq-question {
          font-weight: 600;
          color: #1e293b;
        }
        
        .faq-answer {
          color: #64748b;
          line-height: 1.6;
        }
      }
    }
  }
  
  // 评论样式
  .reviews-content {
    .reviews-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      
      .review-card {
        padding: 16px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        
        .review-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .review-user {
            flex: 1;
            
            .username {
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 4px;
            }
          }
          
          .review-time {
            font-size: 12px;
            color: #94a3b8;
          }
        }
        
        .review-content {
          color: #475569;
          line-height: 1.6;
        }
      }
    }
    
    .reviews-list {
      .review-item {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        padding: 16px;
        background: #f8fafc;
        border-radius: 8px;
        
        .review-right {
          flex: 1;
          
          .review-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
            
            .username {
              font-weight: 600;
              color: #1e293b;
            }
            
            .review-time {
              font-size: 12px;
              color: #94a3b8;
            }
          }
          
          .review-content {
            color: #475569;
            line-height: 1.6;
          }
        }
      }
    }
    
    .reviews-carousel {
      .carousel-review {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        padding: 20px;
        text-align: center;
        
        .review-content {
          font-size: 16px;
          color: #1e293b;
          line-height: 1.6;
          margin-bottom: 12px;
          font-style: italic;
        }
        
        .review-author {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .username {
            color: #64748b;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  // 素材样式
  .materials-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    
    .material-item {
      text-align: center;
      
      .material-label {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 12px;
      }
      
      .qr-code {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }
      
      .ad-images {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
        
        .ad-image {
          width: 80px;
          height: 80px;
          object-fit: cover;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }
      }
    }
  }
  
  .join-section {
    padding: 24px 20px;
    text-align: center;
    background: #f8fafc;
    
    .join-button {
      width: 200px;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 移动端适配
.mobile-view {
  .group-preview {
    .group-header {
      padding: 16px;
      
      .group-info {
        .group-name {
          font-size: 18px;
        }
        
        .group-meta {
          font-size: 13px;
        }
      }
    }
    
    .content-section {
      padding: 20px 16px;
      
      .section-title {
        font-size: 16px;
      }
    }
    
    .reviews-content {
      .reviews-cards {
        grid-template-columns: 1fr;
      }
    }
    
    .materials-content {
      grid-template-columns: 1fr;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>