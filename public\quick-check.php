<?php
// 快速系统诊断脚本
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>系统快速诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔍 系统快速诊断</h1>
    
    <div class="section">
        <h2>PHP 基础信息</h2>
        <?php
        echo "PHP 版本: " . PHP_VERSION . "<br>";
        echo "运行模式: " . php_sapi_name() . "<br>";
        ?>
    </div>
    
    <div class="section">
        <h2>关键扩展检查</h2>
        <?php
        $extensions = ['pdo', 'pdo_mysql', 'mysqli', 'mbstring', 'openssl', 'json'];
        foreach ($extensions as $ext) {
            $loaded = extension_loaded($ext);
            $class = $loaded ? 'success' : 'error';
            $status = $loaded ? '✅' : '❌';
            echo "<span class='{$class}'>{$status} {$ext}</span><br>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>数据库连接测试</h2>
        <?php
        try {
            $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=ffjq', 'root', '123456');
            echo "<span class='success'>✅ 数据库连接成功</span><br>";
        } catch (Exception $e) {
            echo "<span class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</span><br>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>文件权限检查</h2>
        <?php
        $dirs = ['storage', 'bootstrap/cache'];
        foreach ($dirs as $dir) {
            $path = dirname(__DIR__) . '/' . $dir;
            $writable = is_writable($path);
            $class = $writable ? 'success' : 'error';
            $status = $writable ? '✅' : '❌';
            echo "<span class='{$class}'>{$status} {$dir}</span><br>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>Laravel 配置检查</h2>
        <?php
        // 检查配置缓存
        $config_cache = dirname(__DIR__) . '/bootstrap/cache/config.php';
        if (file_exists($config_cache)) {
            echo "<span class='warning'>⚠️ 配置缓存存在，可能导致问题</span><br>";
            echo "<a href='?action=clear_cache'>点击清理配置缓存</a><br>";
        } else {
            echo "<span class='success'>✅ 无配置缓存</span><br>";
        }
        
        // 检查 .env 文件
        $env_file = dirname(__DIR__) . '/.env';
        if (file_exists($env_file)) {
            echo "<span class='success'>✅ .env 文件存在</span><br>";
        } else {
            echo "<span class='error'>❌ .env 文件不存在</span><br>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>快速修复</h2>
        <?php
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'clear_cache':
                    $cache_files = [
                        dirname(__DIR__) . '/bootstrap/cache/config.php',
                        dirname(__DIR__) . '/bootstrap/cache/routes.php',
                        dirname(__DIR__) . '/bootstrap/cache/services.php'
                    ];
                    
                    foreach ($cache_files as $file) {
                        if (file_exists($file)) {
                            if (unlink($file)) {
                                echo "<span class='success'>✅ 已删除: " . basename($file) . "</span><br>";
                            } else {
                                echo "<span class='error'>❌ 删除失败: " . basename($file) . "</span><br>";
                            }
                        }
                    }
                    echo "<a href='?'>返回检查页面</a><br>";
                    break;
            }
        } else {
            echo "<a href='?action=clear_cache'>清理 Laravel 缓存</a><br>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>建议的解决步骤</h2>
        <ol>
            <li>确保 MySQL 服务正在运行</li>
            <li>检查 PHP 是否安装了 MySQL 扩展</li>
            <li>清理 Laravel 配置缓存</li>
            <li>检查文件权限</li>
            <li>重新生成应用密钥</li>
        </ol>
    </div>
</body>
</html>
