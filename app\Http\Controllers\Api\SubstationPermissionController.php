<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 分站权限管理控制器
 */
class SubstationPermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('substation.permission:user_management');
    }

    /**
     * 获取分站权限配置
     */
    public function getConfig(Request $request)
    {
        $substation = $request->attributes->get('substation');
        
        $permissions = $substation->getSetting('detailed_permissions', $this->getDefaultPermissions());
        $featureLimits = $substation->getSetting('feature_limits', $this->getDefaultFeatureLimits());
        $customSettings = $substation->getSetting('custom_settings', []);

        return response()->json([
            'success' => true,
            'data' => [
                'permissions' => $permissions,
                'feature_limits' => $featureLimits,
                'custom_settings' => $customSettings,
            ],
        ]);
    }

    /**
     * 更新分站权限配置
     */
    public function updateConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'feature_limits' => 'nullable|array',
            'custom_settings' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substation = $request->attributes->get('substation');

        try {
            // 验证权限配置的有效性
            $this->validatePermissionConfig($request->permissions);

            $substation->updateSettings([
                'detailed_permissions' => $request->permissions,
                'feature_limits' => $request->feature_limits ?? [],
                'custom_settings' => $request->custom_settings ?? [],
            ]);

            return response()->json([
                'success' => true,
                'message' => '权限配置更新成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '权限配置更新失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取可用权限列表
     */
    public function getAvailablePermissions()
    {
        $permissions = [
            'user' => [
                'label' => '用户管理',
                'permissions' => [
                    'view' => '查看用户',
                    'create' => '创建用户',
                    'edit' => '编辑用户',
                    'delete' => '删除用户',
                    'export' => '导出用户数据',
                    'batch_operation' => '批量操作',
                    'reset_password' => '重置密码',
                    'adjust_balance' => '调整余额',
                ]
            ],
            'agent' => [
                'label' => '代理商管理',
                'permissions' => [
                    'view' => '查看代理商',
                    'create' => '创建代理商',
                    'edit' => '编辑代理商',
                    'delete' => '删除代理商',
                    'approve' => '审核代理商',
                    'commission_adjust' => '调整佣金',
                    'level_manage' => '等级管理',
                    'renew' => '续费管理',
                ]
            ],
            'group' => [
                'label' => '群组管理',
                'permissions' => [
                    'view' => '查看群组',
                    'create' => '创建群组',
                    'edit' => '编辑群组',
                    'delete' => '删除群组',
                    'audit' => '审核群组',
                    'template_manage' => '模板管理',
                    'batch_operation' => '批量操作',
                ]
            ],
            'order' => [
                'label' => '订单管理',
                'permissions' => [
                    'view' => '查看订单',
                    'edit' => '编辑订单',
                    'refund' => '订单退款',
                    'export' => '导出订单',
                    'statistics' => '订单统计',
                ]
            ],
            'finance' => [
                'label' => '财务管理',
                'permissions' => [
                    'view' => '查看财务',
                    'settle' => '结算佣金',
                    'withdraw_approve' => '提现审批',
                    'report' => '财务报表',
                    'export' => '导出财务数据',
                ]
            ],
            'system' => [
                'label' => '系统管理',
                'permissions' => [
                    'settings' => '系统设置',
                    'monitor' => '系统监控',
                    'logs' => '日志查看',
                    'backup' => '数据备份',
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $permissions,
        ]);
    }

    /**
     * 获取默认权限配置
     */
    private function getDefaultPermissions()
    {
        return [
            'user' => [
                'view' => true,
                'create' => true,
                'edit' => true,
                'delete' => false,
                'export' => true,
                'batch_operation' => true,
                'reset_password' => true,
                'adjust_balance' => false,
            ],
            'agent' => [
                'view' => true,
                'create' => true,
                'edit' => true,
                'delete' => false,
                'approve' => true,
                'commission_adjust' => false,
                'level_manage' => false,
                'renew' => true,
            ],
            'group' => [
                'view' => true,
                'create' => true,
                'edit' => true,
                'delete' => true,
                'audit' => true,
                'template_manage' => true,
                'batch_operation' => true,
            ],
            'order' => [
                'view' => true,
                'edit' => true,
                'refund' => true,
                'export' => true,
                'statistics' => true,
            ],
            'finance' => [
                'view' => true,
                'settle' => false,
                'withdraw_approve' => false,
                'report' => true,
                'export' => false,
            ],
            'system' => [
                'settings' => false,
                'monitor' => true,
                'logs' => true,
                'backup' => false,
            ]
        ];
    }

    /**
     * 获取默认功能限制
     */
    private function getDefaultFeatureLimits()
    {
        return [
            'max_users' => 1000,
            'max_agents' => 100,
            'max_groups' => 500,
            'max_daily_orders' => 1000,
            'max_monthly_revenue' => 100000,
            'storage_limit' => 1024, // MB
            'api_rate_limit' => 1000, // 每小时
        ];
    }

    /**
     * 验证权限配置的有效性
     */
    private function validatePermissionConfig($permissions)
    {
        $availableModules = ['user', 'agent', 'group', 'order', 'finance', 'system'];
        
        foreach ($permissions as $module => $modulePermissions) {
            if (!in_array($module, $availableModules)) {
                throw new \InvalidArgumentException("无效的权限模块: {$module}");
            }
            
            if (!is_array($modulePermissions)) {
                throw new \InvalidArgumentException("权限配置格式错误");
            }
            
            foreach ($modulePermissions as $permission => $value) {
                if (!is_bool($value)) {
                    throw new \InvalidArgumentException("权限值必须为布尔类型");
                }
            }
        }
    }
}