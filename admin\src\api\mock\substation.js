import { faker } from '@faker-js/faker'

const allSubstations = Array.from({ length: 15 }, (_, i) => ({
  id: i + 1,
  name: `${faker.company.name()}分站`,
  domain: faker.internet.domainName(),
  owner_name: faker.internet.userName(),
  owner_id: faker.number.int({ min: 100, max: 200 }),
  status: faker.helpers.arrayElement(['active', 'inactive', 'expired']),
  created_at: faker.date.past().toISOString(),
  expired_at: faker.date.future().toISOString(),
  package_name: `套餐${faker.helpers.arrayElement(['A', 'B', 'C'])}`,
}))

export const mockSubstationAPI = {
  getSubstationList(params) {
    const { page = 1, limit = 10, keyword, status } = params
    let filteredData = allSubstations.filter(item => {
      let match = true
      if (keyword && !item.name.includes(keyword) && !item.domain.includes(keyword)) {
        match = false
      }
      if (status && item.status !== status) {
        match = false
      }
      return match
    })
    const total = filteredData.length
    const start = (page - 1) * limit
    const end = page * limit
    const paginatedData = filteredData.slice(start, end)
    return Promise.resolve({
      code: 0,
      data: { list: paginatedData, total },
      message: '成功'
    })
  },

  createSubstation(data) {
    const newSubstation = {
      ...data,
      id: allSubstations.length + 1,
      created_at: new Date().toISOString(),
      expired_at: faker.date.future().toISOString(),
      status: 'active'
    }
    allSubstations.unshift(newSubstation)
    return Promise.resolve({
      code: 0,
      data: newSubstation,
      message: '创建成功'
    })
  },

  updateSubstation(id, data) {
    const index = allSubstations.findIndex(item => item.id === id)
    if (index !== -1) {
      allSubstations[index] = { ...allSubstations[index], ...data }
      return Promise.resolve({
        code: 0,
        data: allSubstations[index],
        message: '更新成功'
      })
    }
    return Promise.reject(new Error('分站未找到'))
  },

  deleteSubstation(id) {
    const index = allSubstations.findIndex(item => item.id === id)
    if (index !== -1) {
      allSubstations.splice(index, 1)
      return Promise.resolve({
        code: 0,
        message: '删除成功'
      })
    }
    return Promise.reject(new Error('分站未找到'))
  },

  renewSubstation(id, data) {
    const index = allSubstations.findIndex(item => item.id === id)
    if (index !== -1) {
      allSubstations[index].expired_at = faker.date.future().toISOString()
      return Promise.resolve({
        code: 0,
        data: allSubstations[index],
        message: '续费成功'
      })
    }
    return Promise.reject(new Error('分站未找到'))
  }
}