<!--
  错误边界组件
  用于捕获和处理组件错误，提供优雅的错误处理和恢复机制
  
  <AUTHOR>
  @version 1.0.0
-->

<template>
  <div class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误状态显示 -->
    <div v-else class="error-fallback" :class="errorSeverityClass">
      <!-- 错误图标和标题 -->
      <div class="error-header">
        <el-icon :size="48" :color="errorIconColor">
          <component :is="errorIcon" />
        </el-icon>
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
      </div>
      
      <!-- 错误详情（开发环境） -->
      <div v-if="showErrorDetails" class="error-details">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="错误详情" name="details">
            <div class="error-stack">
              <pre>{{ errorInfo.stack }}</pre>
            </div>
          </el-collapse-item>
          <el-collapse-item title="组件信息" name="component">
            <div class="component-info">
              <p><strong>组件名称:</strong> {{ errorInfo.componentName }}</p>
              <p><strong>错误时间:</strong> {{ formatDateTime(errorInfo.timestamp) }}</p>
              <p><strong>用户代理:</strong> {{ errorInfo.userAgent }}</p>
              <p><strong>页面URL:</strong> {{ errorInfo.url }}</p>
            </div>
          </el-collapse-item>
          <el-collapse-item title="错误上下文" name="context">
            <div class="error-context">
              <pre>{{ JSON.stringify(errorInfo.context, null, 2) }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button 
          type="primary" 
          @click="handleRetry"
          :loading="retrying"
          :disabled="!canRetry"
        >
          <el-icon><Refresh /></el-icon>
          重试 {{ retryCount > 0 ? `(${maxRetries - retryCount})` : '' }}
        </el-button>
        
        <el-button @click="handleReload">
          <el-icon><RefreshRight /></el-icon>
          刷新页面
        </el-button>
        
        <el-button @click="handleGoBack" v-if="canGoBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
        
        <el-button 
          @click="handleReport" 
          :loading="reporting"
          v-if="canReport"
        >
          <el-icon><Warning /></el-icon>
          报告问题
        </el-button>
      </div>
      
      <!-- 建议操作 -->
      <div class="error-suggestions" v-if="suggestions.length > 0">
        <h4>建议操作：</h4>
        <ul>
          <li v-for="suggestion in suggestions" :key="suggestion">
            {{ suggestion }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured, onBeforeUnmount, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Refresh, RefreshRight, ArrowLeft, Warning,
  CircleCloseFilled, WarningFilled, InfoFilled
} from '@element-plus/icons-vue'
import { useComponentCleanup, useSafeTimer } from '@/utils/componentCleanup'

// Props
const props = defineProps({
  // 错误级别：error, warning, info
  level: {
    type: String,
    default: 'error',
    validator: (value) => ['error', 'warning', 'info'].includes(value)
  },
  
  // 是否显示错误详情
  showDetails: {
    type: Boolean,
    default: process.env.NODE_ENV === 'development'
  },
  
  // 最大重试次数
  maxRetries: {
    type: Number,
    default: 3
  },
  
  // 是否自动重试
  autoRetry: {
    type: Boolean,
    default: false
  },
  
  // 自动重试延迟（毫秒）
  retryDelay: {
    type: Number,
    default: 2000
  },
  
  // 是否可以报告错误
  enableReporting: {
    type: Boolean,
    default: true
  },
  
  // 自定义错误消息
  customMessage: {
    type: String,
    default: ''
  },
  
  // 错误回调
  onError: {
    type: Function,
    default: null
  },
  
  // 重试回调
  onRetry: {
    type: Function,
    default: null
  }
})

// Emits
const emit = defineEmits(['error', 'retry', 'recover'])

// 响应式数据
const hasError = ref(false)
const errorInfo = ref({})
const retryCount = ref(0)
const retrying = ref(false)
const reporting = ref(false)
const activeCollapse = ref([])
const autoRetryTimer = ref(null)

// 路由
const router = useRouter()

// 组件清理管理器
const componentCleanup = useComponentCleanup()
const safeTimer = useSafeTimer()

// 计算属性
const showErrorDetails = computed(() => {
  return props.showDetails && process.env.NODE_ENV === 'development'
})

const canRetry = computed(() => {
  return retryCount.value < props.maxRetries && !retrying.value
})

const canGoBack = computed(() => {
  return window.history.length > 1
})

const canReport = computed(() => {
  return props.enableReporting && process.env.NODE_ENV === 'production'
})

const errorSeverityClass = computed(() => {
  return `error-${props.level}`
})

const errorIcon = computed(() => {
  const icons = {
    error: 'CircleCloseFilled',
    warning: 'WarningFilled',
    info: 'InfoFilled'
  }
  return icons[props.level] || 'CircleCloseFilled'
})

const errorIconColor = computed(() => {
  const colors = {
    error: '#F56C6C',
    warning: '#E6A23C',
    info: '#409EFF'
  }
  return colors[props.level] || '#F56C6C'
})

const errorTitle = computed(() => {
  if (props.customMessage) {
    return props.customMessage
  }
  
  const titles = {
    error: '页面出现错误',
    warning: '页面运行异常',
    info: '页面提示信息'
  }
  return titles[props.level] || '页面出现错误'
})

const errorMessage = computed(() => {
  if (errorInfo.value.message) {
    return errorInfo.value.message
  }
  
  const messages = {
    error: '抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。',
    warning: '页面运行中遇到了一些异常，但仍可继续使用。',
    info: '这是一个提示信息，不影响正常使用。'
  }
  return messages[props.level] || messages.error
})

const suggestions = computed(() => {
  const baseSuggestions = []
  
  if (errorInfo.value.type === 'ChunkLoadError') {
    baseSuggestions.push('清除浏览器缓存后重试')
    baseSuggestions.push('检查网络连接是否正常')
  } else if (errorInfo.value.type === 'TypeError') {
    baseSuggestions.push('刷新页面重新加载资源')
    baseSuggestions.push('检查浏览器版本是否过旧')
  } else if (errorInfo.value.type === 'NetworkError') {
    baseSuggestions.push('检查网络连接')
    baseSuggestions.push('稍后再试')
  } else {
    baseSuggestions.push('刷新页面')
    baseSuggestions.push('清除浏览器缓存')
    baseSuggestions.push('联系技术支持')
  }
  
  return baseSuggestions
})

// 错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary captured error:', error)
  
  // 收集错误信息
  const errorData = {
    message: error.message || '未知错误',
    stack: error.stack || '',
    type: error.constructor.name || 'Error',
    componentName: instance?.$options.name || instance?.$options.__name || 'Unknown',
    timestamp: new Date(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    context: {
      route: router.currentRoute.value,
      props: instance?.props || {},
      info: info
    }
  }
  
  // 设置错误状态
  hasError.value = true
  errorInfo.value = errorData
  
  // 触发错误事件
  emit('error', errorData)
  
  // 调用错误回调
  if (props.onError) {
    props.onError(errorData)
  }
  
  // 记录错误日志
  logError(errorData)
  
  // 自动重试
  if (props.autoRetry && canRetry.value) {
    scheduleAutoRetry()
  }
  
  // 阻止错误继续传播
  return false
})

// 方法
const handleRetry = async () => {
  if (!canRetry.value) return
  
  retrying.value = true
  retryCount.value++
  
  try {
    // 触发重试事件
    emit('retry', retryCount.value)
    
    // 调用重试回调
    if (props.onRetry) {
      await props.onRetry()
    }
    
    // 等待一段时间后重置错误状态
    await new Promise(resolve => safeTimer.setTimeout(resolve, 500))
    
    // 重置错误状态
    hasError.value = false
    errorInfo.value = {}
    
    // 触发恢复事件
    emit('recover')
    
    ElMessage.success('页面已恢复正常')
    
  } catch (error) {
    console.error('Retry failed:', error)
    ElMessage.error('重试失败，请稍后再试')
  } finally {
    retrying.value = false
  }
}

const handleReload = () => {
  window.location.reload()
}

const handleGoBack = () => {
  if (canGoBack.value) {
    window.history.back()
  }
}

const handleReport = async () => {
  if (!canReport.value) return
  
  reporting.value = true
  
  try {
    // 发送错误报告
    await reportError(errorInfo.value)
    
    ElNotification.success({
      title: '报告成功',
      message: '错误报告已发送，我们会尽快处理'
    })
  } catch (error) {
    console.error('Report error failed:', error)
    ElMessage.error('发送报告失败，请稍后重试')
  } finally {
    reporting.value = false
  }
}

const scheduleAutoRetry = () => {
  if (autoRetryTimer.value) {
    clearTimeout(autoRetryTimer.value)
  }
  
  autoRetryTimer.value = safeTimer.setTimeout(() => {
    if (canRetry.value) {
      handleRetry()
    }
  }, props.retryDelay)
}

const logError = (errorData) => {
  // 开发环境：输出到控制台
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 ErrorBoundary Error Details')
    console.error('Message:', errorData.message)
    console.error('Type:', errorData.type)
    console.error('Component:', errorData.componentName)
    console.error('Stack:', errorData.stack)
    console.error('Context:', errorData.context)
    console.groupEnd()
  }
  
  // 生产环境：发送到日志服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成第三方日志服务，如 Sentry、LogRocket 等
    // sendToLogService(errorData)
  }
}

const reportError = async (errorData) => {
  // 模拟发送错误报告
  return new Promise((resolve, reject) => {
    safeTimer.setTimeout(() => {
      if (Math.random() > 0.1) { // 90% 成功率
        resolve()
      } else {
        reject(new Error('网络错误'))
      }
    }, 1000)
  })
}

const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 清理定时器
const cleanup = () => {
  if (autoRetryTimer.value) {
    clearTimeout(autoRetryTimer.value)
    autoRetryTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  // 使用安全的事件监听器
  componentCleanup.addListener(window, 'error', (event) => {
    console.error('Global error:', event.error)
  })

  componentCleanup.addListener(window, 'unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
  })
})

// 暴露方法给父组件
defineExpose({
  retry: handleRetry,
  reset: () => {
    hasError.value = false
    errorInfo.value = {}
    retryCount.value = 0
    cleanup()
  },
  getErrorInfo: () => errorInfo.value
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  background: #fafafa;
  border-radius: 8px;
  text-align: center;
}

.error-fallback.error-error {
  background: #fef0f0;
  border: 1px solid #fde2e2;
}

.error-fallback.error-warning {
  background: #fdf6ec;
  border: 1px solid #faecd8;
}

.error-fallback.error-info {
  background: #ecf5ff;
  border: 1px solid #d9ecff;
}

.error-header {
  margin-bottom: 30px;
}

.error-title {
  margin: 20px 0 10px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.error-message {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  max-width: 600px;
}

.error-details {
  width: 100%;
  max-width: 800px;
  margin: 30px 0;
  text-align: left;
}

.error-stack {
  background: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.component-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.component-info strong {
  color: #303133;
}

.error-context {
  background: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.error-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  margin: 20px 0;
}

.error-suggestions {
  margin-top: 30px;
  text-align: left;
  max-width: 500px;
}

.error-suggestions h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.error-suggestions li {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-fallback {
    padding: 20px 15px;
    min-height: 300px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 200px;
  }
}

/* 动画效果 */
.error-fallback {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .error-fallback {
    background: #1a1a1a;
    color: #e4e7ed;
  }
  
  .error-fallback.error-error {
    background: #2d1b1b;
    border-color: #4c2626;
  }
  
  .error-fallback.error-warning {
    background: #2d2419;
    border-color: #4c3d1a;
  }
  
  .error-fallback.error-info {
    background: #1a2332;
    border-color: #264a73;
  }
  
  .error-title {
    color: #e4e7ed;
  }
  
  .error-message {
    color: #c0c4cc;
  }
  
  .error-stack,
  .error-context {
    background: #262626;
    border-color: #4c4d4f;
    color: #c0c4cc;
  }
  
  .component-info p {
    color: #c0c4cc;
  }
  
  .component-info strong {
    color: #e4e7ed;
  }
  
  .error-suggestions h4 {
    color: #e4e7ed;
  }
  
  .error-suggestions li {
    color: #c0c4cc;
  }
}
</style>