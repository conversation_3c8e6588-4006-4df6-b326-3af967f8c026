/**
 * 导航组件单元测试
 * 测试 NavigationEnhancer 组件的所有功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import NavigationEnhancer from '@/components/NavigationEnhancer.vue'
import { useUserStore } from '@/stores/user'

// 模拟路由
const mockRoutes = [
  { path: '/', component: { template: '<div>Home</div>' } },
  { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
  { path: '/admin', component: { template: '<div>Admin</div>' } }
]

describe('NavigationEnhancer 组件测试', () => {
  let wrapper
  let router
  let pinia
  let userStore

  beforeEach(async () => {
    // 设置测试路由
    router = createRouter({
      history: createWebHistory(),
      routes: mockRoutes
    })

    // 设置 Pinia
    pinia = createPinia()
    setActivePinia(pinia)
    userStore = useUserStore()
    
    // 挂载组件
    wrapper = mount(NavigationEnhancer, {
      global: {
        plugins: [router, pinia],
        stubs: {
          'el-button': { template: '<button><slot /></button>' },
          'el-icon': { template: '<i><slot /></i>' }
        }
      },
      props: {
        collapsed: false
      }
    })

    await router.isReady()
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件初始化', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.navigation-enhancer').exists()).toBe(true)
    })

    it('应该在非折叠状态下显示快捷操作触发按钮', () => {
      expect(wrapper.find('.quick-actions-trigger').exists()).toBe(true)
    })

    it('应该在折叠状态下隐藏快捷操作触发按钮', async () => {
      await wrapper.setProps({ collapsed: true })
      expect(wrapper.find('.quick-actions-trigger').exists()).toBe(false)
    })
  })

  describe('快捷操作面板功能', () => {
    it('点击触发按钮应该显示快捷操作面板', async () => {
      // 初始状态下面板应该隐藏
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(false)
      
      // 点击触发按钮
      const triggerBtn = wrapper.find('.trigger-btn')
      await triggerBtn.trigger('click')
      
      // 面板应该显示
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(true)
    })

    it('点击关闭按钮应该隐藏快捷操作面板', async () => {
      // 先显示面板
      await wrapper.find('.trigger-btn').trigger('click')
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(true)
      
      // 点击关闭按钮
      const closeBtn = wrapper.find('.close-btn')
      await closeBtn.trigger('click')
      
      // 面板应该隐藏
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(false)
    })

    it('应该根据用户角色显示相应的快捷操作', async () => {
      // 设置管理员角色
      userStore.userInfo = { role: 'admin', username: 'admin' }
      await wrapper.vm.$nextTick()
      
      // 获取快捷操作
      const quickActions = wrapper.vm.currentQuickActions
      
      expect(Array.isArray(quickActions)).toBe(true)
      expect(quickActions.length).toBeGreaterThan(0)
      
      // 验证快捷操作的结构
      quickActions.forEach(action => {
        expect(action).toHaveProperty('title')
        expect(action).toHaveProperty('path')
        expect(action).toHaveProperty('icon')
        expect(action).toHaveProperty('description')
      })
    })

    it('普通用户应该显示有限的快捷操作', async () => {
      // 设置普通用户角色
      userStore.userInfo = { role: 'user', username: 'user1' }
      await wrapper.vm.$nextTick()
      
      const quickActions = wrapper.vm.currentQuickActions
      const adminActions = wrapper.vm.currentQuickActions
      
      // 重新设置为管理员角色进行对比
      userStore.userInfo = { role: 'admin', username: 'admin' }
      await wrapper.vm.$nextTick()
      
      const adminQuickActions = wrapper.vm.currentQuickActions
      
      // 普通用户的快捷操作应该少于管理员
      expect(quickActions.length).toBeLessThanOrEqual(adminQuickActions.length)
    })
  })

  describe('导航交互功能', () => {
    it('点击快捷操作项目应该导航到对应页面', async () => {
      const mockAction = {
        path: '/dashboard',
        title: '数据看板',
        icon: 'DataLine',
        description: '查看系统概览',
        color: '#409EFF'
      }

      // 模拟路由跳转
      const routerSpy = vi.spyOn(router, 'push')
      
      // 调用导航方法
      await wrapper.vm.navigateToAction(mockAction)
      
      // 验证路由跳转
      expect(routerSpy).toHaveBeenCalledWith('/dashboard')
      
      // 验证面板关闭
      expect(wrapper.vm.showQuickActions).toBe(false)
    })

    it('图标组件映射应该正确处理已知图标', () => {
      const knownIcon = wrapper.vm.getIconComponent('DataLine')
      expect(knownIcon).toBeTruthy()
    })

    it('图标组件映射应该为未知图标返回默认图标', () => {
      const unknownIcon = wrapper.vm.getIconComponent('UnknownIcon')
      expect(unknownIcon).toBeTruthy()
    })
  })

  describe('响应式行为', () => {
    it('在折叠状态下应该隐藏所有快捷操作相关元素', async () => {
      // 先显示快捷操作面板
      await wrapper.find('.trigger-btn').trigger('click')
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(true)
      
      // 设置为折叠状态
      await wrapper.setProps({ collapsed: true })
      
      // 所有快捷操作相关元素都应该隐藏
      expect(wrapper.find('.quick-actions-trigger').exists()).toBe(false)
      expect(wrapper.find('.quick-actions-panel').exists()).toBe(false)
    })

    it('从折叠状态切换到展开状态应该显示触发按钮', async () => {
      // 初始为折叠状态
      await wrapper.setProps({ collapsed: true })
      expect(wrapper.find('.quick-actions-trigger').exists()).toBe(false)
      
      // 切换到展开状态
      await wrapper.setProps({ collapsed: false })
      expect(wrapper.find('.quick-actions-trigger').exists()).toBe(true)
    })
  })

  describe('组件生命周期', () => {
    it('组件卸载时应该清理事件监听器', () => {
      // 这个测试主要用于检查是否有内存泄漏
      const consoleSpy = vi.spyOn(console, 'error')
      
      wrapper.unmount()
      
      // 如果有未清理的事件监听器，通常会在控制台输出错误
      expect(consoleSpy).not.toHaveBeenCalled()
    })
  })

  describe('边界情况处理', () => {
    it('应该处理空的快捷操作列表', async () => {
      // 模拟返回空的快捷操作列表
      userStore.userInfo = null
      await wrapper.vm.$nextTick()
      
      const quickActions = wrapper.vm.currentQuickActions
      expect(Array.isArray(quickActions)).toBe(true)
    })

    it('应该处理无效的导航路径', async () => {
      const invalidAction = {
        path: '', // 空路径
        title: '无效操作',
        icon: 'DataLine'
      }

      const routerSpy = vi.spyOn(router, 'push')
      
      await wrapper.vm.navigateToAction(invalidAction)
      
      // 即使路径无效，也不应该抛出错误
      expect(routerSpy).toHaveBeenCalledWith('')
    })

    it('应该处理缺失的用户信息', async () => {
      userStore.userInfo = null
      await wrapper.vm.$nextTick()
      
      // 组件不应该崩溃
      expect(wrapper.exists()).toBe(true)
      
      // 应该有默认的处理逻辑
      const quickActions = wrapper.vm.currentQuickActions
      expect(Array.isArray(quickActions)).toBe(true)
    })
  })

  describe('可访问性测试', () => {
    it('快捷操作按钮应该有适当的 ARIA 属性', () => {
      const triggerBtn = wrapper.find('.trigger-btn')
      
      // 检查按钮是否可聚焦
      expect(triggerBtn.element.tagName.toLowerCase()).toBe('button')
    })

    it('快捷操作面板应该有适当的语义结构', async () => {
      await wrapper.find('.trigger-btn').trigger('click')
      
      const panel = wrapper.find('.quick-actions-panel')
      expect(panel.exists()).toBe(true)
      
      // 检查面板标题
      const panelTitle = wrapper.find('.panel-title')
      expect(panelTitle.text()).toContain('快捷操作')
    })
  })

  describe('性能测试', () => {
    it('组件渲染应该在合理时间内完成', () => {
      const startTime = performance.now()
      
      // 重新挂载组件
      const testWrapper = mount(NavigationEnhancer, {
        global: {
          plugins: [router, pinia]
        },
        props: { collapsed: false }
      })
      
      const renderTime = performance.now() - startTime
      
      // 渲染时间应该小于 100ms
      expect(renderTime).toBeLessThan(100)
      
      testWrapper.unmount()
    })

    it('快捷操作计算应该是高效的', () => {
      userStore.userInfo = { role: 'admin', username: 'admin' }
      
      const startTime = performance.now()
      
      // 多次获取快捷操作（测试计算缓存）
      for (let i = 0; i < 100; i++) {
        wrapper.vm.currentQuickActions
      }
      
      const computeTime = performance.now() - startTime
      
      // 100次计算应该在 50ms 内完成
      expect(computeTime).toBeLessThan(50)
    })
  })
})