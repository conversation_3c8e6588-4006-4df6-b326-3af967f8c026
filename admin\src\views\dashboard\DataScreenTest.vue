<template>
  <div class="data-screen-test">
    <div class="test-header">
      <h1>🎯 数据大屏路由测试</h1>
      <p>如果您看到这个页面，说明路由配置正确！</p>
    </div>
    
    <div class="test-content">
      <div class="test-card">
        <h2>✅ 路由状态</h2>
        <p><strong>当前路径：</strong>{{ $route.path }}</p>
        <p><strong>路由名称：</strong>{{ $route.name }}</p>
        <p><strong>页面标题：</strong>{{ $route.meta.title }}</p>
      </div>
      
      <div class="test-card">
        <h2>🔗 导航测试</h2>
        <div class="nav-buttons">
          <button @click="goToDashboard" class="nav-btn">
            返回仪表板
          </button>
          <button @click="goToDataScreen" class="nav-btn">
            访问数据大屏
          </button>
        </div>
      </div>
      
      <div class="test-card">
        <h2>📊 组件测试</h2>
        <p>测试基础组件是否正常工作：</p>
        <div class="component-test">
          <div class="test-metric">
            <div class="metric-value">{{ testValue }}</div>
            <div class="metric-label">测试数值</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-footer">
      <p>🚀 LinkHub Pro 管理系统 - 路由测试页面</p>
      <p>时间：{{ currentTime }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const testValue = ref(1234)
const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const goToDashboard = () => {
  router.push('/admin/dashboard')
}

const goToDataScreen = () => {
  router.push('/data-screen')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  console.log('🎯 数据大屏测试页面已加载')
  console.log('当前路由:', route.path)
})
</script>

<style scoped>
.data-screen-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
}

.test-header h1 {
  font-size: 2.5rem;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.test-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.test-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  max-width: 1200px;
  width: 100%;
  margin-bottom: 40px;
}

.test-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.test-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.test-card h2 {
  margin-bottom: 16px;
  font-size: 1.4rem;
}

.test-card p {
  margin-bottom: 8px;
  line-height: 1.6;
}

.nav-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.nav-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.component-test {
  margin-top: 16px;
}

.test-metric {
  background: rgba(0, 0, 0, 0.2);
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: #4ade80;
}

.metric-label {
  font-size: 14px;
  opacity: 0.8;
}

.test-footer {
  text-align: center;
  opacity: 0.8;
  font-size: 14px;
}

.test-footer p {
  margin: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-screen-test {
    padding: 20px 16px;
  }
  
  .test-header h1 {
    font-size: 2rem;
  }
  
  .test-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .test-card {
    padding: 20px;
  }
  
  .nav-buttons {
    flex-direction: column;
  }
  
  .nav-btn {
    width: 100%;
  }
}
</style>
