<!-- 模块项组件 -->
<template>
  <div class="module-item" :class="moduleClasses">
    <!-- 模块主体 -->
    <div 
      class="module-header"
      @click="handleModuleClick"
      :title="collapsed ? module.title : ''"
    >
      <div class="module-icon-wrapper">
        <div class="module-icon" :style="iconStyles">
          <el-icon><component :is="module.icon" /></el-icon>
        </div>
        <div class="module-status" v-if="moduleStatus">
          <div class="status-dot" :class="`status-${moduleStatus}`"></div>
        </div>
      </div>
      
      <div class="module-content" v-if="!collapsed">
        <div class="module-info">
          <h4 class="module-title">{{ module.title }}</h4>
          <p class="module-description" v-if="module.description">
            {{ module.description }}
          </p>
        </div>
        
        <div class="module-meta">
          <!-- 通知徽章 -->
          <el-badge 
            v-if="badgeCount > 0" 
            :value="badgeCount" 
            :max="99"
            class="module-badge"
          />
          
          <!-- 快速操作按钮 -->
          <div class="module-actions" v-if="quickActions.length > 0">
            <el-button
              v-for="action in quickActions"
              :key="action.key"
              :icon="action.icon"
              size="small"
              text
              @click.stop="handleQuickAction(action)"
            />
          </div>
          
          <!-- 展开指示器 -->
          <div 
            v-if="hasChildren" 
            class="expand-indicator" 
            :class="{ 'expanded': childrenExpanded }"
          >
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 子页面列表 -->
    <CollapseTransition>
      <div v-if="hasChildren && childrenExpanded && !collapsed" class="module-children">
        <div
          v-for="child in visibleChildren"
          :key="child.path"
          class="child-item"
          :class="{ 'child-active': isChildActive(child) }"
          @click="handleChildClick(child)"
        >
          <div class="child-indicator"></div>
          <div class="child-icon">
            <el-icon><component :is="child.icon" /></el-icon>
          </div>
          <span class="child-title">{{ child.title }}</span>
          <div class="child-meta">
            <el-badge 
              v-if="getChildBadge(child)" 
              :value="getChildBadge(child)" 
              size="small"
            />
            <span class="child-shortcut" v-if="child.shortcut">{{ child.shortcut }}</span>
          </div>
        </div>
        
        <!-- 显示更多子页面 -->
        <div 
          v-if="hasMoreChildren && !showAllChildren"
          class="show-more-children"
          @click="showAllChildren = true"
        >
          <el-icon><MoreFilled /></el-icon>
          <span>显示更多 {{ hiddenChildrenCount }} 项</span>
        </div>
      </div>
    </CollapseTransition>

    <!-- 模块工具提示（折叠状态） -->
    <el-tooltip
      v-if="collapsed"
      :content="tooltipContent"
      placement="right"
      :offset="12"
      :show-after="300"
    >
      <template #content>
        <div class="module-tooltip">
          <div class="tooltip-header">
            <div class="tooltip-icon" :style="iconStyles">
              <el-icon><component :is="module.icon" /></el-icon>
            </div>
            <div class="tooltip-info">
              <div class="tooltip-title">{{ module.title }}</div>
              <div class="tooltip-desc">{{ module.description }}</div>
            </div>
          </div>
          
          <div v-if="hasChildren" class="tooltip-children">
            <div
              v-for="child in module.children.slice(0, 6)"
              :key="child.path"
              class="tooltip-child"
              @click="handleChildClick(child)"
            >
              <el-icon><component :is="child.icon" /></el-icon>
              <span>{{ child.title }}</span>
            </div>
          </div>
          
          <div class="tooltip-shortcut" v-if="module.shortcut">
            <kbd>{{ module.shortcut }}</kbd>
          </div>
        </div>
      </template>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowRight, MoreFilled } from '@element-plus/icons-vue'
import CollapseTransition from '@/components/transitions/CollapseTransition.vue'
import { useModuleBadge } from '@/composables/useModuleBadge'

const props = defineProps({
  module: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  active: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'badge-update', 'child-click', 'quick-action'])

const route = useRoute()
const router = useRouter()

// 响应式状态
const childrenExpanded = ref(false)
const showAllChildren = ref(false)
const maxVisibleChildren = 8

// Composables
const { badgeCount, updateBadge } = useModuleBadge(props.module.key)

// 计算属性
const moduleClasses = computed(() => ({
  'module-active': props.active,
  'module-collapsed': props.collapsed,
  'module-expanded': childrenExpanded.value,
  'has-children': hasChildren.value,
  'has-badge': badgeCount.value > 0
}))

const iconStyles = computed(() => ({
  color: props.module.color || '#6366f1',
  background: `${props.module.color || '#6366f1'}10`
}))

const hasChildren = computed(() => {
  return props.module.children && props.module.children.length > 0
})

const visibleChildren = computed(() => {
  if (!hasChildren.value) return []
  
  if (showAllChildren.value) {
    return props.module.children
  }
  
  return props.module.children.slice(0, maxVisibleChildren)
})

const hasMoreChildren = computed(() => {
  return hasChildren.value && props.module.children.length > maxVisibleChildren
})

const hiddenChildrenCount = computed(() => {
  if (!hasMoreChildren.value) return 0
  return props.module.children.length - maxVisibleChildren
})

const quickActions = computed(() => {
  // 根据模块类型返回快速操作
  const actions = []
  
  if (props.module.key === 'community-management') {
    actions.push(
      { key: 'quick-add', icon: 'Plus', title: '快速添加' }
    )
  }
  
  if (props.module.key === 'data-export') {
    actions.push(
      { key: 'export-now', icon: 'Download', title: '立即导出' }
    )
  }
  
  return actions
})

const moduleStatus = computed(() => {
  // 根据模块状态返回状态指示
  const statusMap = {
    'system-monitor': 'success',
    'error-logs': badgeCount.value > 0 ? 'error' : 'success',
    'pending-tasks': badgeCount.value > 0 ? 'warning' : 'success'
  }
  
  return statusMap[props.module.key] || null
})

const tooltipContent = computed(() => {
  let content = props.module.title
  if (props.module.description) {
    content += `\n${props.module.description}`
  }
  if (hasChildren.value) {
    content += `\n包含 ${props.module.children.length} 个子功能`
  }
  return content
})

// 方法
const handleModuleClick = () => {
  if (props.collapsed) {
    // 折叠状态下直接导航
    navigateToModule()
  } else {
    // 展开状态下根据情况处理
    if (hasChildren.value) {
      childrenExpanded.value = !childrenExpanded.value
    } else {
      navigateToModule()
    }
  }
  
  emit('click', props.module)
}

const navigateToModule = () => {
  let targetPath = props.module.path
  
  if (!targetPath && hasChildren.value) {
    targetPath = props.module.children[0].path
  }
  
  if (targetPath) {
    router.push(targetPath)
  }
}

const handleChildClick = (child) => {
  router.push(child.path)
  emit('child-click', child)
}

const handleQuickAction = (action) => {
  emit('quick-action', {
    module: props.module.key,
    action: action.key,
    data: action
  })
}

const isChildActive = (child) => {
  return route.path === child.path
}

const getChildBadge = (child) => {
  // 这里可以实现子页面的徽章逻辑
  const childBadges = {
    'pending-orders': 3,
    'error-logs': 1,
    'notifications': 5
  }
  
  return childBadges[child.key] || 0
}

// 监听活跃状态变化，自动展开子页面
watch(() => props.active, (newValue) => {
  if (newValue && hasChildren.value) {
    // 检查是否有子页面处于活跃状态
    const hasActiveChild = props.module.children?.some(child => 
      route.path === child.path
    )
    
    if (hasActiveChild) {
      childrenExpanded.value = true
    }
  }
})

// 监听徽章变化，通知父组件
watch(badgeCount, (newCount) => {
  emit('badge-update', props.module.key, newCount)
})

// 组件挂载时检查初始状态
onMounted(() => {
  // 如果当前路由匹配子页面，自动展开
  if (hasChildren.value && props.module.children.some(child => route.path === child.path)) {
    childrenExpanded.value = true
  }
  
  // 初始化徽章数据
  updateBadge()
})
</script>

<style lang="scss" scoped>
.module-item {
  margin-bottom: 6px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  
  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(99, 102, 241, 0.2);
    transform: translateX(2px);
  }
  
  &.module-active {
    background: linear-gradient(135deg, 
      rgba(99, 102, 241, 0.1) 0%,
      rgba(99, 102, 241, 0.05) 100%
    );
    border-color: rgba(99, 102, 241, 0.3);
    
    .module-header {
      .module-title {
        color: #6366f1;
        font-weight: 600;
      }
    }
  }
  
  &.module-collapsed {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    margin-bottom: 4px;
    
    .module-header {
      padding: 8px;
      justify-content: center;
    }
  }
  
  &.has-badge::before {
    content: '';
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    background: #ef4444;
    border-radius: 50%;
    z-index: 10;
  }
}

.module-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background: rgba(0, 0, 0, 0.02);
  }
}

.module-icon-wrapper {
  position: relative;
  margin-right: 12px;
  
  .module-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    .el-icon {
      font-size: 16px;
    }
  }
  
  .module-status {
    position: absolute;
    top: -2px;
    right: -2px;
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      border: 2px solid white;
      
      &.status-success {
        background: #10b981;
      }
      
      &.status-warning {
        background: #f59e0b;
        animation: pulse 2s infinite;
      }
      
      &.status-error {
        background: #ef4444;
        animation: pulse 2s infinite;
      }
    }
  }
}

.module-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
}

.module-info {
  flex: 1;
  min-width: 0;
  
  .module-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin: 0 0 2px 0;
    line-height: 1.3;
  }
  
  .module-description {
    font-size: 11px;
    color: #9ca3af;
    margin: 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.module-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .module-actions {
    display: flex;
    opacity: 0;
    transition: opacity 0.2s ease;
    
    .el-button {
      padding: 4px;
      min-height: 24px;
      
      &:hover {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
      }
    }
  }
  
  .expand-indicator {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    transition: transform 0.3s ease;
    
    &.expanded {
      transform: rotate(90deg);
    }
    
    .el-icon {
      font-size: 12px;
    }
  }
}

.module-item:hover .module-actions {
  opacity: 1;
}

.module-children {
  padding: 0 16px 8px 56px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 34px;
    top: 0;
    bottom: 8px;
    width: 1px;
    background: linear-gradient(180deg,
      rgba(99, 102, 241, 0.3) 0%,
      rgba(99, 102, 241, 0.1) 100%
    );
  }
}

.child-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #6b7280;
  position: relative;
  
  &:hover {
    background: rgba(99, 102, 241, 0.05);
    color: #6366f1;
  }
  
  &.child-active {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    font-weight: 500;
    
    .child-indicator {
      background: #6366f1;
    }
  }
  
  .child-indicator {
    width: 6px;
    height: 1px;
    background: #d1d5db;
    margin-right: 8px;
  }
  
  .child-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    
    .el-icon {
      font-size: 12px;
    }
  }
  
  .child-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .child-meta {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .child-shortcut {
      font-size: 10px;
      color: #9ca3af;
      background: #f9fafb;
      padding: 2px 4px;
      border-radius: 3px;
      border: 1px solid #e5e7eb;
    }
  }
}

.show-more-children {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 12px;
  margin-top: 4px;
  background: rgba(99, 102, 241, 0.05);
  border: 1px dashed rgba(99, 102, 241, 0.2);
  border-radius: 6px;
  color: #6366f1;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.4);
  }
}

// 工具提示样式
.module-tooltip {
  max-width: 280px;
  
  .tooltip-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .tooltip-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      margin-right: 12px;
    }
    
    .tooltip-info {
      flex: 1;
      
      .tooltip-title {
        font-weight: 600;
        margin-bottom: 2px;
      }
      
      .tooltip-desc {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
  
  .tooltip-children {
    border-top: 1px solid #e5e7eb;
    padding-top: 8px;
    margin-bottom: 8px;
    
    .tooltip-child {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background 0.2s ease;
      
      &:hover {
        background: #f3f4f6;
      }
      
      .el-icon {
        margin-right: 8px;
        font-size: 12px;
      }
    }
  }
  
  .tooltip-shortcut {
    text-align: center;
    
    kbd {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      color: #6b7280;
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .module-item.module-collapsed {
    .module-icon-wrapper .module-icon {
      width: 32px;
      height: 32px;
      
      .el-icon {
        font-size: 14px;
      }
    }
  }
}
</style>