<template>
  <div class="group-analytics">
    <!-- 数据概览 -->
    <div class="analytics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ analyticsData.total_views }}</div>
              <div class="card-label">总浏览量</div>
              <div class="card-trend">
                <span class="trend-text">较昨日 +12%</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ analyticsData.conversion_rate }}%</div>
              <div class="card-label">转化率</div>
              <div class="card-trend">
                <span class="trend-text">较昨日 +5%</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">¥{{ analyticsData.total_revenue }}</div>
              <div class="card-label">总收入</div>
              <div class="card-trend">
                <span class="trend-text">较昨日 +8%</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ analyticsData.avg_stay_time }}s</div>
              <div class="card-label">平均停留</div>
              <div class="card-trend">
                <span class="trend-text">较昨日 +3%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="analytics-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <span class="chart-title">访问趋势</span>
                <el-radio-group v-model="visitTrendPeriod" size="small">
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="30d">30天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <LineChart :data="visitTrendData" :height="300" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">转化漏斗</span>
            </template>
            <div class="chart-container">
              <FunnelChart :data="conversionData" :height="300" />
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="8">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">流量来源</span>
            </template>
            <div class="chart-container">
              <PieChart :data="trafficSourceData" :height="250" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">设备分布</span>
            </template>
            <div class="chart-container">
              <DoughnutChart :data="deviceData" :height="250" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span class="chart-title">地域分布</span>
            </template>
            <div class="region-stats">
              <div v-for="region in regionData" :key="region.name" class="region-item">
                <div class="region-info">
                  <span class="region-name">{{ region.name }}</span>
                  <span class="region-count">{{ region.count }}</span>
                </div>
                <el-progress
                  :percentage="region.percentage"
                  :stroke-width="6"
                  :show-text="false"
                  :color="region.color"
                />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="analytics-table">
      <el-card shadow="never">
        <template #header>
          <div class="table-header">
            <span class="table-title">详细数据</span>
            <div class="table-actions">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="fetchAnalyticsData"
              />
              <el-button size="small" @click="exportData">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </template>
        
        <el-table :data="detailData" style="width: 100%">
          <el-table-column label="日期" prop="date" width="120" />
          <el-table-column label="浏览量" prop="views" width="100" />
          <el-table-column label="访客数" prop="visitors" width="100" />
          <el-table-column label="新增成员" prop="new_members" width="100" />
          <el-table-column label="收入" prop="revenue" width="120">
            <template #default="{ row }">
              ¥{{ row.revenue }}
            </template>
          </el-table-column>
          <el-table-column label="转化率" prop="conversion_rate" width="100">
            <template #default="{ row }">
              {{ row.conversion_rate }}%
            </template>
          </el-table-column>
          <el-table-column label="平均停留时间" prop="avg_stay_time">
            <template #default="{ row }">
              {{ row.avg_stay_time }}s
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View,
  User,
  Money,
  TrendCharts,
  Download
} from '@element-plus/icons-vue'
import LineChart from '@/components/Charts/LineChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import FunnelChart from '@/components/Charts/FunnelChart.vue'

const props = defineProps({
  groupId: {
    type: [Number, String],
    required: true
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const visitTrendPeriod = ref('7d')
const dateRange = ref([])

const analyticsData = ref({
  total_views: 12580,
  conversion_rate: 15.6,
  total_revenue: 8960,
  avg_stay_time: 145
})

// 图表数据
const visitTrendData = ref({
  labels: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20', '1/21'],
  datasets: [{
    label: '访问量',
    data: [120, 190, 300, 500, 200, 300, 450],
    borderColor: '#3b82f6',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    tension: 0.4
  }]
})

const conversionData = ref([
  { name: '页面访问', value: 1000 },
  { name: '查看详情', value: 800 },
  { name: '点击加入', value: 300 },
  { name: '完成支付', value: 156 }
])

const trafficSourceData = ref({
  labels: ['微信分享', '直接访问', '搜索引擎', '其他'],
  datasets: [{
    data: [45, 25, 20, 10],
    backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
  }]
})

const deviceData = ref({
  labels: ['移动端', '桌面端', '平板'],
  datasets: [{
    data: [70, 25, 5],
    backgroundColor: ['#8b5cf6', '#06b6d4', '#84cc16']
  }]
})

const regionData = ref([
  { name: '广东', count: 2580, percentage: 35, color: '#3b82f6' },
  { name: '北京', count: 1890, percentage: 26, color: '#10b981' },
  { name: '上海', count: 1456, percentage: 20, color: '#f59e0b' },
  { name: '浙江', count: 980, percentage: 13, color: '#ef4444' },
  { name: '其他', count: 456, percentage: 6, color: '#8b5cf6' }
])

const detailData = ref([
  {
    date: '2024-01-21',
    views: 450,
    visitors: 320,
    new_members: 25,
    revenue: 1250,
    conversion_rate: 7.8,
    avg_stay_time: 165
  },
  {
    date: '2024-01-20',
    views: 380,
    visitors: 280,
    new_members: 18,
    revenue: 980,
    conversion_rate: 6.4,
    avg_stay_time: 142
  },
  {
    date: '2024-01-19',
    views: 520,
    visitors: 410,
    new_members: 32,
    revenue: 1680,
    conversion_rate: 7.8,
    avg_stay_time: 158
  }
])

// 方法
const fetchAnalyticsData = async () => {
  try {
    // 模拟API调用
    ElMessage.success('数据已更新')
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中')
}

// 生命周期
onMounted(() => {
  fetchAnalyticsData()
})
</script>

<style lang="scss" scoped>
.group-analytics {
  .analytics-overview {
    margin-bottom: 24px;
    
    .overview-card {
      display: flex;
      align-items: center;
      padding: 24px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }
      
      .card-icon {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 28px;
      }
      
      .card-content {
        flex: 1;
        
        .card-value {
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }
        
        .card-label {
          font-size: 14px;
          color: #64748b;
          margin-bottom: 8px;
        }
        
        .card-trend {
          .trend-text {
            font-size: 12px;
            color: #10b981;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .analytics-charts {
    margin-bottom: 24px;
    
    .chart-card {
      height: 100%;
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .chart-title {
          font-weight: 600;
          color: #1e293b;
        }
      }
      
      .chart-container {
        padding: 16px 0;
      }
      
      .region-stats {
        padding: 16px 0;
        
        .region-item {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .region-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .region-name {
              font-weight: 500;
              color: #1e293b;
            }
            
            .region-count {
              font-size: 14px;
              color: #64748b;
            }
          }
        }
      }
    }
  }
  
  .analytics-table {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .table-title {
        font-weight: 600;
        color: #1e293b;
      }
      
      .table-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
}
</style>