import{n as e,_ as l}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                         *//* empty css                 *//* empty css                       *//* empty css                             *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{T as a,aV as t,at as u,aw as o,a4 as s,a_ as d,aZ as n,aM as r,aL as i,b9 as c,b8 as p,bu as m,aY as _,bh as v,bi as f,U as h,a$ as g,az as b,aT as w,aB as y,aC as V,bw as k,bx as C,aR as U,bp as x,bq as j,ay as D,bk as z,bl as B,by as A,ao as q,X as F,a6 as S,bK as T,bL as Y,b1 as $,Q as E,R as M}from"./element-plus-h2SQQM64.js";import{S as L}from"./StatCard-u_ssO_Ky.js";import{r as H,L as I,e as O,k as R,l as N,t as P,E as K,z as Q,D as X,u as Z,F as G,Y as J,y as W,A as ee,B as le}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const ae={getList:l=>e({url:"/distributor/customers",method:"get",params:l}),getDetail:l=>e({url:`/distributor/customers/${l}`,method:"get"}),create:l=>e({url:"/distributor/customers",method:"post",data:l}),update:(l,a)=>e({url:`/distributor/customers/${l}`,method:"put",data:a}),delete:l=>e({url:`/distributor/customers/${l}`,method:"delete"}),getStats:()=>e({url:"/distributor/customers/stats",method:"get"}),addFollowUp:(l,a)=>e({url:`/distributor/customers/${l}/follow-ups`,method:"post",data:a}),getFollowUps:(l,a)=>e({url:`/distributor/customers/${l}/follow-ups`,method:"get",params:a}),batchUpdateStatus:l=>e({url:"/distributor/customers/batch-status",method:"post",data:l}),export:l=>e({url:"/distributor/customers/export",method:"get",params:l,responseType:"blob"}),getNeedFollowUp:l=>e({url:"/distributor/customers/need-follow-up",method:"get",params:l}),getTags:()=>e({url:"/distributor/customers/tags",method:"get"})},te={class:"customer-management"},ue={class:"page-header"},oe={class:"header-actions"},se={class:"card-header"},de={class:"header-actions"},ne={class:"customer-name"},re={key:0,class:"customer-tags"},ie={class:"amount"},ce={key:0},pe={key:1,class:"no-contact"},me={key:0},_e={key:1,class:"no-follow-up"},ve={class:"pagination-wrapper"},fe={class:"customer-form"},he={key:0,class:"customer-detail"},ge={class:"follow-up-form"},be={class:"batch-operation"},we={class:"help-content"},ye={class:"help-section"},Ve={class:"feature-item"},ke={class:"feature-icon"},Ce={class:"feature-item"},Ue={class:"feature-icon"},xe={class:"feature-item"},je={class:"feature-icon"},De={class:"feature-item"},ze={class:"feature-icon"},Be={class:"help-section"},Ae={class:"help-section"},qe={class:"help-section"},Fe={class:"guide-content"},Se={class:"guide-content"},Te={class:"guide-content"},Ye={class:"guide-content"},$e={class:"help-section"},Ee=l({__name:"CustomerManagement",setup(e){const l=ae,Ee=H(!1),Me=H(!1),Le=H(!1),He=H(!1),Ie=H(!1),Oe=H(!1),Re=H(!1),Ne=H(!1),Pe=H(!1),Ke=H(["add-customer"]),Qe=H([]),Xe=H([{level:"A级",color:"danger",name:"核心客户",criteria:"月消费≥5000元，订单频次高，忠诚度高",strategy:"专人对接，每周跟进，提供VIP服务"},{level:"B级",color:"warning",name:"重要客户",criteria:"月消费1000-5000元，有稳定需求",strategy:"定期维护，每两周跟进，重点培养"},{level:"C级",color:"primary",name:"潜力客户",criteria:"月消费500-1000元，有增长潜力",strategy:"适度关注，每月跟进，挖掘需求"},{level:"D级",color:"info",name:"普通客户",criteria:"月消费<500元，偶尔购买",strategy:"基础维护，季度跟进，群发营销"}]),Ze=H([{status:"活跃",color:"success",description:"近30天内有购买行为或主动联系",action:"保持现有服务水平，及时响应需求"},{status:"不活跃",color:"warning",description:"30-90天内无购买行为，但有互动",action:"主动联系了解情况，提供优惠激活"},{status:"潜在",color:"primary",description:"有意向但未成交，处于考虑阶段",action:"持续跟进，提供专业建议和方案"},{status:"流失",color:"danger",description:"超过90天无任何互动或明确拒绝",action:"尝试挽回，了解流失原因，改进服务"}]),Ge=H([]),Je=H({}),We=H([]),el=H([]),ll=H(null),al=H(null),tl=I({keyword:"",status:"",level:"",source:"",tag:"",need_follow_up:!1}),ul=I({current_page:1,per_page:20,total:0}),ol=I({action:"",status:"",level:"",tag:""}),sl=I({name:"",phone:"",wechat:"",level:"",company:"",remark:""}),dl={name:[{required:!0,message:"请输入客户姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],level:[{required:!0,message:"请选择客户等级",trigger:"change"}]},nl=I({content:"",method:"",next_follow_up:"",remark:""}),rl={content:[{required:!0,message:"请输入跟进内容",trigger:"blur"}],method:[{required:!0,message:"请选择跟进方式",trigger:"change"}]},il=H(),cl=H(),pl=async()=>{try{Ee.value=!0;const e={page:ul.current_page,limit:ul.per_page,...tl},a=await l.getList(e);Ge.value=a.data.data,ul.total=a.data.total,ul.current_page=a.data.current_page,ul.per_page=a.data.per_page}catch(e){console.error("加载客户列表失败:",e),E.error("加载客户列表失败")}finally{Ee.value=!1}},ml=async()=>{try{const e=await l.getStats();Je.value=e.data}catch(e){console.error("加载统计数据失败:",e),E.error("加载统计数据失败")}},_l=async()=>{try{const e=await l.getTags();We.value=e.data}catch(e){console.error("加载标签失败:",e)}},vl=function(e,l){let a;return function(...t){clearTimeout(a),a=setTimeout(()=>{clearTimeout(a),e(...t)},l)}}(()=>{ul.current_page=1,pl()},500),fl=e=>{el.value=e},hl=e=>{ll.value=e,Object.keys(sl).forEach(l=>{void 0!==e[l]&&(sl[l]=e[l])}),Ie.value=!0},gl=e=>{al.value=e,Re.value=!0},bl=async e=>{try{await M.confirm(`确定要删除客户"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await l.delete(e.id),E.success("客户删除成功"),pl(),ml()}catch(a){"cancel"!==a&&(console.error("删除失败:",a),E.error("删除失败"))}},wl=async()=>{try{await il.value.validate(),Le.value=!0,ll.value?(await l.update(ll.value.id,sl),E.success("客户信息更新成功")):(await l.create(sl),E.success("客户创建成功")),Vl()}catch(e){console.error("保存客户失败:",e),E.error("保存客户失败")}finally{Le.value=!1}},yl=async()=>{try{await cl.value.validate(),He.value=!0;const e={customer_id:al.value.id,...nl};await l.addFollowUp(e),E.success("跟进记录添加成功"),kl()}catch(e){console.error("保存跟进记录失败:",e),E.error("保存跟进记录失败")}finally{He.value=!1}},Vl=()=>{Ie.value=!1,ll.value=null,Object.keys(sl).forEach(e=>{sl[e]=""}),pl(),ml(),_l()},kl=()=>{Re.value=!1,Object.keys(nl).forEach(e=>{nl[e]=""}),pl()},Cl=()=>{0!==el.value.length?Ne.value=!0:E.warning("请先选择客户")},Ul=async()=>{if(ol.action)try{Me.value=!0;const e=el.value.map(e=>e.id);"status"===ol.action&&await l.batchUpdateStatus({customer_ids:e,status:ol.status}),E.success("批量操作成功"),Ne.value=!1,pl(),ml()}catch(e){console.error("批量操作失败:",e),E.error("批量操作失败")}finally{Me.value=!1}else E.warning("请选择操作类型")},xl=async()=>{try{await l.export(tl);E.success("数据导出成功")}catch(e){console.error("导出失败:",e),E.error("导出失败")}},jl=e=>({A:"danger",B:"warning",C:"primary",D:"info"}[e]||"info"),Dl=e=>({active:"success",inactive:"warning",potential:"primary",lost:"danger"}[e]||"info"),zl=e=>e<0?`逾期${Math.abs(e)}天`:0===e?"今日跟进":1===e?"明日跟进":`${e}天后`,Bl=e=>new Date(e).toLocaleDateString("zh-CN");return O(()=>{pl(),ml(),_l()}),(e,l)=>{const E=a,M=u,H=d,I=n,O=r,ae=p,ml=c,_l=m,Vl=_,kl=f,Al=g,ql=V,Fl=y,Sl=b,Tl=v,Yl=C,$l=j,El=x,Ml=D,Ll=B,Hl=z,Il=A,Ol=$,Rl=Y,Nl=T,Pl=k;return N(),R("div",te,[P("div",ue,[l[40]||(l[40]=P("div",{class:"header-left"},[P("h2",null,"客户管理"),P("p",{class:"page-description"},"管理您的客户信息，跟踪客户状态，提升客户转化率")],-1)),P("div",oe,[K(M,{type:"info",onClick:l[0]||(l[0]=e=>Pe.value=!0)},{default:Q(()=>[K(E,null,{default:Q(()=>[K(Z(t))]),_:1}),l[37]||(l[37]=X(" 功能说明 ",-1))]),_:1,__:[37]}),K(M,{type:"primary",onClick:l[1]||(l[1]=e=>Ie.value=!0)},{default:Q(()=>[K(E,null,{default:Q(()=>[K(Z(o))]),_:1}),l[38]||(l[38]=X(" 新增客户 ",-1))]),_:1,__:[38]}),K(M,{onClick:xl},{default:Q(()=>[K(E,null,{default:Q(()=>[K(Z(s))]),_:1}),l[39]||(l[39]=X(" 导出数据 ",-1))]),_:1,__:[39]})])]),K(I,{gutter:20,class:"stats-row"},{default:Q(()=>[K(H,{span:6},{default:Q(()=>[K(L,{title:"总客户数",value:Je.value.total_customers||0,icon:"User",color:"#409EFF"},null,8,["value"])]),_:1}),K(H,{span:6},{default:Q(()=>[K(L,{title:"活跃客户",value:Je.value.active_customers||0,icon:"UserFilled",color:"#67C23A"},null,8,["value"])]),_:1}),K(H,{span:6},{default:Q(()=>[K(L,{title:"潜在客户",value:Je.value.potential_customers||0,icon:"View",color:"#E6A23C"},null,8,["value"])]),_:1}),K(H,{span:6},{default:Q(()=>[K(L,{title:"需要跟进",value:Je.value.need_follow_up||0,icon:"Bell",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),K(Vl,{class:"filter-card"},{default:Q(()=>[K(I,{gutter:20},{default:Q(()=>[K(H,{span:6},{default:Q(()=>[K(O,{modelValue:tl.keyword,"onUpdate:modelValue":l[2]||(l[2]=e=>tl.keyword=e),placeholder:"搜索客户姓名、手机号、微信号",clearable:"",onInput:Z(vl)},{prefix:Q(()=>[K(E,null,{default:Q(()=>[K(Z(i))]),_:1})]),_:1},8,["modelValue","onInput"])]),_:1}),K(H,{span:4},{default:Q(()=>[K(ml,{modelValue:tl.status,"onUpdate:modelValue":l[3]||(l[3]=e=>tl.status=e),placeholder:"客户状态",clearable:"",onChange:pl},{default:Q(()=>[K(ae,{label:"活跃",value:"active"}),K(ae,{label:"不活跃",value:"inactive"}),K(ae,{label:"潜在",value:"potential"}),K(ae,{label:"流失",value:"lost"})]),_:1},8,["modelValue"])]),_:1}),K(H,{span:4},{default:Q(()=>[K(ml,{modelValue:tl.level,"onUpdate:modelValue":l[4]||(l[4]=e=>tl.level=e),placeholder:"客户等级",clearable:"",onChange:pl},{default:Q(()=>[K(ae,{label:"A级客户",value:"A"}),K(ae,{label:"B级客户",value:"B"}),K(ae,{label:"C级客户",value:"C"}),K(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1}),K(H,{span:4},{default:Q(()=>[K(ml,{modelValue:tl.source,"onUpdate:modelValue":l[5]||(l[5]=e=>tl.source=e),placeholder:"客户来源",clearable:"",onChange:pl},{default:Q(()=>[K(ae,{label:"推荐",value:"referral"}),K(ae,{label:"广告",value:"advertisement"}),K(ae,{label:"社交媒体",value:"social_media"}),K(ae,{label:"直接访问",value:"direct"}),K(ae,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),K(H,{span:3},{default:Q(()=>[K(ml,{modelValue:tl.tag,"onUpdate:modelValue":l[6]||(l[6]=e=>tl.tag=e),placeholder:"客户标签",clearable:"",onChange:pl},{default:Q(()=>[(N(!0),R(G,null,J(We.value,e=>(N(),W(ae,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),K(H,{span:3},{default:Q(()=>[K(_l,{modelValue:tl.need_follow_up,"onUpdate:modelValue":l[7]||(l[7]=e=>tl.need_follow_up=e),onChange:pl},{default:Q(()=>l[41]||(l[41]=[X(" 需要跟进 ",-1)])),_:1,__:[41]},8,["modelValue"])]),_:1})]),_:1})]),_:1}),K(Vl,null,{header:Q(()=>[P("div",se,[l[44]||(l[44]=P("span",null,"客户列表",-1)),P("div",de,[K(M,{size:"small",onClick:Cl,disabled:0===el.value.length},{default:Q(()=>l[42]||(l[42]=[X(" 批量操作 ",-1)])),_:1,__:[42]},8,["disabled"]),K(M,{size:"small",onClick:pl},{default:Q(()=>[K(E,null,{default:Q(()=>[K(Z(U))]),_:1}),l[43]||(l[43]=X(" 刷新 ",-1))]),_:1,__:[43]})])])]),default:Q(()=>[ee((N(),W(Tl,{data:Ge.value,onSelectionChange:fl,stripe:""},{default:Q(()=>[K(kl,{type:"selection",width:"55"}),K(kl,{prop:"name",label:"姓名",width:"120"},{default:Q(({row:e})=>[P("div",ne,[P("span",null,h(e.name),1),e.tags&&e.tags.length?(N(),R("div",re,[(N(!0),R(G,null,J(e.tags.slice(0,2),e=>(N(),W(Al,{key:e,size:"small",type:"info"},{default:Q(()=>[X(h(e),1)]),_:2},1024))),128))])):le("",!0)])]),_:1}),K(kl,{prop:"phone",label:"手机号",width:"130"}),K(kl,{prop:"wechat",label:"微信号",width:"120"}),K(kl,{label:"等级",width:"80"},{default:Q(({row:e})=>[K(Al,{type:jl(e.level)},{default:Q(()=>[X(h(e.level_text),1)]),_:2},1032,["type"])]),_:1}),K(kl,{label:"状态",width:"80"},{default:Q(({row:e})=>[K(Al,{type:Dl(e.status)},{default:Q(()=>[X(h(e.status_text),1)]),_:2},1032,["type"])]),_:1}),K(kl,{prop:"company",label:"公司",width:"150","show-overflow-tooltip":""}),K(kl,{label:"消费金额",width:"100"},{default:Q(({row:e})=>[P("span",ie,"¥"+h(e.total_spent),1)]),_:1}),K(kl,{prop:"order_count",label:"订单数",width:"80"}),K(kl,{label:"最后联系",width:"120"},{default:Q(({row:e})=>[e.last_contact_at?(N(),R("span",ce,h(Bl(e.last_contact_at)),1)):(N(),R("span",pe,"未联系"))]),_:1}),K(kl,{label:"跟进状态",width:"120"},{default:Q(({row:e})=>{return[e.next_follow_up?(N(),R("div",me,[K(Al,{type:(l=e.follow_up_days,l<0?"danger":l<=1?"warning":"success"),size:"small"},{default:Q(()=>[X(h(zl(e.follow_up_days)),1)]),_:2},1032,["type"])])):(N(),R("span",_e,"无计划"))];var l}),_:1}),K(kl,{label:"操作",width:"200",fixed:"right"},{default:Q(({row:e})=>[K(M,{size:"small",onClick:l=>{return a=e,al.value=a,void(Oe.value=!0);var a}},{default:Q(()=>l[45]||(l[45]=[X(" 详情 ",-1)])),_:2,__:[45]},1032,["onClick"]),K(M,{size:"small",onClick:l=>gl(e)},{default:Q(()=>l[46]||(l[46]=[X(" 跟进 ",-1)])),_:2,__:[46]},1032,["onClick"]),K(Sl,{onCommand:l=>((e,l)=>{switch(e){case"edit":hl(l);break;case"delete":bl(l)}})(l,e)},{dropdown:Q(()=>[K(Fl,null,{default:Q(()=>[K(ql,{command:"edit"},{default:Q(()=>l[48]||(l[48]=[X("编辑",-1)])),_:1,__:[48]}),K(ql,{command:"delete",divided:""},{default:Q(()=>l[49]||(l[49]=[X("删除",-1)])),_:1,__:[49]})]),_:1})]),default:Q(()=>[K(M,{size:"small"},{default:Q(()=>[l[47]||(l[47]=X(" 更多",-1)),K(E,{class:"el-icon--right"},{default:Q(()=>[K(Z(w))]),_:1})]),_:1,__:[47]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Pl,Ee.value]]),P("div",ve,[K(Yl,{"current-page":ul.current_page,"onUpdate:currentPage":l[8]||(l[8]=e=>ul.current_page=e),"page-size":ul.per_page,"onUpdate:pageSize":l[9]||(l[9]=e=>ul.per_page=e),total:ul.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pl,onCurrentChange:pl},null,8,["current-page","page-size","total"])])]),_:1}),K(Ml,{modelValue:Ie.value,"onUpdate:modelValue":l[17]||(l[17]=e=>Ie.value=e),title:ll.value?"编辑客户":"新增客户",width:"800px"},{footer:Q(()=>[K(M,{onClick:l[16]||(l[16]=e=>Ie.value=!1)},{default:Q(()=>l[50]||(l[50]=[X("取消",-1)])),_:1,__:[50]}),K(M,{type:"primary",onClick:wl,loading:Le.value},{default:Q(()=>l[51]||(l[51]=[X(" 保存 ",-1)])),_:1,__:[51]},8,["loading"])]),default:Q(()=>[P("div",fe,[K(El,{model:sl,rules:dl,ref_key:"customerFormRef",ref:il,"label-width":"100px"},{default:Q(()=>[K(I,{gutter:20},{default:Q(()=>[K(H,{span:12},{default:Q(()=>[K($l,{label:"客户姓名",prop:"name"},{default:Q(()=>[K(O,{modelValue:sl.name,"onUpdate:modelValue":l[10]||(l[10]=e=>sl.name=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])]),_:1})]),_:1}),K(H,{span:12},{default:Q(()=>[K($l,{label:"手机号",prop:"phone"},{default:Q(()=>[K(O,{modelValue:sl.phone,"onUpdate:modelValue":l[11]||(l[11]=e=>sl.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),K(H,{span:12},{default:Q(()=>[K($l,{label:"微信号",prop:"wechat"},{default:Q(()=>[K(O,{modelValue:sl.wechat,"onUpdate:modelValue":l[12]||(l[12]=e=>sl.wechat=e),placeholder:"请输入微信号"},null,8,["modelValue"])]),_:1})]),_:1}),K(H,{span:12},{default:Q(()=>[K($l,{label:"客户等级",prop:"level"},{default:Q(()=>[K(ml,{modelValue:sl.level,"onUpdate:modelValue":l[13]||(l[13]=e=>sl.level=e),placeholder:"选择客户等级"},{default:Q(()=>[K(ae,{label:"A级客户",value:"A"}),K(ae,{label:"B级客户",value:"B"}),K(ae,{label:"C级客户",value:"C"}),K(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),K(H,{span:24},{default:Q(()=>[K($l,{label:"公司名称"},{default:Q(()=>[K(O,{modelValue:sl.company,"onUpdate:modelValue":l[14]||(l[14]=e=>sl.company=e),placeholder:"请输入公司名称"},null,8,["modelValue"])]),_:1})]),_:1}),K(H,{span:24},{default:Q(()=>[K($l,{label:"备注"},{default:Q(()=>[K(O,{modelValue:sl.remark,"onUpdate:modelValue":l[15]||(l[15]=e=>sl.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),K(Ml,{modelValue:Oe.value,"onUpdate:modelValue":l[21]||(l[21]=e=>Oe.value=e),title:"客户详情",width:"1000px"},{footer:Q(()=>[K(M,{onClick:l[18]||(l[18]=e=>Oe.value=!1)},{default:Q(()=>l[52]||(l[52]=[X("关闭",-1)])),_:1,__:[52]}),K(M,{type:"primary",onClick:l[19]||(l[19]=e=>hl(al.value))},{default:Q(()=>l[53]||(l[53]=[X(" 编辑 ",-1)])),_:1,__:[53]}),K(M,{onClick:l[20]||(l[20]=e=>gl(al.value))},{default:Q(()=>l[54]||(l[54]=[X(" 添加跟进 ",-1)])),_:1,__:[54]})]),default:Q(()=>[al.value?(N(),R("div",he,[K(Hl,{column:3,border:""},{default:Q(()=>[K(Ll,{label:"客户姓名"},{default:Q(()=>[X(h(al.value.name),1)]),_:1}),K(Ll,{label:"手机号"},{default:Q(()=>[X(h(al.value.phone),1)]),_:1}),K(Ll,{label:"微信号"},{default:Q(()=>[X(h(al.value.wechat||"未填写"),1)]),_:1}),K(Ll,{label:"客户等级"},{default:Q(()=>[K(Al,{type:jl(al.value.level)},{default:Q(()=>[X(h(al.value.level_text),1)]),_:1},8,["type"])]),_:1}),K(Ll,{label:"客户状态"},{default:Q(()=>[K(Al,{type:Dl(al.value.status)},{default:Q(()=>[X(h(al.value.status_text),1)]),_:1},8,["type"])]),_:1}),K(Ll,{label:"公司名称"},{default:Q(()=>[X(h(al.value.company||"未填写"),1)]),_:1}),K(Ll,{label:"消费金额"},{default:Q(()=>[X("¥"+h(al.value.total_spent),1)]),_:1}),K(Ll,{label:"订单数量"},{default:Q(()=>[X(h(al.value.order_count),1)]),_:1}),K(Ll,{label:"最后联系"},{default:Q(()=>[X(h(al.value.last_contact_at?Bl(al.value.last_contact_at):"未联系"),1)]),_:1}),K(Ll,{label:"创建时间",span:"2"},{default:Q(()=>[X(h(Bl(al.value.created_at)),1)]),_:1}),K(Ll,{label:"备注",span:"3"},{default:Q(()=>[X(h(al.value.remark||"无"),1)]),_:1})]),_:1})])):le("",!0)]),_:1},8,["modelValue"]),K(Ml,{modelValue:Re.value,"onUpdate:modelValue":l[27]||(l[27]=e=>Re.value=e),title:"添加跟进记录",width:"600px"},{footer:Q(()=>[K(M,{onClick:l[26]||(l[26]=e=>Re.value=!1)},{default:Q(()=>l[55]||(l[55]=[X("取消",-1)])),_:1,__:[55]}),K(M,{type:"primary",onClick:yl,loading:He.value},{default:Q(()=>l[56]||(l[56]=[X(" 保存 ",-1)])),_:1,__:[56]},8,["loading"])]),default:Q(()=>[P("div",ge,[K(El,{model:nl,rules:rl,ref_key:"followUpFormRef",ref:cl,"label-width":"100px"},{default:Q(()=>[K($l,{label:"跟进内容",prop:"content"},{default:Q(()=>[K(O,{modelValue:nl.content,"onUpdate:modelValue":l[22]||(l[22]=e=>nl.content=e),type:"textarea",rows:4,placeholder:"请输入跟进内容"},null,8,["modelValue"])]),_:1}),K($l,{label:"跟进方式",prop:"method"},{default:Q(()=>[K(ml,{modelValue:nl.method,"onUpdate:modelValue":l[23]||(l[23]=e=>nl.method=e),placeholder:"选择跟进方式"},{default:Q(()=>[K(ae,{label:"电话",value:"phone"}),K(ae,{label:"微信",value:"wechat"}),K(ae,{label:"邮件",value:"email"}),K(ae,{label:"面谈",value:"meeting"}),K(ae,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),K($l,{label:"下次跟进",prop:"next_follow_up"},{default:Q(()=>[K(Il,{modelValue:nl.next_follow_up,"onUpdate:modelValue":l[24]||(l[24]=e=>nl.next_follow_up=e),type:"datetime",placeholder:"选择下次跟进时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),K($l,{label:"备注"},{default:Q(()=>[K(O,{modelValue:nl.remark,"onUpdate:modelValue":l[25]||(l[25]=e=>nl.remark=e),type:"textarea",rows:2,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"]),K(Ml,{modelValue:Ne.value,"onUpdate:modelValue":l[33]||(l[33]=e=>Ne.value=e),title:"批量操作",width:"400px"},{footer:Q(()=>[K(M,{onClick:l[32]||(l[32]=e=>Ne.value=!1)},{default:Q(()=>l[57]||(l[57]=[X("取消",-1)])),_:1,__:[57]}),K(M,{type:"primary",onClick:Ul,loading:Me.value},{default:Q(()=>l[58]||(l[58]=[X(" 确定 ",-1)])),_:1,__:[58]},8,["loading"])]),default:Q(()=>[P("div",be,[P("p",null,"已选择 "+h(el.value.length)+" 个客户",1),K(El,{model:ol,"label-width":"80px"},{default:Q(()=>[K($l,{label:"操作类型"},{default:Q(()=>[K(ml,{modelValue:ol.action,"onUpdate:modelValue":l[28]||(l[28]=e=>ol.action=e),placeholder:"请选择操作"},{default:Q(()=>[K(ae,{label:"更新状态",value:"status"}),K(ae,{label:"更新等级",value:"level"}),K(ae,{label:"添加标签",value:"tag"})]),_:1},8,["modelValue"])]),_:1}),"status"===ol.action?(N(),W($l,{key:0,label:"新状态"},{default:Q(()=>[K(ml,{modelValue:ol.status,"onUpdate:modelValue":l[29]||(l[29]=e=>ol.status=e),placeholder:"选择状态"},{default:Q(()=>[K(ae,{label:"活跃",value:"active"}),K(ae,{label:"不活跃",value:"inactive"}),K(ae,{label:"潜在",value:"potential"}),K(ae,{label:"流失",value:"lost"})]),_:1},8,["modelValue"])]),_:1})):le("",!0),"level"===ol.action?(N(),W($l,{key:1,label:"新等级"},{default:Q(()=>[K(ml,{modelValue:ol.level,"onUpdate:modelValue":l[30]||(l[30]=e=>ol.level=e),placeholder:"选择等级"},{default:Q(()=>[K(ae,{label:"A级客户",value:"A"}),K(ae,{label:"B级客户",value:"B"}),K(ae,{label:"C级客户",value:"C"}),K(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1})):le("",!0),"tag"===ol.action?(N(),W($l,{key:2,label:"标签"},{default:Q(()=>[K(O,{modelValue:ol.tag,"onUpdate:modelValue":l[31]||(l[31]=e=>ol.tag=e),placeholder:"输入标签名称"},null,8,["modelValue"])]),_:1})):le("",!0)]),_:1},8,["model"])])]),_:1},8,["modelValue"]),K(Ml,{modelValue:Pe.value,"onUpdate:modelValue":l[36]||(l[36]=e=>Pe.value=e),title:"客户管理功能说明",width:"900px",class:"help-dialog"},{default:Q(()=>[P("div",we,[l[81]||(l[81]=P("div",{class:"help-section"},[P("h3",null,"📋 功能概述"),P("p",null,"客户管理系统帮助您有效管理和跟踪客户信息，提升客户转化率和维护效率。通过客户分级、状态跟踪、跟进提醒等功能，让您的客户管理更加专业和高效。")],-1)),P("div",ye,[l[63]||(l[63]=P("h3",null,"🚀 核心功能",-1)),K(I,{gutter:20},{default:Q(()=>[K(H,{span:12},{default:Q(()=>[P("div",Ve,[P("div",ke,[K(E,null,{default:Q(()=>[K(Z(q))]),_:1})]),l[59]||(l[59]=P("div",{class:"feature-content"},[P("h4",null,"客户信息管理"),P("p",null,"完整记录客户基本信息、联系方式、公司信息等")],-1))])]),_:1}),K(H,{span:12},{default:Q(()=>[P("div",Ce,[P("div",Ue,[K(E,null,{default:Q(()=>[K(Z(F))]),_:1})]),l[60]||(l[60]=P("div",{class:"feature-content"},[P("h4",null,"跟进提醒"),P("p",null,"设置跟进计划，系统自动提醒，不错过任何商机")],-1))])]),_:1}),K(H,{span:12},{default:Q(()=>[P("div",xe,[P("div",je,[K(E,null,{default:Q(()=>[K(Z(S))]),_:1})]),l[61]||(l[61]=P("div",{class:"feature-content"},[P("h4",null,"客户分级"),P("p",null,"A/B/C/D四级分类，精准定位客户价值")],-1))])]),_:1}),K(H,{span:12},{default:Q(()=>[P("div",De,[P("div",ze,[K(E,null,{default:Q(()=>[K(Z(S))]),_:1})]),l[62]||(l[62]=P("div",{class:"feature-content"},[P("h4",null,"数据统计"),P("p",null,"实时统计客户数量、状态分布、转化情况")],-1))])]),_:1})]),_:1})]),P("div",Be,[l[64]||(l[64]=P("h3",null,"🏆 客户等级说明",-1)),K(Tl,{data:Xe.value,style:{width:"100%"}},{default:Q(()=>[K(kl,{prop:"level",label:"等级",width:"80"},{default:Q(({row:e})=>[K(Al,{type:e.color},{default:Q(()=>[X(h(e.level),1)]),_:2},1032,["type"])]),_:1}),K(kl,{prop:"name",label:"等级名称",width:"120"}),K(kl,{prop:"criteria",label:"评定标准"}),K(kl,{prop:"strategy",label:"维护策略"})]),_:1},8,["data"])]),P("div",Ae,[l[65]||(l[65]=P("h3",null,"📊 客户状态说明",-1)),K(Tl,{data:Ze.value,style:{width:"100%"}},{default:Q(()=>[K(kl,{prop:"status",label:"状态",width:"100"},{default:Q(({row:e})=>[K(Al,{type:e.color,size:"small"},{default:Q(()=>[X(h(e.status),1)]),_:2},1032,["type"])]),_:1}),K(kl,{prop:"description",label:"状态描述"}),K(kl,{prop:"action",label:"建议操作"})]),_:1},8,["data"])]),P("div",qe,[l[75]||(l[75]=P("h3",null,"📝 操作指南",-1)),K(Nl,{modelValue:Ke.value,"onUpdate:modelValue":l[34]||(l[34]=e=>Ke.value=e)},{default:Q(()=>[K(Rl,{title:"如何新增客户？",name:"add-customer"},{default:Q(()=>[P("div",Fe,[l[67]||(l[67]=P("ol",null,[P("li",null,'点击页面右上角的"新增客户"按钮'),P("li",null,"填写客户基本信息（姓名、手机号为必填项）"),P("li",null,"选择客户等级和来源"),P("li",null,"添加客户标签（可选）"),P("li",null,'点击"保存"完成客户创建')],-1)),K(Ol,{type:"info",closable:!1},{default:Q(()=>l[66]||(l[66]=[X(" 💡 建议：新增客户时尽量填写完整信息，有助于后续的客户管理和跟进 ",-1)])),_:1,__:[66]})])]),_:1}),K(Rl,{title:"如何设置跟进提醒？",name:"follow-up"},{default:Q(()=>[P("div",Se,[l[69]||(l[69]=P("ol",null,[P("li",null,"在客户列表中找到需要跟进的客户"),P("li",null,'点击"跟进"按钮'),P("li",null,"填写跟进内容和下次跟进时间"),P("li",null,"选择跟进方式（电话、微信、邮件等）"),P("li",null,"保存后系统会在指定时间提醒您")],-1)),K(Ol,{type:"warning",closable:!1},{default:Q(()=>l[68]||(l[68]=[X(" ⚠️ 注意：跟进提醒会在系统消息中显示，请及时查看处理 ",-1)])),_:1,__:[68]})])]),_:1}),K(Rl,{title:"如何进行批量操作？",name:"batch-operation"},{default:Q(()=>[P("div",Te,[l[71]||(l[71]=P("ol",null,[P("li",null,"在客户列表中勾选需要操作的客户"),P("li",null,'点击"批量操作"按钮'),P("li",null,"选择操作类型（更新状态、等级或标签）"),P("li",null,"设置新的值"),P("li",null,"确认执行批量操作")],-1)),K(Ol,{type:"success",closable:!1},{default:Q(()=>l[70]||(l[70]=[X(" ✅ 提示：批量操作可以大大提高工作效率，适用于客户状态统一更新的场景 ",-1)])),_:1,__:[70]})])]),_:1}),K(Rl,{title:"如何使用筛选功能？",name:"filter"},{default:Q(()=>[P("div",Ye,[l[73]||(l[73]=P("p",null,[P("strong",null,"可用筛选条件：")],-1)),l[74]||(l[74]=P("ul",null,[P("li",null,[P("strong",null,"关键词搜索"),X("：支持按姓名、手机号、微信号搜索")]),P("li",null,[P("strong",null,"客户状态"),X("：活跃、不活跃、潜在、流失")]),P("li",null,[P("strong",null,"客户等级"),X("：A/B/C/D级客户")]),P("li",null,[P("strong",null,"客户来源"),X("：推荐、广告、社交媒体等")]),P("li",null,[P("strong",null,"客户标签"),X("：自定义标签筛选")]),P("li",null,[P("strong",null,"跟进状态"),X("：需要跟进的客户")])],-1)),K(Ol,{type:"info",closable:!1},{default:Q(()=>l[72]||(l[72]=[X(" 💡 技巧：多个筛选条件可以组合使用，帮助您快速找到目标客户 ",-1)])),_:1,__:[72]})])]),_:1})]),_:1},8,["modelValue"])]),l[82]||(l[82]=P("div",{class:"help-section"},[P("h3",null,"💡 使用技巧"),P("div",{class:"tips-grid"},[P("div",{class:"tip-item"},[P("div",{class:"tip-icon"},"🎯"),P("div",{class:"tip-content"},[P("h4",null,"精准分级"),P("p",null,"根据客户消费能力和购买意向进行分级，A级客户重点维护，D级客户适度跟进")])]),P("div",{class:"tip-item"},[P("div",{class:"tip-icon"},"⏰"),P("div",{class:"tip-content"},[P("h4",null,"定期跟进"),P("p",null,"建议A级客户每周跟进，B级客户每两周跟进，C/D级客户每月跟进")])]),P("div",{class:"tip-item"},[P("div",{class:"tip-icon"},"🏷️"),P("div",{class:"tip-content"},[P("h4",null,"标签管理"),P("p",null,'使用标签标记客户特征，如"高价值"、"决策者"、"价格敏感"等')])]),P("div",{class:"tip-item"},[P("div",{class:"tip-icon"},"📊"),P("div",{class:"tip-content"},[P("h4",null,"数据分析"),P("p",null,"定期查看客户统计数据，分析客户结构和转化趋势")])])])],-1)),P("div",$e,[l[80]||(l[80]=P("h3",null,"❓ 常见问题",-1)),K(Nl,{modelValue:Qe.value,"onUpdate:modelValue":l[35]||(l[35]=e=>Qe.value=e)},{default:Q(()=>[K(Rl,{title:"为什么我的客户状态没有自动更新？",name:"faq1"},{default:Q(()=>l[76]||(l[76]=[P("p",null,"客户状态需要手动更新或通过系统规则自动判断。建议定期检查客户最后联系时间和订单情况，及时更新状态。",-1)])),_:1,__:[76]}),K(Rl,{title:"如何批量导入客户信息？",name:"faq2"},{default:Q(()=>l[77]||(l[77]=[P("p",null,'点击"批量导入"按钮，下载模板文件，按照模板格式填写客户信息后上传。支持Excel格式文件。',-1)])),_:1,__:[77]}),K(Rl,{title:"客户等级可以自动调整吗？",name:"faq3"},{default:Q(()=>l[78]||(l[78]=[P("p",null,"目前客户等级需要手动调整。系统会根据客户的消费金额和订单数量提供等级建议，您可以参考后手动调整。",-1)])),_:1,__:[78]}),K(Rl,{title:"跟进提醒在哪里查看？",name:"faq4"},{default:Q(()=>l[79]||(l[79]=[P("p",null,'跟进提醒会在系统消息中显示，同时在客户列表的"跟进状态"列中也会显示提醒信息。',-1)])),_:1,__:[79]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0f489f4d"]]);export{Ee as default};
