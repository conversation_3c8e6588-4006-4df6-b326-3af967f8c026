// 导航系统配置文件
// admin/src/config/navigation-config.js

/**
 * 导航配置常量
 */
export const NAVIGATION_CONFIG = {
  // 基础配置
  ANIMATION_DURATION: 300,
  SEARCH_DEBOUNCE_DELAY: 300,
  MAX_RECENT_ITEMS: 10,
  MAX_FAVORITES: 50,
  
  // 响应式断点
  BREAKPOINTS: {
    mobile: 768,
    tablet: 1024,
    desktop: 1440
  },
  
  // 导航模式
  MODES: {
    SIDEBAR: 'sidebar',
    HEADER: 'header', 
    MOBILE: 'mobile',
    BOTTOM: 'bottom'
  },
  
  // 主题配置
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
  },
  
  // 缓存键名
  CACHE_KEYS: {
    NAVIGATION_STATE: 'nav-state',
    RECENT_ITEMS: 'nav-recent',
    FAVORITES: 'nav-favorites',
    SEARCH_HISTORY: 'nav-search-history',
    USER_PREFERENCES: 'nav-preferences',
    COLLAPSED_STATE: 'nav-collapsed',
    EXPANDED_SECTIONS: 'nav-expanded-sections'
  }
}

/**
 * 导航项类型定义
 */
export const NAVIGATION_TYPES = {
  SECTION: 'section',
  MODULE: 'module',
  PAGE: 'page',
  EXTERNAL: 'external',
  SEPARATOR: 'separator'
}

/**
 * 权限级别
 */
export const PERMISSION_LEVELS = {
  PUBLIC: 'public',
  USER: 'user',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin'
}

/**
 * 图标映射
 */
export const ICON_MAP = {
  // 仪表板类
  dashboard: 'DataLine',
  analytics: 'TrendCharts',
  overview: 'Monitor',
  reports: 'Document',
  
  // 社群管理类
  community: 'Menu',
  groups: 'ChatDotSquare',
  templates: 'Document',
  content: 'EditPen',
  members: 'User',
  
  // 用户管理类
  users: 'User',
  profiles: 'Avatar',
  permissions: 'Lock',
  roles: 'Key',
  
  // 系统管理类
  system: 'Setting',
  settings: 'Tools',
  monitor: 'Monitor',
  logs: 'DocumentCopy',
  backup: 'FolderOpened',
  
  // 财务管理类
  finance: 'Money',
  orders: 'ShoppingCart',
  payments: 'CreditCard',
  commissions: 'Coin',
  withdrawals: 'Wallet',
  
  // 运营工具类
  operations: 'Operation',
  export: 'Download',
  import: 'Upload',
  notifications: 'Bell',
  help: 'QuestionFilled',
  
  // 通用图标
  home: 'House',
  search: 'Search',
  star: 'Star',
  folder: 'Folder',
  link: 'Link',
  more: 'More',
  add: 'Plus',
  edit: 'Edit',
  delete: 'Delete',
  copy: 'DocumentCopy',
  share: 'Share'
}

/**
 * 默认导航结构
 */
export const DEFAULT_NAVIGATION = {
  // 仪表板域
  dashboard: {
    key: 'dashboard',
    title: '仪表板',
    description: '数据概览和分析',
    icon: 'DataLine',
    color: '#3b82f6',
    order: 1,
    permission: PERMISSION_LEVELS.USER,
    modules: [
      {
        key: 'overview',
        title: '概览',
        icon: 'TrendCharts',
        path: '/admin/dashboard',
        permission: PERMISSION_LEVELS.USER,
        badge: null
      },
      {
        key: 'analytics',
        title: '数据分析',
        icon: 'DataLine',
        path: '/admin/analytics',
        permission: PERMISSION_LEVELS.USER,
        badge: null,
        isNew: true
      },
      {
        key: 'reports',
        title: '报表中心',
        icon: 'Document',
        path: '/admin/analytics',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      }
    ]
  },
  
  // 社群管理域
  community: {
    key: 'community',
    title: '社群管理',
    description: '微信群组运营管理',
    icon: 'Menu',
    color: '#10b981',
    order: 2,
    permission: PERMISSION_LEVELS.USER,
    modules: [
      {
        key: 'groups',
        title: '群组列表',
        icon: 'ChatDotSquare',
        path: '/community/groups',
        permission: PERMISSION_LEVELS.USER,
        badge: null,
        children: [
          {
            key: 'group-list',
            title: '全部群组',
            icon: 'List',
            path: '/community/groups'
          },
          {
            key: 'group-create',
            title: '创建群组',
            icon: 'Plus',
            path: '/community/add-enhanced'
          },
          {
            key: 'group-analytics',
            title: '群组分析',
            icon: 'TrendCharts',
            path: '/community/groups/analytics'
          }
        ]
      },
      {
        key: 'templates',
        title: '模板管理',
        icon: 'Document',
        path: '/community/templates',
        permission: PERMISSION_LEVELS.USER,
        badge: null,
        isHot: true
      },
      {
        key: 'content',
        title: '内容管理',
        icon: 'EditPen',
        path: '/community/content',
        permission: PERMISSION_LEVELS.USER,
        badge: null
      }
    ]
  },
  
  // 用户管理域
  users: {
    key: 'users',
    title: '用户管理',
    description: '用户数据和权限管理',
    icon: 'User',
    color: '#f59e0b',
    order: 3,
    permission: PERMISSION_LEVELS.ADMIN,
    modules: [
      {
        key: 'user-list',
        title: '用户列表',
        icon: 'User',
        path: '/users',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'user-analytics',
        title: '用户分析',
        icon: 'TrendCharts',
        path: '/users/analytics',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'user-permissions',
        title: '权限管理',
        icon: 'Lock',
        path: '/users/permissions',
        permission: PERMISSION_LEVELS.SUPER_ADMIN,
        badge: null
      }
    ]
  },
  
  // 财务管理域
  finance: {
    key: 'finance',
    title: '财务管理',
    description: '订单和佣金管理',
    icon: 'Money',
    color: '#ef4444',
    order: 4,
    permission: PERMISSION_LEVELS.ADMIN,
    modules: [
      {
        key: 'orders',
        title: '订单管理',
        icon: 'ShoppingCart',
        path: '/finance/orders',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'commissions',
        title: '佣金管理',
        icon: 'Coin',
        path: '/finance/commissions',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'withdrawals',
        title: '提现管理',
        icon: 'Wallet',
        path: '/finance/withdrawals',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      }
    ]
  },
  
  // 系统管理域
  system: {
    key: 'system',
    title: '系统设置',
    description: '系统配置和管理',
    icon: 'Setting',
    color: '#8b5cf6',
    order: 5,
    permission: PERMISSION_LEVELS.ADMIN,
    modules: [
      {
        key: 'settings',
        title: '基础设置',
        icon: 'Tools',
        path: '/system/settings',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'monitor',
        title: '系统监控',
        icon: 'Monitor',
        path: '/system/monitor',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'logs',
        title: '操作日志',
        icon: 'DocumentCopy',
        path: '/system/logs',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      },
      {
        key: 'notifications',
        title: '通知中心',
        icon: 'Bell',
        path: '/system/notifications',
        permission: PERMISSION_LEVELS.ADMIN,
        badge: null
      }
    ]
  }
}

/**
 * 移动端底部导航配置
 */
export const MOBILE_BOTTOM_NAV = [
  {
    key: 'dashboard',
    label: '首页',
    icon: 'House',
    path: '/dashboard',
    activeColor: '#3b82f6'
  },
  {
    key: 'community',
    label: '社群',
    icon: 'Menu',
    path: '/community',
    activeColor: '#10b981'
  },
  {
    key: 'users',
    label: '用户',
    icon: 'User',
    path: '/users',
    activeColor: '#f59e0b'
  },
  {
    key: 'notifications',
    label: '通知',
    icon: 'Bell',
    path: '/notifications',
    activeColor: '#ef4444'
  },
  {
    key: 'profile',
    label: '我的',
    icon: 'User',
    path: '/profile',
    activeColor: '#8b5cf6'
  }
]

/**
 * 快速操作配置
 */
export const QUICK_ACTIONS = [
  {
    key: 'create-group',
    title: '创建群组',
    icon: 'Plus',
    path: '/community/add-enhanced',
    color: '#3b82f6',
    permission: PERMISSION_LEVELS.USER
  },
  {
    key: 'import-data',
    title: '数据导入',
    icon: 'Upload',
    path: '/system/import',
    color: '#10b981',
    permission: PERMISSION_LEVELS.ADMIN
  },
  {
    key: 'export-data',
    title: '数据导出',
    icon: 'Download',
    path: '/system/export',
    color: '#f59e0b',
    permission: PERMISSION_LEVELS.ADMIN
  },
  {
    key: 'system-monitor',
    title: '系统监控',
    icon: 'Monitor',
    path: '/system/monitor',
    color: '#ef4444',
    permission: PERMISSION_LEVELS.ADMIN
  },
  {
    key: 'help-center',
    title: '帮助中心',
    icon: 'QuestionFilled',
    path: '/help',
    color: '#8b5cf6',
    permission: PERMISSION_LEVELS.PUBLIC
  },
  {
    key: 'feedback',
    title: '意见反馈',
    icon: 'ChatDotSquare',
    path: '/feedback',
    color: '#06b6d4',
    permission: PERMISSION_LEVELS.USER
  }
]

/**
 * 搜索配置
 */
export const SEARCH_CONFIG = {
  // 搜索类别
  CATEGORIES: [
    { key: 'all', label: '全部', icon: 'Search' },
    { key: 'pages', label: '页面', icon: 'Document' },
    { key: 'users', label: '用户', icon: 'User' },
    { key: 'groups', label: '群组', icon: 'Menu' },
    { key: 'data', label: '数据', icon: 'DataLine' }
  ],
  
  // 搜索建议
  SUGGESTIONS: [
    '群组管理',
    '用户分析',
    '数据导出',
    '系统设置',
    '订单管理',
    '佣金统计',
    '操作日志',
    '通知中心'
  ],
  
  // 搜索快捷键
  SHORTCUTS: {
    GLOBAL_SEARCH: ['Meta+K', 'Ctrl+K'],
    NAVIGATE_DOWN: ['ArrowDown'],
    NAVIGATE_UP: ['ArrowUp'],
    SELECT: ['Enter'],
    ESCAPE: ['Escape']
  }
}

/**
 * 面包屑配置
 */
export const BREADCRUMB_CONFIG = {
  // 最大显示层级
  MAX_LEVELS: 5,
  
  // 分隔符
  SEPARATOR: 'ArrowRight',
  
  // 是否显示首页
  SHOW_HOME: true,
  
  // 首页配置
  HOME: {
    title: '首页',
    icon: 'House',
    path: '/dashboard'
  }
}

/**
 * 动画配置
 */
export const ANIMATION_CONFIG = {
  // 动画时长（毫秒）
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  },
  
  // 缓动函数
  EASING: {
    EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
    EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  
  // 动画类型
  TYPES: {
    FADE: 'fade',
    SLIDE: 'slide',
    SCALE: 'scale',
    BOUNCE: 'bounce'
  }
}

/**
 * 颜色主题配置
 */
export const COLOR_THEMES = {
  LIGHT: {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#06b6d4'
  },
  
  DARK: {
    primary: '#60a5fa',
    secondary: '#a78bfa',
    success: '#34d399',
    warning: '#fbbf24',
    danger: '#f87171',
    info: '#22d3ee'
  }
}

/**
 * 性能配置
 */
export const PERFORMANCE_CONFIG = {
  // 虚拟滚动配置
  VIRTUAL_SCROLL: {
    ITEM_HEIGHT: 48,
    BUFFER_SIZE: 10,
    THRESHOLD: 100
  },
  
  // 懒加载配置
  LAZY_LOAD: {
    THRESHOLD: '200px',
    ROOT_MARGIN: '50px'
  },
  
  // 缓存配置
  CACHE: {
    MAX_SIZE: 100,
    TTL: 300000 // 5分钟
  }
}

/**
 * 国际化配置
 */
export const I18N_CONFIG = {
  DEFAULT_LOCALE: 'zh-CN',
  FALLBACK_LOCALE: 'en-US',
  SUPPORTED_LOCALES: ['zh-CN', 'en-US', 'ja-JP'],
  
  // 语言包命名空间
  NAMESPACES: {
    NAVIGATION: 'navigation',
    COMMON: 'common',
    ERRORS: 'errors'
  }
}

/**
 * 无障碍访问配置
 */
export const ACCESSIBILITY_CONFIG = {
  // 键盘导航
  KEYBOARD_NAVIGATION: true,
  
  // 屏幕阅读器支持
  SCREEN_READER: true,
  
  // 高对比度模式
  HIGH_CONTRAST: false,
  
  // 焦点指示器
  FOCUS_INDICATOR: true,
  
  // ARIA 标签
  ARIA_LABELS: {
    MAIN_NAVIGATION: '主导航',
    BREADCRUMB: '面包屑导航',
    SEARCH: '搜索',
    USER_MENU: '用户菜单',
    NOTIFICATIONS: '通知'
  }
}

/**
 * 导出所有配置
 */
export default {
  NAVIGATION_CONFIG,
  NAVIGATION_TYPES,
  PERMISSION_LEVELS,
  ICON_MAP,
  DEFAULT_NAVIGATION,
  MOBILE_BOTTOM_NAV,
  QUICK_ACTIONS,
  SEARCH_CONFIG,
  BREADCRUMB_CONFIG,
  ANIMATION_CONFIG,
  COLOR_THEMES,
  PERFORMANCE_CONFIG,
  I18N_CONFIG,
  ACCESSIBILITY_CONFIG
}