// 新的4域导航架构配置
// admin/src/config/navigation-domains.js

/**
 * 四域导航架构配置
 * 
 * 将原有的17个一级菜单重组为4个业务域：
 * 1. 经营分析域 (Business Analytics)
 * 2. 客户运营域 (Customer Operations) 
 * 3. 财务管控域 (Financial Management)
 * 4. 系统设置域 (System Settings)
 */

export const NAVIGATION_DOMAINS = {
  // 经营分析域
  BUSINESS_ANALYTICS: {
    key: 'business-analytics',
    title: '经营分析',
    icon: 'TrendCharts',
    color: '#1890ff',
    order: 1,
    description: '数据洞察与业务分析',
    modules: {
      DATA_DASHBOARD: {
        key: 'data-dashboard',
        title: '数据看板',
        icon: 'Monitor',
        path: '/dashboard',
        order: 1,
        children: [
          { path: '/dashboard', title: '综合概览', icon: 'DataBoard' },
          { path: '/data-screen', title: '实时大屏', icon: 'DataLine' },
          { path: '/data-screen/reports', title: '自定义报表', icon: 'DataAnalysis' }
        ]
      },
      BUSINESS_ANALYSIS: {
        key: 'business-analysis', 
        title: '业务分析',
        icon: 'DataAnalysis',
        path: '/analytics',
        order: 2,
        children: [
          { path: '/user/analytics', title: '用户增长分析', icon: 'TrendCharts' },
          { path: '/orders/analytics', title: '转化漏斗分析', icon: 'Guide' },
          { path: '/finance/analytics', title: '收益分析', icon: 'Money' }
        ]
      },
      OPERATIONS_MONITORING: {
        key: 'operations-monitoring',
        title: '运营监控', 
        icon: 'Monitor',
        path: '/monitoring',
        order: 3,
        children: [
          { path: '/system/monitor', title: '系统监控', icon: 'View' },
          { path: '/system/alerts', title: '异常告警', icon: 'Warning' },
          { path: '/system/performance', title: '性能分析', icon: 'Odometer' }
        ]
      }
    }
  },

  // 客户运营域
  CUSTOMER_OPERATIONS: {
    key: 'customer-operations',
    title: '客户运营',
    icon: 'UserFilled',
    color: '#52c41a', 
    order: 2,
    description: '用户管理与社群运营',
    modules: {
      COMMUNITY_MANAGEMENT: {
        key: 'community-management',
        title: '社群管理',
        icon: 'Comment',
        path: '/community',
        order: 1,
        children: [
          { path: '/community/groups', title: '群组列表', icon: 'UserFilled' },
          { path: '/community/templates', title: '内容管理', icon: 'Document' },
          { path: '/community/rules', title: '自动化规则', icon: 'MagicStick' },
          { path: '/community/events', title: '活动管理', icon: 'Ticket' },
          { path: '/community/moderation', title: '内容审核', icon: 'ShieldCheck' }
        ]
      },
      USER_MANAGEMENT: {
        key: 'user-management',
        title: '用户管理',
        icon: 'User',
        path: '/user',
        order: 2,
        children: [
          { path: '/user/list', title: '用户列表', icon: 'UserFilled' },
          { path: '/user/analytics', title: '用户分析', icon: 'DataAnalysis' },
          { path: '/permission/roles', title: '权限管理', icon: 'Lock' },
          { path: '/user/behavior', title: '行为追踪', icon: 'View' }
        ]
      },
      DISTRIBUTION_SYSTEM: {
        key: 'distribution-system',
        title: '分销体系',
        icon: 'Share',
        path: '/distribution',
        order: 3,
        children: [
          { path: '/distribution/distributors', title: '分销商管理', icon: 'UserFilled' },
          { path: '/agent', title: '代理商管理', icon: 'Avatar' },
          { path: '/promotion/links', title: '推广管理', icon: 'Share' },
          { path: '/anti-block', title: '防红系统', icon: 'Tools' }
        ]
      },
      ORDER_MANAGEMENT: {
        key: 'order-management',
        title: '订单管理',
        icon: 'Tickets',
        path: '/orders',
        order: 4,
        children: [
          { path: '/orders/list', title: '订单列表', icon: 'List' },
          { path: '/orders/analytics', title: '订单分析', icon: 'DataAnalysis' },
          { path: '/orders/after-sales', title: '售后管理', icon: 'CustomerService' }
        ]
      }
    }
  },

  // 财务管控域
  FINANCIAL_MANAGEMENT: {
    key: 'financial-management',
    title: '财务管控',
    icon: 'Money',
    color: '#faad14',
    order: 3, 
    description: '财务数据与资金管理',
    modules: {
      REVENUE_MANAGEMENT: {
        key: 'revenue-management',
        title: '收支管理',
        icon: 'Money',
        path: '/finance',
        order: 1,
        children: [
          { path: '/finance/dashboard', title: '财务总览', icon: 'DataLine' },
          { path: '/finance/revenue', title: '收入统计', icon: 'TrendCharts' },
          { path: '/finance/expenses', title: '支出管理', icon: 'Minus' }
        ]
      },
      COMMISSION_MANAGEMENT: {
        key: 'commission-management',
        title: '佣金管理',
        icon: 'Medal',
        path: '/commission',
        order: 2,
        children: [
          { path: '/finance/commission-settings', title: '佣金设置', icon: 'Setting' },
          { path: '/finance/commission-logs', title: '佣金发放', icon: 'Medal' },
          { path: '/finance/commission-history', title: '佣金记录', icon: 'Document' }
        ]
      },
      WITHDRAWAL_MANAGEMENT: {
        key: 'withdrawal-management',
        title: '提现管理',
        icon: 'Upload',
        path: '/withdrawal',
        order: 3,
        children: [
          { path: '/finance/withdraw', title: '提现申请', icon: 'Upload' },
          { path: '/finance/withdraw-audit', title: '提现审核', icon: 'Select' },
          { path: '/finance/transactions', title: '提现记录', icon: 'Goods' }
        ]
      },
      PAYMENT_CONFIG: {
        key: 'payment-config',
        title: '支付配置',
        icon: 'CreditCard',
        path: '/admin/payment-settings',
        order: 4,
        children: [
          { path: '/admin/payment-settings', title: '支付通道', icon: 'CreditCard' },
          { path: '/admin/payment-channels', title: '支付设置', icon: 'Setting' },
          { path: '/admin/payment-logs', title: '回调日志', icon: 'Document' }
        ]
      }
    }
  },

  // 系统设置域
  SYSTEM_SETTINGS: {
    key: 'system-settings',
    title: '系统设置',
    icon: 'Setting',
    color: '#722ed1',
    order: 4,
    description: '系统配置与管理工具',
    modules: {
      ORGANIZATION_STRUCTURE: {
        key: 'organization-structure',
        title: '组织架构',
        icon: 'OfficeBuilding',
        path: '/organization',
        order: 1,
        children: [
          { path: '/substation/list', title: '分站管理', icon: 'OfficeBuilding' },
          { path: '/organization/departments', title: '部门设置', icon: 'Guide' },
          { path: '/organization/positions', title: '岗位配置', icon: 'Rank' }
        ]
      },
      SECURITY_MANAGEMENT: {
        key: 'security-management',
        title: '安全管理',
        icon: 'Lock',
        path: '/security',
        order: 2,
        children: [
          { path: '/permission/roles', title: '权限配置', icon: 'Key' },
          { path: '/permission/permissions', title: '角色管理', icon: 'UserFilled' },
          { path: '/system/operation-logs', title: '操作日志', icon: 'Document' },
          { path: '/security/management', title: '安全策略', icon: 'Shield' }
        ]
      },
      SYSTEM_CONFIG: {
        key: 'system-config',
        title: '系统配置',
        icon: 'Tools',
        path: '/system',
        order: 3,
        children: [
          { path: '/system/settings', title: '基础设置', icon: 'Tools' },
          { path: '/system/notifications', title: '通知管理', icon: 'Bell' },
          { path: '/system/export', title: '数据导出', icon: 'Download' },
          { path: '/system/file-management', title: '文件管理', icon: 'Folder' }
        ]
      },
      HELP_SUPPORT: {
        key: 'help-support',
        title: '帮助支持',
        icon: 'InfoFilled',
        path: '/help',
        order: 4,
        children: [
          { path: '/system/user-guide', title: '使用指南', icon: 'InfoFilled' },
          { path: '/system/function-test', title: '功能测试', icon: 'Cpu' },
          { path: '/help/feedback', title: '意见反馈', icon: 'ChatDotSquare' }
        ]
      }
    }
  }
}

/**
 * 角色权限配置 - 基于新的4域架构
 */
export const ROLE_DOMAIN_PERMISSIONS = {
  // 超级管理员 - 完整四域访问
  admin: {
    domains: ['business-analytics', 'customer-operations', 'financial-management', 'system-settings'],
    modules: ['*']
  },
  
  // 分站管理员 - 经营分析 + 客户运营 + 财务管控(受限)
  substation: {
    domains: ['business-analytics', 'customer-operations', 'financial-management'],
    modules: [
      'data-dashboard', 'business-analysis', 'operations-monitoring',
      'community-management', 'user-management', 'distribution-system', 'order-management',
      'revenue-management', 'commission-management' // 财务管控受限
    ]
  },
  
  // 代理商 - 客户运营(分销相关) + 财务管控(佣金相关)
  agent: {
    domains: ['customer-operations', 'financial-management'],
    modules: [
      'distribution-system', 'order-management',
      'commission-management', 'withdrawal-management'
    ]
  },
  
  // 分销员 - 客户运营(群组管理) + 财务管控(个人佣金)
  distributor: {
    domains: ['customer-operations', 'financial-management'],
    modules: [
      'community-management', 'order-management',
      'commission-management'
    ]
  },
  
  // 群主 - 客户运营(群组管理)
  group_owner: {
    domains: ['customer-operations'],
    modules: ['community-management']
  },
  
  // 普通用户 - 个人中心 + 订单管理
  user: {
    domains: ['customer-operations'],
    modules: ['order-management'],
    pages: ['/user/center', '/user/profile', '/orders/list']
  }
}

/**
 * 路由映射 - 将旧路由映射到新的域结构
 */
export const ROUTE_DOMAIN_MAPPING = {
  // 经营分析域路由
  '/dashboard': 'business-analytics.data-dashboard',
  '/data-screen': 'business-analytics.data-dashboard', 
  '/user/analytics': 'business-analytics.business-analysis',
  '/orders/analytics': 'business-analytics.business-analysis',
  '/finance/analytics': 'business-analytics.business-analysis',
  
  // 客户运营域路由
  '/community': 'customer-operations.community-management',
  '/user': 'customer-operations.user-management',
  '/distribution': 'customer-operations.distribution-system',
  '/agent': 'customer-operations.distribution-system',
  '/promotion': 'customer-operations.distribution-system',
  '/anti-block': 'customer-operations.distribution-system',
  '/orders': 'customer-operations.order-management',
  
  // 财务管控域路由
  '/finance': 'financial-management.revenue-management',
  '/payment': 'financial-management.payment-config',
  
  // 系统设置域路由
  '/substation': 'system-settings.organization-structure',
  '/permission': 'system-settings.security-management',
  '/security': 'system-settings.security-management',
  '/system': 'system-settings.system-config'
}

/**
 * 获取用户有权限访问的域
 */
export function getUserDomains(userRole) {
  const roleConfig = ROLE_DOMAIN_PERMISSIONS[userRole]
  if (!roleConfig) return []
  
  return roleConfig.domains.map(domainKey => NAVIGATION_DOMAINS[domainKey.toUpperCase().replace('-', '_')])
    .filter(domain => domain)
}

/**
 * 检查用户是否有访问指定域的权限
 */
export function hasUserDomainAccess(userRole, domainKey) {
  const roleConfig = ROLE_DOMAIN_PERMISSIONS[userRole]
  if (!roleConfig) return false
  
  return roleConfig.domains.includes(domainKey)
}

/**
 * 检查用户是否有访问指定模块的权限
 */
export function hasUserModuleAccess(userRole, moduleKey) {
  const roleConfig = ROLE_DOMAIN_PERMISSIONS[userRole]
  if (!roleConfig) return false
  
  return roleConfig.modules.includes('*') || roleConfig.modules.includes(moduleKey)
}

/**
 * 根据路由路径获取所属域和模块
 */
export function getRouteDomainInfo(routePath) {
  const domainPath = ROUTE_DOMAIN_MAPPING[routePath]
  if (!domainPath) return null
  
  const [domainKey, moduleKey] = domainPath.split('.')
  return { domainKey, moduleKey }
}

/**
 * 生成用户的导航菜单数据
 */
export function generateUserNavigation(userRole) {
  const userDomains = getUserDomains(userRole)
  const roleConfig = ROLE_DOMAIN_PERMISSIONS[userRole]
  
  return userDomains.map(domain => ({
    ...domain,
    modules: Object.values(domain.modules).filter(module => 
      roleConfig.modules.includes('*') || roleConfig.modules.includes(module.key)
    )
  })).filter(domain => domain.modules.length > 0)
}

export default {
  NAVIGATION_DOMAINS,
  ROLE_DOMAIN_PERMISSIONS,
  ROUTE_DOMAIN_MAPPING,
  getUserDomains,
  hasUserDomainAccess,
  hasUserModuleAccess,
  getRouteDomainInfo,
  generateUserNavigation
}