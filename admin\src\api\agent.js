import request from '@/utils/request'

// 代理商相关API
export const agentApi = {
  // 获取我的代理商信息
  getMy() {
    return request({
      url: '/admin/agent/my',
      method: 'get'
    })
  },

  // 获取我的统计数据
  getMyStats() {
    return request({
      url: '/api/v1/admin/agent/my-stats',
      method: 'get'
    })
  },

  // 获取代理商列表
  getList(params) {
    return request({
      url: '/admin/agent/list',
      method: 'get',
      params
    })
  },

  // 获取代理商详情
  getDetail(id) {
    return request({
      url: `/admin/agent/${id}`,
      method: 'get'
    })
  },

  // 创建代理商
  create(data) {
    return request({
      url: '/admin/agent',
      method: 'post',
      data
    })
  },

  // 更新代理商
  update(id, data) {
    return request({
      url: `/admin/agent/${id}`,
      method: 'put',
      data
    })
  },

  // 删除代理商
  delete(id) {
    return request({
      url: `/admin/agent/${id}`,
      method: 'delete'
    })
  },

  // 获取团队数据
  getTeamData() {
    return request({
      url: '/admin/agent/team',
      method: 'get'
    })
  },

  // 获取佣金数据
  getCommissionData(params) {
    return request({
      url: '/admin/agent/commission',
      method: 'get',
      params
    })
  }
}

// 为了兼容性，也导出单独的函数
export const getMy = agentApi.getMy
export const getMyStats = agentApi.getMyStats
export const getList = agentApi.getList
export const getDetail = agentApi.getDetail
export const create = agentApi.create
export const update = agentApi.update
export const deleteAgent = agentApi.delete
export const getTeamData = agentApi.getTeamData
export const getCommissionData = agentApi.getCommissionData

// 代理商申请相关API
export const agentApplicationApi = {
  // 获取申请统计
  getStats() {
    return request({
      url: '/admin/agent-application/stats',
      method: 'get'
    })
  },

  // 获取申请列表
  getList(params) {
    return request({
      url: '/admin/agent-application/list',
      method: 'get',
      params
    })
  },

  // 获取申请详情
  getDetail(id) {
    return request({
      url: `/admin/agent-application/${id}`,
      method: 'get'
    })
  },

  // 审核申请
  review(id, data) {
    return request({
      url: `/admin/agent-application/${id}/review`,
      method: 'post',
      data
    })
  },

  // 批量审核
  batchReview(data) {
    return request({
      url: '/admin/agent-application/batch-review',
      method: 'post',
      data
    })
  }
}

export default agentApi
