<template>
  <div class="group-settings-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">群组配置</h1>
          <p class="page-subtitle">管理群组创建的全局配置和默认设置</p>
        </div>
        <div class="header-actions">
          <el-button @click="resetToDefaults" :loading="resetting">
            <el-icon><RefreshLeft /></el-icon>
            恢复默认
          </el-button>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置表单 -->
    <div class="settings-container">
      <el-form
        ref="formRef"
        :model="settings"
        :rules="formRules"
        label-width="150px"
        size="default"
      >
        <!-- 基础配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>基础配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="默认成员上限" prop="default_member_limit">
                <el-input-number
                  v-model="settings.default_member_limit"
                  :min="1"
                  :max="2000"
                  style="width: 100%"
                />
                <div class="form-tip">新创建群组的默认成员上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认价格" prop="default_price">
                <el-input-number
                  v-model="settings.default_price"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
                <div class="form-tip">新创建群组的默认价格</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="订单有效期" prop="default_order_expire">
                <el-input-number
                  v-model="settings.default_order_expire"
                  :min="5"
                  :max="1440"
                  style="width: 100%"
                />
                <span style="margin-left: 8px;">分钟</span>
                <div class="form-tip">订单未支付的默认有效期</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认按钮文字" prop="default_button_title">
                <el-input
                  v-model="settings.default_button_title"
                  placeholder="立即加入"
                  maxlength="20"
                />
                <div class="form-tip">群组页面的默认按钮文字</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="默认启用功能">
            <el-checkbox-group v-model="settings.default_features">
              <el-checkbox label="city_location">城市定位</el-checkbox>
              <el-checkbox label="analytics">访问统计</el-checkbox>
              <el-checkbox label="conversion_tracking">转化跟踪</el-checkbox>
              <el-checkbox label="auto_publish">自动发布</el-checkbox>
              <el-checkbox label="generate_qr_poster">生成推广海报</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>

        <!-- 分类配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Grid /></el-icon>
              <span>分类配置</span>
              <div class="card-actions">
                <el-button size="small" @click="addCategory">
                  <el-icon><Plus /></el-icon>
                  添加分类
                </el-button>
              </div>
            </div>
          </template>

          <div class="category-list">
            <div
              v-for="(category, index) in settings.categories"
              :key="category.value"
              class="category-item"
            >
              <div class="category-info">
                <el-input
                  v-model="category.name"
                  placeholder="分类名称"
                  style="width: 150px; margin-right: 12px;"
                />
                <el-input
                  v-model="category.value"
                  placeholder="分类值"
                  style="width: 150px; margin-right: 12px;"
                />
                <el-color-picker
                  v-model="category.color"
                  style="margin-right: 12px;"
                />
                <el-input-number
                  v-model="category.recommended_price"
                  :min="0"
                  :precision="2"
                  placeholder="推荐价格"
                  style="width: 120px; margin-right: 12px;"
                />
                <el-input-number
                  v-model="category.recommended_member_limit"
                  :min="1"
                  :max="2000"
                  placeholder="推荐成员数"
                  style="width: 120px; margin-right: 12px;"
                />
              </div>
              <div class="category-actions">
                <el-button
                  size="small"
                  type="danger"
                  @click="removeCategory(index)"
                  :disabled="settings.categories.length <= 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 支付配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><CreditCard /></el-icon>
              <span>支付配置</span>
            </div>
          </template>

          <div class="payment-methods">
            <div
              v-for="method in settings.payment_methods"
              :key="method.code"
              class="payment-method-item"
            >
              <div class="method-info">
                <img :src="method.icon" :alt="method.name" class="method-icon" />
                <span class="method-name">{{ method.name }}</span>
              </div>
              <div class="method-controls">
                <el-switch
                  v-model="method.enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <el-input-number
                  v-model="method.sort_order"
                  :min="1"
                  :max="99"
                  style="width: 80px; margin-left: 12px;"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 营销功能配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Promotion /></el-icon>
              <span>营销功能配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="虚拟数据展示">
                <el-switch
                  v-model="settings.marketing.virtual_data_enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">允许使用虚拟成员数、订单数等营销数据</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="社交证明">
                <el-switch
                  v-model="settings.marketing.social_proof_enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">显示最近加入提示等社交证明信息</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="紧迫感营销">
                <el-switch
                  v-model="settings.marketing.urgency_marketing_enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">显示剩余名额、倒计时等紧迫感元素</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="限时优惠">
                <el-switch
                  v-model="settings.marketing.limited_offer_enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">允许设置限时优惠价格</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 防红系统配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Shield /></el-icon>
              <span>防红系统配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="启用防红系统">
                <el-switch
                  v-model="settings.anti_block.enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">全局启用或禁用防红系统功能</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认检测间隔">
                <el-select v-model="settings.anti_block.default_check_interval" style="width: 100%">
                  <el-option
                    v-for="interval in settings.anti_block.check_intervals"
                    :key="interval"
                    :label="`${interval}分钟`"
                    :value="interval"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="默认切换策略">
                <el-select v-model="settings.anti_block.default_switch_strategy" style="width: 100%">
                  <el-option label="立即切换" value="immediate" />
                  <el-option label="延迟切换" value="delayed" />
                  <el-option label="手动切换" value="manual" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大重试次数">
                <el-input-number
                  v-model="settings.anti_block.max_retry_times"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 分销系统配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Share /></el-icon>
              <span>分销系统配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="启用分销系统">
                <el-switch
                  v-model="settings.distribution.enabled"
                  :active-value="true"
                  :inactive-value="false"
                />
                <div class="form-tip">全局启用或禁用分销功能</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大分销层级">
                <el-input-number
                  v-model="settings.distribution.max_levels"
                  :min="1"
                  :max="5"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="默认结算方式">
                <el-select v-model="settings.distribution.default_settlement" style="width: 100%">
                  <el-option label="即时结算" value="instant" />
                  <el-option label="每日结算" value="daily" />
                  <el-option label="每周结算" value="weekly" />
                  <el-option label="每月结算" value="monthly" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大总佣金比例">
                <el-input-number
                  v-model="settings.distribution.max_total_commission_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  style="width: 100%"
                />
                <span style="margin-left: 8px;">%</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 文件上传配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Upload /></el-icon>
              <span>文件上传配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="最大文件大小">
                <el-input-number
                  v-model="settings.upload.max_file_size"
                  :min="1024"
                  :max="51200"
                  style="width: 100%"
                />
                <span style="margin-left: 8px;">KB</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图片质量">
                <el-input-number
                  v-model="settings.upload.image_quality"
                  :min="10"
                  :max="100"
                  style="width: 100%"
                />
                <span style="margin-left: 8px;">%</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="允许的图片格式">
            <el-checkbox-group v-model="settings.upload.allowed_image_types">
              <el-checkbox label="jpg">JPG</el-checkbox>
              <el-checkbox label="jpeg">JPEG</el-checkbox>
              <el-checkbox label="png">PNG</el-checkbox>
              <el-checkbox label="gif">GIF</el-checkbox>
              <el-checkbox label="webp">WebP</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>

        <!-- 通知配置 -->
        <el-card class="settings-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知配置</span>
            </div>
          </template>

          <div class="notification-settings">
            <div
              v-for="(enabled, key) in settings.notifications"
              :key="key"
              class="notification-item"
            >
              <div class="notification-info">
                <span class="notification-name">{{ getNotificationName(key) }}</span>
                <span class="notification-desc">{{ getNotificationDesc(key) }}</span>
              </div>
              <el-switch
                v-model="settings.notifications[key]"
                :active-value="true"
                :inactive-value="false"
              />
            </div>
          </div>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Grid,
  CreditCard,
  Promotion,
  Shield,
  Share,
  Upload,
  Bell,
  Plus,
  Delete,
  Check,
  RefreshLeft
} from '@element-plus/icons-vue'

// 响应式数据
const formRef = ref()
const saving = ref(false)
const resetting = ref(false)

// 配置数据
const settings = reactive({
  // 基础配置
  default_member_limit: 500,
  default_price: 0,
  default_order_expire: 30,
  default_button_title: '立即加入',
  default_features: ['city_location', 'analytics', 'generate_qr_poster'],

  // 分类配置
  categories: [
    {
      name: '技术编程',
      value: 'technology',
      color: '#409eff',
      recommended_price: 29.9,
      recommended_member_limit: 500
    },
    {
      name: '投资理财',
      value: 'finance',
      color: '#67c23a',
      recommended_price: 99.9,
      recommended_member_limit: 200
    },
    {
      name: '教育学习',
      value: 'education',
      color: '#e6a23c',
      recommended_price: 19.9,
      recommended_member_limit: 300
    }
  ],

  // 支付方式配置
  payment_methods: [
    {
      code: 'wechat',
      name: '微信支付',
      icon: '/icons/wechat-pay.png',
      enabled: true,
      sort_order: 1
    },
    {
      code: 'alipay',
      name: '支付宝',
      icon: '/icons/alipay.png',
      enabled: true,
      sort_order: 2
    },
    {
      code: 'qq',
      name: 'QQ钱包',
      icon: '/icons/qq-pay.png',
      enabled: false,
      sort_order: 3
    },
    {
      code: 'bank',
      name: '银行转账',
      icon: '/icons/bank.png',
      enabled: true,
      sort_order: 4
    }
  ],

  // 营销功能配置
  marketing: {
    virtual_data_enabled: true,
    social_proof_enabled: true,
    urgency_marketing_enabled: true,
    limited_offer_enabled: true
  },

  // 防红系统配置
  anti_block: {
    enabled: true,
    check_intervals: [5, 10, 30, 60],
    default_check_interval: 10,
    default_switch_strategy: 'immediate',
    max_retry_times: 3
  },

  // 分销系统配置
  distribution: {
    enabled: true,
    max_levels: 3,
    default_settlement: 'daily',
    max_total_commission_rate: 100
  },

  // 文件上传配置
  upload: {
    max_file_size: 5120,
    image_quality: 90,
    allowed_image_types: ['jpg', 'jpeg', 'png', 'gif', 'webp']
  },

  // 通知配置
  notifications: {
    group_created: true,
    group_published: true,
    member_joined: false,
    order_created: true,
    payment_received: true,
    domain_switched: true
  }
})

// 表单验证规则
const formRules = {
  default_member_limit: [
    { required: true, message: '请输入默认成员上限', trigger: 'blur' },
    { type: 'number', min: 1, max: 2000, message: '成员上限在1到2000之间', trigger: 'blur' }
  ],
  default_price: [
    { required: true, message: '请输入默认价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  default_order_expire: [
    { required: true, message: '请输入订单有效期', trigger: 'blur' },
    { type: 'number', min: 5, max: 1440, message: '有效期在5到1440分钟之间', trigger: 'blur' }
  ],
  default_button_title: [
    { required: true, message: '请输入默认按钮文字', trigger: 'blur' },
    { max: 20, message: '按钮文字不能超过20个字符', trigger: 'blur' }
  ]
}

// 方法
const saveSettings = async () => {
  try {
    await formRef.value.validate()
    saving.value = true

    // 这里调用API保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败，请重试')
  } finally {
    saving.value = false
  }
}

const resetToDefaults = () => {
  ElMessageBox.confirm('确定要恢复到默认配置吗？当前配置将被覆盖。', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    resetting.value = true
    
    try {
      // 这里调用API恢复默认配置
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('已恢复默认配置')
    } catch (error) {
      ElMessage.error('恢复默认配置失败')
    } finally {
      resetting.value = false
    }
  })
}

const addCategory = () => {
  settings.categories.push({
    name: '',
    value: '',
    color: '#409eff',
    recommended_price: 0,
    recommended_member_limit: 500
  })
}

const removeCategory = (index) => {
  settings.categories.splice(index, 1)
}

const getNotificationName = (key) => {
  const names = {
    group_created: '群组创建通知',
    group_published: '群组发布通知',
    member_joined: '成员加入通知',
    order_created: '订单创建通知',
    payment_received: '支付成功通知',
    domain_switched: '域名切换通知'
  }
  return names[key] || key
}

const getNotificationDesc = (key) => {
  const descriptions = {
    group_created: '当有新群组创建时发送通知',
    group_published: '当群组发布时发送通知',
    member_joined: '当有新成员加入时发送通知',
    order_created: '当有新订单创建时发送通知',
    payment_received: '当收到支付时发送通知',
    domain_switched: '当域名切换时发送通知'
  }
  return descriptions[key] || ''
}

// 生命周期
onMounted(() => {
  // 加载配置数据
  console.log('群组配置页面已加载')
})
</script>

<style lang="scss" scoped>
.group-settings-container {
  min-height: 100vh;
  background: #f5f7fa;

  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 32px;
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;

      .header-left {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }

        .page-subtitle {
          font-size: 14px;
          color: #909399;
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 32px 32px 32px;

    .settings-card {
      margin-bottom: 24px;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      :deep(.el-card__header) {
        background: #fafbfc;
        border-bottom: 1px solid #e4e7ed;
        padding: 16px 24px;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          > span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-left: 8px;
          }

          .card-actions {
            display: flex;
            gap: 8px;
          }
        }
      }

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .category-list {
      .category-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .category-info {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .category-actions {
          margin-left: 12px;
        }
      }
    }

    .payment-methods {
      .payment-method-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .method-info {
          display: flex;
          align-items: center;

          .method-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
          }

          .method-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }
        }

        .method-controls {
          display: flex;
          align-items: center;
        }
      }
    }

    .notification-settings {
      .notification-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .notification-info {
          display: flex;
          flex-direction: column;

          .notification-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }

          .notification-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .group-settings-container {
    .page-header {
      padding: 16px 20px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
      }
    }

    .settings-container {
      padding-left: 20px;
      padding-right: 20px;

      .settings-card {
        :deep(.el-card__body) {
          padding: 16px;
        }
      }

      .category-item,
      .payment-method-item,
      .notification-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }
  }
}
</style>