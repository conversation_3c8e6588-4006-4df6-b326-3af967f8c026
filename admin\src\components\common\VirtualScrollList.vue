<!-- 虚拟滚动组件 -->
<template>
  <div 
    ref="containerRef" 
    class="virtual-scroll-list" 
    :style="containerStyle"
    @scroll="handleScroll"
  >
    <!-- 滚动区域占位 -->
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <!-- 可见项目渲染 -->
      <div :style="visibleAreaStyle">
        <div
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, startIndex + index)"
          :style="getItemStyle(startIndex + index)"
          class="virtual-scroll-item"
        >
          <slot :item="item" :index="startIndex + index" />
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="virtual-scroll-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { throttle, debounce } from '@/utils/performance-optimization'

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  itemHeight: {
    type: [Number, Function],
    default: 50
  },
  containerHeight: {
    type: Number,
    default: 400
  },
  bufferSize: {
    type: Number,
    default: 5
  },
  threshold: {
    type: Number,
    default: 100
  },
  loading: {
    type: Boolean,
    default: false
  },
  keyField: {
    type: String,
    default: 'id'
  }
})

const emit = defineEmits(['scroll', 'reach-bottom', 'item-rendered'])

const containerRef = ref(null)
const scrollTop = ref(0)
const containerHeight = ref(props.containerHeight)

// 缓存项目高度
const itemHeights = ref(new Map())
const estimatedItemHeight = ref(50)

// 计算属性
const totalHeight = computed(() => {
  if (typeof props.itemHeight === 'function') {
    // 动态高度计算
    let height = 0
    for (let i = 0; i < props.items.length; i++) {
      height += getItemHeight(i)
    }
    return height
  }
  return props.items.length * props.itemHeight
})

const visibleCount = computed(() => {
  return Math.ceil(containerHeight.value / estimatedItemHeight.value) + props.bufferSize * 2
})

const startIndex = computed(() => {
  if (typeof props.itemHeight === 'function') {
    return getStartIndexForDynamicHeight()
  }
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - props.bufferSize)
})

const endIndex = computed(() => {
  return Math.min(props.items.length - 1, startIndex.value + visibleCount.value)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

const visibleAreaStyle = computed(() => {
  if (typeof props.itemHeight === 'function') {
    const offsetTop = getOffsetTopForDynamicHeight(startIndex.value)
    return {
      position: 'absolute',
      top: `${offsetTop}px`,
      left: '0',
      right: '0'
    }
  }
  
  return {
    position: 'absolute',
    top: `${startIndex.value * props.itemHeight}px`,
    left: '0',
    right: '0'
  }
})

const containerStyle = computed(() => ({
  height: `${containerHeight.value}px`,
  overflow: 'auto',
  position: 'relative'
}))

// 方法
const getItemHeight = (index) => {
  if (typeof props.itemHeight === 'function') {
    const item = props.items[index]
    const cachedHeight = itemHeights.value.get(getItemKey(item, index))
    if (cachedHeight !== undefined) {
      return cachedHeight
    }
    
    const height = props.itemHeight(item, index)
    itemHeights.value.set(getItemKey(item, index), height)
    return height
  }
  return props.itemHeight
}

const getItemKey = (item, index) => {
  if (item && typeof item === 'object' && props.keyField in item) {
    return item[props.keyField]
  }
  return index
}

const getItemStyle = (index) => ({
  position: 'absolute',
  top: '0',
  left: '0',
  right: '0',
  height: `${getItemHeight(index)}px`
})

const getStartIndexForDynamicHeight = () => {
  let accumulatedHeight = 0
  let index = 0
  
  for (let i = 0; i < props.items.length; i++) {
    const itemHeight = getItemHeight(i)
    if (accumulatedHeight + itemHeight >= scrollTop.value) {
      index = Math.max(0, i - props.bufferSize)
      break
    }
    accumulatedHeight += itemHeight
  }
  
  return index
}

const getOffsetTopForDynamicHeight = (index) => {
  let offsetTop = 0
  for (let i = 0; i < index; i++) {
    offsetTop += getItemHeight(i)
  }
  return offsetTop
}

// 节流的滚动处理
const handleScroll = throttle((event) => {
  const { scrollTop: newScrollTop, scrollHeight, clientHeight } = event.target
  
  scrollTop.value = newScrollTop
  
  // 检查是否接近底部
  const distanceToBottom = scrollHeight - clientHeight - newScrollTop
  if (distanceToBottom < props.threshold) {
    emit('reach-bottom')
  }
  
  emit('scroll', {
    scrollTop: newScrollTop,
    scrollHeight,
    clientHeight,
    startIndex: startIndex.value,
    endIndex: endIndex.value
  })
}, 16) // 60fps

// 防抖的尺寸更新
const updateContainerSize = debounce(() => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerHeight.value = rect.height
  }
}, 100)

// 滚动到指定项目
const scrollToIndex = (index, behavior = 'smooth') => {
  if (!containerRef.value || index < 0 || index >= props.items.length) return
  
  let targetScrollTop = 0
  
  if (typeof props.itemHeight === 'function') {
    targetScrollTop = getOffsetTopForDynamicHeight(index)
  } else {
    targetScrollTop = index * props.itemHeight
  }
  
  containerRef.value.scrollTo({
    top: targetScrollTop,
    behavior
  })
}

// 滚动到指定位置
const scrollTo = (scrollTop, behavior = 'smooth') => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      top: scrollTop,
      behavior
    })
  }
}

// 刷新项目高度缓存
const refreshItemHeights = () => {
  itemHeights.value.clear()
  
  nextTick(() => {
    // 重新计算可见项目的实际高度
    const itemElements = containerRef.value?.querySelectorAll('.virtual-scroll-item')
    if (itemElements) {
      itemElements.forEach((element, index) => {
        const actualIndex = startIndex.value + index
        const item = props.items[actualIndex]
        const height = element.getBoundingClientRect().height
        
        if (height > 0) {
          itemHeights.value.set(getItemKey(item, actualIndex), height)
        }
      })
    }
  })
}

// 获取缓存的统计信息
const getCacheStats = () => ({
  cachedCount: itemHeights.value.size,
  totalCount: props.items.length,
  cacheRatio: itemHeights.value.size / props.items.length
})

// 监听器
watch(() => props.items.length, () => {
  // 清理过期的缓存
  if (typeof props.itemHeight === 'function') {
    const validKeys = new Set()
    props.items.forEach((item, index) => {
      validKeys.add(getItemKey(item, index))
    })
    
    for (const key of itemHeights.value.keys()) {
      if (!validKeys.has(key)) {
        itemHeights.value.delete(key)
      }
    }
  }
})

// 性能监控
const performanceMonitor = ref({
  renderCount: 0,
  lastRenderTime: 0,
  averageRenderTime: 0
})

const trackRenderPerformance = () => {
  const startTime = performance.now()
  
  nextTick(() => {
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    performanceMonitor.value.renderCount++
    performanceMonitor.value.lastRenderTime = renderTime
    
    // 计算平均渲染时间
    const { renderCount, averageRenderTime } = performanceMonitor.value
    performanceMonitor.value.averageRenderTime = 
      (averageRenderTime * (renderCount - 1) + renderTime) / renderCount
    
    // 如果渲染时间过长，发出警告
    if (renderTime > 16) { // 60fps = 16.67ms per frame
      console.warn(`Virtual scroll render took ${renderTime.toFixed(2)}ms`, {
        visibleItems: visibleItems.value.length,
        startIndex: startIndex.value,
        endIndex: endIndex.value
      })
    }
    
    emit('item-rendered', {
      renderTime,
      visibleCount: visibleItems.value.length,
      performance: performanceMonitor.value
    })
  })
}

// 监听可见项变化并追踪性能
watch([startIndex, endIndex], trackRenderPerformance)

// ResizeObserver for better container size tracking
let resizeObserver = null

onMounted(() => {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight
    
    // 使用 ResizeObserver 监听容器大小变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          containerHeight.value = entry.contentRect.height
        }
      })
      resizeObserver.observe(containerRef.value)
    }
    
    // 如果是动态高度，初始化时刷新高度缓存
    if (typeof props.itemHeight === 'function') {
      nextTick(refreshItemHeights)
    }
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToIndex,
  scrollTo,
  refreshItemHeights,
  getCacheStats,
  getPerformanceStats: () => performanceMonitor.value
})
</script>

<style lang="scss" scoped>
.virtual-scroll-list {
  position: relative;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.04);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background 0.3s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

.virtual-scroll-item {
  width: 100%;
  box-sizing: border-box;
}

.virtual-scroll-loading {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #666;
  z-index: 10;
  
  .el-icon {
    font-size: 14px;
    color: #409eff;
  }
}

// 优化渲染性能的 CSS
.virtual-scroll-list * {
  will-change: transform;
}

.virtual-scroll-item {
  contain: layout style paint;
}
</style>