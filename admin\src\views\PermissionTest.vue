<template>
  <div class="permission-test">
    <div class="test-header">
      <h1>🔐 权限功能测试</h1>
      <p>测试不同角色的权限访问控制</p>
    </div>

    <!-- 当前用户信息 -->
    <div class="current-user-info">
      <h3>👤 当前用户信息</h3>
      <div class="user-card">
        <div class="user-avatar">
          <img :src="currentUser?.avatar || '/default-avatar.png'" :alt="currentUser?.name" />
        </div>
        <div class="user-details">
          <h4>{{ currentUser?.name || '未登录' }}</h4>
          <p>用户名: {{ currentUser?.username || '无' }}</p>
          <p>邮箱: {{ currentUser?.email || '无' }}</p>
          <p>角色: <span class="role-tag" :class="currentUser?.role">{{ currentUser?.role || '无' }}</span></p>
          <p>权限数量: {{ currentUser?.permissions?.length || 0 }}</p>
        </div>
      </div>
    </div>

    <!-- 权限测试区域 -->
    <div class="permission-tests">
      <h3>🧪 权限测试</h3>
      
      <div class="test-grid">
        <!-- 页面访问测试 -->
        <div class="test-section">
          <h4>📄 页面访问测试</h4>
          <div class="test-items">
            <div 
              v-for="page in testPages" 
              :key="page.path"
              class="test-item"
              :class="{ 
                'has-permission': hasPagePermission(page.permission),
                'no-permission': !hasPagePermission(page.permission)
              }"
            >
              <span class="test-icon">{{ page.icon }}</span>
              <span class="test-name">{{ page.name }}</span>
              <span class="test-result">
                {{ hasPagePermission(page.permission) ? '✅ 可访问' : '❌ 无权限' }}
              </span>
              <button 
                @click="testPageAccess(page)"
                :disabled="!hasPagePermission(page.permission)"
                class="test-button"
              >
                测试访问
              </button>
            </div>
          </div>
        </div>

        <!-- 功能权限测试 -->
        <div class="test-section">
          <h4>⚙️ 功能权限测试</h4>
          <div class="test-items">
            <div 
              v-for="func in testFunctions" 
              :key="func.permission"
              class="test-item"
              :class="{ 
                'has-permission': hasPermission(func.permission),
                'no-permission': !hasPermission(func.permission)
              }"
            >
              <span class="test-icon">{{ func.icon }}</span>
              <span class="test-name">{{ func.name }}</span>
              <span class="test-result">
                {{ hasPermission(func.permission) ? '✅ 有权限' : '❌ 无权限' }}
              </span>
            </div>
          </div>
        </div>

        <!-- 角色权限测试 -->
        <div class="test-section">
          <h4>🎭 角色权限测试</h4>
          <div class="test-items">
            <div 
              v-for="role in testRoles" 
              :key="role.name"
              class="test-item"
              :class="{ 
                'has-permission': hasRole(role.name),
                'no-permission': !hasRole(role.name)
              }"
            >
              <span class="test-icon">{{ role.icon }}</span>
              <span class="test-name">{{ role.label }}</span>
              <span class="test-result">
                {{ hasRole(role.name) ? '✅ 拥有角色' : '❌ 无此角色' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限切换测试 -->
    <div class="role-switch-test">
      <h3>🔄 权限切换测试</h3>
      <p>快速切换不同角色来测试权限控制</p>
      
      <div class="switch-buttons">
        <button 
          @click="switchRole('admin')"
          class="switch-button admin"
          :class="{ active: currentUser?.role === 'admin' }"
        >
          👑 管理员
        </button>
        <button 
          @click="switchRole('manager')"
          class="switch-button manager"
          :class="{ active: currentUser?.role === 'manager' }"
        >
          👔 经理
        </button>
        <button 
          @click="switchRole('user')"
          class="switch-button user"
          :class="{ active: currentUser?.role === 'user' }"
        >
          👤 普通用户
        </button>
        <button 
          @click="clearAllPermissions"
          class="switch-button clear"
        >
          🗑️ 清除权限
        </button>
      </div>
    </div>

    <!-- 测试日志 -->
    <div class="test-logs">
      <h3>📝 测试日志</h3>
      <div class="log-container">
        <div 
          v-for="(log, index) in testLogs" 
          :key="index"
          :class="['log-entry', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  getCurrentUser, 
  hasPermission, 
  hasRole, 
  setRoleAuth, 
  clearAllAuth,
  checkAuthStatus 
} from '@/utils/adminAuth'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const testLogs = ref([])

// 测试页面配置
const testPages = ref([
  { path: '/dashboard', name: '控制台', icon: '🏠', permission: 'dashboard:view' },
  { path: '/dashboard/analytics', name: '数据分析', icon: '📊', permission: 'dashboard:analytics' },
  { path: '/data-screen', name: '数据大屏', icon: '📺', permission: 'dashboard:view' },
  { path: '/community/groups', name: '群组管理', icon: '👥', permission: 'community:view' },
  { path: '/community/templates', name: '模板管理', icon: '📄', permission: 'community:view' },
  { path: '/community/add-enhanced', name: '创建群组', icon: '➕', permission: 'community:create' },
  { path: '/users', name: '用户管理', icon: '👤', permission: 'users:view' },
  { path: '/users/analytics', name: '用户分析', icon: '📈', permission: 'users:analytics' },
  { path: '/agent/list', name: '代理商管理', icon: '👔', permission: 'agent:view' },
  { path: '/agent/hierarchy', name: '代理商层级', icon: '🏗️', permission: 'agent:view' },
  { path: '/distribution/distributors', name: '分销管理', icon: '🔗', permission: 'distribution:view' },
  { path: '/promotion/links', name: '推广管理', icon: '📢', permission: 'promotion:view' },
  { path: '/anti-block/dashboard', name: '防红系统', icon: '🛡️', permission: 'antiblock:view' },
  { path: '/orders', name: '订单管理', icon: '🛒', permission: 'orders:view' },
  { path: '/finance', name: '财务管理', icon: '💰', permission: 'finance:view' },
  { path: '/payment/settings', name: '支付设置', icon: '💳', permission: 'payment:view' },
  { path: '/permission/roles', name: '权限管理', icon: '🔐', permission: 'permission:view' },
  { path: '/system/settings', name: '系统设置', icon: '⚙️', permission: 'system:settings' },
  { path: '/system/monitor', name: '系统监控', icon: '📡', permission: 'system:monitor' }
])

// 测试功能配置
const testFunctions = ref([
  { name: '创建群组', icon: '➕', permission: 'community:create' },
  { name: '编辑群组', icon: '✏️', permission: 'community:edit' },
  { name: '删除群组', icon: '🗑️', permission: 'community:delete' },
  { name: '创建用户', icon: '👤➕', permission: 'users:create' },
  { name: '编辑用户', icon: '👤✏️', permission: 'users:edit' },
  { name: '删除用户', icon: '👤🗑️', permission: 'users:delete' },
  { name: '查看财务分析', icon: '💹', permission: 'finance:analytics' },
  { name: '查看系统日志', icon: '📋', permission: 'system:logs' }
])

// 测试角色配置
const testRoles = ref([
  { name: 'admin', label: '管理员', icon: '👑' },
  { name: 'manager', label: '经理', icon: '👔' },
  { name: 'user', label: '普通用户', icon: '👤' }
])

// 计算属性
const hasPagePermission = (permission) => {
  return hasPermission(permission)
}

// 方法
const updateCurrentUser = () => {
  currentUser.value = getCurrentUser()
  addLog('info', `用户信息已更新: ${currentUser.value?.name || '未登录'}`)
}

const testPageAccess = async (page) => {
  addLog('info', `测试访问页面: ${page.name}`)
  
  try {
    await router.push(page.path)
    addLog('success', `成功访问页面: ${page.name}`)
    ElMessage.success(`成功访问: ${page.name}`)
  } catch (error) {
    addLog('error', `访问页面失败: ${page.name} - ${error.message}`)
    ElMessage.error(`访问失败: ${page.name}`)
  }
}

const switchRole = async (role) => {
  addLog('info', `切换到角色: ${role}`)
  
  try {
    const success = setRoleAuth(role)
    if (success) {
      updateCurrentUser()
      addLog('success', `成功切换到角色: ${role}`)
      ElMessage.success(`已切换到: ${role}`)
    } else {
      addLog('error', `切换角色失败: ${role}`)
      ElMessage.error(`切换角色失败`)
    }
  } catch (error) {
    addLog('error', `切换角色错误: ${error.message}`)
    ElMessage.error('切换角色时发生错误')
  }
}

const clearAllPermissions = () => {
  addLog('info', '清除所有权限')
  
  try {
    const success = clearAllAuth()
    if (success) {
      updateCurrentUser()
      addLog('success', '所有权限已清除')
      ElMessage.success('权限已清除')
    } else {
      addLog('error', '清除权限失败')
      ElMessage.error('清除权限失败')
    }
  } catch (error) {
    addLog('error', `清除权限错误: ${error.message}`)
    ElMessage.error('清除权限时发生错误')
  }
}

const addLog = (type, message) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  testLogs.value.unshift({
    type,
    time,
    message
  })
  
  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 生命周期
onMounted(() => {
  updateCurrentUser()
  addLog('info', '权限测试页面已加载')
})
</script>

<style scoped>
.permission-test {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.test-header p {
  color: #6b7280;
}

.current-user-info {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.current-user-info h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
}

.user-details p {
  margin: 4px 0;
  color: #6b7280;
  font-size: 14px;
}

.role-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.role-tag.admin {
  background: #dc2626;
}

.role-tag.manager {
  background: #059669;
}

.role-tag.user {
  background: #3b82f6;
}

.permission-tests {
  margin-bottom: 32px;
}

.permission-tests h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.test-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.test-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-item {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.test-item.has-permission {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.test-item.no-permission {
  background: #fef2f2;
  border-color: #fecaca;
}

.test-icon {
  font-size: 18px;
}

.test-name {
  font-weight: 500;
  color: #374151;
}

.test-result {
  font-size: 12px;
  font-weight: 600;
}

.test-button {
  padding: 4px 12px;
  border: none;
  border-radius: 6px;
  background: #3b82f6;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.test-button:hover:not(:disabled) {
  background: #2563eb;
}

.test-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.role-switch-test {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-switch-test h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
}

.role-switch-test p {
  margin: 0 0 16px 0;
  color: #6b7280;
}

.switch-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.switch-button {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.switch-button.admin {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.switch-button.manager {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.switch-button.user {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.switch-button.clear {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.switch-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.switch-button.active {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.test-logs {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-logs h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  margin-bottom: 4px;
  align-items: flex-start;
}

.log-time {
  color: #9ca3af;
  margin-right: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-entry.info .log-message {
  color: #60a5fa;
}

.log-entry.success .log-message {
  color: #34d399;
}

.log-entry.error .log-message {
  color: #f87171;
}

@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
  }
  
  .switch-buttons {
    flex-direction: column;
  }
  
  .test-item {
    grid-template-columns: auto 1fr;
    gap: 8px;
  }
  
  .test-result,
  .test-button {
    grid-column: 1 / -1;
    justify-self: start;
  }
}
</style>
