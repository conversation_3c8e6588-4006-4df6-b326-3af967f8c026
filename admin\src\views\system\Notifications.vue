<template>
  <div class="app-container">
    <!-- 通知统计概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon total-icon">
              <i class="el-icon-bell"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ notificationStats.total }}</div>
              <div class="stat-label">总通知数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon unread-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ notificationStats.unread }}</div>
              <div class="stat-label">未读通知</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today-icon">
              <i class="el-icon-calendar-today"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ notificationStats.today }}</div>
              <div class="stat-label">今日发送</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon rate-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ notificationStats.readRate }}%</div>
              <div class="stat-label">阅读率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>⚡ 快速操作</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" @click="showSendNotificationDialog" class="quick-action-btn">
            <i class="el-icon-s-promotion"></i>
            发送系统通知
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button type="success" @click="showBulkNotificationDialog" class="quick-action-btn">
            <i class="el-icon-user-solid"></i>
            批量用户通知
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button type="warning" @click="showTemplateDialog" class="quick-action-btn">
            <i class="el-icon-document"></i>
            创建消息模板
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button type="info" @click="showScheduleDialog" class="quick-action-btn">
            <i class="el-icon-alarm-clock"></i>
            定时通知
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 通知列表 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>📋 通知列表</span>
          <div>
            <el-select v-model="queryParams.type" placeholder="通知类型" clearable style="width: 120px; margin-right: 10px">
              <el-option label="系统通知" value="system" />
              <el-option label="用户通知" value="user" />
              <el-option label="营销通知" value="marketing" />
              <el-option label="安全通知" value="security" />
            </el-select>
            <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 100px; margin-right: 10px">
              <el-option label="已发送" value="sent" />
              <el-option label="待发送" value="pending" />
              <el-option label="发送失败" value="failed" />
            </el-select>
            <el-button type="primary" @click="refreshNotifications">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="notifications" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getNotificationTypeColor(scope.row.type)">
              {{ getNotificationTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="target" label="目标" width="120">
          <template #default="scope">
            <span v-if="scope.row.target === 'all'">全部用户</span>
            <span v-else-if="scope.row.target === 'group'">用户组</span>
            <span v-else>指定用户</span>
          </template>
        </el-table-column>
        <el-table-column prop="send_count" label="发送数" width="100" />
        <el-table-column prop="read_count" label="已读数" width="100" />
        <el-table-column prop="read_rate" label="阅读率" width="100">
          <template #default="scope">
            {{ scope.row.read_rate }}%
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewNotification(scope.row)">
              查看
            </el-button>
            <el-button type="info" size="small" @click="viewStats(scope.row)">
              统计
            </el-button>
            <el-button type="warning" size="small" @click="resendNotification(scope.row)" v-if="scope.row.status === 'failed'">
              重发
            </el-button>
            <el-button type="danger" size="small" @click="deleteNotification(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.per_page"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 消息模板管理 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>📄 消息模板</span>
          <el-button type="primary" @click="showTemplateDialog">新增模板</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="template in templates" :key="template.id">
          <div class="template-card">
            <div class="template-header">
              <div class="template-title">{{ template.name }}</div>
              <div class="template-actions">
                <el-button type="text" @click="editTemplate(template)">编辑</el-button>
                <el-button type="text" @click="useTemplate(template)">使用</el-button>
                <el-button type="text" @click="deleteTemplate(template)">删除</el-button>
              </div>
            </div>
            <div class="template-content">
              <div class="template-type">{{ getNotificationTypeName(template.type) }}</div>
              <div class="template-preview">{{ template.content }}</div>
              <div class="template-stats">
                <span class="stat-item">使用次数: {{ template.usage_count }}</span>
                <span class="stat-item">创建时间: {{ template.created_at }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 发送通知对话框 -->
    <el-dialog title="发送通知" v-model="sendDialog.visible" width="800px">
      <el-form :model="sendForm" :rules="sendRules" ref="sendFormRef" label-width="100px">
        <el-form-item label="通知类型" prop="type">
          <el-select v-model="sendForm.type" placeholder="请选择通知类型">
            <el-option label="系统通知" value="system" />
            <el-option label="用户通知" value="user" />
            <el-option label="营销通知" value="marketing" />
            <el-option label="安全通知" value="security" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="发送目标" prop="target">
          <el-radio-group v-model="sendForm.target">
            <el-radio label="all">全部用户</el-radio>
            <el-radio label="group">用户组</el-radio>
            <el-radio label="specific">指定用户</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="用户组" v-if="sendForm.target === 'group'">
          <el-select v-model="sendForm.group_id" placeholder="请选择用户组">
            <el-option label="VIP用户" value="vip" />
            <el-option label="分销商" value="distributor" />
            <el-option label="分站管理员" value="substation" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="指定用户" v-if="sendForm.target === 'specific'">
          <el-select v-model="sendForm.user_ids" multiple placeholder="请选择用户">
            <el-option v-for="user in users" :key="user.id" :label="user.nickname" :value="user.id" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="sendForm.title" placeholder="请输入通知标题" />
        </el-form-item>
        
        <el-form-item label="通知内容" prop="content">
          <el-input type="textarea" v-model="sendForm.content" :rows="4" placeholder="请输入通知内容" />
        </el-form-item>
        
        <el-form-item label="发送方式">
          <el-checkbox-group v-model="sendForm.channels">
            <el-checkbox label="web">站内信</el-checkbox>
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="wechat">微信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="发送时间">
          <el-radio-group v-model="sendForm.send_type">
            <el-radio label="now">立即发送</el-radio>
            <el-radio label="scheduled">定时发送</el-radio>
          </el-radio-group>
          <el-date-picker
            v-if="sendForm.send_type === 'scheduled'"
            v-model="sendForm.scheduled_at"
            type="datetime"
            placeholder="选择发送时间"
            style="margin-left: 10px"
          />
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="sendForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="普通" value="normal" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="sendDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="sendNotification" :loading="sending">
            {{ sendForm.send_type === 'now' ? '立即发送' : '定时发送' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 消息模板对话框 -->
    <el-dialog :title="templateDialog.title" v-model="templateDialog.visible" width="600px">
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="模板类型" prop="type">
          <el-select v-model="templateForm.type" placeholder="请选择模板类型">
            <el-option label="系统通知" value="system" />
            <el-option label="用户通知" value="user" />
            <el-option label="营销通知" value="marketing" />
            <el-option label="安全通知" value="security" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模板内容" prop="content">
          <el-input type="textarea" v-model="templateForm.content" :rows="6" placeholder="请输入模板内容，支持变量：{username}, {amount}, {time}等" />
        </el-form-item>
        
        <el-form-item label="变量说明">
          <el-input type="textarea" v-model="templateForm.variables" :rows="3" placeholder="请输入变量说明，如：{username}=用户名，{amount}=金额" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate" :loading="templateSaving">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 统计详情对话框 -->
    <el-dialog title="通知统计" v-model="statsDialog.visible" width="800px">
      <div class="stats-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stats-card">
              <div class="stats-number">{{ statsData.send_count }}</div>
              <div class="stats-label">发送数量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stats-card">
              <div class="stats-number">{{ statsData.read_count }}</div>
              <div class="stats-label">已读数量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stats-card">
              <div class="stats-number">{{ statsData.click_count }}</div>
              <div class="stats-label">点击数量</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="stats-chart">
          <v-chart class="chart" :option="statsChartOptions" autoresize />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import VChart from "vue-echarts";

const notificationStats = reactive({
  total: 1245,
  unread: 89,
  today: 56,
  readRate: 78
})

const queryParams = reactive({
  page: 1,
  per_page: 10,
  type: '',
  status: ''
})

const notifications = ref([
  {
    id: 1,
    title: '系统维护通知',
    type: 'system',
    target: 'all',
    send_count: 1200,
    read_count: 980,
    read_rate: 82,
    status: 'sent',
    created_at: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    title: '新功能上线通知',
    type: 'user',
    target: 'group',
    send_count: 800,
    read_count: 650,
    read_rate: 81,
    status: 'sent',
    created_at: '2024-01-01 09:00:00'
  }
])

const templates = ref([
  {
    id: 1,
    name: '欢迎新用户',
    type: 'user',
    content: '欢迎 {username} 加入我们的平台！',
    usage_count: 120,
    created_at: '2024-01-01'
  },
  {
    id: 2,
    name: '提现成功通知',
    type: 'user',
    content: '您的提现申请已成功处理，金额：{amount}',
    usage_count: 85,
    created_at: '2024-01-01'
  }
])

const users = ref([
  { id: 1, nickname: '用户1' },
  { id: 2, nickname: '用户2' }
])

const sendDialog = reactive({
  visible: false
})

const sendForm = reactive({
  type: 'system',
  target: 'all',
  group_id: '',
  user_ids: [],
  title: '',
  content: '',
  channels: ['web'],
  send_type: 'now',
  scheduled_at: null,
  priority: 'normal'
})

const sendRules = {
  type: [{ required: true, message: '请选择通知类型', trigger: 'change' }],
  target: [{ required: true, message: '请选择发送目标', trigger: 'change' }],
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }]
}

const templateDialog = reactive({
  visible: false,
  title: '新增模板'
})

const templateForm = reactive({
  name: '',
  type: 'user',
  content: '',
  variables: ''
})

const templateRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }]
}

const statsDialog = reactive({
  visible: false
})

const statsData = reactive({
  send_count: 0,
  read_count: 0,
  click_count: 0
})

const statsChartOptions = ref({})

const total = ref(0)
const sending = ref(false)
const templateSaving = ref(false)

// 辅助函数
const getNotificationTypeColor = (type) => {
  const colors = {
    system: 'primary',
    user: 'success',
    marketing: 'warning',
    security: 'danger'
  }
  return colors[type] || 'info'
}

const getNotificationTypeName = (type) => {
  const names = {
    system: '系统通知',
    user: '用户通知',
    marketing: '营销通知',
    security: '安全通知'
  }
  return names[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    sent: 'success',
    pending: 'warning',
    failed: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusName = (status) => {
  const names = {
    sent: '已发送',
    pending: '待发送',
    failed: '发送失败'
  }
  return names[status] || status
}

// 页面操作
const showSendNotificationDialog = () => {
  resetSendForm()
  sendDialog.visible = true
}

const showBulkNotificationDialog = () => {
  resetSendForm()
  sendForm.target = 'group'
  sendDialog.visible = true
}

const showTemplateDialog = () => {
  resetTemplateForm()
  templateDialog.title = '新增模板'
  templateDialog.visible = true
}

const showScheduleDialog = () => {
  resetSendForm()
  sendForm.send_type = 'scheduled'
  sendDialog.visible = true
}

// 表单重置
const resetSendForm = () => {
  Object.assign(sendForm, {
    type: 'system',
    target: 'all',
    group_id: '',
    user_ids: [],
    title: '',
    content: '',
    channels: ['web'],
    send_type: 'now',
    scheduled_at: null,
    priority: 'normal'
  })
}

const resetTemplateForm = () => {
  Object.assign(templateForm, {
    name: '',
    type: 'user',
    content: '',
    variables: ''
  })
}

// 发送通知
const sendNotification = async () => {
  // 表单验证
  sending.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('通知发送成功')
    sendDialog.visible = false
    refreshNotifications()
  } catch (error) {
    ElMessage.error('通知发送失败')
  } finally {
    sending.value = false
  }
}

// 保存模板
const saveTemplate = async () => {
  templateSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('模板保存成功')
    templateDialog.visible = false
    // 刷新模板列表
  } catch (error) {
    ElMessage.error('模板保存失败')
  } finally {
    templateSaving.value = false
  }
}

// 通知操作
const viewNotification = (notification) => {
  ElMessage.info(`查看通知：${notification.title}`)
}

const viewStats = (notification) => {
  statsData.send_count = notification.send_count
  statsData.read_count = notification.read_count
  statsData.click_count = Math.floor(notification.read_count * 0.3)
  
  // 更新图表
  statsChartOptions.value = {
    title: {
      text: '通知统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '通知统计',
        type: 'pie',
        radius: '50%',
        data: [
          { value: statsData.read_count, name: '已读' },
          { value: statsData.send_count - statsData.read_count, name: '未读' }
        ]
      }
    ]
  }
  
  statsDialog.visible = true
}

const resendNotification = (notification) => {
  ElMessage.info(`重新发送：${notification.title}`)
}

const deleteNotification = (notification) => {
  ElMessageBox.confirm(`确定要删除通知 ${notification.title} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    refreshNotifications()
  })
}

// 模板操作
const editTemplate = (template) => {
  Object.assign(templateForm, template)
  templateDialog.title = '编辑模板'
  templateDialog.visible = true
}

const useTemplate = (template) => {
  sendForm.content = template.content
  sendForm.type = template.type
  sendDialog.visible = true
}

const deleteTemplate = (template) => {
  ElMessageBox.confirm(`确定要删除模板 ${template.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

// 列表操作
const refreshNotifications = () => {
  ElMessage.success('通知列表已刷新')
}

const handleSelectionChange = (selection) => {
  // 批量操作
}

const handleSizeChange = (size) => {
  queryParams.per_page = size
  refreshNotifications()
}

const handleCurrentChange = (page) => {
  queryParams.page = page
  refreshNotifications()
}

onMounted(() => {
  total.value = notifications.value.length
})
</script>

<style scoped>
.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.total-icon { background: #409EFF; }
.unread-icon { background: #E6A23C; }
.today-icon { background: #67C23A; }
.rate-icon { background: #F56C6C; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-action-btn {
  width: 100%;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.quick-action-btn i {
  font-size: 20px;
  margin-bottom: 5px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.template-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.template-content {
  color: #606266;
}

.template-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.template-preview {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.template-stats {
  font-size: 12px;
  color: #909399;
}

.template-stats .stat-item {
  margin-right: 15px;
}

.table-pagination {
  margin-top: 20px;
  text-align: center;
}

.stats-container {
  padding: 20px;
}

.stats-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-chart {
  margin-top: 20px;
}

.chart {
  height: 300px;
}
</style> 