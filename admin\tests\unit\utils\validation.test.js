/**
 * Validation Utility Functions Unit Tests
 * 测试表单验证相关工具函数
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// 模拟验证工具函数
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') return false
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim())
}

const validatePhone = (phone) => {
  if (!phone || typeof phone !== 'string') return false
  
  // 支持中国手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  const cleanPhone = phone.replace(/[\s-]/g, '')
  return phoneRegex.test(cleanPhone)
}

const validatePassword = (password) => {
  if (!password || typeof password !== 'string') {
    return {
      isValid: false,
      errors: ['密码不能为空']
    }
  }
  
  const errors = []
  
  if (password.length < 6) {
    errors.push('密码长度至少6位')
  }
  
  if (password.length > 128) {
    errors.push('密码长度不能超过128位')
  }
  
  if (!/[a-zA-Z]/.test(password)) {
    errors.push('密码必须包含字母')
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('密码必须包含数字')
  }
  
  // 检查不安全字符
  if (/[<>'"&]/.test(password)) {
    errors.push('密码包含不安全字符')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

const validateUsername = (username) => {
  if (!username || typeof username !== 'string') {
    return {
      isValid: false,
      errors: ['用户名不能为空']
    }
  }
  
  const errors = []
  const trimmedUsername = username.trim()
  
  if (trimmedUsername.length < 3) {
    errors.push('用户名长度至少3位')
  }
  
  if (trimmedUsername.length > 50) {
    errors.push('用户名长度不能超过50位')
  }
  
  // 如果包含@符号，验证邮箱格式
  if (trimmedUsername.includes('@')) {
    if (!validateEmail(trimmedUsername)) {
      errors.push('邮箱格式不正确')
    }
  } else {
    // 验证用户名格式（只允许字母、数字、下划线、中文）
    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(trimmedUsername)) {
      errors.push('用户名只能包含字母、数字、下划线或中文')
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    cleaned: trimmedUsername
  }
}

const validateUrl = (url) => {
  if (!url || typeof url !== 'string') return false
  
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

const validateIdCard = (idCard) => {
  if (!idCard || typeof idCard !== 'string') return false
  
  // 简单的18位身份证验证
  const idCardRegex = /^\d{17}[\dXx]$/
  return idCardRegex.test(idCard)
}

const validateBankCard = (bankCard) => {
  if (!bankCard || typeof bankCard !== 'string') return false
  
  const cleanCard = bankCard.replace(/[\s-]/g, '')
  
  // 银行卡号长度通常在13-19位之间
  if (!/^\d{13,19}$/.test(cleanCard)) return false
  
  // Luhn算法验证
  let sum = 0
  let isEven = false
  
  for (let i = cleanCard.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanCard[i])
    
    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    isEven = !isEven
  }
  
  return sum % 10 === 0
}

const validateAmount = (amount, options = {}) => {
  const {
    min = 0,
    max = Number.MAX_SAFE_INTEGER,
    decimals = 2,
    allowNegative = false
  } = options
  
  if (amount === null || amount === undefined || amount === '') {
    return {
      isValid: false,
      errors: ['金额不能为空']
    }
  }
  
  const errors = []
  let numAmount
  
  if (typeof amount === 'string') {
    numAmount = parseFloat(amount)
  } else if (typeof amount === 'number') {
    numAmount = amount
  } else {
    return {
      isValid: false,
      errors: ['金额格式不正确']
    }
  }
  
  if (isNaN(numAmount)) {
    errors.push('金额必须是数字')
  } else {
    if (!allowNegative && numAmount < 0) {
      errors.push('金额不能为负数')
    }
    
    if (numAmount < min) {
      errors.push(`金额不能小于${min}`)
    }
    
    if (numAmount > max) {
      errors.push(`金额不能大于${max}`)
    }
    
    // 检查小数位数
    const decimalPart = numAmount.toString().split('.')[1]
    if (decimalPart && decimalPart.length > decimals) {
      errors.push(`金额最多${decimals}位小数`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    value: numAmount
  }
}

const validateRequired = (value, fieldName = '字段') => {
  if (value === null || value === undefined) {
    return {
      isValid: false,
      error: `${fieldName}不能为空`
    }
  }
  
  if (typeof value === 'string' && value.trim() === '') {
    return {
      isValid: false,
      error: `${fieldName}不能为空`
    }
  }
  
  if (Array.isArray(value) && value.length === 0) {
    return {
      isValid: false,
      error: `${fieldName}不能为空`
    }
  }
  
  return { isValid: true }
}

const validateLength = (value, min, max, fieldName = '字段') => {
  if (!value) return { isValid: false, error: `${fieldName}不能为空` }
  
  const length = typeof value === 'string' ? value.length : String(value).length
  const errors = []
  
  if (min !== undefined && length < min) {
    errors.push(`${fieldName}长度至少${min}位`)
  }
  
  if (max !== undefined && length > max) {
    errors.push(`${fieldName}长度不能超过${max}位`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

const validateDate = (date, options = {}) => {
  const {
    minDate,
    maxDate,
    format = 'YYYY-MM-DD'
  } = options
  
  if (!date) {
    return {
      isValid: false,
      error: '日期不能为空'
    }
  }
  
  let dateObj
  
  if (date instanceof Date) {
    dateObj = date
  } else if (typeof date === 'string') {
    dateObj = new Date(date)
  } else {
    return {
      isValid: false,
      error: '日期格式不正确'
    }
  }
  
  if (isNaN(dateObj.getTime())) {
    return {
      isValid: false,
      error: '日期格式不正确'
    }
  }
  
  const errors = []
  
  if (minDate) {
    const minDateObj = new Date(minDate)
    if (dateObj < minDateObj) {
      errors.push(`日期不能早于${minDate}`)
    }
  }
  
  if (maxDate) {
    const maxDateObj = new Date(maxDate)
    if (dateObj > maxDateObj) {
      errors.push(`日期不能晚于${maxDate}`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    date: dateObj
  }
}

describe('Validation Utility Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('validateEmail', () => {
    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]
      
      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true)
      })
    })

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        '',
        'invalid-email',
        '@domain.com',
        'user@',
        'user@@domain.com',
        'user@domain',
        'user <EMAIL>',
        '<EMAIL>'
      ]
      
      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false)
      })
    })

    it('should handle non-string inputs', () => {
      expect(validateEmail(null)).toBe(false)
      expect(validateEmail(undefined)).toBe(false)
      expect(validateEmail(123)).toBe(false)
      expect(validateEmail({})).toBe(false)
      expect(validateEmail([])).toBe(false)
    })

    it('should handle whitespace', () => {
      expect(validateEmail(' <EMAIL> ')).toBe(true)
      expect(validateEmail('   ')).toBe(false)
    })
  })

  describe('validatePhone', () => {
    it('should validate correct Chinese phone numbers', () => {
      const validPhones = [
        '13812345678',
        '15987654321',
        '18888888888',
        '19123456789',
        '138-1234-5678',
        '138 1234 5678'
      ]
      
      validPhones.forEach(phone => {
        expect(validatePhone(phone)).toBe(true)
      })
    })

    it('should reject invalid phone numbers', () => {
      const invalidPhones = [
        '',
        '12812345678', // 不是1开头
        '1381234567',  // 少一位
        '138123456789', // 多一位
        '10812345678',  // 第二位不符合
        'abc12345678',  // 包含字母
        '138-1234-567'  // 格式错误
      ]
      
      invalidPhones.forEach(phone => {
        expect(validatePhone(phone)).toBe(false)
      })
    })

    it('should handle non-string inputs', () => {
      expect(validatePhone(null)).toBe(false)
      expect(validatePhone(undefined)).toBe(false)
      expect(validatePhone(13812345678)).toBe(false)
    })
  })

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const strongPasswords = [
        'password123',
        'MyPassword1',
        'strongPass99',
        'Test123456'
      ]
      
      strongPasswords.forEach(password => {
        const result = validatePassword(password)
        expect(result.isValid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })
    })

    it('should reject passwords that are too short', () => {
      const result = validatePassword('12345')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('密码长度至少6位')
    })

    it('should reject passwords that are too long', () => {
      const longPassword = 'a'.repeat(129) + '1'
      const result = validatePassword(longPassword)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('密码长度不能超过128位')
    })

    it('should reject passwords without letters', () => {
      const result = validatePassword('123456')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('密码必须包含字母')
    })

    it('should reject passwords without numbers', () => {
      const result = validatePassword('password')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('密码必须包含数字')
    })

    it('should reject passwords with unsafe characters', () => {
      const unsafePasswords = [
        'password<123',
        'test>456',
        'mypass"789',
        "test'123",
        'pass&word1'
      ]
      
      unsafePasswords.forEach(password => {
        const result = validatePassword(password)
        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('密码包含不安全字符')
      })
    })

    it('should handle null/undefined passwords', () => {
      const nullResult = validatePassword(null)
      expect(nullResult.isValid).toBe(false)
      expect(nullResult.errors).toContain('密码不能为空')

      const undefinedResult = validatePassword(undefined)
      expect(undefinedResult.isValid).toBe(false)
      expect(undefinedResult.errors).toContain('密码不能为空')
    })

    it('should return multiple errors', () => {
      const result = validatePassword('abc') // 短且无数字
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('密码长度至少6位')
      expect(result.errors).toContain('密码必须包含数字')
    })
  })

  describe('validateUsername', () => {
    it('should validate correct usernames', () => {
      const validUsernames = [
        'testuser',
        'user123',
        'test_user',
        '用户名123',
        '<EMAIL>'
      ]
      
      validUsernames.forEach(username => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })
    })

    it('should validate email format usernames', () => {
      const result = validateUsername('<EMAIL>')
      expect(result.isValid).toBe(true)
    })

    it('should reject invalid email format usernames', () => {
      const result = validateUsername('invalid@email')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('邮箱格式不正确')
    })

    it('should reject usernames with invalid characters', () => {
      const invalidUsernames = [
        'user-name',
        'user.name',
        'user name',
        'user@domain',
        'user#123'
      ]
      
      invalidUsernames.forEach(username => {
        const result = validateUsername(username)
        if (!username.includes('@')) { // 非邮箱格式才检查字符
          expect(result.isValid).toBe(false)
        }
      })
    })

    it('should handle length validation', () => {
      const shortResult = validateUsername('ab')
      expect(shortResult.isValid).toBe(false)
      expect(shortResult.errors).toContain('用户名长度至少3位')

      const longResult = validateUsername('a'.repeat(51))
      expect(longResult.isValid).toBe(false)
      expect(longResult.errors).toContain('用户名长度不能超过50位')
    })

    it('should trim whitespace', () => {
      const result = validateUsername('  testuser  ')
      expect(result.isValid).toBe(true)
      expect(result.cleaned).toBe('testuser')
    })

    it('should handle null/undefined usernames', () => {
      const nullResult = validateUsername(null)
      expect(nullResult.isValid).toBe(false)
      expect(nullResult.errors).toContain('用户名不能为空')
    })
  })

  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      const validUrls = [
        'https://www.example.com',
        'http://example.com',
        'https://subdomain.example.com/path',
        'https://example.com:8080/path?query=value',
        'ftp://example.com/file.txt'
      ]
      
      validUrls.forEach(url => {
        expect(validateUrl(url)).toBe(true)
      })
    })

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        '',
        'not-a-url',
        'www.example.com',
        '//example.com',
        'example.com',
        'http://.',
        'http://.com'
      ]
      
      invalidUrls.forEach(url => {
        expect(validateUrl(url)).toBe(false)
      })
    })

    it('should handle non-string inputs', () => {
      expect(validateUrl(null)).toBe(false)
      expect(validateUrl(undefined)).toBe(false)
      expect(validateUrl(123)).toBe(false)
    })
  })

  describe('validateIdCard', () => {
    it('should validate correct ID card numbers', () => {
      const validIds = [
        '************345678',
        '************34567X',
        '************34567x'
      ]
      
      validIds.forEach(id => {
        expect(validateIdCard(id)).toBe(true)
      })
    })

    it('should reject invalid ID card numbers', () => {
      const invalidIds = [
        '',
        '************34567',  // 17位
        '************3456789', // 19位
        '************34567A', // 无效字符
        'abcdefghijk1234567X'  // 包含字母
      ]
      
      invalidIds.forEach(id => {
        expect(validateIdCard(id)).toBe(false)
      })
    })
  })

  describe('validateBankCard', () => {
    it('should validate correct bank card numbers', () => {
      const validCards = [
        '****************', // Visa测试号
        '****************', // MasterCard测试号
        '****************', // Discover测试号
        '4111-1111-1111-1111', // 带分隔符
        '4111 1111 1111 1111'  // 带空格
      ]
      
      validCards.forEach(card => {
        expect(validateBankCard(card)).toBe(true)
      })
    })

    it('should reject invalid bank card numbers', () => {
      const invalidCards = [
        '',
        '************', // 太短
        '************34567890', // 太长
        '****************', // Luhn算法验证失败
        'abcd1111111111111', // 包含字母
      ]
      
      invalidCards.forEach(card => {
        expect(validateBankCard(card)).toBe(false)
      })
    })
  })

  describe('validateAmount', () => {
    it('should validate correct amounts', () => {
      const validAmounts = [
        100,
        100.50,
        '99.99',
        '1000',
        0
      ]
      
      validAmounts.forEach(amount => {
        const result = validateAmount(amount)
        expect(result.isValid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })
    })

    it('should validate with custom options', () => {
      const result = validateAmount(50, { min: 10, max: 1000 })
      expect(result.isValid).toBe(true)

      const negativeResult = validateAmount(-10, { allowNegative: true })
      expect(negativeResult.isValid).toBe(true)
    })

    it('should reject amounts outside range', () => {
      const minResult = validateAmount(5, { min: 10 })
      expect(minResult.isValid).toBe(false)
      expect(minResult.errors).toContain('金额不能小于10')

      const maxResult = validateAmount(2000, { max: 1000 })
      expect(maxResult.isValid).toBe(false)
      expect(maxResult.errors).toContain('金额不能大于1000')
    })

    it('should reject negative amounts by default', () => {
      const result = validateAmount(-10)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('金额不能为负数')
    })

    it('should validate decimal places', () => {
      const result = validateAmount(100.123, { decimals: 2 })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('金额最多2位小数')
    })

    it('should handle invalid amount formats', () => {
      const invalidAmounts = [null, undefined, '', 'abc', {}, []]
      
      invalidAmounts.forEach(amount => {
        const result = validateAmount(amount)
        expect(result.isValid).toBe(false)
      })
    })
  })

  describe('validateRequired', () => {
    it('should validate non-empty values', () => {
      const validValues = [
        'text',
        123,
        true,
        false,
        [1, 2, 3],
        { key: 'value' },
        0
      ]
      
      validValues.forEach(value => {
        const result = validateRequired(value)
        expect(result.isValid).toBe(true)
      })
    })

    it('should reject empty values', () => {
      const emptyValues = [
        null,
        undefined,
        '',
        '   ',
        []
      ]
      
      emptyValues.forEach(value => {
        const result = validateRequired(value)
        expect(result.isValid).toBe(false)
      })
    })

    it('should use custom field name', () => {
      const result = validateRequired(null, '用户名')
      expect(result.error).toContain('用户名不能为空')
    })
  })

  describe('validateLength', () => {
    it('should validate correct length', () => {
      const result = validateLength('hello', 3, 10)
      expect(result.isValid).toBe(true)
    })

    it('should reject too short values', () => {
      const result = validateLength('ab', 3, 10)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('字段长度至少3位')
    })

    it('should reject too long values', () => {
      const result = validateLength('very long text', 1, 5)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('字段长度不能超过5位')
    })

    it('should handle numbers', () => {
      const result = validateLength(12345, 3, 10)
      expect(result.isValid).toBe(true)
    })

    it('should use custom field name', () => {
      const result = validateLength('ab', 3, 10, '密码')
      expect(result.errors[0]).toContain('密码长度至少3位')
    })
  })

  describe('validateDate', () => {
    it('should validate correct dates', () => {
      const validDates = [
        new Date(),
        '2024-01-15',
        '2024/01/15',
        '2024-01-15T10:30:00Z'
      ]
      
      validDates.forEach(date => {
        const result = validateDate(date)
        expect(result.isValid).toBe(true)
      })
    })

    it('should reject invalid dates', () => {
      const invalidDates = [
        '',
        'invalid-date',
        '2024-13-01', // 无效月份
        '2024-01-32'  // 无效日期
      ]
      
      invalidDates.forEach(date => {
        const result = validateDate(date)
        expect(result.isValid).toBe(false)
      })
    })

    it('should validate date range', () => {
      const minResult = validateDate('2024-01-01', {
        minDate: '2024-01-15'
      })
      expect(minResult.isValid).toBe(false)
      expect(minResult.errors).toContain('日期不能早于2024-01-15')

      const maxResult = validateDate('2024-12-31', {
        maxDate: '2024-06-30'
      })
      expect(maxResult.isValid).toBe(false)
      expect(maxResult.errors).toContain('日期不能晚于2024-06-30')
    })

    it('should return parsed date object', () => {
      const result = validateDate('2024-01-15')
      expect(result.isValid).toBe(true)
      expect(result.date).toBeInstanceOf(Date)
      expect(result.date.getFullYear()).toBe(2024)
    })
  })

  describe('Edge Cases and Performance', () => {
    it('should handle unicode characters', () => {
      expect(validateEmail('用户@example.com')).toBe(false)
      expect(validateUsername('用户123').isValid).toBe(true)
    })

    it('should handle very long strings', () => {
      const longString = 'a'.repeat(10000)
      expect(() => validateLength(longString, 1, 20000)).not.toThrow()
    })

    it('should be performant with large inputs', () => {
      const largeArray = Array.from({ length: 10000 }, (_, i) => `email${i}@example.com`)
      
      const start = performance.now()
      largeArray.forEach(email => validateEmail(email))
      const end = performance.now()
      
      expect(end - start).toBeLessThan(100) // 应该在100ms内完成
    })

    it('should handle circular references gracefully', () => {
      const obj = {}
      obj.self = obj
      
      expect(() => validateRequired(obj)).not.toThrow()
    })
  })
})