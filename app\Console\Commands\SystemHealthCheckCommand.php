<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Order;
use App\Models\WechatGroup;

/**
 * 系统健康检查命令
 */
class SystemHealthCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:health-check 
                           {--format=table : Output format (table, json)}
                           {--detailed : Show detailed information}';

    /**
     * The console command description.
     */
    protected $description = '执行系统健康检查';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 开始系统健康检查...');
        $this->newLine();

        $checks = [
            'database' => $this->checkDatabase(),
            'redis' => $this->checkRedis(),
            'storage' => $this->checkStorage(),
            'queue' => $this->checkQueue(),
            'permissions' => $this->checkPermissions(),
            'environment' => $this->checkEnvironment(),
            'services' => $this->checkServices(),
        ];

        $format = $this->option('format');
        $detailed = $this->option('detailed');

        if ($format === 'json') {
            $this->line(json_encode($checks, JSON_PRETTY_PRINT));
            return 0;
        }

        // 显示检查结果
        $this->displayResults($checks, $detailed);

        // 计算总体健康状态
        $healthyCount = collect($checks)->where('status', 'healthy')->count();
        $totalCount = count($checks);
        $healthPercentage = ($healthyCount / $totalCount) * 100;

        $this->newLine();
        if ($healthPercentage === 100) {
            $this->info("✅ 系统健康状态: 优秀 ({$healthPercentage}%)");
            return 0;
        } elseif ($healthPercentage >= 80) {
            $this->warn("⚠️  系统健康状态: 良好 ({$healthPercentage}%)");
            return 0;
        } else {
            $this->error("❌ 系统健康状态: 需要关注 ({$healthPercentage}%)");
            return 1;
        }
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabase(): array
    {
        try {
            // 测试数据库连接
            DB::connection()->getPdo();
            
            // 检查关键表
            $tables = ['users', 'wechat_groups', 'orders', 'system_settings'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                if (!DB::getSchemaBuilder()->hasTable($table)) {
                    $missingTables[] = $table;
                }
            }

            if (!empty($missingTables)) {
                return [
                    'status' => 'warning',
                    'message' => '缺少数据表: ' . implode(', ', $missingTables),
                    'details' => ['missing_tables' => $missingTables],
                ];
            }

            // 检查数据完整性
            $userCount = User::count();
            $groupCount = WechatGroup::count();
            $orderCount = Order::count();

            return [
                'status' => 'healthy',
                'message' => '数据库连接正常',
                'details' => [
                    'users' => $userCount,
                    'groups' => $groupCount,
                    'orders' => $orderCount,
                ],
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '数据库连接失败: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查Redis连接
     */
    private function checkRedis(): array
    {
        try {
            // 测试Redis连接
            Cache::store('redis')->put('health_check', 'test', 10);
            $value = Cache::store('redis')->get('health_check');
            
            if ($value !== 'test') {
                return [
                    'status' => 'error',
                    'message' => 'Redis读写测试失败',
                    'details' => ['expected' => 'test', 'actual' => $value],
                ];
            }

            // 清理测试数据
            Cache::store('redis')->forget('health_check');

            return [
                'status' => 'healthy',
                'message' => 'Redis连接正常',
                'details' => ['connection' => 'ok', 'read_write' => 'ok'],
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Redis连接失败: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查存储系统
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'System health check test';

            // 测试文件写入
            Storage::put($testFile, $testContent);
            
            // 测试文件读取
            $content = Storage::get($testFile);
            
            // 测试文件删除
            Storage::delete($testFile);

            if ($content !== $testContent) {
                return [
                    'status' => 'error',
                    'message' => '存储读写测试失败',
                    'details' => ['expected' => $testContent, 'actual' => $content],
                ];
            }

            // 检查关键目录
            $directories = ['storage/app', 'storage/logs', 'storage/framework/cache'];
            $issues = [];

            foreach ($directories as $dir) {
                if (!is_dir($dir)) {
                    $issues[] = "目录不存在: {$dir}";
                } elseif (!is_writable($dir)) {
                    $issues[] = "目录不可写: {$dir}";
                }
            }

            if (!empty($issues)) {
                return [
                    'status' => 'warning',
                    'message' => '存储目录权限问题',
                    'details' => ['issues' => $issues],
                ];
            }

            return [
                'status' => 'healthy',
                'message' => '存储系统正常',
                'details' => ['read_write' => 'ok', 'permissions' => 'ok'],
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '存储系统异常: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查队列系统
     */
    private function checkQueue(): array
    {
        try {
            $pendingJobs = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'healthy';
            $message = '队列系统正常';

            if ($failedJobs > 10) {
                $status = 'warning';
                $message = "队列有较多失败任务 ({$failedJobs})";
            } elseif ($pendingJobs > 1000) {
                $status = 'warning';
                $message = "队列积压较多任务 ({$pendingJobs})";
            }

            return [
                'status' => $status,
                'message' => $message,
                'details' => [
                    'pending_jobs' => $pendingJobs,
                    'failed_jobs' => $failedJobs,
                ],
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '队列检查失败: ' . $e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查文件权限
     */
    private function checkPermissions(): array
    {
        $directories = [
            'storage' => storage_path(),
            'bootstrap/cache' => base_path('bootstrap/cache'),
            'public/uploads' => public_path('uploads'),
        ];

        $issues = [];

        foreach ($directories as $name => $path) {
            if (!file_exists($path)) {
                $issues[] = "目录不存在: {$name}";
                continue;
            }

            if (!is_readable($path)) {
                $issues[] = "目录不可读: {$name}";
            }

            if (!is_writable($path)) {
                $issues[] = "目录不可写: {$name}";
            }
        }

        if (!empty($issues)) {
            return [
                'status' => 'error',
                'message' => '文件权限问题',
                'details' => ['issues' => $issues],
            ];
        }

        return [
            'status' => 'healthy',
            'message' => '文件权限正常',
            'details' => ['checked_directories' => count($directories)],
        ];
    }

    /**
     * 检查环境配置
     */
    private function checkEnvironment(): array
    {
        $requiredEnvVars = [
            'APP_KEY',
            'DB_CONNECTION',
            'DB_HOST',
            'DB_DATABASE',
            'JWT_SECRET',
        ];

        $missing = [];
        foreach ($requiredEnvVars as $var) {
            if (empty(env($var))) {
                $missing[] = $var;
            }
        }

        if (!empty($missing)) {
            return [
                'status' => 'error',
                'message' => '缺少必要的环境变量',
                'details' => ['missing_vars' => $missing],
            ];
        }

        // 检查PHP扩展
        $requiredExtensions = ['pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
        $missingExtensions = [];

        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $missingExtensions[] = $ext;
            }
        }

        if (!empty($missingExtensions)) {
            return [
                'status' => 'error',
                'message' => '缺少必要的PHP扩展',
                'details' => ['missing_extensions' => $missingExtensions],
            ];
        }

        return [
            'status' => 'healthy',
            'message' => '环境配置正常',
            'details' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'environment' => app()->environment(),
            ],
        ];
    }

    /**
     * 检查关键服务
     */
    private function checkServices(): array
    {
        $services = [];

        // 检查Web服务器
        if (isset($_SERVER['SERVER_SOFTWARE'])) {
            $services['web_server'] = $_SERVER['SERVER_SOFTWARE'];
        }

        // 检查PHP版本
        $phpVersion = PHP_VERSION;
        if (version_compare($phpVersion, '8.1.0', '<')) {
            return [
                'status' => 'warning',
                'message' => 'PHP版本过低，建议升级到8.1+',
                'details' => ['current_version' => $phpVersion],
            ];
        }

        return [
            'status' => 'healthy',
            'message' => '服务状态正常',
            'details' => $services,
        ];
    }

    /**
     * 显示检查结果
     */
    private function displayResults(array $checks, bool $detailed): void
    {
        $headers = ['检查项目', '状态', '消息'];
        if ($detailed) {
            $headers[] = '详细信息';
        }

        $rows = [];
        foreach ($checks as $name => $check) {
            $status = $this->formatStatus($check['status']);
            $row = [
                $this->formatCheckName($name),
                $status,
                $check['message'],
            ];

            if ($detailed && isset($check['details'])) {
                $row[] = json_encode($check['details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            }

            $rows[] = $row;
        }

        $this->table($headers, $rows);
    }

    /**
     * 格式化状态显示
     */
    private function formatStatus(string $status): string
    {
        return match ($status) {
            'healthy' => '<fg=green>✅ 正常</>',
            'warning' => '<fg=yellow>⚠️  警告</>',
            'error' => '<fg=red>❌ 错误</>',
            default => $status,
        };
    }

    /**
     * 格式化检查项目名称
     */
    private function formatCheckName(string $name): string
    {
        return match ($name) {
            'database' => '数据库',
            'redis' => 'Redis缓存',
            'storage' => '存储系统',
            'queue' => '队列系统',
            'permissions' => '文件权限',
            'environment' => '环境配置',
            'services' => '系统服务',
            default => $name,
        };
    }
}