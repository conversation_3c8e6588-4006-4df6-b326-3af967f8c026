<template>
  <div class="test-preview">
    <h1>预览页面测试</h1>
    <div class="test-info">
      <p>当前时间: {{ currentTime }}</p>
      <p>预览模式: {{ isPreviewMode ? '已启用' : '未启用' }}</p>
      <p>用户信息: {{ userInfo.nickname || '未设置' }}</p>
      <p>Token: {{ token ? '已设置' : '未设置' }}</p>
    </div>
    
    <div class="test-actions">
      <el-button @click="testApi">测试API</el-button>
      <el-button @click="testNavigation">测试导航</el-button>
    </div>
    
    <div v-if="apiResult" class="api-result">
      <h3>API测试结果:</h3>
      <pre>{{ apiResult }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const currentTime = ref('')
const apiResult = ref('')

const isPreviewMode = computed(() => {
  return localStorage.getItem('preview-mode') === 'true'
})

const userInfo = computed(() => userStore.userInfo || {})
const token = computed(() => userStore.token)

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testApi = async () => {
  try {
    // 简单的API测试
    const response = await fetch('/api/v1/test', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })
    apiResult.value = await response.text()
    ElMessage.success('API测试完成')
  } catch (error) {
    apiResult.value = `API错误: ${error.message}`
    ElMessage.error('API测试失败')
  }
}

const testNavigation = () => {
  router.push('/dashboard')
  ElMessage.info('导航到Dashboard')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  
  console.log('TestPreview组件已挂载')
  console.log('预览模式:', isPreviewMode.value)
  console.log('用户信息:', userInfo.value)
  console.log('Token:', token.value)
})
</script>

<style scoped>
.test-preview {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.test-info p {
  margin: 5px 0;
}

.test-actions {
  margin: 20px 0;
}

.test-actions .el-button {
  margin-right: 10px;
}

.api-result {
  background: #f0f0f0;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.api-result pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
