<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\AgentAccount;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\AgentCommissionLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DetailViewController extends Controller
{
    /**
     * 用户详细数据页面
     */
    public function userDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $search = $request->input('search');
        $status = $request->input('status');
        $source = $request->input('source');
        
        $query = User::with(['orders', 'agentAccount']);
        
        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }
        
        // 状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 来源筛选
        if ($source) {
            $query->where('source', $source);
        }
        
        // 时间筛选
        if ($period !== 'all') {
            $days = $this->getPeriodDays($period);
            $query->where('created_at', '>=', now()->subDays($days));
        }
        
        $users = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_users' => $query->count(),
            'active_users' => $query->where('last_login_at', '>=', now()->subDays(7))->count(),
            'new_users_today' => $query->whereDate('created_at', today())->count(),
            'total_orders' => Order::whereIn('user_id', $query->pluck('id'))->count(),
            'total_revenue' => Order::whereIn('user_id', $query->pluck('id'))
                                   ->where('status', 'completed')
                                   ->sum('amount'),
        ];
        
        return view('admin.details.users', compact('users', 'stats', 'period', 'search', 'status', 'source'));
    }
    
    /**
     * 代理商详细数据页面
     */
    public function agentDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $search = $request->input('search');
        $status = $request->input('status');
        $level = $request->input('level');
        
        $query = AgentAccount::with(['user', 'commissionLogs', 'children']);
        
        // 搜索条件
        if ($search) {
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('agent_name', 'like', "%{$search}%");
        }
        
        // 状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 级别筛选
        if ($level) {
            $query->where('level', $level);
        }
        
        // 时间筛选
        if ($period !== 'all') {
            $days = $this->getPeriodDays($period);
            $query->where('created_at', '>=', now()->subDays($days));
        }
        
        $agents = $query->orderBy('total_commission', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_agents' => $query->count(),
            'active_agents' => $query->where('status', 'active')->count(),
            'new_agents_today' => $query->whereDate('created_at', today())->count(),
            'total_commission' => $query->sum('total_commission'),
            'pending_commission' => AgentCommissionLog::where('status', 'pending')->sum('amount'),
        ];
        
        return view('admin.details.agents', compact('agents', 'stats', 'period', 'search', 'status', 'level'));
    }
    
    /**
     * 订单详细数据页面
     */
    public function orderDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $search = $request->input('search');
        $status = $request->input('status');
        $payment_method = $request->input('payment_method');
        
        $query = Order::with(['user', 'wechatGroup']);
        
        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('order_no', 'like', "%{$search}%")
                  ->orWhereHas('user', function($subQ) use ($search) {
                      $subQ->where('name', 'like', "%{$search}%");
                  });
            });
        }
        
        // 状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 支付方式筛选
        if ($payment_method) {
            $query->where('payment_method', $payment_method);
        }
        
        // 时间筛选
        if ($period !== 'all') {
            $days = $this->getPeriodDays($period);
            $query->where('created_at', '>=', now()->subDays($days));
        }
        
        $orders = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_orders' => $query->count(),
            'completed_orders' => $query->where('status', 'completed')->count(),
            'pending_orders' => $query->where('status', 'pending')->count(),
            'total_amount' => $query->where('status', 'completed')->sum('amount'),
            'average_amount' => $query->where('status', 'completed')->avg('amount'),
        ];
        
        return view('admin.details.orders', compact('orders', 'stats', 'period', 'search', 'status', 'payment_method'));
    }
    
    /**
     * 微信群详细数据页面
     */
    public function groupDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $search = $request->input('search');
        $status = $request->input('status');
        $type = $request->input('type');
        
        $query = WechatGroup::with(['user', 'orders']);
        
        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 类型筛选
        if ($type) {
            $query->where('type', $type);
        }
        
        // 时间筛选
        if ($period !== 'all') {
            $days = $this->getPeriodDays($period);
            $query->where('created_at', '>=', now()->subDays($days));
        }
        
        $groups = $query->orderBy('total_revenue', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_groups' => $query->count(),
            'active_groups' => $query->where('status', 'active')->count(),
            'new_groups_today' => $query->whereDate('created_at', today())->count(),
            'total_revenue' => $query->sum('total_revenue'),
            'total_members' => $query->sum('member_count'),
        ];
        
        return view('admin.details.groups', compact('groups', 'stats', 'period', 'search', 'status', 'type'));
    }
    
    /**
     * 收入详细数据页面
     */
    public function revenueDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $source = $request->input('source');
        $payment_method = $request->input('payment_method');
        
        $days = $this->getPeriodDays($period);
        $startDate = now()->subDays($days);
        
        // 收入明细查询
        $query = Order::where('status', 'completed')
                     ->where('created_at', '>=', $startDate)
                     ->with(['user', 'wechatGroup']);
        
        // 来源筛选
        if ($source) {
            $query->where('source', $source);
        }
        
        // 支付方式筛选
        if ($payment_method) {
            $query->where('payment_method', $payment_method);
        }
        
        $revenues = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_revenue' => $query->sum('amount'),
            'order_count' => $query->count(),
            'average_order' => $query->avg('amount'),
            'today_revenue' => Order::where('status', 'completed')
                                   ->whereDate('created_at', today())
                                   ->sum('amount'),
        ];
        
        // 按日期分组的收入数据
        $dailyRevenue = $query->select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(amount) as total'),
            DB::raw('COUNT(*) as count')
        )->groupBy('date')->orderBy('date', 'desc')->get();
        
        // 按来源分组的收入数据
        $sourceRevenue = $query->select(
            'source',
            DB::raw('SUM(amount) as total'),
            DB::raw('COUNT(*) as count')
        )->groupBy('source')->orderBy('total', 'desc')->get();
        
        return view('admin.details.revenue', compact(
            'revenues', 'stats', 'dailyRevenue', 'sourceRevenue', 
            'period', 'source', 'payment_method'
        ));
    }
    
    /**
     * 佣金详细数据页面
     */
    public function commissionDetails(Request $request)
    {
        $period = $request->input('period', '30d');
        $status = $request->input('status');
        $agent_id = $request->input('agent_id');
        
        $query = AgentCommissionLog::with(['agent.user', 'order']);
        
        // 状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 代理商筛选
        if ($agent_id) {
            $query->where('agent_id', $agent_id);
        }
        
        // 时间筛选
        if ($period !== 'all') {
            $days = $this->getPeriodDays($period);
            $query->where('created_at', '>=', now()->subDays($days));
        }
        
        $commissions = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // 统计数据
        $stats = [
            'total_commission' => $query->sum('amount'),
            'paid_commission' => $query->where('status', 'paid')->sum('amount'),
            'pending_commission' => $query->where('status', 'pending')->sum('amount'),
            'commission_count' => $query->count(),
        ];
        
        return view('admin.details.commissions', compact('commissions', 'stats', 'period', 'status', 'agent_id'));
    }
    
    /**
     * 获取周期对应的天数
     */
    private function getPeriodDays($period)
    {
        switch ($period) {
            case '7d': return 7;
            case '30d': return 30;
            case '90d': return 90;
            case '1y': return 365;
            default: return 30;
        }
    }
}