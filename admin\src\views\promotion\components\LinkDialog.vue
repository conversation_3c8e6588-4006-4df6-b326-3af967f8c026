<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑推广链接' : '创建推广链接'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="链接名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入链接名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="目标URL" prop="target_url">
        <el-input
          v-model="formData.target_url"
          placeholder="请输入目标URL，如：https://example.com"
          type="textarea"
          :rows="2"
        />
      </el-form-item>
      
      <el-form-item label="链接类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择链接类型" style="width: 100%">
          <el-option label="群组推广" value="group" />
          <el-option label="分销推广" value="distribution" />
          <el-option label="活动推广" value="activity" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="自定义短码" prop="custom_code">
        <el-input
          v-model="formData.custom_code"
          placeholder="留空则自动生成，支持字母数字组合"
          maxlength="20"
        >
          <template #prepend>
            <span>{{ baseUrl }}/</span>
          </template>
        </el-input>
        <div class="form-help">
          <i class="el-icon-info"></i>
          自定义短码必须唯一，支持字母、数字和连字符
        </div>
      </el-form-item>
      
      <el-form-item label="过期时间" prop="expires_at">
        <el-date-picker
          v-model="formData.expires_at"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
        <div class="form-help">
          <i class="el-icon-info"></i>
          留空表示永不过期
        </div>
      </el-form-item>
      
      <el-form-item label="访问限制">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item prop="max_clicks">
              <el-input-number
                v-model="formData.max_clicks"
                :min="0"
                placeholder="最大点击次数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="daily_limit">
              <el-input-number
                v-model="formData.daily_limit"
                :min="0"
                placeholder="每日访问限制"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="form-help">
          <i class="el-icon-info"></i>
          设置为0表示无限制
        </div>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="active">启用</el-radio>
          <el-radio label="paused">暂停</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { promotionApi } from '@/api/promotion'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  linkData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const baseUrl = 'https://short.example.com'

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => {
  return props.linkData && props.linkData.id
})

const formData = reactive({
  name: '',
  target_url: '',
  type: '',
  custom_code: '',
  expires_at: '',
  max_clicks: 0,
  daily_limit: 0,
  status: 'active',
  description: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入链接名称', trigger: 'blur' },
    { min: 2, max: 50, message: '链接名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  target_url: [
    { required: true, message: '请输入目标URL', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的URL地址', 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择链接类型', trigger: 'change' }
  ],
  custom_code: [
    { 
      pattern: /^[a-zA-Z0-9-]*$/, 
      message: '自定义短码只能包含字母、数字和连字符', 
      trigger: 'blur' 
    }
  ]
}

// 监听对话框打开，初始化表单数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initFormData()
    })
  }
})

const initFormData = () => {
  if (isEdit.value) {
    // 编辑模式，填充现有数据
    Object.keys(formData).forEach(key => {
      if (props.linkData[key] !== undefined) {
        formData[key] = props.linkData[key]
      }
    })
  } else {
    // 创建模式，重置表单
    Object.keys(formData).forEach(key => {
      if (key === 'status') {
        formData[key] = 'active'
      } else if (key === 'max_clicks' || key === 'daily_limit') {
        formData[key] = 0
      } else {
        formData[key] = ''
      }
    })
  }
  
  // 清除验证状态
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = { ...formData }
    
    // 处理空值
    if (!submitData.expires_at) {
      delete submitData.expires_at
    }
    if (!submitData.custom_code) {
      delete submitData.custom_code
    }
    if (!submitData.description) {
      delete submitData.description
    }
    
    if (isEdit.value) {
      await promotionApi.update(props.linkData.id, submitData)
      ElMessage.success('推广链接更新成功')
    } else {
      await promotionApi.create(submitData)
      ElMessage.success('推广链接创建成功')
    }
    
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 4px;
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-input-group__prepend) {
  background-color: #f5f7fa;
  color: #909399;
  border-color: #dcdfe6;
}
</style>