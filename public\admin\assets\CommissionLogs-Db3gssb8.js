import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               */import{T as a,a4 as t,at as l,aR as s,a_ as o,ai as r,U as n,bd as d,c1 as i,_ as u,c2 as c,a6 as m,aZ as _,bw as p,bm as v,bn as f,aY as g,bh as b,bi as h,c3 as w,a$ as y,bx as k,aM as C,aL as D,b9 as z,b8 as x,by as j,bk as V,bl as F,ay as A,ad as E,Q as U,R as Y}from"./element-plus-h2SQQM64.js";import{L as R}from"./LineChart-CydsJ2U8.js";import{r as T,L as B,e as L,k as M,l as S,t as $,E as I,z as P,D as N,u as O,A as Z,y as q,B as H}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const Q={class:"commission-logs"},W={class:"page-header"},G={class:"header-actions"},J={class:"stat-card"},K={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},X={class:"stat-content"},ee={class:"stat-value"},ae={class:"stat-trend positive"},te={class:"stat-card"},le={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},se={class:"stat-content"},oe={class:"stat-value"},re={class:"stat-trend positive"},ne={class:"stat-card"},de={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},ie={class:"stat-content"},ue={class:"stat-value"},ce={class:"stat-trend neutral"},me={class:"stat-card"},_e={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},pe={class:"stat-content"},ve={class:"stat-value"},fe={class:"stat-trend neutral"},ge={class:"card-header"},be={class:"chart-controls"},he={class:"chart-container"},we={class:"card-header"},ye={class:"header-actions"},ke={class:"customer-info"},Ce={class:"customer-name"},De={class:"customer-level"},ze={class:"order-amount"},xe={class:"commission-amount"},je={class:"commission-rate"},Ve={key:0},Fe={key:1,class:"pending-text"},Ae={class:"pagination-wrapper"},Ee={key:0,class:"commission-detail"},Ue={class:"dev-notice"},Ye=e({__name:"CommissionLogs",setup(e){const Ye=T(!1),Re=T(!1),Te=T(!1),Be=T(!1),Le=T(""),Me=T(""),Se=T([]),$e=T("30d"),Ie=T(null),Pe=B({total_commission:28650.5,month_commission:8650.5,pending_commission:1250,commission_rate:15}),Ne=T([]),Oe=B({current_page:1,per_page:20,total:0}),Ze=T({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0}]}),qe={responsive:!0,maintainAspectRatio:!1,interaction:{intersect:!1,mode:"index"},plugins:{legend:{display:!0,position:"top"},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",callbacks:{label:function(e){return`佣金收入: ¥${e.parsed.y.toFixed(2)}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{callback:function(e){return"¥"+e}}},x:{grid:{display:!1}}}},He=[{id:1,order_no:"ORD202401001",customer_name:"张三",customer_level:"A级客户",product_name:"VIP群组套餐",order_amount:2999,commission_amount:449.85,commission_rate:15,status:"settled",created_at:new Date(Date.now()-432e6),settled_at:new Date(Date.now()-1728e5),remark:"正常结算"},{id:2,order_no:"ORD202401002",customer_name:"李四",customer_level:"B级客户",product_name:"标准群组套餐",order_amount:1999,commission_amount:199.9,commission_rate:10,status:"pending",created_at:new Date(Date.now()-2592e5),settled_at:null,remark:""},{id:3,order_no:"ORD202401003",customer_name:"王五",customer_level:"A级客户",product_name:"企业群组套餐",order_amount:4999,commission_amount:749.85,commission_rate:15,status:"settled",created_at:new Date(Date.now()-6912e5),settled_at:new Date(Date.now()-432e6),remark:"正常结算"}],Qe=async()=>{try{Ye.value=!0,await new Promise(e=>setTimeout(e,500));let e=[...He];if(Le.value&&(e=e.filter(e=>e.order_no.includes(Le.value)||e.customer_name.includes(Le.value)||e.product_name.includes(Le.value))),Me.value&&(e=e.filter(e=>e.status===Me.value)),Se.value&&2===Se.value.length){const a=new Date(Se.value[0]),t=new Date(Se.value[1]);e=e.filter(e=>{const l=new Date(e.created_at);return l>=a&&l<=t})}Ne.value=e,Oe.total=e.length}catch(e){U.error("加载佣金数据失败")}finally{Ye.value=!1}},We=async()=>{Re.value=!0;try{await new Promise(e=>setTimeout(e,500));const e={"7d":{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120.5,190.8,300.2,500.6,200.3,300.9,450.7]},"30d":{labels:Array.from({length:30},(e,a)=>`${a+1}日`),data:Array.from({length:30},()=>Math.floor(500*Math.random())+100)},"90d":{labels:["第1月","第2月","第3月"],data:[8000.5,12000.8,15000.2]}}[$e.value];Ze.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#409EFF",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]}}catch(e){U.error("加载图表数据失败")}finally{Re.value=!1}},Ge=()=>{Qe(),We(),U.success("数据已刷新")},Je=function(e,a){let t;return function(...l){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e(...l)},a)}}(()=>{Oe.current_page=1,Qe()},500),Ke=()=>{U.info("导出佣金明细功能开发中..."),Be.value=!0},Xe=e=>({settled:"success",pending:"warning",frozen:"danger"}[e]||"info"),ea=e=>({settled:"已结算",pending:"待结算",frozen:"已冻结"}[e]||"未知"),aa=e=>Number(e).toFixed(2),ta=e=>new Date(e).toLocaleDateString("zh-CN"),la=e=>new Date(e).toLocaleString("zh-CN");return L(()=>{Qe(),We()}),(e,T)=>{const B=a,L=l,He=o,sa=_,oa=f,ra=v,na=g,da=C,ia=x,ua=z,ca=j,ma=w,_a=h,pa=y,va=b,fa=k,ga=F,ba=V,ha=A,wa=p;return S(),M("div",Q,[$("div",W,[T[12]||(T[12]=$("div",{class:"header-left"},[$("h2",null,"佣金查看"),$("p",{class:"page-description"},"查看您的佣金收入明细，跟踪收益情况")],-1)),$("div",G,[I(L,{type:"primary",onClick:Ke},{default:P(()=>[I(B,null,{default:P(()=>[I(O(t))]),_:1}),T[10]||(T[10]=N(" 导出明细 ",-1))]),_:1,__:[10]}),I(L,{onClick:Ge},{default:P(()=>[I(B,null,{default:P(()=>[I(O(s))]),_:1}),T[11]||(T[11]=N(" 刷新数据 ",-1))]),_:1,__:[11]})])]),I(sa,{gutter:20,class:"stats-row"},{default:P(()=>[I(He,{span:6},{default:P(()=>[$("div",J,[$("div",K,[I(B,{size:24},{default:P(()=>[I(O(r))]),_:1})]),$("div",X,[$("div",ee,"¥"+n(aa(Pe.total_commission)),1),T[14]||(T[14]=$("div",{class:"stat-title"},"累计佣金",-1)),$("div",ae,[I(B,null,{default:P(()=>[I(O(d))]),_:1}),T[13]||(T[13]=N(" 12.5% ",-1))])])])]),_:1}),I(He,{span:6},{default:P(()=>[$("div",te,[$("div",le,[I(B,{size:24},{default:P(()=>[I(O(i))]),_:1})]),$("div",se,[$("div",oe,"¥"+n(aa(Pe.month_commission)),1),T[16]||(T[16]=$("div",{class:"stat-title"},"本月佣金",-1)),$("div",re,[I(B,null,{default:P(()=>[I(O(d))]),_:1}),T[15]||(T[15]=N(" 8.3% ",-1))])])])]),_:1}),I(He,{span:6},{default:P(()=>[$("div",ne,[$("div",de,[I(B,{size:24},{default:P(()=>[I(O(u))]),_:1})]),$("div",ie,[$("div",ue,"¥"+n(aa(Pe.pending_commission)),1),T[18]||(T[18]=$("div",{class:"stat-title"},"待结算",-1)),$("div",ce,[I(B,null,{default:P(()=>[I(O(c))]),_:1}),T[17]||(T[17]=N(" 0% ",-1))])])])]),_:1}),I(He,{span:6},{default:P(()=>[$("div",me,[$("div",_e,[I(B,{size:24},{default:P(()=>[I(O(m))]),_:1})]),$("div",pe,[$("div",ve,n(Pe.commission_rate)+"%",1),T[20]||(T[20]=$("div",{class:"stat-title"},"佣金比例",-1)),$("div",fe,[I(B,null,{default:P(()=>[I(O(c))]),_:1}),T[19]||(T[19]=N(" 0% ",-1))])])])]),_:1})]),_:1}),I(na,{class:"chart-card"},{header:P(()=>[$("div",ge,[T[24]||(T[24]=$("span",null,"佣金趋势",-1)),$("div",be,[I(ra,{modelValue:$e.value,"onUpdate:modelValue":T[0]||(T[0]=e=>$e.value=e),size:"small",onChange:We},{default:P(()=>[I(oa,{value:"7d"},{default:P(()=>T[21]||(T[21]=[N("近7天",-1)])),_:1,__:[21]}),I(oa,{value:"30d"},{default:P(()=>T[22]||(T[22]=[N("近30天",-1)])),_:1,__:[22]}),I(oa,{value:"90d"},{default:P(()=>T[23]||(T[23]=[N("近3个月",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])])])]),default:P(()=>[Z((S(),M("div",he,[Re.value?H("",!0):(S(),q(R,{key:0,data:Ze.value,options:qe,height:"300px"},null,8,["data"]))])),[[wa,Re.value]])]),_:1}),I(na,null,{header:P(()=>[$("div",we,[T[25]||(T[25]=$("span",null,"佣金明细",-1)),$("div",ye,[I(da,{modelValue:Le.value,"onUpdate:modelValue":T[1]||(T[1]=e=>Le.value=e),placeholder:"搜索订单号或客户",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:O(Je)},{prefix:P(()=>[I(B,null,{default:P(()=>[I(O(D))]),_:1})]),_:1},8,["modelValue","onInput"]),I(ua,{modelValue:Me.value,"onUpdate:modelValue":T[2]||(T[2]=e=>Me.value=e),placeholder:"状态",style:{width:"120px","margin-right":"10px"},onChange:Qe},{default:P(()=>[I(ia,{label:"全部",value:""}),I(ia,{label:"已结算",value:"settled"}),I(ia,{label:"待结算",value:"pending"}),I(ia,{label:"已冻结",value:"frozen"})]),_:1},8,["modelValue"]),I(ca,{modelValue:Se.value,"onUpdate:modelValue":T[3]||(T[3]=e=>Se.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:Qe},null,8,["modelValue"])])])]),default:P(()=>[Z((S(),q(va,{data:Ne.value,stripe:""},{default:P(()=>[I(_a,{prop:"order_no",label:"订单号",width:"180"},{default:P(({row:e})=>[I(ma,{type:"primary",onClick:a=>{return t=e.order_no,U.info(`查看订单 ${t} 详情`),void(Be.value=!0);var t}},{default:P(()=>[N(n(e.order_no),1)]),_:2},1032,["onClick"])]),_:1}),I(_a,{label:"客户信息",width:"150"},{default:P(({row:e})=>[$("div",ke,[$("div",Ce,n(e.customer_name),1),$("div",De,n(e.customer_level),1)])]),_:1}),I(_a,{prop:"product_name",label:"产品名称",width:"200"}),I(_a,{label:"订单金额",width:"120"},{default:P(({row:e})=>[$("span",ze,"¥"+n(aa(e.order_amount)),1)]),_:1}),I(_a,{label:"佣金金额",width:"120"},{default:P(({row:e})=>[$("span",xe,"¥"+n(aa(e.commission_amount)),1)]),_:1}),I(_a,{label:"佣金比例",width:"100"},{default:P(({row:e})=>[$("span",je,n(e.commission_rate)+"%",1)]),_:1}),I(_a,{label:"状态",width:"100"},{default:P(({row:e})=>[I(pa,{type:Xe(e.status),size:"small"},{default:P(()=>[N(n(ea(e.status)),1)]),_:2},1032,["type"])]),_:1}),I(_a,{label:"结算时间",width:"120"},{default:P(({row:e})=>[e.settled_at?(S(),M("span",Ve,n(ta(e.settled_at)),1)):(S(),M("span",Fe,"待结算"))]),_:1}),I(_a,{label:"创建时间",width:"120"},{default:P(({row:e})=>[N(n(ta(e.created_at)),1)]),_:1}),I(_a,{label:"操作",width:"120",fixed:"right"},{default:P(({row:e})=>[I(L,{size:"small",onClick:a=>{return t=e,Ie.value=t,void(Te.value=!0);var t}},{default:P(()=>T[26]||(T[26]=[N(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"]),"pending"===e.status?(S(),q(L,{key:0,size:"small",type:"primary",onClick:a=>(async e=>{try{await Y.confirm(`确定要申请结算订单 ${e.order_no} 的佣金吗？`,"确认申请",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),U.success("结算申请已提交，请等待审核"),Be.value=!0}catch(a){"cancel"!==a&&U.error("申请失败")}})(e)},{default:P(()=>T[27]||(T[27]=[N(" 申请结算 ",-1)])),_:2,__:[27]},1032,["onClick"])):H("",!0)]),_:1})]),_:1},8,["data"])),[[wa,Ye.value]]),$("div",Ae,[I(fa,{"current-page":Oe.current_page,"onUpdate:currentPage":T[4]||(T[4]=e=>Oe.current_page=e),"page-size":Oe.per_page,"onUpdate:pageSize":T[5]||(T[5]=e=>Oe.per_page=e),total:Oe.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Qe,onCurrentChange:Qe},null,8,["current-page","page-size","total"])])]),_:1}),I(ha,{modelValue:Te.value,"onUpdate:modelValue":T[7]||(T[7]=e=>Te.value=e),title:"佣金详情",width:"600px"},{footer:P(()=>[I(L,{onClick:T[6]||(T[6]=e=>Te.value=!1)},{default:P(()=>T[28]||(T[28]=[N("关闭",-1)])),_:1,__:[28]})]),default:P(()=>[Ie.value?(S(),M("div",Ee,[I(ba,{column:2,border:""},{default:P(()=>[I(ga,{label:"订单号"},{default:P(()=>[N(n(Ie.value.order_no),1)]),_:1}),I(ga,{label:"客户姓名"},{default:P(()=>[N(n(Ie.value.customer_name),1)]),_:1}),I(ga,{label:"产品名称"},{default:P(()=>[N(n(Ie.value.product_name),1)]),_:1}),I(ga,{label:"订单金额"},{default:P(()=>[N("¥"+n(aa(Ie.value.order_amount)),1)]),_:1}),I(ga,{label:"佣金金额"},{default:P(()=>[N("¥"+n(aa(Ie.value.commission_amount)),1)]),_:1}),I(ga,{label:"佣金比例"},{default:P(()=>[N(n(Ie.value.commission_rate)+"%",1)]),_:1}),I(ga,{label:"状态"},{default:P(()=>[I(pa,{type:Xe(Ie.value.status)},{default:P(()=>[N(n(ea(Ie.value.status)),1)]),_:1},8,["type"])]),_:1}),I(ga,{label:"创建时间"},{default:P(()=>[N(n(la(Ie.value.created_at)),1)]),_:1}),Ie.value.settled_at?(S(),q(ga,{key:0,label:"结算时间"},{default:P(()=>[N(n(la(Ie.value.settled_at)),1)]),_:1})):H("",!0),Ie.value.remark?(S(),q(ga,{key:1,label:"备注"},{default:P(()=>[N(n(Ie.value.remark),1)]),_:1})):H("",!0)]),_:1})])):H("",!0)]),_:1},8,["modelValue"]),I(ha,{modelValue:Be.value,"onUpdate:modelValue":T[9]||(T[9]=e=>Be.value=e),title:"功能开发中",width:"400px",center:""},{footer:P(()=>[I(L,{type:"primary",onClick:T[8]||(T[8]=e=>Be.value=!1)},{default:P(()=>T[32]||(T[32]=[N("知道了",-1)])),_:1,__:[32]})]),default:P(()=>[$("div",Ue,[I(B,{size:60,color:"#409EFF"},{default:P(()=>[I(O(E))]),_:1}),T[29]||(T[29]=$("h3",null,"功能开发中",-1)),T[30]||(T[30]=$("p",null,"该功能正在紧急开发中，敬请期待！",-1)),T[31]||(T[31]=$("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-9c3e1d95"]]);export{Ye as default};
