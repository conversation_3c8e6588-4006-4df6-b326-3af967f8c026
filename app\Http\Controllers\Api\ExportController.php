<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ExportTask;
use App\Services\ExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

/**
 * 导出控制器
 * 处理各种数据的导出功能
 */
class ExportController extends Controller
{
    protected ExportService $exportService;

    public function __construct(ExportService $exportService)
    {
        $this->middleware('auth:api');
        $this->exportService = $exportService;
    }

    /**
     * 获取导出任务列表
     */
    public function index(Request $request)
    {
        try {
            $query = ExportTask::where('user_id', auth()->id())
                ->orderBy('created_at', 'desc');

            // 筛选条件
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }

            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }

            $tasks = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $tasks,
                'message' => '导出任务列表获取成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取导出任务列表失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 创建导出任务
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:users,orders,groups,commissions,withdrawals,statistics',
                'name' => 'required|string|max:255',
                'parameters' => 'nullable|array',
                'format' => 'nullable|string|in:xlsx,csv',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $parameters = $request->input('parameters', []);
            $parameters['format'] = $request->input('format', 'xlsx');

            $task = $this->exportService->createExportTask(
                $request->type,
                $request->name,
                $parameters,
                auth()->id()
            );

            // 异步处理导出任务
            dispatch(function () use ($task) {
                $this->exportService->processExportTask($task);
            });

            return response()->json([
                'success' => true,
                'data' => $task,
                'message' => '导出任务创建成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建导出任务失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取导出任务详情
     */
    public function show($id)
    {
        try {
            $task = ExportTask::where('user_id', auth()->id())
                             ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $task,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出任务不存在',
            ], 404);
        }
    }

    /**
     * 下载导出文件
     */
    public function download($id)
    {
        try {
            $task = ExportTask::where('user_id', auth()->id())
                             ->findOrFail($id);

            if (!$task->canDownload()) {
                return response()->json([
                    'success' => false,
                    'message' => '文件不可下载或已过期',
                ], 400);
            }

            $filePath = $this->exportService->getDownloadPath($task);
            
            if (!$filePath || !file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => '文件不存在',
                ], 404);
            }

            $filename = basename($task->file_path);
            
            return response()->download($filePath, $filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '下载失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 取消导出任务
     */
    public function cancel($id)
    {
        try {
            $task = ExportTask::where('user_id', auth()->id())
                             ->where('status', ExportTask::STATUS_PENDING)
                             ->findOrFail($id);

            $task->update([
                'status' => ExportTask::STATUS_CANCELLED,
            ]);

            return response()->json([
                'success' => true,
                'message' => '导出任务已取消',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '取消导出任务失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 删除导出任务
     */
    public function destroy($id)
    {
        try {
            $task = ExportTask::where('user_id', auth()->id())
                             ->findOrFail($id);

            // 删除文件
            if ($task->file_path && Storage::exists($task->file_path)) {
                Storage::delete($task->file_path);
            }

            $task->delete();

            return response()->json([
                'success' => true,
                'message' => '导出任务已删除',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除导出任务失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取导出类型列表
     */
    public function getTypes()
    {
        $types = [
            ExportTask::TYPE_USERS => '用户数据',
            ExportTask::TYPE_ORDERS => '订单数据',
            ExportTask::TYPE_GROUPS => '群组数据',
            ExportTask::TYPE_COMMISSIONS => '佣金数据',
            ExportTask::TYPE_WITHDRAWALS => '提现数据',
            ExportTask::TYPE_STATISTICS => '统计数据',
        ];

        return response()->json([
            'success' => true,
            'data' => $types,
        ]);
    }

    /**
     * 清理过期文件
     */
    public function cleanup()
    {
        try {
            $cleanedCount = $this->exportService->cleanupExpiredExports();

            return response()->json([
                'success' => true,
                'message' => "已清理 {$cleanedCount} 个过期文件",
                'data' => ['cleaned_count' => $cleanedCount],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '清理过期文件失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 管理员获取所有导出任务
     */
    public function adminIndex(Request $request)
    {
        try {
            $this->middleware('role:admin');
            
            $query = ExportTask::with('user')
                ->orderBy('created_at', 'desc');

            // 筛选条件
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }

            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }

            $tasks = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $tasks,
                'message' => '导出任务列表获取成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取导出任务列表失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 管理员删除导出任务
     */
    public function adminDestroy($id)
    {
        try {
            $this->middleware('role:admin');
            
            $task = ExportTask::findOrFail($id);

            // 删除文件
            if ($task->file_path && Storage::exists($task->file_path)) {
                Storage::delete($task->file_path);
            }

            $task->delete();

            return response()->json([
                'success' => true,
                'message' => '导出任务已删除',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除导出任务失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取导出统计
     */
    public function getStats()
    {
        try {
            $userId = auth()->id();
            
            $stats = [
                'total_tasks' => ExportTask::where('user_id', $userId)->count(),
                'completed_tasks' => ExportTask::where('user_id', $userId)
                                              ->where('status', ExportTask::STATUS_COMPLETED)->count(),
                'failed_tasks' => ExportTask::where('user_id', $userId)
                                            ->where('status', ExportTask::STATUS_FAILED)->count(),
                'pending_tasks' => ExportTask::where('user_id', $userId)
                                             ->where('status', ExportTask::STATUS_PENDING)->count(),
                'processing_tasks' => ExportTask::where('user_id', $userId)
                                                ->where('status', ExportTask::STATUS_PROCESSING)->count(),
                'total_file_size' => ExportTask::where('user_id', $userId)
                                               ->where('status', ExportTask::STATUS_COMPLETED)
                                               ->sum('file_size'),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取导出统计失败: ' . $e->getMessage(),
            ], 500);
        }
    }
}