// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      componentRenderTime: new Map(),
      apiResponseTime: new Map(),
      memoryUsage: 0
    }
    
    this.init()
  }
  
  init() {
    // 监控页面加载时间
    if (performance.timing) {
      window.addEventListener('load', () => {
        this.metrics.pageLoadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
        console.log(`📊 页面加载时间: ${this.metrics.pageLoadTime}ms`)
      })
    }
    
    // 监控内存使用
    if (performance.memory) {
      setInterval(() => {
        this.metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024
        if (this.metrics.memoryUsage > 100) { // 超过100MB警告
          console.warn(`⚠️ 内存使用过高: ${this.metrics.memoryUsage.toFixed(2)}MB`)
        }
      }, 30000)
    }
  }
  
  // 监控组件渲染时间
  measureComponentRender(componentName, renderFn) {
    const startTime = performance.now()
    
    const result = renderFn()
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    this.metrics.componentRenderTime.set(componentName, renderTime)
    
    if (renderTime > 100) { // 超过100ms警告
      console.warn(`⚠️ 组件 ${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms`)
    } else {
      console.log(`✅ 组件 ${componentName} 渲染完成: ${renderTime.toFixed(2)}ms`)
    }
    
    return result
  }
  
  // 监控API响应时间
  measureApiResponse(apiName, apiFn) {
    const startTime = performance.now()
    
    return apiFn().finally(() => {
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      this.metrics.apiResponseTime.set(apiName, responseTime)
      
      if (responseTime > 3000) { // 超过3秒警告
        console.warn(`⚠️ API ${apiName} 响应时间过长: ${responseTime.toFixed(2)}ms`)
      } else {
        console.log(`✅ API ${apiName} 响应完成: ${responseTime.toFixed(2)}ms`)
      }
    })
  }
  
  // 获取性能报告
  getPerformanceReport() {
    return {
      pageLoadTime: this.metrics.pageLoadTime,
      componentRenderTimes: Object.fromEntries(this.metrics.componentRenderTime),
      apiResponseTimes: Object.fromEntries(this.metrics.apiResponseTime),
      memoryUsage: this.metrics.memoryUsage,
      timestamp: new Date().toISOString()
    }
  }
  
  // 打印性能报告
  printReport() {
    const report = this.getPerformanceReport()
    console.group('📊 性能监控报告')
    console.log('页面加载时间:', report.pageLoadTime + 'ms')
    console.log('组件渲染时间:', report.componentRenderTimes)
    console.log('API响应时间:', report.apiResponseTimes)
    console.log('内存使用:', report.memoryUsage.toFixed(2) + 'MB')
    console.log('报告时间:', report.timestamp)
    console.groupEnd()
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor()

// 导出性能监控工具
export { performanceMonitor }
export default performanceMonitor