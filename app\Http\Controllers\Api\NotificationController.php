<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * 通知管理控制器
 * 管理系统通知和消息推送
 */
class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth:api');
        $this->notificationService = $notificationService;
    }

    /**
     * 获取通知列表
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 15);
            $type = $request->input('type');
            $status = $request->input('status');

            $query = $user->notifications();

            if ($type) {
                $query->where('type', $type);
            }

            if ($status === 'read') {
                $query->whereNotNull('read_at');
            } elseif ($status === 'unread') {
                $query->whereNull('read_at');
            }

            $notifications = $query->orderBy('created_at', 'desc')
                                  ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $notifications,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '通知列表获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 标记通知为已读
     */
    public function markAsRead(Request $request, string $id)
    {
        try {
            $user = Auth::user();
            $notification = $user->notifications()->findOrFail($id);
            
            $notification->markAsRead();

            return response()->json([
                'success' => true,
                'message' => '通知已标记为已读',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 批量标记为已读
     */
    public function markAllAsRead(Request $request)
    {
        try {
            $user = Auth::user();
            $user->unreadNotifications->markAsRead();

            return response()->json([
                'success' => true,
                'message' => '所有通知已标记为已读',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 删除通知
     */
    public function destroy(Request $request, string $id)
    {
        try {
            $user = Auth::user();
            $notification = $user->notifications()->findOrFail($id);
            
            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => '通知已删除',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 发送通知 (管理员功能)
     */
    public function send(Request $request)
    {
        // 检查管理员权限
        if (!Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => '权限不足',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:system,announcement,promotion,warning',
            'recipients' => 'required|array',
            'recipients.*' => 'integer|exists:users,id',
            'channels' => 'array',
            'channels.*' => 'string|in:database,email,sms',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $data = $request->validated();
            $channels = $data['channels'] ?? ['database'];
            
            $results = [];
            foreach ($data['recipients'] as $userId) {
                $user = User::find($userId);
                if ($user) {
                    $result = $this->notificationService->send(
                        $user,
                        $data['title'],
                        $data['content'],
                        $data['type'],
                        $channels,
                        $data['scheduled_at'] ?? null
                    );
                    $results[] = [
                        'user_id' => $userId,
                        'success' => $result,
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => '通知发送完成',
                'data' => [
                    'total' => count($data['recipients']),
                    'successful' => count(array_filter($results, fn($r) => $r['success'])),
                    'results' => $results,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '通知发送失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 批量发送通知 (管理员功能)
     */
    public function broadcast(Request $request)
    {
        // 检查管理员权限
        if (!Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => '权限不足',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:system,announcement,promotion,warning',
            'target' => 'required|string|in:all,role,active,recent',
            'role' => 'required_if:target,role|string|in:admin,substation,distributor,user',
            'channels' => 'array',
            'channels.*' => 'string|in:database,email,sms',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $data = $request->validated();
            $channels = $data['channels'] ?? ['database'];
            
            // 获取目标用户
            $users = $this->getTargetUsers($data['target'], $data['role'] ?? null);
            
            $results = [];
            foreach ($users as $user) {
                $result = $this->notificationService->send(
                    $user,
                    $data['title'],
                    $data['content'],
                    $data['type'],
                    $channels,
                    $data['scheduled_at'] ?? null
                );
                $results[] = [
                    'user_id' => $user->id,
                    'success' => $result,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => '广播通知发送完成',
                'data' => [
                    'total' => $users->count(),
                    'successful' => count(array_filter($results, fn($r) => $r['success'])),
                    'target' => $data['target'],
                    'role' => $data['role'] ?? null,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '广播通知发送失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取通知统计
     */
    public function statistics(): \Illuminate\Http\JsonResponse
    {
        try {
            $user = Auth::user();
            
            $stats = [
                'total' => $user->notifications()->count(),
                'unread' => $user->unreadNotifications()->count(),
                'read' => $user->readNotifications()->count(),
                'today' => $user->notifications()
                               ->whereDate('created_at', today())
                               ->count(),
                'this_week' => $user->notifications()
                                   ->whereBetween('created_at', [
                                       now()->startOfWeek(),
                                       now()->endOfWeek()
                                   ])
                                   ->count(),
                'by_type' => $user->notifications()
                                 ->select('type', DB::raw('count(*) as count'))
                                 ->groupBy('type')
                                 ->pluck('count', 'type')
                                 ->toArray(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '统计数据获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取通知设置
     */
    public function getSettings()
    {
        try {
            $user = Auth::user();
            
            // 获取用户通知设置，如果没有则使用默认设置
            $settings = $user->notification_settings ?? [
                'email_enabled' => true,
                'sms_enabled' => false,
                'push_enabled' => true,
                'types' => [
                    'system' => true,
                    'announcement' => true,
                    'promotion' => false,
                    'warning' => true,
                ],
                'quiet_hours' => [
                    'enabled' => false,
                    'start' => '22:00',
                    'end' => '08:00',
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $settings,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '设置获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 更新通知设置
     */
    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_enabled' => 'boolean',
            'sms_enabled' => 'boolean',
            'push_enabled' => 'boolean',
            'types' => 'array',
            'types.system' => 'boolean',
            'types.announcement' => 'boolean',
            'types.promotion' => 'boolean',
            'types.warning' => 'boolean',
            'quiet_hours' => 'array',
            'quiet_hours.enabled' => 'boolean',
            'quiet_hours.start' => 'string|date_format:H:i',
            'quiet_hours.end' => 'string|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $user = Auth::user();
            $settings = $request->validated();
            
            $user->update([
                'notification_settings' => $settings,
            ]);

            return response()->json([
                'success' => true,
                'message' => '通知设置已更新',
                'data' => $settings,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '设置更新失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取目标用户
     */
    private function getTargetUsers(string $target, ?string $role = null)
    {
        $query = User::where('status', User::STATUS_ACTIVE);

        switch ($target) {
            case 'all':
                break;
            case 'role':
                if ($role) {
                    $query->where('role', $role);
                }
                break;
            case 'active':
                $query->where('last_login_at', '>=', now()->subDays(7));
                break;
            case 'recent':
                $query->where('created_at', '>=', now()->subDays(30));
                break;
        }

        return $query->get();
    }
}