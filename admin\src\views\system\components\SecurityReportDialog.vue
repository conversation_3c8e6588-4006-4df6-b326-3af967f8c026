<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="生成安全报告"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="security-report-dialog">
      <el-form :model="reportForm" :rules="reportRules" ref="reportFormRef" label-width="120px">
        <el-form-item label="报告类型" prop="type">
          <el-radio-group v-model="reportForm.type">
            <el-radio label="comprehensive">综合安全报告</el-radio>
            <el-radio label="threats">威胁分析报告</el-radio>
            <el-radio label="compliance">合规检查报告</el-radio>
            <el-radio label="performance">性能安全报告</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="时间范围" prop="timeRange">
          <el-select v-model="reportForm.timeRange" placeholder="选择时间范围">
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
            <el-option label="最近90天" value="90d" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="reportForm.timeRange === 'custom'" label="自定义时间" prop="customRange">
          <el-date-picker
            v-model="reportForm.customRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="包含模块" prop="modules">
          <el-checkbox-group v-model="reportForm.modules">
            <el-checkbox label="login_security">登录安全</el-checkbox>
            <el-checkbox label="data_protection">数据保护</el-checkbox>
            <el-checkbox label="access_control">访问控制</el-checkbox>
            <el-checkbox label="threat_detection">威胁检测</el-checkbox>
            <el-checkbox label="vulnerability_scan">漏洞扫描</el-checkbox>
            <el-checkbox label="compliance_check">合规检查</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="报告格式" prop="format">
          <el-radio-group v-model="reportForm.format">
            <el-radio label="pdf">PDF文档</el-radio>
            <el-radio label="html">HTML网页</el-radio>
            <el-radio label="excel">Excel表格</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="详细程度" prop="detail">
          <el-slider
            v-model="reportForm.detail"
            :min="1"
            :max="5"
            :marks="detailMarks"
            show-stops
          />
        </el-form-item>

        <el-form-item label="发送邮件">
          <el-switch v-model="reportForm.sendEmail" />
          <span class="form-tip">生成后自动发送到管理员邮箱</span>
        </el-form-item>

        <el-form-item v-if="reportForm.sendEmail" label="收件人" prop="recipients">
          <el-input
            v-model="reportForm.recipients"
            placeholder="多个邮箱用逗号分隔"
          />
        </el-form-item>
      </el-form>

      <div class="report-preview">
        <h4>报告预览</h4>
        <div class="preview-content">
          <div class="preview-item">
            <span class="label">报告标题:</span>
            <span class="value">{{ getReportTitle() }}</span>
          </div>
          <div class="preview-item">
            <span class="label">生成时间:</span>
            <span class="value">{{ new Date().toLocaleString('zh-CN') }}</span>
          </div>
          <div class="preview-item">
            <span class="label">数据范围:</span>
            <span class="value">{{ getTimeRangeText() }}</span>
          </div>
          <div class="preview-item">
            <span class="label">包含模块:</span>
            <span class="value">{{ reportForm.modules.length }} 个模块</span>
          </div>
          <div class="preview-item">
            <span class="label">预计大小:</span>
            <span class="value">{{ getEstimatedSize() }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="generateReport" :loading="generating">
          <el-icon><Document /></el-icon>
          生成报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'generate'])

const reportFormRef = ref()
const generating = ref(false)

const reportForm = reactive({
  type: 'comprehensive',
  timeRange: '30d',
  customRange: null,
  modules: ['login_security', 'data_protection', 'access_control'],
  format: 'pdf',
  detail: 3,
  sendEmail: false,
  recipients: ''
})

const reportRules = {
  type: [
    { required: true, message: '请选择报告类型', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ],
  customRange: [
    { required: true, message: '请选择自定义时间范围', trigger: 'change' }
  ],
  modules: [
    { type: 'array', min: 1, message: '请至少选择一个模块', trigger: 'change' }
  ],
  format: [
    { required: true, message: '请选择报告格式', trigger: 'change' }
  ],
  recipients: [
    { required: true, message: '请输入收件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

const detailMarks = {
  1: '简要',
  2: '基础',
  3: '标准',
  4: '详细',
  5: '完整'
}

// 计算属性
const getReportTitle = () => {
  const typeMap = {
    comprehensive: '综合安全报告',
    threats: '威胁分析报告',
    compliance: '合规检查报告',
    performance: '性能安全报告'
  }
  return typeMap[reportForm.type] || '安全报告'
}

const getTimeRangeText = () => {
  const rangeMap = {
    '7d': '最近7天',
    '30d': '最近30天',
    '90d': '最近90天',
    'custom': '自定义时间范围'
  }
  return rangeMap[reportForm.timeRange] || '未知'
}

const getEstimatedSize = () => {
  const baseSize = 2 // MB
  const moduleMultiplier = reportForm.modules.length * 0.5
  const detailMultiplier = reportForm.detail * 0.3
  const formatMultiplier = reportForm.format === 'pdf' ? 1.2 : reportForm.format === 'excel' ? 0.8 : 1
  
  const estimatedSize = (baseSize + moduleMultiplier + detailMultiplier) * formatMultiplier
  return `约 ${estimatedSize.toFixed(1)} MB`
}

// 方法
const generateReport = async () => {
  if (!reportFormRef.value) return
  
  try {
    await reportFormRef.value.validate()
    
    generating.value = true
    
    // 模拟报告生成时间
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emit('generate', { ...reportForm })
    ElMessage.success('安全报告生成成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    generating.value = false
  }
}
</script>

<style lang="scss" scoped>
.security-report-dialog {
  .form-tip {
    margin-left: 12px;
    font-size: 12px;
    color: #909399;
  }

  .report-preview {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .preview-content {
      background: #f8fafc;
      border-radius: 8px;
      padding: 16px;

      .preview-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: #909399;
        }

        .value {
          font-size: 14px;
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .security-report-dialog {
    .report-preview {
      .preview-content {
        .preview-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }
    }
  }
}
</style>