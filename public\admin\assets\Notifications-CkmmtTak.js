import{_ as e}from"./index-DtXAftX0.js";/* empty css                     *//* empty css                       *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                */import{H as a}from"./echarts-D68jitv0.js";import{a_ as l,aY as t,U as s,aZ as d,at as i,bh as n,bi as o,a$ as u,bx as r,b9 as c,b8 as p,bp as _,bq as m,bm as b,bB as v,aM as f,bt as g,bu as y,by as h,ay as V,Q as w,R as k}from"./element-plus-h2SQQM64.js";import{L as x,r as C,e as U,k as j,l as z,E as q,z as B,t as T,D as R,y as $,B as P,F,Y as I,u as O}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const S={class:"app-container"},D={class:"stat-item"},M={class:"stat-content"},Y={class:"stat-number"},A={class:"stat-item"},E={class:"stat-content"},L={class:"stat-number"},N={class:"stat-item"},Q={class:"stat-content"},Z={class:"stat-number"},G={class:"stat-item"},H={class:"stat-content"},J={class:"stat-number"},K={class:"card-header"},W={key:0},X={key:1},ee={key:2},ae={class:"table-pagination"},le={class:"card-header"},te={class:"template-card"},se={class:"template-header"},de={class:"template-title"},ie={class:"template-actions"},ne={class:"template-content"},oe={class:"template-type"},ue={class:"template-preview"},re={class:"template-stats"},ce={class:"stat-item"},pe={class:"stat-item"},_e={class:"dialog-footer"},me={class:"dialog-footer"},be={class:"stats-container"},ve={class:"stats-card"},fe={class:"stats-number"},ge={class:"stats-card"},ye={class:"stats-number"},he={class:"stats-card"},Ve={class:"stats-number"},we={class:"stats-chart"},ke=e({__name:"Notifications",setup(e){const ke=x({total:1245,unread:89,today:56,readRate:78}),xe=x({page:1,per_page:10,type:"",status:""}),Ce=C([{id:1,title:"系统维护通知",type:"system",target:"all",send_count:1200,read_count:980,read_rate:82,status:"sent",created_at:"2024-01-01 10:00:00"},{id:2,title:"新功能上线通知",type:"user",target:"group",send_count:800,read_count:650,read_rate:81,status:"sent",created_at:"2024-01-01 09:00:00"}]),Ue=C([{id:1,name:"欢迎新用户",type:"user",content:"欢迎 {username} 加入我们的平台！",usage_count:120,created_at:"2024-01-01"},{id:2,name:"提现成功通知",type:"user",content:"您的提现申请已成功处理，金额：{amount}",usage_count:85,created_at:"2024-01-01"}]),je=C([{id:1,nickname:"用户1"},{id:2,nickname:"用户2"}]),ze=x({visible:!1}),qe=x({type:"system",target:"all",group_id:"",user_ids:[],title:"",content:"",channels:["web"],send_type:"now",scheduled_at:null,priority:"normal"}),Be={type:[{required:!0,message:"请选择通知类型",trigger:"change"}],target:[{required:!0,message:"请选择发送目标",trigger:"change"}],title:[{required:!0,message:"请输入通知标题",trigger:"blur"}],content:[{required:!0,message:"请输入通知内容",trigger:"blur"}]},Te=x({visible:!1,title:"新增模板"}),Re=x({name:"",type:"user",content:"",variables:""}),$e={name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],type:[{required:!0,message:"请选择模板类型",trigger:"change"}],content:[{required:!0,message:"请输入模板内容",trigger:"blur"}]},Pe=x({visible:!1}),Fe=x({send_count:0,read_count:0,click_count:0}),Ie=C({}),Oe=C(0),Se=C(!1),De=C(!1),Me=e=>({system:"系统通知",user:"用户通知",marketing:"营销通知",security:"安全通知"}[e]||e),Ye=e=>({sent:"已发送",pending:"待发送",failed:"发送失败"}[e]||e),Ae=()=>{Qe(),ze.visible=!0},Ee=()=>{Qe(),qe.target="group",ze.visible=!0},Le=()=>{Ze(),Te.title="新增模板",Te.visible=!0},Ne=()=>{Qe(),qe.send_type="scheduled",ze.visible=!0},Qe=()=>{Object.assign(qe,{type:"system",target:"all",group_id:"",user_ids:[],title:"",content:"",channels:["web"],send_type:"now",scheduled_at:null,priority:"normal"})},Ze=()=>{Object.assign(Re,{name:"",type:"user",content:"",variables:""})},Ge=async()=>{Se.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),w.success("通知发送成功"),ze.visible=!1,Je()}catch(e){w.error("通知发送失败")}finally{Se.value=!1}},He=async()=>{De.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),w.success("模板保存成功"),Te.visible=!1}catch(e){w.error("模板保存失败")}finally{De.value=!1}},Je=()=>{w.success("通知列表已刷新")},Ke=e=>{},We=e=>{xe.per_page=e,Je()},Xe=e=>{xe.page=e,Je()};return U(()=>{Oe.value=Ce.value.length}),(e,x)=>{const C=t,U=l,Qe=d,Ze=i,ea=p,aa=c,la=o,ta=u,sa=n,da=r,ia=m,na=v,oa=b,ua=f,ra=y,ca=g,pa=h,_a=_,ma=V;return z(),j("div",S,[q(Qe,{gutter:20},{default:B(()=>[q(U,{span:6},{default:B(()=>[q(C,{class:"stat-card"},{default:B(()=>[T("div",D,[x[24]||(x[24]=T("div",{class:"stat-icon total-icon"},[T("i",{class:"el-icon-bell"})],-1)),T("div",M,[T("div",Y,s(ke.total),1),x[23]||(x[23]=T("div",{class:"stat-label"},"总通知数",-1))])])]),_:1})]),_:1}),q(U,{span:6},{default:B(()=>[q(C,{class:"stat-card"},{default:B(()=>[T("div",A,[x[26]||(x[26]=T("div",{class:"stat-icon unread-icon"},[T("i",{class:"el-icon-message"})],-1)),T("div",E,[T("div",L,s(ke.unread),1),x[25]||(x[25]=T("div",{class:"stat-label"},"未读通知",-1))])])]),_:1})]),_:1}),q(U,{span:6},{default:B(()=>[q(C,{class:"stat-card"},{default:B(()=>[T("div",N,[x[28]||(x[28]=T("div",{class:"stat-icon today-icon"},[T("i",{class:"el-icon-calendar-today"})],-1)),T("div",Q,[T("div",Z,s(ke.today),1),x[27]||(x[27]=T("div",{class:"stat-label"},"今日发送",-1))])])]),_:1})]),_:1}),q(U,{span:6},{default:B(()=>[q(C,{class:"stat-card"},{default:B(()=>[T("div",G,[x[30]||(x[30]=T("div",{class:"stat-icon rate-icon"},[T("i",{class:"el-icon-data-analysis"})],-1)),T("div",H,[T("div",J,s(ke.readRate)+"%",1),x[29]||(x[29]=T("div",{class:"stat-label"},"阅读率",-1))])])]),_:1})]),_:1})]),_:1}),q(C,{style:{"margin-top":"20px"}},{header:B(()=>x[31]||(x[31]=[T("div",{class:"card-header"},[T("span",null,"⚡ 快速操作")],-1)])),default:B(()=>[q(Qe,{gutter:20},{default:B(()=>[q(U,{span:6},{default:B(()=>[q(Ze,{type:"primary",onClick:Ae,class:"quick-action-btn"},{default:B(()=>x[32]||(x[32]=[T("i",{class:"el-icon-s-promotion"},null,-1),R(" 发送系统通知 ",-1)])),_:1,__:[32]})]),_:1}),q(U,{span:6},{default:B(()=>[q(Ze,{type:"success",onClick:Ee,class:"quick-action-btn"},{default:B(()=>x[33]||(x[33]=[T("i",{class:"el-icon-user-solid"},null,-1),R(" 批量用户通知 ",-1)])),_:1,__:[33]})]),_:1}),q(U,{span:6},{default:B(()=>[q(Ze,{type:"warning",onClick:Le,class:"quick-action-btn"},{default:B(()=>x[34]||(x[34]=[T("i",{class:"el-icon-document"},null,-1),R(" 创建消息模板 ",-1)])),_:1,__:[34]})]),_:1}),q(U,{span:6},{default:B(()=>[q(Ze,{type:"info",onClick:Ne,class:"quick-action-btn"},{default:B(()=>x[35]||(x[35]=[T("i",{class:"el-icon-alarm-clock"},null,-1),R(" 定时通知 ",-1)])),_:1,__:[35]})]),_:1})]),_:1})]),_:1}),q(C,{style:{"margin-top":"20px"}},{header:B(()=>[T("div",K,[x[37]||(x[37]=T("span",null,"📋 通知列表",-1)),T("div",null,[q(aa,{modelValue:xe.type,"onUpdate:modelValue":x[0]||(x[0]=e=>xe.type=e),placeholder:"通知类型",clearable:"",style:{width:"120px","margin-right":"10px"}},{default:B(()=>[q(ea,{label:"系统通知",value:"system"}),q(ea,{label:"用户通知",value:"user"}),q(ea,{label:"营销通知",value:"marketing"}),q(ea,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"]),q(aa,{modelValue:xe.status,"onUpdate:modelValue":x[1]||(x[1]=e=>xe.status=e),placeholder:"状态",clearable:"",style:{width:"100px","margin-right":"10px"}},{default:B(()=>[q(ea,{label:"已发送",value:"sent"}),q(ea,{label:"待发送",value:"pending"}),q(ea,{label:"发送失败",value:"failed"})]),_:1},8,["modelValue"]),q(Ze,{type:"primary",onClick:Je},{default:B(()=>x[36]||(x[36]=[R("刷新",-1)])),_:1,__:[36]})])])]),default:B(()=>[q(sa,{data:Ce.value,style:{width:"100%"},onSelectionChange:Ke},{default:B(()=>[q(la,{type:"selection",width:"55"}),q(la,{prop:"id",label:"ID",width:"80"}),q(la,{prop:"title",label:"标题","min-width":"200","show-overflow-tooltip":""}),q(la,{prop:"type",label:"类型",width:"100"},{default:B(e=>{return[q(ta,{type:(a=e.row.type,{system:"primary",user:"success",marketing:"warning",security:"danger"}[a]||"info")},{default:B(()=>[R(s(Me(e.row.type)),1)]),_:2},1032,["type"])];var a}),_:1}),q(la,{prop:"target",label:"目标",width:"120"},{default:B(e=>["all"===e.row.target?(z(),j("span",W,"全部用户")):"group"===e.row.target?(z(),j("span",X,"用户组")):(z(),j("span",ee,"指定用户"))]),_:1}),q(la,{prop:"send_count",label:"发送数",width:"100"}),q(la,{prop:"read_count",label:"已读数",width:"100"}),q(la,{prop:"read_rate",label:"阅读率",width:"100"},{default:B(e=>[R(s(e.row.read_rate)+"% ",1)]),_:1}),q(la,{prop:"status",label:"状态",width:"100"},{default:B(e=>{return[q(ta,{type:(a=e.row.status,{sent:"success",pending:"warning",failed:"danger"}[a]||"info")},{default:B(()=>[R(s(Ye(e.row.status)),1)]),_:2},1032,["type"])];var a}),_:1}),q(la,{prop:"created_at",label:"创建时间",width:"160"}),q(la,{label:"操作",width:"200"},{default:B(e=>[q(Ze,{type:"primary",size:"small",onClick:a=>{return l=e.row,void w.info(`查看通知：${l.title}`);var l}},{default:B(()=>x[38]||(x[38]=[R(" 查看 ",-1)])),_:2,__:[38]},1032,["onClick"]),q(Ze,{type:"info",size:"small",onClick:a=>{return l=e.row,Fe.send_count=l.send_count,Fe.read_count=l.read_count,Fe.click_count=Math.floor(.3*l.read_count),Ie.value={title:{text:"通知统计",left:"center"},tooltip:{trigger:"item"},series:[{name:"通知统计",type:"pie",radius:"50%",data:[{value:Fe.read_count,name:"已读"},{value:Fe.send_count-Fe.read_count,name:"未读"}]}]},void(Pe.visible=!0);var l}},{default:B(()=>x[39]||(x[39]=[R(" 统计 ",-1)])),_:2,__:[39]},1032,["onClick"]),"failed"===e.row.status?(z(),$(Ze,{key:0,type:"warning",size:"small",onClick:a=>{return l=e.row,void w.info(`重新发送：${l.title}`);var l}},{default:B(()=>x[40]||(x[40]=[R(" 重发 ",-1)])),_:2,__:[40]},1032,["onClick"])):P("",!0),q(Ze,{type:"danger",size:"small",onClick:a=>{return l=e.row,void k.confirm(`确定要删除通知 ${l.title} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{w.success("删除成功"),Je()});var l}},{default:B(()=>x[41]||(x[41]=[R(" 删除 ",-1)])),_:2,__:[41]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),T("div",ae,[q(da,{"current-page":xe.page,"onUpdate:currentPage":x[2]||(x[2]=e=>xe.page=e),"page-size":xe.per_page,"onUpdate:pageSize":x[3]||(x[3]=e=>xe.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Oe.value,onSizeChange:We,onCurrentChange:Xe},null,8,["current-page","page-size","total"])])]),_:1}),q(C,{style:{"margin-top":"20px"}},{header:B(()=>[T("div",le,[x[43]||(x[43]=T("span",null,"📄 消息模板",-1)),q(Ze,{type:"primary",onClick:Le},{default:B(()=>x[42]||(x[42]=[R("新增模板",-1)])),_:1,__:[42]})])]),default:B(()=>[q(Qe,{gutter:20},{default:B(()=>[(z(!0),j(F,null,I(Ue.value,e=>(z(),$(U,{span:8,key:e.id},{default:B(()=>[T("div",te,[T("div",se,[T("div",de,s(e.name),1),T("div",ie,[q(Ze,{type:"text",onClick:a=>(e=>{Object.assign(Re,e),Te.title="编辑模板",Te.visible=!0})(e)},{default:B(()=>x[44]||(x[44]=[R("编辑",-1)])),_:2,__:[44]},1032,["onClick"]),q(Ze,{type:"text",onClick:a=>(e=>{qe.content=e.content,qe.type=e.type,ze.visible=!0})(e)},{default:B(()=>x[45]||(x[45]=[R("使用",-1)])),_:2,__:[45]},1032,["onClick"]),q(Ze,{type:"text",onClick:a=>(e=>{k.confirm(`确定要删除模板 ${e.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{w.success("删除成功")})})(e)},{default:B(()=>x[46]||(x[46]=[R("删除",-1)])),_:2,__:[46]},1032,["onClick"])])]),T("div",ne,[T("div",oe,s(Me(e.type)),1),T("div",ue,s(e.content),1),T("div",re,[T("span",ce,"使用次数: "+s(e.usage_count),1),T("span",pe,"创建时间: "+s(e.created_at),1)])])])]),_:2},1024))),128))]),_:1})]),_:1}),q(ma,{title:"发送通知",modelValue:ze.visible,"onUpdate:modelValue":x[15]||(x[15]=e=>ze.visible=e),width:"800px"},{footer:B(()=>[T("div",_e,[q(Ze,{onClick:x[14]||(x[14]=e=>ze.visible=!1)},{default:B(()=>x[56]||(x[56]=[R("取消",-1)])),_:1,__:[56]}),q(Ze,{type:"primary",onClick:Ge,loading:Se.value},{default:B(()=>[R(s("now"===qe.send_type?"立即发送":"定时发送"),1)]),_:1},8,["loading"])])]),default:B(()=>[q(_a,{model:qe,rules:Be,ref:"sendFormRef","label-width":"100px"},{default:B(()=>[q(ia,{label:"通知类型",prop:"type"},{default:B(()=>[q(aa,{modelValue:qe.type,"onUpdate:modelValue":x[4]||(x[4]=e=>qe.type=e),placeholder:"请选择通知类型"},{default:B(()=>[q(ea,{label:"系统通知",value:"system"}),q(ea,{label:"用户通知",value:"user"}),q(ea,{label:"营销通知",value:"marketing"}),q(ea,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1}),q(ia,{label:"发送目标",prop:"target"},{default:B(()=>[q(oa,{modelValue:qe.target,"onUpdate:modelValue":x[5]||(x[5]=e=>qe.target=e)},{default:B(()=>[q(na,{label:"all"},{default:B(()=>x[47]||(x[47]=[R("全部用户",-1)])),_:1,__:[47]}),q(na,{label:"group"},{default:B(()=>x[48]||(x[48]=[R("用户组",-1)])),_:1,__:[48]}),q(na,{label:"specific"},{default:B(()=>x[49]||(x[49]=[R("指定用户",-1)])),_:1,__:[49]})]),_:1},8,["modelValue"])]),_:1}),"group"===qe.target?(z(),$(ia,{key:0,label:"用户组"},{default:B(()=>[q(aa,{modelValue:qe.group_id,"onUpdate:modelValue":x[6]||(x[6]=e=>qe.group_id=e),placeholder:"请选择用户组"},{default:B(()=>[q(ea,{label:"VIP用户",value:"vip"}),q(ea,{label:"分销商",value:"distributor"}),q(ea,{label:"分站管理员",value:"substation"})]),_:1},8,["modelValue"])]),_:1})):P("",!0),"specific"===qe.target?(z(),$(ia,{key:1,label:"指定用户"},{default:B(()=>[q(aa,{modelValue:qe.user_ids,"onUpdate:modelValue":x[7]||(x[7]=e=>qe.user_ids=e),multiple:"",placeholder:"请选择用户"},{default:B(()=>[(z(!0),j(F,null,I(je.value,e=>(z(),$(ea,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):P("",!0),q(ia,{label:"通知标题",prop:"title"},{default:B(()=>[q(ua,{modelValue:qe.title,"onUpdate:modelValue":x[8]||(x[8]=e=>qe.title=e),placeholder:"请输入通知标题"},null,8,["modelValue"])]),_:1}),q(ia,{label:"通知内容",prop:"content"},{default:B(()=>[q(ua,{type:"textarea",modelValue:qe.content,"onUpdate:modelValue":x[9]||(x[9]=e=>qe.content=e),rows:4,placeholder:"请输入通知内容"},null,8,["modelValue"])]),_:1}),q(ia,{label:"发送方式"},{default:B(()=>[q(ca,{modelValue:qe.channels,"onUpdate:modelValue":x[10]||(x[10]=e=>qe.channels=e)},{default:B(()=>[q(ra,{label:"web"},{default:B(()=>x[50]||(x[50]=[R("站内信",-1)])),_:1,__:[50]}),q(ra,{label:"email"},{default:B(()=>x[51]||(x[51]=[R("邮件",-1)])),_:1,__:[51]}),q(ra,{label:"sms"},{default:B(()=>x[52]||(x[52]=[R("短信",-1)])),_:1,__:[52]}),q(ra,{label:"wechat"},{default:B(()=>x[53]||(x[53]=[R("微信",-1)])),_:1,__:[53]})]),_:1},8,["modelValue"])]),_:1}),q(ia,{label:"发送时间"},{default:B(()=>[q(oa,{modelValue:qe.send_type,"onUpdate:modelValue":x[11]||(x[11]=e=>qe.send_type=e)},{default:B(()=>[q(na,{label:"now"},{default:B(()=>x[54]||(x[54]=[R("立即发送",-1)])),_:1,__:[54]}),q(na,{label:"scheduled"},{default:B(()=>x[55]||(x[55]=[R("定时发送",-1)])),_:1,__:[55]})]),_:1},8,["modelValue"]),"scheduled"===qe.send_type?(z(),$(pa,{key:0,modelValue:qe.scheduled_at,"onUpdate:modelValue":x[12]||(x[12]=e=>qe.scheduled_at=e),type:"datetime",placeholder:"选择发送时间",style:{"margin-left":"10px"}},null,8,["modelValue"])):P("",!0)]),_:1}),q(ia,{label:"优先级"},{default:B(()=>[q(aa,{modelValue:qe.priority,"onUpdate:modelValue":x[13]||(x[13]=e=>qe.priority=e),placeholder:"请选择优先级"},{default:B(()=>[q(ea,{label:"低",value:"low"}),q(ea,{label:"普通",value:"normal"}),q(ea,{label:"高",value:"high"}),q(ea,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),q(ma,{title:Te.title,modelValue:Te.visible,"onUpdate:modelValue":x[21]||(x[21]=e=>Te.visible=e),width:"600px"},{footer:B(()=>[T("div",me,[q(Ze,{onClick:x[20]||(x[20]=e=>Te.visible=!1)},{default:B(()=>x[57]||(x[57]=[R("取消",-1)])),_:1,__:[57]}),q(Ze,{type:"primary",onClick:He,loading:De.value},{default:B(()=>x[58]||(x[58]=[R("保存",-1)])),_:1,__:[58]},8,["loading"])])]),default:B(()=>[q(_a,{model:Re,rules:$e,ref:"templateFormRef","label-width":"100px"},{default:B(()=>[q(ia,{label:"模板名称",prop:"name"},{default:B(()=>[q(ua,{modelValue:Re.name,"onUpdate:modelValue":x[16]||(x[16]=e=>Re.name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),q(ia,{label:"模板类型",prop:"type"},{default:B(()=>[q(aa,{modelValue:Re.type,"onUpdate:modelValue":x[17]||(x[17]=e=>Re.type=e),placeholder:"请选择模板类型"},{default:B(()=>[q(ea,{label:"系统通知",value:"system"}),q(ea,{label:"用户通知",value:"user"}),q(ea,{label:"营销通知",value:"marketing"}),q(ea,{label:"安全通知",value:"security"})]),_:1},8,["modelValue"])]),_:1}),q(ia,{label:"模板内容",prop:"content"},{default:B(()=>[q(ua,{type:"textarea",modelValue:Re.content,"onUpdate:modelValue":x[18]||(x[18]=e=>Re.content=e),rows:6,placeholder:"请输入模板内容，支持变量：{username}, {amount}, {time}等"},null,8,["modelValue"])]),_:1}),q(ia,{label:"变量说明"},{default:B(()=>[q(ua,{type:"textarea",modelValue:Re.variables,"onUpdate:modelValue":x[19]||(x[19]=e=>Re.variables=e),rows:3,placeholder:"请输入变量说明，如：{username}=用户名，{amount}=金额"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),q(ma,{title:"通知统计",modelValue:Pe.visible,"onUpdate:modelValue":x[22]||(x[22]=e=>Pe.visible=e),width:"800px"},{default:B(()=>[T("div",be,[q(Qe,{gutter:20},{default:B(()=>[q(U,{span:8},{default:B(()=>[T("div",ve,[T("div",fe,s(Fe.send_count),1),x[59]||(x[59]=T("div",{class:"stats-label"},"发送数量",-1))])]),_:1}),q(U,{span:8},{default:B(()=>[T("div",ge,[T("div",ye,s(Fe.read_count),1),x[60]||(x[60]=T("div",{class:"stats-label"},"已读数量",-1))])]),_:1}),q(U,{span:8},{default:B(()=>[T("div",he,[T("div",Ve,s(Fe.click_count),1),x[61]||(x[61]=T("div",{class:"stats-label"},"点击数量",-1))])]),_:1})]),_:1}),T("div",we,[q(O(a),{class:"chart",option:Ie.value,autoresize:""},null,8,["option"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-6979b6e4"]]);export{ke as default};
