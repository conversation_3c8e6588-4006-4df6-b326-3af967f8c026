import api from './index'

// 仪表板相关API - 支持权限控制
export const getDashboardStats = (params = {}) => {
  return api.get('/admin/dashboard/full-stats', { params })
}

export const getDashboardCharts = () => {
  return api.get('/admin/dashboard/charts')
}

export const getIncomeChart = (params) => {
  return api.get('/admin/dashboard/income-chart', { params })
}

export const getUserGrowthChart = (params) => {
  return api.get('/admin/dashboard/user-growth-chart', { params })
}

export const getOrderChart = (params) => {
  return api.get('/admin/dashboard/order-chart', { params })
}

export const getRegionChart = () => {
  return api.get('/admin/dashboard/region-chart')
}

export const getPopularGroups = () => {
  return api.get('/admin/dashboard/popular-groups')
}

export const getActiveUsers = () => {
  return api.get('/admin/dashboard/active-users')
}

export const getRecentActivities = () => {
  return api.get('/admin/dashboard/recent-activities')
}

export const getIncomeTrend = (params) => {
  return api.get('/admin/dashboard/income-trend', { params })
}

export const getOrderSourceDistribution = () => {
  return api.get('/admin/dashboard/order-source')
}

export const getUserGrowthData = (params) => {
  return api.get('/admin/dashboard/user-growth', { params })
}

export const getSystemStatus = () => {
  return api.get('/admin/dashboard/system-status')
}

export const getRecentOrders = () => {
  return api.get('/admin/dashboard/recent-orders')
}

export const getTopDistributors = () => {
  return api.get('/admin/dashboard/top-distributors')
}

export const getRealTimeData = () => {
  return api.get('/admin/dashboard/realtime')
}

// 获取权限过滤后的用户统计数据
export const getUserStatsWithPermission = (params = {}) => {
  return api.get('/admin/users/stats', { params })
}

// 获取权限过滤后的订单统计数据
export const getOrderStatsWithPermission = (params = {}) => {
  return api.get('/admin/orders/stats', { params })
}

// 获取权限过滤后的群组统计数据
export const getGroupStatsWithPermission = (params = {}) => {
  return api.get('/admin/groups/stats', { params })
}

// 获取权限过滤后的财务统计数据
export const getFinanceStatsWithPermission = (params = {}) => {
  return api.get('/admin/finance/stats', { params })
}