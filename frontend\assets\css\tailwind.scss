// Tailwind CSS SCSS 集成文件
// 用于 Nuxt.js 项目的 SCSS 预处理器配置

// Tailwind CSS 基础样式
@tailwind base;
@tailwind components;
@tailwind utilities;

// SCSS 变量定义（与 Tailwind 配色方案保持一致）
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;

$success-50: #f0fdf4;
$success-100: #dcfce7;
$success-200: #bbf7d0;
$success-300: #86efac;
$success-400: #4ade80;
$success-500: #22c55e;
$success-600: #16a34a;
$success-700: #15803d;
$success-800: #166534;
$success-900: #14532d;

$warning-50: #fffbeb;
$warning-100: #fef3c7;
$warning-200: #fde68a;
$warning-300: #fcd34d;
$warning-400: #fbbf24;
$warning-500: #f59e0b;
$warning-600: #d97706;
$warning-700: #b45309;
$warning-800: #92400e;
$warning-900: #78350f;

$error-50: #fef2f2;
$error-100: #fee2e2;
$error-200: #fecaca;
$error-300: #fca5a5;
$error-400: #f87171;
$error-500: #ef4444;
$error-600: #dc2626;
$error-700: #b91c1c;
$error-800: #991b1b;
$error-900: #7f1d1d;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// SCSS 混合宏（Mixins）
@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@mixin gradient-bg($from, $to) {
  background: linear-gradient(135deg, $from 0%, $to 100%);
}

@mixin shadow-soft($size: md) {
  @if $size == sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  } @else if $size == md {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  } @else if $size == lg {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  } @else if $size == xl {
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.16);
  }
}

@mixin transition-smooth($property: all, $duration: 0.3s) {
  transition: $property $duration cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin hover-lift($distance: 4px) {
  @include transition-smooth(transform);
  
  &:hover {
    transform: translateY(-$distance);
  }
}

// 响应式断点混合宏
@mixin mobile {
  @media (max-width: 640px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 641px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: 1440px) {
    @content;
  }
}

// 自定义组件样式
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg;
    @include transition-smooth();
    @include shadow-soft(sm);
    
    &:hover {
      @include shadow-soft(md);
    }
  }
  
  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg;
    @include transition-smooth();
  }
  
  .card-glass {
    @include glass-effect(0.1);
    @apply rounded-xl p-6;
    @include shadow-soft(lg);
  }
  
  .card-modern {
    @apply bg-white rounded-xl p-6 border border-gray-100;
    @include shadow-soft(md);
    @include transition-smooth();
    
    &:hover {
      @include shadow-soft(lg);
      @include hover-lift(2px);
    }
  }
  
  .input-modern {
    @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @include transition-smooth();
    
    &::placeholder {
      @apply text-gray-400;
    }
  }
  
  .gradient-primary {
    @include gradient-bg($primary-500, $primary-600);
  }
  
  .gradient-success {
    @include gradient-bg($success-500, $success-600);
  }
  
  .gradient-warning {
    @include gradient-bg($warning-500, $warning-600);
  }
  
  .gradient-error {
    @include gradient-bg($error-500, $error-600);
  }
}

// 自定义工具类
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.700'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .backdrop-blur-glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  .scrollbar-thin {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: theme('colors.gray.100');
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: theme('colors.gray.300');
      border-radius: 3px;
      
      &:hover {
        background: theme('colors.gray.400');
      }
    }
  }
}

// 全局样式重置和增强
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-gray-800 bg-gray-50;
    font-family: 'Inter', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  * {
    @apply border-gray-200;
  }
  
  input, textarea, select {
    @apply focus:outline-none;
  }
  
  button {
    @apply focus:outline-none;
  }
  
  a {
    @apply text-primary-600 hover:text-primary-700;
    @include transition-smooth(color);
  }
}

// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 动画工具类
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-in-up {
    animation: slideInUp 0.4s ease-out;
  }
  
  .animate-slide-in-down {
    animation: slideInDown 0.4s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
}
