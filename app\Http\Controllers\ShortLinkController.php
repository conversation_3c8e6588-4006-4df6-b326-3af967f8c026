<?php

namespace App\Http\Controllers;

use App\Models\PromotionLink;
use App\Services\PromotionService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * 短链接重定向控制器
 */
class ShortLinkController extends Controller
{
    protected PromotionService $promotionService;

    public function __construct(PromotionService $promotionService)
    {
        $this->promotionService = $promotionService;
    }

    /**
     * 短链接重定向
     */
    public function redirect(string $shortCode, Request $request): RedirectResponse
    {
        try {
            // 解析短链接
            $linkData = $this->promotionService->resolveShortUrl($shortCode);
            
            if (!$linkData) {
                // 链接不存在或已过期
                return redirect()->route('error.page', ['type' => 'link_expired'])
                    ->with('error', '链接不存在或已过期');
            }

            // 记录访问信息
            $this->recordVisit($shortCode, $request);

            // 重定向到原始URL
            return redirect($linkData['original_url']);

        } catch (\Exception $e) {
            Log::error('短链接重定向失败', [
                'short_code' => $shortCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('error.page', ['type' => 'system_error'])
                ->with('error', '系统错误，请稍后重试');
        }
    }

    /**
     * 获取短链接信息（API接口）
     */
    public function info(string $shortCode): JsonResponse
    {
        try {
            $link = PromotionLink::findByShortCode($shortCode);
            
            if (!$link) {
                return $this->error('链接不存在', null, 404);
            }

            if (!$link->isValid()) {
                return $this->error('链接已失效', null, 410);
            }

            return $this->success([
                'short_code' => $link->short_code,
                'title' => $link->title,
                'description' => $link->description,
                'original_url' => $link->original_url,
                'group' => [
                    'id' => $link->group->id,
                    'title' => $link->group->title,
                    'category' => $link->group->category_name,
                    'price' => $link->group->formatted_price
                ],
                'stats' => [
                    'click_count' => $link->click_count,
                    'created_at' => $link->created_at->format('Y-m-d H:i:s'),
                    'expires_at' => $link->expires_at->format('Y-m-d H:i:s'),
                    'remaining_days' => $link->remaining_days
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('获取链接信息失败：' . $e->getMessage());
        }
    }

    /**
     * 预览短链接（不增加点击量）
     */
    public function preview(string $shortCode): JsonResponse
    {
        try {
            $link = PromotionLink::findByShortCode($shortCode);
            
            if (!$link) {
                return $this->error('链接不存在', null, 404);
            }

            return $this->success([
                'short_code' => $link->short_code,
                'short_url' => $link->short_url,
                'original_url' => $link->original_url,
                'title' => $link->title,
                'description' => $link->description,
                'group' => $link->group->only(['id', 'title', 'category_name', 'price']),
                'is_valid' => $link->isValid(),
                'is_expired' => $link->isExpired(),
                'stats' => $link->getStats()
            ]);

        } catch (\Exception $e) {
            return $this->error('预览链接失败：' . $e->getMessage());
        }
    }

    /**
     * 生成二维码
     */
    public function qrcode(string $shortCode, Request $request): \Illuminate\Http\Response
    {
        try {
            $link = PromotionLink::findByShortCode($shortCode);
            
            if (!$link) {
                abort(404, '链接不存在');
            }

            // 获取二维码参数
            $size = $request->get('size', 300);
            $margin = $request->get('margin', 2);
            $format = $request->get('format', 'png');

            // 验证参数
            $size = max(100, min(1000, (int)$size));
            $margin = max(0, min(10, (int)$margin));
            $format = in_array($format, ['png', 'jpg', 'svg']) ? $format : 'png';

            // 生成二维码
            $qrCodeUrl = $this->promotionService->generateQRCode(
                $link->short_url,
                $link->group,
                [
                    'size' => $size,
                    'margin' => $margin,
                    'format' => $format
                ]
            );

            // 返回二维码图片
            $qrCodeContent = file_get_contents($qrCodeUrl);
            
            return response($qrCodeContent)
                ->header('Content-Type', 'image/' . $format)
                ->header('Cache-Control', 'public, max-age=3600')
                ->header('Content-Disposition', 'inline; filename="qrcode.' . $format . '"');

        } catch (\Exception $e) {
            Log::error('生成二维码失败', [
                'short_code' => $shortCode,
                'error' => $e->getMessage()
            ]);

            abort(500, '生成二维码失败');
        }
    }

    /**
     * 批量检查链接状态
     */
    public function batchCheck(Request $request): JsonResponse
    {
        $validator = \Validator::make($request->all(), [
            'short_codes' => 'required|array|max:100',
            'short_codes.*' => 'string|max:10'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $shortCodes = $request->short_codes;
            $results = [];

            foreach ($shortCodes as $shortCode) {
                $link = PromotionLink::findByShortCode($shortCode);
                
                $results[$shortCode] = [
                    'exists' => $link !== null,
                    'is_valid' => $link ? $link->isValid() : false,
                    'is_expired' => $link ? $link->isExpired() : false,
                    'click_count' => $link ? $link->click_count : 0,
                    'expires_at' => $link ? $link->expires_at->format('Y-m-d H:i:s') : null
                ];
            }

            return $this->success([
                'results' => $results,
                'total_checked' => count($shortCodes),
                'valid_count' => count(array_filter($results, fn($r) => $r['is_valid'])),
                'expired_count' => count(array_filter($results, fn($r) => $r['is_expired'])),
                'not_found_count' => count(array_filter($results, fn($r) => !$r['exists']))
            ]);

        } catch (\Exception $e) {
            return $this->error('批量检查失败：' . $e->getMessage());
        }
    }

    /**
     * 获取热门短链接
     */
    public function popular(Request $request): JsonResponse
    {
        try {
            $limit = min(50, max(1, $request->get('limit', 10)));
            
            $popularLinks = PromotionLink::getPopularLinks($limit);

            $data = $popularLinks->map(function ($link) {
                return [
                    'short_code' => $link->short_code,
                    'short_url' => $link->short_url,
                    'title' => $link->title,
                    'group_title' => $link->group->title,
                    'click_count' => $link->click_count,
                    'click_rate' => $link->click_rate,
                    'created_at' => $link->created_at->format('Y-m-d H:i:s')
                ];
            });

            return $this->success([
                'popular_links' => $data,
                'total' => $data->count()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取热门链接失败：' . $e->getMessage());
        }
    }

    /**
     * 记录访问信息
     */
    private function recordVisit(string $shortCode, Request $request): void
    {
        try {
            // 获取访问者信息
            $visitorInfo = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
                'timestamp' => now()->toDateTimeString()
            ];

            // 记录到日志
            Log::info('短链接访问', [
                'short_code' => $shortCode,
                'visitor_info' => $visitorInfo
            ]);

            // 这里可以扩展记录更详细的访问统计
            // 比如保存到专门的访问日志表中

        } catch (\Exception $e) {
            Log::error('记录访问信息失败', [
                'short_code' => $shortCode,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取链接访问统计
     */
    public function stats(string $shortCode, Request $request): JsonResponse
    {
        try {
            $link = PromotionLink::findByShortCode($shortCode);
            
            if (!$link) {
                return $this->error('链接不存在', null, 404);
            }

            // 检查权限（只有创建者或管理员可以查看统计）
            if (!auth()->check() || 
                (auth()->id() !== $link->created_by && !auth()->user()->hasRole('admin'))) {
                return $this->error('无权限查看统计信息', null, 403);
            }

            $days = $request->get('days', 7);
            
            return $this->success([
                'basic_stats' => $link->getStats(),
                'click_trend' => $link->getClickTrend($days),
                'link_info' => [
                    'short_code' => $link->short_code,
                    'title' => $link->title,
                    'group_title' => $link->group->title,
                    'created_at' => $link->created_at->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 短链接健康检查
     */
    public function healthCheck(): JsonResponse
    {
        try {
            $stats = PromotionLink::getStatsSummary();
            
            return $this->success([
                'service_status' => 'healthy',
                'timestamp' => now()->toDateTimeString(),
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return $this->error('健康检查失败：' . $e->getMessage(), null, 500);
        }
    }
}