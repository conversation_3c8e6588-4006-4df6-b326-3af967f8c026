import{_ as e}from"./index-DtXAftX0.js";import{T as t,U as a,o as s,c6 as l,c7 as r,c2 as i}from"./element-plus-h2SQQM64.js";/* empty css                                                                 */import{r as n,c,k as d,l as u,t as o,B as p,E as v,z as _,y as f,C as y,u as g}from"./vue-vendor-Dy164gUc.js";const m={class:"stat-card__content"},k={class:"stat-card__icon"},b={class:"icon-wrapper"},h={class:"stat-card__data"},S={class:"stat-value"},x={class:"value-number"},w={key:0,class:"value-unit"},C={class:"stat-title"},j={key:0,class:"stat-card__trend"},q={class:"trend-value"},B=e({__name:"StatCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},unit:{type:String,default:""},change:{type:Number,default:void 0},trend:{type:String,default:"flat",validator:e=>["up","down","flat"].includes(e)},icon:{type:String,required:!0},color:{type:String,default:"primary",validator:e=>["primary","success","warning","info","danger"].includes(e)},clickable:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:B}){const L=e,T=B,F=n(null),M=c(()=>"number"==typeof L.value?L.value>=1e4?(L.value/1e4).toFixed(1)+"W":L.value>=1e3?(L.value/1e3).toFixed(1)+"K":L.value.toLocaleString():L.value),N=c(()=>({"trend--up":"up"===L.trend,"trend--down":"down"===L.trend,"trend--flat":"flat"===L.trend})),R=e=>{L.clickable&&(z(e),T("click"))},z=e=>{const t=F.value;if(!t)return;const a=e.currentTarget.getBoundingClientRect(),s=Math.max(a.width,a.height),l=e.clientX-a.left-s/2,r=e.clientY-a.top-s/2;t.style.width=t.style.height=s+"px",t.style.left=l+"px",t.style.top=r+"px",t.classList.add("ripple-active"),setTimeout(()=>{t.classList.remove("ripple-active")},600)};return(n,c)=>{const B=t;return u(),d("div",{class:s(["stat-card",[`stat-card--${e.color}`,{"stat-card--clickable":e.clickable}]]),onClick:R},[c[1]||(c[1]=o("div",{class:"stat-card__bg"},[o("div",{class:"bg-pattern"}),o("div",{class:"bg-gradient"})],-1)),o("div",m,[o("div",k,[o("div",b,[v(B,null,{default:_(()=>[(u(),f(y(e.icon)))]),_:1})])]),o("div",h,[o("div",S,[o("span",x,a(M.value),1),e.unit?(u(),d("span",w,a(e.unit),1)):p("",!0)]),o("div",C,a(e.title),1)]),void 0!==e.change?(u(),d("div",j,[o("div",{class:s(["trend-indicator",N.value])},[v(B,{class:"trend-icon"},{default:_(()=>["up"===e.trend?(u(),f(g(l),{key:0})):p("",!0),"down"===e.trend?(u(),f(g(r),{key:1})):p("",!0),"flat"===e.trend?(u(),f(g(i),{key:2})):p("",!0)]),_:1}),o("span",q,a(Math.abs(e.change))+"%",1)],2),c[0]||(c[0]=o("div",{class:"trend-label"},"较昨日",-1))])):p("",!0)]),c[2]||(c[2]=o("div",{class:"stat-card__hover-effect"},null,-1)),o("div",{class:"stat-card__ripple",ref_key:"rippleRef",ref:F},null,512)],2)}}},[["__scopeId","data-v-53e6a581"]]);export{B as S};
