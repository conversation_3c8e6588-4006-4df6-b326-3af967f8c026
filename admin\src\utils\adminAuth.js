// 管理员权限设置工具
import { setToken, setRefreshToken } from './auth'

// 模拟管理员用户信息
export const mockAdminUser = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  name: '系统管理员',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  role: 'admin',
  roles: ['admin', 'manager', 'user'],
  permissions: [
    'dashboard:view',
    'dashboard:analytics',
    'community:view',
    'community:create',
    'community:edit',
    'community:delete',
    'users:view',
    'users:create',
    'users:edit',
    'users:delete',
    'users:analytics',
    'agent:view',
    'agent:create',
    'agent:edit',
    'agent:delete',
    'agent:commission',
    'distribution:view',
    'distribution:create',
    'distribution:edit',
    'distribution:delete',
    'promotion:view',
    'promotion:create',
    'promotion:edit',
    'promotion:delete',
    'antiblock:view',
    'antiblock:create',
    'antiblock:edit',
    'antiblock:delete',
    'orders:view',
    'orders:create',
    'orders:edit',
    'orders:delete',
    'finance:view',
    'finance:analytics',
    'finance:commission',
    'finance:withdraw',
    'payment:view',
    'payment:create',
    'payment:edit',
    'payment:delete',
    'permission:view',
    'permission:create',
    'permission:edit',
    'permission:delete',
    'system:view',
    'system:settings',
    'system:monitor',
    'system:logs',
    'system:notifications',
    'system:files'
  ],
  loginTime: new Date().toISOString(),
  lastLoginTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
  status: 'active'
}

// 模拟JWT Token
export const mockAdminToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IueuoeeQhuWRmCIsInJvbGUiOiJhZG1pbiIsImlhdCI6MTY5MDAwMDAwMCwiZXhwIjoxNjkwNjA0ODAwfQ.mock_admin_token_signature'

// 模拟Refresh Token
export const mockRefreshToken = 'refresh_token_mock_admin_12345'

// 设置管理员权限
export function setAdminAuth() {
  try {
    // 设置Token
    setToken(mockAdminToken)
    setRefreshToken(mockRefreshToken)
    
    // 设置用户信息
    localStorage.setItem('userInfo', JSON.stringify(mockAdminUser))
    localStorage.setItem('user_role', 'admin')
    localStorage.setItem('user_permissions', JSON.stringify(mockAdminUser.permissions))
    
    // 设置会话信息
    sessionStorage.setItem('isAuthenticated', 'true')
    sessionStorage.setItem('loginTime', mockAdminUser.loginTime)
    
    console.log('✅ 管理员权限设置成功')
    console.log('👤 用户信息:', mockAdminUser)
    console.log('🔑 Token:', mockAdminToken)
    
    return true
  } catch (error) {
    console.error('❌ 设置管理员权限失败:', error)
    return false
  }
}

// 清除所有权限
export function clearAllAuth() {
  try {
    // 清除Cookies
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
    })
    
    // 清除localStorage
    localStorage.clear()
    
    // 清除sessionStorage
    sessionStorage.clear()
    
    console.log('🗑️ 所有权限已清除')
    return true
  } catch (error) {
    console.error('❌ 清除权限失败:', error)
    return false
  }
}

// 检查当前权限状态
export function checkAuthStatus() {
  const token = localStorage.getItem('Admin-Token') || document.cookie.includes('Admin-Token')
  const userInfo = localStorage.getItem('userInfo')
  const role = localStorage.getItem('user_role')
  
  console.log('🔍 当前权限状态:')
  console.log('Token存在:', !!token)
  console.log('用户信息:', userInfo ? JSON.parse(userInfo) : '无')
  console.log('用户角色:', role || '无')
  
  return {
    hasToken: !!token,
    userInfo: userInfo ? JSON.parse(userInfo) : null,
    role: role || null
  }
}

// 设置特定角色权限
export function setRoleAuth(role = 'admin') {
  const roleConfigs = {
    admin: {
      ...mockAdminUser,
      role: 'admin',
      roles: ['admin', 'manager', 'user'],
      permissions: mockAdminUser.permissions
    },
    manager: {
      ...mockAdminUser,
      id: 2,
      username: 'manager',
      name: '部门经理',
      role: 'manager',
      roles: ['manager', 'user'],
      permissions: [
        'dashboard:view',
        'community:view',
        'community:create',
        'community:edit',
        'users:view',
        'users:edit',
        'orders:view',
        'orders:edit',
        'finance:view'
      ]
    },
    user: {
      ...mockAdminUser,
      id: 3,
      username: 'user',
      name: '普通用户',
      role: 'user',
      roles: ['user'],
      permissions: [
        'dashboard:view',
        'community:view',
        'orders:view'
      ]
    }
  }
  
  const userConfig = roleConfigs[role] || roleConfigs.admin
  
  try {
    setToken(mockAdminToken)
    setRefreshToken(mockRefreshToken)
    
    localStorage.setItem('userInfo', JSON.stringify(userConfig))
    localStorage.setItem('user_role', role)
    localStorage.setItem('user_permissions', JSON.stringify(userConfig.permissions))
    
    sessionStorage.setItem('isAuthenticated', 'true')
    sessionStorage.setItem('loginTime', userConfig.loginTime)
    
    console.log(`✅ ${role} 权限设置成功`)
    console.log('👤 用户信息:', userConfig)
    
    return true
  } catch (error) {
    console.error(`❌ 设置 ${role} 权限失败:`, error)
    return false
  }
}

// 验证权限
export function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('user_permissions') || '[]')
    return permissions.includes(permission)
  } catch {
    return false
  }
}

// 验证角色
export function hasRole(role) {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    return userInfo.roles && userInfo.roles.includes(role)
  } catch {
    return false
  }
}

// 获取当前用户信息
export function getCurrentUser() {
  try {
    return JSON.parse(localStorage.getItem('userInfo') || '{}')
  } catch {
    return null
  }
}

// 在浏览器控制台中可用的快捷方法
if (typeof window !== 'undefined') {
  window.setAdminAuth = setAdminAuth
  window.clearAllAuth = clearAllAuth
  window.checkAuthStatus = checkAuthStatus
  window.setRoleAuth = setRoleAuth
  window.hasPermission = hasPermission
  window.hasRole = hasRole
  window.getCurrentUser = getCurrentUser
  
  console.log('🔧 管理员权限工具已加载到全局')
  console.log('可用方法:')
  console.log('- setAdminAuth() - 设置管理员权限')
  console.log('- setRoleAuth("admin"|"manager"|"user") - 设置特定角色权限')
  console.log('- clearAllAuth() - 清除所有权限')
  console.log('- checkAuthStatus() - 检查当前权限状态')
  console.log('- hasPermission("permission") - 检查权限')
  console.log('- hasRole("role") - 检查角色')
  console.log('- getCurrentUser() - 获取当前用户信息')
}
