<!-- 书签收藏组件 -->
<!-- admin/src/components/navigation/Bookmarks.vue -->

<template>
  <div class="bookmarks-manager" :class="bookmarksClasses">
    <!-- 书签头部 -->
    <div class="bookmarks-header">
      <div class="header-left">
        <el-icon class="bookmarks-icon"><StarFilled /></el-icon>
        <h3 class="bookmarks-title">我的书签</h3>
        <el-badge :value="bookmarks.length" :max="99" class="bookmarks-count" />
      </div>
      <div class="header-actions">
        <el-tooltip content="书签设置">
          <el-button 
            type="text" 
            size="small" 
            @click="showSettings = !showSettings"
            class="settings-btn"
          >
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="导入/导出">
          <el-button 
            type="text" 
            size="small" 
            @click="showImportExport = true"
            class="import-export-btn"
          >
            <el-icon><Upload /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 搜索和过滤 -->
    <div class="bookmarks-toolbar" v-if="!compact">
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索书签..."
          :prefix-icon="Search"
          size="small"
          clearable
          @input="handleSearch"
          class="bookmarks-search"
        />
      </div>
      <div class="filter-container">
        <el-select
          v-model="selectedCategory"
          placeholder="分类筛选"
          size="small"
          clearable
          @change="handleCategoryFilter"
          class="category-filter"
        >
          <el-option
            v-for="category in categories"
            :key="category.value"
            :label="category.label"
            :value="category.value"
          />
        </el-select>
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          size="small"
          @change="handleSort"
          class="sort-select"
        >
          <el-option label="最近添加" value="created" />
          <el-option label="最近访问" value="lastVisit" />
          <el-option label="访问次数" value="visitCount" />
          <el-option label="字母顺序" value="alphabetical" />
        </el-select>
      </div>
    </div>
    
    <!-- 书签设置面板 -->
    <transition name="settings-slide">
      <div v-show="showSettings" class="settings-panel">
        <div class="settings-item">
          <label class="setting-label">显示模式</label>
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">网格</el-radio-button>
            <el-radio-button label="list">列表</el-radio-button>
            <el-radio-button label="compact">紧凑</el-radio-button>
          </el-radio-group>
        </div>
        <div class="settings-item">
          <label class="setting-label">自动分类</label>
          <el-switch v-model="autoCategory" />
        </div>
        <div class="settings-item">
          <label class="setting-label">显示访问统计</label>
          <el-switch v-model="showStats" />
        </div>
      </div>
    </transition>
    
    <!-- 书签内容 -->
    <div class="bookmarks-content" :class="`view-${viewMode}`">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <!-- 书签列表 -->
      <div v-else-if="filteredBookmarks.length > 0" class="bookmarks-list">
        <!-- 分组显示 -->
        <div 
          v-for="group in groupedBookmarks" 
          :key="group.category"
          class="bookmark-group"
        >
          <div class="group-header" @click="toggleGroup(group.category)">
            <div class="group-title">
              <el-icon><component :is="getCategoryIcon(group.category)" /></el-icon>
              <span>{{ getCategoryName(group.category) }}</span>
              <el-badge :value="group.items.length" class="group-count" />
            </div>
            <el-icon 
              class="group-toggle"
              :class="{ rotated: expandedGroups.includes(group.category) }"
            >
              <ArrowRight />
            </el-icon>
          </div>
          
          <transition name="group-expand">
            <div 
              v-show="expandedGroups.includes(group.category)" 
              class="group-items"
            >
              <div
                v-for="bookmark in group.items"
                :key="bookmark.id"
                class="bookmark-item"
                :class="getBookmarkClasses(bookmark)"
                @click="navigateToBookmark(bookmark)"
                @contextmenu.prevent="showBookmarkContextMenu($event, bookmark)"
              >
                <!-- 书签图标 -->
                <div class="bookmark-icon">
                  <el-avatar 
                    v-if="bookmark.favicon" 
                    :src="bookmark.favicon" 
                    :size="viewMode === 'compact' ? 20 : 32"
                    @error="handleFaviconError"
                  >
                    <el-icon><component :is="bookmark.icon || 'Link'" /></el-icon>
                  </el-avatar>
                  <div v-else class="icon-placeholder">
                    <el-icon><component :is="bookmark.icon || 'Link'" /></el-icon>
                  </div>
                </div>
                
                <!-- 书签信息 -->
                <div class="bookmark-info">
                  <div class="bookmark-title" :title="bookmark.title">
                    {{ bookmark.title }}
                  </div>
                  <div class="bookmark-url" v-if="viewMode !== 'compact'" :title="bookmark.url">
                    {{ formatUrl(bookmark.url) }}
                  </div>
                  <div class="bookmark-description" v-if="bookmark.description && viewMode === 'grid'">
                    {{ bookmark.description }}
                  </div>
                  <div class="bookmark-meta" v-if="showStats && viewMode !== 'compact'">
                    <div class="meta-item">
                      <el-icon><View /></el-icon>
                      <span>{{ bookmark.visitCount || 0 }} 次访问</span>
                    </div>
                    <div class="meta-item">
                      <el-icon><Clock /></el-icon>
                      <span>{{ formatDate(bookmark.lastVisit) }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- 书签操作 -->
                <div class="bookmark-actions">
                  <el-button 
                    type="text" 
                    size="small" 
                    @click.stop="toggleBookmarkFavorite(bookmark)"
                    class="favorite-btn"
                    :class="{ active: bookmark.isFavorite }"
                  >
                    <el-icon>
                      <component :is="bookmark.isFavorite ? 'StarFilled' : 'Star'" />
                    </el-icon>
                  </el-button>
                  <el-dropdown 
                    @command="handleBookmarkAction"
                    trigger="click"
                    class="bookmark-menu"
                  >
                    <el-button type="text" size="small" @click.stop>
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="`edit-${bookmark.id}`">
                          <el-icon><Edit /></el-icon>
                          编辑
                        </el-dropdown-item>
                        <el-dropdown-item :command="`copy-${bookmark.id}`">
                          <el-icon><DocumentCopy /></el-icon>
                          复制链接
                        </el-dropdown-item>
                        <el-dropdown-item :command="`open-new-${bookmark.id}`">
                          <el-icon><Link /></el-icon>
                          新窗口打开
                        </el-dropdown-item>
                        <el-dropdown-item :command="`category-${bookmark.id}`" divided>
                          <el-icon><Folder /></el-icon>
                          更改分类
                        </el-dropdown-item>
                        <el-dropdown-item :command="`delete-${bookmark.id}`" class="danger-item">
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">
          <el-icon><Star /></el-icon>
        </div>
        <div class="empty-text">
          <h4>{{ searchQuery ? '没有找到匹配的书签' : '还没有书签' }}</h4>
          <p>{{ searchQuery ? '尝试使用不同的关键词搜索' : '开始收藏你喜欢的页面吧' }}</p>
        </div>
        <el-button type="primary" @click="showAddDialog = true" v-if="!searchQuery">
          <el-icon><Plus /></el-icon>
          添加书签
        </el-button>
      </div>
    </div>
    
    <!-- 添加书签对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加书签"
      width="500px"
      :before-close="handleAddDialogClose"
    >
      <el-form :model="newBookmark" :rules="bookmarkRules" ref="addBookmarkForm">
        <el-form-item label="标题" prop="title">
          <el-input v-model="newBookmark.title" placeholder="请输入书签标题" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="newBookmark.url" placeholder="请输入完整的URL地址" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="newBookmark.description" 
            type="textarea" 
            :rows="2"
            placeholder="添加描述（可选）" 
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="newBookmark.category" placeholder="选择分类">
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="newBookmark.tags"
            multiple
            filterable
            allow-create
            placeholder="添加标签"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddBookmark" :loading="isSaving">
          保存
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 导入导出对话框 -->
    <el-dialog
      v-model="showImportExport"
      title="导入/导出书签"
      width="600px"
    >
      <el-tabs v-model="importExportTab">
        <el-tab-pane label="导出书签" name="export">
          <div class="export-panel">
            <p>选择导出格式:</p>
            <el-radio-group v-model="exportFormat">
              <el-radio label="json">JSON 格式</el-radio>
              <el-radio label="html">HTML 书签文件</el-radio>
              <el-radio label="csv">CSV 表格</el-radio>
            </el-radio-group>
            <div class="export-actions">
              <el-button type="primary" @click="handleExport">
                <el-icon><Download /></el-icon>
                导出书签
              </el-button>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="导入书签" name="import">
          <div class="import-panel">
            <el-upload
              :before-upload="handleImportFile"
              :show-file-list="false"
              accept=".json,.html,.csv"
              class="import-upload"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
            </el-upload>
            <p class="import-note">支持 JSON、HTML 书签文件和 CSV 格式</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    
    <!-- 右键菜单 -->
    <teleport to="body">
      <div 
        v-if="showContextMenu"
        class="context-menu"
        :style="contextMenuStyle"
        @click="hideContextMenu"
      >
        <div class="context-menu-item" @click="editBookmark(contextBookmark)">
          <el-icon><Edit /></el-icon>
          <span>编辑书签</span>
        </div>
        <div class="context-menu-item" @click="copyBookmarkLink(contextBookmark)">
          <el-icon><DocumentCopy /></el-icon>
          <span>复制链接</span>
        </div>
        <div class="context-menu-item" @click="openInNewWindow(contextBookmark)">
          <el-icon><Link /></el-icon>
          <span>新窗口打开</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item danger" @click="deleteBookmark(contextBookmark)">
          <el-icon><Delete /></el-icon>
          <span>删除书签</span>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  StarFilled,
  Star,
  Search,
  Setting,
  Upload,
  Download,
  Plus,
  Edit,
  Delete,
  Link,
  View,
  Clock,
  ArrowRight,
  MoreFilled,
  DocumentCopy,
  Folder
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  compact: {
    type: Boolean,
    default: false
  },
  maxItems: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['bookmark-click', 'bookmark-add', 'bookmark-delete'])

const router = useRouter()

// 响应式数据
const bookmarks = ref([])
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('created')
const viewMode = ref('grid')
const autoCategory = ref(true)
const showStats = ref(true)
const expandedGroups = ref(['work', 'personal', 'development'])
const isLoading = ref(false)
const isSaving = ref(false)

// 对话框状态
const showAddDialog = ref(false)
const showImportExport = ref(false)
const showSettings = ref(false)
const importExportTab = ref('export')
const exportFormat = ref('json')

// 右键菜单
const showContextMenu = ref(false)
const contextMenuStyle = ref({})
const contextBookmark = ref(null)

// 新建书签表单
const newBookmark = ref({
  title: '',
  url: '',
  description: '',
  category: 'personal',
  tags: []
})

const addBookmarkForm = ref(null)

// 表单验证规则
const bookmarkRules = {
  title: [
    { required: true, message: '请输入书签标题', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入链接地址', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ]
}

// 分类配置
const categories = computed(() => [
  { value: 'work', label: '工作', icon: 'Briefcase' },
  { value: 'personal', label: '个人', icon: 'User' },
  { value: 'development', label: '开发', icon: 'Code' },
  { value: 'design', label: '设计', icon: 'Brush' },
  { value: 'learning', label: '学习', icon: 'Reading' },
  { value: 'entertainment', label: '娱乐', icon: 'VideoPlay' },
  { value: 'tools', label: '工具', icon: 'Tools' },
  { value: 'other', label: '其他', icon: 'More' }
])

// 可用标签
const availableTags = computed(() => {
  const allTags = bookmarks.value.flatMap(bookmark => bookmark.tags || [])
  return [...new Set(allTags)]
})

// 计算属性
const bookmarksClasses = computed(() => ({
  'compact-mode': props.compact,
  'with-settings': showSettings.value
}))

// 过滤书签
const filteredBookmarks = computed(() => {
  let filtered = bookmarks.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(bookmark =>
      bookmark.title.toLowerCase().includes(query) ||
      bookmark.url.toLowerCase().includes(query) ||
      bookmark.description?.toLowerCase().includes(query) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(bookmark => bookmark.category === selectedCategory.value)
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'created':
        return new Date(b.createdAt) - new Date(a.createdAt)
      case 'lastVisit':
        return new Date(b.lastVisit || 0) - new Date(a.lastVisit || 0)
      case 'visitCount':
        return (b.visitCount || 0) - (a.visitCount || 0)
      case 'alphabetical':
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  // 限制显示数量
  if (props.maxItems && props.maxItems > 0) {
    filtered = filtered.slice(0, props.maxItems)
  }

  return filtered
})

// 分组书签
const groupedBookmarks = computed(() => {
  const groups = {}

  filteredBookmarks.value.forEach(bookmark => {
    const category = bookmark.category || 'other'
    if (!groups[category]) {
      groups[category] = {
        category,
        items: []
      }
    }
    groups[category].items.push(bookmark)
  })

  return Object.values(groups)
})

// 方法
const loadBookmarks = () => {
  isLoading.value = true
  
  // 模拟加载书签数据
  setTimeout(() => {
    bookmarks.value = JSON.parse(localStorage.getItem('user-bookmarks') || '[]')
    
    // 如果没有书签，添加一些示例数据
    if (bookmarks.value.length === 0) {
      bookmarks.value = [
        {
          id: '1',
          title: '群组管理',
          url: '/community/groups',
          description: '管理所有微信群组',
          category: 'work',
          tags: ['管理', '群组'],
          favicon: '',
          icon: 'Menu',
          visitCount: 15,
          lastVisit: Date.now() - 86400000,
          createdAt: Date.now() - 604800000,
          isFavorite: true
        },
        {
          id: '2',
          title: '用户分析',
          url: '/users/analytics',
          description: '查看用户数据和分析报表',
          category: 'work',
          tags: ['分析', '用户'],
          favicon: '',
          icon: 'DataLine',
          visitCount: 8,
          lastVisit: Date.now() - 172800000,
          createdAt: Date.now() - 1209600000,
          isFavorite: false
        },
        {
          id: '3',
          title: 'Vue.js 官方文档',
          url: 'https://vuejs.org',
          description: 'Vue.js 3.x 官方文档',
          category: 'development',
          tags: ['Vue', '前端', '文档'],
          favicon: 'https://vuejs.org/logo.svg',
          icon: 'Document',
          visitCount: 25,
          lastVisit: Date.now() - 43200000,
          createdAt: Date.now() - 2592000000,
          isFavorite: true
        }
      ]
      saveBookmarks()
    }
    
    isLoading.value = false
  }, 500)
}

const saveBookmarks = () => {
  localStorage.setItem('user-bookmarks', JSON.stringify(bookmarks.value))
}

const handleSearch = (query) => {
  // 搜索逻辑已在计算属性中处理
}

const handleCategoryFilter = (category) => {
  // 分类过滤逻辑已在计算属性中处理
}

const handleSort = (sort) => {
  // 排序逻辑已在计算属性中处理
}

const toggleGroup = (category) => {
  const index = expandedGroups.value.indexOf(category)
  if (index > -1) {
    expandedGroups.value.splice(index, 1)
  } else {
    expandedGroups.value.push(category)
  }
}

const getCategoryIcon = (category) => {
  const cat = categories.value.find(c => c.value === category)
  return cat?.icon || 'More'
}

const getCategoryName = (category) => {
  const cat = categories.value.find(c => c.value === category)
  return cat?.label || '其他'
}

const getBookmarkClasses = (bookmark) => ({
  'is-favorite': bookmark.isFavorite,
  'high-visit': (bookmark.visitCount || 0) > 10,
  'recent-visit': bookmark.lastVisit && (Date.now() - bookmark.lastVisit) < 86400000
})

const navigateToBookmark = (bookmark) => {
  // 更新访问统计
  bookmark.visitCount = (bookmark.visitCount || 0) + 1
  bookmark.lastVisit = Date.now()
  saveBookmarks()

  // 导航
  if (bookmark.url.startsWith('http')) {
    window.open(bookmark.url, '_blank')
  } else {
    router.push(bookmark.url)
  }

  emit('bookmark-click', bookmark)
}

const toggleBookmarkFavorite = (bookmark) => {
  bookmark.isFavorite = !bookmark.isFavorite
  saveBookmarks()
}

const handleBookmarkAction = (command) => {
  const [action, id] = command.split('-')
  const bookmark = bookmarks.value.find(b => b.id === id)
  
  if (!bookmark) return

  switch (action) {
    case 'edit':
      editBookmark(bookmark)
      break
    case 'copy':
      copyBookmarkLink(bookmark)
      break
    case 'open':
      if (command.includes('new')) {
        openInNewWindow(bookmark)
      }
      break
    case 'category':
      changeCategoryDialog(bookmark)
      break
    case 'delete':
      deleteBookmark(bookmark)
      break
  }
}

const showBookmarkContextMenu = (event, bookmark) => {
  contextBookmark.value = bookmark
  contextMenuStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`
  }
  showContextMenu.value = true
}

const hideContextMenu = () => {
  showContextMenu.value = false
  contextBookmark.value = null
}

const editBookmark = (bookmark) => {
  // 编辑书签逻辑
  ElMessage.info('编辑功能开发中...')
  hideContextMenu()
}

const copyBookmarkLink = (bookmark) => {
  navigator.clipboard.writeText(bookmark.url).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  })
  hideContextMenu()
}

const openInNewWindow = (bookmark) => {
  window.open(bookmark.url, '_blank')
  hideContextMenu()
}

const deleteBookmark = async (bookmark) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除书签"${bookmark.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = bookmarks.value.findIndex(b => b.id === bookmark.id)
    if (index > -1) {
      bookmarks.value.splice(index, 1)
      saveBookmarks()
      ElMessage.success('书签已删除')
      emit('bookmark-delete', bookmark)
    }
  } catch (error) {
    // 用户取消删除
  }
  
  hideContextMenu()
}

const handleAddBookmark = async () => {
  if (!addBookmarkForm.value) return

  try {
    await addBookmarkForm.value.validate()
    
    isSaving.value = true
    
    const bookmark = {
      id: Date.now().toString(),
      ...newBookmark.value,
      createdAt: Date.now(),
      visitCount: 0,
      isFavorite: false
    }
    
    // 自动获取网站图标
    if (bookmark.url.startsWith('http')) {
      bookmark.favicon = `${new URL(bookmark.url).origin}/favicon.ico`
    }
    
    bookmarks.value.unshift(bookmark)
    saveBookmarks()
    
    ElMessage.success('书签添加成功')
    showAddDialog.value = false
    resetAddForm()
    emit('bookmark-add', bookmark)
  } catch (error) {
    console.error('添加书签失败:', error)
  } finally {
    isSaving.value = false
  }
}

const handleAddDialogClose = () => {
  showAddDialog.value = false
  resetAddForm()
}

const resetAddForm = () => {
  newBookmark.value = {
    title: '',
    url: '',
    description: '',
    category: 'personal',
    tags: []
  }
  if (addBookmarkForm.value) {
    addBookmarkForm.value.resetFields()
  }
}

const handleExport = () => {
  const data = bookmarks.value
  const filename = `bookmarks_${new Date().toISOString().split('T')[0]}`
  
  let content = ''
  let mimeType = ''
  
  switch (exportFormat.value) {
    case 'json':
      content = JSON.stringify(data, null, 2)
      mimeType = 'application/json'
      break
    case 'html':
      content = generateHTMLBookmarks(data)
      mimeType = 'text/html'
      break
    case 'csv':
      content = generateCSVBookmarks(data)
      mimeType = 'text/csv'
      break
  }
  
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${filename}.${exportFormat.value}`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('导出成功')
}

const handleImportFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      let importedBookmarks = []
      
      if (file.type === 'application/json') {
        importedBookmarks = JSON.parse(e.target.result)
      } else {
        ElMessage.error('暂不支持该文件格式')
        return
      }
      
      // 合并书签
      importedBookmarks.forEach(bookmark => {
        if (!bookmarks.value.some(b => b.url === bookmark.url)) {
          bookmark.id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
          bookmarks.value.push(bookmark)
        }
      })
      
      saveBookmarks()
      ElMessage.success(`成功导入 ${importedBookmarks.length} 个书签`)
      showImportExport.value = false
    } catch (error) {
      ElMessage.error('文件格式错误')
    }
  }
  reader.readAsText(file)
  return false
}

const generateHTMLBookmarks = (bookmarks) => {
  let html = '<!DOCTYPE NETSCAPE-Bookmark-file-1>\n<HTML>\n<HEAD>\n<TITLE>Bookmarks</TITLE>\n</HEAD>\n<BODY>\n<H1>Bookmarks</H1>\n<DL>\n'
  
  const grouped = {}
  bookmarks.forEach(bookmark => {
    const category = bookmark.category || 'other'
    if (!grouped[category]) grouped[category] = []
    grouped[category].push(bookmark)
  })
  
  Object.entries(grouped).forEach(([category, items]) => {
    html += `<DT><H3>${getCategoryName(category)}</H3>\n<DL>\n`
    items.forEach(bookmark => {
      html += `<DT><A HREF="${bookmark.url}">${bookmark.title}</A>\n`
    })
    html += '</DL>\n'
  })
  
  html += '</DL>\n</BODY>\n</HTML>'
  return html
}

const generateCSVBookmarks = (bookmarks) => {
  const headers = 'Title,URL,Description,Category,Tags,Created Date\n'
  const rows = bookmarks.map(bookmark => {
    return [
      `"${bookmark.title}"`,
      `"${bookmark.url}"`,
      `"${bookmark.description || ''}"`,
      `"${getCategoryName(bookmark.category)}"`,
      `"${(bookmark.tags || []).join(', ')}"`,
      `"${new Date(bookmark.createdAt).toLocaleDateString()}"`
    ].join(',')
  }).join('\n')
  
  return headers + rows
}

const formatUrl = (url) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname + urlObj.pathname
  } catch {
    return url
  }
}

const formatDate = (timestamp) => {
  if (!timestamp) return '从未访问'
  
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const handleFaviconError = (e) => {
  e.target.src = ''
}

// 点击外部隐藏右键菜单
const handleClickOutside = (event) => {
  if (showContextMenu.value && !event.target.closest('.context-menu')) {
    hideContextMenu()
  }
}

// 生命周期
onMounted(() => {
  loadBookmarks()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听设置变化
watch([viewMode, autoCategory, showStats], () => {
  localStorage.setItem('bookmarks-settings', JSON.stringify({
    viewMode: viewMode.value,
    autoCategory: autoCategory.value,
    showStats: showStats.value
  }))
}, { deep: true })

// 加载保存的设置
onMounted(() => {
  const savedSettings = JSON.parse(localStorage.getItem('bookmarks-settings') || '{}')
  if (savedSettings.viewMode) viewMode.value = savedSettings.viewMode
  if (savedSettings.autoCategory !== undefined) autoCategory.value = savedSettings.autoCategory
  if (savedSettings.showStats !== undefined) showStats.value = savedSettings.showStats
})
</script>

<style lang="scss" scoped>
.bookmarks-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  
  &.compact-mode {
    .bookmarks-header {
      padding: var(--spacing-sm);
      
      .bookmarks-title {
        font-size: var(--text-sm);
      }
    }
    
    .bookmarks-toolbar {
      padding: var(--spacing-sm);
    }
  }
}

// 头部
.bookmarks-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .bookmarks-icon {
      color: var(--color-warning);
      font-size: 20px;
    }
    
    .bookmarks-title {
      margin: 0;
      font-size: var(--text-base);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
    }
    
    .bookmarks-count {
      :deep(.el-badge__content) {
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        line-height: 16px;
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-xs);
    
    .settings-btn,
    .import-export-btn {
      width: 32px;
      height: 32px;
      border-radius: var(--radius-md);
      
      &:hover {
        background: var(--bg-secondary);
      }
    }
  }
}

// 工具栏
.bookmarks-toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  
  .search-container {
    flex: 1;
    max-width: 300px;
    
    .bookmarks-search {
      .el-input__wrapper {
        border-radius: var(--radius-full);
      }
    }
  }
  
  .filter-container {
    display: flex;
    gap: var(--spacing-sm);
    
    .category-filter,
    .sort-select {
      width: 120px;
    }
  }
}

// 设置面板
.settings-panel {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-muted);
  
  .settings-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .setting-label {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      margin-right: var(--spacing-md);
    }
  }
}

// 书签内容
.bookmarks-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  
  &.view-grid {
    .group-items {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: var(--spacing-md);
    }
    
    .bookmark-item {
      flex-direction: column;
      align-items: flex-start;
      padding: var(--spacing-md);
      height: auto;
      
      .bookmark-info {
        width: 100%;
        margin-top: var(--spacing-sm);
        
        .bookmark-description {
          display: block;
          margin-top: var(--spacing-xs);
          color: var(--text-secondary);
          font-size: var(--text-xs);
          line-height: 1.4;
        }
      }
      
      .bookmark-actions {
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
      }
    }
  }
  
  &.view-list {
    .group-items {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }
  }
  
  &.view-compact {
    .group-items {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
    
    .bookmark-item {
      padding: var(--spacing-xs) var(--spacing-sm);
      min-height: 32px;
      
      .bookmark-icon {
        width: 20px;
        height: 20px;
      }
      
      .bookmark-info .bookmark-title {
        font-size: var(--text-xs);
      }
    }
  }
}

// 书签组
.bookmark-group {
  margin-bottom: var(--spacing-lg);
  
  .group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--bg-secondary);
    }
    
    .group-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      
      .group-count {
        :deep(.el-badge__content) {
          font-size: 10px;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
    
    .group-toggle {
      color: var(--text-light);
      transition: transform var(--duration-normal) var(--ease-out);
      
      &.rotated {
        transform: rotate(90deg);
      }
    }
  }
  
  .group-items {
    margin-top: var(--spacing-sm);
  }
}

// 书签项
.bookmark-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  min-height: 64px;
  
  &:hover {
    background: var(--bg-secondary);
    border-color: var(--border-medium);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    
    .bookmark-actions {
      opacity: 1;
    }
  }
  
  &.is-favorite {
    border-color: var(--warning-300);
    background: rgba(245, 158, 11, 0.05);
  }
  
  &.high-visit {
    .bookmark-icon::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: var(--success-500);
      border-radius: 50%;
      border: 2px solid var(--bg-primary);
    }
  }
  
  &.recent-visit {
    border-left: 3px solid var(--success-500);
  }
  
  .bookmark-icon {
    position: relative;
    width: 32px;
    height: 32px;
    
    .icon-placeholder {
      width: 100%;
      height: 100%;
      background: var(--bg-secondary);
      border-radius: var(--radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-muted);
    }
  }
  
  .bookmark-info {
    flex: 1;
    min-width: 0;
    
    .bookmark-title {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--text-primary);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 2px;
    }
    
    .bookmark-url {
      font-size: var(--text-xs);
      color: var(--text-muted);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 2px;
    }
    
    .bookmark-meta {
      display: flex;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-xs);
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 2px;
        font-size: var(--text-xs);
        color: var(--text-light);
        
        .el-icon {
          font-size: 10px;
        }
      }
    }
  }
  
  .bookmark-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
    
    .favorite-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      
      &.active {
        color: var(--warning-500);
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  
  .empty-icon {
    font-size: 48px;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
  }
  
  .empty-text {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-lg);
      color: var(--text-primary);
    }
    
    p {
      margin: 0;
      color: var(--text-secondary);
    }
  }
}

// 导入导出面板
.export-panel,
.import-panel {
  padding: var(--spacing-md);
  
  .export-actions {
    margin-top: var(--spacing-lg);
    text-align: center;
  }
  
  .import-note {
    margin-top: var(--spacing-sm);
    font-size: var(--text-xs);
    color: var(--text-muted);
    text-align: center;
  }
}

// 右键菜单
.context-menu {
  position: fixed;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xs) 0;
  min-width: 140px;
  z-index: var(--z-modal);
  
  .context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--bg-secondary);
    }
    
    &.danger {
      color: var(--color-danger);
    }
    
    .el-icon {
      font-size: 14px;
      color: var(--text-muted);
    }
  }
  
  .context-menu-divider {
    height: 1px;
    background: var(--border-light);
    margin: var(--spacing-xs) 0;
  }
}

// 动画
.settings-slide-enter-active,
.settings-slide-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.settings-slide-enter-from,
.settings-slide-leave-to {
  max-height: 0;
  opacity: 0;
}

.settings-slide-enter-to,
.settings-slide-leave-from {
  max-height: 200px;
  opacity: 1;
}

.group-expand-enter-active,
.group-expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.group-expand-enter-from,
.group-expand-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.group-expand-enter-to,
.group-expand-leave-from {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

// 响应式
@media (max-width: 768px) {
  .bookmarks-manager {
    .bookmarks-toolbar {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-sm);
      
      .search-container {
        max-width: none;
      }
      
      .filter-container {
        justify-content: space-between;
        
        .category-filter,
        .sort-select {
          flex: 1;
        }
      }
    }
    
    .bookmarks-content.view-grid {
      .group-items {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 480px) {
  .bookmarks-content.view-grid {
    padding: var(--spacing-sm);
  }
  
  .bookmark-item {
    min-height: 56px;
  }
}
</style>