<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * 获取系统设置
     */
    public function index(): JsonResponse
    {
        try {
            $settings = [
                'app_name' => config('app.name'),
                'app_url' => config('app.url'),
                'app_version' => config('app.version', '1.0.0'),
                'environment' => config('app.env'),
                'debug' => config('app.debug'),
                'timezone' => config('app.timezone'),
                'locale' => config('app.locale'),
                'currency' => config('app.currency', 'CNY'),
                'date_format' => config('app.date_format', 'Y-m-d'),
                'datetime_format' => config('app.datetime_format', 'Y-m-d H:i:s'),
            ];

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取设置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 更新系统设置
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'app_name' => 'sometimes|string|max:255',
                'app_url' => 'sometimes|url|max:255',
                'currency' => 'sometimes|string|size:3',
                'date_format' => 'sometimes|string|max:50',
                'datetime_format' => 'sometimes|string|max:50',
            ]);

            // 这里可以添加实际的设置更新逻辑
            // 例如保存到数据库或配置文件

            return response()->json([
                'success' => true,
                'message' => '设置更新成功',
                'data' => $validated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '设置更新失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取系统状态
     */
    public function status(): JsonResponse
    {
        try {
            $status = [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connected' => $this->checkDatabaseConnection(),
                'cache_enabled' => Cache::get('test', 'ok') === 'ok',
                'storage_writable' => Storage::disk('local')->put('test.txt', 'test') && Storage::disk('local')->delete('test.txt'),
                'memory_usage' => memory_get_usage(true),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取系统状态失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取配置信息
     */
    public function config(): JsonResponse
    {
        try {
            $config = [
                'api' => [
                    'version' => 'v1',
                    'rate_limit' => config('api.rate_limit', 60),
                    'pagination_limit' => config('api.pagination_limit', 15),
                ],
                'features' => [
                    'registration' => config('features.registration', true),
                    'email_verification' => config('features.email_verification', true),
                    'two_factor_auth' => config('features.two_factor_auth', false),
                    'social_login' => config('features.social_login', false),
                ],
                'uploads' => [
                    'max_file_size' => config('upload.max_file_size', 10240), // KB
                    'allowed_types' => config('upload.allowed_types', ['jpg', 'png', 'jpeg', 'gif', 'pdf']),
                    'max_files' => config('upload.max_files', 5),
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $config
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取配置信息失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabaseConnection(): bool
    {
        try {
            \DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}