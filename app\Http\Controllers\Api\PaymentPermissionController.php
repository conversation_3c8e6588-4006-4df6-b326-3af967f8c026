<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentPermission;
use App\Models\PaymentChannel;
use App\Models\User;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 支付权限控制器
 * 处理支付权限的授权、撤销和查询
 */
class PaymentPermissionController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 获取用户的支付权限列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'permission_type' => 'string|in:use,config,manage',
                'status' => 'string|in:valid,expired,all',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $permissionType = $request->input('permission_type');
            $status = $request->input('status', 'valid');

            // 验证管理权限
            if (!$this->canManagePermissions()) {
                return $this->error('无权限管理支付权限', 403);
            }

            $query = PaymentPermission::forUser($userType, $userId)
                ->with(['paymentChannel', 'grantedBy:id,name']);

            if ($permissionType) {
                $query->byPermissionType($permissionType);
            }

            switch ($status) {
                case 'valid':
                    $query->valid();
                    break;
                case 'expired':
                    $query->where('status', false)
                          ->orWhere('expires_at', '<=', now());
                    break;
                // 'all' 不添加额外条件
            }

            $permissions = $query->orderBy('created_at', 'desc')->get();

            $result = $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'channel_code' => $permission->channel_code,
                    'channel_name' => $permission->paymentChannel->channel_name ?? '',
                    'permission_type' => $permission->permission_type,
                    'permission_type_name' => $permission->permission_type_name,
                    'status' => $permission->status,
                    'is_valid' => $permission->isValid(),
                    'is_expiring_soon' => $permission->isExpiringSoon(),
                    'granted_at' => $permission->granted_at?->toDateTimeString(),
                    'granted_by' => $permission->grantedBy->name ?? '',
                    'expires_at' => $permission->expires_at?->toDateTimeString(),
                    'remark' => $permission->remark,
                ];
            });

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error('获取支付权限列表失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付权限列表失败：' . $e->getMessage());
        }
    }

    /**
     * 批量授权支付权限
     */
    public function grant(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'permissions' => 'required|array|min:1',
                'permissions.*.channel_code' => 'required|string|exists:payment_channels,channel_code',
                'permissions.*.permission_type' => 'required|string|in:use,config,manage',
                'permissions.*.expires_at' => 'nullable|date|after:now',
                'permissions.*.remark' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 验证管理权限
            if (!$this->canManagePermissions()) {
                return $this->error('无权限授权支付权限', 403);
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $permissions = $request->input('permissions');
            $grantedBy = auth()->id();

            // 验证目标用户是否存在
            if (!$this->validateTargetUser($userType, $userId)) {
                return $this->error('目标用户不存在');
            }

            $result = $this->paymentService->grantPaymentPermissions(
                $userType,
                $userId,
                $permissions,
                $grantedBy
            );

            if ($result) {
                Log::info('批量授权支付权限成功', [
                    'user_type' => $userType,
                    'user_id' => $userId,
                    'permissions_count' => count($permissions),
                    'granted_by' => $grantedBy
                ]);

                return $this->success(null, '支付权限授权成功');
            } else {
                return $this->error('支付权限授权失败');
            }

        } catch (\Exception $e) {
            Log::error('批量授权支付权限失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('批量授权支付权限失败：' . $e->getMessage());
        }
    }

    /**
     * 撤销支付权限
     */
    public function revoke(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'channel_code' => 'required|string|exists:payment_channels,channel_code',
                'permission_type' => 'string|in:use,config,manage',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 验证管理权限
            if (!$this->canManagePermissions()) {
                return $this->error('无权限撤销支付权限', 403);
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $channelCode = $request->input('channel_code');
            $permissionType = $request->input('permission_type', 'use');

            $result = $this->paymentService->revokePaymentPermission(
                $userType,
                $userId,
                $channelCode
            );

            if ($result) {
                Log::info('撤销支付权限成功', [
                    'user_type' => $userType,
                    'user_id' => $userId,
                    'channel_code' => $channelCode,
                    'permission_type' => $permissionType,
                    'revoked_by' => auth()->id()
                ]);

                return $this->success(null, '支付权限撤销成功');
            } else {
                return $this->error('支付权限撤销失败');
            }

        } catch (\Exception $e) {
            Log::error('撤销支付权限失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('撤销支付权限失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户支付权限
     */
    public function check(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|string|in:user,distributor,substation',
                'user_id' => 'required|integer|min:1',
                'channel_code' => 'required|string|exists:payment_channels,channel_code',
                'permission_type' => 'string|in:use,config,manage',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $channelCode = $request->input('channel_code');
            $permissionType = $request->input('permission_type', 'use');

            $hasPermission = PaymentPermission::hasPermission(
                $userType,
                $userId,
                $channelCode,
                $permissionType
            );

            return $this->success([
                'has_permission' => $hasPermission,
                'user_type' => $userType,
                'user_id' => $userId,
                'channel_code' => $channelCode,
                'permission_type' => $permissionType,
            ]);

        } catch (\Exception $e) {
            Log::error('检查支付权限失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('检查支付权限失败：' . $e->getMessage());
        }
    }

    /**
     * 获取即将过期的权限
     */
    public function expiring(Request $request): JsonResponse
    {
        try {
            // 验证管理权限
            if (!$this->canManagePermissions()) {
                return $this->error('无权限查看权限信息', 403);
            }

            $permissions = PaymentPermission::expiringSoon()
                ->with(['paymentChannel', 'grantedBy:id,name'])
                ->orderBy('expires_at')
                ->get();

            $result = $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'user_type' => $permission->user_type,
                    'user_id' => $permission->user_id,
                    'channel_code' => $permission->channel_code,
                    'channel_name' => $permission->paymentChannel->channel_name ?? '',
                    'permission_type' => $permission->permission_type,
                    'permission_type_name' => $permission->permission_type_name,
                    'expires_at' => $permission->expires_at?->toDateTimeString(),
                    'days_until_expiry' => $permission->expires_at?->diffInDays(now()),
                    'granted_by' => $permission->grantedBy->name ?? '',
                    'remark' => $permission->remark,
                ];
            });

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error('获取即将过期权限失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取即将过期权限失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付权限统计
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            // 验证管理权限
            if (!$this->canManagePermissions()) {
                return $this->error('无权限查看统计信息', 403);
            }

            $stats = [
                'total_permissions' => PaymentPermission::count(),
                'valid_permissions' => PaymentPermission::valid()->count(),
                'expired_permissions' => PaymentPermission::where('status', false)
                    ->orWhere('expires_at', '<=', now())
                    ->count(),
                'expiring_soon' => PaymentPermission::expiringSoon()->count(),
                'by_user_type' => PaymentPermission::selectRaw('user_type, COUNT(*) as count')
                    ->groupBy('user_type')
                    ->pluck('count', 'user_type'),
                'by_permission_type' => PaymentPermission::selectRaw('permission_type, COUNT(*) as count')
                    ->groupBy('permission_type')
                    ->pluck('count', 'permission_type'),
                'by_channel' => PaymentPermission::selectRaw('channel_code, COUNT(*) as count')
                    ->with('paymentChannel:channel_code,channel_name')
                    ->groupBy('channel_code')
                    ->get()
                    ->mapWithKeys(function ($item) {
                        return [$item->channel_code => [
                            'count' => $item->count,
                            'channel_name' => $item->paymentChannel->channel_name ?? $item->channel_code
                        ]];
                    }),
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取支付权限统计失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付权限统计失败：' . $e->getMessage());
        }
    }

    /**
     * 检查是否有权限管理支付权限
     */
    private function canManagePermissions(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['admin', 'substation']);
    }

    /**
     * 验证目标用户是否存在
     */
    private function validateTargetUser(string $userType, int $userId): bool
    {
        switch ($userType) {
            case 'user':
            case 'distributor':
            case 'substation':
                return User::where('id', $userId)->exists();
            default:
                return false;
        }
    }
}