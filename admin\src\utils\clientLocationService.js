/**
 * 客户端地理位置服务
 * 用于在用户访问落地页时动态替换城市名称
 * 这个服务运行在客户端，获取的是实际访问用户的位置
 */

class ClientLocationService {
  constructor() {
    this.currentCity = '北京' // 默认城市
    this.locationCache = null
    this.cacheExpiry = 30 * 60 * 1000 // 30分钟缓存
    this.isInitialized = false
  }

  /**
   * 初始化并获取用户城市
   * @returns {Promise<string>} 城市名称
   */
  async initialize() {
    if (this.isInitialized && this.locationCache && 
        Date.now() - this.locationCache.timestamp < this.cacheExpiry) {
      return this.locationCache.city
    }

    try {
      const city = await this.detectUserCity()
      
      // 更新缓存
      this.locationCache = {
        city,
        timestamp: Date.now()
      }
      
      this.currentCity = city
      this.isInitialized = true
      
      console.log('客户端检测到用户城市:', city)
      return city
    } catch (error) {
      console.warn('客户端城市检测失败，使用默认城市:', error)
      this.currentCity = '北京'
      this.isInitialized = true
      return this.currentCity
    }
  }

  /**
   * 检测用户城市
   * @returns {Promise<string>}
   */
  async detectUserCity() {
    const methods = [
      () => this.getCityFromStorage(),
      () => this.getCityByIP(),
      () => this.getCityByTimezone(),
      () => this.getCityByLanguage()
    ]

    for (const method of methods) {
      try {
        const city = await method()
        if (city && city !== '未知' && city !== '北京') {
          return city
        }
      } catch (error) {
        console.warn('城市检测方法失败:', error)
        continue
      }
    }

    return '北京' // 默认城市
  }

  /**
   * 通过IP地址获取城市（客户端版本）
   * @returns {Promise<string>}
   */
  async getCityByIP() {
    try {
      // 使用支持CORS的免费IP地理位置服务
      const services = [
        {
          url: 'https://ipapi.co/json/',
          parser: (data) => data.city || data.region_name
        },
        {
          url: 'https://ip-api.com/json/?lang=zh-CN',
          parser: (data) => data.city || data.regionName
        },
        {
          url: 'https://ipinfo.io/json',
          parser: (data) => data.city || data.region
        }
      ]

      for (const service of services) {
        try {
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 8000)
          
          const response = await fetch(service.url, { 
            signal: controller.signal,
            mode: 'cors'
          })
          clearTimeout(timeoutId)
          
          if (!response.ok) continue
          
          const data = await response.json()
          const city = service.parser(data)
          
          if (city) {
            const normalizedCity = this.normalizeCityName(city)
            console.log(`通过 ${service.url} 获取到城市:`, normalizedCity)
            return normalizedCity
          }
        } catch (error) {
          console.warn(`IP服务 ${service.url} 失败:`, error)
          continue
        }
      }
      
      throw new Error('所有IP服务都失败了')
    } catch (error) {
      throw new Error(`IP获取城市失败: ${error.message}`)
    }
  }

  /**
   * 从本地存储获取城市
   * @returns {Promise<string>}
   */
  async getCityFromStorage() {
    try {
      const storedCity = localStorage.getItem('user_detected_city')
      if (storedCity) {
        const cityData = JSON.parse(storedCity)
        if (Date.now() - cityData.timestamp < this.cacheExpiry) {
          return cityData.city
        }
      }
      throw new Error('本地存储中没有有效的城市信息')
    } catch (error) {
      throw new Error(`本地存储获取失败: ${error.message}`)
    }
  }

  /**
   * 根据时区推测城市
   * @returns {Promise<string>}
   */
  async getCityByTimezone() {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      const cityByTimezone = this.getCityByTimezoneMap(timezone)
      
      if (cityByTimezone) {
        return cityByTimezone
      }
      
      throw new Error('无法通过时区确定城市')
    } catch (error) {
      throw new Error(`时区获取城市失败: ${error.message}`)
    }
  }

  /**
   * 根据语言推测城市
   * @returns {Promise<string>}
   */
  async getCityByLanguage() {
    try {
      const language = navigator.language || navigator.userLanguage
      
      if (language.includes('zh-CN')) {
        return '北京'
      } else if (language.includes('zh-TW')) {
        return '台北'
      } else if (language.includes('zh-HK')) {
        return '香港'
      }
      
      return '北京'
    } catch (error) {
      throw new Error(`语言获取城市失败: ${error.message}`)
    }
  }

  /**
   * 时区到城市的映射
   * @param {string} timezone 时区
   * @returns {string|null}
   */
  getCityByTimezoneMap(timezone) {
    const timezoneMap = {
      'Asia/Shanghai': '上海',
      'Asia/Beijing': '北京',
      'Asia/Chongqing': '重庆',
      'Asia/Urumqi': '乌鲁木齐',
      'Asia/Hong_Kong': '香港',
      'Asia/Taipei': '台北',
      'Asia/Macau': '澳门'
    }
    
    return timezoneMap[timezone] || null
  }

  /**
   * 标准化城市名称
   * @param {string} city 原始城市名称
   * @returns {string}
   */
  normalizeCityName(city) {
    if (!city) return '北京'
    
    // 清理城市名称
    city = city.trim().replace(/(市|省|自治区|特别行政区)$/, '')
    
    // 英文城市名称映射
    const englishCityMap = {
      'Beijing': '北京',
      'Shanghai': '上海',
      'Guangzhou': '广州',
      'Shenzhen': '深圳',
      'Hangzhou': '杭州',
      'Nanjing': '南京',
      'Wuhan': '武汉',
      'Chengdu': '成都',
      'Xi\'an': '西安',
      'Xian': '西安',
      'Chongqing': '重庆',
      'Tianjin': '天津',
      'Suzhou': '苏州'
    }
    
    return englishCityMap[city] || city || '北京'
  }

  /**
   * 替换文本中的城市变量
   * @param {string} text 包含{{city}}变量的文本
   * @returns {string} 替换后的文本
   */
  replaceCityInText(text) {
    if (typeof text !== 'string') return text
    return text.replace(/\{\{city\}\}/g, this.currentCity)
  }

  /**
   * 替换对象中的城市变量
   * @param {Object} obj 包含{{city}}变量的对象
   * @returns {Object} 替换后的对象
   */
  replaceCityInObject(obj) {
    if (!obj || typeof obj !== 'object') return obj
    
    const result = Array.isArray(obj) ? [] : {}
    
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        result[key] = this.replaceCityInText(obj[key])
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        result[key] = this.replaceCityInObject(obj[key])
      } else {
        result[key] = obj[key]
      }
    }
    
    return result
  }

  /**
   * 保存检测到的城市到本地存储
   * @param {string} city 城市名称
   */
  saveCityToStorage(city) {
    try {
      const cityData = {
        city,
        timestamp: Date.now()
      }
      localStorage.setItem('user_detected_city', JSON.stringify(cityData))
    } catch (error) {
      console.warn('保存城市到本地存储失败:', error)
    }
  }

  /**
   * 获取当前城市
   * @returns {string}
   */
  getCurrentCity() {
    return this.currentCity
  }

  /**
   * 手动设置城市
   * @param {string} city 城市名称
   */
  setCity(city) {
    this.currentCity = city
    this.saveCityToStorage(city)
  }
}

// 创建单例实例
const clientLocationService = new ClientLocationService()

export default clientLocationService
