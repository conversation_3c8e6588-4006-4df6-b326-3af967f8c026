<?php

namespace App\Database\Migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * 迁移基类
 * 提供健壮性检查和常用迁移方法
 */
class BaseMigration extends Migration
{
    /**
     * 检查表是否存在
     *
     * @param string $tableName 表名
     * @return bool
     */
    protected function tableExists($tableName)
    {
        return Schema::hasTable($tableName);
    }
    
    /**
     * 检查字段是否存在
     *
     * @param string $tableName 表名
     * @param string $columnName 字段名
     * @return bool
     */
    protected function columnExists($tableName, $columnName)
    {
        return Schema::hasColumn($tableName, $columnName);
    }
    
    /**
     * 检查索引是否存在
     *
     * @param string $tableName 表名
     * @param string $indexName 索引名
     * @return bool
     */
    protected function indexExists($tableName, $indexName)
    {
        $indexes = DB::select("SHOW INDEXES FROM {$tableName} WHERE Key_name = '{$indexName}'");
        return !empty($indexes);
    }
    
    /**
     * 安全创建表（如果不存在）
     *
     * @param string $tableName 表名
     * @param \Closure $callback 表结构定义回调
     * @return void
     */
    protected function safeCreateTable($tableName, \Closure $callback)
    {
        if (!$this->tableExists($tableName)) {
            Schema::create($tableName, $callback);
        }
    }
    
    /**
     * 安全添加字段（如果不存在）
     *
     * @param string $tableName 表名
     * @param string $columnName 字段名
     * @param \Closure $callback 字段定义回调
     * @return void
     */
    protected function safeAddColumn($tableName, $columnName, \Closure $callback)
    {
        if ($this->tableExists($tableName) && !$this->columnExists($tableName, $columnName)) {
            Schema::table($tableName, function (Blueprint $table) use ($callback) {
                $callback($table);
            });
        }
    }
    
    /**
     * 安全添加外键（如果引用表存在）
     *
     * @param string $tableName 表名
     * @param string $columnName 字段名
     * @param string $referenceTable 引用表名
     * @param string $referenceColumn 引用字段名
     * @param string $onDelete 删除时操作
     * @return void
     */
    protected function safeAddForeignKey($tableName, $columnName, $referenceTable, $referenceColumn = 'id', $onDelete = 'cascade')
    {
        if ($this->tableExists($tableName) && $this->tableExists($referenceTable) && 
            $this->columnExists($tableName, $columnName) && $this->columnExists($referenceTable, $referenceColumn)) {
            
            // 检查外键是否已存在
            $foreignKeyName = "{$tableName}_{$columnName}_foreign";
            $foreignKeys = DB::select("SELECT * FROM information_schema.KEY_COLUMN_USAGE 
                                      WHERE TABLE_SCHEMA = DATABASE() 
                                      AND TABLE_NAME = '{$tableName}' 
                                      AND COLUMN_NAME = '{$columnName}' 
                                      AND REFERENCED_TABLE_NAME = '{$referenceTable}'");
            
            if (empty($foreignKeys)) {
                Schema::table($tableName, function (Blueprint $table) use ($columnName, $referenceTable, $referenceColumn, $onDelete) {
                    $table->foreign($columnName)
                          ->references($referenceColumn)
                          ->on($referenceTable)
                          ->onDelete($onDelete);
                });
            }
        }
    }
    
    /**
     * 安全添加索引（如果不存在）
     *
     * @param string $tableName 表名
     * @param array|string $columns 字段名
     * @param string $indexName 索引名
     * @param string $indexType 索引类型 (index, unique, fulltext)
     * @return void
     */
    protected function safeAddIndex($tableName, $columns, $indexName = null, $indexType = 'index')
    {
        if ($this->tableExists($tableName)) {
            // 如果没有提供索引名，生成一个
            if ($indexName === null) {
                $indexName = $tableName . '_' . (is_array($columns) ? implode('_', $columns) : $columns) . '_' . $indexType;
            }
            
            // 检查索引是否存在
            if (!$this->indexExists($tableName, $indexName)) {
                Schema::table($tableName, function (Blueprint $table) use ($columns, $indexName, $indexType) {
                    if ($indexType === 'unique') {
                        $table->unique($columns, $indexName);
                    } elseif ($indexType === 'fulltext') {
                        $table->fullText($columns, $indexName);
                    } else {
                        $table->index($columns, $indexName);
                    }
                });
            }
        }
    }
    
    /**
     * 安全删除表（如果存在）
     *
     * @param string $tableName 表名
     * @return void
     */
    protected function safeDropTable($tableName)
    {
        if ($this->tableExists($tableName)) {
            Schema::dropIfExists($tableName);
        }
    }
    
    /**
     * 安全删除字段（如果存在）
     *
     * @param string $tableName 表名
     * @param string|array $columnNames 字段名
     * @return void
     */
    protected function safeDropColumn($tableName, $columnNames)
    {
        if ($this->tableExists($tableName)) {
            $columnNames = is_array($columnNames) ? $columnNames : [$columnNames];
            $columnsToDrop = [];
            
            foreach ($columnNames as $columnName) {
                if ($this->columnExists($tableName, $columnName)) {
                    $columnsToDrop[] = $columnName;
                }
            }
            
            if (!empty($columnsToDrop)) {
                Schema::table($tableName, function (Blueprint $table) use ($columnsToDrop) {
                    $table->dropColumn($columnsToDrop);
                });
            }
        }
    }
    
    /**
     * 安全删除外键（如果存在）
     *
     * @param string $tableName 表名
     * @param string $columnName 字段名
     * @return void
     */
    protected function safeDropForeignKey($tableName, $columnName)
    {
        if ($this->tableExists($tableName)) {
            $foreignKeyName = "{$tableName}_{$columnName}_foreign";
            
            // 检查外键是否存在
            $foreignKeys = DB::select("SELECT * FROM information_schema.KEY_COLUMN_USAGE 
                                      WHERE TABLE_SCHEMA = DATABASE() 
                                      AND TABLE_NAME = '{$tableName}' 
                                      AND COLUMN_NAME = '{$columnName}' 
                                      AND REFERENCED_TABLE_NAME IS NOT NULL");
            
            if (!empty($foreignKeys)) {
                Schema::table($tableName, function (Blueprint $table) use ($foreignKeyName) {
                    $table->dropForeign($foreignKeyName);
                });
            }
        }
    }
}