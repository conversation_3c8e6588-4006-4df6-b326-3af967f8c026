<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // 记录异常到日志
            try {
                if (app()->environment('production')) {
                    \Log::error('Application Error: ' . $e->getMessage(), [
                        'exception' => $e,
                        'url' => request()->fullUrl(),
                        'method' => request()->method(),
                        'ip' => request()->ip(),
                        'user_agent' => request()->userAgent(),
                    ]);
                }
            } catch (\Exception $logException) {
                // 如果日志记录失败，静默处理，避免循环异常
            }
        });

        $this->renderable(function (Throwable $e, $request) {
            // 如果是API请求，返回JSON响应
            if ($request->is('api/*') || $request->expectsJson()) {
                $statusCode = 500;
                $errorCode = 500;
                $message = app()->environment('production') ? '服务器内部错误' : $e->getMessage();
                $errors = null;
                $trace = app()->environment('local') ? $e->getTrace() : null;
                
                // 处理认证异常
                if ($e instanceof \Illuminate\Auth\AuthenticationException) {
                    $statusCode = 401;
                    $errorCode = 401;
                    $message = '未授权访问';
                }
                // 处理授权异常
                else if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
                    $statusCode = 403;
                    $errorCode = 403;
                    $message = '没有权限执行此操作';
                }
                // 处理验证异常
                else if ($e instanceof \Illuminate\Validation\ValidationException) {
                    $statusCode = 422;
                    $errorCode = 422;
                    $message = '参数验证失败';
                    $errors = $e->errors();
                }
                // 处理模型未找到异常
                else if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                    $statusCode = 404;
                    $errorCode = 404;
                    $message = '资源不存在';
                }
                // 处理路由未找到异常
                else if ($e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
                    $statusCode = 404;
                    $errorCode = 404;
                    $message = 'API端点不存在';
                }
                // 处理方法不允许异常
                else if ($e instanceof \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException) {
                    $statusCode = 405;
                    $errorCode = 405;
                    $message = '请求方法不允许';
                }
                // 处理CSRF令牌不匹配异常
                else if ($e instanceof \Illuminate\Session\TokenMismatchException) {
                    $statusCode = 419;
                    $errorCode = 419;
                    $message = 'CSRF令牌已过期，请刷新页面重试';
                }
                // 处理速率限制异常
                else if ($e instanceof \Illuminate\Http\Exceptions\ThrottleRequestsException) {
                    $statusCode = 429;
                    $errorCode = 429;
                    $message = '请求过于频繁，请稍后再试';
                }
                // 处理JWT异常
                else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                    $statusCode = 401;
                    $errorCode = 401001;
                    $message = '令牌已过期';
                }
                else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                    $statusCode = 401;
                    $errorCode = 401002;
                    $message = '令牌无效';
                }
                else if ($e instanceof \Tymon\JWTAuth\Exceptions\JWTException) {
                    $statusCode = 401;
                    $errorCode = 401003;
                    $message = '令牌错误';
                }
                // 处理数据库异常
                else if ($e instanceof \Illuminate\Database\QueryException) {
                    $statusCode = 500;
                    $errorCode = 500001;
                    $message = app()->environment('production') ? '数据库操作错误' : $e->getMessage();
                    
                    // 记录详细错误
                    \Log::error('数据库查询错误', [
                        'exception' => $e->getMessage(),
                        'sql' => $e->getSql() ?? '',
                        'bindings' => $e->getBindings() ?? []
                    ]);
                }
                
                return response()->json([
                    'success' => false,
                    'code' => $errorCode,
                    'message' => $message,
                    'errors' => $errors,
                    'trace' => $trace,
                    'timestamp' => now()->timestamp
                ], $statusCode);
            }

            return null;
        });
    }
} 