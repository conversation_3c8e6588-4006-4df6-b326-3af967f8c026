<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 微信群资源类
 * 控制API返回的数据结构，防止敏感信息泄露
 */
class WechatGroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'description' => $this->description,
            'price' => $this->price,
            'current_members' => $this->current_members,
            'max_members' => $this->max_members,
            'status' => $this->status,
            'status_name' => $this->status_name,
            'view_count' => $this->view_count,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];

        // 基础展示信息
        if ($this->relationLoaded('user')) {
            $data['owner'] = [
                'id' => $this->user->id,
                'username' => $this->user->username,
                'nickname' => $this->user->nickname,
                'avatar' => $this->user->avatar_url ?? null,
            ];
        }

        if ($this->relationLoaded('substation')) {
            $data['substation'] = [
                'id' => $this->substation->id,
                'name' => $this->substation->name,
                'domain' => $this->substation->domain ?? null,
            ];
        }

        // 统计数据（如果已加载）
        if (isset($this->total_orders_count)) {
            $data['total_orders_count'] = $this->total_orders_count;
        }

        if (isset($this->paid_orders_count)) {
            $data['paid_orders_count'] = $this->paid_orders_count;
        }

        if (isset($this->recent_orders_count)) {
            $data['recent_orders_count'] = $this->recent_orders_count;
        }

        // 根据用户权限返回不同级别的数据
        if ($request->user()) {
            $user = $request->user();
            
            // 群主可以看到详细信息
            if ($this->user_id === $user->id) {
                $data = array_merge($data, $this->getOwnerData());
            }
            // 分站管理员可以看到分站群组的详细信息
            elseif ($user->role === 'substation' && $this->substation_id === $user->substation_id) {
                $data = array_merge($data, $this->getSubstationData());
            }
            // 超级管理员可以看到所有信息
            elseif ($user->role === 'admin') {
                $data = array_merge($data, $this->getAdminData());
            }
            // 普通用户只能看到基础信息
            else {
                $data = array_merge($data, $this->getPublicData());
            }
        } else {
            // 未登录用户只能看到公开信息
            $data = array_merge($data, $this->getPublicData());
        }

        return $data;
    }

    /**
     * 群主可见数据
     */
    private function getOwnerData(): array
    {
        return [
            'qr_code_url' => $this->qr_code_url,
            'cover_image_url' => $this->cover_image_url,
            'total_earnings' => $this->total_earnings,
            'payment_methods' => $this->payment_methods,
            'city_location' => $this->city_location,
            'owner_display' => $this->owner_display,
            'hot_display' => $this->hot_display,
            'custom_fields' => $this->custom_fields,
            'marketing_settings' => $this->marketing_settings ?? [],
            'virtual_stats' => $this->virtual_stats ?? [],
            'last_activity_at' => $this->last_activity_at?->format('Y-m-d H:i:s'),
            'remark' => $this->remark,
        ];
    }

    /**
     * 分站管理员可见数据
     */
    private function getSubstationData(): array
    {
        return [
            'total_earnings' => $this->total_earnings,
            'payment_methods' => $this->payment_methods,
            'last_activity_at' => $this->last_activity_at?->format('Y-m-d H:i:s'),
            'remark' => $this->remark,
        ];
    }

    /**
     * 超级管理员可见数据
     */
    private function getAdminData(): array
    {
        return [
            'qr_code_url' => $this->qr_code_url,
            'cover_image_url' => $this->cover_image_url,
            'total_earnings' => $this->total_earnings,
            'payment_methods' => $this->payment_methods,
            'city_location' => $this->city_location,
            'owner_display' => $this->owner_display,
            'hot_display' => $this->hot_display,
            'custom_fields' => $this->custom_fields,
            'marketing_settings' => $this->marketing_settings ?? [],
            'virtual_stats' => $this->virtual_stats ?? [],
            'last_activity_at' => $this->last_activity_at?->format('Y-m-d H:i:s'),
            'remark' => $this->remark,
            'sort_order' => $this->sort_order,
            'template_id' => $this->template_id,
            // 管理员专用字段
            'internal_notes' => $this->internal_notes ?? '',
            'risk_level' => $this->risk_level ?? 'low',
        ];
    }

    /**
     * 公开数据
     */
    private function getPublicData(): array
    {
        $data = [];

        // 只有启用了相应显示选项才返回
        if ($this->owner_display) {
            $data['qr_code_url'] = $this->qr_code_url;
        }

        if ($this->hot_display) {
            $data['virtual_stats'] = [
                'members' => $this->virtual_members + $this->current_members,
                'activity_level' => 'high', // 简化的活跃度显示
            ];
        }

        // 营销展示数据
        if ($this->show_member_reviews && $this->formatted_member_reviews) {
            $data['member_reviews'] = array_slice($this->formatted_member_reviews, 0, 3); // 只显示前3条
        }

        if ($this->show_real_time_stats) {
            $data['real_time_stats'] = [
                'today_views' => $this->today_views + rand(10, 50), // 添加一些随机数
                'total_joins' => $this->total_joins + rand(100, 500),
            ];
        }

        return $data;
    }

    /**
     * 获取额外的元数据
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'timestamp' => now()->toISOString(),
                'user_permissions' => $this->getUserPermissions($request),
            ]
        ];
    }

    /**
     * 获取用户对该群组的权限
     */
    private function getUserPermissions(Request $request): array
    {
        if (!$request->user()) {
            return ['view' => true];
        }

        $user = $request->user();
        $permissions = ['view' => true];

        // 群主权限
        if ($this->user_id === $user->id) {
            $permissions = array_merge($permissions, [
                'edit' => true,
                'delete' => true,
                'view_stats' => true,
                'manage_orders' => true,
            ]);
        }
        // 分站管理员权限
        elseif ($user->role === 'substation' && $this->substation_id === $user->substation_id) {
            $permissions = array_merge($permissions, [
                'edit' => true,
                'view_stats' => true,
            ]);
        }
        // 超级管理员权限
        elseif ($user->role === 'admin') {
            $permissions = array_merge($permissions, [
                'edit' => true,
                'delete' => true,
                'view_stats' => true,
                'manage_orders' => true,
                'manage_users' => true,
            ]);
        }

        return $permissions;
    }
}