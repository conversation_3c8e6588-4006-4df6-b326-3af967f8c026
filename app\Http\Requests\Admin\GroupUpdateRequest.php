<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 群组更新请求验证
 */
class GroupUpdateRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * 获取验证规则
     */
    public function rules(): array
    {
        $groupId = $this->route('id');
        
        return [
            // 基础信息
            'title' => 'sometimes|required|string|max:200',
            'category' => 'sometimes|required|string|in:technology,finance,education,fitness,business,social,entertainment,other',
            'price' => 'sometimes|required|numeric|min:0|max:9999.99',
            'member_limit' => 'sometimes|required|integer|min:1|max:2000',
            'description' => 'sometimes|required|string|min:10|max:1000',
            'cover_image' => 'sometimes|nullable|string|max:500',
            'qr_code' => 'sometimes|nullable|string|max:500',
            'status' => 'sometimes|string|in:active,inactive,draft',
            
            // 城市定位
            'city_location' => 'sometimes|boolean',
            'city_insert_strategy' => 'sometimes|required_if:city_location,true|string|in:auto,prefix,suffix,natural,none',
            
            // 富媒体内容
            'group_intro_title' => 'sometimes|nullable|string|max:100',
            'group_intro_content' => 'sometimes|nullable|string|max:5000',
            'faq_title' => 'sometimes|nullable|string|max:100',
            'faq_content' => 'sometimes|nullable|string|max:10000',
            'member_reviews' => 'sometimes|nullable|string|max:10000',
            'media_images' => 'sometimes|nullable|array|max:10',
            'media_images.*' => 'array',
            'media_images.*.name' => 'required|string|max:255',
            'media_images.*.url' => 'required|string|max:500',
            'promo_video' => 'sometimes|nullable|string|max:500',
            'audio_intro' => 'sometimes|nullable|string|max:500',
            
            // 营销展示
            'read_count_display' => 'sometimes|nullable|string|max:20',
            'like_count' => 'sometimes|integer|min:0|max:999999',
            'want_see_count' => 'sometimes|integer|min:0|max:999999',
            'button_title' => 'sometimes|nullable|string|max:20',
            'virtual_members' => 'sometimes|integer|min:0|max:999999',
            'virtual_orders' => 'sometimes|integer|min:0|max:999999',
            
            // 支付设置
            'payment_methods' => 'sometimes|array',
            'payment_methods.*' => 'string|in:wechat,alipay,qq,bank',
            'order_expire_minutes' => 'sometimes|integer|min:5|max:1440',
            'success_redirect_url' => 'sometimes|nullable|string|max:500|url',
            
            // 分销设置
            'enable_distribution' => 'sometimes|boolean',
            'commission_rate_1' => 'sometimes|required_if:enable_distribution,true|numeric|min:0|max:100',
            'commission_rate_2' => 'sometimes|required_if:enable_distribution,true|numeric|min:0|max:100',
            'commission_rate_3' => 'sometimes|required_if:enable_distribution,true|numeric|min:0|max:100',
            'commission_settlement' => 'sometimes|required_if:enable_distribution,true|string|in:instant,daily,weekly,monthly',
            
            // 防红系统
            'enable_anti_block' => 'sometimes|boolean',
            'domain_pool_id' => 'sometimes|required_if:enable_anti_block,true|integer|exists:domain_pools,id',
            'check_frequency' => 'sometimes|required_if:enable_anti_block,true|string|in:5,10,30,60',
            'switch_strategy' => 'sometimes|required_if:enable_anti_block,true|string|in:immediate,delayed,manual',
            
            // 营销功能
            'enable_limited_offer' => 'sometimes|boolean',
            'offer_price' => 'sometimes|required_if:enable_limited_offer,true|numeric|min:0|max:9999.99',
            'offer_end_time' => 'sometimes|required_if:enable_limited_offer,true|date|after:now',
            'enable_urgency' => 'sometimes|boolean',
            'remaining_slots' => 'sometimes|required_if:enable_urgency,true|integer|min:0|max:999999',
            'show_countdown' => 'sometimes|boolean',
            'enable_social_proof' => 'sometimes|boolean',
            'recent_join_text' => 'sometimes|required_if:enable_social_proof,true|string|max:100',
            
            // 数据统计
            'enable_analytics' => 'sometimes|boolean',
            'enable_conversion_tracking' => 'sometimes|boolean',
            'custom_analytics_code' => 'sometimes|nullable|string|max:2000'
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'title' => '群组名称',
            'category' => '群组分类',
            'price' => '群组价格',
            'member_limit' => '成员上限',
            'description' => '群组描述',
            'cover_image' => '群组头像',
            'qr_code' => '群组二维码',
            'status' => '群组状态',
            'city_location' => '城市定位',
            'city_insert_strategy' => '城市插入策略',
            'group_intro_title' => '介绍标题',
            'group_intro_content' => '介绍内容',
            'faq_title' => 'FAQ标题',
            'faq_content' => 'FAQ内容',
            'member_reviews' => '群友评价',
            'media_images' => '宣传图片',
            'promo_video' => '宣传视频',
            'audio_intro' => '音频介绍',
            'read_count_display' => '阅读数显示',
            'like_count' => '点赞数',
            'want_see_count' => '想看数',
            'button_title' => '按钮文字',
            'virtual_members' => '虚拟成员数',
            'virtual_orders' => '虚拟订单数',
            'payment_methods' => '支付方式',
            'order_expire_minutes' => '订单有效期',
            'success_redirect_url' => '支付成功跳转链接',
            'enable_distribution' => '启用分销',
            'commission_rate_1' => '一级佣金比例',
            'commission_rate_2' => '二级佣金比例',
            'commission_rate_3' => '三级佣金比例',
            'commission_settlement' => '佣金结算方式',
            'enable_anti_block' => '启用防红系统',
            'domain_pool_id' => '域名池',
            'check_frequency' => '检测频率',
            'switch_strategy' => '切换策略',
            'enable_limited_offer' => '启用限时优惠',
            'offer_price' => '优惠价格',
            'offer_end_time' => '优惠结束时间',
            'enable_urgency' => '启用紧迫感营销',
            'remaining_slots' => '剩余名额',
            'show_countdown' => '显示倒计时',
            'enable_social_proof' => '启用社交证明',
            'recent_join_text' => '最近加入提示',
            'enable_analytics' => '启用访问统计',
            'enable_conversion_tracking' => '启用转化跟踪',
            'custom_analytics_code' => '自定义统计代码'
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'title.required' => '请输入群组名称',
            'title.max' => '群组名称不能超过200个字符',
            'category.required' => '请选择群组分类',
            'category.in' => '请选择有效的群组分类',
            'price.required' => '请输入群组价格',
            'price.numeric' => '群组价格必须是数字',
            'price.min' => '群组价格不能小于0',
            'price.max' => '群组价格不能超过9999.99',
            'member_limit.required' => '请输入成员上限',
            'member_limit.integer' => '成员上限必须是整数',
            'member_limit.min' => '成员上限不能小于1',
            'member_limit.max' => '成员上限不能超过2000',
            'description.required' => '请输入群组描述',
            'description.min' => '群组描述至少需要10个字符',
            'description.max' => '群组描述不能超过1000个字符',
            'status.in' => '请选择有效的群组状态',
            'city_insert_strategy.required_if' => '启用城市定位时必须选择插入策略',
            'payment_methods.array' => '支付方式必须是数组格式',
            'payment_methods.*.in' => '请选择有效的支付方式',
            'order_expire_minutes.integer' => '订单有效期必须是整数',
            'order_expire_minutes.min' => '订单有效期不能少于5分钟',
            'order_expire_minutes.max' => '订单有效期不能超过1440分钟（24小时）',
            'success_redirect_url.url' => '支付成功跳转链接格式不正确',
            'commission_rate_1.required_if' => '启用分销时必须设置一级佣金比例',
            'commission_rate_1.numeric' => '一级佣金比例必须是数字',
            'commission_rate_1.min' => '一级佣金比例不能小于0',
            'commission_rate_1.max' => '一级佣金比例不能超过100',
            'commission_rate_2.required_if' => '启用分销时必须设置二级佣金比例',
            'commission_rate_2.numeric' => '二级佣金比例必须是数字',
            'commission_rate_2.min' => '二级佣金比例不能小于0',
            'commission_rate_2.max' => '二级佣金比例不能超过100',
            'commission_rate_3.required_if' => '启用分销时必须设置三级佣金比例',
            'commission_rate_3.numeric' => '三级佣金比例必须是数字',
            'commission_rate_3.min' => '三级佣金比例不能小于0',
            'commission_rate_3.max' => '三级佣金比例不能超过100',
            'commission_settlement.required_if' => '启用分销时必须选择佣金结算方式',
            'commission_settlement.in' => '请选择有效的佣金结算方式',
            'domain_pool_id.required_if' => '启用防红系统时必须选择域名池',
            'domain_pool_id.exists' => '选择的域名池不存在',
            'check_frequency.required_if' => '启用防红系统时必须设置检测频率',
            'check_frequency.in' => '请选择有效的检测频率',
            'switch_strategy.required_if' => '启用防红系统时必须选择切换策略',
            'switch_strategy.in' => '请选择有效的切换策略',
            'offer_price.required_if' => '启用限时优惠时必须设置优惠价格',
            'offer_price.numeric' => '优惠价格必须是数字',
            'offer_price.min' => '优惠价格不能小于0',
            'offer_price.max' => '优惠价格不能超过9999.99',
            'offer_end_time.required_if' => '启用限时优惠时必须设置结束时间',
            'offer_end_time.date' => '优惠结束时间格式不正确',
            'offer_end_time.after' => '优惠结束时间必须是未来时间',
            'remaining_slots.required_if' => '启用紧迫感营销时必须设置剩余名额',
            'remaining_slots.integer' => '剩余名额必须是整数',
            'remaining_slots.min' => '剩余名额不能小于0',
            'recent_join_text.required_if' => '启用社交证明时必须设置最近加入提示',
            'recent_join_text.max' => '最近加入提示不能超过100个字符',
            'custom_analytics_code.max' => '自定义统计代码不能超过2000个字符'
        ];
    }

    /**
     * 配置验证器实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑

            // 验证佣金比例总和不超过100%
            if ($this->input('enable_distribution')) {
                $totalCommission = $this->input('commission_rate_1', 0) + 
                                 $this->input('commission_rate_2', 0) + 
                                 $this->input('commission_rate_3', 0);
                
                if ($totalCommission > 100) {
                    $validator->errors()->add('commission_rates', '佣金比例总和不能超过100%');
                }
            }

            // 验证优惠价格不能高于原价格
            if ($this->input('enable_limited_offer')) {
                $originalPrice = $this->input('price', 0);
                $offerPrice = $this->input('offer_price', 0);
                
                if ($offerPrice >= $originalPrice) {
                    $validator->errors()->add('offer_price', '优惠价格必须低于原价格');
                }
            }

            // 验证免费群组不能启用分销
            if ($this->input('price', 0) == 0 && $this->input('enable_distribution')) {
                $validator->errors()->add('enable_distribution', '免费群组不能启用分销功能');
            }

            // 验证媒体文件数量限制
            $mediaImages = $this->input('media_images', []);
            if (count($mediaImages) > 10) {
                $validator->errors()->add('media_images', '宣传图片最多只能上传10张');
            }

            // 验证群组名称中的城市占位符
            if ($this->input('city_location') && $this->input('city_insert_strategy') === 'natural') {
                $title = $this->input('title', '');
                if ($title && strpos($title, 'xxx') === false) {
                    $validator->errors()->add('title', '使用自然插入策略时，群组名称中必须包含"xxx"占位符');
                }
            }
        });
    }

    /**
     * 准备验证数据
     */
    protected function prepareForValidation(): void
    {
        // 处理布尔值
        $booleanFields = [
            'city_location',
            'enable_distribution',
            'enable_anti_block',
            'enable_limited_offer',
            'enable_urgency',
            'show_countdown',
            'enable_social_proof',
            'enable_analytics',
            'enable_conversion_tracking'
        ];

        foreach ($booleanFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->input($field), FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }

        // 处理数字字段
        $numericFields = [
            'price',
            'member_limit',
            'like_count',
            'want_see_count',
            'virtual_members',
            'virtual_orders',
            'order_expire_minutes',
            'commission_rate_1',
            'commission_rate_2',
            'commission_rate_3',
            'offer_price',
            'remaining_slots'
        ];

        foreach ($numericFields as $field) {
            if ($this->has($field) && $this->input($field) !== null) {
                $this->merge([
                    $field => is_numeric($this->input($field)) ? (float)$this->input($field) : $this->input($field)
                ]);
            }
        }

        // 处理数组字段
        if ($this->has('payment_methods') && is_string($this->input('payment_methods'))) {
            $this->merge([
                'payment_methods' => json_decode($this->input('payment_methods'), true) ?: []
            ]);
        }

        if ($this->has('media_images') && is_string($this->input('media_images'))) {
            $this->merge([
                'media_images' => json_decode($this->input('media_images'), true) ?: []
            ]);
        }
    }
}