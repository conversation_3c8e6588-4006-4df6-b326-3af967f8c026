<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑落地页' : '创建落地页'"
    width="90%"
    :before-close="handleClose"
    class="landing-page-editor-dialog"
  >
    <div class="editor-container">
      <!-- 编辑器工具栏 -->
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <el-button-group>
            <el-button :type="activeTab === 'design' ? 'primary' : ''" @click="activeTab = 'design'">
              <el-icon><Edit /></el-icon>
              设计
            </el-button>
            <el-button :type="activeTab === 'preview' ? 'primary' : ''" @click="activeTab = 'preview'">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button :type="activeTab === 'settings' ? 'primary' : ''" @click="activeTab = 'settings'">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <el-button @click="handleSaveDraft">
            <el-icon><Document /></el-icon>
            保存草稿
          </el-button>
          <el-button type="success" @click="handlePublish">
            <el-icon><Upload /></el-icon>
            发布
          </el-button>
        </div>
      </div>

      <!-- 编辑器内容区 -->
      <div class="editor-content">
        <!-- 设计模式 -->
        <div v-show="activeTab === 'design'" class="design-panel">
          <div class="design-sidebar">
            <div class="component-library">
              <h4>组件库</h4>
              <div class="component-list">
                <div class="component-item" @click="addComponent('header')">
                  <el-icon><Grid /></el-icon>
                  <span>页头</span>
                </div>
                <div class="component-item" @click="addComponent('hero')">
                  <el-icon><Picture /></el-icon>
                  <span>英雄区</span>
                </div>
                <div class="component-item" @click="addComponent('features')">
                  <el-icon><List /></el-icon>
                  <span>特性列表</span>
                </div>
                <div class="component-item" @click="addComponent('form')">
                  <el-icon><Edit /></el-icon>
                  <span>表单</span>
                </div>
                <div class="component-item" @click="addComponent('footer')">
                  <el-icon><Bottom /></el-icon>
                  <span>页脚</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="design-canvas">
            <div class="canvas-container">
              <div class="page-canvas">
                <div v-for="(component, index) in pageComponents" :key="index" 
                     class="component-wrapper"
                     :class="{ active: selectedComponent === index }"
                     @click="selectComponent(index)">
                  <component :is="getComponentName(component.type)" 
                             :data="component.data"
                             @update="updateComponent(index, $event)" />
                  <div class="component-controls">
                    <el-button size="small" type="primary" @click="editComponent(index)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="removeComponent(index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                
                <div v-if="pageComponents.length === 0" class="empty-canvas">
                  <el-icon><Plus /></el-icon>
                  <p>从左侧组件库拖拽组件到这里开始设计</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="design-properties">
            <div class="properties-panel">
              <h4>属性面板</h4>
              <div v-if="selectedComponent !== null" class="component-properties">
                <ComponentProperties 
                  :component="pageComponents[selectedComponent]"
                  @update="updateComponent(selectedComponent, $event)" />
              </div>
              <div v-else class="no-selection">
                <p>请选择一个组件来编辑属性</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 预览模式 -->
        <div v-show="activeTab === 'preview'" class="preview-panel">
          <div class="preview-toolbar">
            <el-radio-group v-model="previewDevice" size="small">
              <el-radio-button label="desktop">
                <el-icon><Monitor /></el-icon>
                桌面
              </el-radio-button>
              <el-radio-button label="tablet">
                <el-icon><Iphone /></el-icon>
                平板
              </el-radio-button>
              <el-radio-button label="mobile">
                <el-icon><Cellphone /></el-icon>
                手机
              </el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="preview-container" :class="`preview-${previewDevice}`">
            <div class="preview-frame">
              <div class="preview-content">
                <div v-for="(component, index) in pageComponents" :key="index">
                  <component :is="getComponentName(component.type)" 
                             :data="component.data"
                             :preview="true" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设置模式 -->
        <div v-show="activeTab === 'settings'" class="settings-panel">
          <el-form :model="pageSettings" label-width="120px">
            <el-divider content-position="left">基本信息</el-divider>
            
            <el-form-item label="页面名称" required>
              <el-input v-model="pageSettings.name" placeholder="请输入页面名称" />
            </el-form-item>
            
            <el-form-item label="页面描述">
              <el-input v-model="pageSettings.description" 
                        type="textarea" 
                        :rows="3"
                        placeholder="请输入页面描述" />
            </el-form-item>
            
            <el-form-item label="页面URL">
              <el-input v-model="pageSettings.url" placeholder="https://example.com/landing-page">
                <template #prepend>
                  <span>https://</span>
                </template>
              </el-input>
            </el-form-item>
            
            <el-divider content-position="left">SEO设置</el-divider>
            
            <el-form-item label="页面标题">
              <el-input v-model="pageSettings.seo_title" 
                        placeholder="页面标题，用于搜索引擎显示"
                        maxlength="60"
                        show-word-limit />
            </el-form-item>
            
            <el-form-item label="页面描述">
              <el-input v-model="pageSettings.seo_description" 
                        type="textarea"
                        :rows="3"
                        placeholder="页面描述，用于搜索引擎显示"
                        maxlength="160"
                        show-word-limit />
            </el-form-item>
            
            <el-form-item label="关键词">
              <el-input v-model="pageSettings.seo_keywords" 
                        placeholder="关键词，用逗号分隔" />
            </el-form-item>
            
            <el-divider content-position="left">高级设置</el-divider>
            
            <el-form-item label="自定义CSS">
              <el-input v-model="pageSettings.custom_css" 
                        type="textarea"
                        :rows="6"
                        placeholder="/* 自定义CSS样式 */" />
            </el-form-item>
            
            <el-form-item label="自定义JS">
              <el-input v-model="pageSettings.custom_js" 
                        type="textarea"
                        :rows="6"
                        placeholder="// 自定义JavaScript代码" />
            </el-form-item>
            
            <el-form-item label="统计代码">
              <el-input v-model="pageSettings.analytics_code" 
                        type="textarea"
                        :rows="4"
                        placeholder="Google Analytics 或其他统计代码" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSave">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Edit, View, Setting, Document, Upload, Grid, Picture, List, Bottom,
  Plus, Delete, Monitor, Iphone, Cellphone
} from '@element-plus/icons-vue'
import ComponentProperties from './ComponentProperties.vue'
import HeaderComponent from './components/HeaderComponent.vue'
import HeroComponent from './components/HeroComponent.vue'
import FeaturesComponent from './components/FeaturesComponent.vue'
import FormComponent from './components/FormComponent.vue'
import FooterComponent from './components/FooterComponent.vue'
import { promotionApi } from '@/api/promotion'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  pageData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const activeTab = ref('design')
const previewDevice = ref('desktop')
const selectedComponent = ref(null)
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => {
  return props.pageData && props.pageData.id
})

const pageComponents = ref([])

const pageSettings = reactive({
  name: '',
  description: '',
  url: '',
  seo_title: '',
  seo_description: '',
  seo_keywords: '',
  custom_css: '',
  custom_js: '',
  analytics_code: ''
})

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initPageData()
    })
  }
})

const initPageData = () => {
  if (isEdit.value) {
    // 编辑模式，填充现有数据
    Object.keys(pageSettings).forEach(key => {
      if (props.pageData[key] !== undefined) {
        pageSettings[key] = props.pageData[key]
      }
    })
    
    // 加载页面组件
    pageComponents.value = props.pageData.components || []
  } else {
    // 创建模式，重置数据
    Object.keys(pageSettings).forEach(key => {
      pageSettings[key] = ''
    })
    pageComponents.value = []
  }
  
  selectedComponent.value = null
}

const addComponent = (type) => {
  const componentData = getDefaultComponentData(type)
  pageComponents.value.push({
    type,
    data: componentData
  })
}

const getDefaultComponentData = (type) => {
  const defaults = {
    header: {
      logo: '',
      title: '网站标题',
      navigation: ['首页', '产品', '关于我们', '联系我们']
    },
    hero: {
      title: '欢迎来到我们的产品',
      subtitle: '这里是产品的简短描述',
      background: '',
      buttonText: '立即开始',
      buttonLink: '#'
    },
    features: {
      title: '产品特性',
      items: [
        { title: '特性一', description: '特性描述', icon: 'star' },
        { title: '特性二', description: '特性描述', icon: 'heart' },
        { title: '特性三', description: '特性描述', icon: 'check' }
      ]
    },
    form: {
      title: '联系我们',
      fields: [
        { type: 'text', label: '姓名', required: true },
        { type: 'email', label: '邮箱', required: true },
        { type: 'textarea', label: '留言', required: false }
      ],
      buttonText: '提交'
    },
    footer: {
      copyright: '© 2024 公司名称. 保留所有权利.',
      links: ['隐私政策', '服务条款', '联系我们']
    }
  }
  
  return defaults[type] || {}
}

const getComponentName = (type) => {
  const components = {
    header: 'HeaderComponent',
    hero: 'HeroComponent',
    features: 'FeaturesComponent',
    form: 'FormComponent',
    footer: 'FooterComponent'
  }
  return components[type] || 'div'
}

const selectComponent = (index) => {
  selectedComponent.value = index
}

const editComponent = (index) => {
  selectedComponent.value = index
}

const updateComponent = (index, data) => {
  if (pageComponents.value[index]) {
    pageComponents.value[index].data = { ...data }
  }
}

const removeComponent = (index) => {
  pageComponents.value.splice(index, 1)
  if (selectedComponent.value === index) {
    selectedComponent.value = null
  }
}

const handleSaveDraft = async () => {
  await handleSave('draft')
}

const handlePublish = async () => {
  await handleSave('published')
}

const handleSave = async (status = 'draft') => {
  if (!pageSettings.name) {
    ElMessage.error('请输入页面名称')
    return
  }
  
  loading.value = true
  try {
    const submitData = {
      ...pageSettings,
      components: pageComponents.value,
      status
    }
    
    if (isEdit.value) {
      await promotionApi.update(props.pageData.id, submitData)
      ElMessage.success('落地页更新成功')
    } else {
      await promotionApi.create(submitData)
      ElMessage.success('落地页创建成功')
    }
    
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.landing-page-editor-dialog {
  :deep(.el-dialog) {
    margin-top: 5vh;
    margin-bottom: 5vh;
    height: 90vh;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }
}

.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.design-panel {
  display: flex;
  height: 100%;
  
  .design-sidebar {
    width: 250px;
    border-right: 1px solid #ebeef5;
    background: #f8f9fa;
    
    .component-library {
      padding: 20px;
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: #303133;
      }
      
      .component-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .component-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px;
          background: white;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            background: #e3f2fd;
            transform: translateY(-1px);
          }
          
          span {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }
  
  .design-canvas {
    flex: 1;
    overflow: auto;
    background: #f0f2f5;
    
    .canvas-container {
      padding: 20px;
      min-height: 100%;
      
      .page-canvas {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        min-height: 600px;
        
        .component-wrapper {
          position: relative;
          border: 2px solid transparent;
          
          &.active {
            border-color: #409eff;
          }
          
          &:hover .component-controls {
            opacity: 1;
          }
          
          .component-controls {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }
        
        .empty-canvas {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          color: #909399;
          
          .el-icon {
            font-size: 48px;
            margin-bottom: 16px;
          }
          
          p {
            margin: 0;
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .design-properties {
    width: 300px;
    border-left: 1px solid #ebeef5;
    background: #f8f9fa;
    
    .properties-panel {
      padding: 20px;
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: #303133;
      }
      
      .no-selection {
        text-align: center;
        color: #909399;
        padding: 40px 20px;
      }
    }
  }
}

.preview-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .preview-toolbar {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #f8f9fa;
    text-align: center;
  }
  
  .preview-container {
    flex: 1;
    overflow: auto;
    background: #f0f2f5;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    
    .preview-frame {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    &.preview-desktop .preview-frame {
      width: 1200px;
      min-height: 800px;
    }
    
    &.preview-tablet .preview-frame {
      width: 768px;
      min-height: 1024px;
    }
    
    &.preview-mobile .preview-frame {
      width: 375px;
      min-height: 667px;
    }
  }
}

.settings-panel {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  overflow: auto;
  height: 100%;
}

.dialog-footer {
  text-align: right;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}
</style>