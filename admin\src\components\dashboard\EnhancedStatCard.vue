<template>
  <div class="enhanced-stat-card" :class="[`theme-${theme}`, { 'loading': loading }]">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 图标区域 -->
      <div class="stat-icon" :style="{ background: iconGradient }">
        <el-icon :size="iconSize">
          <component :is="icon" />
        </el-icon>
        <div class="icon-glow"></div>
      </div>

      <!-- 数据区域 -->
      <div class="stat-data">
        <div class="stat-value-container">
          <CountTo 
            :start-val="0" 
            :end-val="value" 
            :duration="1500"
            :decimals="decimals"
            :prefix="prefix"
            :suffix="suffix"
            class="stat-value"
          />
          <div v-if="trend" class="stat-trend" :class="trendType">
            <el-icon :size="12">
              <ArrowUp v-if="trendType === 'up'" />
              <ArrowDown v-else-if="trendType === 'down'" />
              <Minus v-else />
            </el-icon>
            <span>{{ trend }}</span>
          </div>
        </div>
        
        <div class="stat-label">{{ label }}</div>
        
        <div v-if="description" class="stat-description">
          {{ description }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div v-if="showAction" class="stat-action">
        <el-button 
          text 
          class="action-btn"
          @click="$emit('action-click')"
        >
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 迷你图表 -->
    <div v-if="chartData && chartData.length > 0" class="mini-chart-container">
      <div ref="miniChart" class="mini-chart"></div>
    </div>

    <!-- 底部信息 -->
    <div v-if="footerText" class="card-footer">
      <span class="footer-text">{{ footerText }}</span>
      <span v-if="lastUpdate" class="last-update">
        更新于 {{ formatTime(lastUpdate) }}
      </span>
    </div>

    <!-- 装饰元素 -->
    <div class="decoration-elements">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-line"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { ArrowUp, ArrowDown, Minus, ArrowRight } from '@element-plus/icons-vue'
import CountTo from '@/components/CountTo.vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 基础数据
  value: {
    type: [Number, String],
    required: true
  },
  label: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  
  // 图标配置
  icon: {
    type: [String, Object],
    required: true
  },
  iconSize: {
    type: Number,
    default: 24
  },
  iconGradient: {
    type: String,
    default: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
  },
  
  // 主题配置
  theme: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  
  // 数值格式化
  decimals: {
    type: Number,
    default: 0
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  
  // 趋势数据
  trend: {
    type: String,
    default: ''
  },
  trendType: {
    type: String,
    default: 'up',
    validator: (value) => ['up', 'down', 'flat'].includes(value)
  },
  
  // 图表数据
  chartData: {
    type: Array,
    default: () => []
  },
  chartColor: {
    type: String,
    default: '#3b82f6'
  },
  
  // 其他配置
  loading: {
    type: Boolean,
    default: false
  },
  showAction: {
    type: Boolean,
    default: false
  },
  footerText: {
    type: String,
    default: ''
  },
  lastUpdate: {
    type: Date,
    default: null
  }
})

const emit = defineEmits(['action-click'])

const miniChart = ref(null)
let chartInstance = null

// 计算属性
const themeColors = computed(() => {
  const themes = {
    default: {
      background: 'rgba(255, 255, 255, 0.95)',
      border: 'rgba(255, 255, 255, 0.2)',
      shadow: '0 8px 32px rgba(0, 0, 0, 0.08)'
    },
    primary: {
      background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%)',
      border: 'rgba(59, 130, 246, 0.2)',
      shadow: '0 8px 32px rgba(59, 130, 246, 0.15)'
    },
    success: {
      background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%)',
      border: 'rgba(16, 185, 129, 0.2)',
      shadow: '0 8px 32px rgba(16, 185, 129, 0.15)'
    },
    warning: {
      background: 'linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%)',
      border: 'rgba(245, 158, 11, 0.2)',
      shadow: '0 8px 32px rgba(245, 158, 11, 0.15)'
    },
    danger: {
      background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%)',
      border: 'rgba(239, 68, 68, 0.2)',
      shadow: '0 8px 32px rgba(239, 68, 68, 0.15)'
    },
    info: {
      background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%)',
      border: 'rgba(139, 92, 246, 0.2)',
      shadow: '0 8px 32px rgba(139, 92, 246, 0.15)'
    }
  }
  return themes[props.theme] || themes.default
})

// 方法
const formatTime = (date) => {
  if (!date) return ''
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return date.toLocaleDateString()
}

const initMiniChart = () => {
  if (!miniChart.value || !props.chartData.length) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(miniChart.value)
  
  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: props.chartData.map((_, index) => index)
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [{
      type: 'line',
      data: props.chartData,
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: props.chartColor,
        width: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: `${props.chartColor}40` },
          { offset: 1, color: `${props.chartColor}10` }
        ])
      }
    }]
  }
  
  chartInstance.setOption(option)
}

// 监听图表数据变化
watch(() => props.chartData, () => {
  nextTick(() => {
    initMiniChart()
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initMiniChart()
  })
})
</script>

<style lang="scss" scoped>
.enhanced-stat-card {
  position: relative;
  background: v-bind('themeColors.background');
  backdrop-filter: blur(20px);
  border: 1px solid v-bind('themeColors.border');
  border-radius: 20px;
  box-shadow: v-bind('themeColors.shadow');
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);

    .stat-icon {
      transform: scale(1.1) rotate(5deg);

      .icon-glow {
        opacity: 1;
        transform: scale(1.5);
      }
    }

    .decoration-elements {
      .decoration-circle {
        transform: scale(1.2);
        opacity: 0.8;
      }

      .decoration-line {
        width: 100%;
        opacity: 0.6;
      }
    }
  }

  &.loading {
    pointer-events: none;
  }

  // 主题变体
  &.theme-primary {
    .stat-value {
      color: #1d4ed8;
    }
  }

  &.theme-success {
    .stat-value {
      color: #059669;
    }
  }

  &.theme-warning {
    .stat-value {
      color: #d97706;
    }
  }

  &.theme-danger {
    .stat-value {
      color: #dc2626;
    }
  }

  &.theme-info {
    .stat-value {
      color: #7c3aed;
    }
  }
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 卡片内容
.card-content {
  padding: 24px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
  z-index: 2;
}

// 图标区域
.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;

  .icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: inherit;
    opacity: 0;
    filter: blur(8px);
    transition: all 0.3s ease;
  }
}

// 数据区域
.stat-data {
  flex: 1;
  min-width: 0;

  .stat-value-container {
    display: flex;
    align-items: baseline;
    gap: 12px;
    margin-bottom: 8px;

    .stat-value {
      font-size: 32px;
      font-weight: 700;
      color: #1e293b;
      line-height: 1;
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 12px;

      &.up {
        color: #059669;
        background: rgba(16, 185, 129, 0.1);
      }

      &.down {
        color: #dc2626;
        background: rgba(239, 68, 68, 0.1);
      }

      &.flat {
        color: #6b7280;
        background: rgba(107, 114, 128, 0.1);
      }
    }
  }

  .stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 4px;
  }

  .stat-description {
    font-size: 12px;
    color: #94a3b8;
    line-height: 1.4;
  }
}

// 操作按钮
.stat-action {
  .action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    color: #64748b;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      transform: translateX(2px);
    }
  }
}

// 迷你图表
.mini-chart-container {
  height: 60px;
  margin: 0 24px;
  position: relative;
  z-index: 2;

  .mini-chart {
    width: 100%;
    height: 100%;
  }
}

// 底部信息
.card-footer {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(248, 250, 252, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;

  .footer-text {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
  }

  .last-update {
    font-size: 11px;
    color: #94a3b8;
  }
}

// 装饰元素
.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.05);
    transition: all 0.3s ease;

    &.circle-1 {
      width: 120px;
      height: 120px;
      top: -60px;
      right: -60px;
      background: rgba(59, 130, 246, 0.03);
    }

    &.circle-2 {
      width: 80px;
      height: 80px;
      bottom: -40px;
      left: -40px;
      background: rgba(139, 92, 246, 0.03);
    }
  }

  .decoration-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: all 0.3s ease;
  }
}

// 动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enhanced-stat-card {
    .card-content {
      padding: 20px;
      gap: 12px;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
    }

    .stat-data {
      .stat-value-container {
        .stat-value {
          font-size: 24px;
        }
      }
    }

    .mini-chart-container {
      height: 40px;
      margin: 0 20px;
    }

    .card-footer {
      padding: 12px 20px;
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }
  }
}
</style>