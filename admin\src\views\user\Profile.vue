<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 个人信息 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
            </div>
          </template>
          <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px">
            <el-form-item label="头像">
              <AvatarUpload 
                v-model="profileForm.avatar"
                :size="120"
                :max-size="5"
                :enable-preview="true"
                @success="handleAvatarSuccess"
                @error="handleAvatarError"
              />
            </el-form-item>
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" readonly />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="角色">
              <el-tag :type="getRoleTagType(profileForm.role)">{{ getRoleName(profileForm.role) }}</el-tag>
            </el-form-item>
            <el-form-item label="余额">
              <span class="balance">{{ profileForm.balance }} 元</span>
            </el-form-item>
            <el-form-item label="注册时间">
              <span>{{ profileForm.created_at }}</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateProfile" :loading="profileLoading">更新信息</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 修改密码 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>修改密码</span>
            </div>
          </template>
          <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
            <el-form-item label="当前密码" prop="current_password">
              <el-input
                v-model="passwordForm.current_password"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input
                v-model="passwordForm.password"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认密码" prop="password_confirmation">
              <el-input
                v-model="passwordForm.password_confirmation"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="passwordLoading">修改密码</el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 账户统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>账户统计</span>
            </div>
          </template>
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_orders || 0 }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_amount || 0 }} 元</div>
                <div class="stat-label">总消费金额</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.total_commission || 0 }} 元</div>
                <div class="stat-label">总佣金收入</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.children_count || 0 }}</div>
                <div class="stat-label">下级用户数</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 登录记录 -->
    <el-row class="mt-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>登录记录</span>
            </div>
          </template>
          <div class="login-info">
            <p><strong>最后登录时间：</strong>{{ profileForm.last_login_at || '从未登录' }}</p>
            <p><strong>最后登录IP：</strong>{{ profileForm.last_login_ip || '未知' }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getInfo, updateProfile as updateProfileApi, changePassword as changePasswordApi } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'
import { Plus } from '@element-plus/icons-vue'
import AvatarUpload from '@/components/AvatarUpload.vue'

const userStore = useUserStore()
const profileFormRef = ref()
const passwordFormRef = ref()
const profileLoading = ref(false)
const passwordLoading = ref(false)

const profileForm = ref({
  id: null,
  username: '',
  nickname: '',
  email: '',
  phone: '',
  avatar: '',
  role: '',
  balance: 0,
  created_at: '',
  last_login_at: '',
  last_login_ip: ''
})

const passwordForm = ref({
  current_password: '',
  password: '',
  password_confirmation: ''
})

const userStats = ref({
  total_orders: 0,
  total_amount: 0,
  total_commission: 0,
  children_count: 0
})

const uploadUrl = ref('/api/v1/upload')
const uploadHeaders = ref({
  'Authorization': `Bearer ${getToken()}`
})

const profileRules = reactive({
  nickname: [{ required: true, message: '昵称不能为空', trigger: 'blur' }],
  email: [
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ]
})

const passwordRules = reactive({
  current_password: [{ required: true, message: '当前密码不能为空', trigger: 'blur' }],
  password: [
    { required: true, message: '新密码不能为空', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  password_confirmation: [
    { required: true, message: '确认密码不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const getUserInfo = async () => {
  try {
    const { data } = await getInfo()
    if (data.success) {
      profileForm.value = { ...data.data.user }
      userStats.value = data.data.stats || {}
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

const updateProfile = async () => {
  try {
    await profileFormRef.value.validate()
    profileLoading.value = true
    
    const { data } = await updateProfileApi({
      nickname: profileForm.value.nickname,
      email: profileForm.value.email,
      phone: profileForm.value.phone,
      avatar: profileForm.value.avatar
    })
    
    if (data.success) {
      ElMessage.success('个人信息更新成功')
      // 更新store中的用户信息
      await userStore.getUserInfo()
    }
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    profileLoading.value = false
  }
}

const changePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    const { data } = await changePasswordApi({
      current_password: passwordForm.value.current_password,
      password: passwordForm.value.password,
      password_confirmation: passwordForm.value.password_confirmation
    })
    
    if (data.success) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    }
  } catch (error) {
    console.error('密码修改失败:', error)
  } finally {
    passwordLoading.value = false
  }
}

const resetPasswordForm = () => {
  passwordForm.value = {
    current_password: '',
    password: '',
    password_confirmation: ''
  }
  passwordFormRef.value?.resetFields()
}

const handleAvatarSuccess = (data) => {
  // 头像上传成功后，自动更新个人资料
  profileForm.value.avatar = data.url
  
  // 同时更新用户存储中的头像信息
  if (userStore.userInfo) {
    userStore.userInfo.avatar = data.url
  }
  
  ElMessage.success('头像上传成功!')
}

const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

const getRoleTagType = (role) => {
  const roleMap = {
    admin: 'danger',
    substation: 'warning',
    distributor: 'success',
    user: 'info'
  }
  return roleMap[role]
}

const getRoleName = (role) => {
  const roleMap = {
    admin: '超级管理员',
    substation: '分站管理员',
    distributor: '分销商',
    user: '普通用户'
  }
  return roleMap[role]
}

onMounted(() => {
  getUserInfo()
})
</script>

<style lang="scss" scoped>
.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mt-4 {
    margin-top: 20px;
  }
  
  .balance {
    color: #f56c6c;
    font-weight: bold;
    font-size: 16px;
  }
  
  .stats-row {
    .stat-item {
      text-align: center;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  .login-info {
    p {
      margin: 10px 0;
      font-size: 14px;
      
      strong {
        color: #606266;
      }
    }
  }
}

/* 头像上传样式已移至 AvatarUpload 组件中 */
</style> 