// 路由懒加载优化配置
// admin/src/router/index.js - 优化版

import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/Layout.vue'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.3,
  easing: 'ease',
  speed: 500
})

// 路由组件懒加载函数
const lazyLoad = (view) => {
  return () => import(`@/views/${view}.vue`)
}

// 带错误处理的懒加载
const lazyLoadWithError = (view) => {
  return () => import(`@/views/${view}.vue`)
    .catch(error => {
      console.error(`Failed to load component: ${view}`, error)
      return import('@/views/Error/LoadError.vue')
    })
}

// 预加载重要组件
const preloadComponent = (view) => {
  return () => import(
    /* webpackChunkName: "critical" */
    `@/views/${view}.vue`
  )
}

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: preloadComponent('Login'), // 登录页预加载
    meta: { 
      title: '登录', 
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/fullscreen-data-screen',
    name: 'FullscreenDataScreen',
    component: lazyLoad('dashboard/DataScreenFullscreen'),
    meta: { 
      title: '数据大屏', 
      hidden: true, 
      fullscreen: true 
    }
  },
  {
    path: '/dashboard',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: preloadComponent('dashboard/Dashboard'), // 仪表板预加载
        meta: { 
          title: '数据看板', 
          icon: 'Monitor',
          cache: true // 启用组件缓存
        }
      }
    ]
  },
  {
    path: '/data-screen',
    component: Layout,
    children: [
      {
        path: '',
        name: 'DataScreen',
        component: lazyLoad('dashboard/DataScreen'),
        meta: { 
          title: '数据大屏', 
          icon: 'DataBoard',
          cache: true
        }
      }
    ]
  },
  {
    path: '/community',
    component: Layout,
    meta: { title: '社群管理', icon: 'Comment' },
    children: [
      {
        path: 'groups',
        name: 'GroupList',
        component: lazyLoadWithError('community/GroupList'),
        meta: { 
          title: '社群列表', 
          icon: 'el-icon-s-custom',
          cache: true
        }
      },
      {
        path: 'templates',
        name: 'TemplateManagement',
        component: lazyLoad('community/TemplateManagement'),
        meta: { 
          title: '模板管理', 
          icon: 'el-icon-document-copy' 
        }
      }
    ]
  },
  {
    path: '/distribution',
    component: Layout,
    redirect: '/distribution/groups',
    name: 'Distribution',
    meta: { title: '分销管理', icon: 'el-icon-s-promotion' },
    children: [
      {
        path: 'groups',
        name: 'DistributionGroupList',
        component: lazyLoad('distribution/GroupList'),
        meta: { 
          title: '分销组管理', 
          icon: 'el-icon-s-grid',
          cache: true
        }
      },
      {
        path: 'distributors',
        name: 'DistributorList',
        component: lazyLoad('distribution/DistributorList'),
        meta: { 
          title: '分销商管理', 
          icon: 'el-icon-user-solid',
          cache: true
        }
      },
      {
        path: 'detail/:id',
        name: 'DistributorDetail',
        component: lazyLoad('distribution/DistributorDetail'),
        meta: { 
          title: '分销员详情', 
          icon: 'el-icon-user', 
          hidden: true,
          cache: false // 详情页不缓存
        }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/dashboard',
    name: 'Finance',
    meta: { title: '财务管理', icon: 'el-icon-money' },
    children: [
      {
        path: 'dashboard',
        name: 'FinanceDashboard',
        component: lazyLoad('finance/FinanceDashboard'),
        meta: { 
          title: '财务总览', 
          icon: 'el-icon-data-board',
          cache: true
        }
      },
      {
        path: 'commission-logs',
        name: 'CommissionLog',
        component: lazyLoad('finance/CommissionLog'),
        meta: { 
          title: '佣金明细', 
          icon: 'el-icon-medal',
          cache: true
        }
      },
      {
        path: 'transactions',
        name: 'TransactionList',
        component: lazyLoad('finance/TransactionList'),
        meta: { 
          title: '交易记录', 
          icon: 'el-icon-goods',
          cache: true
        }
      },
      {
        path: 'withdraw',
        name: 'WithdrawManage',
        component: lazyLoad('finance/WithdrawManage'),
        meta: { 
          title: '提现管理', 
          icon: 'el-icon-upload',
          cache: true
        }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    name: 'User',
    meta: { title: '用户管理', icon: 'el-icon-user' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: lazyLoad('user/UserList'),
        meta: { 
          title: '用户列表', 
          icon: 'el-icon-user-solid',
          cache: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: lazyLoad('user/Profile'),
        meta: { 
          title: '个人资料', 
          icon: 'el-icon-s-custom',
          cache: false
        }
      },
      {
        path: 'analytics',
        name: 'UserAnalytics',
        component: lazyLoad('user/UserAnalytics'),
        meta: { 
          title: '用户分析', 
          icon: 'el-icon-data-analysis',
          cache: true
        }
      }
    ]
  },
  {
    path: '/substation',
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'SubstationList',
        component: lazyLoad('substation/SubstationList'),
        meta: { 
          title: '分站管理', 
          icon: 'el-icon-office-building',
          cache: true
        }
      }
    ]
  },
  {
    path: '/anti-block',
    component: Layout,
    redirect: '/anti-block/dashboard',
    name: 'AntiBlock',
    meta: { title: '防红系统', icon: 'el-icon-s-tools' },
    children: [
      {
        path: 'dashboard',
        name: 'AntiBlockDashboard',
        component: lazyLoad('anti-block/Dashboard'),
        meta: { 
          title: '系统概览', 
          icon: 'el-icon-data-line',
          cache: true
        }
      },
      {
        path: 'domains',
        name: 'DomainList',
        component: lazyLoad('anti-block/DomainList'),
        meta: { 
          title: '域名管理', 
          icon: 'el-icon-connection',
          cache: true
        }
      },
      {
        path: 'short-links',
        name: 'ShortLinkList',
        component: lazyLoad('anti-block/ShortLinkList'),
        meta: { 
          title: '短链接管理', 
          icon: 'el-icon-link',
          cache: true
        }
      },
      {
        path: 'analytics',
        name: 'AntiBlockAnalytics',
        component: lazyLoad('anti-block/Analytics'),
        meta: { 
          title: '统计分析', 
          icon: 'el-icon-data-analysis',
          cache: true
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/settings',
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting' },
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: lazyLoad('system/Settings'),
        meta: { 
          title: '系统设置', 
          icon: 'el-icon-s-tools',
          cache: false
        }
      },
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: lazyLoad('system/SystemMonitor'),
        meta: { 
          title: '系统监控', 
          icon: 'el-icon-monitor',
          cache: false
        }
      },
      {
        path: 'export',
        name: 'DataExport',
        component: lazyLoad('system/DataExport'),
        meta: { 
          title: '数据导出', 
          icon: 'el-icon-download',
          cache: false
        }
      },
      {
        path: 'notifications',
        name: 'SystemNotifications',
        component: lazyLoad('system/Notifications'),
        meta: { 
          title: '通知管理', 
          icon: 'el-icon-bell',
          cache: true
        }
      },
      {
        path: 'operation-logs',
        name: 'OperationLogs',
        component: lazyLoad('system/OperationLogs'),
        meta: { 
          title: '操作日志', 
          icon: 'el-icon-document',
          cache: true
        }
      },
      {
        path: 'function-test',
        name: 'FunctionTest',
        component: lazyLoad('system/FunctionTest'),
        meta: { 
          title: '功能测试', 
          icon: 'el-icon-cpu',
          cache: false
        }
      },
      {
        path: 'user-guide',
        name: 'UserGuide',
        component: lazyLoad('system/UserGuide'),
        meta: { 
          title: '使用指南', 
          icon: 'el-icon-info',
          cache: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: lazyLoad('NotFound'),
    meta: { title: '404', hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory('/admin/'),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 预加载策略
const preloadImportantRoutes = () => {
  // 预加载重要的路由组件
  const importantRoutes = [
    'dashboard/Dashboard',
    'community/GroupList',
    'user/UserList',
    'finance/FinanceDashboard'
  ]
  
  requestIdleCallback(() => {
    importantRoutes.forEach(route => {
      import(`@/views/${route}.vue`).catch(err => {
        console.warn(`Failed to preload ${route}:`, err)
      })
    })
  })
}

// 路由加载状态管理
let loadingCount = 0
let loadingTimer = null

const startLoading = () => {
  loadingCount++
  if (loadingCount === 1) {
    loadingTimer = setTimeout(() => {
      NProgress.start()
    }, 200) // 延迟显示加载条，避免闪烁
  }
}

const stopLoading = () => {
  loadingCount = Math.max(0, loadingCount - 1)
  if (loadingCount === 0) {
    if (loadingTimer) {
      clearTimeout(loadingTimer)
      loadingTimer = null
    }
    NProgress.done()
  }
}

// 路由守卫优化
router.beforeEach(async (to, from, next) => {
  startLoading()
  
  const userStore = useUserStore()
  const hasToken = userStore.token

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 晨鑫流量变现系统` : '晨鑫流量变现系统'

  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
      stopLoading()
    } else {
      // 检查用户信息
      if (!userStore.userInfo) {
        try {
          await userStore.getUserInfo()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          await userStore.resetToken()
          next(`/login?redirect=${to.path}`)
          stopLoading()
          return
        }
      }
      next()
    }
  } else {
    // 不需要认证的页面
    if (to.meta.noAuth) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
      stopLoading()
    }
  }
})

router.afterEach((to, from) => {
  stopLoading()
  
  // 页面访问统计
  if (process.env.NODE_ENV === 'production') {
    // 可以在这里添加页面访问统计
    console.log(`Page view: ${to.path}`)
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  stopLoading()
  
  // 可以在这里添加错误上报
  if (process.env.NODE_ENV === 'production') {
    // 上报错误到监控系统
  }
})

// 应用启动时预加载
setTimeout(preloadImportantRoutes, 2000)

export default router