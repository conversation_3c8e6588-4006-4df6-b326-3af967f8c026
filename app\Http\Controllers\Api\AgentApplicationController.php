<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AgentApplication;
use App\Models\Substation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * 代理商申请控制器
 */
class AgentApplicationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * 获取申请列表
     */
    public function index(Request $request)
    {
        $query = AgentApplication::with(['user:id,username,name,phone', 'substation:id,name', 'reviewer:id,username,name']);

        // 根据用户角色过滤数据
        $user = $request->user();
        if ($user->hasRole('substation')) {
            $query->where('substation_id', $user->substation_id);
        }

        // 搜索条件
        if ($request->filled('keyword')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('username', 'like', '%' . $request->keyword . '%')
                  ->orWhere('name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('phone', 'like', '%' . $request->keyword . '%');
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 代理商类型筛选
        if ($request->filled('agent_type')) {
            $query->where('agent_type', $request->agent_type);
        }

        // 代理商等级筛选
        if ($request->filled('agent_level')) {
            $query->where('agent_level', $request->agent_level);
        }

        // 分站筛选
        if ($request->filled('substation_id')) {
            $query->where('substation_id', $request->substation_id);
        }

        $applications = $query->orderBy('created_at', 'desc')
                             ->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $applications,
        ]);
    }

    /**
     * 提交代理商申请
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'agent_type' => 'required|in:individual,enterprise,channel',
            'agent_level' => 'required|in:platform,substation',
            'substation_id' => 'nullable|exists:substations,id',
            'business_info' => 'required|array',
            'business_info.company_name' => 'nullable|string|max:200',
            'business_info.business_license' => 'nullable|string|max:100',
            'business_info.business_scope' => 'nullable|string|max:500',
            'contact_info' => 'required|array',
            'contact_info.contact_person' => 'required|string|max:50',
            'contact_info.contact_phone' => 'required|string|max:20',
            'contact_info.contact_email' => 'nullable|email|max:100',
            'contact_info.contact_address' => 'nullable|string|max:200',
            'experience_info' => 'nullable|array',
            'experience_info.marketing_experience' => 'nullable|string|max:1000',
            'experience_info.team_size' => 'nullable|integer|min:0',
            'experience_info.monthly_target' => 'nullable|numeric|min:0',
            'expected_commission_rate' => 'required|numeric|min:0|max:100',
            'application_reason' => 'required|string|max:1000',
            'attachments' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $user = $request->user();

        // 检查用户是否已经是代理商
        if ($user->hasRole('agent')) {
            return response()->json([
                'success' => false,
                'message' => '您已经是代理商了',
            ], 422);
        }

        // 检查是否已有待审核的申请
        if (AgentApplication::where('user_id', $user->id)->pending()->exists()) {
            return response()->json([
                'success' => false,
                'message' => '您已有待审核的申请，请耐心等待',
            ], 422);
        }

        // 如果申请分站代理商，必须指定分站
        if ($request->agent_level === 'substation' && !$request->substation_id) {
            return response()->json([
                'success' => false,
                'message' => '申请分站代理商必须选择分站',
            ], 422);
        }

        try {
            $application = AgentApplication::create([
                'user_id' => $user->id,
                'agent_type' => $request->agent_type,
                'agent_level' => $request->agent_level,
                'substation_id' => $request->substation_id,
                'business_info' => $request->business_info,
                'contact_info' => $request->contact_info,
                'experience_info' => $request->experience_info ?? [],
                'expected_commission_rate' => $request->expected_commission_rate,
                'application_reason' => $request->application_reason,
                'attachments' => $request->attachments ?? [],
                'status' => AgentApplication::STATUS_PENDING,
            ]);

            return response()->json([
                'success' => true,
                'data' => $application->load(['substation']),
                'message' => '申请提交成功，请等待审核',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '申请提交失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取申请详情
     */
    public function show($id)
    {
        $application = AgentApplication::with([
            'user:id,username,name,phone,email,avatar',
            'substation:id,name,domain',
            'reviewer:id,username,name'
        ])->findOrFail($id);

        // 权限检查
        $user = request()->user();
        if (!$user->hasRole('admin') && 
            !($user->hasRole('substation') && $application->substation_id === $user->substation_id) &&
            $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => '无权限查看此申请',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $application,
        ]);
    }

    /**
     * 审核申请
     */
    public function review(Request $request, $id)
    {
        $application = AgentApplication::findOrFail($id);

        // 权限检查
        $user = $request->user();
        if (!$user->hasRole('admin') && 
            !($user->hasRole('substation') && $application->substation_id === $user->substation_id)) {
            return response()->json([
                'success' => false,
                'message' => '无权限审核此申请',
            ], 403);
        }

        if (!$application->canReview()) {
            return response()->json([
                'success' => false,
                'message' => '该申请无法审核',
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:approve,reject',
            'comment' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            DB::beginTransaction();

            if ($request->action === 'approve') {
                $application->approve($user->id, $request->comment);
                $message = '申请审核通过';
            } else {
                $application->reject($user->id, $request->comment);
                $message = '申请审核拒绝';
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $application->fresh(['user', 'substation', 'reviewer']),
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '审核失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 取消申请
     */
    public function cancel($id)
    {
        $application = AgentApplication::findOrFail($id);

        // 权限检查：只有申请人可以取消
        if ($application->user_id !== request()->user()->id) {
            return response()->json([
                'success' => false,
                'message' => '无权限取消此申请',
            ], 403);
        }

        if (!$application->canCancel()) {
            return response()->json([
                'success' => false,
                'message' => '该申请无法取消',
            ], 422);
        }

        $application->cancel();

        return response()->json([
            'success' => true,
            'message' => '申请已取消',
        ]);
    }

    /**
     * 获取我的申请
     */
    public function getMy(Request $request)
    {
        $user = $request->user();
        
        $applications = AgentApplication::where('user_id', $user->id)
            ->with(['substation:id,name', 'reviewer:id,username,name'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $applications,
        ]);
    }

    /**
     * 获取申请统计
     */
    public function getStats(Request $request)
    {
        $user = $request->user();
        $query = AgentApplication::query();

        // 根据用户角色过滤数据
        if ($user->hasRole('substation')) {
            $query->where('substation_id', $user->substation_id);
        }

        $stats = [
            'total' => $query->count(),
            'pending' => (clone $query)->where('status', AgentApplication::STATUS_PENDING)->count(),
            'approved' => (clone $query)->where('status', AgentApplication::STATUS_APPROVED)->count(),
            'rejected' => (clone $query)->where('status', AgentApplication::STATUS_REJECTED)->count(),
            'today' => (clone $query)->whereDate('created_at', today())->count(),
            'this_week' => (clone $query)->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => (clone $query)->whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 批量审核
     */
    public function batchReview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'application_ids' => 'required|array',
            'application_ids.*' => 'exists:agent_applications,id',
            'action' => 'required|in:approve,reject',
            'comment' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $user = $request->user();
        $processedCount = 0;
        $errors = [];

        try {
            DB::beginTransaction();

            foreach ($request->application_ids as $id) {
                try {
                    $application = AgentApplication::findOrFail($id);

                    // 权限检查
                    if (!$user->hasRole('admin') && 
                        !($user->hasRole('substation') && $application->substation_id === $user->substation_id)) {
                        $errors[] = "申请 {$id}: 无权限审核";
                        continue;
                    }

                    if (!$application->canReview()) {
                        $errors[] = "申请 {$id}: 无法审核";
                        continue;
                    }

                    if ($request->action === 'approve') {
                        $application->approve($user->id, $request->comment);
                    } else {
                        $application->reject($user->id, $request->comment);
                    }

                    $processedCount++;

                } catch (\Exception $e) {
                    $errors[] = "申请 {$id}: " . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "成功处理 {$processedCount} 个申请",
                'processed_count' => $processedCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量审核失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取可选分站列表
     */
    public function getAvailableSubstations()
    {
        $substations = Substation::where('status', 1)
            ->select('id', 'name', 'domain')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $substations,
        ]);
    }
}