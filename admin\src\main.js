import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import { setupErrorHandler } from '@/utils/errorHandler'
import errorHandlingPlugin from '@/plugins/errorHandling'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// ECharts 全局配置
import '@/utils/echarts.js'

// 创建Vue应用
const app = createApp(App)

// 设置错误处理
setupErrorHandler()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(ElementPlus)
app.use(createPinia())
app.use(router)
app.use(errorHandlingPlugin)

// 挂载应用
app.mount('#app')