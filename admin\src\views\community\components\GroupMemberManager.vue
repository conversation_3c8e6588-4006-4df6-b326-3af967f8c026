<template>
  <div class="group-member-manager">
    <!-- 成员统计概览 -->
    <div class="member-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ memberStats.total_members }}</div>
              <div class="stat-label">总成员数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ memberStats.active_members }}</div>
              <div class="stat-label">活跃成员</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ memberStats.new_members_today }}</div>
              <div class="stat-label">今日新增</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Remove /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ memberStats.left_members_today }}</div>
              <div class="stat-label">今日退出</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 成员管理工具栏 -->
    <div class="member-toolbar">
      <div class="toolbar-left">
        <h4>成员列表</h4>
      </div>
      <div class="toolbar-right">
        <el-dropdown @command="handleBatchCommand" v-if="selectedMembers.length > 0">
          <el-button type="primary">
            批量操作 ({{ selectedMembers.length }})<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="tag">
                <el-icon><PriceTag /></el-icon>批量打标签
              </el-dropdown-item>
              <el-dropdown-item command="mute">
                <el-icon><MuteNotification /></el-icon>批量禁言
              </el-dropdown-item>
              <el-dropdown-item command="kick" divided>
                <el-icon><Remove /></el-icon>批量移除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="handleAddMember">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
        <el-button @click="handleExportMembers">
          <el-icon><Download /></el-icon>
          导出成员
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="member-filters">
      <el-form :inline="true" :model="queryParams">
        <el-form-item label="搜索">
          <el-input
            v-model="queryParams.keyword"
            placeholder="用户名/手机号"
            clearable
            @keyup.enter="fetchMembers"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option label="正常" value="active" />
            <el-option label="已退出" value="left" />
            <el-option label="被踢出" value="kicked" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchMembers">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 成员列表 -->
    <div class="member-list">
      <el-table
        v-loading="loading"
        :data="memberList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="成员信息" min-width="200">
          <template #default="{ row }">
            <div class="member-info">
              <el-avatar :src="row.avatar" :size="40">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="member-details">
                <div class="member-name">{{ row.nickname || row.username }}</div>
                <div class="member-phone">{{ row.phone || '未绑定' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="加入时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.joined_at) }}
          </template>
        </el-table-column>
        <el-table-column label="成员标签" min-width="150">
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag"
              class="member-tag"
              size="small"
              closable
              @close="handleRemoveTag(row, tag)"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="row.inputVisible"
              v-model="row.inputValue"
              class="tag-input"
              size="small"
              @keyup.enter="handleInputConfirm(row)"
              @blur="handleInputConfirm(row)"
            />
            <el-button v-else class="button-new-tag" size="small" @click="showInput(row)">
              + 新标签
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="viewMemberProfile(row)">
              用户画像
            </el-button>
            <el-button link type="primary" size="small" @click="viewMember(row)">
              查看
            </el-button>
            <el-button link type="danger" size="small" @click="removeMember(row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchMembers"
          @current-change="fetchMembers"
        />
      </div>
    </div>

    <!-- 用户画像抽屉 -->
    <UserProfile
      :visible="userProfileVisible"
      :user-id="currentUserId"
      @update:visible="userProfileVisible = $event"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserProfile from '../UserProfile.vue'
import {
  User,
  UserFilled,
  Plus,
  Remove,
  Download,
  ArrowDown,
  PriceTag,
  MuteNotification
} from '@element-plus/icons-vue'

const props = defineProps({
  groupId: {
    type: [Number, String],
    required: true
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const loading = ref(false)
const memberList = ref([])
const total = ref(0)
const selectedMembers = ref([])
const userProfileVisible = ref(false)
const currentUserId = ref(null)

const memberStats = ref({
  total_members: 0,
  active_members: 0,
  new_members_today: 0,
  left_members_today: 0
})

const queryParams = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: ''
})

// 方法
const fetchMembers = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    memberList.value = [
      {
        id: 1,
        username: 'user001',
        nickname: '张三',
        phone: '138****1234',
        avatar: '',
        joined_at: '2024-01-15 10:30:00',
        status: 'active',
        tags: ['核心用户', '活跃'],
        inputVisible: false,
        inputValue: ''
      },
      {
        id: 2,
        username: 'user002',
        nickname: '李四',
        phone: '139****5678',
        avatar: '',
        joined_at: '2024-01-16 14:20:00',
        status: 'active',
        tags: ['潜在客户'],
        inputVisible: false,
        inputValue: ''
      },
      {
        id: 3,
        username: 'user003',
        nickname: '王五',
        phone: '137****4321',
        avatar: '',
        joined_at: '2024-02-10 09:00:00',
        status: 'left',
        tags: [],
        inputVisible: false,
        inputValue: ''
      }
    ]
    total.value = 2
    
    memberStats.value = {
      total_members: props.groupData.member_count || 0,
      active_members: Math.floor((props.groupData.member_count || 0) * 0.8),
      new_members_today: 5,
      left_members_today: 1
    }
    
  } catch (error) {
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

const handleAddMember = () => {
  ElMessage.info('添加成员功能开发中')
}

const handleExportMembers = () => {
  ElMessage.info('导出成员功能开发中')
}

const handleSelectionChange = (selection) => {
  selectedMembers.value = selection
}

const viewMember = (member) => {
  ElMessage.info(`查看成员: ${member.nickname}`)
}

const viewMemberProfile = (member) => {
  currentUserId.value = member.id
  userProfileVisible.value = true
}

const removeMember = (member) => {
  ElMessageBox.confirm(
    `确定要移除成员 "${member.nickname}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('成员移除成功')
    fetchMembers()
  })
}

const resetQuery = () => {
  Object.assign(queryParams, {
    page: 1,
    limit: 20,
    keyword: '',
    status: ''
  })
  fetchMembers()
}

// 批量操作处理
const handleBatchCommand = (command) => {
  const memberIds = selectedMembers.value.map(m => m.id)
  if (command === 'tag') {
    ElMessageBox.prompt('请输入要批量添加的标签', '批量打标签', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(({ value }) => {
      ElMessage.success(`已为 ${memberIds.length} 位成员添加标签: "${value}"`)
      fetchMembers() // 刷新数据
    }).catch(() => {})
  } else if (command === 'mute') {
    ElMessageBox.confirm(`确定要批量禁言这 ${memberIds.length} 位成员吗？`, '确认禁言').then(() => {
      ElMessage.success(`已成功禁言 ${memberIds.length} 位成员`)
    }).catch(() => {})
  } else if (command === 'kick') {
    ElMessageBox.confirm(`确定要批量移除这 ${memberIds.length} 位成员吗？`, '确认移除', { type: 'warning' }).then(() => {
      ElMessage.success(`已成功移除 ${memberIds.length} 位成员`)
      fetchMembers() // 刷新数据
    }).catch(() => {})
  }
}

// 标签管理
const handleRemoveTag = (row, tag) => {
  row.tags.splice(row.tags.indexOf(tag), 1)
  // 调用API更新
}

const showInput = (row) => {
  row.inputVisible = true
}

const handleInputConfirm = (row) => {
  if (row.inputValue) {
    row.tags.push(row.inputValue)
    // 调用API更新
  }
  row.inputVisible = false
  row.inputValue = ''
}

// 辅助方法
const getStatusTagType = (status) => {
  const statusMap = {
    active: 'success',
    left: 'info',
    kicked: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    active: '正常',
    left: '已退出',
    kicked: '被踢出'
  }
  return statusMap[status] || '未知'
}

const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchMembers()
})
</script>

<style lang="scss" scoped>
.group-member-manager {
  .member-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .tag-input {
    width: 90px;
    margin-left: 10px;
    vertical-align: middle;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 24px;
    line-height: 22px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .member-stats {
    margin-bottom: 24px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #64748b;
        }
      }
    }
  }
  
  .member-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left h4 {
      margin: 0;
      color: #1e293b;
    }
    
    .toolbar-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .member-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
  }
  
  .member-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    
    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .member-details {
        .member-name {
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }
        
        .member-phone {
          font-size: 12px;
          color: #64748b;
        }
      }
    }
    
    .pagination-container {
      padding: 16px;
      text-align: center;
      border-top: 1px solid #e2e8f0;
    }
  }
}
</style>