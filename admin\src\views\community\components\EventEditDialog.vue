<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑活动' : '创建新活动'"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="活动名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入活动名称" />
      </el-form-item>

      <el-form-item label="活动类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio-button label="online">线上活动</el-radio-button>
          <el-radio-button label="offline">线下活动</el-radio-button>
          <el-radio-button label="check-in">打卡挑战</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="活动时间" prop="time_range">
        <el-date-picker
          v-model="form.time_range"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 100%;"
          value-format="YYYY-MM-DDTHH:mm:ssZ"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="参与费用" prop="fee">
            <el-input-number v-model="form.fee" :precision="2" :min="0" placeholder="0表示免费" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人数限制" prop="max_participants">
            <el-input-number v-model="form.max_participants" :min="0" placeholder="0表示不限制" style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="活动封面" prop="cover_image">
        <!-- 简单的输入框，实际项目可替换为上传组件 -->
        <el-input v-model="form.cover_image" placeholder="请输入封面图片URL" />
      </el-form-item>

      <el-form-item label="活动详情" prop="description">
        <el-input type="textarea" :rows="4" v-model="form.description" placeholder="请输入详细的活动介绍..." />
      </el-form-item>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: Boolean,
  eventData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

const isEdit = computed(() => !!props.eventData)

const getInitialFormState = () => ({
  name: '',
  type: 'online',
  time_range: [],
  fee: 0,
  max_participants: 0,
  cover_image: '',
  description: '',
})

const form = reactive(getInitialFormState())

const rules = {
  name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  time_range: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
}

watch(() => props.eventData, (newVal) => {
  if (newVal) {
    const data = JSON.parse(JSON.stringify(newVal))
    data.time_range = [data.start_time, data.end_time]
    Object.assign(form, data)
  } else {
    Object.assign(form, getInitialFormState())
  }
})

const handleClose = () => {
  formRef.value.resetFields()
  Object.assign(form, getInitialFormState())
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true
      const submissionData = {
        ...form,
        start_time: form.time_range[0],
        end_time: form.time_range[1],
      }
      delete submissionData.time_range

      setTimeout(() => {
        submitting.value = false
        ElMessage.success(isEdit.value ? '活动更新成功' : '活动创建成功')
        emit('success', submissionData)
        emit('update:modelValue', false)
      }, 500)
    }
  })
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>