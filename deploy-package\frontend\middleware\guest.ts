/**
 * Guest 中间件
 * 用于只允许未登录用户访问的页面（如登录页、注册页）
 * 如果用户已登录，则重定向到仪表板
 */

export default defineNuxtRouteMiddleware((to, from) => {
  const { $router } = useNuxtApp()
  
  // 检查用户是否已登录
  const isAuthenticated = () => {
    // 检查 localStorage 中的用户信息
    if (process.client) {
      const userInfo = localStorage.getItem('userInfo')
      const token = localStorage.getItem('token')
      
      if (userInfo && token) {
        try {
          const user = JSON.parse(userInfo)
          const tokenExpiry = localStorage.getItem('tokenExpiry')
          
          // 检查 token 是否过期
          if (tokenExpiry && new Date().getTime() < parseInt(tokenExpiry)) {
            return true
          }
        } catch (error) {
          // 如果解析失败，清除无效数据
          localStorage.removeItem('userInfo')
          localStorage.removeItem('token')
          localStorage.removeItem('tokenExpiry')
        }
      }
    }
    
    // 检查 cookie 中的认证信息
    const tokenCookie = useCookie('token')
    const userCookie = useCookie('userInfo')
    
    return !!(tokenCookie.value && userCookie.value)
  }
  
  // 如果用户已登录，重定向到仪表板
  if (isAuthenticated()) {
    return navigateTo('/dashboard')
  }
})
