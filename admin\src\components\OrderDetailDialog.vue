<template>
  <el-dialog
    v-model="visible"
    title="订单详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="order-detail">
      <el-descriptions title="订单信息" :column="2" border>
        <el-descriptions-item label="订单号">{{ order.orderNo || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ order.userId || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ order.userName || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">
          <span class="amount">¥{{ order.amount || '0.00' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ order.paymentMethod || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ order.createTime || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ order.updateTime || 'N/A' }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">商品信息</el-divider>
      <el-table :data="order.items || []" style="width: 100%">
        <el-table-column prop="productName" label="商品名称" />
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="price" label="单价" width="100">
          <template #default="scope">
            ¥{{ scope.row.price || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="total" label="小计" width="100">
          <template #default="scope">
            ¥{{ scope.row.total || '0.00' }}
          </template>
        </el-table-column>
      </el-table>

      <div class="order-notes" v-if="order.notes">
        <el-divider content-position="left">订单备注</el-divider>
        <p>{{ order.notes }}</p>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印订单</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'shipped': 'info',
    'delivered': 'success',
    'cancelled': 'danger',
    'refunded': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'shipped': '已发货',
    'delivered': '已送达',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || '未知状态'
}

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

const handlePrint = () => {
  window.print()
}
</script>

<style scoped>
.order-detail {
  padding: 20px 0;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
  font-size: 16px;
}

.order-notes {
  margin-top: 20px;
}

.order-notes p {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 0;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media print {
  .dialog-footer {
    display: none;
  }
}
</style>