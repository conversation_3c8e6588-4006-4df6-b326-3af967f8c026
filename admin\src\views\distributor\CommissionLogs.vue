<template>
  <div class="commission-logs">
    <div class="page-header">
      <div class="header-left">
        <h2>佣金查看</h2>
        <p class="page-description">查看您的佣金收入明细，跟踪收益情况</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="exportCommission">
          <el-icon><Download /></el-icon>
          导出明细
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 佣金统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #409EFF20; color: #409EFF;">
            <el-icon :size="24"><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ formatMoney(stats.total_commission) }}</div>
            <div class="stat-title">累计佣金</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              12.5%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #67C23A20; color: #67C23A;">
            <el-icon :size="24"><Wallet /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ formatMoney(stats.month_commission) }}</div>
            <div class="stat-title">本月佣金</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              8.3%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #E6A23C20; color: #E6A23C;">
            <el-icon :size="24"><CreditCard /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ formatMoney(stats.pending_commission) }}</div>
            <div class="stat-title">待结算</div>
            <div class="stat-trend neutral">
              <el-icon><Minus /></el-icon>
              0%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #F56C6C20; color: #F56C6C;">
            <el-icon :size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.commission_rate }}%</div>
            <div class="stat-title">佣金比例</div>
            <div class="stat-trend neutral">
              <el-icon><Minus /></el-icon>
              0%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 佣金趋势图表 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>佣金趋势</span>
          <div class="chart-controls">
            <el-radio-group v-model="chartPeriod" size="small" @change="loadChartData">
              <el-radio-button value="7d">近7天</el-radio-button>
              <el-radio-button value="30d">近30天</el-radio-button>
              <el-radio-button value="90d">近3个月</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="chart-container" v-loading="chartLoading">
        <LineChart
          v-if="!chartLoading"
          :data="chartData"
          :options="chartOptions"
          height="300px"
        />
      </div>
    </el-card>

    <!-- 佣金明细列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>佣金明细</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索订单号或客户"
              style="width: 200px; margin-right: 10px;"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态" style="width: 120px; margin-right: 10px;" @change="loadCommissions">
              <el-option label="全部" value="" />
              <el-option label="已结算" value="settled" />
              <el-option label="待结算" value="pending" />
              <el-option label="已冻结" value="frozen" />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="loadCommissions"
            />
          </div>
        </div>
      </template>

      <el-table :data="commissions" v-loading="loading" stripe>
        <el-table-column prop="order_no" label="订单号" width="180">
          <template #default="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row.order_no)">
              {{ row.order_no }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="客户信息" width="150">
          <template #default="{ row }">
            <div class="customer-info">
              <div class="customer-name">{{ row.customer_name }}</div>
              <div class="customer-level">{{ row.customer_level }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="product_name" label="产品名称" width="200" />
        <el-table-column label="订单金额" width="120">
          <template #default="{ row }">
            <span class="order-amount">¥{{ formatMoney(row.order_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="佣金金额" width="120">
          <template #default="{ row }">
            <span class="commission-amount">¥{{ formatMoney(row.commission_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="佣金比例" width="100">
          <template #default="{ row }">
            <span class="commission-rate">{{ row.commission_rate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="结算时间" width="120">
          <template #default="{ row }">
            <span v-if="row.settled_at">{{ formatDate(row.settled_at) }}</span>
            <span v-else class="pending-text">待结算</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              详情
            </el-button>
            <el-button 
              v-if="row.status === 'pending'" 
              size="small" 
              type="primary" 
              @click="requestSettle(row)"
            >
              申请结算
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadCommissions"
          @current-change="loadCommissions"
        />
      </div>
    </el-card>

    <!-- 佣金详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="佣金详情" width="600px">
      <div v-if="selectedCommission" class="commission-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedCommission.order_no }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ selectedCommission.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ selectedCommission.product_name }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ formatMoney(selectedCommission.order_amount) }}</el-descriptions-item>
          <el-descriptions-item label="佣金金额">¥{{ formatMoney(selectedCommission.commission_amount) }}</el-descriptions-item>
          <el-descriptions-item label="佣金比例">{{ selectedCommission.commission_rate }}%</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(selectedCommission.status)">
              {{ getStatusText(selectedCommission.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedCommission.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="结算时间" v-if="selectedCommission.settled_at">
            {{ formatDateTime(selectedCommission.settled_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" v-if="selectedCommission.remark">
            {{ selectedCommission.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 功能开发提示 -->
    <el-dialog v-model="showDevDialog" title="功能开发中" width="400px" center>
      <div class="dev-notice">
        <el-icon :size="60" color="#409EFF"><Tools /></el-icon>
        <h3>功能开发中</h3>
        <p>该功能正在紧急开发中，敬请期待！</p>
        <p>预计上线时间：2024年1月</p>
      </div>
      <template #footer>
        <el-button type="primary" @click="showDevDialog = false">知道了</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { 
  Download, Refresh, Money, Wallet, CreditCard, TrendCharts,
  ArrowUp, Minus, Search, Tools
} from '@element-plus/icons-vue'
import LineChart from '@/components/Charts/LineChart.vue'

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const showDetailDialog = ref(false)
const showDevDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const chartPeriod = ref('30d')
const selectedCommission = ref(null)

const stats = reactive({
  total_commission: 28650.50,
  month_commission: 8650.50,
  pending_commission: 1250.00,
  commission_rate: 15.0
})

const commissions = ref([])
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

const chartData = ref({
  labels: [],
  datasets: [{
    label: '佣金收入',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4,
    fill: true
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      display: true,
      position: 'top'
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      callbacks: {
        label: function(context) {
          return `佣金收入: ¥${context.parsed.y.toFixed(2)}`
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      },
      ticks: {
        callback: function(value) {
          return '¥' + value
        }
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
}

// 模拟数据
const mockCommissions = [
  {
    id: 1,
    order_no: 'ORD202401001',
    customer_name: '张三',
    customer_level: 'A级客户',
    product_name: 'VIP群组套餐',
    order_amount: 2999.00,
    commission_amount: 449.85,
    commission_rate: 15.0,
    status: 'settled',
    created_at: new Date(Date.now() - 86400000 * 5),
    settled_at: new Date(Date.now() - 86400000 * 2),
    remark: '正常结算'
  },
  {
    id: 2,
    order_no: 'ORD202401002',
    customer_name: '李四',
    customer_level: 'B级客户',
    product_name: '标准群组套餐',
    order_amount: 1999.00,
    commission_amount: 199.90,
    commission_rate: 10.0,
    status: 'pending',
    created_at: new Date(Date.now() - 86400000 * 3),
    settled_at: null,
    remark: ''
  },
  {
    id: 3,
    order_no: 'ORD202401003',
    customer_name: '王五',
    customer_level: 'A级客户',
    product_name: '企业群组套餐',
    order_amount: 4999.00,
    commission_amount: 749.85,
    commission_rate: 15.0,
    status: 'settled',
    created_at: new Date(Date.now() - 86400000 * 8),
    settled_at: new Date(Date.now() - 86400000 * 5),
    remark: '正常结算'
  }
]

// 方法
const loadCommissions = async () => {
  try {
    loading.value = true
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredCommissions = [...mockCommissions]
    
    // 搜索过滤
    if (searchKeyword.value) {
      filteredCommissions = filteredCommissions.filter(commission => 
        commission.order_no.includes(searchKeyword.value) ||
        commission.customer_name.includes(searchKeyword.value) ||
        commission.product_name.includes(searchKeyword.value)
      )
    }
    
    // 状态过滤
    if (statusFilter.value) {
      filteredCommissions = filteredCommissions.filter(commission => commission.status === statusFilter.value)
    }
    
    // 日期过滤
    if (dateRange.value && dateRange.value.length === 2) {
      const startDate = new Date(dateRange.value[0])
      const endDate = new Date(dateRange.value[1])
      filteredCommissions = filteredCommissions.filter(commission => {
        const commissionDate = new Date(commission.created_at)
        return commissionDate >= startDate && commissionDate <= endDate
      })
    }
    
    commissions.value = filteredCommissions
    pagination.total = filteredCommissions.length
    
  } catch (error) {
    ElMessage.error('加载佣金数据失败')
  } finally {
    loading.value = false
  }
}

const loadChartData = async () => {
  chartLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockChartData = {
      '7d': {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [120.50, 190.80, 300.20, 500.60, 200.30, 300.90, 450.70]
      },
      '30d': {
        labels: Array.from({length: 30}, (_, i) => `${i + 1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 500) + 100)
      },
      '90d': {
        labels: ['第1月', '第2月', '第3月'],
        data: [8000.50, 12000.80, 15000.20]
      }
    }
    
    const data = mockChartData[chartPeriod.value]
    chartData.value = {
      labels: data.labels,
      datasets: [{
        label: '佣金收入',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4,
        fill: true,
        pointBackgroundColor: '#409EFF',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      }]
    }
    
  } catch (error) {
    ElMessage.error('加载图表数据失败')
  } finally {
    chartLoading.value = false
  }
}

const refreshData = () => {
  loadCommissions()
  loadChartData()
  ElMessage.success('数据已刷新')
}

const handleSearch = debounce(() => {
  pagination.current_page = 1
  loadCommissions()
}, 500)

const viewDetail = (commission) => {
  selectedCommission.value = commission
  showDetailDialog.value = true
}

const viewOrderDetail = (orderNo) => {
  ElMessage.info(`查看订单 ${orderNo} 详情`)
  showDevDialog.value = true
}

const requestSettle = async (commission) => {
  try {
    await ElMessageBox.confirm(`确定要申请结算订单 ${commission.order_no} 的佣金吗？`, '确认申请', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('结算申请已提交，请等待审核')
    showDevDialog.value = true
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('申请失败')
    }
  }
}

const exportCommission = () => {
  ElMessage.info('导出佣金明细功能开发中...')
  showDevDialog.value = true
}

// 工具方法
const getStatusColor = (status) => {
  const colors = {
    'settled': 'success',
    'pending': 'warning',
    'frozen': 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'settled': '已结算',
    'pending': '待结算',
    'frozen': '已冻结'
  }
  return texts[status] || '未知'
}

const formatMoney = (amount) => {
  return Number(amount).toFixed(2)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadCommissions()
  loadChartData()
})
</script>

<style scoped>
.commission-logs {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-description {
  color: #909399;
  font-size: 14px;
  margin: 5px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  gap: 4px;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.stat-trend.neutral {
  color: #909399;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-name {
  font-weight: bold;
  color: #303133;
}

.customer-level {
  font-size: 12px;
  color: #909399;
}

.order-amount {
  color: #606266;
  font-weight: bold;
}

.commission-amount {
  color: #67C23A;
  font-weight: bold;
}

.commission-rate {
  color: #409EFF;
  font-weight: bold;
}

.pending-text {
  color: #E6A23C;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.commission-detail {
  padding: 20px 0;
}

.dev-notice {
  text-align: center;
  padding: 20px;
}

.dev-notice h3 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.dev-notice p {
  color: #606266;
  margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .commission-logs {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-row .el-col {
    margin-bottom: 15px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>