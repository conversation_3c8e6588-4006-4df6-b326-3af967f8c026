import{n as e,_ as l}from"./index-DtXAftX0.js";/* empty css               *//* empty css                        *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                *//* empty css                     *//* empty css                */import{r as a,L as t,e as u,k as d,l as o,E as n,z as i,t as s,A as r,D as _,y as p,u as m,B as c,F as f,G as v}from"./vue-vendor-Dy164gUc.js";import{aM as y,by as b,b9 as h,b8 as V,at as g,bh as w,bi as k,U,a$ as x,bw as C,bx as j,aY as I,ay as z,bp as S,bq as T,bs as P,cs as D,br as q,Q as R,R as $,aU as A,a_ as B,aE as F,S as N,T as L,a0 as M,af as Z,W as G,ae as O,ak as W,am as E,aq as H,c3 as J,bt as Q,bu as Y,aZ as K}from"./element-plus-h2SQQM64.js";import{I as X}from"./ImageUpload-B-U8MD1C.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                       */import{f as ee}from"./format-3eU4VJ9V.js";/* empty css                       */import"./utils-D1VZuEZr.js";/* empty css                  *//* empty css                    */const le={getSystemSettings:()=>e({url:"/admin/system/settings",method:"get"}),updateSystemSettings:l=>e({url:"/admin/system/settings",method:"put",data:l}),testPaymentConfig:(l,a)=>e({url:"/admin/system/payment/test",method:"post",data:{provider:l,config:a}})},ae=le.getSystemSettings,te=le.updateSystemSettings,ue=le.testPaymentConfig,de={class:"audit-logs-container"},oe={class:"filter-container"},ne={class:"pagination-container"},ie={key:0},se={key:0},re={class:"code-block"},_e=l({__name:"AuditLogs",setup(e){const l=a(!1),f=a([]),v=t({user:"",dateRange:[],actionType:""}),S=t({page:1,limit:10,total:0}),T=a(!1),P=a(null),D=Array.from({length:50}).map((e,l)=>{const a=[{id:1,name:"Admin"},{id:2,name:"Operator"}],t=["create","update","delete","login"][l%4];return{id:l+1,timestamp:new Date(Date.now()-3600*l*1e3).toISOString(),user:a[l%2],ip_address:`192.168.1.${l+1}`,action_type:t,description:`${a[l%2].name} ${t}了文章 '文章标题${l+1}'`,user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...",data_changes:"login"!==t?{before:{status:"draft"},after:{status:"published"}}:null}}),q=()=>{l.value=!0,setTimeout(()=>{S.total=D.length;const e=(S.page-1)*S.limit,a=e+S.limit;f.value=D.slice(e,a),l.value=!1},500)};return u(()=>{q()}),(e,a)=>{const t=y,u=b,D=V,R=h,$=g,A=k,B=x,F=w,N=j,L=I,M=z,Z=C;return o(),d("div",de,[n(L,null,{header:i(()=>a[6]||(a[6]=[s("h3",null,"系统日志",-1)])),default:i(()=>[s("div",oe,[n(t,{modelValue:v.user,"onUpdate:modelValue":a[0]||(a[0]=e=>v.user=e),placeholder:"操作人",style:{width:"180px"},clearable:""},null,8,["modelValue"]),n(u,{modelValue:v.dateRange,"onUpdate:modelValue":a[1]||(a[1]=e=>v.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px","margin-left":"10px"}},null,8,["modelValue"]),n(R,{modelValue:v.actionType,"onUpdate:modelValue":a[2]||(a[2]=e=>v.actionType=e),placeholder:"操作类型",style:{width:"150px","margin-left":"10px"},clearable:""},{default:i(()=>[n(D,{label:"创建",value:"create"}),n(D,{label:"更新",value:"update"}),n(D,{label:"删除",value:"delete"}),n(D,{label:"登录",value:"login"})]),_:1},8,["modelValue"]),n($,{type:"primary",icon:"Search",onClick:q,style:{"margin-left":"10px"}},{default:i(()=>a[7]||(a[7]=[_("搜索",-1)])),_:1,__:[7]})]),r((o(),p(F,{data:f.value,style:{width:"100%","margin-top":"20px"}},{default:i(()=>[n(A,{prop:"timestamp",label:"操作时间",width:"180"},{default:i(({row:e})=>[_(U(m(ee)(e.timestamp)),1)]),_:1}),n(A,{prop:"user.name",label:"操作人",width:"120"}),n(A,{prop:"ip_address",label:"IP地址",width:"150"}),n(A,{label:"操作类型",width:"100"},{default:i(({row:e})=>{return[n(B,{type:(l=e.action_type,{create:"success",update:"primary",delete:"danger",login:"info"}[l]||"default")},{default:i(()=>[_(U(e.action_type.toUpperCase()),1)]),_:2},1032,["type"])];var l}),_:1}),n(A,{prop:"description",label:"操作描述"}),n(A,{label:"操作",width:"100"},{default:i(({row:e})=>[n($,{link:"",type:"primary",size:"small",onClick:l=>{return a=e,P.value=a,void(T.value=!0);var a}},{default:i(()=>a[8]||(a[8]=[_("详情",-1)])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,l.value]]),s("div",ne,[n(N,{"current-page":S.page,"onUpdate:currentPage":a[3]||(a[3]=e=>S.page=e),"page-size":S.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>S.limit=e),total:S.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:q},null,8,["current-page","page-size","total"])])]),_:1}),n(M,{modelValue:T.value,"onUpdate:modelValue":a[5]||(a[5]=e=>T.value=e),title:"日志详情",width:"600px"},{default:i(()=>[P.value?(o(),d("div",ie,[s("p",null,[a[9]||(a[9]=s("strong",null,"操作描述:",-1)),_(" "+U(P.value.description),1)]),s("p",null,[a[10]||(a[10]=s("strong",null,"操作人:",-1)),_(" "+U(P.value.user.name)+" (ID: "+U(P.value.user.id)+")",1)]),s("p",null,[a[11]||(a[11]=s("strong",null,"时间:",-1)),_(" "+U(m(ee)(P.value.timestamp)),1)]),s("p",null,[a[12]||(a[12]=s("strong",null,"IP 地址:",-1)),_(" "+U(P.value.ip_address),1)]),s("p",null,[a[13]||(a[13]=s("strong",null,"User Agent:",-1)),_(" "+U(P.value.user_agent),1)]),P.value.data_changes?(o(),d("div",se,[a[14]||(a[14]=s("h4",null,"数据变更:",-1)),s("pre",re,U(JSON.stringify(P.value.data_changes,null,2)),1)])):c("",!0)])):c("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-db8d149e"]]),pe={class:"backup-settings-container"},me={class:"card-header"},ce=l({__name:"BackupSettings",setup(e){const l=a(!1),v=a(!1),y=a([]),b=t({enabled:!0,frequency:"daily",time:new Date(2023,1,1,2,0,0),retention:7}),j=()=>{l.value=!0,setTimeout(()=>{y.value=[{id:1,timestamp:"2024-06-01T02:00:00Z",filename:"backup-202406010200.zip",size:"128 MB",type:"db"},{id:2,timestamp:"2024-05-31T02:00:00Z",filename:"backup-202405310200.zip",size:"127 MB",type:"db"},{id:3,timestamp:"2024-05-30T15:30:00Z",filename:"manual-backup-202405301530.zip",size:"2.5 GB",type:"files"}],l.value=!1},500)},z=()=>{R.success("自动备份策略已保存")},A=()=>{v.value=!0,R.info("正在创建手动备份，请稍候..."),setTimeout(()=>{v.value=!1,R.success("手动备份创建成功"),j()},3e3)};return u(()=>{j()}),(e,a)=>{const t=P,u=T,j=V,B=h,F=D,N=q,L=g,M=S,Z=I,G=k,O=x,W=w,E=C;return o(),d("div",pe,[n(Z,null,{header:i(()=>a[4]||(a[4]=[s("h3",null,"备份与恢复",-1)])),default:i(()=>[n(Z,{shadow:"never",style:{"margin-bottom":"20px"}},{header:i(()=>a[5]||(a[5]=[s("h4",null,"自动备份策略",-1)])),default:i(()=>[n(M,{model:b,"label-width":"120px"},{default:i(()=>[n(u,{label:"启用自动备份"},{default:i(()=>[n(t,{modelValue:b.enabled,"onUpdate:modelValue":a[0]||(a[0]=e=>b.enabled=e)},null,8,["modelValue"])]),_:1}),b.enabled?(o(),d(f,{key:0},[n(u,{label:"备份频率"},{default:i(()=>[n(B,{modelValue:b.frequency,"onUpdate:modelValue":a[1]||(a[1]=e=>b.frequency=e),placeholder:"请选择频率"},{default:i(()=>[n(j,{label:"每天",value:"daily"}),n(j,{label:"每周",value:"weekly"})]),_:1},8,["modelValue"])]),_:1}),n(u,{label:"备份时间"},{default:i(()=>[n(F,{modelValue:b.time,"onUpdate:modelValue":a[2]||(a[2]=e=>b.time=e),format:"HH:mm",placeholder:"选择备份时间"},null,8,["modelValue"])]),_:1}),n(u,{label:"保留备份数量"},{default:i(()=>[n(N,{modelValue:b.retention,"onUpdate:modelValue":a[3]||(a[3]=e=>b.retention=e),min:1,max:30},null,8,["modelValue"]),a[6]||(a[6]=s("span",null," 个",-1))]),_:1,__:[6]})],64)):c("",!0),n(u,null,{default:i(()=>[n(L,{type:"primary",onClick:z},{default:i(()=>a[7]||(a[7]=[_("保存策略",-1)])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1}),n(Z,{shadow:"never"},{header:i(()=>[s("div",me,[a[9]||(a[9]=s("h4",null,"备份历史",-1)),n(L,{type:"primary",icon:"Plus",onClick:A,loading:v.value},{default:i(()=>a[8]||(a[8]=[_(" 创建手动备份 ",-1)])),_:1,__:[8]},8,["loading"])])]),default:i(()=>[r((o(),p(W,{data:y.value,style:{width:"100%"}},{default:i(()=>[n(G,{prop:"timestamp",label:"备份时间",width:"180"},{default:i(({row:e})=>[_(U(m(ee)(e.timestamp)),1)]),_:1}),n(G,{prop:"filename",label:"文件名"}),n(G,{prop:"size",label:"文件大小",width:"120"}),n(G,{label:"类型",width:"100"},{default:i(({row:e})=>[n(O,{type:"db"===e.type?"success":"primary"},{default:i(()=>[_(U("db"===e.type?"数据库":"文件"),1)]),_:2},1032,["type"])]),_:1}),n(G,{label:"操作",width:"200",fixed:"right"},{default:i(({row:e})=>[n(L,{link:"",type:"primary",icon:"RefreshLeft",size:"small",onClick:l=>{return a=e,void $.confirm(`确定要从备份文件 "${a.filename}" 恢复吗？此操作不可逆，将覆盖现有数据！`,"严重警告",{type:"warning",confirmButtonText:"我确定要恢复"}).then(()=>{R.info("正在执行恢复操作...")}).catch(()=>{});var a}},{default:i(()=>a[10]||(a[10]=[_("恢复",-1)])),_:2,__:[10]},1032,["onClick"]),n(L,{link:"",type:"success",icon:"Download",size:"small",onClick:l=>{return a=e,void R.success(`开始下载备份文件: ${a.filename}`);var a}},{default:i(()=>a[11]||(a[11]=[_("下载",-1)])),_:2,__:[11]},1032,["onClick"]),n(L,{link:"",type:"danger",icon:"Delete",size:"small",onClick:l=>{return a=e,void $.confirm(`确定要删除备份文件 "${a.filename}" 吗？`,"确认删除",{type:"warning"}).then(()=>{y.value=y.value.filter(e=>e.id!==a.id),R.success("备份文件删除成功")}).catch(()=>{});var a}},{default:i(()=>a[12]||(a[12]=[_("删除",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,l.value]])]),_:1})]),_:1})])}}},[["__scopeId","data-v-b5d48ffa"]]),fe={class:"community-settings-container"},ve=l({__name:"CommunitySettings",setup(e){const l=a(!1),r=a(!1),p=t({default_welcome_message:"欢迎 {memberName} 加入 {groupName}！",default_rules:["rule_2"],moderation_enabled:!0,sensitive_words:"广告\n推广\n加V\n私聊",paid_groups_enabled:!0,events_enabled:!0,checkin_enabled:!1}),m=()=>{r.value=!0,setTimeout(()=>{R.success("社群模块设置已保存"),r.value=!1},1e3)};return u(()=>{l.value=!0,setTimeout(()=>{l.value=!1},500)}),(e,l)=>{const a=A,t=y,u=T,c=V,f=h,v=P,b=g,w=S,k=I;return o(),d("div",fe,[n(k,null,{header:i(()=>l[7]||(l[7]=[s("h3",null,"社群模块设置",-1)])),default:i(()=>[n(w,{model:p,"label-width":"150px"},{default:i(()=>[n(a,{"content-position":"left"},{default:i(()=>l[8]||(l[8]=[_("默认设置",-1)])),_:1,__:[8]}),n(u,{label:"新成员默认欢迎语"},{default:i(()=>[n(t,{type:"textarea",rows:3,modelValue:p.default_welcome_message,"onUpdate:modelValue":l[0]||(l[0]=e=>p.default_welcome_message=e),placeholder:"可用 {memberName} 指代新成员昵称，{groupName} 指代群组名称"},null,8,["modelValue"])]),_:1}),n(u,{label:"新群组默认规则"},{default:i(()=>[n(f,{modelValue:p.default_rules,"onUpdate:modelValue":l[1]||(l[1]=e=>p.default_rules=e),multiple:"",placeholder:"选择默认应用的自动化规则",style:{width:"100%"}},{default:i(()=>[n(c,{label:"回复“入群”关键词",value:"rule_1"}),n(c,{label:"禁止发送广告链接",value:"rule_2"})]),_:1},8,["modelValue"])]),_:1}),n(a,{"content-position":"left"},{default:i(()=>l[9]||(l[9]=[_("内容审核",-1)])),_:1,__:[9]}),n(u,{label:"新内容先审后发"},{default:i(()=>[n(v,{modelValue:p.moderation_enabled,"onUpdate:modelValue":l[2]||(l[2]=e=>p.moderation_enabled=e)},null,8,["modelValue"]),l[10]||(l[10]=s("div",{class:"form-item-help"},"开启后，所有用户发布的新帖子和评论都需要经过管理员审核才能显示。",-1))]),_:1,__:[10]}),n(u,{label:"敏感词词库"},{default:i(()=>[n(t,{type:"textarea",rows:5,modelValue:p.sensitive_words,"onUpdate:modelValue":l[3]||(l[3]=e=>p.sensitive_words=e),placeholder:"每行一个敏感词"},null,8,["modelValue"]),l[11]||(l[11]=s("div",{class:"form-item-help"},"当用户发布的内容包含这些词汇时，将自动进入审核队列。",-1))]),_:1,__:[11]}),n(a,{"content-position":"left"},{default:i(()=>l[12]||(l[12]=[_("功能开关",-1)])),_:1,__:[12]}),n(u,{label:"允许创建付费群组"},{default:i(()=>[n(v,{modelValue:p.paid_groups_enabled,"onUpdate:modelValue":l[4]||(l[4]=e=>p.paid_groups_enabled=e)},null,8,["modelValue"])]),_:1}),n(u,{label:"开启活动功能"},{default:i(()=>[n(v,{modelValue:p.events_enabled,"onUpdate:modelValue":l[5]||(l[5]=e=>p.events_enabled=e)},null,8,["modelValue"])]),_:1}),n(u,{label:"开启打卡功能"},{default:i(()=>[n(v,{modelValue:p.checkin_enabled,"onUpdate:modelValue":l[6]||(l[6]=e=>p.checkin_enabled=e)},null,8,["modelValue"])]),_:1}),n(u,null,{default:i(()=>[n(b,{type:"primary",onClick:m,loading:r.value},{default:i(()=>l[13]||(l[13]=[_("保存设置",-1)])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])}}},[["__scopeId","data-v-fff54f81"]]),ye={class:"app-container"},be={style:{"font-size":"14px",color:"#666",margin:"8px 0 0 0"}},he=l({__name:"Settings",setup(e){const l=a("basic"),t=a(),b=a(),h=a(),V=a(),w=a({site_name:"",site_description:"",site_keywords:"",site_logo:"",site_favicon:"",contact_email:"",contact_phone:"",icp_number:""}),k=a({wechat_enabled:!1,wechat_app_id:"",wechat_mch_id:"",wechat_key:"",alipay_enabled:!1,alipay_app_id:"",alipay_private_key:"",alipay_public_key:""}),U=a({email_enabled:!0,sms_enabled:!1,wechat_enabled:!1,system_enabled:!0,user_register:["system"],new_order:["email","system"],withdrawal_request:["email","system"]}),x=a({login_captcha:!0,register_captcha:!0,password_strength:!0,login_attempts:5,session_timeout:120,ip_whitelist:""}),C={site_name:[{required:!0,message:"请输入网站名称",trigger:"blur"}],contact_email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}]},j=e=>{l.value=e},z=async()=>{try{await t.value.validate(),await te("basic",w.value),R.success("基础设置保存成功")}catch(e){console.error("保存基础设置失败:",e),R.error("保存基础设置失败")}},D=()=>{t.value.resetFields()},$=async()=>{try{await te("payment",k.value),R.success("支付设置保存成功")}catch(e){console.error("保存支付设置失败:",e),R.error("保存支付设置失败")}},ee=async()=>{try{await ue(k.value),R.success("支付配置测试通过")}catch(e){console.error("支付配置测试失败:",e),R.error("支付配置测试失败")}},le=async()=>{try{await te("notification",U.value),R.success("通知设置保存成功")}catch(e){console.error("保存通知设置失败:",e),R.error("保存通知设置失败")}},de=async()=>{try{await te("security",x.value),R.success("安全设置保存成功")}catch(e){console.error("保存安全设置失败:",e),R.error("保存安全设置失败")}};return u(()=>{(async()=>{try{const{data:e}=await ae();w.value={...w.value,...e.basic},k.value={...k.value,...e.payment},U.value={...U.value,...e.notification},x.value={...x.value,...e.security}}catch(e){console.error("获取系统设置失败:",e),R.error("获取系统设置失败")}})()}),(e,a)=>{const u=L,R=N,ae=G,te=F,ue=I,oe=B,ne=y,ie=T,se=g,re=S,pe=J,me=A,fe=P,he=Y,Ve=Q,ge=q,we=K;return o(),d("div",ye,[n(we,{gutter:20},{default:i(()=>[n(oe,{span:6},{default:i(()=>[n(ue,null,{header:i(()=>a[30]||(a[30]=[s("h3",null,"系统设置",-1)])),default:i(()=>[n(te,{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),class:"settings-menu",onSelect:j},{default:i(()=>[n(R,{index:"basic"},{default:i(()=>[n(u,null,{default:i(()=>[n(m(M))]),_:1}),a[31]||(a[31]=s("span",null,"基础设置",-1))]),_:1,__:[31]}),n(R,{index:"security"},{default:i(()=>[n(u,null,{default:i(()=>[n(m(Z))]),_:1}),a[32]||(a[32]=s("span",null,"安全设置",-1))]),_:1,__:[32]}),n(ae,{index:"integrations"},{title:i(()=>[n(u,null,{default:i(()=>[n(m(O))]),_:1}),a[33]||(a[33]=s("span",null,"集成设置",-1))]),default:i(()=>[n(R,{index:"payment"},{default:i(()=>a[34]||(a[34]=[_("支付快速配置",-1)])),_:1,__:[34]}),n(R,{index:"notification"},{default:i(()=>a[35]||(a[35]=[_("通知设置",-1)])),_:1,__:[35]}),n(R,{index:"storage"},{default:i(()=>a[36]||(a[36]=[_("存储设置",-1)])),_:1,__:[36]})]),_:1}),n(ae,{index:"modules"},{title:i(()=>[n(u,null,{default:i(()=>[n(m(W))]),_:1}),a[37]||(a[37]=s("span",null,"模块设置",-1))]),default:i(()=>[n(R,{index:"community_settings"},{default:i(()=>a[38]||(a[38]=[_("社群设置",-1)])),_:1,__:[38]})]),_:1}),n(R,{index:"audit_logs"},{default:i(()=>[n(u,null,{default:i(()=>[n(m(E))]),_:1}),a[39]||(a[39]=s("span",null,"系统日志",-1))]),_:1,__:[39]}),n(R,{index:"backup"},{default:i(()=>[n(u,null,{default:i(()=>[n(m(H))]),_:1}),a[40]||(a[40]=s("span",null,"备份与恢复",-1))]),_:1,__:[40]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),n(oe,{span:18},{default:i(()=>[r(n(ue,null,{header:i(()=>a[41]||(a[41]=[s("h3",null,"基础设置",-1)])),default:i(()=>[n(re,{ref_key:"basicFormRef",ref:t,model:w.value,rules:C,"label-width":"120px"},{default:i(()=>[n(ie,{label:"网站名称",prop:"site_name"},{default:i(()=>[n(ne,{modelValue:w.value.site_name,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value.site_name=e),placeholder:"请输入网站名称"},null,8,["modelValue"])]),_:1}),n(ie,{label:"网站描述",prop:"site_description"},{default:i(()=>[n(ne,{modelValue:w.value.site_description,"onUpdate:modelValue":a[2]||(a[2]=e=>w.value.site_description=e),type:"textarea",rows:3,placeholder:"请输入网站描述"},null,8,["modelValue"])]),_:1}),n(ie,{label:"网站关键词",prop:"site_keywords"},{default:i(()=>[n(ne,{modelValue:w.value.site_keywords,"onUpdate:modelValue":a[3]||(a[3]=e=>w.value.site_keywords=e),placeholder:"请输入网站关键词，用逗号分隔"},null,8,["modelValue"])]),_:1}),n(ie,{label:"网站Logo",prop:"site_logo"},{default:i(()=>[n(X,{modelValue:w.value.site_logo,"onUpdate:modelValue":a[4]||(a[4]=e=>w.value.site_logo=e),limit:1},null,8,["modelValue"])]),_:1}),n(ie,{label:"网站图标",prop:"site_favicon"},{default:i(()=>[n(X,{modelValue:w.value.site_favicon,"onUpdate:modelValue":a[5]||(a[5]=e=>w.value.site_favicon=e),limit:1},null,8,["modelValue"])]),_:1}),n(ie,{label:"联系邮箱",prop:"contact_email"},{default:i(()=>[n(ne,{modelValue:w.value.contact_email,"onUpdate:modelValue":a[6]||(a[6]=e=>w.value.contact_email=e),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),n(ie,{label:"联系电话",prop:"contact_phone"},{default:i(()=>[n(ne,{modelValue:w.value.contact_phone,"onUpdate:modelValue":a[7]||(a[7]=e=>w.value.contact_phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),n(ie,{label:"备案号",prop:"icp_number"},{default:i(()=>[n(ne,{modelValue:w.value.icp_number,"onUpdate:modelValue":a[8]||(a[8]=e=>w.value.icp_number=e),placeholder:"请输入备案号"},null,8,["modelValue"])]),_:1}),n(ie,null,{default:i(()=>[n(se,{type:"primary",onClick:z},{default:i(()=>a[42]||(a[42]=[_("保存设置",-1)])),_:1,__:[42]}),n(se,{onClick:D},{default:i(()=>a[43]||(a[43]=[_("重置",-1)])),_:1,__:[43]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[v,"basic"===l.value]]),r(n(ue,null,{header:i(()=>[a[46]||(a[46]=s("h3",null,"支付快速配置",-1)),s("p",be,[a[45]||(a[45]=_("快速启用和配置基本支付功能，详细配置请前往 ",-1)),n(pe,{type:"primary",href:"/payment",target:"_blank"},{default:i(()=>a[44]||(a[44]=[_("支付管理",-1)])),_:1,__:[44]})])]),default:i(()=>[n(re,{ref_key:"paymentFormRef",ref:b,model:k.value,"label-width":"120px"},{default:i(()=>[n(me,{"content-position":"left"},{default:i(()=>a[47]||(a[47]=[_("微信支付",-1)])),_:1,__:[47]}),n(ie,{label:"启用微信支付"},{default:i(()=>[n(fe,{modelValue:k.value.wechat_enabled,"onUpdate:modelValue":a[9]||(a[9]=e=>k.value.wechat_enabled=e)},null,8,["modelValue"])]),_:1}),k.value.wechat_enabled?(o(),d(f,{key:0},[n(ie,{label:"应用ID",prop:"wechat_app_id"},{default:i(()=>[n(ne,{modelValue:k.value.wechat_app_id,"onUpdate:modelValue":a[10]||(a[10]=e=>k.value.wechat_app_id=e),placeholder:"请输入微信应用ID"},null,8,["modelValue"])]),_:1}),n(ie,{label:"商户号",prop:"wechat_mch_id"},{default:i(()=>[n(ne,{modelValue:k.value.wechat_mch_id,"onUpdate:modelValue":a[11]||(a[11]=e=>k.value.wechat_mch_id=e),placeholder:"请输入微信商户号"},null,8,["modelValue"])]),_:1}),n(ie,{label:"API密钥",prop:"wechat_key"},{default:i(()=>[n(ne,{modelValue:k.value.wechat_key,"onUpdate:modelValue":a[12]||(a[12]=e=>k.value.wechat_key=e),type:"password",placeholder:"请输入微信API密钥","show-password":""},null,8,["modelValue"])]),_:1})],64)):c("",!0),n(me,{"content-position":"left"},{default:i(()=>a[48]||(a[48]=[_("支付宝支付",-1)])),_:1,__:[48]}),n(ie,{label:"启用支付宝"},{default:i(()=>[n(fe,{modelValue:k.value.alipay_enabled,"onUpdate:modelValue":a[13]||(a[13]=e=>k.value.alipay_enabled=e)},null,8,["modelValue"])]),_:1}),k.value.alipay_enabled?(o(),d(f,{key:1},[n(ie,{label:"应用ID",prop:"alipay_app_id"},{default:i(()=>[n(ne,{modelValue:k.value.alipay_app_id,"onUpdate:modelValue":a[14]||(a[14]=e=>k.value.alipay_app_id=e),placeholder:"请输入支付宝应用ID"},null,8,["modelValue"])]),_:1}),n(ie,{label:"应用私钥",prop:"alipay_private_key"},{default:i(()=>[n(ne,{modelValue:k.value.alipay_private_key,"onUpdate:modelValue":a[15]||(a[15]=e=>k.value.alipay_private_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝应用私钥"},null,8,["modelValue"])]),_:1}),n(ie,{label:"支付宝公钥",prop:"alipay_public_key"},{default:i(()=>[n(ne,{modelValue:k.value.alipay_public_key,"onUpdate:modelValue":a[16]||(a[16]=e=>k.value.alipay_public_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1})],64)):c("",!0),n(ie,null,{default:i(()=>[n(se,{type:"primary",onClick:$},{default:i(()=>a[49]||(a[49]=[_("保存设置",-1)])),_:1,__:[49]}),n(se,{onClick:ee},{default:i(()=>a[50]||(a[50]=[_("测试支付",-1)])),_:1,__:[50]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[v,"payment"===l.value]]),r(n(ue,null,{header:i(()=>a[51]||(a[51]=[s("h3",null,"通知设置",-1)])),default:i(()=>[n(re,{ref_key:"notificationFormRef",ref:h,model:U.value,"label-width":"120px"},{default:i(()=>[n(ie,{label:"邮件通知"},{default:i(()=>[n(fe,{modelValue:U.value.email_enabled,"onUpdate:modelValue":a[17]||(a[17]=e=>U.value.email_enabled=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"短信通知"},{default:i(()=>[n(fe,{modelValue:U.value.sms_enabled,"onUpdate:modelValue":a[18]||(a[18]=e=>U.value.sms_enabled=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"微信通知"},{default:i(()=>[n(fe,{modelValue:U.value.wechat_enabled,"onUpdate:modelValue":a[19]||(a[19]=e=>U.value.wechat_enabled=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"系统通知"},{default:i(()=>[n(fe,{modelValue:U.value.system_enabled,"onUpdate:modelValue":a[20]||(a[20]=e=>U.value.system_enabled=e)},null,8,["modelValue"])]),_:1}),n(me,{"content-position":"left"},{default:i(()=>a[52]||(a[52]=[_("通知事件",-1)])),_:1,__:[52]}),n(ie,{label:"新用户注册"},{default:i(()=>[n(Ve,{modelValue:U.value.user_register,"onUpdate:modelValue":a[21]||(a[21]=e=>U.value.user_register=e)},{default:i(()=>[n(he,{label:"email"},{default:i(()=>a[53]||(a[53]=[_("邮件",-1)])),_:1,__:[53]}),n(he,{label:"sms"},{default:i(()=>a[54]||(a[54]=[_("短信",-1)])),_:1,__:[54]}),n(he,{label:"system"},{default:i(()=>a[55]||(a[55]=[_("系统",-1)])),_:1,__:[55]})]),_:1},8,["modelValue"])]),_:1}),n(ie,{label:"新订单"},{default:i(()=>[n(Ve,{modelValue:U.value.new_order,"onUpdate:modelValue":a[22]||(a[22]=e=>U.value.new_order=e)},{default:i(()=>[n(he,{label:"email"},{default:i(()=>a[56]||(a[56]=[_("邮件",-1)])),_:1,__:[56]}),n(he,{label:"sms"},{default:i(()=>a[57]||(a[57]=[_("短信",-1)])),_:1,__:[57]}),n(he,{label:"system"},{default:i(()=>a[58]||(a[58]=[_("系统",-1)])),_:1,__:[58]})]),_:1},8,["modelValue"])]),_:1}),n(ie,{label:"提现申请"},{default:i(()=>[n(Ve,{modelValue:U.value.withdrawal_request,"onUpdate:modelValue":a[23]||(a[23]=e=>U.value.withdrawal_request=e)},{default:i(()=>[n(he,{label:"email"},{default:i(()=>a[59]||(a[59]=[_("邮件",-1)])),_:1,__:[59]}),n(he,{label:"sms"},{default:i(()=>a[60]||(a[60]=[_("短信",-1)])),_:1,__:[60]}),n(he,{label:"system"},{default:i(()=>a[61]||(a[61]=[_("系统",-1)])),_:1,__:[61]})]),_:1},8,["modelValue"])]),_:1}),n(ie,null,{default:i(()=>[n(se,{type:"primary",onClick:le},{default:i(()=>a[62]||(a[62]=[_("保存设置",-1)])),_:1,__:[62]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[v,"notification"===l.value]]),r(n(ue,null,{header:i(()=>a[63]||(a[63]=[s("h3",null,"安全设置",-1)])),default:i(()=>[n(re,{ref_key:"securityFormRef",ref:V,model:x.value,"label-width":"120px"},{default:i(()=>[n(ie,{label:"登录验证码"},{default:i(()=>[n(fe,{modelValue:x.value.login_captcha,"onUpdate:modelValue":a[24]||(a[24]=e=>x.value.login_captcha=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"注册验证码"},{default:i(()=>[n(fe,{modelValue:x.value.register_captcha,"onUpdate:modelValue":a[25]||(a[25]=e=>x.value.register_captcha=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"密码强度检查"},{default:i(()=>[n(fe,{modelValue:x.value.password_strength,"onUpdate:modelValue":a[26]||(a[26]=e=>x.value.password_strength=e)},null,8,["modelValue"])]),_:1}),n(ie,{label:"登录失败限制"},{default:i(()=>[n(ge,{modelValue:x.value.login_attempts,"onUpdate:modelValue":a[27]||(a[27]=e=>x.value.login_attempts=e),min:3,max:10,placeholder:"登录失败次数限制"},null,8,["modelValue"]),a[64]||(a[64]=s("span",{style:{"margin-left":"10px"}},"次后锁定账户",-1))]),_:1,__:[64]}),n(ie,{label:"会话超时时间"},{default:i(()=>[n(ge,{modelValue:x.value.session_timeout,"onUpdate:modelValue":a[28]||(a[28]=e=>x.value.session_timeout=e),min:30,max:1440,placeholder:"会话超时时间"},null,8,["modelValue"]),a[65]||(a[65]=s("span",{style:{"margin-left":"10px"}},"分钟",-1))]),_:1,__:[65]}),n(ie,{label:"IP白名单"},{default:i(()=>[n(ne,{modelValue:x.value.ip_whitelist,"onUpdate:modelValue":a[29]||(a[29]=e=>x.value.ip_whitelist=e),type:"textarea",rows:3,placeholder:"请输入IP白名单，每行一个IP或IP段"},null,8,["modelValue"])]),_:1}),n(ie,null,{default:i(()=>[n(se,{type:"primary",onClick:de},{default:i(()=>a[66]||(a[66]=[_("保存设置",-1)])),_:1,__:[66]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[v,"security"===l.value]]),"audit_logs"===l.value?(o(),p(_e,{key:0})):c("",!0),"backup"===l.value?(o(),p(ce,{key:1})):c("",!0),"community_settings"===l.value?(o(),p(ve,{key:2})):c("",!0)]),_:1})]),_:1})])}}},[["__scopeId","data-v-59fe5e92"]]);export{he as default};
