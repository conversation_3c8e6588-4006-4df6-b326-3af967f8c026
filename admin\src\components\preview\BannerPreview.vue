<template>
  <div class="banner-preview">
    <div class="banner-container" :style="bannerStyle">
      <img
        v-if="bannerImage"
        :src="bannerImage"
        :alt="groupData.title"
        class="banner-image"
      />
      <div v-else class="banner-placeholder">
        <el-icon class="placeholder-icon"><Picture /></el-icon>
        <div class="placeholder-text">顶部横幅图片</div>
      </div>
      
      <!-- 横幅覆盖层 -->
      <div v-if="showOverlay" class="banner-overlay">
        <div class="overlay-content">
          <h1 class="banner-title">{{ groupData.title || '群组标题' }}</h1>
          <p v-if="groupData.description" class="banner-description">
            {{ groupData.description }}
          </p>
          <div v-if="groupData.price !== undefined" class="banner-price">
            <span class="price-label">入群费用：</span>
            <span class="price-value">{{ formatPrice(groupData.price) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const bannerImage = computed(() => {
  return props.groupData.banner_image || ''
})

const showOverlay = computed(() => {
  return props.section.config?.showOverlay !== false
})

const bannerStyle = computed(() => {
  const config = props.section.config || {}
  return {
    height: `${config.height || 200}px`,
    position: 'relative'
  }
})

// 方法
const formatPrice = (price) => {
  if (price === 0 || price === '0') {
    return '免费'
  }
  return `¥${price}`
}
</script>

<style lang="scss" scoped>
.banner-preview {
  .banner-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    
    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .banner-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;
      border: 2px dashed #dcdfe6;
      color: #909399;
      
      .placeholder-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
      
      .placeholder-text {
        font-size: 14px;
      }
    }
    
    .banner-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      text-align: center;
      
      .overlay-content {
        padding: 20px;
        
        .banner-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 12px 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .banner-description {
          font-size: 16px;
          margin: 0 0 16px 0;
          opacity: 0.9;
          line-height: 1.5;
        }
        
        .banner-price {
          font-size: 18px;
          font-weight: 500;
          
          .price-label {
            opacity: 0.8;
          }
          
          .price-value {
            color: #ffd700;
            font-weight: 600;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
