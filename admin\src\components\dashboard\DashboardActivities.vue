<template>
  <div class="dashboard-activities">
    <!-- 最新订单 -->
    <div class="orders-card">
      <div class="card-header">
        <h3>最新订单</h3>
        <el-button type="text" @click="viewAllOrders">查看全部</el-button>
      </div>
      <div class="orders-list">
        <div 
          v-for="order in recentOrders" 
          :key="order.id"
          class="order-item"
          @click="viewOrderDetail(order)"
        >
          <div class="order-info">
            <div class="order-id">#{{ order.id }}</div>
            <div class="order-user">{{ order.user }}</div>
            <div class="order-time">{{ formatTime(order.time) }}</div>
          </div>
          <div class="order-amount" :class="order.status">
            ¥{{ order.amount }}
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)" size="small">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门群组排行 -->
    <div class="popular-groups-card">
      <div class="card-header">
        <h3>热门群组排行</h3>
        <el-button type="text" @click="viewAllGroups">查看全部</el-button>
      </div>
      <div class="groups-list">
        <div 
          v-for="(group, index) in popularGroups" 
          :key="group.id"
          class="group-item"
          @click="viewGroupDetail(group)"
        >
          <div class="group-rank">{{ index + 1 }}</div>
          <div class="group-avatar">
            <img :src="group.avatar" :alt="group.name" />
          </div>
          <div class="group-info">
            <div class="group-name">{{ group.name }}</div>
            <div class="group-members">{{ group.members }}人</div>
          </div>
          <div class="group-activity">
            <div class="activity-score">{{ group.activity }}</div>
            <div class="activity-label">活跃度</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新动态 -->
    <div class="recent-activities-card">
      <div class="card-header">
        <h3>最新动态</h3>
        <el-button type="text" @click="refreshActivities">刷新</el-button>
      </div>
      <div class="activities-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <el-icon :color="activity.color">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-text">{{ activity.text }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  User, 
  ChatDotRound, 
  Money, 
  Setting,
  Bell,
  Link
} from '@element-plus/icons-vue'

const emit = defineEmits(['view-order', 'view-group', 'view-all-orders', 'view-all-groups'])

// 模拟数据
const recentOrders = ref([
  { id: '20241201001', user: '张三', amount: '299.00', status: 'paid', time: new Date() },
  { id: '20241201002', user: '李四', amount: '199.00', status: 'pending', time: new Date(Date.now() - 300000) },
  { id: '20241201003', user: '王五', amount: '399.00', status: 'paid', time: new Date(Date.now() - 600000) },
  { id: '20241201004', user: '赵六', amount: '99.00', status: 'failed', time: new Date(Date.now() - 900000) },
  { id: '20241201005', user: '钱七', amount: '599.00', status: 'paid', time: new Date(Date.now() - 1200000) }
])

const popularGroups = ref([
  { id: 1, name: '创业交流群', members: 1234, activity: 98, avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 2, name: '技术分享群', members: 987, activity: 95, avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 3, name: '投资理财群', members: 756, activity: 92, avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 4, name: '生活分享群', members: 543, activity: 88, avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 5, name: '学习交流群', members: 432, activity: 85, avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

const recentActivities = ref([
  { id: 1, text: '用户"张三"完成了订单支付', time: new Date(), icon: Money, color: '#67c23a' },
  { id: 2, text: '新用户"李四"注册成功', time: new Date(Date.now() - 180000), icon: User, color: '#409eff' },
  { id: 3, text: '群组"创业交流群"新增50个成员', time: new Date(Date.now() - 360000), icon: ChatDotRound, color: '#e6a23c' },
  { id: 4, text: '系统完成了定时备份任务', time: new Date(Date.now() - 540000), icon: Setting, color: '#909399' },
  { id: 5, text: '短链接"abc123"被访问1000次', time: new Date(Date.now() - 720000), icon: Link, color: '#f56c6c' }
])

// 方法
const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const getStatusType = (status) => {
  const types = {
    paid: 'success',
    pending: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    paid: '已支付',
    pending: '待支付',
    failed: '支付失败'
  }
  return texts[status] || '未知'
}

const viewOrderDetail = (order) => {
  emit('view-order', order)
}

const viewGroupDetail = (group) => {
  emit('view-group', group)
}

const viewAllOrders = () => {
  emit('view-all-orders')
}

const viewAllGroups = () => {
  emit('view-all-groups')
}

const refreshActivities = () => {
  // 刷新动态数据
  console.log('刷新动态数据')
}

onMounted(() => {
  // 组件挂载后的初始化
})
</script>

<style lang="scss" scoped>
.dashboard-activities {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.orders-card, .popular-groups-card, .recent-activities-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

// 订单列表样式
.orders-list {
  .order-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .order-info {
    flex: 1;

    .order-id {
      font-weight: 600;
      color: #303133;
    }

    .order-user {
      color: #606266;
      font-size: 14px;
    }

    .order-time {
      color: #909399;
      font-size: 12px;
    }
  }

  .order-amount {
    font-weight: 600;
    margin-right: 16px;

    &.paid {
      color: #67c23a;
    }

    &.pending {
      color: #e6a23c;
    }

    &.failed {
      color: #f56c6c;
    }
  }
}

// 群组列表样式
.groups-list {
  .group-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .group-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #409eff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 12px;
  }

  .group-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .group-info {
    flex: 1;

    .group-name {
      font-weight: 600;
      color: #303133;
    }

    .group-members {
      color: #909399;
      font-size: 12px;
    }
  }

  .group-activity {
    text-align: center;

    .activity-score {
      font-weight: 600;
      color: #67c23a;
    }

    .activity-label {
      color: #909399;
      font-size: 12px;
    }
  }
}

// 动态列表样式
.activities-list {
  .activity-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .activity-content {
    flex: 1;

    .activity-text {
      color: #303133;
      line-height: 1.4;
    }

    .activity-time {
      color: #909399;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style>
