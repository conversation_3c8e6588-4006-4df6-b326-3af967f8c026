<template>
  <div class="drag-drop-editor">
    <div class="editor-container">
      <!-- 组件库面板 -->
      <div class="components-panel">
        <h3>组件库</h3>
        <div class="component-categories">
          <el-collapse v-model="activeCategories">
            <!-- 基础组件 -->
            <el-collapse-item title="基础组件" name="basic">
              <div class="component-list">
                <div 
                  v-for="component in basicComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, component)"
                >
                  <div class="component-icon">
                    <el-icon>{{ component.icon }}</el-icon>
                  </div>
                  <div class="component-info">
                    <div class="component-name">{{ component.name }}</div>
                    <div class="component-desc">{{ component.description }}</div>
                  </div>
                </div>
              </div>
            </el-collapse-item>

            <!-- 媒体组件 -->
            <el-collapse-item title="媒体组件" name="media">
              <div class="component-list">
                <div 
                  v-for="component in mediaComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, component)"
                >
                  <div class="component-icon">
                    <el-icon>{{ component.icon }}</el-icon>
                  </div>
                  <div class="component-info">
                    <div class="component-name">{{ component.name }}</div>
                    <div class="component-desc">{{ component.description }}</div>
                  </div>
                </div>
              </div>
            </el-collapse-item>

            <!-- 转化组件 -->
            <el-collapse-item title="转化组件" name="conversion">
              <div class="component-list">
                <div 
                  v-for="component in conversionComponents" 
                  :key="component.type"
                  class="component-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, component)"
                >
                  <div class="component-icon">
                    <el-icon>{{ component.icon }}</el-icon>
                  </div>
                  <div class="component-info">
                    <div class="component-name">{{ component.name }}</div>
                    <div class="component-desc">{{ component.description }}</div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 编辑区域 -->
      <div class="editor-area">
        <div class="editor-toolbar">
          <div class="toolbar-left">
            <el-button size="small" @click="undo" :disabled="!canUndo">
              <el-icon><RefreshLeft /></el-icon>
              撤销
            </el-button>
            <el-button size="small" @click="redo" :disabled="!canRedo">
              <el-icon><RefreshRight /></el-icon>
              重做
            </el-button>
          </div>
          <div class="toolbar-center">
            <el-button-group>
              <el-button 
                size="small" 
                :type="viewMode === 'desktop' ? 'primary' : ''"
                @click="setViewMode('desktop')"
              >
                <el-icon><Monitor /></el-icon>
                桌面
              </el-button>
              <el-button 
                size="small" 
                :type="viewMode === 'mobile' ? 'primary' : ''"
                @click="setViewMode('mobile')"
              >
                <el-icon><Iphone /></el-icon>
                手机
              </el-button>
            </el-button-group>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="previewPage">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button size="small" type="primary" @click="savePage">
              <el-icon><Document /></el-icon>
              保存
            </el-button>
          </div>
        </div>

        <div class="editor-canvas" :class="{ 'mobile-view': viewMode === 'mobile' }">
          <div 
            class="canvas-container"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
          >
            <!-- 页面组件列表 -->
            <div class="page-components">
              <div 
                v-for="(component, index) in pageComponents" 
                :key="component.id"
                class="page-component"
                :class="{ 'selected': selectedComponent?.id === component.id }"
                @click="selectComponent(component)"
              >
                <!-- 组件渲染 -->
                <component 
                  :is="getComponentName(component.type)"
                  v-bind="component.props"
                  :data="component.data"
                />

                <!-- 组件操作按钮 -->
                <div class="component-actions" v-if="selectedComponent?.id === component.id">
                  <el-button size="small" @click="moveUp(index)" :disabled="index === 0">
                    <el-icon><ArrowUp /></el-icon>
                  </el-button>
                  <el-button size="small" @click="moveDown(index)" :disabled="index === pageComponents.length - 1">
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <el-button size="small" @click="duplicateComponent(index)">
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" @click="removeComponent(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 空状态提示 -->
              <div v-if="pageComponents.length === 0" class="empty-canvas">
                <div class="empty-icon">
                  <el-icon size="48"><Grid /></el-icon>
                </div>
                <div class="empty-text">
                  <h4>开始创建您的落地页</h4>
                  <p>从左侧组件库拖拽组件到这里</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <h3>属性设置</h3>
        <div v-if="selectedComponent" class="property-editor">
          <h4>{{ getComponentDisplayName(selectedComponent.type) }}</h4>
          
          <!-- 动态属性编辑器 -->
          <el-form :model="selectedComponent.props" label-width="80px" size="small">
            <template v-for="(prop, key) in getComponentProperties(selectedComponent.type)" :key="key">
              <!-- 文本输入 -->
              <el-form-item v-if="prop.type === 'text'" :label="prop.label">
                <el-input 
                  v-model="selectedComponent.props[key]" 
                  :placeholder="prop.placeholder"
                />
              </el-form-item>
              
              <!-- 多行文本 -->
              <el-form-item v-else-if="prop.type === 'textarea'" :label="prop.label">
                <el-input 
                  v-model="selectedComponent.props[key]" 
                  type="textarea"
                  :rows="3"
                  :placeholder="prop.placeholder"
                />
              </el-form-item>
              
              <!-- 数字输入 -->
              <el-form-item v-else-if="prop.type === 'number'" :label="prop.label">
                <el-input-number 
                  v-model="selectedComponent.props[key]" 
                  :min="prop.min"
                  :max="prop.max"
                />
              </el-form-item>
              
              <!-- 颜色选择 -->
              <el-form-item v-else-if="prop.type === 'color'" :label="prop.label">
                <el-color-picker v-model="selectedComponent.props[key]" />
              </el-form-item>
              
              <!-- 选择器 -->
              <el-form-item v-else-if="prop.type === 'select'" :label="prop.label">
                <el-select v-model="selectedComponent.props[key]">
                  <el-option 
                    v-for="option in prop.options" 
                    :key="option.value"
                    :label="option.label" 
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              
              <!-- 开关 -->
              <el-form-item v-else-if="prop.type === 'switch'" :label="prop.label">
                <el-switch v-model="selectedComponent.props[key]" />
              </el-form-item>
            </template>
          </el-form>
        </div>
        
        <div v-else class="no-selection">
          <div class="no-selection-icon">
            <el-icon size="32"><Setting /></el-icon>
          </div>
          <p>选择一个组件来编辑属性</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  RefreshLeft, RefreshRight, Monitor, Iphone, View, Document,
  ArrowUp, ArrowDown, CopyDocument, Delete, Grid, Setting
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'preview', 'save'])

// 响应式数据
const activeCategories = ref(['basic', 'media', 'conversion'])
const viewMode = ref('desktop')
const selectedComponent = ref(null)
const pageComponents = ref([...props.modelValue])
const history = ref([])
const historyIndex = ref(-1)

// 基础组件
const basicComponents = ref([
  {
    type: 'header',
    name: '标题',
    description: '页面标题组件',
    icon: 'Heading',
    defaultProps: {
      text: '群组标题',
      level: 1,
      align: 'center',
      color: '#333'
    }
  },
  {
    type: 'text',
    name: '文本',
    description: '普通文本段落',
    icon: 'Document',
    defaultProps: {
      content: '这里是文本内容',
      fontSize: 14,
      color: '#666',
      align: 'left'
    }
  },
  {
    type: 'button',
    name: '按钮',
    description: 'CTA行动按钮',
    icon: 'Pointer',
    defaultProps: {
      text: '立即加入',
      type: 'primary',
      size: 'large',
      backgroundColor: '#409eff',
      textColor: '#fff'
    }
  }
])

// 媒体组件
const mediaComponents = ref([
  {
    type: 'image',
    name: '图片',
    description: '图片展示组件',
    icon: 'Picture',
    defaultProps: {
      src: '/placeholder-image.jpg',
      alt: '图片描述',
      width: '100%',
      height: 'auto'
    }
  },
  {
    type: 'video',
    name: '视频',
    description: '视频播放组件',
    icon: 'VideoPlay',
    defaultProps: {
      src: '',
      poster: '',
      autoplay: false,
      controls: true
    }
  },
  {
    type: 'carousel',
    name: '轮播图',
    description: '图片轮播组件',
    icon: 'Grid',
    defaultProps: {
      images: [],
      autoplay: true,
      interval: 3000
    }
  }
])

// 转化组件
const conversionComponents = ref([
  {
    type: 'countdown',
    name: '倒计时',
    description: '紧迫感倒计时',
    icon: 'Timer',
    defaultProps: {
      endTime: '',
      format: 'HH:mm:ss',
      title: '限时优惠'
    }
  },
  {
    type: 'testimonial',
    name: '用户评价',
    description: '社会证明组件',
    icon: 'ChatDotRound',
    defaultProps: {
      content: '这个群组真的很棒！',
      author: '张先生',
      rating: 5,
      avatar: ''
    }
  },
  {
    type: 'stats',
    name: '数据统计',
    description: '统计数据展示',
    icon: 'DataAnalysis',
    defaultProps: {
      title: '群组数据',
      items: [
        { label: '当前成员', value: '128' },
        { label: '剩余名额', value: '72' }
      ]
    }
  }
])

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 方法
const handleDragStart = (event, component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDragEnter = (event) => {
  event.preventDefault()
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))
  
  const newComponent = {
    id: Date.now().toString(),
    type: componentData.type,
    props: { ...componentData.defaultProps },
    data: {}
  }
  
  addComponent(newComponent)
}

const addComponent = (component) => {
  pageComponents.value.push(component)
  saveToHistory()
  emit('update:modelValue', pageComponents.value)
}

const selectComponent = (component) => {
  selectedComponent.value = component
}

const removeComponent = (index) => {
  pageComponents.value.splice(index, 1)
  selectedComponent.value = null
  saveToHistory()
  emit('update:modelValue', pageComponents.value)
}

const duplicateComponent = (index) => {
  const component = pageComponents.value[index]
  const duplicated = {
    ...component,
    id: Date.now().toString()
  }
  pageComponents.value.splice(index + 1, 0, duplicated)
  saveToHistory()
  emit('update:modelValue', pageComponents.value)
}

const moveUp = (index) => {
  if (index > 0) {
    const temp = pageComponents.value[index]
    pageComponents.value[index] = pageComponents.value[index - 1]
    pageComponents.value[index - 1] = temp
    saveToHistory()
    emit('update:modelValue', pageComponents.value)
  }
}

const moveDown = (index) => {
  if (index < pageComponents.value.length - 1) {
    const temp = pageComponents.value[index]
    pageComponents.value[index] = pageComponents.value[index + 1]
    pageComponents.value[index + 1] = temp
    saveToHistory()
    emit('update:modelValue', pageComponents.value)
  }
}

const setViewMode = (mode) => {
  viewMode.value = mode
}

const saveToHistory = () => {
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(JSON.parse(JSON.stringify(pageComponents.value)))
  historyIndex.value = history.value.length - 1
}

const undo = () => {
  if (canUndo.value) {
    historyIndex.value--
    pageComponents.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    emit('update:modelValue', pageComponents.value)
  }
}

const redo = () => {
  if (canRedo.value) {
    historyIndex.value++
    pageComponents.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]))
    emit('update:modelValue', pageComponents.value)
  }
}

const previewPage = () => {
  emit('preview', pageComponents.value)
}

const savePage = () => {
  emit('save', pageComponents.value)
  ElMessage.success('页面保存成功')
}

const getComponentName = (type) => {
  // 返回对应的Vue组件名
  const componentMap = {
    'header': 'HeaderComponent',
    'text': 'TextComponent',
    'button': 'ButtonComponent',
    'image': 'ImageComponent',
    'video': 'VideoComponent',
    'carousel': 'CarouselComponent',
    'countdown': 'CountdownComponent',
    'testimonial': 'TestimonialComponent',
    'stats': 'StatsComponent'
  }
  return componentMap[type] || 'div'
}

const getComponentDisplayName = (type) => {
  const allComponents = [...basicComponents.value, ...mediaComponents.value, ...conversionComponents.value]
  const component = allComponents.find(comp => comp.type === type)
  return component ? component.name : type
}

const getComponentProperties = (type) => {
  // 返回组件的可编辑属性配置
  const propertiesMap = {
    'header': {
      text: { type: 'text', label: '标题文本', placeholder: '输入标题' },
      level: { type: 'select', label: '标题级别', options: [
        { label: 'H1', value: 1 },
        { label: 'H2', value: 2 },
        { label: 'H3', value: 3 }
      ]},
      align: { type: 'select', label: '对齐方式', options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' }
      ]},
      color: { type: 'color', label: '文字颜色' }
    },
    'text': {
      content: { type: 'textarea', label: '文本内容', placeholder: '输入文本内容' },
      fontSize: { type: 'number', label: '字体大小', min: 12, max: 48 },
      color: { type: 'color', label: '文字颜色' },
      align: { type: 'select', label: '对齐方式', options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' }
      ]}
    },
    'button': {
      text: { type: 'text', label: '按钮文字', placeholder: '输入按钮文字' },
      type: { type: 'select', label: '按钮类型', options: [
        { label: '主要', value: 'primary' },
        { label: '成功', value: 'success' },
        { label: '警告', value: 'warning' },
        { label: '危险', value: 'danger' }
      ]},
      size: { type: 'select', label: '按钮大小', options: [
        { label: '小', value: 'small' },
        { label: '中', value: 'medium' },
        { label: '大', value: 'large' }
      ]},
      backgroundColor: { type: 'color', label: '背景颜色' },
      textColor: { type: 'color', label: '文字颜色' }
    }
  }
  return propertiesMap[type] || {}
}

// 初始化历史记录
saveToHistory()
</script>

<style lang="scss" scoped>
.drag-drop-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .editor-container {
    flex: 1;
    display: flex;
    
    .components-panel {
      width: 280px;
      background: #f8f9fa;
      border-right: 1px solid #e9ecef;
      padding: 20px;
      overflow-y: auto;
      
      h3 {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
      }
      
      .component-list {
        .component-item {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          cursor: grab;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #409eff;
            transform: translateY(-1px);
          }
          
          &:active {
            cursor: grabbing;
          }
          
          .component-icon {
            width: 32px;
            height: 32px;
            background: #409eff;
            color: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
          }
          
          .component-info {
            flex: 1;
            
            .component-name {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-bottom: 2px;
            }
            
            .component-desc {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
    
    .editor-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .editor-toolbar {
        height: 50px;
        background: white;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        
        .toolbar-left,
        .toolbar-center,
        .toolbar-right {
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
      
      .editor-canvas {
        flex: 1;
        background: #f0f2f5;
        padding: 20px;
        overflow: auto;
        
        &.mobile-view {
          .canvas-container {
            max-width: 375px;
            margin: 0 auto;
          }
        }
        
        .canvas-container {
          background: white;
          min-height: 600px;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          position: relative;
          
          .page-components {
            .page-component {
              position: relative;
              margin: 10px;
              padding: 10px;
              border: 2px dashed transparent;
              border-radius: 4px;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #409eff;
              }
              
              &.selected {
                border-color: #409eff;
                background: rgba(64, 158, 255, 0.05);
              }
              
              .component-actions {
                position: absolute;
                top: -35px;
                right: 0;
                display: flex;
                gap: 5px;
                background: white;
                padding: 5px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }
          }
          
          .empty-canvas {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #999;
            
            .empty-icon {
              margin-bottom: 20px;
            }
            
            .empty-text {
              text-align: center;
              
              h4 {
                font-size: 18px;
                margin-bottom: 10px;
              }
              
              p {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
    
    .properties-panel {
      width: 300px;
      background: #f8f9fa;
      border-left: 1px solid #e9ecef;
      padding: 20px;
      overflow-y: auto;
      
      h3 {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
      }
      
      .property-editor {
        h4 {
          font-size: 14px;
          color: #333;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #e9ecef;
        }
      }
      
      .no-selection {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #999;
        text-align: center;
        
        .no-selection-icon {
          margin-bottom: 15px;
        }
        
        p {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
