<template>
  <div class="group-marketing-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">营销配置</h1>
          <p class="page-subtitle">管理群组的营销配置和模板应用</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showBatchConfig = true" :disabled="!selectedGroups.length">
            <el-icon><Setting /></el-icon>
            批量配置
          </el-button>
          <el-button @click="fetchGroupList">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card shadow="never">
        <el-form :model="filters" inline>
          <el-form-item label="群组名称">
            <el-input
              v-model="filters.title"
              placeholder="请输入群组名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="城市定位">
            <el-select v-model="filters.auto_city_replace" placeholder="请选择" clearable style="width: 150px">
              <el-option label="全部" value="" />
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="展示类型">
            <el-select v-model="filters.display_type" placeholder="请选择" clearable style="width: 150px">
              <el-option label="全部" value="" />
              <el-option label="文字+图片" :value="1" />
              <el-option label="纯图片" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchGroupList">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><RefreshRight /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 群组列表 -->
    <div class="table-section">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>群组列表</span>
            <div class="header-actions">
              <span class="selected-info" v-if="selectedGroups.length">
                已选择 {{ selectedGroups.length }} 个群组
              </span>
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="groupList"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" label="群组名称" min-width="200" />
          <el-table-column prop="price" label="价格" width="100">
            <template #default="scope">
              ¥{{ scope.row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="avatar_library" label="头像库" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.avatar_library === 'qq' ? 'primary' : 'default'">
                {{ scope.row.avatar_library === 'qq' ? 'QQ头像' : '默认头像' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="display_type" label="展示类型" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.display_type === 1 ? 'success' : 'info'">
                {{ scope.row.display_type === 1 ? '文字+图片' : '纯图片' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="virtual_members" label="虚拟成员" width="100" />
          <el-table-column prop="virtual_orders" label="虚拟订单" width="100" />
          <el-table-column prop="wx_accessible" label="微信访问" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.wx_accessible ? 'success' : 'danger'">
                {{ scope.row.wx_accessible ? '允许' : '限制' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auto_city_replace" label="城市定位" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.auto_city_replace ? 'success' : 'info'">
                {{ scope.row.auto_city_replace ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleEdit(scope.row)">
                配置
              </el-button>
              <el-button size="small" @click="handlePreview(scope.row)">
                预览
              </el-button>
              <el-button size="small" @click="handleTestCity(scope.row)">
                测试
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchGroupList"
            @current-change="fetchGroupList"
          />
        </div>
      </el-card>
    </div>

    <!-- 营销配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="营销配置"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="configForm" label-width="120px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基础配置" name="basic">
            <el-form-item label="营销模板">
              <el-select v-model="configForm.template_id" placeholder="请选择模板">
                <el-option
                  v-for="template in marketingTemplates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="阅读量显示">
              <el-input v-model="configForm.read_count_display" placeholder="如：5万+" />
            </el-form-item>
            <el-form-item label="点赞数">
              <el-input-number v-model="configForm.like_count" :min="0" />
            </el-form-item>
            <el-form-item label="想看数">
              <el-input-number v-model="configForm.want_see_count" :min="0" />
            </el-form-item>
            <el-form-item label="按钮文字">
              <el-input v-model="configForm.button_title" placeholder="如：立即加入群聊" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="内容配置" name="content">
            <el-form-item label="群组介绍标题">
              <el-input v-model="configForm.group_intro_title" placeholder="如：群组简介" />
            </el-form-item>
            <el-form-item label="群组介绍内容">
              <el-input
                v-model="configForm.group_intro_content"
                type="textarea"
                :rows="4"
                placeholder="详细介绍群组的价值和特色"
              />
            </el-form-item>
            <el-form-item label="虚拟成员数">
              <el-input-number v-model="configForm.virtual_members" :min="0" />
            </el-form-item>
            <el-form-item label="虚拟订单数">
              <el-input-number v-model="configForm.virtual_orders" :min="0" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveConfig" :loading="saving">
            保存配置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="群组预览" width="600px">
      <div class="preview-content" v-if="previewData">
        <div class="preview-header">
          <h3>{{ previewData.group_info?.title }}</h3>
          <div class="preview-stats">
            <span>阅读 {{ previewData.marketing_config?.read_count_display || '0' }}</span>
            <span>点赞 {{ previewData.marketing_config?.like_count || 0 }}</span>
            <span>想看 {{ previewData.marketing_config?.want_see_count || 0 }}</span>
          </div>
          <div class="preview-price">¥{{ previewData.group_info?.price }}</div>
        </div>

        <div class="preview-intro" v-if="previewData.landing_page?.content">
          <h4>群组介绍</h4>
          <p>{{ previewData.landing_page.content }}</p>
        </div>

        <div class="preview-members" v-if="previewData.group_info?.member_count">
          <h4>群成员 ({{ previewData.group_info.member_count }}人)</h4>
        </div>

        <div class="preview-qr">
          <img :src="previewData.qr_code" alt="群组二维码" style="width: 200px; height: 200px;" />
        </div>
      </div>
    </el-dialog>

    <!-- 批量配置对话框 -->
    <el-dialog v-model="showBatchConfig" title="批量营销配置" width="500px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="应用范围">
          <el-radio-group v-model="batchForm.scope">
            <el-radio label="selected">选中的群组 ({{ selectedGroups.length }}个)</el-radio>
            <el-radio label="all">所有群组</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="营销模板">
          <el-select v-model="batchForm.template_id" placeholder="请选择模板">
            <el-option
              v-for="template in marketingTemplates"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchConfig = false">取消</el-button>
          <el-button type="primary" @click="applyBatchConfig">
            应用配置
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Setting, Refresh, Search, RefreshRight
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showConfigDialog = ref(false)
const showPreviewDialog = ref(false)
const showBatchConfig = ref(false)
const activeTab = ref('basic')
const testCity = ref('北京')

// 群组列表和分页
const groupList = ref([])
const selectedGroups = ref([])
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filters = reactive({
  title: '',
  auto_city_replace: '',
  display_type: ''
})

// 营销模板
const marketingTemplates = ref([])

// 当前群组ID
const currentGroupId = ref(null)

// 配置表单
const configForm = reactive({
  template_id: '',
  read_count_display: '',
  like_count: 0,
  want_see_count: 0,
  button_title: '',
  group_intro_title: '',
  group_intro_content: '',
  virtual_members: 0,
  virtual_orders: 0
})

// 批量配置表单
const batchForm = reactive({
  scope: 'selected',
  template_id: ''
})

// 预览数据
const previewData = ref(null)

// 获取群组列表
const fetchGroupList = async () => {
  loading.value = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 使用Mock数据
    const mockData = [
      {
        id: 1,
        title: '北京商务精英交流群',
        price: 99.00,
        avatar_library: 'qq',
        display_type: 1,
        virtual_members: 328,
        virtual_orders: 89,
        wx_accessible: 1,
        auto_city_replace: 1,
        created_at: '2024-01-15T10:00:00Z'
      },
      {
        id: 2,
        title: '上海副业赚钱交流群',
        price: 58.00,
        avatar_library: 'default',
        display_type: 1,
        virtual_members: 267,
        virtual_orders: 156,
        wx_accessible: 1,
        auto_city_replace: 1,
        created_at: '2024-01-10T14:30:00Z'
      },
      {
        id: 3,
        title: '深圳学习成长群',
        price: 29.00,
        avatar_library: 'qq',
        display_type: 2,
        virtual_members: 145,
        virtual_orders: 67,
        wx_accessible: 0,
        auto_city_replace: 0,
        created_at: '2024-01-20T09:15:00Z'
      },
      {
        id: 4,
        title: '广州健身运动群',
        price: 39.00,
        avatar_library: 'default',
        display_type: 1,
        virtual_members: 189,
        virtual_orders: 78,
        wx_accessible: 1,
        auto_city_replace: 1,
        created_at: '2024-01-25T16:45:00Z'
      },
      {
        id: 5,
        title: '杭州创业交流群',
        price: 88.00,
        avatar_library: 'qq',
        display_type: 1,
        virtual_members: 234,
        virtual_orders: 123,
        wx_accessible: 1,
        auto_city_replace: 1,
        created_at: '2024-01-12T11:20:00Z'
      }
    ]

    // 应用筛选条件
    let filteredData = [...mockData]

    if (filters.title) {
      filteredData = filteredData.filter(item =>
        item.title.toLowerCase().includes(filters.title.toLowerCase())
      )
    }

    if (filters.auto_city_replace !== '') {
      filteredData = filteredData.filter(item =>
        item.auto_city_replace == filters.auto_city_replace
      )
    }

    if (filters.display_type !== '') {
      filteredData = filteredData.filter(item =>
        item.display_type == filters.display_type
      )
    }

    groupList.value = filteredData
    pagination.total = filteredData.length

    console.log('✅ 群组数据加载成功:', groupList.value)
  } catch (error) {
    console.error('获取群组列表失败:', error)
    ElMessage.error('获取群组列表失败')
  } finally {
    loading.value = false
  }
}

// 获取营销模板
const fetchMarketingTemplates = async () => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 使用Mock数据
    const mockTemplates = [
      {
        id: 1,
        name: '商务交流模板',
        description: '适用于商务人士交流的营销模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '社交娱乐模板',
        description: '适用于社交娱乐的营销模板',
        config: {
          read_count_display: '3万+',
          like_count: 800,
          want_see_count: 600,
          button_title: '加入娱乐群',
          group_intro_title: '娱乐交流群',
          group_intro_content: '轻松愉快的社交环境，分享生活乐趣',
          virtual_members: 120,
          virtual_orders: 60
        },
        created_at: '2024-01-02T00:00:00Z'
      }
    ]

    marketingTemplates.value = mockTemplates
    console.log('✅ 营销模板加载成功:', marketingTemplates.value)
  } catch (error) {
    console.error('获取营销模板失败:', error)
    ElMessage.error('获取营销模板失败')
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedGroups.value = validSelection
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    title: '',
    auto_city_replace: '',
    display_type: ''
  })
  fetchGroupList()
}

// 编辑营销配置
const handleEdit = async (row) => {
  currentGroupId.value = row.id
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 使用Mock营销配置数据
    const mockMarketingConfig = {
      basic: {
        template_id: 1,
        read_count_display: '5万+',
        like_count: 1200,
        want_see_count: 800,
        button_title: '立即加入群聊',
        group_intro_title: `${row.title}简介`,
        group_intro_content: `欢迎加入${row.title}，这里有最新的资讯和优质的服务！`,
        virtual_members: row.virtual_members || 150,
        virtual_orders: row.virtual_orders || 80
      }
    }

    // 将Mock数据赋值给表单
    Object.assign(configForm, mockMarketingConfig.basic)

    showConfigDialog.value = true
    console.log('✅ 营销配置加载成功:', configForm)
  } catch (error) {
    console.error('获取营销配置失败:', error)
    ElMessage.error('获取营销配置失败')
  }
}

// 预览群组
const handlePreview = async (row) => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 使用Mock预览数据
    const mockPreviewData = {
      group_info: {
        title: row.title,
        description: `${row.title}是一个专业的交流平台`,
        price: row.price,
        member_count: row.virtual_members,
        order_count: row.virtual_orders
      },
      marketing_config: {
        read_count_display: '5万+',
        like_count: 1200,
        want_see_count: 800,
        button_title: '立即加入群聊'
      },
      preview_url: `https://preview.example.com/group/${row.id}`,
      qr_code: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://preview.example.com/group/${row.id}`,
      landing_page: {
        title: row.title,
        content: `欢迎加入${row.title}！这里有最新的资讯和优质的服务。`,
        features: ['专业交流', '行业资讯', '商业机会', '技能提升'],
        contact_info: 'wechat123456'
      }
    }

    previewData.value = mockPreviewData
    showPreviewDialog.value = true
    console.log('✅ 预览数据加载成功:', previewData.value)
  } catch (error) {
    console.error('获取预览数据失败:', error)
    ElMessage.error('获取预览数据失败')
  }
}

// 测试城市定位
const handleTestCity = async (row) => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 使用Mock城市定位测试数据
    const mockTestData = {
      original_title: row.title,
      replaced_title: testCity.value ? `[${testCity.value}]${row.title}` : row.title,
      strategy_name: '城市前缀策略',
      test_city: testCity.value || '北京',
      replacement_rules: [
        '在标题前添加城市前缀',
        '保持原有标题内容',
        '使用方括号格式'
      ]
    }

    ElMessage.success(`测试结果：${mockTestData.replaced_title}`)
    console.log('✅ 城市定位测试完成:', mockTestData)
  } catch (error) {
    console.error('城市定位测试失败:', error)
    ElMessage.error('城市定位测试失败')
  }
}

// 保存配置
const saveConfig = async () => {
  if (!currentGroupId.value) return

  saving.value = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟保存成功
    console.log('✅ 营销配置保存成功:', {
      groupId: currentGroupId.value,
      config: configForm
    })

    ElMessage.success('营销配置保存成功')
    showConfigDialog.value = false
    fetchGroupList()
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

// 批量配置
const applyBatchConfig = async () => {
  try {
    const groupIds = batchForm.scope === 'selected'
      ? selectedGroups.value.map(g => g.id)
      : groupList.value.map(g => g.id)

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟批量配置成功
    console.log('✅ 批量配置应用成功:', {
      templateId: batchForm.template_id,
      groupIds: groupIds,
      scope: batchForm.scope,
      affectedCount: groupIds.length
    })

    ElMessage.success(`批量配置应用成功，共影响 ${groupIds.length} 个群组`)
    showBatchConfig.value = false
    fetchGroupList()
  } catch (error) {
    console.error('批量配置失败:', error)
    ElMessage.error('批量配置失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchGroupList()
  fetchMarketingTemplates()
})
</script>

<style lang="scss" scoped>
.group-marketing-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-subtitle {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 20px;

  .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

.table-section {
  .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selected-info {
      color: #409eff;
      font-size: 14px;
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.preview-content {
  .preview-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    .preview-stats {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 10px;

      span {
        color: #909399;
        font-size: 14px;
      }
    }

    .preview-price {
      font-size: 20px;
      font-weight: 600;
      color: #e6a23c;
    }
  }

  .preview-intro {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      line-height: 1.6;
    }
  }

  .preview-members {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }
  }

  .preview-qr {
    text-align: center;

    img {
      border-radius: 8px;
      border: 1px solid #e4e7ed;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .group-marketing-container {
    padding: 10px;
  }

  .page-header .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .filter-section .el-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }

  .table-section {
    .el-table {
      font-size: 12px;
    }

    .el-button {
      padding: 5px 10px;
      font-size: 12px;
    }
  }
}
</style>
