import * as echarts from 'echarts'

/**
 * 加载中国地图数据并注册到ECharts
 * @param {string} source 数据源类型: 'local' | 'datav' | 'github'
 * @returns {Promise<boolean>} 是否加载成功
 */
export async function loadChinaMap(source = 'local') {
  try {
    console.log('🗺️ 开始加载中国地图数据，数据源:', source)

    let mapDataUrl

    switch (source) {
      case 'datav':
        // 阿里云DataV - 真实地图数据
        mapDataUrl = 'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json'
        break
      case 'github':
        // GitHub ECharts官方数据
        mapDataUrl = 'https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json'
        break
      case 'local':
      default:
        // 本地简化数据
        mapDataUrl = '/data/china.json'
        break
    }

    console.log('📡 请求地图数据:', mapDataUrl)
    const response = await fetch(mapDataUrl)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const chinaMapData = await response.json()
    console.log('📦 地图数据加载成功')
    console.log('   - 数据类型:', chinaMapData.type)
    console.log('   - 特征数量:', chinaMapData.features?.length || '未知')
    console.log('   - 数据大小:', JSON.stringify(chinaMapData).length, '字符')

    // 注册地图到ECharts
    echarts.registerMap('china', chinaMapData)
    console.log('✅ 中国地图注册成功')

    return true
  } catch (error) {
    console.error('❌ 地图数据加载失败:', error)
    console.log('🔄 尝试降级到本地数据...')

    // 如果外部数据源失败，尝试本地数据
    if (source !== 'local') {
      return await loadChinaMap('local')
    }

    return false
  }
}

/**
 * 获取省份数据映射
 * @returns {Object} 省份名称到数据的映射
 */
export function getProvinceDataMap() {
  return {
    '北京': 15420,
    '上海': 12680,
    '广东': 25680,
    '江苏': 18950,
    '山东': 16780,
    '浙江': 14320,
    '河南': 13560,
    '四川': 12890,
    '湖北': 11450,
    '湖南': 10980,
    '安徽': 9870,
    '河北': 9560,
    '福建': 8790,
    '江西': 8340,
    '辽宁': 7890,
    '山西': 7450,
    '陕西': 7120,
    '吉林': 6890,
    '黑龙江': 6560,
    '内蒙古': 6230,
    '新疆': 5890,
    '西藏': 2340,
    '青海': 2890,
    '甘肃': 4560,
    '宁夏': 3450,
    '云南': 8900,
    '贵州': 6780,
    '广西': 7890,
    '重庆': 5670,
    '天津': 4890,
    '海南': 3450
  }
}

/**
 * 将数据转换为ECharts地图所需的格式
 * @param {Object} dataMap 省份数据映射
 * @returns {Array} ECharts地图数据格式
 */
export function formatMapData(dataMap) {
  return Object.entries(dataMap).map(([name, value]) => ({
    name,
    value
  }))
}

/**
 * 获取数据值对应的颜色
 * @param {number} value 数据值
 * @param {number} maxValue 最大值
 * @returns {string} 颜色值
 */
export function getColorByValue(value, maxValue) {
  const ratio = value / maxValue
  
  if (ratio > 0.8) return '#dc2626' // 深红色
  if (ratio > 0.6) return '#f87171' // 红色
  if (ratio > 0.4) return '#fbbf24' // 橙色
  if (ratio > 0.2) return '#4ade80' // 绿色
  return '#94a3b8' // 灰色
}

/**
 * 生成地图的视觉映射配置
 * @param {number} minValue 最小值
 * @param {number} maxValue 最大值
 * @returns {Object} 视觉映射配置
 */
export function generateVisualMap(minValue, maxValue) {
  return {
    min: minValue,
    max: maxValue,
    left: 20,
    bottom: 30,
    text: ['高', '低'],
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    inRange: {
      color: ['#94a3b8', '#4ade80', '#fbbf24', '#f87171', '#dc2626']
    },
    calculable: true,
    orient: 'horizontal',
    itemWidth: 20,
    itemHeight: 140,
    textGap: 10,
    precision: 0,
    formatter: function(value) {
      if (value >= 10000) return (value / 10000).toFixed(1) + 'w'
      if (value >= 1000) return (value / 1000).toFixed(1) + 'k'
      return value.toString()
    }
  }
}

/**
 * 下载真实地图数据到本地
 * @param {string} source 数据源
 * @returns {Promise<boolean>} 下载是否成功
 */
export async function downloadRealMapData(source = 'datav') {
  try {
    console.log('📥 开始下载真实地图数据...')

    const urls = {
      datav: 'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json',
      github: 'https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json',
      backup: 'https://geo.datav.aliyun.com/areas_v2/bound/100000_full.json'
    }

    const response = await fetch(urls[source] || urls.datav)

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status}`)
    }

    const mapData = await response.json()

    // 创建下载链接
    const dataStr = JSON.stringify(mapData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `china-map-${source}-${Date.now()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    console.log('✅ 地图数据下载成功')
    console.log('📊 数据统计:')
    console.log('   - 特征数量:', mapData.features?.length)
    console.log('   - 文件大小:', (dataStr.length / 1024).toFixed(2), 'KB')

    return true
  } catch (error) {
    console.error('❌ 地图数据下载失败:', error)
    return false
  }
}

/**
 * 获取可用的地图数据源列表
 * @returns {Array} 数据源列表
 */
export function getMapDataSources() {
  return [
    {
      name: 'datav',
      title: '阿里云DataV',
      url: 'https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json',
      description: '高精度地图数据，包含详细边界信息',
      recommended: true
    },
    {
      name: 'github',
      title: 'ECharts官方',
      url: 'https://raw.githubusercontent.com/apache/echarts/master/map/json/china.json',
      description: 'ECharts官方维护的地图数据',
      recommended: false
    },
    {
      name: 'local',
      title: '本地简化版',
      url: '/data/china.json',
      description: '项目内置的简化地图数据',
      recommended: false
    }
  ]
}
