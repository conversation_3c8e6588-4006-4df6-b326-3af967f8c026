/* 响应式断点系统 */
:root {
  /* 断点定义 */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* 移动端导航高度 */
  --mobile-nav-height: 56px;
  --mobile-tab-height: 48px;
  
  /* 触摸目标尺寸 */
  --touch-min-size: 44px;
  --touch-padding: 8px;
  
  /* 字体大小 */
  --font-size-xs-mobile: 12px;
  --font-size-sm-mobile: 14px;
  --font-size-base-mobile: 16px;
  --font-size-lg-mobile: 18px;
  
  --font-size-xs-desktop: 14px;
  --font-size-sm-desktop: 16px;
  --font-size-base-desktop: 18px;
  --font-size-lg-desktop: 20px;
}

/* 响应式工具类 */
.mobile-only { display: block !important; }
.desktop-only { display: none !important; }

@media (min-width: 768px) {
  .mobile-only { display: none !important; }
  .desktop-only { display: block !important; }
}

/* 隐藏/显示工具类 */
@media (max-width: 575.98px) {
  .hidden-xs { display: none !important; }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .hidden-sm { display: none !important; }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .hidden-md { display: none !important; }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
  .hidden-lg { display: none !important; }
}

@media (min-width: 1200px) {
  .hidden-xl { display: none !important; }
}

/* 响应式字体 */
.responsive-text {
  font-size: var(--font-size-base-mobile);
}

@media (min-width: 768px) {
  .responsive-text {
    font-size: var(--font-size-base-desktop);
  }
}

/* 响应式间距 */
.responsive-padding {
  padding: 16px;
}

@media (min-width: 768px) {
  .responsive-padding {
    padding: 24px;
  }
}

@media (min-width: 1200px) {
  .responsive-padding {
    padding: 32px;
  }
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

@media (min-width: 992px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}