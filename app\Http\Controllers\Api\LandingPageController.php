<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LandingPage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

/**
 * 落地页控制器
 * 用于管理微信落地页的CRUD操作
 */
class LandingPageController extends Controller
{
    /**
     * 获取落地页列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = LandingPage::query();

        // 按类型筛选
        if ($request->filled('page_type')) {
            $query->where('page_type', $request->page_type);
        }

        // 按状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->boolean('status'));
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('page_name', 'like', "%{$search}%")
                  ->orWhere('page_content', 'like', "%{$search}%");
            });
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $pages = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'pages' => $pages->items(),
                'total' => $pages->total(),
                'per_page' => $pages->perPage(),
                'current_page' => $pages->currentPage(),
                'last_page' => $pages->lastPage(),
            ]
        ]);
    }

    /**
     * 创建落地页
     */
    public function store(Request $request): JsonResponse
    {
        $data = $request->validate([
            'page_name' => 'required|string|max:100',
            'page_type' => [
                'required',
                'string',
                Rule::in([
                    LandingPage::TYPE_INVITE,
                    LandingPage::TYPE_GROUP,
                    LandingPage::TYPE_PAYMENT,
                    LandingPage::TYPE_GENERAL,
                ])
            ],
            'page_content' => 'required|string',
            'page_config' => 'nullable|array',
            'status' => 'boolean',
        ]);

        // 如果没有提供配置，使用默认配置
        if (empty($data['page_config'])) {
            $data['page_config'] = LandingPage::getDefaultConfig($data['page_type']);
        }

        $page = LandingPage::create($data);

        return response()->json([
            'code' => 0,
            'message' => '创建成功',
            'data' => $page
        ]);
    }

    /**
     * 获取单个落地页
     */
    public function show(LandingPage $landingPage): JsonResponse
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $landingPage
        ]);
    }

    /**
     * 更新落地页
     */
    public function update(Request $request, LandingPage $landingPage): JsonResponse
    {
        $data = $request->validate([
            'page_name' => 'sometimes|required|string|max:100',
            'page_type' => [
                'sometimes',
                'required',
                'string',
                Rule::in([
                    LandingPage::TYPE_INVITE,
                    LandingPage::TYPE_GROUP,
                    LandingPage::TYPE_PAYMENT,
                    LandingPage::TYPE_GENERAL,
                ])
            ],
            'page_content' => 'sometimes|required|string',
            'page_config' => 'nullable|array',
            'status' => 'boolean',
        ]);

        $landingPage->update($data);

        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => $landingPage->fresh()
        ]);
    }

    /**
     * 删除落地页
     */
    public function destroy(LandingPage $landingPage): JsonResponse
    {
        $landingPage->delete();

        return response()->json([
            'code' => 0,
            'message' => '删除成功'
        ]);
    }

    /**
     * 获取页面类型列表
     */
    public function getPageTypes(): JsonResponse
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => LandingPage::getPageTypes()
        ]);
    }

    /**
     * 获取默认配置
     */
    public function getDefaultConfig(Request $request): JsonResponse
    {
        $request->validate([
            'page_type' => [
                'required',
                'string',
                Rule::in([
                    LandingPage::TYPE_INVITE,
                    LandingPage::TYPE_GROUP,
                    LandingPage::TYPE_PAYMENT,
                    LandingPage::TYPE_GENERAL,
                ])
            ]
        ]);

        $config = LandingPage::getDefaultConfig($request->page_type);

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $config
        ]);
    }

    /**
     * 预览落地页
     */
    public function preview(LandingPage $landingPage): JsonResponse
    {
        // 增加使用次数
        $landingPage->incrementUseCount();

        return response()->json([
            'code' => 0,
            'message' => '预览成功',
            'data' => [
                'page' => $landingPage,
                'preview_url' => url("/landing/{$landingPage->id}/preview")
            ]
        ]);
    }
} 