import request from '@/utils/request'
import { mockFinanceAPI } from './mock/finance.js'

// 检查是否使用模拟数据
const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' ||
                (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL)

// 获取财务统计
export function getFinanceStats() {
  return request({
    url: '/admin/finance/stats',
    method: 'get'
  })
}

// 获取财务图表数据
export function getFinanceCharts(params) {
  return request({
    url: '/admin/finance/charts',
    method: 'get',
    params
  })
}

// 获取最新交易记录
export function getRecentTransactions(params) {
  return request({
    url: '/admin/finance/recent-transactions',
    method: 'get',
    params
  })
}

// 获取交易记录列表
export function getTransactionList(params) {
  return request({
    url: '/admin/finance/transactions',
    method: 'get',
    params
  })
}

// 获取交易详情
export function getTransactionDetail(id) {
  return request({
    url: `/admin/finance/transactions/${id}`,
    method: 'get'
  })
}

// 获取佣金记录列表
export function getCommissionList(params) {
  return request({
    url: '/admin/finance/commissions',
    method: 'get',
    params
  })
}

// 获取佣金统计
export function getCommissionStats() {
  return request({
    url: '/admin/finance/commissions/stats',
    method: 'get'
  })
}

// 结算佣金
export function settleCommission(id) {
  return request({
    url: `/admin/finance/commissions/${id}/settle`,
    method: 'post'
  })
}

// 批量结算佣金
export function batchSettleCommission(ids) {
  return request({
    url: '/admin/finance/commissions/batch-settle',
    method: 'post',
    data: { ids }
  })
}

// 获取提现申请列表
export function getWithdrawalList(params) {
  return request({
    url: '/admin/finance/withdrawals',
    method: 'get',
    params
  })
}

// 获取提现统计
export function getWithdrawalStats() {
  return request({
    url: '/admin/finance/withdrawals/stats',
    method: 'get'
  })
}

// 审核提现申请
export function reviewWithdrawal(id, data) {
  return request({
    url: `/admin/finance/withdrawals/${id}/review`,
    method: 'put',
    data
  })
}

// 批量审核提现申请
export function batchReviewWithdrawal(ids, status, remark) {
  return request({
    url: '/admin/finance/withdrawals/batch-review',
    method: 'put',
    data: { ids, status, remark }
  })
}

// 处理提现申请
export function processWithdrawal(id, data) {
  return request({
    url: `/admin/finance/withdrawals/${id}/process`,
    method: 'put',
    data
  })
}

// 导出财务报表
export function exportFinanceReport(params) {
  return request({
    url: '/admin/finance/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出交易记录
export function exportTransactions(params) {
  return request({
    url: '/admin/finance/transactions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出佣金记录
export function exportCommissions(params) {
  return request({
    url: '/admin/finance/commissions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出提现记录
export function exportWithdrawals(params) {
  return request({
    url: '/admin/finance/withdrawals/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取收入分析
export function getIncomeAnalysis(params) {
  return request({
    url: '/admin/finance/income-analysis',
    method: 'get',
    params
  })
}

// 获取支出分析
export function getExpenseAnalysis(params) {
  return request({
    url: '/admin/finance/expense-analysis',
    method: 'get',
    params
  })
}

// 获取利润分析
export function getProfitAnalysis(params) {
  return request({
    url: '/admin/finance/profit-analysis',
    method: 'get',
    params
  })
}

// ========== 以下是为解决 "No matching export" 错误新增的函数 ==========

// 批量结算佣金 (复数形式，根据错误日志添加)
export function batchSettleCommissions(ids) {
    if (useMock) {
        return mockFinanceAPI.batchSettleCommissions(ids);
    }
    return request({
        url: '/admin/finance/commissions/batch-settle',
        method: 'post',
        data: { ids }
    });
}

// 添加佣金
export function addCommission(data) {
    if (useMock) {
        return mockFinanceAPI.addCommission(data);
    }
    return request({
        url: '/admin/finance/commissions',
        method: 'post',
        data
    });
}

// 搜索订单
export function searchOrders(keyword) {
    if (useMock) {
        return mockFinanceAPI.searchOrders(keyword);
    }
    return request({
        url: '/admin/search/orders',
        method: 'get',
        params: { keyword }
    });
}

// 搜索用户
export function searchUsers(keyword) {
    if (useMock) {
        return mockFinanceAPI.searchUsers(keyword);
    }
    return request({
        url: '/admin/search/users',
        method: 'get',
        params: { keyword }
    });
}
