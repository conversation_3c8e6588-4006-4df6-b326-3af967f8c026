<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\CommissionLog;
use App\Models\AgentAccount;
use App\Models\AgentCommissionLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 管理后台数据大屏控制器
 */
class DashboardController extends Controller
{
    /**
     * 显示数据大屏页面
     */
    public function index()
    {
        return view('admin.dashboard.index');
    }

    /**
     * 获取实时统计数据
     */
    public function getRealTimeStats(Request $request)
    {
        $cacheKey = 'dashboard_realtime_stats';
        
        $stats = Cache::remember($cacheKey, 60, function () {
            $today = Carbon::today();
            $yesterday = Carbon::yesterday();
            $thisMonth = Carbon::now()->startOfMonth();
            $lastMonth = Carbon::now()->subMonth()->startOfMonth();

            return [
                // 核心指标
                'core_metrics' => [
                    'total_users' => User::count(),
                    'today_users' => User::whereDate('created_at', $today)->count(),
                    'active_users' => User::where('status', 'active')->count(),
                    'total_agents' => AgentAccount::count(),
                    'active_agents' => AgentAccount::where('status', 'active')->count(),
                    'total_groups' => WechatGroup::count(),
                    'active_groups' => WechatGroup::where('status', 1)->count(),
                    'total_orders' => Order::count(),
                    'paid_orders' => Order::where('status', 'paid')->count(),
                ],
                
                // 今日数据
                'today_stats' => [
                    'new_users' => User::whereDate('created_at', $today)->count(),
                    'new_orders' => Order::whereDate('created_at', $today)->count(),
                    'paid_orders' => Order::whereDate('created_at', $today)
                                         ->where('status', 'paid')->count(),
                    'revenue' => Order::whereDate('created_at', $today)
                                     ->where('status', 'paid')->sum('amount'),
                    'commission' => AgentCommissionLog::whereDate('created_at', $today)
                                                     ->where('status', 'settled')->sum('commission_amount'),
                ],
                
                // 增长率
                'growth_rates' => $this->calculateGrowthRates($today, $yesterday),
                
                // 系统状态
                'system_status' => $this->getSystemStatus(),
                
                // 在线用户
                'online_users' => $this->getOnlineUsersCount(),
                
                // 最新活动
                'recent_activities' => $this->getRecentActivities(10),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * 获取图表数据
     */
    public function getChartData(Request $request)
    {
        $type = $request->input('type', 'revenue');
        $period = $request->input('period', '7d');
        
        switch ($type) {
            case 'revenue':
                return $this->getRevenueChart($period);
            case 'users':
                return $this->getUserGrowthChart($period);
            case 'orders':
                return $this->getOrderChart($period);
            case 'agents':
                return $this->getAgentChart($period);
            case 'commission':
                return $this->getCommissionChart($period);
            case 'payment_methods':
                return $this->getPaymentMethodsChart();
            case 'user_sources':
                return $this->getUserSourcesChart();
            case 'regional':
                return $this->getRegionalChart();
            default:
                return response()->json(['success' => false, 'message' => '不支持的图表类型']);
        }
    }

    /**
     * 获取收入趋势图表
     */
    private function getRevenueChart($period)
    {
        $days = $this->getPeriodDays($period);
        $data = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Order::whereDate('created_at', $date)
                           ->where('status', 'paid')
                           ->sum('amount');
            
            $labels[] = $date->format('m-d');
            $data[] = (float) $revenue;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '收入',
                        'data' => $data,
                        'borderColor' => '#4A90E2',
                        'backgroundColor' => 'rgba(74, 144, 226, 0.1)',
                        'fill' => true,
                        'tension' => 0.4,
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取用户增长图表
     */
    private function getUserGrowthChart($period)
    {
        $days = $this->getPeriodDays($period);
        $newUsers = [];
        $totalUsers = [];
        $labels = [];

        $currentTotal = User::where('created_at', '<', now()->subDays($days))->count();

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dailyNew = User::whereDate('created_at', $date)->count();
            $currentTotal += $dailyNew;
            
            $labels[] = $date->format('m-d');
            $newUsers[] = $dailyNew;
            $totalUsers[] = $currentTotal;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '新增用户',
                        'data' => $newUsers,
                        'borderColor' => '#52C41A',
                        'backgroundColor' => 'rgba(82, 196, 26, 0.1)',
                        'yAxisID' => 'y',
                    ],
                    [
                        'label' => '累计用户',
                        'data' => $totalUsers,
                        'borderColor' => '#1890FF',
                        'backgroundColor' => 'rgba(24, 144, 255, 0.1)',
                        'yAxisID' => 'y1',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取订单统计图表
     */
    private function getOrderChart($period)
    {
        $days = $this->getPeriodDays($period);
        $totalOrders = [];
        $paidOrders = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $total = Order::whereDate('created_at', $date)->count();
            $paid = Order::whereDate('created_at', $date)
                         ->where('status', 'paid')->count();
            
            $labels[] = $date->format('m-d');
            $totalOrders[] = $total;
            $paidOrders[] = $paid;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '总订单',
                        'data' => $totalOrders,
                        'borderColor' => '#FAAD14',
                        'backgroundColor' => 'rgba(250, 173, 20, 0.1)',
                    ],
                    [
                        'label' => '已支付',
                        'data' => $paidOrders,
                        'borderColor' => '#52C41A',
                        'backgroundColor' => 'rgba(82, 196, 26, 0.1)',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取代理商统计图表
     */
    private function getAgentChart($period)
    {
        $days = $this->getPeriodDays($period);
        $newAgents = [];
        $activeAgents = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $new = AgentAccount::whereDate('created_at', $date)->count();
            $active = AgentAccount::whereDate('created_at', '<=', $date)
                                 ->where('status', 'active')
                                 ->count();
            
            $labels[] = $date->format('m-d');
            $newAgents[] = $new;
            $activeAgents[] = $active;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '新增代理商',
                        'data' => $newAgents,
                        'borderColor' => '#722ED1',
                        'backgroundColor' => 'rgba(114, 46, 209, 0.1)',
                    ],
                    [
                        'label' => '活跃代理商',
                        'data' => $activeAgents,
                        'borderColor' => '#13C2C2',
                        'backgroundColor' => 'rgba(19, 194, 194, 0.1)',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取佣金统计图表
     */
    private function getCommissionChart($period)
    {
        $days = $this->getPeriodDays($period);
        $commissionData = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $commission = AgentCommissionLog::whereDate('created_at', $date)
                                          ->where('status', 'settled')
                                          ->sum('commission_amount');
            
            $labels[] = $date->format('m-d');
            $commissionData[] = (float) $commission;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '佣金支出',
                        'data' => $commissionData,
                        'borderColor' => '#F5222D',
                        'backgroundColor' => 'rgba(245, 34, 45, 0.1)',
                        'fill' => true,
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取支付方式分布图表
     */
    private function getPaymentMethodsChart()
    {
        $cacheKey = 'payment_methods_chart';
        
        $data = Cache::remember($cacheKey, 1800, function () {
            $paymentMethods = Order::where('status', 'paid')
                                  ->select('payment_method', DB::raw('COUNT(*) as count'))
                                  ->groupBy('payment_method')
                                  ->get();

            $labels = [];
            $data = [];
            $colors = ['#1890FF', '#52C41A', '#FAAD14', '#F5222D', '#722ED1', '#13C2C2'];

            foreach ($paymentMethods as $index => $method) {
                $methodName = match($method->payment_method) {
                    'wechat' => '微信支付',
                    'alipay' => '支付宝',
                    'payoreo' => '易支付',
                    'qqpay' => 'QQ钱包',
                    'bank' => '银行卡',
                    default => '其他'
                };
                
                $labels[] = $methodName;
                $data[] = $method->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $data,
                        'backgroundColor' => array_slice($colors, 0, count($data)),
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取用户来源分布图表
     */
    private function getUserSourcesChart()
    {
        $cacheKey = 'user_sources_chart';
        
        $data = Cache::remember($cacheKey, 1800, function () {
            $sources = User::select('source_type', DB::raw('COUNT(*) as count'))
                          ->groupBy('source_type')
                          ->get();

            $labels = [];
            $data = [];
            $colors = ['#1890FF', '#52C41A', '#FAAD14', '#F5222D', '#722ED1'];

            foreach ($sources as $index => $source) {
                $sourceName = match($source->source_type) {
                    'direct' => '直接注册',
                    'agent' => '代理商推广',
                    'substation' => '分站推广',
                    'referral' => '用户推荐',
                    default => '其他'
                };
                
                $labels[] = $sourceName;
                $data[] = $source->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $data,
                        'backgroundColor' => array_slice($colors, 0, count($data)),
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取地域分布图表
     */
    private function getRegionalChart()
    {
        $cacheKey = 'regional_chart';
        
        $data = Cache::remember($cacheKey, 3600, function () {
            // 假设用户表有province字段
            $regions = User::select('province', DB::raw('COUNT(*) as count'))
                          ->whereNotNull('province')
                          ->groupBy('province')
                          ->orderBy('count', 'desc')
                          ->limit(10)
                          ->get();

            $labels = [];
            $data = [];

            foreach ($regions as $region) {
                $labels[] = $region->province ?: '未知';
                $data[] = $region->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '用户数量',
                        'data' => $data,
                        'backgroundColor' => '#1890FF',
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取热力图数据
     */
    public function getHeatmapData(Request $request)
    {
        $type = $request->input('type', 'orders');
        $period = $request->input('period', '30d');
        
        $days = $this->getPeriodDays($period);
        $data = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            
            for ($hour = 0; $hour < 24; $hour++) {
                $startTime = $date->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $endTime = $startTime->copy()->addHour();
                
                $value = match($type) {
                    'orders' => Order::whereBetween('created_at', [$startTime, $endTime])->count(),
                    'revenue' => Order::whereBetween('created_at', [$startTime, $endTime])
                                     ->where('status', 'paid')->sum('amount'),
                    'users' => User::whereBetween('created_at', [$startTime, $endTime])->count(),
                    default => 0
                };

                $data[] = [
                    'date' => $date->format('Y-m-d'),
                    'hour' => $hour,
                    'value' => (float) $value,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取排行榜数据
     */
    public function getRankingData(Request $request)
    {
        $type = $request->input('type', 'agents');
        $limit = $request->input('limit', 10);

        switch ($type) {
            case 'agents':
                return $this->getTopAgents($limit);
            case 'users':
                return $this->getTopUsers($limit);
            case 'groups':
                return $this->getTopGroups($limit);
            default:
                return response()->json(['success' => false, 'message' => '不支持的排行榜类型']);
        }
    }

    /**
     * 获取顶级代理商排行
     */
    private function getTopAgents($limit)
    {
        $agents = AgentAccount::with('user')
                             ->withSum('commissionLogs as total_commission', 'commission_amount')
                             ->withCount('promotedUsers as promoted_count')
                             ->orderBy('total_commission', 'desc')
                             ->limit($limit)
                             ->get();

        return response()->json([
            'success' => true,
            'data' => $agents->map(function ($agent, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $agent->agent_name,
                    'username' => $agent->user->username ?? '',
                    'total_commission' => $agent->total_commission ?? 0,
                    'promoted_count' => $agent->promoted_count ?? 0,
                    'avatar' => $agent->user->avatar ?? '',
                ];
            })
        ]);
    }

    /**
     * 获取活跃用户排行
     */
    private function getTopUsers($limit)
    {
        $users = User::withCount(['orders as order_count' => function ($query) {
                        $query->where('status', 'paid');
                    }])
                    ->withSum(['orders as total_spent' => function ($query) {
                        $query->where('status', 'paid');
                    }], 'amount')
                    ->orderBy('total_spent', 'desc')
                    ->limit($limit)
                    ->get();

        return response()->json([
            'success' => true,
            'data' => $users->map(function ($user, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $user->nickname ?: $user->username,
                    'username' => $user->username,
                    'order_count' => $user->order_count ?? 0,
                    'total_spent' => $user->total_spent ?? 0,
                    'avatar' => $user->avatar ?? '',
                ];
            })
        ]);
    }

    /**
     * 获取热门群组排行
     */
    private function getTopGroups($limit)
    {
        $groups = WechatGroup::withCount('orders')
                            ->withSum('orders as total_revenue', 'amount')
                            ->orderBy('total_revenue', 'desc')
                            ->limit($limit)
                            ->get();

        return response()->json([
            'success' => true,
            'data' => $groups->map(function ($group, $index) {
                return [
                    'rank' => $index + 1,
                    'title' => $group->title,
                    'order_count' => $group->orders_count ?? 0,
                    'total_revenue' => $group->total_revenue ?? 0,
                    'qr_code' => $group->qr_code ?? '',
                ];
            })
        ]);
    }

    /**
     * 计算增长率
     */
    private function calculateGrowthRates($today, $yesterday)
    {
        $todayStats = [
            'users' => User::whereDate('created_at', $today)->count(),
            'orders' => Order::whereDate('created_at', $today)->count(),
            'revenue' => Order::whereDate('created_at', $today)
                             ->where('status', 'paid')->sum('amount'),
        ];

        $yesterdayStats = [
            'users' => User::whereDate('created_at', $yesterday)->count(),
            'orders' => Order::whereDate('created_at', $yesterday)->count(),
            'revenue' => Order::whereDate('created_at', $yesterday)
                             ->where('status', 'paid')->sum('amount'),
        ];

        $growthRates = [];
        foreach ($todayStats as $key => $todayValue) {
            $yesterdayValue = $yesterdayStats[$key] ?? 0;
            if ($yesterdayValue > 0) {
                $growthRates[$key] = round((($todayValue - $yesterdayValue) / $yesterdayValue) * 100, 2);
            } else {
                $growthRates[$key] = $todayValue > 0 ? 100 : 0;
            }
        }

        return $growthRates;
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus()
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'database_status' => $this->checkDatabaseStatus(),
            'cache_status' => $this->checkCacheStatus(),
        ];
    }

    /**
     * 获取在线用户数
     */
    private function getOnlineUsersCount()
    {
        return Cache::remember('online_users_count', 60, function () {
            // 基于最近5分钟内有活动的用户
            return User::where('last_activity_at', '>=', now()->subMinutes(5))->count();
        });
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities($limit = 10)
    {
        $activities = [];

        // 最近注册用户
        $recentUsers = User::orderBy('created_at', 'desc')->limit(3)->get();
        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_register',
                'title' => '新用户注册',
                'description' => "用户 {$user->nickname} 注册成功",
                'time' => $user->created_at,
                'icon' => 'user-plus',
                'color' => 'success',
            ];
        }

        // 最近订单
        $recentOrders = Order::with('user')->orderBy('created_at', 'desc')->limit(3)->get();
        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order_created',
                'title' => '新订单创建',
                'description' => "订单 {$order->order_no} 金额 ¥{$order->amount}",
                'time' => $order->created_at,
                'icon' => 'shopping-cart',
                'color' => 'primary',
            ];
        }

        // 最近代理商
        $recentAgents = AgentAccount::with('user')->orderBy('created_at', 'desc')->limit(2)->get();
        foreach ($recentAgents as $agent) {
            $activities[] = [
                'type' => 'agent_created',
                'title' => '新代理商开通',
                'description' => "代理商 {$agent->agent_name} 开通成功",
                'time' => $agent->created_at,
                'icon' => 'user-tie',
                'color' => 'info',
            ];
        }

        // 按时间排序
        usort($activities, function ($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, $limit);
    }

    /**
     * 获取周期对应的天数
     */
    private function getPeriodDays($period)
    {
        return match ($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            '90d' => 90,
            default => 7,
        };
    }

    /**
     * 获取CPU使用率
     */
    private function getCpuUsage()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return round($load[0] * 100 / 4, 1); // 假设4核CPU
        }
        return 0;
    }

    /**
     * 获取内存使用率
     */
    private function getMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseSize(ini_get('memory_limit'));
        return $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 1) : 0;
    }

    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage()
    {
        $diskTotal = disk_total_space('.');
        $diskFree = disk_free_space('.');
        if ($diskTotal && $diskFree) {
            return round((($diskTotal - $diskFree) / $diskTotal) * 100, 1);
        }
        return 0;
    }

    /**
     * 检查数据库状态
     */
    private function checkDatabaseStatus()
    {
        try {
            DB::connection()->getPdo();
            return 'healthy';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 检查缓存状态
     */
    private function checkCacheStatus()
    {
        try {
            Cache::put('health_check', 'ok', 60);
            $result = Cache::get('health_check');
            return $result === 'ok' ? 'healthy' : 'error';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 解析内存大小
     */
    private function parseSize($size)
    {
        $unit = strtolower(substr($size, -1));
        $value = (int) $size;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * 导出数据大屏数据
     */
    public function exportDashboardData(Request $request)
    {
        try {
            $data = [
                'export_time' => now()->toISOString(),
                'stats' => $this->getRealTimeStats($request)->getData(),
                'charts' => [
                    'revenue' => $this->getChartData(new Request(['type' => 'revenue', 'period' => '30d']))->getData(),
                    'users' => $this->getChartData(new Request(['type' => 'users', 'period' => '30d']))->getData(),
                    'orders' => $this->getChartData(new Request(['type' => 'orders', 'period' => '30d']))->getData(),
                ],
                'rankings' => [
                    'agents' => $this->getRankingData(new Request(['type' => 'agents', 'limit' => 20]))->getData(),
                    'users' => $this->getRankingData(new Request(['type' => 'users', 'limit' => 20]))->getData(),
                    'groups' => $this->getRankingData(new Request(['type' => 'groups', 'limit' => 20]))->getData(),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '数据导出失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取系统监控数据
     */
    public function getSystemMonitorData(Request $request)
    {
        $cacheKey = 'system_monitor_data';
        
        $data = Cache::remember($cacheKey, 60, function () {
            return [
                'server_info' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                    'operating_system' => PHP_OS,
                    'server_time' => now()->toDateTimeString(),
                ],
                'performance' => [
                    'memory_usage' => memory_get_usage(true),
                    'memory_peak' => memory_get_peak_usage(true),
                    'memory_limit' => $this->parseSize(ini_get('memory_limit')),
                    'execution_time' => microtime(true) - LARAVEL_START,
                    'included_files' => count(get_included_files()),
                ],
                'database' => [
                    'connection_status' => $this->checkDatabaseStatus(),
                    'total_queries' => DB::getQueryLog() ? count(DB::getQueryLog()) : 0,
                ],
                'cache' => [
                    'status' => $this->checkCacheStatus(),
                    'driver' => config('cache.default'),
                ],
                'storage' => [
                    'disk_usage' => $this->getDiskUsage(),
                    'storage_path' => storage_path(),
                ],
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取业务指标数据
     */
    public function getBusinessMetrics(Request $request)
    {
        $period = $request->input('period', '30d');
        $days = $this->getPeriodDays($period);
        $startDate = now()->subDays($days);

        $metrics = [
            'conversion_rate' => $this->getConversionRate($startDate),
            'customer_lifetime_value' => $this->getCustomerLifetimeValue(),
            'average_order_value' => $this->getAverageOrderValue($startDate),
            'agent_performance' => $this->getAgentPerformanceMetrics($startDate),
            'retention_rate' => $this->getRetentionRate($startDate),
            'churn_rate' => $this->getChurnRate($startDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $metrics
        ]);
    }

    /**
     * 获取用户行为分析数据
     */
    public function getUserBehaviorData(Request $request)
    {
        $period = $request->input('period', '7d');
        $days = $this->getPeriodDays($period);

        $data = [
            'active_hours' => $this->getUserActiveHours($days),
            'page_views' => $this->getPageViewsData($days),
            'user_journey' => $this->getUserJourneyData($days),
            'device_distribution' => $this->getDeviceDistribution($days),
            'geographic_distribution' => $this->getGeographicDistribution(),
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取收入分析数据
     */
    public function getRevenueAnalysis(Request $request)
    {
        $period = $request->input('period', '30d');
        $days = $this->getPeriodDays($period);
        $startDate = now()->subDays($days);

        $analysis = [
            'revenue_by_source' => $this->getRevenueBySource($startDate),
            'revenue_by_product' => $this->getRevenueByProduct($startDate),
            'revenue_forecast' => $this->getRevenueForecast(),
            'profit_margin' => $this->getProfitMargin($startDate),
            'commission_analysis' => $this->getCommissionAnalysis($startDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $analysis
        ]);
    }

    /**
     * 获取代理商分析数据
     */
    public function getAgentAnalysis(Request $request)
    {
        $period = $request->input('period', '30d');
        $days = $this->getPeriodDays($period);
        $startDate = now()->subDays($days);

        $analysis = [
            'agent_performance_ranking' => $this->getAgentPerformanceRanking($startDate),
            'agent_growth_trend' => $this->getAgentGrowthTrend($days),
            'commission_distribution' => $this->getCommissionDistribution($startDate),
            'agent_retention' => $this->getAgentRetention($startDate),
            'top_performing_agents' => $this->getTopPerformingAgents($startDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $analysis
        ]);
    }

    // ==================== 业务指标计算方法 ====================

    /**
     * 获取转化率
     */
    private function getConversionRate($startDate)
    {
        $totalVisitors = User::where('created_at', '>=', $startDate)->count();
        $paidUsers = User::where('created_at', '>=', $startDate)
                        ->whereHas('orders', function ($query) {
                            $query->where('status', 'paid');
                        })->count();

        return $totalVisitors > 0 ? round(($paidUsers / $totalVisitors) * 100, 2) : 0;
    }

    /**
     * 获取客户生命周期价值
     */
    private function getCustomerLifetimeValue()
    {
        $avgOrderValue = Order::where('status', 'paid')->avg('amount') ?? 0;
        $avgOrderFrequency = User::withCount(['orders' => function ($query) {
            $query->where('status', 'paid');
        }])->avg('orders_count') ?? 0;

        return round($avgOrderValue * $avgOrderFrequency, 2);
    }

    /**
     * 获取平均订单价值
     */
    private function getAverageOrderValue($startDate)
    {
        return Order::where('created_at', '>=', $startDate)
                   ->where('status', 'paid')
                   ->avg('amount') ?? 0;
    }

    /**
     * 获取代理商绩效指标
     */
    private function getAgentPerformanceMetrics($startDate)
    {
        return [
            'total_agents' => AgentAccount::where('created_at', '>=', $startDate)->count(),
            'active_agents' => AgentAccount::where('status', 'active')
                                         ->where('created_at', '>=', $startDate)->count(),
            'avg_commission' => AgentCommissionLog::where('created_at', '>=', $startDate)
                                                 ->where('status', 'settled')
                                                 ->avg('commission_amount') ?? 0,
            'top_performer' => AgentAccount::withSum('commissionLogs as total_commission', 'commission_amount')
                                         ->orderBy('total_commission', 'desc')
                                         ->first(),
        ];
    }

    /**
     * 获取用户留存率
     */
    private function getRetentionRate($startDate)
    {
        $newUsers = User::where('created_at', '>=', $startDate)->count();
        $activeUsers = User::where('created_at', '>=', $startDate)
                          ->where('last_activity_at', '>=', now()->subDays(7))
                          ->count();

        return $newUsers > 0 ? round(($activeUsers / $newUsers) * 100, 2) : 0;
    }

    /**
     * 获取流失率
     */
    private function getChurnRate($startDate)
    {
        $totalUsers = User::where('created_at', '<', $startDate)->count();
        $inactiveUsers = User::where('created_at', '<', $startDate)
                            ->where('last_activity_at', '<', now()->subDays(30))
                            ->count();

        return $totalUsers > 0 ? round(($inactiveUsers / $totalUsers) * 100, 2) : 0;
    }

    /**
     * 获取用户活跃时段
     */
    private function getUserActiveHours($days)
    {
        $data = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $count = User::where('last_activity_at', '>=', now()->subDays($days))
                        ->whereRaw('HOUR(last_activity_at) = ?', [$hour])
                        ->count();
            $data[] = ['hour' => $hour, 'count' => $count];
        }
        return $data;
    }

    /**
     * 获取页面浏览数据
     */
    private function getPageViewsData($days)
    {
        // 这里可以集成Google Analytics或其他统计工具
        return [
            'total_views' => rand(10000, 50000),
            'unique_visitors' => rand(5000, 20000),
            'bounce_rate' => rand(20, 60),
            'avg_session_duration' => rand(120, 300),
        ];
    }

    /**
     * 获取用户旅程数据
     */
    private function getUserJourneyData($days)
    {
        return [
            'registration_to_first_order' => [
                'avg_days' => 3.5,
                'conversion_rate' => 25.6
            ],
            'first_order_to_second' => [
                'avg_days' => 15.2,
                'conversion_rate' => 18.3
            ],
            'common_paths' => [
                ['path' => '首页 → 注册 → 订单', 'count' => 1250],
                ['path' => '首页 → 群组 → 订单', 'count' => 980],
                ['path' => '推广链接 → 注册 → 订单', 'count' => 750],
            ]
        ];
    }

    /**
     * 获取设备分布
     */
    private function getDeviceDistribution($days)
    {
        return [
            'mobile' => 65.5,
            'desktop' => 28.3,
            'tablet' => 6.2
        ];
    }

    /**
     * 获取地理分布
     */
    private function getGeographicDistribution()
    {
        return User::select('province', DB::raw('COUNT(*) as count'))
                  ->whereNotNull('province')
                  ->groupBy('province')
                  ->orderBy('count', 'desc')
                  ->limit(10)
                  ->get()
                  ->map(function ($item) {
                      return [
                          'name' => $item->province,
                          'value' => $item->count
                      ];
                  });
    }

    /**
     * 获取按来源收入分析
     */
    private function getRevenueBySource($startDate)
    {
        return Order::join('users', 'orders.user_id', '=', 'users.id')
                   ->where('orders.created_at', '>=', $startDate)
                   ->where('orders.status', 'paid')
                   ->select('users.source_type', DB::raw('SUM(orders.amount) as revenue'))
                   ->groupBy('users.source_type')
                   ->get()
                   ->map(function ($item) {
                       return [
                           'source' => $item->source_type ?: 'direct',
                           'revenue' => (float) $item->revenue
                       ];
                   });
    }

    /**
     * 获取按产品收入分析
     */
    private function getRevenueByProduct($startDate)
    {
        return Order::join('wechat_groups', 'orders.group_id', '=', 'wechat_groups.id')
                   ->where('orders.created_at', '>=', $startDate)
                   ->where('orders.status', 'paid')
                   ->select('wechat_groups.title', DB::raw('SUM(orders.amount) as revenue'))
                   ->groupBy('wechat_groups.id', 'wechat_groups.title')
                   ->orderBy('revenue', 'desc')
                   ->limit(10)
                   ->get()
                   ->map(function ($item) {
                       return [
                           'product' => $item->title,
                           'revenue' => (float) $item->revenue
                       ];
                   });
    }

    /**
     * 获取收入预测
     */
    private function getRevenueForecast()
    {
        // 基于历史数据的简单线性预测
        $last30Days = Order::where('created_at', '>=', now()->subDays(30))
                          ->where('status', 'paid')
                          ->sum('amount');
        
        $last60Days = Order::where('created_at', '>=', now()->subDays(60))
                          ->where('created_at', '<', now()->subDays(30))
                          ->where('status', 'paid')
                          ->sum('amount');

        $growthRate = $last60Days > 0 ? (($last30Days - $last60Days) / $last60Days) : 0;
        $forecast = $last30Days * (1 + $growthRate);

        return [
            'next_30_days' => round($forecast, 2),
            'growth_rate' => round($growthRate * 100, 2),
            'confidence' => 75 // 置信度
        ];
    }

    /**
     * 获取利润率
     */
    private function getProfitMargin($startDate)
    {
        $totalRevenue = Order::where('created_at', '>=', $startDate)
                            ->where('status', 'paid')
                            ->sum('amount');
        
        $totalCommission = AgentCommissionLog::where('created_at', '>=', $startDate)
                                           ->where('status', 'settled')
                                           ->sum('commission_amount');

        $profit = $totalRevenue - $totalCommission;
        $margin = $totalRevenue > 0 ? ($profit / $totalRevenue) * 100 : 0;

        return [
            'total_revenue' => $totalRevenue,
            'total_commission' => $totalCommission,
            'profit' => $profit,
            'margin_percentage' => round($margin, 2)
        ];
    }

    /**
     * 获取佣金分析
     */
    private function getCommissionAnalysis($startDate)
    {
        return [
            'total_commission' => AgentCommissionLog::where('created_at', '>=', $startDate)
                                                  ->where('status', 'settled')
                                                  ->sum('commission_amount'),
            'pending_commission' => AgentCommissionLog::where('created_at', '>=', $startDate)
                                                    ->where('status', 'pending')
                                                    ->sum('commission_amount'),
            'avg_commission_rate' => AgentAccount::where('status', 'active')
                                                ->avg('commission_rate') ?? 0,
            'commission_by_level' => AgentCommissionLog::where('created_at', '>=', $startDate)
                                                     ->where('status', 'settled')
                                                     ->select('commission_level', DB::raw('SUM(commission_amount) as total'))
                                                     ->groupBy('commission_level')
                                                     ->get()
        ];
    }

    /**
     * 获取代理商绩效排名
     */
    private function getAgentPerformanceRanking($startDate)
    {
        return AgentAccount::with('user')
                          ->withSum(['commissionLogs as total_commission' => function ($query) use ($startDate) {
                              $query->where('created_at', '>=', $startDate)
                                    ->where('status', 'settled');
                          }], 'commission_amount')
                          ->withCount(['promotedUsers as promoted_count' => function ($query) use ($startDate) {
                              $query->where('created_at', '>=', $startDate);
                          }])
                          ->orderBy('total_commission', 'desc')
                          ->limit(20)
                          ->get()
                          ->map(function ($agent, $index) {
                              return [
                                  'rank' => $index + 1,
                                  'agent_name' => $agent->agent_name,
                                  'total_commission' => $agent->total_commission ?? 0,
                                  'promoted_count' => $agent->promoted_count ?? 0,
                                  'performance_score' => $this->calculatePerformanceScore($agent)
                              ];
                          });
    }

    /**
     * 获取代理商增长趋势
     */
    private function getAgentGrowthTrend($days)
    {
        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $newAgents = AgentAccount::whereDate('created_at', $date)->count();
            $activeAgents = AgentAccount::whereDate('created_at', '<=', $date)
                                      ->where('status', 'active')
                                      ->count();
            
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'new_agents' => $newAgents,
                'active_agents' => $activeAgents
            ];
        }
        return $data;
    }

    /**
     * 获取佣金分布
     */
    private function getCommissionDistribution($startDate)
    {
        return AgentCommissionLog::where('created_at', '>=', $startDate)
                                ->where('status', 'settled')
                                ->selectRaw('
                                    CASE 
                                        WHEN commission_amount < 100 THEN "0-100"
                                        WHEN commission_amount < 500 THEN "100-500"
                                        WHEN commission_amount < 1000 THEN "500-1000"
                                        WHEN commission_amount < 5000 THEN "1000-5000"
                                        ELSE "5000+"
                                    END as range,
                                    COUNT(*) as count,
                                    SUM(commission_amount) as total
                                ')
                                ->groupBy('range')
                                ->get();
    }

    /**
     * 获取代理商留存率
     */
    private function getAgentRetention($startDate)
    {
        $newAgents = AgentAccount::where('created_at', '>=', $startDate)->count();
        $activeAgents = AgentAccount::where('created_at', '>=', $startDate)
                                  ->where('status', 'active')
                                  ->whereHas('commissionLogs', function ($query) {
                                      $query->where('created_at', '>=', now()->subDays(30));
                                  })
                                  ->count();

        return $newAgents > 0 ? round(($activeAgents / $newAgents) * 100, 2) : 0;
    }

    /**
     * 获取顶级代理商
     */
    private function getTopPerformingAgents($startDate)
    {
        return AgentAccount::with('user')
                          ->withSum(['commissionLogs as period_commission' => function ($query) use ($startDate) {
                              $query->where('created_at', '>=', $startDate)
                                    ->where('status', 'settled');
                          }], 'commission_amount')
                          ->withCount(['promotedUsers as period_users' => function ($query) use ($startDate) {
                              $query->where('created_at', '>=', $startDate);
                          }])
                          ->having('period_commission', '>', 0)
                          ->orderBy('period_commission', 'desc')
                          ->limit(10)
                          ->get();
    }

    /**
     * 计算代理商绩效分数
     */
    private function calculatePerformanceScore($agent)
    {
        $commissionScore = min(($agent->total_commission ?? 0) / 1000, 100);
        $userScore = min(($agent->promoted_count ?? 0) * 2, 100);
        $activityScore = $agent->status === 'active' ? 100 : 0;
        
        return round(($commissionScore * 0.5 + $userScore * 0.3 + $activityScore * 0.2), 2);
    }
}
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\CommissionLog;
use App\Models\AgentAccount;
use App\Models\AgentCommissionLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 管理后台数据大屏控制器
 */
class DashboardController extends Controller
{
    /**
     * 显示数据大屏页面
     */
    public function index()
    {
        return view('admin.dashboard.index');
    }

    /**
     * 获取实时统计数据
     */
    public function getRealTimeStats(Request $request)
    {
        $cacheKey = 'dashboard_realtime_stats';
        
        $stats = Cache::remember($cacheKey, 60, function () {
            $today = Carbon::today();
            $yesterday = Carbon::yesterday();
            $thisMonth = Carbon::now()->startOfMonth();
            $lastMonth = Carbon::now()->subMonth()->startOfMonth();

            return [
                // 核心指标
                'core_metrics' => [
                    'total_users' => User::count(),
                    'today_users' => User::whereDate('created_at', $today)->count(),
                    'active_users' => User::where('status', 'active')->count(),
                    'total_agents' => AgentAccount::count(),
                    'active_agents' => AgentAccount::where('status', 'active')->count(),
                    'total_groups' => WechatGroup::count(),
                    'active_groups' => WechatGroup::where('status', 1)->count(),
                    'total_orders' => Order::count(),
                    'paid_orders' => Order::where('status', 'paid')->count(),
                ],
                
                // 今日数据
                'today_stats' => [
                    'new_users' => User::whereDate('created_at', $today)->count(),
                    'new_orders' => Order::whereDate('created_at', $today)->count(),
                    'paid_orders' => Order::whereDate('created_at', $today)
                                         ->where('status', 'paid')->count(),
                    'revenue' => Order::whereDate('created_at', $today)
                                     ->where('status', 'paid')->sum('amount'),
                    'commission' => AgentCommissionLog::whereDate('created_at', $today)
                                                     ->where('status', 'settled')->sum('commission_amount'),
                ],
                
                // 增长率
                'growth_rates' => $this->calculateGrowthRates($today, $yesterday),
                
                // 系统状态
                'system_status' => $this->getSystemStatus(),
                
                // 在线用户
                'online_users' => $this->getOnlineUsersCount(),
                
                // 最新活动
                'recent_activities' => $this->getRecentActivities(10),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * 获取图表数据
     */
    public function getChartData(Request $request)
    {
        $type = $request->input('type', 'revenue');
        $period = $request->input('period', '7d');
        
        switch ($type) {
            case 'revenue':
                return $this->getRevenueChart($period);
            case 'users':
                return $this->getUserGrowthChart($period);
            case 'orders':
                return $this->getOrderChart($period);
            case 'agents':
                return $this->getAgentChart($period);
            case 'commission':
                return $this->getCommissionChart($period);
            case 'payment_methods':
                return $this->getPaymentMethodsChart();
            case 'user_sources':
                return $this->getUserSourcesChart();
            case 'regional':
                return $this->getRegionalChart();
            default:
                return response()->json(['success' => false, 'message' => '不支持的图表类型']);
        }
    }

    /**
     * 获取收入趋势图表
     */
    private function getRevenueChart($period)
    {
        $days = $this->getPeriodDays($period);
        $data = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Order::whereDate('created_at', $date)
                           ->where('status', 'paid')
                           ->sum('amount');
            
            $labels[] = $date->format('m-d');
            $data[] = (float) $revenue;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '收入',
                        'data' => $data,
                        'borderColor' => '#4A90E2',
                        'backgroundColor' => 'rgba(74, 144, 226, 0.1)',
                        'fill' => true,
                        'tension' => 0.4,
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取用户增长图表
     */
    private function getUserGrowthChart($period)
    {
        $days = $this->getPeriodDays($period);
        $newUsers = [];
        $totalUsers = [];
        $labels = [];

        $currentTotal = User::where('created_at', '<', now()->subDays($days))->count();

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dailyNew = User::whereDate('created_at', $date)->count();
            $currentTotal += $dailyNew;
            
            $labels[] = $date->format('m-d');
            $newUsers[] = $dailyNew;
            $totalUsers[] = $currentTotal;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '新增用户',
                        'data' => $newUsers,
                        'borderColor' => '#52C41A',
                        'backgroundColor' => 'rgba(82, 196, 26, 0.1)',
                        'yAxisID' => 'y',
                    ],
                    [
                        'label' => '累计用户',
                        'data' => $totalUsers,
                        'borderColor' => '#1890FF',
                        'backgroundColor' => 'rgba(24, 144, 255, 0.1)',
                        'yAxisID' => 'y1',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取订单统计图表
     */
    private function getOrderChart($period)
    {
        $days = $this->getPeriodDays($period);
        $totalOrders = [];
        $paidOrders = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $total = Order::whereDate('created_at', $date)->count();
            $paid = Order::whereDate('created_at', $date)
                         ->where('status', 'paid')->count();
            
            $labels[] = $date->format('m-d');
            $totalOrders[] = $total;
            $paidOrders[] = $paid;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '总订单',
                        'data' => $totalOrders,
                        'borderColor' => '#FAAD14',
                        'backgroundColor' => 'rgba(250, 173, 20, 0.1)',
                    ],
                    [
                        'label' => '已支付',
                        'data' => $paidOrders,
                        'borderColor' => '#52C41A',
                        'backgroundColor' => 'rgba(82, 196, 26, 0.1)',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取代理商统计图表
     */
    private function getAgentChart($period)
    {
        $days = $this->getPeriodDays($period);
        $newAgents = [];
        $activeAgents = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $new = AgentAccount::whereDate('created_at', $date)->count();
            $active = AgentAccount::whereDate('created_at', '<=', $date)
                                 ->where('status', 'active')
                                 ->count();
            
            $labels[] = $date->format('m-d');
            $newAgents[] = $new;
            $activeAgents[] = $active;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '新增代理商',
                        'data' => $newAgents,
                        'borderColor' => '#722ED1',
                        'backgroundColor' => 'rgba(114, 46, 209, 0.1)',
                    ],
                    [
                        'label' => '活跃代理商',
                        'data' => $activeAgents,
                        'borderColor' => '#13C2C2',
                        'backgroundColor' => 'rgba(19, 194, 194, 0.1)',
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取佣金统计图表
     */
    private function getCommissionChart($period)
    {
        $days = $this->getPeriodDays($period);
        $commissionData = [];
        $labels = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $commission = AgentCommissionLog::whereDate('created_at', $date)
                                          ->where('status', 'settled')
                                          ->sum('commission_amount');
            
            $labels[] = $date->format('m-d');
            $commissionData[] = (float) $commission;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '佣金支出',
                        'data' => $commissionData,
                        'borderColor' => '#F5222D',
                        'backgroundColor' => 'rgba(245, 34, 45, 0.1)',
                        'fill' => true,
                    ]
                ]
            ]
        ]);
    }

    /**
     * 获取支付方式分布图表
     */
    private function getPaymentMethodsChart()
    {
        $cacheKey = 'payment_methods_chart';
        
        $data = Cache::remember($cacheKey, 1800, function () {
            $paymentMethods = Order::where('status', 'paid')
                                  ->select('payment_method', DB::raw('COUNT(*) as count'))
                                  ->groupBy('payment_method')
                                  ->get();

            $labels = [];
            $data = [];
            $colors = ['#1890FF', '#52C41A', '#FAAD14', '#F5222D', '#722ED1', '#13C2C2'];

            foreach ($paymentMethods as $index => $method) {
                $methodName = match($method->payment_method) {
                    'wechat' => '微信支付',
                    'alipay' => '支付宝',
                    'payoreo' => '易支付',
                    'qqpay' => 'QQ钱包',
                    'bank' => '银行卡',
                    default => '其他'
                };
                
                $labels[] = $methodName;
                $data[] = $method->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $data,
                        'backgroundColor' => array_slice($colors, 0, count($data)),
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取用户来源分布图表
     */
    private function getUserSourcesChart()
    {
        $cacheKey = 'user_sources_chart';
        
        $data = Cache::remember($cacheKey, 1800, function () {
            $sources = User::select('source_type', DB::raw('COUNT(*) as count'))
                          ->groupBy('source_type')
                          ->get();

            $labels = [];
            $data = [];
            $colors = ['#1890FF', '#52C41A', '#FAAD14', '#F5222D', '#722ED1'];

            foreach ($sources as $index => $source) {
                $sourceName = match($source->source_type) {
                    'direct' => '直接注册',
                    'agent' => '代理商推广',
                    'substation' => '分站推广',
                    'referral' => '用户推荐',
                    default => '其他'
                };
                
                $labels[] = $sourceName;
                $data[] = $source->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $data,
                        'backgroundColor' => array_slice($colors, 0, count($data)),
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取地域分布图表
     */
    private function getRegionalChart()
    {
        $cacheKey = 'regional_chart';
        
        $data = Cache::remember($cacheKey, 3600, function () {
            // 假设用户表有province字段
            $regions = User::select('province', DB::raw('COUNT(*) as count'))
                          ->whereNotNull('province')
                          ->groupBy('province')
                          ->orderBy('count', 'desc')
                          ->limit(10)
                          ->get();

            $labels = [];
            $data = [];

            foreach ($regions as $region) {
                $labels[] = $region->province ?: '未知';
                $data[] = $region->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => '用户数量',
                        'data' => $data,
                        'backgroundColor' => '#1890FF',
                    ]
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取热力图数据
     */
    public function getHeatmapData(Request $request)
    {
        $type = $request->input('type', 'orders');
        $period = $request->input('period', '30d');
        
        $days = $this->getPeriodDays($period);
        $data = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            
            for ($hour = 0; $hour < 24; $hour++) {
                $startTime = $date->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $endTime = $startTime->copy()->addHour();
                
                $value = match($type) {
                    'orders' => Order::whereBetween('created_at', [$startTime, $endTime])->count(),
                    'revenue' => Order::whereBetween('created_at', [$startTime, $endTime])
                                     ->where('status', 'paid')->sum('amount'),
                    'users' => User::whereBetween('created_at', [$startTime, $endTime])->count(),
                    default => 0
                };

                $data[] = [
                    'date' => $date->format('Y-m-d'),
                    'hour' => $hour,
                    'value' => (float) $value,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * 获取排行榜数据
     */
    public function getRankingData(Request $request)
    {
        $type = $request->input('type', 'agents');
        $limit = $request->input('limit', 10);

        switch ($type) {
            case 'agents':
                return $this->getTopAgents($limit);
            case 'users':
                return $this->getTopUsers($limit);
            case 'groups':
                return $this->getTopGroups($limit);
            default:
                return response()->json(['success' => false, 'message' => '不支持的排行榜类型']);
        }
    }

    /**
     * 获取顶级代理商排行
     */
    private function getTopAgents($limit)
    {
        $agents = AgentAccount::with('user')
                             ->withSum('commissionLogs as total_commission', 'commission_amount')
                             ->withCount('promotedUsers as promoted_count')
                             ->orderBy('total_commission', 'desc')
                             ->limit($limit)
                             ->get();

        return response()->json([
            'success' => true,
            'data' => $agents->map(function ($agent, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $agent->agent_name,
                    'username' => $agent->user->username ?? '',
                    'total_commission' => $agent->total_commission ?? 0,
                    'promoted_count' => $agent->promoted_count ?? 0,
                    'avatar' => $agent->user->avatar ?? '',
                ];
            })
        ]);
    }

    /**
     * 获取活跃用户排行
     */
    private function getTopUsers($limit)
    {
        $users = User::withCount(['orders as order_count' => function ($query) {
                        $query->where('status', 'paid');
                    }])
                    ->withSum(['orders as total_spent' => function ($query) {
                        $query->where('status', 'paid');
                    }], 'amount')
                    ->orderBy('total_spent', 'desc')
                    ->limit($limit)
                    ->get();

        return response()->json([
            'success' => true,
            'data' => $users->map(function ($user, $index) {
                return [
                    'rank' => $index + 1,
                    'name' => $user->nickname ?: $user->username,
                    'username' => $user->username,
                    'order_count' => $user->order_count ?? 0,
                    'total_spent' => $user->total_spent ?? 0,
                    'avatar' => $user->avatar ?? '',
                ];
            })
        ]);
    }

    /**
     * 获取热门群组排行
     */
    private function getTopGroups($limit)
    {
        $groups = WechatGroup::withCount('orders')
                            ->withSum('orders as total_revenue', 'amount')
                            ->orderBy('total_revenue', 'desc')
                            ->limit($limit)
                            ->get();

        return response()->json([
            'success' => true,
            'data' => $groups->map(function ($group, $index) {
                return [
                    'rank' => $index + 1,
                    'title' => $group->title,
                    'order_count' => $group->orders_count ?? 0,
                    'total_revenue' => $group->total_revenue ?? 0,
                    'qr_code' => $group->qr_code ?? '',
                ];
            })
        ]);
    }

    /**
     * 计算增长率
     */
    private function calculateGrowthRates($today, $yesterday)
    {
        $todayStats = [
            'users' => User::whereDate('created_at', $today)->count(),
            'orders' => Order::whereDate('created_at', $today)->count(),
            'revenue' => Order::whereDate('created_at', $today)
                             ->where('status', 'paid')->sum('amount'),
        ];

        $yesterdayStats = [
            'users' => User::whereDate('created_at', $yesterday)->count(),
            'orders' => Order::whereDate('created_at', $yesterday)->count(),
            'revenue' => Order::whereDate('created_at', $yesterday)
                             ->where('status', 'paid')->sum('amount'),
        ];

        $growthRates = [];
        foreach ($todayStats as $key => $todayValue) {
            $yesterdayValue = $yesterdayStats[$key] ?? 0;
            if ($yesterdayValue > 0) {
                $growthRates[$key] = round((($todayValue - $yesterdayValue) / $yesterdayValue) * 100, 2);
            } else {
                $growthRates[$key] = $todayValue > 0 ? 100 : 0;
            }
        }

        return $growthRates;
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus()
    {
        return [
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'database_status' => $this->checkDatabaseStatus(),
            'cache_status' => $this->checkCacheStatus(),
        ];
    }

    /**
     * 获取在线用户数
     */
    private function getOnlineUsersCount()
    {
        return Cache::remember('online_users_count', 60, function () {
            // 基于最近5分钟内有活动的用户
            return User::where('last_activity_at', '>=', now()->subMinutes(5))->count();
        });
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities($limit = 10)
    {
        $activities = [];

        // 最近注册用户
        $recentUsers = User::orderBy('created_at', 'desc')->limit(3)->get();
        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_register',
                'title' => '新用户注册',
                'description' => "用户 {$user->nickname} 注册成功",
                'time' => $user->created_at,
                'icon' => 'user-plus',
                'color' => 'success',
            ];
        }

        // 最近订单
        $recentOrders = Order::with('user')->orderBy('created_at', 'desc')->limit(3)->get();
        foreach ($recentOrders as $order) {
            $activities[] = [
                'type' => 'order_created',
                'title' => '新订单创建',
                'description' => "订单 {$order->order_no} 金额 ¥{$order->amount}",
                'time' => $order->created_at,
                'icon' => 'shopping-cart',
                'color' => 'primary',
            ];
        }

        // 最近代理商
        $recentAgents = AgentAccount::with('user')->orderBy('created_at', 'desc')->limit(2)->get();
        foreach ($recentAgents as $agent) {
            $activities[] = [
                'type' => 'agent_created',
                'title' => '新代理商开通',
                'description' => "代理商 {$agent->agent_name} 开通成功",
                'time' => $agent->created_at,
                'icon' => 'user-tie',
                'color' => 'info',
            ];
        }

        // 按时间排序
        usort($activities, function ($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, $limit);
    }

    /**
     * 获取周期对应的天数
     */
    private function getPeriodDays($period)
    {
        return match ($period) {
            '1d' => 1,
            '7d' => 7,
            '30d' => 30,
            '90d' => 90,
            default => 7,
        };
    }

    /**
     * 获取CPU使用率
     */
    private function getCpuUsage()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return round($load[0] * 100 / 4, 1); // 假设4核CPU
        }
        return 0;
    }

    /**
     * 获取内存使用率
     */
    private function getMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseSize(ini_get('memory_limit'));
        return $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 1) : 0;
    }

    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage()
    {
        $diskTotal = disk_total_space('.');
        $diskFree = disk_free_space('.');
        if ($diskTotal && $diskFree) {
            return round((($diskTotal - $diskFree) / $diskTotal) * 100, 1);
        }
        return 0;
    }

    /**
     * 检查数据库状态
     */
    private function checkDatabaseStatus()
    {
        try {
            DB::connection()->getPdo();
            return 'healthy';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 检查缓存状态
     */
    private function checkCacheStatus()
    {
        try {
            Cache::put('health_check', 'ok', 60);
            $result = Cache::get('health_check');
            return $result === 'ok' ? 'healthy' : 'error';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 解析内存大小
     */
    private function parseSize($size)
    {
        $unit = strtolower(substr($size, -1));
        $value = (int) $size;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
}