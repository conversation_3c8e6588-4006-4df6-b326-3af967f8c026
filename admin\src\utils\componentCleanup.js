/**
 * 组件清理工具
 * 提供安全的组件卸载和资源清理功能
 */

import { onBeforeUnmount, onUnmounted, ref } from 'vue'

/**
 * 组件清理管理器
 */
export class ComponentCleanupManager {
  constructor() {
    this.timers = new Set()
    this.listeners = new Set()
    this.observers = new Set()
    this.subscriptions = new Set()
    this.isDestroyed = false
  }

  /**
   * 注册定时器
   */
  addTimer(timer) {
    if (this.isDestroyed) return
    this.timers.add(timer)
    return timer
  }

  /**
   * 注册事件监听器
   */
  addListener(element, event, handler, options) {
    if (this.isDestroyed) return
    
    const listener = { element, event, handler, options }
    this.listeners.add(listener)
    
    if (element && typeof element.addEventListener === 'function') {
      element.addEventListener(event, handler, options)
    }
    
    return listener
  }

  /**
   * 注册观察器
   */
  addObserver(observer) {
    if (this.isDestroyed) return
    this.observers.add(observer)
    return observer
  }

  /**
   * 注册订阅
   */
  addSubscription(subscription) {
    if (this.isDestroyed) return
    this.subscriptions.add(subscription)
    return subscription
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    if (this.isDestroyed) return
    
    try {
      // 清理定时器
      this.timers.forEach(timer => {
        if (timer) {
          clearTimeout(timer)
          clearInterval(timer)
        }
      })
      this.timers.clear()

      // 清理事件监听器
      this.listeners.forEach(listener => {
        try {
          if (listener.element && typeof listener.element.removeEventListener === 'function') {
            listener.element.removeEventListener(listener.event, listener.handler, listener.options)
          }
        } catch (error) {
          console.warn('清理事件监听器失败:', error)
        }
      })
      this.listeners.clear()

      // 清理观察器
      this.observers.forEach(observer => {
        try {
          if (observer && typeof observer.disconnect === 'function') {
            observer.disconnect()
          }
        } catch (error) {
          console.warn('清理观察器失败:', error)
        }
      })
      this.observers.clear()

      // 清理订阅
      this.subscriptions.forEach(subscription => {
        try {
          if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe()
          } else if (subscription && typeof subscription === 'function') {
            subscription()
          }
        } catch (error) {
          console.warn('清理订阅失败:', error)
        }
      })
      this.subscriptions.clear()

      this.isDestroyed = true
    } catch (error) {
      console.error('组件清理过程中发生错误:', error)
    }
  }
}

/**
 * Vue组合式API：安全的组件清理
 */
export function useComponentCleanup() {
  const manager = new ComponentCleanupManager()

  // 组件卸载前清理
  onBeforeUnmount(() => {
    manager.cleanup()
  })

  // 组件卸载后再次确保清理
  onUnmounted(() => {
    manager.cleanup()
  })

  return {
    addTimer: manager.addTimer.bind(manager),
    addListener: manager.addListener.bind(manager),
    addObserver: manager.addObserver.bind(manager),
    addSubscription: manager.addSubscription.bind(manager),
    cleanup: manager.cleanup.bind(manager),
    isDestroyed: () => manager.isDestroyed
  }
}

/**
 * 安全的定时器工具
 */
export function useSafeTimer() {
  const cleanup = useComponentCleanup()

  const setTimeout = (callback, delay) => {
    const timer = window.setTimeout(() => {
      if (!cleanup.isDestroyed()) {
        callback()
      }
    }, delay)
    return cleanup.addTimer(timer)
  }

  const setInterval = (callback, delay) => {
    const timer = window.setInterval(() => {
      if (!cleanup.isDestroyed()) {
        callback()
      } else {
        window.clearInterval(timer)
      }
    }, delay)
    return cleanup.addTimer(timer)
  }

  const clearTimeout = (timer) => {
    window.clearTimeout(timer)
  }

  const clearInterval = (timer) => {
    window.clearInterval(timer)
  }

  return {
    setTimeout,
    setInterval,
    clearTimeout,
    clearInterval
  }
}

/**
 * 安全的事件监听器工具
 */
export function useSafeEventListener() {
  const cleanup = useComponentCleanup()

  const addEventListener = (element, event, handler, options) => {
    return cleanup.addListener(element, event, handler, options)
  }

  return {
    addEventListener
  }
}

/**
 * 安全的观察器工具
 */
export function useSafeObserver() {
  const cleanup = useComponentCleanup()

  const createIntersectionObserver = (callback, options) => {
    const observer = new IntersectionObserver(callback, options)
    return cleanup.addObserver(observer)
  }

  const createMutationObserver = (callback) => {
    const observer = new MutationObserver(callback)
    return cleanup.addObserver(observer)
  }

  const createResizeObserver = (callback) => {
    const observer = new ResizeObserver(callback)
    return cleanup.addObserver(observer)
  }

  return {
    createIntersectionObserver,
    createMutationObserver,
    createResizeObserver
  }
}
