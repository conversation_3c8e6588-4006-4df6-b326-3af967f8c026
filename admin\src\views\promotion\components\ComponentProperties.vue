<template>
  <div class="component-properties">
    <div class="properties-header">
      <h4>组件属性</h4>
      <el-button type="text" size="small" @click="resetProperties">
        <el-icon><Refresh /></el-icon>
        重置
      </el-button>
    </div>

    <div class="properties-content" v-if="selectedComponent">
      <!-- 基础属性 -->
      <div class="property-group">
        <div class="group-title">基础属性</div>
        
        <div class="property-item">
          <label>组件ID</label>
          <el-input 
            v-model="properties.id" 
            size="small" 
            placeholder="组件唯一标识"
            @input="updateProperty('id', $event)"
          />
        </div>
        
        <div class="property-item">
          <label>组件名称</label>
          <el-input 
            v-model="properties.name" 
            size="small" 
            placeholder="组件显示名称"
            @input="updateProperty('name', $event)"
          />
        </div>
        
        <div class="property-item">
          <label>CSS类名</label>
          <el-input 
            v-model="properties.className" 
            size="small" 
            placeholder="自定义CSS类名"
            @input="updateProperty('className', $event)"
          />
        </div>
      </div>

      <!-- 样式属性 -->
      <div class="property-group">
        <div class="group-title">样式属性</div>
        
        <div class="property-item">
          <label>宽度</label>
          <el-input 
            v-model="properties.width" 
            size="small" 
            placeholder="如: 100px, 50%, auto"
            @input="updateProperty('width', $event)"
          >
            <template #append>px</template>
          </el-input>
        </div>
        
        <div class="property-item">
          <label>高度</label>
          <el-input 
            v-model="properties.height" 
            size="small" 
            placeholder="如: 200px, auto"
            @input="updateProperty('height', $event)"
          >
            <template #append>px</template>
          </el-input>
        </div>
        
        <div class="property-item">
          <label>背景颜色</label>
          <el-color-picker 
            v-model="properties.backgroundColor" 
            size="small"
            @change="updateProperty('backgroundColor', $event)"
          />
        </div>
        
        <div class="property-item">
          <label>文字颜色</label>
          <el-color-picker 
            v-model="properties.color" 
            size="small"
            @change="updateProperty('color', $event)"
          />
        </div>
        
        <div class="property-item">
          <label>字体大小</label>
          <el-input-number 
            v-model="properties.fontSize" 
            size="small"
            :min="12"
            :max="72"
            @change="updateProperty('fontSize', $event + 'px')"
          />
        </div>
        
        <div class="property-item">
          <label>字体粗细</label>
          <el-select 
            v-model="properties.fontWeight" 
            size="small"
            @change="updateProperty('fontWeight', $event)"
          >
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="100" value="100" />
            <el-option label="200" value="200" />
            <el-option label="300" value="300" />
            <el-option label="400" value="400" />
            <el-option label="500" value="500" />
            <el-option label="600" value="600" />
            <el-option label="700" value="700" />
            <el-option label="800" value="800" />
            <el-option label="900" value="900" />
          </el-select>
        </div>
        
        <div class="property-item">
          <label>文字对齐</label>
          <el-select 
            v-model="properties.textAlign" 
            size="small"
            @change="updateProperty('textAlign', $event)"
          >
            <el-option label="左对齐" value="left" />
            <el-option label="居中" value="center" />
            <el-option label="右对齐" value="right" />
            <el-option label="两端对齐" value="justify" />
          </el-select>
        </div>
      </div>

      <!-- 布局属性 -->
      <div class="property-group">
        <div class="group-title">布局属性</div>
        
        <div class="property-item">
          <label>外边距</label>
          <div class="margin-padding-control">
            <el-input-number 
              v-model="margins.top" 
              size="small" 
              placeholder="上"
              @change="updateMargin"
            />
            <el-input-number 
              v-model="margins.right" 
              size="small" 
              placeholder="右"
              @change="updateMargin"
            />
            <el-input-number 
              v-model="margins.bottom" 
              size="small" 
              placeholder="下"
              @change="updateMargin"
            />
            <el-input-number 
              v-model="margins.left" 
              size="small" 
              placeholder="左"
              @change="updateMargin"
            />
          </div>
        </div>
        
        <div class="property-item">
          <label>内边距</label>
          <div class="margin-padding-control">
            <el-input-number 
              v-model="paddings.top" 
              size="small" 
              placeholder="上"
              @change="updatePadding"
            />
            <el-input-number 
              v-model="paddings.right" 
              size="small" 
              placeholder="右"
              @change="updatePadding"
            />
            <el-input-number 
              v-model="paddings.bottom" 
              size="small" 
              placeholder="下"
              @change="updatePadding"
            />
            <el-input-number 
              v-model="paddings.left" 
              size="small" 
              placeholder="左"
              @change="updatePadding"
            />
          </div>
        </div>
        
        <div class="property-item">
          <label>边框</label>
          <div class="border-control">
            <el-input-number 
              v-model="properties.borderWidth" 
              size="small" 
              placeholder="宽度"
              :min="0"
              @change="updateBorder"
            />
            <el-select 
              v-model="properties.borderStyle" 
              size="small"
              @change="updateBorder"
            >
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="无边框" value="none" />
            </el-select>
            <el-color-picker 
              v-model="properties.borderColor" 
              size="small"
              @change="updateBorder"
            />
          </div>
        </div>
        
        <div class="property-item">
          <label>圆角</label>
          <el-input-number 
            v-model="properties.borderRadius" 
            size="small"
            :min="0"
            @change="updateProperty('borderRadius', $event + 'px')"
          />
        </div>
      </div>

      <!-- 组件特定属性 -->
      <div class="property-group" v-if="componentSpecificProperties.length > 0">
        <div class="group-title">{{ selectedComponent.type }} 属性</div>
        
        <div 
          v-for="prop in componentSpecificProperties" 
          :key="prop.key"
          class="property-item"
        >
          <label>{{ prop.label }}</label>
          
          <!-- 文本输入 -->
          <el-input 
            v-if="prop.type === 'text'"
            v-model="properties[prop.key]" 
            size="small" 
            :placeholder="prop.placeholder"
            @input="updateProperty(prop.key, $event)"
          />
          
          <!-- 数字输入 -->
          <el-input-number 
            v-else-if="prop.type === 'number'"
            v-model="properties[prop.key]" 
            size="small"
            :min="prop.min"
            :max="prop.max"
            @change="updateProperty(prop.key, $event)"
          />
          
          <!-- 选择器 -->
          <el-select 
            v-else-if="prop.type === 'select'"
            v-model="properties[prop.key]" 
            size="small"
            @change="updateProperty(prop.key, $event)"
          >
            <el-option 
              v-for="option in prop.options" 
              :key="option.value"
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
          
          <!-- 开关 -->
          <el-switch 
            v-else-if="prop.type === 'switch'"
            v-model="properties[prop.key]" 
            size="small"
            @change="updateProperty(prop.key, $event)"
          />
          
          <!-- 颜色选择器 -->
          <el-color-picker 
            v-else-if="prop.type === 'color'"
            v-model="properties[prop.key]" 
            size="small"
            @change="updateProperty(prop.key, $event)"
          />
          
          <!-- 文本域 -->
          <el-input 
            v-else-if="prop.type === 'textarea'"
            v-model="properties[prop.key]" 
            type="textarea"
            size="small"
            :rows="3"
            :placeholder="prop.placeholder"
            @input="updateProperty(prop.key, $event)"
          />
        </div>
      </div>

      <!-- 动画属性 -->
      <div class="property-group">
        <div class="group-title">动画效果</div>
        
        <div class="property-item">
          <label>进入动画</label>
          <el-select 
            v-model="properties.enterAnimation" 
            size="small"
            @change="updateProperty('enterAnimation', $event)"
          >
            <el-option label="无动画" value="none" />
            <el-option label="淡入" value="fadeIn" />
            <el-option label="从左滑入" value="slideInLeft" />
            <el-option label="从右滑入" value="slideInRight" />
            <el-option label="从上滑入" value="slideInUp" />
            <el-option label="从下滑入" value="slideInDown" />
            <el-option label="缩放进入" value="zoomIn" />
            <el-option label="弹跳进入" value="bounceIn" />
          </el-select>
        </div>
        
        <div class="property-item">
          <label>动画延迟</label>
          <el-input-number 
            v-model="properties.animationDelay" 
            size="small"
            :min="0"
            :step="0.1"
            @change="updateProperty('animationDelay', $event + 's')"
          />
        </div>
        
        <div class="property-item">
          <label>动画时长</label>
          <el-input-number 
            v-model="properties.animationDuration" 
            size="small"
            :min="0.1"
            :step="0.1"
            @change="updateProperty('animationDuration', $event + 's')"
          />
        </div>
      </div>

      <!-- 交互属性 -->
      <div class="property-group">
        <div class="group-title">交互设置</div>
        
        <div class="property-item">
          <label>点击事件</label>
          <el-select 
            v-model="properties.clickAction" 
            size="small"
            @change="updateProperty('clickAction', $event)"
          >
            <el-option label="无操作" value="none" />
            <el-option label="跳转链接" value="link" />
            <el-option label="显示弹窗" value="modal" />
            <el-option label="滚动到元素" value="scroll" />
            <el-option label="提交表单" value="submit" />
          </el-select>
        </div>
        
        <div class="property-item" v-if="properties.clickAction === 'link'">
          <label>跳转地址</label>
          <el-input 
            v-model="properties.linkUrl" 
            size="small" 
            placeholder="https://example.com"
            @input="updateProperty('linkUrl', $event)"
          />
        </div>
        
        <div class="property-item">
          <label>悬停效果</label>
          <el-switch 
            v-model="properties.hoverEffect" 
            size="small"
            @change="updateProperty('hoverEffect', $event)"
          />
        </div>
      </div>
    </div>

    <!-- 未选择组件时的提示 -->
    <div v-else class="no-selection">
      <el-icon><Select /></el-icon>
      <p>请选择一个组件来编辑属性</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Refresh, Select } from '@element-plus/icons-vue'

const props = defineProps({
  selectedComponent: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update-property'])

// 基础属性
const properties = reactive({
  id: '',
  name: '',
  className: '',
  width: '',
  height: '',
  backgroundColor: '',
  color: '',
  fontSize: 14,
  fontWeight: 'normal',
  textAlign: 'left',
  borderWidth: 0,
  borderStyle: 'solid',
  borderColor: '#dcdfe6',
  borderRadius: 0,
  enterAnimation: 'none',
  animationDelay: 0,
  animationDuration: 1,
  clickAction: 'none',
  linkUrl: '',
  hoverEffect: false
})

// 外边距和内边距
const margins = reactive({
  top: 0,
  right: 0,
  bottom: 0,
  left: 0
})

const paddings = reactive({
  top: 0,
  right: 0,
  bottom: 0,
  left: 0
})

// 组件特定属性配置
const componentSpecificProperties = computed(() => {
  if (!props.selectedComponent) return []
  
  const componentType = props.selectedComponent.type
  const configs = {
    text: [
      { key: 'content', label: '文本内容', type: 'textarea', placeholder: '请输入文本内容' },
      { key: 'lineHeight', label: '行高', type: 'number', min: 1, max: 3, step: 0.1 }
    ],
    image: [
      { key: 'src', label: '图片地址', type: 'text', placeholder: 'https://example.com/image.jpg' },
      { key: 'alt', label: '替代文本', type: 'text', placeholder: '图片描述' },
      { key: 'objectFit', label: '适应方式', type: 'select', options: [
        { label: '填充', value: 'fill' },
        { label: '包含', value: 'contain' },
        { label: '覆盖', value: 'cover' },
        { label: '缩放', value: 'scale-down' }
      ]}
    ],
    button: [
      { key: 'text', label: '按钮文字', type: 'text', placeholder: '点击按钮' },
      { key: 'buttonType', label: '按钮类型', type: 'select', options: [
        { label: '主要按钮', value: 'primary' },
        { label: '成功按钮', value: 'success' },
        { label: '警告按钮', value: 'warning' },
        { label: '危险按钮', value: 'danger' },
        { label: '信息按钮', value: 'info' }
      ]},
      { key: 'size', label: '按钮大小', type: 'select', options: [
        { label: '大', value: 'large' },
        { label: '默认', value: 'default' },
        { label: '小', value: 'small' }
      ]},
      { key: 'disabled', label: '禁用状态', type: 'switch' }
    ],
    form: [
      { key: 'action', label: '提交地址', type: 'text', placeholder: '/api/submit' },
      { key: 'method', label: '提交方式', type: 'select', options: [
        { label: 'POST', value: 'post' },
        { label: 'GET', value: 'get' }
      ]},
      { key: 'showLabels', label: '显示标签', type: 'switch' }
    ],
    video: [
      { key: 'src', label: '视频地址', type: 'text', placeholder: 'https://example.com/video.mp4' },
      { key: 'poster', label: '封面图片', type: 'text', placeholder: '视频封面地址' },
      { key: 'autoplay', label: '自动播放', type: 'switch' },
      { key: 'controls', label: '显示控制条', type: 'switch' },
      { key: 'loop', label: '循环播放', type: 'switch' }
    ]
  }
  
  return configs[componentType] || []
})

// 监听选中组件变化
watch(() => props.selectedComponent, (newComponent) => {
  if (newComponent) {
    loadComponentProperties(newComponent)
  }
}, { immediate: true })

// 加载组件属性
const loadComponentProperties = (component) => {
  if (!component.properties) return
  
  // 加载基础属性
  Object.keys(properties).forEach(key => {
    if (component.properties[key] !== undefined) {
      properties[key] = component.properties[key]
    }
  })
  
  // 加载边距属性
  if (component.properties.margin) {
    const marginValues = component.properties.margin.split(' ')
    margins.top = parseInt(marginValues[0]) || 0
    margins.right = parseInt(marginValues[1]) || 0
    margins.bottom = parseInt(marginValues[2]) || 0
    margins.left = parseInt(marginValues[3]) || 0
  }
  
  // 加载内边距属性
  if (component.properties.padding) {
    const paddingValues = component.properties.padding.split(' ')
    paddings.top = parseInt(paddingValues[0]) || 0
    paddings.right = parseInt(paddingValues[1]) || 0
    paddings.bottom = parseInt(paddingValues[2]) || 0
    paddings.left = parseInt(paddingValues[3]) || 0
  }
}

// 更新属性
const updateProperty = (key, value) => {
  properties[key] = value
  emit('update-property', key, value)
}

// 更新外边距
const updateMargin = () => {
  const marginValue = `${margins.top}px ${margins.right}px ${margins.bottom}px ${margins.left}px`
  updateProperty('margin', marginValue)
}

// 更新内边距
const updatePadding = () => {
  const paddingValue = `${paddings.top}px ${paddings.right}px ${paddings.bottom}px ${paddings.left}px`
  updateProperty('padding', paddingValue)
}

// 更新边框
const updateBorder = () => {
  const borderValue = `${properties.borderWidth}px ${properties.borderStyle} ${properties.borderColor}`
  updateProperty('border', borderValue)
}

// 重置属性
const resetProperties = () => {
  Object.keys(properties).forEach(key => {
    if (typeof properties[key] === 'string') {
      properties[key] = ''
    } else if (typeof properties[key] === 'number') {
      properties[key] = 0
    } else if (typeof properties[key] === 'boolean') {
      properties[key] = false
    }
  })
  
  Object.keys(margins).forEach(key => {
    margins[key] = 0
  })
  
  Object.keys(paddings).forEach(key => {
    paddings[key] = 0
  })
  
  emit('update-property', 'reset', true)
}
</script>

<style lang="scss" scoped>
.component-properties {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  
  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.properties-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-group {
  margin-bottom: 24px;
  
  .group-title {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.property-item {
  margin-bottom: 16px;
  
  label {
    display: block;
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
  }
  
  .el-input,
  .el-select,
  .el-input-number {
    width: 100%;
  }
  
  .el-color-picker {
    width: 100%;
  }
}

.margin-padding-control {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  
  .el-input-number {
    width: 100%;
  }
}

.border-control {
  display: flex;
  gap: 8px;
  
  .el-input-number {
    flex: 1;
  }
  
  .el-select {
    flex: 1;
  }
  
  .el-color-picker {
    flex: 0 0 auto;
  }
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}
</style>