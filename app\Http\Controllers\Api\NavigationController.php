<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NavigationConfig;
use App\Models\UserNavigationPreference;
use App\Models\NavigationUsageStat;
use App\Models\NavigationSearchLog;
use App\Services\NavigationRecommendationService;
use App\Services\NavigationPermissionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

/**
 * 导航系统API控制器
 * 
 * 提供完整的导航系统功能：
 * - 导航配置管理
 * - 用户偏好设置
 * - 智能搜索推荐
 * - 使用统计分析
 * - 权限控制
 */
class NavigationController extends Controller
{
    protected NavigationRecommendationService $recommendationService;
    protected NavigationPermissionService $permissionService;

    public function __construct(
        NavigationRecommendationService $recommendationService,
        NavigationPermissionService $permissionService
    ) {
        $this->recommendationService = $recommendationService;
        $this->permissionService = $permissionService;
    }

    /**
     * 获取导航配置（支持个性化和权限控制）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getNavigation(Request $request): JsonResponse
    {
        $request->validate([
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'include_hidden' => 'boolean',
            'include_recommendations' => 'boolean'
        ]);

        $user = Auth::user();
        $domain = $request->input('domain');
        $includeHidden = $request->boolean('include_hidden', false);
        $includeRecommendations = $request->boolean('include_recommendations', true);

        try {
            // 获取基础导航树
            $navigation = NavigationConfig::getCachedTree($domain, $user?->id);

            // 应用权限过滤
            if ($user) {
                $navigation = $this->permissionService->filterByPermissions($navigation, $user);
                
                // 应用用户偏好过滤
                if (!$includeHidden) {
                    $navigation = $this->filterHiddenNavigation($navigation, $user->id);
                }

                // 应用个性化排序
                $navigation = $this->applyPersonalizedSorting($navigation, $user->id);
            }

            // 获取推荐导航
            $recommendations = [];
            if ($user && $includeRecommendations) {
                $recommendations = $this->recommendationService->getRecommendations($user->id, $domain);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'navigation' => $navigation,
                    'recommendations' => $recommendations,
                    'user_stats' => $user ? $this->getUserNavigationStats($user->id) : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取导航配置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取导航域配置
     */
    public function getDomains(): JsonResponse
    {
        $domains = [
            [
                'key' => 'business',
                'name' => '业务核心域',
                'description' => '核心业务功能',
                'color' => '#1890ff',
                'icon' => 'business',
                'sort_order' => 1
            ],
            [
                'key' => 'operation',
                'name' => '运营管理域',
                'description' => '运营和管理功能',
                'color' => '#52c41a',
                'icon' => 'operation',
                'sort_order' => 2
            ],
            [
                'key' => 'analytics',
                'name' => '数据分析域',
                'description' => '数据统计和分析',
                'color' => '#f5222d',
                'icon' => 'analytics',
                'sort_order' => 3
            ],
            [
                'key' => 'system',
                'name' => '系统配置域',
                'description' => '系统设置和配置',
                'color' => '#722ed1',
                'icon' => 'system',
                'sort_order' => 4
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $domains
        ]);
    }

    /**
     * 更新用户导航偏好
     */
    public function updatePreference(Request $request): JsonResponse
    {
        $request->validate([
            'navigation_key' => 'required|string',
            'action' => 'required|string|in:favorite,hide,unhide,unfavorite,sort',
            'value' => 'nullable|integer'
        ]);

        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        try {
            $preference = UserNavigationPreference::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'navigation_key' => $request->navigation_key
                ],
                [
                    'visit_count' => 0,
                    'is_favorite' => false,
                    'is_hidden' => false
                ]
            );

            switch ($request->action) {
                case 'favorite':
                    $preference->is_favorite = true;
                    break;
                case 'unfavorite':
                    $preference->is_favorite = false;
                    break;
                case 'hide':
                    $preference->is_hidden = true;
                    break;
                case 'unhide':
                    $preference->is_hidden = false;
                    break;
                case 'sort':
                    $preference->custom_sort = $request->value;
                    break;
            }

            $preference->save();

            // 清除相关缓存
            NavigationConfig::clearCache();

            return response()->json([
                'success' => true,
                'message' => '偏好设置已更新'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '更新偏好设置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 记录导航访问
     */
    public function recordVisit(Request $request): JsonResponse
    {
        $request->validate([
            'navigation_key' => 'required|string',
            'context' => 'nullable|array'
        ]);

        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        try {
            // 更新用户偏好
            $preference = UserNavigationPreference::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'navigation_key' => $request->navigation_key
                ]
            );

            $preference->recordVisit();

            return response()->json([
                'success' => true,
                'message' => '访问已记录'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '记录访问失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 搜索导航
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:1|max:100',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        $user = Auth::user();
        $query = trim($request->query);
        $domain = $request->domain;
        $limit = $request->integer('limit', 20);

        try {
            // 基础搜索
            $searchQuery = NavigationConfig::active()
                ->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('key', 'like', "%{$query}%")
                      ->orWhereJsonContains('meta->keywords', $query);
                });

            if ($domain) {
                $searchQuery->byDomain($domain);
            }

            $results = $searchQuery->limit($limit)->get();

            // 如果有用户，应用权限过滤
            if ($user) {
                $results = $this->permissionService->filterByPermissions($results, $user);
            }

            // 记录搜索日志
            if ($user) {
                NavigationSearchLog::create([
                    'user_id' => $user->id,
                    'query' => $query,
                    'results' => $results->pluck('key')->toArray(),
                    'results_count' => $results->count(),
                    'has_result' => $results->isNotEmpty(),
                    'searched_at' => now(),
                    'ip_address' => $request->ip()
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'results' => $results,
                    'total' => $results->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '搜索失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取智能推荐
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'nullable|string|in:frequent,related,role_based,trending',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'limit' => 'nullable|integer|min:1|max:20'
        ]);

        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        try {
            $recommendations = $this->recommendationService->getRecommendations(
                $user->id,
                $request->domain,
                $request->type,
                $request->integer('limit', 10)
            );

            return response()->json([
                'success' => true,
                'data' => $recommendations
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取推荐失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取用户导航统计
     */
    public function getUserStats(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $days = $request->integer('days', 30);

        try {
            $stats = $this->getUserNavigationStats($user->id, $days);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计数据失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 过滤隐藏的导航
     */
    private function filterHiddenNavigation($navigation, $userId)
    {
        $hiddenKeys = UserNavigationPreference::where('user_id', $userId)
            ->where('is_hidden', true)
            ->pluck('navigation_key')
            ->toArray();

        return $navigation->filter(function ($item) use ($hiddenKeys) {
            return !in_array($item->key, $hiddenKeys);
        });
    }

    /**
     * 应用个性化排序
     */
    private function applyPersonalizedSorting($navigation, $userId)
    {
        $preferences = UserNavigationPreference::where('user_id', $userId)
            ->whereNotNull('custom_sort')
            ->pluck('custom_sort', 'navigation_key')
            ->toArray();

        return $navigation->sortBy(function ($item) use ($preferences) {
            return $preferences[$item->key] ?? $item->sort_order ?? 999;
        })->values();
    }

    /**
     * 获取用户导航统计信息
     */
    private function getUserNavigationStats($userId, $days = 30): array
    {
        $cacheKey = "user_nav_stats:{$userId}:{$days}";

        return Cache::remember($cacheKey, 1800, function () use ($userId, $days) {
            return [
                'popular_pages' => UserNavigationPreference::getPopularForUser($userId, 10),
                'favorites' => UserNavigationPreference::getFavoritesForUser($userId),
                'activity' => NavigationUsageStat::getUserActivityStats($userId, $days),
                'total_visits' => NavigationUsageStat::where('user_id', $userId)
                    ->where('occurred_at', '>=', now()->subDays($days))
                    ->count(),
                'unique_pages' => NavigationUsageStat::where('user_id', $userId)
                    ->where('occurred_at', '>=', now()->subDays($days))
                    ->distinct('navigation_key')
                    ->count()
            ];
        });
    }
}