<template>
  <PageLayout>
    <template #header>
      <div class="page-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">
              <el-icon class="title-icon"><Document /></el-icon>
              支付日志
            </h1>
            <p class="page-description">查看支付回调日志、交易记录和系统日志</p>
          </div>
          <div class="header-actions">
            <el-button @click="refreshLogs" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="exportLogs" type="primary">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <div class="payment-logs">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheckFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.success_count }}</div>
                  <div class="stat-label">成功回调</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon error">
                  <el-icon><CircleCloseFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.failed_count }}</div>
                  <div class="stat-label">失败回调</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.pending_count }}</div>
                  <div class="stat-label">处理中</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon info">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.total_count }}</div>
                  <div class="stat-label">总日志数</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选条件 -->
      <el-card class="filter-card">
        <el-form :model="filters" inline>
          <el-form-item label="日志类型">
            <el-select v-model="filters.type" placeholder="选择类型" clearable>
              <el-option label="支付回调" value="callback" />
              <el-option label="订单创建" value="order_create" />
              <el-option label="支付成功" value="payment_success" />
              <el-option label="支付失败" value="payment_failed" />
              <el-option label="退款" value="refund" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-select v-model="filters.payment_method" placeholder="选择支付方式" clearable>
              <el-option label="微信支付" value="wechat" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="易支付" value="easypay" />
              <el-option label="银行卡" value="bank" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filters.status" placeholder="选择状态" clearable>
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="处理中" value="pending" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.date_range"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="searchLogs" type="primary">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 日志列表 -->
      <el-card class="logs-table-card">
        <el-table
          :data="logs"
          :loading="loading"
          stripe
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="order_no" label="订单号" width="180" />
          <el-table-column prop="payment_method" label="支付方式" width="120">
            <template #default="{ row }">
              <div class="payment-method">
                <img :src="getPaymentIcon(row.payment_method)" :alt="row.payment_method" class="method-icon" />
                {{ getPaymentMethodText(row.payment_method) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="时间" width="180" sortable="custom" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button @click="viewLogDetail(row)" type="primary" link>
                <el-icon><View /></el-icon>
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current_page"
            v-model:page-size="pagination.per_page"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="日志详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ getTypeText(detailDialog.data.type) }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ detailDialog.data.order_no }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPaymentMethodText(detailDialog.data.payment_method) }}</el-descriptions-item>
          <el-descriptions-item label="金额">¥{{ detailDialog.data.amount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(detailDialog.data.status)">
              {{ getStatusText(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailDialog.data.created_at }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ detailDialog.data.updated_at }}</el-descriptions-item>
        </el-descriptions>

        <div class="log-content">
          <h4>请求数据</h4>
          <el-input
            v-model="detailDialog.data.request_data"
            type="textarea"
            :rows="6"
            readonly
            class="log-textarea"
          />
        </div>

        <div class="log-content">
          <h4>响应数据</h4>
          <el-input
            v-model="detailDialog.data.response_data"
            type="textarea"
            :rows="6"
            readonly
            class="log-textarea"
          />
        </div>

        <div v-if="detailDialog.data.error_message" class="log-content">
          <h4>错误信息</h4>
          <el-alert
            :title="detailDialog.data.error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </PageLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document, Refresh, Download, CircleCheckFilled, CircleCloseFilled,
  Clock, Search, RefreshLeft, View
} from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'

// 响应式数据
const loading = ref(false)
const logs = ref([])
const stats = reactive({
  success_count: 0,
  failed_count: 0,
  pending_count: 0,
  total_count: 0
})

const filters = reactive({
  type: '',
  payment_method: '',
  status: '',
  date_range: null
})

const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

const detailDialog = reactive({
  visible: false,
  data: null
})

// 方法
const loadLogs = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    logs.value = [
      {
        id: 1,
        type: 'callback',
        order_no: 'ORDER_20240810001',
        payment_method: 'wechat',
        amount: '99.00',
        status: 'success',
        created_at: '2024-08-10 10:30:00',
        updated_at: '2024-08-10 10:30:05',
        request_data: JSON.stringify({ order_no: 'ORDER_20240810001', amount: 99.00 }, null, 2),
        response_data: JSON.stringify({ code: 0, message: 'success' }, null, 2),
        error_message: null
      },
      {
        id: 2,
        type: 'payment_failed',
        order_no: 'ORDER_20240810002',
        payment_method: 'alipay',
        amount: '199.00',
        status: 'failed',
        created_at: '2024-08-10 09:15:00',
        updated_at: '2024-08-10 09:15:10',
        request_data: JSON.stringify({ order_no: 'ORDER_20240810002', amount: 199.00 }, null, 2),
        response_data: JSON.stringify({ code: -1, message: 'payment failed' }, null, 2),
        error_message: '支付验证失败'
      }
    ]
    
    stats.success_count = 156
    stats.failed_count = 23
    stats.pending_count = 8
    stats.total_count = 187
    
    pagination.total = 187
    
    ElMessage.success('日志加载成功')
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

const refreshLogs = () => {
  loadLogs()
}

const searchLogs = () => {
  pagination.current_page = 1
  loadLogs()
}

const resetFilters = () => {
  Object.assign(filters, {
    type: '',
    payment_method: '',
    status: '',
    date_range: null
  })
  searchLogs()
}

const exportLogs = () => {
  ElMessage.info('导出功能开发中...')
}

const viewLogDetail = (row) => {
  detailDialog.data = row
  detailDialog.visible = true
}

const handleSortChange = ({ prop, order }) => {
  console.log('排序变化:', prop, order)
  loadLogs()
}

const handleSizeChange = (size) => {
  pagination.per_page = size
  loadLogs()
}

const handleCurrentChange = (page) => {
  pagination.current_page = page
  loadLogs()
}

// 辅助方法
const getTypeTagType = (type) => {
  const typeMap = {
    callback: 'primary',
    order_create: 'info',
    payment_success: 'success',
    payment_failed: 'danger',
    refund: 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    callback: '支付回调',
    order_create: '订单创建',
    payment_success: '支付成功',
    payment_failed: '支付失败',
    refund: '退款'
  }
  return typeMap[type] || type
}

const getStatusTagType = (status) => {
  const statusMap = {
    success: 'success',
    failed: 'danger',
    pending: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    pending: '处理中'
  }
  return statusMap[status] || status
}

const getPaymentMethodText = (method) => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    easypay: '易支付',
    bank: '银行卡'
  }
  return methodMap[method] || method
}

const getPaymentIcon = (method) => {
  const iconMap = {
    wechat: '/icons/wechat-pay.png',
    alipay: '/icons/alipay.png',
    easypay: '/icons/easypay.png',
    bank: '/icons/bank.png'
  }
  return iconMap[method] || '/icons/default-pay.png'
}

// 生命周期
onMounted(() => {
  loadLogs()
})
</script>

<style scoped>
.payment-logs {
  padding: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.error {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-icon.info {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
}

.logs-table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 8px;
}

.method-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.amount {
  font-weight: 600;
  color: #f56c6c;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.log-detail {
  max-height: 600px;
  overflow-y: auto;
}

.log-content {
  margin-top: 20px;
}

.log-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.log-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
