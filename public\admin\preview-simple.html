<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkHub Pro 管理后台 - 简化预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            margin-top: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
        }
        .connection-info {
            margin-top: 20px;
            padding: 15px;
            background: #e6f7ff;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .connection-info h4 {
            margin-bottom: 10px;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">LinkHub Pro</div>
        <div class="subtitle">晨鑫流量变现系统管理后台</div>
        
        <div class="connection-info">
            <h4>🎉 连接成功！</h4>
            <p>开发服务器正在正常运行</p>
            <p id="currentUrl"></p>
        </div>
        
        <a href="/?preview=true" class="btn">进入预览模式</a>
        <a href="/" class="btn">正常模式</a>
        
        <div class="status">
            <div>✅ 系统错误已修复</div>
            <div>✅ 用户权限功能正常</div>
            <div>✅ Mock API已启用</div>
            <div>✅ 存储兼容性已修复</div>
        </div>
    </div>
    
    <script>
        // 显示当前URL信息
        document.getElementById('currentUrl').textContent = `当前地址: ${window.location.href}`;
    </script>
</body>
</html>