<?php

namespace App\Services;

use App\Services\AntiBlockService;
use App\Models\DomainPool;
use App\Models\ShortLink;
use App\Models\WechatGroup;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 超级防红服务
 * 整合Laravel域名池 + ThinkPHP混淆技术
 * 
 * <AUTHOR> Enhancement
 * @date 2024-12-19
 */
class UltraAntiBlockService extends AntiBlockService
{
    /**
     * 防检测级别配置
     */
    const LEVEL_LOW = 'low';        // 基础级别
    const LEVEL_MEDIUM = 'medium';   // 标准级别
    const LEVEL_HIGH = 'high';       // 高级级别
    const LEVEL_ULTRA = 'ultra';     // 终极级别
    
    /**
     * 生成终极防检测链接
     * 
     * @param int $groupId 群组ID
     * @param string $originalUrl 原始URL
     * @param string $level 防检测级别
     * @return array 包含多种链接格式
     */
    public function generateUltraSecureLink($groupId, $originalUrl, $level = self::LEVEL_HIGH)
    {
        // 1. 获取最优域名
        $domain = $this->selectOptimalDomain($groupId);
        
        if (!$domain) {
            Log::error('No available domain for group', ['group_id' => $groupId]);
            return [
                'success' => false,
                'message' => '暂无可用域名'
            ];
        }
        
        // 2. 生成短链接
        $shortLink = $this->createShortLink($originalUrl, "Group_{$groupId}", $groupId);
        
        if (!$shortLink) {
            return [
                'success' => false,
                'message' => '短链接生成失败'
            ];
        }
        
        // 3. 根据级别生成不同的链接
        $links = [
            'original' => $originalUrl,
            'short' => $shortLink->getFullUrl(),
        ];
        
        // 4. 添加混淆层（借鉴ThinkPHP）
        if (in_array($level, [self::LEVEL_HIGH, self::LEVEL_ULTRA])) {
            $links['obfuscated'] = $this->obfuscateUrl($shortLink->getFullUrl());
        }
        
        // 5. 添加时间戳验证（终极级别）
        if ($level === self::LEVEL_ULTRA) {
            $links['secure'] = $this->addTimestampVerification($links['obfuscated']);
            $links['encrypted'] = $this->encryptUrl($links['secure']);
        }
        
        // 6. 生成跳转脚本（兼容ThinkPHP的turl方式）
        $links['script'] = $this->generateRedirectScript($shortLink->getFullUrl(), $groupId);
        
        return [
            'success' => true,
            'links' => $links,
            'domain' => $domain->domain,
            'expires_at' => now()->addDays(7)->toDateTimeString()
        ];
    }
    
    /**
     * 智能域名选择算法（增强版）
     * 
     * @param int $groupId
     * @return DomainPool|null
     */
    private function selectOptimalDomain($groupId)
    {
        // 使用复杂的算法选择最优域名
        $query = DomainPool::where('status', 'active')
            ->where('health_score', '>', 70); // 降低阈值以获得更多可用域名
        
        // 1. 首先尝试获取专属域名（基于群组ID哈希）
        $dedicatedDomain = (clone $query)
            ->whereRaw('MOD(?, id) = MOD(id, 10)', [$groupId])
            ->first();
            
        if ($dedicatedDomain) {
            return $dedicatedDomain;
        }
        
        // 2. 获取负载最轻的域名
        $optimalDomain = $query
            ->orderByRaw('
                (health_score * 0.4) + 
                ((100 - LEAST(group_count, 100)) * 0.3) + 
                (CASE 
                    WHEN last_check_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 30
                    WHEN last_check_at > DATE_SUB(NOW(), INTERVAL 6 HOUR) THEN 20
                    WHEN last_check_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 10
                    ELSE 0
                END) DESC
            ')
            ->first();
            
        return $optimalDomain;
    }
    
    /**
     * URL混淆（借鉴ThinkPHP的实现并增强）
     * 
     * @param string $url
     * @return string
     */
    private function obfuscateUrl($url)
    {
        // 多层混淆
        $layers = [
            'a' => base64_encode($url),
            'b' => md5(time() . $url),
            'c' => Str::random(16),
            't' => time(),
            'v' => 2  // 版本号
        ];
        
        // 对数据进行JSON编码和Base64编码
        $obfuscated = base64_encode(json_encode($layers));
        
        // 添加额外的混淆字符
        $obfuscated = str_replace(
            ['=', '+', '/'],
            ['_', '-', '~'],
            $obfuscated
        );
        
        // 生成混淆URL
        return route('anti-block.redirect', [
            'data' => $obfuscated,
            'check' => substr(md5($obfuscated . config('app.key')), 0, 8)
        ]);
    }
    
    /**
     * 添加时间戳验证
     * 
     * @param string $url
     * @return string
     */
    private function addTimestampVerification($url)
    {
        $timestamp = time();
        $expires = $timestamp + 86400; // 24小时有效期
        
        $signature = hash_hmac('sha256', $url . $timestamp . $expires, config('app.key'));
        
        $separator = strpos($url, '?') !== false ? '&' : '?';
        
        return $url . $separator . http_build_query([
            'ts' => $timestamp,
            'exp' => $expires,
            'sig' => substr($signature, 0, 16)
        ]);
    }
    
    /**
     * URL加密（终极防护）
     * 
     * @param string $url
     * @return string
     */
    private function encryptUrl($url)
    {
        $key = substr(md5(config('app.key')), 0, 16);
        $iv = substr(md5(config('app.key')), 16, 16);
        
        $encrypted = openssl_encrypt($url, 'AES-128-CBC', $key, 0, $iv);
        
        return route('anti-block.decrypt', [
            'payload' => urlencode($encrypted)
        ]);
    }
    
    /**
     * 生成跳转脚本（兼容ThinkPHP的turl方式）
     * 
     * @param string $url
     * @param int $groupId
     * @return string
     */
    private function generateRedirectScript($url, $groupId)
    {
        $encoded = base64_encode(urlencode($url));
        
        // 生成类似ThinkPHP的混淆JavaScript
        $script = <<<SCRIPT
<script>
document.title = decodeURIComponent(atob('JUU1JThBJUEwJUU4JUJEJUJEJUU0JUI4JUFELi4u'));
function alcspqandxjmiou1() {
    try {
        setTimeout(function() { gourl(); }, Math.random() * 1000 + 500);
    } catch(e) {
        window.location.href = decodeURIComponent(atob("{$encoded}"));
    }
}
function gourl() {
    window.location.href = decodeURIComponent(atob("{$encoded}"));
}
var _hmt = _hmt || [];
(function() {
    var hm = document.createElement("script");
    hm.src = "/api/anti-block/track/{$groupId}";
    hm.onload = function() { alcspqandxjmiou1(); };
    hm.onerror = function() { gourl(); };
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
})();
</script>
SCRIPT;
        
        return $script;
    }
    
    /**
     * 微信环境智能检测与处理（增强版）
     * 
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function handleWechatEnvironment($request)
    {
        $ua = $request->userAgent();
        $ip = $request->ip();
        
        // 缓存检测结果
        $cacheKey = "wechat_check_{$ip}_" . md5($ua);
        if ($cached = Cache::get($cacheKey)) {
            return $cached;
        }
        
        // 检测各种社交平台
        $platforms = [
            'MicroMessenger' => [
                'name' => '微信',
                'strategy' => 'guide_browser',
                'priority' => 1
            ],
            'QQ/' => [
                'name' => 'QQ',
                'strategy' => 'guide_browser',
                'priority' => 2
            ],
            'Weibo' => [
                'name' => '微博',
                'strategy' => 'direct_jump',
                'priority' => 3
            ],
            'DingTalk' => [
                'name' => '钉钉',
                'strategy' => 'show_qrcode',
                'priority' => 4
            ],
            'TikTok' => [
                'name' => '抖音',
                'strategy' => 'guide_browser',
                'priority' => 5
            ]
        ];
        
        $result = ['allow' => true];
        
        foreach ($platforms as $pattern => $config) {
            if (stripos($ua, $pattern) !== false) {
                $result = $this->executeStrategy($config['strategy'], $config['name']);
                break;
            }
        }
        
        // 缓存结果5分钟
        Cache::put($cacheKey, $result, 300);
        
        return $result;
    }
    
    /**
     * 执行不同的防封策略
     * 
     * @param string $strategy
     * @param string $platform
     * @return array
     */
    private function executeStrategy($strategy, $platform)
    {
        switch ($strategy) {
            case 'guide_browser':
                // 返回引导打开浏览器的页面
                return [
                    'allow' => false,
                    'action' => 'show_guide',
                    'view' => 'anti-block.guide-browser',
                    'data' => [
                        'platform' => $platform,
                        'guide_image' => $this->getGuideImage($platform)
                    ]
                ];
                
            case 'direct_jump':
                // 直接跳转到安全页面
                return [
                    'allow' => false,
                    'action' => 'redirect',
                    'url' => $this->getSafeRedirectUrl()
                ];
                
            case 'show_qrcode':
                // 显示二维码
                return [
                    'allow' => false,
                    'action' => 'show_qrcode',
                    'qrcode' => $this->generateQRCode()
                ];
                
            default:
                return ['allow' => true];
        }
    }
    
    /**
     * 获取引导图片
     * 
     * @param string $platform
     * @return string
     */
    private function getGuideImage($platform)
    {
        $images = [
            '微信' => '/images/guide/wechat-open-browser.png',
            'QQ' => '/images/guide/qq-open-browser.png',
            '抖音' => '/images/guide/tiktok-open-browser.png',
            'default' => '/images/guide/open-browser.png'
        ];
        
        return $images[$platform] ?? $images['default'];
    }
    
    /**
     * 获取安全跳转URL
     * 
     * @return string
     */
    private function getSafeRedirectUrl()
    {
        // 返回一个安全的跳转地址，例如百度
        return 'https://www.baidu.com/s?wd=' . urlencode('请使用浏览器访问');
    }
    
    /**
     * 生成二维码
     * 
     * @return string
     */
    private function generateQRCode()
    {
        // 这里应该生成实际的二维码
        // 暂时返回占位符
        return '/api/qrcode/generate?url=' . urlencode(request()->fullUrl());
    }
    
    /**
     * 批量为群组分配域名
     * 
     * @param array $groupIds
     * @return array
     */
    public function batchAssignDomains(array $groupIds)
    {
        $results = [];
        
        foreach ($groupIds as $groupId) {
            $group = WechatGroup::find($groupId);
            if (!$group) {
                $results[$groupId] = [
                    'success' => false,
                    'message' => '群组不存在'
                ];
                continue;
            }
            
            // 生成防红链接
            $linkResult = $this->generateUltraSecureLink(
                $groupId,
                route('landing.group', ['id' => $groupId]),
                $group->anti_block_level ?? self::LEVEL_HIGH
            );
            
            if ($linkResult['success']) {
                // 更新群组的防红配置
                $group->update([
                    'anti_block_config' => json_encode($linkResult['links']),
                    'primary_domain' => $linkResult['domain'],
                    'links_updated_at' => now()
                ]);
                
                $results[$groupId] = [
                    'success' => true,
                    'links' => $linkResult['links']
                ];
            } else {
                $results[$groupId] = $linkResult;
            }
        }
        
        return $results;
    }
    
    /**
     * 检测并更新域名健康度
     * 
     * @return array
     */
    public function updateDomainsHealth()
    {
        $domains = DomainPool::where('status', 'active')->get();
        $results = [];
        
        foreach ($domains as $domain) {
            $checkResult = $this->performDomainCheck($domain->domain);
            
            // 更新健康度分数
            $domain->update([
                'health_score' => $checkResult['health_score'],
                'status' => $checkResult['status'] == 1 ? 'active' : 'inactive',
                'last_check_at' => now(),
                'check_result' => $checkResult['message']
            ]);
            
            $results[] = [
                'domain' => $domain->domain,
                'health_score' => $checkResult['health_score'],
                'status' => $checkResult['status'],
                'message' => $checkResult['message']
            ];
            
            // 记录检测日志
            $this->logCheckResult($domain->id, $checkResult['status'], $checkResult['message']);
        }
        
        return $results;
    }
    
    /**
     * 获取防红系统实时状态
     * 
     * @return array
     */
    public function getSystemStatus()
    {
        $stats = parent::getStats();
        
        // 添加额外的统计信息
        $stats['health_distribution'] = [
            'excellent' => DomainPool::where('health_score', '>=', 90)->count(),
            'good' => DomainPool::whereBetween('health_score', [70, 89])->count(),
            'fair' => DomainPool::whereBetween('health_score', [50, 69])->count(),
            'poor' => DomainPool::where('health_score', '<', 50)->count()
        ];
        
        $stats['recent_blocks'] = \App\Models\DomainCheckLog::where('status', 3)
            ->where('created_at', '>=', now()->subDays(7))
            ->count();
            
        $stats['avg_health_score'] = DomainPool::where('status', 'active')
            ->avg('health_score');
            
        $stats['protection_level'] = $this->calculateProtectionLevel($stats);
        
        return $stats;
    }
    
    /**
     * 计算系统保护级别
     * 
     * @param array $stats
     * @return string
     */
    private function calculateProtectionLevel($stats)
    {
        $score = 0;
        
        // 根据各项指标计算得分
        if ($stats['active_domains'] > 10) $score += 30;
        elseif ($stats['active_domains'] > 5) $score += 20;
        elseif ($stats['active_domains'] > 0) $score += 10;
        
        if ($stats['avg_health_score'] > 80) $score += 30;
        elseif ($stats['avg_health_score'] > 60) $score += 20;
        elseif ($stats['avg_health_score'] > 40) $score += 10;
        
        if ($stats['recent_blocks'] < 5) $score += 40;
        elseif ($stats['recent_blocks'] < 10) $score += 30;
        elseif ($stats['recent_blocks'] < 20) $score += 20;
        else $score += 10;
        
        // 返回保护级别
        if ($score >= 80) return '极高';
        if ($score >= 60) return '高';
        if ($score >= 40) return '中';
        if ($score >= 20) return '低';
        return '极低';
    }
}