<template>
  <div class="video-preview">
    <div class="video-container">
      <div class="video-header">
        <h3 class="video-title">介绍视频</h3>
      </div>
      
      <div v-if="videoData" class="video-content">
        <div class="video-player">
          <video
            v-if="isLocalVideo"
            :src="videoData.url"
            :poster="videoData.poster"
            :controls="showControls"
            :autoplay="autoplay"
            :loop="loop"
            :muted="muted"
            class="video-element"
            @loadedmetadata="handleVideoLoaded"
          >
            您的浏览器不支持视频播放
          </video>
          
          <div v-else class="video-embed">
            <iframe
              :src="getEmbedUrl(videoData.url)"
              frameborder="0"
              allowfullscreen
              class="embed-iframe"
            ></iframe>
          </div>
        </div>
        
        <!-- 视频信息 -->
        <div class="video-info">
          <div v-if="videoData.title" class="video-title-text">
            {{ videoData.title }}
          </div>
          <div class="video-meta">
            <span v-if="videoDuration" class="meta-item">
              <el-icon><Clock /></el-icon>
              {{ formatDuration(videoDuration) }}
            </span>
            <span class="meta-item">
              <el-icon><VideoPlay /></el-icon>
              {{ videoSource }}
            </span>
          </div>
        </div>
      </div>
      
      <div v-else class="video-placeholder">
        <el-icon class="placeholder-icon"><VideoPlay /></el-icon>
        <div class="placeholder-text">暂无介绍视频</div>
        <div class="placeholder-hint">请上传或添加视频链接</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { VideoPlay, Clock } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const videoDuration = ref(0)

// 计算属性
const videoData = computed(() => {
  const video = props.groupData.intro_video
  if (typeof video === 'string') {
    return { url: video }
  }
  return video || null
})

const isLocalVideo = computed(() => {
  if (!videoData.value) return false
  const url = videoData.value.url
  return !url.includes('qq.com') && 
         !url.includes('youku.com') && 
         !url.includes('bilibili.com') && 
         !url.includes('youtube.com')
})

const showControls = computed(() => {
  return props.section.config?.controls !== false
})

const autoplay = computed(() => {
  return props.section.config?.autoplay === true
})

const loop = computed(() => {
  return props.section.config?.loop === true
})

const muted = computed(() => {
  return props.section.config?.muted === true
})

const videoSource = computed(() => {
  if (!videoData.value) return ''
  
  const url = videoData.value.url
  if (url.includes('qq.com')) return '腾讯视频'
  if (url.includes('youku.com')) return '优酷'
  if (url.includes('iqiyi.com')) return '爱奇艺'
  if (url.includes('bilibili.com')) return 'B站'
  if (url.includes('youtube.com')) return 'YouTube'
  return '本地视频'
})

// 方法
const getEmbedUrl = (url) => {
  // 转换各平台视频链接为嵌入链接
  if (url.includes('bilibili.com')) {
    const bvMatch = url.match(/BV[a-zA-Z0-9]+/)
    if (bvMatch) {
      return `//player.bilibili.com/player.html?bvid=${bvMatch[0]}&page=1`
    }
  }
  
  if (url.includes('youtube.com')) {
    const videoId = url.match(/v=([^&]+)/)
    if (videoId) {
      return `//www.youtube.com/embed/${videoId[1]}`
    }
  }
  
  // 其他平台的处理...
  return url
}

const handleVideoLoaded = (event) => {
  videoDuration.value = event.target.duration
}

const formatDuration = (seconds) => {
  if (!seconds) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}
</script>

<style lang="scss" scoped>
.video-preview {
  .video-container {
    padding: 20px;
    
    .video-header {
      margin-bottom: 16px;
      
      .video-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #e6a23c;
        display: inline-block;
      }
    }
    
    .video-content {
      .video-player {
        position: relative;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        background: #000;
        margin-bottom: 12px;
        
        .video-element {
          width: 100%;
          height: auto;
          max-height: 300px;
        }
        
        .video-embed {
          position: relative;
          width: 100%;
          height: 0;
          padding-bottom: 56.25%; // 16:9 aspect ratio
          
          .embed-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }
      }
      
      .video-info {
        .video-title-text {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
        }
        
        .video-meta {
          display: flex;
          gap: 16px;
          font-size: 14px;
          color: #909399;
          
          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            
            .el-icon {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .video-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #909399;
      text-align: center;
      border: 2px dashed #dcdfe6;
      border-radius: 8px;
      
      .placeholder-icon {
        font-size: 32px;
        margin-bottom: 12px;
      }
      
      .placeholder-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .placeholder-hint {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
}
</style>
