/* 数据大屏响应式优化样式 */

/* 全局重置 */
.data-screen-fullscreen {
  margin: 0 !important;
  padding: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
}

/* 超大屏幕 (>2560px) 4K显示器优化 */
@media (min-width: 2561px) {
  .data-screen,
  .ultra-data-screen,
  .enhanced-data-screen,
  .optimized-data-screen {
    font-size: 18px;
  }
  
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    padding: 32px 80px;
  }
  
  .charts-section,
  .charts-grid {
    gap: 32px;
    padding: 0 80px 32px;
  }
  
  .metric-card {
    padding: 32px;
    min-height: 180px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 450px;
  }
  
  .screen-header {
    padding: 24px 80px;
    height: 100px;
  }
  
  .main-title,
  .screen-title {
    font-size: 36px;
  }
}

/* 大屏幕 (1920px-2560px) 标准大屏优化 */
@media (min-width: 1921px) and (max-width: 2560px) {
  .data-screen,
  .ultra-data-screen,
  .enhanced-data-screen,
  .optimized-data-screen {
    font-size: 16px;
  }
  
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
    padding: 28px 60px;
  }
  
  .charts-section,
  .charts-grid {
    gap: 28px;
    padding: 0 60px 28px;
  }
  
  .metric-card {
    padding: 28px;
    min-height: 160px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 400px;
  }
  
  .screen-header {
    padding: 20px 60px;
    height: 90px;
  }
  
  .main-title,
  .screen-title {
    font-size: 32px;
  }
}

/* 标准屏幕 (1600px-1920px) */
@media (min-width: 1601px) and (max-width: 1920px) {
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 24px 40px;
  }
  
  .charts-section,
  .charts-grid {
    gap: 24px;
    padding: 0 40px 24px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 350px;
  }
}

/* 中大屏幕 (1400px-1600px) */
@media (min-width: 1401px) and (max-width: 1600px) {
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px 32px;
  }
  
  .charts-section,
  .charts-grid {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 20px;
    padding: 0 32px 20px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 320px;
  }
}

/* 中等屏幕 (1200px-1400px) */
@media (min-width: 1201px) and (max-width: 1400px) {
  .data-screen,
  .ultra-data-screen,
  .enhanced-data-screen,
  .optimized-data-screen {
    overflow-y: auto;
  }
  
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
    padding: 18px 28px;
  }
  
  .charts-section,
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 18px;
    padding: 0 28px 18px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 280px;
  }
}

/* 小屏幕 (768px-1200px) */
@media (min-width: 769px) and (max-width: 1200px) {
  .data-screen,
  .ultra-data-screen,
  .enhanced-data-screen,
  .optimized-data-screen {
    overflow-y: auto;
  }
  
  .screen-header {
    padding: 16px 24px;
    height: 70px;
  }
  
  .metrics-section,
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px 24px;
  }
  
  .charts-section,
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 24px 16px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 250px;
  }
  
  .main-title,
  .screen-title {
    font-size: 22px;
  }
}

/* 移动端 (<768px) */
@media (max-width: 768px) {
  .data-screen,
  .ultra-data-screen,
  .enhanced-data-screen,
  .optimized-data-screen {
    overflow-y: auto;
  }
  
  .screen-header {
    flex-direction: column;
    padding: 12px 16px;
    height: auto;
    min-height: 60px;
    text-align: center;
  }
  
  .metrics-section,
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .charts-section,
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 16px 12px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 220px;
  }
  
  .metric-card {
    padding: 16px;
  }
  
  .main-title,
  .screen-title {
    font-size: 18px;
  }
  
  .bottom-metrics,
  .screen-footer {
    height: 40px;
    padding: 6px 16px;
  }
}

/* 超小屏幕 (<480px) */
@media (max-width: 480px) {
  .screen-content {
    padding: 12px;
  }
  
  .metrics-grid {
    gap: 8px;
    padding: 8px 12px;
  }
  
  .charts-grid {
    gap: 8px;
    padding: 0 12px 8px;
  }
  
  .metric-card {
    padding: 12px;
  }
  
  .chart-container,
  .chart-card {
    min-height: 200px;
  }
}

/* 全屏模式优化 */
.fullscreen-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 防止内容溢出 */
.data-screen *,
.ultra-data-screen *,
.enhanced-data-screen *,
.optimized-data-screen * {
  box-sizing: border-box;
}

/* 确保图表容器正确缩放 */
.chart-content canvas {
  max-width: 100% !important;
  max-height: 100% !important;
}
