<template>
  <div class="bottom-tab-bar" :class="{ 'safe-area': isIOS }">
    <div class="tab-bar-container">
      <div
        v-for="tab in tabs"
        :key="tab.path"
        class="tab-item"
        :class="{ active: isActive(tab.path) }"
        @click="handleTabClick(tab)"
        @touchstart="handleTouchStart($event, tab)"
        @touchend="handleTouchEnd"
      >
        <div class="tab-icon" :class="{ 'has-badge': tab.badge }">
          <Icon :name="tab.icon" :size="24" />
          <span v-if="tab.badge" class="badge">{{ tab.badge }}</span>
        </div>
        <span class="tab-label">{{ tab.label }}</span>
      </div>
    </div>
  
    <!-- 长按菜单 -->
    <Transition name="fade">
      <div v-if="longPressMenu.show" class="long-press-menu" :style="menuStyle">
        <div
          v-for="action in longPressMenu.actions"
          :key="action.id"
          class="menu-item"
          @click="handleMenuAction(action)"
        >
          <Icon :name="action.icon" :size="20" />
          <span>{{ action.label }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from '#app'
import { useResponsive } from '~/composables/useResponsive'

interface Tab {
  path: string
  icon: string
  label: string
  badge?: number | string
  longPressActions?: LongPressAction[]
}

interface LongPressAction {
  id: string
  label: string
  icon: string
  action: () => void
}

const props = defineProps<{
  tabs: Tab[]
}>()

const router = useRouter()
const route = useRoute()
const { isIOS } = useResponsive()

const longPressMenu = ref({
  show: false,
  x: 0,
  y: 0,
  actions: [] as LongPressAction[]
})

const menuStyle = computed(() => ({
  left: `${longPressMenu.value.x}px`,
  top: `${longPressMenu.value.y}px`
}))

const isActive = (path: string) => {
  return route.path === path || route.path.startsWith(path + '/')
}

const handleTabClick = (tab: Tab) => {
  router.push(tab.path)
}

let longPressTimer: NodeJS.Timeout | null = null

const handleTouchStart = (event: TouchEvent, tab: Tab) => {
  if (tab.longPressActions?.length) {
    longPressTimer = setTimeout(() => {
      const touch = event.touches[0]
      longPressMenu.value = {
        show: true,
        x: touch.clientX,
        y: touch.clientY - 100, // 向上偏移避免遮挡
        actions: tab.longPressActions
      }
    }, 500) // 500ms长按触发
  }
}

const handleTouchEnd = () => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
}

const handleMenuAction = (action: LongPressAction) => {
  action.action()
  longPressMenu.value.show = false
}

// 点击外部关闭菜单
const closeMenu = (event: MouseEvent) => {
  if (!event.target?.closest('.long-press-menu')) {
    longPressMenu.value.show = false
  }
}

if (process.client) {
  document.addEventListener('click', closeMenu)
}
</script>

<style scoped>
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #eee;
  backdrop-filter: blur(10px);
  z-index: 1000;
  height: 56px;
}

.bottom-tab-bar.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-container {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: space-around;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-item.active {
  color: #007AFF;
}

.tab-icon {
  position: relative;
  margin-bottom: 2px;
}

.tab-label {
  font-size: 12px;
  line-height: 1.2;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF3B30;
  color: white;
  border-radius: 50%;
  min-width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

.long-press-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 140px;
  z-index: 1001;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-item span {
  font-size: 14px;
  color: #333;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 防止内容被底部栏遮挡 */
body {
  padding-bottom: 56px;
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  body {
    padding-bottom: calc(56px + env(safe-area-inset-bottom));
  }
}
</style>