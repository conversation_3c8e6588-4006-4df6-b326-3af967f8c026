<template>
  <div class="commission-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">佣金管理</h1>
          <p class="page-subtitle">管理代理商佣金计算、结算和提现</p>
        </div>
        <div class="header-actions">
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button type="success" @click="showBatchSettleDialog">
            <el-icon><Money /></el-icon>
            批量结算
          </el-button>
          <el-button type="primary" @click="showWithdrawDialog">
            <el-icon><CreditCard /></el-icon>
            提现申请
          </el-button>
        </div>
      </div>
    </div>

    <!-- 佣金统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ commissionStats.totalCommission }}</div>
          <div class="stat-label">累计佣金</div>
          <div class="stat-trend">
            <span class="trend-text">较上月 +{{ commissionStats.monthGrowth }}%</span>
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon available">
          <el-icon><Wallet /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ commissionStats.availableCommission }}</div>
          <div class="stat-label">可提现佣金</div>
          <div class="stat-trend">
            <span class="trend-text">待结算 ¥{{ commissionStats.pendingCommission }}</span>
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon withdrawn">
          <el-icon><CreditCard /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ commissionStats.withdrawnCommission }}</div>
          <div class="stat-label">已提现金额</div>
          <div class="stat-trend">
            <span class="trend-text">本月 ¥{{ commissionStats.monthWithdrawn }}</span>
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ commissionStats.pendingWithdraws }}</div>
          <div class="stat-label">待审核提现</div>
          <div class="stat-trend">
            <span class="trend-text">金额 ¥{{ commissionStats.pendingAmount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-card class="main-card" shadow="never">
      <el-tabs v-model="activeTab" class="commission-tabs">
        <!-- 佣金记录 -->
        <el-tab-pane label="佣金记录" name="records">
          <div class="tab-content">
            <!-- 筛选条件 -->
            <div class="filter-bar">
              <el-form :model="recordFilters" inline class="filter-form">
                <el-form-item label="时间范围">
                  <el-date-picker
                    v-model="recordFilters.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
                <el-form-item label="佣金类型">
                  <el-select v-model="recordFilters.type" placeholder="全部类型" clearable>
                    <el-option label="直推佣金" value="direct" />
                    <el-option label="团队佣金" value="team" />
                    <el-option label="管理奖励" value="management" />
                    <el-option label="特殊奖励" value="special" />
                  </el-select>
                </el-form-item>
                <el-form-item label="状态">
                  <el-select v-model="recordFilters.status" placeholder="全部状态" clearable>
                    <el-option label="待结算" value="pending" />
                    <el-option label="已结算" value="settled" />
                    <el-option label="已提现" value="withdrawn" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchRecords">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="resetRecordFilters">
                    <el-icon><RefreshLeft /></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 佣金记录表格 -->
            <el-table :data="commissionRecords" v-loading="loading" class="records-table">
              <el-table-column prop="order_no" label="订单号" width="140">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewOrderDetail(row.order_no)">
                    {{ row.order_no }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="commission_type" label="佣金类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getCommissionTypeTag(row.commission_type)" size="small">
                    {{ getCommissionTypeText(row.commission_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="佣金金额" width="120" align="right">
                <template #default="{ row }">
                  <span class="commission-amount">¥{{ row.amount }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="rate" label="佣金比例" width="100" align="center">
                <template #default="{ row }">
                  <span class="commission-rate">{{ row.rate }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="source_agent" label="来源代理" width="120">
                <template #default="{ row }">
                  <div class="agent-info">
                    <el-avatar :size="24" :src="row.source_agent?.avatar">
                      {{ row.source_agent?.name?.charAt(0) }}
                    </el-avatar>
                    <span>{{ row.source_agent?.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusTag(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="产生时间" width="140">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="settled_at" label="结算时间" width="140">
                <template #default="{ row }">
                  {{ row.settled_at ? formatDateTime(row.settled_at) : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewCommissionDetail(row)">
                    详情
                  </el-button>
                  <el-button 
                    v-if="row.status === 'pending'" 
                    size="small" 
                    type="success" 
                    link 
                    @click="settleCommission(row)"
                  >
                    结算
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="recordPagination.current"
                v-model:page-size="recordPagination.size"
                :total="recordPagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleRecordSizeChange"
                @current-change="handleRecordCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>

        <!-- 提现记录 -->
        <el-tab-pane label="提现记录" name="withdrawals">
          <div class="tab-content">
            <!-- 提现记录表格 -->
            <el-table :data="withdrawalRecords" v-loading="loading" class="withdrawals-table">
              <el-table-column prop="withdraw_no" label="提现单号" width="140" />
              <el-table-column prop="amount" label="提现金额" width="120" align="right">
                <template #default="{ row }">
                  <span class="withdraw-amount">¥{{ row.amount }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="bank_info" label="收款账户" min-width="200">
                <template #default="{ row }">
                  <div class="bank-info">
                    <div>{{ row.bank_info?.bank_name }}</div>
                    <div class="account-no">{{ row.bank_info?.account_no }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getWithdrawStatusTag(row.status)" size="small">
                    {{ getWithdrawStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="申请时间" width="140">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="processed_at" label="处理时间" width="140">
                <template #default="{ row }">
                  {{ row.processed_at ? formatDateTime(row.processed_at) : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="primary" link @click="viewWithdrawDetail(row)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 佣金规则 -->
        <el-tab-pane label="佣金规则" name="rules">
          <div class="tab-content">
            <div class="rules-content">
              <h3>佣金计算规则</h3>
              <p>这里将显示佣金计算规则和配置</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Money, CreditCard, Wallet, Clock,
  Search, RefreshLeft
} from '@element-plus/icons-vue'

// 页面状态
const loading = ref(false)
const activeTab = ref('records')

// 对话框状态
const showBatchDialog = ref(false)
const showWithdrawFormDialog = ref(false)
const showOrderDetailDialog = ref(false)
const showCommissionDetailDialog = ref(false)
const showWithdrawDetailDialog = ref(false)

// 选中数据
const selectedOrderNo = ref('')
const selectedCommissionRecord = ref(null)
const selectedWithdrawRecord = ref(null)
const batchSettleData = ref({ records: [], totalAmount: 0 })

// 表单数据
const withdrawForm = ref({
  amount: '',
  bank_name: '',
  account_name: '',
  account_no: '',
  remark: ''
})

// 佣金统计数据
const commissionStats = reactive({
  totalCommission: 25680,
  availableCommission: 8900,
  withdrawnCommission: 16780,
  pendingCommission: 1200,
  monthGrowth: 15.6,
  monthWithdrawn: 3200,
  pendingWithdraws: 3,
  pendingAmount: 2400
})

// 佣金记录
const commissionRecords = ref([])
const recordFilters = reactive({
  dateRange: null,
  type: '',
  status: ''
})

const recordPagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 提现记录
const withdrawalRecords = ref([])

// 方法
const refreshData = async () => {
  await loadCommissionRecords()
  await loadWithdrawalRecords()
}

const showBatchSettleDialog = () => {
  // 检查是否有待结算的佣金
  const pendingRecords = commissionRecords.value.filter(record => record.status === 'pending')
  if (pendingRecords.length === 0) {
    ElMessage.warning('没有待结算的佣金记录')
    return
  }

  batchSettleData.value = {
    records: pendingRecords,
    totalAmount: pendingRecords.reduce((sum, record) => sum + record.amount, 0)
  }
  showBatchDialog.value = true
}

const showWithdrawDialog = () => {
  // 检查可提现金额
  if (commissionStats.availableCommission <= 0) {
    ElMessage.warning('当前没有可提现的佣金')
    return
  }

  withdrawForm.value = {
    amount: '',
    bank_name: '',
    account_name: '',
    account_no: '',
    remark: ''
  }
  showWithdrawFormDialog.value = true
}

const searchRecords = () => {
  recordPagination.current = 1
  loadCommissionRecords()
}

const resetRecordFilters = () => {
  Object.assign(recordFilters, {
    dateRange: null,
    type: '',
    status: ''
  })
  searchRecords()
}

const handleRecordSizeChange = (size) => {
  recordPagination.size = size
  loadCommissionRecords()
}

const handleRecordCurrentChange = (current) => {
  recordPagination.current = current
  loadCommissionRecords()
}

const viewOrderDetail = (orderNo) => {
  // 显示订单详情对话框
  selectedOrderNo.value = orderNo
  loadOrderDetail(orderNo)
  showOrderDetailDialog.value = true
}

const viewCommissionDetail = (record) => {
  // 显示佣金详情对话框
  selectedCommissionRecord.value = record
  showCommissionDetailDialog.value = true
}

const settleCommission = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要结算佣金 ¥${record.amount} 吗？`,
      '确认结算',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('佣金结算成功')
    await loadCommissionRecords()
  } catch {
    ElMessage.info('已取消结算')
  }
}

const viewWithdrawDetail = (record) => {
  // 显示提现详情对话框
  selectedWithdrawRecord.value = record
  showWithdrawDetailDialog.value = true
}

const getCommissionTypeTag = (type) => {
  const tagMap = {
    direct: 'success',
    team: 'primary',
    management: 'warning',
    special: 'danger'
  }
  return tagMap[type] || 'info'
}

const getCommissionTypeText = (type) => {
  const textMap = {
    direct: '直推佣金',
    team: '团队佣金',
    management: '管理奖励',
    special: '特殊奖励'
  }
  return textMap[type] || '未知类型'
}

const getStatusTag = (status) => {
  const tagMap = {
    pending: 'warning',
    settled: 'success',
    withdrawn: 'info'
  }
  return tagMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '待结算',
    settled: '已结算',
    withdrawn: '已提现'
  }
  return textMap[status] || '未知状态'
}

const getWithdrawStatusTag = (status) => {
  const tagMap = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    rejected: 'danger'
  }
  return tagMap[status] || 'info'
}

const getWithdrawStatusText = (status) => {
  const textMap = {
    pending: '待审核',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知状态'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 新增功能方法
const loadOrderDetail = async (orderNo) => {
  try {
    // TODO: 调用API获取订单详情
    await new Promise(resolve => setTimeout(resolve, 500))
    console.log('加载订单详情:', orderNo)
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  }
}

const handleBatchSettle = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量结算 ${batchSettleData.value.records.length} 条佣金记录吗？总金额：¥${batchSettleData.value.totalAmount}`,
      '确认批量结算',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用API批量结算
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('批量结算成功')
    showBatchDialog.value = false
    await loadCommissionRecords()
  } catch {
    ElMessage.info('已取消批量结算')
  }
}

const handleWithdrawSubmit = async () => {
  try {
    // TODO: 表单验证和API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('提现申请提交成功')
    showWithdrawFormDialog.value = false
    await loadWithdrawalRecords()
  } catch (error) {
    console.error('提现申请失败:', error)
    ElMessage.error('提现申请失败')
  }
}

const loadCommissionRecords = async () => {
  loading.value = true
  
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    commissionRecords.value = [
      {
        id: 1,
        order_no: 'ORD202401001',
        commission_type: 'direct',
        amount: 150.00,
        rate: 15,
        source_agent: {
          name: '李四',
          avatar: ''
        },
        status: 'settled',
        created_at: '2024-01-15 10:30:00',
        settled_at: '2024-01-16 09:00:00'
      },
      {
        id: 2,
        order_no: 'ORD202401002',
        commission_type: 'team',
        amount: 80.00,
        rate: 8,
        source_agent: {
          name: '王五',
          avatar: ''
        },
        status: 'pending',
        created_at: '2024-01-16 14:20:00',
        settled_at: null
      }
    ]
    
    recordPagination.total = 2
  } catch (error) {
    console.error('加载佣金记录失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadWithdrawalRecords = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    withdrawalRecords.value = [
      {
        id: 1,
        withdraw_no: 'WD202401001',
        amount: 1000.00,
        bank_info: {
          bank_name: '中国工商银行',
          account_no: '****1234'
        },
        status: 'completed',
        created_at: '2024-01-10 16:00:00',
        processed_at: '2024-01-11 10:30:00'
      },
      {
        id: 2,
        withdraw_no: 'WD202401002',
        amount: 500.00,
        bank_info: {
          bank_name: '中国建设银行',
          account_no: '****5678'
        },
        status: 'pending',
        created_at: '2024-01-15 09:20:00',
        processed_at: null
      }
    ]
  } catch (error) {
    console.error('加载提现记录失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.commission-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.available {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.withdrawn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
}

.trend-text {
  color: #059669;
  background: #ecfdf5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 主卡片 */
.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.commission-tabs {
  --el-tabs-header-height: 48px;
}

.tab-content {
  padding: 20px 0;
}

/* 筛选条件 */
.filter-bar {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 表格样式 */
.records-table,
.withdrawals-table {
  --el-table-border-color: #e5e7eb;
  --el-table-bg-color: white;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.commission-amount {
  font-weight: 600;
  color: #059669;
}

.commission-rate {
  font-weight: 500;
  color: #7c3aed;
}

.withdraw-amount {
  font-weight: 600;
  color: #dc2626;
}

.bank-info {
  .account-no {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
  }
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 佣金规则 */
.rules-content {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;

  h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .commission-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filter-form {
    .el-form-item {
      margin-right: 0;
      margin-bottom: 16px;
    }
  }
}
</style>
