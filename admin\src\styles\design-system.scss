// 设计系统 - 统一UI规范
// ================================

// 1. 设计令牌 (Design Tokens)
// ================================

// 颜色系统
:root {
  // 主色调
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  // 辅助色调
  --secondary-50: #fafafa;
  --secondary-100: #f4f4f5;
  --secondary-200: #e4e4e7;
  --secondary-300: #d4d4d8;
  --secondary-400: #a1a1aa;
  --secondary-500: #71717a;
  --secondary-600: #52525b;
  --secondary-700: #3f3f46;
  --secondary-800: #27272a;
  --secondary-900: #18181b;

  // 功能色彩
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  --info-50: #eff6ff;
  --info-100: #dbeafe;
  --info-200: #bfdbfe;
  --info-300: #93c5fd;
  --info-400: #60a5fa;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;
  --info-800: #1e40af;
  --info-900: #1e3a8a;

  // 中性色 - 优化对比度
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // 文字颜色 - 确保可读性
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  --text-inverse: #ffffff;

  // 渐变色
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-400) 0%, var(--success-600) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-400) 0%, var(--warning-600) 100%);
  --gradient-error: linear-gradient(135deg, var(--error-400) 0%, var(--error-600) 100%);
  --gradient-rainbow: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);

  // 间距系统
  --spacing-xs: 0.25rem;   // 4px
  --spacing-sm: 0.5rem;    // 8px
  --spacing-md: 0.75rem;   // 12px
  --spacing-lg: 1rem;      // 16px
  --spacing-xl: 1.5rem;    // 24px
  --spacing-2xl: 2rem;     // 32px
  --spacing-3xl: 3rem;     // 48px
  --spacing-4xl: 4rem;     // 64px
  --spacing-5xl: 6rem;     // 96px

  // 圆角系统
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-full: 9999px;

  // 阴影系统
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  // 字体系统
  --font-family-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 0.75rem;     // 12px
  --font-size-sm: 0.875rem;    // 14px
  --font-size-base: 1rem;      // 16px
  --font-size-lg: 1.125rem;    // 18px
  --font-size-xl: 1.25rem;     // 20px
  --font-size-2xl: 1.5rem;     // 24px
  --font-size-3xl: 1.875rem;   // 30px
  --font-size-4xl: 2.25rem;    // 36px
  --font-size-5xl: 3rem;       // 48px

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  // 动画系统
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  // Z-index 层级
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 2. 基础组件样式
// ================================

// 现代卡片 - 优化配色和对比度
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  color: var(--text-primary); // 确保文字可读性

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2xl);
  }

  // 白色背景上的文字优化
  .text-white {
    color: var(--text-primary) !important;
  }

  .text-light {
    color: var(--text-secondary) !important;
  }
  
  &.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  &.dark {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
  }
}

// 现代按钮
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover::before {
      left: -100%;
    }
  }
  
  // 按钮变体
  &.primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
    
    &:hover {
      background: var(--gray-200);
      border-color: var(--gray-300);
    }
  }
  
  &.success {
    background: var(--gradient-success);
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &.warning {
    background: var(--gradient-warning);
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &.error {
    background: var(--gradient-error);
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &.ghost {
    background: transparent;
    color: var(--primary-600);
    border: 1px solid var(--primary-200);
    
    &:hover {
      background: var(--primary-50);
      border-color: var(--primary-300);
    }
  }
  
  &.link {
    background: transparent;
    color: var(--primary-600);
    padding: var(--spacing-sm);
    
    &:hover {
      color: var(--primary-700);
      text-decoration: underline;
    }
  }
  
  // 按钮尺寸
  &.xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
  
  &.sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
  
  &.lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
  }
  
  &.xl {
    padding: var(--spacing-xl) var(--spacing-3xl);
    font-size: var(--font-size-xl);
  }
}

// 现代输入框
.modern-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
  }
  
  &:disabled {
    background: var(--gray-50);
    color: var(--gray-400);
    cursor: not-allowed;
  }
  
  &.error {
    border-color: var(--error-500);
    
    &:focus {
      border-color: var(--error-500);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
  
  &.success {
    border-color: var(--success-500);
    
    &:focus {
      border-color: var(--success-500);
      box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    }
  }
}

// 现代标签
.modern-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  border: 1px solid transparent;
  
  &.primary {
    background: var(--primary-100);
    color: var(--primary-800);
    border-color: var(--primary-200);
  }
  
  &.success {
    background: var(--success-100);
    color: var(--success-800);
    border-color: var(--success-200);
  }
  
  &.warning {
    background: var(--warning-100);
    color: var(--warning-800);
    border-color: var(--warning-200);
  }
  
  &.error {
    background: var(--error-100);
    color: var(--error-800);
    border-color: var(--error-200);
  }
  
  &.info {
    background: var(--info-100);
    color: var(--info-800);
    border-color: var(--info-200);
  }
  
  &.gray {
    background: var(--gray-100);
    color: var(--gray-800);
    border-color: var(--gray-200);
  }
}

// 3. 布局组件
// ================================

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: var(--spacing-xl);
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-header {
  margin-bottom: var(--spacing-2xl);
  
  .title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
  }
  
  .subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: var(--line-height-relaxed);
  }
}

// 4. 动画效果
// ================================

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 动画类
.animate-fade-in-up {
  animation: fadeInUp 0.6s var(--ease-out) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s var(--ease-out) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s var(--ease-out) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s var(--ease-out) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s var(--ease-out) forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// 延迟动画
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }

// 5. 响应式设计
// ================================

@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-lg);
  }
  
  .content-wrapper {
    padding: 0 var(--spacing-md);
  }
  
  .section-header .title {
    font-size: var(--font-size-2xl);
  }
  
  .modern-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
  }
}

// 6. 配色优化 - 确保文字可读性
// ================================

// 全局文字颜色优化
body {
  color: var(--text-primary);
}

// 白色背景上的文字优化
.bg-white,
[style*="background: white"],
[style*="background-color: white"],
[style*="background: #fff"],
[style*="background-color: #fff"] {
  color: var(--text-primary) !important;

  .text-white {
    color: var(--text-primary) !important;
  }

  .text-light {
    color: var(--text-secondary) !important;
  }

  .text-muted {
    color: var(--text-tertiary) !important;
  }
}

// Element UI 组件文字优化
.el-card {
  color: var(--text-primary);

  .el-card__header {
    color: var(--text-primary);
  }

  .el-card__body {
    color: var(--text-secondary);
  }
}

.el-table {
  color: var(--text-primary);

  .el-table__header {
    color: var(--text-primary);
  }

  .el-table__body {
    color: var(--text-secondary);
  }
}

.el-form-item__label {
  color: var(--text-primary) !important;
}

.el-input__inner {
  color: var(--text-primary) !important;
}

.el-select .el-input__inner {
  color: var(--text-primary) !important;
}

// 统计卡片文字优化
.stat-card {
  background: white;
  color: var(--text-primary);

  .stat-value {
    color: var(--text-primary) !important;
    font-weight: 700;
  }

  .stat-label {
    color: var(--text-secondary) !important;
  }

  .stat-change {
    font-weight: 600;

    &.positive {
      color: var(--success-600) !important;
    }

    &.negative {
      color: var(--error-600) !important;
    }
  }
}

// 仪表板卡片优化
.dashboard-card {
  background: white;
  color: var(--text-primary);

  .card-title {
    color: var(--text-primary) !important;
    font-weight: 600;
  }

  .card-subtitle {
    color: var(--text-secondary) !important;
  }

  .card-value {
    color: var(--text-primary) !important;
    font-weight: 700;
  }
}

// 按钮文字对比度确保
.el-button {
  &.el-button--primary {
    color: white !important;
  }

  &.el-button--success {
    color: white !important;
  }

  &.el-button--warning {
    color: white !important;
  }

  &.el-button--danger {
    color: white !important;
  }

  &.el-button--info {
    color: white !important;
  }
}

// 链接颜色优化
a {
  color: var(--primary-600) !important;

  &:hover {
    color: var(--primary-700) !important;
  }
}
