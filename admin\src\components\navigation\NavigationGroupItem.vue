<template>
  <div class="navigation-group" :class="{ collapsed }" :style="{ '--group-delay': `${index * 0.1}s` }">
    <!-- 分组标题 -->
    <div class="group-header" v-if="!collapsed">
      <div class="group-title">
        <div class="title-icon" :style="{ background: group.gradient }">
          <el-icon size="14">
            <component :is="group.icon" />
          </el-icon>
        </div>
        <span class="title-text">{{ group.title }}</span>
        <div class="item-count">{{ visibleItems.length }}</div>
      </div>
      <div class="group-divider"></div>
    </div>
    
    <!-- 分组项目 -->
    <div class="group-items">
      <navigation-menu-item
        v-for="(item, itemIndex) in visibleItems"
        :key="item.path"
        :item="item"
        :collapsed="collapsed"
        :active="isActive(item.path)"
        :index="itemIndex"
        @click="handleItemClick(item)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import NavigationMenuItem from './NavigationMenuItem.vue'

// Props
const props = defineProps({
  group: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  index: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['item-click'])

// Router
const route = useRoute()

// 计算属性
const visibleItems = computed(() => {
  return props.group.items.filter(item => !item.hidden)
})

// Methods
const isActive = (path) => {
  // 精确匹配或路径前缀匹配
  return route.path === path || 
         (route.path.startsWith(path + '/') && path !== '/')
}

const handleItemClick = (item) => {
  emit('item-click', item)
}
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.navigation-group {
  margin-bottom: 32px;
  animation: group-fade-in 0.6s ease-out;
  animation-delay: var(--group-delay, 0s);
  animation-fill-mode: both;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .group-header {
    margin-bottom: 16px;
    
    .group-title {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #606266;
      font-size: 12px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.8px;
      padding: 0 4px;
      margin-bottom: 8px;
      
      .title-icon {
        width: 24px;
        height: 24px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.1) rotate(5deg);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
      }
      
      .title-text {
        flex: 1;
        font-size: 13px;
        font-weight: 600;
        color: #303133;
        transition: color 0.3s ease;
      }
      
      .item-count {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        color: #667eea;
        font-size: 10px;
        font-weight: 700;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 18px;
        text-align: center;
        border: 1px solid rgba(102, 126, 234, 0.2);
      }
    }
    
    .group-divider {
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, #e4e7ed 20%, rgba(228, 231, 237, 0.5) 50%, transparent 80%);
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 1px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transition: width 0.8s ease;
      }
    }
    
    // 悬浮时分割线延展效果
    &:hover .group-divider::after {
      width: 60%;
    }
  }
  
  .group-items {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  // 收缩状态
  &.collapsed {
    margin-bottom: 24px;
    
    .group-items {
      gap: 8px;
    }
  }
  
  // 聚焦状态
  &:focus-within {
    .group-header .group-title .title-text {
      color: #667eea;
    }
    
    .group-divider::after {
      width: 100%;
    }
  }
}

// 动画定义
@keyframes group-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navigation-group {
    margin-bottom: 24px;
    
    .group-header {
      margin-bottom: 12px;
      
      .group-title {
        gap: 8px;
        
        .title-icon {
          width: 20px;
          height: 20px;
        }
        
        .title-text {
          font-size: 12px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .navigation-group {
    .group-header .group-title {
      .title-text {
        color: #000;
        font-weight: 800;
      }
      
      .item-count {
        background: #000;
        color: #fff;
        border-color: #000;
      }
    }
    
    .group-divider {
      background: #000;
      height: 2px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .navigation-group {
    .group-header .group-title {
      .title-text {
        color: #e2e8f0;
      }
      
      .item-count {
        background: rgba(102, 126, 234, 0.2);
        color: #8b9aff;
        border-color: rgba(102, 126, 234, 0.3);
      }
    }
    
    .group-divider {
      background: linear-gradient(90deg, #374151 20%, rgba(55, 65, 81, 0.5) 50%, transparent 80%);
    }
  }
}

// 动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .navigation-group {
    animation: none;
    
    .group-header .title-icon {
      transition: none;
    }
    
    .group-divider::after {
      transition: none;
    }
  }
}
</style>