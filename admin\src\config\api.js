/**
 * API配置中心
 * 统一管理所有API接口配置
 */

// API基础配置
export const API_CONFIG = {
  // 基础URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 超时时间
  TIMEOUT: 30000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 重试延迟
  RETRY_DELAY: 1000,
  
  // 缓存时间（毫秒）
  CACHE_TIME: 5 * 60 * 1000, // 5分钟
  
  // 分页配置
  PAGE_SIZE: 20,
  
  // 最大文件上传大小（MB）
  MAX_FILE_SIZE: 10,
  
  // 支持的文件类型
  ALLOWED_FILE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx']
}

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    UPDATE_PASSWORD: '/auth/password'
  },
  
  // 用户管理
  USERS: {
    LIST: '/users',
    DETAIL: '/users/:id',
    CREATE: '/users',
    UPDATE: '/users/:id',
    DELETE: '/users/:id',
    BATCH_UPDATE: '/users/batch',
    EXPORT: '/users/export'
  },
  
  // 群组管理
  GROUPS: {
    LIST: '/groups',
    DETAIL: '/groups/:id',
    CREATE: '/groups',
    UPDATE: '/groups/:id',
    DELETE: '/groups/:id',
    MEMBERS: '/groups/:id/members',
    ANALYTICS: '/groups/:id/analytics',
    EXPORT: '/groups/export'
  },
  
  // 订单管理
  ORDERS: {
    LIST: '/orders',
    DETAIL: '/orders/:id',
    CREATE: '/orders',
    UPDATE: '/orders/:id',
    CANCEL: '/orders/:id/cancel',
    EXPORT: '/orders/export',
    STATISTICS: '/orders/statistics'
  },
  
  // 财务管理
  FINANCE: {
    TRANSACTIONS: '/finance/transactions',
    COMMISSIONS: '/finance/commissions',
    WITHDRAWALS: '/finance/withdrawals',
    BALANCE: '/finance/balance',
    EXPORT: '/finance/export',
    ANALYTICS: '/finance/analytics'
  },
  
  // 系统设置
  SYSTEM: {
    SETTINGS: '/system/settings',
    LOGS: '/system/logs',
    NOTIFICATIONS: '/system/notifications',
    BACKUPS: '/system/backups',
    MONITOR: '/system/monitor'
  },
  
  // 分销管理
  DISTRIBUTION: {
    AGENTS: '/distribution/agents',
    COMMISSIONS: '/distribution/commissions',
    WITHDRAWALS: '/distribution/withdrawals',
    ANALYTICS: '/distribution/analytics'
  },
  
  // 内容管理
  CONTENT: {
    ARTICLES: '/content/articles',
    TEMPLATES: '/content/templates',
    CATEGORIES: '/content/categories',
    UPLOAD: '/content/upload'
  },
  
  // 推广链接
  PROMOTION: {
    LINKS: '/promotion/links',
    ANALYTICS: '/promotion/analytics',
    SHORT_LINKS: '/promotion/short-links'
  },
  
  // 分站管理
  SUBSTATIONS: {
    LIST: '/substations',
    DETAIL: '/substations/:id',
    CREATE: '/substations',
    UPDATE: '/substations/:id',
    DELETE: '/substations/:id',
    ANALYTICS: '/substations/:id/analytics'
  },
  
  // 防封域名
  ANTIBLOCK: {
    DOMAINS: '/anti-block/domains',
    SHORT_LINKS: '/anti-block/short-links',
    ANALYTICS: '/anti-block/analytics',
    CHECK: '/anti-block/check'
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: '/upload/image',
    FILE: '/upload/file',
    MULTIPLE: '/upload/multiple',
    DELETE: '/upload/delete'
  }
}

// 获取完整的API URL
export function getApiUrl(endpoint, params = {}) {
  let url = endpoint
  
  // 替换路径参数
  Object.keys(params).forEach(key => {
    url = url.replace(`:${key}`, params[key])
  })
  
  return `${API_CONFIG.BASE_URL}${url}`
}

// 获取分页参数
export function getPaginationParams(page = 1, size = API_CONFIG.PAGE_SIZE) {
  return {
    page,
    size,
    offset: (page - 1) * size
  }
}

// 获取排序参数
export function getSortParams(sortField, sortOrder = 'asc') {
  return {
    sort: sortField,
    order: sortOrder
  }
}

// 获取过滤参数
export function getFilterParams(filters) {
  const params = {}
  
  Object.keys(filters).forEach(key => {
    if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
      params[key] = filters[key]
    }
  })
  
  return params
}

export default {
  API_CONFIG,
  API_ENDPOINTS,
  getApiUrl,
  getPaginationParams,
  getSortParams,
  getFilterParams
}