<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 群组配置
    |--------------------------------------------------------------------------
    */

    // 群组分类配置
    'categories' => [
        'technology' => [
            'name' => '技术编程',
            'icon' => 'code',
            'color' => '#409eff',
            'description' => '技术交流、编程学习、开发经验分享',
            'recommended_price' => 29.9,
            'recommended_member_limit' => 500,
            'tags' => ['技术', '编程', '开发', '交流']
        ],
        'finance' => [
            'name' => '投资理财',
            'icon' => 'money',
            'color' => '#67c23a',
            'description' => '投资策略、理财规划、市场分析',
            'recommended_price' => 99.9,
            'recommended_member_limit' => 200,
            'tags' => ['理财', '投资', '股票', '基金']
        ],
        'education' => [
            'name' => '教育学习',
            'icon' => 'book',
            'color' => '#e6a23c',
            'description' => '学习交流、知识分享、教育资源',
            'recommended_price' => 19.9,
            'recommended_member_limit' => 300,
            'tags' => ['学习', '教育', '成长', '知识']
        ],
        'fitness' => [
            'name' => '健身运动',
            'icon' => 'sport',
            'color' => '#f56c6c',
            'description' => '健身指导、运动交流、健康生活',
            'recommended_price' => 39.9,
            'recommended_member_limit' => 400,
            'tags' => ['健身', '运动', '健康', '锻炼']
        ],
        'business' => [
            'name' => '商业创业',
            'icon' => 'business',
            'color' => '#909399',
            'description' => '创业交流、商业合作、项目对接',
            'recommended_price' => 199.9,
            'recommended_member_limit' => 150,
            'tags' => ['创业', '商业', '合作', '项目']
        ],
        'social' => [
            'name' => '社交交友',
            'icon' => 'social',
            'color' => '#ff69b4',
            'description' => '社交活动、交友聚会、兴趣爱好',
            'recommended_price' => 9.9,
            'recommended_member_limit' => 500,
            'tags' => ['社交', '交友', '聚会', '兴趣']
        ],
        'entertainment' => [
            'name' => '娱乐休闲',
            'icon' => 'entertainment',
            'color' => '#8a2be2',
            'description' => '娱乐分享、休闲活动、兴趣爱好',
            'recommended_price' => 15.9,
            'recommended_member_limit' => 400,
            'tags' => ['娱乐', '休闲', '游戏', '影视']
        ],
        'other' => [
            'name' => '其他',
            'icon' => 'other',
            'color' => '#606266',
            'description' => '其他类型的群组',
            'recommended_price' => 9.9,
            'recommended_member_limit' => 500,
            'tags' => ['其他', '综合']
        ]
    ],

    // 支付方式配置
    'payment_methods' => [
        'wechat' => [
            'name' => '微信支付',
            'icon' => '/icons/wechat-pay.png',
            'enabled' => true,
            'sort_order' => 1
        ],
        'alipay' => [
            'name' => '支付宝',
            'icon' => '/icons/alipay.png',
            'enabled' => true,
            'sort_order' => 2
        ],
        'qq' => [
            'name' => 'QQ钱包',
            'icon' => '/icons/qq-pay.png',
            'enabled' => false,
            'sort_order' => 3
        ],
        'bank' => [
            'name' => '银行转账',
            'icon' => '/icons/bank.png',
            'enabled' => true,
            'sort_order' => 4
        ]
    ],

    // 城市插入策略配置
    'city_strategies' => [
        'auto' => [
            'name' => '智能判断',
            'description' => '系统自动选择最佳插入位置'
        ],
        'prefix' => [
            'name' => '前缀模式',
            'description' => '在标题前面添加城市名称'
        ],
        'suffix' => [
            'name' => '后缀模式',
            'description' => '在标题后面添加城市名称'
        ],
        'natural' => [
            'name' => '自然插入',
            'description' => '替换标题中的xxx占位符'
        ],
        'none' => [
            'name' => '不插入',
            'description' => '保持原标题不变'
        ]
    ],

    // 群组状态配置
    'statuses' => [
        'active' => [
            'name' => '已发布',
            'color' => 'success',
            'description' => '群组正常运行中'
        ],
        'inactive' => [
            'name' => '已下架',
            'color' => 'warning',
            'description' => '群组已暂停使用'
        ],
        'draft' => [
            'name' => '草稿',
            'color' => 'info',
            'description' => '群组尚未发布'
        ]
    ],

    // 默认配置
    'defaults' => [
        'member_limit' => 500,
        'price' => 0,
        'order_expire_minutes' => 30,
        'button_title' => '立即加入',
        'city_location' => true,
        'city_insert_strategy' => 'auto',
        'enable_analytics' => true,
        'enable_conversion_tracking' => true,
        'auto_publish' => false,
        'send_notification' => false,
        'generate_qr_poster' => true
    ],

    // 限制配置
    'limits' => [
        'max_price' => 9999.99,
        'min_price' => 0,
        'max_member_limit' => 2000,
        'min_member_limit' => 1,
        'max_title_length' => 200,
        'min_title_length' => 2,
        'max_description_length' => 1000,
        'min_description_length' => 10,
        'max_media_images' => 10,
        'max_commission_rate' => 100,
        'min_commission_rate' => 0
    ],

    // 文件上传配置
    'upload' => [
        'max_file_size' => 5120, // KB
        'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'allowed_video_types' => ['mp4', 'avi', 'mov'],
        'allowed_audio_types' => ['mp3', 'wav', 'aac'],
        'image_quality' => 90,
        'thumbnail_size' => [200, 200],
        'cover_size' => [800, 600]
    ],

    // 营销功能配置
    'marketing' => [
        'virtual_data_enabled' => true,
        'social_proof_enabled' => true,
        'urgency_marketing_enabled' => true,
        'limited_offer_enabled' => true,
        'countdown_enabled' => true,
        'member_avatars_enabled' => true,
        'reviews_enabled' => true,
        'real_time_stats_enabled' => true
    ],

    // 防红系统配置
    'anti_block' => [
        'enabled' => true,
        'check_intervals' => [5, 10, 30, 60], // 分钟
        'switch_strategies' => ['immediate', 'delayed', 'manual'],
        'default_check_interval' => 10,
        'default_switch_strategy' => 'immediate',
        'max_retry_times' => 3,
        'timeout' => 10
    ],

    // 分销系统配置
    'distribution' => [
        'enabled' => true,
        'max_levels' => 3,
        'settlement_methods' => ['instant', 'daily', 'weekly', 'monthly'],
        'default_settlement' => 'daily',
        'min_commission_rate' => 0,
        'max_commission_rate' => 100,
        'max_total_commission_rate' => 100
    ],

    // 模板配置
    'templates' => [
        'enabled' => true,
        'max_user_templates' => 10,
        'template_categories' => [
            'system' => '系统模板',
            'user' => '用户模板'
        ]
    ],

    // 统计分析配置
    'analytics' => [
        'enabled' => true,
        'retention_days' => 365,
        'real_time_enabled' => true,
        'conversion_tracking_enabled' => true,
        'custom_events_enabled' => true
    ],

    // 缓存配置
    'cache' => [
        'group_stats_ttl' => 1800, // 30分钟
        'template_list_ttl' => 3600, // 1小时
        'category_stats_ttl' => 7200, // 2小时
        'domain_pool_ttl' => 600 // 10分钟
    ],

    // 通知配置
    'notifications' => [
        'group_created' => true,
        'group_published' => true,
        'member_joined' => false,
        'order_created' => true,
        'payment_received' => true,
        'domain_switched' => true
    ]
];