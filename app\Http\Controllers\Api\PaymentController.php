<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\WechatGroup;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 支付控制器
 * 处理支付相关的API请求
 */
class PaymentController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
        $this->middleware('auth:api')->except(['notify', 'return', 'show']);
        $this->middleware('throttle:payment')->only(['createOrder', 'createPayment']);
    }

    /**
     * 创建支付订单
     */
    public function createOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'wechat_group_id' => 'required|integer|exists:wechat_groups,id',
            'customer_name' => 'required|string|max:50',
            'customer_phone' => 'nullable|string|max:20',
            'customer_wechat' => 'nullable|string|max:50',
            'payment_method' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($request->wechat_group_id);
            
            // 检查群组状态
            if ($group->status !== 1) {
                return $this->errorResponse('群组不可用', null, 400);
            }

            // 检查群组是否已满
            if ($group->current_members >= $group->max_members) {
                return $this->errorResponse('群组已满', null, 400);
            }

            // 验证支付方式
            $availablePayments = $group->getDetailedPaymentMethods();
            $paymentCodes = array_column($availablePayments, 'channel_code');
            
            if (!in_array($request->payment_method, $paymentCodes)) {
                return $this->errorResponse('不支持的支付方式', null, 400);
            }

            // 创建订单数据
            $orderData = [
                'wechat_group_id' => $group->id,
                'user_id' => Auth::id(),
                'amount' => $group->price,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'customer_wechat' => $request->customer_wechat,
                'customer_ip' => $request->ip(),
                'customer_city' => $this->getClientCity($request),
                'payment_methods' => $availablePayments,
            ];

            $result = $this->paymentService->createOrder($orderData);

            return $this->successResponse('订单创建成功', $result);

        } catch (Exception $e) {
            Log::error('创建支付订单失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);
            
            return $this->errorResponse('订单创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建支付
     */
    public function createPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string|exists:orders,order_no',
            'payment_method' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $order = Order::where('order_no', $request->order_no)->first();
            
            // 检查订单状态
            if (!$order->isPending()) {
                return $this->errorResponse('订单状态不正确', null, 400);
            }

            // 检查订单是否过期
            if ($order->isExpired()) {
                $order->cancel('订单已过期');
                return $this->errorResponse('订单已过期', null, 400);
            }

            // 更新订单支付方式
            $order->update(['payment_method' => $request->payment_method]);

            // 创建支付
            $paymentResult = $this->paymentService->createPayment($order, $request->payment_method);

            return $this->successResponse('支付创建成功', [
                'order_no' => $order->order_no,
                'amount' => $order->amount,
                'payment_info' => $paymentResult,
                'expired_at' => $order->expired_at->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('创建支付失败', [
                'error' => $e->getMessage(),
                'order_no' => $request->order_no,
                'payment_method' => $request->payment_method
            ]);
            
            return $this->errorResponse('支付创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrder(Request $request, string $orderNo)
    {
        try {
            $result = $this->paymentService->queryOrderStatus($orderNo);
            
            if (!$result['success']) {
                return $this->errorResponse($result['message'], null, 404);
            }

            return $this->successResponse('查询成功', $result['data']);

        } catch (Exception $e) {
            return $this->errorResponse('查询失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 支付回调处理
     */
    public function notify(Request $request, string $method)
    {
        try {
            // 记录回调日志
            Log::info('收到支付回调', [
                'method' => $method,
                'data' => $request->all(),
                'ip' => $request->ip(),
            ]);

            // 验证回调来源IP（如果配置了白名单）
            if (!$this->verifyCallbackIP($request->ip())) {
                Log::warning('支付回调IP验证失败', ['ip' => $request->ip()]);
                return response('IP not allowed', 403);
            }

            // 处理回调
            $result = $this->paymentService->handleCallback($method, $request->all());

            if ($result['success']) {
                // 根据不同支付方式返回相应格式
                return $this->getCallbackResponse($method, true);
            } else {
                Log::error('支付回调处理失败', [
                    'method' => $method,
                    'result' => $result
                ]);
                return $this->getCallbackResponse($method, false);
            }

        } catch (Exception $e) {
            Log::error('支付回调异常', [
                'method' => $method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->getCallbackResponse($method, false);
        }
    }

    /**
     * 支付返回页面
     */
    public function return(Request $request)
    {
        $orderNo = $request->input('order_no') ?? $request->input('out_trade_no');
        
        if (!$orderNo) {
            return redirect()->route('payment.error')->with('message', '订单号缺失');
        }

        $order = Order::where('order_no', $orderNo)->first();
        
        if (!$order) {
            return redirect()->route('payment.error')->with('message', '订单不存在');
        }

        if ($order->isPaid()) {
            return redirect()->route('payment.success', $order->id);
        } else {
            return redirect()->route('payment.pending', $order->id);
        }
    }

    /**
     * 显示支付页面
     */
    public function show(Request $request, string $orderNo)
    {
        try {
            $order = Order::where('order_no', $orderNo)
                         ->with(['wechatGroup', 'user'])
                         ->first();

            if (!$order) {
                return $this->errorResponse('订单不存在', null, 404);
            }

            // 检查订单状态
            if ($order->isPaid()) {
                return $this->successResponse('订单已支付', [
                    'status' => 'paid',
                    'order' => $order,
                    'paid_at' => $order->paid_at
                ]);
            }

            if ($order->isExpired()) {
                $order->cancel('订单已过期');
                return $this->errorResponse('订单已过期', null, 400);
            }

            // 获取支付信息
            $paymentInfo = $this->paymentService->getPaymentInfo($order);

            return $this->successResponse('获取支付信息成功', $paymentInfo);

        } catch (Exception $e) {
            return $this->errorResponse('获取支付信息失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(Request $request, string $orderNo)
    {
        try {
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return $this->errorResponse('订单不存在', null, 404);
            }

            // 检查权限
            if ($order->user_id && $order->user_id !== Auth::id()) {
                return $this->errorResponse('无权操作此订单', null, 403);
            }

            if (!$order->isPending()) {
                return $this->errorResponse('订单状态不允许取消', null, 400);
            }

            $order->cancel('用户主动取消');

            return $this->successResponse('订单已取消');

        } catch (Exception $e) {
            return $this->errorResponse('取消订单失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取支付方式列表
     */
    public function getPaymentMethods(Request $request)
    {
        try {
            $groupId = $request->input('group_id');
            
            if ($groupId) {
                $group = WechatGroup::findOrFail($groupId);
                $methods = $group->getDetailedPaymentMethods();
            } else {
                $methods = $this->paymentService->getAvailablePaymentMethods();
            }

            return $this->successResponse('获取支付方式成功', $methods);

        } catch (Exception $e) {
            return $this->errorResponse('获取支付方式失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 验证回调IP
     */
    private function verifyCallbackIP(string $ip): bool
    {
        $whitelist = config('payment.security.ip_whitelist', []);
        
        if (empty($whitelist)) {
            return true; // 如果没有配置白名单，则允许所有IP
        }

        return in_array($ip, $whitelist);
    }

    /**
     * 获取回调响应
     */
    private function getCallbackResponse(string $method, bool $success): mixed
    {
        return match($method) {
            'wechat' => $success ? 
                response('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>') :
                response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>'),
            'alipay' => $success ? response('success') : response('fail'),
            'payoreo' => $success ? response('success') : response('fail'),
            default => $success ? response('OK') : response('ERROR')
        };
    }

    /**
     * 获取客户端城市
     */
    private function getClientCity(Request $request): string
    {
        // 这里可以集成IP定位服务
        return $request->header('CF-IPCountry', '未知');
    }

    /**
     * 成功响应
     */
    private function successResponse(string $message, array $data = [])
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp,
        ]);
    }

    /**
     * 错误响应
     */
    private function errorResponse(string $message, $errors = null, int $code = 400)
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->timestamp,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }
}