/**
 * User Management E2E Tests
 * 测试用户管理功能的端到端流程
 */

import { test, expect } from '@playwright/test'

test.describe('User Management', () => {
  test.beforeEach(async ({ page }) => {
    // 模拟已登录状态
    await page.goto('/login')
    
    // 设置认证token
    await page.evaluate(() => {
      localStorage.setItem('Admin-Token', 'mock-admin-token')
      localStorage.setItem('userInfo', JSON.stringify({
        id: 1,
        username: 'admin',
        role: 'admin',
        permissions: ['*']
      }))
    })
    
    // 模拟认证检查API
    await page.route('/api/admin/auth/user', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: {
              id: 1,
              username: 'admin',
              nickname: '超级管理员',
              role: 'admin',
              permissions: ['*']
            }
          }
        })
      })
    })
  })

  test.describe('User List Page', () => {
    test('should display user list correctly', async ({ page }) => {
      // 模拟用户列表API
      await page.route('/api/admin/users*', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              users: [
                {
                  id: 1,
                  username: 'admin',
                  nickname: '超级管理员',
                  email: '<EMAIL>',
                  role: 'admin',
                  status: 'active',
                  created_at: '2024-01-01T00:00:00Z',
                  last_login: '2024-01-15T10:30:00Z'
                },
                {
                  id: 2,
                  username: 'user1',
                  nickname: '普通用户',
                  email: '<EMAIL>',
                  role: 'user',
                  status: 'active',
                  created_at: '2024-01-02T00:00:00Z',
                  last_login: '2024-01-14T09:20:00Z'
                }
              ],
              pagination: {
                current_page: 1,
                total_pages: 1,
                total_records: 2,
                per_page: 20
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      
      // 检查页面标题和基本元素
      await expect(page.locator('h1')).toContainText('用户管理')
      await expect(page.locator('.user-table')).toBeVisible()
      
      // 检查用户数据显示
      await expect(page.locator('tbody tr')).toHaveCount(2)
      await expect(page.locator('tbody tr:first-child td:first-child')).toContainText('admin')
      await expect(page.locator('tbody tr:nth-child(2) td:first-child')).toContainText('user1')
      
      // 检查操作按钮
      await expect(page.locator('.btn-create-user')).toBeVisible()
      await expect(page.locator('.btn-export')).toBeVisible()
    })

    test('should handle search functionality', async ({ page }) => {
      await page.route('/api/admin/users*', (route) => {
        const url = new URL(route.request().url())
        const search = url.searchParams.get('search')
        
        let users = [
          {
            id: 1,
            username: 'admin',
            nickname: '超级管理员',
            email: '<EMAIL>',
            role: 'admin'
          },
          {
            id: 2,
            username: 'testuser',
            nickname: '测试用户',
            email: '<EMAIL>',
            role: 'user'
          }
        ]
        
        if (search) {
          users = users.filter(user => 
            user.username.includes(search) || 
            user.nickname.includes(search) ||
            user.email.includes(search)
          )
        }
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              users,
              pagination: {
                current_page: 1,
                total_pages: 1,
                total_records: users.length,
                per_page: 20
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      
      // 搜索用户
      await page.fill('.search-input', 'test')
      await page.click('.btn-search')
      
      // 验证搜索结果
      await expect(page.locator('tbody tr')).toHaveCount(1)
      await expect(page.locator('tbody tr:first-child')).toContainText('testuser')
    })

    test('should handle pagination', async ({ page }) => {
      await page.route('/api/admin/users*', (route) => {
        const url = new URL(route.request().url())
        const page_num = parseInt(url.searchParams.get('page') || '1')
        
        const totalUsers = 50
        const perPage = 20
        const start = (page_num - 1) * perPage
        const end = Math.min(start + perPage, totalUsers)
        
        const users = Array.from({ length: end - start }, (_, i) => ({
          id: start + i + 1,
          username: `user${start + i + 1}`,
          nickname: `用户${start + i + 1}`,
          email: `user${start + i + 1}@example.com`,
          role: 'user'
        }))
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              users,
              pagination: {
                current_page: page_num,
                total_pages: Math.ceil(totalUsers / perPage),
                total_records: totalUsers,
                per_page: perPage
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      
      // 检查分页组件
      await expect(page.locator('.pagination')).toBeVisible()
      
      // 点击下一页
      await page.click('.pagination .btn-next')
      
      // 验证页面更新
      await expect(page.locator('.pagination .current-page')).toContainText('2')
    })

    test('should handle filtering and sorting', async ({ page }) => {
      await page.route('/api/admin/users*', (route) => {
        const url = new URL(route.request().url())
        const role = url.searchParams.get('role')
        const sort = url.searchParams.get('sort')
        
        let users = [
          {
            id: 1,
            username: 'admin',
            role: 'admin',
            created_at: '2024-01-01T00:00:00Z'
          },
          {
            id: 2,
            username: 'user1',
            role: 'user',
            created_at: '2024-01-02T00:00:00Z'
          },
          {
            id: 3,
            username: 'agent1',
            role: 'agent',
            created_at: '2024-01-03T00:00:00Z'
          }
        ]
        
        if (role) {
          users = users.filter(user => user.role === role)
        }
        
        if (sort === 'created_at_desc') {
          users.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        }
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              users,
              pagination: {
                current_page: 1,
                total_pages: 1,
                total_records: users.length
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      
      // 筛选角色
      await page.selectOption('.role-filter', 'user')
      
      // 验证筛选结果
      await expect(page.locator('tbody tr')).toHaveCount(1)
      await expect(page.locator('tbody tr:first-child')).toContainText('user1')
      
      // 排序
      await page.selectOption('.sort-select', 'created_at_desc')
      
      // 验证排序（这里需要检查实际的排序结果）
    })
  })

  test.describe('Create User', () => {
    test('should create new user successfully', async ({ page }) => {
      await page.route('/api/admin/users', (route) => {
        if (route.request().method() === 'POST') {
          const postData = route.request().postDataJSON()
          
          route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              success: true,
              data: {
                user: {
                  id: 999,
                  ...postData,
                  created_at: new Date().toISOString()
                }
              },
              message: '用户创建成功'
            })
          })
        }
      })
      
      await page.goto('/users')
      
      // 点击创建用户按钮
      await page.click('.btn-create-user')
      
      // 验证模态框打开
      await expect(page.locator('.user-dialog')).toBeVisible()
      await expect(page.locator('.dialog-title')).toContainText('创建用户')
      
      // 填写用户信息
      await page.fill('#username', 'newuser')
      await page.fill('#nickname', '新用户')
      await page.fill('#email', '<EMAIL>')
      await page.fill('#phone', '13812345678')
      await page.fill('#password', 'password123')
      await page.fill('#confirm_password', 'password123')
      await page.selectOption('#role', 'user')
      
      // 提交表单
      await page.click('.btn-confirm')
      
      // 验证成功消息
      await expect(page.locator('.el-message--success')).toContainText('用户创建成功')
      
      // 验证模态框关闭
      await expect(page.locator('.user-dialog')).not.toBeVisible()
      
      // 验证用户列表刷新
      // 这里需要重新模拟用户列表API以包含新用户
    })

    test('should validate form fields', async ({ page }) => {
      await page.goto('/users')
      await page.click('.btn-create-user')
      
      // 尝试提交空表单
      await page.click('.btn-confirm')
      
      // 验证必填字段验证
      await expect(page.locator('.field-error')).toHaveCount.toBeGreaterThan(0)
      
      // 验证邮箱格式验证
      await page.fill('#email', 'invalid-email')
      await page.blur('#email')
      await expect(page.locator('.email-error')).toContainText('邮箱格式不正确')
      
      // 验证手机号格式验证
      await page.fill('#phone', '123456')
      await page.blur('#phone')
      await expect(page.locator('.phone-error')).toContainText('手机号格式不正确')
      
      // 验证密码确认
      await page.fill('#password', 'password123')
      await page.fill('#confirm_password', 'different')
      await page.blur('#confirm_password')
      await expect(page.locator('.password-confirm-error')).toContainText('密码确认不匹配')
    })

    test('should handle duplicate username error', async ({ page }) => {
      await page.route('/api/admin/users', (route) => {
        route.fulfill({
          status: 422,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: '用户名已存在',
            errors: {
              username: ['用户名已存在']
            }
          })
        })
      })
      
      await page.goto('/users')
      await page.click('.btn-create-user')
      
      // 填写重复的用户名
      await page.fill('#username', 'admin') // 假设admin已存在
      await page.fill('#nickname', '测试用户')
      await page.fill('#email', '<EMAIL>')
      await page.fill('#password', 'password123')
      await page.fill('#confirm_password', 'password123')
      
      await page.click('.btn-confirm')
      
      // 验证错误消息
      await expect(page.locator('.el-message--error')).toContainText('用户名已存在')
      await expect(page.locator('.username-error')).toContainText('用户名已存在')
    })
  })

  test.describe('Edit User', () => {
    test('should edit user successfully', async ({ page }) => {
      const userId = 2
      
      // 模拟获取用户详情API
      await page.route(`/api/admin/users/${userId}`, (route) => {
        if (route.request().method() === 'GET') {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              success: true,
              data: {
                user: {
                  id: userId,
                  username: 'edituser',
                  nickname: '编辑用户',
                  email: '<EMAIL>',
                  phone: '13812345678',
                  role: 'user',
                  status: 'active'
                }
              }
            })
          })
        } else if (route.request().method() === 'PUT') {
          const putData = route.request().postDataJSON()
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              success: true,
              data: {
                user: {
                  id: userId,
                  ...putData
                }
              },
              message: '用户更新成功'
            })
          })
        }
      })
      
      await page.goto('/users')
      
      // 点击编辑按钮
      await page.click(`.btn-edit[data-user-id="${userId}"]`)
      
      // 验证编辑模态框打开并预填数据
      await expect(page.locator('.user-dialog')).toBeVisible()
      await expect(page.locator('.dialog-title')).toContainText('编辑用户')
      await expect(page.locator('#username')).toHaveValue('edituser')
      await expect(page.locator('#nickname')).toHaveValue('编辑用户')
      
      // 修改用户信息
      await page.fill('#nickname', '修改后的用户名')
      await page.fill('#email', '<EMAIL>')
      
      // 提交修改
      await page.click('.btn-confirm')
      
      // 验证成功消息
      await expect(page.locator('.el-message--success')).toContainText('用户更新成功')
      
      // 验证模态框关闭
      await expect(page.locator('.user-dialog')).not.toBeVisible()
    })

    test('should not allow editing username', async ({ page }) => {
      const userId = 2
      
      await page.route(`/api/admin/users/${userId}`, (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              user: {
                id: userId,
                username: 'edituser',
                nickname: '编辑用户',
                email: '<EMAIL>',
                role: 'user'
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      await page.click(`.btn-edit[data-user-id="${userId}"]`)
      
      // 验证用户名字段被禁用
      await expect(page.locator('#username')).toBeDisabled()
    })
  })

  test.describe('User Status Management', () => {
    test('should enable/disable user successfully', async ({ page }) => {
      const userId = 2
      
      await page.route(`/api/admin/users/${userId}/status`, (route) => {
        const putData = route.request().postDataJSON()
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: putData.status === 'disabled' ? '用户已禁用' : '用户已启用'
          })
        })
      })
      
      await page.goto('/users')
      
      // 点击禁用用户按钮
      await page.click(`.btn-disable[data-user-id="${userId}"]`)
      
      // 验证确认对话框
      await expect(page.locator('.el-message-box')).toBeVisible()
      await expect(page.locator('.el-message-box__message')).toContainText('确定要禁用该用户吗？')
      
      // 确认禁用
      await page.click('.el-message-box__btns .el-button--primary')
      
      // 验证成功消息
      await expect(page.locator('.el-message--success')).toContainText('用户已禁用')
      
      // 验证用户状态在列表中更新
      const userRow = page.locator(`tbody tr[data-user-id="${userId}"]`)
      await expect(userRow.locator('.user-status')).toContainText('已禁用')
    })

    test('should cancel status change operation', async ({ page }) => {
      const userId = 2
      
      await page.goto('/users')
      
      // 点击禁用按钮
      await page.click(`.btn-disable[data-user-id="${userId}"]`)
      
      // 点击取消
      await page.click('.el-message-box__btns .el-button--default')
      
      // 验证对话框关闭，状态未改变
      await expect(page.locator('.el-message-box')).not.toBeVisible()
      // 这里需要检查用户状态保持不变
    })
  })

  test.describe('Delete User', () => {
    test('should delete user successfully', async ({ page }) => {
      const userId = 3
      
      await page.route(`/api/admin/users/${userId}`, (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '用户删除成功'
          })
        })
      })
      
      await page.goto('/users')
      
      // 点击删除按钮
      await page.click(`.btn-delete[data-user-id="${userId}"]`)
      
      // 验证确认对话框
      await expect(page.locator('.el-message-box')).toBeVisible()
      await expect(page.locator('.el-message-box__message')).toContainText('确定要删除该用户吗？此操作不可恢复')
      
      // 确认删除
      await page.click('.el-message-box__btns .el-button--primary')
      
      // 验证成功消息
      await expect(page.locator('.el-message--success')).toContainText('用户删除成功')
      
      // 验证用户从列表中移除
      await expect(page.locator(`tbody tr[data-user-id="${userId}"]`)).not.toBeVisible()
    })

    test('should prevent deleting current admin user', async ({ page }) => {
      const adminUserId = 1
      
      await page.goto('/users')
      
      // 管理员用户的删除按钮应该被禁用或不存在
      const deleteButton = page.locator(`.btn-delete[data-user-id="${adminUserId}"]`)
      
      if (await deleteButton.isVisible()) {
        await expect(deleteButton).toBeDisabled()
      } else {
        // 删除按钮不应该存在
        await expect(deleteButton).not.toBeVisible()
      }
    })
  })

  test.describe('User Detail View', () => {
    test('should show user details correctly', async ({ page }) => {
      const userId = 2
      
      await page.route(`/api/admin/users/${userId}`, (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              user: {
                id: userId,
                username: 'detailuser',
                nickname: '详情用户',
                email: '<EMAIL>',
                phone: '13812345678',
                role: 'user',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z',
                last_login: '2024-01-15T10:30:00Z',
                login_count: 25,
                groups: [
                  { id: 1, name: '测试群组1' },
                  { id: 2, name: '测试群组2' }
                ],
                orders: [
                  { id: 1, amount: 100.00, status: 'completed' },
                  { id: 2, amount: 50.00, status: 'pending' }
                ]
              }
            }
          })
        })
      })
      
      await page.goto('/users')
      
      // 点击查看详情按钮
      await page.click(`.btn-detail[data-user-id="${userId}"]`)
      
      // 验证详情抽屉打开
      await expect(page.locator('.user-detail-drawer')).toBeVisible()
      
      // 验证用户信息显示
      await expect(page.locator('.detail-username')).toContainText('detailuser')
      await expect(page.locator('.detail-nickname')).toContainText('详情用户')
      await expect(page.locator('.detail-email')).toContainText('<EMAIL>')
      await expect(page.locator('.detail-phone')).toContainText('13812345678')
      await expect(page.locator('.detail-role')).toContainText('普通用户')
      
      // 验证统计信息
      await expect(page.locator('.stat-login-count')).toContainText('25')
      
      // 验证关联群组
      await expect(page.locator('.user-groups')).toBeVisible()
      await expect(page.locator('.group-item')).toHaveCount(2)
      
      // 验证订单历史
      await expect(page.locator('.user-orders')).toBeVisible()
      await expect(page.locator('.order-item')).toHaveCount(2)
    })

    test('should close detail drawer correctly', async ({ page }) => {
      const userId = 2
      
      await page.goto('/users')
      await page.click(`.btn-detail[data-user-id="${userId}"]`)
      
      await expect(page.locator('.user-detail-drawer')).toBeVisible()
      
      // 点击关闭按钮
      await page.click('.drawer-close-btn')
      
      // 验证抽屉关闭
      await expect(page.locator('.user-detail-drawer')).not.toBeVisible()
    })
  })

  test.describe('Bulk Operations', () => {
    test('should select multiple users', async ({ page }) => {
      await page.goto('/users')
      
      // 选择多个用户
      await page.check('.user-checkbox[data-user-id="2"]')
      await page.check('.user-checkbox[data-user-id="3"]')
      
      // 验证批量操作工具栏显示
      await expect(page.locator('.bulk-actions')).toBeVisible()
      await expect(page.locator('.selected-count')).toContainText('已选择 2 个用户')
      
      // 验证批量操作按钮
      await expect(page.locator('.btn-bulk-enable')).toBeVisible()
      await expect(page.locator('.btn-bulk-disable')).toBeVisible()
      await expect(page.locator('.btn-bulk-export')).toBeVisible()
    })

    test('should perform bulk enable operation', async ({ page }) => {
      await page.route('/api/admin/users/bulk-status', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '批量操作完成，已启用 2 个用户'
          })
        })
      })
      
      await page.goto('/users')
      
      // 选择用户
      await page.check('.user-checkbox[data-user-id="2"]')
      await page.check('.user-checkbox[data-user-id="3"]')
      
      // 点击批量启用
      await page.click('.btn-bulk-enable')
      
      // 验证确认对话框
      await expect(page.locator('.el-message-box')).toBeVisible()
      await expect(page.locator('.el-message-box__message')).toContainText('确定要启用选中的 2 个用户吗？')
      
      // 确认操作
      await page.click('.el-message-box__btns .el-button--primary')
      
      // 验证成功消息
      await expect(page.locator('.el-message--success')).toContainText('批量操作完成，已启用 2 个用户')
      
      // 验证选择状态重置
      await expect(page.locator('.bulk-actions')).not.toBeVisible()
    })

    test('should select all users', async ({ page }) => {
      await page.goto('/users')
      
      // 点击全选复选框
      await page.check('.select-all-checkbox')
      
      // 验证所有用户被选中
      const userCheckboxes = page.locator('.user-checkbox')
      const count = await userCheckboxes.count()
      
      for (let i = 0; i < count; i++) {
        await expect(userCheckboxes.nth(i)).toBeChecked()
      }
      
      // 验证批量操作工具栏显示正确数量
      await expect(page.locator('.selected-count')).toContainText(`已选择 ${count} 个用户`)
    })
  })

  test.describe('Export Functionality', () => {
    test('should export user data', async ({ page }) => {
      await page.route('/api/admin/users/export', (route) => {
        route.fulfill({
          status: 200,
          headers: {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': 'attachment; filename=users_export.xlsx'
          },
          body: Buffer.from('mock-excel-content')
        })
      })
      
      await page.goto('/users')
      
      // 设置下载监听
      const downloadPromise = page.waitForEvent('download')
      
      // 点击导出按钮
      await page.click('.btn-export')
      
      // 等待下载开始
      const download = await downloadPromise
      
      // 验证下载文件名
      expect(download.suggestedFilename()).toBe('users_export.xlsx')
    })

    test('should export selected users only', async ({ page }) => {
      await page.goto('/users')
      
      // 选择特定用户
      await page.check('.user-checkbox[data-user-id="2"]')
      await page.check('.user-checkbox[data-user-id="3"]')
      
      // 点击批量导出
      await page.click('.btn-bulk-export')
      
      // 验证导出确认对话框
      await expect(page.locator('.export-dialog')).toBeVisible()
      await expect(page.locator('.export-dialog')).toContainText('导出选中的 2 个用户')
    })
  })

  test.describe('Permission-based Access', () => {
    test('should hide edit/delete buttons for users without permission', async ({ page }) => {
      // 模拟受限权限用户
      await page.evaluate(() => {
        localStorage.setItem('userInfo', JSON.stringify({
          id: 5,
          username: 'limited_user',
          role: 'user',
          permissions: ['user_view'] // 只有查看权限
        }))
      })
      
      await page.goto('/users')
      
      // 验证编辑和删除按钮不显示
      await expect(page.locator('.btn-edit')).not.toBeVisible()
      await expect(page.locator('.btn-delete')).not.toBeVisible()
      await expect(page.locator('.btn-create-user')).not.toBeVisible()
      
      // 但查看详情按钮应该可见
      await expect(page.locator('.btn-detail')).toBeVisible()
    })

    test('should restrict access to user management page', async ({ page }) => {
      // 模拟无权限用户
      await page.evaluate(() => {
        localStorage.setItem('userInfo', JSON.stringify({
          id: 6,
          username: 'no_permission_user',
          role: 'guest',
          permissions: []
        }))
      })
      
      // 尝试访问用户管理页面
      await page.goto('/users')
      
      // 应该被重定向到无权限页面或仪表板
      await expect(page).toHaveURL(/\/(403|dashboard)/)
      
      // 或显示权限不足消息
      if (await page.locator('.permission-denied').isVisible()) {
        await expect(page.locator('.permission-denied')).toContainText('权限不足')
      }
    })
  })
})