<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentChannel;
use App\Models\PaymentConfig;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * 支付通道管理控制器
 * 负责管理支付通道的配置和权限
 */
class PaymentChannelController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 获取支付通道列表（管理员）
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // 只有管理员可以查看所有支付通道
        if (!$user->isAdmin()) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        $query = PaymentChannel::with('paymentConfigs');
        
        // 搜索
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('channel_name', 'like', "%{$search}%")
                  ->orWhere('channel_code', 'like', "%{$search}%");
            });
        }

        // 状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        $channels = $query->orderBy('sort_order')->get();

        // 添加统计信息
        $channels->each(function ($channel) {
            $channel->stats = $channel->getStats();
        });

        return response()->json([
            'success' => true,
            'data' => $channels,
        ]);
    }

    /**
     * 创建支付通道（管理员）
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isAdmin()) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        $validator = Validator::make($request->all(), [
            'channel_code' => 'required|string|max:50|unique:payment_channels,channel_code',
            'channel_name' => 'required|string|max:100',
            'channel_desc' => 'nullable|string|max:255',
            'channel_icon' => 'nullable|string|max:100',
            'allow_substation' => 'boolean',
            'allow_distribution' => 'boolean',
            'min_distribution_level' => 'integer|min:1|max:4',
            'fee_rate' => 'numeric|min:0|max:1',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $channel = PaymentChannel::create($request->all());

        return response()->json([
            'success' => true,
            'message' => '支付通道创建成功',
            'data' => $channel,
        ], 201);
    }

    /**
     * 更新支付通道（管理员）
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        
        if (!$user->isAdmin()) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        $channel = PaymentChannel::find($id);
        if (!$channel) {
            return response()->json(['success' => false, 'message' => '支付通道不存在'], 404);
        }

        $validator = Validator::make($request->all(), [
            'channel_name' => 'required|string|max:100',
            'channel_desc' => 'nullable|string|max:255',
            'channel_icon' => 'nullable|string|max:100',
            'status' => 'boolean',
            'allow_substation' => 'boolean',
            'allow_distribution' => 'boolean',
            'min_distribution_level' => 'integer|min:1|max:4',
            'fee_rate' => 'numeric|min:0|max:1',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $channel->update($request->all());

        return response()->json([
            'success' => true,
            'message' => '支付通道更新成功',
            'data' => $channel,
        ]);
    }

    /**
     * 删除支付通道（管理员）
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        if (!$user->isAdmin()) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        $channel = PaymentChannel::find($id);
        if (!$channel) {
            return response()->json(['success' => false, 'message' => '支付通道不存在'], 404);
        }

        // 检查是否有配置在使用
        $configCount = PaymentConfig::where('channel_code', $channel->channel_code)->count();
        if ($configCount > 0) {
            return response()->json(['success' => false, 'message' => '该支付通道正在使用中，无法删除'], 400);
        }

        $channel->delete();

        return response()->json([
            'success' => true,
            'message' => '支付通道删除成功',
        ]);
    }

    /**
     * 获取用户可配置的支付通道
     */
    public function getUserChannels(Request $request)
    {
        $user = Auth::user();
        
        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;
        
        $channels = $this->paymentService->getUserConfigurableChannels($userType, $userId);

        return response()->json([
            'success' => true,
            'data' => $channels,
        ]);
    }

    /**
     * 保存用户支付配置
     */
    public function saveUserConfig(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'channel_code' => 'required|string|exists:payment_channels,channel_code',
            'config_name' => 'required|string|max:100',
            'config_data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;

        $configData = $request->input('config_data');
        $configData['config_name'] = $request->input('config_name');

        $result = $this->paymentService->saveUserPaymentConfig(
            $userType,
            $userId,
            $request->input('channel_code'),
            $configData
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['config'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
            ], 400);
        }
    }

    /**
     * 测试用户支付配置
     */
    public function testUserConfig(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'channel_code' => 'required|string|exists:payment_channels,channel_code',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;

        $result = $this->paymentService->testUserPaymentConfig(
            $userType,
            $userId,
            $request->input('channel_code')
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
            ], 400);
        }
    }

    /**
     * 获取用户支付配置
     */
    public function getUserConfig(Request $request, $channelCode)
    {
        $user = Auth::user();
        
        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;

        $config = PaymentConfig::getConfigForUser($userType, $userId, $channelCode);
        
        if (!$config) {
            return response()->json(['success' => false, 'message' => '配置不存在'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'config_name' => $config->config_name,
                'config_data' => $config->getDisplayConfigData(),
                'status' => $config->status,
                'test_status' => $config->test_status,
                'test_time' => $config->test_time,
            ],
        ]);
    }

    /**
     * 删除用户支付配置
     */
    public function deleteUserConfig(Request $request, $channelCode)
    {
        $user = Auth::user();
        
        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;

        $config = PaymentConfig::getConfigForUser($userType, $userId, $channelCode);
        
        if (!$config) {
            return response()->json(['success' => false, 'message' => '配置不存在'], 404);
        }

        $config->delete();

        return response()->json([
            'success' => true,
            'message' => '配置删除成功',
        ]);
    }

    /**
     * 获取支付统计数据
     */
    public function getPaymentStats(Request $request)
    {
        $user = Auth::user();
        
        $userType = $user->isSubstation() ? 'substation' : 'distribution';
        $userId = $user->isSubstation() ? $user->substation->id : $user->id;
        
        $period = $request->input('period', 'month');
        $stats = $this->paymentService->getPaymentStats($userType, $userId, $period);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 获取支付通道模板
     */
    public function getChannelTemplate($channelCode)
    {
        $channel = PaymentChannel::where('channel_code', $channelCode)->first();
        
        if (!$channel) {
            return response()->json(['success' => false, 'message' => '支付通道不存在'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'channel_code' => $channel->channel_code,
                'channel_name' => $channel->channel_name,
                'template' => $channel->getConfigTemplate(),
            ],
        ]);
    }

    /**
     * 获取启用的支付通道列表（公开接口）
     */
    public function getActiveChannels()
    {
        $channels = PaymentChannel::where('status', 1)
            ->orderBy('sort_order')
            ->get(['channel_code', 'channel_name', 'channel_desc', 'channel_icon', 'fee_rate']);

        // 结合配置文件中的支付方式信息
        $configMethods = config('payment.methods', []);
        $activeChannels = [];

        foreach ($channels as $channel) {
            $methodConfig = $configMethods[$channel->channel_code] ?? [];
            
            // 检查支付方式是否启用
            if (isset($methodConfig['enabled']) && !$methodConfig['enabled']) {
                continue;
            }
            
            $activeChannels[] = [
                'code' => $channel->channel_code,
                'name' => $methodConfig['name'] ?? $channel->channel_name,
                'description' => $methodConfig['description'] ?? $channel->channel_desc,
                'icon' => $methodConfig['icon'] ?? $channel->channel_icon,
                'fee_rate' => $methodConfig['fee_rate'] ?? $channel->fee_rate,
                'enabled' => true,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $activeChannels,
        ]);
    }
} 