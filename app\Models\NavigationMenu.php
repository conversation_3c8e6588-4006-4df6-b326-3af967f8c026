<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

/**
 * 导航菜单模型
 */
class NavigationMenu extends Model
{
    protected $fillable = [
        'code', 'name', 'domain', 'icon', 'route', 'component',
        'meta', 'parent_id', 'sort_order', 'is_visible', 'is_cacheable',
        'permission', 'roles'
    ];

    protected $casts = [
        'meta' => 'array',
        'roles' => 'array',
        'is_visible' => 'boolean',
        'is_cacheable' => 'boolean',
    ];

    // 域常量
    const DOMAIN_CORE = 'core';
    const DOMAIN_OPERATION = 'operation';  
    const DOMAIN_ANALYTICS = 'analytics';
    const DOMAIN_SYSTEM = 'system';

    /**
     * 子菜单关系
     */
    public function children(): HasMany
    {
        return $this->hasMany(NavigationMenu::class, 'parent_id')
                    ->where('is_visible', true)
                    ->orderBy('sort_order');
    }

    /**
     * 父菜单关系
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(NavigationMenu::class, 'parent_id');
    }

    /**
     * 使用统计关系
     */
    public function usageStats(): HasMany
    {
        return $this->hasMany(NavigationUsageStat::class, 'menu_code', 'code');
    }

    /**
     * 查询作用域 - 按域筛选
     */
    public function scopeDomain($query, $domain)
    {
        return $query->where('domain', $domain);
    }

    /**
     * 查询作用域 - 可见的菜单
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * 查询作用域 - 根菜单
     */
    public function scopeRoot($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 检查用户是否有权限访问该菜单
     */
    public function hasPermission($user): bool
    {
        // 检查角色权限
        if ($this->roles && !empty($this->roles)) {
            if (!in_array($user->role, $this->roles)) {
                return false;
            }
        }

        // 检查具体权限
        if ($this->permission) {
            return $user->hasPermissionCached($this->permission);
        }

        return true;
    }

    /**
     * 获取缓存的菜单树
     */
    public static function getCachedMenuTree($domain = null, $userId = null): array
    {
        $cacheKey = "navigation:tree:" . ($domain ?? 'all') . ':' . ($userId ?? 'guest');
        
        return Cache::remember($cacheKey, 3600, function () use ($domain, $userId) {
            $query = static::with(['children'])
                          ->visible()
                          ->root()
                          ->orderBy('sort_order');

            if ($domain) {
                $query->domain($domain);
            }

            $menus = $query->get();

            // 如果有用户，过滤权限
            if ($userId) {
                $user = User::find($userId);
                if ($user) {
                    $menus = $menus->filter(function ($menu) use ($user) {
                        return $menu->hasPermission($user);
                    });
                }
            }

            return $menus->toArray();
        });
    }

    /**
     * 清除相关缓存
     */
    public static function clearCache()
    {
        Cache::tags(['navigation'])->flush();
    }
}