// 默认图片配置
export const defaultImages = {
  // 模板封面图片
  templates: {
    'template-1': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iIzQwOWVmZiIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mqKHmnb/kuIDjgIHmioDmnK/kuqTmtYEgMjAweDEyMDwvdGV4dD4KPC9zdmc+',
    'template-2': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iIzY3YzIzYSIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mioDmnK/kuqTmtYHmqKHmnb/kuozkgIHkuozjgIEgMjAweDEyMDwvdGV4dD4KPC9zdmc+',
    'template-3': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iI2U2YTIzYyIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mioDmnK/kuqTmtYHmqKHmnb/kuInkgIHkuInkgIEgMjAweDEyMDwvdGV4dD4KPC9zdmc+',
    'template-4': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iI2Y1NmM2YyIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mioDmnK/kuqTmtYHmqKHmnb/lm5vkgIHlm5vkgIEgMjAweDEyMDwvdGV4dD4KPC9zdmc+'
  },
  
  // 二维码占位图片
  qrCodes: {
    default: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjdmYSIgc3Ryb2tlPSIjZTRlN2VkIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8dGV4dCB4PSIxMDAiIHk9IjkwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5MDkzOTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7kuoznu7TnoIHljaDkvY3lm77niYc8L3RleHQ+CiAgPHRleHQgeD0iMTAwIiB5PSIxMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzkwOTM5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPjIwMHgyMDA8L3RleHQ+Cjwvc3ZnPg=='
  },
  
  // 默认头像
  avatars: {
    default: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSI2MCIgZmlsbD0iI2Y1ZjdmYSIgc3Ryb2tlPSIjZTRlN2VkIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjQ1IiByPSIxNSIgZmlsbD0iIzlkOWQ5ZCIvPgogIDxwYXRoIGQ9Ik0zNSA4NWMwLTE0IDExLTI1IDI1LTI1czI1IDExIDI1IDI1IiBmaWxsPSIjOWQ5ZDlkIi8+CiAgPHRleHQgeD0iNjAiIHk9IjEwNSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEwIiBmaWxsPSIjOTA5Mzk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5aSa5YOP5aS05YOP8J+RpDwvdGV4dD4KPC9zdmc+'
  }
}

// 获取默认图片的函数
export function getDefaultImage(type, key = 'default') {
  const typeImages = defaultImages[type]
  if (!typeImages) {
    return defaultImages.avatars.default
  }
  
  return typeImages[key] || typeImages.default || defaultImages.avatars.default
}

// 生成服务二维码占位图片
export function generateServiceQrCode(id) {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="#f5f7fa" stroke="#e4e7ed" stroke-width="2"/>
      <text x="100" y="90" font-family="Arial, sans-serif" font-size="14" fill="#909399" text-anchor="middle" dy=".3em">客服二维码 #${id}</text>
      <text x="100" y="110" font-family="Arial, sans-serif" font-size="12" fill="#909399" text-anchor="middle" dy=".3em">200x200</text>
    </svg>
  `)}`
}

// 生成模板封面占位图片
export function generateTemplateCover(id, title = '模板封面') {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  const color = colors[id % colors.length]
  
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="200" height="120" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="120" fill="${color}"/>
      <text x="100" y="60" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle" dy=".3em">${title}</text>
      <text x="100" y="80" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle" dy=".3em">200x120</text>
    </svg>
  `)}`
}