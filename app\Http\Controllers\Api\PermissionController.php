<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

/**
 * 权限管理控制器
 * 管理系统角色和权限
 */
class PermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('role:admin');
    }

    /**
     * 获取所有角色
     */
    public function getRoles(Request $request): JsonResponse
    {
        try {
            $query = Role::with('permissions');
            
            // 搜索过滤
            if ($request->filled('search')) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }
            
            $roles = $query->orderBy('created_at', 'desc')->paginate(20);
            
            return response()->json([
                'success' => true,
                'data' => $roles,
                'message' => '角色列表获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取角色列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建角色
     */
    public function createRole(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:roles,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            DB::beginTransaction();

            $role = Role::create([
                'name' => $validated['name'],
                'guard_name' => 'api',
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? ''
            ]);

            // 分配权限
            if (!empty($validated['permissions'])) {
                $permissions = Permission::whereIn('id', $validated['permissions'])->get();
                $role->syncPermissions($permissions);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $role->load('permissions'),
                'message' => '角色创建成功'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '角色创建失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新角色
     */
    public function updateRole(Request $request, $id): JsonResponse
    {
        try {
            $role = Role::findOrFail($id);
            
            $validated = $request->validate([
                'name' => ['required', 'string', 'max:255', Rule::unique('roles')->ignore($id)],
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            DB::beginTransaction();

            $role->update([
                'name' => $validated['name'],
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? ''
            ]);

            // 更新权限
            if (isset($validated['permissions'])) {
                $permissions = Permission::whereIn('id', $validated['permissions'])->get();
                $role->syncPermissions($permissions);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $role->load('permissions'),
                'message' => '角色更新成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '角色更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除角色
     */
    public function deleteRole($id): JsonResponse
    {
        try {
            $role = Role::findOrFail($id);
            
            // 检查是否有用户使用此角色
            $userCount = User::role($role->name)->count();
            if ($userCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => "无法删除角色，还有 {$userCount} 个用户正在使用此角色"
                ], 422);
            }

            $role->delete();

            return response()->json([
                'success' => true,
                'message' => '角色删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '角色删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取所有权限
     */
    public function getPermissions(Request $request): JsonResponse
    {
        try {
            $query = Permission::query();
            
            // 按模块分组
            if ($request->filled('group_by_module')) {
                $permissions = $query->get()->groupBy(function ($permission) {
                    return explode('.', $permission->name)[0] ?? 'other';
                });
            } else {
                $permissions = $query->orderBy('name')->paginate(50);
            }
            
            return response()->json([
                'success' => true,
                'data' => $permissions,
                'message' => '权限列表获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取权限列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建权限
     */
    public function createPermission(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:permissions,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'module' => 'required|string|max:100'
            ]);

            $permission = Permission::create([
                'name' => $validated['name'],
                'guard_name' => 'api',
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? '',
                'module' => $validated['module']
            ]);

            return response()->json([
                'success' => true,
                'data' => $permission,
                'message' => '权限创建成功'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '权限创建失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 为角色分配权限
     */
    public function assignPermissions(Request $request, $roleId): JsonResponse
    {
        try {
            $role = Role::findOrFail($roleId);
            
            $validated = $request->validate([
                'permissions' => 'required|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $role->syncPermissions($permissions);

            return response()->json([
                'success' => true,
                'data' => $role->load('permissions'),
                'message' => '权限分配成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '权限分配失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 撤销角色权限
     */
    public function revokePermissions(Request $request, $roleId): JsonResponse
    {
        try {
            $role = Role::findOrFail($roleId);
            
            $validated = $request->validate([
                'permissions' => 'required|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $role->revokePermissionTo($permissions);

            return response()->json([
                'success' => true,
                'data' => $role->load('permissions'),
                'message' => '权限撤销成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '权限撤销失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 为用户分配角色
     */
    public function assignUserRole(Request $request, $userId): JsonResponse
    {
        try {
            $user = User::findOrFail($userId);
            
            $validated = $request->validate([
                'roles' => 'required|array',
                'roles.*' => 'exists:roles,id'
            ]);

            $roles = Role::whereIn('id', $validated['roles'])->get();
            $user->syncRoles($roles);

            return response()->json([
                'success' => true,
                'data' => $user->load('roles'),
                'message' => '用户角色分配成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '用户角色分配失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户权限
     */
    public function getUserPermissions($userId): JsonResponse
    {
        try {
            $user = User::with(['roles.permissions', 'permissions'])->findOrFail($userId);
            
            // 获取用户所有权限（通过角色和直接分配）
            $allPermissions = $user->getAllPermissions();
            
            // 按模块分组
            $permissionsByModule = $allPermissions->groupBy(function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'permissions' => $allPermissions,
                    'permissions_by_module' => $permissionsByModule,
                    'roles' => $user->roles
                ],
                'message' => '用户权限获取成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户权限失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 直接为用户分配权限
     */
    public function assignUserPermissions(Request $request, $userId): JsonResponse
    {
        try {
            $user = User::findOrFail($userId);
            
            $validated = $request->validate([
                'permissions' => 'required|array',
                'permissions.*' => 'exists:permissions,id'
            ]);

            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $user->syncPermissions($permissions);

            return response()->json([
                'success' => true,
                'data' => $user->load('permissions'),
                'message' => '用户权限分配成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '用户权限分配失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取权限统计
     */
    public function getPermissionStats(): JsonResponse
    {
        try {
            $stats = [
                'total_roles' => Role::count(),
                'total_permissions' => Permission::count(),
                'users_with_roles' => User::has('roles')->count(),
                'users_with_direct_permissions' => User::has('permissions')->count(),
                'role_usage' => Role::withCount('users')->get()->map(function ($role) {
                    return [
                        'role' => $role->name,
                        'display_name' => $role->display_name,
                        'users_count' => $role->users_count
                    ];
                }),
                'permission_modules' => Permission::selectRaw('
                    SUBSTRING_INDEX(name, ".", 1) as module,
                    COUNT(*) as count
                ')
                ->groupBy('module')
                ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => '权限统计获取成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取权限统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 初始化系统权限
     */
    public function initializePermissions(): JsonResponse
    {
        try {
            DB::beginTransaction();

            // 定义系统权限
            $permissions = [
                // 用户管理
                ['name' => 'user.view', 'display_name' => '查看用户', 'module' => 'user'],
                ['name' => 'user.create', 'display_name' => '创建用户', 'module' => 'user'],
                ['name' => 'user.update', 'display_name' => '更新用户', 'module' => 'user'],
                ['name' => 'user.delete', 'display_name' => '删除用户', 'module' => 'user'],
                
                // 订单管理
                ['name' => 'order.view', 'display_name' => '查看订单', 'module' => 'order'],
                ['name' => 'order.update', 'display_name' => '更新订单', 'module' => 'order'],
                ['name' => 'order.refund', 'display_name' => '订单退款', 'module' => 'order'],
                ['name' => 'order.export', 'display_name' => '导出订单', 'module' => 'order'],
                
                // 群组管理
                ['name' => 'group.view', 'display_name' => '查看群组', 'module' => 'group'],
                ['name' => 'group.create', 'display_name' => '创建群组', 'module' => 'group'],
                ['name' => 'group.update', 'display_name' => '更新群组', 'module' => 'group'],
                ['name' => 'group.delete', 'display_name' => '删除群组', 'module' => 'group'],
                
                // 佣金管理
                ['name' => 'commission.view', 'display_name' => '查看佣金', 'module' => 'commission'],
                ['name' => 'commission.settle', 'display_name' => '结算佣金', 'module' => 'commission'],
                ['name' => 'commission.adjust', 'display_name' => '调整佣金', 'module' => 'commission'],
                
                // 系统管理
                ['name' => 'system.monitor', 'display_name' => '系统监控', 'module' => 'system'],
                ['name' => 'system.settings', 'display_name' => '系统设置', 'module' => 'system'],
                ['name' => 'system.backup', 'display_name' => '系统备份', 'module' => 'system'],
                
                // 权限管理
                ['name' => 'permission.view', 'display_name' => '查看权限', 'module' => 'permission'],
                ['name' => 'permission.manage', 'display_name' => '管理权限', 'module' => 'permission'],
            ];

            foreach ($permissions as $permissionData) {
                Permission::firstOrCreate(
                    ['name' => $permissionData['name']],
                    array_merge($permissionData, ['guard_name' => 'api'])
                );
            }

            // 创建默认角色
            $adminRole = Role::firstOrCreate(
                ['name' => 'admin'],
                [
                    'display_name' => '超级管理员',
                    'description' => '拥有所有权限的超级管理员',
                    'guard_name' => 'api'
                ]
            );

            $substationRole = Role::firstOrCreate(
                ['name' => 'substation'],
                [
                    'display_name' => '分站管理员',
                    'description' => '分站管理员，管理本分站用户和业务',
                    'guard_name' => 'api'
                ]
            );

            $distributorRole = Role::firstOrCreate(
                ['name' => 'distributor'],
                [
                    'display_name' => '分销商',
                    'description' => '分销商，可以创建和管理群组',
                    'guard_name' => 'api'
                ]
            );

            // 为管理员分配所有权限
            $adminRole->syncPermissions(Permission::all());

            // 为分站管理员分配部分权限
            $substationPermissions = Permission::whereIn('module', ['user', 'order', 'group', 'commission'])->get();
            $substationRole->syncPermissions($substationPermissions);

            // 为分销商分配基础权限
            $distributorPermissions = Permission::whereIn('name', [
                'group.view', 'group.create', 'group.update',
                'order.view', 'commission.view'
            ])->get();
            $distributorRole->syncPermissions($distributorPermissions);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '系统权限初始化成功',
                'data' => [
                    'permissions_created' => count($permissions),
                    'roles_created' => 3
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '系统权限初始化失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}