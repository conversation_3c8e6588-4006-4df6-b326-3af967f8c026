<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PromotionLink;
use App\Models\LinkAccessLog;
use App\Models\User;
use App\Models\CommissionLog;
use App\Services\AntiBlockService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 推广链接控制器 (V2.0 - 全功能版)
 * <AUTHOR>
 * @date 2024-07-26
 */
class PromotionLinkController extends Controller
{
    protected $antiBlockService;

    public function __construct(AntiBlockService $antiBlockService)
    {
        $this->middleware('auth:api');
        $this->antiBlockService = $antiBlockService;
    }
    
    /**
     * 获取推广链接列表 (带统计)
     */
    public function index(Request $request)
    {
        $links = $request->user()->promotionLinks()
            ->withCount(['accessLogs as clicks', 'registrations as conversions'])
            ->withSum('commissions as commission_total', 'amount')
            ->latest()
            ->paginate($request->input('limit', 10));

        return response()->json(['code' => 0, 'data' => $links]);
    }

    /**
     * 获取推广中心全局统计数据
     */
    public function getStats(Request $request)
    {
        $user = $request->user();

        $totalLinks = $user->promotionLinks()->count();
        $activeLinks = $user->promotionLinks()->where('status', 'active')->count();

        $totalClicks = LinkAccessLog::whereIn('promotion_link_id', $user->promotionLinks()->pluck('id'))->count();
        $todayClicks = LinkAccessLog::whereIn('promotion_link_id', $user->promotionLinks()->pluck('id'))->whereDate('created_at', Carbon::today())->count();
        
        $conversions = $user->children()->count();
        $todayConversions = $user->children()->whereDate('created_at', Carbon::today())->count();

        $conversionRate = $totalClicks > 0 ? round(($conversions / $totalClicks) * 100, 2) : 0;
        
        // 分销团队人数 (下线总数)
        $teamMembers = $conversions; 
        $newMembersThisMonth = $user->children()->where('created_at', '>=', Carbon::now()->startOfMonth())->count();

        return response()->json(['code' => 0, 'data' => [
            'totalLinks' => $totalLinks,
            'activeLinks' => $activeLinks,
            'totalClicks' => $totalClicks,
            'todayClicks' => $todayClicks,
            'conversions' => $conversions,
            'todayConversions' => $todayConversions,
            'conversionRate' => $conversionRate,
            'teamMembers' => $teamMembers,
            'newMembersThisMonth' => $newMembersThisMonth,
        ]]);
    }

    /**
     * 获取推广图表数据
     */
    public function getChartData(Request $request)
    {
        $user = $request->user();
        $request->validate([
            'period' => 'required|in:7,30,90',
            'type' => 'required|in:clicks,conversions,commission',
        ]);
        
        $days = $request->period;
        $type = $request->type;
        
        $endDate = Carbon::now()->endOfDay();
        $startDate = Carbon::now()->subDays($days - 1)->startOfDay();

        $query = null;
        if ($type === 'clicks') {
            $query = LinkAccessLog::whereIn('promotion_link_id', $user->promotionLinks()->pluck('id'));
        } elseif ($type === 'conversions') {
            $query = User::where('referrer_id', $user->id);
        } elseif ($type === 'commission') {
            $query = CommissionLog::where('user_id', $user->id);
        }

        $data = $query->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->get([
                DB::raw('DATE(created_at) as date'),
                DB::raw($type === 'commission' ? 'SUM(amount) as total' : 'COUNT(*) as total')
            ])
            ->pluck('total', 'date');
            
        $labels = [];
        $values = [];
        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dateString = $date->format('Y-m-d');
            $labels[] = $date->format('m-d');
            $values[] = (float)($data[$dateString] ?? 0);
        }

        return response()->json(['code' => 0, 'data' => ['labels' => $labels, 'data' => $values]]);
    }
    
    /**
     * 创建一个新的推广链接
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'target_group_id' => 'nullable|exists:wechat_groups,id',
            'landing_page_id' => 'nullable|exists:landing_pages,id',
            'remark' => 'nullable|string|max:255',
        ]);

        $user = $request->user();

        // 生成目标URL
        $targetUrl = $this->generateTargetUrl($data, $user);
        
        // 创建短链接（防红处理）
        $protectedData = $this->antiBlockService->createShortLink($targetUrl);

        $link = PromotionLink::create([
            'user_id' => $user->id,
            'name' => $data['name'],
            'target_url' => $targetUrl,
            'protected_url' => $protectedData['short_url'] ?? $targetUrl, // 如果防红失败, 使用原始链接
            'remark' => $data['remark'] ?? null,
            'target_group_id' => $data['target_group_id'] ?? null,
            'landing_page_id' => $data['landing_page_id'] ?? null,
        ]);

        return response()->json(['code' => 0, 'data' => $link], 201);
    }

    /**
     * 生成目标URL
     */
    private function generateTargetUrl(array $data, $user): string
    {
        $refParam = '?ref=' . $user->id;
        
        // 如果指定了落地页
        if (!empty($data['landing_page_id'])) {
            $baseUrl = url('/landing/' . $data['landing_page_id']);
            
            // 如果同时指定了群组，添加群组参数
            if (!empty($data['target_group_id'])) {
                $baseUrl .= $refParam . '&group_id=' . $data['target_group_id'];
            } else {
                $baseUrl .= $refParam;
            }
            
            return $baseUrl;
        }
        
        // 如果指定了群组但没有落地页，使用群组落地页
        if (!empty($data['target_group_id'])) {
            return url('/group/' . $data['target_group_id'] . '/landing' . $refParam);
        }
        
        // 默认使用邀请落地页
        return url('/invite' . $refParam);
    }
    
    /**
     * 更新推广链接
     */
    public function update(Request $request, PromotionLink $promotionLink)
    {
        $this->authorize('update', $promotionLink);

        $data = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'status' => 'sometimes|required|in:active,inactive',
            'remark' => 'nullable|string|max:255',
        ]);

        $promotionLink->update($data);
        return response()->json(['code' => 0, 'data' => $promotionLink]);
    }

    /**
     * 删除推广链接
     */
    public function destroy(Request $request, PromotionLink $promotionLink)
    {
        $this->authorize('delete', $promotionLink);
        $promotionLink->delete();
        return response()->json(['code' => 0, 'message' => '推广链接已删除']);
    }
} 