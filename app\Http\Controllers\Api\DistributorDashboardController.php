<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\GroupTemplate;
use App\Models\Order;
use App\Models\CommissionLog;
use Carbon\Carbon;

class DistributorDashboardController extends Controller
{
    /**
     * 分销员登录
     */
    public function login(Request $request)
    {
        $request->validate([
            'account' => 'required|string',
            'password' => 'required|string',
        ]);

        $user = User::where('distributor_account', $request->account)
                   ->where('is_distributor', true)
                   ->where('distributor_status', 1)
                   ->first();

        if (!$user || !Hash::check($request->password, $user->distributor_password)) {
            // 记录登录失败日志
            if ($user) {
                DB::table('distributor_login_logs')->insert([
                    'distributor_id' => $user->id,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'login_at' => now(),
                    'login_result' => 'failed',
                    'failure_reason' => 'invalid_password',
                ]);
            }
            
            return response()->json(['error' => '账号或密码错误'], 401);
        }

        // 记录登录成功日志
        DB::table('distributor_login_logs')->insert([
            'distributor_id' => $user->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'login_result' => 'success',
        ]);

        // 生成分销员专用token
        $token = $user->createToken('distributor-token', ['distributor'])->plainTextToken;

        return response()->json([
            'token' => $token,
            'distributor' => [
                'id' => $user->id,
                'name' => $user->name,
                'account' => $user->distributor_account,
                'balance' => $user->distributor_balance,
                'phone' => $user->distributor_phone,
                'status' => $user->distributor_status,
                'created_at' => $user->distributor_created_at,
            ]
        ]);
    }

    /**
     * 分销员后台首页数据
     */
    public function dashboard(Request $request)
    {
        $user = $request->user();
        
        if (!$user->is_distributor) {
            return response()->json(['error' => '无权访问'], 403);
        }

        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        // 统计数据
        $stats = [
            // 今日数据
            'today' => [
                'orders' => $this->getTodayOrders($user->id),
                'income' => $this->getTodayIncome($user->id),
                'commission' => $this->getTodayCommission($user->id),
            ],
            // 总数据
            'total' => [
                'groups' => WechatGroup::where('distributor_id', $user->id)->count(),
                'orders' => Order::whereHas('wechatGroup', function($q) use ($user) {
                    $q->where('distributor_id', $user->id);
                })->count(),
                'income' => $this->getTotalIncome($user->id),
                'commission' => $user->distributor_balance,
            ],
            // 本月数据
            'month' => [
                'orders' => $this->getMonthOrders($user->id),
                'income' => $this->getMonthIncome($user->id),
                'commission' => $this->getMonthCommission($user->id),
            ]
        ];

        // 最近7天销售数据
        $chartData = $this->getWeeklyChartData($user->id);

        // 最新订单
        $recentOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('distributor_id', $user->id);
        })
        ->with(['wechatGroup:id,title', 'user:id,name'])
        ->orderBy('created_at', 'desc')
        ->limit(10)
        ->get();

        return response()->json([
            'stats' => $stats,
            'chart_data' => $chartData,
            'recent_orders' => $recentOrders,
            'balance' => $user->distributor_balance,
        ]);
    }

    /**
     * 分销员群组管理
     */
    public function groups(Request $request)
    {
        $user = $request->user();
        
        $groups = WechatGroup::where('distributor_id', $user->id)
            ->with(['orders' => function($q) {
                $q->selectRaw('wechat_group_id, COUNT(*) as count, SUM(amount) as total_amount')
                  ->groupBy('wechat_group_id');
            }])
            ->get()
            ->map(function($group) use ($user) {
                $orderStats = $group->orders->first();
                $commission = $this->getGroupCommission($group->id);
                
                return [
                    'id' => $group->id,
                    'title' => $group->title,
                    'price' => $group->price,
                    'status' => $group->status,
                    'created_at' => $group->created_at,
                    'stats' => [
                        'order_count' => $orderStats->count ?? 0,
                        'total_income' => $orderStats->total_amount ?? 0,
                        'commission' => $commission,
                    ]
                ];
            });

        return response()->json($groups);
    }

    /**
     * 修改分销员密码
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->distributor_password)) {
            return response()->json(['error' => '当前密码错误'], 400);
        }

        $user->update([
            'distributor_password' => Hash::make($request->new_password)
        ]);

        return response()->json(['message' => '密码修改成功']);
    }

    /**
     * 获取可用模板
     */
    public function availableTemplates(Request $request)
    {
        $user = $request->user();
        
        $templates = GroupTemplate::where(function($query) use ($user) {
            $query->where('is_public', true)
                  ->orWhereJsonContains('allowed_distributors', $user->id)
                  ->orWhereJsonContains('allowed_distribution_groups', $user->distribution_group_id);
        })
        ->where('status', 'active')
        ->get();

        return response()->json($templates);
    }

    /**
     * 使用模板创建群组
     */
    public function createGroupFromTemplate(Request $request)
    {
        $request->validate([
            'template_id' => 'required|exists:group_templates,id',
            'title' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
        ]);

        $user = $request->user();
        $template = GroupTemplate::findOrFail($request->template_id);

        // 检查权限
        if (!$template->is_public && 
            !in_array($user->id, $template->allowed_distributors ?? []) &&
            !in_array($user->distribution_group_id, $template->allowed_distribution_groups ?? [])) {
            return response()->json(['error' => '无权使用此模板'], 403);
        }

        // 创建群组
        $group = WechatGroup::create([
            'distributor_id' => $user->id,
            'title' => $request->title,
            'price' => $request->price,
            'description' => $template->description,
            'image' => $template->image,
            'template_id' => $template->id,
            'status' => 'active',
            // 从模板复制营销字段
            'red_packet_count' => $template->marketing_data['red_packet_count'] ?? '10万+',
            'like_count' => $template->marketing_data['like_count'] ?? 324,
            'message_count' => $template->marketing_data['message_count'] ?? 341,
            'virtual_member_count' => $template->marketing_data['virtual_member_count'] ?? 500,
        ]);

        return response()->json([
            'message' => '群组创建成功',
            'group' => $group
        ]);
    }

    /**
     * 提现申请
     */
    public function requestWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'withdrawal_type' => 'required|in:alipay,wechat,bank',
            'withdrawal_account' => 'required|string|max:100',
        ]);

        $user = $request->user();

        if ($request->amount > $user->distributor_balance) {
            return response()->json(['error' => '余额不足'], 400);
        }

        DB::transaction(function() use ($request, $user) {
            // 冻结余额
            $user->decrement('distributor_balance', $request->amount);

            // 创建提现记录
            DB::table('distributor_withdrawals')->insert([
                'distributor_id' => $user->id,
                'amount' => $request->amount,
                'withdrawal_type' => $request->withdrawal_type,
                'withdrawal_account' => $request->withdrawal_account,
                'status' => 1, // 审核中
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        });

        return response()->json(['message' => '提现申请已提交，等待审核']);
    }

    /**
     * 提现记录
     */
    public function withdrawalHistory(Request $request)
    {
        $user = $request->user();
        
        $withdrawals = DB::table('distributor_withdrawals')
            ->where('distributor_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json($withdrawals);
    }

    // 私有方法
    private function getTodayOrders($distributorId)
    {
        return Order::whereHas('wechatGroup', function($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        })->whereDate('created_at', Carbon::today())->count();
    }

    private function getTodayIncome($distributorId)
    {
        return Order::whereHas('wechatGroup', function($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        })->whereDate('created_at', Carbon::today())->sum('amount');
    }

    private function getTodayCommission($distributorId)
    {
        return CommissionLog::where('distributor_id', $distributorId)
            ->whereDate('created_at', Carbon::today())
            ->sum('amount');
    }

    private function getTotalIncome($distributorId)
    {
        return Order::whereHas('wechatGroup', function($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        })->sum('amount');
    }

    private function getMonthOrders($distributorId)
    {
        return Order::whereHas('wechatGroup', function($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        })->whereMonth('created_at', Carbon::now()->month)->count();
    }

    private function getMonthIncome($distributorId)
    {
        return Order::whereHas('wechatGroup', function($q) use ($distributorId) {
            $q->where('distributor_id', $distributorId);
        })->whereMonth('created_at', Carbon::now()->month)->sum('amount');
    }

    private function getMonthCommission($distributorId)
    {
        return CommissionLog::where('distributor_id', $distributorId)
            ->whereMonth('created_at', Carbon::now()->month)
            ->sum('amount');
    }

    private function getGroupCommission($groupId)
    {
        return CommissionLog::whereHas('order', function($q) use ($groupId) {
            $q->where('wechat_group_id', $groupId);
        })->sum('amount');
    }

    private function getWeeklyChartData($distributorId)
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $income = Order::whereHas('wechatGroup', function($q) use ($distributorId) {
                $q->where('distributor_id', $distributorId);
            })->whereDate('created_at', $date)->sum('amount');
            
            $data[] = [
                'date' => $date->format('m-d'),
                'income' => $income,
            ];
        }
        return $data;
    }
}