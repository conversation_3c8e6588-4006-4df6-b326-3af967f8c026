<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现 - 分销员登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            height: 100vh;
            background: linear-gradient(135deg, #F59E0B, #D97706, #92400E);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        
        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .decoration-item {
            position: absolute;
            opacity: 0.1;
            background: #fff;
            border-radius: 50%;
        }
        
        .decoration-item:nth-child(1) {
            width: 400px;
            height: 400px;
            top: -200px;
            right: -100px;
        }
        
        .decoration-item:nth-child(2) {
            width: 300px;
            height: 300px;
            bottom: -150px;
            left: -50px;
        }
        
        .decoration-item:nth-child(3) {
            width: 200px;
            height: 200px;
            top: 50%;
            left: 10%;
            transform: translateY(-50%);
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            width: 400px;
            padding: 40px;
            z-index: 2;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            font-size: 28px;
            color: #92400E;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .login-header p {
            color: #78350F;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #78350F;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #D6D3D1;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
            background: rgba(255, 255, 255, 0.8);
        }
        
        .form-control:focus {
            border-color: #F59E0B;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
            outline: none;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .form-check {
            display: flex;
            align-items: center;
        }
        
        .form-check input {
            margin-right: 8px;
            accent-color: #F59E0B;
        }
        
        .form-check label {
            font-size: 14px;
            color: #78350F;
        }
        
        .forgot-link {
            color: #B45309;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .forgot-link:hover {
            color: #92400E;
            text-decoration: underline;
        }
        
        .btn-login {
            width: 100%;
            padding: 12px;
            background: linear-gradient(90deg, #F59E0B, #D97706);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-login:hover {
            background: linear-gradient(90deg, #D97706, #B45309);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.4);
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            font-size: 13px;
            color: #78350F;
        }
        
        .signup-link {
            color: #B45309;
            text-decoration: none;
            font-weight: 500;
        }
        
        .signup-link:hover {
            text-decoration: underline;
        }
        
        .benefits {
            margin-top: 30px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 15px;
        }
        
        .benefits h4 {
            font-size: 14px;
            color: #92400E;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .benefits h4 svg {
            margin-right: 5px;
        }
        
        .benefits ul {
            list-style: none;
        }
        
        .benefits li {
            font-size: 13px;
            color: #78350F;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .benefits li::before {
            content: '✓';
            color: #F59E0B;
            margin-right: 8px;
            font-weight: bold;
        }
        
        @media (max-width: 480px) {
            .login-container {
                width: 90%;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 装饰元素 -->
    <div class="decoration">
        <div class="decoration-item"></div>
        <div class="decoration-item"></div>
        <div class="decoration-item"></div>
    </div>
    
    <!-- 登录容器 -->
    <div class="login-container">
        <div class="login-header">
            <h1>晨鑫流量变现</h1>
            <p>分销员管理系统</p>
        </div>
        
        <form>
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" class="form-control" id="username" placeholder="请输入分销员用户名">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" class="form-control" id="password" placeholder="请输入密码">
            </div>
            
            <div class="form-options">
                <div class="form-check">
                    <input type="checkbox" id="remember">
                    <label for="remember">记住我</label>
                </div>
                
                <a href="#" class="forgot-link">忘记密码?</a>
            </div>
            
            <button type="button" class="btn-login" id="loginBtn">登录</button>
        </form>
        
        <div class="benefits">
            <h4>
                <svg viewBox="0 0 24 24" width="16" height="16" fill="#F59E0B">
                    <path d="M12 2L4.5 20.29l.71.71L12 18l6.79 3 .71-.71z"/>
                </svg>
                分销员特权
            </h4>
            <ul>
                <li>专属推广链接与二维码</li>
                <li>实时佣金统计与提现</li>
                <li>团队管理与业绩分析</li>
                <li>营销素材一键分享</li>
            </ul>
        </div>
        
        <div class="login-footer">
            还不是分销员? <a href="#" class="signup-link">立即申请</a>
            <p style="margin-top: 10px;">© 2024 晨鑫流量变现. 智能社群营销与多级分销平台</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 登录按钮点击效果
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.addEventListener('click', function() {
                this.innerHTML = '登录中...';
                this.style.opacity = '0.8';
                
                setTimeout(() => {
                    alert('这是一个演示页面，无法实际登录。请返回到预览页面。');
                    this.innerHTML = '登录';
                    this.style.opacity = '1';
                    window.close();
                }, 2000);
            });
            
            // 添加动态背景效果
            const decoration = document.querySelector('.decoration');
            
            for (let i = 0; i < 10; i++) {
                const dot = document.createElement('div');
                dot.classList.add('decoration-dot');
                dot.style.position = 'absolute';
                dot.style.width = Math.random() * 10 + 5 + 'px';
                dot.style.height = dot.style.width;
                dot.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                dot.style.borderRadius = '50%';
                dot.style.top = Math.random() * 100 + '%';
                dot.style.left = Math.random() * 100 + '%';
                
                // 添加动画
                dot.style.animation = `float ${Math.random() * 10 + 10}s infinite ease-in-out`;
                dot.style.animationDelay = Math.random() * 5 + 's';
                
                decoration.appendChild(dot);
            }
            
            // 添加动画关键帧
            const style = document.createElement('style');
            style.innerHTML = `
                @keyframes float {
                    0%, 100% {
                        transform: translate(0, 0);
                    }
                    50% {
                        transform: translate(${Math.random() * 30}px, ${Math.random() * 30}px);
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>