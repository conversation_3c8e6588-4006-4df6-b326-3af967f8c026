<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\OperationLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * 日志管理控制器
 * 管理系统操作日志、错误日志等
 */
class LogController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $this->middleware('role:admin');
    }

    /**
     * 获取操作日志
     */
    public function getOperationLogs(Request $request): JsonResponse
    {
        try {
            $query = OperationLog::with('user:id,username,nickname');
            
            // 用户筛选
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }
            
            // 操作类型筛选
            if ($request->filled('action')) {
                $query->where('action', 'like', '%' . $request->action . '%');
            }
            
            // 模块筛选
            if ($request->filled('module')) {
                $query->where('module', $request->module);
            }
            
            // IP地址筛选
            if ($request->filled('ip_address')) {
                $query->where('ip_address', $request->ip_address);
            }
            
            // 时间范围筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }
            
            // 关键词搜索
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('action', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('ip_address', 'like', "%{$search}%");
                });
            }
            
            $logs = $query->orderBy('created_at', 'desc')
                         ->paginate($request->input('per_page', 20));
            
            return response()->json([
                'success' => true,
                'data' => $logs,
                'message' => '操作日志获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取操作日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取登录日志
     */
    public function getLoginLogs(Request $request): JsonResponse
    {
        try {
            $query = OperationLog::with('user:id,username,nickname')
                                ->where('action', 'login');
            
            // 用户筛选
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }
            
            // 时间范围筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }
            
            $logs = $query->orderBy('created_at', 'desc')
                         ->paginate($request->input('per_page', 20));
            
            return response()->json([
                'success' => true,
                'data' => $logs,
                'message' => '登录日志获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取登录日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取错误日志
     */
    public function getErrorLogs(Request $request): JsonResponse
    {
        try {
            $logPath = storage_path('logs');
            $logFiles = [];
            
            // 获取日志文件列表
            if (is_dir($logPath)) {
                $files = scandir($logPath);
                foreach ($files as $file) {
                    if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
                        $filePath = $logPath . '/' . $file;
                        $logFiles[] = [
                            'name' => $file,
                            'size' => filesize($filePath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
                            'path' => $filePath
                        ];
                    }
                }
            }
            
            // 如果指定了文件，读取文件内容
            if ($request->filled('file')) {
                $fileName = $request->file;
                $filePath = $logPath . '/' . $fileName;
                
                if (!file_exists($filePath) || !is_readable($filePath)) {
                    return response()->json([
                        'success' => false,
                        'message' => '日志文件不存在或不可读'
                    ], 404);
                }
                
                $lines = $request->input('lines', 100);
                $content = $this->readLogFile($filePath, $lines);
                
                return response()->json([
                    'success' => true,
                    'data' => [
                        'file' => $fileName,
                        'content' => $content,
                        'total_lines' => count(file($filePath))
                    ],
                    'message' => '日志内容获取成功'
                ]);
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'files' => $logFiles,
                    'total_files' => count($logFiles)
                ],
                'message' => '日志文件列表获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取错误日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取日志统计
     */
    public function getLogStatistics(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', '7d');
            $startDate = match($period) {
                '1d' => now()->startOfDay(),
                '7d' => now()->subDays(7),
                '30d' => now()->subDays(30),
                '90d' => now()->subDays(90),
                default => now()->subDays(7)
            };

            // 操作日志统计
            $operationStats = OperationLog::where('created_at', '>=', $startDate)
                ->selectRaw('
                    COUNT(*) as total_operations,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    module,
                    COUNT(*) as module_count
                ')
                ->groupBy('module')
                ->get();

            // 每日操作统计
            $dailyStats = OperationLog::where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // 用户活跃度统计
            $userActivityStats = OperationLog::where('created_at', '>=', $startDate)
                ->with('user:id,username,nickname')
                ->selectRaw('user_id, COUNT(*) as operation_count')
                ->groupBy('user_id')
                ->orderBy('operation_count', 'desc')
                ->limit(10)
                ->get();

            // IP访问统计
            $ipStats = OperationLog::where('created_at', '>=', $startDate)
                ->selectRaw('ip_address, COUNT(*) as access_count')
                ->groupBy('ip_address')
                ->orderBy('access_count', 'desc')
                ->limit(10)
                ->get();

            // 错误日志统计
            $errorStats = $this->getErrorLogStats();

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => $period,
                    'operation_stats' => $operationStats,
                    'daily_stats' => $dailyStats,
                    'user_activity' => $userActivityStats,
                    'ip_stats' => $ipStats,
                    'error_stats' => $errorStats,
                    'summary' => [
                        'total_operations' => $operationStats->sum('module_count'),
                        'unique_users' => OperationLog::where('created_at', '>=', $startDate)
                                                    ->distinct('user_id')->count(),
                        'unique_ips' => OperationLog::where('created_at', '>=', $startDate)
                                                  ->distinct('ip_address')->count(),
                    ]
                ],
                'message' => '日志统计获取成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取日志统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出日志
     */
    public function exportLogs(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|in:operation,login,error',
                'format' => 'sometimes|in:csv,json',
                'start_date' => 'sometimes|date',
                'end_date' => 'sometimes|date|after_or_equal:start_date',
                'user_id' => 'sometimes|exists:users,id'
            ]);

            $type = $validated['type'];
            $format = $validated['format'] ?? 'csv';
            
            $query = OperationLog::with('user:id,username,nickname');
            
            // 应用筛选条件
            if ($type === 'login') {
                $query->where('action', 'login');
            }
            
            if (isset($validated['start_date'])) {
                $query->where('created_at', '>=', $validated['start_date']);
            }
            
            if (isset($validated['end_date'])) {
                $query->where('created_at', '<=', $validated['end_date']);
            }
            
            if (isset($validated['user_id'])) {
                $query->where('user_id', $validated['user_id']);
            }
            
            $logs = $query->orderBy('created_at', 'desc')->get();
            
            // 生成文件
            $fileName = "logs_{$type}_" . now()->format('Y_m_d_H_i_s');
            
            if ($format === 'csv') {
                $content = $this->generateCsvContent($logs);
                $fileName .= '.csv';
                $mimeType = 'text/csv';
            } else {
                $content = json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                $fileName .= '.json';
                $mimeType = 'application/json';
            }
            
            // 保存到临时文件
            $tempPath = storage_path('app/temp/' . $fileName);
            if (!is_dir(dirname($tempPath))) {
                mkdir(dirname($tempPath), 0755, true);
            }
            
            file_put_contents($tempPath, $content);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'file_name' => $fileName,
                    'file_size' => filesize($tempPath),
                    'download_url' => route('api.logs.download', ['file' => $fileName]),
                    'records_count' => $logs->count()
                ],
                'message' => '日志导出成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '日志导出失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 下载导出的日志文件
     */
    public function downloadLog($fileName)
    {
        try {
            $filePath = storage_path('app/temp/' . $fileName);
            
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => '文件不存在或已过期'
                ], 404);
            }
            
            return response()->download($filePath)->deleteFileAfterSend();
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '文件下载失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清理日志
     */
    public function clearLogs(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|in:operation,error,all',
                'days' => 'sometimes|integer|min:1|max:365',
                'confirm' => 'required|boolean|accepted'
            ]);

            $type = $validated['type'];
            $days = $validated['days'] ?? 30;
            $cutoffDate = now()->subDays($days);
            
            $deletedCount = 0;
            
            if ($type === 'operation' || $type === 'all') {
                $deletedCount += OperationLog::where('created_at', '<', $cutoffDate)->delete();
            }
            
            if ($type === 'error' || $type === 'all') {
                // 清理错误日志文件
                $logPath = storage_path('logs');
                $files = glob($logPath . '/*.log');
                
                foreach ($files as $file) {
                    if (filemtime($file) < $cutoffDate->timestamp) {
                        unlink($file);
                    }
                }
            }
            
            // 记录清理操作
            OperationLog::create([
                'user_id' => auth()->id(),
                'action' => 'clear_logs',
                'module' => 'system',
                'description' => "清理了 {$days} 天前的 {$type} 日志，删除 {$deletedCount} 条记录",
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'deleted_count' => $deletedCount,
                    'cutoff_date' => $cutoffDate->toDateTimeString()
                ],
                'message' => '日志清理成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '日志清理失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取日志详情
     */
    public function getLogDetail($id): JsonResponse
    {
        try {
            $log = OperationLog::with('user:id,username,nickname,email')
                              ->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $log,
                'message' => '日志详情获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取日志详情失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 记录操作日志
     */
    public function recordOperation(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'action' => 'required|string|max:255',
                'module' => 'required|string|max:100',
                'description' => 'nullable|string|max:1000',
                'target_type' => 'nullable|string|max:100',
                'target_id' => 'nullable|integer',
                'extra_data' => 'nullable|array'
            ]);

            $log = OperationLog::create([
                'user_id' => auth()->id(),
                'action' => $validated['action'],
                'module' => $validated['module'],
                'description' => $validated['description'] ?? '',
                'target_type' => $validated['target_type'],
                'target_id' => $validated['target_id'],
                'extra_data' => $validated['extra_data'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'data' => $log,
                'message' => '操作日志记录成功'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作日志记录失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // 私有辅助方法

    /**
     * 读取日志文件
     */
    private function readLogFile(string $filePath, int $lines = 100): array
    {
        $file = new \SplFileObject($filePath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $content = [];
        
        $file->seek($startLine);
        while (!$file->eof() && count($content) < $lines) {
            $line = trim($file->fgets());
            if (!empty($line)) {
                $content[] = [
                    'line_number' => $file->key(),
                    'content' => $line,
                    'timestamp' => $this->extractTimestamp($line),
                    'level' => $this->extractLogLevel($line)
                ];
            }
        }
        
        return array_reverse($content);
    }

    /**
     * 提取日志时间戳
     */
    private function extractTimestamp(string $line): ?string
    {
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 提取日志级别
     */
    private function extractLogLevel(string $line): ?string
    {
        if (preg_match('/\.(ERROR|WARNING|INFO|DEBUG|CRITICAL|ALERT|EMERGENCY):/', $line, $matches)) {
            return strtolower($matches[1]);
        }
        return null;
    }

    /**
     * 获取错误日志统计
     */
    private function getErrorLogStats(): array
    {
        $logPath = storage_path('logs');
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'error_count' => 0,
            'warning_count' => 0,
            'latest_error' => null
        ];
        
        if (is_dir($logPath)) {
            $files = glob($logPath . '/*.log');
            $stats['total_files'] = count($files);
            
            foreach ($files as $file) {
                $stats['total_size'] += filesize($file);
                
                // 简单统计错误和警告数量
                $content = file_get_contents($file);
                $stats['error_count'] += substr_count($content, '.ERROR:');
                $stats['warning_count'] += substr_count($content, '.WARNING:');
            }
        }
        
        return $stats;
    }

    /**
     * 生成CSV内容
     */
    private function generateCsvContent($logs): string
    {
        $csv = "ID,用户,操作,模块,描述,IP地址,时间\n";
        
        foreach ($logs as $log) {
            $csv .= implode(',', [
                $log->id,
                '"' . ($log->user->nickname ?? $log->user->username ?? '未知用户') . '"',
                '"' . $log->action . '"',
                '"' . $log->module . '"',
                '"' . str_replace('"', '""', $log->description) . '"',
                $log->ip_address,
                $log->created_at->format('Y-m-d H:i:s')
            ]) . "\n";
        }
        
        return $csv;
    }
}