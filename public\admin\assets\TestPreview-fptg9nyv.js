import{_ as e,u as a}from"./index-DtXAftX0.js";import{af as s,r as l,c as t,e as o,k as n,l as r,t as u,B as i,E as c,z as v,D as d}from"./vue-vendor-Dy164gUc.js";import{U as p,at as m,Q as f}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const _={class:"test-preview"},h={class:"test-info"},k={class:"test-actions"},I={key:0,class:"api-result"},g=e({__name:"TestPreview",setup(e){const g=s(),w=a(),P=l(""),A=l(""),b=t(()=>"true"===localStorage.getItem("preview-mode")),j=t(()=>w.userInfo||{}),T=t(()=>w.token),x=()=>{P.value=(new Date).toLocaleString()},y=async()=>{try{const e=await fetch("/api/v1/test",{headers:{Authorization:`Bearer ${w.token}`}});A.value=await e.text(),f.success("API测试完成")}catch(e){A.value=`API错误: ${e.message}`,f.error("API测试失败")}},D=()=>{g.push("/dashboard"),f.info("导航到Dashboard")};return o(()=>{x(),setInterval(x,1e3),console.log("TestPreview组件已挂载"),console.log("预览模式:",b.value),console.log("用户信息:",j.value),console.log("Token:",T.value)}),(e,a)=>{const s=m;return r(),n("div",_,[a[3]||(a[3]=u("h1",null,"预览页面测试",-1)),u("div",h,[u("p",null,"当前时间: "+p(P.value),1),u("p",null,"预览模式: "+p(b.value?"已启用":"未启用"),1),u("p",null,"用户信息: "+p(j.value.nickname||"未设置"),1),u("p",null,"Token: "+p(T.value?"已设置":"未设置"),1)]),u("div",k,[c(s,{onClick:y},{default:v(()=>a[0]||(a[0]=[d("测试API",-1)])),_:1,__:[0]}),c(s,{onClick:D},{default:v(()=>a[1]||(a[1]=[d("测试导航",-1)])),_:1,__:[1]})]),A.value?(r(),n("div",I,[a[2]||(a[2]=u("h3",null,"API测试结果:",-1)),u("pre",null,p(A.value),1)])):i("",!0)])}}},[["__scopeId","data-v-2b9eddbd"]]);export{g as default};
