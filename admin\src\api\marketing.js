import api from './index'

/**
 * 营销功能相关API
 */
export const marketingApi = {
  // 获取群组营销配置
  getMarketingConfig(groupId) {
    return api.get(`/groups/${groupId}/marketing-config`)
  },

  // 更新群组营销配置
  updateMarketingConfig(groupId, data) {
    return api.put(`/groups/${groupId}/marketing-config`, data)
  },

  // 生成虚拟群友数据
  generateVirtualMembers(groupId, count = 13) {
    return api.post(`/groups/${groupId}/virtual-members`, { count })
  },

  // 预览群组展示效果
  previewGroup(groupId, city = '北京') {
    return api.get(`/groups/${groupId}/preview`, { params: { city } })
  },

  // 测试城市定位功能
  testCityLocation(groupId, testCity) {
    return api.post(`/groups/${groupId}/test-city`, { test_city: testCity })
  },

  // 应用营销模板
  applyMarketingTemplate(groupId, templateId) {
    return api.post(`/groups/${groupId}/apply-template`, { template_id: templateId })
  },

  // 获取营销模板列表
  getMarketingTemplates() {
    return api.get('/marketing-templates')
  },

  // 批量更新群组营销配置
  batchUpdateMarketing(groupIds, marketingConfig) {
    return api.post('/groups/batch-marketing', {
      group_ids: groupIds,
      marketing_config: marketingConfig
    })
  },

  // 批量应用营销模板
  batchApplyTemplate(groupIds, templateId) {
    return api.post('/groups/batch-marketing', {
      group_ids: groupIds,
      template_id: templateId
    })
  }
}

/**
 * 城市定位相关API
 */
export const locationApi = {
  // 通过IP获取城市
  getLocationByIP(ip) {
    return api.get('/location/ip', { params: { ip } })
  },

  // 坐标反向地理编码
  reverseGeocode(latitude, longitude) {
    return api.post('/location/reverse', { latitude, longitude })
  },

  // 获取城市列表
  getCities() {
    return api.get('/location/cities')
  },

  // 智能城市推荐
  recommendCities() {
    return api.get('/location/recommend')
  },

  // 批量IP定位
  batchLocation(ips) {
    return api.post('/location/batch', { ips })
  }
}

/**
 * 防封系统相关API
 */
export const antiBlockApi = {
  // 获取域名健康状态
  getDomainHealth() {
    return api.get('/anti-block/domain-health')
  },

  // 检查域名健康
  checkDomainHealth(domain) {
    return api.post('/anti-block/check-domain', { domain })
  },

  // 获取浏览器统计
  getBrowserStats(days = 7) {
    return api.get('/anti-block/browser-stats', { params: { days } })
  },

  // 验证群组访问
  validateGroupAccess(groupId) {
    return api.post(`/anti-block/validate-access/${groupId}`)
  },

  // 获取访问报告
  getAccessReport(groupId, period = '7days') {
    return api.get(`/anti-block/access-report/${groupId}`, { params: { period } })
  }
}

/**
 * 群组管理增强API
 */
export const groupApi = {
  // 获取群组列表（带营销信息）
  getGroupsWithMarketing(params) {
    return api.get('/wechat-groups', { params })
  },

  // 获取群组详细信息
  getGroupDetail(groupId) {
    return api.get(`/wechat-groups/${groupId}`)
  },

  // 获取群组统计数据
  getGroupStats(groupId) {
    return api.get(`/wechat-groups/${groupId}/detailed-stats`)
  },

  // 获取群组访问统计
  getGroupAccessStats(groupId, days = 7) {
    return api.get(`/group/${groupId}/stats`, { params: { days } })
  },

  // 获取群组地理分布
  getGroupGeographic(groupId, days = 7) {
    return api.get(`/group/${groupId}/geographic`, { params: { days } })
  }
}

export default {
  marketing: marketingApi,
  location: locationApi,
  antiBlock: antiBlockApi,
  group: groupApi
}