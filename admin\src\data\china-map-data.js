/**
 * 中国地图数据配置
 * 包含省份坐标、边界数据和样式配置
 */

// 中国主要省份数据
export const chinaProvinces = [
  { name: '北京', code: 'BJ', value: 2580, x: 116.4, y: 39.9, color: '#ff6b6b' },
  { name: '上海', code: 'SH', value: 2340, x: 121.5, y: 31.2, color: '#4ecdc4' },
  { name: '广东', code: 'GD', value: 3200, x: 113.3, y: 23.1, color: '#45b7d1' },
  { name: '浙江', code: 'ZJ', value: 1890, x: 120.2, y: 30.3, color: '#96ceb4' },
  { name: '江苏', code: 'JS', value: 2100, x: 118.8, y: 32.1, color: '#feca57' },
  { name: '山东', code: 'SD', value: 1750, x: 117.0, y: 36.7, color: '#ff9ff3' },
  { name: '河南', code: 'HN', value: 1420, x: 113.6, y: 34.8, color: '#54a0ff' },
  { name: '四川', code: 'SC', value: 1680, x: 104.1, y: 30.7, color: '#5f27cd' },
  { name: '湖北', code: 'HB', value: 1350, x: 114.3, y: 30.6, color: '#00d2d3' },
  { name: '湖南', code: 'HN2', value: 1280, x: 112.9, y: 28.2, color: '#ff6348' },
  { name: '安徽', code: 'AH', value: 1150, x: 117.3, y: 31.9, color: '#ff9f43' },
  { name: '河北', code: 'HEB', value: 1320, x: 114.5, y: 38.0, color: '#10ac84' },
  { name: '江西', code: 'JX', value: 980, x: 115.9, y: 28.7, color: '#ee5a6f' },
  { name: '山西', code: 'SX', value: 890, x: 112.5, y: 37.9, color: '#0abde3' },
  { name: '辽宁', code: 'LN', value: 1100, x: 123.4, y: 41.8, color: '#3742fa' },
  { name: '福建', code: 'FJ', value: 1050, x: 119.3, y: 26.1, color: '#2ed573' },
  { name: '陕西', code: 'SX2', value: 920, x: 108.9, y: 34.3, color: '#ff4757' },
  { name: '黑龙江', code: 'HLJ', value: 780, x: 126.6, y: 45.8, color: '#5352ed' },
  { name: '广西', code: 'GX', value: 850, x: 108.3, y: 22.8, color: '#ff6b81' },
  { name: '云南', code: 'YN', value: 720, x: 102.7, y: 25.0, color: '#70a1ff' },
  { name: '贵州', code: 'GZ', value: 650, x: 106.7, y: 26.6, color: '#7bed9f' },
  { name: '吉林', code: 'JL', value: 580, x: 125.3, y: 43.9, color: '#ff7675' },
  { name: '重庆', code: 'CQ', value: 890, x: 106.5, y: 29.6, color: '#fd79a8' },
  { name: '天津', code: 'TJ', value: 760, x: 117.2, y: 39.1, color: '#fdcb6e' },
  { name: '内蒙古', code: 'NMG', value: 520, x: 111.8, y: 40.8, color: '#6c5ce7' },
  { name: '甘肃', code: 'GS', value: 480, x: 103.8, y: 36.1, color: '#a29bfe' },
  { name: '新疆', code: 'XJ', value: 420, x: 87.6, y: 43.8, color: '#fd79a8' },
  { name: '海南', code: 'HI', value: 380, x: 110.3, y: 20.0, color: '#00b894' },
  { name: '宁夏', code: 'NX', value: 280, x: 106.3, y: 38.5, color: '#e17055' },
  { name: '青海', code: 'QH', value: 320, x: 101.8, y: 36.6, color: '#81ecec' },
  { name: '西藏', code: 'XZ', value: 180, x: 91.1, y: 29.6, color: '#fab1a0' }
]

// 地图样式配置
export const mapConfig = {
  // 地图容器配置
  container: {
    width: '100%',
    height: '300px',
    background: 'rgba(255, 255, 255, 0.05)',
    borderRadius: '12px',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  
  // 省份样式配置
  province: {
    default: {
      fill: 'rgba(59, 130, 246, 0.3)',
      stroke: 'rgba(255, 255, 255, 0.5)',
      strokeWidth: 1
    },
    hover: {
      fill: 'rgba(59, 130, 246, 0.6)',
      stroke: 'rgba(255, 255, 255, 0.8)',
      strokeWidth: 2
    },
    active: {
      fill: 'rgba(59, 130, 246, 0.8)',
      stroke: '#ffffff',
      strokeWidth: 2
    }
  },
  
  // 数据点样式
  dataPoint: {
    minRadius: 3,
    maxRadius: 15,
    colors: {
      low: '#4ade80',    // 绿色 - 低值
      medium: '#fbbf24', // 黄色 - 中值
      high: '#f87171',   // 红色 - 高值
      max: '#dc2626'     // 深红 - 最高值
    }
  },
  
  // 工具提示配置
  tooltip: {
    background: 'rgba(0, 0, 0, 0.8)',
    color: '#ffffff',
    borderRadius: '8px',
    padding: '12px',
    fontSize: '14px'
  }
}

// 地图投影配置
export const projectionConfig = {
  center: [104, 35.5], // 中国中心坐标
  scale: 1,
  translate: [0, 0]
}

// 数据值范围配置
export const dataRanges = {
  low: { min: 0, max: 500, color: '#4ade80', label: '较少' },
  medium: { min: 501, max: 1000, color: '#fbbf24', label: '一般' },
  high: { min: 1001, max: 2000, color: '#f87171', label: '较多' },
  max: { min: 2001, max: Infinity, color: '#dc2626', label: '很多' }
}

// 获取省份数据值对应的颜色
export const getProvinceColor = (value) => {
  for (const [key, range] of Object.entries(dataRanges)) {
    if (value >= range.min && value <= range.max) {
      return range.color
    }
  }
  return dataRanges.low.color
}

// 获取省份数据值对应的标签
export const getProvinceLabel = (value) => {
  for (const [key, range] of Object.entries(dataRanges)) {
    if (value >= range.min && value <= range.max) {
      return range.label
    }
  }
  return dataRanges.low.label
}

// 模拟地域数据生成
export const generateMockRegionData = () => {
  return chinaProvinces.map(province => ({
    ...province,
    value: Math.floor(Math.random() * 3000) + 100,
    growth: (Math.random() * 20 - 10).toFixed(1), // -10% 到 +10%
    users: Math.floor(Math.random() * 10000) + 1000,
    orders: Math.floor(Math.random() * 500) + 50
  }))
}

// 地图交互事件配置
export const mapEvents = {
  onProvinceHover: (province, event) => {
    console.log('Province hovered:', province.name)
  },
  onProvinceClick: (province, event) => {
    console.log('Province clicked:', province.name)
  },
  onProvinceLeave: (province, event) => {
    console.log('Province left:', province.name)
  }
}

// ECharts地图配置
export const echartsMapOption = {
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    textStyle: {
      color: '#ffffff',
      fontSize: 14
    },
    formatter: function(params) {
      const data = params.data || {}
      return `
        <div style="padding: 8px;">
          <div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
          <div>用户数量: ${data.value || 0}</div>
          <div>增长率: ${data.growth || 0}%</div>
          <div>订单数: ${data.orders || 0}</div>
        </div>
      `
    }
  },
  visualMap: {
    min: 0,
    max: 3000,
    left: 'left',
    top: 'bottom',
    text: ['高', '低'],
    textStyle: {
      color: '#ffffff'
    },
    inRange: {
      color: ['#4ade80', '#fbbf24', '#f87171', '#dc2626']
    },
    calculable: true
  },
  geo: {
    map: 'china',
    roam: true,
    scaleLimit: {
      min: 0.8,
      max: 2
    },
    itemStyle: {
      areaColor: 'rgba(59, 130, 246, 0.1)',
      borderColor: 'rgba(255, 255, 255, 0.3)',
      borderWidth: 1
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(59, 130, 246, 0.3)',
        borderColor: 'rgba(255, 255, 255, 0.8)',
        borderWidth: 2
      }
    },
    label: {
      show: false,
      color: '#ffffff',
      fontSize: 12
    }
  },
  series: [
    {
      name: '用户分布',
      type: 'map',
      map: 'china',
      geoIndex: 0,
      data: []
    },
    {
      name: '热点数据',
      type: 'scatter',
      coordinateSystem: 'geo',
      data: [],
      symbolSize: function(val) {
        return Math.max(val[2] / 100, 6)
      },
      itemStyle: {
        color: '#feca57',
        shadowBlur: 10,
        shadowColor: '#feca57'
      },
      emphasis: {
        itemStyle: {
          color: '#ff6b6b'
        }
      }
    }
  ]
}
