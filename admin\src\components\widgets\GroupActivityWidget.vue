<template>
  <div class="group-activity-widget">
    <div class="activity-header">
      <div class="header-info">
        <h4>📱 群组活动概览</h4>
        <p>实时群组动态和创建统计</p>
      </div>
      <el-button 
        type="primary" 
        size="small" 
        @click="createGroup"
        class="create-group-btn"
      >
        <el-icon><Plus /></el-icon>
        创建群组
      </el-button>
    </div>

    <div class="activity-stats">
      <div class="stat-item">
        <div class="stat-icon active">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ activeGroups }}</div>
          <div class="stat-label">活跃群组</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon created">
          <el-icon><Plus /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ todayCreated }}</div>
          <div class="stat-label">今日新建</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon members">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ totalMembers }}</div>
          <div class="stat-label">总成员数</div>
        </div>
      </div>
    </div>

    <div class="recent-activities">
      <div class="activities-header">
        <h5>🕒 最近活动</h5>
        <el-button text size="small" @click="viewAllActivities">
          查看全部
        </el-button>
      </div>
      
      <div class="activities-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
          :class="{ 'group-creation': activity.type === 'group_created' }"
        >
          <div class="activity-avatar">
            <el-avatar :size="32" :src="activity.user.avatar">
              {{ activity.user.name?.charAt(0) }}
            </el-avatar>
          </div>
          
          <div class="activity-content">
            <div class="activity-text">
              <span class="user-name">{{ activity.user.name }}</span>
              <span class="activity-action">{{ activity.action }}</span>
              <span class="group-name" v-if="activity.groupName">
                "{{ activity.groupName }}"
              </span>
            </div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
          
          <div class="activity-badge" v-if="activity.type === 'group_created'">
            <el-tag type="success" size="small">新建</el-tag>
          </div>
          
          <div class="activity-actions">
            <el-button 
              v-if="activity.actionable" 
              size="small" 
              text 
              @click="handleActivityAction(activity)"
            >
              {{ activity.actionText }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="quick-actions" v-if="config.showGroupCreation">
      <div class="actions-header">
        <h5>⚡ 快速操作</h5>
      </div>
      
      <div class="actions-grid">
        <div class="action-card primary" @click="createGroup">
          <div class="action-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="action-text">
            <div class="action-title">创建群组</div>
            <div class="action-desc">快速创建新群组</div>
          </div>
        </div>
        
        <div class="action-card" @click="manageGroups">
          <div class="action-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="action-text">
            <div class="action-title">管理群组</div>
            <div class="action-desc">群组设置管理</div>
          </div>
        </div>
        
        <div class="action-card" @click="viewAnalytics">
          <div class="action-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="action-text">
            <div class="action-title">数据分析</div>
            <div class="action-desc">群组数据统计</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePreferencesStore } from '@/stores/preferences'
import { 
  Plus, ChatDotRound, User, Setting, DataAnalysis 
} from '@element-plus/icons-vue'

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ 
      limit: 10, 
      showGroupCreation: true 
    })
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update'])

// 依赖注入
const router = useRouter()
const preferencesStore = usePreferencesStore()

// 响应式数据
const activeGroups = ref(156)
const todayCreated = ref(8)
const totalMembers = ref(12580)

const recentActivities = ref([
  {
    id: 1,
    type: 'group_created',
    user: {
      name: '张三',
      avatar: ''
    },
    action: '创建了群组',
    groupName: '产品交流群',
    time: new Date(Date.now() - 1000 * 60 * 15),
    actionable: true,
    actionText: '查看'
  },
  {
    id: 2,
    type: 'member_joined',
    user: {
      name: '李四',
      avatar: ''
    },
    action: '加入了群组',
    groupName: '技术讨论群',
    time: new Date(Date.now() - 1000 * 60 * 30),
    actionable: false
  },
  {
    id: 3,
    type: 'group_created',
    user: {
      name: '王五',
      avatar: ''
    },
    action: '创建了群组',
    groupName: '营销策划群',
    time: new Date(Date.now() - 1000 * 60 * 45),
    actionable: true,
    actionText: '查看'
  },
  {
    id: 4,
    type: 'group_updated',
    user: {
      name: '赵六',
      avatar: ''
    },
    action: '更新了群组设置',
    groupName: '客服支持群',
    time: new Date(Date.now() - 1000 * 60 * 60),
    actionable: false
  },
  {
    id: 5,
    type: 'member_left',
    user: {
      name: '孙七',
      avatar: ''
    },
    action: '离开了群组',
    groupName: '测试群组',
    time: new Date(Date.now() - 1000 * 60 * 90),
    actionable: false
  }
])

// 计算属性
const displayActivities = computed(() => {
  return recentActivities.value.slice(0, props.config.limit || 5)
})

// 方法
const createGroup = () => {
  // 记录功能使用
  preferencesStore.recordFeatureUsage('create_group')
  router.push('/groups/create')
}

const manageGroups = () => {
  preferencesStore.recordFeatureUsage('manage_groups')
  router.push('/groups')
}

const viewAnalytics = () => {
  preferencesStore.recordFeatureUsage('group_analytics')
  router.push('/analytics/groups')
}

const viewAllActivities = () => {
  router.push('/activities/groups')
}

const handleActivityAction = (activity) => {
  if (activity.type === 'group_created') {
    router.push(`/groups/${activity.groupId || 'new'}`)
  }
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return time.toLocaleDateString('zh-CN', { 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 模拟数据更新
const updateData = () => {
  // 模拟实时数据更新
  activeGroups.value = 150 + Math.floor(Math.random() * 20)
  todayCreated.value = 5 + Math.floor(Math.random() * 10)
  totalMembers.value = 12000 + Math.floor(Math.random() * 1000)
  
  // 通知父组件数据更新
  emit('update', 'group_activity', {
    activeGroups: activeGroups.value,
    todayCreated: todayCreated.value,
    totalMembers: totalMembers.value
  })
}

// 生命周期
onMounted(() => {
  // 定期更新数据
  const interval = setInterval(updateData, 30000) // 30秒更新一次
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style lang="scss" scoped>
.group-activity-widget {
  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-info {
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    .create-group-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }
    }
  }

  .activity-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .stat-item {
      display: flex;
      align-items: center;
      padding: 12px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;

      .stat-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 16px;

        &.active {
          background: #dcfce7;
          color: #16a34a;
        }

        &.created {
          background: #dbeafe;
          color: #2563eb;
        }

        &.members {
          background: #fef3c7;
          color: #d97706;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 18px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .stat-label {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
  }

  .recent-activities {
    margin-bottom: 24px;

    .activities-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h5 {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .activities-list {
      max-height: 200px;
      overflow-y: auto;

      .activity-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f1f5f9;

        &:last-child {
          border-bottom: none;
        }

        &.group-creation {
          background: linear-gradient(90deg, #f0f9ff 0%, transparent 100%);
          border-left: 3px solid #3b82f6;
          padding-left: 12px;
          margin-left: -12px;
          border-radius: 4px;
        }

        .activity-avatar {
          margin-right: 12px;
        }

        .activity-content {
          flex: 1;

          .activity-text {
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 2px;

            .user-name {
              font-weight: 600;
              color: #3b82f6;
            }

            .group-name {
              font-weight: 500;
              color: #059669;
            }
          }

          .activity-time {
            font-size: 12px;
            color: #9ca3af;
          }
        }

        .activity-badge {
          margin-right: 8px;
        }

        .activity-actions {
          margin-left: 8px;
        }
      }
    }
  }

  .quick-actions {
    .actions-header {
      margin-bottom: 12px;

      h5 {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;

      .action-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 12px;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          border-color: #d1d5db;
          transform: translateY(-1px);
        }

        &.primary {
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
          border-color: #3b82f6;

          &:hover {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          }

          .action-icon {
            color: #3b82f6;
          }
        }

        .action-icon {
          font-size: 20px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .action-text {
          text-align: center;

          .action-title {
            font-size: 12px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;
          }

          .action-desc {
            font-size: 11px;
            color: #6b7280;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .group-activity-widget {
    .activity-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .activity-stats {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .quick-actions {
      .actions-grid {
        grid-template-columns: 1fr;
        gap: 8px;

        .action-card {
          flex-direction: row;
          text-align: left;

          .action-icon {
            margin-right: 12px;
            margin-bottom: 0;
          }

          .action-text {
            text-align: left;
          }
        }
      }
    }
  }
}
</style>