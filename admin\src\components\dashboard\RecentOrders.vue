<template>
  <div class="recent-orders-card">
    <div class="card-header">
      <h3 class="card-title">最新订单</h3>
      <el-button text @click="viewAll">
        查看全部
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <div class="orders-list">
      <div 
        v-for="order in orders" 
        :key="order.id"
        class="order-item"
        @click="viewDetail(order.id)"
      >
        <div class="order-avatar">
          <el-avatar 
            :size="40" 
            :style="{ 
              background: generateAvatarColor(order.userName),
              color: 'white',
              fontWeight: '600'
            }"
          >
            {{ getAvatarText(order.userName) }}
          </el-avatar>
        </div>
        <div class="order-info">
          <div class="order-title">{{ order.groupName }}</div>
          <div class="order-meta">
            {{ order.userName }} · {{ formatTime(order.createdAt) }}
          </div>
        </div>
        <div class="order-amount">
          ¥{{ order.amount }}
        </div>
        <div class="order-status" :class="order.status">
          {{ getOrderStatusText(order.status) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  }
})

const viewAll = () => {
  ElMessage.info('跳转到订单列表页面')
}

const viewDetail = (orderId) => {
  ElMessage.info(`查看订单详情: ${orderId}`)
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return date.toLocaleDateString()
}

const getOrderStatusText = (status) => {
  const statusMap = {
    paid: '已支付',
    pending: '待支付',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const generateAvatarColor = (name) => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  ]
  
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  const index = Math.abs(hash) % colors.length
  return colors[index]
}

const getAvatarText = (name) => {
  if (!name) return '?'
  if (/[\u4e00-\u9fa5]/.test(name)) {
    return name.slice(-1)
  }
  return name.charAt(0).toUpperCase()
}
</script>

<style lang="scss" scoped>
.recent-orders-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .orders-list {
    .order-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 0;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f9fafb;
        border-radius: 12px;
        padding: 16px 12px;
        margin: 0 -12px;
      }

      .order-info {
        flex: 1;

        .order-title {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .order-meta {
          font-size: 12px;
          color: #6b7280;
        }
      }

      .order-amount {
        font-weight: 600;
        color: #10b981;
        font-size: 14px;
      }

      .order-status {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 500;

        &.paid {
          background: #dcfce7;
          color: #166534;
        }

        &.pending {
          background: #fef3c7;
          color: #92400e;
        }

        &.cancelled {
          background: #fee2e2;
          color: #991b1b;
        }
      }
    }
  }
}
</style>