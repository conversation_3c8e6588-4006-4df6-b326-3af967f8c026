<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\EnhancedGroupManagementService;
use App\Services\GroupOperationService;
use App\Services\GroupRecommendationService;
use App\Services\GroupTemplateService;
use App\Models\WechatGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Exception;

/**
 * 增强群组管理API控制器
 * 提供智能化的群组管理、优化、监控功能
 */
class EnhancedGroupController extends Controller
{
    public function __construct(
        private readonly EnhancedGroupManagementService $enhancedService,
        private readonly GroupOperationService $operationService,
        private readonly GroupRecommendationService $recommendationService,
        private readonly GroupTemplateService $templateService
    ) {
        $this->middleware('auth:api');
        $this->middleware('throttle:30,1')->only(['createSmartGroup', 'batchOptimize']);
        $this->middleware('throttle:60,1')->only(['monitorPerformance', 'lifecycleManagement']);
    }

    /**
     * 智能群组创建
     */
    public function createSmartGroup(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:200',
            'description' => 'nullable|string|max:2000',
            'price' => 'required|numeric|min:0|max:9999',
            'member_limit' => 'required|integer|min:1|max:2000',
            'category' => 'nullable|string|max:50',
            'template_key' => 'nullable|string',
            'city' => 'nullable|string|max:20',
            'apply_optimizations' => 'nullable|boolean',
            'auto_setup' => 'nullable|boolean',
            'custom_settings' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $groupData = $request->all();
            
            // 如果指定了模板，先从模板创建
            if ($request->filled('template_key')) {
                $template = $this->templateService->createGroupFromTemplate(
                    $request->template_key,
                    $user,
                    $groupData
                );
                
                return $this->successResponse('基于模板创建群组成功', [
                    'group' => $template->load(['user', 'substation']),
                    'template_used' => $request->template_key,
                ]);
            }
            
            // 智能创建群组
            $result = $this->enhancedService->createSmartGroup($groupData, $user);
            
            return $this->successResponse('智能群组创建成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('群组创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 群组生命周期管理
     */
    public function lifecycleManagement(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限管理此群组', null, 403);
            }

            $managementPlan = $this->enhancedService->manageGroupLifecycle($group);

            return $this->successResponse('生命周期管理分析完成', $managementPlan);

        } catch (Exception $e) {
            return $this->errorResponse('生命周期管理失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 智能定价策略
     */
    public function smartPricingStrategy(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $pricingStrategy = $this->enhancedService->generateSmartPricingStrategy($group);

            return $this->successResponse('智能定价策略生成成功', $pricingStrategy);

        } catch (Exception $e) {
            return $this->errorResponse('定价策略生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 群组性能实时监控
     */
    public function monitorPerformance(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'nullable|array|max:50',
            'group_ids.*' => 'integer|exists:wechat_groups,id',
            'include_alerts' => 'nullable|boolean',
            'include_recommendations' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $groupIds = $request->group_ids;

            // 如果指定了群组ID，验证权限
            if ($groupIds) {
                $userGroups = WechatGroup::whereIn('id', $groupIds)
                    ->where(function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                        if ($user->isAdmin()) {
                            $query->orWhere('id', '>', 0); // 管理员可以查看所有群组
                        }
                    })
                    ->pluck('id')
                    ->toArray();

                if (count($userGroups) !== count($groupIds)) {
                    return $this->errorResponse('部分群组无权限访问', null, 403);
                }
            } else {
                // 获取用户的所有群组
                $groupIds = WechatGroup::where('user_id', $user->id)
                    ->where('status', WechatGroup::STATUS_ACTIVE)
                    ->pluck('id')
                    ->toArray();
            }

            $monitoringResults = $this->enhancedService->monitorGroupPerformance($groupIds);

            return $this->successResponse('性能监控完成', $monitoringResults);

        } catch (Exception $e) {
            return $this->errorResponse('性能监控失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量群组优化
     */
    public function batchOptimize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'required|array|min:1|max:50',
            'group_ids.*' => 'required|integer|exists:wechat_groups,id',
            'optimization_options' => 'nullable|array',
            'optimization_options.optimize_title' => 'nullable|boolean',
            'optimization_options.optimize_price' => 'nullable|boolean',
            'optimization_options.optimize_content' => 'nullable|boolean',
            'optimization_options.optimize_virtual_data' => 'nullable|boolean',
            'auto_apply' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            
            // 验证用户权限
            $userGroups = WechatGroup::whereIn('id', $request->group_ids)
                ->where('user_id', $user->id)
                ->pluck('id')
                ->toArray();

            if (count($userGroups) !== count($request->group_ids)) {
                return $this->errorResponse('部分群组无权限操作', null, 403);
            }

            $optimizationOptions = array_merge([
                'optimize_title' => true,
                'optimize_price' => true,
                'optimize_content' => true,
                'optimize_virtual_data' => true,
                'auto_apply' => false,
            ], $request->optimization_options ?? []);

            $results = $this->enhancedService->batchOptimizeGroups(
                $request->group_ids,
                $optimizationOptions
            );

            return $this->successResponse('批量优化完成', $results);

        } catch (Exception $e) {
            return $this->errorResponse('批量优化失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建A/B测试
     */
    public function createABTest(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'test_type' => 'required|string|in:title,price,description,image',
            'variant_a' => 'required|array',
            'variant_b' => 'required|array',
            'traffic_split' => 'nullable|integer|min:10|max:90',
            'duration' => 'nullable|integer|min:1|max:30',
            'success_metric' => 'nullable|string|in:conversion_rate,revenue,engagement',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限操作此群组', null, 403);
            }

            $testConfig = $request->all();
            $abTest = $this->enhancedService->createGroupABTest($group, $testConfig);

            return $this->successResponse('A/B测试创建成功', $abTest);

        } catch (Exception $e) {
            return $this->errorResponse('A/B测试创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 群组健康度检查
     */
    public function healthCheck(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $healthReport = $this->operationService->calculateGroupHealth($group);

            return $this->successResponse('健康度检查完成', $healthReport);

        } catch (Exception $e) {
            return $this->errorResponse('健康度检查失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 营销策略推荐
     */
    public function marketingRecommendations(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $recommendations = $this->operationService->getMarketingRecommendations($group);

            return $this->successResponse('营销策略推荐生成成功', $recommendations);

        } catch (Exception $e) {
            return $this->errorResponse('营销策略推荐失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 个性化群组推荐
     */
    public function personalizedRecommendations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50',
            'context' => 'nullable|array',
            'include_reasons' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $context = array_merge($request->context ?? [], [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
            ]);
            
            $limit = $request->limit ?? 10;
            
            $recommendations = $this->recommendationService->recommendGroups($user, $context, $limit);

            return $this->successResponse('个性化推荐生成成功', $recommendations);

        } catch (Exception $e) {
            return $this->errorResponse('个性化推荐失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 群组模板管理
     */
    public function getTemplates(Request $request): JsonResponse
    {
        try {
            $templates = $this->templateService->getPresetTemplates();
            $usageStats = $this->templateService->getTemplateUsageStats();
            $userRecommendations = $this->templateService->recommendTemplatesForUser(Auth::user());

            return $this->successResponse('模板获取成功', [
                'preset_templates' => $templates,
                'usage_statistics' => $usageStats,
                'user_recommendations' => $userRecommendations,
                'quick_create_configs' => $this->templateService->getQuickCreateConfigs(),
            ]);

        } catch (Exception $e) {
            return $this->errorResponse('模板获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量创建群组
     */
    public function batchCreateFromTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template' => 'required|string',
            'cities' => 'required|array|min:1|max:20',
            'cities.*' => 'string|max:20',
            'count' => 'nullable|integer|min:1|max:10',
            'customization' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $batchConfig = $request->all();
            
            $results = $this->templateService->batchCreateGroups($batchConfig, $user);

            return $this->successResponse('批量创建完成', [
                'results' => $results,
                'summary' => [
                    'total_attempts' => count($results),
                    'successful' => count(array_filter($results, fn($r) => $r['success'])),
                    'failed' => count(array_filter($results, fn($r) => !$r['success'])),
                ],
            ]);

        } catch (Exception $e) {
            return $this->errorResponse('批量创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 导出群组为模板
     */
    public function exportAsTemplate(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return $this->errorResponse('无权限操作此群组', null, 403);
            }

            $template = $this->templateService->exportGroupAsTemplate($group, $request->template_name);

            return $this->successResponse('模板导出成功', $template);

        } catch (Exception $e) {
            return $this->errorResponse('模板导出失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 群组搜索建议
     */
    public function searchSuggestions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:1|max:100',
            'limit' => 'nullable|integer|min:1|max:20',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $query = $request->query;
            $limit = $request->limit ?? 10;
            
            $suggestions = $this->recommendationService->getPersonalizedSearchSuggestions($query, $user, $limit);

            return $this->successResponse('搜索建议生成成功', $suggestions);

        } catch (Exception $e) {
            return $this->errorResponse('搜索建议生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取推荐标签
     */
    public function getRecommendationTags(): JsonResponse
    {
        try {
            $tags = $this->recommendationService->getRecommendationTags();

            return $this->successResponse('推荐标签获取成功', $tags);

        } catch (Exception $e) {
            return $this->errorResponse('推荐标签获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 统一成功响应格式
     */
    private function successResponse(string $message, array $data = []): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp,
        ]);
    }

    /**
     * 统一错误响应格式
     */
    private function errorResponse(string $message, $errors = null, int $code = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->timestamp,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }
}