<template>
  <div class="data-screen-demo">
    <!-- 演示控制面板 -->
    <div class="demo-controls">
      <div class="controls-header">
        <h2>数据大屏演示中心</h2>
        <p>体验不同风格的现代化数据可视化大屏</p>
      </div>
      
      <div class="screen-options">
        <div class="option-card" 
             v-for="screen in screenOptions" 
             :key="screen.key"
             @click="selectScreen(screen.key)"
             :class="{ active: selectedScreen === screen.key }">
          <div class="option-preview">
            <div class="preview-image" :style="{ background: screen.gradient }">
              <div class="preview-content">
                <div class="preview-chart">
                  <div v-for="i in 5" :key="i" 
                       class="chart-bar" 
                       :style="{ height: Math.random() * 80 + 20 + '%' }">
                  </div>
                </div>
                <div class="preview-metrics">
                  <div class="metric-item" v-for="j in 3" :key="j">
                    <div class="metric-value">{{ Math.floor(Math.random() * 1000) }}</div>
                    <div class="metric-label">指标{{ j }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="option-info">
            <h3>{{ screen.title }}</h3>
            <p>{{ screen.description }}</p>
            <div class="option-features">
              <span v-for="feature in screen.features" 
                    :key="feature" 
                    class="feature-tag">
                {{ feature }}
              </span>
            </div>
          </div>
          
          <div class="option-actions">
            <button class="action-btn preview" @click.stop="previewScreen(screen.key)">
              预览
            </button>
            <button class="action-btn fullscreen" @click.stop="openFullscreen(screen.key)">
              全屏
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前选中的大屏预览 -->
    <div class="screen-preview" v-if="selectedScreen">
      <div class="preview-header">
        <h3>{{ getCurrentScreen().title }} - 实时预览</h3>
        <div class="preview-controls">
          <button class="ctrl-btn" @click="refreshPreview">
            <i class="icon-refresh"></i>
            刷新
          </button>
          <button class="ctrl-btn" @click="openFullscreen(selectedScreen)">
            <i class="icon-fullscreen"></i>
            全屏查看
          </button>
        </div>
      </div>
      
      <div class="preview-container">
        <!-- 根据选择的屏幕类型渲染不同组件 -->
        <component :is="getScreenComponent(selectedScreen)" 
                   :key="selectedScreen"
                   class="preview-screen" />
      </div>
    </div>

    <!-- 功能特性展示 -->
    <div class="features-showcase">
      <div class="showcase-header">
        <h2>核心功能特性</h2>
        <p>现代化数据大屏的强大功能</p>
      </div>
      
      <div class="features-grid">
        <div class="feature-item" v-for="feature in coreFeatures" :key="feature.key">
          <div class="feature-icon" :style="{ background: feature.color }">
            <i :class="feature.icon"></i>
          </div>
          <div class="feature-content">
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
            <ul class="feature-details">
              <li v-for="detail in feature.details" :key="detail">{{ detail }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术规格 -->
    <div class="tech-specs">
      <div class="specs-header">
        <h2>技术规格</h2>
        <p>基于现代Web技术栈构建</p>
      </div>
      
      <div class="specs-content">
        <div class="spec-category">
          <h3>前端技术</h3>
          <div class="tech-list">
            <div class="tech-item" v-for="tech in frontendTech" :key="tech.name">
              <div class="tech-logo" :style="{ background: tech.color }">
                {{ tech.logo }}
              </div>
              <div class="tech-info">
                <div class="tech-name">{{ tech.name }}</div>
                <div class="tech-version">{{ tech.version }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="spec-category">
          <h3>可视化库</h3>
          <div class="tech-list">
            <div class="tech-item" v-for="viz in visualizationLibs" :key="viz.name">
              <div class="tech-logo" :style="{ background: viz.color }">
                {{ viz.logo }}
              </div>
              <div class="tech-info">
                <div class="tech-name">{{ viz.name }}</div>
                <div class="tech-version">{{ viz.version }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="spec-category">
          <h3>性能指标</h3>
          <div class="performance-metrics">
            <div class="metric-card" v-for="metric in performanceMetrics" :key="metric.key">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
              <div class="metric-description">{{ metric.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 导入不同的数据大屏组件
import DataScreen from './DataScreen.vue'
import EnhancedDataScreen from './EnhancedDataScreen.vue'
import UltraDataScreen from './UltraDataScreen.vue'
import SimpleDataScreen from './SimpleDataScreen.vue'

const router = useRouter()
const selectedScreen = ref('ultra')

// 大屏选项配置
const screenOptions = ref([
  {
    key: 'ultra',
    title: 'Ultra 数据大屏',
    description: '最新一代科技感数据大屏，具备完整的交互功能和动态效果',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    features: ['实时数据', '3D效果', '智能分析', '响应式设计']
  },
  {
    key: 'enhanced',
    title: 'Enhanced 增强版',
    description: '增强版数据大屏，平衡了功能性和性能表现',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    features: ['现代UI', '流畅动画', '多图表', '移动适配']
  },
  {
    key: 'classic',
    title: 'Classic 经典版',
    description: '经典数据大屏设计，稳定可靠，适合生产环境',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    features: ['稳定性', '兼容性', '高性能', '易维护']
  },
  {
    key: 'simple',
    title: 'Simple 简约版',
    description: '简约风格数据大屏，专注核心数据展示',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    features: ['简洁设计', '快速加载', '核心功能', '轻量级']
  }
])

// 核心功能特性
const coreFeatures = ref([
  {
    key: 'realtime',
    title: '实时数据更新',
    description: '支持WebSocket实时数据推送，毫秒级数据更新',
    icon: 'icon-zap',
    color: 'linear-gradient(135deg, #667eea, #764ba2)',
    details: [
      'WebSocket实时连接',
      '毫秒级数据刷新',
      '断线自动重连',
      '数据缓存机制'
    ]
  },
  {
    key: 'responsive',
    title: '响应式设计',
    description: '完美适配各种屏幕尺寸，从手机到4K大屏',
    icon: 'icon-monitor',
    color: 'linear-gradient(135deg, #f093fb, #f5576c)',
    details: [
      '移动端优化',
      '平板适配',
      '4K大屏支持',
      '自适应布局'
    ]
  },
  {
    key: 'interactive',
    title: '交互式图表',
    description: '丰富的图表交互功能，支持钻取和联动分析',
    icon: 'icon-activity',
    color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
    details: [
      '图表联动',
      '数据钻取',
      '筛选器',
      '工具提示'
    ]
  },
  {
    key: 'customizable',
    title: '高度可定制',
    description: '支持主题定制、布局调整和组件配置',
    icon: 'icon-settings',
    color: 'linear-gradient(135deg, #43e97b, #38f9d7)',
    details: [
      '主题系统',
      '布局编辑',
      '组件配置',
      '样式定制'
    ]
  }
])

// 前端技术栈
const frontendTech = ref([
  { name: 'Vue 3', version: '3.3+', logo: 'V', color: '#4fc08d' },
  { name: 'TypeScript', version: '5.0+', logo: 'TS', color: '#3178c6' },
  { name: 'Vite', version: '4.0+', logo: '⚡', color: '#646cff' },
  { name: 'Element Plus', version: '2.3+', logo: 'E', color: '#409eff' }
])

// 可视化库
const visualizationLibs = ref([
  { name: 'ECharts', version: '5.4+', logo: 'E', color: '#c23531' },
  { name: 'D3.js', version: '7.8+', logo: 'D3', color: '#f68e56' },
  { name: 'Chart.js', version: '4.2+', logo: 'C', color: '#ff6384' },
  { name: 'Three.js', version: '0.150+', logo: '3D', color: '#000000' }
])

// 性能指标
const performanceMetrics = ref([
  {
    key: 'fps',
    value: '60 FPS',
    label: '动画帧率',
    description: '流畅的动画体验'
  },
  {
    key: 'load',
    value: '< 2s',
    label: '加载时间',
    description: '快速启动和响应'
  },
  {
    key: 'memory',
    value: '< 100MB',
    label: '内存占用',
    description: '优化的内存使用'
  },
  {
    key: 'compatibility',
    value: '95%+',
    label: '浏览器兼容',
    description: '广泛的设备支持'
  }
])

// 计算属性
const getCurrentScreen = () => {
  return screenOptions.value.find(screen => screen.key === selectedScreen.value)
}

// 方法
const selectScreen = (screenKey) => {
  selectedScreen.value = screenKey
  ElMessage.success(`已选择 ${getCurrentScreen().title}`)
}

const getScreenComponent = (screenKey) => {
  const componentMap = {
    'ultra': UltraDataScreen,
    'enhanced': EnhancedDataScreen,
    'classic': DataScreen,
    'simple': SimpleDataScreen
  }
  return componentMap[screenKey] || UltraDataScreen
}

const previewScreen = (screenKey) => {
  selectedScreen.value = screenKey
  // 滚动到预览区域
  document.querySelector('.screen-preview')?.scrollIntoView({ 
    behavior: 'smooth' 
  })
}

const openFullscreen = (screenKey) => {
  const routeMap = {
    'ultra': '/data-screen/ultra',
    'enhanced': '/data-screen/enhanced', 
    'classic': '/data-screen/classic',
    'simple': '/data-screen/simple'
  }
  
  const route = routeMap[screenKey] || '/data-screen/ultra'
  window.open(route, '_blank')
}

const refreshPreview = () => {
  // 强制重新渲染组件
  const currentKey = selectedScreen.value
  selectedScreen.value = null
  setTimeout(() => {
    selectedScreen.value = currentKey
  }, 100)
  ElMessage.success('预览已刷新')
}
</script>

<style scoped>
.data-screen-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  color: #ffffff;
}

/* 演示控制面板 */
.demo-controls {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-header {
  text-align: center;
  margin-bottom: 32px;
}

.controls-header h2 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.controls-header p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.screen-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.option-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.option-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.option-card:hover::before {
  left: 100%;
}

.option-card.active {
  border-color: #00d4ff;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

.option-preview {
  margin-bottom: 16px;
}

.preview-image {
  height: 120px;
  border-radius: 12px;
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.preview-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.preview-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 60px;
}

.chart-bar {
  width: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  animation: chartPulse 2s ease-in-out infinite;
}

@keyframes chartPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.preview-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  text-align: right;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

.metric-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.option-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.option-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.option-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.feature-tag {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.option-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: transparent;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.action-btn.fullscreen {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
  color: #00d4ff;
}

.action-btn.fullscreen:hover {
  background: rgba(0, 212, 255, 0.3);
}

/* 屏幕预览 */
.screen-preview {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.ctrl-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.ctrl-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.preview-container {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.preview-screen {
  width: 100%;
  height: 600px;
  transform: scale(0.8);
  transform-origin: top left;
  border-radius: 16px;
  overflow: hidden;
}

/* 功能特性展示 */
.features-showcase {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.showcase-header {
  text-align: center;
  margin-bottom: 32px;
}

.showcase-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
}

.showcase-header p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.feature-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #ffffff;
  margin-bottom: 16px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.feature-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.feature-details {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-details li {
  padding: 4px 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  padding-left: 16px;
}

.feature-details li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #00d4ff;
  font-weight: 600;
}

/* 技术规格 */
.tech-specs {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.specs-header {
  text-align: center;
  margin-bottom: 32px;
}

.specs-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
}

.specs-header p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.specs-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.spec-category h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tech-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.tech-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.tech-info {
  flex: 1;
}

.tech-name {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
}

.tech-version {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.metric-card .metric-value {
  font-size: 24px;
  font-weight: 800;
  color: #00d4ff;
  margin-bottom: 8px;
}

.metric-card .metric-label {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.metric-card .metric-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-screen-demo {
    padding: 24px;
  }
  
  .screen-options {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .specs-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .data-screen-demo {
    padding: 16px;
  }
  
  .demo-controls,
  .screen-preview,
  .features-showcase,
  .tech-specs {
    padding: 20px;
  }
  
  .controls-header h2 {
    font-size: 24px;
  }
  
  .screen-options {
    grid-template-columns: 1fr;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .preview-screen {
    transform: scale(0.6);
    height: 400px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
}

/* 图标字体 */
.icon-zap::before { content: '⚡'; }
.icon-monitor::before { content: '🖥️'; }
.icon-activity::before { content: '📊'; }
.icon-settings::before { content: '⚙️'; }
.icon-refresh::before { content: '🔄'; }
.icon-fullscreen::before { content: '⛶'; }
</style>