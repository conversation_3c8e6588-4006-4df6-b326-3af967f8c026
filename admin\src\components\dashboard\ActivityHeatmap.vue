<template>
  <div class="activity-heatmap-card">
    <div class="card-header">
      <h3 class="card-title">用户活动热力图</h3>
      <div class="activity-legend">
        <span class="legend-item">
          <div class="legend-color low"></div>
          低活跃
        </span>
        <span class="legend-item">
          <div class="legend-color medium"></div>
          中活跃
        </span>
        <span class="legend-item">
          <div class="legend-color high"></div>
          高活跃
        </span>
      </div>
    </div>
    <div class="heatmap-container">
      <div class="heatmap-grid">
        <div 
          v-for="(day, index) in heatmapData" 
          :key="index"
          class="heatmap-cell"
          :class="day.level"
          :title="`${day.date}: ${day.count} 活跃用户`"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const heatmapData = ref([])

const generateHeatmapData = () => {
  const data = []
  const today = new Date()
  for (let i = 364; i >= 0; i--) {
    const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000)
    const count = Math.floor(Math.random() * 20)
    let level = 'none'
    if (count > 15) level = 'high'
    else if (count > 8) level = 'medium'
    else if (count > 3) level = 'low'
    
    data.push({
      date: date.toLocaleDateString(),
      count,
      level
    })
  }
  heatmapData.value = data
}

onMounted(() => {
  generateHeatmapData()
})
</script>

<style lang="scss" scoped>
.activity-heatmap-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .activity-legend {
      display: flex;
      gap: 16px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #6b7280;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;

          &.low {
            background: #dcfce7;
          }

          &.medium {
            background: #86efac;
          }

          &.high {
            background: #22c55e;
          }
        }
      }
    }
  }

  .heatmap-container {
    .heatmap-grid {
      display: grid;
      grid-template-columns: repeat(53, 1fr);
      gap: 2px;
      max-width: 100%;
      overflow-x: auto;

      .heatmap-cell {
        width: 10px;
        height: 10px;
        border-radius: 2px;
        background: #f3f4f6;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.2);
        }

        &.low {
          background: #dcfce7;
        }

        &.medium {
          background: #86efac;
        }

        &.high {
          background: #22c55e;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .activity-heatmap-card {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    .heatmap-container .heatmap-grid {
      grid-template-columns: repeat(26, 1fr);
      
      .heatmap-cell {
        width: 8px;
        height: 8px;
      }
    }
  }
}
</style>