<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\CommissionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * 处理佣金计算队列任务
 */
class ProcessCommissionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Order $order;

    /**
     * 任务最大尝试次数
     */
    public int $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(CommissionService $commissionService): void
    {
        try {
            Log::info('开始处理订单佣金', [
                'order_id' => $this->order->id,
                'order_no' => $this->order->order_no,
                'amount' => $this->order->amount,
            ]);

            // 处理订单佣金
            $commissionService->processOrderCommission($this->order);

            Log::info('订单佣金处理完成', [
                'order_id' => $this->order->id,
                'order_no' => $this->order->order_no,
            ]);

        } catch (\Exception $e) {
            Log::error('订单佣金处理失败', [
                'order_id' => $this->order->id,
                'order_no' => $this->order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常，让队列系统处理重试
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('订单佣金处理任务最终失败', [
            'order_id' => $this->order->id,
            'order_no' => $this->order->order_no,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // 这里可以发送通知给管理员
        // 或者将订单标记为需要人工处理
    }
}