<?php

return [
    /*
    |--------------------------------------------------------------------------
    | IP定位服务配置
    |--------------------------------------------------------------------------
    |
    | 配置IP地址定位服务的相关参数
    |
    */

    // 缓存配置
    'cache' => [
        'enabled' => env('IP_LOCATION_CACHE_ENABLED', true),
        'ttl' => env('IP_LOCATION_CACHE_TTL', 3600), // 缓存时间（秒）
    ],

    // 百度地图API配置
    'baidu' => [
        'enabled' => env('BAIDU_MAP_ENABLED', true),
        'api_key' => env('BAIDU_MAP_API_KEY', 'VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om'),
        'timeout' => env('BAIDU_MAP_TIMEOUT', 5),
    ],

    // 百度云IP地理位置API配置
    'baidu_cloud' => [
        'enabled' => env('BAIDU_CLOUD_ENABLED', true),
        'timeout' => env('BAIDU_CLOUD_TIMEOUT', 5),
    ],

    // 淘宝IP地址库配置
    'taobao' => [
        'enabled' => env('TAOBAO_IP_ENABLED', false), // 默认关闭，因为可能不稳定
        'timeout' => env('TAOBAO_IP_TIMEOUT', 5),
    ],

    // IP-API配置
    'ip_api' => [
        'enabled' => env('IP_API_ENABLED', true),
        'timeout' => env('IP_API_TIMEOUT', 5),
    ],

    // 回退配置
    'fallback' => [
        'default_city' => env('DEFAULT_CITY', '本地'),
    ],

    // 安全配置
    'security' => [
        'ip_whitelist' => env('IP_LOCATION_WHITELIST', ''), // 逗号分隔的IP白名单
        'max_failures_per_hour' => env('MAX_IP_FAILURES_PER_HOUR', 10),
    ],

    // 高德地图配置（用于坐标反向地理编码）
    'amap' => [
        'key' => env('AMAP_API_KEY', ''),
        'enabled' => env('AMAP_ENABLED', false),
    ],
];