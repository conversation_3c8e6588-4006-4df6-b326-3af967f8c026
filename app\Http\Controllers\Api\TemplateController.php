<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnifiedTemplateService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Exception;

/**
 * 统一模板管理API控制器
 * 提供模板的创建、管理、应用、分析等功能
 */
class TemplateController extends Controller
{
    public function __construct(
        private readonly UnifiedTemplateService $templateService
    ) {
        $this->middleware('auth:api');
        $this->middleware('throttle:30,1')->only(['createCustomTemplate', 'generateAITemplate']);
        $this->middleware('throttle:60,1')->only(['batchApply', 'analyzePerformance']);
    }

    /**
     * 获取统一模板列表
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'category' => 'nullable|string|max:50',
            'type' => 'nullable|string|in:group,content,marketing,custom',
            'source' => 'nullable|string|in:system,user,community',
            'user_id' => 'nullable|integer',
            'include_stats' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $filters = $request->only(['category', 'type', 'source', 'user_id']);
            $templates = $this->templateService->getUnifiedTemplates($filters);

            return $this->successResponse('模板列表获取成功', $templates);

        } catch (Exception $e) {
            return $this->errorResponse('模板列表获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 智能模板推荐
     */
    public function smartRecommendations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'context' => 'nullable|array',
            'limit' => 'nullable|integer|min:1|max:20',
            'include_reasons' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $context = $request->context ?? [];
            
            $recommendations = $this->templateService->getSmartTemplateRecommendations($user, $context);

            return $this->successResponse('智能推荐生成成功', $recommendations);

        } catch (Exception $e) {
            return $this->errorResponse('智能推荐失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建自定义模板
     */
    public function createCustomTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'type' => 'required|string|in:group,content,marketing',
            'category' => 'required|string|max:50',
            'template_data' => 'required|array',
            'is_public' => 'nullable|boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:20',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $templateData = $request->all();
            
            $result = $this->templateService->createCustomTemplate($templateData, $user);

            return $this->successResponse('自定义模板创建成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 应用模板
     */
    public function applyTemplate(Request $request, string $templateId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'customization' => 'nullable|array',
            'auto_optimize' => 'nullable|boolean',
            'preview_only' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $customization = $request->customization ?? [];
            
            if ($request->boolean('preview_only')) {
                // 仅预览，不实际创建
                $preview = $this->templateService->previewTemplate($templateId, $customization, $user);
                return $this->successResponse('模板预览生成成功', $preview);
            }
            
            $result = $this->templateService->applyTemplate($templateId, $customization, $user);

            return $this->successResponse('模板应用成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板应用失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量应用模板
     */
    public function batchApply(Request $request, string $templateId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'variations' => 'required|array|min:1|max:20',
            'variations.*' => 'array',
            'base_customization' => 'nullable|array',
            'auto_optimize' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $batchConfig = $request->all();
            
            $results = $this->templateService->batchApplyTemplate($templateId, $batchConfig, $user);

            return $this->successResponse('批量应用完成', $results);

        } catch (Exception $e) {
            return $this->errorResponse('批量应用失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 模板性能分析
     */
    public function analyzePerformance(string $templateId): JsonResponse
    {
        try {
            $analysis = $this->templateService->analyzeTemplatePerformance($templateId);

            return $this->successResponse('性能分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('性能分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 模板市场分析
     */
    public function marketAnalysis(): JsonResponse
    {
        try {
            $analysis = $this->templateService->analyzeTemplateMarket();

            return $this->successResponse('市场分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('市场分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * AI模板生成
     */
    public function generateAITemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'requirements' => 'required|array',
            'requirements.category' => 'required|string|max:50',
            'requirements.target_audience' => 'nullable|string|max:100',
            'requirements.price_range' => 'nullable|array',
            'requirements.features' => 'nullable|array',
            'requirements.style' => 'nullable|string|in:professional,casual,creative,formal',
            'save_as_template' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $requirements = $request->requirements;
            
            $result = $this->templateService->generateAITemplate($requirements, $user);

            return $this->successResponse('AI模板生成成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('AI模板生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取模板详情
     */
    public function show(string $templateId): JsonResponse
    {
        try {
            $template = $this->templateService->getTemplateDetails($templateId);
            
            if (!$template) {
                return $this->errorResponse('模板不存在', null, 404);
            }

            return $this->successResponse('模板详情获取成功', $template);

        } catch (Exception $e) {
            return $this->errorResponse('模板详情获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 更新自定义模板
     */
    public function update(Request $request, string $templateId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:500',
            'template_data' => 'nullable|array',
            'is_public' => 'nullable|boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:20',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $updateData = $request->all();
            
            $result = $this->templateService->updateCustomTemplate($templateId, $updateData, $user);

            return $this->successResponse('模板更新成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板更新失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 删除自定义模板
     */
    public function destroy(string $templateId): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $result = $this->templateService->deleteCustomTemplate($templateId, $user);

            return $this->successResponse('模板删除成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板删除失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 复制模板
     */
    public function duplicate(Request $request, string $templateId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'modifications' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $duplicateData = $request->all();
            
            $result = $this->templateService->duplicateTemplate($templateId, $duplicateData, $user);

            return $this->successResponse('模板复制成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板复制失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 分享模板到社区
     */
    public function shareToCommunity(Request $request, string $templateId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'share_message' => 'nullable|string|max:200',
            'allow_modifications' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $user = Auth::user();
            $shareData = $request->all();
            
            $result = $this->templateService->shareTemplateToCommunity($templateId, $shareData, $user);

            return $this->successResponse('模板分享成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('模板分享失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取用户的模板使用统计
     */
    public function userStats(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $stats = $this->templateService->getUserTemplateStats($user);

            return $this->successResponse('用户统计获取成功', $stats);

        } catch (Exception $e) {
            return $this->errorResponse('用户统计获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 统一成功响应格式
     */
    private function successResponse(string $message, array $data = []): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp,
        ]);
    }

    /**
     * 统一错误响应格式
     */
    private function errorResponse(string $message, $errors = null, int $code = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->timestamp,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }

    // ==================== 缺失的路由方法 ====================
    
    /**
     * 管理员获取模板列表
     */
    public function adminIndex(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'search' => 'sometimes|string|max:255',
                'category' => 'sometimes|string|max:100',
                'status' => 'sometimes|in:active,inactive',
                'sort_by' => 'sometimes|in:created_at,updated_at,usage_count',
                'sort_order' => 'sometimes|in:asc,desc',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('参数验证失败', $validator->errors(), 422);
            }

            $templates = $this->templateService->getAdminTemplateList($request->all());

            return $this->successResponse('模板列表获取成功', $templates);

        } catch (Exception $e) {
            return $this->errorResponse('模板列表获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建模板（管理员）
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'category' => 'required|string|max:100',
                'type' => 'required|in:group,content,custom',
                'config' => 'required|array',
                'preview_image' => 'sometimes|string|max:500',
                'tags' => 'sometimes|array',
                'tags.*' => 'string|max:50',
                'is_public' => 'sometimes|boolean',
                'sort_order' => 'sometimes|integer|min:0',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('参数验证失败', $validator->errors(), 422);
            }

            $template = $this->templateService->createTemplate($request->all());

            return $this->successResponse('模板创建成功', $template);

        } catch (Exception $e) {
            return $this->errorResponse('模板创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 管理员查看模板详情
     */
    public function adminShow(string $templateId): JsonResponse
    {
        try {
            $template = $this->templateService->getAdminTemplateDetail($templateId);

            if (!$template) {
                return $this->errorResponse('模板不存在', null, 404);
            }

            return $this->successResponse('模板详情获取成功', $template);

        } catch (Exception $e) {
            return $this->errorResponse('模板详情获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量操作模板
     */
    public function batchOperation(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:activate,deactivate,delete,duplicate',
                'template_ids' => 'required|array',
                'template_ids.*' => 'string',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('参数验证失败', $validator->errors(), 422);
            }

            $result = $this->templateService->batchOperation(
                $request->action,
                $request->template_ids
            );

            return $this->successResponse('批量操作成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('批量操作失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 上传模板图片
     */
    public function uploadImage(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
                'type' => 'sometimes|in:preview,icon,background',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse('参数验证失败', $validator->errors(), 422);
            }

            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $type = $request->get('type', 'preview');
                $filename = 'template_' . $type . '_' . time() . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('templates', $filename, 'public');
                
                $imageUrl = '/storage/' . $path;

                return $this->successResponse('图片上传成功', [
                    'url' => $imageUrl,
                    'filename' => $filename,
                    'type' => $type,
                ]);
            }

            return $this->errorResponse('图片上传失败', null, 422);

        } catch (Exception $e) {
            return $this->errorResponse('图片上传失败: ' . $e->getMessage(), null, 500);
        }
    }
}