<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GroupTemplate;
use App\Models\WechatGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * 群组模板管理控制器
 */
class GroupTemplateController extends Controller
{
    /**
     * 获取模板列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = GroupTemplate::with('creator:id,username');

        // 筛选条件
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('is_preset')) {
            if ($request->is_preset) {
                $query->preset();
            } else {
                $query->custom();
            }
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('template_name', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%")
                  ->orWhere('template_code', 'like', "%{$keyword}%");
            });
        }

        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if ($sortBy === 'usage') {
            $query->orderByUsage($sortOrder);
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }

        // 分页
        $perPage = $request->get('per_page', 15);
        $templates = $query->paginate($perPage);

        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $templates
        ]);
    }

    /**
     * 获取模板详情
     */
    public function show(int $id): JsonResponse
    {
        $template = GroupTemplate::with('creator:id,username')->find($id);

        if (!$template) {
            return response()->json([
                'code' => 404,
                'message' => '模板不存在'
            ], 404);
        }

        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $template
        ]);
    }

    /**
     * 创建模板
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:100',
            'category' => 'required|string|max:50',
            'description' => 'nullable|string',
            'template_data' => 'required|array',
            'template_data.title' => 'required|string|max:200',
            'template_data.description' => 'required|string',
            'template_data.price' => 'required|numeric|min:0',
            'custom_fields_config' => 'nullable|array',
            'cover_image' => 'nullable|string',
            'sort_order' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // 生成唯一的模板代码
            $templateCode = $this->generateUniqueTemplateCode($request->template_name);

            $template = GroupTemplate::create([
                'template_code' => $templateCode,
                'template_name' => $request->template_name,
                'category' => $request->category,
                'description' => $request->description,
                'template_data' => $request->template_data,
                'custom_fields_config' => $request->custom_fields_config,
                'cover_image' => $request->cover_image,
                'sort_order' => $request->get('sort_order', 0),
                'is_active' => true,
                'is_preset' => false,
                'created_by' => Auth::id(),
            ]);

            return response()->json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新模板
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $template = GroupTemplate::find($id);

        if (!$template) {
            return response()->json([
                'code' => 404,
                'message' => '模板不存在'
            ], 404);
        }

        // 检查权限（只有创建者或管理员可以编辑）
        if (!$this->canEditTemplate($template)) {
            return response()->json([
                'code' => 403,
                'message' => '无权限编辑该模板'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:100',
            'category' => 'required|string|max:50',
            'description' => 'nullable|string',
            'template_data' => 'required|array',
            'template_data.title' => 'required|string|max:200',
            'template_data.description' => 'required|string',
            'template_data.price' => 'required|numeric|min:0',
            'custom_fields_config' => 'nullable|array',
            'cover_image' => 'nullable|string',
            'sort_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $template->update([
                'template_name' => $request->template_name,
                'category' => $request->category,
                'description' => $request->description,
                'template_data' => $request->template_data,
                'custom_fields_config' => $request->custom_fields_config,
                'cover_image' => $request->cover_image,
                'sort_order' => $request->get('sort_order', $template->sort_order),
                'is_active' => $request->get('is_active', $template->is_active),
            ]);

            return response()->json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除模板
     */
    public function destroy(int $id): JsonResponse
    {
        $template = GroupTemplate::find($id);

        if (!$template) {
            return response()->json([
                'code' => 404,
                'message' => '模板不存在'
            ], 404);
        }

        // 检查权限
        if (!$this->canEditTemplate($template)) {
            return response()->json([
                'code' => 403,
                'message' => '无权限删除该模板'
            ], 403);
        }

        // 预设模板不能删除
        if ($template->is_preset) {
            return response()->json([
                'code' => 400,
                'message' => '预设模板不能删除'
            ], 400);
        }

        try {
            $template->delete();

            return response()->json([
                'code' => 200,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 基于模板创建群组
     */
    public function createGroupFromTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:group_templates,id',
            'customization' => 'nullable|array',
            'city' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $template = GroupTemplate::find($request->template_id);
            
            if (!$template->is_active) {
                return response()->json([
                    'code' => 400,
                    'message' => '模板已禁用'
                ], 400);
            }

            $user = Auth::user();
            $customization = $request->get('customization', []);
            $city = $request->get('city', '本地');

            // 获取模板数据并替换变量
            $templateData = $template->getTemplateDataWithReplacements([
                'city' => $city,
                'username' => $user->username,
                'nickname' => $user->nickname ?? $user->username,
            ]);

            // 合并自定义参数
            $groupData = array_merge($templateData, $customization);

            // 设置基础属性
            $groupData['user_id'] = $user->id;
            $groupData['substation_id'] = $user->substation_id;
            $groupData['template_id'] = $template->template_code;
            $groupData['status'] = WechatGroup::STATUS_ACTIVE;

            // 创建群组
            $group = WechatGroup::create($groupData);

            // 增加模板使用次数
            $template->incrementUsage();

            return response()->json([
                'code' => 200,
                'message' => '群组创建成功',
                'data' => $group
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 将群组导出为模板
     */
    public function exportGroupAsTemplate(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:100',
            'category' => 'required|string|max:50',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $group = WechatGroup::find($groupId);

            if (!$group) {
                return response()->json([
                    'code' => 404,
                    'message' => '群组不存在'
                ], 404);
            }

            // 检查权限
            if ($group->user_id !== Auth::id() && Auth::user()->role !== 'admin') {
                return response()->json([
                    'code' => 403,
                    'message' => '无权限操作该群组'
                ], 403);
            }

            // 生成模板代码
            $templateCode = $this->generateUniqueTemplateCode($request->template_name);

            // 提取群组数据作为模板
            $templateData = [
                'title' => $group->title,
                'description' => $group->description,
                'price' => $group->price,
                'member_limit' => $group->member_limit,
                'payment_methods' => $group->payment_methods,
                'city_location' => $group->city_location,
                'owner_display' => $group->owner_display,
                'hot_display' => $group->hot_display,
                'faq_content' => $group->faq_content,
                'member_reviews' => $group->member_reviews,
                'display_type' => $group->display_type,
                'avatar_type' => $group->avatar_type,
                'virtual_members' => $group->virtual_members,
                'virtual_orders' => $group->virtual_orders,
                'virtual_income' => $group->virtual_income,
                'welcome_message' => $group->welcome_message,
                'group_rules' => $group->group_rules,
                'custom_buttons' => $group->custom_buttons,
                'customer_service_url' => $group->customer_service_url,
            ];

            $template = GroupTemplate::create([
                'template_code' => $templateCode,
                'template_name' => $request->template_name,
                'category' => $request->category,
                'description' => $request->description,
                'template_data' => $templateData,
                'custom_fields_config' => $group->custom_fields,
                'cover_image' => $group->cover_image,
                'is_active' => true,
                'is_preset' => false,
                'created_by' => Auth::id(),
            ]);

            return response()->json([
                'code' => 200,
                'message' => '模板导出成功',
                'data' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '导出失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取模板分类列表
     */
    public function categories(): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => GroupTemplate::getCategories()
        ]);
    }

    /**
     * 上传模板封面
     */
    public function uploadCover(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '文件格式不正确',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $file = $request->file('file');
            $filename = 'template_covers/' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public', $filename);

            return response()->json([
                'code' => 200,
                'message' => '上传成功',
                'data' => [
                    'url' => Storage::url($filename),
                    'path' => $filename
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '上传失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成唯一的模板代码
     */
    private function generateUniqueTemplateCode(string $templateName): string
    {
        $baseCode = Str::slug($templateName, '_');
        $code = $baseCode;
        $counter = 1;

        while (GroupTemplate::where('template_code', $code)->exists()) {
            $code = $baseCode . '_' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * 检查是否可以编辑模板
     */
    private function canEditTemplate(GroupTemplate $template): bool
    {
        $user = Auth::user();
        
        // 管理员可以编辑所有模板
        if ($user->role === 'admin') {
            return true;
        }

        // 创建者可以编辑自己的模板
        if ($template->created_by === $user->id) {
            return true;
        }

        return false;
    }
} 