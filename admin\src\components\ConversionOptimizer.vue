<template>
  <div class="conversion-optimizer">
    <!-- 紧迫感元素 -->
    <div class="urgency-section">
      <h3>紧迫感设置</h3>
      <div class="urgency-options">
        <!-- 限时优惠 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="urgencySettings.limitedTime.enabled" />
            <h4>限时优惠</h4>
          </div>
          <div v-if="urgencySettings.limitedTime.enabled" class="option-content">
            <el-form-item label="优惠结束时间">
              <el-date-picker
                v-model="urgencySettings.limitedTime.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="优惠文案">
              <el-input
                v-model="urgencySettings.limitedTime.text"
                placeholder="例如：限时特价，仅剩24小时！"
              />
            </el-form-item>
            <el-form-item label="倒计时样式">
              <el-select v-model="urgencySettings.limitedTime.style">
                <el-option label="数字倒计时" value="digital" />
                <el-option label="圆形进度条" value="circular" />
                <el-option label="简约文字" value="text" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 剩余名额 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="urgencySettings.limitedSlots.enabled" />
            <h4>剩余名额</h4>
          </div>
          <div v-if="urgencySettings.limitedSlots.enabled" class="option-content">
            <el-form-item label="总名额">
              <el-input-number
                v-model="urgencySettings.limitedSlots.total"
                :min="1"
                :max="10000"
              />
            </el-form-item>
            <el-form-item label="已占用名额">
              <el-input-number
                v-model="urgencySettings.limitedSlots.used"
                :min="0"
                :max="urgencySettings.limitedSlots.total"
              />
            </el-form-item>
            <el-form-item label="显示样式">
              <el-select v-model="urgencySettings.limitedSlots.style">
                <el-option label="进度条" value="progress" />
                <el-option label="数字显示" value="number" />
                <el-option label="百分比" value="percentage" />
              </el-select>
            </el-form-item>
            <el-form-item label="紧急阈值(%)">
              <el-slider
                v-model="urgencySettings.limitedSlots.urgentThreshold"
                :min="10"
                :max="90"
                show-tooltip
              />
              <div class="form-tip">当剩余名额低于此百分比时显示紧急状态</div>
            </el-form-item>
          </div>
        </div>

        <!-- 实时活动 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="urgencySettings.liveActivity.enabled" />
            <h4>实时活动提示</h4>
          </div>
          <div v-if="urgencySettings.liveActivity.enabled" class="option-content">
            <el-form-item label="活动类型">
              <el-checkbox-group v-model="urgencySettings.liveActivity.types">
                <el-checkbox label="recent_joins">最近加入提示</el-checkbox>
                <el-checkbox label="viewing_count">当前浏览人数</el-checkbox>
                <el-checkbox label="success_stories">成功案例弹窗</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="显示频率(秒)">
              <el-input-number
                v-model="urgencySettings.liveActivity.frequency"
                :min="5"
                :max="60"
              />
            </el-form-item>
          </div>
        </div>
      </div>
    </div>

    <!-- 社会证明元素 -->
    <div class="social-proof-section">
      <h3>社会证明设置</h3>
      <div class="proof-options">
        <!-- 用户评价 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="socialProof.testimonials.enabled" />
            <h4>用户评价展示</h4>
          </div>
          <div v-if="socialProof.testimonials.enabled" class="option-content">
            <div class="testimonials-list">
              <div 
                v-for="(testimonial, index) in socialProof.testimonials.items"
                :key="index"
                class="testimonial-item"
              >
                <el-form-item :label="`评价 ${index + 1}`">
                  <el-input
                    v-model="testimonial.content"
                    type="textarea"
                    :rows="2"
                    placeholder="输入用户评价内容"
                  />
                </el-form-item>
                <div class="testimonial-meta">
                  <el-form-item label="用户名">
                    <el-input v-model="testimonial.author" placeholder="用户名" />
                  </el-form-item>
                  <el-form-item label="头像">
                    <el-input v-model="testimonial.avatar" placeholder="头像URL" />
                  </el-form-item>
                  <el-form-item label="评分">
                    <el-rate v-model="testimonial.rating" />
                  </el-form-item>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeTestimonial(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
            <el-button @click="addTestimonial" type="primary" size="small">
              <el-icon><Plus /></el-icon>
              添加评价
            </el-button>
          </div>
        </div>

        <!-- 成功案例 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="socialProof.successStories.enabled" />
            <h4>成功案例</h4>
          </div>
          <div v-if="socialProof.successStories.enabled" class="option-content">
            <div class="success-stories-list">
              <div 
                v-for="(story, index) in socialProof.successStories.items"
                :key="index"
                class="story-item"
              >
                <el-form-item :label="`案例 ${index + 1}`">
                  <el-input v-model="story.title" placeholder="案例标题" />
                </el-form-item>
                <el-form-item label="描述">
                  <el-input
                    v-model="story.description"
                    type="textarea"
                    :rows="2"
                    placeholder="案例描述"
                  />
                </el-form-item>
                <div class="story-meta">
                  <el-form-item label="成果数据">
                    <el-input v-model="story.result" placeholder="例如：月收入提升300%" />
                  </el-form-item>
                  <el-form-item label="案例图片">
                    <el-input v-model="story.image" placeholder="图片URL" />
                  </el-form-item>
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeSuccessStory(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
            <el-button @click="addSuccessStory" type="primary" size="small">
              <el-icon><Plus /></el-icon>
              添加案例
            </el-button>
          </div>
        </div>

        <!-- 信任标识 -->
        <div class="option-card">
          <div class="option-header">
            <el-switch v-model="socialProof.trustBadges.enabled" />
            <h4>信任标识</h4>
          </div>
          <div v-if="socialProof.trustBadges.enabled" class="option-content">
            <el-form-item label="选择标识">
              <el-checkbox-group v-model="socialProof.trustBadges.badges">
                <el-checkbox label="ssl_secure">SSL安全认证</el-checkbox>
                <el-checkbox label="money_back">30天退款保证</el-checkbox>
                <el-checkbox label="privacy_protection">隐私保护</el-checkbox>
                <el-checkbox label="verified_business">企业认证</el-checkbox>
                <el-checkbox label="customer_support">24/7客服支持</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="显示位置">
              <el-select v-model="socialProof.trustBadges.position">
                <el-option label="页面底部" value="footer" />
                <el-option label="支付按钮附近" value="payment" />
                <el-option label="页面顶部" value="header" />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA按钮优化 -->
    <div class="cta-optimization-section">
      <h3>CTA按钮优化</h3>
      <div class="cta-options">
        <el-form-item label="按钮文案">
          <el-input v-model="ctaSettings.text" placeholder="例如：立即加入，开启成功之路" />
        </el-form-item>
        <el-form-item label="按钮颜色">
          <el-color-picker v-model="ctaSettings.color" />
        </el-form-item>
        <el-form-item label="按钮大小">
          <el-select v-model="ctaSettings.size">
            <el-option label="小" value="small" />
            <el-option label="中" value="medium" />
            <el-option label="大" value="large" />
            <el-option label="超大" value="extra-large" />
          </el-select>
        </el-form-item>
        <el-form-item label="动画效果">
          <el-select v-model="ctaSettings.animation">
            <el-option label="无动画" value="none" />
            <el-option label="脉冲效果" value="pulse" />
            <el-option label="摇摆效果" value="shake" />
            <el-option label="发光效果" value="glow" />
          </el-select>
        </el-form-item>
        <el-form-item label="按钮位置">
          <el-checkbox-group v-model="ctaSettings.positions">
            <el-checkbox label="header">页面顶部</el-checkbox>
            <el-checkbox label="middle">页面中部</el-checkbox>
            <el-checkbox label="footer">页面底部</el-checkbox>
            <el-checkbox label="sticky">悬浮固定</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
    </div>

    <!-- 预览效果 -->
    <div class="preview-section">
      <h3>效果预览</h3>
      <div class="preview-container">
        <div class="preview-phone">
          <div class="phone-screen">
            <!-- 这里显示预览效果 -->
            <div class="preview-content">
              <div v-if="urgencySettings.limitedTime.enabled" class="preview-countdown">
                ⏰ {{ urgencySettings.limitedTime.text }}
              </div>
              <div v-if="urgencySettings.limitedSlots.enabled" class="preview-slots">
                🔥 仅剩 {{ urgencySettings.limitedSlots.total - urgencySettings.limitedSlots.used }} 个名额
              </div>
              <div v-if="socialProof.testimonials.enabled" class="preview-testimonial">
                ⭐ "{{ socialProof.testimonials.items[0]?.content || '用户评价内容' }}"
              </div>
              <div class="preview-cta">
                <button 
                  class="cta-button"
                  :style="{ 
                    backgroundColor: ctaSettings.color,
                    fontSize: getButtonSize(ctaSettings.size)
                  }"
                >
                  {{ ctaSettings.text || '立即加入' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 紧迫感设置
const urgencySettings = reactive({
  limitedTime: {
    enabled: false,
    endTime: '',
    text: '限时特价，仅剩24小时！',
    style: 'digital'
  },
  limitedSlots: {
    enabled: false,
    total: 100,
    used: 73,
    style: 'progress',
    urgentThreshold: 20
  },
  liveActivity: {
    enabled: false,
    types: ['recent_joins'],
    frequency: 15
  }
})

// 社会证明设置
const socialProof = reactive({
  testimonials: {
    enabled: false,
    items: [
      {
        content: '这个群组真的很棒，学到了很多有用的知识！',
        author: '张先生',
        avatar: '',
        rating: 5
      }
    ]
  },
  successStories: {
    enabled: false,
    items: [
      {
        title: '从零基础到月入过万',
        description: '通过群组学习，3个月实现收入翻倍',
        result: '月收入提升300%',
        image: ''
      }
    ]
  },
  trustBadges: {
    enabled: false,
    badges: ['ssl_secure', 'money_back'],
    position: 'footer'
  }
})

// CTA按钮设置
const ctaSettings = reactive({
  text: '立即加入，开启成功之路',
  color: '#409eff',
  size: 'large',
  animation: 'pulse',
  positions: ['middle', 'footer']
})

// 方法
const addTestimonial = () => {
  socialProof.testimonials.items.push({
    content: '',
    author: '',
    avatar: '',
    rating: 5
  })
}

const removeTestimonial = (index) => {
  socialProof.testimonials.items.splice(index, 1)
}

const addSuccessStory = () => {
  socialProof.successStories.items.push({
    title: '',
    description: '',
    result: '',
    image: ''
  })
}

const removeSuccessStory = (index) => {
  socialProof.successStories.items.splice(index, 1)
}

const getButtonSize = (size) => {
  const sizes = {
    small: '14px',
    medium: '16px',
    large: '18px',
    'extra-large': '20px'
  }
  return sizes[size] || '16px'
}

// 计算属性
const optimizationConfig = computed(() => ({
  urgency: urgencySettings,
  socialProof: socialProof,
  cta: ctaSettings
}))

// 监听配置变化
watch(optimizationConfig, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })
</script>

<style lang="scss" scoped>
.conversion-optimizer {
  .urgency-section,
  .social-proof-section,
  .cta-optimization-section,
  .preview-section {
    margin-bottom: 40px;
    
    h3 {
      font-size: 20px;
      color: #333;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #e9ecef;
    }
  }
  
  .urgency-options,
  .proof-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
  }
  
  .option-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    background: #f8f9fa;
    
    .option-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      
      h4 {
        font-size: 16px;
        color: #333;
        margin: 0;
      }
    }
    
    .option-content {
      .testimonial-item,
      .story-item {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        
        .testimonial-meta,
        .story-meta {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 10px;
          align-items: end;
        }
      }
    }
  }
  
  .cta-options {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .preview-section {
    .preview-container {
      display: flex;
      justify-content: center;
      
      .preview-phone {
        width: 300px;
        height: 600px;
        border: 8px solid #333;
        border-radius: 25px;
        overflow: hidden;
        background: white;
        
        .phone-screen {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          
          .preview-content {
            text-align: center;
            color: white;
            padding: 20px;
            
            .preview-countdown,
            .preview-slots,
            .preview-testimonial {
              background: rgba(255, 255, 255, 0.2);
              padding: 10px;
              border-radius: 8px;
              margin-bottom: 15px;
              font-size: 14px;
            }
            
            .preview-cta {
              margin-top: 30px;
              
              .cta-button {
                padding: 15px 30px;
                border: none;
                border-radius: 25px;
                color: white;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                  transform: scale(1.05);
                }
              }
            }
          }
        }
      }
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .conversion-optimizer {
    .urgency-options,
    .proof-options {
      grid-template-columns: 1fr;
    }
    
    .option-card {
      .option-content {
        .testimonial-meta,
        .story-meta {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
