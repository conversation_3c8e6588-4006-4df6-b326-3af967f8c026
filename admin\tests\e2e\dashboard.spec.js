/**
 * Dashboard Navigation E2E Tests
 * 测试仪表板导航和基本功能
 */

import { test, expect } from '@playwright/test'

test.describe('Dashboard Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // 设置登录状态
    await page.goto('/login')
    await page.evaluate(() => {
      localStorage.setItem('Admin-Token', 'mock-admin-token')
      localStorage.setItem('userInfo', JSON.stringify({
        id: 1,
        username: 'admin',
        nickname: '超级管理员',
        role: 'admin',
        permissions: ['*']
      }))
    })

    // 模拟认证API
    await page.route('/api/admin/auth/user', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: {
              id: 1,
              username: 'admin',
              nickname: '超级管理员',
              role: 'admin',
              permissions: ['*']
            }
          }
        })
      })
    })

    // 模拟仪表板数据API
    await page.route('/api/admin/dashboard/stats', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            total_users: 1234,
            total_groups: 567,
            total_orders: 8901,
            total_revenue: 123456.78,
            today_new_users: 45,
            today_new_orders: 89,
            active_groups: 234,
            conversion_rate: 12.5
          }
        })
      })
    })
  })

  test.describe('Main Navigation', () => {
    test('should display main navigation correctly', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 检查主要导航元素
      await expect(page.locator('.sidebar')).toBeVisible()
      await expect(page.locator('.main-header')).toBeVisible()
      await expect(page.locator('.main-content')).toBeVisible()
      
      // 检查logo和标题
      await expect(page.locator('.logo')).toBeVisible()
      await expect(page.locator('.system-title')).toContainText('晨鑫流量变现系统')
      
      // 检查用户信息
      await expect(page.locator('.user-dropdown')).toBeVisible()
      await expect(page.locator('.user-name')).toContainText('超级管理员')
    })

    test('should show correct navigation menu items', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 验证主要菜单项
      const expectedMenuItems = [
        '仪表板',
        '用户管理',
        '社群管理',
        '订单管理',
        '财务管理',
        '分销管理',
        '防封管理',
        '系统设置'
      ]
      
      for (const item of expectedMenuItems) {
        await expect(page.locator('.sidebar-menu').locator(`text=${item}`)).toBeVisible()
      }
    })

    test('should navigate between menu items correctly', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 点击用户管理菜单
      await page.click('.sidebar-menu a[href="/users"]')
      await expect(page).toHaveURL('/users')
      
      // 点击社群管理菜单
      await page.click('.sidebar-menu a[href="/groups"]')
      await expect(page).toHaveURL('/groups')
      
      // 点击仪表板返回
      await page.click('.sidebar-menu a[href="/dashboard"]')
      await expect(page).toHaveURL('/dashboard')
    })

    test('should highlight active menu item', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 验证仪表板菜单项处于激活状态
      await expect(page.locator('.sidebar-menu a[href="/dashboard"]')).toHaveClass(/active/)
      
      // 导航到用户管理
      await page.click('.sidebar-menu a[href="/users"]')
      await expect(page.locator('.sidebar-menu a[href="/users"]')).toHaveClass(/active/)
      await expect(page.locator('.sidebar-menu a[href="/dashboard"]')).not.toHaveClass(/active/)
    })

    test('should handle submenu navigation', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 点击有子菜单的项目（如社群管理）
      const groupManagementMenu = page.locator('.sidebar-menu .submenu-parent:has-text("社群管理")')
      
      if (await groupManagementMenu.isVisible()) {
        await groupManagementMenu.click()
        
        // 验证子菜单展开
        await expect(page.locator('.submenu-items')).toBeVisible()
        
        // 点击子菜单项
        await page.click('.submenu-items a[href="/groups/list"]')
        await expect(page).toHaveURL('/groups/list')
      }
    })
  })

  test.describe('Dashboard Content', () => {
    test('should display key statistics correctly', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 验证统计卡片显示
      await expect(page.locator('.stat-card')).toHaveCount.toBeGreaterThan(4)
      
      // 验证具体统计数据
      await expect(page.locator('.stat-total-users')).toContainText('1,234')
      await expect(page.locator('.stat-total-groups')).toContainText('567')
      await expect(page.locator('.stat-total-orders')).toContainText('8,901')
      await expect(page.locator('.stat-total-revenue')).toContainText('123,456.78')
    })

    test('should display charts and graphs', async ({ page }) => {
      // 模拟图表数据API
      await page.route('/api/admin/dashboard/charts', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              user_growth: [
                { date: '2024-01-01', count: 100 },
                { date: '2024-01-02', count: 120 },
                { date: '2024-01-03', count: 135 }
              ],
              order_distribution: [
                { category: '新订单', value: 45 },
                { category: '处理中', value: 30 },
                { category: '已完成', value: 25 }
              ]
            }
          })
        })
      })
      
      await page.goto('/dashboard')
      
      // 验证图表容器存在
      await expect(page.locator('.chart-container')).toBeVisible()
      await expect(page.locator('.user-growth-chart')).toBeVisible()
      await expect(page.locator('.order-distribution-chart')).toBeVisible()
    })

    test('should display recent activities', async ({ page }) => {
      // 模拟最近活动API
      await page.route('/api/admin/dashboard/activities', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              activities: [
                {
                  id: 1,
                  type: 'user_register',
                  message: '新用户 testuser 注册成功',
                  timestamp: '2024-01-15T10:30:00Z'
                },
                {
                  id: 2,
                  type: 'order_created',
                  message: '订单 #12345 创建成功',
                  timestamp: '2024-01-15T10:25:00Z'
                },
                {
                  id: 3,
                  type: 'group_created',
                  message: '新群组 "测试群" 创建成功',
                  timestamp: '2024-01-15T10:20:00Z'
                }
              ]
            }
          })
        })
      })
      
      await page.goto('/dashboard')
      
      // 验证活动列表显示
      await expect(page.locator('.recent-activities')).toBeVisible()
      await expect(page.locator('.activity-item')).toHaveCount(3)
      
      // 验证活动内容
      await expect(page.locator('.activity-item:first-child')).toContainText('新用户 testuser 注册成功')
      await expect(page.locator('.activity-item:nth-child(2)')).toContainText('订单 #12345 创建成功')
    })

    test('should display system status', async ({ page }) => {
      // 模拟系统状态API
      await page.route('/api/admin/dashboard/system-status', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              system_status: 'healthy',
              cpu_usage: 45.2,
              memory_usage: 67.8,
              disk_usage: 34.1,
              active_connections: 1024
            }
          })
        })
      })
      
      await page.goto('/dashboard')
      
      // 验证系统状态显示
      await expect(page.locator('.system-status')).toBeVisible()
      await expect(page.locator('.status-indicator.healthy')).toBeVisible()
      await expect(page.locator('.cpu-usage')).toContainText('45.2%')
      await expect(page.locator('.memory-usage')).toContainText('67.8%')
    })
  })

  test.describe('User Dropdown Menu', () => {
    test('should show user dropdown menu items', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 点击用户下拉菜单
      await page.click('.user-dropdown')
      
      // 验证下拉菜单项
      await expect(page.locator('.dropdown-menu')).toBeVisible()
      await expect(page.locator('.dropdown-menu a[href="/profile"]')).toContainText('个人资料')
      await expect(page.locator('.dropdown-menu a[href="/settings"]')).toContainText('系统设置')
      await expect(page.locator('.dropdown-menu .logout-btn')).toContainText('退出登录')
    })

    test('should navigate to profile page', async ({ page }) => {
      await page.goto('/dashboard')
      
      await page.click('.user-dropdown')
      await page.click('.dropdown-menu a[href="/profile"]')
      
      await expect(page).toHaveURL('/profile')
    })

    test('should handle logout correctly', async ({ page }) => {
      // 模拟退出登录API
      await page.route('/api/admin/auth/logout', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '退出登录成功'
          })
        })
      })
      
      await page.goto('/dashboard')
      
      await page.click('.user-dropdown')
      await page.click('.dropdown-menu .logout-btn')
      
      // 验证退出确认对话框
      await expect(page.locator('.el-message-box')).toBeVisible()
      await expect(page.locator('.el-message-box__message')).toContainText('确定要退出登录吗？')
      
      // 确认退出
      await page.click('.el-message-box__btns .el-button--primary')
      
      // 验证跳转到登录页
      await expect(page).toHaveURL('/login')
      
      // 验证存储被清理
      const token = await page.evaluate(() => localStorage.getItem('Admin-Token'))
      expect(token).toBeNull()
    })
  })

  test.describe('Responsive Behavior', () => {
    test('should collapse sidebar on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await page.goto('/dashboard')
      
      // 在小屏幕上侧边栏应该是折叠的
      await expect(page.locator('.sidebar')).toHaveClass(/collapsed/)
      
      // 点击菜单切换按钮
      await page.click('.sidebar-toggle')
      
      // 侧边栏应该展开
      await expect(page.locator('.sidebar')).not.toHaveClass(/collapsed/)
    })

    test('should adapt layout for mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto('/dashboard')
      
      // 验证移动端布局调整
      await expect(page.locator('.main-content')).toHaveCSS('padding-left', '0px')
      
      // 统计卡片应该垂直堆叠
      const statCards = page.locator('.stat-card')
      const firstCardBox = await statCards.first().boundingBox()
      const secondCardBox = await statCards.nth(1).boundingBox()
      
      // 第二个卡片应该在第一个下面
      expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height)
    })

    test('should handle tablet layout', async ({ page }) => {
      await page.setViewportSize({ width: 1024, height: 768 })
      await page.goto('/dashboard')
      
      // 平板尺寸下应该正常显示侧边栏
      await expect(page.locator('.sidebar')).toBeVisible()
      await expect(page.locator('.sidebar')).not.toHaveClass(/collapsed/)
    })
  })

  test.describe('Search and Quick Actions', () => {
    test('should show global search functionality', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 检查全局搜索框
      const searchBox = page.locator('.global-search')
      if (await searchBox.isVisible()) {
        await expect(searchBox).toBeVisible()
        
        // 测试搜索功能
        await searchBox.fill('测试用户')
        await page.keyboard.press('Enter')
        
        // 验证搜索结果页面或下拉结果
        await expect(page.locator('.search-results')).toBeVisible()
      }
    })

    test('should show quick action buttons', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 检查快捷操作按钮
      const quickActions = [
        '.btn-create-user',
        '.btn-create-group',
        '.btn-create-order'
      ]
      
      for (const action of quickActions) {
        const button = page.locator(action)
        if (await button.isVisible()) {
          await expect(button).toBeVisible()
        }
      }
    })
  })

  test.describe('Real-time Updates', () => {
    test('should handle real-time notifications', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 模拟WebSocket或Server-Sent Events
      await page.evaluate(() => {
        // 模拟接收到实时通知
        window.dispatchEvent(new CustomEvent('notification', {
          detail: {
            type: 'info',
            message: '有新订单创建',
            timestamp: new Date().toISOString()
          }
        }))
      })
      
      // 验证通知显示
      await expect(page.locator('.notification-toast')).toBeVisible()
      await expect(page.locator('.notification-toast')).toContainText('有新订单创建')
    })

    test('should update statistics in real-time', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 等待初始数据加载
      await expect(page.locator('.stat-total-users')).toContainText('1,234')
      
      // 模拟数据更新
      await page.route('/api/admin/dashboard/stats', (route) => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              total_users: 1235, // 用户数增加1
              total_groups: 567,
              total_orders: 8902, // 订单数增加1
              total_revenue: 123456.78
            }
          })
        })
      })
      
      // 触发数据刷新
      await page.click('.refresh-stats')
      
      // 验证数据更新
      await expect(page.locator('.stat-total-users')).toContainText('1,235')
      await expect(page.locator('.stat-total-orders')).toContainText('8,902')
    })
  })

  test.describe('Error Handling', () => {
    test('should handle dashboard data loading errors', async ({ page }) => {
      // 模拟API错误
      await page.route('/api/admin/dashboard/stats', (route) => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            message: '服务器错误'
          })
        })
      })
      
      await page.goto('/dashboard')
      
      // 验证错误状态显示
      await expect(page.locator('.error-message')).toContainText('数据加载失败')
      
      // 验证重试按钮
      const retryButton = page.locator('.retry-button')
      if (await retryButton.isVisible()) {
        await expect(retryButton).toBeVisible()
      }
    })

    test('should handle network connectivity issues', async ({ page }) => {
      await page.goto('/dashboard')
      
      // 模拟网络断开
      await page.setOffline(true)
      
      // 尝试刷新数据
      await page.click('.refresh-stats')
      
      // 验证离线状态提示
      await expect(page.locator('.offline-indicator')).toBeVisible()
      
      // 恢复网络连接
      await page.setOffline(false)
      
      // 验证在线状态恢复
      await expect(page.locator('.offline-indicator')).not.toBeVisible()
    })
  })

  test.describe('Performance', () => {
    test('should load dashboard quickly', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/dashboard')
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // 仪表板应该在3秒内加载完成
      expect(loadTime).toBeLessThan(3000)
    })

    test('should handle large datasets efficiently', async ({ page }) => {
      // 模拟大数据集
      await page.route('/api/admin/dashboard/activities', (route) => {
        const activities = Array.from({ length: 1000 }, (_, i) => ({
          id: i + 1,
          type: 'user_action',
          message: `活动 ${i + 1}`,
          timestamp: new Date().toISOString()
        }))
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { activities }
          })
        })
      })
      
      await page.goto('/dashboard')
      
      // 验证大数据集处理（应该使用分页或虚拟滚动）
      const visibleActivities = page.locator('.activity-item')
      const count = await visibleActivities.count()
      
      // 不应该一次性渲染所有1000条数据
      expect(count).toBeLessThan(100)
    })
  })
})