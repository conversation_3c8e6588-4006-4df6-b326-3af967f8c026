// 权限管理模拟数据和API

// 模拟角色数据
const mockRoles = [
  {
    id: 1,
    name: '超级管理员',
    code: 'super_admin',
    description: '拥有系统所有权限的超级管理员',
    status: 'active',
    userCount: 2,
    permissionCount: 50,
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-15 14:30:00',
    creator: '系统',
    permissions: ['*']
  },
  {
    id: 2,
    name: '管理员',
    code: 'admin',
    description: '系统管理员，拥有大部分管理权限',
    status: 'active',
    userCount: 5,
    permissionCount: 35,
    createdAt: '2024-01-02 09:15:00',
    updatedAt: '2024-01-20 16:45:00',
    creator: '超级管理员',
    permissions: ['user:*', 'role:*', 'community:*']
  },
  {
    id: 3,
    name: '编辑者',
    code: 'editor',
    description: '内容编辑者，可以管理内容相关功能',
    status: 'active',
    userCount: 8,
    permissionCount: 20,
    createdAt: '2024-01-03 11:20:00',
    updatedAt: '2024-01-25 10:15:00',
    creator: '管理员',
    permissions: ['community:read', 'community:write', 'template:*']
  },
  {
    id: 4,
    name: '查看者',
    code: 'viewer',
    description: '只读权限用户，只能查看数据',
    status: 'active',
    userCount: 15,
    permissionCount: 8,
    createdAt: '2024-01-04 13:45:00',
    updatedAt: '2024-01-28 09:30:00',
    creator: '管理员',
    permissions: ['community:read', 'user:read']
  },
  {
    id: 5,
    name: '客服',
    code: 'customer_service',
    description: '客服人员，处理用户相关问题',
    status: 'inactive',
    userCount: 3,
    permissionCount: 12,
    createdAt: '2024-01-05 15:10:00',
    updatedAt: '2024-01-30 11:20:00',
    creator: '管理员',
    permissions: ['user:read', 'user:update', 'community:read']
  }
]

// 模拟统计数据
const mockRoleStats = {
  total_roles: 5,
  active_roles: 4,
  inactive_roles: 1,
  total_users: 33,
  total_permissions: 125,
  recent_changes: 8
}

// 模拟API响应
export const mockPermissionAPI = {
  // 获取角色列表
  getRoleList: (params = {}) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        let result = [...mockRoles]
        
        // 模拟筛选
        if (params.keyword) {
          result = result.filter(item => 
            item.name.includes(params.keyword) ||
            item.code.includes(params.keyword) ||
            item.description.includes(params.keyword)
          )
        }
        
        if (params.status) {
          result = result.filter(item => item.status === params.status)
        }
        
        // 模拟分页
        const page = params.page || 1
        const limit = params.limit || 20
        const start = (page - 1) * limit
        const end = start + limit
        
        resolve({
          code: 200,
          data: {
            list: result.slice(start, end),
            total: result.length,
            page,
            limit
          },
          message: 'success'
        })
      }, 300)
    })
  },

  // 获取角色统计
  getRoleStats: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: mockRoleStats,
          message: 'success'
        })
      }, 200)
    })
  },

  // 删除角色
  deleteRole: (id) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockRoles.findIndex(role => role.id === id)
        if (index > -1) {
          mockRoles.splice(index, 1)
          resolve({
            code: 200,
            message: '删除成功'
          })
        } else {
          reject({
            code: 404,
            message: '角色不存在'
          })
        }
      }, 500)
    })
  },

  // 更新角色状态
  updateRoleStatus: (id, status) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const role = mockRoles.find(role => role.id === id)
        if (role) {
          role.status = status
          role.updatedAt = new Date().toLocaleString('zh-CN')
          resolve({
            code: 200,
            data: role,
            message: '状态更新成功'
          })
        } else {
          reject({
            code: 404,
            message: '角色不存在'
          })
        }
      }, 500)
    })
  }
}
