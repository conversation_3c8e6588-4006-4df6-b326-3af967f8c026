<template>
  <section class="hero-component" :style="componentStyle">
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="hero-title">{{ data.title }}</h1>
        <p class="hero-subtitle" v-if="data.subtitle">{{ data.subtitle }}</p>
        <div class="hero-actions" v-if="data.buttonText">
          <el-button type="primary" size="large" @click="handleAction">
            {{ data.buttonText }}
          </el-button>
        </div>
      </div>
      
      <div class="hero-image" v-if="data.image">
        <img :src="data.image" :alt="data.title" />
      </div>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      title: '欢迎来到我们的产品',
      subtitle: '这里是产品的简短描述',
      buttonText: '立即开始',
      buttonLink: '#',
      image: '',
      backgroundColor: '#f8f9fa',
      textColor: '#333333'
    })
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update'])

const componentStyle = computed(() => ({
  backgroundColor: props.data.backgroundColor || '#f8f9fa',
  color: props.data.textColor || '#333333',
  backgroundImage: props.data.backgroundImage ? `url(${props.data.backgroundImage})` : 'none',
  backgroundSize: 'cover',
  backgroundPosition: 'center'
}))

const handleAction = () => {
  if (props.data.buttonLink && props.data.buttonLink !== '#') {
    window.open(props.data.buttonLink, '_blank')
  }
}
</script>

<style lang="scss" scoped>
.hero-component {
  padding: 80px 0;
  min-height: 500px;
  display: flex;
  align-items: center;
  
  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    
    .hero-content {
      .hero-title {
        font-size: 48px;
        font-weight: 700;
        line-height: 1.2;
        margin: 0 0 24px 0;
      }
      
      .hero-subtitle {
        font-size: 20px;
        line-height: 1.6;
        margin: 0 0 32px 0;
        opacity: 0.8;
      }
      
      .hero-actions {
        .el-button {
          padding: 16px 32px;
          font-size: 16px;
        }
      }
    }
    
    .hero-image {
      text-align: center;
      
      img {
        max-width: 100%;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      }
    }
  }
}

@media (max-width: 768px) {
  .hero-component {
    padding: 60px 0;
    
    .hero-container {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
      
      .hero-content {
        .hero-title {
          font-size: 36px;
        }
        
        .hero-subtitle {
          font-size: 18px;
        }
      }
    }
  }
}
</style>