<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * 角色权限中间件
 * 验证用户是否具有指定角色权限
 */
class RoleMiddleware
{
    /**
     * 处理传入的请求
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        $user = auth()->user();
        
        // 检查用户是否已登录
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录',
                'code' => 401
            ], 401);
        }
        
        // 检查用户状态
        if (!$user->isActive()) {
            return response()->json([
                'success' => false,
                'message' => '账户已被禁用或已过期',
                'code' => 403
            ], 403);
        }
        
        // 超级管理员拥有所有权限
        if ($user->isAdmin()) {
            return $next($request);
        }
        
        // 检查用户是否具有指定角色
        $hasRole = false;
        foreach ($roles as $role) {
            if ($user->role === $role) {
                $hasRole = true;
                break;
            }
        }
        
        if (!$hasRole) {
            return response()->json([
                'success' => false,
                'message' => '权限不足',
                'code' => 403
            ], 403);
        }
        
        return $next($request);
    }
} 