<template>
  <div class="info-preview">
    <div class="info-container">
      <!-- 群组头像和基本信息 -->
      <div class="group-header">
        <div class="group-avatar">
          <img
            v-if="groupData.avatar"
            :src="groupData.avatar"
            :alt="groupData.title"
            class="avatar-image"
          />
          <div v-else class="avatar-placeholder">
            <el-icon class="placeholder-icon"><User /></el-icon>
          </div>
        </div>
        
        <div class="group-info">
          <h2 class="group-title">{{ groupData.title || '群组标题' }}</h2>
          <div v-if="showPrice && groupData.price !== undefined" class="group-price">
            <span class="price-text">{{ formatPrice(groupData.price) }}</span>
          </div>
          <div v-if="showDescription && groupData.description" class="group-description">
            {{ groupData.description }}
          </div>
        </div>
      </div>
      
      <!-- 群组统计信息 -->
      <div class="group-stats">
        <div class="stat-item">
          <div class="stat-value">{{ memberCount }}</div>
          <div class="stat-label">群成员</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ activityLevel }}</div>
          <div class="stat-label">活跃度</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ groupType }}</div>
          <div class="stat-label">群类型</div>
        </div>
      </div>
      
      <!-- 群组标签 -->
      <div v-if="groupTags.length > 0" class="group-tags">
        <el-tag
          v-for="tag in groupTags"
          :key="tag"
          size="small"
          type="primary"
          effect="plain"
        >
          {{ tag }}
        </el-tag>
      </div>
      
      <!-- 入群按钮 -->
      <div class="join-actions">
        <el-button type="primary" size="large" class="join-button">
          <el-icon><Plus /></el-icon>
          {{ joinButtonText }}
        </el-button>
        <div class="join-hint">点击按钮扫码入群</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { User, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const showPrice = computed(() => {
  return props.section.config?.showPrice !== false
})

const showDescription = computed(() => {
  return props.section.config?.showDescription !== false
})

const memberCount = computed(() => {
  return props.groupData.member_count || '500+'
})

const joinButtonText = computed(() => {
  return props.groupData.join_button_text || '立即加入群组'
})

const activityLevel = computed(() => {
  return props.groupData.activity_level || '高'
})

const groupType = computed(() => {
  return props.groupData.group_type || '学习交流'
})

const groupTags = computed(() => {
  return props.groupData.tags || ['技术交流', '学习成长', '资源分享']
})

// 方法
const formatPrice = (price) => {
  if (price === 0 || price === '0') {
    return '免费入群'
  }
  return `¥${price} 入群`
}
</script>

<style lang="scss" scoped>
.info-preview {
  .info-container {
    padding: 20px;
    
    .group-header {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      
      .group-avatar {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;
        
        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .avatar-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
          border: 2px dashed #dcdfe6;
          color: #909399;
          
          .placeholder-icon {
            font-size: 24px;
          }
        }
      }
      
      .group-info {
        flex: 1;
        
        .group-title {
          font-size: 20px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
          line-height: 1.3;
        }
        
        .group-price {
          margin-bottom: 8px;
          
          .price-text {
            display: inline-block;
            padding: 4px 12px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
          }
        }
        
        .group-description {
          font-size: 14px;
          color: #606266;
          line-height: 1.5;
        }
      }
    }
    
    .group-stats {
      display: flex;
      justify-content: space-around;
      padding: 16px 0;
      margin-bottom: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .group-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 20px;
    }
    
    .join-actions {
      text-align: center;
      
      .join-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        
        &:hover {
          opacity: 0.9;
        }
      }
      
      .join-hint {
        margin-top: 8px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>
