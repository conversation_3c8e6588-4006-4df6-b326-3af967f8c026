<template>
  <el-drawer
    v-model="visible"
    title="用户详情"
    size="600px"
    @close="handleClose"
  >
    <div v-loading="loading" class="user-detail">
      <div v-if="userDetail" class="detail-content">
        <!-- 用户基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <div class="user-header">
            <el-avatar :size="80" :src="userDetail.avatar" :alt="userDetail.username">
              {{ userDetail.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <div class="user-info">
              <div class="username">{{ userDetail.username }}</div>
              <div class="user-id">ID: {{ userDetail.id }}</div>
              <el-tag :type="getStatusTagType(userDetail.status)">
                {{ getStatusText(userDetail.status) }}
              </el-tag>
            </div>
          </div>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="邮箱">{{ userDetail.email }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ userDetail.phone }}</el-descriptions-item>
            <el-descriptions-item label="角色">
              <el-tag :type="getRoleTagType(userDetail.role)">
                {{ getRoleText(userDetail.role) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="余额">
              <span class="money-text">¥{{ userDetail.balance?.toFixed(2) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ formatDate(userDetail.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="最后登录">{{ formatDate(userDetail.last_login) }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 统计信息 -->
        <div class="info-section">
          <h3>统计信息</h3>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">{{ userDetail.stats?.total_orders || 0 }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">¥{{ (userDetail.stats?.total_amount || 0).toFixed(2) }}</div>
                <div class="stat-label">总消费金额</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-value">{{ userDetail.stats?.total_groups || 0 }}</div>
                <div class="stat-label">加入群组数</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 最近订单 -->
        <div class="info-section">
          <h3>最近订单</h3>
          <el-table :data="userDetail.recent_orders" size="small">
            <el-table-column label="订单号" prop="order_no" width="150" />
            <el-table-column label="金额" width="100">
              <template #default="{ row }">
                <span class="money-text">¥{{ row.amount?.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getOrderStatusType(row.status)" size="small">
                  {{ getOrderStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="时间">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 余额变动记录 -->
        <div class="info-section">
          <h3>余额变动记录</h3>
          <el-table :data="userDetail.balance_logs" size="small">
            <el-table-column label="类型" width="80">
              <template #default="{ row }">
                <el-tag :type="getBalanceTypeTag(row.type)" size="small">
                  {{ getBalanceTypeText(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="金额" width="100">
              <template #default="{ row }">
                <span :class="row.amount > 0 ? 'positive-amount' : 'negative-amount'">
                  {{ row.amount > 0 ? '+' : '' }}¥{{ row.amount?.toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="余额" width="100">
              <template #default="{ row }">
                <span class="money-text">¥{{ row.balance?.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" />
            <el-table-column label="时间" width="150">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getUserDetail } from '@/api/user'
import { formatDate } from '@/utils/format'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const userDetail = ref(null)

// 监听用户ID变化
watch(() => props.userId, (newVal) => {
  if (newVal && visible.value) {
    fetchUserDetail()
  }
})

// 监听抽屉显示状态
watch(visible, (newVal) => {
  if (newVal && props.userId) {
    fetchUserDetail()
  }
})

// 获取用户详情
const fetchUserDetail = async () => {
  if (!props.userId) return
  
  loading.value = true
  try {
    const { data } = await getUserDetail(props.userId)
    userDetail.value = data
  } catch (error) {
    console.error('获取用户详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 工具函数
const getRoleTagType = (role) => {
  const types = {
    user: '',
    distributor: 'success',
    agent: 'warning',
    admin: 'danger'
  }
  return types[role] || ''
}

const getRoleText = (role) => {
  const texts = {
    user: '普通用户',
    distributor: '分销员',
    agent: '代理商',
    admin: '管理员'
  }
  return texts[role] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    disabled: '禁用',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

const getOrderStatusType = (status) => {
  const types = {
    paid: 'success',
    pending: 'warning',
    cancelled: 'danger'
  }
  return types[status] || ''
}

const getOrderStatusText = (status) => {
  const texts = {
    paid: '已支付',
    pending: '待支付',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getBalanceTypeTag = (type) => {
  const types = {
    recharge: 'success',
    consume: 'warning',
    refund: 'info',
    reward: 'success',
    deduct: 'danger'
  }
  return types[type] || ''
}

const getBalanceTypeText = (type) => {
  const texts = {
    recharge: '充值',
    consume: '消费',
    refund: '退款',
    reward: '奖励',
    deduct: '扣费'
  }
  return texts[type] || '未知'
}

const handleClose = () => {
  visible.value = false
  userDetail.value = null
}
</script>

<style lang="scss" scoped>
.user-detail {
  padding: 0 16px;
}

.info-section {
  margin-bottom: 32px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
  }
}

.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
}

.user-info {
  .username {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
  }
  
  .user-id {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 8px;
  }
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  
  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 12px;
    color: #64748b;
  }
}

.money-text {
  color: #f56c6c;
  font-weight: 600;
}

.positive-amount {
  color: #67c23a;
  font-weight: 600;
}

.negative-amount {
  color: #f56c6c;
  font-weight: 600;
}
</style>