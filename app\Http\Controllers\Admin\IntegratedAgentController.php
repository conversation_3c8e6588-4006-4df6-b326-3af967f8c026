<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AgentAccount;
use App\Models\AgentCommissionLog;
use App\Models\SubstationAgentRelation;
use App\Models\CommissionDistributionRule;
use App\Models\User;
use App\Models\Substation;
use App\Services\CommissionCalculatorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class IntegratedAgentController extends Controller
{
    protected $commissionCalculator;

    public function __construct(CommissionCalculatorService $commissionCalculator)
    {
        $this->commissionCalculator = $commissionCalculator;
    }

    /**
     * 显示代理商列表（整合版）
     */
    public function index(Request $request)
    {
        $query = AgentAccount::with(['user', 'substation', 'parentAgent'])
                            ->orderBy('created_at', 'desc');

        // 搜索过滤
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('agent_code', 'like', "%{$search}%")
                  ->orWhere('agent_name', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // 代理商等级过滤
        if ($request->filled('agent_level')) {
            $query->byAgentLevel($request->agent_level);
        }

        // 代理商类型过滤
        if ($request->filled('agent_type')) {
            $query->byAgentType($request->agent_type);
        }

        // 分站过滤
        if ($request->filled('substation_id')) {
            $query->bySubstation($request->substation_id);
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 有效期过滤
        if ($request->filled('validity_status')) {
            switch ($request->validity_status) {
                case 'valid':
                    $query->valid();
                    break;
                case 'expiring':
                    $query->expiringSoon(7);
                    break;
                case 'expired':
                    $query->expired();
                    break;
            }
        }

        $agents = $query->paginate(15);

        // 统计数据
        $stats = [
            'total' => AgentAccount::count(),
            'platform_agents' => AgentAccount::platformAgents()->count(),
            'substation_agents' => AgentAccount::substationAgents()->count(),
            'active' => AgentAccount::where('status', 'active')->count(),
            'expired' => AgentAccount::expired()->count(),
            'expiring_soon' => AgentAccount::expiringSoon(7)->count(),
        ];

        // 获取分站列表用于筛选
        $substations = Substation::where('status', 'active')->get();

        return view('admin.agents.integrated.index', compact('agents', 'stats', 'substations'));
    }

    /**
     * 显示创建代理商表单（整合版）
     */
    public function create()
    {
        // 获取可选用户（未开通代理商的用户）
        $users = User::whereDoesntHave('agentAccount')
                    ->where('status', 'active')
                    ->get();

        // 获取分站列表
        $substations = Substation::where('status', 'active')->get();

        // 获取可作为上级的代理商
        $parentAgents = AgentAccount::where('status', 'active')
                                  ->where('agent_grade', '<', 5)
                                  ->get();

        return view('admin.agents.integrated.create', compact('users', 'substations', 'parentAgents'));
    }

    /**
     * 存储新代理商（整合版）
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id|unique:agent_accounts,user_id',
            'agent_name' => 'required|string|max:100',
            'agent_level' => 'required|in:platform,substation',
            'agent_type' => 'required|in:individual,enterprise,channel',
            'substation_id' => 'nullable|exists:substations,id',
            'parent_agent_id' => 'nullable|exists:agent_accounts,id',
            'agent_grade' => 'required|integer|between:1,5',
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'nullable|date|after:today',
            'commission_type' => 'required|in:no_commission,percentage',
            'commission_rate' => 'nullable|numeric|between:0,100',
            'permissions' => 'array',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            // 验证代理商等级逻辑
            if ($request->agent_level === 'substation' && !$request->substation_id) {
                throw new \Exception('分站代理商必须选择所属分站');
            }

            if ($request->parent_agent_id) {
                $parentAgent = AgentAccount::find($request->parent_agent_id);
                if (!$parentAgent->canBeParentOf(new AgentAccount(['agent_grade' => $request->agent_grade]))) {
                    throw new \Exception('选择的上级代理商等级不符合要求');
                }
            }

            // 创建代理商账户
            $agent = new AgentAccount();
            $agent->user_id = $request->user_id;
            $agent->substation_id = $request->substation_id;
            $agent->agent_level = $request->agent_level;
            $agent->agent_type = $request->agent_type;
            $agent->parent_agent_id = $request->parent_agent_id;
            $agent->agent_grade = $request->agent_grade;
            $agent->agent_code = AgentAccount::generateAgentCode($request->agent_level);
            $agent->agent_name = $request->agent_name;
            $agent->status = 'active';
            
            // 设置有效期
            $agent->setValidityPeriod(
                $request->validity_period,
                $request->custom_end_date
            );

            // 设置佣金
            if ($request->commission_type === 'no_commission') {
                $agent->no_commission = true;
                $agent->commission_rate = 0;
            } else {
                $agent->no_commission = false;
                $agent->commission_rate = $request->commission_rate ?? 0;
            }

            $agent->permissions = $request->permissions ?? [];
            $agent->created_by = auth()->id();
            $agent->remark = $request->remark;

            $agent->save();

            // 如果是分站代理商，创建分站关联关系
            if ($request->agent_level === 'substation' && $request->substation_id) {
                SubstationAgentRelation::create([
                    'substation_id' => $request->substation_id,
                    'agent_id' => $agent->id,
                    'relation_type' => 'primary',
                    'commission_rate' => $agent->commission_rate,
                    'permissions' => $agent->permissions,
                    'status' => 'active',
                    'started_at' => now()
                ]);
            }

            // 更新用户的代理商信息
            $user = User::find($request->user_id);
            $user->agent_id = $agent->id;
            $user->agent_code = $agent->agent_code;
            $user->source_type = $request->agent_level === 'platform' ? 'agent' : 'substation_agent';
            $user->agent_bind_at = now();
            $user->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '代理商开通成功',
                'data' => [
                    'agent_id' => $agent->id,
                    'agent_code' => $agent->agent_code
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '开通失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示代理商详情（整合版）
     */
    public function show(AgentAccount $agent)
    {
        $agent->load([
            'user',
            'substation',
            'parentAgent',
            'childAgents.user',
            'substationRelations.substation',
            'commissionLogs' => function ($query) {
                $query->latest()->limit(10);
            }
        ]);

        // 获取佣金统计
        $commissionStats = $this->commissionCalculator->getAgentCommissionStats($agent);

        // 获取推广用户统计
        $userStats = [
            'total_users' => $agent->promotedUsers()->count(),
            'active_users' => $agent->promotedUsers()->where('status', 'active')->count(),
            'new_users_this_month' => $agent->promotedUsers()
                                           ->whereMonth('created_at', now()->month)
                                           ->count()
        ];

        return view('admin.agents.integrated.show', compact('agent', 'commissionStats', 'userStats'));
    }

    /**
     * 显示编辑代理商表单
     */
    public function edit(AgentAccount $agent)
    {
        $substations = Substation::where('status', 'active')->get();
        $parentAgents = AgentAccount::where('status', 'active')
                                  ->where('id', '!=', $agent->id)
                                  ->where('agent_grade', '<', $agent->agent_grade)
                                  ->get();

        return view('admin.agents.integrated.edit', compact('agent', 'substations', 'parentAgents'));
    }

    /**
     * 更新代理商信息
     */
    public function update(Request $request, AgentAccount $agent)
    {
        $validator = Validator::make($request->all(), [
            'agent_name' => 'required|string|max:100',
            'agent_type' => 'required|in:individual,enterprise,channel',
            'substation_id' => 'nullable|exists:substations,id',
            'parent_agent_id' => 'nullable|exists:agent_accounts,id',
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'nullable|date|after:today',
            'commission_type' => 'required|in:no_commission,percentage',
            'commission_rate' => 'nullable|numeric|between:0,100',
            'permissions' => 'array',
            'status' => 'required|in:active,inactive,suspended',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            // 更新基本信息
            $agent->agent_name = $request->agent_name;
            $agent->agent_type = $request->agent_type;
            $agent->substation_id = $request->substation_id;
            $agent->parent_agent_id = $request->parent_agent_id;
            $agent->status = $request->status;

            // 更新有效期
            $agent->setValidityPeriod(
                $request->validity_period,
                $request->custom_end_date
            );

            // 更新佣金设置
            if ($request->commission_type === 'no_commission') {
                $agent->no_commission = true;
                $agent->commission_rate = 0;
            } else {
                $agent->no_commission = false;
                $agent->commission_rate = $request->commission_rate ?? 0;
            }

            $agent->permissions = $request->permissions ?? [];
            $agent->remark = $request->remark;

            $agent->save();

            // 更新分站关联关系
            if ($agent->isSubstationAgent() && $request->substation_id) {
                $relation = SubstationAgentRelation::where('agent_id', $agent->id)
                                                 ->where('substation_id', $request->substation_id)
                                                 ->first();
                
                if ($relation) {
                    $relation->update([
                        'commission_rate' => $agent->commission_rate,
                        'permissions' => $agent->permissions,
                        'status' => $agent->status === 'active' ? 'active' : 'inactive'
                    ]);
                } else {
                    SubstationAgentRelation::create([
                        'substation_id' => $request->substation_id,
                        'agent_id' => $agent->id,
                        'relation_type' => 'primary',
                        'commission_rate' => $agent->commission_rate,
                        'permissions' => $agent->permissions,
                        'status' => $agent->status === 'active' ? 'active' : 'inactive',
                        'started_at' => now()
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '代理商信息更新成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量操作
     */
    public function batchAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,suspend,delete,renew',
            'agent_ids' => 'required|array',
            'agent_ids.*' => 'exists:agent_accounts,id',
            'renew_period' => 'required_if:action,renew|in:week,month,quarter,half_year,year'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $agentIds = $request->agent_ids;
        $action = $request->action;
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        DB::beginTransaction();
        try {
            foreach ($agentIds as $agentId) {
                $agent = AgentAccount::find($agentId);
                if (!$agent) {
                    $results['failed']++;
                    $results['errors'][] = "代理商 ID {$agentId} 不存在";
                    continue;
                }

                switch ($action) {
                    case 'activate':
                        $agent->status = 'active';
                        $agent->save();
                        break;
                    case 'suspend':
                        $agent->status = 'suspended';
                        $agent->save();
                        break;
                    case 'delete':
                        $agent->delete();
                        break;
                    case 'renew':
                        $agent->setValidityPeriod($request->renew_period);
                        $agent->status = 'active';
                        $agent->save();
                        break;
                }

                $results['success']++;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "批量操作完成：成功 {$results['success']} 个，失败 {$results['failed']} 个",
                'data' => $results
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量操作失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 佣金模拟计算
     */
    public function simulateCommission(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'user_id' => 'required|exists:users,id',
            'rule_id' => 'nullable|exists:commission_distribution_rules,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->commissionCalculator->simulateCommission(
            $request->amount,
            $request->user_id,
            $request->rule_id
        );

        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }

    /**
     * 获取代理商层级结构
     */
    public function getAgentHierarchy(AgentAccount $agent)
    {
        $hierarchy = [
            'agent' => $agent->load('user', 'substation'),
            'parent_path' => [],
            'children' => []
        ];

        // 获取上级路径
        $parent = $agent->parentAgent;
        while ($parent) {
            array_unshift($hierarchy['parent_path'], $parent->load('user'));
            $parent = $parent->parentAgent;
        }

        // 获取下级代理商
        $hierarchy['children'] = $agent->childAgents()
                                      ->with('user', 'childAgents')
                                      ->get()
                                      ->map(function ($child) {
                                          return [
                                              'agent' => $child,
                                              'children_count' => $child->childAgents()->count()
                                          ];
                                      });

        return response()->json([
            'success' => true,
            'data' => $hierarchy
        ]);
    }
}