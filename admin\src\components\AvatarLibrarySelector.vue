<template>
  <div class="avatar-library-selector">
    <!-- 头像库选择 -->
    <div class="library-selector">
      <el-select v-model="selectedLibrary" placeholder="选择头像库" @change="handleLibraryChange">
        <el-option
          v-for="library in avatarLibraries"
          :key="library.value"
          :label="library.label"
          :value="library.value"
        >
          <div class="library-option">
            <span class="library-name">{{ library.label }}</span>
            <span class="library-count">{{ library.count }}个头像</span>
          </div>
        </el-option>
      </el-select>
      
      <el-button @click="showUploadDialog = true" type="primary" plain>
        <el-icon><Plus /></el-icon>
        自定义上传
      </el-button>
    </div>

    <!-- 头像预览网格 -->
    <div class="avatar-grid">
      <div
        v-for="(avatar, index) in currentAvatars"
        :key="index"
        class="avatar-item"
        :class="{ selected: selectedAvatars.includes(avatar.url) }"
        @click="toggleAvatar(avatar.url)"
      >
        <img :src="avatar.url" :alt="`头像${index + 1}`" />
        <div class="avatar-overlay">
          <el-icon v-if="selectedAvatars.includes(avatar.url)"><Check /></el-icon>
        </div>
      </div>
    </div>

    <!-- 选择统计 -->
    <div class="selection-stats">
      <span class="stats-text">
        已选择 {{ selectedAvatars.length }} 个头像
        <template v-if="maxSelection > 0">
          / 最多 {{ maxSelection }} 个
        </template>
      </span>
      
      <div class="stats-actions">
        <el-button @click="selectAll" size="small" type="primary" plain>
          全选
        </el-button>
        <el-button @click="clearSelection" size="small">
          清空
        </el-button>
      </div>
    </div>

    <!-- 自定义上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="自定义头像上传"
      width="600px"
      append-to-body
    >
      <div class="custom-upload-section">
        <MediaUploader
          v-model="customAvatars"
          type="image"
          :multiple="true"
          :limit="20"
          accept="image/*"
          list-type="picture-card"
          @change="handleCustomUpload"
        >
          <template #tip>
            <div class="upload-tip">
              建议尺寸：100x100px，支持JPG、PNG格式，最多上传20个
            </div>
          </template>
        </MediaUploader>
      </div>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomAvatars">确定</el-button>
      </template>
    </el-dialog>

    <!-- 头像管理对话框 -->
    <el-dialog
      v-model="showManageDialog"
      title="头像库管理"
      width="800px"
      append-to-body
    >
      <div class="avatar-management">
        <div class="management-header">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索头像..."
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <div class="management-actions">
            <el-button @click="batchDelete" :disabled="!hasSelection" type="danger" plain>
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
            <el-button @click="exportAvatars" type="primary" plain>
              <el-icon><Download /></el-icon>
              导出头像
            </el-button>
          </div>
        </div>
        
        <div class="management-grid">
          <div
            v-for="(avatar, index) in filteredAvatars"
            :key="index"
            class="management-item"
            :class="{ selected: managementSelection.includes(avatar.url) }"
          >
            <img :src="avatar.url" :alt="`头像${index + 1}`" />
            <div class="item-overlay">
              <el-checkbox
                v-model="managementSelection"
                :label="avatar.url"
              />
            </div>
            <div class="item-actions">
              <el-button @click="previewAvatar(avatar)" size="small" type="primary" plain>
                预览
              </el-button>
              <el-button @click="deleteAvatar(avatar, index)" size="small" type="danger" plain>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showManageDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Check, Search, Delete, Download } from '@element-plus/icons-vue'
import MediaUploader from './MediaUploader.vue'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  maxSelection: {
    type: Number,
    default: 0 // 0表示无限制
  },
  multiple: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedLibrary = ref('default')
const selectedAvatars = ref([])
const showUploadDialog = ref(false)
const showManageDialog = ref(false)
const customAvatars = ref([])
const searchKeyword = ref('')
const managementSelection = ref([])

// 头像库数据
const avatarLibraries = ref([
  { value: 'default', label: '默认头像', count: 50 },
  { value: 'business', label: '商务头像', count: 30 },
  { value: 'cartoon', label: '卡通头像', count: 40 },
  { value: 'animal', label: '动物头像', count: 25 },
  { value: 'custom', label: '自定义头像', count: 0 }
])

const avatarData = reactive({
  default: [],
  business: [],
  cartoon: [],
  animal: [],
  custom: []
})

// 计算属性
const currentAvatars = computed(() => {
  return avatarData[selectedLibrary.value] || []
})

const filteredAvatars = computed(() => {
  if (!searchKeyword.value) return currentAvatars.value
  
  return currentAvatars.value.filter(avatar => 
    avatar.name && avatar.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const hasSelection = computed(() => {
  return managementSelection.value.length > 0
})

// 方法
const handleLibraryChange = (value) => {
  selectedLibrary.value = value
  loadAvatars(value)
}

const loadAvatars = async (library) => {
  try {
    // 模拟加载头像数据
    const response = await fetch(`/api/avatars/${library}`)
    const data = await response.json()
    
    if (data.success) {
      avatarData[library] = data.data
    }
  } catch (error) {
    // 使用模拟数据
    avatarData[library] = generateMockAvatars(library)
  }
}

const generateMockAvatars = (library) => {
  const count = avatarLibraries.value.find(lib => lib.value === library)?.count || 20
  const avatars = []
  
  for (let i = 1; i <= count; i++) {
    avatars.push({
      url: `/avatars/${library}/${i}.jpg`,
      name: `${library}_${i}`,
      size: '100x100'
    })
  }
  
  return avatars
}

const toggleAvatar = (url) => {
  if (!props.multiple) {
    selectedAvatars.value = [url]
    emit('update:modelValue', url)
    emit('change', url)
    return
  }
  
  const index = selectedAvatars.value.indexOf(url)
  
  if (index > -1) {
    selectedAvatars.value.splice(index, 1)
  } else {
    if (props.maxSelection > 0 && selectedAvatars.value.length >= props.maxSelection) {
      ElMessage.warning(`最多只能选择 ${props.maxSelection} 个头像`)
      return
    }
    selectedAvatars.value.push(url)
  }
  
  emit('update:modelValue', [...selectedAvatars.value])
  emit('change', [...selectedAvatars.value])
}

const selectAll = () => {
  if (props.maxSelection > 0) {
    selectedAvatars.value = currentAvatars.value.slice(0, props.maxSelection).map(avatar => avatar.url)
  } else {
    selectedAvatars.value = currentAvatars.value.map(avatar => avatar.url)
  }
  
  emit('update:modelValue', [...selectedAvatars.value])
  emit('change', [...selectedAvatars.value])
}

const clearSelection = () => {
  selectedAvatars.value = []
  emit('update:modelValue', [])
  emit('change', [])
}

const handleCustomUpload = (urls) => {
  customAvatars.value = urls
}

const saveCustomAvatars = () => {
  if (customAvatars.value.length > 0) {
    const customLibrary = avatarLibraries.value.find(lib => lib.value === 'custom')
    if (customLibrary) {
      customLibrary.count = customAvatars.value.length
    }
    
    avatarData.custom = customAvatars.value.map((url, index) => ({
      url,
      name: `custom_${index + 1}`,
      size: '100x100'
    }))
    
    selectedLibrary.value = 'custom'
    ElMessage.success('自定义头像保存成功')
  }
  
  showUploadDialog.value = false
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的头像吗？', '批量删除', {
      type: 'warning'
    })
    
    // 执行删除操作
    const currentLibraryData = avatarData[selectedLibrary.value]
    avatarData[selectedLibrary.value] = currentLibraryData.filter(
      avatar => !managementSelection.value.includes(avatar.url)
    )
    
    managementSelection.value = []
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const deleteAvatar = async (avatar, index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个头像吗？', '删除头像', {
      type: 'warning'
    })
    
    avatarData[selectedLibrary.value].splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const previewAvatar = (avatar) => {
  // 实现头像预览功能
  window.open(avatar.url, '_blank')
}

const exportAvatars = () => {
  // 实现头像导出功能
  ElMessage.info('导出功能开发中...')
}

// 初始化
onMounted(() => {
  loadAvatars('default')
  
  // 初始化选中状态
  if (props.modelValue) {
    if (Array.isArray(props.modelValue)) {
      selectedAvatars.value = [...props.modelValue]
    } else {
      selectedAvatars.value = [props.modelValue]
    }
  }
})
</script>

<style lang="scss" scoped>
.avatar-library-selector {
  .library-selector {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
    
    .el-select {
      flex: 1;
      max-width: 200px;
    }
    
    .library-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .library-name {
        font-weight: 500;
      }
      
      .library-count {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
    max-height: 300px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    
    .avatar-item {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      &.selected {
        border-color: #409eff;
        
        .avatar-overlay {
          opacity: 1;
        }
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(64, 158, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        
        .el-icon {
          color: white;
          font-size: 24px;
        }
      }
    }
  }
  
  .selection-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #e4e7ed;
    
    .stats-text {
      font-size: 14px;
      color: #606266;
    }
    
    .stats-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .custom-upload-section {
    .upload-tip {
      text-align: center;
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }
  
  .avatar-management {
    .management-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .management-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .management-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 16px;
      max-height: 400px;
      overflow-y: auto;
      
      .management-item {
        position: relative;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s;
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          
          .item-actions {
            opacity: 1;
          }
        }
        
        &.selected {
          border-color: #409eff;
        }
        
        img {
          width: 100%;
          height: 120px;
          object-fit: cover;
        }
        
        .item-overlay {
          position: absolute;
          top: 8px;
          right: 8px;
        }
        
        .item-actions {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.8);
          padding: 8px;
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;
          
          .el-button {
            flex: 1;
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
}
</style>
