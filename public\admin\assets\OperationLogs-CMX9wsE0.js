import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css               *//* empty css                *//* empty css                    */import{H as a}from"./echarts-D68jitv0.js";import{bj as l,bg as t,aY as s,a_ as o,U as i,aZ as d,bp as u,bq as n,b9 as r,b8 as p,by as c,aM as _,at as m,bh as b,bi as f,a$ as v,bw as g,bx as y,bk as h,bl as w,b1 as V,ay as k,bm as x,bB as j,br as U,Q as z,R as C}from"./element-plus-h2SQQM64.js";import{L as Y,r as T,e as D,k as I,l as M,E as A,z as P,t as W,D as q,u as S,F as B,Y as H,y as N,A as O,B as J}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const K={class:"app-container"},L={class:"stat-item"},$={class:"stat-content"},E={class:"stat-number"},F={class:"stat-item"},Q={class:"stat-content"},R={class:"stat-number"},Z={class:"stat-item"},G={class:"stat-content"},X={class:"stat-number"},ee={class:"stat-item"},ae={class:"stat-content"},le={class:"stat-number"},te={class:"card-header"},se={class:"user-info"},oe={class:"user-name"},ie={class:"user-role"},de={class:"module-name"},ue={class:"table-pagination"},ne={key:0,class:"log-detail"},re={class:"detail-section"},pe={key:0,class:"detail-section"},ce={class:"json-data"},_e={key:1,class:"detail-section"},me={class:"json-data"},be={key:2,class:"detail-section"},fe={class:"dialog-footer"},ve=e({__name:"OperationLogs",setup(e){const ve=Y({total:25687,today:156,active_users:45,errors:12}),ge=Y({page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),ye=T([{id:1,user:{nickname:"管理员",role_name:"超级管理员"},operation_type:"login",module:"system",description:"用户登录系统",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:120,created_at:"2024-01-01 10:00:00",request_data:{username:"admin"},response_data:{success:!0}},{id:2,user:{nickname:"普通用户",role_name:"用户"},operation_type:"create",module:"user",description:"创建新用户",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:350,created_at:"2024-01-01 09:30:00"}]),he=T([{id:1,nickname:"管理员"},{id:2,nickname:"普通用户"}]),we=Y({visible:!1,log:null}),Ve=Y({visible:!1}),ke=Y({type:"by_date",before_date:null,keep_count:1e4,status:""}),xe=T({}),je=T({}),Ue=T(!1),ze=T(!1),Ce=T(0),Ye=T("operation"),Te=e=>({login:"success",create:"primary",update:"warning",delete:"danger",export:"info",setting:"primary"}[e]||"info"),De=e=>({login:"登录",create:"创建",update:"更新",delete:"删除",export:"导出",setting:"设置"}[e]||e),Ie=e=>({user:"用户管理",order:"订单管理",finance:"财务管理",distribution:"分销管理",system:"系统设置"}[e]||e),Me=e=>({success:"success",failed:"danger",warning:"warning"}[e]||"info"),Ae=e=>({success:"成功",failed:"失败",warning:"警告"}[e]||e),Pe=e=>{Ye.value=e,qe(),z.info(`切换到${We(e)}`)},We=e=>({operation:"操作日志",security:"安全日志",permission:"权限日志",system:"系统日志"}[e]||"操作日志"),qe=e=>{Ue.value=!0,setTimeout(()=>{Ue.value=!1},500)},Se=()=>{Ue.value=!0,setTimeout(()=>{Ue.value=!1,z.success("搜索完成")},1e3)},Be=()=>{Object.assign(ge,{page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),Se()},He=()=>{z.success("导出任务已创建")},Ne=()=>{Se()},Oe=()=>{Ve.visible=!0},Je=async()=>{ze.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),z.success("日志清理完成"),Ve.visible=!1,Ne()}catch(e){z.error("日志清理失败")}finally{ze.value=!1}},Ke=e=>{ge.per_page=e,Se()},Le=e=>{ge.page=e,Se()};return D(()=>{Ce.value=ye.value.length,xe.value={title:{text:"操作类型分布",left:"center"},tooltip:{trigger:"item"},series:[{name:"操作类型",type:"pie",radius:"50%",data:[{value:1048,name:"登录"},{value:735,name:"创建"},{value:580,name:"更新"},{value:484,name:"删除"},{value:300,name:"导出"}]}]},je.value={title:{text:"操作趋势",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"操作次数",type:"line",data:[120,132,101,134,90,230,210]}]}}),(e,Y)=>{const T=t,D=l,We=s,qe=o,$e=d,Ee=p,Fe=r,Qe=n,Re=c,Ze=_,Ge=m,Xe=u,ea=f,aa=v,la=b,ta=y,sa=w,oa=h,ia=V,da=k,ua=j,na=x,ra=U,pa=g;return M(),I("div",K,[A(We,{class:"log-type-card",style:{"margin-bottom":"20px"}},{default:P(()=>[A(D,{modelValue:Ye.value,"onUpdate:modelValue":Y[0]||(Y[0]=e=>Ye.value=e),onTabChange:Pe},{default:P(()=>[A(T,{label:"操作日志",name:"operation"},{label:P(()=>Y[16]||(Y[16]=[W("span",null,[W("i",{class:"el-icon-operation"}),q(" 操作日志")],-1)])),_:1}),A(T,{label:"安全日志",name:"security"},{label:P(()=>Y[17]||(Y[17]=[W("span",null,[W("i",{class:"el-icon-lock"}),q(" 安全日志")],-1)])),_:1}),A(T,{label:"权限日志",name:"permission"},{label:P(()=>Y[18]||(Y[18]=[W("span",null,[W("i",{class:"el-icon-key"}),q(" 权限日志")],-1)])),_:1}),A(T,{label:"系统日志",name:"system"},{label:P(()=>Y[19]||(Y[19]=[W("span",null,[W("i",{class:"el-icon-monitor"}),q(" 系统日志")],-1)])),_:1})]),_:1},8,["modelValue"])]),_:1}),A($e,{gutter:20},{default:P(()=>[A(qe,{span:6},{default:P(()=>[A(We,{class:"stat-card"},{default:P(()=>[W("div",L,[Y[21]||(Y[21]=W("div",{class:"stat-icon total-icon"},[W("i",{class:"el-icon-document"})],-1)),W("div",$,[W("div",E,i(ve.total),1),Y[20]||(Y[20]=W("div",{class:"stat-label"},"总日志数",-1))])])]),_:1})]),_:1}),A(qe,{span:6},{default:P(()=>[A(We,{class:"stat-card"},{default:P(()=>[W("div",F,[Y[23]||(Y[23]=W("div",{class:"stat-icon today-icon"},[W("i",{class:"el-icon-calendar-today"})],-1)),W("div",Q,[W("div",R,i(ve.today),1),Y[22]||(Y[22]=W("div",{class:"stat-label"},"今日操作",-1))])])]),_:1})]),_:1}),A(qe,{span:6},{default:P(()=>[A(We,{class:"stat-card"},{default:P(()=>[W("div",Z,[Y[25]||(Y[25]=W("div",{class:"stat-icon user-icon"},[W("i",{class:"el-icon-user"})],-1)),W("div",G,[W("div",X,i(ve.active_users),1),Y[24]||(Y[24]=W("div",{class:"stat-label"},"活跃用户",-1))])])]),_:1})]),_:1}),A(qe,{span:6},{default:P(()=>[A(We,{class:"stat-card"},{default:P(()=>[W("div",ee,[Y[27]||(Y[27]=W("div",{class:"stat-icon error-icon"},[W("i",{class:"el-icon-warning"})],-1)),W("div",ae,[W("div",le,i(ve.errors),1),Y[26]||(Y[26]=W("div",{class:"stat-label"},"错误操作",-1))])])]),_:1})]),_:1})]),_:1}),A($e,{gutter:20,style:{"margin-top":"20px"}},{default:P(()=>[A(qe,{span:12},{default:P(()=>[A(We,null,{header:P(()=>Y[28]||(Y[28]=[W("div",{class:"card-header"},[W("span",null,"📊 操作类型统计")],-1)])),default:P(()=>[A(S(a),{class:"chart",option:xe.value,autoresize:""},null,8,["option"])]),_:1})]),_:1}),A(qe,{span:12},{default:P(()=>[A(We,null,{header:P(()=>Y[29]||(Y[29]=[W("div",{class:"card-header"},[W("span",null,"📈 操作趋势")],-1)])),default:P(()=>[A(S(a),{class:"chart",option:je.value,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),A(We,{style:{"margin-top":"20px"}},{header:P(()=>Y[30]||(Y[30]=[W("div",{class:"card-header"},[W("span",null,"🔍 日志筛选")],-1)])),default:P(()=>[A(Xe,{inline:!0,model:ge,class:"filter-form"},{default:P(()=>[A(Qe,{label:"操作用户"},{default:P(()=>[A(Fe,{modelValue:ge.user_id,"onUpdate:modelValue":Y[1]||(Y[1]=e=>ge.user_id=e),placeholder:"选择用户",clearable:"",filterable:""},{default:P(()=>[(M(!0),I(B,null,H(he.value,e=>(M(),N(Ee,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),A(Qe,{label:"操作类型"},{default:P(()=>[A(Fe,{modelValue:ge.operation_type,"onUpdate:modelValue":Y[2]||(Y[2]=e=>ge.operation_type=e),placeholder:"选择类型",clearable:""},{default:P(()=>[A(Ee,{label:"登录",value:"login"}),A(Ee,{label:"创建",value:"create"}),A(Ee,{label:"更新",value:"update"}),A(Ee,{label:"删除",value:"delete"}),A(Ee,{label:"导出",value:"export"}),A(Ee,{label:"设置",value:"setting"})]),_:1},8,["modelValue"])]),_:1}),A(Qe,{label:"模块"},{default:P(()=>[A(Fe,{modelValue:ge.module,"onUpdate:modelValue":Y[3]||(Y[3]=e=>ge.module=e),placeholder:"选择模块",clearable:""},{default:P(()=>[A(Ee,{label:"用户管理",value:"user"}),A(Ee,{label:"订单管理",value:"order"}),A(Ee,{label:"财务管理",value:"finance"}),A(Ee,{label:"分销管理",value:"distribution"}),A(Ee,{label:"系统设置",value:"system"})]),_:1},8,["modelValue"])]),_:1}),A(Qe,{label:"状态"},{default:P(()=>[A(Fe,{modelValue:ge.status,"onUpdate:modelValue":Y[4]||(Y[4]=e=>ge.status=e),placeholder:"选择状态",clearable:""},{default:P(()=>[A(Ee,{label:"成功",value:"success"}),A(Ee,{label:"失败",value:"failed"}),A(Ee,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1}),A(Qe,{label:"时间范围"},{default:P(()=>[A(Re,{modelValue:ge.date_range,"onUpdate:modelValue":Y[5]||(Y[5]=e=>ge.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),A(Qe,{label:"IP地址"},{default:P(()=>[A(Ze,{modelValue:ge.ip,"onUpdate:modelValue":Y[6]||(Y[6]=e=>ge.ip=e),placeholder:"IP地址",clearable:""},null,8,["modelValue"])]),_:1}),A(Qe,null,{default:P(()=>[A(Ge,{type:"primary",onClick:Se},{default:P(()=>Y[31]||(Y[31]=[W("i",{class:"el-icon-search"},null,-1),q(" 搜索 ",-1)])),_:1,__:[31]}),A(Ge,{onClick:Be},{default:P(()=>Y[32]||(Y[32]=[W("i",{class:"el-icon-refresh"},null,-1),q(" 重置 ",-1)])),_:1,__:[32]}),A(Ge,{type:"success",onClick:He},{default:P(()=>Y[33]||(Y[33]=[W("i",{class:"el-icon-download"},null,-1),q(" 导出 ",-1)])),_:1,__:[33]})]),_:1})]),_:1},8,["model"])]),_:1}),A(We,{style:{"margin-top":"20px"}},{header:P(()=>[W("div",te,[Y[36]||(Y[36]=W("span",null,"📋 操作日志",-1)),W("div",null,[A(Ge,{type:"primary",onClick:Ne},{default:P(()=>Y[34]||(Y[34]=[W("i",{class:"el-icon-refresh"},null,-1),q(" 刷新 ",-1)])),_:1,__:[34]}),A(Ge,{type:"warning",onClick:Oe},{default:P(()=>Y[35]||(Y[35]=[W("i",{class:"el-icon-delete"},null,-1),q(" 清理日志 ",-1)])),_:1,__:[35]})])])]),default:P(()=>[O((M(),N(la,{data:ye.value,style:{width:"100%"}},{default:P(()=>[A(ea,{prop:"id",label:"ID",width:"80"}),A(ea,{prop:"user",label:"操作用户",width:"120"},{default:P(e=>[W("div",se,[W("div",oe,i(e.row.user?.nickname||"系统"),1),W("div",ie,i(e.row.user?.role_name||"System"),1)])]),_:1}),A(ea,{prop:"operation_type",label:"操作类型",width:"100"},{default:P(e=>[A(aa,{type:Te(e.row.operation_type)},{default:P(()=>[q(i(De(e.row.operation_type)),1)]),_:2},1032,["type"])]),_:1}),A(ea,{prop:"module",label:"模块",width:"100"},{default:P(e=>[W("span",de,i(Ie(e.row.module)),1)]),_:1}),A(ea,{prop:"description",label:"操作描述","min-width":"200","show-overflow-tooltip":""}),A(ea,{prop:"status",label:"状态",width:"80"},{default:P(e=>[A(aa,{type:Me(e.row.status),size:"small"},{default:P(()=>[q(i(Ae(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),A(ea,{prop:"ip_address",label:"IP地址",width:"120"}),A(ea,{prop:"user_agent",label:"用户代理",width:"150","show-overflow-tooltip":""}),A(ea,{prop:"execution_time",label:"耗时",width:"80"},{default:P(e=>[q(i(e.row.execution_time)+"ms ",1)]),_:1}),A(ea,{prop:"created_at",label:"操作时间",width:"160"}),A(ea,{label:"操作",width:"120"},{default:P(e=>[A(Ge,{type:"primary",size:"small",onClick:a=>{return l=e.row,we.log=l,void(we.visible=!0);var l}},{default:P(()=>Y[37]||(Y[37]=[q(" 详情 ",-1)])),_:2,__:[37]},1032,["onClick"]),"failed"===e.row.status?(M(),N(Ge,{key:0,type:"danger",size:"small",onClick:a=>(e.row,void C.confirm("确定要删除这条日志吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{z.success("删除成功"),Ne()}))},{default:P(()=>Y[38]||(Y[38]=[q(" 删除 ",-1)])),_:2,__:[38]},1032,["onClick"])):J("",!0)]),_:1})]),_:1},8,["data"])),[[pa,Ue.value]]),W("div",ue,[A(ta,{"current-page":ge.page,"onUpdate:currentPage":Y[7]||(Y[7]=e=>ge.page=e),"page-size":ge.per_page,"onUpdate:pageSize":Y[8]||(Y[8]=e=>ge.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Ce.value,onSizeChange:Ke,onCurrentChange:Le},null,8,["current-page","page-size","total"])])]),_:1}),A(da,{title:"操作详情",modelValue:we.visible,"onUpdate:modelValue":Y[9]||(Y[9]=e=>we.visible=e),width:"800px"},{default:P(()=>[we.log?(M(),I("div",ne,[A(oa,{column:2,border:""},{default:P(()=>[A(sa,{label:"操作ID"},{default:P(()=>[q(i(we.log.id),1)]),_:1}),A(sa,{label:"操作用户"},{default:P(()=>[q(i(we.log.user?.nickname||"系统"),1)]),_:1}),A(sa,{label:"操作类型"},{default:P(()=>[A(aa,{type:Te(we.log.operation_type)},{default:P(()=>[q(i(De(we.log.operation_type)),1)]),_:1},8,["type"])]),_:1}),A(sa,{label:"模块"},{default:P(()=>[q(i(Ie(we.log.module)),1)]),_:1}),A(sa,{label:"操作描述"},{default:P(()=>[q(i(we.log.description),1)]),_:1}),A(sa,{label:"状态"},{default:P(()=>[A(aa,{type:Me(we.log.status)},{default:P(()=>[q(i(Ae(we.log.status)),1)]),_:1},8,["type"])]),_:1}),A(sa,{label:"IP地址"},{default:P(()=>[q(i(we.log.ip_address),1)]),_:1}),A(sa,{label:"执行时间"},{default:P(()=>[q(i(we.log.execution_time)+"ms ",1)]),_:1}),A(sa,{label:"操作时间"},{default:P(()=>[q(i(we.log.created_at),1)]),_:1})]),_:1}),W("div",re,[Y[39]||(Y[39]=W("h4",null,"用户代理",-1)),A(Ze,{type:"textarea",value:we.log.user_agent,readonly:""},null,8,["value"])]),we.log.request_data?(M(),I("div",pe,[Y[40]||(Y[40]=W("h4",null,"请求数据",-1)),W("pre",ce,i(JSON.stringify(we.log.request_data,null,2)),1)])):J("",!0),we.log.response_data?(M(),I("div",_e,[Y[41]||(Y[41]=W("h4",null,"响应数据",-1)),W("pre",me,i(JSON.stringify(we.log.response_data,null,2)),1)])):J("",!0),we.log.error_message?(M(),I("div",be,[Y[42]||(Y[42]=W("h4",null,"错误信息",-1)),A(ia,{title:we.log.error_message,type:"error","show-icon":""},null,8,["title"])])):J("",!0)])):J("",!0)]),_:1},8,["modelValue"]),A(da,{title:"清理日志",modelValue:Ve.visible,"onUpdate:modelValue":Y[15]||(Y[15]=e=>Ve.visible=e),width:"500px"},{footer:P(()=>[W("div",fe,[A(Ge,{onClick:Y[14]||(Y[14]=e=>Ve.visible=!1)},{default:P(()=>Y[46]||(Y[46]=[q("取消",-1)])),_:1,__:[46]}),A(Ge,{type:"danger",onClick:Je,loading:ze.value},{default:P(()=>Y[47]||(Y[47]=[q(" 确认清理 ",-1)])),_:1,__:[47]},8,["loading"])])]),default:P(()=>[A(Xe,{model:ke,"label-width":"100px"},{default:P(()=>[A(Qe,{label:"清理方式"},{default:P(()=>[A(na,{modelValue:ke.type,"onUpdate:modelValue":Y[10]||(Y[10]=e=>ke.type=e)},{default:P(()=>[A(ua,{label:"by_date"},{default:P(()=>Y[43]||(Y[43]=[q("按时间",-1)])),_:1,__:[43]}),A(ua,{label:"by_count"},{default:P(()=>Y[44]||(Y[44]=[q("按数量",-1)])),_:1,__:[44]}),A(ua,{label:"by_status"},{default:P(()=>Y[45]||(Y[45]=[q("按状态",-1)])),_:1,__:[45]})]),_:1},8,["modelValue"])]),_:1}),"by_date"===ke.type?(M(),N(Qe,{key:0,label:"清理条件"},{default:P(()=>[A(Re,{modelValue:ke.before_date,"onUpdate:modelValue":Y[11]||(Y[11]=e=>ke.before_date=e),type:"date",placeholder:"清理此日期之前的日志"},null,8,["modelValue"])]),_:1})):J("",!0),"by_count"===ke.type?(M(),N(Qe,{key:1,label:"保留数量"},{default:P(()=>[A(ra,{modelValue:ke.keep_count,"onUpdate:modelValue":Y[12]||(Y[12]=e=>ke.keep_count=e),min:1e3,max:1e5},null,8,["modelValue"])]),_:1})):J("",!0),"by_status"===ke.type?(M(),N(Qe,{key:2,label:"清理状态"},{default:P(()=>[A(Fe,{modelValue:ke.status,"onUpdate:modelValue":Y[13]||(Y[13]=e=>ke.status=e),placeholder:"选择要清理的状态"},{default:P(()=>[A(Ee,{label:"成功",value:"success"}),A(Ee,{label:"失败",value:"failed"}),A(Ee,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1})):J("",!0),A(Qe,null,{default:P(()=>[A(ia,{title:"注意：清理操作不可恢复，请谨慎操作！",type:"warning","show-icon":"",closable:!1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-d52b4139"]]);export{ve as default};
