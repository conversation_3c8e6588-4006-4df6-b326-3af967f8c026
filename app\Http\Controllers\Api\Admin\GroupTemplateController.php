<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\GroupTemplate;
use App\Models\WechatGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

/**
 * 群组模板管理控制器
 */
class GroupTemplateController extends Controller
{
    /**
     * 获取模板列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'category' => 'string|in:technology,finance,education,fitness,business,social,entertainment,other',
                'type' => 'string|in:system,user',
                'status' => 'string|in:active,inactive',
                'is_public' => 'boolean',
                'search' => 'string|max:255',
                'sort_by' => 'string|in:created_at,name,use_count,rating',
                'sort_order' => 'string|in:asc,desc'
            ]);

            $query = GroupTemplate::with('creator');

            // 搜索
            if (!empty($params['search'])) {
                $search = $params['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // 分类筛选
            if (!empty($params['category'])) {
                $query->where('category', $params['category']);
            }

            // 类型筛选
            if (!empty($params['type'])) {
                $query->where('type', $params['type']);
            }

            // 状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 公开状态筛选
            if (isset($params['is_public'])) {
                $query->where('is_public', $params['is_public']);
            }

            // 排序
            $sortBy = $params['sort_by'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // 分页
            $perPage = $params['per_page'] ?? 15;
            $result = $query->paginate($perPage);

            return $this->success([
                'data' => $result->items(),
                'total' => $result->total(),
                'per_page' => $result->perPage(),
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage()
            ], '获取模板列表成功');

        } catch (\Exception $e) {
            Log::error('获取模板列表失败', [
                'error' => $e->getMessage(),
                'params' => $request->all()
            ]);

            return $this->error('获取模板列表失败');
        }
    }

    /**
     * 创建模板
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:100|unique:group_templates,name',
                'category' => 'required|string|in:technology,finance,education,fitness,business,social,entertainment,other',
                'description' => 'nullable|string|max:1000',
                'preview_image' => 'nullable|string|max:500',
                'template_data' => 'required|array',
                'type' => 'string|in:system,user',
                'is_public' => 'boolean',
                'sort_order' => 'integer|min:0|max:9999'
            ]);

            // 验证模板数据结构
            $validationResult = $this->validateTemplateData($data['template_data']);
            if (!$validationResult['valid']) {
                return $this->error($validationResult['message'], 400);
            }

            $data['creator_id'] = auth()->id();
            $data['type'] = $data['type'] ?? 'user';
            $data['is_public'] = $data['is_public'] ?? false;
            $data['status'] = 'active';

            $template = GroupTemplate::create($data);

            Log::info('模板创建成功', [
                'template_id' => $template->id,
                'name' => $template->name,
                'category' => $template->category,
                'user_id' => auth()->id()
            ]);

            return $this->success($template->load('creator'), '模板创建成功');

        } catch (\Exception $e) {
            Log::error('模板创建失败', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    public function show(int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::with('creator')->find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            // 获取模板统计信息
            $stats = $template->getStats();
            $template->stats = $stats;

            // 获取相似模板
            $similarTemplates = $template->getSimilarTemplates(5);
            $template->similar_templates = $similarTemplates;

            return $this->success($template, '获取模板详情成功');

        } catch (\Exception $e) {
            Log::error('获取模板详情失败', [
                'template_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取模板详情失败');
        }
    }

    /**
     * 更新模板
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            // 检查权限
            if (!$template->canEdit(auth()->user())) {
                return $this->error('没有权限编辑此模板', 403);
            }

            $data = $request->validate([
                'name' => [
                    'sometimes',
                    'required',
                    'string',
                    'max:100',
                    Rule::unique('group_templates', 'name')->ignore($id)
                ],
                'category' => 'sometimes|required|string|in:technology,finance,education,fitness,business,social,entertainment,other',
                'description' => 'sometimes|nullable|string|max:1000',
                'preview_image' => 'sometimes|nullable|string|max:500',
                'template_data' => 'sometimes|required|array',
                'is_public' => 'sometimes|boolean',
                'status' => 'sometimes|string|in:active,inactive',
                'sort_order' => 'sometimes|integer|min:0|max:9999'
            ]);

            // 验证模板数据结构
            if (isset($data['template_data'])) {
                $validationResult = $this->validateTemplateData($data['template_data']);
                if (!$validationResult['valid']) {
                    return $this->error($validationResult['message'], 400);
                }
            }

            $template->update($data);

            Log::info('模板更新成功', [
                'template_id' => $id,
                'user_id' => auth()->id()
            ]);

            return $this->success($template->load('creator'), '模板更新成功');

        } catch (\Exception $e) {
            Log::error('模板更新失败', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除模板
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            // 检查权限
            if (!$template->canEdit(auth()->user())) {
                return $this->error('没有权限删除此模板', 403);
            }

            // 系统模板不能删除
            if ($template->type === 'system') {
                return $this->error('系统模板不能删除', 403);
            }

            $template->delete();

            Log::info('模板删除成功', [
                'template_id' => $id,
                'name' => $template->name,
                'user_id' => auth()->id()
            ]);

            return $this->success(null, '模板删除成功');

        } catch (\Exception $e) {
            Log::error('模板删除失败', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板删除失败：' . $e->getMessage());
        }
    }

    /**
     * 应用模板
     */
    public function apply(Request $request, int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            if (!$template->canUse()) {
                return $this->error('模板不可用', 403);
            }

            $data = $request->validate([
                'existing_data' => 'nullable|array'
            ]);

            $existingData = $data['existing_data'] ?? [];
            $appliedData = $template->applyToGroupData($existingData);

            // 增加使用次数
            $template->incrementUseCount();

            Log::info('模板应用成功', [
                'template_id' => $id,
                'template_name' => $template->name,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'template' => $template,
                'applied_data' => $appliedData
            ], '模板应用成功');

        } catch (\Exception $e) {
            Log::error('模板应用失败', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板应用失败：' . $e->getMessage());
        }
    }

    /**
     * 复制模板
     */
    public function duplicate(Request $request, int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            $data = $request->validate([
                'name' => 'required|string|max:100|unique:group_templates,name'
            ]);

            $newTemplate = $template->duplicate($data['name']);

            Log::info('模板复制成功', [
                'original_id' => $id,
                'new_id' => $newTemplate->id,
                'new_name' => $data['name'],
                'user_id' => auth()->id()
            ]);

            return $this->success($newTemplate->load('creator'), '模板复制成功');

        } catch (\Exception $e) {
            Log::error('模板复制失败', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板复制失败：' . $e->getMessage());
        }
    }

    /**
     * 从群组创建模板
     */
    public function createFromGroup(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'group_id' => 'required|integer|exists:wechat_groups,id',
                'name' => 'required|string|max:100|unique:group_templates,name',
                'description' => 'nullable|string|max:1000',
                'category' => 'required|string|in:technology,finance,education,fitness,business,social,entertainment,other',
                'is_public' => 'boolean'
            ]);

            $group = WechatGroup::find($data['group_id']);

            if (!$group) {
                return $this->error('群组不存在', 404);
            }

            $templateInfo = [
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'category' => $data['category'],
                'is_public' => $data['is_public'] ?? false
            ];

            $template = GroupTemplate::createFromGroupData($group->toArray(), $templateInfo);

            Log::info('从群组创建模板成功', [
                'group_id' => $data['group_id'],
                'template_id' => $template->id,
                'template_name' => $template->name,
                'user_id' => auth()->id()
            ]);

            return $this->success($template->load('creator'), '模板创建成功');

        } catch (\Exception $e) {
            Log::error('从群组创建模板失败', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return $this->error('模板创建失败：' . $e->getMessage());
        }
    }

    /**
     * 模板评分
     */
    public function rate(Request $request, int $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'rating' => 'required|numeric|min:1|max:5'
            ]);

            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            $rating = $data['rating'];
            $template->addRating($rating);

            Log::info('模板评分成功', [
                'template_id' => $id,
                'rating' => $rating,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'template' => $template->fresh(),
                'average_rating' => $template->average_rating
            ], '评分成功');

        } catch (\Exception $e) {
            Log::error('模板评分失败', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('评分失败：' . $e->getMessage());
        }
    }

    /**
     * 获取模板预览
     */
    public function preview(int $id): JsonResponse
    {
        try {
            $template = GroupTemplate::find($id);

            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            $previewData = $template->getPreviewData();

            return $this->success($previewData, '获取预览数据成功');

        } catch (\Exception $e) {
            Log::error('获取模板预览失败', [
                'template_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取预览数据失败');
        }
    }

    /**
     * 批量操作模板
     */
    public function batchAction(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'action' => 'required|string|in:enable,disable,delete,publish,unpublish',
                'template_ids' => 'required|array|min:1',
                'template_ids.*' => 'integer|exists:group_templates,id'
            ]);

            $action = $data['action'];
            $templateIds = $data['template_ids'];
            $results = [];

            foreach ($templateIds as $templateId) {
                try {
                    $result = $this->executeBatchAction($action, $templateId);
                    $results[$templateId] = $result;
                } catch (\Exception $e) {
                    $results[$templateId] = [
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
            }

            $successCount = collect($results)->where('success', true)->count();
            $totalCount = count($results);

            Log::info('模板批量操作完成', [
                'action' => $action,
                'total' => $totalCount,
                'success' => $successCount,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'action' => $action,
                'total_count' => $totalCount,
                'success_count' => $successCount,
                'results' => $results
            ], "批量操作完成，成功处理 {$successCount}/{$totalCount} 个模板");

        } catch (\Exception $e) {
            Log::error('模板批量操作失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }

    // 私有方法

    /**
     * 验证模板数据结构
     */
    private function validateTemplateData(array $templateData): array
    {
        $requiredFields = ['title', 'description', 'category'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!isset($templateData[$field]) || empty($templateData[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'valid' => false,
                'message' => '模板数据缺少必需字段：' . implode(', ', $missingFields)
            ];
        }

        // 验证价格字段
        if (isset($templateData['price']) && (!is_numeric($templateData['price']) || $templateData['price'] < 0)) {
            return [
                'valid' => false,
                'message' => '价格必须是非负数'
            ];
        }

        // 验证成员上限
        if (isset($templateData['member_limit']) && (!is_integer($templateData['member_limit']) || $templateData['member_limit'] < 1)) {
            return [
                'valid' => false,
                'message' => '成员上限必须是正整数'
            ];
        }

        return [
            'valid' => true,
            'message' => '验证通过'
        ];
    }

    /**
     * 执行批量操作
     */
    private function executeBatchAction(string $action, int $templateId): array
    {
        $template = GroupTemplate::find($templateId);

        if (!$template) {
            return [
                'success' => false,
                'message' => '模板不存在'
            ];
        }

        // 检查权限
        if (!$template->canEdit(auth()->user())) {
            return [
                'success' => false,
                'message' => '没有权限操作此模板'
            ];
        }

        switch ($action) {
            case 'enable':
                $template->update(['status' => 'active']);
                return [
                    'success' => true,
                    'message' => '启用成功'
                ];

            case 'disable':
                $template->update(['status' => 'inactive']);
                return [
                    'success' => true,
                    'message' => '禁用成功'
                ];

            case 'publish':
                $template->update(['is_public' => true]);
                return [
                    'success' => true,
                    'message' => '发布成功'
                ];

            case 'unpublish':
                $template->update(['is_public' => false]);
                return [
                    'success' => true,
                    'message' => '取消发布成功'
                ];

            case 'delete':
                if ($template->type === 'system') {
                    return [
                        'success' => false,
                        'message' => '系统模板不能删除'
                    ];
                }
                $template->delete();
                return [
                    'success' => true,
                    'message' => '删除成功'
                ];

            default:
                return [
                    'success' => false,
                    'message' => '不支持的操作'
                ];
        }
    }
}