<template>
  <div class="notification-center-card">
    <div class="card-header">
      <h3 class="card-title">通知中心</h3>
      <el-badge :value="unreadCount" :hidden="unreadCount === 0">
        <el-button text @click="viewAll">
          查看全部
        </el-button>
      </el-badge>
    </div>
    <div class="notifications-list">
      <div 
        v-for="notification in notifications.slice(0, 5)" 
        :key="notification.id"
        class="notification-item"
        :class="{ unread: !notification.read }"
        @click="markAsRead(notification.id)"
      >
        <div class="notification-icon" :class="notification.type">
          <el-icon>
            <component :is="getNotificationIcon(notification.type)" />
          </el-icon>
        </div>
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
        </div>
        <div v-if="!notification.read" class="unread-dot"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

const props = defineProps({
  notifications: {
    type: Array,
    default: () => []
  }
})

const unreadCount = computed(() => {
  return props.notifications.filter(n => !n.read).length
})

const getNotificationIcon = (type) => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: SuccessFilled,
    error: Warning
  }
  return iconMap[type] || InfoFilled
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return date.toLocaleDateString()
}

const markAsRead = (notificationId) => {
  const notification = props.notifications.find(n => n.id === notificationId)
  if (notification && !notification.read) {
    notification.read = true
    ElMessage.success('已标记为已读')
  }
}

const viewAll = () => {
  ElMessage.info('跳转到通知列表页面')
}
</script>

<style lang="scss" scoped>
.notification-center-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .notifications-list {
    .notification-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f9fafb;
        border-radius: 12px;
        padding: 12px;
        margin: 0 -12px;
      }

      &.unread {
        background: rgba(59, 130, 246, 0.05);
      }

      .notification-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;

        &.warning {
          background: #fef3c7;
          color: #d97706;
        }

        &.info {
          background: #dbeafe;
          color: #2563eb;
        }

        &.success {
          background: #dcfce7;
          color: #16a34a;
        }

        &.error {
          background: #fee2e2;
          color: #dc2626;
        }
      }

      .notification-content {
        flex: 1;

        .notification-title {
          font-size: 14px;
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .notification-time {
          font-size: 12px;
          color: #6b7280;
        }
      }

      .unread-dot {
        width: 8px;
        height: 8px;
        background: #3b82f6;
        border-radius: 50%;
        flex-shrink: 0;
      }
    }
  }
}
</style>