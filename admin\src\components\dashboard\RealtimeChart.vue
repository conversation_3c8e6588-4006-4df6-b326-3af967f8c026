<template>
  <div class="realtime-chart-card">
    <div class="card-header">
      <h3 class="card-title">实时数据概览</h3>
      <div class="card-actions">
        <el-select v-model="timeRange" size="small" class="time-selector" @change="updateChart">
          <el-option label="今日" value="today" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="本年" value="year" />
        </el-select>
      </div>
    </div>
    <div class="chart-container">
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

const props = defineProps({
  timeRange: {
    type: String,
    default: 'today'
  }
})

const timeRange = ref(props.timeRange)
const chartRef = ref(null)

const updateChart = () => {
  // 模拟图表更新
  console.log('更新图表:', timeRange.value)
}

const initChart = () => {
  if (!chartRef.value) return
  
  // 这里可以集成 ECharts 或其他图表库
  // 现在用简单的样式模拟图表
  chartRef.value.innerHTML = `
    <div class="mock-chart">
      <div class="chart-line"></div>
      <div class="chart-data">
        <div class="data-point" style="left: 10%; height: 60%"></div>
        <div class="data-point" style="left: 25%; height: 80%"></div>
        <div class="data-point" style="left: 40%; height: 45%"></div>
        <div class="data-point" style="left: 55%; height: 90%"></div>
        <div class="data-point" style="left: 70%; height: 70%"></div>
        <div class="data-point" style="left: 85%; height: 85%"></div>
      </div>
    </div>
  `
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.realtime-chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .time-selector {
      width: 100px;
    }
  }

  .chart-container {
    height: calc(100% - 60px);
    min-height: 200px;

    .chart {
      width: 100%;
      height: 100%;
    }
  }
}

// 模拟图表样式
:deep(.mock-chart) {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  overflow: hidden;

  .chart-line {
    position: absolute;
    bottom: 20%;
    left: 5%;
    right: 5%;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
    border-radius: 1px;
  }

  .chart-data {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;

    .data-point {
      position: absolute;
      bottom: 0;
      width: 8px;
      background: linear-gradient(180deg, #3b82f6, #1d4ed8);
      border-radius: 4px 4px 0 0;
      animation: chartPulse 2s ease-in-out infinite;

      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.4s; }
      &:nth-child(4) { animation-delay: 0.6s; }
      &:nth-child(5) { animation-delay: 0.8s; }
      &:nth-child(6) { animation-delay: 1s; }
    }
  }
}

@keyframes chartPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}
</style>