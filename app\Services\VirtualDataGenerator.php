<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

/**
 * 虚拟数据生成器
 * 借鉴ThinkPHP的虚拟成员生成逻辑，并进行增强
 * 
 * <AUTHOR> Enhancement
 * @date 2024-12-19
 */
class VirtualDataGenerator
{
    /**
     * 昵称库（从ThinkPHP源码借鉴并扩展）
     */
    private $nicknames = [
        // 文艺风格
        '最美的太阳花', '孤海的浪漫', '薰衣草', '木槿花', '森林小巷少女与狐',
        '冬日暖阳', '午後の夏天', '嘴角的美人痣', '朽梦挽歌', '心淡然',
        '歇斯底里的狂笑', '夏好温暖', '彼岸花开', '岸与海的距离', '猫味萝莉',
        '软甜阮甜', '枯酒无味', '寄个拥抱', '少女病', '江南酒馆',
        '淡尘轻烟', '过气软妹', '檬℃柠叶', '仙九', '且听风铃',
        '野性萌', '樱桃小丸子', '少女の烦躁期', '无名小姐', '香味少女',
        '清澈的眼眸', '海草舞蹈', '淡淡de茶香味', '雨后彩虹', '安全等待你来',
        '薄荷蓝', '指尖上的星空', '雲朵兒', '准风月谈', '柠檬',
        '一整个夏天', '青狐', '暧昧的笑', '黄六', '李家闵',
        
        // 商务风格
        '创业先锋', '商界精英', '投资达人', '职场新秀', '行业专家',
        '市场总监', '产品经理', '技术大牛', '运营高手', '销售冠军',
        '财务顾问', '法务专员', '人力资源', '项目负责人', '团队领导',
        
        // 年轻潮流
        '潮流达人', '时尚博主', '美妆达人', '健身教练', '美食探店',
        '旅行博主', '摄影师', '设计师', '程序员', '自媒体人',
        '网红小姐姐', '帅气小哥哥', '萌妹子', '型男', '女神',
        
        // 随机字符组合
        '用户' . rand(10000, 99999),
        '会员' . rand(1000, 9999),
        '粉丝' . rand(100, 999)
    ];
    
    /**
     * 评论模板库
     */
    private $commentTemplates = [
        // 感谢类
        '真的太感谢群主了，学到很多！',
        '群主分享的内容太实用了，已经用上了',
        '感谢群主的无私分享，受益匪浅',
        '跟着群主学习，进步很大',
        '群主太厉害了，佩服佩服',
        
        // 认可类
        '群里氛围很好，大家都很热心',
        '这个群质量很高，值得推荐',
        '每天都有新收获，物超所值',
        '内容很实用，正是我需要的',
        '加入这个群是正确的选择',
        
        // 收获类
        '今天学到了新技能，开心',
        '按照群里的方法，真的有效果',
        '实践了群里的建议，效果不错',
        '群里的资源很丰富，太棒了',
        '每天都有干货分享，赞',
        
        // 互动类
        '大家一起加油，共同进步',
        '有问题随时问，群友都会帮忙',
        '群里高手很多，学习氛围浓厚',
        '新人报到，请多关照',
        '一起努力，一起成长',
        
        // 推荐类
        '已经推荐给朋友了，他们也想加入',
        '这么好的群，必须分享给朋友',
        '强烈推荐大家加入',
        '错过这个群真的会后悔',
        '早加入早受益，不要犹豫'
    ];
    
    /**
     * 头像路径配置
     */
    private $avatarPaths = [
        'qq' => 'face/qq/',      // QQ风格头像
        'za' => 'face/za/',       // 综合风格头像
        'business' => 'face/business/',  // 商务风格
        'young' => 'face/young/'        // 年轻风格
    ];
    
    /**
     * 生成虚拟成员列表
     * 
     * @param int $count 生成数量
     * @param string $style 头像风格
     * @param array $options 其他选项
     * @return array
     */
    public function generateMembers($count = 13, $style = 'qq', $options = [])
    {
        $members = [];
        $usedNicknames = [];
        $usedAvatars = [];
        
        // 获取可用的头像列表
        $avatars = $this->getAvailableAvatars($style);
        
        for ($i = 0; $i < $count; $i++) {
            // 获取不重复的昵称
            $nickname = $this->getUniqueNickname($usedNicknames);
            $usedNicknames[] = $nickname;
            
            // 获取不重复的头像
            $avatar = $this->getUniqueAvatar($avatars, $usedAvatars);
            $usedAvatars[] = $avatar;
            
            $members[] = [
                'id' => $i + 1,
                'nickname' => $nickname,
                'avatar' => $avatar,
                'join_time' => $this->getRandomJoinTime(),
                'activity_level' => $this->getRandomActivityLevel(),
                'status' => $this->getRandomStatus(),
                'role' => $this->getRandomRole($i),
                'contribution' => rand(10, 1000),
                'last_active' => $this->getRandomLastActive()
            ];
        }
        
        // 如果需要排序
        if (!empty($options['sort'])) {
            $members = $this->sortMembers($members, $options['sort']);
        }
        
        return $members;
    }
    
    /**
     * 生成虚拟评论列表
     * 
     * @param int $count 生成数量
     * @param string $style 风格
     * @return array
     */
    public function generateComments($count = 5, $style = 'qq')
    {
        $comments = [];
        $avatars = $this->getAvailableAvatars($style);
        
        for ($i = 0; $i < $count; $i++) {
            $comments[] = [
                'id' => $i + 1,
                'nickname' => $this->getRandomNickname(),
                'avatar' => $this->getRandomFromArray($avatars),
                'content' => $this->getRandomComment(),
                'likes' => $this->getRandomLikes(),
                'time' => $this->getRandomCommentTime(),
                'replies' => rand(0, 10),
                'is_hot' => rand(0, 100) > 80, // 20%概率成为热评
                'is_pinned' => $i === 0 && rand(0, 100) > 50 // 第一条50%概率置顶
            ];
        }
        
        // 按点赞数排序
        usort($comments, function($a, $b) {
            return $b['likes'] - $a['likes'];
        });
        
        return $comments;
    }
    
    /**
     * 生成格式化的评论文本（兼容ThinkPHP格式）
     * 
     * @param int $count
     * @return string
     */
    public function generateFormattedComments($count = 5)
    {
        $comments = [];
        
        for ($i = 0; $i < $count; $i++) {
            $nickname = $this->getRandomNickname();
            $content = $this->getRandomComment();
            $likes = $this->getRandomLikes();
            
            // 使用ThinkPHP的格式：昵称----评论内容----点赞数
            $comments[] = "{$nickname}----{$content}----{$likes}";
        }
        
        return implode("\n", $comments);
    }
    
    /**
     * 生成虚拟统计数据
     * 
     * @return array
     */
    public function generateStats()
    {
        return [
            'total_members' => rand(100, 500),
            'active_members' => rand(50, 200),
            'today_joined' => rand(5, 20),
            'total_messages' => rand(1000, 10000),
            'today_messages' => rand(50, 500),
            'read_count' => $this->formatNumber(rand(10000, 1000000)),
            'like_count' => rand(100, 5000),
            'share_count' => rand(50, 1000),
            'comment_count' => rand(20, 500)
        ];
    }
    
    /**
     * 获取可用的头像列表
     * 
     * @param string $style
     * @return array
     */
    private function getAvailableAvatars($style)
    {
        $cacheKey = "avatars_{$style}";
        
        // 尝试从缓存获取
        if ($cached = Cache::get($cacheKey)) {
            return $cached;
        }
        
        $avatars = [];
        $path = $this->avatarPaths[$style] ?? $this->avatarPaths['qq'];
        
        // 兼容ThinkPHP的头像命名规则
        if ($style === 'qq') {
            // QQ风格：1.jpg, 2.jpg, ..., 46.jpg
            for ($i = 1; $i <= 46; $i++) {
                $avatars[] = "/{$path}{$i}.jpg";
            }
        } elseif ($style === 'za') {
            // 综合风格：1.jpeg, 2.jpeg, ..., 41.jpeg
            for ($i = 1; $i <= 41; $i++) {
                $avatars[] = "/{$path}{$i}.jpeg";
            }
        } else {
            // 其他风格，尝试读取目录
            $fullPath = public_path($path);
            if (is_dir($fullPath)) {
                $files = glob($fullPath . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
                foreach ($files as $file) {
                    $avatars[] = '/' . $path . basename($file);
                }
            }
        }
        
        // 如果没有找到头像，使用默认头像
        if (empty($avatars)) {
            $avatars = ['/images/default-avatar.png'];
        }
        
        // 缓存1小时
        Cache::put($cacheKey, $avatars, 3600);
        
        return $avatars;
    }
    
    /**
     * 获取唯一的昵称
     * 
     * @param array $used
     * @return string
     */
    private function getUniqueNickname(&$used)
    {
        $maxAttempts = 100;
        $attempt = 0;
        
        do {
            $nickname = $this->getRandomNickname();
            $attempt++;
        } while (in_array($nickname, $used) && $attempt < $maxAttempts);
        
        // 如果找不到唯一的，添加随机数
        if ($attempt >= $maxAttempts) {
            $nickname .= rand(100, 999);
        }
        
        return $nickname;
    }
    
    /**
     * 获取唯一的头像
     * 
     * @param array $available
     * @param array $used
     * @return string
     */
    private function getUniqueAvatar($available, &$used)
    {
        $unused = array_diff($available, $used);
        
        if (empty($unused)) {
            // 如果所有头像都用过了，重新开始
            $used = [];
            $unused = $available;
        }
        
        return $this->getRandomFromArray($unused);
    }
    
    /**
     * 获取随机昵称
     * 
     * @return string
     */
    private function getRandomNickname()
    {
        return $this->getRandomFromArray($this->nicknames);
    }
    
    /**
     * 获取随机评论
     * 
     * @return string
     */
    private function getRandomComment()
    {
        return $this->getRandomFromArray($this->commentTemplates);
    }
    
    /**
     * 获取随机点赞数
     * 
     * @return int
     */
    private function getRandomLikes()
    {
        // 使用指数分布，让大部分评论点赞数较少，少部分很高
        $rand = rand(0, 100);
        
        if ($rand < 60) {
            return rand(10, 100);      // 60%的评论：10-100赞
        } elseif ($rand < 85) {
            return rand(100, 500);     // 25%的评论：100-500赞
        } elseif ($rand < 95) {
            return rand(500, 1000);    // 10%的评论：500-1000赞
        } else {
            return rand(1000, 9999);   // 5%的评论：1000+赞
        }
    }
    
    /**
     * 获取随机加入时间
     * 
     * @return string
     */
    private function getRandomJoinTime()
    {
        $days = rand(1, 365);
        return now()->subDays($days)->format('Y-m-d');
    }
    
    /**
     * 获取随机评论时间
     * 
     * @return string
     */
    private function getRandomCommentTime()
    {
        $minutes = rand(1, 10080); // 过去一周内
        return now()->subMinutes($minutes)->diffForHumans();
    }
    
    /**
     * 获取随机活跃度
     * 
     * @return int
     */
    private function getRandomActivityLevel()
    {
        // 正态分布，大部分在60-90之间
        $mean = 75;
        $stdDev = 15;
        
        $activity = $mean + $stdDev * $this->gaussianRandom();
        
        return max(0, min(100, round($activity)));
    }
    
    /**
     * 获取随机状态
     * 
     * @return string
     */
    private function getRandomStatus()
    {
        $statuses = [
            'online' => 20,    // 20%在线
            'away' => 30,      // 30%离开
            'offline' => 50    // 50%离线
        ];
        
        return $this->weightedRandom($statuses);
    }
    
    /**
     * 获取随机角色
     * 
     * @param int $index
     * @return string
     */
    private function getRandomRole($index)
    {
        if ($index === 0) {
            return '群主';
        } elseif ($index < 3) {
            return '管理员';
        } elseif (rand(0, 100) > 90) {
            return '活跃成员';
        } else {
            return '普通成员';
        }
    }
    
    /**
     * 获取随机最后活跃时间
     * 
     * @return string
     */
    private function getRandomLastActive()
    {
        $minutes = rand(1, 1440); // 过去24小时内
        return now()->subMinutes($minutes)->format('H:i');
    }
    
    /**
     * 格式化数字（如10万+）
     * 
     * @param int $number
     * @return string
     */
    private function formatNumber($number)
    {
        if ($number >= 1000000) {
            return round($number / 10000) . '万+';
        } elseif ($number >= 100000) {
            return round($number / 10000, 1) . '万+';
        } elseif ($number >= 10000) {
            return round($number / 10000, 1) . '万';
        } else {
            return (string)$number;
        }
    }
    
    /**
     * 从数组中随机获取一个元素
     * 
     * @param array $array
     * @return mixed
     */
    private function getRandomFromArray($array)
    {
        return $array[array_rand($array)];
    }
    
    /**
     * 高斯随机数生成器
     * 
     * @return float
     */
    private function gaussianRandom()
    {
        $u1 = mt_rand() / mt_getrandmax();
        $u2 = mt_rand() / mt_getrandmax();
        
        return sqrt(-2 * log($u1)) * cos(2 * pi() * $u2);
    }
    
    /**
     * 加权随机选择
     * 
     * @param array $weights
     * @return string
     */
    private function weightedRandom($weights)
    {
        $rand = rand(1, array_sum($weights));
        
        foreach ($weights as $key => $weight) {
            $rand -= $weight;
            if ($rand <= 0) {
                return $key;
            }
        }
        
        return array_key_first($weights);
    }
    
    /**
     * 排序成员列表
     * 
     * @param array $members
     * @param string $sortBy
     * @return array
     */
    private function sortMembers($members, $sortBy)
    {
        switch ($sortBy) {
            case 'activity':
                usort($members, fn($a, $b) => $b['activity_level'] - $a['activity_level']);
                break;
            case 'contribution':
                usort($members, fn($a, $b) => $b['contribution'] - $a['contribution']);
                break;
            case 'join_time':
                usort($members, fn($a, $b) => strtotime($a['join_time']) - strtotime($b['join_time']));
                break;
        }
        
        return $members;
    }
}