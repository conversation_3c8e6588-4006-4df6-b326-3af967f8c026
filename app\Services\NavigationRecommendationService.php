<?php

namespace App\Services;

use App\Models\NavigationConfig;
use App\Models\UserNavigationPreference;
use App\Models\NavigationUsageStat;
use App\Models\NavigationRecommendation;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * 导航智能推荐服务
 * 
 * 基于多种算法提供个性化导航推荐：
 * 1. 频率推荐：基于用户访问频率
 * 2. 协同过滤：基于相似用户行为
 * 3. 角色推荐：基于角色群体偏好
 * 4. 趋势推荐：基于全局热门趋势
 * 5. 内容推荐：基于内容相似性
 */
class NavigationRecommendationService
{
    const CACHE_TTL = 1800; // 30分钟缓存
    const RECOMMENDATION_LIMIT = 20;

    /**
     * 获取用户推荐导航
     */
    public function getRecommendations(
        int $userId, 
        ?string $domain = null, 
        ?string $type = null, 
        int $limit = 10
    ): array {
        $cacheKey = "nav_recommendations:{$userId}:{$domain}:{$type}:{$limit}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId, $domain, $type, $limit) {
            $user = User::find($userId);
            if (!$user) {
                return [];
            }

            // 获取不同类型的推荐
            $recommendations = match ($type) {
                'frequent' => $this->getFrequencyBasedRecommendations($userId, $domain, $limit),
                'related' => $this->getCollaborativeRecommendations($userId, $domain, $limit),
                'role_based' => $this->getRoleBasedRecommendations($user->role, $domain, $limit),
                'trending' => $this->getTrendingRecommendations($domain, $limit),
                default => $this->getMixedRecommendations($userId, $user->role, $domain, $limit)
            };

            // 记录推荐结果
            $this->recordRecommendations($userId, $recommendations, $type ?? 'mixed');

            return $recommendations;
        });
    }

    /**
     * 基于频率的推荐
     */
    protected function getFrequencyBasedRecommendations(int $userId, ?string $domain, int $limit): array
    {
        // 获取用户访问历史
        $userHistory = UserNavigationPreference::where('user_id', $userId)
            ->where('visit_count', '>', 0)
            ->orderByDesc('visit_count')
            ->orderByDesc('last_visited_at')
            ->pluck('navigation_key')
            ->toArray();

        if (empty($userHistory)) {
            return $this->getTrendingRecommendations($domain, $limit);
        }

        // 基于用户历史找相关导航
        $relatedNavigation = $this->findRelatedNavigation($userHistory, $domain);

        // 排除用户已访问的导航
        $recommendations = $relatedNavigation->reject(function ($nav) use ($userHistory) {
            return in_array($nav['key'], $userHistory);
        });

        return $recommendations->take($limit)->values()->all();
    }

    /**
     * 协同过滤推荐
     */
    protected function getCollaborativeRecommendations(int $userId, ?string $domain, int $limit): array
    {
        // 找到相似用户
        $similarUsers = $this->findSimilarUsers($userId, 20);
        
        if ($similarUsers->isEmpty()) {
            return $this->getRoleBasedRecommendations(
                User::find($userId)?->role ?? 'user', 
                $domain, 
                $limit
            );
        }

        // 获取相似用户的热门导航
        $recommendations = UserNavigationPreference::whereIn('user_id', $similarUsers->pluck('id'))
            ->where('visit_count', '>', 2)
            ->whereHas('navigationConfig', function ($query) use ($domain) {
                $query->active();
                if ($domain) {
                    $query->where('domain', $domain);
                }
            })
            ->with('navigationConfig')
            ->get()
            ->groupBy('navigation_key')
            ->map(function ($group) {
                return [
                    'key' => $group->first()->navigation_key,
                    'navigation' => $group->first()->navigationConfig,
                    'score' => $group->sum('visit_count'),
                    'user_count' => $group->count(),
                    'avg_visits' => $group->avg('visit_count')
                ];
            })
            ->sortByDesc('score')
            ->take($limit)
            ->values();

        // 排除当前用户已访问的导航
        $userVisited = UserNavigationPreference::where('user_id', $userId)
            ->pluck('navigation_key')
            ->toArray();

        return $recommendations->reject(function ($rec) use ($userVisited) {
            return in_array($rec['key'], $userVisited);
        })->take($limit)->values()->all();
    }

    /**
     * 基于角色的推荐
     */
    protected function getRoleBasedRecommendations(string $role, ?string $domain, int $limit): array
    {
        $cacheKey = "role_nav_recommendations:{$role}:{$domain}:{$limit}";

        return Cache::remember($cacheKey, 3600, function () use ($role, $domain, $limit) {
            return NavigationUsageStat::getRoleNavigationStats($role, 30)
                ->when($domain, function ($collection) use ($domain) {
                    return $collection->filter(function ($stat) use ($domain) {
                        return $stat->navigationConfig?->domain === $domain;
                    });
                })
                ->map(function ($stat) {
                    return [
                        'key' => $stat->navigation_key,
                        'navigation' => $stat->navigationConfig,
                        'score' => $stat->visit_count,
                        'unique_users' => $stat->unique_users,
                        'type' => 'role_based'
                    ];
                })
                ->take($limit)
                ->values()
                ->all();
        });
    }

    /**
     * 趋势推荐
     */
    protected function getTrendingRecommendations(?string $domain, int $limit): array
    {
        $cacheKey = "trending_nav_recommendations:{$domain}:{$limit}";

        return Cache::remember($cacheKey, 1800, function () use ($domain, $limit) {
            return NavigationUsageStat::getPopularNavigation(7, $limit * 2)
                ->when($domain, function ($collection) use ($domain) {
                    return $collection->filter(function ($stat) use ($domain) {
                        return $stat->navigationConfig?->domain === $domain;
                    });
                })
                ->map(function ($stat) {
                    return [
                        'key' => $stat->navigation_key,
                        'navigation' => $stat->navigationConfig,
                        'score' => $stat->visit_count,
                        'unique_users' => $stat->unique_users,
                        'type' => 'trending'
                    ];
                })
                ->take($limit)
                ->values()
                ->all();
        });
    }

    /**
     * 混合推荐策略
     */
    protected function getMixedRecommendations(int $userId, string $role, ?string $domain, int $limit): array
    {
        $recommendations = collect();

        // 30% 频率推荐
        $frequencyRecs = collect($this->getFrequencyBasedRecommendations($userId, $domain, ceil($limit * 0.3)));
        $recommendations = $recommendations->merge($frequencyRecs);

        // 25% 协同过滤
        $collaborativeRecs = collect($this->getCollaborativeRecommendations($userId, $domain, ceil($limit * 0.25)));
        $recommendations = $recommendations->merge($collaborativeRecs);

        // 25% 角色推荐
        $roleRecs = collect($this->getRoleBasedRecommendations($role, $domain, ceil($limit * 0.25)));
        $recommendations = $recommendations->merge($roleRecs);

        // 20% 趋势推荐
        $trendingRecs = collect($this->getTrendingRecommendations($domain, ceil($limit * 0.2)));
        $recommendations = $recommendations->merge($trendingRecs);

        // 去重并按分数排序
        return $recommendations
            ->unique('key')
            ->sortByDesc('score')
            ->take($limit)
            ->values()
            ->all();
    }

    /**
     * 寻找相似用户
     */
    protected function findSimilarUsers(int $userId, int $limit = 20): Collection
    {
        // 获取用户的导航偏好
        $userPreferences = UserNavigationPreference::where('user_id', $userId)
            ->where('visit_count', '>', 0)
            ->pluck('visit_count', 'navigation_key');

        if ($userPreferences->isEmpty()) {
            return collect();
        }

        // 使用余弦相似度找相似用户
        $similarUsers = DB::table('user_navigation_preferences as unp1')
            ->join('user_navigation_preferences as unp2', 'unp1.navigation_key', '=', 'unp2.navigation_key')
            ->where('unp1.user_id', $userId)
            ->where('unp2.user_id', '!=', $userId)
            ->where('unp1.visit_count', '>', 0)
            ->where('unp2.visit_count', '>', 0)
            ->selectRaw('
                unp2.user_id,
                COUNT(*) as common_pages,
                SUM(unp1.visit_count * unp2.visit_count) as dot_product,
                SQRT(SUM(unp1.visit_count * unp1.visit_count)) as norm1,
                SQRT(SUM(unp2.visit_count * unp2.visit_count)) as norm2
            ')
            ->groupBy('unp2.user_id')
            ->havingRaw('common_pages >= 3')
            ->get()
            ->map(function ($user) {
                $user->similarity = $user->norm1 * $user->norm2 > 0 
                    ? $user->dot_product / ($user->norm1 * $user->norm2) 
                    : 0;
                return $user;
            })
            ->sortByDesc('similarity')
            ->take($limit);

        return $similarUsers;
    }

    /**
     * 寻找相关导航
     */
    protected function findRelatedNavigation(array $userHistory, ?string $domain): Collection
    {
        // 基于用户历史找同域或相关的导航
        $baseQuery = NavigationConfig::active();
        
        if ($domain) {
            $baseQuery->where('domain', $domain);
        }

        // 获取相关导航（同父级、同标签等）
        $relatedNavigation = $baseQuery->where(function ($query) use ($userHistory) {
            // 同父级的导航
            $parentKeys = NavigationConfig::whereIn('key', $userHistory)
                ->whereNotNull('parent_key')
                ->pluck('parent_key')
                ->unique();

            if ($parentKeys->isNotEmpty()) {
                $query->whereIn('parent_key', $parentKeys);
            }

            // 基于标签的相关性
            $tags = NavigationConfig::whereIn('key', $userHistory)
                ->whereNotNull('meta->tags')
                ->get()
                ->pluck('meta.tags')
                ->flatten()
                ->unique();

            if ($tags->isNotEmpty()) {
                foreach ($tags as $tag) {
                    $query->orWhereJsonContains('meta->tags', $tag);
                }
            }
        })->get();

        return $relatedNavigation->map(function ($nav) {
            return [
                'key' => $nav->key,
                'navigation' => $nav,
                'score' => 1,
                'type' => 'related'
            ];
        });
    }

    /**
     * 记录推荐结果
     */
    protected function recordRecommendations(int $userId, array $recommendations, string $type): void
    {
        if (empty($recommendations)) {
            return;
        }

        $records = collect($recommendations)->map(function ($rec) use ($userId, $type) {
            return [
                'user_id' => $userId,
                'navigation_key' => $rec['key'],
                'recommendation_type' => $type,
                'score' => $rec['score'] ?? 0,
                'reason' => json_encode([
                    'type' => $rec['type'] ?? $type,
                    'score' => $rec['score'] ?? 0
                ]),
                'recommended_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ];
        });

        // 批量插入推荐记录（先删除旧记录避免重复）
        NavigationRecommendation::where('user_id', $userId)
            ->whereIn('navigation_key', $records->pluck('navigation_key'))
            ->delete();

        NavigationRecommendation::insert($records->all());
    }

    /**
     * 获取推荐效果统计
     */
    public function getRecommendationStats(int $userId, int $days = 30): array
    {
        return [
            'total_recommendations' => NavigationRecommendation::where('user_id', $userId)
                ->where('recommended_at', '>=', now()->subDays($days))
                ->count(),
            'clicked_recommendations' => NavigationRecommendation::where('user_id', $userId)
                ->where('recommended_at', '>=', now()->subDays($days))
                ->where('is_clicked', true)
                ->count(),
            'click_rate' => NavigationRecommendation::where('user_id', $userId)
                ->where('recommended_at', '>=', now()->subDays($days))
                ->avg('is_clicked') * 100,
            'type_performance' => NavigationRecommendation::where('user_id', $userId)
                ->where('recommended_at', '>=', now()->subDays($days))
                ->selectRaw('recommendation_type, COUNT(*) as total, AVG(is_clicked) * 100 as click_rate')
                ->groupBy('recommendation_type')
                ->get()
        ];
    }

    /**
     * 清除推荐缓存
     */
    public function clearRecommendationCache(?int $userId = null): void
    {
        if ($userId) {
            $pattern = "nav_recommendations:{$userId}:*";
        } else {
            $pattern = "nav_recommendations:*";
        }
        
        Cache::forget($pattern);
        Cache::forget('role_nav_recommendations:*');
        Cache::forget('trending_nav_recommendations:*');
    }
}