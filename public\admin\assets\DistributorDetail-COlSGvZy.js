import{_ as a,n as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                */import{ag as l,af as s,r as t,L as i,e as d,k as o,l as r,t as u,A as n,E as c,z as m,D as v,y as p,B as f,F as _,Y as b}from"./vue-vendor-Dy164gUc.js";import{e as h}from"./export-BIRLwzxN.js";import{bZ as g,at as y,bw as w,aS as x,U as k,a$ as V,aY as j,bb as C,bh as U,bi as z,bp as $,bq as D,aM as L,b9 as q,b8 as A,bm as R,bB as B,ay as F}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const E={class:"app-container"},I={class:"detail-header"},M={class:"detail-content"},O={slot:"header",class:"clearfix"},S={class:"info-grid"},Y={class:"info-item"},Z={class:"info-avatar"},G={class:"info-details"},H={class:"info-email"},J={class:"info-phone"},K={class:"info-grid-right"},N={class:"info-row"},P={class:"info-value"},Q={class:"info-row"},T={class:"info-value"},W={class:"info-row"},X={class:"info-value"},aa={class:"info-row"},ea={class:"info-value"},la={class:"info-row"},sa={class:"info-row"},ta={class:"info-value"},ia={slot:"header",class:"clearfix"},da={class:"stats-grid"},oa={class:"stat-item"},ra={class:"stat-number"},ua={class:"stat-item"},na={class:"stat-number"},ca={class:"stat-item"},ma={class:"stat-number"},va={class:"stat-item"},pa={class:"stat-number"},fa={class:"stat-item"},_a={class:"stat-number"},ba={class:"stat-item"},ha={class:"stat-number"},ga={class:"stat-item"},ya={class:"stat-number"},wa={class:"stat-item"},xa={class:"stat-number"},ka={slot:"header",class:"clearfix"},Va={class:"children-list"},ja={class:"child-info"},Ca={class:"child-name"},Ua={class:"child-email"},za={class:"child-actions"},$a={slot:"header",class:"clearfix"},Da={class:"commission-amount"},La={slot:"footer",class:"dialog-footer"},qa=a({__name:"DistributorDetail",setup(a){const qa=l(),Aa=s(),Ra=qa.params.id,Ba=t(!0),Fa=t({}),Ea=t({}),Ia=t([]),Ma=t([]),Oa=t(!1),Sa=t({name:"",email:"",phone:"",distribution_group_id:null,status:1}),Ya=i({name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"}]});async function Za(){try{Ba.value=!0;const a=(await e({url:`/distributors/${Ra}`,method:"get"})).data;Fa.value=a.distributor,Ea.value=a.stats,Ia.value=a.recent_commissions,Ba.value=!1}catch(a){Ba.value=!1,g({title:"错误",message:"获取分销员详情失败",type:"error"})}}function Ga(){Aa.go(-1)}function Ha(){Sa.value={name:Fa.value.name,email:Fa.value.email,phone:Fa.value.phone,distribution_group_id:Fa.value.distribution_group_id,status:Fa.value.status},Oa.value=!0}function Ja(){this.$refs.editForm.validate(async a=>{if(a)try{await e({url:`/distributors/${Ra}`,method:"put",data:Sa.value}),Oa.value=!1,await Za(),g({title:"成功",message:"更新成功",type:"success"})}catch(l){g({title:"错误",message:"更新失败",type:"error"})}})}function Ka(){Za()}async function Na(){try{g({title:"提示",message:"正在导出分销商详情...",type:"info"});const a={distributor_id:Ra.value,format:"excel",fields:["id","username","level","total_commission","balance","direct_members","team_members","created_at"]},e=await h(a),l=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=l,s.download=`分销商详情_${(new Date).toLocaleDateString()}.xlsx`,document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(l),g({title:"成功",message:"导出成功",type:"success"})}catch(a){g({title:"错误",message:"导出失败："+a.message,type:"error"})}}function Pa(){Aa.push(`/distribution/children/${Ra}`)}function Qa(){Aa.push(`/finance/commission?user_id=${Ra}`)}function Ta(a){return{1:"初级分销员",2:"中级分销员",3:"高级分销员",4:"金牌分销员"}[a]||"初级分销员"}function Wa(a){return{direct:"直接佣金",indirect:"间接佣金",bonus:"奖金佣金"}[a]||"佣金"}return d(()=>{Za(),async function(){try{const a=await e({url:"/distribution-groups",method:"get",params:{all:!0}});Ma.value=a.data.data}catch(a){console.error("获取分销组选项失败:",a)}}()}),(a,e)=>{const l=y,s=x,t=V,i=j,d=C,h=z,g=U,qa=L,Ra=D,Za=A,Xa=q,ae=B,ee=R,le=$,se=F,te=w;return r(),o("div",E,[u("div",I,[c(l,{onClick:Ga,icon:"el-icon-back",class:"back-btn"},{default:m(()=>e[7]||(e[7]=[v("返回",-1)])),_:1,__:[7]}),e[8]||(e[8]=u("h2",null,"分销员详情",-1))]),n((r(),o("div",M,[c(i,{class:"box-card",shadow:"hover"},{default:m(()=>{return[u("div",O,[e[10]||(e[10]=u("span",{class:"card-title"},"基本信息",-1)),c(l,{style:{float:"right",padding:"3px 0"},type:"text",onClick:Ha},{default:m(()=>e[9]||(e[9]=[v("编辑",-1)])),_:1,__:[9]})]),u("div",S,[u("div",Y,[u("div",Z,[c(s,{size:80,src:Fa.value.avatar},{default:m(()=>[v(k(Fa.value.name.charAt(0)),1)]),_:1},8,["src"])]),u("div",G,[u("h3",null,k(Fa.value.name),1),u("p",H,k(Fa.value.email),1),u("p",J,k(Fa.value.phone),1),c(t,{type:(a=Fa.value.level,{1:"info",2:"success",3:"warning",4:"danger"}[a]||"info"),size:"medium"},{default:m(()=>[v(k(Ta(Fa.value.level)),1)]),_:1},8,["type"])])]),u("div",K,[u("div",N,[e[11]||(e[11]=u("span",{class:"info-label"},"用户ID:",-1)),u("span",P,k(Fa.value.id),1)]),u("div",Q,[e[12]||(e[12]=u("span",{class:"info-label"},"邀请码:",-1)),u("span",T,k(Fa.value.invite_code),1)]),u("div",W,[e[13]||(e[13]=u("span",{class:"info-label"},"上级:",-1)),u("span",X,k(Fa.value.parent?Fa.value.parent.name:"无"),1)]),u("div",aa,[e[14]||(e[14]=u("span",{class:"info-label"},"分销组:",-1)),u("span",ea,k(Fa.value.distribution_group?Fa.value.distribution_group.name:"未分配"),1)]),u("div",la,[e[15]||(e[15]=u("span",{class:"info-label"},"状态:",-1)),c(t,{type:1===Fa.value.status?"success":"danger"},{default:m(()=>[v(k(1===Fa.value.status?"正常":"禁用"),1)]),_:1},8,["type"])]),u("div",sa,[e[16]||(e[16]=u("span",{class:"info-label"},"注册时间:",-1)),u("span",ta,k(Fa.value.created_at),1)])])])];var a}),_:1}),c(i,{class:"box-card",shadow:"hover"},{default:m(()=>[u("div",ia,[e[19]||(e[19]=u("span",{class:"card-title"},"统计数据",-1)),c(d,{style:{float:"right"}},{default:m(()=>[c(l,{size:"mini",onClick:Ka},{default:m(()=>e[17]||(e[17]=[v("刷新",-1)])),_:1,__:[17]}),c(l,{size:"mini",type:"primary",onClick:Na},{default:m(()=>e[18]||(e[18]=[v("导出",-1)])),_:1,__:[18]})]),_:1})]),u("div",da,[u("div",oa,[u("div",ra,"¥"+k(Ea.value.total_commission),1),e[20]||(e[20]=u("div",{class:"stat-label"},"总佣金",-1))]),u("div",ua,[u("div",na,"¥"+k(Ea.value.month_commission),1),e[21]||(e[21]=u("div",{class:"stat-label"},"本月佣金",-1))]),u("div",ca,[u("div",ma,"¥"+k(Ea.value.today_commission),1),e[22]||(e[22]=u("div",{class:"stat-label"},"今日佣金",-1))]),u("div",va,[u("div",pa,k(Ea.value.total_orders),1),e[23]||(e[23]=u("div",{class:"stat-label"},"总订单",-1))]),u("div",fa,[u("div",_a,k(Ea.value.today_orders),1),e[24]||(e[24]=u("div",{class:"stat-label"},"今日订单",-1))]),u("div",ba,[u("div",ha,k(Ea.value.children_count),1),e[25]||(e[25]=u("div",{class:"stat-label"},"下级人数",-1))]),u("div",ga,[u("div",ya,k(Ea.value.active_children),1),e[26]||(e[26]=u("div",{class:"stat-label"},"活跃下级",-1))]),u("div",wa,[u("div",xa,"¥"+k(Fa.value.balance),1),e[27]||(e[27]=u("div",{class:"stat-label"},"账户余额",-1))])])]),_:1}),Fa.value.children&&Fa.value.children.length>0?(r(),p(i,{key:0,class:"box-card",shadow:"hover"},{default:m(()=>[u("div",ka,[e[29]||(e[29]=u("span",{class:"card-title"},"下级分销员",-1)),c(l,{style:{float:"right",padding:"3px 0"},type:"text",onClick:Pa},{default:m(()=>e[28]||(e[28]=[v("查看全部",-1)])),_:1,__:[28]})]),u("div",Va,[(r(!0),o(_,null,b(Fa.value.children,a=>(r(),o("div",{class:"child-item",key:a.id},[c(s,{size:40,src:a.avatar},{default:m(()=>[v(k(a.name.charAt(0)),1)]),_:2},1032,["src"]),u("div",ja,[u("div",Ca,k(a.name),1),u("div",Ua,k(a.email),1)]),u("div",za,[c(l,{size:"mini",type:"text",onClick:e=>function(a){Aa.push(`/distribution/detail/${a.id}`)}(a)},{default:m(()=>e[30]||(e[30]=[v("查看",-1)])),_:2,__:[30]},1032,["onClick"])])]))),128))])]),_:1})):f("",!0),c(i,{class:"box-card",shadow:"hover"},{default:m(()=>[u("div",$a,[e[32]||(e[32]=u("span",{class:"card-title"},"最近佣金记录",-1)),c(l,{style:{float:"right",padding:"3px 0"},type:"text",onClick:Qa},{default:m(()=>e[31]||(e[31]=[v("查看全部",-1)])),_:1,__:[31]})]),c(g,{data:Ia.value,border:"",fit:"","highlight-current-row":""},{default:m(()=>[c(h,{label:"时间",width:"160"},{default:m(({row:a})=>[v(k(a.created_at),1)]),_:1}),c(h,{label:"订单号",width:"140"},{default:m(({row:a})=>[v(k(a.order?a.order.order_no:"-"),1)]),_:1}),c(h,{label:"订单金额",width:"120"},{default:m(({row:a})=>[v(" ¥"+k(a.order?a.order.amount:"-"),1)]),_:1}),c(h,{label:"佣金金额",width:"120"},{default:m(({row:a})=>[u("span",Da,"¥"+k(a.amount),1)]),_:1}),c(h,{label:"佣金类型",width:"120"},{default:m(({row:a})=>{return[c(t,{size:"small",type:(e=a.type,{direct:"success",indirect:"warning",bonus:"danger"}[e]||"info")},{default:m(()=>[v(k(Wa(a.type)),1)]),_:2},1032,["type"])];var e}),_:1}),c(h,{label:"状态",width:"100"},{default:m(({row:a})=>[c(t,{size:"small",type:"paid"===a.status?"success":"warning"},{default:m(()=>[v(k("paid"===a.status?"已发放":"待发放"),1)]),_:2},1032,["type"])]),_:1}),c(h,{label:"备注","min-width":"150"},{default:m(({row:a})=>[v(k(a.remark||"-"),1)]),_:1})]),_:1},8,["data"])]),_:1})])),[[te,Ba.value]]),c(se,{title:"编辑分销员信息",modelValue:Oa.value,"onUpdate:modelValue":e[6]||(e[6]=a=>Oa.value=a),width:"600px"},{default:m(()=>[c(le,{model:Sa.value,rules:Ya,ref_key:"editForm",ref:Sa,"label-width":"100px"},{default:m(()=>[c(Ra,{label:"姓名",prop:"name"},{default:m(()=>[c(qa,{modelValue:Sa.value.name,"onUpdate:modelValue":e[0]||(e[0]=a=>Sa.value.name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),c(Ra,{label:"邮箱",prop:"email"},{default:m(()=>[c(qa,{modelValue:Sa.value.email,"onUpdate:modelValue":e[1]||(e[1]=a=>Sa.value.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),c(Ra,{label:"手机号",prop:"phone"},{default:m(()=>[c(qa,{modelValue:Sa.value.phone,"onUpdate:modelValue":e[2]||(e[2]=a=>Sa.value.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),c(Ra,{label:"分销组"},{default:m(()=>[c(Xa,{modelValue:Sa.value.distribution_group_id,"onUpdate:modelValue":e[3]||(e[3]=a=>Sa.value.distribution_group_id=a),placeholder:"请选择分销组"},{default:m(()=>[(r(!0),o(_,null,b(Ma.value,a=>(r(),p(Za,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),c(Ra,{label:"状态"},{default:m(()=>[c(ee,{modelValue:Sa.value.status,"onUpdate:modelValue":e[4]||(e[4]=a=>Sa.value.status=a)},{default:m(()=>[c(ae,{label:1},{default:m(()=>e[33]||(e[33]=[v("正常",-1)])),_:1,__:[33]}),c(ae,{label:2},{default:m(()=>e[34]||(e[34]=[v("禁用",-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),u("div",La,[c(l,{onClick:e[5]||(e[5]=a=>Oa.value=!1)},{default:m(()=>e[35]||(e[35]=[v("取消",-1)])),_:1,__:[35]}),c(l,{type:"primary",onClick:Ja},{default:m(()=>e[36]||(e[36]=[v("确定",-1)])),_:1,__:[36]})])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4701d843"]]);export{qa as default};
