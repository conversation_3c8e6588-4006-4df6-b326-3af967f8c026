<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\GroupMember;
use App\Models\Order;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * 群组数据导出服务
 */
class GroupExportService
{
    /**
     * 导出群组数据到Excel
     */
    public function exportToExcel(WechatGroup $group): string
    {
        $spreadsheet = new Spreadsheet();
        
        // 创建群组基本信息工作表
        $this->createGroupInfoSheet($spreadsheet, $group);
        
        // 创建成员列表工作表
        $this->createMembersSheet($spreadsheet, $group);
        
        // 创建订单列表工作表
        $this->createOrdersSheet($spreadsheet, $group);
        
        // 创建统计数据工作表
        $this->createStatsSheet($spreadsheet, $group);
        
        // 保存文件
        $filename = $this->generateFilename($group, 'xlsx');
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $writer = new Xlsx($spreadsheet);
        $writer->save($filepath);
        
        return $filepath;
    }

    /**
     * 导出群组数据到CSV
     */
    public function exportToCsv(WechatGroup $group): string
    {
        $data = $this->prepareGroupData($group);
        
        $filename = $this->generateFilename($group, 'csv');
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $file = fopen($filepath, 'w');
        
        // 写入BOM以支持中文
        fwrite($file, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($file, [
            '群组ID',
            '群组名称',
            '分类',
            '价格',
            '成员上限',
            '当前成员数',
            '订单数量',
            '总收入',
            '状态',
            '创建时间',
            '更新时间'
        ]);
        
        // 写入数据
        fputcsv($file, [
            $group->id,
            $group->title,
            $group->category_name,
            $group->price,
            $group->member_limit,
            $group->member_count,
            $group->order_count,
            $group->total_revenue,
            $group->status_name,
            $group->created_at->format('Y-m-d H:i:s'),
            $group->updated_at->format('Y-m-d H:i:s')
        ]);
        
        fclose($file);
        
        return $filepath;
    }

    /**
     * 导出群组数据到PDF
     */
    public function exportToPdf(WechatGroup $group): string
    {
        // 这里可以使用TCPDF或DomPDF生成PDF
        // 暂时返回一个简单的实现
        $filename = $this->generateFilename($group, 'pdf');
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        // 生成HTML内容
        $html = $this->generatePdfHtml($group);
        
        // 这里应该使用PDF库生成PDF文件
        // 暂时保存为HTML文件
        file_put_contents($filepath, $html);
        
        return $filepath;
    }

    /**
     * 批量导出群组数据
     */
    public function batchExport(array $groupIds, string $format = 'excel'): string
    {
        $groups = WechatGroup::whereIn('id', $groupIds)
            ->with(['creator', 'members.user', 'orders.user'])
            ->get();
        
        switch ($format) {
            case 'excel':
                return $this->batchExportToExcel($groups);
            case 'csv':
                return $this->batchExportToCsv($groups);
            default:
                throw new \InvalidArgumentException('不支持的导出格式');
        }
    }

    /**
     * 导出群组分析报告
     */
    public function exportAnalyticsReport(WechatGroup $group, array $dateRange = []): string
    {
        $spreadsheet = new Spreadsheet();
        
        // 创建分析报告工作表
        $this->createAnalyticsSheet($spreadsheet, $group, $dateRange);
        
        // 保存文件
        $filename = $this->generateFilename($group, 'xlsx', 'analytics');
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $writer = new Xlsx($spreadsheet);
        $writer->save($filepath);
        
        return $filepath;
    }

    // 私有方法

    /**
     * 创建群组基本信息工作表
     */
    private function createGroupInfoSheet(Spreadsheet $spreadsheet, WechatGroup $group): void
    {
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('群组信息');
        
        // 设置表头样式
        $headerStyle = [
            'font' => ['bold' => true, 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E3F2FD']],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];
        
        // 基本信息
        $data = [
            ['字段', '值'],
            ['群组ID', $group->id],
            ['群组名称', $group->title],
            ['群组分类', $group->category_name],
            ['群组价格', $group->formatted_price],
            ['成员上限', $group->member_limit],
            ['当前成员数', $group->member_count],
            ['订单数量', $group->order_count],
            ['总收入', '¥' . number_format($group->total_revenue, 2)],
            ['浏览次数', $group->view_count],
            ['转化率', $group->conversion_rate . '%'],
            ['状态', $group->status_name],
            ['创建者', $group->creator->name ?? '未知'],
            ['创建时间', $group->created_at->format('Y-m-d H:i:s')],
            ['更新时间', $group->updated_at->format('Y-m-d H:i:s')]
        ];
        
        // 写入数据
        $sheet->fromArray($data, null, 'A1');
        
        // 应用样式
        $sheet->getStyle('A1:B1')->applyFromArray($headerStyle);
        $sheet->getColumnDimension('A')->setWidth(15);
        $sheet->getColumnDimension('B')->setWidth(30);
        
        // 自动调整行高
        foreach (range(1, count($data)) as $row) {
            $sheet->getRowDimension($row)->setRowHeight(-1);
        }
    }

    /**
     * 创建成员列表工作表
     */
    private function createMembersSheet(Spreadsheet $spreadsheet, WechatGroup $group): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('成员列表');
        
        // 表头
        $headers = [
            '成员ID',
            '用户名',
            '手机号',
            '角色',
            '状态',
            '加入时间',
            '最后活跃时间',
            '消息数量',
            '贡献分数',
            '邀请人'
        ];
        
        $sheet->fromArray([$headers], null, 'A1');
        
        // 获取成员数据
        $members = $group->members()->with(['user', 'inviter'])->get();
        $memberData = [];
        
        foreach ($members as $member) {
            $memberData[] = [
                $member->id,
                $member->user->name ?? '未知',
                $member->user->phone ?? '',
                $member->role_name,
                $member->status_name,
                $member->joined_at->format('Y-m-d H:i:s'),
                $member->last_active_at ? $member->last_active_at->format('Y-m-d H:i:s') : '',
                $member->message_count,
                $member->contribution_score,
                $member->inviter->name ?? ''
            ];
        }
        
        if (!empty($memberData)) {
            $sheet->fromArray($memberData, null, 'A2');
        }
        
        // 设置样式
        $this->applyTableStyle($sheet, count($headers), count($memberData) + 1);
    }

    /**
     * 创建订单列表工作表
     */
    private function createOrdersSheet(Spreadsheet $spreadsheet, WechatGroup $group): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('订单列表');
        
        // 表头
        $headers = [
            '订单ID',
            '订单号',
            '用户名',
            '手机号',
            '金额',
            '支付方式',
            '订单状态',
            '支付时间',
            '创建时间'
        ];
        
        $sheet->fromArray([$headers], null, 'A1');
        
        // 获取订单数据
        $orders = $group->orders()->with('user')->get();
        $orderData = [];
        
        foreach ($orders as $order) {
            $orderData[] = [
                $order->id,
                $order->order_no,
                $order->user->name ?? '未知',
                $order->user->phone ?? '',
                '¥' . number_format($order->amount, 2),
                $order->payment_method ?? '',
                $order->status_name ?? '',
                $order->paid_at ? $order->paid_at->format('Y-m-d H:i:s') : '',
                $order->created_at->format('Y-m-d H:i:s')
            ];
        }
        
        if (!empty($orderData)) {
            $sheet->fromArray($orderData, null, 'A2');
        }
        
        // 设置样式
        $this->applyTableStyle($sheet, count($headers), count($orderData) + 1);
    }

    /**
     * 创建统计数据工作表
     */
    private function createStatsSheet(Spreadsheet $spreadsheet, WechatGroup $group): void
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('统计数据');
        
        // 获取统计数据
        $stats = $group->getStats();
        
        $data = [
            ['统计项目', '数值'],
            ['总订单数', $stats['total_orders']],
            ['已支付订单', $stats['paid_orders']],
            ['总收入', '¥' . number_format($stats['total_income'], 2)],
            ['今日订单', $stats['today_orders']],
            ['今日收入', '¥' . number_format($stats['today_income'], 2)],
            ['昨日订单', $stats['yesterday_orders']],
            ['昨日收入', '¥' . number_format($stats['yesterday_income'], 2)]
        ];
        
        $sheet->fromArray($data, null, 'A1');
        
        // 设置样式
        $this->applyTableStyle($sheet, 2, count($data));
    }

    /**
     * 创建分析报告工作表
     */
    private function createAnalyticsSheet(Spreadsheet $spreadsheet, WechatGroup $group, array $dateRange): void
    {
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('分析报告');
        
        // 这里可以添加更详细的分析数据
        $data = [
            ['分析项目', '数值', '说明'],
            ['群组名称', $group->title, ''],
            ['分析时间', date('Y-m-d H:i:s'), ''],
            ['数据范围', implode(' 至 ', $dateRange), ''],
            ['页面浏览量', $group->view_count, '总浏览次数'],
            ['成员数量', $group->member_count, '当前成员数'],
            ['转化率', $group->conversion_rate . '%', '订单转化率'],
            ['平均订单价值', '¥' . number_format($group->avg_order_value, 2), '']
        ];
        
        $sheet->fromArray($data, null, 'A1');
        
        // 设置样式
        $this->applyTableStyle($sheet, 3, count($data));
    }

    /**
     * 批量导出到Excel
     */
    private function batchExportToExcel($groups): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('群组列表');
        
        // 表头
        $headers = [
            '群组ID',
            '群组名称',
            '分类',
            '价格',
            '成员上限',
            '当前成员数',
            '订单数量',
            '总收入',
            '状态',
            '创建者',
            '创建时间'
        ];
        
        $sheet->fromArray([$headers], null, 'A1');
        
        // 数据
        $data = [];
        foreach ($groups as $group) {
            $data[] = [
                $group->id,
                $group->title,
                $group->category_name,
                $group->formatted_price,
                $group->member_limit,
                $group->member_count,
                $group->order_count,
                '¥' . number_format($group->total_revenue, 2),
                $group->status_name,
                $group->creator->name ?? '未知',
                $group->created_at->format('Y-m-d H:i:s')
            ];
        }
        
        if (!empty($data)) {
            $sheet->fromArray($data, null, 'A2');
        }
        
        // 设置样式
        $this->applyTableStyle($sheet, count($headers), count($data) + 1);
        
        // 保存文件
        $filename = 'groups_batch_' . date('YmdHis') . '.xlsx';
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $writer = new Xlsx($spreadsheet);
        $writer->save($filepath);
        
        return $filepath;
    }

    /**
     * 批量导出到CSV
     */
    private function batchExportToCsv($groups): string
    {
        $filename = 'groups_batch_' . date('YmdHis') . '.csv';
        $filepath = storage_path('app/exports/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $file = fopen($filepath, 'w');
        
        // 写入BOM以支持中文
        fwrite($file, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($file, [
            '群组ID',
            '群组名称',
            '分类',
            '价格',
            '成员上限',
            '当前成员数',
            '订单数量',
            '总收入',
            '状态',
            '创建者',
            '创建时间'
        ]);
        
        // 写入数据
        foreach ($groups as $group) {
            fputcsv($file, [
                $group->id,
                $group->title,
                $group->category_name,
                $group->price,
                $group->member_limit,
                $group->member_count,
                $group->order_count,
                $group->total_revenue,
                $group->status_name,
                $group->creator->name ?? '未知',
                $group->created_at->format('Y-m-d H:i:s')
            ]);
        }
        
        fclose($file);
        
        return $filepath;
    }

    /**
     * 应用表格样式
     */
    private function applyTableStyle($sheet, int $columnCount, int $rowCount): void
    {
        // 表头样式
        $headerStyle = [
            'font' => ['bold' => true, 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E3F2FD']],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
        ];
        
        // 数据样式
        $dataStyle = [
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
        ];
        
        // 应用表头样式
        $headerRange = 'A1:' . chr(64 + $columnCount) . '1';
        $sheet->getStyle($headerRange)->applyFromArray($headerStyle);
        
        // 应用数据样式
        if ($rowCount > 1) {
            $dataRange = 'A2:' . chr(64 + $columnCount) . $rowCount;
            $sheet->getStyle($dataRange)->applyFromArray($dataStyle);
        }
        
        // 自动调整列宽
        foreach (range('A', chr(64 + $columnCount)) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * 生成文件名
     */
    private function generateFilename(WechatGroup $group, string $extension, string $type = 'data'): string
    {
        $groupName = preg_replace('/[^a-zA-Z0-9\u4e00-\u9fa5]/', '_', $group->title);
        $groupName = mb_substr($groupName, 0, 20); // 限制长度
        
        return "group_{$type}_{$group->id}_{$groupName}_" . date('YmdHis') . ".{$extension}";
    }

    /**
     * 准备群组数据
     */
    private function prepareGroupData(WechatGroup $group): array
    {
        return [
            'basic_info' => [
                'id' => $group->id,
                'title' => $group->title,
                'category' => $group->category_name,
                'price' => $group->price,
                'member_limit' => $group->member_limit,
                'member_count' => $group->member_count,
                'order_count' => $group->order_count,
                'total_revenue' => $group->total_revenue,
                'status' => $group->status_name,
                'created_at' => $group->created_at->format('Y-m-d H:i:s')
            ],
            'members' => $group->members()->with('user')->get()->toArray(),
            'orders' => $group->orders()->with('user')->get()->toArray(),
            'stats' => $group->getStats()
        ];
    }

    /**
     * 生成PDF HTML内容
     */
    private function generatePdfHtml(WechatGroup $group): string
    {
        $stats = $group->getStats();
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>群组数据报告 - {$group->title}</title>
            <style>
                body { font-family: 'SimSun', serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .info-table th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>群组数据报告</h1>
                <p>生成时间：" . date('Y-m-d H:i:s') . "</p>
            </div>
            
            <h2>基本信息</h2>
            <table class='info-table'>
                <tr><th>群组名称</th><td>{$group->title}</td></tr>
                <tr><th>群组分类</th><td>{$group->category_name}</td></tr>
                <tr><th>群组价格</th><td>{$group->formatted_price}</td></tr>
                <tr><th>成员上限</th><td>{$group->member_limit}</td></tr>
                <tr><th>当前成员数</th><td>{$group->member_count}</td></tr>
                <tr><th>订单数量</th><td>{$group->order_count}</td></tr>
                <tr><th>总收入</th><td>¥" . number_format($group->total_revenue, 2) . "</td></tr>
                <tr><th>状态</th><td>{$group->status_name}</td></tr>
                <tr><th>创建时间</th><td>{$group->created_at->format('Y-m-d H:i:s')}</td></tr>
            </table>
            
            <h2>统计数据</h2>
            <table class='info-table'>
                <tr><th>总订单数</th><td>{$stats['total_orders']}</td></tr>
                <tr><th>已支付订单</th><td>{$stats['paid_orders']}</td></tr>
                <tr><th>总收入</th><td>¥" . number_format($stats['total_income'], 2) . "</td></tr>
                <tr><th>今日订单</th><td>{$stats['today_orders']}</td></tr>
                <tr><th>今日收入</th><td>¥" . number_format($stats['today_income'], 2) . "</td></tr>
            </table>
        </body>
        </html>";
    }
}