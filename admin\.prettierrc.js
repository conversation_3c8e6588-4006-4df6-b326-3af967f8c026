module.exports = {
  // ==========================================
  // LinkHub Pro 开发规范 - Prettier配置
  // ==========================================
  
  // 基础格式化选项
  semi: false,                    // 不使用分号
  singleQuote: true,             // 使用单引号
  quoteProps: 'as-needed',       // 仅在需要时给对象属性加引号
  trailingComma: 'none',         // 不使用尾随逗号
  
  // 缩进和空格
  tabWidth: 2,                   // 使用2个空格缩进
  useTabs: false,                // 使用空格而不是tab
  
  // 行宽和换行
  printWidth: 100,               // 行宽限制100字符
  endOfLine: 'lf',               // 使用LF换行符
  
  // 括号和空格
  bracketSpacing: true,          // 对象字面量的括号间加空格 { foo: bar }
  bracketSameLine: false,        // 多行HTML元素的>放在下一行
  
  // 箭头函数
  arrowParens: 'avoid',          // 单参数箭头函数不加括号 x => x
  
  // Vue文件特殊配置
  vueIndentScriptAndStyle: false, // Vue文件中script和style标签不缩进
  
  // HTML相关
  htmlWhitespaceSensitivity: 'css', // HTML空白敏感性
  
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // 文件覆盖配置
  overrides: [
    {
      files: '*.vue',
      options: {
        // Vue文件特殊配置
        parser: 'vue',
        printWidth: 120,           // Vue文件行宽可以稍长
        htmlWhitespaceSensitivity: 'ignore'
      }
    },
    {
      files: '*.json',
      options: {
        // JSON文件配置
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        // Markdown文件配置
        printWidth: 80,
        proseWrap: 'preserve',     // 保持原有换行
        tabWidth: 2
      }
    },
    {
      files: '*.scss',
      options: {
        // SCSS文件配置
        printWidth: 120,
        singleQuote: false         // SCSS使用双引号
      }
    },
    {
      files: '*.css',
      options: {
        // CSS文件配置
        printWidth: 120,
        singleQuote: false         // CSS使用双引号
      }
    }
  ]
}
