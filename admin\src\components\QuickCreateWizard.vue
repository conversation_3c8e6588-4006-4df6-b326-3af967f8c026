<template>
  <div class="quick-create-wizard">
    <el-dialog
      v-model="visible"
      title="快速创建群组落地页"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="wizard-container">
        <!-- 步骤指示器 -->
        <div class="wizard-steps">
          <el-steps :active="currentStep" align-center>
            <el-step 
              v-for="(step, index) in steps" 
              :key="index"
              :title="step.title" 
              :description="step.description"
              :icon="step.icon"
            />
          </el-steps>
        </div>

        <!-- 步骤内容 -->
        <div class="wizard-content">
          <!-- 第一步：基本信息 -->
          <div v-if="currentStep === 0" class="step-content">
            <h3>群组基本信息</h3>
            <el-form :model="formData" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="群组名称" required>
                    <el-input 
                      v-model="formData.title" 
                      placeholder="输入群组名称"
                      maxlength="50"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="群组类型" required>
                    <el-select v-model="formData.category" placeholder="选择群组类型">
                      <el-option 
                        v-for="category in categories" 
                        :key="category.value"
                        :label="category.label" 
                        :value="category.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="群组描述" required>
                <el-input 
                  v-model="formData.description" 
                  type="textarea" 
                  :rows="3"
                  placeholder="简要描述群组的价值和特色"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="入群价格" required>
                    <el-input-number 
                      v-model="formData.price" 
                      :min="0" 
                      :max="9999"
                      :precision="2"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="群组人数上限">
                    <el-input-number 
                      v-model="formData.memberLimit" 
                      :min="10" 
                      :max="10000"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="当前成员数">
                    <el-input-number 
                      v-model="formData.currentMembers" 
                      :min="0" 
                      :max="formData.memberLimit"
                      controls-position="right"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 第二步：选择模板 -->
          <div v-if="currentStep === 1" class="step-content">
            <h3>选择落地页模板</h3>
            <div class="template-selection">
              <div class="template-grid">
                <div 
                  v-for="template in filteredTemplates" 
                  :key="template.id"
                  class="template-option"
                  :class="{ 'selected': formData.templateId === template.id }"
                  @click="selectTemplate(template)"
                >
                  <div class="template-preview">
                    <img :src="template.preview" :alt="template.name" />
                  </div>
                  <div class="template-info">
                    <h4>{{ template.name }}</h4>
                    <p>{{ template.description }}</p>
                    <div class="template-tags">
                      <el-tag 
                        v-for="tag in template.tags" 
                        :key="tag" 
                        size="small"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                  <div v-if="formData.templateId === template.id" class="selected-indicator">
                    <el-icon><Check /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三步：内容配置 -->
          <div v-if="currentStep === 2" class="step-content">
            <h3>内容配置</h3>
            <el-form :model="formData" label-width="120px">
              <el-tabs v-model="activeContentTab">
                <el-tab-pane label="群组介绍" name="intro">
                  <el-form-item label="介绍标题">
                    <el-input v-model="formData.content.introTitle" placeholder="群组介绍标题" />
                  </el-form-item>
                  <el-form-item label="介绍内容">
                    <el-input 
                      v-model="formData.content.introContent" 
                      type="textarea" 
                      :rows="4"
                      placeholder="详细介绍群组的价值和特色"
                    />
                  </el-form-item>
                </el-tab-pane>
                
                <el-tab-pane label="常见问题" name="faq">
                  <div class="faq-list">
                    <div 
                      v-for="(faq, index) in formData.content.faqs" 
                      :key="index"
                      class="faq-item"
                    >
                      <el-form-item :label="`问题 ${index + 1}`">
                        <el-input v-model="faq.question" placeholder="输入问题" />
                      </el-form-item>
                      <el-form-item label="答案">
                        <el-input 
                          v-model="faq.answer" 
                          type="textarea" 
                          :rows="2"
                          placeholder="输入答案"
                        />
                      </el-form-item>
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="removeFaq(index)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                  <el-button @click="addFaq" type="primary" size="small">
                    <el-icon><Plus /></el-icon>
                    添加问题
                  </el-button>
                </el-tab-pane>
                
                <el-tab-pane label="用户评价" name="reviews">
                  <div class="reviews-list">
                    <div 
                      v-for="(review, index) in formData.content.reviews" 
                      :key="index"
                      class="review-item"
                    >
                      <el-form-item :label="`评价 ${index + 1}`">
                        <el-input 
                          v-model="review.content" 
                          type="textarea" 
                          :rows="2"
                          placeholder="输入用户评价"
                        />
                      </el-form-item>
                      <div class="review-meta">
                        <el-form-item label="用户名">
                          <el-input v-model="review.author" placeholder="用户名" />
                        </el-form-item>
                        <el-form-item label="评分">
                          <el-rate v-model="review.rating" />
                        </el-form-item>
                        <el-button 
                          type="danger" 
                          size="small" 
                          @click="removeReview(index)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                  <el-button @click="addReview" type="primary" size="small">
                    <el-icon><Plus /></el-icon>
                    添加评价
                  </el-button>
                </el-tab-pane>
              </el-tabs>
            </el-form>
          </div>

          <!-- 第四步：转化优化 -->
          <div v-if="currentStep === 3" class="step-content">
            <h3>转化率优化</h3>
            <ConversionOptimizer v-model="formData.optimization" />
          </div>

          <!-- 第五步：预览确认 -->
          <div v-if="currentStep === 4" class="step-content">
            <h3>预览确认</h3>
            <div class="preview-container">
              <div class="preview-summary">
                <h4>配置摘要</h4>
                <div class="summary-item">
                  <strong>群组名称：</strong>{{ formData.title }}
                </div>
                <div class="summary-item">
                  <strong>群组类型：</strong>{{ getCategoryLabel(formData.category) }}
                </div>
                <div class="summary-item">
                  <strong>入群价格：</strong>¥{{ formData.price }}
                </div>
                <div class="summary-item">
                  <strong>选择模板：</strong>{{ getSelectedTemplate()?.name }}
                </div>
                <div class="summary-item">
                  <strong>FAQ数量：</strong>{{ formData.content.faqs.length }} 个
                </div>
                <div class="summary-item">
                  <strong>用户评价：</strong>{{ formData.content.reviews.length }} 条
                </div>
              </div>
              
              <div class="preview-actions">
                <el-button @click="previewLandingPage" type="primary">
                  <el-icon><View /></el-icon>
                  预览落地页
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="wizard-actions">
          <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
          <el-button v-if="currentStep < steps.length - 1" type="primary" @click="nextStep">
            下一步
          </el-button>
          <el-button v-if="currentStep === steps.length - 1" type="success" @click="createGroup">
            创建群组
          </el-button>
          <el-button @click="closeWizard">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Plus, View } from '@element-plus/icons-vue'
import ConversionOptimizer from './ConversionOptimizer.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'group-created'])

// 响应式数据
const visible = ref(props.modelValue)
const currentStep = ref(0)
const activeContentTab = ref('intro')

// 步骤配置
const steps = ref([
  { title: '基本信息', description: '填写群组基本信息', icon: 'Edit' },
  { title: '选择模板', description: '选择合适的落地页模板', icon: 'Grid' },
  { title: '内容配置', description: '配置群组介绍和FAQ', icon: 'Document' },
  { title: '转化优化', description: '设置转化率优化元素', icon: 'TrendCharts' },
  { title: '预览确认', description: '预览并确认创建', icon: 'View' }
])

// 分类选项
const categories = ref([
  { label: '教育培训', value: 'education' },
  { label: '商务交流', value: 'business' },
  { label: '娱乐社交', value: 'entertainment' },
  { label: '技术分享', value: 'technology' },
  { label: '交友相亲', value: 'dating' },
  { label: '创业投资', value: 'startup' },
  { label: '母婴育儿', value: 'parenting' }
])

// 表单数据
const formData = reactive({
  title: '',
  category: '',
  description: '',
  price: 0,
  memberLimit: 200,
  currentMembers: 0,
  templateId: '',
  content: {
    introTitle: '',
    introContent: '',
    faqs: [
      { question: '', answer: '' }
    ],
    reviews: [
      { content: '', author: '', rating: 5 }
    ]
  },
  optimization: {}
})

// 模板数据（简化版）
const templates = ref([
  {
    id: 'edu-001',
    name: '专业教育模板',
    description: '适合在线教育、培训机构使用',
    category: 'education',
    preview: '/templates/previews/education-1.jpg',
    tags: ['教育', '专业', '简洁']
  },
  {
    id: 'bus-001',
    name: '商务精英模板',
    description: '高端商务风格，适合企业交流群',
    category: 'business',
    preview: '/templates/previews/business-1.jpg',
    tags: ['商务', '高端', '专业']
  },
  {
    id: 'ent-001',
    name: '活力娱乐模板',
    description: '年轻活力的设计风格',
    category: 'entertainment',
    preview: '/templates/previews/entertainment-1.jpg',
    tags: ['娱乐', '活力', '年轻']
  }
])

// 计算属性
const filteredTemplates = computed(() => {
  if (!formData.category) return templates.value
  return templates.value.filter(template => 
    template.category === formData.category || template.category === 'general'
  )
})

// 方法
const nextStep = () => {
  if (validateCurrentStep()) {
    currentStep.value++
  }
}

const prevStep = () => {
  currentStep.value--
}

const validateCurrentStep = () => {
  switch (currentStep.value) {
    case 0:
      if (!formData.title || !formData.category || !formData.description) {
        ElMessage.warning('请填写完整的基本信息')
        return false
      }
      break
    case 1:
      if (!formData.templateId) {
        ElMessage.warning('请选择一个模板')
        return false
      }
      break
  }
  return true
}

const selectTemplate = (template) => {
  formData.templateId = template.id
}

const addFaq = () => {
  formData.content.faqs.push({ question: '', answer: '' })
}

const removeFaq = (index) => {
  formData.content.faqs.splice(index, 1)
}

const addReview = () => {
  formData.content.reviews.push({ content: '', author: '', rating: 5 })
}

const removeReview = (index) => {
  formData.content.reviews.splice(index, 1)
}

const getCategoryLabel = (value) => {
  const category = categories.value.find(cat => cat.value === value)
  return category ? category.label : value
}

const getSelectedTemplate = () => {
  return templates.value.find(template => template.id === formData.templateId)
}

const previewLandingPage = () => {
  // 打开预览窗口
  const previewUrl = `/preview/landing-page?data=${encodeURIComponent(JSON.stringify(formData))}`
  window.open(previewUrl, '_blank', 'width=400,height=800')
}

const createGroup = () => {
  ElMessage.success('群组创建成功！')
  emit('group-created', formData)
  closeWizard()
}

const closeWizard = () => {
  visible.value = false
  currentStep.value = 0
  // 重置表单数据
  Object.assign(formData, {
    title: '',
    category: '',
    description: '',
    price: 0,
    memberLimit: 200,
    currentMembers: 0,
    templateId: '',
    content: {
      introTitle: '',
      introContent: '',
      faqs: [{ question: '', answer: '' }],
      reviews: [{ content: '', author: '', rating: 5 }]
    },
    optimization: {}
  })
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})
</script>

<style lang="scss" scoped>
.quick-create-wizard {
  .wizard-container {
    min-height: 600px;
    
    .wizard-steps {
      margin-bottom: 40px;
    }
    
    .wizard-content {
      min-height: 400px;
      
      .step-content {
        h3 {
          font-size: 20px;
          color: #333;
          margin-bottom: 20px;
          text-align: center;
        }
        
        .template-selection {
          .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            
            .template-option {
              border: 2px solid #e9ecef;
              border-radius: 8px;
              overflow: hidden;
              cursor: pointer;
              transition: all 0.3s ease;
              position: relative;
              
              &:hover {
                border-color: #409eff;
                transform: translateY(-2px);
              }
              
              &.selected {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
              }
              
              .template-preview {
                height: 150px;
                
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
              
              .template-info {
                padding: 15px;
                
                h4 {
                  font-size: 16px;
                  color: #333;
                  margin-bottom: 5px;
                }
                
                p {
                  font-size: 14px;
                  color: #666;
                  margin-bottom: 10px;
                }
                
                .template-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 5px;
                }
              }
              
              .selected-indicator {
                position: absolute;
                top: 10px;
                right: 10px;
                width: 24px;
                height: 24px;
                background: #409eff;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
        }
        
        .faq-list,
        .reviews-list {
          .faq-item,
          .review-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
            
            .review-meta {
              display: flex;
              align-items: end;
              gap: 15px;
            }
          }
        }
        
        .preview-container {
          display: flex;
          gap: 30px;
          
          .preview-summary {
            flex: 1;
            
            h4 {
              font-size: 18px;
              color: #333;
              margin-bottom: 15px;
            }
            
            .summary-item {
              margin-bottom: 10px;
              font-size: 14px;
              
              strong {
                color: #333;
              }
            }
          }
          
          .preview-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
          }
        }
      }
    }
    
    .wizard-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .quick-create-wizard {
    .wizard-container {
      .wizard-content {
        .step-content {
          .template-selection {
            .template-grid {
              grid-template-columns: 1fr;
            }
          }
          
          .preview-container {
            flex-direction: column;
          }
        }
      }
    }
  }
}
</style>
