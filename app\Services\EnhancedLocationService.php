<?php

namespace App\Services;

use App\Services\IPLocationService;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 增强版定位服务
 * 整合Laravel高级功能 + ThinkPHP xxx占位符机制
 * 
 * <AUTHOR> Enhancement
 * @date 2024-12-19
 */
class EnhancedLocationService extends IPLocationService
{
    /**
     * 占位符配置映射
     */
    private $placeholders = [
        'xxx' => 'city',              // ThinkPHP兼容格式
        '{city}' => 'city',           // 新格式 - 城市
        '{省份}' => 'province',       // 省份
        '{地区}' => 'district',       // 地区
        '[位置]' => 'full_address',   // 完整地址
        '{{城市}}' => 'city',         // 双括号格式
        '[城市名]' => 'city_clean'    // 清洁的城市名（无"市"后缀）
    ];
    
    /**
     * 智能替换内容中的地理位置占位符
     * 
     * @param string $content 需要替换的内容
     * @param string|null $ip IP地址
     * @return string 替换后的内容
     */
    public function smartReplace($content, $ip = null)
    {
        $locationData = $this->getFullLocationData($ip);
        
        // 遍历所有占位符进行替换
        foreach ($this->placeholders as $placeholder => $field) {
            if (isset($locationData[$field])) {
                $content = str_replace($placeholder, $locationData[$field], $content);
            }
        }
        
        // 处理特殊的xxx占位符（完全兼容ThinkPHP）
        if (strpos($content, 'xxx') !== false) {
            $city = $locationData['city_clean'] ?? $locationData['city'] ?? '';
            $content = str_ireplace('xxx', $city, $content);
        }
        
        return $content;
    }
    
    /**
     * 获取完整的地理位置数据
     * 
     * @param string|null $ip IP地址
     * @return array 位置数据
     */
    public function getFullLocationData($ip = null)
    {
        $ip = $ip ?: request()->ip();
        
        // 多级缓存策略
        $cacheKey = "location_full_{$ip}";
        
        // 1. Session缓存（最快）
        if ($data = session($cacheKey)) {
            Log::debug('Location from session cache', ['ip' => $ip]);
            return $data;
        }
        
        // 2. Cookie缓存（兼容ThinkPHP的curcity2）
        if ($city = Cookie::get('curcity2')) {
            Log::debug('Location from cookie (ThinkPHP compatible)', ['city' => $city]);
            return $this->buildLocationData($city);
        }
        
        // 3. Redis/内存缓存
        if ($data = Cache::get($cacheKey)) {
            Log::debug('Location from Redis cache', ['ip' => $ip]);
            return $data;
        }
        
        // 4. 从多个API获取
        $data = $this->fetchFromMultipleAPIs($ip);
        
        // 5. 保存到所有缓存层
        $this->saveToAllCaches($cacheKey, $data);
        
        return $data;
    }
    
    /**
     * 从多个API获取位置（智能降级）
     * 
     * @param string $ip
     * @return array
     */
    private function fetchFromMultipleAPIs($ip)
    {
        // 1. 百度地图API（主要，使用ThinkPHP同样的API密钥）
        if ($data = $this->getCityDataFromBaiduEnhanced($ip)) {
            return $this->formatLocationData($data);
        }
        
        // 2. 高德地图API（备用）
        if ($data = $this->getCityDataFromAmap($ip)) {
            return $this->formatLocationData($data);
        }
        
        // 3. 腾讯位置API（备用）
        if ($data = $this->getCityDataFromTencent($ip)) {
            return $this->formatLocationData($data);
        }
        
        // 4. 百度云API（备用）
        if ($data = $this->getCityDataFromBaiduCloud($ip)) {
            return $this->formatLocationData($data);
        }
        
        // 5. 淘宝API（最后降级）
        if ($data = $this->getCityDataFromTaobao($ip)) {
            return $this->formatLocationData($data);
        }
        
        // 6. 本地IP库（最终降级）
        return $this->getDefaultLocationData();
    }
    
    /**
     * 增强版百度地图API（使用ThinkPHP相同的API key）
     * 
     * @param string $ip
     * @return array|null
     */
    private function getCityDataFromBaiduEnhanced($ip)
    {
        try {
            // 使用与ThinkPHP相同的API密钥确保兼容性
            $apiKey = config('services.ip_location.baidu.api_key', 'VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om');
            $url = "https://api.map.baidu.com/location/ip?ip={$ip}&coor=bd09ll&ak={$apiKey}";
            
            $response = Http::timeout(3)->get($url);
            
            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['content']['address_detail'])) {
                    $detail = $data['content']['address_detail'];
                    return [
                        'city' => $detail['city'] ?? '',
                        'province' => $detail['province'] ?? '',
                        'district' => $detail['district'] ?? '',
                        'street' => $detail['street'] ?? '',
                        'raw' => $data
                    ];
                }
            }
        } catch (\Exception $e) {
            Log::warning('Baidu API failed', ['error' => $e->getMessage()]);
        }
        
        return null;
    }
    
    /**
     * 新增：高德地图API
     * 
     * @param string $ip
     * @return array|null
     */
    private function getCityDataFromAmap($ip)
    {
        try {
            $key = config('services.ip_location.amap.key', 'fdc6f731641c83601f4ffeb3dca10cd6');
            $url = "https://restapi.amap.com/v3/ip?ip={$ip}&key={$key}";
            
            $response = Http::timeout(3)->get($url);
            
            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && !empty($data['city'])) {
                    return [
                        'city' => $data['city'],
                        'province' => $data['province'] ?? '',
                        'adcode' => $data['adcode'] ?? '',
                        'raw' => $data
                    ];
                }
            }
        } catch (\Exception $e) {
            Log::warning('Amap API failed', ['error' => $e->getMessage()]);
        }
        
        return null;
    }
    
    /**
     * 新增：腾讯位置服务API
     * 
     * @param string $ip
     * @return array|null
     */
    private function getCityDataFromTencent($ip)
    {
        try {
            $key = config('services.ip_location.tencent.key');
            if (!$key) {
                return null;
            }
            
            $url = "https://apis.map.qq.com/ws/location/v1/ip";
            
            $response = Http::timeout(3)->get($url, [
                'ip' => $ip,
                'key' => $key
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == 0 && isset($data['result']['ad_info'])) {
                    $adInfo = $data['result']['ad_info'];
                    return [
                        'city' => $adInfo['city'] ?? '',
                        'province' => $adInfo['province'] ?? '',
                        'district' => $adInfo['district'] ?? '',
                        'raw' => $data
                    ];
                }
            }
        } catch (\Exception $e) {
            Log::warning('Tencent API failed', ['error' => $e->getMessage()]);
        }
        
        return null;
    }
    
    /**
     * 格式化位置数据
     * 
     * @param array $data
     * @return array
     */
    private function formatLocationData($data)
    {
        $city = $data['city'] ?? '';
        $province = $data['province'] ?? '';
        
        // 清理城市名称（移除"市"后缀，兼容ThinkPHP）
        $cityClean = str_replace(['市', '自治区', '特别行政区'], '', $city);
        
        // 如果城市为空，使用省份
        if (empty($city) && !empty($province)) {
            $city = str_replace('省', '', $province);
            $cityClean = $city;
        }
        
        return [
            'city' => $city,
            'city_clean' => $cityClean,  // 干净的城市名（无后缀）
            'province' => $province,
            'district' => $data['district'] ?? '',
            'street' => $data['street'] ?? '',
            'full_address' => $this->buildFullAddress($data),
            'source' => $data['source'] ?? 'api',
            'raw' => $data['raw'] ?? null
        ];
    }
    
    /**
     * 构建完整地址
     * 
     * @param array $data
     * @return string
     */
    private function buildFullAddress($data)
    {
        $parts = array_filter([
            $data['province'] ?? '',
            $data['city'] ?? '',
            $data['district'] ?? '',
            $data['street'] ?? ''
        ]);
        
        return implode('', $parts);
    }
    
    /**
     * 根据城市名构建位置数据
     * 
     * @param string $city
     * @return array
     */
    private function buildLocationData($city)
    {
        $cityClean = str_replace(['市', '自治区'], '', $city);
        
        return [
            'city' => $city,
            'city_clean' => $cityClean,
            'province' => '',
            'district' => '',
            'street' => '',
            'full_address' => $city,
            'source' => 'cookie'
        ];
    }
    
    /**
     * 获取默认位置数据
     * 
     * @return array
     */
    private function getDefaultLocationData()
    {
        $defaultCity = config('services.ip_location.fallback.default_city', '全国');
        
        return [
            'city' => $defaultCity,
            'city_clean' => $defaultCity,
            'province' => '',
            'district' => '',
            'street' => '',
            'full_address' => $defaultCity,
            'source' => 'default'
        ];
    }
    
    /**
     * 保存到所有缓存层
     * 
     * @param string $cacheKey
     * @param array $data
     */
    private function saveToAllCaches($cacheKey, $data)
    {
        // 1. Session缓存
        session([$cacheKey => $data]);
        
        // 2. Cookie缓存（兼容ThinkPHP）
        $city = $data['city_clean'] ?? $data['city'] ?? '';
        if ($city) {
            Cookie::queue('curcity2', $city, 600); // 10分钟，与ThinkPHP一致
        }
        
        // 3. Redis缓存
        Cache::put($cacheKey, $data, now()->addHours(1));
        
        // 4. 数据库缓存（如果启用）
        if (config('services.ip_location.database_cache', false)) {
            // 注释掉，因为IpCityCache模型可能不存在
            // \App\Models\IpCityCache::updateOrCreate(
            //     ['ip' => request()->ip()],
            //     [
            //         'city' => $data['city'] ?? '',
            //         'province' => $data['province'] ?? '',
            //         'country' => '中国',
            //         'source' => $data['source'] ?? 'unknown',
            //         'raw_data' => json_encode($data['raw'] ?? [])
            //     ]
            // );
        }
    }
    
    /**
     * 判断是否为私有IP
     * 
     * @param string $ip
     * @return bool
     */
    private function isPrivateIP($ip)
    {
        $privateRanges = [
            '10.0.0.0|**************',
            '**********|**************',
            '***********|***************',
            '*********|***************'
        ];
        
        $longIp = ip2long($ip);
        if ($longIp === false) {
            return false;
        }
        
        foreach ($privateRanges as $range) {
            list($start, $end) = explode('|', $range);
            if ($longIp >= ip2long($start) && $longIp <= ip2long($end)) {
                return true;
            }
        }
        
        return false;
    }
}