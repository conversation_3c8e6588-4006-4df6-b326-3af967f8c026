/**
 * 分销员服务层单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { DistributorService } from '@/services/DistributorService'
import { ElMessage, ElNotification } from 'element-plus'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElNotification: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API 客户端
const mockApiClient = {
  getCustomers: vi.fn(),
  getCustomerDetail: vi.fn(),
  createCustomer: vi.fn(),
  updateCustomer: vi.fn(),
  deleteCustomer: vi.fn()
}

describe('DistributorService', () => {
  let service

  beforeEach(() => {
    service = new DistributorService()
    service.apiClient = mockApiClient
    service.cache.clear()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('缓存管理', () => {
    it('应该正确生成缓存键', () => {
      const key1 = service.getCacheKey('getCustomers', [{ page: 1 }])
      const key2 = service.getCacheKey('getCustomers', [{ page: 2 }])
      
      expect(key1).toBe('getCustomers_[{"page":1}]')
      expect(key2).toBe('getCustomers_[{"page":2}]')
      expect(key1).not.toBe(key2)
    })

    it('应该正确设置和获取缓存', () => {
      const testData = { name: '张三', age: 30 }
      const cacheKey = 'test_key'

      service.setCache(cacheKey, testData)
      const cached = service.getCache(cacheKey)

      expect(cached).toEqual(testData)
    })

    it('应该处理过期缓存', () => {
      const testData = { name: '张三' }
      const cacheKey = 'test_key'

      // 设置缓存
      service.setCache(cacheKey, testData)
      
      // 模拟缓存过期
      const cachedItem = service.cache.get(cacheKey)
      cachedItem.timestamp = Date.now() - (6 * 60 * 1000) // 6分钟前
      
      const result = service.getCache(cacheKey)
      expect(result).toBeNull()
      expect(service.cache.has(cacheKey)).toBe(false)
    })

    it('应该正确清除缓存', () => {
      service.setCache('customers_1', { data: 'test1' })
      service.setCache('customers_2', { data: 'test2' })
      service.setCache('orders_1', { data: 'test3' })

      service.clearCache('customers')

      expect(service.getCache('customers_1')).toBeNull()
      expect(service.getCache('customers_2')).toBeNull()
      expect(service.getCache('orders_1')).toEqual({ data: 'test3' })
    })
  })

  describe('客户管理', () => {
    describe('getCustomers', () => {
      const mockCustomersResponse = {
        data: {
          data: [
            { id: 1, name: '张三', phone: '13800138001', level: 'A' },
            { id: 2, name: '李四', phone: '13800138002', level: 'B' }
          ],
          total: 2,
          total_pages: 1
        }
      }

      it('应该成功获取客户列表', async () => {
        mockApiClient.getCustomers.mockResolvedValue(mockCustomersResponse)

        const result = await service.getCustomers({ page: 1, limit: 20 })

        expect(mockApiClient.getCustomers).toHaveBeenCalledWith({ page: 1, limit: 20 })
        expect(result.data).toHaveLength(2)
        expect(result.data[0].name).toBe('张三')
        expect(result.data[0].level_text).toBe('A级客户')
      })

      it('应该使用缓存', async () => {
        mockApiClient.getCustomers.mockResolvedValue(mockCustomersResponse)

        // 第一次调用
        await service.getCustomers({ page: 1 })
        expect(mockApiClient.getCustomers).toHaveBeenCalledTimes(1)

        // 第二次调用应该使用缓存
        await service.getCustomers({ page: 1 })
        expect(mockApiClient.getCustomers).toHaveBeenCalledTimes(1)
      })

      it('应该跳过缓存', async () => {
        mockApiClient.getCustomers.mockResolvedValue(mockCustomersResponse)

        await service.getCustomers({ page: 1 }, false)
        await service.getCustomers({ page: 1 }, false)

        expect(mockApiClient.getCustomers).toHaveBeenCalledTimes(2)
      })

      it('应该处理API错误', async () => {
        const error = new Error('网络错误')
        mockApiClient.getCustomers.mockRejectedValue(error)

        await expect(service.getCustomers()).rejects.toThrow('网络错误')
        expect(ElMessage.error).toHaveBeenCalled()
      })

      it('应该处理数据格式错误', async () => {
        mockApiClient.getCustomers.mockResolvedValue({ data: null })

        await expect(service.getCustomers()).rejects.toThrow('客户列表数据格式错误')
      })
    })

    describe('getCustomerDetail', () => {
      const mockCustomerResponse = {
        data: {
          id: 1,
          name: '张三',
          phone: '13800138001',
          level: 'A',
          created_at: '2024-01-01'
        }
      }

      it('应该成功获取客户详情', async () => {
        mockApiClient.getCustomerDetail.mockResolvedValue(mockCustomerResponse)

        const result = await service.getCustomerDetail(1)

        expect(mockApiClient.getCustomerDetail).toHaveBeenCalledWith(1)
        expect(result.name).toBe('张三')
        expect(result.level_text).toBe('A级客户')
        expect(result.created_at).toBeInstanceOf(Date)
      })

      it('应该验证客户ID', async () => {
        await expect(service.getCustomerDetail()).rejects.toThrow('客户ID不能为空')
        await expect(service.getCustomerDetail(null)).rejects.toThrow('客户ID不能为空')
      })

      it('应该处理数据格式错误', async () => {
        mockApiClient.getCustomerDetail.mockResolvedValue({ data: null })

        await expect(service.getCustomerDetail(1)).rejects.toThrow('客户详情数据格式错误')
      })
    })

    describe('createCustomer', () => {
      const validCustomerData = {
        name: '王五',
        phone: '13800138003',
        level: 'B',
        email: '<EMAIL>'
      }

      const mockCreateResponse = {
        data: {
          id: 3,
          ...validCustomerData,
          created_at: '2024-01-15'
        }
      }

      it('应该成功创建客户', async () => {
        mockApiClient.createCustomer.mockResolvedValue(mockCreateResponse)

        const result = await service.createCustomer(validCustomerData)

        expect(mockApiClient.createCustomer).toHaveBeenCalledWith(
          expect.objectContaining({
            name: '王五',
            phone: '13800138003',
            level: 'B'
          })
        )
        expect(result.name).toBe('王五')
        expect(ElNotification.success).toHaveBeenCalled()
      })

      it('应该验证必填字段', async () => {
        await expect(service.createCustomer({})).rejects.toThrow('客户姓名不能为空')
        await expect(service.createCustomer(null)).rejects.toThrow('客户数据不能为空')
      })

      it('应该验证手机号格式', async () => {
        const invalidData = { ...validCustomerData, phone: '123456' }
        await expect(service.createCustomer(invalidData)).rejects.toThrow('手机号格式不正确')
      })

      it('应该验证邮箱格式', async () => {
        const invalidData = { ...validCustomerData, email: 'invalid-email' }
        await expect(service.createCustomer(invalidData)).rejects.toThrow('邮箱格式不正确')
      })

      it('应该验证客户等级', async () => {
        const invalidData = { ...validCustomerData, level: 'X' }
        await expect(service.createCustomer(invalidData)).rejects.toThrow('客户等级不正确')
      })

      it('应该清除相关缓存', async () => {
        mockApiClient.createCustomer.mockResolvedValue(mockCreateResponse)
        service.setCache('getCustomers_test', { data: 'test' })

        await service.createCustomer(validCustomerData)

        expect(service.getCache('getCustomers_test')).toBeNull()
      })
    })

    describe('updateCustomer', () => {
      const updateData = {
        name: '张三更新',
        phone: '13800138001',
        level: 'A'
      }

      const mockUpdateResponse = {
        data: {
          id: 1,
          ...updateData,
          updated_at: '2024-01-15'
        }
      }

      it('应该成功更新客户', async () => {
        mockApiClient.updateCustomer.mockResolvedValue(mockUpdateResponse)

        const result = await service.updateCustomer(1, updateData)

        expect(mockApiClient.updateCustomer).toHaveBeenCalledWith(1, updateData)
        expect(result.name).toBe('张三更新')
        expect(ElNotification.success).toHaveBeenCalled()
      })

      it('应该验证客户ID', async () => {
        await expect(service.updateCustomer(null, updateData)).rejects.toThrow('客户ID不能为空')
      })

      it('应该清除相关缓存', async () => {
        mockApiClient.updateCustomer.mockResolvedValue(mockUpdateResponse)
        service.setCache('getCustomers_test', { data: 'test' })
        service.setCache('getCustomerDetail_test', { data: 'test' })

        await service.updateCustomer(1, updateData)

        expect(service.getCache('getCustomers_test')).toBeNull()
        expect(service.getCache('getCustomerDetail_test')).toBeNull()
      })
    })

    describe('deleteCustomer', () => {
      it('应该成功删除客户', async () => {
        mockApiClient.deleteCustomer.mockResolvedValue({})

        const result = await service.deleteCustomer(1)

        expect(mockApiClient.deleteCustomer).toHaveBeenCalledWith(1)
        expect(result).toBe(true)
        expect(ElNotification.success).toHaveBeenCalled()
      })

      it('应该验证客户ID', async () => {
        await expect(service.deleteCustomer(null)).rejects.toThrow('客户ID不能为空')
      })

      it('应该清除相关缓存', async () => {
        mockApiClient.deleteCustomer.mockResolvedValue({})
        service.setCache('getCustomers_test', { data: 'test' })
        service.setCache('getCustomerDetail_test', { data: 'test' })

        await service.deleteCustomer(1)

        expect(service.getCache('getCustomers_test')).toBeNull()
        expect(service.getCache('getCustomerDetail_test')).toBeNull()
      })
    })
  })

  describe('统计数据', () => {
    it('应该成功获取分销员统计数据', async () => {
      const result = await service.getDistributorStats()

      expect(result).toHaveProperty('customers')
      expect(result).toHaveProperty('groups')
      expect(result).toHaveProperty('commission')
      expect(result).toHaveProperty('orders')
      expect(result).toHaveProperty('updated_at')

      expect(result.customers).toHaveProperty('total')
      expect(result.customers).toHaveProperty('active')
      expect(result.customers).toHaveProperty('new_this_month')
      expect(result.customers).toHaveProperty('trend')
    })

    it('应该使用缓存', async () => {
      // 第一次调用
      const result1 = await service.getDistributorStats()
      
      // 第二次调用应该使用缓存
      const result2 = await service.getDistributorStats()

      expect(result1).toEqual(result2)
    })
  })

  describe('数据处理', () => {
    describe('processCustomerItem', () => {
      it('应该正确处理客户数据项', () => {
        const rawCustomer = {
          id: 1,
          name: '张三',
          phone: '13800138001',
          level: 'A',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z'
        }

        const processed = service.processCustomerItem(rawCustomer)

        expect(processed.id).toBe(1)
        expect(processed.name).toBe('张三')
        expect(processed.level_text).toBe('A级客户')
        expect(processed.status_text).toBe('活跃')
        expect(processed.created_at).toBeInstanceOf(Date)
        expect(processed.days_since_created).toBeGreaterThan(0)
      })

      it('应该处理缺失字段', () => {
        const rawCustomer = { id: 1 }
        const processed = service.processCustomerItem(rawCustomer)

        expect(processed.name).toBe('未知客户')
        expect(processed.phone).toBe('')
        expect(processed.level).toBe('C')
        expect(processed.status).toBe('potential')
        expect(processed.tags).toEqual([])
      })
    })

    describe('preprocessCustomerData', () => {
      it('应该正确预处理客户数据', () => {
        const rawData = {
          name: '张三',
          phone: '',
          tags: ['VIP', '', '重要客户', null],
          preferences: {
            contact_method: 'phone',
            contact_time: ''
          }
        }

        const processed = service.preprocessCustomerData(rawData)

        expect(processed.phone).toBeNull()
        expect(processed.tags).toEqual(['VIP', '重要客户'])
        expect(processed.preferences.contact_time).toBeNull()
      })
    })

    describe('validateCustomerData', () => {
      it('应该验证有效的客户数据', () => {
        const validData = {
          name: '张三',
          phone: '13800138001',
          email: '<EMAIL>',
          level: 'A',
          status: 'active'
        }

        expect(() => service.validateCustomerData(validData)).not.toThrow()
      })

      it('应该拒绝无效数据', () => {
        expect(() => service.validateCustomerData(null)).toThrow('客户数据不能为空')
        expect(() => service.validateCustomerData({})).toThrow('客户姓名不能为空')
        
        const invalidPhone = { name: '张三', phone: '123456' }
        expect(() => service.validateCustomerData(invalidPhone)).toThrow('手机号格式不正确')
        
        const invalidEmail = { name: '张三', email: 'invalid' }
        expect(() => service.validateCustomerData(invalidEmail)).toThrow('邮箱格式不正确')
      })

      it('应该支持更新操作验证', () => {
        const updateData = { phone: '13800138001' }
        expect(() => service.validateCustomerData(updateData, false)).not.toThrow()
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理网络错误', () => {
      const networkError = new Error('Network Error')
      networkError.request = {}

      service.handleApiError(networkError, '默认错误')

      expect(ElMessage.error).toHaveBeenCalledWith('网络连接失败，请检查网络设置')
    })

    it('应该处理HTTP状态码错误', () => {
      const httpError = new Error('HTTP Error')
      httpError.response = {
        status: 400,
        data: { message: '请求参数错误' }
      }

      service.handleApiError(httpError, '默认错误')

      expect(ElMessage.error).toHaveBeenCalledWith('请求参数错误')
    })

    it('应该处理不同的HTTP状态码', () => {
      const testCases = [
        { status: 401, expected: '未授权访问' },
        { status: 403, expected: '权限不足' },
        { status: 404, expected: '资源不存在' },
        { status: 500, expected: '服务器内部错误' }
      ]

      testCases.forEach(({ status, expected }) => {
        const error = new Error('HTTP Error')
        error.response = { status, data: {} }

        service.handleApiError(error, '默认错误')

        expect(ElMessage.error).toHaveBeenCalledWith(expected)
      })
    })

    it('应该处理未知错误', () => {
      const unknownError = new Error('未知错误')

      service.handleApiError(unknownError, '默认错误')

      expect(ElMessage.error).toHaveBeenCalledWith('未知错误')
    })
  })

  describe('工具方法', () => {
    it('应该正确获取等级文本', () => {
      expect(service.getLevelText('A')).toBe('A级客户')
      expect(service.getLevelText('B')).toBe('B级客户')
      expect(service.getLevelText('C')).toBe('C级客户')
      expect(service.getLevelText('D')).toBe('D级客户')
      expect(service.getLevelText('X')).toBe('C级客户') // 默认值
    })

    it('应该正确获取状态文本', () => {
      expect(service.getStatusText('active')).toBe('活跃')
      expect(service.getStatusText('inactive')).toBe('不活跃')
      expect(service.getStatusText('potential')).toBe('潜在')
      expect(service.getStatusText('lost')).toBe('流失')
      expect(service.getStatusText('unknown')).toBe('潜在') // 默认值
    })

    it('应该正确计算天数差', () => {
      const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      expect(service.calculateDaysSince(threeDaysAgo)).toBe(3)
      
      expect(service.calculateDaysSince(null)).toBe(0)
      expect(service.calculateDaysSince('invalid')).toBe(0)
    })
  })
})