<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>页面错误</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            max-width: 400px;
        }
        
        .error-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: shake 1s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }
        
        .error-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .error-message {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255,255,255,0.5);
        }
        
        .btn-secondary:hover {
            background: rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">😵</div>
        <h1 class="error-title">页面出错了</h1>
        <p class="error-message">{{ $message ?? '抱歉，页面暂时无法访问，请稍后重试' }}</p>
        
        <div class="error-actions">
            <button class="btn btn-primary" onclick="window.location.reload()">
                刷新页面
            </button>
            <a href="{{ $redirect_url ?? 'https://www.baidu.com' }}" class="btn btn-secondary">
                返回首页
            </a>
        </div>
    </div>

    <script>
        // 3秒后自动跳转
        setTimeout(function() {
            if (confirm('页面将自动跳转，是否继续？')) {
                window.location.href = '{{ $redirect_url ?? "https://www.baidu.com" }}';
            }
        }, 3000);
    </script>
</body>
</html>