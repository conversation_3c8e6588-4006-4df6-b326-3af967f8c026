<template>
  <PageLayout
    :title="`订单详情 #${orderInfo.orderNo || ''}`"
    subtitle="查看订单详细信息"
    icon="View"
    :loading="loading"
  >
    <template #actions>
      <el-button @click="$router.go(-1)">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <el-button type="primary" @click="handlePrint">
        <el-icon><Printer /></el-icon>
        打印订单
      </el-button>
    </template>

    <div class="order-detail" v-if="!loading">
      <!-- 订单基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>订单信息</h3>
            <el-tag :type="getStatusColor(orderInfo.status)" size="large">
              {{ getStatusName(orderInfo.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>订单号：</label>
              <span>{{ orderInfo.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ orderInfo.amount }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>支付方式：</label>
              <span>{{ getPaymentMethod(orderInfo.paymentMethod) }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(orderInfo.createdAt) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>支付时间：</label>
              <span>{{ formatDate(orderInfo.paidAt) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>完成时间：</label>
              <span>{{ formatDate(orderInfo.completedAt) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 用户信息 -->
      <el-card class="user-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>用户信息</h3>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ orderInfo.user.username }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号：</label>
              <span>{{ orderInfo.user.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ orderInfo.user.email }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 商品信息 -->
      <el-card class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>商品信息</h3>
          </div>
        </template>
        
        <el-table :data="orderInfo.items" style="width: 100%">
          <el-table-column prop="name" label="商品名称" />
          <el-table-column prop="sku" label="SKU" />
          <el-table-column prop="price" label="单价" width="120">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              ¥{{ (row.price * row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 订单操作记录 -->
      <el-card class="logs-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>操作记录</h3>
          </div>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="log in orderInfo.logs"
            :key="log.id"
            :timestamp="formatDate(log.createdAt)"
            placement="top"
          >
            <div class="log-content">
              <div class="log-action">{{ log.action }}</div>
              <div class="log-operator">操作人：{{ log.operator }}</div>
              <div class="log-remark" v-if="log.remark">{{ log.remark }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </PageLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, View, Printer } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'

const router = useRouter()
const route = useRoute()
const loading = ref(true)

// 订单信息
const orderInfo = reactive({
  id: '',
  orderNo: '',
  amount: 0,
  status: '',
  paymentMethod: '',
  createdAt: '',
  paidAt: '',
  completedAt: '',
  user: {
    username: '',
    phone: '',
    email: ''
  },
  items: [],
  logs: []
})

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    pending: 'warning',
    paid: 'success',
    completed: 'success',
    cancelled: 'danger',
    refunded: 'info'
  }
  return colorMap[status] || ''
}

// 获取支付方式
const getPaymentMethod = (method) => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    bank: '银行卡',
    balance: '余额支付'
  }
  return methodMap[method] || '未知'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 打印订单
const handlePrint = () => {
  window.print()
}

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    Object.assign(orderInfo, {
      id: route.params.id,
      orderNo: 'ORD202408020001',
      amount: 99.00,
      status: 'paid',
      paymentMethod: 'wechat',
      createdAt: '2024-08-02T10:30:00Z',
      paidAt: '2024-08-02T10:35:00Z',
      completedAt: null,
      user: {
        username: '张三',
        phone: '***********',
        email: '<EMAIL>'
      },
      items: [
        {
          id: 1,
          name: 'VIP群组入群费',
          sku: 'VIP-001',
          price: 99.00,
          quantity: 1
        }
      ],
      logs: [
        {
          id: 1,
          action: '订单创建',
          operator: '系统',
          createdAt: '2024-08-02T10:30:00Z',
          remark: '用户下单成功'
        },
        {
          id: 2,
          action: '支付成功',
          operator: '系统',
          createdAt: '2024-08-02T10:35:00Z',
          remark: '微信支付成功'
        }
      ]
    })
  } catch (error) {
    ElMessage.error('加载订单详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail {
  .info-card,
  .user-card,
  .items-card,
  .logs-card {
    margin-bottom: 24px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .info-item {
    margin-bottom: 16px;
    
    label {
      font-weight: 500;
      color: #666;
      margin-right: 8px;
    }
    
    span {
      color: #333;
      
      &.amount {
        font-size: 18px;
        font-weight: 600;
        color: #f56c6c;
      }
    }
  }
  
  .log-content {
    .log-action {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }
    
    .log-operator {
      font-size: 12px;
      color: #999;
      margin-bottom: 4px;
    }
    
    .log-remark {
      font-size: 12px;
      color: #666;
    }
  }
}

@media print {
  .order-detail {
    .card-header {
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
  }
}
</style>
