/**
 * 地理位置服务
 * 用于获取用户的地理位置信息，包括城市名称
 */

class LocationService {
  constructor() {
    this.currentCity = '北京' // 默认城市
    this.isLocationEnabled = false
    this.locationCache = null
    this.cacheExpiry = 30 * 60 * 1000 // 30分钟缓存
  }

  /**
   * 获取用户当前城市
   * @returns {Promise<string>} 城市名称
   */
  async getCurrentCity() {
    // 检查缓存
    if (this.locationCache && Date.now() - this.locationCache.timestamp < this.cacheExpiry) {
      return this.locationCache.city
    }

    try {
      // 尝试多种方式获取城市信息
      const city = await this.getCityByMultipleMethods()
      
      // 更新缓存
      this.locationCache = {
        city,
        timestamp: Date.now()
      }
      
      this.currentCity = city
      return city
    } catch (error) {
      console.warn('获取城市信息失败，使用默认城市:', error)
      return this.currentCity
    }
  }

  /**
   * 使用多种方法获取城市信息
   * @returns {Promise<string>}
   */
  async getCityByMultipleMethods() {
    const methods = [
      () => this.getCityByGeolocation(),
      () => this.getCityByIP(),
      () => this.getCityByBrowser(),
      () => this.getCityFromStorage()
    ]

    for (const method of methods) {
      try {
        const city = await method()
        if (city && city !== '未知') {
          return city
        }
      } catch (error) {
        console.warn('获取城市方法失败:', error)
        continue
      }
    }

    return this.currentCity // 返回默认城市
  }

  /**
   * 通过HTML5 Geolocation API获取位置
   * @returns {Promise<string>}
   */
  getCityByGeolocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('浏览器不支持地理位置'))
        return
      }

      const options = {
        enableHighAccuracy: false,
        timeout: 10000,
        maximumAge: 300000 // 5分钟缓存
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { latitude, longitude } = position.coords
            const city = await this.reverseGeocode(latitude, longitude)
            resolve(city)
          } catch (error) {
            reject(error)
          }
        },
        (error) => {
          reject(new Error(`地理位置获取失败: ${error.message}`))
        },
        options
      )
    })
  }

  /**
   * 通过IP地址获取城市
   * @returns {Promise<string>}
   */
  async getCityByIP() {
    try {
      // 使用国内外免费的IP地理位置服务
      const services = [
        {
          url: 'https://pv.sohu.com/cityjson?ie=utf-8',
          parser: (text) => {
            // 搜狐IP接口返回JSONP格式
            const match = text.match(/var returnCitySN = ({.*?});/)
            if (match) {
              const data = JSON.parse(match[1])
              return data.cname || data.cid
            }
            return null
          }
        },
        {
          url: 'https://whois.pconline.com.cn/ipJson.jsp?json=true',
          parser: (text) => {
            try {
              const data = JSON.parse(text)
              return data.city || data.pro
            } catch {
              return null
            }
          }
        },
        {
          url: 'https://ip-api.com/json/?lang=zh-CN',
          parser: (text) => {
            try {
              const data = JSON.parse(text)
              return data.city || data.regionName
            } catch {
              return null
            }
          }
        }
      ]

      for (const service of services) {
        try {
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 5000)

          const response = await fetch(service.url, {
            signal: controller.signal,
            mode: 'cors'
          })
          clearTimeout(timeoutId)

          const text = await response.text()
          const city = service.parser(text)

          if (city) {
            // 处理中文城市名称
            const normalizedCity = this.normalizeCityName(city)
            console.log(`通过 ${service.url} 获取到城市:`, normalizedCity)
            return normalizedCity
          }
        } catch (error) {
          console.warn(`IP服务 ${service.url} 失败:`, error)
          continue
        }
      }

      throw new Error('所有IP服务都失败了')
    } catch (error) {
      throw new Error(`IP获取城市失败: ${error.message}`)
    }
  }

  /**
   * 通过浏览器语言和时区推测城市
   * @returns {Promise<string>}
   */
  async getCityByBrowser() {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      const language = navigator.language || navigator.userLanguage
      
      // 根据时区推测城市
      const cityByTimezone = this.getCityByTimezone(timezone)
      if (cityByTimezone) {
        return cityByTimezone
      }
      
      // 根据语言推测地区
      if (language.includes('zh-CN')) {
        return '北京'
      } else if (language.includes('zh-TW')) {
        return '台北'
      } else if (language.includes('zh-HK')) {
        return '香港'
      }
      
      return '北京' // 默认
    } catch (error) {
      throw new Error(`浏览器信息获取失败: ${error.message}`)
    }
  }

  /**
   * 从本地存储获取城市
   * @returns {Promise<string>}
   */
  async getCityFromStorage() {
    try {
      const storedCity = localStorage.getItem('user_city')
      if (storedCity) {
        return storedCity
      }
      throw new Error('本地存储中没有城市信息')
    } catch (error) {
      throw new Error(`本地存储获取失败: ${error.message}`)
    }
  }

  /**
   * 反向地理编码
   * @param {number} lat 纬度
   * @param {number} lng 经度
   * @returns {Promise<string>}
   */
  async reverseGeocode(lat, lng) {
    try {
      // 使用高德地图API（需要申请key）
      const amapKey = 'your_amap_key' // 需要配置
      if (amapKey && amapKey !== 'your_amap_key') {
        const response = await fetch(
          `https://restapi.amap.com/v3/geocode/regeo?key=${amapKey}&location=${lng},${lat}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`
        )
        const data = await response.json()
        if (data.status === '1' && data.regeocode) {
          return data.regeocode.addressComponent.city || data.regeocode.addressComponent.province
        }
      }

      // 备用方案：使用免费服务
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=zh`
      )
      const data = await response.json()
      return data.city || data.locality || '未知'
    } catch (error) {
      throw new Error(`反向地理编码失败: ${error.message}`)
    }
  }

  /**
   * 根据时区推测城市
   * @param {string} timezone 时区
   * @returns {string|null}
   */
  getCityByTimezone(timezone) {
    const timezoneMap = {
      'Asia/Shanghai': '上海',
      'Asia/Beijing': '北京',
      'Asia/Chongqing': '重庆',
      'Asia/Urumqi': '乌鲁木齐',
      'Asia/Hong_Kong': '香港',
      'Asia/Taipei': '台北',
      'Asia/Macau': '澳门'
    }
    
    return timezoneMap[timezone] || null
  }

  /**
   * 标准化城市名称
   * @param {string} city 原始城市名称
   * @returns {string}
   */
  normalizeCityName(city) {
    if (!city) return '北京'

    // 清理城市名称
    city = city.trim()

    // 移除常见后缀
    city = city.replace(/(市|省|自治区|特别行政区)$/, '')

    // 英文城市名称映射
    const englishCityMap = {
      'Beijing': '北京',
      'Shanghai': '上海',
      'Guangzhou': '广州',
      'Shenzhen': '深圳',
      'Hangzhou': '杭州',
      'Nanjing': '南京',
      'Wuhan': '武汉',
      'Chengdu': '成都',
      'Xi\'an': '西安',
      'Xian': '西安',
      'Chongqing': '重庆',
      'Tianjin': '天津',
      'Suzhou': '苏州',
      'Changsha': '长沙',
      'Zhengzhou': '郑州',
      'Qingdao': '青岛',
      'Dalian': '大连',
      'Ningbo': '宁波',
      'Xiamen': '厦门'
    }

    // 中文城市名称标准化
    const chineseCityMap = {
      '北京市': '北京',
      '上海市': '上海',
      '天津市': '天津',
      '重庆市': '重庆',
      '广东省广州市': '广州',
      '广东省深圳市': '深圳',
      '浙江省杭州市': '杭州',
      '江苏省南京市': '南京',
      '湖北省武汉市': '武汉',
      '四川省成都市': '成都',
      '陕西省西安市': '西安',
      '江苏省苏州市': '苏州',
      '湖南省长沙市': '长沙',
      '河南省郑州市': '郑州',
      '山东省青岛市': '青岛',
      '辽宁省大连市': '大连',
      '浙江省宁波市': '宁波',
      '福建省厦门市': '厦门'
    }

    // 先尝试英文映射
    if (englishCityMap[city]) {
      return englishCityMap[city]
    }

    // 再尝试中文映射
    if (chineseCityMap[city]) {
      return chineseCityMap[city]
    }

    // 处理省市组合格式，如"广东省广州"
    const provinceMatch = city.match(/^(.+?省)?(.+?)$/)
    if (provinceMatch && provinceMatch[2]) {
      const cityName = provinceMatch[2].replace(/(市|区|县)$/, '')
      if (cityName && cityName !== city) {
        return this.normalizeCityName(cityName)
      }
    }

    return city || '北京'
  }

  /**
   * 手动设置城市
   * @param {string} city 城市名称
   */
  setCity(city) {
    this.currentCity = city
    localStorage.setItem('user_city', city)
    
    // 清除缓存
    this.locationCache = null
  }

  /**
   * 获取热门城市列表
   * @returns {Array<string>}
   */
  getHotCities() {
    return [
      '北京', '上海', '广州', '深圳', '杭州', '南京',
      '武汉', '成都', '西安', '重庆', '天津', '苏州',
      '长沙', '郑州', '青岛', '大连', '宁波', '厦门'
    ]
  }

  /**
   * 检查是否支持地理位置
   * @returns {boolean}
   */
  isGeolocationSupported() {
    return 'geolocation' in navigator
  }

  /**
   * 请求地理位置权限
   * @returns {Promise<boolean>}
   */
  async requestLocationPermission() {
    if (!this.isGeolocationSupported()) {
      return false
    }

    try {
      const result = await navigator.permissions.query({ name: 'geolocation' })
      return result.state === 'granted'
    } catch (error) {
      console.warn('权限查询失败:', error)
      return false
    }
  }
}

// 创建单例实例
const locationService = new LocationService()

export default locationService
