<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

/**
 * 认证中间件
 * 处理用户认证逻辑
 */
class Authenticate extends Middleware
{
    /**
     * 获取用户应重定向到的路径
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        // API请求返回JSON错误
        if ($request->is('api/*')) {
            abort(response()->json([
                'success' => false,
                'message' => '未认证',
                'code' => 401
            ], 401));
        }

        return route('login');
    }

    /**
     * 处理未认证的请求
     */
    protected function unauthenticated($request, array $guards)
    {
        if ($request->expectsJson() || $request->is('api/*')) {
            abort(response()->json([
                'success' => false,
                'message' => '未认证，请先登录',
                'code' => 401
            ], 401));
        }

        throw new \Illuminate\Auth\AuthenticationException(
            'Unauthenticated.', $guards, $this->redirectTo($request)
        );
    }
} 