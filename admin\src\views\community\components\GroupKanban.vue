<template>
  <div class="group-kanban">
    <div class="kanban-board">
      <div
        v-for="status in statusColumns"
        :key="status.key"
        class="kanban-column"
      >
        <div class="column-header">
          <div class="header-title">
            <span class="status-icon" :class="status.key"></span>
            <span class="status-name">{{ status.name }}</span>
            <el-tag size="small" type="info">{{ getGroupsByStatus(status.key).length }}</el-tag>
          </div>
          <el-dropdown @command="handleColumnCommand">
            <el-button text size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="`add-${status.key}`">添加群组</el-dropdown-item>
                <el-dropdown-item :command="`filter-${status.key}`">筛选</el-dropdown-item>
                <el-dropdown-item :command="`export-${status.key}`">导出</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <div class="column-content">
          <Sortable
            :list="getGroupsByStatus(status.key)"
            :options="{
              group: 'groups',
              animation: 200,
              ghostClass: 'ghost'
            }"
            @change="handleDragChange"
            class="drag-area"
          >
            <template #item="{ element }">
              <div class="kanban-card" @click="handleCardClick(element)">
                <div class="card-header">
                  <div class="group-avatar">
                    <el-avatar :src="element.avatar" :alt="element.name" size="small">
                      {{ element.name.charAt(0) }}
                    </el-avatar>
                  </div>
                  <div class="card-actions">
                    <el-dropdown @command="handleCardCommand" trigger="click">
                      <el-button text size="small">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit-${element.id}`">编辑</el-dropdown-item>
                          <el-dropdown-item :command="`members-${element.id}`">成员</el-dropdown-item>
                          <el-dropdown-item :command="`analytics-${element.id}`">分析</el-dropdown-item>
                          <el-dropdown-item :command="`delete-${element.id}`" divided>删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>

                <div class="card-content">
                  <h4 class="group-name">{{ element.name }}</h4>
                  <p class="group-description">{{ element.description || '暂无描述' }}</p>
                  
                  <div class="group-metrics">
                    <div class="metric-item">
                      <span class="metric-label">成员</span>
                      <span class="metric-value">{{ element.current_members || 0 }}/{{ element.max_members || 500 }}</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">收益</span>
                      <span class="metric-value revenue">¥{{ (element.total_revenue || 0).toFixed(0) }}</span>
                    </div>
                  </div>

                  <div class="group-progress">
                    <el-progress
                      :percentage="getMemberPercentage(element)"
                      :stroke-width="4"
                      :show-text="false"
                      :color="getProgressColor(getMemberPercentage(element))"
                    />
                  </div>

                  <div class="group-tags">
                    <el-tag :type="getCategoryTagType(element.category)" size="small">
                      {{ getCategoryText(element.category) }}
                    </el-tag>
                    <el-tag
                      v-if="element.health_score"
                      :type="getHealthTagType(element.health_score)"
                      size="small"
                    >
                      健康度: {{ element.health_score }}
                    </el-tag>
                  </div>
                </div>

                <div class="card-footer">
                  <div class="owner-info">
                    <el-icon><User /></el-icon>
                    <span>{{ element.owner_name || '未知群主' }}</span>
                  </div>
                  <div class="create-time">
                    {{ formatDate(element.created_at) }}
                  </div>
                </div>
              </div>
            </template>
          </Sortable>

          <div v-if="getGroupsByStatus(status.key).length === 0" class="empty-column">
            <el-empty description="暂无群组" :image-size="60" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { MoreFilled, User } from '@element-plus/icons-vue'
import { Sortable } from 'sortablejs-vue3'

const props = defineProps({
  groups: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update', 'action'])

// 状态列配置
const statusColumns = [
  { key: 'active', name: '活跃群组', color: '#67c23a' },
  { key: 'paused', name: '暂停群组', color: '#e6a23c' },
  { key: 'full', name: '已满群组', color: '#909399' },
  { key: 'pending', name: '待审核', color: '#f56c6c' }
]

// 根据状态获取群组
const getGroupsByStatus = (status) => {
  return props.groups.filter(group => group.status === status)
}

// 处理拖拽变化
const handleDragChange = (evt) => {
  if (evt.moved) {
    const { element, newIndex } = evt.moved
    // 根据新位置确定状态
    // 这里需要根据实际的拖拽目标来确定新状态
    emit('update', {
      type: 'move',
      groupId: element.id,
      newStatus: 'active', // 这里应该根据实际拖拽目标确定
      newIndex
    })
  }
}

// 处理卡片点击
const handleCardClick = (group) => {
  emit('action', 'view', group)
}

// 处理列操作
const handleColumnCommand = (command) => {
  const [action, status] = command.split('-')
  
  switch (action) {
    case 'add':
      emit('action', 'create', { status })
      break
    case 'filter':
      ElMessage.info(`筛选 ${status} 状态的群组`)
      break
    case 'export':
      ElMessage.info(`导出 ${status} 状态的群组数据`)
      break
  }
}

// 处理卡片操作
const handleCardCommand = (command) => {
  const [action, groupId] = command.split('-')
  const group = props.groups.find(g => g.id === parseInt(groupId))
  
  if (group) {
    emit('action', action, group)
  }
}

// 工具函数
const getMemberPercentage = (group) => {
  const current = group.current_members || 0
  const max = group.max_members || 500
  return Math.round((current / max) * 100)
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const getCategoryTagType = (category) => {
  const types = {
    startup: 'success',
    finance: 'warning',
    tech: 'primary',
    education: 'info',
    other: ''
  }
  return types[category] || ''
}

const getCategoryText = (category) => {
  const texts = {
    startup: '创业交流',
    finance: '投资理财',
    tech: '科技互联网',
    education: '教育培训',
    other: '其他'
  }
  return texts[category] || '未知'
}

const getHealthTagType = (score) => {
  if (score >= 80) return 'success'
  if (score >= 60) return 'primary'
  if (score >= 40) return 'warning'
  return 'danger'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.group-kanban {
  .kanban-board {
    display: flex;
    gap: 20px;
    min-height: 600px;
    overflow-x: auto;
    padding: 20px 0;

    .kanban-column {
      flex: 1;
      min-width: 300px;
      background: #f8fafc;
      border-radius: 12px;
      padding: 16px;

      .column-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 2px solid #e5e7eb;

        .header-title {
          display: flex;
          align-items: center;
          gap: 8px;

          .status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.active {
              background: #67c23a;
            }

            &.paused {
              background: #e6a23c;
            }

            &.full {
              background: #909399;
            }

            &.pending {
              background: #f56c6c;
            }
          }

          .status-name {
            font-weight: 600;
            color: #374151;
          }
        }
      }

      .column-content {
        .drag-area {
          min-height: 400px;
        }

        .kanban-card {
          background: white;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
          }

          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .group-avatar {
              display: flex;
              align-items: center;
            }

            .card-actions {
              opacity: 0;
              transition: opacity 0.3s ease;
            }
          }

          &:hover .card-actions {
            opacity: 1;
          }

          .card-content {
            .group-name {
              font-size: 16px;
              font-weight: 600;
              color: #374151;
              margin: 0 0 8px 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .group-description {
              font-size: 12px;
              color: #6b7280;
              margin: 0 0 12px 0;
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              min-height: 32px;
            }

            .group-metrics {
              display: flex;
              justify-content: space-between;
              margin-bottom: 12px;

              .metric-item {
                text-align: center;

                .metric-label {
                  display: block;
                  font-size: 11px;
                  color: #9ca3af;
                  margin-bottom: 2px;
                }

                .metric-value {
                  font-size: 13px;
                  font-weight: 600;
                  color: #374151;

                  &.revenue {
                    color: #10b981;
                  }
                }
              }
            }

            .group-progress {
              margin-bottom: 12px;

              :deep(.el-progress) {
                .el-progress-bar__outer {
                  border-radius: 2px;
                  background: #f3f4f6;
                }

                .el-progress-bar__inner {
                  border-radius: 2px;
                }
              }
            }

            .group-tags {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;
            }
          }

          .card-footer {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #9ca3af;

            .owner-info {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }

        .empty-column {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #9ca3af;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .group-kanban {
    .kanban-board {
      .kanban-column {
        min-width: 250px;
      }
    }
  }
}

@media (max-width: 768px) {
  .group-kanban {
    .kanban-board {
      flex-direction: column;
      
      .kanban-column {
        min-width: auto;
        width: 100%;
      }
    }
  }
}
</style>