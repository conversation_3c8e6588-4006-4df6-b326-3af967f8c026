<template>
  <div class="enhanced-group-creator">
    <!-- 转化率优化面板 -->
    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 mb-6 border border-green-200">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900">转化率优化助手</h3>
            <p class="text-sm text-gray-600">实时分析并提供优化建议</p>
          </div>
        </div>
        <div class="text-right">
          <div class="text-2xl font-bold text-green-600">{{ estimatedConversionRate }}%</div>
          <div class="text-xs text-gray-500">预估转化率</div>
        </div>
      </div>
      
      <!-- 优化进度条 -->
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700">优化完成度</span>
          <span class="text-sm text-gray-500">{{ completedOptimizations }}/{{ totalOptimizations }}</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${(completedOptimizations / totalOptimizations) * 100}%` }"
          ></div>
        </div>
      </div>
      
      <!-- 快速优化建议 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div 
          v-for="suggestion in topSuggestions" 
          :key="suggestion.id"
          @click="applySuggestion(suggestion)"
          class="bg-white rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border border-gray-200"
        >
          <div class="flex items-center mb-2">
            <div :class="['w-3 h-3 rounded-full mr-2', suggestion.completed ? 'bg-green-500' : 'bg-orange-500']"></div>
            <span class="text-sm font-medium text-gray-900">{{ suggestion.title }}</span>
          </div>
          <p class="text-xs text-gray-600">{{ suggestion.description }}</p>
          <div class="mt-2 text-xs text-blue-600 font-medium">+{{ suggestion.impact }}% 转化率</div>
        </div>
      </div>
    </div>

    <!-- 智能表单增强 -->
    <div class="space-y-6">
      <!-- 智能标题生成器 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">🎯 智能标题生成器</h3>
          <button 
            @click="generateTitles"
            :disabled="!formData.category"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {{ generatingTitles ? '生成中...' : '生成标题' }}
          </button>
        </div>
        
        <div v-if="generatedTitles.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div 
            v-for="(title, index) in generatedTitles" 
            :key="index"
            @click="selectTitle(title)"
            class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-900">{{ title.text }}</span>
              <div class="flex items-center text-xs text-green-600">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                {{ title.score }}分
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1">{{ title.reason }}</p>
          </div>
        </div>
      </div>

      <!-- 价格优化建议 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">💰 价格策略优化</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div 
            v-for="strategy in pricingStrategies" 
            :key="strategy.name"
            @click="applyPricingStrategy(strategy)"
            :class="[
              'p-4 border-2 rounded-lg cursor-pointer transition-colors',
              selectedPricingStrategy === strategy.name ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
            ]"
          >
            <div class="text-center">
              <div class="text-2xl mb-2">{{ strategy.icon }}</div>
              <div class="font-medium text-gray-900">{{ strategy.name }}</div>
              <div class="text-sm text-gray-600 mt-1">{{ strategy.description }}</div>
              <div class="text-lg font-bold text-blue-600 mt-2">¥{{ strategy.price }}</div>
            </div>
          </div>
        </div>
        
        <!-- 价格影响分析 -->
        <div v-if="priceAnalysis" class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-medium text-gray-900 mb-2">价格影响分析</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-semibold text-blue-600">{{ priceAnalysis.conversionRate }}%</div>
              <div class="text-gray-600">预估转化率</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-green-600">{{ priceAnalysis.expectedOrders }}</div>
              <div class="text-gray-600">预期订单/天</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-purple-600">¥{{ priceAnalysis.dailyRevenue }}</div>
              <div class="text-gray-600">预期日收入</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-orange-600">{{ priceAnalysis.competitiveness }}</div>
              <div class="text-gray-600">竞争力指数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 社会证明优化器 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">👥 社会证明优化器</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-900 mb-3">数据设置</h4>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">浏览量显示</label>
                <div class="flex items-center space-x-2">
                  <input
                    v-model="socialProof.viewCount"
                    type="text"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="8万+"
                  />
                  <button 
                    @click="generateViewCount"
                    class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm"
                  >
                    智能生成
                  </button>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">点赞数</label>
                <div class="flex items-center space-x-2">
                  <input
                    v-model.number="socialProof.likeCount"
                    type="number"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="1500"
                  />
                  <button 
                    @click="generateLikeCount"
                    class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm"
                  >
                    智能生成
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 mb-3">效果预览</h4>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center space-x-4 text-sm text-gray-600">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ socialProof.viewCount }} 人已读
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                  </svg>
                  {{ socialProof.likeCount }} 人点赞
                </div>
              </div>
              <div class="mt-3 text-xs text-green-600">
                社会证明强度：{{ getSocialProofStrength() }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 紧迫感营销优化 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">⏰ 紧迫感营销优化</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-900 mb-3">营销策略</h4>
            <div class="space-y-3">
              <label 
                v-for="strategy in urgencyStrategies" 
                :key="strategy.id"
                class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  v-model="selectedUrgencyStrategies"
                  type="checkbox"
                  :value="strategy.id"
                  class="mt-1 w-4 h-4 text-blue-600"
                />
                <div class="flex-1">
                  <div class="font-medium text-gray-900">{{ strategy.name }}</div>
                  <div class="text-sm text-gray-600">{{ strategy.description }}</div>
                  <div class="text-xs text-green-600 mt-1">转化率提升：+{{ strategy.impact }}%</div>
                </div>
              </label>
            </div>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 mb-3">效果预览</h4>
            <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
              <div v-if="selectedUrgencyStrategies.includes('limited_time')" class="mb-3">
                <div class="flex items-center text-orange-600 mb-1">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="font-medium">限时优惠</span>
                </div>
                <div class="text-sm text-gray-700">仅剩 23小时59分钟</div>
              </div>
              
              <div v-if="selectedUrgencyStrategies.includes('limited_quantity')" class="mb-3">
                <div class="flex items-center text-red-600 mb-1">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="font-medium">限量名额</span>
                </div>
                <div class="text-sm text-gray-700">仅剩 12 个名额</div>
              </div>
              
              <div class="text-xs text-gray-500 mt-3">
                预计转化率提升：+{{ calculateUrgencyImpact() }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:formData'])

// 响应式数据
const generatingTitles = ref(false)
const generatedTitles = ref([])
const selectedPricingStrategy = ref('')
const socialProof = ref({
  viewCount: '8万+',
  likeCount: 1500
})
const selectedUrgencyStrategies = ref([])

// 转化率优化建议
const optimizationSuggestions = ref([
  { id: 1, title: '优化群组标题', description: '使用更吸引人的标题', impact: 0.8, completed: false },
  { id: 2, title: '设置合理价格', description: '价格在9.9-99.9元区间', impact: 1.2, completed: false },
  { id: 3, title: '添加社会证明', description: '显示浏览量和点赞数', impact: 1.5, completed: false },
  { id: 4, title: '启用紧迫感营销', description: '限时优惠提升转化', impact: 2.0, completed: false },
  { id: 5, title: '完善用户评价', description: '添加真实用户评价', impact: 1.0, completed: false },
  { id: 6, title: '优化按钮文案', description: '使用更有吸引力的CTA', impact: 0.6, completed: false }
])

// 价格策略
const pricingStrategies = ref([
  { name: '低价策略', icon: '💰', price: 9.9, description: '低门槛高转化', conversionRate: 8.5 },
  { name: '中价策略', icon: '⭐', price: 29.9, description: '平衡价值与转化', conversionRate: 5.2 },
  { name: '高价策略', icon: '👑', price: 99.9, description: '高价值精品定位', conversionRate: 2.8 }
])

// 紧迫感策略
const urgencyStrategies = ref([
  { id: 'limited_time', name: '限时优惠', description: '设置优惠截止时间', impact: 2.0 },
  { id: 'limited_quantity', name: '限量名额', description: '显示剩余名额数量', impact: 1.5 },
  { id: 'social_pressure', name: '社交压力', description: '显示其他人正在购买', impact: 1.2 },
  { id: 'price_increase', name: '涨价预告', description: '提示即将涨价', impact: 1.8 }
])

// 计算属性
const completedOptimizations = computed(() => {
  return optimizationSuggestions.value.filter(s => s.completed).length
})

const totalOptimizations = computed(() => {
  return optimizationSuggestions.value.length
})

const topSuggestions = computed(() => {
  return optimizationSuggestions.value
    .filter(s => !s.completed)
    .sort((a, b) => b.impact - a.impact)
    .slice(0, 3)
})

const estimatedConversionRate = computed(() => {
  let baseRate = 2.5
  let bonus = 0
  
  optimizationSuggestions.value.forEach(suggestion => {
    if (suggestion.completed) {
      bonus += suggestion.impact
    }
  })
  
  return Math.min(Math.round((baseRate + bonus) * 10) / 10, 15.0)
})

const priceAnalysis = computed(() => {
  if (!props.formData.price) return null
  
  const price = props.formData.price
  let conversionRate = 5.0
  
  if (price <= 19.9) conversionRate = 8.5
  else if (price <= 49.9) conversionRate = 5.2
  else if (price <= 99.9) conversionRate = 3.1
  else conversionRate = 1.8
  
  const expectedOrders = Math.round(conversionRate * 10)
  const dailyRevenue = Math.round(expectedOrders * price)
  const competitiveness = price <= 29.9 ? '高' : price <= 69.9 ? '中' : '低'
  
  return {
    conversionRate: conversionRate.toFixed(1),
    expectedOrders,
    dailyRevenue,
    competitiveness
  }
})

// 方法
const generateTitles = async () => {
  if (!props.formData.category) return
  
  generatingTitles.value = true
  
  // 模拟AI生成标题
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  const titleTemplates = {
    'business': [
      { text: '商务精英交流群', score: 8.5, reason: '突出精英定位，吸引高端用户' },
      { text: '创业项目对接群', score: 9.2, reason: '明确价值主张，针对性强' },
      { text: '商业资源共享群', score: 8.8, reason: '强调资源价值，吸引力强' }
    ],
    'education': [
      { text: '学霸养成计划群', score: 9.0, reason: '结果导向，激发学习欲望' },
      { text: '知识变现实战群', score: 8.7, reason: '结合热点，实用性强' },
      { text: '技能提升加速群', score: 8.3, reason: '强调效率，符合用户需求' }
    ]
  }
  
  generatedTitles.value = titleTemplates[props.formData.category] || []
  generatingTitles.value = false
}

const selectTitle = (title) => {
  emit('update:formData', { ...props.formData, title: title.text })
  updateOptimizationStatus()
}

const applyPricingStrategy = (strategy) => {
  selectedPricingStrategy.value = strategy.name
  emit('update:formData', { ...props.formData, price: strategy.price })
  updateOptimizationStatus()
}

const generateViewCount = () => {
  const counts = ['5万+', '8万+', '12万+', '15万+', '20万+']
  socialProof.value.viewCount = counts[Math.floor(Math.random() * counts.length)]
  updateSocialProof()
}

const generateLikeCount = () => {
  const baseCount = Math.floor(Math.random() * 2000) + 500
  socialProof.value.likeCount = baseCount
  updateSocialProof()
}

const getSocialProofStrength = () => {
  const viewNum = parseInt(socialProof.value.viewCount.replace(/[万+]/g, '')) || 0
  const likeNum = socialProof.value.likeCount || 0
  
  if (viewNum >= 10 && likeNum >= 1000) return '强'
  if (viewNum >= 5 && likeNum >= 500) return '中'
  return '弱'
}

const calculateUrgencyImpact = () => {
  return selectedUrgencyStrategies.value.reduce((total, strategyId) => {
    const strategy = urgencyStrategies.value.find(s => s.id === strategyId)
    return total + (strategy ? strategy.impact : 0)
  }, 0).toFixed(1)
}

const applySuggestion = (suggestion) => {
  // 根据建议类型执行相应操作
  switch (suggestion.id) {
    case 1: // 优化标题
      if (props.formData.category) {
        generateTitles()
      }
      break
    case 2: // 设置价格
      applyPricingStrategy(pricingStrategies.value[1]) // 中价策略
      break
    case 3: // 社会证明
      generateViewCount()
      generateLikeCount()
      break
    case 4: // 紧迫感营销
      selectedUrgencyStrategies.value = ['limited_time']
      emit('update:formData', { ...props.formData, show_limited_time: true })
      break
  }
  
  suggestion.completed = true
}

const updateOptimizationStatus = () => {
  // 更新优化状态
  optimizationSuggestions.value[0].completed = props.formData.title && props.formData.title.length >= 5
  optimizationSuggestions.value[1].completed = props.formData.price >= 9.9 && props.formData.price <= 99.9
  optimizationSuggestions.value[2].completed = socialProof.value.likeCount > 0
  optimizationSuggestions.value[3].completed = props.formData.show_limited_time
  optimizationSuggestions.value[4].completed = props.formData.reviews?.filter(r => r.content).length >= 2
  optimizationSuggestions.value[5].completed = props.formData.button_title !== '立即加入群聊'
}

const updateSocialProof = () => {
  emit('update:formData', {
    ...props.formData,
    read_count_display: socialProof.value.viewCount,
    like_count: socialProof.value.likeCount
  })
  updateOptimizationStatus()
}

// 监听表单数据变化
watch(() => props.formData, () => {
  updateOptimizationStatus()
}, { deep: true })

// 监听紧迫感策略变化
watch(selectedUrgencyStrategies, (newStrategies) => {
  const hasLimitedTime = newStrategies.includes('limited_time')
  emit('update:formData', { ...props.formData, show_limited_time: hasLimitedTime })
  updateOptimizationStatus()
}, { deep: true })
</script>

<style scoped>
.enhanced-group-creator {
  @apply space-y-6;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>