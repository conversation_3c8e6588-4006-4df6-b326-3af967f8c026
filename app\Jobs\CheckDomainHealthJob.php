<?php

namespace App\Jobs;

use App\Models\DomainPool;
use App\Services\AntiBlockService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * 域名健康检查队列任务
 */
class CheckDomainHealthJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ?int $domainId;

    /**
     * 任务最大尝试次数
     */
    public int $tries = 2;

    /**
     * 任务超时时间（秒）
     */
    public int $timeout = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $domainId = null)
    {
        $this->domainId = $domainId;
    }

    /**
     * Execute the job.
     */
    public function handle(AntiBlockService $antiBlockService): void
    {
        try {
            if ($this->domainId) {
                // 检查单个域名
                $result = $antiBlockService->checkSingleDomain($this->domainId);
                
                Log::info('单个域名健康检查完成', [
                    'domain_id' => $this->domainId,
                    'result' => $result,
                ]);
            } else {
                // 检查所有域名
                $domains = DomainPool::where('status', 1)->pluck('id')->toArray();
                
                if (!empty($domains)) {
                    $results = $antiBlockService->batchCheckDomains($domains);
                    
                    Log::info('批量域名健康检查完成', [
                        'checked_count' => count($domains),
                        'results' => $results,
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('域名健康检查失败', [
                'domain_id' => $this->domainId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('域名健康检查任务失败', [
            'domain_id' => $this->domainId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}