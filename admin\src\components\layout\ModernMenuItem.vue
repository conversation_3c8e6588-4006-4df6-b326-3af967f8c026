<template>
  <div class="enhanced-menu-item">
    <!-- 单级菜单项 -->
    <el-tooltip 
      v-if="!hasChildren"
      :content="item.meta?.title"
      placement="right"
      :disabled="!collapsed"
      :show-after="500"
    >
      <el-menu-item 
        :index="resolvePath"
        class="menu-item-single"
        @click="handleClick"
      >
        <div class="menu-item-content">
          <div class="menu-icon" :class="getIconClass()">
            <el-icon v-if="item.meta?.icon">
              <component :is="getIconComponent()" />
            </el-icon>
            <div v-else class="default-icon">
              <div class="icon-dot"></div>
            </div>
          </div>
          <transition name="menu-text">
            <span v-if="!collapsed" class="menu-title">{{ item.meta?.title }}</span>
          </transition>
          <div v-if="item.meta?.badge && !collapsed" class="menu-badge">
            {{ item.meta.badge }}
          </div>
          <div v-if="item.meta?.shortcut && !collapsed" class="menu-shortcut">
            {{ item.meta.shortcut }}
          </div>
          <div v-if="hasNotification() && !collapsed" class="notification-dot"></div>
        </div>
        <div class="menu-item-bg"></div>
        <div class="menu-item-indicator"></div>
      </el-menu-item>
    </el-tooltip>

    <!-- 多级菜单项 -->
    <el-tooltip 
      v-else
      :content="item.meta?.title"
      placement="right"
      :disabled="!collapsed"
      :show-after="500"
    >
      <el-sub-menu :index="resolvePath" class="menu-item-group">
        <template #title>
          <div class="menu-item-content">
            <div class="menu-icon" :class="getIconClass()">
              <el-icon v-if="item.meta?.icon">
                <component :is="getIconComponent()" />
              </el-icon>
              <div v-else class="default-icon">
                <div class="icon-dot"></div>
              </div>
            </div>
            <transition name="menu-text">
              <span v-if="!collapsed" class="menu-title">{{ item.meta?.title }}</span>
            </transition>
            <div v-if="item.meta?.badge && !collapsed" class="menu-badge">
              {{ item.meta.badge }}
            </div>
            <div v-if="hasNotification() && !collapsed" class="notification-dot"></div>
          </div>
        </template>

        <ModernMenuItem
          v-for="child in item.children"
          :key="child.path"
          :item="child"
          :base-path="resolvePath"
          :collapsed="collapsed"
          class="submenu-item"
        />
      </el-sub-menu>
    </el-tooltip>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { checkMenuPermission } from '@/config/navigation'
import { useUserStore } from '@/stores/user'
import {
  Monitor, DataLine, Comment, User, UserFilled, Document, Share, Grid, Avatar, Money, List,
  OfficeBuilding, Lock, Connection, TrendCharts, Tools, Edit, DocumentCopy, MagicStick, 
  Key, Link, Tickets, DataAnalysis, Upload, Download, Goods, Medal, Cpu, Setting,
  InfoFilled, CreditCard, Folder, View, Bell
} from '@element-plus/icons-vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  },
  collapsed: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const userStore = useUserStore()

// 计算属性
const hasChildren = computed(() => {
  return props.item.children && props.item.children.length > 0
})

const resolvePath = computed(() => {
  if (props.item.path.startsWith('/')) {
    return props.item.path
  }
  return `${props.basePath}/${props.item.path}`.replace(/\/+/g, '/')
})

// 方法
const handleClick = () => {
  if (props.item.meta?.external) {
    window.open(props.item.path, '_blank')
  } else {
    router.push(resolvePath.value)
  }
}

const getIconClass = () => {
  const iconMap = {
    'Monitor': 'dashboard-icon',
    'DataLine': 'datascreen-icon',
    'User': 'user-icon',
    'UserFilled': 'user-icon',
    'Comment': 'community-icon',
    'Money': 'finance-icon',
    'Tickets': 'order-icon',
    'Setting': 'system-icon',
    'DataAnalysis': 'analytics-icon',
    'Connection': 'network-icon',
    'Document': 'document-icon',
    'Lock': 'security-icon'
  }
  return iconMap[props.item.meta?.icon] || 'default-icon'
}

const getIconComponent = () => {
  const iconComponents = {
    'Monitor': Monitor,
    'DataLine': DataLine,
    'Comment': Comment,
    'User': User,
    'UserFilled': UserFilled,
    'Document': Document,
    'Share': Share,
    'Grid': Grid,
    'Avatar': Avatar,
    'Money': Money,
    'List': List,
    'OfficeBuilding': OfficeBuilding,
    'Lock': Lock,
    'Connection': Connection,
    'TrendCharts': TrendCharts,
    'Tools': Tools,
    'Edit': Edit,
    'DocumentCopy': DocumentCopy,
    'MagicStick': MagicStick,
    'Key': Key,
    'Link': Link,
    'Tickets': Tickets,
    'DataAnalysis': TrendCharts,
    'Upload': Upload,
    'Download': Download,
    'Goods': Goods,
    'Medal': Medal,
    'Cpu': Cpu,
    'Setting': Setting,
    'InfoFilled': InfoFilled,
    'CreditCard': CreditCard,
    'Folder': Folder,
    'View': View,
    'Bell': Bell
  }
  return iconComponents[props.item.meta?.icon] || Monitor
}

const hasNotification = () => {
  // 这里可以根据实际业务逻辑判断是否有通知
  const notificationRoutes = ['/system/notifications', '/orders/list', '/user/list']
  return notificationRoutes.includes(props.item.path)
}
</script>

<style lang="scss" scoped>
.enhanced-menu-item {
  margin-bottom: 4px;

  // 单级菜单项样式
  :deep(.menu-item-single) {
    margin: 0 0 4px 0;
    border-radius: 12px;
    height: 48px;
    line-height: 48px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(4px);

      .menu-item-bg {
        opacity: 1;
        transform: translateX(0);
      }

      .menu-icon {
        transform: scale(1.1);
        color: #ffffff;
      }
    }

    &.is-active {
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      color: #ffffff;
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);

      .menu-icon {
        color: #ffffff;
        transform: scale(1.1);
      }

      .menu-title {
        font-weight: 600;
      }

      &::after {
        content: '';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: #ffffff;
        border-radius: 2px;
      }
    }

    .menu-item-content {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 0 16px;
      position: relative;
      z-index: 2;

      .menu-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.8);

        .default-icon {
          .icon-dot {
            width: 6px;
            height: 6px;
            background: currentColor;
            border-radius: 50%;
          }
        }
      }

      .menu-title {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .menu-badge {
        background: #ef4444;
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        line-height: 1.2;
      }

      .menu-shortcut {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: 8px;
      }

      .notification-dot {
        width: 8px;
        height: 8px;
        background: #ef4444;
        border-radius: 50%;
        margin-left: 8px;
        animation: pulse 2s infinite;
      }
    }

    .menu-item-indicator {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: #ffffff;
      border-radius: 0 2px 2px 0;
      transition: height 0.3s ease;
    }

    &.is-active .menu-item-indicator {
      height: 24px;
    }

    .menu-item-bg {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      opacity: 0;
      transition: all 0.5s ease;
      z-index: 1;
    }
  }

  // 多级菜单项样式
  :deep(.menu-item-group) {
    margin: 0 0 4px 0;
    border-radius: 12px;
    overflow: hidden;

    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      border-radius: 12px;
      transition: all 0.3s ease;
      background: transparent;
      position: relative;
      overflow: hidden;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(4px);

        .menu-icon {
          transform: scale(1.1);
          color: #ffffff;
        }
      }

      .menu-item-content {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 0 16px;

        .menu-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          transition: all 0.3s ease;
          color: rgba(255, 255, 255, 0.8);

          .default-icon {
            .icon-dot {
              width: 6px;
              height: 6px;
              background: currentColor;
              border-radius: 50%;
            }
          }
        }

        .menu-title {
          flex: 1;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .menu-badge {
          background: #ef4444;
          color: white;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 10px;
          min-width: 16px;
          text-align: center;
          line-height: 1.2;
        }
      }
    }

    .el-menu {
      background: transparent;

      .submenu-item {
        :deep(.menu-item-single) {
          margin-left: 20px;
          height: 40px;
          line-height: 40px;
          border-radius: 8px;

          .menu-item-content {
            padding: 0 12px;

            .menu-icon {
              width: 16px;
              height: 16px;
              margin-right: 8px;
            }

            .menu-title {
              font-size: 13px;
            }
          }

          &.is-active {
            background: rgba(255, 255, 255, 0.15);
            
            &::after {
              width: 3px;
              height: 16px;
              right: 8px;
            }
          }
        }
      }
    }
  }
}

// 折叠状态下的样式调整
.modern-menu-item {
  :deep(.el-menu--collapse) {
    .menu-item-single,
    .el-sub-menu__title {
      .menu-item-content {
        justify-content: center;
        padding: 0;

        .menu-icon {
          margin-right: 0;
        }

        .menu-title,
        .menu-badge {
          display: none;
        }
      }
    }
  }
}

// 工具提示样式
:deep(.el-tooltip__popper) {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 12px;
  padding: 8px 12px;
  backdrop-filter: blur(10px);
}
</style>