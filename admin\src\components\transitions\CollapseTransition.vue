<template>
  <transition
    name="collapse"
    @enter="enter"
    @after-enter="afterEnter"
    @leave="leave"
    @after-leave="afterLeave"
  >
    <slot />
  </transition>
</template>

<script setup>
const enter = (el) => {
  el.style.height = '0'
  el.style.overflow = 'hidden'
  el.offsetHeight // 强制重排
  el.style.height = el.scrollHeight + 'px'
}

const afterEnter = (el) => {
  el.style.height = 'auto'
  el.style.overflow = ''
}

const leave = (el) => {
  el.style.height = el.scrollHeight + 'px'
  el.style.overflow = 'hidden'
  el.offsetHeight // 强制重排
  el.style.height = '0'
}

const afterLeave = (el) => {
  el.style.height = ''
  el.style.overflow = ''
}
</script>

<style scoped>
.collapse-enter-active,
.collapse-leave-active {
  transition: height 0.3s ease;
}
</style>
