<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 推广链接表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('promotion_links', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->unsignedBigInteger('group_id')->comment('群组ID');
            $table->foreign('group_id')->references('id')->on('wechat_groups')->onDelete('cascade');
            
            // 链接信息
            $table->string('short_code', 10)->unique()->comment('短码');
            $table->text('original_url')->comment('原始URL');
            $table->string('title', 200)->nullable()->comment('链接标题');
            $table->text('description')->nullable()->comment('链接描述');
            
            // 统计信息
            $table->integer('click_count')->default(0)->comment('点击次数');
            $table->timestamp('last_clicked_at')->nullable()->comment('最后点击时间');
            
            // 状态信息
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamp('expires_at')->nullable()->comment('过期时间');
            
            // 创建者信息
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            
            // 追踪参数
            $table->json('tracking_params')->nullable()->comment('追踪参数');
            
            $table->timestamps();
            
            // 索引
            $table->index(['group_id', 'is_active']);
            $table->index(['created_by']);
            $table->index(['expires_at']);
            $table->index(['click_count']);
            $table->index(['created_at']);
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_links');
    }
};