<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Models\DomainPool;
use App\Models\GroupTemplate;
use App\Services\GroupService;
use App\Services\AntiBlockService;
use App\Services\PaymentService;
use App\Http\Requests\Admin\GroupCreateRequest;
use App\Http\Requests\Admin\GroupUpdateRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 群组管理控制器
 */
class GroupController extends Controller
{
    protected GroupService $groupService;
    protected AntiBlockService $antiBlockService;
    protected PaymentService $paymentService;

    public function __construct(
        GroupService $groupService,
        AntiBlockService $antiBlockService,
        PaymentService $paymentService
    ) {
        $this->groupService = $groupService;
        $this->antiBlockService = $antiBlockService;
        $this->paymentService = $paymentService;
    }

    /**
     * 获取群组列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'category' => 'string',
                'status' => 'string|in:active,inactive,draft',
                'price_range' => 'string',
                'date_range' => 'array',
                'search' => 'string|max:255',
                'sort_by' => 'string|in:created_at,updated_at,price,member_count,order_count',
                'sort_order' => 'string|in:asc,desc'
            ]);

            $groups = $this->groupService->getGroupList($params);

            return $this->success($groups, '获取群组列表成功');
        } catch (\Exception $e) {
            Log::error('获取群组列表失败', [
                'error' => $e->getMessage(),
                'params' => $request->all()
            ]);
            
            return $this->error('获取群组列表失败');
        }
    }

    /**
     * 创建群组
     */
    public function store(GroupCreateRequest $request): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $data = $request->validated();
            
            // 创建群组
            $group = $this->groupService->createGroup($data);
            
            // 如果启用防红系统，设置域名池
            if ($data['enable_anti_block'] && !empty($data['domain_pool_id'])) {
                $this->antiBlockService->assignDomainPool($group->id, $data['domain_pool_id']);
            }
            
            // 如果需要生成推广海报
            if ($data['generate_qr_poster']) {
                $this->groupService->generatePoster($group->id);
            }
            
            // 如果设置自动发布
            if ($data['auto_publish']) {
                $group->update(['status' => 'active']);
            }
            
            DB::commit();
            
            // 记录操作日志
            Log::info('群组创建成功', [
                'group_id' => $group->id,
                'title' => $group->title,
                'user_id' => auth()->id()
            ]);
            
            return $this->success($group->load(['category', 'creator']), '群组创建成功');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('群组创建失败', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'user_id' => auth()->id()
            ]);
            
            return $this->error('群组创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取群组详情
     */
    public function show(int $id): JsonResponse
    {
        try {
            $group = $this->groupService->getGroupDetail($id);
            
            if (!$group) {
                return $this->error('群组不存在', 404);
            }
            
            return $this->success($group, '获取群组详情成功');
            
        } catch (\Exception $e) {
            Log::error('获取群组详情失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取群组详情失败');
        }
    }

    /**
     * 更新群组
     */
    public function update(GroupUpdateRequest $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $data = $request->validated();
            
            $group = $this->groupService->updateGroup($id, $data);
            
            if (!$group) {
                return $this->error('群组不存在', 404);
            }
            
            // 更新防红系统配置
            if (isset($data['enable_anti_block'])) {
                if ($data['enable_anti_block'] && !empty($data['domain_pool_id'])) {
                    $this->antiBlockService->assignDomainPool($id, $data['domain_pool_id']);
                } else {
                    $this->antiBlockService->removeDomainPool($id);
                }
            }
            
            DB::commit();
            
            Log::info('群组更新成功', [
                'group_id' => $id,
                'user_id' => auth()->id()
            ]);
            
            return $this->success($group, '群组更新成功');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('群组更新失败', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return $this->error('群组更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除群组
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $result = $this->groupService->deleteGroup($id);
            
            if (!$result) {
                return $this->error('群组不存在或删除失败', 404);
            }
            
            DB::commit();
            
            Log::info('群组删除成功', [
                'group_id' => $id,
                'user_id' => auth()->id()
            ]);
            
            return $this->success(null, '群组删除成功');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('群组删除失败', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return $this->error('群组删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除群组
     */
    public function batchDelete(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:wechat_groups,id'
        ]);

        DB::beginTransaction();
        
        try {
            $ids = $request->input('ids');
            $result = $this->groupService->batchDeleteGroups($ids);
            
            DB::commit();
            
            Log::info('批量删除群组成功', [
                'group_ids' => $ids,
                'count' => count($ids),
                'user_id' => auth()->id()
            ]);
            
            return $this->success([
                'deleted_count' => $result
            ], '批量删除成功');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('批量删除群组失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return $this->error('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 切换群组状态
     */
    public function toggleStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:active,inactive,draft'
        ]);

        try {
            $status = $request->input('status');
            $group = $this->groupService->toggleGroupStatus($id, $status);
            
            if (!$group) {
                return $this->error('群组不存在', 404);
            }
            
            Log::info('群组状态切换成功', [
                'group_id' => $id,
                'status' => $status,
                'user_id' => auth()->id()
            ]);
            
            return $this->success($group, '状态更新成功');
            
        } catch (\Exception $e) {
            Log::error('群组状态切换失败', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return $this->error('状态更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取群组统计数据
     */
    public function stats(int $id = null): JsonResponse
    {
        try {
            if ($id) {
                // 获取单个群组统计
                $stats = $this->groupService->getGroupStats($id);
            } else {
                // 获取总体统计
                $stats = $this->groupService->getOverallStats();
            }
            
            return $this->success($stats, '获取统计数据成功');
            
        } catch (\Exception $e) {
            Log::error('获取群组统计失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取统计数据失败');
        }
    }

    /**
     * 获取群组成员列表
     */
    public function members(Request $request, int $id): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'search' => 'string|max:255'
            ]);

            $members = $this->groupService->getGroupMembers($id, $params);
            
            return $this->success($members, '获取群组成员成功');
            
        } catch (\Exception $e) {
            Log::error('获取群组成员失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取群组成员失败');
        }
    }

    /**
     * 移除群组成员
     */
    public function removeMember(int $groupId, int $userId): JsonResponse
    {
        try {
            $result = $this->groupService->removeGroupMember($groupId, $userId);
            
            if (!$result) {
                return $this->error('成员不存在或移除失败', 404);
            }
            
            Log::info('移除群组成员成功', [
                'group_id' => $groupId,
                'user_id' => $userId,
                'operator_id' => auth()->id()
            ]);
            
            return $this->success(null, '成员移除成功');
            
        } catch (\Exception $e) {
            Log::error('移除群组成员失败', [
                'group_id' => $groupId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('移除成员失败：' . $e->getMessage());
        }
    }

    /**
     * 获取群组订单列表
     */
    public function orders(Request $request, int $id): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'status' => 'string|in:pending,paid,cancelled,refunded',
                'date_range' => 'array'
            ]);

            $orders = $this->groupService->getGroupOrders($id, $params);
            
            return $this->success($orders, '获取群组订单成功');
            
        } catch (\Exception $e) {
            Log::error('获取群组订单失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取群组订单失败');
        }
    }

    /**
     * 预览群组
     */
    public function preview(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:200',
                'description' => 'required|string|max:1000',
                'price' => 'required|numeric|min:0',
                'cover_image' => 'nullable|string',
                'city_location' => 'boolean',
                'city_insert_strategy' => 'string|in:auto,prefix,suffix,natural,none'
            ]);

            $preview = $this->groupService->generatePreview($data);
            
            return $this->success($preview, '预览生成成功');
            
        } catch (\Exception $e) {
            Log::error('群组预览失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return $this->error('预览生成失败：' . $e->getMessage());
        }
    }

    /**
     * 生成推广海报
     */
    public function generatePoster(Request $request, int $id): JsonResponse
    {
        try {
            $options = $request->validate([
                'template' => 'string|in:default,modern,simple',
                'include_qr' => 'boolean',
                'custom_text' => 'string|max:100'
            ]);

            $poster = $this->groupService->generatePoster($id, $options);
            
            if (!$poster) {
                return $this->error('群组不存在或海报生成失败', 404);
            }
            
            return $this->success([
                'poster_url' => $poster
            ], '推广海报生成成功');
            
        } catch (\Exception $e) {
            Log::error('生成推广海报失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('海报生成失败：' . $e->getMessage());
        }
    }

    /**
     * 复制群组
     */
    public function duplicate(int $id): JsonResponse
    {
        DB::beginTransaction();
        
        try {
            $newGroup = $this->groupService->duplicateGroup($id);
            
            if (!$newGroup) {
                return $this->error('群组不存在或复制失败', 404);
            }
            
            DB::commit();
            
            Log::info('群组复制成功', [
                'original_id' => $id,
                'new_id' => $newGroup->id,
                'user_id' => auth()->id()
            ]);
            
            return $this->success($newGroup, '群组复制成功');
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('群组复制失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('群组复制失败：' . $e->getMessage());
        }
    }

    /**
     * 导出群组数据
     */
    public function export(Request $request, int $id): JsonResponse
    {
        try {
            $format = $request->input('format', 'excel');
            
            $file = $this->groupService->exportGroupData($id, $format);
            
            if (!$file) {
                return $this->error('群组不存在或导出失败', 404);
            }
            
            return response()->download($file);
            
        } catch (\Exception $e) {
            Log::error('导出群组数据失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('数据导出失败：' . $e->getMessage());
        }
    }

    /**
     * 测试城市替换
     */
    public function testCityReplacement(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:200',
                'city' => 'required|string|max:50',
                'strategy' => 'required|string|in:auto,prefix,suffix,natural,none'
            ]);

            $result = $this->groupService->testCityReplacement(
                $data['title'],
                $data['city'],
                $data['strategy']
            );
            
            return $this->success([
                'result' => $result
            ], '城市替换测试成功');
            
        } catch (\Exception $e) {
            Log::error('城市替换测试失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return $this->error('测试失败：' . $e->getMessage());
        }
    }

    /**
     * 获取推荐设置
     */
    public function recommendedSettings(Request $request): JsonResponse
    {
        try {
            $category = $request->input('category');
            
            $settings = $this->groupService->getRecommendedSettings($category);
            
            return $this->success($settings, '获取推荐设置成功');
            
        } catch (\Exception $e) {
            Log::error('获取推荐设置失败', [
                'category' => $request->input('category'),
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取推荐设置失败');
        }
    }

    /**
     * 获取群组分析数据
     */
    public function analytics(Request $request, int $id): JsonResponse
    {
        try {
            $params = $request->validate([
                'date_range' => 'array',
                'metrics' => 'array',
                'group_by' => 'string|in:day,week,month'
            ]);

            $analytics = $this->groupService->getGroupAnalytics($id, $params);
            
            return $this->success($analytics, '获取分析数据成功');
            
        } catch (\Exception $e) {
            Log::error('获取群组分析数据失败', [
                'group_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取分析数据失败');
        }
    }
}