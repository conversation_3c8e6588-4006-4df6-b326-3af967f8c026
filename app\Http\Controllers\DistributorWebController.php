<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class DistributorWebController extends Controller
{
    /**
     * 显示分销员登录页面
     */
    public function showLogin()
    {
        return view('distributor.login');
    }

    /**
     * 显示分销员后台首页
     */
    public function dashboard()
    {
        return view('distributor.dashboard');
    }

    /**
     * 分销员登录处理
     */
    public function login(Request $request)
    {
        $request->validate([
            'account' => 'required|string',
            'password' => 'required|string',
        ]);

        $user = User::where('distributor_account', $request->account)
                   ->where('is_distributor', true)
                   ->where('distributor_status', 1)
                   ->first();

        if (!$user || !Hash::check($request->password, $user->distributor_password)) {
            return response()->json(['error' => '账号或密码错误'], 401);
        }

        // 记录登录日志
        $user->distributorLoginLogs()->create([
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'login_result' => 'success',
        ]);

        // 生成token
        $token = $user->createToken('distributor-web-token', ['distributor'])->plainTextToken;

        return response()->json([
            'success' => true,
            'token' => $token,
            'distributor' => [
                'id' => $user->id,
                'name' => $user->name,
                'account' => $user->distributor_account,
                'balance' => $user->distributor_balance,
            ]
        ]);
    }

    /**
     * 分销员退出登录
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        
        return response()->json(['success' => true]);
    }
}