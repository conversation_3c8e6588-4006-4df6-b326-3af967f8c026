/* 管理后台配色修复 - 解决白色背景上白色文字问题 */
/* ================================================== */

/* 1. Element UI 组件配色修复 */
/* ========================== */

// 卡片组件
.el-card {
  background: white !important;
  color: var(--text-primary) !important;
  
  .el-card__header {
    background: #f9fafb !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid #e5e7eb !important;
  }
  
  .el-card__body {
    color: var(--text-secondary) !important;
  }
}

// 表格组件
.el-table {
  background: white !important;
  color: var(--text-primary) !important;
  
  .el-table__header-wrapper {
    background: #f9fafb !important;
    
    th {
      background: #f9fafb !important;
      color: var(--text-primary) !important;
      font-weight: 600;
    }
  }
  
  .el-table__body-wrapper {
    td {
      color: var(--text-secondary) !important;
    }
  }
  
  .el-table__row {
    &:hover {
      background: #f9fafb !important;
      
      td {
        color: var(--text-primary) !important;
      }
    }
  }
}

// 表单组件
.el-form {
  .el-form-item__label {
    color: var(--text-primary) !important;
    font-weight: 500;
  }
  
  .el-input {
    .el-input__inner {
      background: white !important;
      color: var(--text-primary) !important;
      border: 1px solid #d1d5db !important;
      
      &::placeholder {
        color: #9ca3af !important;
      }
    }
  }
  
  .el-textarea {
    .el-textarea__inner {
      background: white !important;
      color: var(--text-primary) !important;
      border: 1px solid #d1d5db !important;
      
      &::placeholder {
        color: #9ca3af !important;
      }
    }
  }
  
  .el-select {
    .el-input__inner {
      background: white !important;
      color: var(--text-primary) !important;
    }
  }
}

// 按钮组件
.el-button {
  &.el-button--default {
    background: white !important;
    color: var(--text-primary) !important;
    border: 1px solid #d1d5db !important;
    
    &:hover {
      background: #f9fafb !important;
      color: var(--text-primary) !important;
    }
  }
  
  &.el-button--primary {
    background: var(--primary-600) !important;
    color: white !important;
    border-color: var(--primary-600) !important;
  }
  
  &.el-button--success {
    background: var(--success-600) !important;
    color: white !important;
    border-color: var(--success-600) !important;
  }
  
  &.el-button--warning {
    background: var(--warning-600) !important;
    color: white !important;
    border-color: var(--warning-600) !important;
  }
  
  &.el-button--danger {
    background: var(--error-600) !important;
    color: white !important;
    border-color: var(--error-600) !important;
  }
  
  &.el-button--info {
    background: var(--info-600) !important;
    color: white !important;
    border-color: var(--info-600) !important;
  }
}

// 对话框组件
.el-dialog {
  background: white !important;
  color: var(--text-primary) !important;
  
  .el-dialog__header {
    background: #f9fafb !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid #e5e7eb !important;
    
    .el-dialog__title {
      color: var(--text-primary) !important;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    color: var(--text-secondary) !important;
  }
  
  .el-dialog__footer {
    background: #f9fafb !important;
    border-top: 1px solid #e5e7eb !important;
  }
}

// 下拉菜单
.el-dropdown-menu {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  
  .el-dropdown-menu__item {
    color: var(--text-secondary) !important;
    
    &:hover {
      background: #f9fafb !important;
      color: var(--text-primary) !important;
    }
  }
}

// 分页组件
.el-pagination {
  color: var(--text-secondary) !important;
  
  .el-pager {
    li {
      background: white !important;
      color: var(--text-secondary) !important;
      border: 1px solid #d1d5db !important;
      
      &:hover {
        color: var(--primary-600) !important;
      }
      
      &.active {
        background: var(--primary-600) !important;
        color: white !important;
      }
    }
  }
  
  .btn-prev,
  .btn-next {
    background: white !important;
    color: var(--text-secondary) !important;
    border: 1px solid #d1d5db !important;
    
    &:hover {
      color: var(--primary-600) !important;
    }
  }
}

/* 2. 自定义组件配色修复 */
/* ===================== */

// 统计卡片
.stat-card,
.dashboard-card {
  background: white !important;
  color: var(--text-primary) !important;
  border: 1px solid #e5e7eb !important;
  
  .card-title {
    color: var(--text-primary) !important;
    font-weight: 600;
  }
  
  .card-subtitle {
    color: var(--text-secondary) !important;
  }
  
  .card-value {
    color: var(--text-primary) !important;
    font-weight: 700;
  }
  
  .card-change {
    font-weight: 600;
    
    &.positive {
      color: var(--success-600) !important;
    }
    
    &.negative {
      color: var(--error-600) !important;
    }
  }
}

// 现代按钮
.modern-btn {
  &.primary {
    background: var(--gradient-primary) !important;
    color: white !important;
  }
  
  &.secondary {
    background: white !important;
    color: var(--text-primary) !important;
    border: 1px solid #d1d5db !important;
    
    &:hover {
      background: #f9fafb !important;
    }
  }
  
  &.success {
    background: var(--gradient-success) !important;
    color: white !important;
  }
  
  &.warning {
    background: var(--gradient-warning) !important;
    color: white !important;
  }
  
  &.error {
    background: var(--gradient-error) !important;
    color: white !important;
  }
}

// 现代卡片
.modern-card {
  background: rgba(255, 255, 255, 0.95) !important;
  color: var(--text-primary) !important;
  
  .text-white {
    color: var(--text-primary) !important;
  }
  
  .text-light {
    color: var(--text-secondary) !important;
  }
  
  .text-muted {
    color: var(--text-tertiary) !important;
  }
}

/* 3. 全局配色修复 */
/* =============== */

// 白色背景上的文字修复
.bg-white,
[style*="background: white"],
[style*="background-color: white"],
[style*="background: #fff"],
[style*="background-color: #fff"] {
  color: var(--text-primary) !important;
  
  .text-white {
    color: var(--text-primary) !important;
  }
  
  .text-light {
    color: var(--text-secondary) !important;
  }
  
  .text-muted {
    color: var(--text-tertiary) !important;
  }
}

// 链接颜色
a {
  color: var(--primary-600) !important;
  
  &:hover {
    color: var(--primary-700) !important;
  }
}

// 面包屑导航
.el-breadcrumb {
  color: var(--text-tertiary) !important;
  
  .el-breadcrumb__item {
    color: var(--text-tertiary) !important;
    
    &:last-child {
      color: var(--text-primary) !important;
    }
    
    a {
      color: var(--primary-600) !important;
      
      &:hover {
        color: var(--primary-700) !important;
      }
    }
  }
}

// 标签页
.el-tabs {
  .el-tabs__item {
    color: var(--text-tertiary) !important;
    
    &.is-active {
      color: var(--primary-600) !important;
    }
    
    &:hover {
      color: var(--primary-600) !important;
    }
  }
  
  .el-tabs__content {
    color: var(--text-secondary) !important;
  }
}

/* 4. 特殊情况修复 */
/* =============== */

// 修复可能的内联样式问题
[style*="color: white"] {
  color: var(--text-primary) !important;
}

[style*="color: #fff"] {
  color: var(--text-primary) !important;
}

[style*="color: #ffffff"] {
  color: var(--text-primary) !important;
}

// 保持渐变背景上的白色文字
[style*="background: linear-gradient"] .text-white,
[style*="background-image: linear-gradient"] .text-white,
.gradient-bg .text-white {
  color: white !important;
}
