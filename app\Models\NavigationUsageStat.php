<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 导航使用统计模型
 */
class NavigationUsageStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'navigation_key', 'user_id', 'user_role', 'action',
        'context', 'occurred_at', 'ip_address', 'user_agent'
    ];

    protected $casts = [
        'context' => 'array',
        'occurred_at' => 'datetime',
    ];

    public $timestamps = false;

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联导航配置
     */
    public function navigationConfig(): BelongsTo
    {
        return $this->belongsTo(NavigationConfig::class, 'navigation_key', 'key');
    }

    /**
     * 获取热门导航统计
     */
    public static function getPopularNavigation($days = 30, $limit = 20)
    {
        return self::where('occurred_at', '>=', now()->subDays($days))
            ->selectRaw('navigation_key, COUNT(*) as visit_count, COUNT(DISTINCT user_id) as unique_users')
            ->groupBy('navigation_key')
            ->orderByDesc('visit_count')
            ->limit($limit)
            ->with('navigationConfig')
            ->get();
    }

    /**
     * 获取角色导航偏好统计
     */
    public static function getRoleNavigationStats($role, $days = 30)
    {
        return self::where('user_role', $role)
            ->where('occurred_at', '>=', now()->subDays($days))
            ->selectRaw('navigation_key, COUNT(*) as visit_count, COUNT(DISTINCT user_id) as unique_users')
            ->groupBy('navigation_key')
            ->orderByDesc('visit_count')
            ->with('navigationConfig')
            ->get();
    }

    /**
     * 获取用户活跃度统计
     */
    public static function getUserActivityStats($userId, $days = 30)
    {
        return self::where('user_id', $userId)
            ->where('occurred_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(occurred_at) as date, COUNT(*) as actions, COUNT(DISTINCT navigation_key) as unique_pages')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }
}