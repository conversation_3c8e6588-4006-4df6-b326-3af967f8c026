import{u as s,t as e}from"./index-DtXAftX0.js";import"./vue-vendor-Dy164gUc.js";import"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";function r(e){const r=s(),n=r.userInfo?.permissions||[];return Array.isArray(e)?e.some(s=>n.includes(s)):n.includes(e)}function n(n,o=null){const i=s(),t=o||i.userInfo?.role;if(!t)return!1;const{meta:u}=n;return!u||!(u.roles&&u.roles.length>0&&!u.roles.includes(t))&&(!(u.permissions&&u.permissions.length>0&&!r(u.permissions))&&e(n,t))}export{n as checkRoutePermission,r as hasPermission};
