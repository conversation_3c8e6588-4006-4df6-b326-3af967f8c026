<?php

namespace App\Events;

use App\Models\WechatGroup;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 内容优化完成事件
 * 当内容优化任务完成时触发，用于通知用户和记录结果
 */
class ContentOptimizationCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly WechatGroup $group,
        public readonly array $optimizationResults,
        public readonly float $executionTime
    ) {}

    /**
     * 获取事件应该广播的频道
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->group->user_id),
            new PrivateChannel('group.' . $this->group->id),
        ];
    }

    /**
     * 广播事件名称
     */
    public function broadcastAs(): string
    {
        return 'content.optimization.completed';
    }

    /**
     * 广播数据
     */
    public function broadcastWith(): array
    {
        return [
            'group' => [
                'id' => $this->group->id,
                'title' => $this->group->title,
                'category' => $this->group->category,
            ],
            'optimization_results' => [
                'total_optimizations' => $this->optimizationResults['summary']['total_optimizations'] ?? 0,
                'successful_optimizations' => $this->optimizationResults['summary']['successful_optimizations'] ?? 0,
                'improvement_score' => $this->optimizationResults['summary']['improvement_score'] ?? 0,
                'optimization_types' => $this->optimizationResults['summary']['optimization_types'] ?? [],
                'overall_success_rate' => $this->optimizationResults['summary']['overall_success_rate'] ?? 0,
            ],
            'execution_metrics' => [
                'execution_time' => $this->executionTime,
                'completed_at' => now()->toISOString(),
            ],
            'next_actions' => $this->generateNextActions(),
        ];
    }

    /**
     * 生成后续建议动作
     */
    private function generateNextActions(): array
    {
        $actions = [];
        
        $successRate = $this->optimizationResults['summary']['overall_success_rate'] ?? 0;
        $improvementScore = $this->optimizationResults['summary']['improvement_score'] ?? 0;
        
        if ($successRate < 50) {
            $actions[] = [
                'type' => 'review_failed_optimizations',
                'title' => '检查失败的优化项',
                'description' => '部分优化未能成功执行，建议手动检查并调整',
                'priority' => 'high',
            ];
        }
        
        if ($improvementScore > 10) {
            $actions[] = [
                'type' => 'monitor_performance',
                'title' => '监控性能变化',
                'description' => '优化效果显著，建议持续监控性能变化',
                'priority' => 'medium',
            ];
        }
        
        if ($improvementScore < 5) {
            $actions[] = [
                'type' => 'consider_manual_optimization',
                'title' => '考虑手动优化',
                'description' => '自动优化效果有限，可能需要人工干预',
                'priority' => 'medium',
            ];
        }
        
        // 总是建议A/B测试
        $actions[] = [
            'type' => 'create_ab_test',
            'title' => '创建A/B测试',
            'description' => '对优化后的内容进行A/B测试以验证效果',
            'priority' => 'low',
        ];
        
        return $actions;
    }
}