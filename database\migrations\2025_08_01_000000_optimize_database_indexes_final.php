<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * 数据库索引最终优化迁移
 * 基于深度代码-数据库匹配分析结果进行的索引优化
 */
return new class extends Migration
{
    /**
     * 执行迁移
     */
    public function up()
    {
        echo "🚀 开始执行数据库索引优化...\n";

        // 检查并添加orders表复合索引
        $this->addOrdersIndexes();

        // 检查并添加commission_logs表复合索引
        $this->addCommissionLogsIndexes();

        // 检查并添加wechat_groups表复合索引
        $this->addWechatGroupsIndexes();

        // 检查并添加users表复合索引
        $this->addUsersIndexes();

        // 检查并添加其他表的优化索引
        $this->addOtherIndexes();

        // 执行数据库表优化
        $this->optimizeTableStructures();

        echo "✅ 数据库索引优化完成！\n";
    }

    /**
     * 添加orders表索引
     */
    private function addOrdersIndexes()
    {
        Schema::table('orders', function (Blueprint $table) {
            // 用户订单状态查询优化
            if (!$this->indexExists('orders', 'idx_user_status_created')) {
                $table->index(['user_id', 'status', 'created_at'], 'idx_user_status_created');
            }
            
            // 分站订单状态查询优化
            if (!$this->indexExists('orders', 'idx_substation_status')) {
                $table->index(['substation_id', 'status'], 'idx_substation_status');
            }
            
            // 支付时间范围查询优化
            if (!$this->indexExists('orders', 'idx_paid_at_status')) {
                $table->index(['paid_at', 'status'], 'idx_paid_at_status');
            }
            
            // 微信群订单统计优化
            if (!$this->indexExists('orders', 'idx_wechat_group_status_created')) {
                $table->index(['wechat_group_id', 'status', 'created_at'], 'idx_wechat_group_status_created');
            }
        });
    }

    /**
     * 添加commission_logs表索引
     */
    private function addCommissionLogsIndexes()
    {
        Schema::table('commission_logs', function (Blueprint $table) {
            // 用户佣金状态查询优化
            if (!$this->indexExists('commission_logs', 'idx_user_status_created')) {
                $table->index(['user_id', 'status', 'created_at'], 'idx_user_status_created');
            }
            
            // 佣金类型统计优化
            if (!$this->indexExists('commission_logs', 'idx_type_status_created')) {
                $table->index(['type', 'status', 'created_at'], 'idx_type_status_created');
            }
            
            // 结算时间查询优化
            if (!$this->indexExists('commission_logs', 'idx_settled_at_status')) {
                $table->index(['settled_at', 'status'], 'idx_settled_at_status');
            }
        });
    }

    /**
     * 添加wechat_groups表索引
     */
    private function addWechatGroupsIndexes()
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 分站群组状态排序优化
            if (!$this->indexExists('wechat_groups', 'idx_substation_status_sort')) {
                $table->index(['substation_id', 'status', 'sort_order'], 'idx_substation_status_sort');
            }
            
            // 城市定位查询优化
            if (!$this->indexExists('wechat_groups', 'idx_city_status_price')) {
                $table->index(['city_location', 'status', 'price'], 'idx_city_status_price');
            }
            
            // 热门群组查询优化
            if (!$this->indexExists('wechat_groups', 'idx_hot_status_view')) {
                $table->index(['hot_display', 'status', 'view_count'], 'idx_hot_status_view');
            }
            
            // 模板使用统计优化
            if (!$this->indexExists('wechat_groups', 'idx_template_status')) {
                $table->index(['template_id', 'status'], 'idx_template_status');
            }
        });
    }

    /**
     * 添加users表索引
     */
    private function addUsersIndexes()
    {
        Schema::table('users', function (Blueprint $table) {
            // 角色状态查询优化
            if (!$this->indexExists('users', 'idx_role_status_created')) {
                $table->index(['role', 'status', 'created_at'], 'idx_role_status_created');
            }
            
            // 上级用户状态查询优化
            if (!$this->indexExists('users', 'idx_parent_status')) {
                $table->index(['parent_id', 'status'], 'idx_parent_status');
            }
            
            // 分销商等级查询优化
            if (!$this->indexExists('users', 'idx_distributor_level_status')) {
                $table->index(['distributor_level', 'status'], 'idx_distributor_level_status');
            }
            
            // 最后登录时间查询优化
            if (!$this->indexExists('users', 'idx_last_login_status')) {
                $table->index(['last_login_at', 'status'], 'idx_last_login_status');
            }
        });
    }

    /**
     * 添加其他表的优化索引
     */
    private function addOtherIndexes()
    {
        // transactions表索引优化
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                if (!$this->indexExists('transactions', 'idx_user_type_created')) {
                    $table->index(['user_id', 'type', 'created_at'], 'idx_user_type_created');
                }
                
                if (!$this->indexExists('transactions', 'idx_status_created')) {
                    $table->index(['status', 'created_at'], 'idx_status_created');
                }
            });
        }
        
        // balance_logs表索引优化
        if (Schema::hasTable('balance_logs')) {
            Schema::table('balance_logs', function (Blueprint $table) {
                if (!$this->indexExists('balance_logs', 'idx_user_type_created')) {
                    $table->index(['user_id', 'type', 'created_at'], 'idx_user_type_created');
                }
            });
        }
        
        // withdrawals表索引优化
        if (Schema::hasTable('withdrawals')) {
            Schema::table('withdrawals', function (Blueprint $table) {
                if (!$this->indexExists('withdrawals', 'idx_user_status_created')) {
                    $table->index(['user_id', 'status', 'created_at'], 'idx_user_status_created');
                }
            });
        }
    }

    /**
     * 优化数据库表结构
     */
    private function optimizeTableStructures()
    {
        echo "🔧 优化数据库表结构...\n";

        try {
            // 优化表结构
            $tables = ['users', 'orders', 'commission_logs', 'wechat_groups', 'substations', 'transactions'];

            foreach ($tables as $table) {
                if (Schema::hasTable($table)) {
                    DB::statement("OPTIMIZE TABLE {$table}");
                    DB::statement("ANALYZE TABLE {$table}");
                    echo "  ✅ 优化表: {$table}\n";
                }
            }

            // 更新表统计信息
            DB::statement('FLUSH TABLES');
            echo "  ✅ 刷新表缓存\n";

        } catch (\Exception $e) {
            echo "  ⚠️ 表优化警告: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 检查索引是否存在
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            return !empty($indexes);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        // 删除添加的索引
        $this->dropIndexIfExists('orders', 'idx_user_status_created');
        $this->dropIndexIfExists('orders', 'idx_substation_status');
        $this->dropIndexIfExists('orders', 'idx_paid_at_status');
        $this->dropIndexIfExists('orders', 'idx_wechat_group_status_created');
        
        $this->dropIndexIfExists('commission_logs', 'idx_user_status_created');
        $this->dropIndexIfExists('commission_logs', 'idx_type_status_created');
        $this->dropIndexIfExists('commission_logs', 'idx_settled_at_status');
        
        $this->dropIndexIfExists('wechat_groups', 'idx_substation_status_sort');
        $this->dropIndexIfExists('wechat_groups', 'idx_city_status_price');
        $this->dropIndexIfExists('wechat_groups', 'idx_hot_status_view');
        $this->dropIndexIfExists('wechat_groups', 'idx_template_status');
        
        $this->dropIndexIfExists('users', 'idx_role_status_created');
        $this->dropIndexIfExists('users', 'idx_parent_status');
        $this->dropIndexIfExists('users', 'idx_distributor_level_status');
        $this->dropIndexIfExists('users', 'idx_last_login_status');
        
        if (Schema::hasTable('transactions')) {
            $this->dropIndexIfExists('transactions', 'idx_user_type_created');
            $this->dropIndexIfExists('transactions', 'idx_status_created');
        }
        
        if (Schema::hasTable('balance_logs')) {
            $this->dropIndexIfExists('balance_logs', 'idx_user_type_created');
        }
        
        if (Schema::hasTable('withdrawals')) {
            $this->dropIndexIfExists('withdrawals', 'idx_user_status_created');
        }
    }

    /**
     * 安全删除索引
     */
    private function dropIndexIfExists(string $table, string $indexName): void
    {
        if ($this->indexExists($table, $indexName)) {
            Schema::table($table, function (Blueprint $table) use ($indexName) {
                $table->dropIndex($indexName);
            });
        }
    }
};
