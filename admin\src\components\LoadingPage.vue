<template>
  <div class="loading-page">
    <div class="loading-container">
      <!-- 现代化加载动画 -->
      <div class="loading-animation">
        <div class="loading-circles">
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>
          <div class="circle circle-3"></div>
          <div class="circle circle-4"></div>
        </div>
        
        <div class="loading-logo">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3b82f6"/>
                <stop offset="100%" style="stop-color:#8b5cf6"/>
              </linearGradient>
            </defs>
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" 
                  stroke="url(#logoGradient)" 
                  stroke-width="2" 
                  fill="none" 
                  stroke-linecap="round" 
                  stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 加载文本 -->
      <div class="loading-content">
        <h1 class="loading-title">{{ loadingTitle }}</h1>
        <p class="loading-message">{{ loadingMessage }}</p>
        
        <!-- 进度条 -->
        <div v-if="showProgress" class="loading-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <div class="progress-text">{{ progress }}%</div>
        </div>

        <!-- 加载步骤 -->
        <div v-if="steps.length > 0" class="loading-steps">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="step-item"
            :class="{ 
              'active': index === currentStep, 
              'completed': index < currentStep,
              'pending': index > currentStep 
            }"
          >
            <div class="step-icon">
              <el-icon v-if="index < currentStep"><Check /></el-icon>
              <el-icon v-else-if="index === currentStep" class="spin"><Loading /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-text">{{ step }}</span>
          </div>
        </div>

        <!-- 取消按钮 -->
        <div v-if="showCancel" class="loading-actions">
          <el-button @click="handleCancel" size="small">
            取消加载
          </el-button>
        </div>
      </div>

      <!-- 装饰性元素 -->
      <div class="loading-decorations">
        <div class="decoration decoration-1"></div>
        <div class="decoration decoration-2"></div>
        <div class="decoration decoration-3"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Check, Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 加载标题
  title: {
    type: String,
    default: '系统加载中'
  },
  // 加载消息
  message: {
    type: String,
    default: '正在为您准备最佳的体验，请稍候...'
  },
  // 是否显示进度条
  showProgress: {
    type: Boolean,
    default: false
  },
  // 当前进度 (0-100)
  progress: {
    type: Number,
    default: 0
  },
  // 加载步骤
  steps: {
    type: Array,
    default: () => []
  },
  // 当前步骤
  currentStep: {
    type: Number,
    default: 0
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: false
  },
  // 加载类型
  type: {
    type: String,
    default: 'default', // default, init, upload, download, processing
    validator: (value) => ['default', 'init', 'upload', 'download', 'processing'].includes(value)
  }
})

// Emits
const emit = defineEmits(['cancel'])

// 计算属性
const loadingTitle = computed(() => {
  const titleMap = {
    init: '系统初始化',
    upload: '文件上传中',
    download: '文件下载中',
    processing: '数据处理中',
    default: props.title
  }
  return titleMap[props.type] || props.title
})

const loadingMessage = computed(() => {
  const messageMap = {
    init: '正在初始化系统组件，请稍候...',
    upload: '正在上传文件，请保持网络连接...',
    download: '正在下载文件，请稍候...',
    processing: '正在处理您的请求，请稍候...',
    default: props.message
  }
  return messageMap[props.type] || props.message
})

// 方法
const handleCancel = () => {
  emit('cancel')
}

// 自动更新文本动画
const textAnimation = ref('')
const animationTimer = ref(null)

const startTextAnimation = () => {
  let dots = 0
  animationTimer.value = setInterval(() => {
    dots = (dots + 1) % 4
    textAnimation.value = '.'.repeat(dots)
  }, 500)
}

const stopTextAnimation = () => {
  if (animationTimer.value) {
    clearInterval(animationTimer.value)
    animationTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  startTextAnimation()
})

onUnmounted(() => {
  stopTextAnimation()
})
</script>

<style scoped>
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-container {
  position: relative;
  text-align: center;
  z-index: 10;
}

/* 加载动画 */
.loading-animation {
  position: relative;
  margin-bottom: 40px;
}

.loading-circles {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
}

.circle {
  position: absolute;
  border-radius: 50%;
  border: 3px solid transparent;
  animation: spin 2s linear infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  border-top-color: rgba(255, 255, 255, 0.8);
  animation-delay: 0s;
}

.circle-2 {
  width: 90px;
  height: 90px;
  top: 15px;
  left: 15px;
  border-right-color: rgba(255, 255, 255, 0.6);
  animation-delay: -0.5s;
  animation-direction: reverse;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 30px;
  left: 30px;
  border-bottom-color: rgba(255, 255, 255, 0.4);
  animation-delay: -1s;
}

.circle-4 {
  width: 30px;
  height: 30px;
  top: 45px;
  left: 45px;
  border-left-color: rgba(255, 255, 255, 0.2);
  animation-delay: -1.5s;
  animation-direction: reverse;
}

.loading-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  animation: pulse 2s ease-in-out infinite;
}

.loading-logo svg {
  width: 100%;
  height: 100%;
}

/* 加载内容 */
.loading-content {
  max-width: 400px;
}

.loading-title {
  color: white;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.loading-message {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 0 0 30px 0;
  line-height: 1.5;
}

/* 进度条 */
.loading-progress {
  margin: 24px 0;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(90deg, transparent 0%, white 50%, transparent 100%);
  animation: shimmer 1.5s ease-in-out infinite;
}

.progress-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

/* 加载步骤 */
.loading-steps {
  margin: 24px 0;
  text-align: left;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.step-item.active {
  color: white;
}

.step-item.completed {
  color: rgba(255, 255, 255, 0.8);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-item.active .step-icon {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

.step-item.completed .step-icon {
  background: rgba(76, 175, 80, 0.8);
  color: white;
}

.step-text {
  font-size: 14px;
}

/* 操作按钮 */
.loading-actions {
  margin-top: 32px;
}

.loading-actions .el-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.loading-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 装饰性元素 */
.loading-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.decoration-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  right: 15%;
  animation-delay: -2s;
}

.decoration-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  left: 80%;
  animation-delay: -4s;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }
  33% {
    transform: translateY(-20px) translateX(10px) scale(1.05);
  }
  66% {
    transform: translateY(10px) translateX(-10px) scale(0.95);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container {
    padding: 20px;
  }

  .loading-circles {
    width: 100px;
    height: 100px;
  }

  .circle-1 {
    width: 100px;
    height: 100px;
  }

  .circle-2 {
    width: 75px;
    height: 75px;
    top: 12.5px;
    left: 12.5px;
  }

  .circle-3 {
    width: 50px;
    height: 50px;
    top: 25px;
    left: 25px;
  }

  .circle-4 {
    width: 25px;
    height: 25px;
    top: 37.5px;
    left: 37.5px;
  }

  .loading-logo {
    width: 30px;
    height: 30px;
  }

  .loading-title {
    font-size: 24px;
  }

  .loading-message {
    font-size: 14px;
  }

  .decoration {
    display: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .loading-page {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
}
</style>