/**
 * 性能优化配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  // 默认缓存时间（毫秒）
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  
  // 不同数据类型的缓存时间
  TTL: {
    CUSTOMER_LIST: 3 * 60 * 1000,      // 客户列表：3分钟
    CUSTOMER_DETAIL: 5 * 60 * 1000,    // 客户详情：5分钟
    STATS: 2 * 60 * 1000,              // 统计数据：2分钟
    COMMISSION: 10 * 60 * 1000,        // 佣金数据：10分钟
    ORDERS: 1 * 60 * 1000,             // 订单数据：1分钟
    PROMOTION_LINKS: 5 * 60 * 1000     // 推广链接：5分钟
  },
  
  // 缓存大小限制
  MAX_SIZE: 100, // 最大缓存条目数
  
  // 缓存键前缀
  KEY_PREFIX: 'distributor_cache_'
}

/**
 * 分页配置
 */
export const PAGINATION_CONFIG = {
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 20,
  
  // 可选的每页大小
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  
  // 预加载页数
  PRELOAD_PAGES: 1
}

/**
 * 防抖配置
 */
export const DEBOUNCE_CONFIG = {
  // 搜索防抖延迟
  SEARCH_DELAY: 500,
  
  // 输入防抖延迟
  INPUT_DELAY: 300,
  
  // 滚动防抖延迟
  SCROLL_DELAY: 100,
  
  // 窗口大小变化防抖延迟
  RESIZE_DELAY: 200
}

/**
 * 节流配置
 */
export const THROTTLE_CONFIG = {
  // API请求节流间隔
  API_REQUEST_INTERVAL: 100,
  
  // 按钮点击节流间隔
  BUTTON_CLICK_INTERVAL: 1000,
  
  // 滚动事件节流间隔
  SCROLL_INTERVAL: 16, // 约60fps
  
  // 鼠标移动节流间隔
  MOUSE_MOVE_INTERVAL: 16
}

/**
 * 懒加载配置
 */
export const LAZY_LOAD_CONFIG = {
  // 图片懒加载阈值
  IMAGE_THRESHOLD: 100,
  
  // 组件懒加载阈值
  COMPONENT_THRESHOLD: 200,
  
  // 数据懒加载阈值
  DATA_THRESHOLD: 300,
  
  // 预加载距离
  PRELOAD_DISTANCE: 500
}

/**
 * 虚拟滚动配置
 */
export const VIRTUAL_SCROLL_CONFIG = {
  // 启用虚拟滚动的最小条目数
  MIN_ITEMS: 100,
  
  // 默认条目高度
  ITEM_HEIGHT: 60,
  
  // 缓冲区大小
  BUFFER_SIZE: 10,
  
  // 预渲染条目数
  PRERENDER_COUNT: 5
}

/**
 * 网络请求配置
 */
export const REQUEST_CONFIG = {
  // 请求超时时间
  TIMEOUT: 30000, // 30秒
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 重试延迟
  RETRY_DELAY: 1000,
  
  // 并发请求限制
  CONCURRENT_LIMIT: 6,
  
  // 请求队列大小
  QUEUE_SIZE: 50
}

/**
 * 内存管理配置
 */
export const MEMORY_CONFIG = {
  // 内存使用警告阈值（MB）
  WARNING_THRESHOLD: 100,
  
  // 内存使用限制阈值（MB）
  LIMIT_THRESHOLD: 200,
  
  // 垃圾回收间隔（毫秒）
  GC_INTERVAL: 60000, // 1分钟
  
  // 内存监控间隔（毫秒）
  MONITOR_INTERVAL: 10000 // 10秒
}

/**
 * 性能监控配置
 */
export const MONITOR_CONFIG = {
  // 是否启用性能监控
  ENABLED: process.env.NODE_ENV === 'development',
  
  // 监控间隔
  INTERVAL: 5000, // 5秒
  
  // 性能指标阈值
  THRESHOLDS: {
    FPS: 30,           // 最低FPS
    MEMORY: 100,       // 内存使用上限（MB）
    LOAD_TIME: 3000,   // 页面加载时间上限（毫秒）
    API_TIME: 5000     // API响应时间上限（毫秒）
  },
  
  // 采样率
  SAMPLE_RATE: 0.1, // 10%
  
  // 数据保留时间
  RETENTION_TIME: 24 * 60 * 60 * 1000 // 24小时
}

/**
 * 组件优化配置
 */
export const COMPONENT_CONFIG = {
  // 组件更新策略
  UPDATE_STRATEGY: {
    // 是否启用浅比较
    SHALLOW_COMPARE: true,
    
    // 是否启用记忆化
    MEMOIZATION: true,
    
    // 是否启用虚拟DOM优化
    VIRTUAL_DOM_OPTIMIZATION: true
  },
  
  // 组件缓存配置
  CACHE: {
    // 最大缓存组件数
    MAX_CACHED_COMPONENTS: 20,
    
    // 缓存策略
    STRATEGY: 'LRU', // LRU, LFU, FIFO
    
    // 缓存时间
    TTL: 10 * 60 * 1000 // 10分钟
  }
}

/**
 * 数据处理配置
 */
export const DATA_PROCESSING_CONFIG = {
  // 批处理大小
  BATCH_SIZE: 100,
  
  // 批处理间隔
  BATCH_INTERVAL: 50,
  
  // 数据转换并发数
  TRANSFORM_CONCURRENCY: 4,
  
  // 大数据集处理阈值
  LARGE_DATASET_THRESHOLD: 1000,
  
  // 分片大小
  CHUNK_SIZE: 500
}

/**
 * 图表优化配置
 */
export const CHART_CONFIG = {
  // 数据点数量限制
  MAX_DATA_POINTS: 1000,
  
  // 动画持续时间
  ANIMATION_DURATION: 300,
  
  // 是否启用硬件加速
  HARDWARE_ACCELERATION: true,
  
  // 渲染策略
  RENDER_STRATEGY: 'canvas', // canvas, svg, webgl
  
  // 数据采样策略
  SAMPLING_STRATEGY: 'LTTB' // LTTB, average, min-max
}

/**
 * 预加载配置
 */
export const PRELOAD_CONFIG = {
  // 预加载策略
  STRATEGY: 'intersection', // intersection, viewport, manual
  
  // 预加载优先级
  PRIORITY: {
    CRITICAL: 1,    // 关键资源
    HIGH: 2,        // 高优先级
    NORMAL: 3,      // 普通优先级
    LOW: 4          // 低优先级
  },
  
  // 预加载资源类型
  RESOURCE_TYPES: {
    IMAGES: true,
    COMPONENTS: true,
    DATA: true,
    SCRIPTS: false
  }
}

/**
 * 错误处理配置
 */
export const ERROR_HANDLING_CONFIG = {
  // 错误重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000,
    BACKOFF_FACTOR: 2
  },
  
  // 错误边界配置
  BOUNDARY: {
    FALLBACK_COMPONENT: 'ErrorFallback',
    LOG_ERRORS: true,
    REPORT_ERRORS: process.env.NODE_ENV === 'production'
  },
  
  // 错误恢复策略
  RECOVERY: {
    AUTO_RECOVERY: true,
    RECOVERY_DELAY: 5000,
    MAX_RECOVERY_ATTEMPTS: 2
  }
}

/**
 * 开发环境优化配置
 */
export const DEV_CONFIG = {
  // 是否启用热重载优化
  HOT_RELOAD_OPTIMIZATION: true,
  
  // 是否启用源码映射优化
  SOURCE_MAP_OPTIMIZATION: true,
  
  // 是否启用开发工具
  DEV_TOOLS: true,
  
  // 调试模式
  DEBUG_MODE: process.env.NODE_ENV === 'development'
}

/**
 * 生产环境优化配置
 */
export const PROD_CONFIG = {
  // 代码分割策略
  CODE_SPLITTING: {
    STRATEGY: 'route', // route, component, manual
    CHUNK_SIZE_LIMIT: 244 * 1024, // 244KB
    MAX_CHUNKS: 30
  },
  
  // 资源压缩配置
  COMPRESSION: {
    GZIP: true,
    BROTLI: true,
    LEVEL: 6
  },
  
  // 缓存策略
  CACHING: {
    STATIC_ASSETS: '1y',
    API_RESPONSES: '5m',
    HTML: 'no-cache'
  }
}

/**
 * 获取性能配置
 * @param {string} key - 配置键
 * @returns {any} 配置值
 */
export function getPerformanceConfig(key) {
  const configs = {
    cache: CACHE_CONFIG,
    pagination: PAGINATION_CONFIG,
    debounce: DEBOUNCE_CONFIG,
    throttle: THROTTLE_CONFIG,
    lazyLoad: LAZY_LOAD_CONFIG,
    virtualScroll: VIRTUAL_SCROLL_CONFIG,
    request: REQUEST_CONFIG,
    memory: MEMORY_CONFIG,
    monitor: MONITOR_CONFIG,
    component: COMPONENT_CONFIG,
    dataProcessing: DATA_PROCESSING_CONFIG,
    chart: CHART_CONFIG,
    preload: PRELOAD_CONFIG,
    errorHandling: ERROR_HANDLING_CONFIG,
    dev: DEV_CONFIG,
    prod: PROD_CONFIG
  }
  
  return configs[key] || null
}

/**
 * 性能优化工具类
 */
export class PerformanceOptimizer {
  constructor() {
    this.metrics = new Map()
    this.observers = new Map()
    this.timers = new Map()
  }
  
  /**
   * 开始性能测量
   * @param {string} name - 测量名称
   */
  startMeasure(name) {
    this.metrics.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    })
  }
  
  /**
   * 结束性能测量
   * @param {string} name - 测量名称
   * @returns {number} 持续时间
   */
  endMeasure(name) {
    const metric = this.metrics.get(name)
    if (metric) {
      metric.endTime = performance.now()
      metric.duration = metric.endTime - metric.startTime
      return metric.duration
    }
    return 0
  }
  
  /**
   * 获取性能指标
   * @param {string} name - 指标名称
   * @returns {object} 性能指标
   */
  getMetric(name) {
    return this.metrics.get(name)
  }
  
  /**
   * 清除性能指标
   * @param {string} name - 指标名称
   */
  clearMetric(name) {
    this.metrics.delete(name)
  }
  
  /**
   * 获取所有性能指标
   * @returns {Map} 所有指标
   */
  getAllMetrics() {
    return new Map(this.metrics)
  }
  
  /**
   * 清除所有性能指标
   */
  clearAllMetrics() {
    this.metrics.clear()
  }
  
  /**
   * 监控内存使用
   * @param {Function} callback - 回调函数
   */
  monitorMemory(callback) {
    if (performance.memory) {
      const checkMemory = () => {
        const memInfo = {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        }
        callback(memInfo)
      }
      
      const timer = setInterval(checkMemory, MEMORY_CONFIG.MONITOR_INTERVAL)
      this.timers.set('memory', timer)
      
      // 立即执行一次
      checkMemory()
    }
  }
  
  /**
   * 停止内存监控
   */
  stopMemoryMonitor() {
    const timer = this.timers.get('memory')
    if (timer) {
      clearInterval(timer)
      this.timers.delete('memory')
    }
  }
  
  /**
   * 监控FPS
   * @param {Function} callback - 回调函数
   */
  monitorFPS(callback) {
    let lastTime = performance.now()
    let frames = 0
    
    const measureFPS = () => {
      frames++
      const currentTime = performance.now()
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime))
        callback(fps)
        
        frames = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(measureFPS)
    }
    
    requestAnimationFrame(measureFPS)
  }
  
  /**
   * 优化图片加载
   * @param {HTMLImageElement} img - 图片元素
   * @param {string} src - 图片源
   * @param {object} options - 选项
   */
  optimizeImageLoading(img, src, options = {}) {
    const {
      lazy = true,
      placeholder = '',
      quality = 80,
      format = 'webp'
    } = options
    
    if (lazy) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const target = entry.target
            target.src = src
            observer.unobserve(target)
          }
        })
      }, {
        threshold: 0.1,
        rootMargin: `${LAZY_LOAD_CONFIG.IMAGE_THRESHOLD}px`
      })
      
      if (placeholder) {
        img.src = placeholder
      }
      
      observer.observe(img)
      this.observers.set(img, observer)
    } else {
      img.src = src
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    // 清理定时器
    this.timers.forEach(timer => clearInterval(timer))
    this.timers.clear()
    
    // 清理观察者
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    
    // 清理指标
    this.clearAllMetrics()
  }
}

// 创建全局性能优化器实例
export const performanceOptimizer = new PerformanceOptimizer()

// 在开发环境下启用性能监控
if (MONITOR_CONFIG.ENABLED) {
  // 监控内存使用
  performanceOptimizer.monitorMemory((memInfo) => {
    if (memInfo.used > MEMORY_CONFIG.WARNING_THRESHOLD) {
      console.warn(`内存使用过高: ${memInfo.used}MB / ${memInfo.limit}MB`)
    }
  })
  
  // 监控FPS
  performanceOptimizer.monitorFPS((fps) => {
    if (fps < MONITOR_CONFIG.THRESHOLDS.FPS) {
      console.warn(`FPS过低: ${fps}`)
    }
  })
}

export default {
  CACHE_CONFIG,
  PAGINATION_CONFIG,
  DEBOUNCE_CONFIG,
  THROTTLE_CONFIG,
  LAZY_LOAD_CONFIG,
  VIRTUAL_SCROLL_CONFIG,
  REQUEST_CONFIG,
  MEMORY_CONFIG,
  MONITOR_CONFIG,
  COMPONENT_CONFIG,
  DATA_PROCESSING_CONFIG,
  CHART_CONFIG,
  PRELOAD_CONFIG,
  ERROR_HANDLING_CONFIG,
  DEV_CONFIG,
  PROD_CONFIG,
  getPerformanceConfig,
  PerformanceOptimizer,
  performanceOptimizer
}