/**
 * 数据大屏屏幕适配工具
 * 用于处理不同屏幕尺寸下的数据大屏显示优化
 */

export class ScreenAdapter {
  constructor() {
    this.breakpoints = {
      xs: 480,
      sm: 768,
      md: 1024,
      lg: 1200,
      xl: 1400,
      xxl: 1600,
      xxxl: 1920,
      xxxxl: 2560
    }
    
    this.currentBreakpoint = this.getCurrentBreakpoint()
    this.isFullscreen = false
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this))
    document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
  }

  /**
   * 获取当前屏幕断点
   */
  getCurrentBreakpoint() {
    const width = window.innerWidth
    
    if (width >= this.breakpoints.xxxxl) return 'xxxxl'
    if (width >= this.breakpoints.xxxl) return 'xxxl'
    if (width >= this.breakpoints.xxl) return 'xxl'
    if (width >= this.breakpoints.xl) return 'xl'
    if (width >= this.breakpoints.lg) return 'lg'
    if (width >= this.breakpoints.md) return 'md'
    if (width >= this.breakpoints.sm) return 'sm'
    return 'xs'
  }

  /**
   * 获取适配的布局配置
   */
  getLayoutConfig() {
    const configs = {
      xxxxl: { // 4K显示器
        metricsColumns: 4,
        chartsColumns: '2fr 1fr 1fr',
        cardHeight: 450,
        padding: '32px 80px',
        gap: 32,
        fontSize: 18
      },
      xxxl: { // 1920px+
        metricsColumns: 4,
        chartsColumns: '2fr 1fr 1fr',
        cardHeight: 400,
        padding: '28px 60px',
        gap: 28,
        fontSize: 16
      },
      xxl: { // 1600px+
        metricsColumns: 4,
        chartsColumns: '2fr 1fr 1fr',
        cardHeight: 350,
        padding: '24px 40px',
        gap: 24,
        fontSize: 16
      },
      xl: { // 1400px+
        metricsColumns: 4,
        chartsColumns: '2fr 1fr 1fr',
        cardHeight: 320,
        padding: '20px 32px',
        gap: 20,
        fontSize: 15
      },
      lg: { // 1200px+
        metricsColumns: 2,
        chartsColumns: '1fr',
        cardHeight: 280,
        padding: '18px 28px',
        gap: 18,
        fontSize: 14
      },
      md: { // 1024px+
        metricsColumns: 2,
        chartsColumns: '1fr',
        cardHeight: 250,
        padding: '16px 24px',
        gap: 16,
        fontSize: 14
      },
      sm: { // 768px+
        metricsColumns: 2,
        chartsColumns: '1fr',
        cardHeight: 220,
        padding: '12px 16px',
        gap: 12,
        fontSize: 13
      },
      xs: { // <768px
        metricsColumns: 1,
        chartsColumns: '1fr',
        cardHeight: 200,
        padding: '8px 12px',
        gap: 8,
        fontSize: 12
      }
    }
    
    return configs[this.currentBreakpoint] || configs.lg
  }

  /**
   * 应用适配样式
   */
  applyAdaptiveStyles(element) {
    if (!element) return
    
    const config = this.getLayoutConfig()
    const root = element.querySelector('.optimized-data-screen') || element
    
    // 设置CSS变量
    root.style.setProperty('--metrics-columns', config.metricsColumns)
    root.style.setProperty('--charts-columns', config.chartsColumns)
    root.style.setProperty('--card-height', `${config.cardHeight}px`)
    root.style.setProperty('--content-padding', config.padding)
    root.style.setProperty('--grid-gap', `${config.gap}px`)
    root.style.setProperty('--base-font-size', `${config.fontSize}px`)
    
    // 添加断点类名
    root.className = root.className.replace(/\s*breakpoint-\w+/g, '')
    root.classList.add(`breakpoint-${this.currentBreakpoint}`)
    
    // 全屏模式处理
    if (this.isFullscreen) {
      root.classList.add('fullscreen-mode')
    } else {
      root.classList.remove('fullscreen-mode')
    }
  }

  /**
   * 切换全屏模式
   */
  async toggleFullscreen() {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (error) {
      console.error('全屏切换失败:', error)
    }
  }

  /**
   * 获取图表自适应配置
   */
  getChartConfig() {
    const config = this.getLayoutConfig()
    
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: this.currentBreakpoint !== 'xs',
          labels: {
            color: '#e2e8f0',
            font: {
              size: Math.max(10, config.fontSize - 2)
            }
          }
        }
      },
      scales: {
        x: {
          grid: { 
            color: 'rgba(255, 255, 255, 0.1)',
            display: this.currentBreakpoint !== 'xs'
          },
          ticks: { 
            color: '#e2e8f0',
            font: {
              size: Math.max(10, config.fontSize - 2)
            }
          }
        },
        y: {
          grid: { 
            color: 'rgba(255, 255, 255, 0.1)',
            display: this.currentBreakpoint !== 'xs'
          },
          ticks: { 
            color: '#e2e8f0',
            font: {
              size: Math.max(10, config.fontSize - 2)
            }
          }
        }
      }
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    const newBreakpoint = this.getCurrentBreakpoint()
    if (newBreakpoint !== this.currentBreakpoint) {
      this.currentBreakpoint = newBreakpoint
      this.onBreakpointChange?.(newBreakpoint)
    }
  }

  /**
   * 处理全屏状态变化
   */
  handleFullscreenChange() {
    this.isFullscreen = !!document.fullscreenElement
    this.onFullscreenChange?.(this.isFullscreen)
  }

  /**
   * 设置断点变化回调
   */
  onBreakpointChange(callback) {
    this.onBreakpointChange = callback
  }

  /**
   * 设置全屏变化回调
   */
  onFullscreenChange(callback) {
    this.onFullscreenChange = callback
  }

  /**
   * 销毁适配器
   */
  destroy() {
    window.removeEventListener('resize', this.handleResize.bind(this))
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this))
  }
}

/**
 * Vue 3 Composition API 钩子
 */
export function useScreenAdapter() {
  const adapter = new ScreenAdapter()
  
  const currentBreakpoint = ref(adapter.currentBreakpoint)
  const isFullscreen = ref(adapter.isFullscreen)
  
  // 设置回调
  adapter.onBreakpointChange = (breakpoint) => {
    currentBreakpoint.value = breakpoint
  }
  
  adapter.onFullscreenChange = (fullscreen) => {
    isFullscreen.value = fullscreen
  }
  
  onUnmounted(() => {
    adapter.destroy()
  })
  
  return {
    adapter,
    currentBreakpoint,
    isFullscreen,
    toggleFullscreen: () => adapter.toggleFullscreen(),
    getLayoutConfig: () => adapter.getLayoutConfig(),
    getChartConfig: () => adapter.getChartConfig()
  }
}

/**
 * 数字格式化工具
 */
export const formatters = {
  number: (num) => {
    if (num >= 100000000) return (num / 100000000).toFixed(1) + '亿'
    if (num >= 10000) return (num / 10000).toFixed(1) + 'w'
    return num.toLocaleString()
  },
  
  currency: (num) => {
    return '¥' + formatters.number(num)
  },
  
  percentage: (num) => {
    return num.toFixed(1) + '%'
  }
}
