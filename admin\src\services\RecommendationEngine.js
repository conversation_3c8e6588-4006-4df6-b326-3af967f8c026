/**
 * 智能推荐引擎
 * 基于用户行为、群组数据和机器学习算法提供个性化推荐
 */

class RecommendationEngine {
  constructor() {
    this.userProfiles = new Map()
    this.groupFeatures = new Map()
    this.behaviorPatterns = new Map()
    this.recommendations = new Map()
  }

  /**
   * 分析用户行为模式
   */
  analyzeUserBehavior(userId, behaviorData) {
    const profile = this.getUserProfile(userId)
    
    // 更新用户兴趣标签
    this.updateInterestTags(profile, behaviorData)
    
    // 分析浏览模式
    this.analyzeBrowsingPattern(profile, behaviorData)
    
    // 计算用户价值分数
    this.calculateUserValueScore(profile, behaviorData)
    
    // 预测用户偏好
    this.predictUserPreferences(profile)
    
    this.userProfiles.set(userId, profile)
    
    return profile
  }

  /**
   * 获取用户画像
   */
  getUserProfile(userId) {
    if (!this.userProfiles.has(userId)) {
      this.userProfiles.set(userId, {
        id: userId,
        interestTags: [],
        browsingPattern: {
          preferredTime: [],
          sessionDuration: 0,
          pageDepth: 0,
          returnFrequency: 0
        },
        valueScore: 0,
        preferences: {
          priceRange: [0, 1000],
          categories: [],
          contentTypes: []
        },
        conversionProbability: 0,
        lastUpdated: new Date()
      })
    }
    return this.userProfiles.get(userId)
  }

  /**
   * 更新用户兴趣标签
   */
  updateInterestTags(profile, behaviorData) {
    const { viewedGroups, searchKeywords, clickedCategories } = behaviorData
    
    // 基于浏览的群组提取标签
    if (viewedGroups) {
      viewedGroups.forEach(group => {
        if (group.tags) {
          group.tags.forEach(tag => {
            this.addInterestTag(profile, tag, 0.3)
          })
        }
        
        // 基于群组类别
        if (group.category) {
          this.addInterestTag(profile, group.category, 0.5)
        }
      })
    }
    
    // 基于搜索关键词
    if (searchKeywords) {
      searchKeywords.forEach(keyword => {
        this.addInterestTag(profile, keyword, 0.7)
      })
    }
    
    // 基于点击的分类
    if (clickedCategories) {
      clickedCategories.forEach(category => {
        this.addInterestTag(profile, category, 0.8)
      })
    }
  }

  /**
   * 添加兴趣标签
   */
  addInterestTag(profile, tag, weight) {
    const existingTag = profile.interestTags.find(t => t.name === tag)
    
    if (existingTag) {
      existingTag.weight = Math.min(1.0, existingTag.weight + weight * 0.1)
      existingTag.count += 1
    } else {
      profile.interestTags.push({
        name: tag,
        weight: weight * 0.1,
        count: 1,
        firstSeen: new Date()
      })
    }
    
    // 保持标签数量在合理范围内
    profile.interestTags.sort((a, b) => b.weight - a.weight)
    if (profile.interestTags.length > 20) {
      profile.interestTags = profile.interestTags.slice(0, 20)
    }
  }

  /**
   * 分析浏览模式
   */
  analyzeBrowsingPattern(profile, behaviorData) {
    const { sessionStart, sessionEnd, pagesViewed, timeSpent } = behaviorData
    
    if (sessionStart && sessionEnd) {
      const sessionDuration = (sessionEnd - sessionStart) / 1000 / 60 // 分钟
      profile.browsingPattern.sessionDuration = 
        (profile.browsingPattern.sessionDuration + sessionDuration) / 2
      
      // 分析偏好时间段
      const hour = new Date(sessionStart).getHours()
      this.updatePreferredTime(profile, hour)
    }
    
    if (pagesViewed) {
      profile.browsingPattern.pageDepth = 
        Math.max(profile.browsingPattern.pageDepth, pagesViewed)
    }
  }

  /**
   * 更新偏好时间段
   */
  updatePreferredTime(profile, hour) {
    const timeSlot = Math.floor(hour / 4) // 将24小时分为6个时段
    const existing = profile.browsingPattern.preferredTime.find(t => t.slot === timeSlot)
    
    if (existing) {
      existing.count += 1
    } else {
      profile.browsingPattern.preferredTime.push({
        slot: timeSlot,
        count: 1
      })
    }
  }

  /**
   * 计算用户价值分数
   */
  calculateUserValueScore(profile, behaviorData) {
    let score = 0
    
    // 基于浏览深度
    score += Math.min(profile.browsingPattern.pageDepth * 10, 30)
    
    // 基于会话时长
    score += Math.min(profile.browsingPattern.sessionDuration * 2, 20)
    
    // 基于兴趣标签数量和权重
    const tagScore = profile.interestTags.reduce((sum, tag) => sum + tag.weight, 0)
    score += Math.min(tagScore * 10, 25)
    
    // 基于历史转化
    if (behaviorData.hasConverted) {
      score += 25
    }
    
    profile.valueScore = Math.min(score, 100)
  }

  /**
   * 预测用户偏好
   */
  predictUserPreferences(profile) {
    // 基于兴趣标签预测价格范围
    const highValueTags = ['VIP', '高端', '专业', '精英']
    const hasHighValueInterest = profile.interestTags.some(tag => 
      highValueTags.some(hvTag => tag.name.includes(hvTag))
    )
    
    if (hasHighValueInterest) {
      profile.preferences.priceRange = [100, 2000]
    } else {
      profile.preferences.priceRange = [0, 500]
    }
    
    // 预测偏好分类
    profile.preferences.categories = profile.interestTags
      .filter(tag => tag.weight > 0.3)
      .map(tag => tag.name)
      .slice(0, 5)
  }

  /**
   * 生成个性化推荐
   */
  generateRecommendations(userId, options = {}) {
    const profile = this.getUserProfile(userId)
    const { limit = 10, type = 'groups', excludeIds = [] } = options
    
    let candidates = []
    
    switch (type) {
      case 'groups':
        candidates = this.getGroupCandidates(profile, excludeIds)
        break
      case 'content':
        candidates = this.getContentCandidates(profile, excludeIds)
        break
      case 'templates':
        candidates = this.getTemplateCandidates(profile, excludeIds)
        break
    }
    
    // 计算推荐分数
    const scoredCandidates = candidates.map(candidate => ({
      ...candidate,
      score: this.calculateRecommendationScore(profile, candidate),
      reason: this.generateRecommendationReason(profile, candidate)
    }))
    
    // 排序并返回top N
    const recommendations = scoredCandidates
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
    
    // 缓存推荐结果
    this.recommendations.set(`${userId}_${type}`, {
      recommendations,
      generatedAt: new Date(),
      profile: { ...profile }
    })
    
    return recommendations
  }

  /**
   * 获取群组候选
   */
  getGroupCandidates(profile, excludeIds) {
    // 这里应该从数据库获取群组数据
    // 模拟数据
    return [
      {
        id: 1,
        title: '程序员技术交流群',
        category: 'tech',
        price: 99,
        tags: ['技术', '程序员', '交流'],
        memberCount: 256,
        rating: 4.8,
        conversionRate: 0.15
      },
      {
        id: 2,
        title: '投资理财学习群',
        category: 'finance',
        price: 199,
        tags: ['投资', '理财', '学习'],
        memberCount: 189,
        rating: 4.6,
        conversionRate: 0.12
      }
    ].filter(group => !excludeIds.includes(group.id))
  }

  /**
   * 计算推荐分数
   */
  calculateRecommendationScore(profile, candidate) {
    let score = 0
    
    // 兴趣匹配度 (40%)
    const interestScore = this.calculateInterestMatch(profile, candidate)
    score += interestScore * 0.4
    
    // 价格匹配度 (20%)
    const priceScore = this.calculatePriceMatch(profile, candidate)
    score += priceScore * 0.2
    
    // 质量分数 (20%)
    const qualityScore = this.calculateQualityScore(candidate)
    score += qualityScore * 0.2
    
    // 流行度分数 (10%)
    const popularityScore = this.calculatePopularityScore(candidate)
    score += popularityScore * 0.1
    
    // 新颖度分数 (10%)
    const noveltyScore = this.calculateNoveltyScore(profile, candidate)
    score += noveltyScore * 0.1
    
    return Math.min(score, 1.0)
  }

  /**
   * 计算兴趣匹配度
   */
  calculateInterestMatch(profile, candidate) {
    if (!candidate.tags || candidate.tags.length === 0) return 0
    
    let matchScore = 0
    let totalWeight = 0
    
    candidate.tags.forEach(tag => {
      const userTag = profile.interestTags.find(ut => 
        ut.name.toLowerCase().includes(tag.toLowerCase()) ||
        tag.toLowerCase().includes(ut.name.toLowerCase())
      )
      
      if (userTag) {
        matchScore += userTag.weight
      }
      totalWeight += 1
    })
    
    return totalWeight > 0 ? matchScore / totalWeight : 0
  }

  /**
   * 计算价格匹配度
   */
  calculatePriceMatch(profile, candidate) {
    const [minPrice, maxPrice] = profile.preferences.priceRange
    const price = candidate.price || 0
    
    if (price >= minPrice && price <= maxPrice) {
      return 1.0
    } else if (price < minPrice) {
      return 0.8 // 价格低于预期，仍有一定吸引力
    } else {
      // 价格高于预期，根据超出程度降低分数
      const excess = price - maxPrice
      return Math.max(0, 1 - excess / maxPrice)
    }
  }

  /**
   * 计算质量分数
   */
  calculateQualityScore(candidate) {
    let score = 0
    
    // 基于评分
    if (candidate.rating) {
      score += (candidate.rating / 5) * 0.5
    }
    
    // 基于转化率
    if (candidate.conversionRate) {
      score += candidate.conversionRate * 0.3
    }
    
    // 基于成员数量（适中的成员数量更好）
    if (candidate.memberCount) {
      const idealRange = [50, 500]
      if (candidate.memberCount >= idealRange[0] && candidate.memberCount <= idealRange[1]) {
        score += 0.2
      } else {
        score += 0.1
      }
    }
    
    return Math.min(score, 1.0)
  }

  /**
   * 计算流行度分数
   */
  calculatePopularityScore(candidate) {
    // 基于成员数量和活跃度
    const memberScore = Math.min((candidate.memberCount || 0) / 1000, 1)
    const ratingScore = (candidate.rating || 0) / 5
    
    return (memberScore + ratingScore) / 2
  }

  /**
   * 计算新颖度分数
   */
  calculateNoveltyScore(profile, candidate) {
    // 检查用户是否已经接触过类似内容
    const similarExposure = profile.interestTags.filter(tag =>
      candidate.tags && candidate.tags.includes(tag.name)
    ).length
    
    // 新颖度与已接触的相似内容成反比
    return Math.max(0, 1 - similarExposure / 10)
  }

  /**
   * 生成推荐理由
   */
  generateRecommendationReason(profile, candidate) {
    const reasons = []
    
    // 基于兴趣匹配
    const matchingTags = candidate.tags?.filter(tag =>
      profile.interestTags.some(ut => ut.name.toLowerCase().includes(tag.toLowerCase()))
    ) || []
    
    if (matchingTags.length > 0) {
      reasons.push(`与您的兴趣"${matchingTags.join('、')}"匹配`)
    }
    
    // 基于价格
    const [minPrice, maxPrice] = profile.preferences.priceRange
    if (candidate.price >= minPrice && candidate.price <= maxPrice) {
      reasons.push('价格在您的预算范围内')
    }
    
    // 基于质量
    if (candidate.rating >= 4.5) {
      reasons.push('用户评价很高')
    }
    
    if (candidate.conversionRate >= 0.15) {
      reasons.push('转化率表现优秀')
    }
    
    return reasons.length > 0 ? reasons.join('，') : '为您精心推荐'
  }

  /**
   * 获取推荐解释
   */
  getRecommendationExplanation(userId, candidateId) {
    const profile = this.getUserProfile(userId)
    // 返回详细的推荐解释
    return {
      userProfile: {
        topInterests: profile.interestTags.slice(0, 5),
        priceRange: profile.preferences.priceRange,
        valueScore: profile.valueScore
      },
      matchingFactors: [
        '兴趣标签匹配度高',
        '价格符合预期',
        '质量评分优秀'
      ],
      confidence: 0.85
    }
  }

  /**
   * 记录推荐反馈
   */
  recordFeedback(userId, candidateId, action, feedback = {}) {
    const profile = this.getUserProfile(userId)
    
    // 更新用户行为数据
    if (!profile.feedback) {
      profile.feedback = []
    }
    
    profile.feedback.push({
      candidateId,
      action, // 'click', 'convert', 'dismiss', 'like', 'dislike'
      feedback,
      timestamp: new Date()
    })
    
    // 基于反馈调整用户画像
    this.adjustProfileBasedOnFeedback(profile, candidateId, action, feedback)
    
    this.userProfiles.set(userId, profile)
  }

  /**
   * 基于反馈调整用户画像
   */
  adjustProfileBasedOnFeedback(profile, candidateId, action, feedback) {
    // 根据用户行为调整兴趣权重
    switch (action) {
      case 'convert':
        // 转化行为，大幅提升相关标签权重
        this.boostRelatedTags(profile, candidateId, 0.3)
        break
      case 'click':
        // 点击行为，适度提升相关标签权重
        this.boostRelatedTags(profile, candidateId, 0.1)
        break
      case 'dismiss':
        // 忽略行为，降低相关标签权重
        this.reduceRelatedTags(profile, candidateId, 0.1)
        break
      case 'dislike':
        // 不喜欢，显著降低相关标签权重
        this.reduceRelatedTags(profile, candidateId, 0.2)
        break
    }
  }

  /**
   * 提升相关标签权重
   */
  boostRelatedTags(profile, candidateId, boost) {
    // 这里需要根据candidateId获取候选项的标签
    // 然后提升用户画像中对应标签的权重
  }

  /**
   * 降低相关标签权重
   */
  reduceRelatedTags(profile, candidateId, reduction) {
    // 这里需要根据candidateId获取候选项的标签
    // 然后降低用户画像中对应标签的权重
  }

  /**
   * 获取推荐统计
   */
  getRecommendationStats(userId) {
    const cached = this.recommendations.get(`${userId}_groups`)
    if (!cached) return null
    
    return {
      totalRecommendations: cached.recommendations.length,
      avgScore: cached.recommendations.reduce((sum, r) => sum + r.score, 0) / cached.recommendations.length,
      topCategories: this.getTopCategories(cached.recommendations),
      generatedAt: cached.generatedAt
    }
  }

  /**
   * 获取热门分类
   */
  getTopCategories(recommendations) {
    const categoryCount = {}
    recommendations.forEach(r => {
      if (r.category) {
        categoryCount[r.category] = (categoryCount[r.category] || 0) + 1
      }
    })
    
    return Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([category, count]) => ({ category, count }))
  }
}

export default RecommendationEngine