<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * 支付配置测试控制器
 */
class PaymentTestController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 测试支付配置
     */
    public function test(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'methods' => 'required|array',
                'methods.*' => 'string|in:wechat,alipay,qq,bank'
            ]);

            $methods = $request->input('methods');
            $results = [];

            foreach ($methods as $method) {
                $result = $this->testPaymentMethod($method);
                $results[$method] = $result;
            }

            // 判断整体测试结果
            $allSuccess = collect($results)->every(function ($result) {
                return $result['success'];
            });

            $message = $allSuccess ? '所有支付方式配置正常' : '部分支付方式配置异常';

            return $this->success([
                'success' => $allSuccess,
                'message' => $message,
                'results' => $results
            ], '支付配置测试完成');

        } catch (\Exception $e) {
            Log::error('支付配置测试失败', [
                'error' => $e->getMessage(),
                'methods' => $request->input('methods', []),
                'user_id' => auth()->id()
            ]);

            return $this->error('支付配置测试失败：' . $e->getMessage());
        }
    }

    /**
     * 测试单个支付方式
     */
    private function testPaymentMethod(string $method): array
    {
        try {
            switch ($method) {
                case 'wechat':
                    return $this->testWechatPay();
                case 'alipay':
                    return $this->testAlipay();
                case 'qq':
                    return $this->testQQPay();
                case 'bank':
                    return $this->testBankTransfer();
                default:
                    return [
                        'success' => false,
                        'message' => '不支持的支付方式',
                        'details' => []
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '测试异常：' . $e->getMessage(),
                'details' => []
            ];
        }
    }

    /**
     * 测试微信支付
     */
    private function testWechatPay(): array
    {
        $config = config('payment.wechat');
        $details = [];

        // 检查配置项
        $requiredFields = ['app_id', 'mch_id', 'key', 'cert_path', 'key_path'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'success' => false,
                'message' => '微信支付配置不完整',
                'details' => [
                    'missing_fields' => $missingFields,
                    'config_status' => '配置缺失'
                ]
            ];
        }

        // 检查证书文件
        $certPath = storage_path('app/certs/' . $config['cert_path']);
        $keyPath = storage_path('app/certs/' . $config['key_path']);

        $details['cert_exists'] = file_exists($certPath);
        $details['key_exists'] = file_exists($keyPath);

        if (!$details['cert_exists'] || !$details['key_exists']) {
            return [
                'success' => false,
                'message' => '微信支付证书文件缺失',
                'details' => $details
            ];
        }

        // 尝试连接微信支付API
        try {
            $testResult = $this->paymentService->testWechatConnection();
            $details['api_connection'] = $testResult;

            return [
                'success' => true,
                'message' => '微信支付配置正常',
                'details' => $details
            ];
        } catch (\Exception $e) {
            $details['api_error'] = $e->getMessage();

            return [
                'success' => false,
                'message' => '微信支付API连接失败',
                'details' => $details
            ];
        }
    }

    /**
     * 测试支付宝
     */
    private function testAlipay(): array
    {
        $config = config('payment.alipay');
        $details = [];

        // 检查配置项
        $requiredFields = ['app_id', 'private_key', 'public_key', 'alipay_public_key'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'success' => false,
                'message' => '支付宝配置不完整',
                'details' => [
                    'missing_fields' => $missingFields,
                    'config_status' => '配置缺失'
                ]
            ];
        }

        // 验证密钥格式
        $details['private_key_valid'] = $this->validateRSAKey($config['private_key']);
        $details['public_key_valid'] = $this->validateRSAKey($config['public_key']);

        if (!$details['private_key_valid'] || !$details['public_key_valid']) {
            return [
                'success' => false,
                'message' => '支付宝密钥格式错误',
                'details' => $details
            ];
        }

        // 尝试连接支付宝API
        try {
            $testResult = $this->paymentService->testAlipayConnection();
            $details['api_connection'] = $testResult;

            return [
                'success' => true,
                'message' => '支付宝配置正常',
                'details' => $details
            ];
        } catch (\Exception $e) {
            $details['api_error'] = $e->getMessage();

            return [
                'success' => false,
                'message' => '支付宝API连接失败',
                'details' => $details
            ];
        }
    }

    /**
     * 测试QQ钱包
     */
    private function testQQPay(): array
    {
        $config = config('payment.qq');
        $details = [];

        // 检查配置项
        $requiredFields = ['mch_id', 'key'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'success' => false,
                'message' => 'QQ钱包配置不完整',
                'details' => [
                    'missing_fields' => $missingFields,
                    'config_status' => '配置缺失'
                ]
            ];
        }

        // 尝试连接QQ钱包API
        try {
            $testResult = $this->paymentService->testQQPayConnection();
            $details['api_connection'] = $testResult;

            return [
                'success' => true,
                'message' => 'QQ钱包配置正常',
                'details' => $details
            ];
        } catch (\Exception $e) {
            $details['api_error'] = $e->getMessage();

            return [
                'success' => false,
                'message' => 'QQ钱包API连接失败',
                'details' => $details
            ];
        }
    }

    /**
     * 测试银行转账
     */
    private function testBankTransfer(): array
    {
        $config = config('payment.bank');
        $details = [];

        // 检查配置项
        $requiredFields = ['account_name', 'account_number', 'bank_name'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'success' => false,
                'message' => '银行转账配置不完整',
                'details' => [
                    'missing_fields' => $missingFields,
                    'config_status' => '配置缺失'
                ]
            ];
        }

        $details['config_complete'] = true;
        $details['account_info'] = [
            'account_name' => $config['account_name'],
            'bank_name' => $config['bank_name'],
            'account_number' => substr($config['account_number'], 0, 4) . '****' . substr($config['account_number'], -4)
        ];

        return [
            'success' => true,
            'message' => '银行转账配置正常',
            'details' => $details
        ];
    }

    /**
     * 验证RSA密钥格式
     */
    private function validateRSAKey(string $key): bool
    {
        try {
            $resource = openssl_pkey_get_private($key);
            if ($resource === false) {
                $resource = openssl_pkey_get_public($key);
            }
            
            return $resource !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取支付方式状态
     */
    public function status(): JsonResponse
    {
        try {
            $methods = ['wechat', 'alipay', 'qq', 'bank'];
            $status = [];

            foreach ($methods as $method) {
                $config = config("payment.{$method}");
                $status[$method] = [
                    'name' => $this->getPaymentMethodName($method),
                    'enabled' => !empty($config) && ($config['enabled'] ?? false),
                    'configured' => $this->isMethodConfigured($method, $config)
                ];
            }

            return $this->success($status, '获取支付方式状态成功');

        } catch (\Exception $e) {
            return $this->error('获取支付方式状态失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付方式名称
     */
    private function getPaymentMethodName(string $method): string
    {
        $names = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'qq' => 'QQ钱包',
            'bank' => '银行转账'
        ];

        return $names[$method] ?? $method;
    }

    /**
     * 检查支付方式是否已配置
     */
    private function isMethodConfigured(string $method, array $config): bool
    {
        if (empty($config)) {
            return false;
        }

        switch ($method) {
            case 'wechat':
                return !empty($config['app_id']) && 
                       !empty($config['mch_id']) && 
                       !empty($config['key']);
            case 'alipay':
                return !empty($config['app_id']) && 
                       !empty($config['private_key']) && 
                       !empty($config['public_key']);
            case 'qq':
                return !empty($config['mch_id']) && 
                       !empty($config['key']);
            case 'bank':
                return !empty($config['account_name']) && 
                       !empty($config['account_number']) && 
                       !empty($config['bank_name']);
            default:
                return false;
        }
    }
}