import{_ as a}from"./index-DtXAftX0.js";/* empty css               *//* empty css               *//* empty css                *//* empty css                    *//* empty css                        *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                  */import{aY as e,bj as l,bg as t,T as s,_ as d,bs as o,bp as u,bq as n,aM as i,b9 as p,b8 as r,at as _,ae as c,bR as m,bA as y,a5 as f,c1 as b,bt as h,bu as V,br as v,bv as g,a$ as w,af as k,aZ as U,a_ as C,ai as j,U as x,am as I,a6 as q,b6 as B,aR as P,Q as $}from"./element-plus-h2SQQM64.js";import{P as A}from"./PageLayout-C6qH3ReN.js";import{g as D,a as z,t as O,b as M,u as R,c as E}from"./payment-BistKFiU.js";import{r as L,L as N,e as S,k as T,l as F,E as Q,z as W,t as Y,B as Z,u as G,D as H}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const J={class:"payment-settings"},K={class:"settings-section"},X={class:"card-header"},aa={class:"payment-config"},ea={class:"config-header"},la={class:"header-info"},ta={key:0,class:"config-form"},sa={class:"payment-config"},da={class:"config-header"},oa={class:"header-info"},ua={key:0,class:"config-form"},na={class:"payment-config"},ia={class:"config-header"},pa={class:"header-info"},ra={key:0,class:"config-form"},_a={class:"payment-config"},ca={class:"config-header"},ma={class:"header-info"},ya={key:0,class:"config-form"},fa={class:"settings-section"},ba={class:"card-header"},ha={class:"settings-section"},Va={class:"card-header"},va={class:"stat-card success"},ga={class:"stat-icon"},wa={class:"stat-content"},ka={class:"stat-value"},Ua={class:"stat-card primary"},Ca={class:"stat-icon"},ja={class:"stat-content"},xa={class:"stat-value"},Ia={class:"stat-card warning"},qa={class:"stat-icon"},Ba={class:"stat-content"},Pa={class:"stat-value"},$a={class:"stat-card info"},Aa={class:"stat-icon"},Da={class:"stat-content"},za={class:"stat-value"},Oa=a({__name:"PaymentSettings",setup(a){const Oa=L("alipay"),Ma=L(!1),Ra=L(!1),Ea=N({alipay:!1,wechat:!1,easypay:!1,bank:!1}),La=N({alipay:{enabled:!1,app_id:"",private_key:"",public_key:"",gateway:"https://openapi.alipay.com/gateway.do",notify_url:"",return_url:""},wechat:{enabled:!1,app_id:"",mch_id:"",key:"",cert_path:"",notify_url:""},easypay:{enabled:!0,pid:"",key:"",api_url:"",notify_url:"",return_url:""},bank:{enabled:!1,supported_banks:["ICBC","ABC","BOC","CCB"],fee_rate:.6,min_amount:.01,max_amount:5e4}}),Na=N({require_payment_password:!0,require_sms_verification:!0,large_amount_threshold:1e3,ip_whitelist:"",risk_rules:["frequency_limit","amount_limit"]}),Sa=N({total_amount:0,total_orders:0,success_rate:0,avg_time:0}),Ta=L("/api/upload/cert"),Fa=L({Authorization:`Bearer ${localStorage.getItem("token")}`}),Qa=async()=>{Ma.value=!0;try{await R(La),await E(Na),$.success("支付设置保存成功")}catch(a){console.log("API不可用，模拟保存支付配置"),$.success("支付设置保存成功（演示模式）")}finally{Ma.value=!1}},Wa=async(a,e)=>{try{await O(a,e),$.success(`${a} 支付方式已${e?"启用":"禁用"}`)}catch(l){console.log(`API不可用，模拟${a}支付方式${e?"启用":"禁用"}`),$.success(`${a} 支付方式已${e?"启用":"禁用"}（演示模式）`)}},Ya=async a=>{Ea[a]=!0;try{const e={amount:.01,subject:"测试支付",out_trade_no:`test_${Date.now()}`};await M(a,e),$.success(`${a} 支付通道测试成功`)}catch(e){console.log(`API不可用，模拟${a}支付通道测试`),$.success(`${a} 支付通道测试成功（演示模式）`)}finally{Ea[a]=!1}},Za=(a,e)=>{200===a.code?(La.wechat.cert_path=a.data.path,$.success("证书上传成功")):$.error("证书上传失败")},Ga=a=>{const e="application/x-pkcs12"===a.type||a.name.endsWith(".pem"),l=a.size/1024/1024<2;return e?!!l||($.error("证书文件大小不能超过 2MB"),!1):($.error("证书文件格式不正确"),!1)},Ha=async()=>{Ra.value=!0;try{const a=await z();Object.assign(Sa,a.data)}catch(a){console.log("API不可用，使用模拟支付统计数据"),Object.assign(Sa,{total_amount:1234567.89,total_orders:5678,success_rate:98.5,avg_time:2.3})}finally{Ra.value=!1}};return S(()=>{(async()=>{try{const a=await D();Object.assign(La,a.data)}catch(a){console.log("API不可用，使用模拟支付配置数据"),Object.assign(La,{alipay:{enabled:!1,app_id:"",private_key:"",public_key:"",gateway:"https://openapi.alipay.com/gateway.do",notify_url:"",return_url:""},wechat:{enabled:!1,app_id:"",mch_id:"",key:"",cert_path:"",key_path:"",notify_url:""},qq:{enabled:!1,app_id:"",app_key:"",mch_id:""}})}})(),Ha()}),(a,$)=>{const D=s,z=_,O=o,M=i,R=n,E=r,L=p,N=u,S=t,Ja=y,Ka=V,Xa=h,ae=v,ee=l,le=e,te=w,se=C,de=U;return F(),T("div",J,[Q(A,{title:"支付设置",subtitle:"管理系统支付配置和支付方式"},{default:W(()=>[Y("div",K,[Q(le,{class:"settings-card"},{header:W(()=>[Y("div",X,[$[37]||($[37]=Y("h3",null,"支付方式配置",-1)),Q(z,{type:"primary",onClick:Qa,loading:Ma.value},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(g))]),_:1}),$[36]||($[36]=H(" 保存所有设置 ",-1))]),_:1,__:[36]},8,["loading"])])]),default:W(()=>[Q(ee,{modelValue:Oa.value,"onUpdate:modelValue":$[30]||($[30]=a=>Oa.value=a),class:"payment-tabs"},{default:W(()=>[Q(S,{label:"支付宝",name:"alipay"},{default:W(()=>[Y("div",aa,[Y("div",ea,[Y("div",la,[Q(D,{class:"payment-icon alipay"},{default:W(()=>[Q(G(d))]),_:1}),$[38]||($[38]=Y("div",{class:"payment-info"},[Y("h4",null,"支付宝支付"),Y("p",null,"接入支付宝官方支付接口，支持扫码支付、手机支付等")],-1))]),Q(O,{modelValue:La.alipay.enabled,"onUpdate:modelValue":$[0]||($[0]=a=>La.alipay.enabled=a),size:"large",onChange:$[1]||($[1]=a=>Wa("alipay",a))},null,8,["modelValue"])]),La.alipay.enabled?(F(),T("div",ta,[Q(N,{model:La.alipay,"label-width":"120px"},{default:W(()=>[Q(R,{label:"应用ID",required:""},{default:W(()=>[Q(M,{modelValue:La.alipay.app_id,"onUpdate:modelValue":$[2]||($[2]=a=>La.alipay.app_id=a),placeholder:"请输入支付宝应用ID","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"商户私钥",required:""},{default:W(()=>[Q(M,{modelValue:La.alipay.private_key,"onUpdate:modelValue":$[3]||($[3]=a=>La.alipay.private_key=a),type:"textarea",rows:4,placeholder:"请输入商户私钥","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"支付宝公钥",required:""},{default:W(()=>[Q(M,{modelValue:La.alipay.public_key,"onUpdate:modelValue":$[4]||($[4]=a=>La.alipay.public_key=a),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1}),Q(R,{label:"网关地址"},{default:W(()=>[Q(L,{modelValue:La.alipay.gateway,"onUpdate:modelValue":$[5]||($[5]=a=>La.alipay.gateway=a),style:{width:"100%"}},{default:W(()=>[Q(E,{label:"正式环境",value:"https://openapi.alipay.com/gateway.do"}),Q(E,{label:"沙箱环境",value:"https://openapi.alipaydev.com/gateway.do"})]),_:1},8,["modelValue"])]),_:1}),Q(R,{label:"回调地址"},{default:W(()=>[Q(M,{modelValue:La.alipay.notify_url,"onUpdate:modelValue":$[6]||($[6]=a=>La.alipay.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),Q(R,{label:"返回地址"},{default:W(()=>[Q(M,{modelValue:La.alipay.return_url,"onUpdate:modelValue":$[7]||($[7]=a=>La.alipay.return_url=a),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),Q(R,null,{default:W(()=>[Q(z,{onClick:$[8]||($[8]=a=>Ya("alipay")),loading:Ea.alipay},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(c))]),_:1}),$[39]||($[39]=H(" 测试连接 ",-1))]),_:1,__:[39]},8,["loading"])]),_:1})]),_:1},8,["model"])])):Z("",!0)])]),_:1}),Q(S,{label:"微信支付",name:"wechat"},{default:W(()=>[Y("div",sa,[Y("div",da,[Y("div",oa,[Q(D,{class:"payment-icon wechat"},{default:W(()=>[Q(G(m))]),_:1}),$[40]||($[40]=Y("div",{class:"payment-info"},[Y("h4",null,"微信支付"),Y("p",null,"接入微信官方支付接口，支持扫码支付、公众号支付等")],-1))]),Q(O,{modelValue:La.wechat.enabled,"onUpdate:modelValue":$[9]||($[9]=a=>La.wechat.enabled=a),size:"large",onChange:$[10]||($[10]=a=>Wa("wechat",a))},null,8,["modelValue"])]),La.wechat.enabled?(F(),T("div",ua,[Q(N,{model:La.wechat,"label-width":"120px"},{default:W(()=>[Q(R,{label:"应用ID",required:""},{default:W(()=>[Q(M,{modelValue:La.wechat.app_id,"onUpdate:modelValue":$[11]||($[11]=a=>La.wechat.app_id=a),placeholder:"请输入微信应用ID","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"商户号",required:""},{default:W(()=>[Q(M,{modelValue:La.wechat.mch_id,"onUpdate:modelValue":$[12]||($[12]=a=>La.wechat.mch_id=a),placeholder:"请输入微信商户号","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"商户密钥",required:""},{default:W(()=>[Q(M,{modelValue:La.wechat.key,"onUpdate:modelValue":$[13]||($[13]=a=>La.wechat.key=a),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"证书文件"},{default:W(()=>[Q(Ja,{class:"cert-upload",action:Ta.value,headers:Fa.value,"on-success":Za,"before-upload":Ga,accept:".pem,.p12"},{tip:W(()=>$[42]||($[42]=[Y("div",{class:"el-upload__tip"}," 支持 .pem 和 .p12 格式的证书文件 ",-1)])),default:W(()=>[Q(z,null,{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(f))]),_:1}),$[41]||($[41]=H(" 上传证书 ",-1))]),_:1,__:[41]})]),_:1},8,["action","headers"])]),_:1}),Q(R,{label:"回调地址"},{default:W(()=>[Q(M,{modelValue:La.wechat.notify_url,"onUpdate:modelValue":$[14]||($[14]=a=>La.wechat.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),Q(R,null,{default:W(()=>[Q(z,{onClick:$[15]||($[15]=a=>Ya("wechat")),loading:Ea.wechat},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(c))]),_:1}),$[43]||($[43]=H(" 测试连接 ",-1))]),_:1,__:[43]},8,["loading"])]),_:1})]),_:1},8,["model"])])):Z("",!0)])]),_:1}),Q(S,{label:"易支付",name:"easypay"},{default:W(()=>[Y("div",na,[Y("div",ia,[Y("div",pa,[Q(D,{class:"payment-icon easypay"},{default:W(()=>[Q(G(b))]),_:1}),$[44]||($[44]=Y("div",{class:"payment-info"},[Y("h4",null,"易支付"),Y("p",null,"第三方聚合支付平台，支持多种支付方式")],-1))]),Q(O,{modelValue:La.easypay.enabled,"onUpdate:modelValue":$[16]||($[16]=a=>La.easypay.enabled=a),size:"large",onChange:$[17]||($[17]=a=>Wa("easypay",a))},null,8,["modelValue"])]),La.easypay.enabled?(F(),T("div",ra,[Q(N,{model:La.easypay,"label-width":"120px"},{default:W(()=>[Q(R,{label:"商户ID",required:""},{default:W(()=>[Q(M,{modelValue:La.easypay.pid,"onUpdate:modelValue":$[18]||($[18]=a=>La.easypay.pid=a),placeholder:"请输入易支付商户ID","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"商户密钥",required:""},{default:W(()=>[Q(M,{modelValue:La.easypay.key,"onUpdate:modelValue":$[19]||($[19]=a=>La.easypay.key=a),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),Q(R,{label:"API地址",required:""},{default:W(()=>[Q(M,{modelValue:La.easypay.api_url,"onUpdate:modelValue":$[20]||($[20]=a=>La.easypay.api_url=a),placeholder:"请输入易支付API地址"},null,8,["modelValue"])]),_:1}),Q(R,{label:"回调地址"},{default:W(()=>[Q(M,{modelValue:La.easypay.notify_url,"onUpdate:modelValue":$[21]||($[21]=a=>La.easypay.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),Q(R,{label:"返回地址"},{default:W(()=>[Q(M,{modelValue:La.easypay.return_url,"onUpdate:modelValue":$[22]||($[22]=a=>La.easypay.return_url=a),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),Q(R,null,{default:W(()=>[Q(z,{onClick:$[23]||($[23]=a=>Ya("easypay")),loading:Ea.easypay},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(c))]),_:1}),$[45]||($[45]=H(" 测试连接 ",-1))]),_:1,__:[45]},8,["loading"])]),_:1})]),_:1},8,["model"])])):Z("",!0)])]),_:1}),Q(S,{label:"银行卡支付",name:"bank"},{default:W(()=>[Y("div",_a,[Y("div",ca,[Y("div",ma,[Q(D,{class:"payment-icon bank"},{default:W(()=>[Q(G(d))]),_:1}),$[46]||($[46]=Y("div",{class:"payment-info"},[Y("h4",null,"银行卡支付"),Y("p",null,"支持各大银行的网银支付和快捷支付")],-1))]),Q(O,{modelValue:La.bank.enabled,"onUpdate:modelValue":$[24]||($[24]=a=>La.bank.enabled=a),size:"large",onChange:$[25]||($[25]=a=>Wa("bank",a))},null,8,["modelValue"])]),La.bank.enabled?(F(),T("div",ya,[Q(N,{model:La.bank,"label-width":"120px"},{default:W(()=>[Q(R,{label:"支持银行"},{default:W(()=>[Q(Xa,{modelValue:La.bank.supported_banks,"onUpdate:modelValue":$[26]||($[26]=a=>La.bank.supported_banks=a)},{default:W(()=>[Q(Ka,{label:"ICBC"},{default:W(()=>$[47]||($[47]=[H("工商银行",-1)])),_:1,__:[47]}),Q(Ka,{label:"ABC"},{default:W(()=>$[48]||($[48]=[H("农业银行",-1)])),_:1,__:[48]}),Q(Ka,{label:"BOC"},{default:W(()=>$[49]||($[49]=[H("中国银行",-1)])),_:1,__:[49]}),Q(Ka,{label:"CCB"},{default:W(()=>$[50]||($[50]=[H("建设银行",-1)])),_:1,__:[50]}),Q(Ka,{label:"COMM"},{default:W(()=>$[51]||($[51]=[H("交通银行",-1)])),_:1,__:[51]}),Q(Ka,{label:"CMB"},{default:W(()=>$[52]||($[52]=[H("招商银行",-1)])),_:1,__:[52]}),Q(Ka,{label:"CITIC"},{default:W(()=>$[53]||($[53]=[H("中信银行",-1)])),_:1,__:[53]}),Q(Ka,{label:"CEB"},{default:W(()=>$[54]||($[54]=[H("光大银行",-1)])),_:1,__:[54]})]),_:1},8,["modelValue"])]),_:1}),Q(R,{label:"手续费率"},{default:W(()=>[Q(ae,{modelValue:La.bank.fee_rate,"onUpdate:modelValue":$[27]||($[27]=a=>La.bank.fee_rate=a),min:0,max:10,precision:2,step:.01},null,8,["modelValue"]),$[55]||($[55]=Y("span",{class:"input-suffix"},"%",-1))]),_:1,__:[55]}),Q(R,{label:"最小金额"},{default:W(()=>[Q(ae,{modelValue:La.bank.min_amount,"onUpdate:modelValue":$[28]||($[28]=a=>La.bank.min_amount=a),min:.01,precision:2},null,8,["modelValue"]),$[56]||($[56]=Y("span",{class:"input-suffix"},"元",-1))]),_:1,__:[56]}),Q(R,{label:"最大金额"},{default:W(()=>[Q(ae,{modelValue:La.bank.max_amount,"onUpdate:modelValue":$[29]||($[29]=a=>La.bank.max_amount=a),min:1,precision:2},null,8,["modelValue"]),$[57]||($[57]=Y("span",{class:"input-suffix"},"元",-1))]),_:1,__:[57]})]),_:1},8,["model"])])):Z("",!0)])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),Y("div",fa,[Q(le,{class:"settings-card"},{header:W(()=>[Y("div",ba,[$[59]||($[59]=Y("h3",null,"安全设置",-1)),Q(te,{type:"warning"},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(k))]),_:1}),$[58]||($[58]=H(" 安全级别：高 ",-1))]),_:1,__:[58]})])]),default:W(()=>[Q(N,{model:Na,"label-width":"150px"},{default:W(()=>[Q(R,{label:"支付密码验证"},{default:W(()=>[Q(O,{modelValue:Na.require_payment_password,"onUpdate:modelValue":$[31]||($[31]=a=>Na.require_payment_password=a)},null,8,["modelValue"]),$[60]||($[60]=Y("span",{class:"form-tip"},"开启后用户支付时需要输入支付密码",-1))]),_:1,__:[60]}),Q(R,{label:"短信验证"},{default:W(()=>[Q(O,{modelValue:Na.require_sms_verification,"onUpdate:modelValue":$[32]||($[32]=a=>Na.require_sms_verification=a)},null,8,["modelValue"]),$[61]||($[61]=Y("span",{class:"form-tip"},"大额支付时需要短信验证码确认",-1))]),_:1,__:[61]}),Q(R,{label:"大额支付阈值"},{default:W(()=>[Q(ae,{modelValue:Na.large_amount_threshold,"onUpdate:modelValue":$[33]||($[33]=a=>Na.large_amount_threshold=a),min:100,max:5e4,step:100},null,8,["modelValue"]),$[62]||($[62]=Y("span",{class:"input-suffix"},"元",-1))]),_:1,__:[62]}),Q(R,{label:"IP白名单"},{default:W(()=>[Q(M,{modelValue:Na.ip_whitelist,"onUpdate:modelValue":$[34]||($[34]=a=>Na.ip_whitelist=a),type:"textarea",rows:3,placeholder:"每行一个IP地址，支持CIDR格式"},null,8,["modelValue"])]),_:1}),Q(R,{label:"风控规则"},{default:W(()=>[Q(Xa,{modelValue:Na.risk_rules,"onUpdate:modelValue":$[35]||($[35]=a=>Na.risk_rules=a)},{default:W(()=>[Q(Ka,{label:"frequency_limit"},{default:W(()=>$[63]||($[63]=[H("频率限制",-1)])),_:1,__:[63]}),Q(Ka,{label:"amount_limit"},{default:W(()=>$[64]||($[64]=[H("金额限制",-1)])),_:1,__:[64]}),Q(Ka,{label:"device_binding"},{default:W(()=>$[65]||($[65]=[H("设备绑定",-1)])),_:1,__:[65]}),Q(Ka,{label:"geo_restriction"},{default:W(()=>$[66]||($[66]=[H("地域限制",-1)])),_:1,__:[66]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),Y("div",ha,[Q(le,{class:"settings-card"},{header:W(()=>[Y("div",Va,[$[68]||($[68]=Y("h3",null,"支付统计",-1)),Q(z,{onClick:Ha,loading:Ra.value},{default:W(()=>[Q(D,null,{default:W(()=>[Q(G(P))]),_:1}),$[67]||($[67]=H(" 刷新 ",-1))]),_:1,__:[67]},8,["loading"])])]),default:W(()=>[Q(de,{gutter:20},{default:W(()=>[Q(se,{span:6},{default:W(()=>{return[Y("div",va,[Y("div",ga,[Q(D,null,{default:W(()=>[Q(G(j))]),_:1})]),Y("div",wa,[Y("div",ka,"¥"+x((a=Sa.total_amount,new Intl.NumberFormat("zh-CN").format(a))),1),$[69]||($[69]=Y("div",{class:"stat-label"},"总交易金额",-1))])])];var a}),_:1}),Q(se,{span:6},{default:W(()=>[Y("div",Ua,[Y("div",Ca,[Q(D,null,{default:W(()=>[Q(G(I))]),_:1})]),Y("div",ja,[Y("div",xa,x(Sa.total_orders),1),$[70]||($[70]=Y("div",{class:"stat-label"},"总订单数",-1))])])]),_:1}),Q(se,{span:6},{default:W(()=>[Y("div",Ia,[Y("div",qa,[Q(D,null,{default:W(()=>[Q(G(q))]),_:1})]),Y("div",Ba,[Y("div",Pa,x(Sa.success_rate)+"%",1),$[71]||($[71]=Y("div",{class:"stat-label"},"成功率",-1))])])]),_:1}),Q(se,{span:6},{default:W(()=>[Y("div",$a,[Y("div",Aa,[Q(D,null,{default:W(()=>[Q(G(B))]),_:1})]),Y("div",Da,[Y("div",za,x(Sa.avg_time)+"s",1),$[72]||($[72]=Y("div",{class:"stat-label"},"平均处理时间",-1))])])]),_:1})]),_:1})]),_:1})])]),_:1})])}}},[["__scopeId","data-v-c2edb290"]]);export{Oa as default};
