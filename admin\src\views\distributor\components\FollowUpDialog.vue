<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加跟进记录"
    width="600px"
    :before-close="handleClose"
  >
    <div class="follow-up-dialog">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent
      >
      <el-form-item label="客户信息">
        <div class="customer-info">
          <span class="customer-name">{{ customer.name }}</span>
          <el-tag :type="getLevelColor(customer.level)" size="small">
            {{ customer.level_text }}
          </el-tag>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="跟进方式" prop="type">
            <el-select v-model="form.type" placeholder="请选择跟进方式">
              <el-option label="电话" value="call" />
              <el-option label="微信" value="wechat" />
              <el-option label="邮件" value="email" />
              <el-option label="会面" value="meeting" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跟进结果" prop="result">
            <el-select v-model="form.result" placeholder="请选择跟进结果">
              <el-option label="成功" value="successful" />
              <el-option label="失败" value="failed" />
              <el-option label="待定" value="pending" />
              <el-option label="已安排" value="scheduled" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="跟进主题" prop="title">
        <el-input v-model="form.title" placeholder="请输入跟进主题" />
      </el-form-item>

      <el-form-item label="跟进内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请详细描述跟进内容"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系时间" prop="contact_time">
            <el-date-picker
              v-model="form.contact_time"
              type="datetime"
              placeholder="请选择联系时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下次跟进" prop="next_follow_up">
            <el-date-picker
              v-model="form.next_follow_up"
              type="datetime"
              placeholder="请选择下次跟进时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="潜在价值" prop="potential_value">
        <el-input-number
          v-model="form.potential_value"
          :min="0"
          :precision="2"
          placeholder="预估潜在价值"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="其他备注信息"
        />
      </el-form-item>

      <!-- 快捷模板 -->
      <el-form-item label="快捷模板">
        <div class="template-buttons">
          <el-button
            v-for="template in quickTemplates"
            :key="template.id"
            size="small"
            @click="applyTemplate(template)"
          >
            {{ template.name }}
          </el-button>
        </div>
      </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          保存跟进记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  customer: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['success', 'cancel', 'update:visible'])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 响应式数据
const loading = ref(false)
const formRef = ref()

const form = reactive({
  type: '',
  title: '',
  content: '',
  result: '',
  contact_time: new Date(),
  next_follow_up: null,
  potential_value: null,
  remarks: ''
})

const rules = {
  type: [
    { required: true, message: '请选择跟进方式', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入跟进主题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' }
  ],
  result: [
    { required: true, message: '请选择跟进结果', trigger: 'change' }
  ],
  contact_time: [
    { required: true, message: '请选择联系时间', trigger: 'change' }
  ]
}

// 快捷模板
const quickTemplates = ref([
  {
    id: 1,
    name: '初次联系',
    type: 'call',
    title: '初次电话联系',
    content: '进行了初次电话沟通，了解客户基本需求和意向。',
    result: 'successful'
  },
  {
    id: 2,
    name: '需求确认',
    type: 'wechat',
    title: '需求确认沟通',
    content: '通过微信进一步确认客户具体需求，讨论解决方案。',
    result: 'successful'
  },
  {
    id: 3,
    name: '报价跟进',
    type: 'call',
    title: '报价方案跟进',
    content: '跟进报价方案，解答客户疑问，推进合作进程。',
    result: 'pending'
  },
  {
    id: 4,
    name: '合同洽谈',
    type: 'meeting',
    title: '合同条款洽谈',
    content: '面谈讨论合同具体条款，协商合作细节。',
    result: 'scheduled'
  }
])

// 监听表单变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('跟进记录添加成功')
    emit('success', {
      customer_id: props.customer.id,
      ...form,
      id: Date.now(),
      created_at: new Date()
    })
    handleClose()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('添加跟进记录失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  resetForm()
  emit('update:visible', false)
  emit('cancel')
}

const applyTemplate = (template) => {
  form.type = template.type
  form.title = template.title
  form.content = template.content
  form.result = template.result
  
  // 如果是安排类型，自动设置下次跟进时间
  if (template.result === 'scheduled') {
    const nextWeek = new Date()
    nextWeek.setDate(nextWeek.getDate() + 7)
    form.next_follow_up = nextWeek
  }
}

const resetForm = () => {
  Object.assign(form, {
    type: '',
    title: '',
    content: '',
    result: '',
    contact_time: new Date(),
    next_follow_up: null,
    potential_value: null,
    remarks: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'A': 'danger',
    'B': 'warning',
    'C': 'primary',
    'D': 'info'
  }
  return colors[level] || 'info'
}
</script>

<style scoped>
.follow-up-dialog {
  padding: 20px 0;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.customer-name {
  font-weight: 500;
  color: #303133;
}

.template-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-buttons .el-button {
  margin: 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .follow-up-dialog .el-row {
    flex-direction: column;
  }
  
  .follow-up-dialog .el-col {
    margin-bottom: 10px;
  }
  
  .template-buttons {
    flex-direction: column;
  }
  
  .template-buttons .el-button {
    width: 100%;
  }
}
</style>