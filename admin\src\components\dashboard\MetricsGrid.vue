<template>
  <div class="metrics-grid">
    <div class="metric-card" v-for="metric in metrics" :key="metric.key">
      <div class="metric-icon" :style="{ background: metric.color }">
        <el-icon :size="24">
          <component :is="getIconComponent(metric.icon)" />
        </el-icon>
      </div>
      <div class="metric-content">
        <div class="metric-value">{{ formatNumber(metric.value) }}</div>
        <div class="metric-label">{{ metric.label }}</div>
        <div class="metric-change" :class="metric.changeType">
          <el-icon :size="12">
            <ArrowUp v-if="metric.changeType === 'positive'" />
            <ArrowDown v-else />
          </el-icon>
          {{ metric.change }}
        </div>
      </div>
      <div class="metric-chart">
        <div class="mini-chart" :style="{ background: `linear-gradient(135deg, ${metric.color}40, ${metric.color}20)` }"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { 
  UserFilled, Tickets, Money, TrendCharts, ArrowUp, ArrowDown 
} from '@element-plus/icons-vue'

const props = defineProps({
  metrics: {
    type: Array,
    default: () => []
  }
})

const getIconComponent = (iconName) => {
  const iconMap = {
    'UserFilled': UserFilled,
    'Tickets': Tickets,
    'Money': Money,
    'TrendCharts': TrendCharts
  }
  return iconMap[iconName] || UserFilled
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}
</script>

<style lang="scss" scoped>
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .metric-content {
    flex: 1;

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
      line-height: 1;
    }

    .metric-label {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .metric-change {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;

      &.positive {
        color: #10b981;
      }

      &.negative {
        color: #ef4444;
      }
    }
  }

  .metric-chart {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    position: relative;

    .mini-chart {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .metric-card {
    padding: 20px;

    .metric-content .metric-value {
      font-size: 24px;
    }
  }
}
</style>