<template>
  <header class="header-component" :style="componentStyle">
    <div class="header-container">
      <div class="header-logo">
        <img v-if="data.logo" :src="data.logo" :alt="data.title" />
        <h1 v-else>{{ data.title }}</h1>
      </div>
      
      <nav class="header-nav" v-if="data.navigation && data.navigation.length">
        <ul>
          <li v-for="(item, index) in data.navigation" :key="index">
            <a href="#" @click.prevent>{{ item }}</a>
          </li>
        </ul>
      </nav>
      
      <div class="header-actions" v-if="data.showActions">
        <el-button type="primary">{{ data.actionText || '开始使用' }}</el-button>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      logo: '',
      title: '网站标题',
      navigation: ['首页', '产品', '关于我们', '联系我们'],
      showActions: true,
      actionText: '开始使用',
      backgroundColor: '#ffffff',
      textColor: '#333333'
    })
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const componentStyle = computed(() => ({
  backgroundColor: props.data.backgroundColor || '#ffffff',
  color: props.data.textColor || '#333333'
}))
</script>

<style lang="scss" scoped>
.header-component {
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  
  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .header-logo {
      display: flex;
      align-items: center;
      
      img {
        height: 40px;
        width: auto;
      }
      
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
    }
    
    .header-nav {
      ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 32px;
        
        li {
          a {
            text-decoration: none;
            color: inherit;
            font-weight: 500;
            transition: color 0.2s ease;
            
            &:hover {
              color: #409eff;
            }
          }
        }
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
}

@media (max-width: 768px) {
  .header-component {
    .header-container {
      flex-direction: column;
      gap: 16px;
      
      .header-nav ul {
        gap: 16px;
      }
    }
  }
}
</style>