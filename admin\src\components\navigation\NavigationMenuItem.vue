<template>
  <div 
    class="navigation-menu-item" 
    :class="{ 
      active, 
      collapsed, 
      'has-badge': hasBadge,
      'nav-item-hover': !active 
    }"
    :style="{ '--item-delay': `${index * 0.05}s` }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    role="button"
    :tabindex="0"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
  >
    <div class="item-content">
      <!-- 图标 -->
      <div class="item-icon" :class="{ 'icon-bounce': iconBounce }">
        <el-icon size="18">
          <component :is="item.icon" />
        </el-icon>
        
        <!-- 通知点 -->
        <div v-if="hasBadge && collapsed" class="notification-dot"></div>
      </div>
      
      <!-- 文字内容 -->
      <transition name="item-text" mode="out-in">
        <div v-if="!collapsed" class="item-text">
          <div class="text-content">
            <span class="item-title">{{ item.title }}</span>
            <span v-if="item.description" class="item-description">{{ item.description }}</span>
          </div>
          
          <!-- 徽章 -->
          <transition name="badge-bounce" mode="out-in">
            <el-badge
              v-if="hasBadge"
              :value="item.badge"
              :max="99"
              :type="item.badgeType || 'primary'"
              class="item-badge"
            />
          </transition>
        </div>
      </transition>
    </div>
    
    <!-- 激活状态指示器 -->
    <transition name="indicator-slide">
      <div v-if="active" class="active-indicator"></div>
    </transition>
    
    <!-- 悬浮状态左侧边框 -->
    <div v-if="!active" class="hover-border" :class="{ visible: isHovering }"></div>
    
    <!-- tooltip for collapsed state -->
    <el-tooltip
      v-if="collapsed"
      :content="`${item.title}${hasBadge ? ` (${item.badge})` : ''}`"
      placement="right"
      effect="dark"
      :show-after="300"
      :hide-after="100"
    >
      <template #default>
        <div class="tooltip-trigger"></div>
      </template>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  active: {
    type: Boolean,
    default: false
  },
  index: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['click'])

// Data
const isHovering = ref(false)
const iconBounce = ref(false)

// 计算属性
const hasBadge = computed(() => {
  return props.item.badge && props.item.badge > 0
})

// Methods
const handleClick = () => {
  // 触发图标弹跳动画
  iconBounce.value = true
  setTimeout(() => {
    iconBounce.value = false
  }, 300)
  
  emit('click', props.item)
}

const handleMouseEnter = () => {
  isHovering.value = true
}

const handleMouseLeave = () => {
  isHovering.value = false
}
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.navigation-menu-item {
  position: relative;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  animation: item-slide-in 0.4s ease-out;
  animation-delay: var(--item-delay, 0s);
  animation-fill-mode: both;
  
  // 焦点样式
  &:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
  }
  
  .item-content {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    gap: 12px;
    position: relative;
    z-index: 2;
    
    .item-icon {
      width: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #606266;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      
      &.icon-bounce {
        animation: icon-bounce 0.3s ease;
      }
      
      .notification-dot {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 6px;
        height: 6px;
        background: #ef4444;
        border-radius: 50%;
        border: 1px solid white;
        animation: notification-pulse 2s infinite;
      }
    }
    
    .item-text {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 20px;
      
      .text-content {
        flex: 1;
        
        .item-title {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          transition: all 0.3s ease;
          line-height: 1.4;
        }
        
        .item-description {
          display: block;
          font-size: 11px;
          color: #909399;
          line-height: 1.3;
          margin-top: 2px;
          opacity: 0.8;
          transition: opacity 0.3s ease;
        }
      }
      
      .item-badge {
        margin-left: 8px;
        flex-shrink: 0;
        
        :deep(.el-badge__content) {
          font-size: 10px;
          font-weight: 700;
          border: 1px solid rgba(255, 255, 255, 0.5);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  // 激活状态指示器
  .active-indicator {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
  
  // 悬浮边框
  .hover-border {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 16px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 2px 2px 0;
    transition: width 0.3s ease;
    
    &.visible {
      width: 3px;
    }
  }
  
  // Tooltip触发区域
  .tooltip-trigger {
    position: absolute;
    inset: 0;
  }
  
  // Hover效果
  &.nav-item-hover:hover:not(.active) {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.03));
    transform: translateX(4px);
    
    .item-icon {
      color: #667eea;
      transform: scale(1.1);
    }
    
    .item-title {
      color: #667eea;
      font-weight: 600;
    }
    
    .item-description {
      opacity: 1;
    }
  }
  
  // 激活状态
  &.active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.12), rgba(118, 75, 162, 0.06));
    transform: translateX(2px);
    box-shadow: inset 0 0 0 1px rgba(102, 126, 234, 0.1);
    
    .item-icon {
      color: #667eea;
      transform: scale(1.05);
    }
    
    .item-title {
      color: #667eea;
      font-weight: 700;
    }
    
    .item-description {
      color: #667eea;
      opacity: 0.8;
    }
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
      border-radius: 0 2px 2px 0;
    }
  }
  
  // 收缩状态
  &.collapsed {
    .item-content {
      justify-content: center;
      padding: 16px 12px;
    }
  }
  
  // 有徽章的样式增强
  &.has-badge:not(.collapsed) {
    .item-badge {
      animation: badge-gentle-bounce 3s infinite;
    }
  }
  
  // 按压效果
  &:active {
    transform: translateX(1px) scale(0.98);
  }
}

// 动画定义
@keyframes item-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes icon-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes notification-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.3);
  }
}

@keyframes badge-gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1px);
  }
}

// 过渡动画
.item-text-enter-active,
.item-text-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.item-text-enter-from,
.item-text-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.badge-bounce-enter-active {
  animation: badge-bounce-in 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.badge-bounce-leave-active {
  animation: badge-bounce-out 0.2s ease-in;
}

.indicator-slide-enter-active {
  transition: all 0.3s ease;
}

.indicator-slide-leave-active {
  transition: all 0.2s ease;
}

.indicator-slide-enter-from {
  opacity: 0;
  transform: translateY(-50%) scale(0.5);
}

.indicator-slide-leave-to {
  opacity: 0;
  transform: translateY(-50%) scale(0.5);
}

@keyframes badge-bounce-in {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes badge-bounce-out {
  to {
    transform: scale(0);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navigation-menu-item {
    .item-content {
      padding: 12px 16px;
      
      .item-text .text-content {
        .item-title {
          font-size: 13px;
        }
        
        .item-description {
          font-size: 10px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .navigation-menu-item {
    &.active {
      background: #000;
      color: #fff;
      
      .item-icon,
      .item-title {
        color: #fff;
      }
      
      &::before {
        background: #fff;
      }
    }
    
    &:hover:not(.active) {
      background: rgba(0, 0, 0, 0.1);
      border: 1px solid #000;
    }
  }
}

// 暗色主题
@media (prefers-color-scheme: dark) {
  .navigation-menu-item {
    .item-content {
      .item-icon {
        color: #9ca3af;
      }
      
      .item-text .text-content {
        .item-title {
          color: #e5e7eb;
        }
        
        .item-description {
          color: #6b7280;
        }
      }
    }
    
    &:hover:not(.active) {
      background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.08));
    }
    
    &.active {
      background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.1));
    }
  }
}

// 动画偏好
@media (prefers-reduced-motion: reduce) {
  .navigation-menu-item {
    animation: none;
    transition: none;
    
    * {
      animation: none !important;
      transition: none !important;
    }
  }
}
</style>