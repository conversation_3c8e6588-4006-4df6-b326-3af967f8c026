// 响应式导航适配 Composable
import { ref, computed, onMounted, onUnmounted } from 'vue'

export function useResponsiveNavigation() {
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  
  // 断点配置
  const breakpoints = {
    xs: 480,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    xxl: 1536,
    desktop: 1440
  }
  
  // 响应式计算属性
  const currentBreakpoint = computed(() => {
    const width = windowWidth.value
    if (width < breakpoints.xs) return 'xs'
    if (width < breakpoints.sm) return 'sm'
    if (width < breakpoints.md) return 'md'
    if (width < breakpoints.lg) return 'lg'
    if (width < breakpoints.xl) return 'xl'
    if (width < breakpoints.xxl) return 'xxl'
    return 'desktop'
  })
  
  const isMobile = computed(() => ['xs', 'sm'].includes(currentBreakpoint.value))
  const isTablet = computed(() => currentBreakpoint.value === 'md')
  const isDesktop = computed(() => ['lg', 'xl', 'xxl', 'desktop'].includes(currentBreakpoint.value))
  
  // 导航宽度配置
  const navigationWidth = computed(() => {
    switch (currentBreakpoint.value) {
      case 'xs':
      case 'sm':
        return 280 // 移动端全屏抽屉
      case 'md':
        return 260 // 平板端
      case 'lg':
        return 280 // 小屏桌面
      case 'xl':
        return 300 // 标准桌面
      case 'xxl':
      case 'desktop':
        return 320 // 大屏桌面
      default:
        return 300
    }
  })
  
  const collapsedWidth = computed(() => {
    if (isMobile.value) return 0
    if (isTablet.value) return 60
    return 80
  })
  
  // 导航行为配置
  const navigationBehavior = computed(() => ({
    // 是否显示搜索框
    showSearch: !isMobile.value || currentBreakpoint.value !== 'xs',
    
    // 是否显示快捷操作
    showQuickActions: isDesktop.value,
    
    // 是否显示推荐面板
    showRecommendations: isDesktop.value && windowWidth.value >= breakpoints.xl,
    
    // 默认是否折叠
    defaultCollapsed: isMobile.value,
    
    // 是否使用抽屉模式
    useDrawerMode: isMobile.value,
    
    // 动画持续时间
    animationDuration: isMobile.value ? '0.25s' : '0.3s',
    
    // 模块显示数量
    maxVisibleModules: (() => {
      if (windowHeight.value < 600) return 4
      if (windowHeight.value < 800) return 6
      if (windowHeight.value < 1000) return 8
      return 10
    })(),
    
    // 子页面显示数量
    maxVisibleChildren: windowHeight.value < 600 ? 3 : 6
  }))
  
  // 响应式样式配置
  const responsiveStyles = computed(() => ({
    navigation: {
      width: `${navigationWidth.value}px`,
      minWidth: `${navigationWidth.value}px`,
      maxWidth: `${navigationWidth.value}px`,
      transition: `all ${navigationBehavior.value.animationDuration} cubic-bezier(0.4, 0, 0.2, 1)`
    },
    
    collapsed: {
      width: `${collapsedWidth.value}px`,
      minWidth: `${collapsedWidth.value}px`,
      maxWidth: `${collapsedWidth.value}px`
    },
    
    // 移动端样式
    mobile: isMobile.value ? {
      position: 'fixed',
      top: '0',
      left: '0',
      height: '100vh',
      zIndex: '1000',
      transform: 'translateX(-100%)',
      boxShadow: 'none'
    } : {},
    
    // 平板端样式
    tablet: isTablet.value ? {
      borderRadius: '0 12px 12px 0',
      backdropFilter: 'blur(20px)'
    } : {},
    
    // 桌面端样式
    desktop: isDesktop.value ? {
      borderRadius: '0 16px 16px 0',
      backdropFilter: 'blur(24px)',
      boxShadow: '4px 0 24px rgba(0, 0, 0, 0.1)'
    } : {}
  }))
  
  // 自适应布局配置
  const layoutConfig = computed(() => ({
    // 域标题显示方式
    domainTitleDisplay: (() => {
      if (isMobile.value) return 'icon-only'
      if (isTablet.value) return 'compact'
      return 'full'
    })(),
    
    // 模块布局方式
    moduleLayout: (() => {
      if (isMobile.value) return 'list'
      if (isTablet.value) return 'compact-grid'
      return 'detailed-list'
    })(),
    
    // 字体大小配置
    fontSize: {
      domainTitle: isMobile.value ? '14px' : '16px',
      moduleTitle: isMobile.value ? '13px' : '14px',
      childTitle: isMobile.value ? '11px' : '12px'
    },
    
    // 间距配置
    spacing: {
      domainPadding: isMobile.value ? '12px 16px' : '16px 20px',
      modulePadding: isMobile.value ? '8px 12px' : '12px 16px',
      childPadding: isMobile.value ? '4px 8px' : '6px 12px'
    }
  }))
  
  // 触摸设备检测
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 ||
           navigator.msMaxTouchPoints > 0
  })
  
  // 性能优化配置
  const performanceConfig = computed(() => ({
    // 是否启用虚拟滚动
    enableVirtualScroll: navigationBehavior.value.maxVisibleModules > 8,
    
    // 是否启用懒加载
    enableLazyLoad: isMobile.value || isTablet.value,
    
    // 防抖延迟
    debounceDelay: isMobile.value ? 100 : 150,
    
    // 节流间隔
    throttleInterval: isMobile.value ? 16 : 32
  }))
  
  // 窗口大小变化处理
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }
  
  // 防抖处理窗口变化
  let resizeTimeout = null
  const handleResize = () => {
    if (resizeTimeout) clearTimeout(resizeTimeout)
    resizeTimeout = setTimeout(updateWindowSize, 100)
  }
  
  // 方向变化处理
  const handleOrientationChange = () => {
    // 延迟更新以确保获取到正确的尺寸
    setTimeout(updateWindowSize, 300)
  }
  
  onMounted(() => {
    window.addEventListener('resize', handleResize, { passive: true })
    window.addEventListener('orientationchange', handleOrientationChange, { passive: true })
    
    // 初始化时更新一次
    updateWindowSize()
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleOrientationChange)
    if (resizeTimeout) clearTimeout(resizeTimeout)
  })
  
  return {
    // 响应式状态
    windowWidth,
    windowHeight,
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isTouchDevice,
    
    // 配置对象
    navigationWidth,
    collapsedWidth,
    navigationBehavior,
    responsiveStyles,
    layoutConfig,
    performanceConfig,
    
    // 工具方法
    updateWindowSize
  }
}