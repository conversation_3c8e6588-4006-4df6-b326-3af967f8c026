<template>
  <div class="dashboard-card" :class="cardClass">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="showHeader">
      <div class="header-left">
        <div class="card-icon" v-if="icon" :class="iconClass">
          <i :class="icon"></i>
        </div>
        <div class="header-content">
          <h3 class="card-title">{{ title }}</h3>
          <p class="card-subtitle" v-if="subtitle">{{ subtitle }}</p>
        </div>
      </div>
      
      <div class="header-right" v-if="$slots.actions || actions.length">
        <slot name="actions">
          <el-dropdown v-if="actions.length" trigger="click">
            <el-button type="text" size="small">
              <i class="el-icon-more"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="action in actions"
                  :key="action.key"
                  @click="handleAction(action)"
                >
                  <i :class="action.icon" v-if="action.icon"></i>
                  {{ action.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 统计数据展示 -->
      <div v-if="type === 'stat'" class="stat-content">
        <div class="stat-main">
          <div class="stat-value" :class="valueClass">
            {{ formatValue(value) }}
            <span class="stat-unit" v-if="unit">{{ unit }}</span>
          </div>
          <div class="stat-change" v-if="change !== undefined" :class="changeClass">
            <i :class="changeIcon"></i>
            <span>{{ formatChange(change) }}</span>
          </div>
        </div>
        
        <div class="stat-chart" v-if="chartData">
          <canvas ref="chartCanvas" :width="chartWidth" :height="chartHeight"></canvas>
        </div>
      </div>
      
      <!-- 进度条展示 -->
      <div v-else-if="type === 'progress'" class="progress-content">
        <div class="progress-info">
          <span class="progress-label">{{ progressLabel }}</span>
          <span class="progress-value">{{ progressValue }}%</span>
        </div>
        <el-progress
          :percentage="progressValue"
          :color="progressColor"
          :stroke-width="progressStrokeWidth"
          :show-text="false"
        />
        <div class="progress-description" v-if="progressDescription">
          {{ progressDescription }}
        </div>
      </div>
      
      <!-- 列表展示 -->
      <div v-else-if="type === 'list'" class="list-content">
        <div
          v-for="(item, index) in listData"
          :key="index"
          class="list-item"
          @click="handleListItemClick(item, index)"
        >
          <div class="item-icon" v-if="item.icon" :class="item.iconClass">
            <i :class="item.icon"></i>
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-description" v-if="item.description">
              {{ item.description }}
            </div>
          </div>
          <div class="item-value" v-if="item.value">
            {{ item.value }}
          </div>
        </div>
      </div>
      
      <!-- 自定义内容 -->
      <div v-else class="custom-content">
        <slot></slot>
      </div>
    </div>
    
    <!-- 卡片底部 -->
    <div class="card-footer" v-if="$slots.footer || showFooter">
      <slot name="footer">
        <div class="footer-content">
          <span class="footer-text">{{ footerText }}</span>
          <el-button
            v-if="footerAction"
            type="text"
            size="small"
            @click="handleFooterAction"
          >
            {{ footerAction.label }}
            <i :class="footerAction.icon" v-if="footerAction.icon"></i>
          </el-button>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { Chart, registerables } from 'chart.js'

// 注册 Chart.js 组件
Chart.register(...registerables)

// Props 定义
const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    default: ''
  },
  // 卡片副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 卡片图标
  icon: {
    type: String,
    default: ''
  },
  // 卡片类型
  type: {
    type: String,
    default: 'custom', // stat, progress, list, custom
    validator: (value) => ['stat', 'progress', 'list', 'custom'].includes(value)
  },
  // 卡片主题
  theme: {
    type: String,
    default: 'default', // default, primary, success, warning, error, info
    validator: (value) => ['default', 'primary', 'success', 'warning', 'error', 'info'].includes(value)
  },
  // 卡片尺寸
  size: {
    type: String,
    default: 'default', // small, default, large
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否显示底部
  showFooter: {
    type: Boolean,
    default: false
  },
  // 操作按钮
  actions: {
    type: Array,
    default: () => []
  },
  // 统计数值
  value: {
    type: [Number, String],
    default: 0
  },
  // 数值单位
  unit: {
    type: String,
    default: ''
  },
  // 变化值
  change: {
    type: Number,
    default: undefined
  },
  // 图表数据
  chartData: {
    type: Object,
    default: null
  },
  // 进度条标签
  progressLabel: {
    type: String,
    default: ''
  },
  // 进度条数值
  progressValue: {
    type: Number,
    default: 0
  },
  // 进度条颜色
  progressColor: {
    type: String,
    default: '#409EFF'
  },
  // 进度条粗细
  progressStrokeWidth: {
    type: Number,
    default: 8
  },
  // 进度条描述
  progressDescription: {
    type: String,
    default: ''
  },
  // 列表数据
  listData: {
    type: Array,
    default: () => []
  },
  // 底部文本
  footerText: {
    type: String,
    default: ''
  },
  // 底部操作
  footerAction: {
    type: Object,
    default: null
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'action',
  'footer-action',
  'list-item-click',
  'card-click'
])

// 响应式数据
const chartCanvas = ref()
const chartInstance = ref(null)
const chartWidth = ref(200)
const chartHeight = ref(60)

// 计算属性
const cardClass = computed(() => [
  `card-${props.theme}`,
  `card-${props.size}`,
  {
    'card-clickable': props.clickable,
    'card-loading': props.loading
  }
])

const iconClass = computed(() => [
  `icon-${props.theme}`
])

const valueClass = computed(() => [
  `value-${props.theme}`
])

const changeClass = computed(() => {
  if (props.change === undefined) return []
  return [
    props.change >= 0 ? 'change-positive' : 'change-negative'
  ]
})

const changeIcon = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
})

// 监听器
watch(() => props.chartData, (newData) => {
  if (newData && chartCanvas.value) {
    nextTick(() => {
      updateChart()
    })
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  if (props.chartData && chartCanvas.value) {
    nextTick(() => {
      initChart()
    })
  }
})

// 方法
const formatValue = (value) => {
  if (typeof value === 'number') {
    if (value >= 10000) {
      return (value / 10000).toFixed(1) + 'w'
    }
    if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'k'
    }
    return value.toLocaleString()
  }
  return value
}

const formatChange = (change) => {
  if (change === undefined) return ''
  const prefix = change >= 0 ? '+' : ''
  return `${prefix}${change.toFixed(1)}%`
}

const handleAction = (action) => {
  emit('action', action)
}

const handleFooterAction = () => {
  emit('footer-action', props.footerAction)
}

const handleListItemClick = (item, index) => {
  emit('list-item-click', item, index)
}

const initChart = () => {
  if (!chartCanvas.value || !props.chartData) return
  
  const ctx = chartCanvas.value.getContext('2d')
  
  chartInstance.value = new Chart(ctx, {
    type: props.chartData.type || 'line',
    data: props.chartData.data,
    options: {
      responsive: false,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          display: false
        },
        y: {
          display: false
        }
      },
      elements: {
        point: {
          radius: 0
        },
        line: {
          borderWidth: 2,
          tension: 0.4
        }
      },
      ...props.chartData.options
    }
  })
}

const updateChart = () => {
  if (chartInstance.value && props.chartData) {
    chartInstance.value.data = props.chartData.data
    chartInstance.value.update()
  } else {
    initChart()
  }
}
</script>

<style scoped lang="scss">
.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2xl);
  }
  
  &.card-clickable {
    cursor: pointer;
  }
  
  &.card-loading {
    opacity: 0.7;
    pointer-events: none;
  }
  
  // 主题样式
  &.card-primary {
    border-color: var(--primary-200);
    
    .card-icon.icon-primary {
      background: var(--gradient-primary);
      color: white;
    }
    
    .stat-value.value-primary {
      color: var(--primary-600);
    }
  }
  
  &.card-success {
    border-color: var(--success-200);
    
    .card-icon.icon-success {
      background: var(--gradient-success);
      color: white;
    }
    
    .stat-value.value-success {
      color: var(--success-600);
    }
  }
  
  &.card-warning {
    border-color: var(--warning-200);
    
    .card-icon.icon-warning {
      background: var(--gradient-warning);
      color: white;
    }
    
    .stat-value.value-warning {
      color: var(--warning-600);
    }
  }
  
  &.card-error {
    border-color: var(--error-200);
    
    .card-icon.icon-error {
      background: var(--gradient-error);
      color: white;
    }
    
    .stat-value.value-error {
      color: var(--error-600);
    }
  }
  
  &.card-info {
    border-color: var(--info-200);
    
    .card-icon.icon-info {
      background: var(--gradient-secondary);
      color: white;
    }
    
    .stat-value.value-info {
      color: var(--info-600);
    }
  }
  
  // 尺寸样式
  &.card-small {
    .card-header {
      padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .card-content {
      padding: 0 var(--spacing-lg) var(--spacing-md);
    }
    
    .card-footer {
      padding: var(--spacing-md) var(--spacing-lg);
    }
  }
  
  &.card-large {
    .card-header {
      padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-lg);
    }
    
    .card-content {
      padding: 0 var(--spacing-2xl) var(--spacing-xl);
    }
    
    .card-footer {
      padding: var(--spacing-lg) var(--spacing-2xl) var(--spacing-2xl);
    }
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--gray-100);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  background: var(--gray-100);
  color: var(--gray-600);
}

.header-content {
  .card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs) 0;
  }
  
  .card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
  }
}

.card-content {
  padding: 0 var(--spacing-xl) var(--spacing-lg);
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.stat-main {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  line-height: var(--line-height-tight);
  display: flex;
  align-items: baseline;
  gap: var(--spacing-sm);
  
  .stat-unit {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    color: var(--gray-600);
  }
}

.stat-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacing-sm);
  
  &.change-positive {
    color: var(--success-600);
  }
  
  &.change-negative {
    color: var(--error-600);
  }
}

.stat-chart {
  width: 200px;
  height: 60px;
}

.progress-content {
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .progress-label {
      font-size: var(--font-size-sm);
      color: var(--gray-700);
      font-weight: var(--font-weight-medium);
    }
    
    .progress-value {
      font-size: var(--font-size-sm);
      color: var(--gray-900);
      font-weight: var(--font-weight-semibold);
    }
  }
  
  .progress-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-md);
  }
}

.list-content {
  .list-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background: var(--gray-50);
      border-radius: var(--radius-lg);
      padding-left: var(--spacing-md);
      padding-right: var(--spacing-md);
    }
  }
  
  .item-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    background: var(--gray-100);
    color: var(--gray-600);
  }
  
  .item-content {
    flex: 1;
    
    .item-title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--gray-900);
      margin-bottom: var(--spacing-xs);
    }
    
    .item-description {
      font-size: var(--font-size-xs);
      color: var(--gray-600);
    }
  }
  
  .item-value {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
  }
}

.card-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--gray-100);
  background: rgba(255, 255, 255, 0.5);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .footer-text {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-card {
    .card-header {
      padding: var(--spacing-lg);
    }
    
    .card-content {
      padding: 0 var(--spacing-lg) var(--spacing-md);
    }
    
    .card-footer {
      padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .stat-content {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .stat-chart {
      width: 100%;
      height: 40px;
    }
  }
}
</style>
