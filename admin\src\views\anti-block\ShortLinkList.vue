<template>
  <div class="short-link-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h1>🔗 短链接管理</h1>
        <p class="page-desc">管理系统生成的防红短链接，监控访问情况和域名使用</p>
      </div>
      
      <div class="page-actions">
        <el-button type="primary" @click="showAddDialog">
          <i class="el-icon-plus"></i> 创建短链接
        </el-button>
        <el-button type="success" @click="generateQRCode" :disabled="selectedLinks.length !== 1">
          <i class="el-icon-s-grid"></i> 生成二维码
        </el-button>
        <el-button type="info" @click="exportLinks">
          <i class="el-icon-download"></i> 导出数据
        </el-button>
      </div>
    </div>

    <!-- 使用说明 -->
    <el-card class="help-card" style="margin-bottom: 20px;">
      <div slot="header" class="card-header">
        <span>💡 短链接使用说明</span>
        <el-button type="text" @click="showHelpDialog">查看详情</el-button>
      </div>
      
      <div class="help-content">
        <div class="help-tips">
          <div class="tip-item">
            <i class="el-icon-info" style="color: #409eff;"></i>
            <span><strong>自动生成：</strong>分销员推广链接会自动生成防红短链接</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-success" style="color: #67c23a;"></i>
            <span><strong>智能切换：</strong>域名异常时自动切换到备用域名</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-view" style="color: #e6a23c;"></i>
            <span><strong>实时统计：</strong>详细记录每次访问的数据和来源</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="filters" class="filter-form">
        <el-form-item label="链接类型">
          <el-select v-model="filters.link_type" placeholder="全部类型" clearable>
            <el-option label="推广链接" value="recruit"></el-option>
            <el-option label="支付链接" value="payment"></el-option>
            <el-option label="其他链接" value="other"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="链接状态">
          <el-select v-model="filters.status" placeholder="全部状态" clearable>
            <el-option label="正常" :value="1"></el-option>
            <el-option label="异常" :value="2"></el-option>
            <el-option label="禁用" :value="3"></el-option>
            <el-option label="过期" :value="4"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="域名">
          <el-select v-model="filters.domain_id" placeholder="全部域名" clearable>
            <el-option 
              v-for="domain in domainOptions" 
              :key="domain.id" 
              :label="domain.domain" 
              :value="domain.id">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="filters.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input v-model="filters.keyword" placeholder="短链接代码或原始URL" clearable style="width: 200px;"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="loadShortLinks">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 短链接列表 -->
    <el-card>
      <div slot="header" class="card-header">
        <span>短链接列表 ({{ pagination.total }})</span>
        <div>
          <el-button 
            type="warning" 
            size="small" 
            @click="batchSwitchDomain" 
            :disabled="selectedLinks.length === 0"
            v-if="isAdmin">
            批量切换域名
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="batchDelete" 
            :disabled="selectedLinks.length === 0"
            v-if="isAdmin">
            批量删除
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="shortLinks" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe>
        <el-table-column type="selection" width="55"></el-table-column>
        
        <el-table-column prop="short_code" label="短链接" width="120">
          <template #default="scope">
            <div class="short-code-info">
              <span class="short-code">{{ scope.row.short_code }}</span>
              <el-button 
                type="text" 
                size="mini" 
                @click="copyShortLink(scope.row)"
                style="margin-left: 5px;">
                <i class="el-icon-copy-document"></i>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="full_url" label="完整链接" width="200">
          <template #default="scope">
            <div class="full-url-info">
              <span class="full-url">{{ scope.row.full_url }}</span>
              <el-button
                type="text"
                size="mini"
                @click="copyFullUrl(scope.row)"
                style="margin-left: 5px;">
                <i class="el-icon-copy-document"></i>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="original_url" label="原始URL" min-width="250">
          <template #default="scope">
            <div class="original-url">
              <el-tooltip :content="scope.row.original_url" placement="top">
                <span>{{ truncateUrl(scope.row.original_url, 40) }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="domain" label="使用域名" width="150">
          <template #default="scope">
            <div class="domain-info">
              <span class="domain-text">{{ scope.row.domain.domain }}</span>
              <el-tag 
                :type="getDomainHealthColor(scope.row.domain.health_score)" 
                size="mini"
                style="margin-left: 5px;">
                {{ scope.row.domain.health_score }}%
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="link_type" label="类型" width="100">
          <template #default="scope">
            <el-tag size="small" :type="getLinkTypeColor(scope.row.link_type)">
              {{ getLinkTypeName(scope.row.link_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="click_count" label="点击量" width="80">
          <template #default="scope">
            <span class="click-count">{{ scope.row.click_count }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="140">
          <template #default="scope">
            <span>{{ formatTime(scope.row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="last_click_at" label="最后访问" width="140">
          <template #default="scope">
            <span>{{ formatTime(scope.row.last_click_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="viewStats(scope.row)">
              统计
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="switchDomain(scope.row)"
              v-if="isAdmin">
              切换域名
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="editShortLink(scope.row)"
              v-if="isAdmin">
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="deleteShortLink(scope.row)"
              style="color: #f56c6c;"
              v-if="isAdmin">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 创建/编辑短链接对话框 -->
    <el-dialog 
      :title="editMode ? '编辑短链接' : '创建短链接'" 
      :visible.sync="dialogVisible" 
      width="500px"
      @close="resetForm">
      <el-form :model="linkForm" :rules="linkRules" ref="linkForm" label-width="100px">
        <el-form-item label="原始URL" prop="original_url">
          <el-input 
            v-model="linkForm.original_url" 
            placeholder="请输入完整的URL地址"
            type="textarea"
            rows="2">
          </el-input>
          <div class="form-tip">
            🔗 请输入完整的URL地址，包含 http:// 或 https://
          </div>
        </el-form-item>
        
        <el-form-item label="链接类型" prop="link_type">
          <el-select v-model="linkForm.link_type" placeholder="选择链接类型" style="width: 100%;">
            <el-option label="推广链接" value="recruit"></el-option>
            <el-option label="支付链接" value="payment"></el-option>
            <el-option label="其他链接" value="other"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="选择域名" prop="domain_id">
          <el-select v-model="linkForm.domain_id" placeholder="选择域名" style="width: 100%;">
            <el-option 
              v-for="domain in availableDomains" 
              :key="domain.id" 
              :label="`${domain.domain} (健康度: ${domain.health_score}%)`" 
              :value="domain.id">
            </el-option>
          </el-select>
          <div class="form-tip">
            💡 系统会自动选择最佳域名，也可手动指定
          </div>
        </el-form-item>
        
        <el-form-item label="自定义代码">
          <el-input 
            v-model="linkForm.custom_code" 
            placeholder="留空则自动生成"
            maxlength="20">
          </el-input>
          <div class="form-tip">
            🎯 自定义短链接代码，仅支持字母数字，留空则自动生成
          </div>
        </el-form-item>
        
        <el-form-item label="有效期">
          <el-date-picker
            v-model="linkForm.expires_at"
            type="datetime"
            placeholder="选择过期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;">
          </el-date-picker>
          <div class="form-tip">
            ⏰ 留空则永久有效，过期后链接将无法访问
          </div>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="linkForm.remarks" 
            type="textarea" 
            rows="2" 
            placeholder="链接用途说明">
          </el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ editMode ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 切换域名对话框 -->
    <el-dialog title="切换域名" :visible.sync="switchDomainVisible" width="400px">
      <div class="switch-domain-content">
        <p>为短链接 <strong>{{ currentLink.short_code }}</strong> 切换域名：</p>
        <el-select v-model="selectedDomainId" placeholder="选择新域名" style="width: 100%;">
          <el-option 
            v-for="domain in availableDomains" 
            :key="domain.id" 
            :label="`${domain.domain} (健康度: ${domain.health_score}%)`" 
            :value="domain.id">
          </el-option>
        </el-select>
        <div class="form-tip" style="margin-top: 10px;">
          💡 切换域名后，原链接将重定向到新域名
        </div>
      </div>
      
      <div slot="footer">
        <el-button @click="switchDomainVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSwitchDomain" :loading="switching">切换</el-button>
      </div>
    </el-dialog>

    <!-- 访问统计对话框 -->
    <el-dialog title="访问统计" :visible.sync="statsVisible" width="800px">
      <div class="stats-content">
        <div class="stats-header">
          <h4>{{ currentLink.short_code }} 的访问统计</h4>
          <div class="stats-summary">
            <div class="summary-item">
              <span class="summary-label">总访问量</span>
              <span class="summary-value">{{ linkStats.total_clicks }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">今日访问</span>
              <span class="summary-value">{{ linkStats.today_clicks }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">独立访客</span>
              <span class="summary-value">{{ linkStats.unique_visitors }}</span>
            </div>
          </div>
        </div>
        
        <el-tabs v-model="statsTab">
          <el-tab-pane label="访问趋势" name="trend">
            <div class="chart-container">
              <!-- 这里可以集成图表组件 -->
              <p>访问趋势图表</p>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="访问记录" name="logs">
            <el-table :data="accessLogs" v-loading="statsLoading" size="small">
              <el-table-column prop="access_time" label="访问时间" width="140">
                <template #default="scope">
                  <span>{{ formatTime(scope.row.access_time) }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="ip_address" label="IP地址" width="120">
                <template #default="scope">
                  <span>{{ scope.row.ip_address }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="user_agent" label="设备信息" min-width="200">
                <template #default="scope">
                  <el-tooltip :content="scope.row.user_agent" placement="top">
                    <span>{{ truncateText(scope.row.user_agent, 30) }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="referer" label="来源" min-width="150">
                <template #default="scope">
                  <span>{{ scope.row.referer || '直接访问' }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="region" label="地区" width="100">
                <template #default="scope">
                  <span>{{ scope.row.region || '-' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 二维码对话框 -->
    <el-dialog title="二维码" :visible.sync="qrCodeVisible" width="400px">
      <div class="qrcode-content">
        <div class="qrcode-image" v-if="qrCodeUrl">
          <img :src="qrCodeUrl" alt="二维码" />
        </div>
        <div class="qrcode-info">
          <p><strong>短链接：</strong>{{ currentLink.full_url }}</p>
          <p><strong>创建时间：</strong>{{ formatTime(new Date()) }}</p>
        </div>
      </div>
      
      <div slot="footer">
        <el-button @click="qrCodeVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadQRCode">下载二维码</el-button>
      </div>
    </el-dialog>

    <!-- 帮助说明对话框 -->
    <el-dialog title="短链接使用说明" :visible.sync="helpVisible" width="700px">
      <div class="help-detail">
        <h3>🔗 什么是防红短链接？</h3>
        <p>防红短链接是经过特殊处理的短链接，能够有效避免被微信、QQ等平台检测和封禁。</p>
        
        <h3>🚀 主要功能</h3>
        <ul>
          <li><strong>智能域名选择：</strong>系统自动选择最健康的域名生成短链接</li>
          <li><strong>自动域名切换：</strong>当域名异常时自动切换到备用域名</li>
          <li><strong>访问统计分析：</strong>详细记录每次访问的数据和来源</li>
          <li><strong>二维码生成：</strong>一键生成短链接二维码</li>
          <li><strong>批量管理：</strong>支持批量操作和数据导出</li>
        </ul>
        
        <h3>📊 链接类型说明</h3>
        <el-table :data="linkTypeDescriptions" size="small">
          <el-table-column prop="type" label="类型" width="100"></el-table-column>
          <el-table-column prop="name" label="名称" width="100"></el-table-column>
          <el-table-column prop="description" label="说明"></el-table-column>
        </el-table>
        
        <h3>⚠️ 使用注意事项</h3>
        <el-alert type="warning" :closable="false">
          <ul style="margin: 0; padding-left: 20px;">
            <li>原始URL必须是完整的地址，包含协议头（http://或https://）</li>
            <li>自定义代码仅支持字母和数字，建议使用有意义的代码</li>
            <li>设置合理的过期时间，避免链接长期有效造成安全风险</li>
            <li>定期检查链接状态，及时处理异常链接</li>
          </ul>
        </el-alert>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getShortLinks, 
  createShortLink, 
  updateShortLink, 
  deleteShortLink,
  switchShortLinkDomain,
  getAccessLogs,
  generateQRCode,
  batchDeleteShortLinks,
  exportShortLinks
} from '@/api/anti-block'
import { getDomainList } from '@/api/anti-block'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const switchDomainVisible = ref(false)
const statsVisible = ref(false)
const qrCodeVisible = ref(false)
const helpVisible = ref(false)
const editMode = ref(false)
const submitting = ref(false)
const switching = ref(false)
const statsLoading = ref(false)

// 短链接列表
const shortLinks = ref([])
const selectedLinks = ref([])

// 域名选项
const domainOptions = ref([])
const availableDomains = ref([])

// 分页
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

// 筛选条件
const filters = ref({
  link_type: '',
  status: '',
  domain_id: '',
  date_range: '',
  keyword: ''
})

// 短链接表单
const linkForm = ref({
  original_url: '',
  link_type: 'recruit',
  domain_id: '',
  custom_code: '',
  expires_at: '',
  remarks: ''
})

// 表单验证
const linkRules = {
  original_url: [
    { required: true, message: '请输入原始URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  link_type: [
    { required: true, message: '请选择链接类型', trigger: 'change' }
  ]
}

// 当前操作的链接
const currentLink = ref({})
const selectedDomainId = ref('')

// 统计数据
const linkStats = ref({
  total_clicks: 0,
  today_clicks: 0,
  unique_visitors: 0
})
const accessLogs = ref([])
const statsTab = ref('trend')

// 二维码
const qrCodeUrl = ref('')

// 帮助说明数据
const linkTypeDescriptions = [
  { type: 'recruit', name: '推广链接', description: '用于分销员推广的链接' },
  { type: 'payment', name: '支付链接', description: '用于支付页面的链接' },
  { type: 'other', name: '其他链接', description: '其他用途的链接' }
]

// 计算属性
const isAdmin = computed(() => userStore.userInfo?.role === 'admin')

// 生命周期
onMounted(() => {
  loadShortLinks()
  loadDomainOptions()
})
  
// 方法定义
// 加载短链接列表
const loadShortLinks = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.current,
      per_page: pagination.value.pageSize,
      ...filters.value
    }
    
    const { data } = await getShortLinks(params)
    shortLinks.value = data.data || []
    pagination.value.total = data.total || 0
  } catch (error) {
    ElMessage.error('加载短链接列表失败')
  } finally {
    loading.value = false
  }
}

// 加载域名选项
const loadDomainOptions = async () => {
  try {
    const { data } = await getDomainList({ per_page: 100, status: 1 })
    domainOptions.value = data.data || []
    availableDomains.value = data.data || []
  } catch (error) {
    console.error('加载域名选项失败')
  }
}

// 显示创建对话框
const showAddDialog = () => {
  editMode.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑短链接
const editShortLink = (link) => {
  editMode.value = true
  dialogVisible.value = true
  linkForm.value = {
    ...link,
    domain_id: link.domain.id
  }
}

// 提交表单
const submitForm = async () => {
  try {
    // 需要获取表单引用进行验证
    submitting.value = true
    
    if (editMode.value) {
      await updateShortLink(linkForm.value.id, linkForm.value)
      ElMessage.success('短链接更新成功')
    } else {
      await createShortLink(linkForm.value)
      ElMessage.success('短链接创建成功')
    }
    
    dialogVisible.value = false
    loadShortLinks()
  } catch (error) {
    if (error.fields) return
    ElMessage.error(editMode.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 删除短链接
const deleteShortLinkAction = (link) => {
  ElMessageBox.confirm(`确定删除短链接 ${link.short_code} 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteShortLink(link.id)
      ElMessage.success('删除成功')
      loadShortLinks()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 切换域名
const switchDomain = (link) => {
  currentLink.value = link
  selectedDomainId.value = link.domain.id
  switchDomainVisible.value = true
}

const confirmSwitchDomain = async () => {
  if (!selectedDomainId.value) {
    ElMessage.warning('请选择新域名')
    return
  }
  
  switching.value = true
  try {
    await switchShortLinkDomain(currentLink.value.id, selectedDomainId.value)
    ElMessage.success('域名切换成功')
    switchDomainVisible.value = false
    loadShortLinks()
  } catch (error) {
    ElMessage.error('域名切换失败')
  } finally {
    switching.value = false
  }
}

// 查看统计
const viewStats = async (link) => {
  currentLink.value = link
  statsVisible.value = true
  await loadLinkStats(link.id)
}

const loadLinkStats = async (linkId) => {
  statsLoading.value = true
  try {
    // 这里应该调用获取短链接统计的API
    linkStats.value = {
      total_clicks: 1250,
      today_clicks: 45,
      unique_visitors: 890
    }
    
    const { data } = await getAccessLogs({ link_id: linkId, limit: 50 })
    accessLogs.value = data.data || []
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 生成二维码
const generateQRCodeAction = async () => {
  if (selectedLinks.value.length !== 1) {
    ElMessage.warning('请选择一个短链接')
    return
  }
  
  try {
    const link = selectedLinks.value[0]
    const { data } = await generateQRCode({ url: link.full_url })
    qrCodeUrl.value = data.qr_code_url
    currentLink.value = link
    qrCodeVisible.value = true
  } catch (error) {
    ElMessage.error('生成二维码失败')
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrCodeUrl.value) return
  
  const a = document.createElement('a')
  a.href = qrCodeUrl.value
  a.download = `qrcode_${currentLink.value.short_code}.png`
  a.click()
}

// 批量操作
const handleSelectionChange = (selection) => {
  selectedLinks.value = selection
}

const batchSwitchDomain = async () => {
  if (selectedLinks.value.length === 0) return
  
  ElMessageBox.prompt('请输入新域名ID', '批量切换域名', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^\d+$/,
    inputErrorMessage: '请输入有效的域名ID'
  }).then(async ({ value }) => {
    try {
      const linkIds = selectedLinks.value.map(l => l.id)
      // 这里应该调用批量切换域名的API
      ElMessage.success('批量切换成功')
      loadShortLinks()
    } catch (error) {
      ElMessage.error('批量切换失败')
    }
  })
}

const batchDeleteAction = async () => {
  if (selectedLinks.value.length === 0) return
  
  ElMessageBox.confirm(`确定删除选中的 ${selectedLinks.value.length} 个短链接吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const linkIds = selectedLinks.value.map(l => l.id)
      await batchDeleteShortLinks(linkIds)
      ElMessage.success('批量删除成功')
      loadShortLinks()
    } catch (error) {
      ElMessage.error('批量删除失败')
    }
  })
}

// 复制链接
const copyShortLink = (link) => {
  copyToClipboard(link.full_url)
}

const copyFullUrl = (link) => {
  copyToClipboard(link.full_url)
}

const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('复制成功')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 导出数据
const exportLinks = async () => {
  try {
    const response = await exportShortLinks(filters.value)
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `short_links_${new Date().toISOString().split('T')[0]}.xlsx`
    a.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 筛选和分页
const resetFilters = () => {
  filters.value = {
    link_type: '',
    status: '',
    domain_id: '',
    date_range: '',
    keyword: ''
  }
  loadShortLinks()
}

const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  loadShortLinks()
}

const handleCurrentChange = (page) => {
  pagination.value.current = page
  loadShortLinks()
}

// 辅助方法
const resetForm = () => {
  linkForm.value = {
    original_url: '',
    link_type: 'recruit',
    domain_id: '',
    custom_code: '',
    expires_at: '',
    remarks: ''
  }
  // 在 composition API 中，需要使用 nextTick 和 ref
  // this.$nextTick(() => {
  //   this.$refs.linkForm && this.$refs.linkForm.clearValidate()
  // })
}

const showHelpDialog = () => {
  helpVisible.value = true
}

const getLinkTypeName = (type) => {
  const types = {
    'recruit': '推广链接',
    'payment': '支付链接',
    'other': '其他链接'
  }
  return types[type] || type
}

const getLinkTypeColor = (type) => {
  const colors = {
    'recruit': 'primary',
    'payment': 'success',
    'other': 'info'
  }
  return colors[type] || ''
}

const getStatusName = (status) => {
  const statuses = {
    1: '正常',
    2: '异常',
    3: '禁用',
    4: '过期'
  }
  return statuses[status] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  }
  return colors[status] || ''
}

const getDomainHealthColor = (score) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'primary'
  if (score >= 60) return 'warning'
  return 'danger'
}

const truncateUrl = (url, length) => {
  if (!url) return ''
  return url.length > length ? url.substring(0, length) + '...' : url
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.short-link-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  
  .page-title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }
    
    .page-desc {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .page-actions {
    display: flex;
    gap: 10px;
  }
}

.help-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .help-content {
    .help-tips {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .tip-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #606266;
        
        i {
          font-size: 16px;
        }
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;
  
  .filter-form {
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.short-code-info {
  display: flex;
  align-items: center;
  
  .short-code {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    color: #303133;
    font-weight: 500;
  }
}

.full-url-info {
  display: flex;
  align-items: center;
  
  .full-url {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }
}

.original-url {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

.domain-info {
  display: flex;
  align-items: center;
  
  .domain-text {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    color: #303133;
  }
}

.click-count {
  font-weight: 500;
  color: #409eff;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.switch-domain-content {
  .form-tip {
    margin-top: 10px;
  }
}

.stats-content {
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
      color: #303133;
    }
    
    .stats-summary {
      display: flex;
      gap: 20px;
      
      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .summary-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 5px;
        }
        
        .summary-value {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }
  
  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border-radius: 4px;
    color: #909399;
  }
}

.qrcode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .qrcode-image {
    margin-bottom: 20px;
    
    img {
      width: 200px;
      height: 200px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }
  }
  
  .qrcode-info {
    text-align: center;
    
    p {
      margin: 8px 0;
      font-size: 14px;
      color: #606266;
    }
  }
}

.help-detail {
  h3 {
    color: #303133;
    margin: 20px 0 15px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  ul {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 15px;
    
    li {
      margin-bottom: 8px;
    }
  }
  
  p {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 15px;
  }
}
</style> 