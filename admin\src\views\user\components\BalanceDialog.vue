<template>
  <el-dialog
    v-model="dialogVisible"
    title="调整用户余额"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="balance-info">
      <div class="user-info">
        <el-avatar :src="userData.avatar" :alt="userData.username" size="large">
          {{ userData.username?.charAt(0).toUpperCase() }}
        </el-avatar>
        <div class="user-details">
          <div class="username">{{ userData.username }}</div>
          <div class="name">{{ userData.name }}</div>
        </div>
      </div>
      
      <div class="current-balance">
        <div class="balance-item">
          <span class="label">当前余额：</span>
          <span class="value">¥{{ (userData.balance || 0).toFixed(2) }}</span>
        </div>
        <div class="balance-item">
          <span class="label">冻结余额：</span>
          <span class="value">¥{{ (userData.frozen_balance || 0).toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="balance-form"
    >
      <el-form-item label="调整类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio label="increase">增加余额</el-radio>
          <el-radio label="decrease">减少余额</el-radio>
          <el-radio label="set">设置余额</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="调整金额" prop="amount">
        <el-input-number
          v-model="formData.amount"
          :min="0.01"
          :max="999999"
          :precision="2"
          style="width: 100%"
          placeholder="请输入调整金额"
        />
      </el-form-item>

      <el-form-item label="调整原因" prop="reason">
        <el-select v-model="formData.reason" placeholder="请选择调整原因" style="width: 100%">
          <el-option label="系统调整" value="system_adjust" />
          <el-option label="充值" value="recharge" />
          <el-option label="提现" value="withdraw" />
          <el-option label="佣金结算" value="commission" />
          <el-option label="退款" value="refund" />
          <el-option label="奖励" value="reward" />
          <el-option label="惩罚" value="penalty" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注说明" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入调整说明"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 预览调整后的余额 -->
      <el-form-item label="调整后余额">
        <div class="preview-balance">
          <span class="preview-value" :class="{ 'negative': previewBalance < 0 }">
            ¥{{ previewBalance.toFixed(2) }}
          </span>
          <span v-if="previewBalance < 0" class="warning-text">
            (余额不足，请检查调整金额)
          </span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="loading" 
          :disabled="previewBalance < 0"
          @click="handleSubmit"
        >
          确认调整
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { adjustUserBalance } from '@/api/user'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref(null)
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 表单数据
const formData = reactive({
  type: 'increase',
  amount: 0,
  reason: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入调整金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '调整金额必须大于0.01', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请选择调整原因', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入调整说明', trigger: 'blur' },
    { min: 5, max: 200, message: '说明长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 预览调整后的余额
const previewBalance = computed(() => {
  const currentBalance = props.userData.balance || 0
  const amount = formData.amount || 0
  
  switch (formData.type) {
    case 'increase':
      return currentBalance + amount
    case 'decrease':
      return currentBalance - amount
    case 'set':
      return amount
    default:
      return currentBalance
  }
})

// 监听对话框显示状态，重置表单
watch(dialogVisible, (visible) => {
  if (visible) {
    // 重置表单数据
    Object.assign(formData, {
      type: 'increase',
      amount: 0,
      reason: '',
      remark: ''
    })
  }
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 二次确认
    const confirmText = `确定要${getTypeText(formData.type)} ¥${formData.amount.toFixed(2)} 吗？`
    await ElMessageBox.confirm(confirmText, '确认调整', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true

    const submitData = {
      type: formData.type,
      amount: formData.amount,
      reason: formData.reason,
      remark: formData.remark
    }

    await adjustUserBalance(props.userData.id, submitData)
    ElMessage.success('余额调整成功')

    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('调整失败:', error)
      if (error.response?.data?.message) {
        ElMessage.error(error.response.data.message)
      } else {
        ElMessage.error('余额调整失败')
      }
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}

// 获取调整类型文本
const getTypeText = (type) => {
  const typeMap = {
    increase: '增加余额',
    decrease: '减少余额',
    set: '设置余额'
  }
  return typeMap[type] || '调整余额'
}
</script>

<style lang="scss" scoped>
.balance-info {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .user-details {
      margin-left: 12px;
      
      .username {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .name {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .current-balance {
    display: flex;
    gap: 24px;
    
    .balance-item {
      .label {
        color: #666;
        font-size: 14px;
      }
      
      .value {
        color: #333;
        font-size: 16px;
        font-weight: 600;
        margin-left: 8px;
      }
    }
  }
}

.balance-form {
  .preview-balance {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .preview-value {
      font-size: 18px;
      font-weight: 700;
      color: #52c41a;
      
      &.negative {
        color: #f5222d;
      }
    }
    
    .warning-text {
      color: #f5222d;
      font-size: 12px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .balance-info {
    .current-balance {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style>