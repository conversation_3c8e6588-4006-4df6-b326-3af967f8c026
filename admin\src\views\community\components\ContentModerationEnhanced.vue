<template>
  <div class="content-moderation-enhanced">
    <!-- 审核统计面板 -->
    <div class="stats-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card pending">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.pending }}</div>
                <div class="stats-label">待审核</div>
              </div>
            </div>
            <div class="stats-trend">
              <span class="trend-text">较昨日 +{{ stats.pendingIncrease }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card approved">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.approved }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </div>
            <div class="stats-trend">
              <span class="trend-text">通过率 {{ stats.approvalRate }}%</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card rejected">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.rejected }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
            <div class="stats-trend">
              <span class="trend-text">拒绝率 {{ stats.rejectionRate }}%</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card auto">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Robot /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.autoProcessed }}</div>
                <div class="stats-label">自动处理</div>
              </div>
            </div>
            <div class="stats-trend">
              <span class="trend-text">准确率 {{ stats.autoAccuracy }}%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 智能审核工具栏 -->
    <div class="smart-toolbar">
      <el-card class="toolbar-card">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <el-button-group>
              <el-button 
                type="primary" 
                @click="enableAutoReview"
                :loading="autoReviewLoading"
              >
                <el-icon><MagicStick /></el-icon>
                启用智能审核
              </el-button>
              <el-button @click="showSensitiveWordsDialog = true">
                <el-icon><Warning /></el-icon>
                敏感词管理
              </el-button>
              <el-button @click="showRulesDialog = true">
                <el-icon><Setting /></el-icon>
                审核规则
              </el-button>
            </el-button-group>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索内容、用户、群组..."
              style="width: 300px"
              @keyup.enter="handleSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="filterType" placeholder="内容类型" style="width: 120px; margin-left: 12px;">
              <el-option label="全部" value="" />
              <el-option label="文本" value="text" />
              <el-option label="图片" value="image" />
              <el-option label="视频" value="video" />
              <el-option label="链接" value="link" />
            </el-select>
            <el-select v-model="filterRisk" placeholder="风险等级" style="width: 120px; margin-left: 12px;">
              <el-option label="全部" value="" />
              <el-option label="高风险" value="high" />
              <el-option label="中风险" value="medium" />
              <el-option label="低风险" value="low" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-actions" v-if="selectedContent.length > 0">
      <el-card class="batch-card">
        <div class="batch-content">
          <div class="batch-info">
            <span>已选择 {{ selectedContent.length }} 项内容</span>
          </div>
          <div class="batch-buttons">
            <el-button-group>
              <el-button 
                type="success" 
                @click="batchApprove"
                :loading="batchLoading"
              >
                <el-icon><Check /></el-icon>
                批量通过
              </el-button>
              <el-button 
                type="danger" 
                @click="batchReject"
                :loading="batchLoading"
              >
                <el-icon><Close /></el-icon>
                批量拒绝
              </el-button>
              <el-button 
                type="warning" 
                @click="batchFlag"
                :loading="batchLoading"
              >
                <el-icon><Flag /></el-icon>
                标记关注
              </el-button>
              <el-button 
                @click="batchIntelligentReview"
                :loading="batchLoading"
              >
                <el-icon><MagicStick /></el-icon>
                智能审核
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 内容列表 -->
    <div class="content-list">
      <el-card class="list-card">
        <template #header>
          <div class="list-header">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
              <el-tab-pane :label="`待审核 (${counts.pending})`" name="pending" />
              <el-tab-pane :label="`已通过 (${counts.approved})`" name="approved" />
              <el-tab-pane :label="`已拒绝 (${counts.rejected})`" name="rejected" />
              <el-tab-pane :label="`已标记 (${counts.flagged})`" name="flagged" />
            </el-tabs>
            <div class="header-actions">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="list">列表</el-radio-button>
                <el-radio-button label="card">卡片</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <el-table
            v-loading="loading"
            :data="contentList"
            @selection-change="handleSelectionChange"
            row-key="id"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="内容预览" min-width="300">
              <template #default="{ row }">
                <div class="content-preview">
                  <div class="content-type-icon">
                    <el-icon v-if="row.type === 'text'"><Document /></el-icon>
                    <el-icon v-else-if="row.type === 'image'"><Picture /></el-icon>
                    <el-icon v-else-if="row.type === 'video'"><VideoPlay /></el-icon>
                    <el-icon v-else><Link /></el-icon>
                  </div>
                  <div class="content-details">
                    <div class="content-text">{{ row.content }}</div>
                    <div class="content-meta">
                      <el-tag size="small" :type="getRiskTagType(row.risk_level)">
                        {{ getRiskText(row.risk_level) }}
                      </el-tag>
                      <span class="meta-item">
                        <el-icon><User /></el-icon>
                        {{ row.author.name }}
                      </span>
                      <span class="meta-item">
                        <el-icon><ChatDotRound /></el-icon>
                        {{ row.group.name }}
                      </span>
                      <span class="meta-item">
                        <el-icon><Clock /></el-icon>
                        {{ formatDate(row.created_at) }}
                      </span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="AI分析" width="200">
              <template #default="{ row }">
                <div class="ai-analysis" v-if="row.ai_analysis">
                  <div class="analysis-score">
                    <el-progress
                      :percentage="row.ai_analysis.confidence * 100"
                      :color="getConfidenceColor(row.ai_analysis.confidence)"
                      :stroke-width="6"
                    />
                    <span class="score-text">置信度: {{ (row.ai_analysis.confidence * 100).toFixed(1) }}%</span>
                  </div>
                  <div class="analysis-tags">
                    <el-tag
                      v-for="tag in row.ai_analysis.tags"
                      :key="tag"
                      size="small"
                      type="info"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
                <el-button
                  v-else
                  size="small"
                  @click="runAIAnalysis(row)"
                  :loading="row.analyzing"
                >
                  AI分析
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button-group v-if="row.status === 'pending'">
                    <el-button
                      type="success"
                      size="small"
                      @click="handleApprove(row)"
                    >
                      通过
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleReject(row)"
                    >
                      拒绝
                    </el-button>
                  </el-button-group>
                  <el-button
                    size="small"
                    @click="viewContentDetail(row)"
                  >
                    详情
                  </el-button>
                  <el-dropdown @command="handleCommand">
                    <el-button size="small">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="`flag-${row.id}`">
                          标记关注
                        </el-dropdown-item>
                        <el-dropdown-item :command="`history-${row.id}`">
                          审核历史
                        </el-dropdown-item>
                        <el-dropdown-item :command="`similar-${row.id}`">
                          相似内容
                        </el-dropdown-item>
                        <el-dropdown-item :command="`user-${row.author.id}`" divided>
                          用户详情
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="20">
            <el-col :span="8" v-for="item in contentList" :key="item.id">
              <ContentCard
                :content="item"
                :selected="selectedContent.includes(item.id)"
                @select="toggleContentSelection"
                @action="handleContentAction"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 敏感词管理对话框 -->
    <SensitiveWordsDialog
      v-model="showSensitiveWordsDialog"
      @update="handleSensitiveWordsUpdate"
    />

    <!-- 审核规则对话框 -->
    <ReviewRulesDialog
      v-model="showRulesDialog"
      @update="handleRulesUpdate"
    />

    <!-- 内容详情对话框 -->
    <ContentDetailDialog
      v-model="showContentDetail"
      :content="currentContent"
      @action="handleDetailAction"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Check, Close, Robot, MagicStick, Warning, Setting, Search,
  Flag, Document, Picture, VideoPlay, Link, User, ChatDotRound,
  ArrowDown
} from '@element-plus/icons-vue'

// 组件导入
import ContentCard from './ContentCard.vue'
import SensitiveWordsDialog from './SensitiveWordsDialog.vue'
import ReviewRulesDialog from './ReviewRulesDialog.vue'
import ContentDetailDialog from './ContentDetailDialog.vue'

// API导入
import {
  intelligentContentReview,
  batchReviewContent,
  getContentReviewStats,
  getSensitiveWords,
  getReviewRules
} from '@/api/community-enhanced'

// 响应式数据
const loading = ref(false)
const batchLoading = ref(false)
const autoReviewLoading = ref(false)
const activeTab = ref('pending')
const viewMode = ref('list')
const searchKeyword = ref('')
const filterType = ref('')
const filterRisk = ref('')

const selectedContent = ref([])
const contentList = ref([])
const total = ref(0)
const currentContent = ref({})

const showSensitiveWordsDialog = ref(false)
const showRulesDialog = ref(false)
const showContentDetail = ref(false)

const pagination = reactive({
  page: 1,
  size: 20
})

const stats = reactive({
  pending: 0,
  approved: 0,
  rejected: 0,
  autoProcessed: 0,
  pendingIncrease: 0,
  approvalRate: 0,
  rejectionRate: 0,
  autoAccuracy: 0
})

const counts = reactive({
  pending: 0,
  approved: 0,
  rejected: 0,
  flagged: 0
})

// 模拟数据
const mockContentList = {
  pending: [
    {
      id: 1,
      content: '这是一条待审核的内容，包含一些可能敏感的词汇...',
      type: 'text',
      risk_level: 'medium',
      author: { id: 1, name: '用户A' },
      group: { id: 1, name: '技术交流群' },
      status: 'pending',
      created_at: new Date().toISOString(),
      ai_analysis: {
        confidence: 0.75,
        tags: ['可能违规', '需人工审核']
      }
    }
  ],
  approved: [],
  rejected: [],
  flagged: []
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    contentList.value = mockContentList[activeTab.value] || []
    total.value = contentList.value.length
    
    // 更新计数
    Object.keys(mockContentList).forEach(key => {
      counts[key] = mockContentList[key].length
    })
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // 模拟统计数据
    Object.assign(stats, {
      pending: 25,
      approved: 156,
      rejected: 12,
      autoProcessed: 89,
      pendingIncrease: 5,
      approvalRate: 92.8,
      rejectionRate: 7.2,
      autoAccuracy: 94.5
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleTabClick = () => {
  pagination.page = 1
  selectedContent.value = []
  loadData()
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleSelectionChange = (selection) => {
  // 确保selection是数组
  const validSelection = Array.isArray(selection) ? selection : []
  selectedContent.value = validSelection.map(item => item.id)
}

const toggleContentSelection = (contentId) => {
  const index = selectedContent.value.indexOf(contentId)
  if (index > -1) {
    selectedContent.value.splice(index, 1)
  } else {
    selectedContent.value.push(contentId)
  }
}

const enableAutoReview = async () => {
  autoReviewLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('智能审核已启用，正在处理待审核内容...')
    loadData()
  } catch (error) {
    ElMessage.error('启用智能审核失败')
  } finally {
    autoReviewLoading.value = false
  }
}

const batchApprove = async () => {
  if (selectedContent.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要批量通过选中的 ${selectedContent.value.length} 项内容吗？`,
      '批量通过确认'
    )
    
    batchLoading.value = true
    await batchReviewContent(selectedContent.value, 'approve')
    ElMessage.success('批量通过成功')
    selectedContent.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const batchReject = async () => {
  if (selectedContent.value.length === 0) return
  
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因（可选）',
      '批量拒绝确认'
    )
    
    batchLoading.value = true
    await batchReviewContent(selectedContent.value, 'reject', reason)
    ElMessage.success('批量拒绝成功')
    selectedContent.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const batchFlag = async () => {
  if (selectedContent.value.length === 0) return
  
  batchLoading.value = true
  try {
    await batchReviewContent(selectedContent.value, 'flag')
    ElMessage.success('批量标记成功')
    selectedContent.value = []
    loadData()
  } catch (error) {
    ElMessage.error('批量操作失败')
  } finally {
    batchLoading.value = false
  }
}

const batchIntelligentReview = async () => {
  if (selectedContent.value.length === 0) return
  
  batchLoading.value = true
  try {
    // 模拟智能审核
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('智能审核完成')
    selectedContent.value = []
    loadData()
  } catch (error) {
    ElMessage.error('智能审核失败')
  } finally {
    batchLoading.value = false
  }
}

const handleApprove = async (content) => {
  try {
    ElMessage.success(`内容已通过审核`)
    loadData()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleReject = async (content) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因（可选）',
      '拒绝确认'
    )
    ElMessage.success(`内容已拒绝`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const runAIAnalysis = async (content) => {
  content.analyzing = true
  try {
    await intelligentContentReview(content.id)
    ElMessage.success('AI分析完成')
    loadData()
  } catch (error) {
    ElMessage.error('AI分析失败')
  } finally {
    content.analyzing = false
  }
}

const viewContentDetail = (content) => {
  currentContent.value = content
  showContentDetail.value = true
}

const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const contentId = parseInt(id)
  
  switch (action) {
    case 'flag':
      // 标记内容
      ElMessage.success('内容已标记关注')
      break
    case 'history':
      // 查看审核历史
      ElMessage.info('查看审核历史功能开发中...')
      break
    case 'similar':
      // 查找相似内容
      ElMessage.info('查找相似内容功能开发中...')
      break
    case 'user':
      // 查看用户详情
      ElMessage.info('查看用户详情功能开发中...')
      break
  }
}

const handleContentAction = (action, content) => {
  switch (action) {
    case 'approve':
      handleApprove(content)
      break
    case 'reject':
      handleReject(content)
      break
    case 'detail':
      viewContentDetail(content)
      break
  }
}

const handleDetailAction = (action, content) => {
  showContentDetail.value = false
  handleContentAction(action, content)
}

const handleSensitiveWordsUpdate = () => {
  ElMessage.success('敏感词库已更新')
  loadData()
}

const handleRulesUpdate = () => {
  ElMessage.success('审核规则已更新')
  loadData()
}

const handlePageSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

// 工具函数
const getRiskTagType = (riskLevel) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return types[riskLevel] || 'info'
}

const getRiskText = (riskLevel) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[riskLevel] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    flagged: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    flagged: '已标记'
  }
  return texts[status] || '未知'
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
  loadStats()
})
</script>

<style lang="scss" scoped>
.content-moderation-enhanced {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stats-panel {
  margin-bottom: 20px;
  
  .stats-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .stats-content {
      display: flex;
      align-items: center;
      padding: 20px;
      
      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 16px;
      }
      
      .stats-info {
        flex: 1;
        
        .stats-value {
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }
        
        .stats-label {
          font-size: 14px;
          color: #64748b;
        }
      }
    }
    
    .stats-trend {
      padding: 12px 20px;
      background: rgba(0, 0, 0, 0.05);
      
      .trend-text {
        font-size: 12px;
        color: #64748b;
      }
    }
    
    &.pending .stats-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    &.approved .stats-icon {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    &.rejected .stats-icon {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    &.auto .stats-icon {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
  }
}

.smart-toolbar {
  margin-bottom: 20px;
  
  .toolbar-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .toolbar-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      
      .toolbar-right {
        display: flex;
        align-items: center;
      }
    }
  }
}

.batch-actions {
  margin-bottom: 20px;
  
  .batch-card {
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .batch-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .batch-info {
        font-weight: 600;
      }
    }
  }
}

.content-list {
  .list-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
    
    .list-view {
      :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;
        
        .el-table__header {
          background: #f8fafc;
        }
        
        .el-table__row {
          transition: all 0.3s ease;
          
          &:hover {
            background: #f8fafc;
          }
        }
      }
      
      .content-preview {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        
        .content-type-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
          font-size: 18px;
          flex-shrink: 0;
        }
        
        .content-details {
          flex: 1;
          min-width: 0;
          
          .content-text {
            font-size: 14px;
            color: #374151;
            margin-bottom: 8px;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          
          .content-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: #6b7280;
            
            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }
      }
      
      .ai-analysis {
        .analysis-score {
          margin-bottom: 8px;
          
          .score-text {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
            display: block;
          }
        }
        
        .analysis-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
      
      .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    
    .card-view {
      padding: 20px 0;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-panel {
    .el-col {
      margin-bottom: 16px;
    }
  }
  
  .smart-toolbar {
    .toolbar-content {
      flex-direction: column;
      gap: 16px;
      
      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .content-moderation-enhanced {
    padding: 12px;
  }
  
  .batch-actions {
    .batch-content {
      flex-direction: column;
      gap: 12px;
    }
  }
  
  .content-list {
    .list-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card,
.toolbar-card,
.batch-card,
.list-card {
  animation: fadeInUp 0.6s ease-out;
}
</style>
