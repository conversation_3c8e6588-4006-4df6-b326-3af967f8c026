<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="batch-content">
      <!-- 操作确认信息 -->
      <el-alert
        :title="getAlertTitle()"
        :type="getAlertType()"
        :closable="false"
        show-icon
        class="alert-info"
      >
        <p>{{ getAlertDescription() }}</p>
        <p class="selected-count">已选择 {{ selectedLinks.length }} 个推广链接</p>
      </el-alert>

      <!-- 选中的链接列表 -->
      <el-card class="links-card" shadow="never">
        <template #header>
          <span>选中的链接</span>
        </template>
        
        <div class="links-list">
          <div
            v-for="link in selectedLinks.slice(0, 5)"
            :key="link.id"
            class="link-item"
          >
            <div class="link-info">
              <span class="link-title">{{ link.title || '未设置标题' }}</span>
              <el-tag size="small" type="info">{{ link.short_code }}</el-tag>
            </div>
            <div class="link-meta">
              <span class="group-name">{{ link.group?.title }}</span>
              <span class="click-count">{{ link.click_count }} 次点击</span>
            </div>
          </div>
          
          <div v-if="selectedLinks.length > 5" class="more-links">
            还有 {{ selectedLinks.length - 5 }} 个链接...
          </div>
        </div>
      </el-card>

      <!-- 延长有效期的额外配置 -->
      <el-card v-if="action === 'extend_expiry'" class="config-card" shadow="never">
        <template #header>
          <span>延长配置</span>
        </template>
        
        <el-form :model="extendConfig" label-width="100px">
          <el-form-item label="延长天数" required>
            <el-input-number
              v-model="extendConfig.days"
              :min="1"
              :max="365"
              :step="1"
              style="width: 200px"
            />
            <span class="input-suffix">天</span>
          </el-form-item>
          
          <el-form-item>
            <el-alert
              :title="`延长后的过期时间将增加 ${extendConfig.days} 天`"
              type="info"
              :closable="false"
              show-icon
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作风险提示 -->
      <el-card v-if="isDestructiveAction()" class="warning-card" shadow="never">
        <el-alert
          title="操作风险提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <ul class="warning-list">
            <li v-if="action === 'delete'">删除操作不可恢复，请谨慎操作</li>
            <li v-if="action === 'disable'">禁用后链接将无法访问</li>
            <li>批量操作可能需要一些时间完成</li>
            <li>操作过程中请勿关闭页面</li>
          </ul>
        </el-alert>
      </el-card>

      <!-- 操作统计预览 -->
      <div class="stats-preview">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ selectedLinks.length }}</div>
              <div class="stat-label">待操作链接</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ getTotalClicks() }}</div>
              <div class="stat-label">总点击量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ getActiveLinksCount() }}</div>
              <div class="stat-label">活跃链接</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          :type="getConfirmButtonType()"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ getConfirmButtonText() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePromotionStore } from '@/stores/promotion'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  action: {
    type: String,
    required: true
  },
  selectedLinks: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 状态管理
const promotionStore = usePromotionStore()

// 响应式数据
const loading = ref(false)

// 延长有效期配置
const extendConfig = reactive({
  days: 30
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getDialogTitle = () => {
  const titleMap = {
    enable: '批量启用推广链接',
    disable: '批量禁用推广链接',
    delete: '批量删除推广链接',
    extend_expiry: '批量延长有效期'
  }
  return titleMap[props.action] || '批量操作'
}

const getAlertTitle = () => {
  const titleMap = {
    enable: '启用确认',
    disable: '禁用确认',
    delete: '删除确认',
    extend_expiry: '延长确认'
  }
  return titleMap[props.action] || '操作确认'
}

const getAlertType = () => {
  const typeMap = {
    enable: 'success',
    disable: 'warning',
    delete: 'error',
    extend_expiry: 'info'
  }
  return typeMap[props.action] || 'info'
}

const getAlertDescription = () => {
  const descMap = {
    enable: '启用后，这些推广链接将可以正常访问和统计点击量。',
    disable: '禁用后，这些推广链接将无法访问，但数据会保留。',
    delete: '删除后，这些推广链接及其统计数据将被永久删除，无法恢复。',
    extend_expiry: '将为选中的推广链接延长有效期，延长后可以继续使用。'
  }
  return descMap[props.action] || '确认执行此批量操作？'
}

const getConfirmButtonType = () => {
  const typeMap = {
    enable: 'success',
    disable: 'warning',
    delete: 'danger',
    extend_expiry: 'primary'
  }
  return typeMap[props.action] || 'primary'
}

const getConfirmButtonText = () => {
  const textMap = {
    enable: '确认启用',
    disable: '确认禁用',
    delete: '确认删除',
    extend_expiry: '确认延长'
  }
  return textMap[props.action] || '确认操作'
}

const isDestructiveAction = () => {
  return ['delete', 'disable'].includes(props.action)
}

const getTotalClicks = () => {
  return props.selectedLinks.reduce((total, link) => total + (link.click_count || 0), 0)
}

const getActiveLinksCount = () => {
  return props.selectedLinks.filter(link => link.is_active && !link.is_expired).length
}

const handleConfirm = async () => {
  // 对于危险操作，再次确认
  if (isDestructiveAction()) {
    try {
      await ElMessageBox.confirm(
        `确定要${props.action === 'delete' ? '删除' : '禁用'}这 ${props.selectedLinks.length} 个推广链接吗？`,
        '最终确认',
        {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      )
    } catch {
      return
    }
  }

  loading.value = true
  try {
    const requestData = {
      action: props.action,
      link_ids: props.selectedLinks.map(link => link.id)
    }

    // 如果是延长有效期，添加天数参数
    if (props.action === 'extend_expiry') {
      requestData.extend_days = extendConfig.days
    }

    const response = await promotionStore.batchAction(requestData)
    
    const successMessage = getSuccessMessage(response)
    ElMessage.success(successMessage)
    
    emit('success')
    
  } catch (error) {
    ElMessage.error('批量操作失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const getSuccessMessage = (response) => {
  const { success_count, failed_count } = response
  const actionText = {
    enable: '启用',
    disable: '禁用',
    delete: '删除',
    extend_expiry: '延长有效期'
  }[props.action] || '操作'

  if (failed_count === 0) {
    return `成功${actionText} ${success_count} 个推广链接`
  } else {
    return `${actionText}完成：成功 ${success_count} 个，失败 ${failed_count} 个`
  }
}
</script>

<style scoped>
.batch-content {
  padding: 10px 0;
}

.alert-info {
  margin-bottom: 20px;
}

.selected-count {
  margin: 10px 0 0 0;
  font-weight: 600;
  color: #409EFF;
}

.links-card,
.config-card,
.warning-card {
  margin-bottom: 20px;
}

.links-list {
  max-height: 200px;
  overflow-y: auto;
}

.link-item {
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.link-item:last-child {
  border-bottom: none;
}

.link-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}

.link-title {
  font-weight: 600;
  color: #303133;
  flex: 1;
  margin-right: 10px;
}

.link-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.more-links {
  text-align: center;
  padding: 15px 0;
  color: #909399;
  font-style: italic;
}

.input-suffix {
  margin-left: 10px;
  color: #909399;
}

.warning-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.warning-list li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.stats-preview {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-alert__content) {
  padding-left: 0;
}
</style>