import{_ as e}from"./index-DtXAftX0.js";import{r as t,c as l,d as a,e as n,k as i,l as u,A as s,t as r,G as o,E as c,z as d,D as m}from"./vue-vendor-Dy164gUc.js";import{at as p,bb as v,p as f,U as _,Q as y,R as h}from"./element-plus-h2SQQM64.js";const b={class:"rich-text-editor"},g={class:"editor-toolbar"},x=["innerHTML"],T={class:"editor-footer"},w={class:"word-count"},L=e({__name:"RichTextEditor",props:{modelValue:{type:String,default:""},height:{type:Number,default:200},placeholder:{type:String,default:"请输入内容..."},maxLength:{type:Number,default:1e4}},emits:["update:modelValue","focus","blur"],setup(e,{expose:L,emit:C}){const k=e,z=C,M=t(null),B=t(""),H=t(!1),$=l(()=>(M.value?.innerText||"").length);a(()=>k.modelValue,e=>{e!==B.value&&(B.value=e,M.value&&(M.value.innerHTML=e))},{immediate:!0});const j=(e,t=null)=>{document.execCommand(e,!1,t),M.value?.focus(),N()},E=async()=>{try{const{value:e}=await h.prompt("请输入链接地址","插入链接",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入有效的链接地址"});e&&j("createLink",e)}catch(e){}},P=async()=>{try{const{value:e}=await h.prompt("请输入图片地址","插入图片",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,inputErrorMessage:"请输入有效的图片地址"});e&&j("insertImage",e)}catch(e){}},V=async()=>{try{const{value:e}=await h.prompt("请输入视频地址","插入视频",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入有效的视频地址",inputPlaceholder:"支持 MP4、优酷、腾讯视频、B站等链接"});e&&R(e)}catch(e){}},R=e=>{let t="";if(D(e))t=`<video controls style="max-width: 100%; height: auto; margin: 8px 0;">\n      <source src="${e}" type="video/mp4">\n      您的浏览器不支持视频播放。\n    </video>`;else if(I(e)){const l=A(e);l&&(t=`<iframe src="//player.bilibili.com/player.html?bvid=${l}" \n        scrolling="no" border="0" frameborder="no" framespacing="0" \n        allowfullscreen="true" style="width: 100%; height: 400px; margin: 8px 0;">\n      </iframe>`)}else if(U(e)){const l=F(e);l&&(t=`<iframe src="https://www.youtube.com/embed/${l}" \n        frameborder="0" allowfullscreen style="width: 100%; height: 400px; margin: 8px 0;">\n      </iframe>`)}else t=`<div class="video-container" style="margin: 8px 0;">\n      <p>视频链接：<a href="${e}" target="_blank">${e}</a></p>\n      <p style="color: #909399; font-size: 12px;">点击链接观看视频</p>\n    </div>`;t&&(document.execCommand("insertHTML",!1,t),N())},D=e=>/\.(mp4|webm|ogg|avi|mov|wmv|flv)$/i.test(e),I=e=>/bilibili\.com\/video\/(BV\w+|av\d+)/i.test(e),A=e=>{const t=e.match(/bilibili\.com\/video\/(BV\w+)/i);return t?t[1]:null},U=e=>/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i.test(e),F=e=>{const t=e.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i);return t?t[1]:null},N=()=>{const e=M.value?.innerHTML||"";B.value=e,z("update:modelValue",e),$.value>k.maxLength&&y.warning(`内容长度不能超过 ${k.maxLength} 字`)},S=()=>{H.value=!0,z("focus")},Z=()=>{H.value=!1,z("blur")},G=e=>{e.preventDefault();const t=e.clipboardData.getData("text/plain").replace(/<[^>]*>/g,"");document.execCommand("insertText",!1,t),N()},O=()=>{M.value&&(B.value&&""!==B.value.trim()||(M.value.innerHTML=`<p style="color: #c0c4cc; pointer-events: none;">${k.placeholder}</p>`))},Q=()=>{M.value&&M.value.innerHTML.includes(k.placeholder)&&(M.value.innerHTML="")};return n(()=>{M.value&&(M.value.innerHTML=B.value||"",B.value||O(),M.value.addEventListener("focus",Q),M.value.addEventListener("blur",()=>{M.value.innerText.trim()||O()}))}),L({focus:()=>M.value?.focus(),blur:()=>M.value?.blur(),clear:()=>{M.value&&(M.value.innerHTML="",N())},insertText:e=>{j("insertText",e)},getContent:()=>B.value,getTextContent:()=>M.value?.innerText||""}),(t,l)=>{const a=p,n=v;return u(),i("div",b,[s(r("div",g,[c(n,null,{default:d(()=>[c(a,{size:"small",type:"default",onClick:l[0]||(l[0]=e=>j("bold")),title:"粗体"},{default:d(()=>l[9]||(l[9]=[r("strong",null,"B",-1)])),_:1,__:[9]}),c(a,{size:"small",type:"default",onClick:l[1]||(l[1]=e=>j("italic")),title:"斜体"},{default:d(()=>l[10]||(l[10]=[r("em",null,"I",-1)])),_:1,__:[10]}),c(a,{size:"small",type:"default",onClick:l[2]||(l[2]=e=>j("underline")),title:"下划线"},{default:d(()=>l[11]||(l[11]=[r("u",null,"U",-1)])),_:1,__:[11]})]),_:1}),l[21]||(l[21]=r("div",{class:"toolbar-divider"},null,-1)),c(n,null,{default:d(()=>[c(a,{size:"small",type:"default",onClick:l[3]||(l[3]=e=>j("justifyLeft")),title:"左对齐"},{default:d(()=>l[12]||(l[12]=[m(" ← ",-1)])),_:1,__:[12]}),c(a,{size:"small",type:"default",onClick:l[4]||(l[4]=e=>j("justifyCenter")),title:"居中"},{default:d(()=>l[13]||(l[13]=[m(" ↔ ",-1)])),_:1,__:[13]}),c(a,{size:"small",type:"default",onClick:l[5]||(l[5]=e=>j("justifyRight")),title:"右对齐"},{default:d(()=>l[14]||(l[14]=[m(" → ",-1)])),_:1,__:[14]})]),_:1}),l[22]||(l[22]=r("div",{class:"toolbar-divider"},null,-1)),c(n,null,{default:d(()=>[c(a,{size:"small",type:"default",onClick:l[6]||(l[6]=e=>j("insertUnorderedList")),title:"无序列表"},{default:d(()=>l[15]||(l[15]=[m(" • ",-1)])),_:1,__:[15]}),c(a,{size:"small",type:"default",onClick:l[7]||(l[7]=e=>j("insertOrderedList")),title:"有序列表"},{default:d(()=>l[16]||(l[16]=[m(" 1. ",-1)])),_:1,__:[16]})]),_:1}),l[23]||(l[23]=r("div",{class:"toolbar-divider"},null,-1)),c(n,null,{default:d(()=>[c(a,{size:"small",type:"default",onClick:E,title:"插入链接"},{default:d(()=>l[17]||(l[17]=[m(" 🔗 ",-1)])),_:1,__:[17]}),c(a,{size:"small",type:"default",onClick:P,title:"插入图片"},{default:d(()=>l[18]||(l[18]=[m(" 🖼️ ",-1)])),_:1,__:[18]}),c(a,{size:"small",type:"default",onClick:V,title:"插入视频"},{default:d(()=>l[19]||(l[19]=[m(" 🎥 ",-1)])),_:1,__:[19]})]),_:1}),l[24]||(l[24]=r("div",{class:"toolbar-divider"},null,-1)),c(a,{size:"small",type:"default",onClick:l[8]||(l[8]=e=>j("removeFormat")),title:"清除格式"},{default:d(()=>l[20]||(l[20]=[m(" 清除 ",-1)])),_:1,__:[20]})],512),[[o,!0]]),r("div",{ref_key:"editorRef",ref:M,class:"editor-content",style:f({height:e.height+"px"}),contenteditable:"true",onInput:N,onFocus:S,onBlur:Z,onPaste:G,innerHTML:B.value},null,44,x),r("div",T,[r("span",w,_($.value)+" 字",1)])])}}},[["__scopeId","data-v-d559a663"]]);export{L as R};
