<template>
  <div class="group-create-inline">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Plus /></el-icon>
          </div>
          <div class="header-text">
            <h1>{{ isEdit ? '编辑群组' : '创建群组' }}</h1>
            <p>{{ isEdit ? '修改群组信息和配置' : '创建新的社群，开始您的社群运营之旅' }}</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleCancel" class="action-btn secondary">
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit" class="action-btn primary">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新群组' : '创建群组' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-row :gutter="24">
        <!-- 左侧表单区域 -->
        <el-col :span="16">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="create-form"
          >
            <!-- 基础信息卡片 -->
            <el-card class="form-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <el-icon><Setting /></el-icon>
                  <span>基础信息</span>
                </div>
              </template>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="群组名称" prop="name">
                    <el-input
                      v-model="formData.name"
                      placeholder="请输入群组名称"
                      maxlength="50"
                      show-word-limit
                      @input="updatePreview"
                    />
                    <div class="form-tip">
                      💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="群组分类" prop="category">
                    <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%" @change="updatePreview">
                      <el-option label="创业交流" value="startup" />
                      <el-option label="投资理财" value="finance" />
                      <el-option label="科技互联网" value="tech" />
                      <el-option label="教育培训" value="education" />
                      <el-option label="生活服务" value="life" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="入群价格" prop="price">
                    <el-input-number
                      v-model="formData.price"
                      :min="0"
                      :max="9999"
                      :precision="2"
                      style="width: 100%"
                      @change="updatePreview"
                    />
                    <div class="form-tip">设置为0表示免费群组</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大成员数" prop="max_members">
                    <el-input-number
                      v-model="formData.max_members"
                      :min="10"
                      :max="10000"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="群组描述" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入群组描述"
                  maxlength="500"
                  show-word-limit
                  @input="updatePreview"
                />
              </el-form-item>
            </el-card>

            <!-- 群主信息卡片 -->
            <el-card class="form-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <el-icon><User /></el-icon>
                  <span>群主信息</span>
                </div>
              </template>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="群主名称" prop="owner_name">
                    <el-input
                      v-model="formData.owner_name"
                      placeholder="请输入群主名称"
                      maxlength="50"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="群主头像" prop="owner_avatar">
                    <el-upload
                      class="avatar-uploader"
                      :show-file-list="false"
                      :before-upload="beforeAvatarUpload"
                      action="#"
                    >
                      <img v-if="formData.owner_avatar" :src="formData.owner_avatar" class="avatar" />
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <!-- 其他设置卡片 -->
            <el-card class="form-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <el-icon><Tools /></el-icon>
                  <span>其他设置</span>
                </div>
              </template>
              
              <el-form-item label="群规" prop="rules">
                <el-input
                  v-model="formData.rules"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入群规内容"
                  maxlength="2000"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item label="关键词">
                <el-input
                  v-model="formData.keywords"
                  placeholder="请输入关键词，用逗号分隔"
                  maxlength="200"
                />
              </el-form-item>

              <el-form-item label="群组介绍">
                <el-input
                  v-model="formData.introduction"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入群组详细介绍"
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="群组状态">
                    <el-switch
                      v-model="formData.status"
                      :active-value="1"
                      :inactive-value="0"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="推荐群组">
                    <el-switch
                      v-model="formData.is_recommended"
                      active-text="是"
                      inactive-text="否"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-col>

        <!-- 右侧预览区域 -->
        <el-col :span="8">
          <div class="preview-section">
            <el-card class="preview-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <el-icon><View /></el-icon>
                  <span>实时预览</span>
                </div>
              </template>
              
              <div class="preview-content">
                <div class="preview-item">
                  <div class="preview-label">群组名称：</div>
                  <div class="preview-value">{{ formData.name || '未设置' }}</div>
                </div>
                
                <div class="preview-item">
                  <div class="preview-label">群组分类：</div>
                  <div class="preview-value">{{ getCategoryText(formData.category) || '未设置' }}</div>
                </div>
                
                <div class="preview-item">
                  <div class="preview-label">入群价格：</div>
                  <div class="preview-value">
                    {{ formData.price === 0 ? '免费' : `¥${formData.price}` }}
                  </div>
                </div>
                
                <div class="preview-item">
                  <div class="preview-label">最大成员：</div>
                  <div class="preview-value">{{ formData.max_members }}人</div>
                </div>
                
                <div class="preview-item">
                  <div class="preview-label">群组描述：</div>
                  <div class="preview-value">{{ formData.description || '未设置' }}</div>
                </div>
                
                <div class="preview-item">
                  <div class="preview-label">群主名称：</div>
                  <div class="preview-value">{{ formData.owner_name || '未设置' }}</div>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Setting, User, Tools, View, ArrowLeft, Check
} from '@element-plus/icons-vue'

const props = defineProps({
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['success', 'cancel'])

const formRef = ref(null)
const loading = ref(false)

// 是否为编辑模式
const isEdit = computed(() => props.groupData && props.groupData.id)

// 表单数据
const formData = reactive({
  name: '',
  category: '',
  price: 0,
  max_members: 500,
  description: '',
  owner_name: '',
  owner_avatar: '',
  rules: '',
  keywords: '',
  introduction: '',
  status: 1,
  is_recommended: false
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择群组分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入入群价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: '请输入最大成员数', trigger: 'blur' },
    { type: 'number', min: 10, max: 10000, message: '成员数在10-10000之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入群组描述', trigger: 'blur' },
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ],
  owner_name: [
    { required: true, message: '请输入群主名称', trigger: 'blur' },
    { max: 50, message: '群主名称不能超过 50 个字符', trigger: 'blur' }
  ],
  rules: [
    { required: true, message: '请输入群规', trigger: 'blur' },
    { max: 2000, message: '群规不能超过 2000 个字符', trigger: 'blur' }
  ]
}

// 方法
const updatePreview = () => {
  // 实时更新预览
}

const getCategoryText = (category) => {
  const categoryMap = {
    startup: '创业交流',
    finance: '投资理财',
    tech: '科技互联网',
    education: '教育培训',
    life: '生活服务',
    other: '其他'
  }
  return categoryMap[category]
}

const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const handleCancel = () => {
  emit('cancel')
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 准备提交数据
    const submitData = {
      ...formData,
      // 确保数据格式正确
      price: Number(formData.price),
      max_members: Number(formData.max_members),
      status: Number(formData.status),
      is_recommended: Boolean(formData.is_recommended)
    }

    // 这里应该调用真实的API
    // 目前使用模拟调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '群组更新成功' : '群组创建成功')
    emit('success', submitData)
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.groupData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      name: newData.name || '',
      category: newData.category || '',
      price: newData.price || 0,
      max_members: newData.max_members || 500,
      description: newData.description || '',
      owner_name: newData.owner_name || '',
      owner_avatar: newData.owner_avatar || '',
      rules: newData.rules || '',
      keywords: newData.keywords || '',
      introduction: newData.introduction || '',
      status: newData.status !== undefined ? newData.status : 1,
      is_recommended: newData.is_recommended || false
    })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.group-create-inline {
  min-height: 100vh;
  background: #f5f7fa;

  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          padding: 10px 20px;
          border-radius: 8px;
          font-weight: 500;

          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }

          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
              opacity: 0.9;
            }
          }
        }
      }
    }
  }

  .form-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;

    .form-card {
      margin-bottom: 24px;
      border-radius: 12px;
      border: 1px solid #e4e7ed;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;
      }

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }

    .avatar-uploader {
      :deep(.el-upload) {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
        }
      }

      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 80px;
      }

      .avatar {
        width: 80px;
        height: 80px;
        display: block;
      }
    }

    .preview-section {
      position: sticky;
      top: 24px;

      .preview-card {
        border-radius: 12px;
        border: 1px solid #e4e7ed;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;
        }

        .preview-content {
          .preview-item {
            display: flex;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .preview-label {
              width: 80px;
              font-size: 14px;
              color: #909399;
              flex-shrink: 0;
            }

            .preview-value {
              flex: 1;
              font-size: 14px;
              color: #303133;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .group-create-inline {
    .form-container {
      .el-col:last-child {
        margin-top: 24px;
      }
    }
  }
}

@media (max-width: 768px) {
  .group-create-inline {
    .page-header {
      padding: 16px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;

        .header-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .form-container {
      padding: 16px;
    }
  }
}
</style>
