<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title>支付成功 - {{ $group->title }}</title>
    <meta name="description" content="恭喜您成功加入{{ $group->title }}">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }
        
        /* 成功头部 */
        .success-header {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }
        
        .success-header::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 0;
            right: 0;
            height: 30px;
            background: white;
            border-radius: 30px 30px 0 0;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            animation: successPulse 1.5s ease-in-out;
        }
        
        @keyframes successPulse {
            0% { transform: scale(0.5); opacity: 0; }
            50% { transform: scale(1.1); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .success-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .success-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .order-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .order-item:last-child {
            margin-bottom: 0;
        }
        
        /* 主要内容 */
        .main-content {
            padding: 30px 20px;
        }
        
        .content-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3436;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #00b894;
            margin-right: 10px;
            border-radius: 2px;
        }
        
        /* 二维码区域 */
        .qr-section {
            text-align: center;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            margin-bottom: 30px;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            border: 3px solid #00b894;
            border-radius: 15px;
            padding: 10px;
            background: white;
        }
        
        .qr-description {
            color: #636e72;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .save-qr-btn {
            background: #00b894;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .save-qr-btn:hover {
            background: #00a085;
            transform: translateY(-2px);
        }
        
        /* 下一步操作 */
        .next-steps {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .step-item:last-child {
            margin-bottom: 0;
        }
        
        .step-icon {
            width: 40px;
            height: 40px;
            background: #00b894;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 18px;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: bold;
            color: #2d3436;
            margin-bottom: 5px;
        }
        
        .step-description {
            color: #636e72;
            font-size: 14px;
            line-height: 1.5;
        }
        
        /* 重要提示 */
        .important-tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .tips-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .tips-title::before {
            content: '⚠️';
            margin-right: 8px;
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
        }
        
        .tips-list li {
            color: #856404;
            font-size: 14px;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .tips-list li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #e17055;
            font-weight: bold;
        }
        
        /* 客服区域 */
        .customer-service-section {
            background: linear-gradient(135deg, #ff7675, #fd79a8);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .service-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        .service-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .service-description {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .contact-service-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 10px 25px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .contact-service-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }
        
        /* 底部操作 */
        .bottom-actions {
            padding: 20px;
            text-align: center;
        }
        
        .back-btn {
            background: #ddd;
            color: #636e72;
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #bbb;
        }
        
        /* 浮动分享按钮 */
        .share-float {
            position: fixed;
            right: 20px;
            bottom: 20px;
            width: 60px;
            height: 60px;
            background: #74b9ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(116,185,255,0.4);
            z-index: 999;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .success-header {
                padding: 30px 15px;
            }
            
            .main-content {
                padding: 20px 15px;
            }
            
            .qr-code {
                width: 180px;
                height: 180px;
            }
            
            .success-title {
                font-size: 22px;
            }
        }
        
        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px 20px;
            margin: 20px;
            max-width: 350px;
            width: 100%;
            text-align: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3436;
        }
        
        .modal-qr {
            width: 200px;
            height: 200px;
            margin: 0 auto 15px;
        }
        
        .modal-close {
            background: #ddd;
            color: #636e72;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            cursor: pointer;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 成功头部 -->
        <div class="success-header">
            <div class="success-icon">✅</div>
            <h1 class="success-title">{{ $success_content['title'] }}</h1>
            <p class="success-subtitle">{{ $success_content['subtitle'] }}</p>
            
            <div class="order-info">
                <div class="order-item">
                    <span>订单号：</span>
                    <span>{{ $order->order_no }}</span>
                </div>
                <div class="order-item">
                    <span>支付金额：</span>
                    <span>¥{{ number_format($order->amount, 2) }}</span>
                </div>
                <div class="order-item">
                    <span>支付时间：</span>
                    <span>{{ $order->paid_at->format('Y-m-d H:i:s') }}</span>
                </div>
                <div class="order-item">
                    <span>群组名称：</span>
                    <span>{{ $group->title }}</span>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 二维码区域 -->
            @if($qr_code_url)
            <div class="content-section">
                <h2 class="section-title">扫码加入群组</h2>
                <div class="qr-section">
                    <img src="{{ $qr_code_url }}" alt="群组二维码" class="qr-code" id="groupQrCode">
                    <p class="qr-description">{{ $success_content['description'] }}</p>
                    <button class="save-qr-btn" onclick="saveQrCode()">保存二维码</button>
                </div>
            </div>
            @endif

            <!-- 下一步操作 -->
            <div class="content-section">
                <h2 class="section-title">接下来该做什么？</h2>
                <div class="next-steps">
                    @foreach($next_steps as $step)
                    <div class="step-item">
                        <div class="step-icon">
                            @if($step['icon'] == 'qrcode')
                                📱
                            @elseif($step['icon'] == 'save')
                                💾
                            @elseif($step['icon'] == 'service')
                                👨‍💼
                            @else
                                ✨
                            @endif
                        </div>
                        <div class="step-content">
                            <div class="step-title">{{ $step['title'] }}</div>
                            <div class="step-description">{{ $step['description'] }}</div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- 重要提示 -->
            <div class="content-section">
                <div class="important-tips">
                    <div class="tips-title">重要提示</div>
                    <ul class="tips-list">
                        @foreach($success_content['tips'] as $tip)
                        <li>{{ $tip }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>

            <!-- 客服区域 -->
            @if($customer_service['show'])
            <div class="content-section">
                <div class="customer-service-section">
                    @if($customer_service['avatar'])
                    <img src="{{ $customer_service['avatar'] }}" alt="客服头像" class="service-avatar">
                    @endif
                    <div class="service-title">{{ $customer_service['title'] }}</div>
                    <p class="service-description">{{ $customer_service['desc'] }}</p>
                    <button class="contact-service-btn" onclick="showCustomerService()">联系客服</button>
                </div>
            </div>
            @endif
        </div>

        <!-- 底部操作 -->
        <div class="bottom-actions">
            <button class="back-btn" onclick="goBack()">返回首页</button>
        </div>

        <!-- 分享浮动按钮 -->
        <div class="share-float" onclick="shareGroup()">
            📤
        </div>
    </div>

    <!-- 客服二维码模态框 -->
    @if($customer_service['qr_code'])
    <div class="modal" id="serviceModal">
        <div class="modal-content">
            <h3 class="modal-title">联系客服</h3>
            <img src="{{ $customer_service['qr_code'] }}" alt="客服二维码" class="modal-qr">
            <p>扫描上方二维码添加客服微信</p>
            <button class="modal-close" onclick="hideServiceModal()">关闭</button>
        </div>
    </div>
    @endif

    <script>
        // 页面数据
        const pageData = {
            groupId: {{ $group->id }},
            orderNo: '{{ $order->order_no }}',
            groupTitle: '{{ $group->title }}',
            qrCodeUrl: '{{ $qr_code_url }}'
        };

        // 保存二维码
        function saveQrCode() {
            // 移动端长按保存提示
            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                alert('请长按二维码图片选择"保存到相册"');
            } else {
                // PC端下载
                const link = document.createElement('a');
                link.href = pageData.qrCodeUrl;
                link.download = `${pageData.groupTitle}-群组二维码.png`;
                link.click();
            }
        }

        // 显示客服
        function showCustomerService() {
            @if($customer_service['qr_code'])
            document.getElementById('serviceModal').style.display = 'flex';
            @else
            alert('请联系客服获取帮助');
            @endif
        }

        // 隐藏客服模态框
        function hideServiceModal() {
            document.getElementById('serviceModal').style.display = 'none';
        }

        // 分享群组
        function shareGroup() {
            if (navigator.share) {
                navigator.share({
                    title: pageData.groupTitle,
                    text: `我刚加入了"${pageData.groupTitle}"，一起来看看吧！`,
                    url: window.location.href
                });
            } else {
                // 复制链接
                const textArea = document.createElement('textarea');
                textArea.value = window.location.href;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('链接已复制到剪贴板');
            }
        }

        // 返回首页
        function goBack() {
            // 可以跳转到主页或群组列表页
            window.history.back();
        }

        // 页面加载完成后的操作
        document.addEventListener('DOMContentLoaded', function() {
            // 自动保存订单信息到本地存储
            const orderInfo = {
                orderNo: pageData.orderNo,
                groupId: pageData.groupId,
                groupTitle: pageData.groupTitle,
                joinTime: new Date().toISOString()
            };
            
            let joinedGroups = JSON.parse(localStorage.getItem('joinedGroups') || '[]');
            joinedGroups.push(orderInfo);
            localStorage.setItem('joinedGroups', JSON.stringify(joinedGroups));

            // 发送成功事件统计
            if (typeof gtag !== 'undefined') {
                gtag('event', 'purchase', {
                    'transaction_id': pageData.orderNo,
                    'value': {{ $order->amount }},
                    'currency': 'CNY',
                    'items': [{
                        'item_id': pageData.groupId,
                        'item_name': pageData.groupTitle,
                        'category': '{{ $group->category }}',
                        'quantity': 1,
                        'price': {{ $order->amount }}
                    }]
                });
            }
        });

        // 防止页面被缓存
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        });

        // 页面离开前提醒保存二维码
        let hasInteracted = false;
        document.addEventListener('click', function() {
            hasInteracted = true;
        });

        window.addEventListener('beforeunload', function(event) {
            if (hasInteracted && pageData.qrCodeUrl) {
                event.preventDefault();
                event.returnValue = '请确保已保存群组二维码，否则可能无法加入群组';
                return event.returnValue;
            }
        });
    </script>
</body>
</html>