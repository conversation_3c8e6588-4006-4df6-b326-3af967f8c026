<template>
  <div class="collapsed-tooltips">
    <el-tooltip
      v-for="tooltip in tooltips"
      :key="tooltip.id"
      :content="tooltip.content"
      :placement="placement"
      effect="dark"
    >
      <div 
        class="tooltip-trigger"
        @click="handleTooltipClick(tooltip)"
      >
        <el-icon>
          <component :is="tooltip.icon" />
        </el-icon>
        <span v-if="!collapsed" class="tooltip-label">
          {{ tooltip.label }}
        </span>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup>
import { computed } from "vue"

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  placement: {
    type: String,
    default: "right"
  },
  tooltips: {
    type: Array,
    default: () => [
      {
        id: 1,
        label: "帮助",
        content: "查看系统帮助文档",
        icon: "QuestionFilled"
      },
      {
        id: 2,
        label: "反馈",
        content: "提交问题反馈",
        icon: "ChatLineSquare"
      },
      {
        id: 3,
        label: "快捷键",
        content: "查看快捷键说明",
        icon: "Keyboard"
      }
    ]
  }
})

const emit = defineEmits(["tooltip-click"])

const handleTooltipClick = (tooltip) => {
  emit("tooltip-click", tooltip)
}
</script>

<style scoped>
.collapsed-tooltips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-trigger {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--el-text-color-regular);
}

.tooltip-trigger:hover {
  background: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.tooltip-label {
  margin-left: 8px;
  font-size: 13px;
}
</style>
