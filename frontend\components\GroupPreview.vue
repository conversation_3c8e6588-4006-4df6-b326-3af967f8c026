<template>
  <div class="bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 预览标题 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="font-semibold">实时预览</h3>
        <div class="flex items-center space-x-2">
          <button 
            @click="viewMode = 'mobile'"
            :class="['p-1 rounded', viewMode === 'mobile' ? 'bg-white/20' : 'hover:bg-white/10']"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 011 1v8a1 1 0 01-1 1H5a1 1 0 01-1-1V7zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 000-2H9z"></path>
            </svg>
          </button>
          <button 
            @click="viewMode = 'desktop'"
            :class="['p-1 rounded', viewMode === 'desktop' ? 'bg-white/20' : 'hover:bg-white/10']"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="p-4">
      <div 
        :class="[
          'mx-auto bg-gray-50 rounded-lg overflow-hidden transition-all duration-300',
          viewMode === 'mobile' ? 'max-w-sm' : 'max-w-full'
        ]"
      >
        <!-- 群组头部 -->
        <div class="relative">
          <div class="h-32 bg-gradient-to-r from-blue-400 to-purple-500"></div>
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
            <div class="p-4 text-white w-full">
              <h1 class="text-lg font-bold mb-1">
                {{ formData.title || '群组名称' }}
              </h1>
              <div class="flex items-center space-x-4 text-xs">
                <span>👁 {{ formData.read_count_display || '1万+' }}</span>
                <span>👍 {{ formData.like_count || 0 }}</span>
                <span>👥 {{ formData.current_members || 0 }}/{{ formData.member_limit || 200 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格和购买区域 -->
        <div class="p-4 bg-white">
          <div class="text-center mb-4">
            <div class="flex items-center justify-center space-x-2">
              <span 
                v-if="formData.show_limited_time && formData.original_price > formData.price"
                class="text-sm text-gray-500 line-through"
              >
                ¥{{ formData.original_price }}
              </span>
              <span class="text-2xl font-bold text-red-600">
                ¥{{ formData.price || 0 }}
              </span>
            </div>
            <div class="text-xs text-gray-600 mt-1">一次付费，永久入群</div>
            
            <!-- 限时优惠倒计时 -->
            <div 
              v-if="formData.show_limited_time && formData.discount_end_time"
              class="mt-2 text-xs text-orange-600 bg-orange-50 rounded px-2 py-1 inline-block"
            >
              🔥 限时优惠，仅剩 {{ getTimeRemaining() }}
            </div>
          </div>

          <button 
            class="w-full bg-red-600 text-white py-2 px-4 rounded-lg font-medium text-sm hover:bg-red-700 transition-colors"
          >
            {{ formData.button_title || '立即加入群聊' }}
          </button>
        </div>

        <!-- 群组信息 -->
        <div class="p-4 bg-white border-t">
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">群组分类</span>
              <span class="font-medium">{{ getCategoryName(formData.category) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">当前人数</span>
              <span class="font-medium">{{ formData.current_members || 0 }}/{{ formData.member_limit || 200 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">想看人数</span>
              <span class="font-medium">{{ formData.want_see_count || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- 群组描述 -->
        <div v-if="formData.description" class="p-4 bg-white border-t">
          <h4 class="font-medium text-gray-900 mb-2">群组介绍</h4>
          <p class="text-sm text-gray-700 leading-relaxed">
            {{ formData.description }}
          </p>
        </div>

        <!-- 成员头像展示 -->
        <div v-if="formData.current_members > 0" class="p-4 bg-white border-t">
          <h4 class="font-medium text-gray-900 mb-3">群成员</h4>
          <div class="grid grid-cols-8 gap-2">
            <div 
              v-for="i in Math.min(16, formData.current_members)" 
              :key="i"
              class="relative"
            >
              <img 
                :src="getAvatarUrl(formData.avatar_library || 'default', i)"
                :alt="`成员${i}`"
                class="w-8 h-8 rounded-full border border-gray-200"
              />
              <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white"></div>
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-2">
            还有 {{ Math.max(0, formData.current_members - 16) }} 位成员在群里...
          </p>
        </div>

        <!-- 用户评价预览 -->
        <div v-if="hasValidReviews" class="p-4 bg-white border-t">
          <h4 class="font-medium text-gray-900 mb-3">用户评价</h4>
          <div class="space-y-3">
            <div 
              v-for="(review, index) in validReviews.slice(0, 2)"
              :key="index"
              class="flex items-start space-x-3"
            >
              <img 
                :src="getAvatarUrl(formData.avatar_library || 'default', index + 1)"
                :alt="review.author"
                class="w-8 h-8 rounded-full"
              />
              <div class="flex-1">
                <div class="flex items-center space-x-2 mb-1">
                  <span class="text-sm font-medium">{{ review.author || `用户${index + 1}` }}</span>
                  <div class="flex text-yellow-400">
                    <svg v-for="star in review.rating" :key="star" class="w-3 h-3 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </div>
                </div>
                <p class="text-xs text-gray-700">{{ review.content }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ预览 -->
        <div v-if="hasValidFAQs" class="p-4 bg-white border-t">
          <h4 class="font-medium text-gray-900 mb-3">常见问题</h4>
          <div class="space-y-2">
            <div 
              v-for="(faq, index) in validFAQs.slice(0, 2)"
              :key="index"
              class="border border-gray-200 rounded-lg p-3"
            >
              <div class="font-medium text-sm text-gray-900 mb-1">
                {{ faq.question }}
              </div>
              <div class="text-xs text-gray-600">
                {{ faq.answer }}
              </div>
            </div>
          </div>
        </div>

        <!-- 付费内容预览 -->
        <div v-if="formData.paid_content_type" class="p-4 bg-gray-100 border-t">
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mb-2">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-900 mb-1">付费后可获得</p>
            <p class="text-xs text-gray-600">
              {{ getContentTypeLabel(formData.paid_content_type) }}
            </p>
          </div>
        </div>

        <!-- 安全保障 -->
        <div class="p-4 bg-white border-t">
          <h4 class="font-medium text-gray-900 mb-3">安全保障</h4>
          <div class="space-y-2">
            <div class="flex items-center text-xs text-gray-600">
              <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              实名认证群主
            </div>
            <div class="flex items-center text-xs text-gray-600">
              <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              7天无理由退款
            </div>
            <div class="flex items-center text-xs text-gray-600">
              <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              24小时客服支持
            </div>
          </div>
        </div>
      </div>

      <!-- 预览提示 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          👆 这是用户看到的群组页面效果
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const viewMode = ref('mobile')

// 计算属性
const validReviews = computed(() => {
  return props.formData.reviews?.filter(review => review.content && review.content.trim()) || []
})

const hasValidReviews = computed(() => {
  return validReviews.value.length > 0
})

const validFAQs = computed(() => {
  return props.formData.faqs?.filter(faq => faq.question && faq.question.trim() && faq.answer && faq.answer.trim()) || []
})

const hasValidFAQs = computed(() => {
  return validFAQs.value.length > 0
})

// 方法
const getCategoryName = (category) => {
  const categories = {
    'business': '商务合作',
    'entrepreneurship': '创业交流',
    'dating': '扩列交友',
    'marriage': '婚恋相亲',
    'education': '学习教育',
    'health': '健身健康',
    'parenting': '育儿教育',
    'investment': '投资理财'
  }
  return categories[category] || '其他'
}

const getAvatarUrl = (library, index) => {
  const libraries = {
    'default': '/avatars/default',
    'business': '/avatars/business',
    'dating': '/avatars/dating',
    'education': '/avatars/education'
  }
  return `${libraries[library] || libraries.default}/${index}.jpg`
}

const getContentTypeLabel = (type) => {
  const labels = {
    'qr_code': '入群二维码',
    'link': '独家下载链接',
    'document': '专属文档内容',
    'video': '独家视频内容'
  }
  return labels[type] || '专属内容'
}

const getTimeRemaining = () => {
  if (!props.formData.discount_end_time) return '24小时'
  
  const now = new Date()
  const endTime = new Date(props.formData.discount_end_time)
  const diff = endTime - now
  
  if (diff <= 0) return '已结束'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `${days}天${hours % 24}小时`
  }
  
  return `${hours}小时${minutes}分钟`
}
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>