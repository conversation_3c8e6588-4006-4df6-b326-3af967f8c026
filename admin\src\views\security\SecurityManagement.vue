<template>
  <div class="security-management">
    <PageLayout title="安全管理" subtitle="系统安全配置和监控">
      <!-- 安全概览 -->
      <div class="overview-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card success">
              <div class="stat-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ securityStats.threat_blocked }}</div>
                <div class="stat-label">威胁拦截</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card warning">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ securityStats.security_alerts }}</div>
                <div class="stat-label">安全警报</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card primary">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ securityStats.active_sessions }}</div>
                <div class="stat-label">活跃会话</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card info">
              <div class="stat-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ securityStats.security_level }}</div>
                <div class="stat-label">安全等级</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 安全配置 -->
      <div class="config-section">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <h3>安全配置</h3>
              <el-button type="primary" @click="saveSecurityConfig" :loading="saving">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
            </div>
          </template>

          <el-tabs v-model="activeTab" class="security-tabs">
            <!-- 访问控制 -->
            <el-tab-pane label="访问控制" name="access">
              <el-form :model="securityConfig.access" label-width="150px">
                <el-form-item label="登录失败限制">
                  <el-input-number
                    v-model="securityConfig.access.max_login_attempts"
                    :min="3"
                    :max="10"
                  />
                  <span class="form-tip">次失败后锁定账户</span>
                </el-form-item>
                
                <el-form-item label="账户锁定时间">
                  <el-input-number
                    v-model="securityConfig.access.lockout_duration"
                    :min="5"
                    :max="1440"
                  />
                  <span class="form-tip">分钟</span>
                </el-form-item>
                
                <el-form-item label="会话超时">
                  <el-input-number
                    v-model="securityConfig.access.session_timeout"
                    :min="15"
                    :max="480"
                  />
                  <span class="form-tip">分钟</span>
                </el-form-item>
                
                <el-form-item label="强制双因子认证">
                  <el-switch v-model="securityConfig.access.require_2fa" />
                  <span class="form-tip">管理员账户强制启用</span>
                </el-form-item>
                
                <el-form-item label="IP白名单">
                  <el-input
                    v-model="securityConfig.access.ip_whitelist"
                    type="textarea"
                    :rows="4"
                    placeholder="每行一个IP地址或CIDR段"
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 密码策略 -->
            <el-tab-pane label="密码策略" name="password">
              <el-form :model="securityConfig.password" label-width="150px">
                <el-form-item label="最小长度">
                  <el-input-number
                    v-model="securityConfig.password.min_length"
                    :min="6"
                    :max="32"
                  />
                  <span class="form-tip">字符</span>
                </el-form-item>
                
                <el-form-item label="复杂度要求">
                  <el-checkbox-group v-model="securityConfig.password.complexity">
                    <el-checkbox label="uppercase">包含大写字母</el-checkbox>
                    <el-checkbox label="lowercase">包含小写字母</el-checkbox>
                    <el-checkbox label="numbers">包含数字</el-checkbox>
                    <el-checkbox label="symbols">包含特殊字符</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                
                <el-form-item label="密码有效期">
                  <el-input-number
                    v-model="securityConfig.password.expiry_days"
                    :min="0"
                    :max="365"
                  />
                  <span class="form-tip">天（0表示永不过期）</span>
                </el-form-item>
                
                <el-form-item label="历史密码检查">
                  <el-input-number
                    v-model="securityConfig.password.history_count"
                    :min="0"
                    :max="10"
                  />
                  <span class="form-tip">不能重复最近几次密码</span>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 审计日志 -->
            <el-tab-pane label="审计日志" name="audit">
              <el-form :model="securityConfig.audit" label-width="150px">
                <el-form-item label="启用审计日志">
                  <el-switch v-model="securityConfig.audit.enabled" />
                </el-form-item>
                
                <el-form-item label="日志级别">
                  <el-select v-model="securityConfig.audit.log_level">
                    <el-option label="基础" value="basic" />
                    <el-option label="详细" value="detailed" />
                    <el-option label="完整" value="full" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="日志保留期">
                  <el-input-number
                    v-model="securityConfig.audit.retention_days"
                    :min="7"
                    :max="365"
                  />
                  <span class="form-tip">天</span>
                </el-form-item>
                
                <el-form-item label="记录事件">
                  <el-checkbox-group v-model="securityConfig.audit.events">
                    <el-checkbox label="login">登录/登出</el-checkbox>
                    <el-checkbox label="admin">管理操作</el-checkbox>
                    <el-checkbox label="data">数据变更</el-checkbox>
                    <el-checkbox label="security">安全事件</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 防护设置 -->
            <el-tab-pane label="防护设置" name="protection">
              <el-form :model="securityConfig.protection" label-width="150px">
                <el-form-item label="启用防火墙">
                  <el-switch v-model="securityConfig.protection.firewall_enabled" />
                </el-form-item>
                
                <el-form-item label="DDoS防护">
                  <el-switch v-model="securityConfig.protection.ddos_protection" />
                </el-form-item>
                
                <el-form-item label="SQL注入防护">
                  <el-switch v-model="securityConfig.protection.sql_injection_protection" />
                </el-form-item>
                
                <el-form-item label="XSS防护">
                  <el-switch v-model="securityConfig.protection.xss_protection" />
                </el-form-item>
                
                <el-form-item label="CSRF防护">
                  <el-switch v-model="securityConfig.protection.csrf_protection" />
                </el-form-item>
                
                <el-form-item label="请求频率限制">
                  <el-input-number
                    v-model="securityConfig.protection.rate_limit"
                    :min="10"
                    :max="1000"
                  />
                  <span class="form-tip">次/分钟</span>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>

      <!-- 安全日志 -->
      <div class="logs-section">
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <h3>安全日志</h3>
              <div class="header-actions">
                <el-button @click="refreshLogs" :loading="loadingLogs">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button @click="exportLogs">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="securityLogs" v-loading="loadingLogs" stripe>
            <el-table-column prop="timestamp" label="时间" width="160" />
            <el-table-column prop="event_type" label="事件类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getEventTypeColor(row.event_type)">
                  {{ getEventTypeText(row.event_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="user" label="用户" width="120" />
            <el-table-column prop="ip_address" label="IP地址" width="140" />
            <el-table-column prop="description" label="描述" show-overflow-tooltip />
            <el-table-column prop="risk_level" label="风险等级" width="100">
              <template #default="{ row }">
                <el-tag :type="getRiskLevelColor(row.risk_level)" size="small">
                  {{ getRiskLevelText(row.risk_level) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="logPagination.current"
              v-model:page-size="logPagination.size"
              :total="logPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleLogSizeChange"
              @current-change="handleLogCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </PageLayout>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Warning, User, Lock, Check, Refresh, Download
} from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'

// 响应式数据
const activeTab = ref('access')
const saving = ref(false)
const loadingLogs = ref(false)

const securityStats = reactive({
  threat_blocked: 1247,
  security_alerts: 3,
  active_sessions: 156,
  security_level: '高'
})

const securityConfig = reactive({
  access: {
    max_login_attempts: 5,
    lockout_duration: 30,
    session_timeout: 120,
    require_2fa: true,
    ip_whitelist: ''
  },
  password: {
    min_length: 8,
    complexity: ['uppercase', 'lowercase', 'numbers'],
    expiry_days: 90,
    history_count: 5
  },
  audit: {
    enabled: true,
    log_level: 'detailed',
    retention_days: 90,
    events: ['login', 'admin', 'security']
  },
  protection: {
    firewall_enabled: true,
    ddos_protection: true,
    sql_injection_protection: true,
    xss_protection: true,
    csrf_protection: true,
    rate_limit: 100
  }
})

const securityLogs = ref([])
const logPagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 方法
const saveSecurityConfig = async () => {
  saving.value = true
  try {
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('安全配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  } finally {
    saving.value = false
  }
}

const refreshLogs = async () => {
  loadingLogs.value = true
  try {
    // 模拟加载日志数据
    await new Promise(resolve => setTimeout(resolve, 500))
    securityLogs.value = [
      {
        id: 1,
        timestamp: '2024-01-01 10:00:00',
        event_type: 'login_failed',
        user: 'admin',
        ip_address: '*************',
        description: '登录失败，密码错误',
        risk_level: 'medium'
      },
      {
        id: 2,
        timestamp: '2024-01-01 09:30:00',
        event_type: 'suspicious_activity',
        user: 'unknown',
        ip_address: '********',
        description: '检测到可疑活动',
        risk_level: 'high'
      }
    ]
    logPagination.total = 2
  } catch (error) {
    ElMessage.error('加载日志失败：' + error.message)
  } finally {
    loadingLogs.value = false
  }
}

const exportLogs = async () => {
  try {
    ElMessage.success('日志导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

const handleLogSizeChange = (size) => {
  logPagination.size = size
  refreshLogs()
}

const handleLogCurrentChange = (current) => {
  logPagination.current = current
  refreshLogs()
}

const getEventTypeColor = (type) => {
  const colors = {
    login_failed: 'warning',
    suspicious_activity: 'danger',
    admin_action: 'primary',
    security_alert: 'danger'
  }
  return colors[type] || 'info'
}

const getEventTypeText = (type) => {
  const texts = {
    login_failed: '登录失败',
    suspicious_activity: '可疑活动',
    admin_action: '管理操作',
    security_alert: '安全警报'
  }
  return texts[type] || '未知'
}

const getRiskLevelColor = (level) => {
  const colors = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return colors[level] || 'info'
}

const getRiskLevelText = (level) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return texts[level] || '未知'
}

// 生命周期
onMounted(() => {
  refreshLogs()
})
</script>

<style lang="scss" scoped>
.security-management {
  .overview-section {
    margin-bottom: 24px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #52c41a, #95de64);
      }

      &.warning .stat-icon {
        background: linear-gradient(135deg, #fa8c16, #ffc53d);
      }

      &.primary .stat-icon {
        background: linear-gradient(135deg, #1677ff, #69c0ff);
      }

      &.info .stat-icon {
        background: linear-gradient(135deg, #722ed1, #b37feb);
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .config-section,
  .logs-section {
    margin-bottom: 24px;

    .config-card,
    .logs-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }
      }
    }
  }

  .security-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 24px 0;
    }

    .form-tip {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  .logs-card {
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .security-management {
    .overview-section {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .security-management {
    .stat-card {
      flex-direction: column;
      text-align: center;
    }

    .config-section,
    .logs-section {
      .card-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }
    }
  }
}
</style>