/**
 * Vue错误处理插件
 * 提供全局的错误处理和组件卸载保护
 */

import { ElMessage } from 'element-plus'

/**
 * 错误处理插件
 */
export default {
  install(app) {
    // 全局错误处理器
    app.config.errorHandler = (error, instance, info) => {
      console.error('Vue Error:', error, info)
      
      // 过滤掉组件卸载相关的错误
      if (
        error?.message?.includes('Cannot read properties of null') ||
        error?.message?.includes('reading \'type\'') ||
        error?.stack?.includes('unmountComponent') ||
        error?.stack?.includes('unmount') ||
        info?.includes('unmount')
      ) {
        // 这些是组件卸载时的正常错误，不需要显示给用户
        return
      }

      // 过滤掉其他已知的无害错误
      if (
        error?.message?.includes('拒绝了我们的连接请求') ||
        error?.message?.includes('Network Error') ||
        error?.message?.includes('ERR_CONNECTION_REFUSED') ||
        error?.message?.includes('data2 is not iterable') ||
        error?.message?.includes('is not iterable')
      ) {
        return
      }

      // 显示用户友好的错误信息
      if (process.env.NODE_ENV === 'development') {
        const errorMessage = error?.message || error?.toString() || '未知错误'
        ElMessage.error(`组件错误: ${errorMessage}`)
      } else {
        ElMessage.error('页面出现异常，请刷新重试')
      }
    }

    // 全局警告处理器
    app.config.warnHandler = (msg, instance, trace) => {
      // 在开发环境下记录警告
      if (process.env.NODE_ENV === 'development') {
        console.warn('Vue Warning:', msg, trace)
      }
    }

    // 提供全局错误处理方法
    app.provide('$handleError', (error, context = '') => {
      console.error(`Error in ${context}:`, error)
      
      // 过滤掉组件卸载相关的错误
      if (
        error?.message?.includes('Cannot read properties of null') ||
        error?.stack?.includes('unmountComponent')
      ) {
        return
      }

      if (process.env.NODE_ENV === 'development') {
        ElMessage.error(`${context}: ${error.message}`)
      } else {
        ElMessage.error('操作失败，请重试')
      }
    })

    // 提供安全的异步操作方法
    app.provide('$safeAsync', async (asyncFn, context = '') => {
      try {
        return await asyncFn()
      } catch (error) {
        console.error(`Async error in ${context}:`, error)
        
        // 过滤掉组件卸载相关的错误
        if (
          error?.message?.includes('Cannot read properties of null') ||
          error?.stack?.includes('unmountComponent')
        ) {
          return null
        }

        if (process.env.NODE_ENV === 'development') {
          ElMessage.error(`${context}: ${error.message}`)
        } else {
          ElMessage.error('操作失败，请重试')
        }
        
        throw error
      }
    })
  }
}

/**
 * 组合式API：安全的错误处理
 */
export function useErrorHandling() {
  const handleError = (error, context = '') => {
    console.error(`Error in ${context}:`, error)
    
    // 过滤掉组件卸载相关的错误
    if (
      error?.message?.includes('Cannot read properties of null') ||
      error?.stack?.includes('unmountComponent')
    ) {
      return
    }

    if (process.env.NODE_ENV === 'development') {
      ElMessage.error(`${context}: ${error.message}`)
    } else {
      ElMessage.error('操作失败，请重试')
    }
  }

  const safeAsync = async (asyncFn, context = '') => {
    try {
      return await asyncFn()
    } catch (error) {
      handleError(error, context)
      throw error
    }
  }

  return {
    handleError,
    safeAsync
  }
}
