/**
 * User Store Unit Tests
 * 测试用户状态管理的所有功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useUserStore } from '@/stores/user'
import * as authApi from '@/api/auth'
import * as authUtils from '@/utils/auth'

// 模拟API调用
vi.mock('@/api/auth')
vi.mock('@/utils/auth')

// 模拟导航配置
vi.mock('@/config/navigation', () => ({
  getUserDefaultRoute: vi.fn().mockReturnValue('/dashboard')
}))

describe('User Store', () => {
  let userStore
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    userStore = useUserStore()
    
    // 清除所有模拟
    vi.clearAllMocks()
    
    // 重置localStorage模拟
    localStorage.clear()
    
    // 清除定时器
    if (window.autoLogoutTimer) {
      clearTimeout(window.autoLogoutTimer)
      window.autoLogoutTimer = null
    }
  })

  afterEach(() => {
    if (window.autoLogoutTimer) {
      clearTimeout(window.autoLogoutTimer)
      window.autoLogoutTimer = null
    }
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      expect(userStore.token).toBe('')
      expect(userStore.userInfo).toBeNull()
      expect(userStore.roles).toEqual([])
      expect(userStore.nickname).toBe('')
      expect(userStore.avatar).toBe('')
      expect(userStore.userRole).toBe('')
    })

    it('should load token from storage on initialization', () => {
      const mockToken = 'stored-token'
      authUtils.getToken.mockReturnValue(mockToken)
      
      // 重新创建store实例
      setActivePinia(createPinia())
      const newUserStore = useUserStore()
      
      expect(newUserStore.token).toBe(mockToken)
    })
  })

  describe('Login Functionality', () => {
    it('should handle successful login', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            token: 'test-token',
            user: {
              id: 1,
              username: 'testuser',
              nickname: '测试用户',
              role: 'admin',
              roles: ['admin'],
              email: '<EMAIL>'
            }
          }
        }
      }
      
      authApi.login.mockResolvedValue(mockResponse)
      
      const credentials = { username: 'testuser', password: 'password123' }
      const result = await userStore.login(credentials)
      
      // 验证API调用
      expect(authApi.login).toHaveBeenCalledWith(credentials)
      
      // 验证token设置
      expect(authUtils.setToken).toHaveBeenCalledWith('test-token')
      expect(userStore.token).toBe('test-token')
      
      // 验证用户信息设置
      expect(userStore.userInfo).toEqual(mockResponse.data.data.user)
      
      // 验证会话信息保存
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'sessionInfo',
        expect.stringContaining('"role":"admin"')
      )
      
      // 验证返回值
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle login failure with 401 error', async () => {
      const error = new Error('Unauthorized')
      error.response = {
        status: 401,
        data: { message: '用户名或密码错误' }
      }
      
      authApi.login.mockRejectedValue(error)
      
      await expect(userStore.login({ username: 'wrong', password: 'wrong' }))
        .rejects.toThrow('用户名或密码错误')
    })

    it('should handle login failure with 403 error', async () => {
      const error = new Error('Forbidden')
      error.response = {
        status: 403,
        data: { message: '账户已禁用' }
      }
      
      authApi.login.mockRejectedValue(error)
      
      await expect(userStore.login({ username: 'disabled', password: 'pass' }))
        .rejects.toThrow('账户已被禁用或权限不足')
    })

    it('should handle login failure with 429 error', async () => {
      const error = new Error('Too Many Requests')
      error.response = {
        status: 429,
        data: { message: '请求过于频繁' }
      }
      
      authApi.login.mockRejectedValue(error)
      
      await expect(userStore.login({ username: 'user', password: 'pass' }))
        .rejects.toThrow('登录尝试过于频繁，请稍后再试')
    })

    it('should handle login failure with 500 error', async () => {
      const error = new Error('Internal Server Error')
      error.response = {
        status: 500,
        data: { message: '服务器错误' }
      }
      
      authApi.login.mockRejectedValue(error)
      
      await expect(userStore.login({ username: 'user', password: 'pass' }))
        .rejects.toThrow('服务器内部错误，请稍后重试')
    })

    it('should handle network errors', async () => {
      const error = new Error('Network Error')
      
      authApi.login.mockRejectedValue(error)
      
      await expect(userStore.login({ username: 'user', password: 'pass' }))
        .rejects.toThrow('Network Error')
    })

    it('should handle API failure response', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'Login failed'
        }
      }
      
      authApi.login.mockResolvedValue(mockResponse)
      
      await expect(userStore.login({ username: 'user', password: 'pass' }))
        .rejects.toThrow('Login failed')
    })
  })

  describe('Get User Info', () => {
    it('should fetch user info successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            user: {
              id: 1,
              username: 'testuser',
              nickname: '测试用户',
              role: 'admin'
            }
          }
        }
      }
      
      authApi.getInfo.mockResolvedValue(mockResponse)
      
      const result = await userStore.getUserInfo()
      
      expect(authApi.getInfo).toHaveBeenCalled()
      expect(userStore.userInfo).toEqual(mockResponse.data.data.user)
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle getUserInfo failure', async () => {
      const error = new Error('Failed to get user info')
      authApi.getInfo.mockRejectedValue(error)
      
      await expect(userStore.getUserInfo()).rejects.toThrow('Failed to get user info')
    })

    it('should handle API failure response for getUserInfo', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'User not found'
        }
      }
      
      authApi.getInfo.mockResolvedValue(mockResponse)
      
      await expect(userStore.getUserInfo()).rejects.toThrow('User not found')
    })
  })

  describe('Logout Functionality', () => {
    beforeEach(() => {
      // 设置初始登录状态
      userStore.token = 'test-token'
      userStore.userInfo = { id: 1, username: 'test', role: 'admin' }
      localStorage.setItem('sessionInfo', JSON.stringify({ loginTime: new Date() }))
      window.autoLogoutTimer = setTimeout(() => {}, 1000)
    })

    it('should handle successful logout', async () => {
      authApi.logout.mockResolvedValue({ success: true })
      
      await userStore.logout()
      
      expect(authApi.logout).toHaveBeenCalled()
      expect(authUtils.removeToken).toHaveBeenCalled()
      expect(userStore.token).toBe('')
      expect(userStore.userInfo).toBeNull()
      expect(localStorage.removeItem).toHaveBeenCalledWith('sessionInfo')
      expect(window.autoLogoutTimer).toBeNull()
    })

    it('should handle logout even when API fails', async () => {
      authApi.logout.mockRejectedValue(new Error('Logout API failed'))
      
      await userStore.logout()
      
      // 即使API失败，本地状态也应该被清理
      expect(authUtils.removeToken).toHaveBeenCalled()
      expect(userStore.token).toBe('')
      expect(userStore.userInfo).toBeNull()
    })
  })

  describe('Preview Mode', () => {
    it('should enter preview mode correctly', () => {
      userStore.enterPreviewMode()
      
      expect(authUtils.setToken).toHaveBeenCalledWith(
        expect.stringMatching(/preview-mode-token-\d+/)
      )
      
      expect(userStore.userInfo).toMatchObject({
        id: 1,
        username: 'admin',
        nickname: '超级管理员 (预览)',
        role: 'admin',
        roles: ['admin'],
        permissions: ['*']
      })
      
      expect(userStore.token).toMatch(/preview-mode-token-\d+/)
    })
  })

  describe('Role Computed Properties', () => {
    it('should compute role properties correctly', () => {
      userStore.userInfo = {
        id: 1,
        username: 'admin',
        role: 'admin',
        roles: ['admin']
      }
      
      expect(userStore.isAdmin).toBe(true)
      expect(userStore.isSubstation).toBe(false)
      expect(userStore.isAgent).toBe(false)
      expect(userStore.isDistributor).toBe(false)
      expect(userStore.isGroupOwner).toBe(false)
      expect(userStore.isUser).toBe(false)
    })

    it('should handle all role types', () => {
      const roles = [
        { role: 'admin', property: 'isAdmin' },
        { role: 'substation', property: 'isSubstation' },
        { role: 'agent', property: 'isAgent' },
        { role: 'distributor', property: 'isDistributor' },
        { role: 'group_owner', property: 'isGroupOwner' },
        { role: 'user', property: 'isUser' }
      ]
      
      roles.forEach(({ role, property }) => {
        userStore.userInfo = { id: 1, username: 'test', role }
        expect(userStore[property]).toBe(true)
        
        // 其他角色应该为false
        roles.filter(r => r.property !== property).forEach(otherRole => {
          expect(userStore[otherRole.property]).toBe(false)
        })
      })
    })
  })

  describe('Permission System', () => {
    it('should grant all permissions to admin', () => {
      userStore.userInfo = { role: 'admin' }
      
      expect(userStore.hasPermission('any_permission')).toBe(true)
      expect(userStore.hasPermission('user_management')).toBe(true)
    })

    it('should check permissions for substation role', () => {
      userStore.userInfo = { role: 'substation' }
      
      expect(userStore.hasPermission('user_management')).toBe(true)
      expect(userStore.hasPermission('agent_management')).toBe(true)
      expect(userStore.hasPermission('order_management')).toBe(true)
      expect(userStore.hasPermission('group_management')).toBe(true)
      expect(userStore.hasPermission('finance_view')).toBe(true)
      
      // 不应该有的权限
      expect(userStore.hasPermission('system_config')).toBe(false)
    })

    it('should check permissions for agent role', () => {
      userStore.userInfo = { role: 'agent' }
      
      expect(userStore.hasPermission('team_management')).toBe(true)
      expect(userStore.hasPermission('commission_view')).toBe(true)
      expect(userStore.hasPermission('performance_view')).toBe(true)
      expect(userStore.hasPermission('application_management')).toBe(true)
      
      expect(userStore.hasPermission('user_management')).toBe(false)
    })

    it('should check permissions for distributor role', () => {
      userStore.userInfo = { role: 'distributor' }
      
      expect(userStore.hasPermission('customer_management')).toBe(true)
      expect(userStore.hasPermission('group_management')).toBe(true)
      expect(userStore.hasPermission('promotion_management')).toBe(true)
      expect(userStore.hasPermission('commission_view')).toBe(true)
      
      expect(userStore.hasPermission('user_management')).toBe(false)
    })

    it('should check permissions for group_owner role', () => {
      userStore.userInfo = { role: 'group_owner' }
      
      expect(userStore.hasPermission('group_management')).toBe(true)
      expect(userStore.hasPermission('content_management')).toBe(true)
      expect(userStore.hasPermission('template_management')).toBe(true)
      
      expect(userStore.hasPermission('user_management')).toBe(false)
    })

    it('should check permissions for user role', () => {
      userStore.userInfo = { role: 'user' }
      
      expect(userStore.hasPermission('profile_management')).toBe(true)
      expect(userStore.hasPermission('order_view')).toBe(true)
      
      expect(userStore.hasPermission('user_management')).toBe(false)
    })

    it('should return false for no user info', () => {
      userStore.userInfo = null
      
      expect(userStore.hasPermission('any_permission')).toBe(false)
    })
  })

  describe('Route Access Control', () => {
    it('should allow admin to access all routes', () => {
      userStore.userInfo = { role: 'admin' }
      
      expect(userStore.hasRouteAccess('/any-route')).toBe(true)
    })

    it('should check route permissions for non-admin users', () => {
      userStore.userInfo = { role: 'user' }
      
      // 这里需要根据实际的导航配置来测试
      // 由于我们模拟了导航配置，这里只测试调用是否正确
      expect(typeof userStore.hasRouteAccess('/some-route')).toBe('boolean')
    })

    it('should return false when no user info', () => {
      userStore.userInfo = null
      
      expect(userStore.hasRouteAccess('/any-route')).toBe(false)
    })
  })

  describe('Session Management', () => {
    it('should validate session correctly', () => {
      const now = new Date()
      const loginTime = new Date(now.getTime() - 2 * 60 * 60 * 1000) // 2小时前
      
      userStore.token = 'valid-token'
      localStorage.setItem('sessionInfo', JSON.stringify({
        loginTime: loginTime.toISOString()
      }))
      
      expect(userStore.isSessionValid).toBe(true)
    })

    it('should invalidate expired session', () => {
      const now = new Date()
      const loginTime = new Date(now.getTime() - 25 * 60 * 60 * 1000) // 25小时前
      
      userStore.token = 'expired-token'
      localStorage.setItem('sessionInfo', JSON.stringify({
        loginTime: loginTime.toISOString()
      }))
      
      expect(userStore.isSessionValid).toBe(false)
    })

    it('should return false when no token or session info', () => {
      userStore.token = ''
      localStorage.removeItem('sessionInfo')
      
      expect(userStore.isSessionValid).toBe(false)
    })

    it('should handle malformed session info', () => {
      userStore.token = 'valid-token'
      localStorage.setItem('sessionInfo', 'invalid-json')
      
      expect(userStore.isSessionValid).toBe(false)
    })

    it('should setup auto logout timer', () => {
      const spy = vi.spyOn(global, 'setTimeout')
      
      userStore.setupAutoLogout()
      
      expect(spy).toHaveBeenCalledWith(
        expect.any(Function),
        24 * 60 * 60 * 1000 // 24小时
      )
      
      spy.mockRestore()
    })

    it('should clear previous timer when setting up new one', () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
      
      // 设置初始计时器
      window.autoLogoutTimer = setTimeout(() => {}, 1000)
      const originalTimer = window.autoLogoutTimer
      
      userStore.setupAutoLogout()
      
      expect(clearTimeoutSpy).toHaveBeenCalledWith(originalTimer)
      
      clearTimeoutSpy.mockRestore()
    })
  })

  describe('User Default Route', () => {
    it('should get default route for user role', () => {
      userStore.userInfo = { role: 'admin' }
      
      const defaultRoute = userStore.getUserDefaultRoute()
      
      expect(defaultRoute).toBe('/dashboard')
    })

    it('should return fallback route when navigation config fails', () => {
      userStore.userInfo = { role: 'unknown' }
      
      // 模拟导航配置失败
      vi.doMock('@/config/navigation', () => {
        throw new Error('Navigation config error')
      })
      
      const defaultRoute = userStore.getUserDefaultRoute()
      
      expect(defaultRoute).toBe('/dashboard')
    })
  })

  describe('Token Management', () => {
    it('should reset token correctly', () => {
      userStore.token = 'some-token'
      
      userStore.resetToken()
      
      expect(authUtils.removeToken).toHaveBeenCalled()
      expect(userStore.token).toBe('')
    })

    it('should set token correctly', () => {
      const newToken = 'new-token'
      
      userStore.setToken(newToken)
      
      expect(authUtils.setToken).toHaveBeenCalledWith(newToken)
      expect(userStore.token).toBe(newToken)
    })

    it('should set user info correctly', () => {
      const userInfo = {
        id: 1,
        username: 'test',
        role: 'admin'
      }
      
      userStore.setUserInfo(userInfo)
      
      expect(userStore.userInfo).toEqual(userInfo)
    })
  })

  describe('Computed Properties', () => {
    beforeEach(() => {
      userStore.userInfo = {
        id: 1,
        username: 'testuser',
        nickname: '测试用户',
        avatar: '/test-avatar.png',
        role: 'admin',
        roles: ['admin', 'user']
      }
    })

    it('should compute roles correctly', () => {
      expect(userStore.roles).toEqual(['admin', 'user'])
    })

    it('should compute nickname correctly', () => {
      expect(userStore.nickname).toBe('测试用户')
    })

    it('should compute avatar correctly', () => {
      expect(userStore.avatar).toBe('/test-avatar.png')
    })

    it('should compute userRole correctly', () => {
      expect(userStore.userRole).toBe('admin')
    })

    it('should handle empty user info', () => {
      userStore.userInfo = null
      
      expect(userStore.roles).toEqual([])
      expect(userStore.nickname).toBe('')
      expect(userStore.avatar).toBe('')
      expect(userStore.userRole).toBe('')
    })
  })
})