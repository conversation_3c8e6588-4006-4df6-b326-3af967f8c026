<template>
  <div class="simple-404">
    <div class="container">
      <div class="content">
        <h1 class="title">404</h1>
        <h2 class="subtitle">页面未找到</h2>
        <p class="description">
          抱歉，您访问的页面不存在或已被删除。
        </p>
        <div class="actions">
          <el-button type="primary" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { HomeFilled, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/admin/dashboard')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}
</script>

<style scoped>
.simple-404 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.title {
  font-size: 6rem;
  font-weight: bold;
  color: #2d3748;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 2rem;
  color: #4a5568;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.description {
  font-size: 1.1rem;
  color: #718096;
  line-height: 1.6;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.actions .el-button {
  padding: 12px 24px;
  font-size: 1rem;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 40px 20px;
  }
  
  .title {
    font-size: 4rem;
  }
  
  .subtitle {
    font-size: 1.5rem;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .actions .el-button {
    width: 200px;
  }
}
</style>
