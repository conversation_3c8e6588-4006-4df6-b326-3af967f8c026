import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                    *//* empty css                          *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               */import{aZ as a,a_ as l,T as t,af as s,U as o,be as i,ao as d,aY as u,bj as n,bg as r,bp as c,bq as _,br as p,bs as m,aM as f,bt as b,bu as v,b9 as V,b8 as g,at as y,bv as w,bh as h,bi as x,a$ as j,bw as U,bx as k,aR as z,a4 as C,Q as P}from"./element-plus-h2SQQM64.js";import{P as S}from"./PageLayout-C6qH3ReN.js";import{r as q,L as I,e as D,k as L,l as R,E as T,z as M,t as Q,u as A,D as E,A as F,y as X}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const Y={class:"security-management"},Z={class:"overview-section"},$={class:"stat-card success"},B={class:"stat-icon"},G={class:"stat-content"},H={class:"stat-value"},J={class:"stat-card warning"},K={class:"stat-icon"},N={class:"stat-content"},O={class:"stat-value"},W={class:"stat-card primary"},ee={class:"stat-icon"},ae={class:"stat-content"},le={class:"stat-value"},te={class:"stat-card info"},se={class:"stat-icon"},oe={class:"stat-content"},ie={class:"stat-value"},de={class:"config-section"},ue={class:"card-header"},ne={class:"logs-section"},re={class:"card-header"},ce={class:"header-actions"},_e={class:"pagination-wrapper"},pe=e({__name:"SecurityManagement",setup(e){const pe=q("access"),me=q(!1),fe=q(!1),be=I({threat_blocked:1247,security_alerts:3,active_sessions:156,security_level:"高"}),ve=I({access:{max_login_attempts:5,lockout_duration:30,session_timeout:120,require_2fa:!0,ip_whitelist:""},password:{min_length:8,complexity:["uppercase","lowercase","numbers"],expiry_days:90,history_count:5},audit:{enabled:!0,log_level:"detailed",retention_days:90,events:["login","admin","security"]},protection:{firewall_enabled:!0,ddos_protection:!0,sql_injection_protection:!0,xss_protection:!0,csrf_protection:!0,rate_limit:100}}),Ve=q([]),ge=I({current:1,size:20,total:0}),ye=async()=>{me.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),P.success("安全配置保存成功")}catch(e){P.error("保存失败："+e.message)}finally{me.value=!1}},we=async()=>{fe.value=!0;try{await new Promise(e=>setTimeout(e,500)),Ve.value=[{id:1,timestamp:"2024-01-01 10:00:00",event_type:"login_failed",user:"admin",ip_address:"*************",description:"登录失败，密码错误",risk_level:"medium"},{id:2,timestamp:"2024-01-01 09:30:00",event_type:"suspicious_activity",user:"unknown",ip_address:"********",description:"检测到可疑活动",risk_level:"high"}],ge.total=2}catch(e){P.error("加载日志失败："+e.message)}finally{fe.value=!1}},he=async()=>{try{P.success("日志导出成功")}catch(e){P.error("导出失败："+e.message)}},xe=e=>{ge.size=e,we()},je=e=>{ge.current=e,we()},Ue=e=>({login_failed:"登录失败",suspicious_activity:"可疑活动",admin_action:"管理操作",security_alert:"安全警报"}[e]||"未知"),ke=e=>({low:"低",medium:"中",high:"高"}[e]||"未知");return D(()=>{we()}),(e,P)=>{const q=t,I=l,D=a,ze=y,Ce=p,Pe=_,Se=m,qe=f,Ie=c,De=r,Le=v,Re=b,Te=g,Me=V,Qe=n,Ae=u,Ee=x,Fe=j,Xe=h,Ye=k,Ze=U;return R(),L("div",Y,[T(S,{title:"安全管理",subtitle:"系统安全配置和监控"},{default:M(()=>[Q("div",Z,[T(D,{gutter:20},{default:M(()=>[T(I,{span:6},{default:M(()=>[Q("div",$,[Q("div",B,[T(q,null,{default:M(()=>[T(A(s))]),_:1})]),Q("div",G,[Q("div",H,o(be.threat_blocked),1),P[22]||(P[22]=Q("div",{class:"stat-label"},"威胁拦截",-1))])])]),_:1}),T(I,{span:6},{default:M(()=>[Q("div",J,[Q("div",K,[T(q,null,{default:M(()=>[T(A(i))]),_:1})]),Q("div",N,[Q("div",O,o(be.security_alerts),1),P[23]||(P[23]=Q("div",{class:"stat-label"},"安全警报",-1))])])]),_:1}),T(I,{span:6},{default:M(()=>[Q("div",W,[Q("div",ee,[T(q,null,{default:M(()=>[T(A(d))]),_:1})]),Q("div",ae,[Q("div",le,o(be.active_sessions),1),P[24]||(P[24]=Q("div",{class:"stat-label"},"活跃会话",-1))])])]),_:1}),T(I,{span:6},{default:M(()=>[Q("div",te,[Q("div",se,[T(q,null,{default:M(()=>[T(A(s))]),_:1})]),Q("div",oe,[Q("div",ie,o(be.security_level),1),P[25]||(P[25]=Q("div",{class:"stat-label"},"安全等级",-1))])])]),_:1})]),_:1})]),Q("div",de,[T(Ae,{class:"config-card"},{header:M(()=>[Q("div",ue,[P[27]||(P[27]=Q("h3",null,"安全配置",-1)),T(ze,{type:"primary",onClick:ye,loading:me.value},{default:M(()=>[T(q,null,{default:M(()=>[T(A(w))]),_:1}),P[26]||(P[26]=E(" 保存配置 ",-1))]),_:1,__:[26]},8,["loading"])])]),default:M(()=>[T(Qe,{modelValue:pe.value,"onUpdate:modelValue":P[19]||(P[19]=e=>pe.value=e),class:"security-tabs"},{default:M(()=>[T(De,{label:"访问控制",name:"access"},{default:M(()=>[T(Ie,{model:ve.access,"label-width":"150px"},{default:M(()=>[T(Pe,{label:"登录失败限制"},{default:M(()=>[T(Ce,{modelValue:ve.access.max_login_attempts,"onUpdate:modelValue":P[0]||(P[0]=e=>ve.access.max_login_attempts=e),min:3,max:10},null,8,["modelValue"]),P[28]||(P[28]=Q("span",{class:"form-tip"},"次失败后锁定账户",-1))]),_:1,__:[28]}),T(Pe,{label:"账户锁定时间"},{default:M(()=>[T(Ce,{modelValue:ve.access.lockout_duration,"onUpdate:modelValue":P[1]||(P[1]=e=>ve.access.lockout_duration=e),min:5,max:1440},null,8,["modelValue"]),P[29]||(P[29]=Q("span",{class:"form-tip"},"分钟",-1))]),_:1,__:[29]}),T(Pe,{label:"会话超时"},{default:M(()=>[T(Ce,{modelValue:ve.access.session_timeout,"onUpdate:modelValue":P[2]||(P[2]=e=>ve.access.session_timeout=e),min:15,max:480},null,8,["modelValue"]),P[30]||(P[30]=Q("span",{class:"form-tip"},"分钟",-1))]),_:1,__:[30]}),T(Pe,{label:"强制双因子认证"},{default:M(()=>[T(Se,{modelValue:ve.access.require_2fa,"onUpdate:modelValue":P[3]||(P[3]=e=>ve.access.require_2fa=e)},null,8,["modelValue"]),P[31]||(P[31]=Q("span",{class:"form-tip"},"管理员账户强制启用",-1))]),_:1,__:[31]}),T(Pe,{label:"IP白名单"},{default:M(()=>[T(qe,{modelValue:ve.access.ip_whitelist,"onUpdate:modelValue":P[4]||(P[4]=e=>ve.access.ip_whitelist=e),type:"textarea",rows:4,placeholder:"每行一个IP地址或CIDR段"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),T(De,{label:"密码策略",name:"password"},{default:M(()=>[T(Ie,{model:ve.password,"label-width":"150px"},{default:M(()=>[T(Pe,{label:"最小长度"},{default:M(()=>[T(Ce,{modelValue:ve.password.min_length,"onUpdate:modelValue":P[5]||(P[5]=e=>ve.password.min_length=e),min:6,max:32},null,8,["modelValue"]),P[32]||(P[32]=Q("span",{class:"form-tip"},"字符",-1))]),_:1,__:[32]}),T(Pe,{label:"复杂度要求"},{default:M(()=>[T(Re,{modelValue:ve.password.complexity,"onUpdate:modelValue":P[6]||(P[6]=e=>ve.password.complexity=e)},{default:M(()=>[T(Le,{label:"uppercase"},{default:M(()=>P[33]||(P[33]=[E("包含大写字母",-1)])),_:1,__:[33]}),T(Le,{label:"lowercase"},{default:M(()=>P[34]||(P[34]=[E("包含小写字母",-1)])),_:1,__:[34]}),T(Le,{label:"numbers"},{default:M(()=>P[35]||(P[35]=[E("包含数字",-1)])),_:1,__:[35]}),T(Le,{label:"symbols"},{default:M(()=>P[36]||(P[36]=[E("包含特殊字符",-1)])),_:1,__:[36]})]),_:1},8,["modelValue"])]),_:1}),T(Pe,{label:"密码有效期"},{default:M(()=>[T(Ce,{modelValue:ve.password.expiry_days,"onUpdate:modelValue":P[7]||(P[7]=e=>ve.password.expiry_days=e),min:0,max:365},null,8,["modelValue"]),P[37]||(P[37]=Q("span",{class:"form-tip"},"天（0表示永不过期）",-1))]),_:1,__:[37]}),T(Pe,{label:"历史密码检查"},{default:M(()=>[T(Ce,{modelValue:ve.password.history_count,"onUpdate:modelValue":P[8]||(P[8]=e=>ve.password.history_count=e),min:0,max:10},null,8,["modelValue"]),P[38]||(P[38]=Q("span",{class:"form-tip"},"不能重复最近几次密码",-1))]),_:1,__:[38]})]),_:1},8,["model"])]),_:1}),T(De,{label:"审计日志",name:"audit"},{default:M(()=>[T(Ie,{model:ve.audit,"label-width":"150px"},{default:M(()=>[T(Pe,{label:"启用审计日志"},{default:M(()=>[T(Se,{modelValue:ve.audit.enabled,"onUpdate:modelValue":P[9]||(P[9]=e=>ve.audit.enabled=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"日志级别"},{default:M(()=>[T(Me,{modelValue:ve.audit.log_level,"onUpdate:modelValue":P[10]||(P[10]=e=>ve.audit.log_level=e)},{default:M(()=>[T(Te,{label:"基础",value:"basic"}),T(Te,{label:"详细",value:"detailed"}),T(Te,{label:"完整",value:"full"})]),_:1},8,["modelValue"])]),_:1}),T(Pe,{label:"日志保留期"},{default:M(()=>[T(Ce,{modelValue:ve.audit.retention_days,"onUpdate:modelValue":P[11]||(P[11]=e=>ve.audit.retention_days=e),min:7,max:365},null,8,["modelValue"]),P[39]||(P[39]=Q("span",{class:"form-tip"},"天",-1))]),_:1,__:[39]}),T(Pe,{label:"记录事件"},{default:M(()=>[T(Re,{modelValue:ve.audit.events,"onUpdate:modelValue":P[12]||(P[12]=e=>ve.audit.events=e)},{default:M(()=>[T(Le,{label:"login"},{default:M(()=>P[40]||(P[40]=[E("登录/登出",-1)])),_:1,__:[40]}),T(Le,{label:"admin"},{default:M(()=>P[41]||(P[41]=[E("管理操作",-1)])),_:1,__:[41]}),T(Le,{label:"data"},{default:M(()=>P[42]||(P[42]=[E("数据变更",-1)])),_:1,__:[42]}),T(Le,{label:"security"},{default:M(()=>P[43]||(P[43]=[E("安全事件",-1)])),_:1,__:[43]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),T(De,{label:"防护设置",name:"protection"},{default:M(()=>[T(Ie,{model:ve.protection,"label-width":"150px"},{default:M(()=>[T(Pe,{label:"启用防火墙"},{default:M(()=>[T(Se,{modelValue:ve.protection.firewall_enabled,"onUpdate:modelValue":P[13]||(P[13]=e=>ve.protection.firewall_enabled=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"DDoS防护"},{default:M(()=>[T(Se,{modelValue:ve.protection.ddos_protection,"onUpdate:modelValue":P[14]||(P[14]=e=>ve.protection.ddos_protection=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"SQL注入防护"},{default:M(()=>[T(Se,{modelValue:ve.protection.sql_injection_protection,"onUpdate:modelValue":P[15]||(P[15]=e=>ve.protection.sql_injection_protection=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"XSS防护"},{default:M(()=>[T(Se,{modelValue:ve.protection.xss_protection,"onUpdate:modelValue":P[16]||(P[16]=e=>ve.protection.xss_protection=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"CSRF防护"},{default:M(()=>[T(Se,{modelValue:ve.protection.csrf_protection,"onUpdate:modelValue":P[17]||(P[17]=e=>ve.protection.csrf_protection=e)},null,8,["modelValue"])]),_:1}),T(Pe,{label:"请求频率限制"},{default:M(()=>[T(Ce,{modelValue:ve.protection.rate_limit,"onUpdate:modelValue":P[18]||(P[18]=e=>ve.protection.rate_limit=e),min:10,max:1e3},null,8,["modelValue"]),P[44]||(P[44]=Q("span",{class:"form-tip"},"次/分钟",-1))]),_:1,__:[44]})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),Q("div",ne,[T(Ae,{class:"logs-card"},{header:M(()=>[Q("div",re,[P[47]||(P[47]=Q("h3",null,"安全日志",-1)),Q("div",ce,[T(ze,{onClick:we,loading:fe.value},{default:M(()=>[T(q,null,{default:M(()=>[T(A(z))]),_:1}),P[45]||(P[45]=E(" 刷新 ",-1))]),_:1,__:[45]},8,["loading"]),T(ze,{onClick:he},{default:M(()=>[T(q,null,{default:M(()=>[T(A(C))]),_:1}),P[46]||(P[46]=E(" 导出 ",-1))]),_:1,__:[46]})])])]),default:M(()=>[F((R(),X(Xe,{data:Ve.value,stripe:""},{default:M(()=>[T(Ee,{prop:"timestamp",label:"时间",width:"160"}),T(Ee,{prop:"event_type",label:"事件类型",width:"120"},{default:M(({row:e})=>{return[T(Fe,{type:(a=e.event_type,{login_failed:"warning",suspicious_activity:"danger",admin_action:"primary",security_alert:"danger"}[a]||"info")},{default:M(()=>[E(o(Ue(e.event_type)),1)]),_:2},1032,["type"])];var a}),_:1}),T(Ee,{prop:"user",label:"用户",width:"120"}),T(Ee,{prop:"ip_address",label:"IP地址",width:"140"}),T(Ee,{prop:"description",label:"描述","show-overflow-tooltip":""}),T(Ee,{prop:"risk_level",label:"风险等级",width:"100"},{default:M(({row:e})=>{return[T(Fe,{type:(a=e.risk_level,{low:"success",medium:"warning",high:"danger"}[a]||"info"),size:"small"},{default:M(()=>[E(o(ke(e.risk_level)),1)]),_:2},1032,["type"])];var a}),_:1})]),_:1},8,["data"])),[[Ze,fe.value]]),Q("div",_e,[T(Ye,{"current-page":ge.current,"onUpdate:currentPage":P[20]||(P[20]=e=>ge.current=e),"page-size":ge.size,"onUpdate:pageSize":P[21]||(P[21]=e=>ge.size=e),total:ge.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:je},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1})])}}},[["__scopeId","data-v-5b6240db"]]);export{pe as default};
