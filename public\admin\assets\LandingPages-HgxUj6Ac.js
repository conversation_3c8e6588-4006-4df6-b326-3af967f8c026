import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                       *//* empty css                        *//* empty css                *//* empty css                       *//* empty css               */import{L as l,c as a,d as t,k as s,l as o,t as n,E as d,z as i,D as u,u as r,B as c,F as p,Y as m,y as v,r as _,A as g,C as f,G as h,n as y,e as b,J as V,a3 as k}from"./vue-vendor-Dy164gUc.js";import{T as w,aR as C,at as x,aM as U,b$ as z,br as I,b8 as S,b9 as j,U as D,bs as $,cn as Y,bb as T,ac as A,Y as O,a0 as E,am as L,a5 as q,ak as M,bS as B,ah as W,co as R,o as F,bQ as P,aw as N,bm as G,bn as H,ar as J,cp as Q,cq as K,bp as Z,aU as X,bq as ee,ay as le,Q as ae,a$ as te,ao as se,bG as oe,bw as ne,aZ as de,a_ as ie,bd as ue,a6 as re,cr as ce,aT as pe,by as me,a4 as ve,aY as _e,b3 as ge,p as fe,bc as he,bh as ye,bi as be,ax as Ve,cl as ke,aL as we,ab as Ce,az as xe,aB as Ue,aC as ze,bx as Ie,R as Se}from"./element-plus-h2SQQM64.js";/* empty css                     *//* empty css                  *//* empty css                        *//* empty css                        *//* empty css                 *//* empty css                          */import{p as je,a as De}from"./promotion-DjIFk3EX.js";/* empty css                    */import{f as $e}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";const Ye={class:"component-properties"},Te={class:"properties-header"},Ae={key:0,class:"properties-content"},Oe={class:"property-group"},Ee={class:"property-item"},Le={class:"property-item"},qe={class:"property-item"},Me={class:"property-group"},Be={class:"property-item"},We={class:"property-item"},Re={class:"property-item"},Fe={class:"property-item"},Pe={class:"property-item"},Ne={class:"property-item"},Ge={class:"property-item"},He={class:"property-group"},Je={class:"property-item"},Qe={class:"margin-padding-control"},Ke={class:"property-item"},Ze={class:"margin-padding-control"},Xe={class:"property-item"},el={class:"border-control"},ll={class:"property-item"},al={key:0,class:"property-group"},tl={class:"group-title"},sl={class:"property-group"},ol={class:"property-item"},nl={class:"property-item"},dl={class:"property-item"},il={class:"property-group"},ul={class:"property-item"},rl={key:0,class:"property-item"},cl={class:"property-item"},pl={key:1,class:"no-selection"},ml=e({__name:"ComponentProperties",props:{selectedComponent:{type:Object,default:null}},emits:["update-property"],setup(e,{emit:_}){const g=e,f=_,h=l({id:"",name:"",className:"",width:"",height:"",backgroundColor:"",color:"",fontSize:14,fontWeight:"normal",textAlign:"left",borderWidth:0,borderStyle:"solid",borderColor:"#dcdfe6",borderRadius:0,enterAnimation:"none",animationDelay:0,animationDuration:1,clickAction:"none",linkUrl:"",hoverEffect:!1}),y=l({top:0,right:0,bottom:0,left:0}),b=l({top:0,right:0,bottom:0,left:0}),V=a(()=>{if(!g.selectedComponent)return[];return{text:[{key:"content",label:"文本内容",type:"textarea",placeholder:"请输入文本内容"},{key:"lineHeight",label:"行高",type:"number",min:1,max:3,step:.1}],image:[{key:"src",label:"图片地址",type:"text",placeholder:"https://example.com/image.jpg"},{key:"alt",label:"替代文本",type:"text",placeholder:"图片描述"},{key:"objectFit",label:"适应方式",type:"select",options:[{label:"填充",value:"fill"},{label:"包含",value:"contain"},{label:"覆盖",value:"cover"},{label:"缩放",value:"scale-down"}]}],button:[{key:"text",label:"按钮文字",type:"text",placeholder:"点击按钮"},{key:"buttonType",label:"按钮类型",type:"select",options:[{label:"主要按钮",value:"primary"},{label:"成功按钮",value:"success"},{label:"警告按钮",value:"warning"},{label:"危险按钮",value:"danger"},{label:"信息按钮",value:"info"}]},{key:"size",label:"按钮大小",type:"select",options:[{label:"大",value:"large"},{label:"默认",value:"default"},{label:"小",value:"small"}]},{key:"disabled",label:"禁用状态",type:"switch"}],form:[{key:"action",label:"提交地址",type:"text",placeholder:"/api/submit"},{key:"method",label:"提交方式",type:"select",options:[{label:"POST",value:"post"},{label:"GET",value:"get"}]},{key:"showLabels",label:"显示标签",type:"switch"}],video:[{key:"src",label:"视频地址",type:"text",placeholder:"https://example.com/video.mp4"},{key:"poster",label:"封面图片",type:"text",placeholder:"视频封面地址"},{key:"autoplay",label:"自动播放",type:"switch"},{key:"controls",label:"显示控制条",type:"switch"},{key:"loop",label:"循环播放",type:"switch"}]}[g.selectedComponent.type]||[]});t(()=>g.selectedComponent,e=>{e&&k(e)},{immediate:!0});const k=e=>{if(e.properties){if(Object.keys(h).forEach(l=>{void 0!==e.properties[l]&&(h[l]=e.properties[l])}),e.properties.margin){const l=e.properties.margin.split(" ");y.top=parseInt(l[0])||0,y.right=parseInt(l[1])||0,y.bottom=parseInt(l[2])||0,y.left=parseInt(l[3])||0}if(e.properties.padding){const l=e.properties.padding.split(" ");b.top=parseInt(l[0])||0,b.right=parseInt(l[1])||0,b.bottom=parseInt(l[2])||0,b.left=parseInt(l[3])||0}}},T=(e,l)=>{h[e]=l,f("update-property",e,l)},A=()=>{const e=`${y.top}px ${y.right}px ${y.bottom}px ${y.left}px`;T("margin",e)},O=()=>{const e=`${b.top}px ${b.right}px ${b.bottom}px ${b.left}px`;T("padding",e)},E=()=>{const e=`${h.borderWidth}px ${h.borderStyle} ${h.borderColor}`;T("border",e)},L=()=>{Object.keys(h).forEach(e=>{"string"==typeof h[e]?h[e]="":"number"==typeof h[e]?h[e]=0:"boolean"==typeof h[e]&&(h[e]=!1)}),Object.keys(y).forEach(e=>{y[e]=0}),Object.keys(b).forEach(e=>{b[e]=0}),f("update-property","reset",!0)};return(l,a)=>{const t=w,_=x,g=U,f=z,k=I,q=S,M=j,B=$;return o(),s("div",Ye,[n("div",Te,[a[46]||(a[46]=n("h4",null,"组件属性",-1)),d(_,{type:"text",size:"small",onClick:L},{default:i(()=>[d(t,null,{default:i(()=>[d(r(C))]),_:1}),a[45]||(a[45]=u(" 重置 ",-1))]),_:1,__:[45]})]),e.selectedComponent?(o(),s("div",Ae,[n("div",Oe,[a[50]||(a[50]=n("div",{class:"group-title"},"基础属性",-1)),n("div",Ee,[a[47]||(a[47]=n("label",null,"组件ID",-1)),d(g,{modelValue:h.id,"onUpdate:modelValue":a[0]||(a[0]=e=>h.id=e),size:"small",placeholder:"组件唯一标识",onInput:a[1]||(a[1]=e=>T("id",e))},null,8,["modelValue"])]),n("div",Le,[a[48]||(a[48]=n("label",null,"组件名称",-1)),d(g,{modelValue:h.name,"onUpdate:modelValue":a[2]||(a[2]=e=>h.name=e),size:"small",placeholder:"组件显示名称",onInput:a[3]||(a[3]=e=>T("name",e))},null,8,["modelValue"])]),n("div",qe,[a[49]||(a[49]=n("label",null,"CSS类名",-1)),d(g,{modelValue:h.className,"onUpdate:modelValue":a[4]||(a[4]=e=>h.className=e),size:"small",placeholder:"自定义CSS类名",onInput:a[5]||(a[5]=e=>T("className",e))},null,8,["modelValue"])])]),n("div",Me,[a[60]||(a[60]=n("div",{class:"group-title"},"样式属性",-1)),n("div",Be,[a[52]||(a[52]=n("label",null,"宽度",-1)),d(g,{modelValue:h.width,"onUpdate:modelValue":a[6]||(a[6]=e=>h.width=e),size:"small",placeholder:"如: 100px, 50%, auto",onInput:a[7]||(a[7]=e=>T("width",e))},{append:i(()=>a[51]||(a[51]=[u("px",-1)])),_:1},8,["modelValue"])]),n("div",We,[a[54]||(a[54]=n("label",null,"高度",-1)),d(g,{modelValue:h.height,"onUpdate:modelValue":a[8]||(a[8]=e=>h.height=e),size:"small",placeholder:"如: 200px, auto",onInput:a[9]||(a[9]=e=>T("height",e))},{append:i(()=>a[53]||(a[53]=[u("px",-1)])),_:1},8,["modelValue"])]),n("div",Re,[a[55]||(a[55]=n("label",null,"背景颜色",-1)),d(f,{modelValue:h.backgroundColor,"onUpdate:modelValue":a[10]||(a[10]=e=>h.backgroundColor=e),size:"small",onChange:a[11]||(a[11]=e=>T("backgroundColor",e))},null,8,["modelValue"])]),n("div",Fe,[a[56]||(a[56]=n("label",null,"文字颜色",-1)),d(f,{modelValue:h.color,"onUpdate:modelValue":a[12]||(a[12]=e=>h.color=e),size:"small",onChange:a[13]||(a[13]=e=>T("color",e))},null,8,["modelValue"])]),n("div",Pe,[a[57]||(a[57]=n("label",null,"字体大小",-1)),d(k,{modelValue:h.fontSize,"onUpdate:modelValue":a[14]||(a[14]=e=>h.fontSize=e),size:"small",min:12,max:72,onChange:a[15]||(a[15]=e=>T("fontSize",e+"px"))},null,8,["modelValue"])]),n("div",Ne,[a[58]||(a[58]=n("label",null,"字体粗细",-1)),d(M,{modelValue:h.fontWeight,"onUpdate:modelValue":a[16]||(a[16]=e=>h.fontWeight=e),size:"small",onChange:a[17]||(a[17]=e=>T("fontWeight",e))},{default:i(()=>[d(q,{label:"正常",value:"normal"}),d(q,{label:"粗体",value:"bold"}),d(q,{label:"100",value:"100"}),d(q,{label:"200",value:"200"}),d(q,{label:"300",value:"300"}),d(q,{label:"400",value:"400"}),d(q,{label:"500",value:"500"}),d(q,{label:"600",value:"600"}),d(q,{label:"700",value:"700"}),d(q,{label:"800",value:"800"}),d(q,{label:"900",value:"900"})]),_:1},8,["modelValue"])]),n("div",Ge,[a[59]||(a[59]=n("label",null,"文字对齐",-1)),d(M,{modelValue:h.textAlign,"onUpdate:modelValue":a[18]||(a[18]=e=>h.textAlign=e),size:"small",onChange:a[19]||(a[19]=e=>T("textAlign",e))},{default:i(()=>[d(q,{label:"左对齐",value:"left"}),d(q,{label:"居中",value:"center"}),d(q,{label:"右对齐",value:"right"}),d(q,{label:"两端对齐",value:"justify"})]),_:1},8,["modelValue"])])]),n("div",He,[a[65]||(a[65]=n("div",{class:"group-title"},"布局属性",-1)),n("div",Je,[a[61]||(a[61]=n("label",null,"外边距",-1)),n("div",Qe,[d(k,{modelValue:y.top,"onUpdate:modelValue":a[20]||(a[20]=e=>y.top=e),size:"small",placeholder:"上",onChange:A},null,8,["modelValue"]),d(k,{modelValue:y.right,"onUpdate:modelValue":a[21]||(a[21]=e=>y.right=e),size:"small",placeholder:"右",onChange:A},null,8,["modelValue"]),d(k,{modelValue:y.bottom,"onUpdate:modelValue":a[22]||(a[22]=e=>y.bottom=e),size:"small",placeholder:"下",onChange:A},null,8,["modelValue"]),d(k,{modelValue:y.left,"onUpdate:modelValue":a[23]||(a[23]=e=>y.left=e),size:"small",placeholder:"左",onChange:A},null,8,["modelValue"])])]),n("div",Ke,[a[62]||(a[62]=n("label",null,"内边距",-1)),n("div",Ze,[d(k,{modelValue:b.top,"onUpdate:modelValue":a[24]||(a[24]=e=>b.top=e),size:"small",placeholder:"上",onChange:O},null,8,["modelValue"]),d(k,{modelValue:b.right,"onUpdate:modelValue":a[25]||(a[25]=e=>b.right=e),size:"small",placeholder:"右",onChange:O},null,8,["modelValue"]),d(k,{modelValue:b.bottom,"onUpdate:modelValue":a[26]||(a[26]=e=>b.bottom=e),size:"small",placeholder:"下",onChange:O},null,8,["modelValue"]),d(k,{modelValue:b.left,"onUpdate:modelValue":a[27]||(a[27]=e=>b.left=e),size:"small",placeholder:"左",onChange:O},null,8,["modelValue"])])]),n("div",Xe,[a[63]||(a[63]=n("label",null,"边框",-1)),n("div",el,[d(k,{modelValue:h.borderWidth,"onUpdate:modelValue":a[28]||(a[28]=e=>h.borderWidth=e),size:"small",placeholder:"宽度",min:0,onChange:E},null,8,["modelValue"]),d(M,{modelValue:h.borderStyle,"onUpdate:modelValue":a[29]||(a[29]=e=>h.borderStyle=e),size:"small",onChange:E},{default:i(()=>[d(q,{label:"实线",value:"solid"}),d(q,{label:"虚线",value:"dashed"}),d(q,{label:"点线",value:"dotted"}),d(q,{label:"无边框",value:"none"})]),_:1},8,["modelValue"]),d(f,{modelValue:h.borderColor,"onUpdate:modelValue":a[30]||(a[30]=e=>h.borderColor=e),size:"small",onChange:E},null,8,["modelValue"])])]),n("div",ll,[a[64]||(a[64]=n("label",null,"圆角",-1)),d(k,{modelValue:h.borderRadius,"onUpdate:modelValue":a[31]||(a[31]=e=>h.borderRadius=e),size:"small",min:0,onChange:a[32]||(a[32]=e=>T("borderRadius",e+"px"))},null,8,["modelValue"])])]),V.value.length>0?(o(),s("div",al,[n("div",tl,D(e.selectedComponent.type)+" 属性",1),(o(!0),s(p,null,m(V.value,e=>(o(),s("div",{key:e.key,class:"property-item"},[n("label",null,D(e.label),1),"text"===e.type?(o(),v(g,{key:0,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,size:"small",placeholder:e.placeholder,onInput:l=>T(e.key,l)},null,8,["modelValue","onUpdate:modelValue","placeholder","onInput"])):"number"===e.type?(o(),v(k,{key:1,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,size:"small",min:e.min,max:e.max,onChange:l=>T(e.key,l)},null,8,["modelValue","onUpdate:modelValue","min","max","onChange"])):"select"===e.type?(o(),v(M,{key:2,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,size:"small",onChange:l=>T(e.key,l)},{default:i(()=>[(o(!0),s(p,null,m(e.options,e=>(o(),v(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):"switch"===e.type?(o(),v(B,{key:3,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,size:"small",onChange:l=>T(e.key,l)},null,8,["modelValue","onUpdate:modelValue","onChange"])):"color"===e.type?(o(),v(f,{key:4,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,size:"small",onChange:l=>T(e.key,l)},null,8,["modelValue","onUpdate:modelValue","onChange"])):"textarea"===e.type?(o(),v(g,{key:5,modelValue:h[e.key],"onUpdate:modelValue":l=>h[e.key]=l,type:"textarea",size:"small",rows:3,placeholder:e.placeholder,onInput:l=>T(e.key,l)},null,8,["modelValue","onUpdate:modelValue","placeholder","onInput"])):c("",!0)]))),128))])):c("",!0),n("div",sl,[a[69]||(a[69]=n("div",{class:"group-title"},"动画效果",-1)),n("div",ol,[a[66]||(a[66]=n("label",null,"进入动画",-1)),d(M,{modelValue:h.enterAnimation,"onUpdate:modelValue":a[33]||(a[33]=e=>h.enterAnimation=e),size:"small",onChange:a[34]||(a[34]=e=>T("enterAnimation",e))},{default:i(()=>[d(q,{label:"无动画",value:"none"}),d(q,{label:"淡入",value:"fadeIn"}),d(q,{label:"从左滑入",value:"slideInLeft"}),d(q,{label:"从右滑入",value:"slideInRight"}),d(q,{label:"从上滑入",value:"slideInUp"}),d(q,{label:"从下滑入",value:"slideInDown"}),d(q,{label:"缩放进入",value:"zoomIn"}),d(q,{label:"弹跳进入",value:"bounceIn"})]),_:1},8,["modelValue"])]),n("div",nl,[a[67]||(a[67]=n("label",null,"动画延迟",-1)),d(k,{modelValue:h.animationDelay,"onUpdate:modelValue":a[35]||(a[35]=e=>h.animationDelay=e),size:"small",min:0,step:.1,onChange:a[36]||(a[36]=e=>T("animationDelay",e+"s"))},null,8,["modelValue"])]),n("div",dl,[a[68]||(a[68]=n("label",null,"动画时长",-1)),d(k,{modelValue:h.animationDuration,"onUpdate:modelValue":a[37]||(a[37]=e=>h.animationDuration=e),size:"small",min:.1,step:.1,onChange:a[38]||(a[38]=e=>T("animationDuration",e+"s"))},null,8,["modelValue"])])]),n("div",il,[a[73]||(a[73]=n("div",{class:"group-title"},"交互设置",-1)),n("div",ul,[a[70]||(a[70]=n("label",null,"点击事件",-1)),d(M,{modelValue:h.clickAction,"onUpdate:modelValue":a[39]||(a[39]=e=>h.clickAction=e),size:"small",onChange:a[40]||(a[40]=e=>T("clickAction",e))},{default:i(()=>[d(q,{label:"无操作",value:"none"}),d(q,{label:"跳转链接",value:"link"}),d(q,{label:"显示弹窗",value:"modal"}),d(q,{label:"滚动到元素",value:"scroll"}),d(q,{label:"提交表单",value:"submit"})]),_:1},8,["modelValue"])]),"link"===h.clickAction?(o(),s("div",rl,[a[71]||(a[71]=n("label",null,"跳转地址",-1)),d(g,{modelValue:h.linkUrl,"onUpdate:modelValue":a[41]||(a[41]=e=>h.linkUrl=e),size:"small",placeholder:"https://example.com",onInput:a[42]||(a[42]=e=>T("linkUrl",e))},null,8,["modelValue"])])):c("",!0),n("div",cl,[a[72]||(a[72]=n("label",null,"悬停效果",-1)),d(B,{modelValue:h.hoverEffect,"onUpdate:modelValue":a[43]||(a[43]=e=>h.hoverEffect=e),size:"small",onChange:a[44]||(a[44]=e=>T("hoverEffect",e))},null,8,["modelValue"])])])])):(o(),s("div",pl,[d(t,null,{default:i(()=>[d(r(Y))]),_:1}),a[74]||(a[74]=n("p",null,"请选择一个组件来编辑属性",-1))]))])}}},[["__scopeId","data-v-ba51d75d"]]),vl={class:"editor-container"},_l={class:"editor-toolbar"},gl={class:"toolbar-left"},fl={class:"toolbar-right"},hl={class:"editor-content"},yl={class:"design-panel"},bl={class:"design-sidebar"},Vl={class:"component-library"},kl={class:"component-list"},wl={class:"design-canvas"},Cl={class:"canvas-container"},xl={class:"page-canvas"},Ul=["onClick"],zl={class:"component-controls"},Il={key:0,class:"empty-canvas"},Sl={class:"design-properties"},jl={class:"properties-panel"},Dl={key:0,class:"component-properties"},$l={key:1,class:"no-selection"},Yl={class:"preview-panel"},Tl={class:"preview-toolbar"},Al={class:"preview-frame"},Ol={class:"preview-content"},El={class:"settings-panel"},Ll={class:"dialog-footer"},ql=e({__name:"LandingPageEditor",props:{modelValue:{type:Boolean,default:!1},pageData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:b}){const V=e,k=b,C=_("design"),z=_("desktop"),I=_(null),S=_(!1),j=a({get:()=>V.modelValue,set:e=>k("update:modelValue",e)}),$=a(()=>V.pageData&&V.pageData.id),Y=_([]),te=l({name:"",description:"",url:"",seo_title:"",seo_description:"",seo_keywords:"",custom_css:"",custom_js:"",analytics_code:""});t(()=>V.modelValue,e=>{e&&y(()=>{se()})});const se=()=>{$.value?(Object.keys(te).forEach(e=>{void 0!==V.pageData[e]&&(te[e]=V.pageData[e])}),Y.value=V.pageData.components||[]):(Object.keys(te).forEach(e=>{te[e]=""}),Y.value=[]),I.value=null},oe=e=>{const l=ne(e);Y.value.push({type:e,data:l})},ne=e=>({header:{logo:"",title:"网站标题",navigation:["首页","产品","关于我们","联系我们"]},hero:{title:"欢迎来到我们的产品",subtitle:"这里是产品的简短描述",background:"",buttonText:"立即开始",buttonLink:"#"},features:{title:"产品特性",items:[{title:"特性一",description:"特性描述",icon:"star"},{title:"特性二",description:"特性描述",icon:"heart"},{title:"特性三",description:"特性描述",icon:"check"}]},form:{title:"联系我们",fields:[{type:"text",label:"姓名",required:!0},{type:"email",label:"邮箱",required:!0},{type:"textarea",label:"留言",required:!1}],buttonText:"提交"},footer:{copyright:"© 2024 公司名称. 保留所有权利.",links:["隐私政策","服务条款","联系我们"]}}[e]||{}),de=e=>({header:"HeaderComponent",hero:"HeroComponent",features:"FeaturesComponent",form:"FormComponent",footer:"FooterComponent"}[e]||"div"),ie=(e,l)=>{Y.value[e]&&(Y.value[e].data={...l})},ue=async()=>{await ce("draft")},re=async()=>{await ce("published")},ce=async(e="draft")=>{if(te.name){S.value=!0;try{const l={...te,components:Y.value,status:e};$.value?(await je.update(V.pageData.id,l),ae.success("落地页更新成功")):(await je.create(l),ae.success("落地页创建成功")),k("success"),pe()}catch(l){console.error("保存失败:",l),ae.error("保存失败")}finally{S.value=!1}}else ae.error("请输入页面名称")},pe=()=>{j.value=!1};return(e,l)=>{const a=w,t=x,_=T,y=H,b=G,V=X,k=U,S=ee,ae=Z,se=le;return o(),v(se,{modelValue:j.value,"onUpdate:modelValue":l[19]||(l[19]=e=>j.value=e),title:$.value?"编辑落地页":"创建落地页",width:"90%","before-close":pe,class:"landing-page-editor-dialog"},{footer:i(()=>[n("div",Ll,[d(t,{onClick:pe},{default:i(()=>l[41]||(l[41]=[u("取消",-1)])),_:1,__:[41]}),d(t,{onClick:ue},{default:i(()=>l[42]||(l[42]=[u("保存草稿",-1)])),_:1,__:[42]}),d(t,{type:"primary",onClick:ce},{default:i(()=>[u(D($.value?"更新":"创建"),1)]),_:1})])]),default:i(()=>[n("div",vl,[n("div",_l,[n("div",gl,[d(_,null,{default:i(()=>[d(t,{type:"design"===C.value?"primary":"",onClick:l[0]||(l[0]=e=>C.value="design")},{default:i(()=>[d(a,null,{default:i(()=>[d(r(A))]),_:1}),l[20]||(l[20]=u(" 设计 ",-1))]),_:1,__:[20]},8,["type"]),d(t,{type:"preview"===C.value?"primary":"",onClick:l[1]||(l[1]=e=>C.value="preview")},{default:i(()=>[d(a,null,{default:i(()=>[d(r(O))]),_:1}),l[21]||(l[21]=u(" 预览 ",-1))]),_:1,__:[21]},8,["type"]),d(t,{type:"settings"===C.value?"primary":"",onClick:l[2]||(l[2]=e=>C.value="settings")},{default:i(()=>[d(a,null,{default:i(()=>[d(r(E))]),_:1}),l[22]||(l[22]=u(" 设置 ",-1))]),_:1,__:[22]},8,["type"])]),_:1})]),n("div",fl,[d(t,{onClick:ue},{default:i(()=>[d(a,null,{default:i(()=>[d(r(L))]),_:1}),l[23]||(l[23]=u(" 保存草稿 ",-1))]),_:1,__:[23]}),d(t,{type:"success",onClick:re},{default:i(()=>[d(a,null,{default:i(()=>[d(r(q))]),_:1}),l[24]||(l[24]=u(" 发布 ",-1))]),_:1,__:[24]})])]),n("div",hl,[g(n("div",yl,[n("div",bl,[n("div",Vl,[l[30]||(l[30]=n("h4",null,"组件库",-1)),n("div",kl,[n("div",{class:"component-item",onClick:l[3]||(l[3]=e=>oe("header"))},[d(a,null,{default:i(()=>[d(r(M))]),_:1}),l[25]||(l[25]=n("span",null,"页头",-1))]),n("div",{class:"component-item",onClick:l[4]||(l[4]=e=>oe("hero"))},[d(a,null,{default:i(()=>[d(r(B))]),_:1}),l[26]||(l[26]=n("span",null,"英雄区",-1))]),n("div",{class:"component-item",onClick:l[5]||(l[5]=e=>oe("features"))},[d(a,null,{default:i(()=>[d(r(W))]),_:1}),l[27]||(l[27]=n("span",null,"特性列表",-1))]),n("div",{class:"component-item",onClick:l[6]||(l[6]=e=>oe("form"))},[d(a,null,{default:i(()=>[d(r(A))]),_:1}),l[28]||(l[28]=n("span",null,"表单",-1))]),n("div",{class:"component-item",onClick:l[7]||(l[7]=e=>oe("footer"))},[d(a,null,{default:i(()=>[d(r(R))]),_:1}),l[29]||(l[29]=n("span",null,"页脚",-1))])])])]),n("div",wl,[n("div",Cl,[n("div",xl,[(o(!0),s(p,null,m(Y.value,(e,l)=>(o(),s("div",{key:l,class:F(["component-wrapper",{active:I.value===l}]),onClick:e=>(e=>{I.value=e})(l)},[(o(),v(f(de(e.type)),{data:e.data,onUpdate:e=>ie(l,e)},null,40,["data","onUpdate"])),n("div",zl,[d(t,{size:"small",type:"primary",onClick:e=>(e=>{I.value=e})(l)},{default:i(()=>[d(a,null,{default:i(()=>[d(r(A))]),_:1})]),_:2},1032,["onClick"]),d(t,{size:"small",type:"danger",onClick:e=>(e=>{Y.value.splice(e,1),I.value===e&&(I.value=null)})(l)},{default:i(()=>[d(a,null,{default:i(()=>[d(r(P))]),_:1})]),_:2},1032,["onClick"])])],10,Ul))),128)),0===Y.value.length?(o(),s("div",Il,[d(a,null,{default:i(()=>[d(r(N))]),_:1}),l[31]||(l[31]=n("p",null,"从左侧组件库拖拽组件到这里开始设计",-1))])):c("",!0)])])]),n("div",Sl,[n("div",jl,[l[33]||(l[33]=n("h4",null,"属性面板",-1)),null!==I.value?(o(),s("div",Dl,[d(ml,{component:Y.value[I.value],onUpdate:l[8]||(l[8]=e=>ie(I.value,e))},null,8,["component"])])):(o(),s("div",$l,l[32]||(l[32]=[n("p",null,"请选择一个组件来编辑属性",-1)])))])])],512),[[h,"design"===C.value]]),g(n("div",Yl,[n("div",Tl,[d(b,{modelValue:z.value,"onUpdate:modelValue":l[9]||(l[9]=e=>z.value=e),size:"small"},{default:i(()=>[d(y,{label:"desktop"},{default:i(()=>[d(a,null,{default:i(()=>[d(r(J))]),_:1}),l[34]||(l[34]=u(" 桌面 ",-1))]),_:1,__:[34]}),d(y,{label:"tablet"},{default:i(()=>[d(a,null,{default:i(()=>[d(r(Q))]),_:1}),l[35]||(l[35]=u(" 平板 ",-1))]),_:1,__:[35]}),d(y,{label:"mobile"},{default:i(()=>[d(a,null,{default:i(()=>[d(r(K))]),_:1}),l[36]||(l[36]=u(" 手机 ",-1))]),_:1,__:[36]})]),_:1},8,["modelValue"])]),n("div",{class:F(["preview-container",`preview-${z.value}`])},[n("div",Al,[n("div",Ol,[(o(!0),s(p,null,m(Y.value,(e,l)=>(o(),s("div",{key:l},[(o(),v(f(de(e.type)),{data:e.data,preview:!0},null,8,["data"]))]))),128))])])],2)],512),[[h,"preview"===C.value]]),g(n("div",El,[d(ae,{model:te,"label-width":"120px"},{default:i(()=>[d(V,{"content-position":"left"},{default:i(()=>l[37]||(l[37]=[u("基本信息",-1)])),_:1,__:[37]}),d(S,{label:"页面名称",required:""},{default:i(()=>[d(k,{modelValue:te.name,"onUpdate:modelValue":l[10]||(l[10]=e=>te.name=e),placeholder:"请输入页面名称"},null,8,["modelValue"])]),_:1}),d(S,{label:"页面描述"},{default:i(()=>[d(k,{modelValue:te.description,"onUpdate:modelValue":l[11]||(l[11]=e=>te.description=e),type:"textarea",rows:3,placeholder:"请输入页面描述"},null,8,["modelValue"])]),_:1}),d(S,{label:"页面URL"},{default:i(()=>[d(k,{modelValue:te.url,"onUpdate:modelValue":l[12]||(l[12]=e=>te.url=e),placeholder:"https://example.com/landing-page"},{prepend:i(()=>l[38]||(l[38]=[n("span",null,"https://",-1)])),_:1},8,["modelValue"])]),_:1}),d(V,{"content-position":"left"},{default:i(()=>l[39]||(l[39]=[u("SEO设置",-1)])),_:1,__:[39]}),d(S,{label:"页面标题"},{default:i(()=>[d(k,{modelValue:te.seo_title,"onUpdate:modelValue":l[13]||(l[13]=e=>te.seo_title=e),placeholder:"页面标题，用于搜索引擎显示",maxlength:"60","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(S,{label:"页面描述"},{default:i(()=>[d(k,{modelValue:te.seo_description,"onUpdate:modelValue":l[14]||(l[14]=e=>te.seo_description=e),type:"textarea",rows:3,placeholder:"页面描述，用于搜索引擎显示",maxlength:"160","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(S,{label:"关键词"},{default:i(()=>[d(k,{modelValue:te.seo_keywords,"onUpdate:modelValue":l[15]||(l[15]=e=>te.seo_keywords=e),placeholder:"关键词，用逗号分隔"},null,8,["modelValue"])]),_:1}),d(V,{"content-position":"left"},{default:i(()=>l[40]||(l[40]=[u("高级设置",-1)])),_:1,__:[40]}),d(S,{label:"自定义CSS"},{default:i(()=>[d(k,{modelValue:te.custom_css,"onUpdate:modelValue":l[16]||(l[16]=e=>te.custom_css=e),type:"textarea",rows:6,placeholder:"/* 自定义CSS样式 */"},null,8,["modelValue"])]),_:1}),d(S,{label:"自定义JS"},{default:i(()=>[d(k,{modelValue:te.custom_js,"onUpdate:modelValue":l[17]||(l[17]=e=>te.custom_js=e),type:"textarea",rows:6,placeholder:"// 自定义JavaScript代码"},null,8,["modelValue"])]),_:1}),d(S,{label:"统计代码"},{default:i(()=>[d(k,{modelValue:te.analytics_code,"onUpdate:modelValue":l[18]||(l[18]=e=>te.analytics_code=e),type:"textarea",rows:4,placeholder:"Google Analytics 或其他统计代码"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])],512),[[h,"settings"===C.value]])])])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-0f0a9f4a"]]),Ml={class:"template-selector"},Bl={class:"template-categories"},Wl={class:"template-grid"},Rl=["onClick"],Fl={class:"template-preview"},Pl=["src","alt"],Nl={class:"template-overlay"},Gl={class:"template-info"},Hl={class:"template-name"},Jl={class:"template-desc"},Ql={class:"template-meta"},Kl={class:"template-category"},Zl={class:"template-stats"},Xl={class:"usage-count"},ea={class:"rating"},la={class:"template-actions"},aa={key:0,class:"empty-state"},ta={class:"dialog-footer"},sa={class:"template-preview-container"},oa=["src"],na=e({__name:"TemplateSelector",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","select"],setup(e,{emit:l}){const t=e,f=l,h=_(!1),y=_("all"),k=_(null),C=_(!1),U=_(null),z=a({get:()=>t.modelValue,set:e=>f("update:modelValue",e)}),I=_([]),S=[{id:1,name:"产品营销模板",description:"适用于产品推广和营销活动的专业模板",category:"marketing",preview_image:"/placeholder.svg?height=300&width=400",usage_count:1250,rating:4.8,preview_url:"/placeholder.svg?height=600&width=800&text=模板1预览"},{id:2,name:"产品展示模板",description:"展示产品特性和优势的精美模板",category:"product",preview_image:"/placeholder.svg?height=300&width=400",usage_count:890,rating:4.9,preview_url:"/placeholder.svg?height=600&width=800&text=模板2预览"},{id:3,name:"活动报名模板",description:"在线活动报名和信息收集模板",category:"event",preview_image:"/placeholder.svg?height=300&width=400",usage_count:567,rating:4.7,preview_url:"/placeholder.svg?height=600&width=800&text=模板3预览"},{id:4,name:"表单收集模板",description:"用户信息收集和问卷调查模板",category:"form",preview_image:"/placeholder.svg?height=300&width=400",usage_count:432,rating:4.6,preview_url:"/placeholder.svg?height=600&width=800&text=模板4预览"}],j=a(()=>"all"===y.value?I.value:I.value.filter(e=>e.category===y.value)),$=a(()=>U.value?.preview_url||""),Y=()=>{k.value=null},T=()=>{k.value?(f("select",k.value),A()):ae.warning("请选择一个模板")},A=()=>{z.value=!1,k.value=null},E=e=>({marketing:"营销推广",product:"产品展示",event:"活动页面",form:"表单收集"}[e]||"未知");return b(()=>{(async()=>{h.value=!0;try{const{data:e}=await je.getList({type:"template"});I.value=e||S}catch(e){console.error("加载模板失败:",e),I.value=S}finally{h.value=!1}})()}),(e,l)=>{const a=H,t=G,_=w,f=x,b=te,I=le,S=ne;return o(),v(I,{modelValue:z.value,"onUpdate:modelValue":l[2]||(l[2]=e=>z.value=e),title:"选择模板",width:"80%","before-close":A},{footer:i(()=>[n("div",ta,[d(f,{onClick:A},{default:i(()=>l[11]||(l[11]=[u("取消",-1)])),_:1,__:[11]}),d(f,{type:"primary",disabled:!k.value,onClick:T},{default:i(()=>l[12]||(l[12]=[u(" 确定使用 ",-1)])),_:1,__:[12]},8,["disabled"])])]),default:i(()=>[n("div",Ml,[n("div",Bl,[d(t,{modelValue:y.value,"onUpdate:modelValue":l[0]||(l[0]=e=>y.value=e),onChange:Y},{default:i(()=>[d(a,{value:"all"},{default:i(()=>l[3]||(l[3]=[u("全部模板",-1)])),_:1,__:[3]}),d(a,{value:"marketing"},{default:i(()=>l[4]||(l[4]=[u("营销推广",-1)])),_:1,__:[4]}),d(a,{value:"product"},{default:i(()=>l[5]||(l[5]=[u("产品展示",-1)])),_:1,__:[5]}),d(a,{value:"event"},{default:i(()=>l[6]||(l[6]=[u("活动页面",-1)])),_:1,__:[6]}),d(a,{value:"form"},{default:i(()=>l[7]||(l[7]=[u("表单收集",-1)])),_:1,__:[7]})]),_:1},8,["modelValue"])]),g((o(),s("div",Wl,[(o(!0),s(p,null,m(j.value,e=>{return o(),s("div",{key:e.id,class:F(["template-card",{selected:k.value?.id===e.id}]),onClick:l=>(e=>{k.value=e})(e)},[n("div",Fl,[n("img",{src:e.preview_image,alt:e.name},null,8,Pl),n("div",Nl,[d(f,{type:"primary",size:"small",onClick:V(l=>(e=>{U.value=e,C.value=!0})(e),["stop"])},{default:i(()=>[d(_,null,{default:i(()=>[d(r(O))]),_:1}),l[8]||(l[8]=u(" 预览 ",-1))]),_:2,__:[8]},1032,["onClick"])])]),n("div",Gl,[n("h4",Hl,D(e.name),1),n("p",Jl,D(e.description),1),n("div",Ql,[n("div",Kl,[d(b,{size:"small",type:(a=e.category,{marketing:"primary",product:"success",event:"warning",form:"info"}[a]||"")},{default:i(()=>[u(D(E(e.category)),1)]),_:2},1032,["type"])]),n("div",Zl,[n("span",Xl,[d(_,null,{default:i(()=>[d(r(se))]),_:1}),u(" "+D(e.usage_count||0),1)]),n("span",ea,[d(_,null,{default:i(()=>[d(r(oe))]),_:1}),u(" "+D(e.rating||5),1)])])])]),n("div",la,[d(f,{type:"primary",size:"small",onClick:V(l=>(e=>{k.value=e,T()})(e),["stop"])},{default:i(()=>l[9]||(l[9]=[u(" 使用模板 ",-1)])),_:2,__:[9]},1032,["onClick"])])],10,Rl);var a}),128))])),[[S,h.value]]),0!==j.value.length||h.value?c("",!0):(o(),s("div",aa,[d(_,null,{default:i(()=>[d(r(L))]),_:1}),l[10]||(l[10]=n("p",null,"暂无模板",-1))]))]),d(I,{modelValue:C.value,"onUpdate:modelValue":l[1]||(l[1]=e=>C.value=e),title:"模板预览",width:"90%","append-to-body":""},{default:i(()=>[n("div",sa,[U.value?(o(),s("iframe",{key:0,src:$.value,frameborder:"0",width:"100%",height:"600px"},null,8,oa)):c("",!0)])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-f9114bd7"]]),da={class:"analytics-container"},ia={class:"metrics-overview"},ua={class:"metric-card"},ra={class:"metric-icon visits"},ca={class:"metric-content"},pa={class:"metric-number"},ma={class:"metric-trend up"},va={class:"metric-card"},_a={class:"metric-icon unique"},ga={class:"metric-content"},fa={class:"metric-number"},ha={class:"metric-trend up"},ya={class:"metric-card"},ba={class:"metric-icon conversions"},Va={class:"metric-content"},ka={class:"metric-number"},wa={class:"metric-trend up"},Ca={class:"metric-card"},xa={class:"metric-icon rate"},Ua={class:"metric-content"},za={class:"metric-number"},Ia={class:"metric-trend down"},Sa={class:"filter-section"},ja={class:"card-header"},Da={class:"chart-container"},$a={key:0,class:"chart-placeholder"},Ya={key:1,class:"trend-chart"},Ta={class:"chart-demo"},Aa={class:"chart-data"},Oa={class:"data-label"},Ea={class:"data-value"},La={class:"source-stats"},qa={class:"source-info"},Ma={class:"source-name"},Ba={class:"source-count"},Wa={class:"source-progress"},Ra={class:"source-percentage"},Fa={class:"device-stats"},Pa={class:"device-info"},Na={class:"device-name"},Ga={class:"device-stats-detail"},Ha={class:"device-count"},Ja={class:"device-percentage"},Qa={class:"region-stats"},Ka={class:"region-info"},Za={class:"region-name"},Xa={class:"region-count"},et={class:"region-bar"},lt={class:"region-percentage"},at={class:"funnel-container"},tt={class:"funnel-steps"},st={class:"step-content"},ot={class:"step-name"},nt={class:"step-count"},dt={class:"step-percentage"},it={key:0,class:"step-loss"},ut={class:"card-header"},rt={class:"heatmap-container"},ct={class:"heatmap-placeholder"},pt={class:"drawer-footer"},mt=e({__name:"LandingPageAnalytics",props:{modelValue:{type:Boolean,default:!1},pageId:{type:[Number,String],default:null}},emits:["update:modelValue"],setup(e,{emit:f}){const h=e,y=f,b=_(!1),V=_("visits"),k=_([]),U=a({get:()=>h.modelValue,set:e=>y("update:modelValue",e)}),z=l({total_visits:0,unique_visitors:0,conversions:0,conversion_rate:0}),I=_([]),S=_([]),j=_([]),$=_([]),Y=_([]),T=_([]),A=a(()=>Math.max(...I.value.map(e=>e.value)));t(()=>h.modelValue,e=>{e&&h.pageId&&(E(),L())});const E=()=>{const e=new Date,l=new Date;l.setDate(e.getDate()-7),k.value=[l.toISOString().split("T")[0],e.toISOString().split("T")[0]]},L=async()=>{if(h.pageId){b.value=!0;try{const e={start_date:k.value[0],end_date:k.value[1]},l=await De.getClickTrend({page_id:h.pageId,...e});z.total_visits=l.data.total_visits||12580,z.unique_visitors=l.data.unique_visitors||8960;const a=await De.getConversionFunnel({page_id:h.pageId,...e});z.conversions=a.data.conversions||1258,z.conversion_rate=a.data.conversion_rate||10,q()}catch(e){console.error("加载统计数据失败:",e),z.total_visits=12580,z.unique_visitors=8960,z.conversions=1258,z.conversion_rate=10,q()}finally{b.value=!1}}},q=()=>{I.value=[];for(let e=6;e>=0;e--){const l=new Date;l.setDate(l.getDate()-e),I.value.push({date:l.toISOString().split("T")[0],value:Math.floor(500*Math.random())+100})}S.value=[{name:"搜索引擎",count:4580,percentage:36},{name:"社交媒体",count:3520,percentage:28},{name:"直接访问",count:2640,percentage:21},{name:"推荐链接",count:1840,percentage:15}],j.value=[{name:"手机",count:7560,percentage:60},{name:"桌面",count:3780,percentage:30},{name:"平板",count:1260,percentage:10}],$.value=[{name:"广东",count:3520,percentage:28},{name:"浙江",count:2640,percentage:21},{name:"江苏",count:1890,percentage:15},{name:"上海",count:1260,percentage:10},{name:"其他",count:3270,percentage:26}],Y.value=[{name:"页面访问",count:12580,percentage:100},{name:"内容浏览",count:8960,percentage:71},{name:"表单填写",count:3520,percentage:28},{name:"提交成功",count:1258,percentage:10}],T.value=[{ip:"***********",location:"广东深圳",device:"手机",browser:"Chrome",source:"微信",stay_time:145,converted:!0,visit_time:(new Date).toISOString()},{ip:"***********",location:"浙江杭州",device:"桌面",browser:"Safari",source:"百度",stay_time:89,converted:!1,visit_time:new Date(Date.now()-3e5).toISOString()}]},M=()=>{q()},W=()=>{ae.success("热力图生成功能开发中...")},R=()=>{ae.success("数据导出功能开发中...")},F=e=>e>=1e4?(e/1e4).toFixed(1)+"W":e.toString(),P=()=>{U.value=!1};return(e,l)=>{const a=w,t=ie,_=de,f=me,h=x,y=H,E=G,q=_e,N=he,Z=be,X=te,ee=ye,le=Ve,ae=ne;return o(),v(le,{modelValue:U.value,"onUpdate:modelValue":l[2]||(l[2]=e=>U.value=e),title:"落地页数据分析",size:"70%",direction:"rtl","before-close":P},{footer:i(()=>[n("div",pt,[d(h,{onClick:R},{default:i(()=>l[26]||(l[26]=[u("导出完整报告",-1)])),_:1,__:[26]}),d(h,{type:"primary",onClick:P},{default:i(()=>l[27]||(l[27]=[u("关闭",-1)])),_:1,__:[27]})])]),default:i(()=>[g((o(),s("div",da,[n("div",ia,[d(_,{gutter:16},{default:i(()=>[d(t,{span:6},{default:i(()=>[n("div",ua,[n("div",ra,[d(a,null,{default:i(()=>[d(r(O))]),_:1})]),n("div",ca,[n("div",pa,D(F(z.total_visits)),1),l[4]||(l[4]=n("div",{class:"metric-label"},"总访问量",-1)),n("div",ma,[d(a,null,{default:i(()=>[d(r(ue))]),_:1}),l[3]||(l[3]=n("span",null,"+15.2% 较上周",-1))])])])]),_:1}),d(t,{span:6},{default:i(()=>[n("div",va,[n("div",_a,[d(a,null,{default:i(()=>[d(r(se))]),_:1})]),n("div",ga,[n("div",fa,D(F(z.unique_visitors)),1),l[6]||(l[6]=n("div",{class:"metric-label"},"独立访客",-1)),n("div",ha,[d(a,null,{default:i(()=>[d(r(ue))]),_:1}),l[5]||(l[5]=n("span",null,"+12.8% 较上周",-1))])])])]),_:1}),d(t,{span:6},{default:i(()=>[n("div",ya,[n("div",ba,[d(a,null,{default:i(()=>[d(r(re))]),_:1})]),n("div",Va,[n("div",ka,D(z.conversions),1),l[8]||(l[8]=n("div",{class:"metric-label"},"转化次数",-1)),n("div",wa,[d(a,null,{default:i(()=>[d(r(ue))]),_:1}),l[7]||(l[7]=n("span",null,"+8.5% 较上周",-1))])])])]),_:1}),d(t,{span:6},{default:i(()=>[n("div",Ca,[n("div",xa,[d(a,null,{default:i(()=>[d(r(ce))]),_:1})]),n("div",Ua,[n("div",za,D(z.conversion_rate)+"%",1),l[10]||(l[10]=n("div",{class:"metric-label"},"转化率",-1)),n("div",Ia,[d(a,null,{default:i(()=>[d(r(pe))]),_:1}),l[9]||(l[9]=n("span",null,"-2.1% 较上周",-1))])])])]),_:1})]),_:1})]),n("div",Sa,[d(f,{modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:L},null,8,["modelValue"]),d(h,{type:"primary",onClick:L},{default:i(()=>[d(a,null,{default:i(()=>[d(r(C))]),_:1}),l[11]||(l[11]=u(" 刷新数据 ",-1))]),_:1,__:[11]}),d(h,{type:"success",onClick:R},{default:i(()=>[d(a,null,{default:i(()=>[d(r(ve))]),_:1}),l[12]||(l[12]=u(" 导出数据 ",-1))]),_:1,__:[12]})]),d(q,{class:"chart-card"},{header:i(()=>[n("div",ja,[l[16]||(l[16]=n("span",null,"📈 访问趋势分析",-1)),d(E,{modelValue:V.value,"onUpdate:modelValue":l[1]||(l[1]=e=>V.value=e),size:"small",onChange:M},{default:i(()=>[d(y,{label:"visits"},{default:i(()=>l[13]||(l[13]=[u("访问量",-1)])),_:1,__:[13]}),d(y,{label:"visitors"},{default:i(()=>l[14]||(l[14]=[u("访客数",-1)])),_:1,__:[14]}),d(y,{label:"conversions"},{default:i(()=>l[15]||(l[15]=[u("转化数",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])])]),default:i(()=>[n("div",Da,[I.value.length?(o(),s("div",Ya,[n("div",Ta,[n("h4",null,D({visits:"访问量趋势",visitors:"访客数趋势",conversions:"转化数趋势"}[V.value]||"数据趋势"),1),n("div",Aa,[(o(!0),s(p,null,m(I.value,(e,l)=>(o(),s("div",{key:l,class:"data-point"},[n("div",{class:"data-bar",style:fe({height:e.value/A.value*100+"%"})},null,4),n("span",Oa,D(e.date),1),n("span",Ea,D(e.value),1)]))),128))])])])):(o(),s("div",$a,[d(a,null,{default:i(()=>[d(r(ge))]),_:1}),l[17]||(l[17]=n("p",null,"加载中...",-1))]))])]),_:1}),d(_,{gutter:20},{default:i(()=>[d(t,{span:8},{default:i(()=>[d(q,{class:"stats-card"},{header:i(()=>l[18]||(l[18]=[n("span",null,"📱 访问来源分析",-1)])),default:i(()=>[n("div",La,[(o(!0),s(p,null,m(S.value,e=>(o(),s("div",{key:e.name,class:"source-item"},[n("div",qa,[n("span",Ma,D(e.name),1),n("span",Ba,D(e.count),1)]),n("div",Wa,[d(N,{percentage:e.percentage,"stroke-width":8,"show-text":!1,color:"#409eff"},null,8,["percentage"])]),n("span",Ra,D(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1}),d(t,{span:8},{default:i(()=>[d(q,{class:"stats-card"},{header:i(()=>l[19]||(l[19]=[n("span",null,"💻 设备分析",-1)])),default:i(()=>[n("div",Fa,[(o(!0),s(p,null,m(j.value,e=>(o(),s("div",{key:e.name,class:"device-item"},[n("div",Pa,[d(a,null,{default:i(()=>["桌面"===e.name?(o(),v(r(J),{key:0})):"平板"===e.name?(o(),v(r(Q),{key:1})):(o(),v(r(K),{key:2}))]),_:2},1024),n("span",Na,D(e.name),1)]),n("div",Ga,[n("div",Ha,D(e.count),1),n("div",Ja,D(e.percentage)+"%",1)])]))),128))])]),_:1})]),_:1}),d(t,{span:8},{default:i(()=>[d(q,{class:"stats-card"},{header:i(()=>l[20]||(l[20]=[n("span",null,"🌍 地区分布",-1)])),default:i(()=>[n("div",Qa,[(o(!0),s(p,null,m($.value,e=>(o(),s("div",{key:e.name,class:"region-item"},[n("div",Ka,[n("span",Za,D(e.name),1),n("span",Xa,D(e.count),1)]),n("div",et,[n("div",{class:"region-fill",style:fe({width:e.percentage+"%"})},null,4)]),n("span",lt,D(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1})]),_:1}),d(q,{class:"funnel-card"},{header:i(()=>l[21]||(l[21]=[n("span",null,"🎯 转化漏斗分析",-1)])),default:i(()=>[n("div",at,[n("div",tt,[(o(!0),s(p,null,m(Y.value,(e,l)=>(o(),s("div",{key:l,class:"funnel-step"},[n("div",{class:"step-bar",style:fe({width:e.percentage+"%"})},[n("div",st,[n("span",ot,D(e.name),1),n("span",nt,D(e.count),1)])],4),n("div",dt,D(e.percentage)+"%",1),l<Y.value.length-1?(o(),s("div",it," 流失: "+D(Y.value[l].count-Y.value[l+1].count),1)):c("",!0)]))),128))])])]),_:1}),d(q,{class:"heatmap-card"},{header:i(()=>[n("div",ut,[l[23]||(l[23]=n("span",null,"🔥 页面热力图",-1)),d(h,{type:"primary",size:"small",onClick:W},{default:i(()=>[d(a,null,{default:i(()=>[d(r(C))]),_:1}),l[22]||(l[22]=u(" 生成热力图 ",-1))]),_:1,__:[22]})])]),default:i(()=>[n("div",rt,[n("div",ct,[d(a,null,{default:i(()=>[d(r(B))]),_:1}),l[24]||(l[24]=n("p",null,'点击"生成热力图"查看用户行为热力图',-1))])])]),_:1}),d(q,{class:"records-card"},{header:i(()=>l[25]||(l[25]=[n("span",null,"📋 实时访问记录",-1)])),default:i(()=>[d(ee,{data:T.value,style:{width:"100%"},"max-height":"300"},{default:i(()=>[d(Z,{prop:"ip",label:"IP地址",width:"120"}),d(Z,{prop:"location",label:"地区",width:"100"}),d(Z,{prop:"device",label:"设备",width:"80"}),d(Z,{prop:"browser",label:"浏览器",width:"100"}),d(Z,{prop:"source",label:"来源",width:"120"}),d(Z,{prop:"stay_time",label:"停留时间",width:"100"},{default:i(({row:e})=>[u(D(e.stay_time)+"s ",1)]),_:1}),d(Z,{prop:"converted",label:"是否转化",width:"100"},{default:i(({row:e})=>[d(X,{type:e.converted?"success":"info",size:"small"},{default:i(()=>[u(D(e.converted?"已转化":"未转化"),1)]),_:2},1032,["type"])]),_:1}),d(Z,{prop:"visit_time",label:"访问时间",width:"160"},{default:i(({row:e})=>{return[u(D((l=e.visit_time,l?new Date(l).toLocaleString("zh-CN"):"")),1)];var l}),_:1})]),_:1},8,["data"])]),_:1})])),[[ae,b.value]])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-3aece70d"]]),vt={class:"landing-pages-container"},_t={class:"page-header"},gt={class:"page-actions"},ft={class:"stat-card primary"},ht={class:"stat-icon"},yt={class:"stat-content"},bt={class:"stat-number"},Vt={class:"stat-trend up"},kt={class:"stat-card success"},wt={class:"stat-icon"},Ct={class:"stat-content"},xt={class:"stat-number"},Ut={class:"stat-trend up"},zt={class:"stat-card warning"},It={class:"stat-icon"},St={class:"stat-content"},jt={class:"stat-number"},Dt={class:"stat-trend up"},$t={class:"stat-card danger"},Yt={class:"stat-icon"},Tt={class:"stat-content"},At={class:"stat-number"},Ot={class:"stat-trend down"},Et={class:"filter-container"},Lt={class:"card-header"},qt={class:"header-actions"},Mt={key:0,class:"grid-view"},Bt={class:"landing-page-card"},Wt={class:"card-preview"},Rt={class:"preview-image"},Ft=["src","alt"],Pt={class:"preview-overlay"},Nt={class:"status-badge"},Gt={class:"card-content"},Ht={class:"page-info"},Jt={class:"page-name"},Qt={class:"page-desc"},Kt={class:"page-stats"},Zt={class:"stat-item"},Xt={class:"stat-value"},es={class:"stat-item"},ls={class:"stat-value"},as={class:"card-actions"},ts={key:1,class:"list-view"},ss={class:"page-info-cell"},os={class:"page-thumbnail"},ns=["src","alt"],ds={class:"page-details"},is={class:"page-name"},us={class:"page-url"},rs={class:"page-template"},cs={class:"visit-stats"},ps={class:"total-visits"},ms={class:"today-visits"},vs={class:"conversion-stats"},_s={class:"conversions"},gs={class:"conversion-rate"},fs={class:"pagination-container"},hs=e({__name:"LandingPages",setup(e){const a=_([]),t=_(0),f=_(!0),h=_("grid"),y=_(!1),V=_(!1),C=_(!1),z=_({}),I=_(null),$=_([]),Y=_({total_pages:0,total_visits:0,avg_conversion_rate:0,avg_stay_time:0}),T=l({page:1,limit:12,keyword:"",status:"",template_id:"",date_range:[]}),E=async()=>{f.value=!0;try{const e={...T};e.date_range&&2===e.date_range.length&&(e.start_date=e.date_range[0],e.end_date=e.date_range[1],delete e.date_range);const{data:l}=await je.getList(e);a.value=l.list||B,t.value=l.total||B.length}catch(e){console.error("获取落地页列表失败:",e),a.value=B,t.value=B.length}finally{f.value=!1}},q=async()=>{try{const{data:e}=await je.getStats();Y.value=e}catch(e){console.error("获取统计数据失败:",e),Y.value={total_pages:24,total_visits:156789,avg_conversion_rate:12.5,avg_stay_time:145}}},B=[{id:1,name:"产品推广落地页",description:"高转化率的产品展示页面",url:"https://example.com/product-landing",status:"published",template_id:1,visit_count:12580,today_visits:156,conversions:1258,conversion_rate:10,preview_image:"/placeholder.svg?height=200&width=300",created_at:"2024-01-15 10:30:00"},{id:2,name:"活动报名页面",description:"在线活动报名收集页面",url:"https://example.com/event-signup",status:"published",template_id:4,visit_count:8960,today_visits:89,conversions:896,conversion_rate:10,preview_image:"/placeholder.svg?height=200&width=300",created_at:"2024-01-14 15:20:00"},{id:3,name:"营销活动页面",description:"限时优惠活动推广页面",url:"https://example.com/promotion",status:"draft",template_id:3,visit_count:0,today_visits:0,conversions:0,conversion_rate:0,preview_image:"/placeholder.svg?height=200&width=300",created_at:"2024-01-13 09:15:00"}],R=()=>{T.page=1,E()},F=()=>{z.value={},y.value=!0},P=()=>{V.value=!0},J=()=>{ae.info("模板管理功能开发中...")},Q=e=>{z.value={...e},y.value=!0},K=e=>{window.open(e.url||"#","_blank")},Z=e=>{I.value=e.id,C.value=!0},X=async e=>{try{await Se.confirm(`确定要复制落地页 "${e.name}" 吗？`,"确认复制",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),await je.duplicate(e.id,{name:`${e.name} - 副本`}),ae.success("复制成功"),E()}catch(l){"cancel"!==l&&ae.error("复制失败")}},ee=e=>{const[l,t]=e.split("-"),s=parseInt(t),o=a.value.find(e=>e.id===s);switch(l){case"analytics":Z(o);break;case"copy":X(o);break;case"publish":le(s,"published");break;case"offline":le(s,"offline");break;case"delete":se(s)}},le=async(e,l)=>{try{const a={published:"发布",offline:"下线"}[l];await Se.confirm(`确定要${a}这个落地页吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await je.update(e,{status:l}),ae.success(`${a}成功`),E()}catch(a){"cancel"!==a&&ae.error("操作失败")}},se=async e=>{try{await Se.confirm("确定要删除这个落地页吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await je.delete(e),ae.success("删除成功"),E()}catch(l){"cancel"!==l&&ae.error("删除失败")}},oe=()=>{ae.success("导出功能开发中...")},ce=e=>{$.value=e},ge=e=>{T.limit=e,E()},fe=e=>{T.page=e,E()},he=()=>{E(),q()},Ve=e=>{z.value={template_id:e.id},y.value=!0},De=e=>e>=1e4?(e/1e4).toFixed(1)+"W":e.toString(),Ye=e=>({published:"success",draft:"warning",offline:"info"}[e]||"info"),Te=e=>({published:"已发布",draft:"草稿",offline:"已下线"}[e]||"未知");return b(()=>{E(),q()}),(e,l)=>{const _=w,b=x,$=ie,E=de,q=U,B=S,le=j,ae=me,se=_e,Se=H,je=G,Ae=te,Oe=ze,Ee=Ue,Le=xe,qe=be,Me=ye,Be=Ie,We=ne;return o(),s("div",vt,[n("div",_t,[l[13]||(l[13]=n("div",{class:"page-title"},[n("h1",null,"🎨 落地页管理"),n("p",{class:"page-desc"},"创建和管理高转化率的推广落地页面")],-1)),n("div",gt,[d(b,{type:"info",onClick:J},{default:i(()=>[d(_,null,{default:i(()=>[d(r(L))]),_:1}),l[10]||(l[10]=u(" 模板管理 ",-1))]),_:1,__:[10]}),d(b,{type:"success",onClick:P},{default:i(()=>[d(_,null,{default:i(()=>[d(r(L))]),_:1}),l[11]||(l[11]=u(" 从模板创建 ",-1))]),_:1,__:[11]}),d(b,{type:"primary",onClick:F},{default:i(()=>[d(_,null,{default:i(()=>[d(r(N))]),_:1}),l[12]||(l[12]=u(" 创建落地页 ",-1))]),_:1,__:[12]})])]),d(E,{gutter:20,class:"stats-row"},{default:i(()=>[d($,{span:6},{default:i(()=>[n("div",ft,[n("div",ht,[d(_,null,{default:i(()=>[d(r(L))]),_:1})]),n("div",yt,[n("div",bt,D(Y.value.total_pages),1),l[15]||(l[15]=n("div",{class:"stat-label"},"总落地页",-1)),n("div",Vt,[d(_,null,{default:i(()=>[d(r(ue))]),_:1}),l[14]||(l[14]=n("span",null,"+12 较上月",-1))])])])]),_:1}),d($,{span:6},{default:i(()=>[n("div",kt,[n("div",wt,[d(_,null,{default:i(()=>[d(r(O))]),_:1})]),n("div",Ct,[n("div",xt,D(De(Y.value.total_visits)),1),l[17]||(l[17]=n("div",{class:"stat-label"},"总访问量",-1)),n("div",Ut,[d(_,null,{default:i(()=>[d(r(ue))]),_:1}),l[16]||(l[16]=n("span",null,"+28.5% 较上月",-1))])])])]),_:1}),d($,{span:6},{default:i(()=>[n("div",zt,[n("div",It,[d(_,null,{default:i(()=>[d(r(re))]),_:1})]),n("div",St,[n("div",jt,D(Y.value.avg_conversion_rate)+"%",1),l[19]||(l[19]=n("div",{class:"stat-label"},"平均转化率",-1)),n("div",Dt,[d(_,null,{default:i(()=>[d(r(ue))]),_:1}),l[18]||(l[18]=n("span",null,"+3.2% 较上月",-1))])])])]),_:1}),d($,{span:6},{default:i(()=>[n("div",$t,[n("div",Yt,[d(_,null,{default:i(()=>[d(r(ke))]),_:1})]),n("div",Tt,[n("div",At,D(Y.value.avg_stay_time)+"s",1),l[21]||(l[21]=n("div",{class:"stat-label"},"平均停留时间",-1)),n("div",Ot,[d(_,null,{default:i(()=>[d(r(pe))]),_:1}),l[20]||(l[20]=n("span",null,"-5.1% 较上月",-1))])])])]),_:1})]),_:1}),d(se,{class:"filter-card"},{default:i(()=>[n("div",Et,[d(q,{modelValue:T.keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>T.keyword=e),placeholder:"搜索落地页名称、描述",style:{width:"250px"},class:"filter-item",onKeyup:k(R,["enter"]),clearable:""},{prefix:i(()=>[d(_,null,{default:i(()=>[d(r(we))]),_:1})]),_:1},8,["modelValue"]),d(le,{modelValue:T.status,"onUpdate:modelValue":l[1]||(l[1]=e=>T.status=e),placeholder:"状态筛选",clearable:"",style:{width:"120px"},class:"filter-item"},{default:i(()=>[d(B,{label:"全部",value:""}),d(B,{label:"已发布",value:"published"}),d(B,{label:"草稿",value:"draft"}),d(B,{label:"已下线",value:"offline"})]),_:1},8,["modelValue"]),d(le,{modelValue:T.template_id,"onUpdate:modelValue":l[2]||(l[2]=e=>T.template_id=e),placeholder:"模板筛选",clearable:"",style:{width:"150px"},class:"filter-item"},{default:i(()=>[d(B,{label:"全部模板",value:""}),d(B,{label:"营销模板",value:"1"}),d(B,{label:"产品展示",value:"2"}),d(B,{label:"活动推广",value:"3"}),d(B,{label:"表单收集",value:"4"})]),_:1},8,["modelValue"]),d(ae,{modelValue:T.date_range,"onUpdate:modelValue":l[3]||(l[3]=e=>T.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",class:"filter-item"},null,8,["modelValue"]),d(b,{type:"primary",onClick:R},{default:i(()=>[d(_,null,{default:i(()=>[d(r(we))]),_:1}),l[22]||(l[22]=u(" 搜索 ",-1))]),_:1,__:[22]}),d(b,{type:"success",onClick:oe},{default:i(()=>[d(_,null,{default:i(()=>[d(r(ve))]),_:1}),l[23]||(l[23]=u(" 导出数据 ",-1))]),_:1,__:[23]})])]),_:1}),d(se,{class:"list-card"},{header:i(()=>[n("div",Lt,[l[26]||(l[26]=n("h3",null,"落地页列表",-1)),n("div",qt,[d(je,{modelValue:h.value,"onUpdate:modelValue":l[4]||(l[4]=e=>h.value=e),size:"small"},{default:i(()=>[d(Se,{value:"grid"},{default:i(()=>[d(_,null,{default:i(()=>[d(r(M))]),_:1}),l[24]||(l[24]=u(" 卡片视图 ",-1))]),_:1,__:[24]}),d(Se,{value:"list"},{default:i(()=>[d(_,null,{default:i(()=>[d(r(W))]),_:1}),l[25]||(l[25]=u(" 列表视图 ",-1))]),_:1,__:[25]})]),_:1},8,["modelValue"])])])]),default:i(()=>["grid"===h.value?(o(),s("div",Mt,[d(E,{gutter:20},{default:i(()=>[(o(!0),s(p,null,m(a.value,e=>(o(),v($,{span:8,key:e.id},{default:i(()=>[n("div",Bt,[n("div",Wt,[n("div",Rt,[n("img",{src:e.preview_image||"/placeholder.svg?height=200&width=300",alt:e.name},null,8,Ft),n("div",Pt,[d(b,{type:"primary",size:"small",onClick:l=>K(e)},{default:i(()=>[d(_,null,{default:i(()=>[d(r(O))]),_:1}),l[27]||(l[27]=u(" 预览 ",-1))]),_:2,__:[27]},1032,["onClick"]),d(b,{type:"success",size:"small",onClick:l=>Q(e)},{default:i(()=>[d(_,null,{default:i(()=>[d(r(A))]),_:1}),l[28]||(l[28]=u(" 编辑 ",-1))]),_:2,__:[28]},1032,["onClick"])])]),n("div",Nt,[d(Ae,{type:Ye(e.status),size:"small"},{default:i(()=>[u(D(Te(e.status)),1)]),_:2},1032,["type"])])]),n("div",Gt,[n("div",Ht,[n("h4",Jt,D(e.name),1),n("p",Qt,D(e.description||"暂无描述"),1)]),n("div",Kt,[n("div",Zt,[l[29]||(l[29]=n("span",{class:"stat-label"},"访问量",-1)),n("span",Xt,D(De(e.visit_count||0)),1)]),n("div",es,[l[30]||(l[30]=n("span",{class:"stat-label"},"转化率",-1)),n("span",ls,D(e.conversion_rate||0)+"%",1)])]),n("div",as,[d(b,{type:"text",size:"small",onClick:l=>Z(e)},{default:i(()=>[d(_,null,{default:i(()=>[d(r(re))]),_:1}),l[31]||(l[31]=u(" 数据分析 ",-1))]),_:2,__:[31]},1032,["onClick"]),d(b,{type:"text",size:"small",onClick:l=>X(e)},{default:i(()=>[d(_,null,{default:i(()=>[d(r(Ce))]),_:1}),l[32]||(l[32]=u(" 复制 ",-1))]),_:2,__:[32]},1032,["onClick"]),d(Le,{onCommand:ee},{dropdown:i(()=>[d(Ee,null,{default:i(()=>["draft"===e.status?(o(),v(Oe,{key:0,command:`publish-${e.id}`},{default:i(()=>l[34]||(l[34]=[u(" 发布 ",-1)])),_:2,__:[34]},1032,["command"])):c("",!0),"published"===e.status?(o(),v(Oe,{key:1,command:`offline-${e.id}`},{default:i(()=>l[35]||(l[35]=[u(" 下线 ",-1)])),_:2,__:[35]},1032,["command"])):c("",!0),d(Oe,{command:`delete-${e.id}`,divided:""},{default:i(()=>l[36]||(l[36]=[u(" 删除 ",-1)])),_:2,__:[36]},1032,["command"])]),_:2},1024)]),default:i(()=>[d(b,{type:"text",size:"small"},{default:i(()=>[l[33]||(l[33]=u(" 更多",-1)),d(_,{class:"el-icon--right"},{default:i(()=>[d(r(pe))]),_:1})]),_:1,__:[33]})]),_:2},1024)])])])]),_:2},1024))),128))]),_:1})])):(o(),s("div",ts,[g((o(),v(Me,{data:a.value,style:{width:"100%"},onSelectionChange:ce},{default:i(()=>[d(qe,{type:"selection",width:"55"}),d(qe,{label:"落地页信息",width:"300"},{default:i(({row:e})=>{return[n("div",ss,[n("div",os,[n("img",{src:e.preview_image||"/placeholder.svg?height=60&width=80",alt:e.name},null,8,ns)]),n("div",ds,[n("div",is,D(e.name),1),n("div",us,D(e.url||"未设置URL"),1),n("div",rs,"模板: "+D((l=e.template_id,{1:"营销模板",2:"产品展示",3:"活动推广",4:"表单收集"}[l]||"未知模板")),1)])])];var l}),_:1}),d(qe,{label:"状态",width:"100"},{default:i(({row:e})=>[d(Ae,{type:Ye(e.status)},{default:i(()=>[u(D(Te(e.status)),1)]),_:2},1032,["type"])]),_:1}),d(qe,{label:"访问统计",width:"120"},{default:i(({row:e})=>[n("div",cs,[n("div",ps,D(De(e.visit_count||0))+" 次",1),n("div",ms,"今日: "+D(e.today_visits||0),1)])]),_:1}),d(qe,{label:"转化数据",width:"120"},{default:i(({row:e})=>[n("div",vs,[n("div",_s,D(e.conversions||0)+" 转化",1),n("div",gs,D(e.conversion_rate||0)+"%",1)])]),_:1}),d(qe,{label:"创建时间",width:"160"},{default:i(({row:e})=>[u(D(r($e)(e.created_at)),1)]),_:1}),d(qe,{label:"操作",width:"200",fixed:"right"},{default:i(({row:e})=>[d(b,{type:"primary",size:"small",onClick:l=>Q(e)},{default:i(()=>l[37]||(l[37]=[u(" 编辑 ",-1)])),_:2,__:[37]},1032,["onClick"]),d(b,{type:"success",size:"small",onClick:l=>K(e)},{default:i(()=>l[38]||(l[38]=[u(" 预览 ",-1)])),_:2,__:[38]},1032,["onClick"]),d(Le,{onCommand:ee},{dropdown:i(()=>[d(Ee,null,{default:i(()=>[d(Oe,{command:`analytics-${e.id}`},{default:i(()=>l[40]||(l[40]=[u("数据分析",-1)])),_:2,__:[40]},1032,["command"]),d(Oe,{command:`copy-${e.id}`},{default:i(()=>l[41]||(l[41]=[u("复制页面",-1)])),_:2,__:[41]},1032,["command"]),"draft"===e.status?(o(),v(Oe,{key:0,command:`publish-${e.id}`},{default:i(()=>l[42]||(l[42]=[u("发布",-1)])),_:2,__:[42]},1032,["command"])):c("",!0),"published"===e.status?(o(),v(Oe,{key:1,command:`offline-${e.id}`},{default:i(()=>l[43]||(l[43]=[u("下线",-1)])),_:2,__:[43]},1032,["command"])):c("",!0),d(Oe,{command:`delete-${e.id}`,divided:""},{default:i(()=>l[44]||(l[44]=[u("删除",-1)])),_:2,__:[44]},1032,["command"])]),_:2},1024)]),default:i(()=>[d(b,{type:"info",size:"small"},{default:i(()=>[l[39]||(l[39]=u(" 更多",-1)),d(_,{class:"el-icon--right"},{default:i(()=>[d(r(pe))]),_:1})]),_:1,__:[39]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[We,f.value]])])),n("div",fs,[d(Be,{"current-page":T.page,"onUpdate:currentPage":l[5]||(l[5]=e=>T.page=e),"page-size":T.limit,"onUpdate:pageSize":l[6]||(l[6]=e=>T.limit=e),"page-sizes":[12,24,36,48],total:t.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ge,onCurrentChange:fe},null,8,["current-page","page-size","total"])])]),_:1}),d(ql,{modelValue:y.value,"onUpdate:modelValue":l[7]||(l[7]=e=>y.value=e),"page-data":z.value,onSuccess:he},null,8,["modelValue","page-data"]),d(na,{modelValue:V.value,"onUpdate:modelValue":l[8]||(l[8]=e=>V.value=e),onSelect:Ve},null,8,["modelValue"]),d(mt,{modelValue:C.value,"onUpdate:modelValue":l[9]||(l[9]=e=>C.value=e),"page-id":I.value},null,8,["modelValue","page-id"])])}}},[["__scopeId","data-v-b878aad4"]]);export{hs as default};
