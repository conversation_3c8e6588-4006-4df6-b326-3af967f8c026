<template>
  <div class="app-container">
    <!-- 数据导出概览 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <span>📊 数据导出概览</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon export-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exportStats.total_exports }}</div>
              <div class="stat-label">总导出次数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon today-icon">
              <i class="el-icon-calendar-today"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exportStats.today_exports }}</div>
              <div class="stat-label">今日导出</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon size-icon">
              <i class="el-icon-files"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exportStats.total_size }}</div>
              <div class="stat-label">总文件大小</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon pending-icon">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exportStats.pending_exports }}</div>
              <div class="stat-label">待处理任务</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 快速导出 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>⚡ 快速导出</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="template in exportTemplates" :key="template.key">
          <div class="export-template">
            <div class="template-header">
              <div class="template-icon">
                <i :class="template.icon"></i>
              </div>
              <div class="template-info">
                <div class="template-name">{{ template.name }}</div>
                <div class="template-desc">{{ template.description }}</div>
              </div>
            </div>
            
            <div class="template-stats">
              <div class="stat-row">
                <span class="stat-label">数据量:</span>
                <span class="stat-value">{{ template.count }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">预计大小:</span>
                <span class="stat-value">{{ template.size }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">最后更新:</span>
                <span class="stat-value">{{ template.last_updated }}</span>
              </div>
            </div>
            
            <div class="template-actions">
              <el-button type="primary" @click="quickExport(template)" :loading="template.loading">
                立即导出
              </el-button>
              <el-button type="info" @click="scheduleExport(template)">
                定时导出
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 自定义导出 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>🎯 自定义导出</span>
        </div>
      </template>
      
      <el-form :model="customExportForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="导出数据类型">
              <el-select v-model="customExportForm.data_type" placeholder="请选择数据类型" @change="onDataTypeChange">
                <el-option label="用户数据" value="users" />
                <el-option label="订单数据" value="orders" />
                <el-option label="财务数据" value="finance" />
                <el-option label="分销数据" value="distribution" />
                <el-option label="社群数据" value="community" />
                <el-option label="防红数据" value="anti-block" />
                <el-option label="系统日志" value="logs" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="导出格式">
              <el-radio-group v-model="customExportForm.format">
                <el-radio label="excel">Excel (.xlsx)</el-radio>
                <el-radio label="csv">CSV (.csv)</el-radio>
                <el-radio label="json">JSON (.json)</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="customExportForm.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            
            <el-form-item label="数据量限制">
              <el-input-number v-model="customExportForm.limit" :min="1" :max="100000" />
              <span class="form-tip">建议单次导出不超过50000条数据</span>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="导出字段">
              <el-checkbox-group v-model="customExportForm.fields">
                <el-checkbox v-for="field in availableFields" :key="field.key" :label="field.key">
                  {{ field.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="筛选条件">
              <el-input type="textarea" v-model="customExportForm.filters" placeholder="请输入筛选条件（JSON格式）" />
            </el-form-item>
            
            <el-form-item label="排序方式">
              <el-select v-model="customExportForm.sort_by" placeholder="选择排序字段">
                <el-option label="创建时间" value="created_at" />
                <el-option label="更新时间" value="updated_at" />
                <el-option label="ID" value="id" />
              </el-select>
              <el-select v-model="customExportForm.sort_order" placeholder="排序方式" style="margin-left: 10px">
                <el-option label="升序" value="asc" />
                <el-option label="降序" value="desc" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="导出名称">
              <el-input v-model="customExportForm.name" placeholder="请输入导出文件名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="startCustomExport" :loading="customExporting">
            开始导出
          </el-button>
          <el-button @click="resetCustomForm">重置</el-button>
          <el-button type="info" @click="previewData">预览数据</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 导出任务列表 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>📋 导出任务列表</span>
          <el-button type="primary" @click="refreshTasks">刷新</el-button>
        </div>
      </template>
      
      <el-table :data="exportTasks" style="width: 100%">
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="name" label="任务名称" />
        <el-table-column prop="data_type" label="数据类型" width="100" />
        <el-table-column prop="format" label="格式" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.progress" 
              :status="scope.row.status === 'failed' ? 'exception' : ''"
            />
          </template>
        </el-table-column>
        <el-table-column prop="file_size" label="文件大小" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="downloadFile(scope.row)"
              :disabled="scope.row.status !== 'completed'"
            >
              下载
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="retryTask(scope.row)"
              :disabled="scope.row.status !== 'failed'"
            >
              重试
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteTask(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-pagination">
        <el-pagination
          v-model:current-page="taskPage"
          v-model:page-size="taskPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="taskTotal"
          @size-change="handleTaskSizeChange"
          @current-change="handleTaskCurrentChange"
        />
      </div>
    </el-card>

    <!-- 定时导出设置对话框 -->
    <el-dialog title="定时导出设置" v-model="scheduleDialog.visible" width="600px">
      <el-form :model="scheduleForm" label-width="100px">
        <el-form-item label="导出频率">
          <el-select v-model="scheduleForm.frequency" placeholder="请选择导出频率">
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="执行时间">
          <el-time-picker v-model="scheduleForm.time" placeholder="选择时间" />
        </el-form-item>
        
        <el-form-item label="邮件通知">
          <el-input v-model="scheduleForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        
        <el-form-item label="是否启用">
          <el-switch v-model="scheduleForm.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="scheduleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveSchedule">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据预览对话框 -->
    <el-dialog
      title="数据预览"
      v-model="previewDialog.visible"
      width="80%"
      top="5vh"
    >
      <div v-if="previewDialog.data.length > 0">
        <el-table :data="previewDialog.data" stripe>
          <el-table-column 
            v-for="column in previewDialog.columns" 
            :key="column.key"
            :prop="column.key"
            :label="column.label"
            show-overflow-tooltip
          />
        </el-table>
        <div style="margin-top: 10px; color: #666; font-size: 14px;">
          <i class="el-icon-info"></i> 预览数据仅显示前10条记录
        </div>
      </div>
      <div v-else style="text-align: center; padding: 40px;">
        <i class="el-icon-document" style="font-size: 48px; color: #ddd;"></i>
        <p style="color: #999; margin-top: 10px;">暂无数据</p>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialog.visible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { previewExportData } from '@/api/export'

const exportStats = reactive({
  total_exports: 1245,
  today_exports: 23,
  total_size: '2.5GB',
  pending_exports: 3
})

const exportTemplates = ref([
  {
    key: 'users',
    name: '用户数据',
    description: '导出所有用户的基本信息',
    icon: 'el-icon-user',
    count: '12,345',
    size: '2.5MB',
    last_updated: '2024-01-01 10:00',
    loading: false
  },
  {
    key: 'orders',
    name: '订单数据',
    description: '导出所有订单记录',
    icon: 'el-icon-goods',
    count: '8,765',
    size: '5.2MB',
    last_updated: '2024-01-01 09:30',
    loading: false
  },
  {
    key: 'finance',
    name: '财务数据',
    description: '导出收支明细记录',
    icon: 'el-icon-money',
    count: '3,456',
    size: '1.8MB',
    last_updated: '2024-01-01 08:45',
    loading: false
  }
])

const customExportForm = reactive({
  data_type: '',
  format: 'excel',
  date_range: [],
  limit: 10000,
  fields: [],
  filters: '',
  sort_by: 'created_at',
  sort_order: 'desc',
  name: ''
})

const availableFields = ref([])

const exportTasks = ref([
  {
    id: 1,
    name: '用户数据导出',
    data_type: 'users',
    format: 'excel',
    status: 'completed',
    progress: 100,
    file_size: '2.5MB',
    created_at: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '订单数据导出',
    data_type: 'orders',
    format: 'csv',
    status: 'processing',
    progress: 65,
    file_size: '-',
    created_at: '2024-01-01 10:30:00'
  },
  {
    id: 3,
    name: '财务数据导出',
    data_type: 'finance',
    format: 'json',
    status: 'failed',
    progress: 0,
    file_size: '-',
    created_at: '2024-01-01 09:15:00'
  }
])

const scheduleDialog = reactive({
  visible: false,
  template: null
})

const previewDialog = reactive({
  visible: false,
  data: [],
  columns: []
})

const scheduleForm = reactive({
  frequency: 'daily',
  time: null,
  email: '',
  enabled: true
})

const customExporting = ref(false)
const taskPage = ref(1)
const taskPageSize = ref(10)
const taskTotal = ref(0)

// 数据类型变更时更新可用字段
const onDataTypeChange = (type) => {
  const fieldMap = {
    users: [
      { key: 'id', label: 'ID' },
      { key: 'username', label: '用户名' },
      { key: 'email', label: '邮箱' },
      { key: 'phone', label: '手机号' },
      { key: 'created_at', label: '创建时间' }
    ],
    orders: [
      { key: 'id', label: '订单ID' },
      { key: 'user_id', label: '用户ID' },
      { key: 'amount', label: '金额' },
      { key: 'status', label: '状态' },
      { key: 'created_at', label: '创建时间' }
    ],
    finance: [
      { key: 'id', label: 'ID' },
      { key: 'type', label: '类型' },
      { key: 'amount', label: '金额' },
      { key: 'balance', label: '余额' },
      { key: 'created_at', label: '创建时间' }
    ]
  }
  
  availableFields.value = fieldMap[type] || []
  customExportForm.fields = availableFields.value.map(field => field.key)
}

// 快速导出
const quickExport = async (template) => {
  template.loading = true
  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success(`${template.name} 导出成功`)
  } catch (error) {
    ElMessage.error(`${template.name} 导出失败`)
  } finally {
    template.loading = false
  }
}

// 定时导出
const scheduleExport = (template) => {
  scheduleDialog.template = template
  scheduleDialog.visible = true
}

// 自定义导出
const startCustomExport = async () => {
  if (!customExportForm.data_type) {
    ElMessage.warning('请选择数据类型')
    return
  }
  
  customExporting.value = true
  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('导出任务已创建')
    refreshTasks()
  } catch (error) {
    ElMessage.error('导出任务创建失败')
  } finally {
    customExporting.value = false
  }
}

// 重置表单
const resetCustomForm = () => {
  Object.assign(customExportForm, {
    data_type: '',
    format: 'excel',
    date_range: [],
    limit: 10000,
    fields: [],
    filters: '',
    sort_by: 'created_at',
    sort_order: 'desc',
    name: ''
  })
  availableFields.value = []
}

// 预览数据
const previewData = async () => {
  if (!customExportForm.data_type) {
    ElMessage.warning('请选择数据类型')
    return
  }
  
  try {
    ElMessage.info('正在获取预览数据...')
    
    // 构建预览参数
    const previewParams = {
      data_type: customExportForm.data_type,
      fields: customExportForm.fields,
      filters: customExportForm.filters,
      date_range: customExportForm.date_range,
      limit: 10 // 预览只显示前10条
    }
    
    // 调用预览API
    const response = await previewExportData(previewParams)
    
    if (response.data.success) {
      // 显示预览对话框
      previewDialog.visible = true
      previewDialog.data = response.data.data
      previewDialog.columns = response.data.columns
      ElMessage.success('预览数据获取成功')
    } else {
      ElMessage.error('预览数据获取失败')
    }
  } catch (error) {
    ElMessage.error('预览数据获取失败：' + error.message)
  }
}

// 状态相关
const getStatusType = (status) => {
  const types = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

// 任务操作
const downloadFile = (task) => {
  ElMessage.success(`开始下载 ${task.name}`)
}

const retryTask = (task) => {
  ElMessage.info(`重试任务 ${task.name}`)
  task.status = 'pending'
  task.progress = 0
}

const deleteTask = (task) => {
  ElMessageBox.confirm(`确定要删除任务 ${task.name} 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    refreshTasks()
  })
}

// 刷新任务列表
const refreshTasks = () => {
  ElMessage.success('任务列表已刷新')
}

// 分页处理
const handleTaskSizeChange = (size) => {
  taskPageSize.value = size
  refreshTasks()
}

const handleTaskCurrentChange = (page) => {
  taskPage.value = page
  refreshTasks()
}

// 保存定时任务
const saveSchedule = () => {
  ElMessage.success('定时导出设置已保存')
  scheduleDialog.visible = false
}

onMounted(() => {
  taskTotal.value = exportTasks.value.length
})
</script>

<style scoped>
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-card .el-card__header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.overview-card .card-header span {
  color: white;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.export-icon { background: rgba(255, 255, 255, 0.2); }
.today-icon { background: rgba(255, 255, 255, 0.2); }
.size-icon { background: rgba(255, 255, 255, 0.2); }
.pending-icon { background: rgba(255, 255, 255, 0.2); }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.export-template {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.export-template:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.template-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.template-desc {
  font-size: 14px;
  color: #909399;
}

.template-stats {
  margin-bottom: 15px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.template-actions {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.table-pagination {
  margin-top: 20px;
  text-align: center;
}
</style> 