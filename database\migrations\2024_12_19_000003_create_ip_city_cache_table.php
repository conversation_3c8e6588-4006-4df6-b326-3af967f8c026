<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ip_city_cache', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address', 45)->unique()->comment('IP地址');
            $table->string('city', 50)->comment('城市名称');
            $table->string('province', 50)->nullable()->comment('省份');
            $table->string('country', 50)->default('中国')->comment('国家');
            $table->string('isp', 100)->nullable()->comment('运营商');
            $table->string('data_source', 20)->comment('数据来源');
            $table->json('raw_data')->nullable()->comment('原始数据');
            $table->integer('hit_count')->default(1)->comment('命中次数');
            $table->timestamp('last_hit_at')->useCurrent()->comment('最后命中时间');
            $table->timestamps();

            // 索引
            $table->index(['city', 'created_at'], 'idx_city_time');
            $table->index(['province', 'created_at'], 'idx_province_time');
            $table->index('data_source', 'idx_data_source');
            $table->index('last_hit_at', 'idx_last_hit');
            $table->index('hit_count', 'idx_hit_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ip_city_cache');
    }
};