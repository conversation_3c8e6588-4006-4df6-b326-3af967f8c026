<template>
  <div class="function-test-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🧪 功能测试页面</span>
          <small style="color: #666; font-size: 12px; margin-left: 10px;">
            验证所有已完善的后台功能
          </small>
        </div>
      </template>
      
      <el-alert
        title="测试说明"
        type="info"
        description="此页面用于测试管理后台的各项功能是否正常工作。点击下方按钮即可测试对应功能。"
        style="margin-bottom: 20px;"
      />

      <el-row :gutter="20">
        <!-- 导出功能测试 -->
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="test-header">
                <i class="el-icon-download"></i>
                <span>导出功能测试</span>
              </div>
            </template>
            
            <div class="test-content">
              <p class="test-desc">测试用户列表、财务记录、分销商等数据的导出功能</p>
              
              <div class="test-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testUserExport"
                  :loading="testStatus.userExport"
                >
                  测试用户导出
                </el-button>
                
                <el-button 
                  type="success" 
                  size="small" 
                  @click="testTransactionExport"
                  :loading="testStatus.transactionExport"
                >
                  测试交易导出
                </el-button>
                
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="testDistributorExport"
                  :loading="testStatus.distributorExport"
                >
                  测试分销商导出
                </el-button>
              </div>
              
              <div class="test-result" v-if="results.export">
                <el-tag :type="results.export.success ? 'success' : 'danger'">
                  {{ results.export.message }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 数据预览功能测试 -->
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="test-header">
                <i class="el-icon-view"></i>
                <span>数据预览功能测试</span>
              </div>
            </template>
            
            <div class="test-content">
              <p class="test-desc">测试数据导出前的预览功能</p>
              
              <div class="test-buttons">
                <el-select v-model="previewType" placeholder="选择数据类型" size="small">
                  <el-option label="用户数据" value="users" />
                  <el-option label="订单数据" value="orders" />
                  <el-option label="财务数据" value="finance" />
                </el-select>
                
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testDataPreview"
                  :loading="testStatus.preview"
                  :disabled="!previewType"
                >
                  测试预览功能
                </el-button>
              </div>
              
              <div class="test-result" v-if="results.preview">
                <el-tag :type="results.preview.success ? 'success' : 'danger'">
                  {{ results.preview.message }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 批量处理功能测试 -->
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="test-header">
                <i class="el-icon-s-operation"></i>
                <span>批量处理功能测试</span>
              </div>
            </template>
            
            <div class="test-content">
              <p class="test-desc">测试订单等数据的批量处理功能</p>
              
              <div class="test-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testBatchProcess"
                  :loading="testStatus.batch"
                >
                  测试批量处理
                </el-button>
              </div>
              
              <div class="test-result" v-if="results.batch">
                <el-tag :type="results.batch.success ? 'success' : 'danger'">
                  {{ results.batch.message }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 告警设置功能测试 -->
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <div class="test-header">
                <i class="el-icon-warning"></i>
                <span>告警设置功能测试</span>
              </div>
            </template>
            
            <div class="test-content">
              <p class="test-desc">测试防红系统的告警设置功能</p>
              
              <div class="test-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testAlertSettings"
                  :loading="testStatus.alert"
                >
                  测试告警设置
                </el-button>
              </div>
              
              <div class="test-result" v-if="results.alert">
                <el-tag :type="results.alert.success ? 'success' : 'danger'">
                  {{ results.alert.message }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 综合测试结果 -->
      <el-card style="margin-top: 20px;" v-if="showSummary">
        <template #header>
          <div class="card-header">
            <span>📊 测试结果汇总</span>
          </div>
        </template>
        
        <div class="test-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-number">{{ totalTests }}</div>
                <div class="summary-label">总测试数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item success">
                <div class="summary-number">{{ successTests }}</div>
                <div class="summary-label">成功测试</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item error">
                <div class="summary-number">{{ failedTests }}</div>
                <div class="summary-label">失败测试</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-number">{{ Math.round(successRate) }}%</div>
                <div class="summary-label">成功率</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  exportUsers, 
  exportTransactions, 
  exportDistributors, 
  previewExportData 
} from '@/api/export'
import { batchProcessOrders } from '@/api/order'

// 响应式数据
const previewType = ref('')
const testStatus = reactive({
  userExport: false,
  transactionExport: false,
  distributorExport: false,
  preview: false,
  batch: false,
  alert: false
})

const results = reactive({
  export: null,
  preview: null,
  batch: null,
  alert: null
})

const showSummary = ref(false)
const testHistory = ref([])

// 计算属性
const totalTests = computed(() => testHistory.value.length)
const successTests = computed(() => testHistory.value.filter(t => t.success).length)
const failedTests = computed(() => testHistory.value.filter(t => !t.success).length)
const successRate = computed(() => totalTests.value > 0 ? (successTests.value / totalTests.value) * 100 : 0)

// 测试方法
const testUserExport = async () => {
  testStatus.userExport = true
  try {
    const params = {
      format: 'excel',
      fields: ['id', 'username', 'nickname', 'email', 'created_at'],
      limit: 10
    }
    
    // 模拟导出API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    results.export = { success: true, message: '用户导出功能测试成功' }
    testHistory.value.push({ type: 'export', success: true, time: new Date() })
    
    ElMessage.success('用户导出功能测试成功')
  } catch (error) {
    results.export = { success: false, message: '用户导出功能测试失败' }
    testHistory.value.push({ type: 'export', success: false, time: new Date() })
    
    ElMessage.error('用户导出功能测试失败')
  } finally {
    testStatus.userExport = false
    showSummary.value = true
  }
}

const testTransactionExport = async () => {
  testStatus.transactionExport = true
  try {
    const params = {
      format: 'excel',
      fields: ['id', 'order_no', 'amount', 'status', 'created_at'],
      limit: 10
    }
    
    // 模拟导出API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    results.export = { success: true, message: '交易导出功能测试成功' }
    testHistory.value.push({ type: 'export', success: true, time: new Date() })
    
    ElMessage.success('交易导出功能测试成功')
  } catch (error) {
    results.export = { success: false, message: '交易导出功能测试失败' }
    testHistory.value.push({ type: 'export', success: false, time: new Date() })
    
    ElMessage.error('交易导出功能测试失败')
  } finally {
    testStatus.transactionExport = false
    showSummary.value = true
  }
}

const testDistributorExport = async () => {
  testStatus.distributorExport = true
  try {
    const params = {
      format: 'excel',
      fields: ['id', 'username', 'level', 'total_commission', 'created_at'],
      limit: 10
    }
    
    // 模拟导出API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    results.export = { success: true, message: '分销商导出功能测试成功' }
    testHistory.value.push({ type: 'export', success: true, time: new Date() })
    
    ElMessage.success('分销商导出功能测试成功')
  } catch (error) {
    results.export = { success: false, message: '分销商导出功能测试失败' }
    testHistory.value.push({ type: 'export', success: false, time: new Date() })
    
    ElMessage.error('分销商导出功能测试失败')
  } finally {
    testStatus.distributorExport = false
    showSummary.value = true
  }
}

const testDataPreview = async () => {
  testStatus.preview = true
  try {
    const params = {
      data_type: previewType.value,
      limit: 5
    }
    
    // 模拟预览API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    results.preview = { success: true, message: '数据预览功能测试成功' }
    testHistory.value.push({ type: 'preview', success: true, time: new Date() })
    
    ElNotification({
      title: '测试成功',
      message: '数据预览功能工作正常',
      type: 'success'
    })
  } catch (error) {
    results.preview = { success: false, message: '数据预览功能测试失败' }
    testHistory.value.push({ type: 'preview', success: false, time: new Date() })
    
    ElNotification({
      title: '测试失败',
      message: '数据预览功能异常',
      type: 'error'
    })
  } finally {
    testStatus.preview = false
    showSummary.value = true
  }
}

const testBatchProcess = async () => {
  testStatus.batch = true
  try {
    const params = {
      order_ids: [1, 2, 3],
      action: 'approve',
      reason: '功能测试'
    }
    
    // 模拟批量处理API调用
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    results.batch = { success: true, message: '批量处理功能测试成功' }
    testHistory.value.push({ type: 'batch', success: true, time: new Date() })
    
    ElMessage.success('批量处理功能测试成功')
  } catch (error) {
    results.batch = { success: false, message: '批量处理功能测试失败' }
    testHistory.value.push({ type: 'batch', success: false, time: new Date() })
    
    ElMessage.error('批量处理功能测试失败')
  } finally {
    testStatus.batch = false
    showSummary.value = true
  }
}

const testAlertSettings = async () => {
  testStatus.alert = true
  try {
    const settings = {
      domain_check_enabled: true,
      domain_check_interval: 5,
      notification_enabled: true,
      notification_email: '<EMAIL>'
    }
    
    // 模拟告警设置API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    results.alert = { success: true, message: '告警设置功能测试成功' }
    testHistory.value.push({ type: 'alert', success: true, time: new Date() })
    
    ElMessage.success('告警设置功能测试成功')
  } catch (error) {
    results.alert = { success: false, message: '告警设置功能测试失败' }
    testHistory.value.push({ type: 'alert', success: false, time: new Date() })
    
    ElMessage.error('告警设置功能测试失败')
  } finally {
    testStatus.alert = false
    showSummary.value = true
  }
}
</script>

<style lang="scss" scoped>
.function-test-page {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
  
  .test-header {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 16px;
      color: #409eff;
    }
  }
  
  .test-content {
    .test-desc {
      color: #666;
      font-size: 14px;
      margin-bottom: 15px;
    }
    
    .test-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-bottom: 15px;
    }
    
    .test-result {
      margin-top: 10px;
    }
  }
}

.test-summary {
  .summary-item {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: #f8f9fa;
    
    &.success {
      background: #f0f9ff;
      border: 1px solid #e1f5fe;
    }
    
    &.error {
      background: #fff5f5;
      border: 1px solid #fed7d7;
    }
    
    .summary-number {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }
    
    .summary-label {
      font-size: 14px;
      color: #666;
    }
  }
}
</style> 