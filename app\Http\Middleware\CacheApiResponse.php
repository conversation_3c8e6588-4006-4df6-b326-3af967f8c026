<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

/**
 * API响应缓存中间件
 * 缓存GET请求的API响应，提高性能
 */
class CacheApiResponse
{
    /**
     * 需要缓存的路由
     *
     * @var array
     */
    protected $routes = [
        'api/v1/dashboard/stats' => 300, // 5分钟
        'api/v1/dashboard/charts' => 300,
        'api/v1/wechat-groups' => 300,
        'api/v1/user/profile' => 600, // 10分钟
        'api/v1/system/settings/public' => 1800, // 30分钟
        'api/v1/system/info' => 1800,
    ];

    /**
     * 处理请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 只缓存GET请求
        if (!$request->isMethod('GET')) {
            return $next($request);
        }

        // 检查是否需要缓存
        $path = $request->path();
        $ttl = $this->getCacheTTL($path);
        
        if ($ttl === null) {
            return $next($request);
        }

        // 生成缓存键
        $cacheKey = $this->generateCacheKey($request);
        
        // 检查是否有缓存
        if (Cache::has($cacheKey)) {
            $cachedResponse = Cache::get($cacheKey);
            
            // 添加缓存标记头
            $response = response()->json(
                $cachedResponse['data'],
                $cachedResponse['status']
            );
            
            $response->header('X-API-Cache', 'HIT');
            $response->header('X-API-Cache-Time', $cachedResponse['time']);
            
            return $response;
        }

        // 执行请求
        $response = $next($request);
        
        // 只缓存成功的响应
        if ($response->getStatusCode() === 200) {
            $responseData = json_decode($response->getContent(), true);
            
            // 缓存响应
            Cache::put($cacheKey, [
                'data' => $responseData,
                'status' => $response->getStatusCode(),
                'time' => now()->toIso8601String()
            ], $ttl);
            
            // 添加缓存标记头
            $response->header('X-API-Cache', 'MISS');
        }

        return $response;
    }

    /**
     * 获取缓存TTL
     *
     * @param  string  $path
     * @return int|null
     */
    protected function getCacheTTL($path)
    {
        foreach ($this->routes as $route => $ttl) {
            if (fnmatch($route, $path)) {
                return $ttl;
            }
        }
        
        return null;
    }

    /**
     * 生成缓存键
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function generateCacheKey(Request $request)
    {
        $userId = $request->user() ? $request->user()->id : 'guest';
        $queryString = $request->getQueryString() ?: '';
        
        return 'api_cache:' . $userId . ':' . $request->path() . ':' . md5($queryString);
    }
}