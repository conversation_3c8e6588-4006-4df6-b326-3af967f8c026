<template>
  <div class="recommendation-panel">
    <div class="panel-header">
      <h4>推荐功能</h4>
      <el-button text size="small" @click="refreshRecommendations">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
    
    <div class="recommendations-list">
      <div 
        v-for="item in recommendations" 
        :key="item.id"
        class="recommendation-item"
        @click="handleRecommendationClick(item)"
      >
        <el-icon class="item-icon">
          <component :is="item.icon" />
        </el-icon>
        <div class="item-content">
          <div class="item-title">{{ item.title }}</div>
          <div class="item-desc">{{ item.description }}</div>
        </div>
        <el-tag v-if="item.tag" size="small" :type="item.tagType">
          {{ item.tag }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue"
import { useRouter } from "vue-router"
import { useUserStore } from "@/stores/user"

const router = useRouter()
const userStore = useUserStore()

const recommendations = ref([
  {
    id: 1,
    title: "群组管理",
    description: "管理微信群组",
    icon: "User",
    route: "/community/groups",
    tag: "热门",
    tagType: "danger"
  },
  {
    id: 2,
    title: "数据统计",
    description: "查看系统数据",
    icon: "TrendCharts",
    route: "/dashboard/data",
    tag: "推荐",
    tagType: "success"
  },
  {
    id: 3,
    title: "用户管理",
    description: "管理系统用户",
    icon: "UserFilled",
    route: "/user/list"
  }
])

const handleRecommendationClick = (item) => {
  if (item.route) {
    router.push(item.route)
  }
}

const refreshRecommendations = () => {
  // 刷新推荐逻辑
}

onMounted(() => {
  // 初始化推荐
})
</script>

<style scoped>
.recommendation-panel {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.recommendations-list {
  max-height: 300px;
  overflow-y: auto;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: all 0.2s;
}

.recommendation-item:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary);
}

.item-icon {
  margin-right: 12px;
  color: var(--el-color-primary);
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.item-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
}
</style>
