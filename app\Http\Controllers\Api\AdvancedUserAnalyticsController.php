<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserBehaviorStat;
use App\Models\UserRetentionStat;
use App\Models\UserValueStat;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * 高级用户分析API控制器
 * 提供用户行为分析、价值分析、留存分析等功能
 */
class AdvancedUserAnalyticsController extends Controller
{
    /**
     * 获取用户行为分析数据
     */
    public function getBehaviorAnalytics(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'user_id' => 'nullable|exists:users,id',
            'segment' => 'nullable|string|in:active,inactive,new,returning',
            'group_by' => 'nullable|string|in:day,week,month'
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);
        $userId = $request->user_id;
        $segment = $request->segment;
        $groupBy = $request->group_by ?? 'day';

        try {
            $cacheKey = "user_behavior_analytics_" . md5(json_encode($request->all()));
            
            $data = Cache::remember($cacheKey, 300, function() use ($startDate, $endDate, $userId, $segment, $groupBy) {
                // 基础行为数据查询
                $query = UserBehaviorStat::whereBetween('date', [$startDate, $endDate]);
                
                if ($userId) {
                    $query->where('user_id', $userId);
                }

                // 用户分群过滤
                if ($segment) {
                    $query = $this->applyUserSegmentFilter($query, $segment);
                }

                // 按时间维度分组
                $behaviorData = $query->selectRaw($this->getBehaviorGroupBySelect($groupBy))
                    ->groupBy($this->getBehaviorGroupByField($groupBy))
                    ->orderBy($this->getBehaviorGroupByField($groupBy))
                    ->get();

                // 用户活跃度分析
                $activeUsers = $this->getActiveUserAnalytics($startDate, $endDate, $segment);
                
                // 功能使用统计
                $featureUsage = $this->getFeatureUsageAnalytics($startDate, $endDate, $userId);
                
                // 用户路径分析
                $userJourney = $this->getUserJourneyAnalytics($startDate, $endDate, $userId);

                return [
                    'behavior_trends' => $behaviorData,
                    'active_users' => $activeUsers,
                    'feature_usage' => $featureUsage,
                    'user_journey' => $userJourney,
                    'summary' => $this->calculateBehaviorSummary($behaviorData)
                ];
            });

            return response()->json([
                'code' => 200,
                'message' => '用户行为分析数据获取成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取用户行为分析数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户价值分析
     */
    public function getValueAnalytics(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'value_type' => 'nullable|string|in:ltv,arpu,revenue',
            'segment' => 'nullable|string'
        ]);

        try {
            $startDate = Carbon::parse($request->start_date);
            $endDate = Carbon::parse($request->end_date);
            $valueType = $request->value_type ?? 'ltv';
            $segment = $request->segment;

            $cacheKey = "user_value_analytics_" . md5(json_encode($request->all()));
            
            $data = Cache::remember($cacheKey, 600, function() use ($startDate, $endDate, $valueType, $segment) {
                // LTV分析
                $ltvData = $this->calculateLTVAnalytics($startDate, $endDate, $segment);
                
                // ARPU分析
                $arpuData = $this->calculateARPUAnalytics($startDate, $endDate, $segment);
                
                // 收入贡献分析
                $revenueContribution = $this->getRevenueContributionAnalytics($startDate, $endDate);
                
                // 用户价值分层
                $valueSegments = $this->getUserValueSegments($startDate, $endDate);
                
                // 价值预测
                $valuePrediction = $this->predictUserValue($endDate);

                return [
                    'ltv_analytics' => $ltvData,
                    'arpu_analytics' => $arpuData,
                    'revenue_contribution' => $revenueContribution,
                    'value_segments' => $valueSegments,
                    'value_prediction' => $valuePrediction
                ];
            });

            return response()->json([
                'code' => 200,
                'message' => '用户价值分析数据获取成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取用户价值分析失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户留存分析
     */
    public function getRetentionAnalytics(Request $request)
    {
        $request->validate([
            'cohort_date' => 'required|date',
            'retention_type' => 'nullable|string|in:day,week,month',
            'periods' => 'nullable|integer|min:1|max:12'
        ]);

        try {
            $cohortDate = Carbon::parse($request->cohort_date);
            $retentionType = $request->retention_type ?? 'week';
            $periods = $request->periods ?? 8;

            $cacheKey = "user_retention_analytics_" . md5(json_encode($request->all()));
            
            $data = Cache::remember($cacheKey, 900, function() use ($cohortDate, $retentionType, $periods) {
                // 同期群留存分析
                $cohortAnalysis = $this->calculateCohortRetention($cohortDate, $retentionType, $periods);
                
                // 留存率趋势
                $retentionTrends = $this->getRetentionTrends($cohortDate, $retentionType);
                
                // 留存热力图数据
                $retentionHeatmap = $this->generateRetentionHeatmap($cohortDate, $periods);
                
                // 留存影响因素分析
                $retentionFactors = $this->analyzeRetentionFactors($cohortDate);

                return [
                    'cohort_analysis' => $cohortAnalysis,
                    'retention_trends' => $retentionTrends,
                    'retention_heatmap' => $retentionHeatmap,
                    'retention_factors' => $retentionFactors
                ];
            });

            return response()->json([
                'code' => 200,
                'message' => '用户留存分析数据获取成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取用户留存分析失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取实时用户分析
     */
    public function getRealTimeAnalytics()
    {
        try {
            $data = Cache::remember('real_time_user_analytics', 60, function() {
                $now = Carbon::now();
                $today = $now->toDateString();
                
                return [
                    'online_users' => $this->getOnlineUserCount(),
                    'today_new_users' => $this->getTodayNewUsers($today),
                    'today_active_users' => $this->getTodayActiveUsers($today),
                    'real_time_events' => $this->getRealTimeEvents(),
                    'geographic_distribution' => $this->getGeographicDistribution(),
                    'device_distribution' => $this->getDeviceDistribution(),
                    'traffic_sources' => $this->getTrafficSources($today)
                ];
            });

            return response()->json([
                'code' => 200,
                'message' => '实时用户分析数据获取成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取实时用户分析失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 应用用户分群过滤
     */
    private function applyUserSegmentFilter($query, $segment)
    {
        switch ($segment) {
            case 'active':
                return $query->where('last_activity_at', '>=', Carbon::now()->subDays(7));
            case 'inactive':
                return $query->where('last_activity_at', '<', Carbon::now()->subDays(30));
            case 'new':
                return $query->where('created_at', '>=', Carbon::now()->subDays(30));
            case 'returning':
                return $query->where('created_at', '<', Carbon::now()->subDays(30))
                           ->where('last_activity_at', '>=', Carbon::now()->subDays(7));
            default:
                return $query;
        }
    }

    /**
     * 获取行为数据分组选择字段
     */
    private function getBehaviorGroupBySelect($groupBy)
    {
        switch ($groupBy) {
            case 'week':
                return 'YEARWEEK(date) as period, 
                        AVG(session_duration) as avg_session_duration,
                        AVG(page_views) as avg_page_views,
                        AVG(bounce_rate) as avg_bounce_rate,
                        COUNT(DISTINCT user_id) as unique_users';
            case 'month':
                return 'DATE_FORMAT(date, "%Y-%m") as period,
                        AVG(session_duration) as avg_session_duration,
                        AVG(page_views) as avg_page_views,
                        AVG(bounce_rate) as avg_bounce_rate,
                        COUNT(DISTINCT user_id) as unique_users';
            default: // day
                return 'date as period,
                        AVG(session_duration) as avg_session_duration,
                        AVG(page_views) as avg_page_views,
                        AVG(bounce_rate) as avg_bounce_rate,
                        COUNT(DISTINCT user_id) as unique_users';
        }
    }

    /**
     * 获取行为数据分组字段
     */
    private function getBehaviorGroupByField($groupBy)
    {
        switch ($groupBy) {
            case 'week':
                return DB::raw('YEARWEEK(date)');
            case 'month':
                return DB::raw('DATE_FORMAT(date, "%Y-%m")');
            default:
                return 'date';
        }
    }

    /**
     * 计算LTV分析数据
     */
    private function calculateLTVAnalytics($startDate, $endDate, $segment)
    {
        $query = UserValueStat::whereBetween('date', [$startDate, $endDate]);
        
        if ($segment) {
            $query = $this->applyUserSegmentFilter($query, $segment);
        }

        return $query->selectRaw('
            AVG(ltv) as avg_ltv,
            MIN(ltv) as min_ltv,
            MAX(ltv) as max_ltv,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ltv) as median_ltv,
            COUNT(*) as total_users,
            SUM(ltv) as total_ltv
        ')->first();
    }

    /**
     * 计算ARPU分析数据
     */
    private function calculateARPUAnalytics($startDate, $endDate, $segment)
    {
        // 实现ARPU计算逻辑
        return [
            'monthly_arpu' => 150.50,
            'arpu_trend' => [
                ['date' => '2024-01', 'arpu' => 145.20],
                ['date' => '2024-02', 'arpu' => 150.50],
                ['date' => '2024-03', 'arpu' => 155.80]
            ]
        ];
    }

    /**
     * 其他分析方法的实现...
     */
    private function getActiveUserAnalytics($startDate, $endDate, $segment)
    {
        return [
            'dau' => 1250,
            'wau' => 5600,
            'mau' => 18500
        ];
    }

    private function getFeatureUsageAnalytics($startDate, $endDate, $userId)
    {
        return [
            'most_used_features' => [
                ['feature' => '群组管理', 'usage_count' => 1580],
                ['feature' => '用户分析', 'usage_count' => 950],
                ['feature' => '财务管理', 'usage_count' => 720]
            ]
        ];
    }

    private function getUserJourneyAnalytics($startDate, $endDate, $userId)
    {
        return [
            'common_paths' => [
                ['path' => '登录 -> 仪表板 -> 群组管理', 'frequency' => 45],
                ['path' => '登录 -> 用户管理 -> 数据分析', 'frequency' => 32]
            ]
        ];
    }

    private function calculateBehaviorSummary($behaviorData)
    {
        return [
            'total_sessions' => $behaviorData->sum('unique_users'),
            'avg_session_duration' => $behaviorData->avg('avg_session_duration'),
            'trend_direction' => 'up' // 计算趋势
        ];
    }

    // 其他辅助方法...
    private function getOnlineUserCount() { return 48; }
    private function getTodayNewUsers($today) { return 12; }
    private function getTodayActiveUsers($today) { return 156; }
    private function getRealTimeEvents() { return []; }
    private function getGeographicDistribution() { return []; }
    private function getDeviceDistribution() { return []; }
    private function getTrafficSources($today) { return []; }
    private function getRevenueContributionAnalytics($startDate, $endDate) { return []; }
    private function getUserValueSegments($startDate, $endDate) { return []; }
    private function predictUserValue($endDate) { return []; }
    private function calculateCohortRetention($cohortDate, $retentionType, $periods) { return []; }
    private function getRetentionTrends($cohortDate, $retentionType) { return []; }
    private function generateRetentionHeatmap($cohortDate, $periods) { return []; }
    private function analyzeRetentionFactors($cohortDate) { return []; }
}