<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon :size="120" :color="errorConfig.iconColor">
            <component :is="errorConfig.icon" />
          </el-icon>
        </div>
        
        <!-- 错误标题 -->
        <h1 class="error-title">{{ errorConfig.code }}</h1>
        <h2 class="error-subtitle">{{ errorConfig.title }}</h2>
        
        <!-- 错误描述 -->
        <p class="error-description">
          {{ errorConfig.description }}
        </p>
        
        <!-- 错误详情 (可选) -->
        <div v-if="errorDetails" class="error-details">
          <el-collapse>
            <el-collapse-item title="错误详情" name="details">
              <pre>{{ errorDetails }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button 
            v-for="action in errorConfig.actions" 
            :key="action.key"
            :type="action.type" 
            @click="handleAction(action.key)"
          >
            <el-icon><component :is="action.icon" /></el-icon>
            {{ action.label }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  Lock, 
  Warning, 
  QuestionFilled,
  ArrowLeft, 
  HomeFilled, 
  Refresh 
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 错误类型配置
const errorConfigs = {
  403: {
    code: '403',
    title: '访问被拒绝',
    description: '抱歉，您没有权限访问此页面。请联系管理员获取相应权限。',
    icon: Lock,
    iconColor: '#f56565',
    actions: [
      { key: 'back', label: '返回上页', type: 'primary', icon: ArrowLeft },
      { key: 'home', label: '回到首页', type: 'default', icon: HomeFilled }
    ]
  },
  404: {
    code: '404',
    title: '页面未找到',
    description: '抱歉，您访问的页面不存在或已被删除。',
    icon: QuestionFilled,
    iconColor: '#909399',
    actions: [
      { key: 'home', label: '返回首页', type: 'primary', icon: HomeFilled },
      { key: 'back', label: '返回上页', type: 'default', icon: ArrowLeft }
    ]
  },
  500: {
    code: '500',
    title: '服务器错误',
    description: '抱歉，服务器出现了一些问题。请稍后再试或联系技术支持。',
    icon: Warning,
    iconColor: '#f56565',
    actions: [
      { key: 'retry', label: '重新加载', type: 'primary', icon: Refresh },
      { key: 'back', label: '返回上页', type: 'default', icon: ArrowLeft },
      { key: 'home', label: '回到首页', type: 'default', icon: HomeFilled }
    ]
  },
  load: {
    code: 'ERROR',
    title: '组件加载失败',
    description: '抱歉，请求的页面组件无法正常加载。这可能是由于网络问题或组件文件缺失导致的。',
    icon: Warning,
    iconColor: '#f56565',
    actions: [
      { key: 'retry', label: '重新加载', type: 'primary', icon: Refresh },
      { key: 'back', label: '返回上页', type: 'default', icon: ArrowLeft },
      { key: 'home', label: '回到首页', type: 'default', icon: HomeFilled }
    ]
  }
}

// 获取错误类型
const errorType = computed(() => {
  return route.params.type || route.query.type || '404'
})

// 获取错误详情
const errorDetails = computed(() => {
  return route.query.details || null
})

// 获取错误配置
const errorConfig = computed(() => {
  return errorConfigs[errorType.value] || errorConfigs['404']
})

// 处理操作
const handleAction = (actionKey) => {
  switch (actionKey) {
    case 'back':
      goBack()
      break
    case 'home':
      goHome()
      break
    case 'retry':
      handleRetry()
      break
  }
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

const goHome = () => {
  router.push('/')
}

const handleRetry = () => {
  window.location.reload()
}

// 页面标题设置
onMounted(() => {
  document.title = `${errorConfig.value.code} - ${errorConfig.value.title}`
})
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  margin-bottom: 30px;
}

.error-title {
  font-size: 4rem;
  font-weight: bold;
  color: #2d3748;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-subtitle {
  font-size: 1.8rem;
  color: #4a5568;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.error-description {
  font-size: 1.1rem;
  color: #718096;
  line-height: 1.6;
  margin-bottom: 30px;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details pre {
  background: #f7fafc;
  padding: 15px;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #2d3748;
  overflow-x: auto;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .el-button {
  padding: 12px 24px;
  font-size: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.error-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 40px 20px;
  }
  
  .error-title {
    font-size: 3rem;
  }
  
  .error-subtitle {
    font-size: 1.4rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 200px;
  }
}
</style>
