<template>
  <div class="role-switcher">
    <el-dropdown 
      v-if="availableRoles.length > 1" 
      trigger="click" 
      @command="handleRoleSwitch"
      class="role-dropdown"
    >
      <span class="role-switcher-trigger">
        <el-icon><Switch /></el-icon>
        <span class="current-role">{{ currentRoleText }}</span>
        <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu class="role-dropdown-menu">
          <div class="dropdown-header">
            <span>切换角色</span>
          </div>
          <el-dropdown-item 
            v-for="role in availableRoles" 
            :key="role.value"
            :command="role.value"
            :class="{ 'is-active': role.value === currentRole }"
          >
            <div class="role-item">
              <el-icon class="role-icon">
                <component :is="role.icon" />
              </el-icon>
              <div class="role-info">
                <div class="role-name">{{ role.label }}</div>
                <div class="role-desc">{{ role.description }}</div>
              </div>
              <el-icon v-if="role.value === currentRole" class="check-icon">
                <Check />
              </el-icon>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 单角色显示 -->
    <div v-else class="single-role">
      <el-icon><User /></el-icon>
      <span>{{ currentRoleText }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Switch, ArrowDown, Check, User, Avatar, Comment, 
  OfficeBuilding, UserFilled, Setting 
} from '@element-plus/icons-vue'
import { getRoleDisplayName, getUserDefaultRoute } from '@/config/navigation'

const router = useRouter()
const userStore = useUserStore()

// 角色配置
const roleConfig = {
  admin: {
    label: '超级管理员',
    description: '系统全局管理权限',
    icon: 'Setting'
  },
  substation: {
    label: '分站管理员',
    description: '分站运营管理权限',
    icon: 'OfficeBuilding'
  },
  agent: {
    label: '代理商',
    description: '代理商业务管理',
    icon: 'Avatar'
  },
  distributor: {
    label: '分销员',
    description: '分销业务和客户管理',
    icon: 'UserFilled'
  },
  group_owner: {
    label: '群主',
    description: '群组内容管理',
    icon: 'Comment'
  },
  user: {
    label: '普通用户',
    description: '基础功能使用',
    icon: 'User'
  }
}

// 计算属性
const currentRole = computed(() => userStore.userInfo?.role)
const currentRoleText = computed(() => getRoleDisplayName(currentRole.value))

// 获取用户可用角色（模拟多角色用户）
const availableRoles = computed(() => {
  // 这里应该从用户信息中获取可用角色
  // 目前模拟一个用户可能有多个角色的情况
  const userRoles = userStore.userInfo?.available_roles || [currentRole.value]
  
  return userRoles.map(role => ({
    value: role,
    label: roleConfig[role]?.label || role,
    description: roleConfig[role]?.description || '',
    icon: roleConfig[role]?.icon || 'User'
  })).filter(Boolean)
})

// 方法
const handleRoleSwitch = async (targetRole) => {
  if (targetRole === currentRole.value) {
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要切换到"${roleConfig[targetRole]?.label}"角色吗？切换后将跳转到对应的工作台。`,
      '角色切换确认',
      {
        confirmButtonText: '确定切换',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 执行角色切换
    await switchRole(targetRole)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('角色切换失败')
    }
  }
}

const switchRole = async (targetRole) => {
  try {
    // 这里应该调用API切换角色
    // 目前模拟切换过程
    
    // 更新用户信息中的当前角色
    if (userStore.userInfo) {
      userStore.userInfo.role = targetRole
    }
    
    // 清除相关缓存
    localStorage.removeItem('user_permissions_cache')
    
    // 跳转到新角色的默认页面
    const defaultRoute = getUserDefaultRoute(targetRole)
    await router.push(defaultRoute)
    
    ElMessage.success(`已切换到${roleConfig[targetRole]?.label}角色`)
    
    // 刷新页面以确保权限生效
    setTimeout(() => {
      window.location.reload()
    }, 1000)
    
  } catch (error) {
    console.error('角色切换失败:', error)
    throw error
  }
}

// 监听角色变化
watch(currentRole, (newRole, oldRole) => {
  if (newRole && oldRole && newRole !== oldRole) {
    // 角色发生变化时的处理逻辑
    console.log(`角色从 ${oldRole} 切换到 ${newRole}`)
  }
})
</script>

<style scoped>
.role-switcher {
  display: inline-block;
}

.role-dropdown {
  cursor: pointer;
}

.role-switcher-trigger {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  font-size: 14px;
  gap: 6px;
}

.role-switcher-trigger:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.current-role {
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.role-switcher-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.single-role {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

/* 下拉菜单样式 */
:deep(.role-dropdown-menu) {
  min-width: 280px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  
  .dropdown-header {
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
  }
  
  .el-dropdown-menu__item {
    padding: 0;
    height: auto;
    
    &:hover {
      background: #f1f5f9;
    }
    
    &.is-active {
      background: #eff6ff;
      border-left: 3px solid #3b82f6;
    }
  }
}

.role-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  width: 100%;
  gap: 12px;
}

.role-icon {
  font-size: 18px;
  color: #6b7280;
  flex-shrink: 0;
}

.role-info {
  flex: 1;
}

.role-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.role-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.check-icon {
  font-size: 16px;
  color: #3b82f6;
  flex-shrink: 0;
}

.is-active .role-icon,
.is-active .role-name {
  color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-switcher-trigger {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  :deep(.role-dropdown-menu) {
    min-width: 240px;
  }
  
  .role-item {
    padding: 10px 12px;
  }
}
</style>