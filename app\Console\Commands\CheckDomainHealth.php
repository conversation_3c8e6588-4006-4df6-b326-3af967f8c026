<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AntiBlockService;
use Illuminate\Support\Facades\Log;

/**
 * 域名健康检查定时任务
 */
class CheckDomainHealth extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'domain:check-health 
                           {--batch=10 : 每批处理的域名数量}
                           {--intelligent : 使用智能检查模式}
                           {--all : 检查所有域名，包括已封禁的}';

    /**
     * 命令描述
     */
    protected $description = '检查域名健康状态，支持批量和智能检查模式';

    protected AntiBlockService $antiBlockService;

    public function __construct(AntiBlockService $antiBlockService)
    {
        parent::__construct();
        $this->antiBlockService = $antiBlockService;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始执行域名健康检查...');
        
        $batchSize = (int) $this->option('batch');
        $useIntelligent = $this->option('intelligent');
        $checkAll = $this->option('all');
        
        try {
            if ($useIntelligent) {
                $this->performIntelligentCheck($checkAll);
            } else {
                $this->performBasicCheck($batchSize, $checkAll);
            }
            
            $this->info('域名健康检查完成！');
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('域名健康检查失败: ' . $e->getMessage());
            Log::error('域名健康检查命令执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 执行智能检查
     */
    private function performIntelligentCheck(bool $checkAll): void
    {
        $this->info('使用智能检查模式...');
        
        $result = $this->antiBlockService->batchIntelligentCheck();
        
        if ($result['success'] ?? true) {
            $this->info("智能检查完成:");
            $this->line("  总计: {$result['total']}");
            $this->line("  成功: {$result['success']}");
            $this->line("  失败: {$result['failed']}");
            $this->line("  封禁: {$result['blocked']}");
        } else {
            $this->error("智能检查失败: " . ($result['message'] ?? '未知错误'));
        }
    }

    /**
     * 执行基础检查
     */
    private function performBasicCheck(int $batchSize, bool $checkAll): void
    {
        $this->info('使用基础检查模式...');
        
        // 获取需要检查的域名
        $query = \App\Models\DomainPool::query();
        
        if (!$checkAll) {
            $query->where('status', '!=', 3); // 排除已封禁的域名
        }
        
        $domains = $query->pluck('id')->toArray();
        $total = count($domains);
        
        if ($total === 0) {
            $this->info('没有需要检查的域名');
            return;
        }
        
        $this->info("找到 {$total} 个域名需要检查");
        
        // 分批处理
        $batches = array_chunk($domains, $batchSize);
        $processed = 0;
        $success = 0;
        $failed = 0;
        
        $progressBar = $this->output->createProgressBar($total);
        $progressBar->start();
        
        foreach ($batches as $batch) {
            $results = $this->antiBlockService->batchCheckDomains($batch);
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $success++;
                } else {
                    $failed++;
                }
                $processed++;
                $progressBar->advance();
            }
            
            // 批次间稍作延迟，避免过于频繁的请求
            usleep(500000); // 0.5秒
        }
        
        $progressBar->finish();
        $this->newLine();
        
        $this->info("基础检查完成:");
        $this->line("  处理: {$processed}");
        $this->line("  成功: {$success}");
        $this->line("  失败: {$failed}");
    }
}