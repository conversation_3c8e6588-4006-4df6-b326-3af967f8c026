<template>
  <aside class="modern-navigation-sidebar" :class="{ 
    collapsed: isCollapsed,
    'mobile-open': mobileOpen 
  }">
    <!-- Logo区域 -->
    <div class="nav-logo">
      <div class="logo-container">
        <div class="logo-icon">
          <el-icon size="28"><Connection /></el-icon>
        </div>
        <transition name="logo-text">
          <div v-if="!isCollapsed" class="logo-text">
            <h1>LinkHub Pro</h1>
            <p>专业社群管理平台</p>
          </div>
        </transition>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <div class="menu-content">
        <navigation-group-item
          v-for="(group, index) in navigationGroups"
          :key="group.key"
          :group="group"
          :collapsed="isCollapsed"
          :index="index"
          @item-click="handleNavItemClick"
        />
      </div>
    </nav>

    <!-- 用户信息面板 -->
    <navigation-user-panel 
      :collapsed="isCollapsed"
      :user="userInfo"
      @logout="handleLogout"
      @profile="handleProfile"
      @settings="handleSettings"
    />

    <!-- 移动端遮罩层 -->
    <div 
      v-if="mobileOpen" 
      class="mobile-overlay"
      @click="$emit('mobile-close')"
    ></div>
  </aside>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { Connection } from '@element-plus/icons-vue'
import NavigationGroupItem from './NavigationGroupItem.vue'
import NavigationUserPanel from './NavigationUserPanel.vue'

// Props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  mobileOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle-collapse', 'mobile-close'])

// Store & Router
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()

// Data
const isCollapsed = ref(props.collapsed)
const userInfo = computed(() => userStore.userInfo)

// 导航分组数据 - 继承UserList.vue的设计理念
const navigationGroups = ref([
  {
    key: 'business-core',
    title: '业务核心',
    icon: 'Monitor',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    items: [
      {
        path: '/dashboard',
        title: '数据看板',
        icon: 'DataLine',
        badge: null,
        description: '实时数据监控与分析'
      },
      {
        path: '/data-screen',
        title: '数据大屏',
        icon: 'Monitor',
        badge: null,
        description: '全屏数据展示'
      },
      {
        path: '/data-screen', 
        title: '数据大屏',
        icon: 'Monitor',
        badge: null,
        description: '全屏数据展示'
      },
      {
        path: '/community/groups',
        title: '社群管理', 
        icon: 'UserFilled',
        badge: 3,
        badgeType: 'primary',
        description: '社群创建与管理'
      }
    ]
  },
  {
    key: 'operation',
    title: '运营管理',
    icon: 'Operation',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    items: [
      {
        path: '/distribution/distributors',
        title: '分销管理',
        icon: 'Share',
        badge: null,
        description: '分销商管理'
      },
      {
        path: '/orders/list',
        title: '订单管理',
        icon: 'Tickets', 
        badge: 12,
        badgeType: 'warning',
        description: '订单处理与跟踪'
      },
      {
        path: '/anti-block/dashboard',
        title: '防红系统',
        icon: 'Shield',
        badge: null,
        description: '链接防封检测'
      },
      {
        path: '/promotion/links',
        title: '推广管理',
        icon: 'Promotion',
        badge: null,
        description: '推广链接管理'
      }
    ]
  },
  {
    key: 'user-permission',
    title: '用户与权限',
    icon: 'User',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    items: [
      {
        path: '/user/list',
        title: '用户管理',
        icon: 'UserFilled',
        badge: null,
        description: '用户信息管理'
      },
      {
        path: '/agent/dashboard',
        title: '代理商管理',
        icon: 'Avatar',
        badge: null,
        description: '代理商系统管理'
      },
      {
        path: '/substation/list',
        title: '分站管理',
        icon: 'OfficeBuilding',
        badge: null,
        description: '分站系统管理'
      },
      {
        path: '/permission/roles',
        title: '权限管理',
        icon: 'Lock',
        badge: null,
        description: '角色权限配置'
      }
    ]
  },
  {
    key: 'finance',
    title: '财务管理',
    icon: 'Money',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    items: [
      {
        path: '/finance/dashboard',
        title: '财务总览',
        icon: 'DataLine',
        badge: null,
        description: '财务数据概览'
      },
      {
        path: '/finance/commission-logs',
        title: '佣金明细',
        icon: 'Medal',
        badge: null,
        description: '佣金记录查看'
      },
      {
        path: '/finance/transactions',
        title: '交易记录',
        icon: 'Goods',
        badge: null,
        description: '交易流水管理'
      },
      {
        path: '/admin/payment-settings',
        title: '支付管理',
        icon: 'CreditCard',
        badge: null,
        description: '支付渠道配置'
      }
    ]
  },
  {
    key: 'system',
    title: '系统配置',
    icon: 'Setting',
    gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    items: [
      {
        path: '/system/settings',
        title: '系统设置',
        icon: 'Tools',
        badge: null,
        description: '系统基础配置'
      },
      {
        path: '/system/operation-logs',
        title: '操作日志',
        icon: 'Document',
        badge: null,
        description: '系统操作记录'
      },
      {
        path: '/security/management',
        title: '安全管理',
        icon: 'Shield',
        badge: 2,
        badgeType: 'danger',
        description: '系统安全配置'
      }
    ]
  }
])

// Methods
const handleNavItemClick = (item) => {
  console.log('🔗 ModernNav导航到:', item.path, '项目:', item.title)
  try {
    router.push(item.path)
    emit('mobile-close')
    console.log('✅ ModernNav导航成功')
  } catch (error) {
    console.error('❌ ModernNav导航失败:', error)
  }
}

const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}

const handleProfile = () => {
  router.push('/user/profile')
  emit('mobile-close')
}

const handleSettings = () => {
  router.push('/system/settings')
  emit('mobile-close')
}

// 监听prop变化
watch(() => props.collapsed, (newVal) => {
  isCollapsed.value = newVal
})

// 键盘快捷键支持
onMounted(() => {
  const handleKeydown = (e) => {
    // Ctrl + B 切换侧边栏
    if (e.ctrlKey && e.key === 'b') {
      e.preventDefault()
      emit('toggle-collapse')
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  return () => {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
// 导入变量
@import '@/styles/variables.scss';

.modern-navigation-sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);
  border-right: 1px solid #e4e7ed;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
  
  &.collapsed {
    width: 80px;
  }
  
  // Logo区域 - 继承UserList.vue的渐变设计
  .nav-logo {
    padding: 24px 20px;
    border-bottom: 1px solid #f0f2ff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    
    // 添加光泽效果
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: logo-shimmer 3s ease-in-out infinite;
    }
    
    .logo-container {
      display: flex;
      align-items: center;
      gap: 12px;
      color: white;
      position: relative;
      z-index: 2;
      
      .logo-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05) rotate(5deg);
          background: rgba(255, 255, 255, 0.2);
        }
      }
      
      .logo-text {
        flex: 1;
        
        h1 {
          margin: 0;
          font-size: 20px;
          font-weight: 700;
          line-height: 1.2;
          background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        p {
          margin: 2px 0 0 0;
          font-size: 12px;
          opacity: 0.8;
          line-height: 1.3;
          font-weight: 400;
        }
      }
    }
  }
  
  // 导航菜单
  .nav-menu {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px 0;
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.2);
      border-radius: 2px;
      
      &:hover {
        background: rgba(102, 126, 234, 0.4);
      }
    }
    
    .menu-content {
      padding: 0 16px;
    }
  }
  
  // 移动端样式
  @media (max-width: 768px) {
    position: fixed;
    left: -280px;
    z-index: 1000;
    
    &.mobile-open {
      left: 0;
    }
    
    .mobile-overlay {
      position: fixed;
      top: 0;
      left: 280px;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: -1;
    }
  }
  
  // 平板端样式
  @media (max-width: 1024px) {
    width: 240px;
    
    &.collapsed {
      width: 64px;
    }
    
    .nav-logo {
      padding: 20px 16px;
      
      .logo-container {
        .logo-icon {
          width: 40px;
          height: 40px;
        }
        
        .logo-text h1 {
          font-size: 18px;
        }
      }
    }
  }
}

// Logo文字过渡动画
.logo-text-enter-active,
.logo-text-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-text-enter-from,
.logo-text-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

// Logo光泽动画
@keyframes logo-shimmer {
  0% {
    transform: rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: rotate(180deg);
    opacity: 0.1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.3;
  }
}

// 焦点状态样式
.modern-navigation-sidebar:focus-within {
  .nav-logo {
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.3);
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .modern-navigation-sidebar {
    border-right-width: 2px;
    border-right-color: #000;
    
    .nav-logo {
      background: #000;
      color: #fff;
    }
  }
}

// 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .modern-navigation-sidebar {
    transition: width 0.1s linear;
    
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
</style>