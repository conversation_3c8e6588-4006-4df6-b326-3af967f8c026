<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ThemeController extends Controller
{
    /**
     * 切换主题
     */
    public function switchTheme(Request $request)
    {
        $theme = $request->input('theme', 'default');
        
        // 验证主题名称
        $allowedThemes = ['default', 'glassmorphism', 'dark', 'light'];
        if (!in_array($theme, $allowedThemes)) {
            return response()->json([
                'success' => false,
                'message' => '不支持的主题类型'
            ], 400);
        }
        
        // 保存主题设置到会话
        Session::put('admin_theme', $theme);
        
        // 如果用户已登录，也可以保存到用户设置中
        if (auth()->check()) {
            $user = auth()->user();
            $settings = $user->settings ?? [];
            $settings['theme'] = $theme;
            $user->settings = $settings;
            $user->save();
        }
        
        return response()->json([
            'success' => true,
            'message' => '主题切换成功',
            'theme' => $theme
        ]);
    }
    
    /**
     * 获取当前主题
     */
    public function getCurrentTheme()
    {
        $theme = 'default';
        
        // 优先从用户设置获取
        if (auth()->check()) {
            $user = auth()->user();
            $theme = $user->settings['theme'] ?? $theme;
        }
        
        // 其次从会话获取
        $theme = Session::get('admin_theme', $theme);
        
        return response()->json([
            'success' => true,
            'theme' => $theme
        ]);
    }
    
    /**
     * 获取可用主题列表
     */
    public function getAvailableThemes()
    {
        $themes = [
            [
                'name' => 'default',
                'title' => '默认主题',
                'description' => '经典的管理后台主题',
                'preview' => '/admin/img/themes/default-preview.jpg'
            ],
            [
                'name' => 'glassmorphism',
                'title' => '毛玻璃主题',
                'description' => '现代化的毛玻璃效果主题',
                'preview' => '/admin/img/themes/glassmorphism-preview.jpg'
            ],
            [
                'name' => 'dark',
                'title' => '深色主题',
                'description' => '护眼的深色主题',
                'preview' => '/admin/img/themes/dark-preview.jpg'
            ],
            [
                'name' => 'light',
                'title' => '浅色主题',
                'description' => '简洁的浅色主题',
                'preview' => '/admin/img/themes/light-preview.jpg'
            ]
        ];
        
        return response()->json([
            'success' => true,
            'themes' => $themes
        ]);
    }
    
    /**
     * 主题设置页面
     */
    public function settings()
    {
        $currentTheme = 'default';
        
        if (auth()->check()) {
            $user = auth()->user();
            $currentTheme = $user->settings['theme'] ?? $currentTheme;
        }
        
        $currentTheme = Session::get('admin_theme', $currentTheme);
        
        return view('admin.settings.theme', compact('currentTheme'));
    }
}