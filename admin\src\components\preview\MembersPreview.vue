<template>
  <div class="members-preview">
    <div class="members-container">
      <div class="members-header">
        <h3 class="members-title">群成员</h3>
        <div v-if="showCount" class="member-count">
          {{ totalMembers }}人
        </div>
      </div>
      
      <div v-if="displayAvatars.length > 0" class="members-content">
        <div class="avatar-grid">
          <div
            v-for="(avatar, index) in displayAvatars"
            :key="index"
            class="avatar-item"
            :style="getAvatarStyle(index)"
          >
            <img
              :src="avatar"
              :alt="`成员${index + 1}`"
              @error="handleImageError($event, index)"
              @load="handleImageLoad($event)"
            />
          </div>
          
          <!-- 更多成员提示 -->
          <div v-if="hasMoreMembers" class="more-members">
            <div class="more-overlay">
              <span class="more-text">+{{ remainingCount }}</span>
            </div>
          </div>
        </div>
        
        <!-- 成员统计 -->
        <div class="member-stats">
          <div class="stat-item">
            <div class="stat-label">在线成员</div>
            <div class="stat-value">{{ onlineCount }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">今日活跃</div>
            <div class="stat-value">{{ activeToday }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">新增成员</div>
            <div class="stat-value">{{ newMembers }}</div>
          </div>
        </div>
        
        <!-- 最新加入 -->
        <div class="recent-joins">
          <div class="recent-title">最新加入</div>
          <div class="recent-list">
            <div
              v-for="(member, index) in recentMembers"
              :key="index"
              class="recent-item"
            >
              <img
                :src="member.avatar"
                :alt="member.name"
                class="recent-avatar"
                @error="handleRecentImageError($event, index)"
                @load="handleImageLoad($event)"
              />
              <div class="recent-info">
                <div class="recent-name">{{ member.name }}</div>
                <div class="recent-time">{{ member.joinTime }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="members-placeholder">
        <el-icon class="placeholder-icon"><User /></el-icon>
        <div class="placeholder-text">暂无成员头像</div>
        <div class="placeholder-hint">请设置虚拟成员头像库</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { User } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const avatarLibrary = computed(() => {
  return props.groupData.avatar_library || 'default'
})

const maxCount = computed(() => {
  return props.section.config?.maxCount || 12
})

const showCount = computed(() => {
  return props.section.config?.showCount !== false
})

// 模拟头像数据
const allAvatars = computed(() => {
  // 使用更可靠的头像URL
  const avatars = []

  // 使用本地生成的头像或可靠的服务
  const names = [
    '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
    '郑一', '王二', '李三', '张四', '刘五', '陈六', '杨七', '黄八',
    '赵九', '周十', '徐一', '朱二', '秦三', '尤四', '许五', '何六',
    '吕七', '施八', '张九', '孔十', '曹一', '严二', '华三', '金四',
    '魏五', '陶六', '姜七', '戚八', '谢九', '邹十', '喻一', '柏二',
    '水三', '窦四', '章五', '云六', '苏七', '潘八', '葛九', '奚十'
  ]

  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
    '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
  ]

  for (let i = 0; i < 50; i++) {
    const name = names[i] || `用户${i + 1}`
    const color = colors[i % colors.length].replace('#', '')

    // 优先使用UI Avatars，这个服务比较稳定
    const avatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=64&background=${color}&color=fff&font-size=0.33`
    avatars.push(avatar)
  }

  return avatars
})

const displayAvatars = computed(() => {
  return allAvatars.value.slice(0, maxCount.value)
})

const hasMoreMembers = computed(() => {
  return allAvatars.value.length > maxCount.value
})

const remainingCount = computed(() => {
  return Math.max(0, allAvatars.value.length - maxCount.value)
})

const totalMembers = computed(() => {
  return props.groupData.member_count || allAvatars.value.length
})

const onlineCount = computed(() => {
  return Math.floor(totalMembers.value * 0.3) // 30% 在线率
})

const activeToday = computed(() => {
  return Math.floor(totalMembers.value * 0.15) // 15% 今日活跃
})

const newMembers = computed(() => {
  return Math.floor(Math.random() * 10) + 1 // 随机新增成员
})

const recentMembers = computed(() => {
  const names = ['小明', '小红', '小李', '小王', '小张']
  const times = ['刚刚', '5分钟前', '10分钟前', '半小时前', '1小时前']
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', '96CEB4', 'FFEAA7']

  return names.slice(0, 3).map((name, index) => ({
    name,
    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=32&background=${colors[index]}&color=fff&font-size=0.33`,
    joinTime: times[index]
  }))
})

// 图片加载错误处理
const handleImageError = (event, index) => {
  console.warn(`头像加载失败: ${event.target.src}`)
  // 使用纯色背景作为fallback
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', '96CEB4', 'FFEAA7']
  const color = colors[index % colors.length]
  event.target.src = `https://ui-avatars.com/api/?name=用户${index + 1}&size=64&background=${color}&color=fff&font-size=0.33`
}

const handleRecentImageError = (event, index) => {
  console.warn(`最近成员头像加载失败: ${event.target.src}`)
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', '96CEB4', 'FFEAA7']
  const color = colors[index % colors.length]
  const names = ['小明', '小红', '小李', '小王', '小张']
  const name = names[index] || `用户${index + 1}`
  event.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=32&background=${color}&color=fff&font-size=0.33`
}

const handleImageLoad = (event) => {
  // 图片加载成功
  event.target.style.opacity = '1'
}

// 方法
const getAvatarStyle = (index) => {
  // 添加一些随机的动画延迟，让头像显示更生动
  return {
    animationDelay: `${index * 0.1}s`
  }
}
</script>

<style lang="scss" scoped>
.members-preview {
  .members-container {
    padding: 20px;
    
    .members-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .members-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #f56c6c;
        display: inline-block;
      }
      
      .member-count {
        font-size: 14px;
        color: #909399;
        background: #f5f7fa;
        padding: 4px 12px;
        border-radius: 12px;
      }
    }
    
    .members-content {
      .avatar-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 8px;
        margin-bottom: 20px;
        
        .avatar-item {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          border: 2px solid #e4e7ed;
          transition: all 0.3s;
          animation: fadeInUp 0.5s ease-out;
          
          &:hover {
            transform: scale(1.1);
            border-color: #409eff;
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .more-members {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #f5f7fa;
          border: 2px dashed #dcdfe6;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .more-overlay {
            .more-text {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }
          }
        }
      }
      
      .member-stats {
        display: flex;
        justify-content: space-around;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 20px;
        
        .stat-item {
          text-align: center;
          
          .stat-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }
      }
      
      .recent-joins {
        .recent-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 12px;
        }
        
        .recent-list {
          .recent-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            
            &:last-child {
              border-bottom: none;
            }
            
            .recent-avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
            }
            
            .recent-info {
              flex: 1;
              
              .recent-name {
                font-size: 13px;
                color: #303133;
                font-weight: 500;
              }
              
              .recent-time {
                font-size: 11px;
                color: #909399;
                margin-top: 2px;
              }
            }
          }
        }
      }
    }
    
    .members-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #909399;
      text-align: center;
      
      .placeholder-icon {
        font-size: 32px;
        margin-bottom: 12px;
      }
      
      .placeholder-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .placeholder-hint {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .members-preview {
    .members-container {
      .members-content {
        .avatar-grid {
          grid-template-columns: repeat(4, 1fr);
          
          .avatar-item {
            width: 35px;
            height: 35px;
          }
          
          .more-members {
            width: 35px;
            height: 35px;
          }
        }
      }
    }
  }
}
</style>
