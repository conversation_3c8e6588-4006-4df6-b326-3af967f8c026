<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理后台预览</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background-size: 400% 400%;
      animation: gradient 15s ease infinite;
      position: relative;
      overflow: hidden;
    }
    
    @keyframes gradient {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
    
    .particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }
    
    .particle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      animation: float 8s infinite ease-in-out;
    }
    
    @keyframes float {
      0%, 100% {
        transform: translateY(0) translateX(0);
      }
      25% {
        transform: translateY(-20px) translateX(10px);
      }
      50% {
        transform: translateY(-35px) translateX(-15px);
      }
      75% {
        transform: translateY(-15px) translateX(25px);
      }
    }
    
    .login-container {
      position: relative;
      z-index: 2;
      display: flex;
      width: 900px;
      max-width: 95%;
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      backdrop-filter: blur(10px);
    }
    
    .login-image {
      flex: 1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
    
    .login-image::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }
    
    .login-image-text {
      position: relative;
      color: white;
      text-align: center;
      padding: 0 30px;
    }
    
    .login-image-text h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    
    .login-image-text p {
      font-size: 1.1rem;
      line-height: 1.6;
      font-weight: 300;
      text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    }
    
    .login-form {
      flex: 1;
      padding: 50px 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .login-header h1 {
      color: #2d3748;
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 10px;
    }
    
    .login-header p {
      color: #718096;
      font-size: 1rem;
    }
    
    .login-content {
      margin-bottom: 30px;
    }
    
    .login-content p {
      color: #4a5568;
      line-height: 1.7;
      margin-bottom: 15px;
      font-size: 1rem;
    }
    
    .preview-button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 15px 25px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    
    .preview-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 7px 20px rgba(102, 126, 234, 0.6);
    }
    
    .preview-button:active {
      transform: translateY(1px);
    }
    
    .preview-button .icon {
      margin-right: 10px;
      font-size: 1.2rem;
    }
    
    .note {
      margin-top: 25px;
      color: #718096;
      font-size: 0.9rem;
      text-align: center;
      line-height: 1.6;
      padding: 15px;
      background-color: rgba(237, 242, 247, 0.8);
      border-radius: 8px;
      border-left: 4px solid #667eea;
    }
    
    .note .icon {
      margin-right: 8px;
      color: #667eea;
    }
    
    @media (max-width: 768px) {
      .login-container {
        flex-direction: column;
        width: 95%;
      }
      
      .login-image {
        height: 200px;
        min-height: 200px;
      }
      
      .login-form {
        padding: 30px 20px;
      }
      
      .login-header h1 {
        font-size: 1.8rem;
      }
      
      .login-image-text h2 {
        font-size: 2rem;
      }
    }
    
    /* 加载动画 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading {
      animation: spin 1s linear infinite;
    }
  </style>
</head>
<body>
  <!-- 背景粒子效果 -->
  <div class="particles" id="particles"></div>
  
  <!-- 登录容器 -->
  <div class="login-container">
    <!-- 左侧图片区域 -->
    <div class="login-image">
      <div class="login-image-text">
        <h2>欢迎使用管理系统</h2>
        <p>这是一个功能强大的后台管理系统，为您提供全面的数据管理和分析能力。</p>
      </div>
    </div>
    
    <!-- 右侧登录表单区域 -->
    <div class="login-form">
      <div class="login-header">
        <h1>管理后台预览</h1>
        <p>体验系统功能，无需真实登录</p>
      </div>
      
      <div class="login-content">
        <p>点击下方按钮将模拟登录状态，直接进入管理后台预览。</p>
        <p>这是一个临时的预览模式，仅用于查看界面效果，不会影响实际数据。</p>
      </div>
      
      <button class="preview-button" id="previewButton">
        <span class="icon">🚀</span> 进入预览模式
      </button>
      
      <p class="note">
        <span class="icon">ℹ️</span> 注意：此模式下部分功能可能无法正常工作，因为没有真实的登录凭证。
      </p>
    </div>
  </div>

  <script>
    // 创建背景粒子效果
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      const particleCount = 15;
      
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.classList.add('particle');
        
        // 随机大小
        const size = Math.random() * 12 + 4;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        
        // 随机位置
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        
        // 随机动画延迟
        particle.style.animationDelay = `${Math.random() * 5}s`;
        
        // 随机透明度
        particle.style.opacity = Math.random() * 0.4 + 0.1;
        
        particlesContainer.appendChild(particle);
      }
    }
    
    // 页面加载完成后创建粒子
    document.addEventListener('DOMContentLoaded', function() {
      createParticles();
      
      // 登录按钮点击事件
      document.getElementById('previewButton').addEventListener('click', function() {
        // 设置一个模拟的token到Cookie中
        document.cookie = "Admin-Token=preview_mock_token_for_demo; path=/; max-age=86400";
        
        // 创建一个模拟的用户信息对象
        const mockUserInfo = {
          id: 1,
          username: 'admin',
          nickname: '预览账号',
          avatar: '',
          role: 'admin',
          roles: ['admin'],
          permissions: ['*']
        };
        
        // 将用户信息存储到localStorage
        localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
        
        // 显示加载状态
        this.innerHTML = '<span class="icon loading">⏳</span> 正在进入...';
        
        // 延迟跳转，展示加载效果
        setTimeout(() => {
          // 重定向到仪表盘页面 - 预览模式下使用根路径
          window.location.href = '/dashboard';
        }, 1000);
      });
    });
  </script>
</body>
</html>