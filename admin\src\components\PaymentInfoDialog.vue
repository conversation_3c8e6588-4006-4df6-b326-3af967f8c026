<template>
  <el-dialog
    v-model="visible"
    title="支付信息"
    width="700px"
    :before-close="handleClose"
  >
    <div class="payment-info">
      <el-descriptions title="订单信息" :column="2" border>
        <el-descriptions-item label="订单号">{{ orderData.order_no || 'N/A' }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusType(orderData.status)">{{ getStatusText(orderData.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额">
          <span class="amount">¥{{ orderData.amount || '0.00' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(orderData.created_at) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">支付详情</el-divider>
      
      <div v-if="orderData.status === 'paid'" class="payment-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付方式">
            <el-tag :type="getPaymentMethodType(orderData.payment_method)">
              {{ getPaymentMethodText(orderData.payment_method) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ formatDate(orderData.paid_at) }}</el-descriptions-item>
          <el-descriptions-item label="交易流水号">{{ orderData.transaction_id || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag type="success">支付成功</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="payment-timeline">
          <h4>支付流程</h4>
          <el-timeline>
            <el-timeline-item
              timestamp="订单创建"
              :time="formatDate(orderData.created_at)"
              type="primary"
            >
              用户创建订单，等待支付
            </el-timeline-item>
            <el-timeline-item
              timestamp="支付完成"
              :time="formatDate(orderData.paid_at)"
              type="success"
            >
              用户完成支付，订单状态更新为已支付
            </el-timeline-item>
            <el-timeline-item
              timestamp="订单处理"
              :time="formatDate(orderData.updated_at)"
              type="info"
            >
              系统处理订单，准备发货
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <div v-else-if="orderData.status === 'pending'" class="pending-payment">
        <el-alert
          title="订单待支付"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>该订单尚未完成支付，请提醒用户及时支付。</p>
            <div class="payment-actions">
              <el-button type="primary" size="small" @click="sendPaymentReminder">
                发送支付提醒
              </el-button>
              <el-button type="warning" size="small" @click="cancelOrder">
                取消订单
              </el-button>
            </div>
          </template>
        </el-alert>
        
        <div class="qr-code-section" v-if="paymentQRCode">
          <h4>支付二维码</h4>
          <div class="qr-code">
            <img :src="paymentQRCode" alt="支付二维码" />
            <p>用户可扫描此二维码完成支付</p>
          </div>
        </div>
      </div>
      
      <div v-else-if="orderData.status === 'cancelled'" class="cancelled-order">
        <el-alert
          title="订单已取消"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>该订单已被取消，无法进行支付操作。</p>
            <p><strong>取消时间：</strong>{{ formatDate(orderData.cancelled_at) }}</p>
            <p><strong>取消原因：</strong>{{ orderData.cancel_reason || '用户主动取消' }}</p>
          </template>
        </el-alert>
      </div>
      
      <div v-else-if="orderData.status === 'refunded'" class="refunded-order">
        <el-alert
          title="订单已退款"
          type="danger"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>该订单已完成退款处理。</p>
            <p><strong>退款时间：</strong>{{ formatDate(orderData.refunded_at) }}</p>
            <p><strong>退款金额：</strong>¥{{ orderData.refund_amount || orderData.amount }}</p>
            <p><strong>退款原因：</strong>{{ orderData.refund_reason || '用户申请退款' }}</p>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshPaymentStatus" v-if="orderData.status === 'pending'">
          刷新支付状态
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'refresh'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟支付二维码
const paymentQRCode = ref('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'cancelled': 'info',
    'refunded': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || '未知状态'
}

const getPaymentMethodType = (method) => {
  const typeMap = {
    'wechat': 'success',
    'alipay': 'primary',
    'qqpay': 'warning',
    'bank': 'info'
  }
  return typeMap[method] || 'info'
}

const getPaymentMethodText = (method) => {
  const textMap = {
    'wechat': '微信支付',
    'alipay': '支付宝',
    'qqpay': 'QQ钱包',
    'bank': '银行卡'
  }
  return textMap[method] || '未知支付方式'
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString('zh-CN')
}

const sendPaymentReminder = async () => {
  try {
    await ElMessageBox.confirm('确定要发送支付提醒吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('支付提醒已发送')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('发送提醒失败')
    }
  }
}

const cancelOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('订单已取消')
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败')
    }
  }
}

const refreshPaymentStatus = async () => {
  try {
    ElMessage.success('支付状态已刷新')
    emit('refresh')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}
</script>

<style scoped>
.payment-info {
  padding: 20px 0;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
  font-size: 16px;
}

.payment-details {
  margin-top: 20px;
}

.payment-timeline {
  margin-top: 30px;
}

.payment-timeline h4 {
  margin-bottom: 15px;
  color: #333;
}

.pending-payment {
  margin-top: 20px;
}

.payment-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.qr-code-section {
  margin-top: 30px;
  text-align: center;
}

.qr-code-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.qr-code img {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.qr-code p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.cancelled-order,
.refunded-order {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>