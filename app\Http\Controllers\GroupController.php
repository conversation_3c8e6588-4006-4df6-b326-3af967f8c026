<?php

namespace App\Http\Controllers;

use App\Models\WechatGroup;
use App\Models\GroupAccessLog;
use App\Services\GroupAccessValidationService;
use App\Services\BrowserDetectionService;
use App\Services\IPLocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 群组前端访问控制器
 * 处理群组的前端访问、验证和展示
 */
class GroupController extends Controller
{
    private GroupAccessValidationService $validationService;
    private BrowserDetectionService $browserService;
    private IPLocationService $ipLocationService;

    public function __construct(
        GroupAccessValidationService $validationService,
        BrowserDetectionService $browserService,
        IPLocationService $ipLocationService
    ) {
        $this->validationService = $validationService;
        $this->browserService = $browserService;
        $this->ipLocationService = $ipLocationService;
    }

    /**
     * 群组访问入口（带防封验证）
     */
    public function index(Request $request, $id)
    {
        try {
            // 获取访问域名
            $domain = $request->getHost();
            
            // 验证访问权限
            $validation = $this->validationService->validateGroupAccess($id, $domain);
            
            if (!$validation['valid']) {
                // 生成错误跳转页面
                return response($this->browserService->generateBrowserGuidePage(null))
                        ->header('Content-Type', 'text/html');
            }
            
            $group = $validation['group'];
            
            // 检查微信浏览器访问控制
            if ($group->wx_accessible == 2 && $this->browserService->isWechatBrowser()) {
                // 生成浏览器引导页面
                $targetUrl = route('group.show', ['id' => $group->id]);
                return response($this->browserService->generateBrowserGuidePage($targetUrl, $group->title))
                        ->header('Content-Type', 'text/html');
            }
            
            // 生成安全访问链接并跳转
            $secureLink = $this->validationService->generateSecureGroupLink($group);
            
            return redirect($secureLink);

        } catch (\Exception $e) {
            Log::error('群组访问入口异常', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // 返回通用错误页面
            $fallbackUrl = 'https://www.baidu.com/s?wd=' . urlencode('系统维护中');
            return response($this->browserService->generateBrowserGuidePage($fallbackUrl))
                    ->header('Content-Type', 'text/html');
        }
    }

    /**
     * 群组展示页面
     */
    public function show(Request $request, $id)
    {
        try {
            // 验证访问令牌
            $token = $request->input('t');
            $timestamp = $request->input('time');
            
            if ($token && $timestamp) {
                if (!$this->validationService->validateAccessToken($id, $token, (int)$timestamp)) {
                    return $this->redirectToError('访问链接已过期');
                }
            }

            // 再次验证群组访问权限
            $validation = $this->validationService->validateGroupAccess($id, $request->getHost());
            
            if (!$validation['valid']) {
                return $this->redirectToError($validation['message']);
            }

            $group = $validation['group'];
            
            // 增加浏览次数
            $group->increment('view_count');
            $group->increment('today_views');
            $group->update(['last_activity_at' => now()]);

            // 获取城市信息用于标题替换
            $userCity = $validation['detected_city'];
            $displayTitle = $group->getCityReplacedTitle($userCity);

            // 生成虚拟成员数据
            $virtualMembers = $group->generateVirtualMembers();
            
            // 生成虚拟群友评价
            $virtualReviews = $group->generateVirtualReviews();

            // 准备页面数据
            $pageData = [
                'group' => $group,
                'display_title' => $displayTitle,
                'original_title' => $group->title,
                'user_city' => $userCity,
                'has_city_placeholder' => strpos($group->title, 'xxx') !== false,
                'virtual_members' => $virtualMembers,
                'virtual_reviews' => $virtualReviews,
                'formatted_faq' => $group->formatted_faq_content,
                'browser_info' => $validation['browser_info'],
                'payment_methods' => $validation['payment_channels'],
                'stats' => [
                    'view_count' => $group->view_count,
                    'like_count' => $group->like_count,
                    'want_see_count' => $group->want_see_count,
                    'virtual_stats' => $group->virtual_stats,
                ],
            ];

            // 根据展示类型返回不同的视图
            $viewName = $group->display_type == 2 ? 'group.show_image' : 'group.show';
            
            return view($viewName, $pageData);

        } catch (\Exception $e) {
            Log::error('群组展示页面异常', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->redirectToError('页面加载失败');
        }
    }

    /**
     * 群组二维码展示
     */
    public function qrcode(Request $request, $id)
    {
        try {
            $group = WechatGroup::find($id);
            
            if (!$group) {
                return response('群组不存在', 404);
            }

            // 生成群组访问链接
            $groupUrl = route('group.index', ['id' => $group->id]);
            
            // 生成二维码
            $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=290x290&data=' . urlencode($groupUrl);
            
            return response()->view('group.qrcode', [
                'group' => $group,
                'qr_code_url' => $qrCodeUrl,
                'group_url' => $groupUrl,
            ]);

        } catch (\Exception $e) {
            return response('二维码生成失败', 500);
        }
    }

    /**
     * 客服页面
     */
    public function kefu(Request $request, $id)
    {
        try {
            $group = WechatGroup::find($id);
            
            if (!$group) {
                return response('群组不存在', 404);
            }

            return view('group.kefu', [
                'group' => $group,
                'customer_service_qr' => $group->customer_service_qr,
                'customer_service_title' => $group->customer_service_title ?? 'VIP专属客服',
                'customer_service_desc' => $group->customer_service_desc ?? '如有问题请联系客服',
            ]);

        } catch (\Exception $e) {
            return response('客服页面加载失败', 500);
        }
    }

    /**
     * 支付成功页面
     */
    public function success(Request $request, $id)
    {
        try {
            $group = WechatGroup::find($id);
            
            if (!$group) {
                return response('群组不存在', 404);
            }

            // 检查订单状态
            $orderNo = session('orderid');
            if ($orderNo) {
                $order = \App\Models\Order::where('order_no', $orderNo)
                                         ->where('wechat_group_id', $id)
                                         ->first();
                
                if ($order && $order->isPaid()) {
                    return view('group.success', [
                        'group' => $group,
                        'order' => $order,
                    ]);
                }
            }

            // 如果没有有效订单，重定向到群组页面
            return redirect()->route('group.show', ['id' => $id]);

        } catch (\Exception $e) {
            return $this->redirectToError('页面加载失败');
        }
    }

    /**
     * 获取群组访问统计API
     */
    public function getAccessStats(Request $request, $id)
    {
        try {
            $days = $request->input('days', 7);
            $stats = $this->validationService->getGroupAccessStats($id, $days);
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取地域访问分布API
     */
    public function getGeographicDistribution(Request $request, $id)
    {
        try {
            $days = $request->input('days', 7);
            $distribution = $this->validationService->getGeographicDistribution($id, $days);
            
            return response()->json([
                'success' => true,
                'data' => $distribution
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取地域分布失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证群组访问API
     */
    public function validateAccess(Request $request, $id)
    {
        try {
            $validation = $this->validationService->validateGroupAccess($id);
            
            if ($validation['valid']) {
                return response()->json([
                    'status' => 'success',
                    'message' => '访问验证通过',
                    'data' => [
                        'group_id' => $id,
                        'detected_city' => $validation['detected_city'],
                        'browser_type' => $validation['browser_info']['type'],
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $validation['message'],
                    'redirect' => $validation['redirect_url']
                ], 403);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '验证失败',
                'redirect' => 'https://www.baidu.com/s?wd=' . urlencode('系统错误')
            ], 500);
        }
    }

    /**
     * 重定向到错误页面
     */
    private function redirectToError(string $message): \Illuminate\Http\Response
    {
        $fallbackUrl = 'https://www.baidu.com/s?wd=' . urlencode($message);
        return response($this->browserService->generateBrowserGuidePage($fallbackUrl))
                ->header('Content-Type', 'text/html');
    }
}