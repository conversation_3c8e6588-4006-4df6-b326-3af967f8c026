<?php

namespace App\Events;

use App\Models\WechatGroup;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 内容性能告警事件
 * 当检测到内容性能异常时触发
 */
class ContentPerformanceAlert implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly WechatGroup $group,
        public readonly array $alert
    ) {}

    /**
     * 获取事件应该广播的频道
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->group->user_id),
            new PrivateChannel('group.' . $this->group->id),
        ];
    }

    /**
     * 广播事件名称
     */
    public function broadcastAs(): string
    {
        return 'content.performance.alert';
    }

    /**
     * 广播数据
     */
    public function broadcastWith(): array
    {
        return [
            'alert_id' => $this->alert['id'],
            'group' => [
                'id' => $this->group->id,
                'title' => $this->group->title,
                'category' => $this->group->category,
            ],
            'alert' => [
                'type' => $this->alert['type'],
                'severity' => $this->alert['severity'],
                'title' => $this->alert['title'],
                'message' => $this->alert['message'],
                'suggested_actions' => $this->alert['suggested_actions'],
                'created_at' => $this->alert['created_at'],
            ],
            'timestamp' => now()->toISOString(),
        ];
    }
}