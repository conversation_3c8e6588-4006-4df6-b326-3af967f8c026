import{_ as e,u as a}from"./index-DtXAftX0.js";/* empty css                  *//* empty css                    */import{T as l,aw as t,c5 as s,bQ as i,bA as u,ay as r,Q as o}from"./element-plus-h2SQQM64.js";import{r as m,c as d,d as n,k as p,l as c,E as v,z as f,u as _,t as g}from"./vue-vendor-Dy164gUc.js";const y={class:"image-upload"},V=["src"],b={class:"el-upload-list__item-actions"},k=["onClick"],x=["onClick"],z=["src"],h=e({__name:"ImageUpload",props:{modelValue:{type:[String,Array],default:""},limit:{type:Number,default:1},accept:{type:String,default:"image/*"},maxSize:{type:Number,default:5}},emits:["update:modelValue"],setup(e,{emit:h}){const j=e,A=h,I=a(),S=m([]),w=m(!1),B=m(""),C=d(()=>"/api/v1/admin/upload/image"),U=d(()=>({Authorization:`Bearer ${I.token}`}));n(()=>j.modelValue,e=>{e?Array.isArray(e)?S.value=e.map((e,a)=>({name:`image-${a}`,url:e,uid:a})):S.value=[{name:"image",url:e,uid:1}]:S.value=[]},{immediate:!0});const $=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<j.maxSize;return a?!!l||(o.error(`图片大小不能超过 ${j.maxSize}MB!`),!1):(o.error("只能上传图片文件!"),!1)},N=(e,a)=>{200===e.code?(a.url=e.data.url,L(),o.success("上传成功")):o.error(e.message||"上传失败")},Q=e=>{console.error("上传失败:",e),o.error("上传失败")},E=e=>{const a=S.value.findIndex(a=>a.uid===e.uid);a>-1&&(S.value.splice(a,1),L())},L=()=>{const e=S.value.map(e=>e.url).filter(Boolean);1===j.limit?A("update:modelValue",e[0]||""):A("update:modelValue",e)};return(a,o)=>{const m=l,d=u,n=r;return c(),p("div",y,[v(d,{"file-list":S.value,"onUpdate:fileList":o[0]||(o[0]=e=>S.value=e),action:C.value,headers:U.value,"before-upload":$,"on-success":N,"on-error":Q,"on-remove":E,limit:e.limit,accept:e.accept,"list-type":"picture-card","auto-upload":!0},{file:f(({file:e})=>[g("div",null,[g("img",{class:"el-upload-list__item-thumbnail",src:e.url,alt:""},null,8,V),g("span",b,[g("span",{class:"el-upload-list__item-preview",onClick:a=>(e=>{B.value=e.url,w.value=!0})(e)},[v(m,null,{default:f(()=>[v(_(s))]),_:1})],8,k),g("span",{class:"el-upload-list__item-delete",onClick:a=>E(e)},[v(m,null,{default:f(()=>[v(_(i))]),_:1})],8,x)])])]),default:f(()=>[v(m,null,{default:f(()=>[v(_(t))]),_:1})]),_:1},8,["file-list","action","headers","limit","accept"]),v(n,{modelValue:w.value,"onUpdate:modelValue":o[1]||(o[1]=e=>w.value=e),title:"图片预览"},{default:f(()=>[g("img",{"w-full":"",src:B.value,alt:"Preview Image"},null,8,z)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b5106737"]]);export{h as I};
