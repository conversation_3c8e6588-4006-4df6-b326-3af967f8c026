<template>
  <div class="popular-groups-card">
    <div class="card-header">
      <h3 class="card-title">热门群组排行</h3>
      <el-button text @click="viewAll">
        查看全部
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <div class="groups-ranking">
      <div 
        v-for="(group, index) in groups" 
        :key="group.id"
        class="ranking-item"
        @click="viewDetail(group.id)"
      >
        <div class="ranking-number" :class="{ 'top-three': index < 3 }">
          {{ index + 1 }}
        </div>
        <div class="group-info">
          <div class="group-name">{{ group.name }}</div>
          <div class="group-stats">
            <span class="member-count">{{ group.memberCount }}人</span>
            <span class="separator">·</span>
            <span class="revenue">¥{{ group.revenue.toLocaleString() }}</span>
          </div>
        </div>
        <div class="group-trend" :class="getTrendClass(group.trend)">
          <span class="trend-value">{{ group.trend }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  groups: {
    type: Array,
    default: () => []
  }
})

const viewAll = () => {
  ElMessage.info('跳转到群组列表页面')
}

const viewDetail = (groupId) => {
  ElMessage.info(`查看群组详情: ${groupId}`)
}

const getTrendClass = (trend) => {
  if (trend.startsWith('+')) return 'positive'
  if (trend.startsWith('-')) return 'negative'
  return 'neutral'
}
</script>

<style lang="scss" scoped>
.popular-groups-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .groups-ranking {
    .ranking-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f9fafb;
        border-radius: 12px;
        padding: 16px 12px;
        margin: 0 -12px;
      }

      .ranking-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 14px;
        background: #f3f4f6;
        color: #6b7280;

        &.top-three {
          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
          color: white;
        }
      }

      .group-info {
        flex: 1;

        .group-name {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .group-stats {
          font-size: 12px;
          color: #6b7280;

          .separator {
            margin: 0 8px;
          }

          .revenue {
            font-weight: 600;
            color: #10b981;
          }
        }
      }

      .group-trend {
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;

        &.positive {
          background: #dcfce7;
          color: #166534;
        }

        &.negative {
          background: #fee2e2;
          color: #991b1b;
        }

        &.neutral {
          background: #f3f4f6;
          color: #6b7280;
        }
      }
    }
  }
}
</style>