<template>
  <div class="group-add-enhanced">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" circle />
        <div class="header-info">
          <h1 class="page-title">创建群组</h1>
          <p class="page-subtitle">快速创建包含丰富多媒体内容的群组落地页</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="handlePreview" :icon="View" type="primary" plain>
          实时预览
        </el-button>
        <el-button @click="handleSave" :icon="Check" type="primary" :loading="saving">
          保存群组
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧表单区域 -->
      <div class="form-section">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          
          <!-- 基础信息卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>基础信息</span>
              </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组名称" prop="title">
                  <el-input 
                    v-model="form.title" 
                    placeholder="请输入群组名称"
                    @input="updatePreview"
                  />
                  <div class="form-tip">
                    💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="群组价格" prop="price">
                  <el-input-number 
                    v-model="form.price" 
                    :min="0" 
                    :precision="2"
                    style="width: 100%"
                    @change="updatePreview"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="群组描述">
              <el-input 
                v-model="form.description" 
                type="textarea" 
                :rows="3"
                placeholder="请输入群组描述"
                @input="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 多媒体内容卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Picture /></el-icon>
                <span>多媒体内容</span>
              </div>
            </template>

            <!-- 顶部海报 -->
            <el-form-item label="顶部海报">
              <MediaUploader
                v-model="form.banner_image"
                type="image"
                :limit="1"
                accept="image/*"
                @change="updatePreview"
              >
                <template #tip>
                  <div class="upload-tip">
                    建议尺寸：750x400px，支持JPG、PNG格式
                  </div>
                </template>
              </MediaUploader>
            </el-form-item>

            <!-- 群组头像 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="群组头像">
                  <MediaUploader
                    v-model="form.avatar"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">建议尺寸：200x200px</div>
                    </template>
                  </MediaUploader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二维码">
                  <MediaUploader
                    v-model="form.qr_code"
                    type="image"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                    @change="updatePreview"
                  >
                    <template #tip>
                      <div class="upload-tip">群组二维码图片</div>
                    </template>
                  </MediaUploader>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 虚拟成员头像库 -->
            <el-form-item label="成员头像库">
              <AvatarLibrarySelector
                v-model="form.avatar_library"
                @change="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 内容编辑卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>内容编辑</span>
              </div>
            </template>

            <!-- 富文本内容 -->
            <el-form-item label="详细介绍">
              <ModernRichTextEditor
                v-model="form.rich_content"
                :height="300"
                placeholder="请输入群组的详细介绍，支持富文本格式..."
                :max-length="5000"
                @change="updatePreview"
              />
            </el-form-item>

            <!-- 多图片展示 -->
            <el-form-item label="展示图片">
              <MediaUploader
                v-model="form.gallery_images"
                type="image"
                :limit="9"
                accept="image/*"
                multiple
                @change="updatePreview"
              >
                <template #tip>
                  <div class="upload-tip">
                    最多上传9张图片，支持拖拽排序
                  </div>
                </template>
              </MediaUploader>
            </el-form-item>

            <!-- 视频内容 -->
            <el-form-item label="介绍视频">
              <VideoUploader
                v-model="form.intro_video"
                @change="updatePreview"
              />
            </el-form-item>
          </el-card>

          <!-- 布局设置卡片 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Grid /></el-icon>
                <span>布局设置</span>
              </div>
            </template>

            <LayoutDesigner
              v-model="form.layout_config"
              @change="updatePreview"
            />
          </el-card>

          <!-- 城市定位设置 -->
          <el-card class="form-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon><Location /></el-icon>
                <span>城市定位</span>
              </div>
            </template>

            <el-form-item>
              <el-switch
                v-model="form.auto_city_replace"
                :active-value="1"
                :inactive-value="0"
                @change="handleCityToggle"
              />
              <span class="switch-label">启用城市定位功能</span>
            </el-form-item>

            <template v-if="form.auto_city_replace === 1">
              <el-form-item label="插入策略">
                <el-select v-model="form.city_insert_strategy" style="width: 100%">
                  <el-option label="智能判断（推荐）" value="auto" />
                  <el-option label="前缀模式（城市·标题）" value="prefix" />
                  <el-option label="后缀模式（标题·城市）" value="suffix" />
                  <el-option label="自然插入（智能融入）" value="natural" />
                </el-select>
              </el-form-item>

              <el-form-item label="测试效果">
                <div class="test-tip">
                  <el-icon><InfoFilled /></el-icon>
                  <span>此处仅用于测试城市替换效果，实际用户访问落地页时会自动根据其IP获取真实城市</span>
                </div>
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-input v-model="testCity" placeholder="输入测试城市" />
                  </el-col>
                  <el-col :span="8">
                    <el-button @click="testCityReplacement" size="small">测试替换效果</el-button>
                  </el-col>
                  <el-col :span="8">
                    <span v-if="testResult" class="test-result">{{ testResult }}</span>
                  </el-col>
                </el-row>
              </el-form-item>
            </template>
          </el-card>
        </el-form>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-section">
        <div class="preview-container">
          <div class="preview-header">
            <span>实时预览</span>
            <el-button @click="refreshPreview" :icon="RefreshRight" size="small" circle />
          </div>
          <div class="preview-content">
            <LandingPagePreview
              :group-data="previewData"
              :layout-config="form.layout_config"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <PreviewDialog
      v-model="previewVisible"
      :group-data="previewData"
      :layout-config="form.layout_config"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  ArrowLeft, Check, View, Setting, Picture, Document, Grid, Location,
  InfoFilled, RefreshRight
} from '@element-plus/icons-vue'

// 组件导入
import MediaUploader from '@/components/MediaUploader.vue'
import VideoUploader from '@/components/VideoUploader.vue'
import AvatarLibrarySelector from '@/components/AvatarLibrarySelector.vue'
import ModernRichTextEditor from '@/components/ModernRichTextEditor.vue'
import LayoutDesigner from '@/components/LayoutDesigner.vue'
import LandingPagePreview from '@/components/LandingPagePreview.vue'
import PreviewDialog from '@/components/PreviewDialog.vue'

const router = useRouter()
const formRef = ref()
const saving = ref(false)
const previewVisible = ref(false)
const testCity = ref('北京')
const testResult = ref('')

// 表单数据
const form = reactive({
  title: '',
  price: 0,
  description: '',
  banner_image: '',
  avatar: '',
  qr_code: '',
  avatar_library: 'default',
  rich_content: '',
  gallery_images: [],
  intro_video: '',
  layout_config: {
    sections: [
      { id: 'banner', name: '顶部海报', visible: true, order: 1 },
      { id: 'info', name: '基础信息', visible: true, order: 2 },
      { id: 'content', name: '详细介绍', visible: true, order: 3 },
      { id: 'gallery', name: '图片展示', visible: true, order: 4 },
      { id: 'video', name: '介绍视频', visible: true, order: 5 },
      { id: 'members', name: '成员展示', visible: true, order: 6 },
      { id: 'qrcode', name: '二维码', visible: true, order: 7 }
    ]
  },
  auto_city_replace: 0,
  city_insert_strategy: 'auto'
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ]
}

// 预览数据
const previewData = computed(() => {
  return {
    ...form,
    title: processedTitle.value,
    formatted_price: form.price === 0 ? '免费' : `¥${form.price}`
  }
})

// 城市替换后的标题
const processedTitle = computed(() => {
  if (!form.title) return ''
  
  if (form.auto_city_replace !== 1) {
    return form.title
  }
  
  const currentCity = testCity.value
  let result = form.title
  
  switch (form.city_insert_strategy) {
    case 'prefix':
      const cleanTitle = form.title.replace(/^xxx/, '')
      result = currentCity + '·' + cleanTitle
      break
    case 'suffix':
      const baseTitleSuffix = form.title.replace(/xxx/, '')
      result = baseTitleSuffix + '·' + currentCity
      break
    case 'natural':
      result = form.title.replace(/xxx/g, currentCity)
      break
    case 'auto':
      if (form.title.includes('xxx')) {
        result = form.title.replace(/xxx/g, currentCity)
      } else {
        result = currentCity + '·' + form.title
      }
      break
    default:
      result = form.title.replace(/xxx/g, currentCity)
  }
  
  return result
})

// 方法
const goBack = () => {
  router.go(-1)
}

const updatePreview = () => {
  // 实时更新预览
}

const refreshPreview = () => {
  // 刷新预览
}

const handlePreview = () => {
  previewVisible.value = true
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    saving.value = true
    
    // 保存逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('群组创建成功')
    router.push('/community')
  } catch (error) {
    ElMessage.error('保存失败，请检查表单')
  } finally {
    saving.value = false
  }
}

const handleCityToggle = (value) => {
  if (value) {
    if (!form.title.includes('xxx')) {
      ElMessage.info('建议在群组名称中使用"xxx"作为城市占位符')
    }
  }
}

const testCityReplacement = () => {
  if (!form.title) {
    ElMessage.warning('请先输入群组名称')
    return
  }
  
  testResult.value = processedTitle.value
  ElMessage.success(`城市替换效果：${processedTitle.value}`)
}

onMounted(() => {
  // 初始化
})
</script>

<style lang="scss" scoped>
.group-add-enhanced {
  min-height: 100vh;
  background: #f5f7fa;

  .page-header {
    background: white;
    padding: 20px 24px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 4px 0;
        }

        .page-subtitle {
          font-size: 14px;
          color: #909399;
          margin: 0;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .main-content {
    display: flex;
    gap: 24px;
    padding: 24px;
    max-width: 1600px;
    margin: 0 auto;

    .form-section {
      flex: 1;
      max-width: 800px;

      .form-card {
        margin-bottom: 24px;
        border-radius: 12px;
        border: 1px solid #e4e7ed;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;
        }

        :deep(.el-card__body) {
          padding: 24px;
        }
      }

      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        line-height: 1.4;
      }

      .test-tip {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        padding: 12px 16px;
        background: #fff7ed;
        border: 1px solid #fed7aa;
        border-radius: 8px;
        font-size: 13px;
        color: #9a3412;

        .el-icon {
          color: #ea580c;
          font-size: 16px;
        }
      }

      .test-result {
        color: #67c23a;
        font-weight: 500;
        font-size: 14px;
      }

      .switch-label {
        margin-left: 12px;
        font-size: 14px;
        color: #606266;
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
        text-align: center;
      }
    }

    .preview-section {
      width: 400px;
      position: sticky;
      top: 100px;
      height: fit-content;

      .preview-container {
        background: white;
        border-radius: 12px;
        border: 1px solid #e4e7ed;
        overflow: hidden;

        .preview-header {
          padding: 16px 20px;
          border-bottom: 1px solid #e4e7ed;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: 600;
          color: #303133;
        }

        .preview-content {
          padding: 20px;
          max-height: 600px;
          overflow-y: auto;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .group-add-enhanced {
    .main-content {
      flex-direction: column;

      .preview-section {
        width: 100%;
        position: static;
      }
    }
  }
}
</style>
