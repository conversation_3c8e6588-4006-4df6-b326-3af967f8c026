<template>
  <div class="app-container">
    <PageLayout>
      <template #header>
        <div class="page-header">
          <h1>
            <el-icon><MagicStick /></el-icon>
            活动与营销管理
          </h1>
          <p>创建、管理并推广您的社群活动，提升用户参与度和活跃度。</p>
        </div>
      </template>

      <div class="toolbar-container">
        <el-button type="primary" icon="Plus" @click="handleCreateEvent">
          创建新活动
        </el-button>
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="event-tabs">
          <el-tab-pane label="全部活动" name="all"></el-tab-pane>
          <el-tab-pane label="进行中" name="ongoing"></el-tab-pane>
          <el-tab-pane label="未开始" name="upcoming"></el-tab-pane>
          <el-tab-pane label="已结束" name="finished"></el-tab-pane>
        </el-tabs>
      </div>

      <div class="event-list-container">
        <el-row :gutter="20">
          <el-col :span="8" v-for="event in filteredEvents" :key="event.id" class="event-col">
            <el-card class="event-card" shadow="hover">
              <div class="event-image-wrapper">
                <img :src="event.cover_image" class="event-image" />
                <el-tag class="event-status-tag" :type="getEventStatus(event).type">{{ getEventStatus(event).text }}</el-tag>
              </div>
              <div class="event-info">
                <h3 class="event-title">{{ event.name }}</h3>
                <p class="event-time">
                  <el-icon><Clock /></el-icon> {{ formatDate(event.start_time) }} - {{ formatDate(event.end_time) }}
                </p>
                <div class="event-stats">
                  <span><el-icon><User /></el-icon> {{ event.participants_count }} / {{ event.max_participants }} 人</span>
                  <span><el-icon><Money /></el-icon> ¥{{ event.fee }}</span>
                </div>
              </div>
              <div class="event-actions">
                <el-button-group>
                  <el-button type="primary" size="small" @click="handleEditEvent(event)">编辑</el-button>
                  <el-button type="success" size="small" @click="handleViewData(event)">数据</el-button>
                </el-button-group>
                <el-dropdown @command="handleMoreCommand">
                   <el-button size="small">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'poster', event: event}">
                        <el-icon><Picture /></el-icon>生成海报
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'copy', event: event}">
                        <el-icon><DocumentCopy /></el-icon>复制活动
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', event: event}" divided>
                        <el-icon><Delete /></el-icon>删除活动
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-empty v-if="filteredEvents.length === 0" description="暂无活动" />
      </div>
    </PageLayout>

    <!-- 创建/编辑活动对话框 -->
    <EventEditDialog
      v-model="dialogVisible"
      :event-data="currentEvent"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { MagicStick, Plus, Clock, User, Money, ArrowDown, Picture, DocumentCopy, Delete } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import EventEditDialog from './components/EventEditDialog.vue' // 需要创建的子组件
import { formatDate } from '@/utils/format'

const loading = ref(false)
const dialogVisible = ref(false)
const activeTab = ref('all')
const events = ref([])
const currentEvent = ref(null)

const filteredEvents = computed(() => {
  const now = new Date()
  if (activeTab.value === 'all') {
    return events.value
  }
  return events.value.filter(event => {
    const start = new Date(event.start_time)
    const end = new Date(event.end_time)
    if (activeTab.value === 'ongoing') return start <= now && end >= now
    if (activeTab.value === 'upcoming') return start > now
    if (activeTab.value === 'finished') return end < now
    return false
  })
})

const getEventStatus = (event) => {
  const now = new Date()
  const start = new Date(event.start_time)
  const end = new Date(event.end_time)
  if (end < now) return { text: '已结束', type: 'info' }
  if (start <= now && end >= now) return { text: '进行中', type: 'success' }
  if (start > now) return { text: '未开始', type: 'warning' }
  return { text: '未知', type: 'info' }
}

const fetchEvents = () => {
  loading.value = true
  setTimeout(() => {
    events.value = [
      { id: 1, name: 'AI技术应用线上分享会', type: 'online', start_time: '2024-06-10T19:00:00Z', end_time: '2024-06-10T21:00:00Z', participants_count: 88, max_participants: 200, fee: 0, cover_image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500', status: 'ongoing' },
      { id: 2, name: '产品经理线下交流沙龙', type: 'offline', start_time: '2024-06-15T14:00:00Z', end_time: '2024-06-15T17:00:00Z', participants_count: 45, max_participants: 50, fee: 99, cover_image: 'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?w=500', status: 'upcoming' },
      { id: 3, name: '30天健身打卡挑战', type: 'check-in', start_time: '2024-05-01T00:00:00Z', end_time: '2024-05-30T23:59:59Z', participants_count: 152, max_participants: 200, fee: 19.9, cover_image: 'https://images.unsplash.com/photo-1541534401786-20772b48a48c?w=500', status: 'finished' },
    ]
    loading.value = false
  }, 500)
}

const handleTabClick = () => {
  // 可以在此根据tab重新从后端拉取数据
}

const handleCreateEvent = () => {
  currentEvent.value = null
  dialogVisible.value = true
}

const handleEditEvent = (event) => {
  currentEvent.value = JSON.parse(JSON.stringify(event))
  dialogVisible.value = true
}

const handleViewData = (event) => {
  ElMessage.info(`查看活动 "${event.name}" 的数据分析...`)
  // router.push(`/community/events/${event.id}/analytics`)
}

const handleMoreCommand = (command) => {
  const { action, event } = command
  if (action === 'poster') {
    ElMessage.success(`正在为活动 "${event.name}" 生成推广海报...`)
  } else if (action === 'copy') {
    ElMessage.success(`活动 "${event.name}" 已复制`)
    fetchEvents()
  } else if (action === 'delete') {
    ElMessageBox.confirm(`确定要删除活动 "${event.name}" 吗？`, '警告', {
      type: 'warning',
    }).then(() => {
      events.value = events.value.filter(e => e.id !== event.id)
      ElMessage.success('活动删除成功')
    }).catch(() => {})
  }
}

const handleSuccess = () => {
  fetchEvents()
}

onMounted(() => {
  fetchEvents()
})
</script>

<style lang="scss" scoped>
.page-header {
  h1 { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
  p { color: #64748b; font-size: 14px; margin-top: 0; }
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.event-tabs {
  border-bottom: none;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }
}

.event-col {
  margin-bottom: 20px;
}

.event-card {
  .event-image-wrapper {
    position: relative;
    .event-image {
      width: 100%;
      height: 180px;
      object-fit: cover;
      border-radius: 4px;
    }
    .event-status-tag {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }

  .event-info {
    padding: 14px 0;
    .event-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 8px 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .event-time {
      font-size: 13px;
      color: #64748b;
      margin: 0 0 10px 0;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .event-stats {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #334155;
      span {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .event-actions {
    border-top: 1px solid #e2e8f0;
    padding-top: 14px;
    display: flex;
    justify-content: space-between;
  }
}
</style>