<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ContentPerformanceAnalyticsService;
use App\Services\AIContentGeneratorService;
use App\Services\ContentServiceMerged as ContentAutomationService;
use App\Services\ContentServiceMerged;
use App\Models\WechatGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Exception;

/**
 * 高级内容管理API控制器
 * 提供AI驱动的内容生成、性能分析、自动化管理等高级功能
 */
class AdvancedContentController extends Controller
{
    public function __construct(
        private readonly ContentPerformanceAnalyticsService $analyticsService,
        private readonly AIContentGeneratorService $aiService,
        private readonly ContentAutomationService $automationService,
        private readonly ContentServiceMerged $advancedService
    ) {
        $this->middleware('auth:api');
        $this->middleware('throttle:30,1')->only(['generateAIContent', 'createAutomationRule']);
        $this->middleware('throttle:60,1')->only(['comprehensiveAnalysis', 'batchAutomation']);
    }

    /**
     * 综合性能分析
     */
    public function comprehensiveAnalysis(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'time_range' => 'nullable|string|in:1d,7d,30d,90d,1y',
            'include_predictions' => 'nullable|boolean',
            'include_recommendations' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $options = [
                'time_range' => $request->time_range ?? '30d',
                'include_predictions' => $request->boolean('include_predictions', true),
                'include_recommendations' => $request->boolean('include_recommendations', true),
            ];

            $analysis = $this->analyticsService->comprehensivePerformanceAnalysis($group, $options);

            return $this->successResponse('综合分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 多维度对比分析
     */
    public function multiDimensionalAnalysis(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'required|array|min:2|max:10',
            'group_ids.*' => 'required|integer|exists:wechat_groups,id',
            'dimensions' => 'nullable|array',
            'dimensions.*' => 'string|in:time,category,price,user',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $userId = Auth::id();
            
            // 验证用户权限
            $userGroups = WechatGroup::whereIn('id', $request->group_ids)
                ->where('user_id', $userId)
                ->pluck('id')
                ->toArray();

            if (count($userGroups) !== count($request->group_ids)) {
                return $this->errorResponse('部分群组无权限访问', null, 403);
            }

            $dimensions = $request->dimensions ?? ['time', 'category', 'price'];
            $analysis = $this->analyticsService->multiDimensionalAnalysis($request->group_ids, $dimensions);

            return $this->successResponse('多维度分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 实时性能监控
     */
    public function realTimeMonitoring(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'nullable|array|max:50',
            'group_ids.*' => 'integer|exists:wechat_groups,id',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $userId = Auth::id();
            $groupIds = $request->group_ids;

            // 如果指定了群组ID，验证权限
            if ($groupIds) {
                $userGroups = WechatGroup::whereIn('id', $groupIds)
                    ->where('user_id', $userId)
                    ->pluck('id')
                    ->toArray();

                if (count($userGroups) !== count($groupIds)) {
                    return $this->errorResponse('部分群组无权限访问', null, 403);
                }
            } else {
                // 获取用户的所有群组
                $groupIds = WechatGroup::where('user_id', $userId)
                    ->where('status', WechatGroup::STATUS_ACTIVE)
                    ->pluck('id')
                    ->toArray();
            }

            $monitoring = $this->analyticsService->realTimePerformanceMonitoring($groupIds);

            return $this->successResponse('实时监控数据获取成功', $monitoring);

        } catch (Exception $e) {
            return $this->errorResponse('监控数据获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * AI内容生成
     */
    public function generateAIContent(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'content_type' => 'required|string|in:titles,descriptions,faq,marketing,seo',
            'provider' => 'nullable|string|in:openai,claude,gemini,local',
            'style' => 'nullable|string|in:professional,casual,persuasive,creative',
            'count' => 'nullable|integer|min:1|max:10',
            'options' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限操作此群组', null, 403);
            }

            $options = array_merge($request->options ?? [], [
                'provider' => $request->provider ?? 'openai',
                'style' => $request->style ?? 'professional',
                'count' => $request->count ?? 5,
            ]);

            $result = match ($request->content_type) {
                'titles' => $this->aiService->generateTitles($group, $options),
                'descriptions' => $this->aiService->generateDescriptions($group, $options),
                'faq' => $this->aiService->generateFAQ($group, $options),
                'marketing' => $this->aiService->generateMarketingCopy($group, $options),
                'seo' => $this->aiService->generateSEOContent($group, $options),
                default => throw new Exception('不支持的内容类型'),
            };

            return $this->successResponse('AI内容生成成功', [
                'content_type' => $request->content_type,
                'generated_content' => $result,
                'generation_options' => $options,
            ]);

        } catch (Exception $e) {
            return $this->errorResponse('AI内容生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 多语言内容生成
     */
    public function generateMultiLanguageContent(Request $request, int $groupId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'languages' => 'required|array|min:1|max:5',
            'languages.*' => 'string|in:en,ja,ko,es,fr,de,ru',
            'options' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限操作此群组', null, 403);
            }

            $result = $this->aiService->generateMultiLanguageContent(
                $group,
                $request->languages,
                $request->options ?? []
            );

            return $this->successResponse('多语言内容生成成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('多语言内容生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建自动化规则
     */
    public function createAutomationRule(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'type' => 'required|string|in:schedule,trigger,condition,workflow',
            'triggers' => 'required|array|min:1',
            'triggers.*.type' => 'required|string|in:time,performance,user_action,market_change',
            'conditions' => 'nullable|array',
            'actions' => 'required|array|min:1',
            'actions.*.type' => 'required|string|in:optimize,publish,analyze,notify,update',
            'schedule' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $ruleData = array_merge($request->all(), [
                'user_id' => Auth::id(),
            ]);

            $rule = $this->automationService->createAutomationRule($ruleData);

            return $this->successResponse('自动化规则创建成功', $rule);

        } catch (Exception $e) {
            return $this->errorResponse('自动化规则创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 执行自动化规则
     */
    public function executeAutomationRule(Request $request, string $ruleId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'context' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $context = array_merge($request->context ?? [], [
                'user_id' => Auth::id(),
                'executed_by' => 'manual',
            ]);

            $result = $this->automationService->executeRule($ruleId, $context);

            return $this->successResponse('自动化规则执行完成', $result);

        } catch (Exception $e) {
            return $this->errorResponse('自动化规则执行失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量自动化处理
     */
    public function batchAutomation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'required|array|min:1|max:50',
            'group_ids.*' => 'required|integer|exists:wechat_groups,id',
            'automation_config' => 'required|array',
            'automation_config.actions' => 'required|array|min:1',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $userId = Auth::id();
            
            // 验证用户权限
            $userGroups = WechatGroup::whereIn('id', $request->group_ids)
                ->where('user_id', $userId)
                ->pluck('id')
                ->toArray();

            if (count($userGroups) !== count($request->group_ids)) {
                return $this->errorResponse('部分群组无权限访问', null, 403);
            }

            $result = $this->automationService->batchAutomation(
                $request->group_ids,
                $request->automation_config
            );

            return $this->successResponse('批量自动化处理完成', $result);

        } catch (Exception $e) {
            return $this->errorResponse('批量自动化处理失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 智能工作流创建
     */
    public function createSmartWorkflow(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'steps' => 'required|array|min:1',
            'triggers' => 'required|array|min:1',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $workflowData = array_merge($request->all(), [
                'user_id' => Auth::id(),
            ]);

            $workflow = $this->automationService->createSmartWorkflow($workflowData);

            return $this->successResponse('智能工作流创建成功', $workflow);

        } catch (Exception $e) {
            return $this->errorResponse('智能工作流创建失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 调度优化
     */
    public function optimizeScheduling(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $optimization = $this->automationService->optimizeScheduling($userId);

            return $this->successResponse('调度优化分析完成', $optimization);

        } catch (Exception $e) {
            return $this->errorResponse('调度优化失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 启用自适应自动化
     */
    public function enableAdaptiveAutomation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'learning_mode' => 'nullable|string|in:conservative,balanced,aggressive',
            'adaptation_frequency' => 'nullable|string|in:daily,weekly,monthly',
            'performance_threshold' => 'nullable|numeric|min:0|max:1',
            'auto_adjust_rules' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('参数验证失败', $validator->errors(), 422);
        }

        try {
            $userId = Auth::id();
            $preferences = $request->only([
                'learning_mode',
                'adaptation_frequency',
                'performance_threshold',
                'auto_adjust_rules'
            ]);

            $result = $this->automationService->enableAdaptiveAutomation($userId, $preferences);

            return $this->successResponse('自适应自动化启用成功', $result);

        } catch (Exception $e) {
            return $this->errorResponse('自适应自动化启用失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 用户行为深度分析
     */
    public function deepUserBehaviorAnalysis(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $analysis = $this->analyticsService->deepUserBehaviorAnalysis($group);

            return $this->successResponse('用户行为深度分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('用户行为分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 内容生命周期分析
     */
    public function contentLifecycleAnalysis(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $analysis = $this->analyticsService->contentLifecycleAnalysis($group);

            return $this->successResponse('内容生命周期分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('生命周期分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 竞争力分析
     */
    public function competitiveAnalysis(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $analysis = $this->analyticsService->competitiveAnalysis($group);

            return $this->successResponse('竞争力分析完成', $analysis);

        } catch (Exception $e) {
            return $this->errorResponse('竞争力分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 预测性内容分析
     */
    public function predictiveContentAnalysis(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if ($group->user_id !== Auth::id()) {
                return $this->errorResponse('无权限访问此群组', null, 403);
            }

            $prediction = $this->advancedService->predictContentPerformance($group);

            return $this->successResponse('预测性分析完成', $prediction);

        } catch (Exception $e) {
            return $this->errorResponse('预测性分析失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 个性化内容优化
     */
    public function personalizedContentOptimization(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            $user = Auth::user();
            
            if ($group->user_id !== $user->id) {
                return $this->errorResponse('无权限操作此群组', null, 403);
            }

            $optimization = $this->advancedService->personalizeContent($group, $user);

            return $this->successResponse('个性化优化完成', $optimization);

        } catch (Exception $e) {
            return $this->errorResponse('个性化优化失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 统一成功响应格式
     */
    private function successResponse(string $message, array $data = []): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp,
        ]);
    }

    /**
     * 统一错误响应格式
     */
    private function errorResponse(string $message, $errors = null, int $code = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->timestamp,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }
}