<template>
  <div class="social-share">
    <el-card class="share-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Share /></el-icon>
          <span>社交分享推广</span>
        </div>
      </template>

      <!-- 分享预览 -->
      <div class="share-preview">
        <h4>分享预览</h4>
        <div class="preview-card">
          <div class="preview-image">
            <img :src="shareData.image || '/default-group-image.jpg'" alt="群组图片" />
          </div>
          <div class="preview-content">
            <h5>{{ shareData.title }}</h5>
            <p>{{ shareData.description }}</p>
            <div class="preview-stats">
              <span>💰 ¥{{ shareData.price }}</span>
              <span>👥 {{ shareData.members }}人已加入</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分享渠道 -->
      <div class="share-channels">
        <h4>选择分享渠道</h4>
        <div class="channel-grid">
          <div 
            v-for="channel in shareChannels" 
            :key="channel.id"
            class="channel-item"
            :class="{ active: selectedChannels.includes(channel.id) }"
            @click="toggleChannel(channel.id)"
          >
            <div class="channel-icon" :style="{ background: channel.color }">
              <el-icon><component :is="channel.icon" /></el-icon>
            </div>
            <span class="channel-name">{{ channel.name }}</span>
            <div class="channel-stats">
              <small>{{ channel.reach }}万用户</small>
            </div>
          </div>
        </div>
      </div>

      <!-- 分享设置 -->
      <div class="share-settings">
        <h4>分享设置</h4>
        <el-form :model="shareSettings" label-width="100px">
          <el-form-item label="分享标题">
            <el-input 
              v-model="shareSettings.title" 
              placeholder="自定义分享标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="分享描述">
            <el-input 
              v-model="shareSettings.description" 
              type="textarea"
              :rows="3"
              placeholder="自定义分享描述"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="推广标签">
            <el-select 
              v-model="shareSettings.tags" 
              multiple 
              placeholder="选择推广标签"
              style="width: 100%"
            >
              <el-option label="限时优惠" value="discount" />
              <el-option label="新群推荐" value="new" />
              <el-option label="热门群组" value="hot" />
              <el-option label="精品内容" value="premium" />
              <el-option label="专业交流" value="professional" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="定时发送">
            <el-switch v-model="shareSettings.scheduled" />
            <el-date-picker
              v-if="shareSettings.scheduled"
              v-model="shareSettings.scheduleTime"
              type="datetime"
              placeholder="选择发送时间"
              style="margin-left: 10px;"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="share-actions">
        <el-button @click="generateShareContent">
          <el-icon><Document /></el-icon>
          生成分享内容
        </el-button>
        <el-button type="primary" @click="shareToChannels" :loading="sharing">
          <el-icon><Share /></el-icon>
          {{ sharing ? '分享中...' : '立即分享' }}
        </el-button>
        <el-button @click="copyShareLink">
          <el-icon><CopyDocument /></el-icon>
          复制链接
        </el-button>
      </div>

      <!-- 分享记录 -->
      <div class="share-history">
        <h4>分享记录</h4>
        <el-table :data="shareHistory" style="width: 100%">
          <el-table-column prop="channel" label="渠道" width="100" />
          <el-table-column prop="time" label="分享时间" width="150" />
          <el-table-column prop="views" label="浏览量" width="80" />
          <el-table-column prop="clicks" label="点击量" width="80" />
          <el-table-column label="转化率" width="80">
            <template #default="{ row }">
              {{ ((row.conversions / row.clicks) * 100).toFixed(1) }}%
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="viewShareDetails(row)">详情</el-button>
              <el-button size="small" type="primary" @click="reshare(row)">再次分享</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Share, Document, CopyDocument, 
  ChatDotRound, Platform, VideoCamera, 
  Message, Phone, Link 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  groupData: {
    type: Object,
    required: true
  }
})

// 响应式数据
const sharing = ref(false)
const selectedChannels = ref(['wechat', 'qq'])

const shareData = computed(() => ({
  title: props.groupData.name || '精品社群推荐',
  description: props.groupData.description || '加入我们的专业交流群，获取最新资讯和优质内容',
  price: props.groupData.price || 99,
  members: props.groupData.virtualMembers || 1280,
  image: props.groupData.avatar
}))

const shareSettings = reactive({
  title: '',
  description: '',
  tags: [],
  scheduled: false,
  scheduleTime: null
})

const shareChannels = ref([
  {
    id: 'wechat',
    name: '微信朋友圈',
    icon: 'ChatDotRound',
    color: '#07c160',
    reach: 12.5
  },
  {
    id: 'qq',
    name: 'QQ空间',
    icon: 'Platform',
    color: '#12b7f5',
    reach: 8.3
  },
  {
    id: 'weibo',
    name: '新浪微博',
    icon: 'VideoCamera',
    color: '#e6162d',
    reach: 5.2
  },
  {
    id: 'douyin',
    name: '抖音',
    icon: 'VideoCamera',
    color: '#000000',
    reach: 15.8
  },
  {
    id: 'xiaohongshu',
    name: '小红书',
    icon: 'Message',
    color: '#ff2442',
    reach: 3.6
  },
  {
    id: 'zhihu',
    name: '知乎',
    icon: 'Document',
    color: '#0084ff',
    reach: 4.2
  }
])

const shareHistory = ref([
  {
    channel: '微信朋友圈',
    time: '2025-08-07 14:30',
    views: 1250,
    clicks: 89,
    conversions: 12
  },
  {
    channel: 'QQ空间',
    time: '2025-08-07 10:15',
    views: 680,
    clicks: 45,
    conversions: 6
  },
  {
    channel: '新浪微博',
    time: '2025-08-06 16:20',
    views: 420,
    clicks: 28,
    conversions: 3
  }
])

// 方法
const toggleChannel = (channelId) => {
  const index = selectedChannels.value.indexOf(channelId)
  if (index > -1) {
    selectedChannels.value.splice(index, 1)
  } else {
    selectedChannels.value.push(channelId)
  }
}

const generateShareContent = () => {
  const content = `🔥 ${shareData.value.title}

${shareData.value.description}

💰 特价：¥${shareData.value.price}
👥 已有${shareData.value.members}人加入
⏰ 限时优惠，先到先得！

点击链接立即加入：
${window.location.origin}/group/${props.groupData.id}

#精品社群 #专业交流 #限时优惠`

  navigator.clipboard.writeText(content).then(() => {
    ElMessage.success('分享内容已复制到剪贴板')
  })
}

const shareToChannels = async () => {
  if (selectedChannels.value.length === 0) {
    ElMessage.warning('请选择至少一个分享渠道')
    return
  }

  sharing.value = true
  try {
    // 模拟分享API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`已成功分享到${selectedChannels.value.length}个渠道`)
    
    // 更新分享记录
    const newRecord = {
      channel: selectedChannels.value.map(id => 
        shareChannels.value.find(c => c.id === id)?.name
      ).join(', '),
      time: new Date().toLocaleString(),
      views: 0,
      clicks: 0,
      conversions: 0
    }
    shareHistory.value.unshift(newRecord)
  } catch (error) {
    ElMessage.error('分享失败，请重试')
  } finally {
    sharing.value = false
  }
}

const copyShareLink = () => {
  const link = `${window.location.origin}/group/${props.groupData.id}`
  navigator.clipboard.writeText(link).then(() => {
    ElMessage.success('分享链接已复制到剪贴板')
  })
}

const viewShareDetails = (row) => {
  ElMessage.info(`查看${row.channel}的详细数据`)
}

const reshare = (row) => {
  ElMessage.info(`重新分享到${row.channel}`)
}
</script>

<style lang="scss" scoped>
.social-share {
  .share-card {
    border-radius: 12px;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
  }
  
  .share-preview {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
  
  .preview-card {
    display: flex;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
  }
  
  .preview-image {
    width: 80px;
    height: 80px;
    margin-right: 15px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }
  }
  
  .preview-content {
    flex: 1;
    
    h5 {
      margin-bottom: 8px;
      color: #333;
      font-size: 16px;
    }
    
    p {
      margin-bottom: 10px;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }
  }
  
  .preview-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
  }
  
  .share-channels {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
  
  .channel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
  }
  
  .channel-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409eff;
      transform: translateY(-2px);
    }
    
    &.active {
      border-color: #409eff;
      background: #f0f9ff;
    }
  }
  
  .channel-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 8px;
  }
  
  .channel-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .channel-stats {
    font-size: 12px;
    color: #999;
  }
  
  .share-settings {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
  
  .share-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
  }
  
  .share-history {
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
}
</style>
