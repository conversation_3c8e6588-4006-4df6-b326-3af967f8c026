/**
 * 性能优化工具集
 * 用于优化群组落地页的加载速度和用户体验
 */

// 图片懒加载
export class LazyImageLoader {
  constructor(options = {}) {
    this.options = {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }
    this.observer = null
    this.init()
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        this.options
      )
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        this.loadImage(img)
        this.observer.unobserve(img)
      }
    })
  }

  loadImage(img) {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.classList.add('loaded')
      img.removeAttribute('data-src')
    }
  }

  observe(img) {
    if (this.observer) {
      this.observer.observe(img)
    } else {
      // 降级处理
      this.loadImage(img)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// 图片压缩和优化
export class ImageOptimizer {
  static compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height)

        canvas.toBlob(resolve, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }

  static generateThumbnail(file, size = 200) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        const { width, height } = img
        const ratio = Math.min(size / width, size / height)
        const newWidth = width * ratio
        const newHeight = height * ratio

        canvas.width = size
        canvas.height = size

        // 居中绘制
        const x = (size - newWidth) / 2
        const y = (size - newHeight) / 2

        ctx.fillStyle = '#f0f0f0'
        ctx.fillRect(0, 0, size, size)
        ctx.drawImage(img, x, y, newWidth, newHeight)

        canvas.toBlob(resolve, 'image/jpeg', 0.8)
      }

      img.src = URL.createObjectURL(file)
    })
  }
}

// 资源预加载管理器
export class ResourcePreloader {
  constructor() {
    this.preloadedResources = new Set()
    this.loadingPromises = new Map()
  }

  preloadImage(src) {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)
    }

    const promise = new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.preloadedResources.add(src)
        this.loadingPromises.delete(src)
        resolve(img)
      }
      img.onerror = () => {
        this.loadingPromises.delete(src)
        reject(new Error(`Failed to load image: ${src}`))
      }
      img.src = src
    })

    this.loadingPromises.set(src, promise)
    return promise
  }

  preloadVideo(src) {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }

    const promise = new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.oncanplaythrough = () => {
        this.preloadedResources.add(src)
        resolve(video)
      }
      video.onerror = () => {
        reject(new Error(`Failed to load video: ${src}`))
      }
      video.preload = 'metadata'
      video.src = src
    })

    return promise
  }

  preloadFont(fontFamily, fontUrl) {
    if (this.preloadedResources.has(fontUrl)) {
      return Promise.resolve()
    }

    const promise = new Promise((resolve, reject) => {
      const font = new FontFace(fontFamily, `url(${fontUrl})`)
      font.load().then(() => {
        document.fonts.add(font)
        this.preloadedResources.add(fontUrl)
        resolve(font)
      }).catch(reject)
    })

    return promise
  }
}

// 缓存管理器
export class CacheManager {
  constructor(cacheName = 'landing-page-cache', version = '1.0') {
    this.cacheName = `${cacheName}-v${version}`
    this.memoryCache = new Map()
    this.maxMemoryCacheSize = 50
  }

  // 内存缓存
  setMemoryCache(key, value, ttl = 300000) { // 默认5分钟
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }

    this.memoryCache.set(key, {
      value,
      expires: Date.now() + ttl
    })
  }

  getMemoryCache(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null

    if (Date.now() > cached.expires) {
      this.memoryCache.delete(key)
      return null
    }

    return cached.value
  }

  // 浏览器缓存
  async setBrowserCache(key, data) {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName)
        const response = new Response(JSON.stringify(data))
        await cache.put(key, response)
      } catch (error) {
        console.warn('Failed to set browser cache:', error)
      }
    }
  }

  async getBrowserCache(key) {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName)
        const response = await cache.match(key)
        if (response) {
          return await response.json()
        }
      } catch (error) {
        console.warn('Failed to get browser cache:', error)
      }
    }
    return null
  }

  async clearCache() {
    this.memoryCache.clear()
    if ('caches' in window) {
      try {
        await caches.delete(this.cacheName)
      } catch (error) {
        console.warn('Failed to clear browser cache:', error)
      }
    }
  }
}

// 性能监控器
export class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.observers = []
  }

  // 监控页面加载性能
  measurePageLoad() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0]
        this.metrics.pageLoad = {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart
        }
      })
    }
  }

  // 监控资源加载
  measureResourceLoad() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.initiatorType === 'img' || entry.initiatorType === 'video') {
            this.metrics.resources = this.metrics.resources || []
            this.metrics.resources.push({
              name: entry.name,
              type: entry.initiatorType,
              duration: entry.duration,
              size: entry.transferSize
            })
          }
        })
      })
      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  // 监控用户交互
  measureUserInteraction() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.metrics.interactions = this.metrics.interactions || []
          this.metrics.interactions.push({
            name: entry.name,
            duration: entry.duration,
            startTime: entry.startTime
          })
        })
      })
      observer.observe({ entryTypes: ['measure'] })
      this.observers.push(observer)
    }
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink
      } : null
    }
  }

  // 清理监控器
  disconnect() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// 移动端优化工具
export class MobileOptimizer {
  static isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  static isIOS() {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  }

  static isAndroid() {
    return /Android/.test(navigator.userAgent)
  }

  static getViewportSize() {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }

  static optimizeForMobile() {
    if (this.isMobile()) {
      // 禁用双击缩放
      document.addEventListener('touchstart', (e) => {
        if (e.touches.length > 1) {
          e.preventDefault()
        }
      })

      // 优化滚动性能
      document.body.style.webkitOverflowScrolling = 'touch'

      // 禁用选择文本
      document.body.style.webkitUserSelect = 'none'
      document.body.style.userSelect = 'none'

      // iOS Safari 优化
      if (this.isIOS()) {
        // 隐藏地址栏
        window.addEventListener('load', () => {
          setTimeout(() => {
            window.scrollTo(0, 1)
          }, 0)
        })
      }
    }
  }

  static addTouchFeedback(element) {
    if (this.isMobile()) {
      element.addEventListener('touchstart', () => {
        element.style.opacity = '0.7'
      })

      element.addEventListener('touchend', () => {
        element.style.opacity = '1'
      })

      element.addEventListener('touchcancel', () => {
        element.style.opacity = '1'
      })
    }
  }
}

// 兼容性检测器
export class CompatibilityChecker {
  static checkWebPSupport() {
    return new Promise((resolve) => {
      const webP = new Image()
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2)
      }
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
    })
  }

  static checkVideoSupport() {
    const video = document.createElement('video')
    return {
      mp4: video.canPlayType('video/mp4') !== '',
      webm: video.canPlayType('video/webm') !== '',
      ogg: video.canPlayType('video/ogg') !== ''
    }
  }

  static checkIntersectionObserverSupport() {
    return 'IntersectionObserver' in window
  }

  static checkServiceWorkerSupport() {
    return 'serviceWorker' in navigator
  }

  static getCompatibilityReport() {
    return {
      webP: this.checkWebPSupport(),
      video: this.checkVideoSupport(),
      intersectionObserver: this.checkIntersectionObserverSupport(),
      serviceWorker: this.checkServiceWorkerSupport(),
      localStorage: 'localStorage' in window,
      sessionStorage: 'sessionStorage' in window,
      geolocation: 'geolocation' in navigator,
      deviceMotion: 'DeviceMotionEvent' in window
    }
  }
}

// 导出默认配置
export const defaultConfig = {
  lazyLoading: {
    rootMargin: '50px',
    threshold: 0.1
  },
  imageOptimization: {
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    thumbnailSize: 200
  },
  caching: {
    memoryTTL: 300000, // 5分钟
    maxMemorySize: 50
  },
  mobile: {
    touchFeedback: true,
    optimizeScrolling: true,
    hideAddressBar: true
  }
}
