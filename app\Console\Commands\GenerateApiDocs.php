<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ApiDocumentationService;

/**
 * 生成API文档命令
 */
class GenerateApiDocs extends Command
{
    protected $signature = 'api:docs {--format=all : 生成格式 (json|yaml|html|all)}';
    protected $description = '生成API文档';

    protected ApiDocumentationService $docService;

    public function __construct(ApiDocumentationService $docService)
    {
        parent::__construct();
        $this->docService = $docService;
    }

    public function handle()
    {
        $this->info('🔄 正在生成API文档...');
        
        try {
            $documentation = $this->docService->generateDocumentation();
            
            $this->info('✅ API文档生成成功！');
            $this->newLine();
            
            $this->displayStats($documentation);
            $this->displayAccessInfo();
            
        } catch (\Exception $e) {
            $this->error('❌ API文档生成失败: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function displayStats(array $documentation): void
    {
        $pathCount = count($documentation['paths'] ?? []);
        $schemaCount = count($documentation['components']['schemas'] ?? []);
        $tagCount = count($documentation['tags'] ?? []);

        $this->info("📊 文档统计:");
        $this->line("  • API路径: {$pathCount}");
        $this->line("  • 数据模型: {$schemaCount}");
        $this->line("  • 标签分类: {$tagCount}");
    }

    private function displayAccessInfo(): void
    {
        $baseUrl = config('app.url');
        
        $this->info("🌐 访问地址:");
        $this->line("  • Swagger UI: {$baseUrl}/api/documentation");
        $this->line("  • JSON文档: {$baseUrl}/docs/api-docs.json");
        $this->line("  • YAML文档: {$baseUrl}/docs/api-docs.yaml");
        $this->line("  • HTML文档: {$baseUrl}/docs/api-docs.html");
    }
}