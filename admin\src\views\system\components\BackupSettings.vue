<template>
  <div class="backup-settings-container">
    <el-card>
      <template #header>
        <h3>备份与恢复</h3>
      </template>

      <!-- 自动备份策略 -->
      <el-card shadow="never" style="margin-bottom: 20px;">
        <template #header>
          <h4>自动备份策略</h4>
        </template>
        <el-form :model="backupPolicy" label-width="120px">
          <el-form-item label="启用自动备份">
            <el-switch v-model="backupPolicy.enabled" />
          </el-form-item>
          <template v-if="backupPolicy.enabled">
            <el-form-item label="备份频率">
              <el-select v-model="backupPolicy.frequency" placeholder="请选择频率">
                <el-option label="每天" value="daily"></el-option>
                <el-option label="每周" value="weekly"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备份时间">
              <el-time-picker
                v-model="backupPolicy.time"
                format="HH:mm"
                placeholder="选择备份时间"
              />
            </el-form-item>
            <el-form-item label="保留备份数量">
              <el-input-number v-model="backupPolicy.retention" :min="1" :max="30" />
              <span> 个</span>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button type="primary" @click="saveBackupPolicy">保存策略</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 手动备份与列表 -->
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <h4>备份历史</h4>
            <el-button type="primary" icon="Plus" @click="createManualBackup" :loading="creatingBackup">
              创建手动备份
            </el-button>
          </div>
        </template>
        <el-table :data="backupList" v-loading="loading" style="width: 100%">
          <el-table-column prop="timestamp" label="备份时间" width="180">
            <template #default="{ row }">{{ formatDate(row.timestamp) }}</template>
          </el-table-column>
          <el-table-column prop="filename" label="文件名" />
          <el-table-column prop="size" label="文件大小" width="120" />
          <el-table-column label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.type === 'db' ? 'success' : 'primary'">
                {{ row.type === 'db' ? '数据库' : '文件' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" icon="RefreshLeft" size="small" @click="handleRestore(row)">恢复</el-button>
              <el-button link type="success" icon="Download" size="small" @click="handleDownload(row)">下载</el-button>
              <el-button link type="danger" icon="Delete" size="small" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/format'
import { Plus, RefreshLeft, Download, Delete } from '@element-plus/icons-vue'

const loading = ref(false)
const creatingBackup = ref(false)
const backupList = ref([])
const backupPolicy = reactive({
  enabled: true,
  frequency: 'daily',
  time: new Date(2023, 1, 1, 2, 0, 0), // 凌晨2点
  retention: 7,
})

const fetchBackups = () => {
  loading.value = true
  setTimeout(() => {
    backupList.value = [
      { id: 1, timestamp: '2024-06-01T02:00:00Z', filename: 'backup-202406010200.zip', size: '128 MB', type: 'db' },
      { id: 2, timestamp: '2024-05-31T02:00:00Z', filename: 'backup-202405310200.zip', size: '127 MB', type: 'db' },
      { id: 3, timestamp: '2024-05-30T15:30:00Z', filename: 'manual-backup-202405301530.zip', size: '2.5 GB', type: 'files' },
    ]
    loading.value = false
  }, 500)
}

const saveBackupPolicy = () => {
  ElMessage.success('自动备份策略已保存')
}

const createManualBackup = () => {
  creatingBackup.value = true
  ElMessage.info('正在创建手动备份，请稍候...')
  setTimeout(() => {
    creatingBackup.value = false
    ElMessage.success('手动备份创建成功')
    fetchBackups()
  }, 3000)
}

const handleRestore = (backup) => {
  ElMessageBox.confirm(
    `确定要从备份文件 "${backup.filename}" 恢复吗？此操作不可逆，将覆盖现有数据！`,
    '严重警告',
    { type: 'warning', confirmButtonText: '我确定要恢复' }
  ).then(() => {
    ElMessage.info('正在执行恢复操作...')
  }).catch(() => {})
}

const handleDownload = (backup) => {
  ElMessage.success(`开始下载备份文件: ${backup.filename}`)
  // window.location.href = `/api/backups/download/${backup.id}`
}

const handleDelete = (backup) => {
   ElMessageBox.confirm(`确定要删除备份文件 "${backup.filename}" 吗？`, '确认删除', { type: 'warning' })
    .then(() => {
      backupList.value = backupList.value.filter(b => b.id !== backup.id)
      ElMessage.success('备份文件删除成功')
    }).catch(() => {})
}

onMounted(() => {
  fetchBackups()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>