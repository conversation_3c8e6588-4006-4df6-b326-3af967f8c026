<template>
  <div class="modern-role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Avatar /></el-icon>
          </div>
          <div class="header-text">
            <h1>角色管理</h1>
            <p>管理系统角色权限，配置用户访问控制和功能权限</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出角色
          </el-button>
          <el-button type="primary" @click="handleCreate" class="action-btn primary">
            <el-icon><Plus /></el-icon>
            创建角色
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-card" v-for="stat in roleStats" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索角色名称、标识、描述"
              prefix-icon="Search"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            />
            <el-select
              v-model="searchForm.status"
              placeholder="角色状态"
              clearable
              class="filter-select"
            >
              <el-option label="全部状态" value="" />
              <el-option label="启用" value="active" />
              <el-option label="禁用" value="inactive" />
            </el-select>
            <el-select
              v-model="searchForm.type"
              placeholder="角色类型"
              clearable
              class="filter-select"
            >
              <el-option label="全部类型" value="" />
              <el-option label="系统角色" value="system" />
              <el-option label="自定义角色" value="custom" />
            </el-select>
          </div>
          <div class="filter-right">
            <el-button @click="handleSearch" type="primary" class="search-btn">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset" class="reset-btn">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 角色列表 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="table-title">
              <span>角色列表</span>
              <el-tag size="small" type="info">共 {{ total }} 个角色</el-tag>
            </div>
            <div class="table-actions">
              <el-button 
                v-if="selectedRoles.length > 0" 
                @click="handleBatchDelete" 
                type="danger" 
                size="small"
                plain
              >
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedRoles.length }})
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="roleList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          class="modern-table"
          stripe
          border
        >
          <el-table-column type="selection" width="55" align="center" />
          
          <el-table-column label="角色信息" min-width="200">
            <template #default="{ row }">
              <div class="role-info">
                <div class="role-icon" :style="{ background: getRoleColor(row.name) }">
                  <el-icon size="18" color="white">
                    <component :is="getRoleIcon(row.name)" />
                  </el-icon>
                </div>
                <div class="role-details">
                  <div class="role-name">{{ row.display_name || row.name }}</div>
                  <div class="role-code">{{ row.name }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="角色描述" prop="description" min-width="200" />
          
          <el-table-column label="权限数量" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row.permissions_count || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="用户数量" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="primary" size="small">{{ row.users_count || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="角色类型" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_system ? 'warning' : 'success'" size="small">
                {{ row.is_system ? '系统角色' : '自定义' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              <div class="time-info">
                <div>{{ formatDate(row.created_at) }}</div>
                <small>{{ formatTime(row.created_at) }}</small>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button @click="handleEdit(row)" type="primary" size="small" plain>
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button @click="handlePermissions(row)" type="success" size="small" plain>
                  <el-icon><Key /></el-icon>
                  权限
                </el-button>
                <el-dropdown @command="(command) => handleCommand(command, row)" trigger="click">
                  <el-button type="info" size="small" plain>
                    <el-icon><MoreFilled /></el-icon>
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="copy">
                        <el-icon><DocumentCopy /></el-icon>
                        复制角色
                      </el-dropdown-item>
                      <el-dropdown-item command="users">
                        <el-icon><User /></el-icon>
                        用户管理
                      </el-dropdown-item>
                      <el-dropdown-item command="toggle" :divided="true">
                        <el-icon><component :is="row.status === 'active' ? 'Lock' : 'Unlock'" /></el-icon>
                        {{ row.status === 'active' ? '禁用角色' : '启用角色' }}
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="!row.is_system">
                        <el-icon><Delete /></el-icon>
                        删除角色
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            class="modern-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 角色编辑对话框 -->
    <RoleEditDialog
      v-model="editDialogVisible"
      :role-data="currentRole"
      @success="handleEditSuccess"
    />

    <!-- 权限配置对话框 -->
    <PermissionConfigDialog
      v-model="permissionDialogVisible"
      :role-data="currentRole"
      @success="handlePermissionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Avatar, Download, Plus, Search, RefreshLeft, Edit, Delete, 
  Key, User, MoreFilled, DocumentCopy, Lock, Unlock,
  UserFilled, TrendCharts, ArrowUp, ArrowDown, Star, Setting, Management
} from '@element-plus/icons-vue'
import RoleEditDialog from './components/RoleDialog.vue'
import PermissionConfigDialog from './components/PermissionConfigDialog.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const roleList = ref([])
const selectedRoles = ref([])
const editDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const currentRole = ref({})
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  type: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20
})

// 统计数据
const roleStats = ref([
  {
    key: 'total',
    label: '总角色数',
    value: 8,
    icon: 'Avatar',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+2'
  },
  {
    key: 'active',
    label: '启用角色',
    value: 6,
    icon: 'UserFilled',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+1'
  },
  {
    key: 'permissions',
    label: '总权限数',
    value: 45,
    icon: 'Key',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+5'
  },
  {
    key: 'users',
    label: '已分配用户',
    value: 156,
    icon: 'Management',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12'
  }
])

// 模拟角色数据
const mockRoleList = [
  {
    id: 1,
    name: 'admin',
    display_name: '超级管理员',
    description: '拥有系统所有权限，可以管理所有功能模块',
    permissions_count: 45,
    users_count: 3,
    is_system: true,
    status: 'active',
    created_at: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: 'substation',
    display_name: '分站管理员',
    description: '管理分站运营，拥有分站内所有权限',
    permissions_count: 32,
    users_count: 8,
    is_system: true,
    status: 'active',
    created_at: '2024-01-15 14:20:00'
  },
  {
    id: 3,
    name: 'agent',
    display_name: '代理商',
    description: '管理下级分销员，拥有团队管理权限',
    permissions_count: 25,
    users_count: 23,
    is_system: true,
    status: 'active',
    created_at: '2024-02-01 09:30:00'
  },
  {
    id: 4,
    name: 'distributor',
    display_name: '分销员',
    description: '推广群组链接，获得佣金收益',
    permissions_count: 18,
    users_count: 89,
    is_system: true,
    status: 'active',
    created_at: '2024-02-15 16:10:00'
  },
  {
    id: 5,
    name: 'group_owner',
    display_name: '群主',
    description: '管理自己的群组，发布群组内容',
    permissions_count: 12,
    users_count: 34,
    is_system: true,
    status: 'active',
    created_at: '2024-03-01 11:45:00'
  },
  {
    id: 6,
    name: 'user',
    display_name: '普通用户',
    description: '基础用户权限，可以加入群组',
    permissions_count: 8,
    users_count: 1234,
    is_system: true,
    status: 'active',
    created_at: '2024-01-01 10:00:00'
  },
  {
    id: 7,
    name: 'custom_role',
    display_name: '自定义角色',
    description: '用户自定义的角色，可以灵活配置权限',
    permissions_count: 15,
    users_count: 5,
    is_system: false,
    status: 'active',
    created_at: '2024-04-01 15:30:00'
  },
  {
    id: 8,
    name: 'test_role',
    display_name: '测试角色',
    description: '用于测试的角色，已禁用',
    permissions_count: 10,
    users_count: 0,
    is_system: false,
    status: 'inactive',
    created_at: '2024-05-01 09:15:00'
  }
]

// 工具函数
const getRoleIcon = (roleName) => {
  const icons = {
    admin: 'Star',
    substation: 'Management', 
    agent: 'Avatar',
    distributor: 'Share',
    group_owner: 'UserFilled',
    user: 'User',
    custom_role: 'Setting',
    test_role: 'Key'
  }
  return icons[roleName] || 'Setting'
}

const getRoleColor = (roleName) => {
  const colors = {
    admin: 'linear-gradient(135deg, #f56c6c 0%, #ff9a9e 100%)',
    substation: 'linear-gradient(135deg, #e6a23c 0%, #ffd666 100%)',
    agent: 'linear-gradient(135deg, #409eff 0%, #4facfe 100%)',
    distributor: 'linear-gradient(135deg, #67c23a 0%, #43e97b 100%)',
    group_owner: 'linear-gradient(135deg, #909399 0%, #c0c4cc 100%)',
    user: 'linear-gradient(135deg, #909399 0%, #c0c4cc 100%)',
    custom_role: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    test_role: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  }
  return colors[roleName] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatTime = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 事件处理函数
const handleCreate = () => {
  currentRole.value = {}
  editDialogVisible.value = true
}

const handleEdit = (row) => {
  currentRole.value = { ...row }
  editDialogVisible.value = true
}

const handlePermissions = (row) => {
  currentRole.value = { ...row }
  permissionDialogVisible.value = true
}

const handleCommand = (command, row) => {
  switch (command) {
    case 'copy':
      handleCopy(row)
      break
    case 'users':
      handleUsers(row)
      break
    case 'toggle':
      handleToggleStatus(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

const handleCopy = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要复制角色"${row.display_name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    // 模拟复制操作
    const newRole = {
      ...row,
      id: Date.now(),
      name: `${row.name}_copy`,
      display_name: `${row.display_name}_副本`,
      is_system: false,
      users_count: 0,
      created_at: new Date().toISOString()
    }
    
    roleList.value.push(newRole)
    total.value++
    ElMessage.success('角色复制成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('复制失败')
    }
  }
}

const handleUsers = (row) => {
  ElMessage.info(`用户管理功能开发中... 角色：${row.display_name}`)
}

const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'inactive' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(`确定要${action}角色"${row.display_name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟状态切换
    row.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (row) => {
  if (row.is_system) {
    ElMessage.warning('系统角色不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除角色"${row.display_name}"吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 模拟删除操作
    const index = roleList.value.findIndex(role => role.id === row.id)
    if (index > -1) {
      roleList.value.splice(index, 1)
      total.value--
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedRoles.value = selection
}

const handleBatchDelete = async () => {
  if (selectedRoles.value.length === 0) {
    ElMessage.warning('请选择要删除的角色')
    return
  }

  const systemRoles = selectedRoles.value.filter(role => role.is_system)
  if (systemRoles.length > 0) {
    ElMessage.warning('选中的角色包含系统角色，系统角色不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要批量删除选中的 ${selectedRoles.value.length} 个角色吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    // 模拟批量删除
    selectedRoles.value.forEach(role => {
      const index = roleList.value.findIndex(r => r.id === role.id)
      if (index > -1) {
        roleList.value.splice(index, 1)
        total.value--
      }
    })
    
    selectedRoles.value = []
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    let filteredList = [...mockRoleList]
    
    if (searchForm.keyword) {
      filteredList = filteredList.filter(role => 
        role.display_name.includes(searchForm.keyword) ||
        role.name.includes(searchForm.keyword) ||
        role.description.includes(searchForm.keyword)
      )
    }
    
    if (searchForm.status) {
      filteredList = filteredList.filter(role => role.status === searchForm.status)
    }
    
    if (searchForm.type) {
      const isSystem = searchForm.type === 'system'
      filteredList = filteredList.filter(role => role.is_system === isSystem)
    }
    
    roleList.value = filteredList
    total.value = filteredList.length
    loading.value = false
  }, 500)
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.type = ''
  roleList.value = [...mockRoleList]
  total.value = mockRoleList.length
  ElMessage.info('搜索条件已重置')
}

const handlePageChange = (page) => {
  pagination.page = page
  // 这里可以添加分页逻辑
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  // 这里可以添加分页逻辑
}

const handleExport = async () => {
  try {
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleEditSuccess = () => {
  // 刷新列表
  roleList.value = [...mockRoleList]
  ElMessage.success('角色信息更新成功')
}

const handlePermissionSuccess = () => {
  // 刷新列表
  roleList.value = [...mockRoleList]
  ElMessage.success('权限配置更新成功')
}

// 初始化数据
const initData = () => {
  console.log('🚀 角色管理页面开始初始化...')
  console.log('📊 模拟角色数据:', mockRoleList.length, '条')

  roleList.value = [...mockRoleList]
  total.value = mockRoleList.length

  console.log('✅ 角色管理页面初始化完成')
  console.log('   - 角色列表:', roleList.value.length, '条')
  console.log('   - 总数:', total.value)
}

// 初始化
onMounted(() => {
  console.log('🔄 角色管理页面挂载完成，开始初始化数据...')
  initData()
})
</script>

<style lang="scss" scoped>
.modern-role-management {
  min-height: auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }
  
  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  // 筛选区域
  .filter-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .filter-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      
      :deep(.el-card__body) {
        padding: 24px;
      }
      
      .filter-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        
        .filter-left {
          display: flex;
          gap: 16px;
          flex: 1;
          
          .search-input {
            width: 300px;
            
            :deep(.el-input__wrapper) {
              border-radius: 8px;
              border: 1px solid #dcdfe6;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #c0c4cc;
              }
              
              &.is-focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
              }
            }
          }
          
          .filter-select {
            width: 150px;
            
            :deep(.el-input__wrapper) {
              border-radius: 8px;
            }
          }
        }
        
        .filter-right {
          display: flex;
          gap: 12px;
          
          .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0 20px;
            height: 36px;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
          }
          
          .reset-btn {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            border-radius: 8px;
            padding: 0 20px;
            height: 36px;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
        }
      }
    }
  }
  
  // 表格区域
  .table-section {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    
    .table-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      
      :deep(.el-card__header) {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
        border-bottom: 1px solid #e4e7ed;
        padding: 20px 24px;
        
        .table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .table-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            
            .el-tag {
              background: rgba(102, 126, 234, 0.1);
              color: #667eea;
              border: none;
            }
          }
          
          .table-actions {
            .el-button {
              border-radius: 8px;
              font-weight: 500;
            }
          }
        }
      }
      
      :deep(.el-card__body) {
        padding: 0;
      }
      
      .modern-table {
        :deep(.el-table__header) {
          background: #fafbfc;
          
          th {
            background: #fafbfc !important;
            border-bottom: 1px solid #e4e7ed;
            font-weight: 600;
            color: #606266;
            font-size: 13px;
            padding: 16px 12px;
          }
        }
        
        :deep(.el-table__body) {
          tr {
            transition: all 0.3s ease;
            
            &:hover {
              background: #f8f9ff !important;
            }
            
            td {
              border-bottom: 1px solid #f0f2f5;
              padding: 16px 12px;
            }
          }
        }
        
        .role-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .role-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.1);
            }
          }
          
          .role-details {
            .role-name {
              font-weight: 600;
              color: #303133;
              font-size: 14px;
              margin-bottom: 2px;
            }
            
            .role-code {
              font-size: 12px;
              color: #909399;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 4px;
            }
          }
        }
        
        .time-info {
          div {
            font-size: 13px;
            color: #303133;
            margin-bottom: 2px;
          }
          
          small {
            font-size: 11px;
            color: #909399;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          
          .el-button {
            border-radius: 6px;
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
            min-height: 28px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-1px);
            }
          }
          
          .el-dropdown {
            .el-button {
              border-radius: 6px;
            }
          }
        }
      }
      
      .pagination-wrapper {
        padding: 24px;
        display: flex;
        justify-content: center;
        background: #fafbfc;
        border-top: 1px solid #e4e7ed;
        
        .modern-pagination {
          :deep(.el-pagination__total) {
            color: #606266;
            font-weight: 500;
          }
          
          :deep(.el-pagination__sizes) {
            .el-select .el-input__wrapper {
              border-radius: 6px;
            }
          }
          
          :deep(.btn-prev),
          :deep(.btn-next) {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            background: white;
            
            &:hover {
              background: #667eea;
              border-color: #667eea;
              color: white;
            }
          }
          
          :deep(.el-pager) {
            li {
              border-radius: 6px;
              margin: 0 2px;
              border: 1px solid transparent;
              transition: all 0.3s ease;
              
              &:hover {
                background: #f0f2ff;
                border-color: #667eea;
                color: #667eea;
              }
              
              &.is-active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-color: #667eea;
                color: white;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-role-management {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-role-management {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
    
    .filter-content {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        flex-direction: column;
        width: 100%;
        
        .search-input,
        .filter-select {
          width: 100%;
        }
      }
      
      .filter-right {
        width: 100%;
        justify-content: center;
      }
    }
    
    .modern-table {
      :deep(.el-table__body) {
        .action-buttons {
          flex-direction: column;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

.filter-card,
.table-card {
  animation: fadeInUp 0.6s ease-out;
}

.filter-card {
  animation-delay: 0.5s;
}

.table-card {
  animation-delay: 0.6s;
}
</style>