<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataSyncService;

/**
 * 同步分站数据命令
 */
class SyncSubstationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'substation:sync 
                            {--substation= : 指定分站ID}
                            {--agent= : 指定代理商ID}
                            {--all : 同步所有数据}
                            {--check : 检查数据一致性}
                            {--fix : 修复数据不一致}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步分站和代理商数据';

    protected $syncService;

    public function __construct(DataSyncService $syncService)
    {
        parent::__construct();
        $this->syncService = $syncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始数据同步...');
        
        if ($this->option('check')) {
            $this->checkDataConsistency();
            return;
        }
        
        if ($this->option('fix')) {
            $this->fixDataInconsistency();
            return;
        }
        
        if ($this->option('all')) {
            $this->syncAllData();
            return;
        }
        
        if ($substationId = $this->option('substation')) {
            $this->syncSubstation($substationId);
            return;
        }
        
        if ($agentId = $this->option('agent')) {
            $this->syncAgent($agentId);
            return;
        }
        
        // 默认同步所有数据
        $this->syncAllData();
    }
    
    /**
     * 同步指定分站数据
     */
    private function syncSubstation($substationId)
    {
        $this->info("同步分站 {$substationId} 数据...");
        
        if ($this->syncService->syncSubstationData($substationId)) {
            $this->info("分站 {$substationId} 数据同步成功");
        } else {
            $this->error("分站 {$substationId} 数据同步失败");
        }
    }
    
    /**
     * 同步指定代理商数据
     */
    private function syncAgent($agentId)
    {
        $this->info("同步代理商 {$agentId} 数据...");
        
        if ($this->syncService->syncAgentData($agentId)) {
            $this->info("代理商 {$agentId} 数据同步成功");
        } else {
            $this->error("代理商 {$agentId} 数据同步失败");
        }
    }
    
    /**
     * 同步所有数据
     */
    private function syncAllData()
    {
        $this->info('同步所有分站数据...');
        $substationResult = $this->syncService->syncAllSubstations();
        
        $this->info("分站同步完成: 总计 {$substationResult['total']}, 成功 {$substationResult['success']}, 失败 {$substationResult['failed']}");
        
        $this->info('同步所有代理商数据...');
        $agentResult = $this->syncService->syncAllAgents();
        
        $this->info("代理商同步完成: 总计 {$agentResult['total']}, 成功 {$agentResult['success']}, 失败 {$agentResult['failed']}");
        
        $this->info('数据同步完成!');
    }
    
    /**
     * 检查数据一致性
     */
    private function checkDataConsistency()
    {
        $this->info('检查数据一致性...');
        
        $issues = $this->syncService->checkDataConsistency();
        
        if (empty($issues)) {
            $this->info('数据一致性检查通过，没有发现问题');
        } else {
            $this->warn("发现 " . count($issues) . " 个数据不一致问题:");
            
            foreach ($issues as $issue) {
                $this->line("- {$issue['type']}: " . json_encode($issue));
            }
            
            $this->info('运行 --fix 选项来修复这些问题');
        }
    }
    
    /**
     * 修复数据不一致
     */
    private function fixDataInconsistency()
    {
        $this->info('修复数据不一致问题...');
        
        $result = $this->syncService->fixDataInconsistency();
        
        $this->info("修复完成: 总问题 {$result['total_issues']}, 已修复 {$result['fixed_count']}");
        
        if ($result['fixed_count'] < $result['total_issues']) {
            $this->warn('部分问题修复失败，请查看日志了解详情');
        }
    }
}