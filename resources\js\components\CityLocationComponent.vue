<template>
  <div class="city-location-wrapper">
    <!-- 定位状态显示 -->
    <div v-if="isLocating" class="location-status">
      <i class="el-icon-loading"></i>
      定位中...
    </div>
    
    <!-- 城市显示和编辑 -->
    <div class="city-display" v-else>
      <span class="current-city">{{ currentCity }}</span>
      <el-button size="mini" type="text" @click="showCitySelector = true">
        切换城市
      </el-button>
    </div>

    <!-- 城市选择器 -->
    <el-dialog
      title="选择城市"
      :visible.sync="showCitySelector"
      width="400px"
    >
      <div class="city-selector">
        <!-- 当前定位城市 -->
        <div class="location-section" v-if="detectedCity">
          <h4>当前定位</h4>
          <el-button 
            type="primary" 
            size="small"
            @click="selectCity(detectedCity)"
          >
            {{ detectedCity }}
          </el-button>
        </div>

        <!-- 热门城市 -->
        <div class="hot-cities-section">
          <h4>热门城市</h4>
          <div class="city-grid">
            <el-button
              v-for="city in hotCities"
              :key="city"
              size="small"
              @click="selectCity(city)"
            >
              {{ city }}
            </el-button>
          </div>
        </div>

        <!-- 搜索城市 -->
        <div class="search-section">
          <h4>搜索城市</h4>
          <el-autocomplete
            v-model="citySearch"
            :fetch-suggestions="searchCities"
            placeholder="输入城市名称"
            @select="handleCitySelect"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CityLocationComponent',
  props: {
    value: {
      type: String,
      default: ''
    },
    autoDetect: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLocating: false,
      currentCity: this.value || '本地',
      detectedCity: '',
      showCitySelector: false,
      citySearch: '',
      recentCities: this.getRecentCities(),
      hotCities: [
        '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都',
        '重庆', '天津', '西安', '苏州', '长沙', '沈阳', '青岛', '郑州'
      ],
      allCities: [], // 从后端获取的完整城市列表
      locationMethods: ['amap', 'ip', 'browser'], // 定位方法优先级
      locationTimeout: 8000, // 定位超时时间
      cacheExpiry: 30 * 60 * 1000 // 缓存30分钟
    }
  },
  mounted() {
    if (this.autoDetect && !this.value) {
      this.detectUserLocation();
    }
    this.loadAllCities();
  },
  methods: {
    /**
     * 智能定位用户城市
     */
    async detectUserLocation() {
      this.isLocating = true;
      
      try {
        // 方案1: 使用高德地图API定位
        const amapCity = await this.getLocationFromAmap();
        if (amapCity) {
          this.detectedCity = amapCity;
          this.currentCity = amapCity;
          this.$emit('input', amapCity);
          return;
        }

        // 方案2: 使用后端IP定位
        const ipCity = await this.getLocationFromIP();
        if (ipCity) {
          this.detectedCity = ipCity;
          this.currentCity = ipCity;
          this.$emit('input', ipCity);
          return;
        }

        // 方案3: 使用浏览器定位API
        const browserCity = await this.getLocationFromBrowser();
        if (browserCity) {
          this.detectedCity = browserCity;
          this.currentCity = browserCity;
          this.$emit('input', browserCity);
        }

      } catch (error) {
        console.warn('城市定位失败:', error);
        this.currentCity = '本地';
      } finally {
        this.isLocating = false;
      }
    },

    /**
     * 使用高德地图API定位
     */
    getLocationFromAmap() {
      return new Promise((resolve) => {
        if (!window.AMap) {
          resolve(null);
          return;
        }

        window.AMap.plugin(['AMap.Geolocation', 'AMap.Geocoder'], () => {
          const geolocation = new window.AMap.Geolocation({
            enableHighAccuracy: true,
            timeout: 4000,
            maximumAge: 0,
          });

          geolocation.getCurrentPosition((status, result) => {
            if (status === 'complete') {
              const geocoder = new window.AMap.Geocoder();
              const lnglat = result.position;
              
              geocoder.getAddress(lnglat, (status, result) => {
                if (status === 'complete') {
                  const city = result.regeocode.addressComponent.city;
                  resolve(city ? city.replace('市', '') : null);
                } else {
                  resolve(null);
                }
              });
            } else {
              resolve(null);
            }
          });
        });
      });
    },

    /**
     * 使用后端IP定位
     */
    async getLocationFromIP() {
      try {
        const response = await this.$http.get('/api/location/ip');
        return response.data.data.city;
      } catch (error) {
        return null;
      }
    },

    /**
     * 使用浏览器定位API
     */
    getLocationFromBrowser() {
      return new Promise((resolve) => {
        if (!navigator.geolocation) {
          resolve(null);
          return;
        }

        navigator.geolocation.getCurrentPosition(
          async (position) => {
            try {
              // 使用坐标反向地理编码
              const city = await this.reverseGeocode(
                position.coords.latitude,
                position.coords.longitude
              );
              resolve(city);
            } catch (error) {
              resolve(null);
            }
          },
          () => resolve(null),
          { timeout: 5000 }
        );
      });
    },

    /**
     * 坐标反向地理编码
     */
    async reverseGeocode(lat, lng) {
      try {
        const response = await this.$http.post('/api/location/reverse', {
          latitude: lat,
          longitude: lng
        });
        return response.data.data.city;
      } catch (error) {
        return null;
      }
    },

    /**
     * 加载所有城市列表
     */
    async loadAllCities() {
      try {
        const response = await this.$http.get('/api/location/cities');
        this.allCities = response.data.data;
      } catch (error) {
        console.warn('加载城市列表失败:', error);
      }
    },

    /**
     * 搜索城市
     */
    searchCities(queryString, callback) {
      const results = this.allCities
        .filter(city => city.includes(queryString))
        .map(city => ({ value: city }))
        .slice(0, 10);
      
      callback(results);
    },

    /**
     * 选择城市
     */
    selectCity(city) {
      this.currentCity = city;
      this.showCitySelector = false;
      this.$emit('input', city);
      this.$emit('change', city);
    },

    /**
     * 处理城市选择
     */
    handleCitySelect(item) {
      this.selectCity(item.value);
    }
  }
}
</script>

<style scoped>
.city-location-wrapper {
  display: inline-block;
}

.location-status {
  color: #409EFF;
  font-size: 14px;
}

.city-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-city {
  font-weight: 500;
  color: #303133;
}

.city-selector {
  padding: 10px 0;
}

.location-section,
.hot-cities-section,
.search-section {
  margin-bottom: 20px;
}

.location-section h4,
.hot-cities-section h4,
.search-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.city-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.city-grid .el-button {
  margin: 0;
}
</style>