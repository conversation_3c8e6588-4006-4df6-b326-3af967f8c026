<template>
  <div class="test-navigation-system">
    <div class="test-header">
      <h1>🧪 导航系统和权限测试</h1>
      <p>测试和验证导航系统、权限控制和群组创建功能保护</p>
    </div>

    <!-- 当前用户信息 -->
    <el-card class="user-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>当前用户信息</span>
        </div>
      </template>
      <div class="user-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="用户角色">
            <el-tag :type="getRoleTagType(currentUserRole)">
              {{ getRoleDisplayName(currentUserRole) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ userStore.userInfo?.id || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ userStore.userInfo?.username || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="数据权限范围">{{ getDashboardScope(currentUserRole) }}</el-descriptions-item>
          <el-descriptions-item label="财务权限范围">{{ getFinanceScope(currentUserRole) }}</el-descriptions-item>
          <el-descriptions-item label="群组创建权限">
            <el-tag :type="canCreateGroup(currentUserRole) ? 'success' : 'danger'">
              {{ canCreateGroup(currentUserRole) ? '✅ 允许' : '❌ 禁止' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 角色切换测试 -->
    <el-card class="role-switch-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Switch /></el-icon>
          <span>角色切换测试</span>
        </div>
      </template>
      <div class="role-switch">
        <el-radio-group v-model="testRole" @change="handleRoleChange">
          <el-radio-button 
            v-for="role in availableRoles" 
            :key="role.key" 
            :value="role.key"
          >
            {{ role.name }}
          </el-radio-button>
        </el-radio-group>
        <el-button type="primary" @click="applyRoleChange" :loading="switching">
          应用角色切换
        </el-button>
      </div>
    </el-card>

    <!-- 导航权限测试 -->
    <el-card class="navigation-test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Menu /></el-icon>
          <span>导航权限测试</span>
        </div>
      </template>
      <div class="navigation-test">
        <div class="test-section">
          <h3>可访问的导航分组</h3>
          <div class="navigation-groups">
            <div 
              v-for="(group, key) in visibleGroups" 
              :key="key"
              class="nav-group"
            >
              <div class="group-header">
                <el-icon><component :is="getIconComponent(group.icon)" /></el-icon>
                <span class="group-title">{{ group.title }}</span>
                <el-tag size="small">{{ group.children.length }}个菜单</el-tag>
              </div>
              <div class="group-children">
                <div 
                  v-for="child in group.children" 
                  :key="child.path"
                  class="nav-item"
                  :class="{ protected: child.protected }"
                >
                  <el-icon><component :is="getIconComponent(child.icon)" /></el-icon>
                  <span>{{ child.title }}</span>
                  <el-tag v-if="child.protected" type="success" size="small">核心功能</el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 群组创建功能测试 -->
    <el-card class="group-creation-test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Plus /></el-icon>
          <span>群组创建功能测试</span>
        </div>
      </template>
      <div class="group-creation-test">
        <div class="test-result">
          <el-alert
            :title="groupCreationResult.canAccess ? '✅ 群组创建功能可用' : '❌ 群组创建功能不可用'"
            :type="groupCreationResult.canAccess ? 'success' : 'error'"
            :description="`当前角色 ${getRoleDisplayName(currentUserRole)} 有 ${groupCreationResult.entryCount} 个访问入口`"
            show-icon
            :closable="false"
          />
        </div>
        
        <div class="access-entries">
          <h4>访问入口列表</h4>
          <el-table :data="groupCreationResult.entries" style="width: 100%">
            <el-table-column prop="type" label="入口类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getEntryTypeTag(row.type)">{{ getEntryTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="入口名称" />
            <el-table-column prop="location" label="位置" />
            <el-table-column prop="path" label="路径" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button size="small" @click="testNavigation(row.path)">测试</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="quick-actions-test">
          <h4>快速操作测试</h4>
          <div class="quick-actions">
            <el-button
              v-for="action in quickActions"
              :key="action.path"
              :type="action.path === '/community/add' ? 'primary' : 'default'"
              @click="testNavigation(action.path)"
            >
              <el-icon><component :is="getIconComponent(action.icon)" /></el-icon>
              {{ action.title }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 权限验证测试 -->
    <el-card class="permission-test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Lock /></el-icon>
          <span>权限验证测试</span>
        </div>
      </template>
      <div class="permission-test">
        <div class="test-section">
          <h4>数据访问权限测试</h4>
          <el-table :data="permissionTestResults" style="width: 100%">
            <el-table-column prop="resource" label="资源类型" />
            <el-table-column prop="action" label="操作" />
            <el-table-column prop="allowed" label="是否允许" width="100">
              <template #default="{ row }">
                <el-tag :type="row.allowed ? 'success' : 'danger'">
                  {{ row.allowed ? '✅ 允许' : '❌ 禁止' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="原因" />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 测试日志 -->
    <el-card class="test-log-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>测试日志</span>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      <div class="test-logs">
        <div 
          v-for="(log, index) in testLogs" 
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-type">{{ log.type.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User, Switch, Menu, Plus, Lock, Document, Monitor, DataLine, Comment, 
  UserFilled, Share, Money, Setting, Avatar, ShoppingCart, Link, Lightning,
  Management, Tools
} from '@element-plus/icons-vue'
import { 
  canCreateGroup, 
  getDashboardScope, 
  getFinanceScope,
  canPerformAction 
} from '@/utils/dataPermission'
import { getRoleDisplayName } from '@/config/navigation'
import { 
  getSimpleVisibleNavigationGroups,
  getSimpleUserQuickActions,
  validateGroupCreationAccess,
  runGroupCreationValidation
} from '@/config/simpleNavigationTest'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const testRole = ref('admin')
const switching = ref(false)
const testLogs = ref([])

// 可用角色列表
const availableRoles = [
  { key: 'admin', name: '超级管理员' },
  { key: 'substation', name: '分站管理员' },
  { key: 'agent', name: '代理商' },
  { key: 'distributor', name: '分销员' },
  { key: 'group_owner', name: '群主' },
  { key: 'user', name: '普通用户' }
]

// 计算属性
const currentUserRole = computed(() => {
  return userStore.userInfo?.role || 'user'
})

const visibleGroups = computed(() => {
  const groups = getSimpleVisibleNavigationGroups(currentUserRole.value)
  addLog('info', `获取到 ${Object.keys(groups).length} 个可见导航分组`)
  return groups
})

const quickActions = computed(() => {
  const actions = getSimpleUserQuickActions(currentUserRole.value)
  addLog('info', `获取到 ${actions.length} 个快速操作`)
  return actions
})

const groupCreationResult = computed(() => {
  const result = validateGroupCreationAccess(currentUserRole.value)
  addLog('info', `群组创建验证: ${result.canAccess ? '通过' : '失败'}, ${result.entryCount} 个入口`)
  return result
})

const permissionTestResults = computed(() => {
  const results = []
  const role = currentUserRole.value
  
  // 测试各种权限
  const testCases = [
    { resource: 'group', action: 'create', description: '创建群组' },
    { resource: 'group', action: 'view', description: '查看群组' },
    { resource: 'user', action: 'create', description: '创建用户' },
    { resource: 'user', action: 'view', description: '查看用户' },
    { resource: 'order', action: 'view', description: '查看订单' },
    { resource: 'finance', action: 'view', description: '查看财务' }
  ]
  
  testCases.forEach(testCase => {
    const allowed = canPerformAction(testCase.action, testCase.resource, role)
    results.push({
      resource: testCase.description,
      action: testCase.action,
      allowed,
      reason: allowed ? '权限允许' : '权限不足'
    })
  })
  
  return results
})

// 方法
const addLog = (type, message) => {
  testLogs.value.unshift({
    type,
    message,
    time: new Date()
  })
  
  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

const handleRoleChange = (newRole) => {
  addLog('info', `准备切换角色到: ${getRoleDisplayName(newRole)}`)
}

const applyRoleChange = () => {
  switching.value = true
  addLog('info', `开始切换角色到: ${getRoleDisplayName(testRole.value)}`)
  
  // 模拟角色切换
  setTimeout(() => {
    // 更新用户信息
    userStore.setUserInfo({
      ...userStore.userInfo,
      role: testRole.value
    })
    
    switching.value = false
    addLog('success', `角色切换成功: ${getRoleDisplayName(testRole.value)}`)
    ElMessage.success(`已切换到 ${getRoleDisplayName(testRole.value)}`)
  }, 1000)
}

const testNavigation = (path) => {
  addLog('info', `测试导航到: ${path}`)
  try {
    router.push(path)
    addLog('success', `导航成功: ${path}`)
    ElMessage.success(`导航到 ${path} 成功`)
  } catch (error) {
    addLog('error', `导航失败: ${path} - ${error.message}`)
    ElMessage.error(`导航失败: ${error.message}`)
  }
}

const clearLogs = () => {
  testLogs.value = []
  ElMessage.success('日志已清空')
}

const getRoleTagType = (role) => {
  const typeMap = {
    admin: 'danger',
    substation: 'warning',
    agent: 'primary',
    distributor: 'success',
    group_owner: 'info',
    user: 'default'
  }
  return typeMap[role] || 'default'
}

const getIconComponent = (iconName) => {
  const iconMap = {
    Monitor, DataLine, Comment, User, UserFilled, Share, Money, Setting, 
    Avatar, ShoppingCart, Link, Lightning, Management, Tools, Plus
  }
  return iconMap[iconName] || Document
}

const getEntryTypeTag = (type) => {
  const tagMap = {
    navigation: 'primary',
    quick_action: 'success',
    workbench: 'warning'
  }
  return tagMap[type] || 'default'
}

const getEntryTypeName = (type) => {
  const nameMap = {
    navigation: '导航菜单',
    quick_action: '快速操作',
    workbench: '工作台'
  }
  return nameMap[type] || type
}

const formatTime = (time) => {
  return time.toLocaleTimeString()
}

// 监听角色变化
watch(() => currentUserRole.value, (newRole, oldRole) => {
  if (oldRole) {
    addLog('info', `角色已变更: ${getRoleDisplayName(oldRole)} → ${getRoleDisplayName(newRole)}`)
  }
})

onMounted(() => {
  addLog('info', '导航系统测试页面已加载')
  addLog('info', `当前用户角色: ${getRoleDisplayName(currentUserRole.value)}`)
  
  // 运行完整的群组创建验证
  setTimeout(() => {
    const validationResults = runGroupCreationValidation()
    addLog('info', '已完成群组创建功能完整性验证')
  }, 1000)
})
</script>

<style lang="scss" scoped>
.test-navigation-system {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    text-align: center;
    margin-bottom: 32px;

    h1 {
      color: #1f2937;
      margin-bottom: 8px;
    }

    p {
      color: #6b7280;
      font-size: 16px;
    }
  }

  .el-card {
    margin-bottom: 24px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;

      .el-button {
        margin-left: auto;
      }
    }
  }

  .user-info-card {
    .user-info {
      :deep(.el-descriptions__label) {
        font-weight: 600;
      }
    }
  }

  .role-switch-card {
    .role-switch {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;
    }
  }

  .navigation-test-card {
    .navigation-groups {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .nav-group {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;

        .group-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          font-weight: 600;
          color: #374151;

          .el-tag {
            margin-left: auto;
          }
        }

        .group-children {
          .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 4px;
            transition: background-color 0.2s;

            &:hover {
              background-color: #f9fafb;
            }

            &.protected {
              background-color: #fef3c7;
              border: 1px solid #fbbf24;
            }

            .el-tag {
              margin-left: auto;
            }
          }
        }
      }
    }
  }

  .group-creation-test-card {
    .test-result {
      margin-bottom: 24px;
    }

    .access-entries {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 16px;
        color: #374151;
      }
    }

    .quick-actions-test {
      h4 {
        margin-bottom: 16px;
        color: #374151;
      }

      .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }
    }
  }

  .permission-test-card {
    .test-section {
      h4 {
        margin-bottom: 16px;
        color: #374151;
      }
    }
  }

  .test-log-card {
    .test-logs {
      max-height: 400px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 12px;

      .log-item {
        display: flex;
        gap: 12px;
        padding: 8px;
        border-bottom: 1px solid #f3f4f6;

        &.info {
          color: #3b82f6;
        }

        &.success {
          color: #10b981;
        }

        &.error {
          color: #ef4444;
        }

        .log-time {
          color: #6b7280;
          min-width: 80px;
        }

        .log-type {
          font-weight: 600;
          min-width: 60px;
        }

        .log-message {
          flex: 1;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-navigation-system {
    padding: 16px;

    .navigation-groups {
      grid-template-columns: 1fr !important;
    }

    .role-switch {
      flex-direction: column;
      align-items: stretch !important;

      .el-radio-group {
        width: 100%;
      }
    }

    .quick-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>