# LinkHub Pro 开发规范文档

## 📋 文档概览

**版本**: v1.0  
**更新时间**: 2025-08-08  
**适用范围**: LinkHub Pro 全栈项目  
**维护团队**: 开发团队  

---

## 🎯 开发规范目标

基于项目代码清理经验，制定本规范以确保：
- ✅ 代码质量持续提升
- ✅ 项目结构保持清晰
- ✅ 开发效率不断优化
- ✅ 维护成本有效控制

---

## 🧹 代码清理规范

### **1. 禁止提交的文件类型**

#### **🚫 调试文件**
```bash
# 禁止提交以下类型的文件
*.html                    # 调试页面（除了正式的入口文件）
*-test.html              # 测试页面
*-debug.html             # 调试页面
*-fix-*.html             # 修复验证页面
*-diagnosis.html         # 诊断页面
*-preview-test.html      # 预览测试页面
```

#### **🚫 临时脚本**
```bash
# 禁止提交临时脚本
debug-*.js              # 调试脚本
fix-*.js                # 修复脚本
temp-*.js               # 临时脚本
*-temp.*                # 临时文件
quick-fix.*             # 快速修复文件
```

#### **🚫 文档报告**
```bash
# 禁止提交临时报告
*-report.md             # 各种报告文件
*-analysis.md           # 分析文档
*-fix-*.md              # 修复文档
*-检测报告.md            # 中文报告
*-修复报告.md            # 中文修复报告
```

#### **🚫 备份文件**
```bash
# 禁止提交备份文件
*.backup                # 备份文件
*.bak                   # 备份文件
*.old                   # 旧版本文件
*-backup.*              # 备份文件
*-old.*                 # 旧版本文件
```

### **2. 调试代码清理规范**

#### **✅ 允许保留的调试代码**
```javascript
// ✅ 错误处理 - 保留
console.error('API请求失败:', error)
console.warn('配置项缺失:', configKey)

// ✅ 重要警告 - 保留
console.warn('图片加载失败:', imageUrl)
console.warn('格式化命令失败:', command, error)
```

#### **❌ 必须清理的调试代码**
```javascript
// ❌ 开发调试 - 必须删除
console.log('调试信息:', data)
console.log('🔍 检查数据:', response)
console.log('✅ 功能正常')

// ❌ 详细日志 - 必须删除
console.log(`🔄 处理中: ${status}`)
console.log('🎯 找到数据:', result)
```

### **3. 文件清理检查清单**

#### **提交前自检**
- [ ] 删除所有调试HTML页面
- [ ] 清理console.log调试输出
- [ ] 移除临时脚本文件
- [ ] 删除备份和旧版本文件
- [ ] 清理临时文档和报告
- [ ] 检查.gitignore是否生效

---

## 📚 文档管理规范

### **1. 文档目录结构**

```
项目根目录/
├── README.md                    # 项目主文档
├── DEVELOPMENT_GUIDELINES.md    # 开发规范（本文档）
├── CHANGELOG.md                 # 变更日志
├── docs/                        # 文档目录
│   ├── API.md                   # API文档
│   ├── DEPLOYMENT.md            # 部署文档
│   ├── USER_MANUAL.md           # 用户手册
│   └── ARCHITECTURE.md          # 架构文档
└── admin/
    └── README.md                # 前端项目文档
```

### **2. 文档管理原则**

#### **✅ 集中管理**
- 所有项目文档统一放在 `docs/` 目录
- 避免在各个功能目录散落文档
- 使用清晰的文档命名规范

#### **✅ 定期维护**
- 每月检查并更新过时文档
- 删除临时修复报告和分析文档
- 保持文档与代码同步更新

#### **✅ 版本控制**
- 重要文档变更记录在CHANGELOG.md
- 使用语义化版本号管理文档版本
- 定期备份重要文档

### **3. 禁止的文档实践**

#### **🚫 散落文档**
```bash
# 禁止在以下位置创建文档
/admin/修复报告.md
/src/components/组件说明.md
/public/功能测试.md
/routes/API说明.md
```

#### **🚫 临时报告**
```bash
# 禁止保留临时报告
*-fix-report.md
*-analysis-report.md
*-test-report.md
功能检测报告.md
问题修复说明.md
```

---

## 💻 代码质量规范

### **1. 组件命名规范**

#### **Vue组件命名**
```javascript
// ✅ 正确命名 - PascalCase
ModernRichTextEditor.vue
LandingPagePreview.vue
GroupCreateForm.vue
NavigationSidebar.vue

// ❌ 错误命名
richTextEditor.vue      // camelCase
rich-text-editor.vue    // kebab-case
RichTexteditor.vue      // 不一致的大小写
```

#### **文件和目录命名**
```bash
# ✅ 目录命名 - kebab-case
components/
views/
utils/
api/

# ✅ 工具文件命名 - camelCase
mockApi.js
authUtils.js
formatHelper.js

# ✅ 配置文件命名 - kebab-case
vite.config.js
package.json
```

### **2. 文件结构组织规范**

#### **Vue组件结构**
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 2. 定义Props和Emits
const props = defineProps({
  // props定义
})

const emit = defineEmits(['update:modelValue', 'change'])

// 3. 响应式数据
const data = ref('')

// 4. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 5. 方法定义
const handleMethod = () => {
  // 方法实现
}

// 6. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 组件样式 */
</style>
```

#### **目录结构规范**
```bash
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── layout/         # 布局组件
│   └── business/       # 业务组件
├── views/              # 页面组件
├── utils/              # 工具函数
├── api/                # API接口
├── stores/             # 状态管理
├── styles/             # 样式文件
└── assets/             # 静态资源
```

### **3. 注释和文档规范**

#### **函数注释**
```javascript
/**
 * 处理文件上传
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @param {string} options.url - 上传地址
 * @param {Function} options.onProgress - 进度回调
 * @returns {Promise<Object>} 上传结果
 */
const uploadFile = async (file, options = {}) => {
  // 实现逻辑
}
```

#### **组件注释**
```vue
<script setup>
/**
 * 现代化富文本编辑器组件
 * 
 * 功能特性：
 * - 支持中文输入法
 * - 丰富的格式化选项
 * - 实时预览同步
 * - 移动端适配
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-08
 */

const props = defineProps({
  /** 编辑器内容 */
  modelValue: {
    type: String,
    default: ''
  },
  /** 编辑器高度 */
  height: {
    type: Number,
    default: 200
  }
})
</script>
```

### **4. 错误处理最佳实践**

#### **API错误处理**
```javascript
// ✅ 正确的错误处理
const fetchData = async () => {
  try {
    const response = await api.getData()
    return response.data
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('数据加载失败，请重试')
    throw error
  }
}
```

#### **组件错误边界**
```vue
<script setup>
import { onErrorCaptured } from 'vue'

// 错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('组件错误:', error)
  console.error('错误信息:', info)
  
  // 上报错误
  reportError(error, info)
  
  return false // 阻止错误继续传播
})
</script>
```

---

## 🔄 开发流程规范

### **1. 功能开发流程**

#### **开发前准备**
1. **阅读开发规范** - 重新阅读本文档
2. **检查项目状态** - 确保项目处于清洁状态
3. **创建功能分支** - 从main分支创建feature分支
4. **制定开发计划** - 明确功能需求和实现方案

#### **开发过程中**
1. **遵循命名规范** - 使用规范的文件和组件命名
2. **及时提交代码** - 小步快跑，频繁提交
3. **编写测试用例** - 确保功能正确性
4. **更新文档** - 同步更新相关文档

#### **开发完成后**
1. **代码自检** - 使用下方检查清单
2. **清理调试代码** - 删除所有调试输出
3. **运行测试** - 确保所有测试通过
4. **提交代码** - 提交到feature分支

### **2. 代码提交前检查清单**

#### **📋 必检项目**
- [ ] **删除调试文件** - 所有*-test.html, *-debug.html等
- [ ] **清理调试代码** - 删除console.log调试输出
- [ ] **移除临时文件** - 删除*.temp, *.backup等文件
- [ ] **检查文件命名** - 确保符合命名规范
- [ ] **更新文档** - 同步更新相关文档
- [ ] **运行测试** - 确保功能正常
- [ ] **检查.gitignore** - 确保忽略规则生效

#### **📋 代码质量检查**
- [ ] **组件结构** - 模板、脚本、样式结构清晰
- [ ] **错误处理** - 添加适当的错误处理
- [ ] **性能优化** - 避免不必要的重渲染
- [ ] **可访问性** - 考虑无障碍访问
- [ ] **移动端适配** - 确保响应式设计
- [ ] **浏览器兼容** - 测试主流浏览器

### **3. 定期维护流程**

#### **每周维护**
- 检查并清理临时文件
- 更新过时的注释和文档
- 运行完整测试套件
- 检查依赖包更新

#### **每月维护**
- 进行全面的代码清理
- 更新开发规范文档
- 检查项目结构优化
- 清理无用的依赖包

#### **每季度维护**
- 全面重构过时代码
- 更新技术栈版本
- 优化构建和部署流程
- 进行安全审计

---

## 📝 实施要求

### **1. 团队协作要求**

#### **必读文档**
- 每次迭代开始前，所有团队成员必须重新阅读本开发规范
- 新成员入职时必须学习并签署开发规范承诺书
- 定期组织开发规范培训和经验分享

#### **代码审查**
- 所有代码提交必须经过Code Review
- 使用本文档作为Code Review检查清单
- 发现违反规范的代码必须要求修改

### **2. 工具集成**

#### **Git Hooks**
```bash
# pre-commit hook - 提交前检查
#!/bin/sh
echo "🔍 检查代码规范..."

# 检查是否有调试文件
if git diff --cached --name-only | grep -E "\.(html|temp|backup|bak)$"; then
    echo "❌ 发现调试文件，请清理后再提交"
    exit 1
fi

# 检查console.log
if git diff --cached | grep -E "console\.log"; then
    echo "❌ 发现console.log调试代码，请清理后再提交"
    exit 1
fi

echo "✅ 代码规范检查通过"
```

#### **ESLint配置**
```json
{
  "rules": {
    "no-console": ["warn", { 
      "allow": ["warn", "error"] 
    }],
    "no-debugger": "error",
    "no-unused-vars": "error"
  }
}
```

### **3. 持续改进**

#### **规范更新**
- 每季度回顾和更新开发规范
- 收集团队反馈，持续优化规范内容
- 根据项目发展调整规范要求

#### **最佳实践分享**
- 定期分享代码清理和优化经验
- 建立团队知识库，积累最佳实践
- 鼓励创新，但要保持规范一致性

---

## ✅ 规范遵循承诺

我承诺在LinkHub Pro项目开发中严格遵循本开发规范，包括但不限于：

- ✅ 不提交任何调试文件和临时文件
- ✅ 及时清理调试代码和无用注释
- ✅ 遵循文件命名和代码结构规范
- ✅ 保持文档更新和项目整洁
- ✅ 积极参与代码审查和规范改进

**签署人**: _______________  
**签署时间**: _______________  

---

## 📞 联系方式

如有规范相关问题，请联系：
- **技术负责人**: [技术负责人联系方式]
- **项目经理**: [项目经理联系方式]
- **开发团队**: [团队联系方式]

---

**文档版本**: v1.0  
**最后更新**: 2025-08-08  
**下次审查**: 2025-11-08
