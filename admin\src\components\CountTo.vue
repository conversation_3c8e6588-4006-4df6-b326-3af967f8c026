<template>
  <span class="count-to" :class="{ 'counting': isCounting }">
    {{ displayValue }}
  </span>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  startVal: {
    type: Number,
    default: 0
  },
  endVal: {
    type: Number,
    required: true
  },
  duration: {
    type: Number,
    default: 3000
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  decimals: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0
  },
  decimal: {
    type: String,
    default: '.'
  },
  separator: {
    type: String,
    default: ','
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  useEasing: {
    type: Boolean,
    default: true
  },
  easingFn: {
    type: Function,
    default: (t, b, c, d) => {
      // easeInOutExpo
      if (t === 0) return b
      if (t === d) return b + c
      if ((t /= d / 2) < 1) {
        return c / 2 * Math.pow(2, 10 * (t - 1)) + b
      }
      return c / 2 * (-Math.pow(2, -10 * --t) + 2) + b
    }
  }
})

const emit = defineEmits(['mountedCallback', 'callback'])

const displayValue = ref('')
const isCounting = ref(false)
let localStartVal = props.startVal
let localDuration = props.duration
let printVal = null
let paused = false
let localStart = null
let remaining = null
let rAF = null

// 格式化数字
const formatNumber = (num) => {
  const numStr = num.toFixed(props.decimals)
  const parts = numStr.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
  return props.prefix + parts.join(props.decimal) + props.suffix
}

// 缓动函数
const easeInOutExpo = (t, b, c, d) => {
  if (t === 0) return b
  if (t === d) return b + c
  if ((t /= d / 2) < 1) {
    return c / 2 * Math.pow(2, 10 * (t - 1)) + b
  }
  return c / 2 * (-Math.pow(2, -10 * --t) + 2) + b
}

// 计数函数
const count = (timestamp) => {
  if (!localStart) localStart = timestamp
  
  const progress = timestamp - localStart
  remaining = localDuration - progress

  if (props.useEasing) {
    if (localStartVal === props.endVal) {
      printVal = props.endVal
    } else {
      printVal = props.easingFn(progress, localStartVal, props.endVal - localStartVal, localDuration)
    }
  } else {
    printVal = localStartVal + (props.endVal - localStartVal) * (progress / localDuration)
  }

  if (localStartVal > props.endVal) {
    printVal = printVal < props.endVal ? props.endVal : printVal
  } else {
    printVal = printVal > props.endVal ? props.endVal : printVal
  }

  displayValue.value = formatNumber(printVal)

  if (progress < localDuration) {
    rAF = requestAnimationFrame(count)
  } else {
    isCounting.value = false
    emit('callback')
  }
}

// 开始计数
const start = () => {
  if (isCounting.value) return
  
  isCounting.value = true
  localStart = null
  localDuration = props.duration
  localStartVal = props.startVal
  rAF = requestAnimationFrame(count)
}

// 暂停计数
const pause = () => {
  if (!isCounting.value) return
  
  paused = true
  cancelAnimationFrame(rAF)
}

// 恢复计数
const resume = () => {
  if (!paused) return
  
  paused = false
  localStart = null
  localDuration = remaining
  localStartVal = printVal
  rAF = requestAnimationFrame(count)
}

// 重置计数
const reset = () => {
  localStart = null
  cancelAnimationFrame(rAF)
  displayValue.value = formatNumber(props.startVal)
  isCounting.value = false
}

// 重新开始
const restart = () => {
  reset()
  start()
}

// 监听结束值变化
watch(() => props.endVal, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    if (props.autoplay) {
      restart()
    } else {
      displayValue.value = formatNumber(newVal)
    }
  }
})

// 监听开始值变化
watch(() => props.startVal, () => {
  if (props.autoplay) {
    restart()
  }
})

onMounted(() => {
  if (props.autoplay) {
    start()
  } else {
    displayValue.value = formatNumber(props.startVal)
  }
  emit('mountedCallback')
})

// 暴露方法给父组件
defineExpose({
  start,
  pause,
  resume,
  reset,
  restart
})
</script>

<style lang="scss" scoped>
.count-to {
  display: inline-block;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.counting {
    color: #1890ff;
    text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 数字动画效果
.count-to {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
  }
  
  &.counting::before {
    transform: translateX(100%);
  }
}

// 响应式字体大小
@media (max-width: 768px) {
  .count-to {
    font-size: 0.9em;
  }
}
</style>