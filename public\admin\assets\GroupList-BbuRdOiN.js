import{_ as e,u as a,k as l}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                */import{r as t,c as s,L as n,d as o,y as u,l as r,a2 as d,z as i,t as c,E as m,D as p,k as _,u as v,F as f,Y as g,a3 as b,n as h,e as y,B as w,A as V,af as k,a6 as x,G as C,C as q}from"./vue-vendor-Dy164gUc.js";import{at as U,bp as D,aZ as z,a_ as j,bq as $,aM as I,b9 as T,b8 as M,br as A,bA as B,T as S,aw as O,bm as L,bB as F,bs as G,a$ as P,U as R,ay as Q,Q as H,ao as E,an as Y,bC as N,aT as K,aB as J,aC as Z,bD as W,bE as X,az as ee,a4 as ae,bw as le,bi as te,aS as se,bh as ne,bx as oe,R as ue,aY as re,a6 as de,ax as ie,bn as ce,ab as me,bz as pe,b1 as _e,aR as ve,a8 as fe,ac as ge,aL as be,bF as he,bG as ye,aU as we,bH as Ve,bI as ke,bJ as xe,o as Ce,ap as qe,bK as Ue,bL as De,bM as ze,bN as je,am as $e,bv as Ie,Y as Te,bO as Me,bb as Ae,bP as Be,a0 as Se,$ as Oe,bQ as Le,aV as Fe,by as Ge,bR as Pe,ak as Re,bS as Qe,bc as He,aA as Ee,p as Ye}from"./element-plus-h2SQQM64.js";/* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                        */import{u as Ne,c as Ke,g as Je,a as Ze,b as We,d as Xe,e as ea,f as aa,h as la}from"./community-DNWNbya4.js";import{R as ta}from"./RichTextEditor-Cp69n7mq.js";import{G as sa}from"./GroupCreateForm-CUdmzNwa.js";import na from"./UserProfile-D2NIrp8S.js";/* empty css                 *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                         */import{I as oa}from"./ImageUpload-B-U8MD1C.js";/* empty css                         */import{f as ua}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";/* empty css                          */const ra={class:"avatar-upload"},da=["src"],ia={class:"qrcode-upload"},ca=["src"],ma={class:"dialog-footer"},pa={__name:"GroupDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:l}){const y=e,w=l,V=a(),k=t(null),x=t(null),C=t(!1),q=t(!1),E=t(""),Y=s({get:()=>y.modelValue,set:e=>w("update:modelValue",e)}),N=s(()=>y.groupData&&y.groupData.id),K=s(()=>"/api/v1/upload/image"),J=s(()=>({Authorization:`Bearer ${V.token}`})),Z=n({type:"normal",auto_city_replace:0,read_count_display:"10万+",like_count:888,want_see_count:666,button_title:"立即加入群聊",avatar_library:"qq",status:"active"}),W=n({name:"",category:"",description:"",price:0,max_members:500,avatar:"",qr_code:"",status:1,is_recommended:!1,tags:[],announcement:""}),X={name:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"群组名称长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择群组分类",trigger:"change"}],price:[{required:!0,message:"请输入入群价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],max_members:[{required:!0,message:"请输入最大成员数",trigger:"blur"},{type:"number",min:1,max:500,message:"成员数在 1 到 500 之间",trigger:"blur"}],description:[{max:200,message:"描述长度不能超过 200 个字符",trigger:"blur"}]};o(()=>y.groupData,e=>{e&&Object.keys(e).length>0?Object.assign(W,{name:e.name||"",category:e.category||"",description:e.description||"",price:e.price||0,max_members:e.max_members||500,avatar:e.avatar||"",qr_code:e.qr_code||"",status:void 0!==e.status?e.status:1,is_recommended:e.is_recommended||!1,tags:e.tags||[],announcement:e.announcement||""}):Object.assign(W,{name:"",category:"",description:"",price:0,max_members:500,avatar:"",qr_code:"",status:1,is_recommended:!1,tags:[],announcement:""})},{immediate:!0,deep:!0});const ee=e=>{e.success?(W.avatar=e.data.url,H.success("头像上传成功")):H.error("头像上传失败")},ae=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<2;return a?!!l||(H.error("图片大小不能超过 2MB!"),!1):(H.error("只能上传图片文件!"),!1)},le=e=>{e.success?(W.qr_code=e.data.url,H.success("二维码上传成功")):H.error("二维码上传失败")},te=e=>ae(e),se=()=>{q.value=!0,h(()=>{x.value?.focus()})},ne=()=>{E.value&&!W.tags.includes(E.value)&&W.tags.push(E.value),q.value=!1,E.value=""},oe=async()=>{if(k.value)try{await k.value.validate(),C.value=!0;const e={...W};N.value?(await Ne(y.groupData.id,e),H.success("群组更新成功")):(await Ke(e),H.success("群组创建成功")),w("success"),de()}catch(e){console.error("提交失败:",e),e.response?.data?.message?H.error(e.response.data.message):H.error(N.value?"更新失败":"创建失败")}finally{C.value=!1}},ue=e=>{w("success",e),Y.value=!1},re=()=>{Y.value=!1},de=()=>{k.value?.resetFields(),Y.value=!1};return(e,a)=>{const l=I,t=$,s=j,n=M,o=T,h=z,y=A,w=S,V=B,H=F,ie=L,ce=G,me=P,pe=U,_e=D,ve=Q;return r(),u(ve,{modelValue:Y.value,"onUpdate:modelValue":a[9]||(a[9]=e=>Y.value=e),title:N.value?"编辑群组":"创建群组",width:"900px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:de,class:"unified-group-dialog"},d({default:i(()=>[N.value?(r(),u(_e,{key:0,ref_key:"formRef",ref:k,model:W,rules:X,"label-width":"100px",class:"group-form"},{default:i(()=>[m(h,{gutter:20},{default:i(()=>[m(s,{span:12},{default:i(()=>[m(t,{label:"群组名称",prop:"name"},{default:i(()=>[m(l,{modelValue:W.name,"onUpdate:modelValue":a[0]||(a[0]=e=>W.name=e),placeholder:"请输入群组名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),m(s,{span:12},{default:i(()=>[m(t,{label:"群组分类",prop:"category"},{default:i(()=>[m(o,{modelValue:W.category,"onUpdate:modelValue":a[1]||(a[1]=e=>W.category=e),placeholder:"请选择分类",style:{width:"100%"}},{default:i(()=>[m(n,{label:"创业交流",value:"startup"}),m(n,{label:"投资理财",value:"finance"}),m(n,{label:"科技互联网",value:"tech"}),m(n,{label:"教育培训",value:"education"}),m(n,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(h,{gutter:20},{default:i(()=>[m(s,{span:12},{default:i(()=>[m(t,{label:"入群价格",prop:"price"},{default:i(()=>[m(y,{modelValue:W.price,"onUpdate:modelValue":a[2]||(a[2]=e=>W.price=e),min:0,max:9999,precision:2,style:{width:"100%"},placeholder:"0.00"},null,8,["modelValue"])]),_:1})]),_:1}),m(s,{span:12},{default:i(()=>[m(t,{label:"最大成员数",prop:"max_members"},{default:i(()=>[m(y,{modelValue:W.max_members,"onUpdate:modelValue":a[3]||(a[3]=e=>W.max_members=e),min:1,max:500,style:{width:"100%"},placeholder:"500"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(t,{label:"群组描述",prop:"description"},{default:i(()=>[m(l,{modelValue:W.description,"onUpdate:modelValue":a[4]||(a[4]=e=>W.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),m(h,{gutter:20},{default:i(()=>[m(s,{span:12},{default:i(()=>[m(t,{label:"群组头像"},{default:i(()=>[c("div",ra,[m(V,{class:"avatar-uploader",action:K.value,headers:J.value,"show-file-list":!1,"on-success":ee,"before-upload":ae,accept:"image/*"},{default:i(()=>[W.avatar?(r(),_("img",{key:0,src:W.avatar,class:"avatar",alt:"群组头像"},null,8,da)):(r(),u(w,{key:1,class:"avatar-uploader-icon"},{default:i(()=>[m(v(O))]),_:1}))]),_:1},8,["action","headers"]),a[10]||(a[10]=c("div",{class:"upload-tip"},"建议尺寸：200x200px",-1))])]),_:1})]),_:1}),m(s,{span:12},{default:i(()=>[m(t,{label:"群二维码"},{default:i(()=>[c("div",ia,[m(V,{class:"qrcode-uploader",action:K.value,headers:J.value,"show-file-list":!1,"on-success":le,"before-upload":te,accept:"image/*"},{default:i(()=>[W.qr_code?(r(),_("img",{key:0,src:W.qr_code,class:"qrcode",alt:"群二维码"},null,8,ca)):(r(),u(w,{key:1,class:"qrcode-uploader-icon"},{default:i(()=>[m(v(O))]),_:1}))]),_:1},8,["action","headers"]),a[11]||(a[11]=c("div",{class:"upload-tip"},"建议尺寸：400x400px",-1))])]),_:1})]),_:1})]),_:1}),m(h,{gutter:20},{default:i(()=>[m(s,{span:12},{default:i(()=>[m(t,{label:"群组状态",prop:"status"},{default:i(()=>[m(ie,{modelValue:W.status,"onUpdate:modelValue":a[5]||(a[5]=e=>W.status=e)},{default:i(()=>[m(H,{label:1},{default:i(()=>a[12]||(a[12]=[p("活跃",-1)])),_:1,__:[12]}),m(H,{label:0},{default:i(()=>a[13]||(a[13]=[p("暂停",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),m(s,{span:12},{default:i(()=>[m(t,{label:"是否推荐",prop:"is_recommended"},{default:i(()=>[m(ce,{modelValue:W.is_recommended,"onUpdate:modelValue":a[6]||(a[6]=e=>W.is_recommended=e),"active-text":"推荐","inactive-text":"不推荐"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(t,{label:"群组标签"},{default:i(()=>[(r(!0),_(f,null,g(W.tags,e=>(r(),u(me,{key:e,closable:"",onClose:a=>(e=>{const a=W.tags.indexOf(e);a>-1&&W.tags.splice(a,1)})(e),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:i(()=>[p(R(e),1)]),_:2},1032,["onClose"]))),128)),q.value?(r(),u(l,{key:0,ref_key:"inputRef",ref:x,modelValue:E.value,"onUpdate:modelValue":a[7]||(a[7]=e=>E.value=e),size:"small",style:{width:"100px"},onKeyup:b(ne,["enter"]),onBlur:ne},null,8,["modelValue"])):(r(),u(pe,{key:1,size:"small",onClick:se},{default:i(()=>a[14]||(a[14]=[p("+ 添加标签",-1)])),_:1,__:[14]}))]),_:1}),m(t,{label:"群组公告"},{default:i(()=>[m(ta,{modelValue:W.announcement,"onUpdate:modelValue":a[8]||(a[8]=e=>W.announcement=e),height:200,placeholder:"请输入群组公告"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])):(r(),u(sa,{key:1,mode:"dialog","user-role":"admin","default-values":Z,"show-preview":!0,"show-templates":!0,onSuccess:ue,onCancel:re},null,8,["default-values"]))]),_:2},[N.value?{name:"footer",fn:i(()=>[c("div",ma,[m(pe,{onClick:de},{default:i(()=>a[15]||(a[15]=[p("取消",-1)])),_:1,__:[15]}),m(pe,{type:"primary",loading:C.value,onClick:oe},{default:i(()=>a[16]||(a[16]=[p(" 更新 ",-1)])),_:1,__:[16]},8,["loading"])])]),key:"0"}:void 0]),1032,["modelValue","title"])}}},_a=e(pa,[["__scopeId","data-v-3fb94d21"]]),va={class:"group-member-manager"},fa={class:"member-stats"},ga={class:"stat-card"},ba={class:"stat-icon"},ha={class:"stat-content"},ya={class:"stat-value"},wa={class:"stat-card"},Va={class:"stat-icon"},ka={class:"stat-content"},xa={class:"stat-value"},Ca={class:"stat-card"},qa={class:"stat-icon"},Ua={class:"stat-content"},Da={class:"stat-value"},za={class:"stat-card"},ja={class:"stat-icon"},$a={class:"stat-content"},Ia={class:"stat-value"},Ta={class:"member-toolbar"},Ma={class:"toolbar-right"},Aa={class:"member-filters"},Ba={class:"member-list"},Sa={class:"member-info"},Oa={class:"member-details"},La={class:"member-name"},Fa={class:"member-phone"},Ga={class:"pagination-container"},Pa={__name:"GroupMemberManager",props:{groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},setup(e){const a=e,l=t(!1),s=t([]),o=t(0),d=t([]),h=t(!1),k=t(null),x=t({total_members:0,active_members:0,new_members_today:0,left_members_today:0}),C=n({page:1,limit:20,keyword:"",status:""}),q=async()=>{l.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),s.value=[{id:1,username:"user001",nickname:"张三",phone:"138****1234",avatar:"",joined_at:"2024-01-15 10:30:00",status:"active",tags:["核心用户","活跃"],inputVisible:!1,inputValue:""},{id:2,username:"user002",nickname:"李四",phone:"139****5678",avatar:"",joined_at:"2024-01-16 14:20:00",status:"active",tags:["潜在客户"],inputVisible:!1,inputValue:""},{id:3,username:"user003",nickname:"王五",phone:"137****4321",avatar:"",joined_at:"2024-02-10 09:00:00",status:"left",tags:[],inputVisible:!1,inputValue:""}],o.value=2,x.value={total_members:a.groupData.member_count||0,active_members:Math.floor(.8*(a.groupData.member_count||0)),new_members_today:5,left_members_today:1}}catch(e){H.error("获取成员列表失败")}finally{l.value=!1}},A=()=>{H.info("添加成员功能开发中")},B=()=>{H.info("导出成员功能开发中")},L=e=>{d.value=e},F=()=>{Object.assign(C,{page:1,limit:20,keyword:"",status:""}),q()},G=e=>{const a=d.value.map(e=>e.id);"tag"===e?ue.prompt("请输入要批量添加的标签","批量打标签",{confirmButtonText:"确定",cancelButtonText:"取消"}).then(({value:e})=>{H.success(`已为 ${a.length} 位成员添加标签: "${e}"`),q()}).catch(()=>{}):"mute"===e?ue.confirm(`确定要批量禁言这 ${a.length} 位成员吗？`,"确认禁言").then(()=>{H.success(`已成功禁言 ${a.length} 位成员`)}).catch(()=>{}):"kick"===e&&ue.confirm(`确定要批量移除这 ${a.length} 位成员吗？`,"确认移除",{type:"warning"}).then(()=>{H.success(`已成功移除 ${a.length} 位成员`),q()}).catch(()=>{})},Q=e=>{e.inputValue&&e.tags.push(e.inputValue),e.inputVisible=!1,e.inputValue=""},re=e=>({active:"正常",left:"已退出",kicked:"被踢出"}[e]||"未知");return y(()=>{q()}),(e,a)=>{const t=S,n=j,y=z,de=U,ie=Z,ce=J,me=ee,pe=I,_e=$,ve=M,fe=T,ge=D,be=te,he=se,ye=P,we=ne,Ve=oe,ke=le;return r(),_("div",va,[c("div",fa,[m(y,{gutter:20},{default:i(()=>[m(n,{span:6},{default:i(()=>[c("div",ga,[c("div",ba,[m(t,null,{default:i(()=>[m(v(E))]),_:1})]),c("div",ha,[c("div",ya,R(x.value.total_members),1),a[5]||(a[5]=c("div",{class:"stat-label"},"总成员数",-1))])])]),_:1}),m(n,{span:6},{default:i(()=>[c("div",wa,[c("div",Va,[m(t,null,{default:i(()=>[m(v(Y))]),_:1})]),c("div",ka,[c("div",xa,R(x.value.active_members),1),a[6]||(a[6]=c("div",{class:"stat-label"},"活跃成员",-1))])])]),_:1}),m(n,{span:6},{default:i(()=>[c("div",Ca,[c("div",qa,[m(t,null,{default:i(()=>[m(v(O))]),_:1})]),c("div",Ua,[c("div",Da,R(x.value.new_members_today),1),a[7]||(a[7]=c("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),m(n,{span:6},{default:i(()=>[c("div",za,[c("div",ja,[m(t,null,{default:i(()=>[m(v(N))]),_:1})]),c("div",$a,[c("div",Ia,R(x.value.left_members_today),1),a[8]||(a[8]=c("div",{class:"stat-label"},"今日退出",-1))])])]),_:1})]),_:1})]),c("div",Ta,[a[14]||(a[14]=c("div",{class:"toolbar-left"},[c("h4",null,"成员列表")],-1)),c("div",Ma,[d.value.length>0?(r(),u(me,{key:0,onCommand:G},{dropdown:i(()=>[m(ce,null,{default:i(()=>[m(ie,{command:"tag"},{default:i(()=>[m(t,null,{default:i(()=>[m(v(W))]),_:1}),a[9]||(a[9]=p("批量打标签 ",-1))]),_:1,__:[9]}),m(ie,{command:"mute"},{default:i(()=>[m(t,null,{default:i(()=>[m(v(X))]),_:1}),a[10]||(a[10]=p("批量禁言 ",-1))]),_:1,__:[10]}),m(ie,{command:"kick",divided:""},{default:i(()=>[m(t,null,{default:i(()=>[m(v(N))]),_:1}),a[11]||(a[11]=p("批量移除 ",-1))]),_:1,__:[11]})]),_:1})]),default:i(()=>[m(de,{type:"primary"},{default:i(()=>[p(" 批量操作 ("+R(d.value.length)+")",1),m(t,{class:"el-icon--right"},{default:i(()=>[m(v(K))]),_:1})]),_:1})]),_:1})):w("",!0),m(de,{type:"primary",onClick:A},{default:i(()=>[m(t,null,{default:i(()=>[m(v(O))]),_:1}),a[12]||(a[12]=p(" 添加成员 ",-1))]),_:1,__:[12]}),m(de,{onClick:B},{default:i(()=>[m(t,null,{default:i(()=>[m(v(ae))]),_:1}),a[13]||(a[13]=p(" 导出成员 ",-1))]),_:1,__:[13]})])]),c("div",Aa,[m(ge,{inline:!0,model:C},{default:i(()=>[m(_e,{label:"搜索"},{default:i(()=>[m(pe,{modelValue:C.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>C.keyword=e),placeholder:"用户名/手机号",clearable:"",onKeyup:b(q,["enter"])},null,8,["modelValue"])]),_:1}),m(_e,{label:"状态"},{default:i(()=>[m(fe,{modelValue:C.status,"onUpdate:modelValue":a[1]||(a[1]=e=>C.status=e),placeholder:"全部状态",clearable:""},{default:i(()=>[m(ve,{label:"正常",value:"active"}),m(ve,{label:"已退出",value:"left"}),m(ve,{label:"被踢出",value:"kicked"})]),_:1},8,["modelValue"])]),_:1}),m(_e,null,{default:i(()=>[m(de,{type:"primary",onClick:q},{default:i(()=>a[15]||(a[15]=[p("搜索",-1)])),_:1,__:[15]}),m(de,{onClick:F},{default:i(()=>a[16]||(a[16]=[p("重置",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),c("div",Ba,[V((r(),u(we,{data:s.value,style:{width:"100%"},onSelectionChange:L},{default:i(()=>[m(be,{type:"selection",width:"55"}),m(be,{label:"成员信息","min-width":"200"},{default:i(({row:e})=>[c("div",Sa,[m(he,{src:e.avatar,size:40},{default:i(()=>[m(t,null,{default:i(()=>[m(v(E))]),_:1})]),_:2},1032,["src"]),c("div",Oa,[c("div",La,R(e.nickname||e.username),1),c("div",Fa,R(e.phone||"未绑定"),1)])])]),_:1}),m(be,{label:"加入时间",width:"160"},{default:i(({row:e})=>{return[p(R((a=e.joined_at,a?new Date(a).toLocaleString("zh-CN"):"未知")),1)];var a}),_:1}),m(be,{label:"成员标签","min-width":"150"},{default:i(({row:e})=>[(r(!0),_(f,null,g(e.tags,a=>(r(),u(ye,{key:a,class:"member-tag",size:"small",closable:"",onClose:l=>((e,a)=>{e.tags.splice(e.tags.indexOf(a),1)})(e,a)},{default:i(()=>[p(R(a),1)]),_:2},1032,["onClose"]))),128)),e.inputVisible?(r(),u(pe,{key:0,modelValue:e.inputValue,"onUpdate:modelValue":a=>e.inputValue=a,class:"tag-input",size:"small",onKeyup:b(a=>Q(e),["enter"]),onBlur:a=>Q(e)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(r(),u(de,{key:1,class:"button-new-tag",size:"small",onClick:a=>(e=>{e.inputVisible=!0})(e)},{default:i(()=>a[17]||(a[17]=[p(" + 新标签 ",-1)])),_:2,__:[17]},1032,["onClick"]))]),_:1}),m(be,{label:"状态",width:"100"},{default:i(({row:e})=>{return[m(ye,{type:(a=e.status,{active:"success",left:"info",kicked:"danger"}[a]||"info")},{default:i(()=>[p(R(re(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),m(be,{label:"操作",width:"200",fixed:"right"},{default:i(({row:e})=>[m(de,{link:"",type:"primary",size:"small",onClick:a=>{return l=e,k.value=l.id,void(h.value=!0);var l}},{default:i(()=>a[18]||(a[18]=[p(" 用户画像 ",-1)])),_:2,__:[18]},1032,["onClick"]),m(de,{link:"",type:"primary",size:"small",onClick:a=>{return l=e,void H.info(`查看成员: ${l.nickname}`);var l}},{default:i(()=>a[19]||(a[19]=[p(" 查看 ",-1)])),_:2,__:[19]},1032,["onClick"]),m(de,{link:"",type:"danger",size:"small",onClick:a=>{return l=e,void ue.confirm(`确定要移除成员 "${l.nickname}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{H.success("成员移除成功"),q()});var l}},{default:i(()=>a[20]||(a[20]=[p(" 移除 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,l.value]]),c("div",Ga,[m(Ve,{"current-page":C.page,"onUpdate:currentPage":a[2]||(a[2]=e=>C.page=e),"page-size":C.limit,"onUpdate:pageSize":a[3]||(a[3]=e=>C.limit=e),total:o.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:q},null,8,["current-page","page-size","total"])])]),m(na,{visible:h.value,"user-id":k.value,"onUpdate:visible":a[4]||(a[4]=e=>h.value=e)},null,8,["visible","user-id"])])}}},Ra=e(Pa,[["__scopeId","data-v-4e29aac0"]]),Qa={class:"analytics-content"},Ha={class:"stat-item"},Ea={class:"stat-value"},Ya={class:"stat-item"},Na={class:"stat-value"},Ka={class:"stat-item"},Ja={class:"stat-value"},Za={class:"stat-item"},Wa={class:"stat-value"},Xa={class:"chart-placeholder"},el={class:"placeholder-content"},al={class:"chart-placeholder"},ll={class:"placeholder-content"},tl={__name:"GroupAnalyticsSimple",props:{visible:{type:Boolean,default:!1},groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:a}){const l=e,s=a,n=t([{date:"2024-01-01",newMembers:12,activeMembers:156,messages:234,engagement:78.5},{date:"2024-01-02",newMembers:8,activeMembers:162,messages:198,engagement:82.1},{date:"2024-01-03",newMembers:15,activeMembers:171,messages:267,engagement:85.3},{date:"2024-01-04",newMembers:6,activeMembers:168,messages:189,engagement:79.8},{date:"2024-01-05",newMembers:11,activeMembers:175,messages:245,engagement:88.2}]),d=()=>{s("update:visible",!1)};return o(()=>l.visible,e=>{e&&console.log("群组分析打开，群组ID:",l.groupId)}),(a,l)=>{const t=re,s=j,o=z,_=S,f=te,g=ne,b=ie;return r(),u(b,{"model-value":e.visible,title:"群组分析",size:"60%","onUpdate:modelValue":l[0]||(l[0]=e=>a.$emit("update:visible",e)),onClose:d},{default:i(()=>[c("div",Qa,[m(o,{gutter:20,class:"stats-row"},{default:i(()=>[m(s,{span:6},{default:i(()=>[m(t,{class:"stat-card"},{default:i(()=>[c("div",Ha,[c("div",Ea,R(e.groupData.memberCount||0),1),l[1]||(l[1]=c("div",{class:"stat-label"},"总成员数",-1))])]),_:1})]),_:1}),m(s,{span:6},{default:i(()=>[m(t,{class:"stat-card"},{default:i(()=>[c("div",Ya,[c("div",Na,R(e.groupData.monthlyActiveUsers||0),1),l[2]||(l[2]=c("div",{class:"stat-label"},"月活跃用户",-1))])]),_:1})]),_:1}),m(s,{span:6},{default:i(()=>[m(t,{class:"stat-card"},{default:i(()=>[c("div",Ka,[c("div",Ja,R(e.groupData.dailyMessages||0),1),l[3]||(l[3]=c("div",{class:"stat-label"},"日均消息数",-1))])]),_:1})]),_:1}),m(s,{span:6},{default:i(()=>[m(t,{class:"stat-card"},{default:i(()=>[c("div",Za,[c("div",Wa,R(((e.groupData.memberCount||0)/(e.groupData.maxMembers||500)*100).toFixed(1))+"%",1),l[4]||(l[4]=c("div",{class:"stat-label"},"群组满员率",-1))])]),_:1})]),_:1})]),_:1}),m(o,{gutter:20,style:{"margin-top":"20px"}},{default:i(()=>[m(s,{span:12},{default:i(()=>[m(t,{class:"chart-card",shadow:"never"},{header:i(()=>l[5]||(l[5]=[c("span",{class:"chart-title"},"成员增长趋势",-1)])),default:i(()=>[c("div",Xa,[c("div",el,[m(_,{size:"48"},{default:i(()=>[m(v(de))]),_:1}),l[6]||(l[6]=c("p",null,"成员增长趋势图",-1)),l[7]||(l[7]=c("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1}),m(s,{span:12},{default:i(()=>[m(t,{class:"chart-card",shadow:"never"},{header:i(()=>l[8]||(l[8]=[c("span",{class:"chart-title"},"活跃度分析",-1)])),default:i(()=>[c("div",al,[c("div",ll,[m(_,{size:"48"},{default:i(()=>[m(v(de))]),_:1}),l[9]||(l[9]=c("p",null,"活跃度分析图",-1)),l[10]||(l[10]=c("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1})]),_:1}),m(t,{style:{"margin-top":"20px"},shadow:"never"},{header:i(()=>l[11]||(l[11]=[c("span",null,"详细数据",-1)])),default:i(()=>[m(g,{data:n.value,style:{width:"100%"}},{default:i(()=>[m(f,{prop:"date",label:"日期",width:"120"}),m(f,{prop:"newMembers",label:"新增成员",width:"100"}),m(f,{prop:"activeMembers",label:"活跃成员",width:"100"}),m(f,{prop:"messages",label:"消息数",width:"100"}),m(f,{prop:"engagement",label:"参与度",width:"100"},{default:i(({row:e})=>[p(R(e.engagement)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["model-value"])}}},sl=e(tl,[["__scopeId","data-v-f8722904"]]),nl={class:"qrcode-container"},ol={class:"qrcode-tabs"},ul={key:0,class:"qrcode-display"},rl={key:0,class:"qrcode-image"},dl=["src"],il={class:"qrcode-info"},cl={class:"qrcode-url"},ml={key:1,class:"no-qrcode"},pl={key:1,class:"qrcode-display"},_l={key:0,class:"qrcode-image"},vl=["src"],fl={class:"qrcode-info"},gl={key:1,class:"no-qrcode"},bl={class:"qrcode-actions"},hl={class:"dialog-footer"},yl=e({__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","qrcode-updated"],setup(e,{emit:a}){const n=e,d=a,g=k(),b=s({get:()=>n.modelValue,set:e=>d("update:modelValue",e)}),h=t("promotion"),y=t(!1),V=t(""),x=t("");s(()=>`/api/v1/wechat-groups/${n.groupData.id}/qr-code`),s(()=>({Authorization:`Bearer ${l()}`})),o(b,e=>{e&&n.groupData.id&&q()});const C=e=>{h.value=e},q=async()=>{if(n.groupData.id){y.value=!0;try{const e=await fetch(`/api/admin/groups/${n.groupData.id}/promotion-link`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l()}`},body:JSON.stringify({enable_anti_block:!0,enable_short_link:!0,link_type:"promotion"})});if(!e.ok)throw new Error("API请求失败");const a=await e.json();if(200!==a.code||!a.data)throw new Error(a.message||"生成推广链接失败");{const e=a.data;x.value=e.short_url||e.anti_block_url||e.original_url,V.value=`https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(x.value)}`,console.log("✅ 防红推广二维码生成成功:",{groupId:n.groupData.id,originalUrl:e.original_url,antiBlockUrl:e.anti_block_url,shortUrl:e.short_url,finalUrl:x.value,antiBlockEnabled:e.anti_block_enabled,shortLinkEnabled:e.short_link_enabled,qrCode:V.value});const l=[];e.anti_block_enabled&&l.push("防红保护"),e.short_link_enabled&&l.push("短链接"),l.length>0?H.success(`推广链接生成成功，已启用：${l.join("、")}`):H.success("推广链接生成成功")}}catch(e){console.error("生成防红推广二维码失败:",e),console.log("⚠️ 防红系统不可用，使用降级方案");const a=n.groupData.id;x.value=`${window.location.origin}/landing/group/${a}`,V.value=`https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(x.value)}`,H.warning("防红系统暂时不可用，已生成普通推广链接")}finally{y.value=!1}}},D=()=>{q()},z=async()=>{if(x.value)try{await navigator.clipboard.writeText(x.value),H.success("推广链接已复制到剪贴板")}catch(e){const a=document.createElement("textarea");a.value=x.value,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),H.success("推广链接已复制到剪贴板")}else H.warning("推广链接还未生成")},j=(e,a)=>{if(!e)return;const l=document.createElement("a");l.href=e,l.download=`${n.groupData.title||n.groupData.name}-${a}.png`,document.body.appendChild(l),l.click(),document.body.removeChild(l),H.success(`${a}下载成功`)},$=()=>{g.push("/promotion/links"),b.value=!1},T=()=>{g.push(`/community/groups/edit/${n.groupData.id}`),b.value=!1};return(a,l)=>{const t=ce,s=L,n=S,o=U,d=I,g=pe,k=_e,M=Q;return r(),u(M,{modelValue:b.value,"onUpdate:modelValue":l[5]||(l[5]=e=>b.value=e),title:"推广二维码",width:"600px","destroy-on-close":!0},{footer:i(()=>[c("div",hl,[m(o,{onClick:l[4]||(l[4]=e=>b.value=!1)},{default:i(()=>l[18]||(l[18]=[p("关闭",-1)])),_:1,__:[18]})])]),default:i(()=>[c("div",nl,[c("div",ol,[m(s,{modelValue:h.value,"onUpdate:modelValue":l[0]||(l[0]=e=>h.value=e),onChange:C},{default:i(()=>[m(t,{value:"promotion"},{default:i(()=>l[6]||(l[6]=[p("推广二维码",-1)])),_:1,__:[6]}),m(t,{value:"entry"},{default:i(()=>l[7]||(l[7]=[p("入群二维码",-1)])),_:1,__:[7]})]),_:1},8,["modelValue"])]),"promotion"===h.value?(r(),_("div",ul,[V.value?(r(),_("div",rl,[c("img",{src:V.value,alt:"推广二维码"},null,8,dl),c("div",il,[c("h4",null,R(e.groupData.title||e.groupData.name),1),l[8]||(l[8]=c("p",null,"扫码访问落地页，引导用户付费",-1)),c("div",cl,[m(d,{modelValue:x.value,"onUpdate:modelValue":l[1]||(l[1]=e=>x.value=e),readonly:"",size:"small",placeholder:"推广链接"},{append:i(()=>[m(o,{onClick:z,size:"small"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(me))]),_:1})]),_:1})]),_:1},8,["modelValue"])])])])):(r(),_("div",ml,[m(g,{description:"正在生成推广二维码...","image-size":100}),m(o,{type:"primary",onClick:q,loading:y.value},{default:i(()=>l[9]||(l[9]=[p(" 生成推广二维码 ",-1)])),_:1,__:[9]},8,["loading"])]))])):(r(),_("div",pl,[e.groupData.qr_code?(r(),_("div",_l,[c("img",{src:e.groupData.qr_code,alt:"入群二维码"},null,8,vl),c("div",fl,[c("h4",null,R(e.groupData.title||e.groupData.name),1),l[10]||(l[10]=c("p",null,"付费后展示给用户的入群二维码",-1)),m(k,{title:"注意：这是付费后内容，只有完成支付的用户才能看到",type:"warning",closable:!1,"show-icon":""})])])):(r(),_("div",gl,[m(g,{description:"暂未配置入群二维码","image-size":100}),l[11]||(l[11]=c("p",{class:"tip-text"},'请在群组编辑页面的"付费后内容配置"中上传入群二维码',-1))]))])),c("div",bl,["promotion"===h.value?(r(),_(f,{key:0},[V.value?(r(),u(o,{key:0,onClick:l[2]||(l[2]=e=>j(V.value,"推广二维码"))},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ae))]),_:1}),l[12]||(l[12]=p(" 下载推广二维码 ",-1))]),_:1,__:[12]})):w("",!0),m(o,{onClick:z},{default:i(()=>[m(n,null,{default:i(()=>[m(v(me))]),_:1}),l[13]||(l[13]=p(" 复制推广链接 ",-1))]),_:1,__:[13]}),m(o,{onClick:D,loading:y.value},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ve))]),_:1}),l[14]||(l[14]=p(" 刷新二维码 ",-1))]),_:1,__:[14]},8,["loading"]),m(o,{type:"success",onClick:$},{default:i(()=>[m(n,null,{default:i(()=>[m(v(fe))]),_:1}),l[15]||(l[15]=p(" 推广链接管理 ",-1))]),_:1,__:[15]})],64)):(r(),_(f,{key:1},[m(o,{type:"primary",onClick:T},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ge))]),_:1}),l[16]||(l[16]=p(" 配置入群内容 ",-1))]),_:1,__:[16]}),e.groupData.qr_code?(r(),u(o,{key:0,onClick:l[3]||(l[3]=a=>j(e.groupData.qr_code,"入群二维码"))},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ae))]),_:1}),l[17]||(l[17]=p(" 下载入群二维码 ",-1))]),_:1,__:[17]})):w("",!0)],64))])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-95e83ea2"]]),wl={class:"content-template-library"},Vl={class:"card-header"},kl={class:"card-title"},xl={class:"header-actions"},Cl={class:"template-filters"},ql={class:"template-list"},Ul=["onClick"],Dl={class:"template-header"},zl={class:"template-content"},jl={class:"template-title"},$l={class:"template-preview"},Il={class:"template-footer"},Tl={class:"usage-count"},Ml=e({__name:"ContentTemplateLibrary",emits:["template-selected"],setup(e,{emit:a}){const l=a,n=t(""),o=t(""),d=t(""),b=t([{id:1,title:"互联网技术交流群标题模板",category:"title",industry:"tech",content:"{city}程序员技术交流群 - 汇聚IT精英，分享前沿技术",usageCount:156}]),h=s(()=>b.value.filter(e=>{const a=!n.value||e.category===n.value,l=!o.value||e.industry===o.value,t=!d.value||e.title.includes(d.value)||e.content.includes(d.value);return a&&l&&t})),y=()=>{H.info("创建模板功能开发中")},w=e=>({title:"标题",description:"描述",faq:"FAQ",reviews:"评论"}[e]||e);return(e,a)=>{const t=S,s=U,b=M,V=T,k=j,x=I,C=z,q=P,D=re;return r(),_("div",wl,[m(D,{class:"library-card",shadow:"never"},{header:i(()=>[c("div",Vl,[c("span",kl,[m(t,null,{default:i(()=>[m(v(he))]),_:1}),a[3]||(a[3]=p(" 内容模板库 ",-1))]),c("div",xl,[m(s,{type:"primary",size:"small",onClick:y},{default:i(()=>[m(t,null,{default:i(()=>[m(v(O))]),_:1}),a[4]||(a[4]=p(" 新建模板 ",-1))]),_:1,__:[4]})])])]),default:i(()=>[c("div",Cl,[m(C,{gutter:20},{default:i(()=>[m(k,{span:8},{default:i(()=>[m(V,{modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=e=>n.value=e),placeholder:"选择分类",clearable:""},{default:i(()=>[m(b,{label:"全部分类",value:""}),m(b,{label:"群组标题",value:"title"}),m(b,{label:"群组描述",value:"description"}),m(b,{label:"FAQ问答",value:"faq"}),m(b,{label:"用户评论",value:"reviews"})]),_:1},8,["modelValue"])]),_:1}),m(k,{span:8},{default:i(()=>[m(V,{modelValue:o.value,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value=e),placeholder:"选择行业",clearable:""},{default:i(()=>[m(b,{label:"全部行业",value:""}),m(b,{label:"互联网/科技",value:"tech"}),m(b,{label:"金融/投资",value:"finance"}),m(b,{label:"教育/培训",value:"education"})]),_:1},8,["modelValue"])]),_:1}),m(k,{span:8},{default:i(()=>[m(x,{modelValue:d.value,"onUpdate:modelValue":a[2]||(a[2]=e=>d.value=e),placeholder:"搜索模板...",clearable:""},{prefix:i(()=>[m(t,null,{default:i(()=>[m(v(be))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),c("div",ql,[m(C,{gutter:20},{default:i(()=>[(r(!0),_(f,null,g(h.value,e=>(r(),u(k,{span:8,key:e.id},{default:i(()=>{return[c("div",{class:"template-card",onClick:a=>(e=>{l("template-selected",e),H.success(`已选择模板：${e.title}`)})(e)},[c("div",Dl,[m(q,{type:(a=e.category,{title:"primary",description:"success",faq:"warning",reviews:"info"}[a]||""),size:"small"},{default:i(()=>[p(R(w(e.category)),1)]),_:2},1032,["type"])]),c("div",zl,[c("h4",jl,R(e.title),1),c("p",$l,R(e.content.substring(0,100))+"...",1)]),c("div",Il,[c("span",Tl,"使用 "+R(e.usageCount)+" 次",1)])],8,Ul)];var a}),_:2},1024))),128))]),_:1})])]),_:1})])}}},[["__scopeId","data-v-e02fe381"]]),Al={class:"ai-content-generator"},Bl={class:"card-header"},Sl={class:"card-title"},Ol={class:"content-type-selector"},Ll={class:"generation-config"},Fl={class:"generation-actions"},Gl={key:0,class:"generation-results"},Pl={class:"results-list"},Rl={class:"result-header"},Ql={class:"result-index"},Hl={class:"result-actions"},El={class:"result-content"},Yl={class:"batch-actions"},Nl={key:1,class:"generation-history"},Kl={class:"history-item"},Jl={class:"history-type"},Zl={class:"history-content"},Wl=e({__name:"AIContentGenerator",emits:["content-generated"],setup(e,{emit:a}){const l=a,s=t("title"),o=t(!1),d=t([]),b=t([]),h=n({industry:"",audience:"",style:"professional",count:3,keywords:"",customPrompt:""}),y={title:{tech:["{city}程序员技术交流群","{city}互联网从业者聚集地","{city}IT精英学习成长群","{city}技术大牛经验分享群"],finance:["{city}投资理财交流群","{city}财富增值学习群","{city}金融精英圈","{city}理财规划师群"],education:["{city}学习成长交流群","{city}知识分享互助群","{city}教育资源共享群","{city}终身学习者联盟"]},description:{professional:"汇聚行业精英，分享前沿资讯，共同成长进步。严格筛选成员，确保高质量交流环境。",casual:"轻松愉快的交流氛围，大家一起聊天学习，分享生活和工作中的点点滴滴。",warm:"温馨的大家庭，互帮互助，共同进步。每个人都能在这里找到归属感。"},faq:[{question:"这个群主要讨论什么内容？",answer:"主要围绕{industry}相关话题，包括行业动态、经验分享、资源交换等。"},{question:"群里有什么规则吗？",answer:"禁止发广告、恶意刷屏，鼓励有价值的分享和讨论，营造良好的交流环境。"},{question:"如何更好地融入群聊？",answer:"主动参与讨论，分享有价值的内容，尊重其他成员，积极互动交流。"}],reviews:[{username:"行业专家",content:"群里的分享质量很高，学到了很多实用的知识和经验。",rating:5},{username:"资深从业者",content:"氛围很好，大家都很乐于分享，是个不错的学习平台。",rating:5},{username:"新手小白",content:"刚入行的时候加入的，得到了很多前辈的指导和帮助。",rating:4}]},V=e=>{console.log("切换内容类型:",e)},k=async()=>{if(h.industry&&h.audience){o.value=!0;try{await new Promise(e=>setTimeout(e,2e3));const e=[];for(let a=0;a<h.count;a++){const l=await x();e.push({id:Date.now()+a,content:l,rating:0,type:s.value})}d.value=e,b.value.unshift({timestamp:new Date,type:s.value,content:e[0].content,config:{...h}}),H.success(`成功生成 ${e.length} 个内容方案`)}catch(e){H.error("生成失败，请重试")}finally{o.value=!1}}else H.warning("请先选择行业类型和目标人群")},x=async()=>{const{industry:e,audience:a,style:l,keywords:t,customPrompt:n}=h;switch(s.value){case"title":return C(e,a,t);case"description":return q(l,e,n);case"faq":return D(e);case"reviews":return B();case"intro":return O(e,a);default:return"生成的内容"}},C=(e,a,l)=>{const t=y.title[e]||y.title.tech;let s=t[Math.floor(Math.random()*t.length)].replace("{city}","xxx");if(l){const e=l.split(",").map(e=>e.trim()),a=e[Math.floor(Math.random()*e.length)];s=s.replace("程序员",a).replace("投资理财",a)}return s},q=(e,a,l)=>{let t=y.description[e]||y.description.professional;return l&&(t+="\n\n"+l),t.replace("{industry}",E(a))},D=(e,a)=>y.faq.map(a=>({...a,answer:a.answer.replace("{industry}",E(e))})).map(e=>`${e.question}----${e.answer}`).join("\n"),B=(e,a)=>y.reviews.map(e=>`${e.username}----${e.content}----${e.rating}`).join("\n"),O=(e,a,l)=>`欢迎加入我们的${E(e)}交流群！这里汇聚了众多${Y(a)}，大家可以在这里分享经验、交流心得、互相学习。我们致力于打造一个高质量的交流平台，让每个成员都能在这里收获成长。`,F=()=>{d.value=[]},G=()=>{k()},Q=()=>{const e=d.value.map((e,a)=>`方案${a+1}:\n${e.content}\n\n`).join(""),a=new Blob([e],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`AI生成内容_${(new Date).toISOString().slice(0,10)}.txt`,t.click(),URL.revokeObjectURL(l)},E=e=>({tech:"互联网科技",finance:"金融投资",education:"教育培训",ecommerce:"电商零售",health:"健康医疗",entertainment:"娱乐游戏",realestate:"房产装修",food:"美食餐饮",travel:"旅游出行",other:"其他行业"}[e]||e),Y=e=>({students:"学生朋友",newcomers:"职场新人",experts:"资深专家",entrepreneurs:"创业者",mothers:"宝妈群体",seniors:"中老年朋友",youngpro:"年轻白领",freelancers:"自由职业者"}[e]||e);return(e,a)=>{const t=S,n=P,y=ce,x=L,C=M,q=T,D=$,B=j,O=z,E=A,Y=I,N=U,K=we,J=Ve,Z=xe,W=ke,X=re;return r(),_("div",Al,[m(X,{class:"generator-card",shadow:"never"},{header:i(()=>[c("div",Bl,[c("span",Sl,[m(t,null,{default:i(()=>[m(v(ye))]),_:1}),a[7]||(a[7]=p(" AI智能内容生成 ",-1))]),m(n,{type:"success",size:"small"},{default:i(()=>a[8]||(a[8]=[p("Beta",-1)])),_:1,__:[8]})])]),default:i(()=>[c("div",Ol,[m(x,{modelValue:s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>s.value=e),onChange:V},{default:i(()=>[m(y,{label:"title"},{default:i(()=>a[9]||(a[9]=[p("群组标题",-1)])),_:1,__:[9]}),m(y,{label:"description"},{default:i(()=>a[10]||(a[10]=[p("群组描述",-1)])),_:1,__:[10]}),m(y,{label:"faq"},{default:i(()=>a[11]||(a[11]=[p("FAQ问答",-1)])),_:1,__:[11]}),m(y,{label:"reviews"},{default:i(()=>a[12]||(a[12]=[p("用户评论",-1)])),_:1,__:[12]}),m(y,{label:"intro"},{default:i(()=>a[13]||(a[13]=[p("群组介绍",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),c("div",Ll,[m(O,{gutter:20},{default:i(()=>[m(B,{span:12},{default:i(()=>[m(D,{label:"行业类型"},{default:i(()=>[m(q,{modelValue:h.industry,"onUpdate:modelValue":a[1]||(a[1]=e=>h.industry=e),placeholder:"选择行业"},{default:i(()=>[m(C,{label:"互联网/科技",value:"tech"}),m(C,{label:"金融/投资",value:"finance"}),m(C,{label:"教育/培训",value:"education"}),m(C,{label:"电商/零售",value:"ecommerce"}),m(C,{label:"健康/医疗",value:"health"}),m(C,{label:"娱乐/游戏",value:"entertainment"}),m(C,{label:"房产/装修",value:"realestate"}),m(C,{label:"美食/餐饮",value:"food"}),m(C,{label:"旅游/出行",value:"travel"}),m(C,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),m(B,{span:12},{default:i(()=>[m(D,{label:"目标人群"},{default:i(()=>[m(q,{modelValue:h.audience,"onUpdate:modelValue":a[2]||(a[2]=e=>h.audience=e),placeholder:"选择目标人群"},{default:i(()=>[m(C,{label:"学生群体",value:"students"}),m(C,{label:"职场新人",value:"newcomers"}),m(C,{label:"资深专家",value:"experts"}),m(C,{label:"创业者",value:"entrepreneurs"}),m(C,{label:"宝妈群体",value:"mothers"}),m(C,{label:"中老年人",value:"seniors"}),m(C,{label:"年轻白领",value:"youngpro"}),m(C,{label:"自由职业者",value:"freelancers"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(O,{gutter:20},{default:i(()=>[m(B,{span:12},{default:i(()=>[m(D,{label:"内容风格"},{default:i(()=>[m(q,{modelValue:h.style,"onUpdate:modelValue":a[3]||(a[3]=e=>h.style=e),placeholder:"选择风格"},{default:i(()=>[m(C,{label:"专业严谨",value:"professional"}),m(C,{label:"轻松幽默",value:"casual"}),m(C,{label:"温馨亲切",value:"warm"}),m(C,{label:"激励鼓舞",value:"motivational"}),m(C,{label:"简洁明了",value:"concise"}),m(C,{label:"详细全面",value:"detailed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),m(B,{span:12},{default:i(()=>[m(D,{label:"生成数量"},{default:i(()=>[m(E,{modelValue:h.count,"onUpdate:modelValue":a[4]||(a[4]=e=>h.count=e),min:1,max:10,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(D,{label:"关键词"},{default:i(()=>[m(Y,{modelValue:h.keywords,"onUpdate:modelValue":a[5]||(a[5]=e=>h.keywords=e),placeholder:"输入相关关键词，用逗号分隔",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),m(D,{label:"自定义要求"},{default:i(()=>[m(Y,{modelValue:h.customPrompt,"onUpdate:modelValue":a[6]||(a[6]=e=>h.customPrompt=e),placeholder:"描述您的具体要求，如：需要包含价格信息、突出优势等",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),c("div",Fl,[m(N,{type:"primary",onClick:k,loading:o.value,size:"large"},{default:i(()=>[m(t,null,{default:i(()=>[m(v(ye))]),_:1}),p(" "+R(o.value?"生成中...":"智能生成"),1)]),_:1},8,["loading"]),m(N,{onClick:F},{default:i(()=>a[14]||(a[14]=[p("清空结果",-1)])),_:1,__:[14]})]),d.value.length>0?(r(),_("div",Gl,[m(K,{"content-position":"left"},{default:i(()=>a[15]||(a[15]=[p("生成结果",-1)])),_:1,__:[15]}),c("div",Pl,[(r(!0),_(f,null,g(d.value,(e,t)=>(r(),_("div",{key:t,class:"result-item"},[c("div",Rl,[c("span",Ql,"方案 "+R(t+1),1),c("div",Hl,[m(N,{type:"primary",size:"small",onClick:a=>(e=>{l("content-generated",{type:s.value,content:e.content}),H.success("内容已应用")})(e)},{default:i(()=>a[16]||(a[16]=[p(" 使用此内容 ",-1)])),_:2,__:[16]},1032,["onClick"]),m(N,{size:"small",onClick:a=>(async e=>{if("undefined"!=typeof navigator&&navigator.clipboard)try{await navigator.clipboard.writeText(e.content),H.success("内容已复制到剪贴板")}catch(a){H.error("复制失败")}else H.warning("剪贴板功能在当前环境中不可用")})(e)},{default:i(()=>a[17]||(a[17]=[p(" 复制 ",-1)])),_:2,__:[17]},1032,["onClick"]),m(J,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,size:"small",onChange:a=>((e,a)=>{e.rating=a})(e,a)},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),c("div",El,[c("pre",null,R(e.content),1)])]))),128))]),c("div",Yl,[m(N,{onClick:G},{default:i(()=>a[18]||(a[18]=[p("重新生成全部",-1)])),_:1,__:[18]}),m(N,{onClick:Q},{default:i(()=>a[19]||(a[19]=[p("导出结果",-1)])),_:1,__:[19]})])])):w("",!0),b.value.length>0?(r(),_("div",Nl,[m(K,{"content-position":"left"},{default:i(()=>a[20]||(a[20]=[p("历史记录",-1)])),_:1,__:[20]}),m(W,null,{default:i(()=>[(r(!0),_(f,null,g(b.value.slice(0,5),(e,l)=>{return r(),u(Z,{key:l,timestamp:(t=e.timestamp,new Date(t).toLocaleString("zh-CN"))},{default:i(()=>{return[c("div",Kl,[c("div",Jl,R((l=e.type,{title:"群组标题",description:"群组描述",faq:"FAQ问答",reviews:"用户评论",intro:"群组介绍"}[l]||l)),1),c("div",Zl,R(e.content.substring(0,50))+"...",1),m(N,{type:"text",size:"small",onClick:a=>(e=>{Object.assign(h,e.config),s.value=e.type})(e)},{default:i(()=>a[21]||(a[21]=[p(" 重新使用 ",-1)])),_:2,__:[21]},1032,["onClick"])])];var l}),_:2},1032,["timestamp"]);var t}),128))]),_:1})])):w("",!0)]),_:1})])}}},[["__scopeId","data-v-18d311bd"]]),Xl={class:"preview-container"},et={class:"preview-header"},at={class:"preview-actions"},lt={class:"group-preview"},tt={class:"group-header"},st={class:"group-avatar"},nt={class:"group-info"},ot={class:"group-name"},ut={class:"group-meta"},rt={class:"group-price"},dt={class:"group-members"},it={key:0,class:"content-section"},ct={class:"section-title"},mt=["innerHTML"],pt={key:1,class:"content-section"},_t={class:"section-title"},vt={class:"faq-content"},ft={class:"faq-answer"},gt={key:1,class:"faq-list"},bt={class:"faq-question"},ht={class:"faq-index"},yt={class:"faq-answer"},wt={key:2,class:"faq-cards"},Vt={class:"faq-question"},kt={class:"faq-answer"},xt={key:2,class:"content-section"},Ct={class:"section-title"},qt={class:"reviews-content"},Ut={key:0,class:"reviews-cards"},Dt={class:"review-header"},zt={class:"review-user"},jt={class:"username"},$t={class:"review-time"},It={class:"review-content"},Tt={key:1,class:"reviews-list"},Mt={class:"review-left"},At={class:"review-right"},Bt={class:"review-meta"},St={class:"username"},Ot={class:"review-time"},Lt={class:"review-content"},Ft={key:2,class:"reviews-carousel"},Gt={class:"carousel-review"},Pt={class:"review-content"},Rt={class:"review-author"},Qt={class:"username"},Ht={key:3,class:"content-section"},Et={key:0,class:"extra-block"},Yt={class:"section-title"},Nt=["innerHTML"],Kt={key:1,class:"extra-block"},Jt={class:"section-title"},Zt=["innerHTML"],Wt={key:4,class:"content-section"},Xt={class:"materials-content"},es={key:0,class:"material-item"},as=["src"],ls={key:1,class:"material-item"},ts={class:"ad-images"},ss=["src"],ns={class:"join-section"},os={class:"dialog-footer"},us=e({__name:"ContentPreviewDialog",props:{modelValue:{type:Boolean,default:!1},contentData:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","apply-content"],setup(e,{emit:a}){const l=e,n=a,o=s({get:()=>l.modelValue,set:e=>n("update:modelValue",e)}),d=t("desktop"),b=s(()=>{if(!l.contentData.faq_content)return[];return l.contentData.faq_content.split("\n").filter(e=>e.includes("----")).map(e=>{const[a,l]=e.split("----");return{question:a?.trim()||"",answer:l?.trim()||""}})}),h=s(()=>{if(!l.contentData.user_reviews)return[];return l.contentData.user_reviews.split("\n").filter(e=>e.includes("----")).map(e=>{const a=e.split("----");return{username:a[0]?.trim()||"",content:a[1]?.trim()||"",rating:parseInt(a[2])||5,avatar:a[3]?.trim()||"",created_at:a[4]?.trim()||(new Date).toISOString().slice(0,19).replace("T"," ")}})}),y=s(()=>{const e=l.contentData.reviews_count||6;return h.value.slice(0,e)}),V=()=>{H.success("预览已刷新")},k=()=>{H.info("导出功能开发中")},x=()=>{n("apply-content",l.contentData),o.value=!1},C=e=>{if(!e)return"";const a=new Date(e),l=new Date-a,t=Math.floor(l/864e5);return 0===t?"今天":1===t?"昨天":t<7?`${t}天前`:a.toLocaleDateString("zh-CN")};return(a,l)=>{const t=ce,s=L,n=S,q=U,D=se,z=De,j=Ue,$=re,I=Ve,T=je,M=ze,A=Q;return r(),u(A,{modelValue:o.value,"onUpdate:modelValue":l[2]||(l[2]=e=>o.value=e),title:"内容预览",width:"800px","destroy-on-close":!0,class:"content-preview-dialog"},{footer:i(()=>[c("div",os,[m(q,{onClick:l[1]||(l[1]=e=>o.value=!1)},{default:i(()=>l[12]||(l[12]=[p("关闭",-1)])),_:1,__:[12]}),m(q,{type:"primary",onClick:x},{default:i(()=>l[13]||(l[13]=[p("应用内容",-1)])),_:1,__:[13]})])]),default:i(()=>[c("div",Xl,[c("div",et,[m(s,{modelValue:d.value,"onUpdate:modelValue":l[0]||(l[0]=e=>d.value=e),size:"small"},{default:i(()=>[m(t,{label:"desktop"},{default:i(()=>l[3]||(l[3]=[p("桌面端",-1)])),_:1,__:[3]}),m(t,{label:"mobile"},{default:i(()=>l[4]||(l[4]=[p("移动端",-1)])),_:1,__:[4]})]),_:1},8,["modelValue"]),c("div",at,[m(q,{size:"small",onClick:V},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ve))]),_:1}),l[5]||(l[5]=p(" 刷新 ",-1))]),_:1,__:[5]}),m(q,{size:"small",onClick:k},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ae))]),_:1}),l[6]||(l[6]=p(" 导出 ",-1))]),_:1,__:[6]})])]),c("div",{class:Ce(["preview-content",{"mobile-view":"mobile"===d.value}])},[c("div",lt,[c("div",tt,[c("div",st,[m(D,{src:e.groupData.avatar,size:60},{default:i(()=>[m(n,null,{default:i(()=>[m(v(qe))]),_:1})]),_:1},8,["src"])]),c("div",nt,[c("h3",ot,R(e.groupData.name||"群组名称"),1),c("div",ut,[c("span",rt,R(e.groupData.price>0?`¥${e.groupData.price}`:"免费"),1),c("span",dt,R(e.groupData.member_count||0)+"/"+R(e.groupData.max_members||500)+"人 ",1)])])]),e.contentData.show_intro&&e.contentData.group_intro_content?(r(),_("div",it,[c("h4",ct,R(e.contentData.group_intro_title||"群简介"),1),c("div",{class:"section-content",innerHTML:e.contentData.group_intro_content},null,8,mt)])):w("",!0),e.contentData.show_faq&&b.value.length>0?(r(),_("div",pt,[c("h4",_t,R(e.contentData.faq_title||"常见问题"),1),c("div",vt,["collapse"===e.contentData.faq_style?(r(),u(j,{key:0,accordion:""},{default:i(()=>[(r(!0),_(f,null,g(b.value,(e,a)=>(r(),u(z,{key:a,title:e.question,name:a},{default:i(()=>[c("div",ft,R(e.answer),1)]),_:2},1032,["title","name"]))),128))]),_:1})):"list"===e.contentData.faq_style?(r(),_("div",gt,[(r(!0),_(f,null,g(b.value,(e,a)=>(r(),_("div",{key:a,class:"faq-item"},[c("div",bt,[c("span",ht,"Q"+R(a+1),1),p(" "+R(e.question),1)]),c("div",yt,[l[7]||(l[7]=c("span",{class:"answer-label"},"A:",-1)),p(" "+R(e.answer),1)])]))),128))])):(r(),_("div",wt,[(r(!0),_(f,null,g(b.value,(e,a)=>(r(),u($,{key:a,class:"faq-card",shadow:"hover"},{header:i(()=>[c("div",Vt,R(e.question),1)]),default:i(()=>[c("div",kt,R(e.answer),1)]),_:2},1024))),128))]))])])):w("",!0),e.contentData.show_reviews&&h.value.length>0?(r(),_("div",xt,[c("h4",Ct,R(e.contentData.reviews_title||"用户评价"),1),c("div",qt,["card"===e.contentData.reviews_style?(r(),_("div",Ut,[(r(!0),_(f,null,g(y.value,(e,a)=>(r(),_("div",{key:a,class:"review-card"},[c("div",Dt,[m(D,{src:e.avatar,size:32},{default:i(()=>[m(n,null,{default:i(()=>[m(v(E))]),_:1})]),_:2},1032,["src"]),c("div",zt,[c("div",jt,R(e.username),1),m(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),c("div",$t,R(C(e.created_at)),1)]),c("div",It,R(e.content),1)]))),128))])):"list"===e.contentData.reviews_style?(r(),_("div",Tt,[(r(!0),_(f,null,g(y.value,(e,a)=>(r(),_("div",{key:a,class:"review-item"},[c("div",Mt,[m(D,{src:e.avatar,size:40},{default:i(()=>[m(n,null,{default:i(()=>[m(v(E))]),_:1})]),_:2},1032,["src"])]),c("div",At,[c("div",Bt,[c("span",St,R(e.username),1),m(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),c("span",Ot,R(C(e.created_at)),1)]),c("div",Lt,R(e.content),1)])]))),128))])):(r(),_("div",Ft,[m(M,{height:"120px",autoplay:!0,interval:3e3},{default:i(()=>[(r(!0),_(f,null,g(y.value,(e,a)=>(r(),u(T,{key:a},{default:i(()=>[c("div",Gt,[c("div",Pt,'"'+R(e.content)+'"',1),c("div",Rt,[c("span",Qt,"—— "+R(e.username),1),m(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])])]),_:2},1024))),128))]),_:1})]))])])):w("",!0),e.contentData.show_extra?(r(),_("div",Ht,[e.contentData.extra_title1&&e.contentData.extra_content1?(r(),_("div",Et,[c("h4",Yt,R(e.contentData.extra_title1),1),c("div",{class:"section-content",innerHTML:e.contentData.extra_content1},null,8,Nt)])):w("",!0),e.contentData.extra_title2&&e.contentData.extra_content2?(r(),_("div",Kt,[c("h4",Jt,R(e.contentData.extra_title2),1),c("div",{class:"section-content",innerHTML:e.contentData.extra_content2},null,8,Zt)])):w("",!0)])):w("",!0),e.contentData.customer_service_qr||e.contentData.ad_image?.length>0?(r(),_("div",Wt,[l[10]||(l[10]=c("h4",{class:"section-title"},"相关素材",-1)),c("div",Xt,[e.contentData.customer_service_qr?(r(),_("div",es,[l[8]||(l[8]=c("div",{class:"material-label"},"客服二维码",-1)),c("img",{src:e.contentData.customer_service_qr,alt:"客服二维码",class:"qr-code"},null,8,as)])):w("",!0),e.contentData.ad_image?.length>0?(r(),_("div",ls,[l[9]||(l[9]=c("div",{class:"material-label"},"广告图片",-1)),c("div",ts,[(r(!0),_(f,null,g(e.contentData.ad_image,(e,a)=>(r(),_("img",{key:a,src:e,alt:"广告图片",class:"ad-image"},null,8,ss))),128))])])):w("",!0)])])):w("",!0),c("div",ns,[m(q,{type:"primary",size:"large",class:"join-button"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(O))]),_:1}),l[11]||(l[11]=p(" 立即加入群组 ",-1))]),_:1,__:[11]})])])],2)])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-4cce9b66"]]),rs={class:"group-content-manager"},ds={class:"content-toolbar"},is={class:"toolbar-left"},cs={class:"content-title"},ms={class:"toolbar-right"},ps={class:"smart-tools-bar"},_s={class:"ai-generator-panel"},vs={class:"template-library-panel"},fs={class:"content-editor"},gs={class:"section-header"},bs={class:"section-title"},hs={class:"section-header"},ys={class:"section-title"},ws={class:"section-header"},Vs={class:"section-title"},ks={class:"faq-manager"},xs={class:"faq-header"},Cs={class:"faq-list"},qs={class:"faq-item-header"},Us={class:"faq-index"},Ds={class:"section-header"},zs={class:"section-title"},js={class:"reviews-manager"},$s={class:"reviews-header"},Is={class:"reviews-list"},Ts={class:"review-header"},Ms={class:"review-user"},As={class:"review-meta"},Bs={class:"section-header"},Ss={class:"section-title"},Os={class:"section-header"},Ls={class:"section-title"},Fs={key:0},Gs=e({__name:"GroupContentManager",props:{groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},emits:["content-updated"],setup(e,{emit:a}){const l=e,s=a,d=t(!1),b=t(!1),h=t(!1),k=t(null),q=n({cover_image:"",member_image:"",price:0,title:"",subtitle:"",read_count:"10万+",like_count:3659,want_see_count:665,button_title:"加入群，学习更多副业知识",avatar_library:"default",wx_accessible:1,remark:"",group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",user_reviews:"",customer_service_qr:"",ad_qr_code:"",show_customer_service:1,customer_service_avatar:"",customer_service_title:"VIP专属客服",customer_service_desc:"出现不能付款，不能入群等问题，请联系我！看到信息发回",extra_title3:"",extra_content3:"",payment_methods:[],status:1,show_faq:!0,faq_style:"collapse",show_reviews:!0,reviews_title:"用户评价",reviews_count:6,reviews_style:"card",show_extra:!0,extra_title1:"",extra_content1:"",extra_title2:"",extra_content2:""}),B=t([]),P=t([]),Q={group_intro_title:[{required:!0,message:"请输入群组介绍标题",trigger:"blur"},{max:60,message:"标题长度不能超过60个字符",trigger:"blur"}],faq_title:[{required:!0,message:"请输入FAQ标题",trigger:"blur"},{max:60,message:"标题长度不能超过60个字符",trigger:"blur"}]};o(()=>l.groupId,e=>{e&&Y()},{immediate:!0});const Y=async()=>{if(l.groupId){d.value=!0;try{const{data:e}=await Je(l.groupId);Object.assign(q,e.content||{}),e.faq_content&&(B.value=X(e.faq_content)),e.user_reviews&&(P.value=ae(e.user_reviews))}catch(e){console.error("获取群组内容失败:",e),H.error("获取群组内容失败")}finally{d.value=!1}}},N=async()=>{k.value&&await k.value.validate(async e=>{if(e){b.value=!0;try{const e={...q,faq_content:ee(B.value),user_reviews:te(P.value)};await Ze(l.groupId,e),H.success("内容保存成功"),s("content-updated")}catch(a){console.error("保存内容失败:",a),H.error("保存内容失败")}finally{b.value=!1}}})},K=()=>{h.value=!0},J=()=>{ue.confirm("确定要重置所有内容吗？此操作不可恢复。","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Y(),H.success("内容已重置")})},Z=()=>{B.value.push({question:"",answer:""})},W=()=>{P.value.push({username:"",content:"",rating:5,avatar:"",created_at:(new Date).toISOString().slice(0,19).replace("T"," ")})},X=e=>{if(!e)return[];return e.split("\n").filter(e=>e.includes("----")).map(e=>{const[a,l]=e.split("----");return{question:a?.trim()||"",answer:l?.trim()||""}})},ee=e=>e.filter(e=>e.question&&e.answer).map(e=>`${e.question}----${e.answer}`).join("\n"),ae=e=>{if(!e)return[];return e.split("\n").filter(e=>e.includes("----")).map(e=>{const a=e.split("----");return{username:a[0]?.trim()||"",content:a[1]?.trim()||"",rating:parseInt(a[2])||5,avatar:a[3]?.trim()||"",created_at:a[4]?.trim()||(new Date).toISOString().slice(0,19).replace("T"," ")}})},te=e=>e.filter(e=>e.username&&e.content).map(e=>`${e.username}----${e.content}----${e.rating}----${e.avatar}----${e.created_at}`).join("\n");return y(()=>{l.groupId&&Y()}),(a,l)=>{const t=S,s=U,n=x("MagicStick"),o=x("Collection"),y=x("TrendCharts"),H=Ae,Y=Wl,X=Be,ee=Ml,ae=$,te=j,ne=z,oe=A,ue=I,de=M,ie=T,ce=F,me=L,_e=re,ve=G,fe=pe,ge=se,be=Ve,he=Ge,ye=we,ke=D,xe=le;return r(),_("div",rs,[c("div",ds,[c("div",is,[c("h4",cs,[m(t,null,{default:i(()=>[m(v($e))]),_:1}),l[37]||(l[37]=p(" 群组内容管理 ",-1))]),l[38]||(l[38]=c("p",{class:"content-desc"},"管理群组的展示内容、FAQ、用户评论等信息",-1))]),c("div",ms,[m(s,{type:"primary",onClick:N,loading:b.value},{default:i(()=>[m(t,null,{default:i(()=>[m(v(Ie))]),_:1}),l[39]||(l[39]=p(" 保存内容 ",-1))]),_:1,__:[39]},8,["loading"]),m(s,{onClick:K},{default:i(()=>[m(t,null,{default:i(()=>[m(v(Te))]),_:1}),l[40]||(l[40]=p(" 预览效果 ",-1))]),_:1,__:[40]}),m(s,{onClick:J},{default:i(()=>[m(t,null,{default:i(()=>[m(v(Me))]),_:1}),l[41]||(l[41]=p(" 重置 ",-1))]),_:1,__:[41]})])]),c("div",ps,[m(H,null,{default:i(()=>[m(s,{onClick:l[0]||(l[0]=e=>a.showAIGenerator=!a.showAIGenerator)},{default:i(()=>[m(t,null,{default:i(()=>[m(n)]),_:1}),l[42]||(l[42]=p(" AI生成助手 ",-1))]),_:1,__:[42]}),m(s,{onClick:l[1]||(l[1]=e=>a.showTemplateLibrary=!a.showTemplateLibrary)},{default:i(()=>[m(t,null,{default:i(()=>[m(o)]),_:1}),l[43]||(l[43]=p(" 模板库 ",-1))]),_:1,__:[43]}),m(s,{onClick:a.analyzeContent},{default:i(()=>[m(t,null,{default:i(()=>[m(y)]),_:1}),l[44]||(l[44]=p(" 内容分析 ",-1))]),_:1,__:[44]},8,["onClick"]),m(s,{onClick:a.optimizeContent},{default:i(()=>[m(t,null,{default:i(()=>[m(y)]),_:1}),l[45]||(l[45]=p(" 智能优化 ",-1))]),_:1,__:[45]},8,["onClick"])]),_:1})]),m(X,null,{default:i(()=>[V(c("div",_s,[m(Y,{onContentGenerated:a.handleAIGenerated},null,8,["onContentGenerated"])],512),[[C,a.showAIGenerator]])]),_:1}),m(X,null,{default:i(()=>[V(c("div",vs,[m(ee,{onTemplateSelected:a.handleTemplateSelected},null,8,["onTemplateSelected"])],512),[[C,a.showTemplateLibrary]])]),_:1}),V((r(),_("div",fs,[m(ke,{model:q,rules:Q,ref_key:"contentFormRef",ref:k,"label-width":"120px"},{default:i(()=>[m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",gs,[c("span",bs,[m(t,null,{default:i(()=>[m(v(Se))]),_:1}),l[46]||(l[46]=p(" 基础设置 ",-1))])])]),default:i(()=>[m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"群组头像"},{default:i(()=>[m(oa,{modelValue:q.cover_image,"onUpdate:modelValue":l[2]||(l[2]=e=>q.cover_image=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"成员图"},{default:i(()=>[m(oa,{modelValue:q.member_image,"onUpdate:modelValue":l[3]||(l[3]=e=>q.member_image=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ne,{gutter:20},{default:i(()=>[m(te,{span:8},{default:i(()=>[m(ae,{label:"入群费用",prop:"price"},{default:i(()=>[m(oe,{modelValue:q.price,"onUpdate:modelValue":l[4]||(l[4]=e=>q.price=e),min:0,precision:2,style:{width:"100%"},placeholder:"0表示免费"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:8},{default:i(()=>[m(ae,{label:"群名称",prop:"title"},{default:i(()=>[m(ue,{modelValue:q.title,"onUpdate:modelValue":l[5]||(l[5]=e=>q.title=e),placeholder:"请输入群名称"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:8},{default:i(()=>[m(ae,{label:"副标题"},{default:i(()=>[m(ue,{modelValue:q.subtitle,"onUpdate:modelValue":l[6]||(l[6]=e=>q.subtitle=e),placeholder:"请输入副标题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ne,{gutter:20},{default:i(()=>[m(te,{span:6},{default:i(()=>[m(ae,{label:"阅读数"},{default:i(()=>[m(ue,{modelValue:q.read_count,"onUpdate:modelValue":l[7]||(l[7]=e=>q.read_count=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:6},{default:i(()=>[m(ae,{label:"点赞数"},{default:i(()=>[m(oe,{modelValue:q.like_count,"onUpdate:modelValue":l[8]||(l[8]=e=>q.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:6},{default:i(()=>[m(ae,{label:"想看数"},{default:i(()=>[m(oe,{modelValue:q.want_see_count,"onUpdate:modelValue":l[9]||(l[9]=e=>q.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:6},{default:i(()=>[m(ae,{label:"头像库选择"},{default:i(()=>[m(ie,{modelValue:q.avatar_library,"onUpdate:modelValue":l[10]||(l[10]=e=>q.avatar_library=e),placeholder:"选择头像库"},{default:i(()=>[m(de,{label:"默认头像",value:"default"}),m(de,{label:"商务头像",value:"business"}),m(de,{label:"交友头像",value:"dating"}),m(de,{label:"征婚头像",value:"marriage"}),m(de,{label:"健身头像",value:"fitness"}),m(de,{label:"家庭头像",value:"family"}),m(de,{label:"扩列头像",value:"qq"}),m(de,{label:"综合头像",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ae,{label:"按键名称"},{default:i(()=>[m(ue,{modelValue:q.button_title,"onUpdate:modelValue":l[11]||(l[11]=e=>q.button_title=e),placeholder:"如：加入群，学习更多副业知识"},null,8,["modelValue"])]),_:1}),m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"引导浏览器打开"},{default:i(()=>[m(me,{modelValue:q.wx_accessible,"onUpdate:modelValue":l[12]||(l[12]=e=>q.wx_accessible=e)},{default:i(()=>[m(ce,{label:1},{default:i(()=>l[47]||(l[47]=[p("微信能打开",-1)])),_:1,__:[47]}),m(ce,{label:2},{default:i(()=>l[48]||(l[48]=[p("微信内不能打开",-1)])),_:1,__:[48]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"备注信息"},{default:i(()=>[m(ue,{modelValue:q.remark,"onUpdate:modelValue":l[13]||(l[13]=e=>q.remark=e),placeholder:"备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",hs,[c("span",ys,[m(t,null,{default:i(()=>[m(v(Oe))]),_:1}),l[49]||(l[49]=p(" 内容设置 ",-1))])])]),default:i(()=>[m(ae,{label:"区块一标题"},{default:i(()=>[m(ue,{modelValue:q.group_intro_title,"onUpdate:modelValue":l[14]||(l[14]=e=>q.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1}),m(ae,{label:"区块一内容"},{default:i(()=>[m(ue,{type:"textarea",modelValue:q.group_intro_content,"onUpdate:modelValue":l[15]||(l[15]=e=>q.group_intro_content=e),rows:4,placeholder:"输入群组介绍内容"},null,8,["modelValue"])]),_:1})]),_:1}),m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",ws,[c("span",Vs,[m(t,null,{default:i(()=>[m(v(Fe))]),_:1}),l[50]||(l[50]=p(" 常见问题 (FAQ) ",-1))]),m(ve,{modelValue:q.show_faq,"onUpdate:modelValue":l[16]||(l[16]=e=>q.show_faq=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:i(()=>[m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"FAQ标题",prop:"faq_title"},{default:i(()=>[m(ue,{modelValue:q.faq_title,"onUpdate:modelValue":l[17]||(l[17]=e=>q.faq_title=e),placeholder:"如：常见问题、FAQ",maxlength:"60","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"展示样式"},{default:i(()=>[m(ie,{modelValue:q.faq_style,"onUpdate:modelValue":l[18]||(l[18]=e=>q.faq_style=e),placeholder:"选择展示样式"},{default:i(()=>[m(de,{label:"折叠面板",value:"collapse"}),m(de,{label:"列表展示",value:"list"}),m(de,{label:"卡片展示",value:"card"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ae,{label:"FAQ内容"},{default:i(()=>[c("div",ks,[c("div",xs,[l[52]||(l[52]=c("span",null,"问答列表",-1)),m(s,{type:"primary",size:"small",onClick:Z},{default:i(()=>[m(t,null,{default:i(()=>[m(v(O))]),_:1}),l[51]||(l[51]=p(" 添加问答 ",-1))]),_:1,__:[51]})]),c("div",Cs,[(r(!0),_(f,null,g(B.value,(e,a)=>(r(),_("div",{key:a,class:"faq-item"},[c("div",qs,[c("span",Us,R(a+1),1),m(ue,{modelValue:e.question,"onUpdate:modelValue":a=>e.question=a,placeholder:"输入问题",class:"faq-question"},null,8,["modelValue","onUpdate:modelValue"]),m(s,{type:"danger",size:"small",circle:"",onClick:e=>(e=>{B.value.splice(e,1)})(a)},{default:i(()=>[m(t,null,{default:i(()=>[m(v(Le))]),_:1})]),_:2},1032,["onClick"])]),m(ue,{type:"textarea",modelValue:e.answer,"onUpdate:modelValue":a=>e.answer=a,placeholder:"输入答案",rows:3,class:"faq-answer"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),0===B.value.length?(r(),u(fe,{key:0,description:"暂无FAQ内容","image-size":80})):w("",!0)])])]),_:1})]),_:1}),m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",Ds,[c("span",zs,[m(t,null,{default:i(()=>[m(v(Pe))]),_:1}),l[53]||(l[53]=p(" 用户评论 ",-1))]),m(ve,{modelValue:q.show_reviews,"onUpdate:modelValue":l[19]||(l[19]=e=>q.show_reviews=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:i(()=>[m(ne,{gutter:20},{default:i(()=>[m(te,{span:8},{default:i(()=>[m(ae,{label:"评论标题"},{default:i(()=>[m(ue,{modelValue:q.reviews_title,"onUpdate:modelValue":l[20]||(l[20]=e=>q.reviews_title=e),placeholder:"如：用户评价、群友反馈"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:8},{default:i(()=>[m(ae,{label:"显示数量"},{default:i(()=>[m(oe,{modelValue:q.reviews_count,"onUpdate:modelValue":l[21]||(l[21]=e=>q.reviews_count=e),min:1,max:20,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:8},{default:i(()=>[m(ae,{label:"评论样式"},{default:i(()=>[m(ie,{modelValue:q.reviews_style,"onUpdate:modelValue":l[22]||(l[22]=e=>q.reviews_style=e),placeholder:"选择样式"},{default:i(()=>[m(de,{label:"卡片样式",value:"card"}),m(de,{label:"列表样式",value:"list"}),m(de,{label:"轮播样式",value:"carousel"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ae,{label:"评论内容"},{default:i(()=>[c("div",js,[c("div",$s,[l[55]||(l[55]=c("span",null,"评论列表",-1)),m(s,{type:"primary",size:"small",onClick:W},{default:i(()=>[m(t,null,{default:i(()=>[m(v(O))]),_:1}),l[54]||(l[54]=p(" 添加评论 ",-1))]),_:1,__:[54]})]),c("div",Is,[(r(!0),_(f,null,g(P.value,(e,a)=>(r(),_("div",{key:a,class:"review-item"},[c("div",Ts,[m(ge,{src:e.avatar,size:40},{default:i(()=>[m(t,null,{default:i(()=>[m(v(E))]),_:1})]),_:2},1032,["src"]),c("div",Ms,[m(ue,{modelValue:e.username,"onUpdate:modelValue":a=>e.username=a,placeholder:"用户名",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),m(be,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),m(s,{type:"danger",size:"small",circle:"",onClick:e=>(e=>{P.value.splice(e,1)})(a)},{default:i(()=>[m(t,null,{default:i(()=>[m(v(Le))]),_:1})]),_:2},1032,["onClick"])]),m(ue,{type:"textarea",modelValue:e.content,"onUpdate:modelValue":a=>e.content=a,placeholder:"输入评论内容",rows:2},null,8,["modelValue","onUpdate:modelValue"]),c("div",As,[m(ue,{modelValue:e.avatar,"onUpdate:modelValue":a=>e.avatar=a,placeholder:"头像URL（可选）",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),m(he,{modelValue:e.created_at,"onUpdate:modelValue":a=>e.created_at=a,type:"datetime",placeholder:"评论时间",size:"small",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","onUpdate:modelValue"])])]))),128)),0===P.value.length?(r(),u(fe,{key:0,description:"暂无用户评论","image-size":80})):w("",!0)])])]),_:1})]),_:1}),m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",Bs,[c("span",Ss,[m(t,null,{default:i(()=>[m(v(Re))]),_:1}),l[56]||(l[56]=p(" 扩展内容区块 ",-1))]),m(ve,{modelValue:q.show_extra,"onUpdate:modelValue":l[23]||(l[23]=e=>q.show_extra=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:i(()=>[m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"区块1标题"},{default:i(()=>[m(ue,{modelValue:q.extra_title1,"onUpdate:modelValue":l[24]||(l[24]=e=>q.extra_title1=e),placeholder:"如：服务优势、特色功能"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"区块2标题"},{default:i(()=>[m(ue,{modelValue:q.extra_title2,"onUpdate:modelValue":l[25]||(l[25]=e=>q.extra_title2=e),placeholder:"如：联系方式、注意事项"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"区块1内容"},{default:i(()=>[m(ta,{modelValue:q.extra_content1,"onUpdate:modelValue":l[26]||(l[26]=e=>q.extra_content1=e),height:150,placeholder:"输入扩展区块1的内容"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"区块2内容"},{default:i(()=>[m(ta,{modelValue:q.extra_content2,"onUpdate:modelValue":l[27]||(l[27]=e=>q.extra_content2=e),height:150,placeholder:"输入扩展区块2的内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),m(_e,{class:"content-section",shadow:"never"},{header:i(()=>[c("div",Os,[c("span",Ls,[m(t,null,{default:i(()=>[m(v(Qe))]),_:1}),l[57]||(l[57]=p(" 展示广告 ",-1))])])]),default:i(()=>[m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"客服二维码"},{default:i(()=>[m(oa,{modelValue:q.customer_service_qr,"onUpdate:modelValue":l[28]||(l[28]=e=>q.customer_service_qr=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"广告二维码"},{default:i(()=>[m(oa,{modelValue:q.ad_qr_code,"onUpdate:modelValue":l[29]||(l[29]=e=>q.ad_qr_code=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ye,{"content-position":"left"},{default:i(()=>l[58]||(l[58]=[p("客服信息设置",-1)])),_:1,__:[58]}),m(ae,{label:"客服信息显示"},{default:i(()=>[m(me,{modelValue:q.show_customer_service,"onUpdate:modelValue":l[30]||(l[30]=e=>q.show_customer_service=e)},{default:i(()=>[m(ce,{label:1},{default:i(()=>l[59]||(l[59]=[p("不显示",-1)])),_:1,__:[59]}),m(ce,{label:2},{default:i(()=>l[60]||(l[60]=[p("显示",-1)])),_:1,__:[60]})]),_:1},8,["modelValue"])]),_:1}),2===q.show_customer_service?(r(),_("div",Fs,[m(ne,{gutter:20},{default:i(()=>[m(te,{span:12},{default:i(()=>[m(ae,{label:"客服头像"},{default:i(()=>[m(oa,{modelValue:q.customer_service_avatar,"onUpdate:modelValue":l[31]||(l[31]=e=>q.customer_service_avatar=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),m(te,{span:12},{default:i(()=>[m(ae,{label:"客服标题"},{default:i(()=>[m(ue,{modelValue:q.customer_service_title,"onUpdate:modelValue":l[32]||(l[32]=e=>q.customer_service_title=e),placeholder:"如：VIP专属客服"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),m(ae,{label:"客服描述"},{default:i(()=>[m(ue,{modelValue:q.customer_service_desc,"onUpdate:modelValue":l[33]||(l[33]=e=>q.customer_service_desc=e),placeholder:"如：出现不能付款，不能入群等问题，请联系我！看到信息发回"},null,8,["modelValue"])]),_:1})])):w("",!0),m(ye,{"content-position":"left"},{default:i(()=>l[61]||(l[61]=[p("区块三设置",-1)])),_:1,__:[61]}),m(ae,{label:"区块三标题"},{default:i(()=>[m(ue,{modelValue:q.extra_title3,"onUpdate:modelValue":l[34]||(l[34]=e=>q.extra_title3=e),placeholder:"区块三标题"},null,8,["modelValue"])]),_:1}),m(ae,{label:"区块三图片"},{default:i(()=>[m(ta,{modelValue:q.extra_content3,"onUpdate:modelValue":l[35]||(l[35]=e=>q.extra_content3=e),height:200,placeholder:"可以插入图片和富文本内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])])),[[xe,d.value]]),m(us,{modelValue:h.value,"onUpdate:modelValue":l[36]||(l[36]=e=>h.value=e),"content-data":q,"group-data":e.groupData},null,8,["modelValue","content-data","group-data"])])}}},[["__scopeId","data-v-94a82f95"]]),Ps={class:"modern-group-list"},Rs={class:"page-header"},Qs={class:"header-content"},Hs={class:"header-left"},Es={class:"header-icon"},Ys={class:"header-actions"},Ns={class:"stats-section"},Ks={class:"stats-container"},Js={class:"stat-content"},Zs={class:"stat-value"},Ws={class:"stat-label"},Xs={class:"filter-section"},en={class:"filter-content"},an={class:"filter-left"},ln={class:"search-group"},tn={class:"filter-group"},sn={class:"filter-right"},nn={class:"table-section"},on={class:"table-header"},un={class:"header-left"},rn={class:"total-count"},dn={class:"header-right"},cn={key:0,class:"table-view"},mn={class:"group-info"},pn={class:"group-avatar"},_n={class:"group-details"},vn={class:"group-name"},fn={class:"group-desc"},gn={class:"group-meta"},bn={class:"group-id"},hn={class:"group-category"},yn={class:"owner-info"},wn={class:"owner-name"},Vn={class:"price-info"},kn={class:"price-value"},xn={class:"member-stats"},Cn={class:"member-count"},qn={class:"current"},Un={class:"max"},Dn={class:"health-score"},zn={class:"score-label"},jn={class:"time-info"},$n={class:"date"},In={class:"action-buttons"},Tn={key:1,class:"card-view"},Mn=["onClick"],An={class:"card-header"},Bn={class:"group-avatar"},Sn={class:"card-actions"},On={class:"card-content"},Ln={class:"group-title"},Fn={class:"group-description"},Gn={class:"group-stats"},Pn={class:"stat-item"},Rn={class:"value"},Qn={class:"stat-item"},Hn={class:"value price"},En={class:"group-tags"},Yn={class:"pagination-wrapper"},Nn=e({__name:"GroupList",setup(e){const a=t([]),l=t(0),s=t(!0),o=t(!1),d=t(!1),h=t(!1),k=t(!1),x=t(!1),C=t({}),D=t(null),$=t([]),A=t("table"),B=t([{key:"total",label:"总群组数",value:156,icon:"Comment",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"CaretTop",change:"+15.8%"},{key:"active",label:"活跃群组",value:128,icon:"ChatDotRound",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"CaretTop",change:"+8.2%"},{key:"members",label:"总成员数",value:12456,icon:"User",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"CaretTop",change:"+12.1%"},{key:"revenue",label:"总收入",value:"¥89,234",icon:"Money",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"CaretTop",change:"+18.5%"}]),L=n({page:1,limit:20,keyword:"",status:"",category:""}),F=async()=>{s.value=!0;try{const e=await We(L),{data:t}=e;t&&t.list?(a.value=t.list,l.value=t.total||0):(a.value=[],l.value=0,H.warning("暂无数据"))}catch(e){console.error("获取群组列表失败:",e),H.error("获取群组列表失败"),a.value=[],l.value=0}finally{s.value=!1}},G=async()=>{try{const e=await Xe(),{data:a}=e;a&&(stats.value=a)}catch(e){console.error("获取统计数据失败:",e)}},Q=()=>{L.page=1,F()},E=()=>{C.value={},o.value=!0},Y=e=>{C.value={...e},o.value=!0},N=e=>{C.value={...e},d.value=!0},W=e=>{D.value=e.id,h.value=!0},X=()=>{H.info("正在跳转到自动化规则配置页面...")},de=async(e,a)=>{try{const l="paused"===a?"暂停":"恢复";await ue.confirm(`确定要${l}该群组吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await la(e,a),H.success(`${l}成功`),F()}catch(l){"cancel"!==l&&H.error("操作失败")}},ie=e=>{const[l,t]=e.split("-"),s=parseInt(t),n=a.value.find(e=>e.id===s);switch(l){case"edit":Y(n);break;case"members":N(n);break;case"analytics":W(n);break;case"qrcode":C.value={...n},k.value=!0;break;case"content":C.value={...n},x.value=!0;break;case"clone":(async()=>{try{await ue.confirm("确定要克隆该群组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),H.success("群组克隆成功"),F()}catch(e){"cancel"!==e&&H.error("克隆失败")}})();break;case"pause":de(s,"paused");break;case"resume":de(s,"active");break;case"delete":(async e=>{try{await ue.confirm("确定要删除该群组吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await aa(e),H.success("删除成功"),F()}catch(a){"cancel"!==a&&H.error("删除失败")}})(s)}},ce=async()=>{try{await ea(L),H.success("导出成功")}catch(e){H.error("导出失败")}},me=()=>{0!==$.value.length?ue.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"暂停群组",cancelButtonText:"恢复群组"}).then(()=>{pe()}).catch(e=>{"cancel"===e&&pe()}):H.warning("请先选择要操作的群组")},pe=async e=>{try{$.value.map(e=>e.id);H.success("批量操作成功"),F()}catch(a){H.error("批量操作失败")}},_e=e=>{$.value=e},ve=e=>{L.limit=e,F()},fe=e=>{L.page=e,F()},ge=()=>{F(),G()},he=()=>{F()},ye=e=>({startup:"创业交流",finance:"投资理财",tech:"科技互联网",education:"教育培训",other:"其他"}[e]||"未知"),we=e=>({active:"success",paused:"warning",full:"info",pending:"danger"}[e]||""),Ve=e=>({active:"活跃",paused:"暂停",full:"已满",pending:"待审核"}[e]||"未知"),ke=e=>!e||e<40?"较差":e<60?"一般":e<80?"良好":"优秀";return y(()=>{F(),G()}),(e,t)=>{const n=S,y=U,F=I,G=M,H=T,ue=re,de=Ae,pe=te,xe=se,Ue=P,De=He,ze=Z,je=J,Ie=ee,Te=ne,Me=j,Be=z,Oe=oe,Fe=le;return r(),_("div",Ps,[c("div",Rs,[c("div",Qs,[c("div",Hs,[c("div",Es,[m(n,{size:"24"},{default:i(()=>[m(v(qe))]),_:1})]),t[12]||(t[12]=c("div",{class:"header-text"},[c("h1",null,"社群管理"),c("p",null,"管理和监控您的社群运营状况，提升群组活跃度和收益")],-1))]),c("div",Ys,[m(y,{onClick:ce,class:"action-btn secondary"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ae))]),_:1}),t[13]||(t[13]=p(" 导出数据 ",-1))]),_:1,__:[13]}),m(y,{type:"primary",onClick:E,class:"action-btn primary"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(O))]),_:1}),t[14]||(t[14]=p(" 创建群组 ",-1))]),_:1,__:[14]})])])]),c("div",Ns,[c("div",Ks,[(r(!0),_(f,null,g(B.value,e=>(r(),_("div",{class:"stat-card",key:e.key},[c("div",{class:"stat-icon",style:Ye({background:e.color})},[m(n,{size:"20"},{default:i(()=>[(r(),u(q(e.icon)))]),_:2},1024)],4),c("div",Js,[c("div",Zs,R(e.value),1),c("div",Ws,R(e.label),1)]),c("div",{class:Ce(["stat-trend",e.trend])},[m(n,{size:"14"},{default:i(()=>[(r(),u(q(e.trendIcon)))]),_:2},1024),c("span",null,R(e.change),1)],2)]))),128))])]),c("div",Xs,[m(ue,{class:"filter-card",shadow:"never"},{default:i(()=>[c("div",en,[c("div",an,[c("div",ln,[m(F,{modelValue:L.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>L.keyword=e),placeholder:"搜索群组名称、群主...",class:"search-input",onKeyup:b(Q,["enter"]),clearable:""},{prefix:i(()=>[m(n,null,{default:i(()=>[m(v(be))]),_:1})]),_:1},8,["modelValue"])]),c("div",tn,[m(H,{modelValue:L.status,"onUpdate:modelValue":t[1]||(t[1]=e=>L.status=e),placeholder:"群组状态",clearable:"",class:"filter-select"},{default:i(()=>[m(G,{label:"全部状态",value:""}),m(G,{label:"活跃",value:"active"}),m(G,{label:"暂停",value:"paused"}),m(G,{label:"已满",value:"full"}),m(G,{label:"待审核",value:"pending"})]),_:1},8,["modelValue"]),m(H,{modelValue:L.category,"onUpdate:modelValue":t[2]||(t[2]=e=>L.category=e),placeholder:"群组分类",clearable:"",class:"filter-select"},{default:i(()=>[m(G,{label:"全部分类",value:""}),m(G,{label:"创业交流",value:"startup"}),m(G,{label:"投资理财",value:"finance"}),m(G,{label:"科技互联网",value:"tech"}),m(G,{label:"教育培训",value:"education"}),m(G,{label:"其他",value:"other"})]),_:1},8,["modelValue"])])]),c("div",sn,[m(y,{type:"primary",onClick:Q,class:"action-btn"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(be))]),_:1}),t[15]||(t[15]=p(" 搜索 ",-1))]),_:1,__:[15]}),m(y,{type:"success",onClick:X,class:"action-btn"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(Se))]),_:1}),t[16]||(t[16]=p(" 自动化规则 ",-1))]),_:1,__:[16]}),m(y,{type:"warning",onClick:ce,class:"action-btn"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(ae))]),_:1}),t[17]||(t[17]=p(" 导出数据 ",-1))]),_:1,__:[17]}),$.value.length>0?(r(),u(y,{key:0,type:"info",onClick:me,class:"action-btn"},{default:i(()=>[p(" 批量操作 ("+R($.value.length)+") ",1)]),_:1})):w("",!0)])])]),_:1})]),c("div",nn,[m(ue,{class:"table-card",shadow:"never"},{header:i(()=>[c("div",on,[c("div",un,[t[18]||(t[18]=c("h3",null,"群组列表",-1)),c("span",rn,"共 "+R(l.value)+" 个群组",1)]),c("div",dn,[m(de,null,{default:i(()=>[m(y,{type:"table"===A.value?"primary":"",onClick:t[3]||(t[3]=e=>A.value="table")},{default:i(()=>t[19]||(t[19]=[p(" 表格视图 ",-1)])),_:1,__:[19]},8,["type"]),m(y,{type:"card"===A.value?"primary":"",onClick:t[4]||(t[4]=e=>A.value="card")},{default:i(()=>t[20]||(t[20]=[p(" 卡片视图 ",-1)])),_:1,__:[20]},8,["type"])]),_:1})])])]),default:i(()=>["table"===A.value?(r(),_("div",cn,[V((r(),u(Te,{data:a.value,"element-loading-text":"加载中...",class:"modern-table",onSelectionChange:_e},{default:i(()=>[m(pe,{type:"selection",width:"55"}),m(pe,{label:"群组信息",width:"280",fixed:"left"},{default:i(({row:e})=>[c("div",mn,[c("div",pn,[m(xe,{src:e.avatar,alt:e.name,size:"large"},{default:i(()=>[p(R(e.name.charAt(0)),1)]),_:2},1032,["src","alt"]),c("div",{class:Ce(["status-dot",e.status])},null,2)]),c("div",_n,[c("div",vn,R(e.name),1),c("div",fn,R(e.description),1),c("div",gn,[c("span",bn,"ID: "+R(e.id),1),c("span",hn,R(ye(e.category)),1)])])])]),_:1}),m(pe,{label:"群主信息",width:"150"},{default:i(({row:e})=>[c("div",yn,[c("div",wn,R(e.owner?.name||e.owner_name),1),m(Ue,{size:"small",type:"info",class:"owner-role"},{default:i(()=>[p(R(e.owner?.role||e.owner_role||"群主"),1)]),_:2},1024)])]),_:1}),m(pe,{label:"价格",width:"100"},{default:i(({row:e})=>[c("div",Vn,[c("span",kn,"¥"+R((e.price||0).toFixed(2)),1)])]),_:1}),m(pe,{label:"成员统计",width:"180"},{default:i(({row:e})=>{return[c("div",xn,[c("div",Cn,[c("span",qn,R(e.memberCount||e.current_members||0),1),t[21]||(t[21]=c("span",{class:"separator"},"/",-1)),c("span",Un,R(e.maxMembers||e.max_members||500),1)]),m(De,{percentage:(e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500)*100,"stroke-width":6,"show-text":!1,color:(a=(e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500),a<.5?"#67c23a":a<.8?"#e6a23c":"#f56c6c"),class:"member-progress"},null,8,["percentage","color"])])];var a}),_:1}),m(pe,{label:"健康度",width:"120"},{default:i(({row:e})=>{return[c("div",Dn,[c("div",{class:Ce(["score-circle",(a=e.health_score,!a||a<40?"poor":a<60?"fair":a<80?"good":"excellent")])},R(e.health_score||"N/A"),3),c("div",zn,R(ke(e.health_score)),1)])];var a}),_:1}),m(pe,{label:"状态",width:"100"},{default:i(({row:e})=>[m(Ue,{type:we(e.status),class:"status-tag"},{default:i(()=>[p(R(Ve(e.status)),1)]),_:2},1032,["type"])]),_:1}),m(pe,{label:"创建时间",width:"160"},{default:i(({row:e})=>[c("div",jn,[c("div",$n,R(v(ua)(e.created_at)),1)])]),_:1}),m(pe,{label:"操作",width:"280",fixed:"right"},{default:i(({row:e})=>[c("div",In,[m(y,{type:"primary",size:"small",onClick:a=>Y(e),class:"action-btn-small"},{default:i(()=>t[22]||(t[22]=[p(" 编辑 ",-1)])),_:2,__:[22]},1032,["onClick"]),m(y,{type:"success",size:"small",onClick:a=>N(e),class:"action-btn-small"},{default:i(()=>t[23]||(t[23]=[p(" 成员 ",-1)])),_:2,__:[23]},1032,["onClick"]),m(y,{type:"info",size:"small",onClick:a=>W(e),class:"action-btn-small"},{default:i(()=>t[24]||(t[24]=[p(" 分析 ",-1)])),_:2,__:[24]},1032,["onClick"]),m(Ie,{onCommand:ie,class:"action-dropdown"},{dropdown:i(()=>[m(je,null,{default:i(()=>[m(ze,{command:`qrcode-${e.id}`},{default:i(()=>t[26]||(t[26]=[p(" 二维码 ",-1)])),_:2,__:[26]},1032,["command"]),m(ze,{command:`content-${e.id}`},{default:i(()=>[m(n,null,{default:i(()=>[m(v($e))]),_:1}),t[27]||(t[27]=p(" 内容管理 ",-1))]),_:2,__:[27]},1032,["command"]),m(ze,{command:`clone-${e.id}`},{default:i(()=>t[28]||(t[28]=[p(" 克隆群组 ",-1)])),_:2,__:[28]},1032,["command"]),"active"===e.status?(r(),u(ze,{key:0,command:`pause-${e.id}`},{default:i(()=>t[29]||(t[29]=[p(" 暂停群组 ",-1)])),_:2,__:[29]},1032,["command"])):w("",!0),"paused"===e.status?(r(),u(ze,{key:1,command:`resume-${e.id}`},{default:i(()=>t[30]||(t[30]=[p(" 恢复群组 ",-1)])),_:2,__:[30]},1032,["command"])):w("",!0),m(ze,{command:`delete-${e.id}`,divided:""},{default:i(()=>[m(n,null,{default:i(()=>[m(v(Le))]),_:1}),t[31]||(t[31]=p(" 删除群组 ",-1))]),_:2,__:[31]},1032,["command"])]),_:2},1024)]),default:i(()=>[m(y,{type:"warning",size:"small",class:"action-btn-small"},{default:i(()=>[t[25]||(t[25]=p(" 更多",-1)),m(n,{class:"el-icon--right"},{default:i(()=>[m(v(K))]),_:1})]),_:1,__:[25]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Fe,s.value]])])):(r(),_("div",Tn,[m(Be,{gutter:24},{default:i(()=>[(r(!0),_(f,null,g(a.value,e=>(r(),u(Me,{span:8,key:e.id},{default:i(()=>{return[c("div",{class:"group-card",onClick:a=>Y(e)},[c("div",An,[c("div",Bn,[m(xe,{src:e.avatar,alt:e.name,size:"large"},{default:i(()=>[p(R(e.name.charAt(0)),1)]),_:2},1032,["src","alt"]),c("div",{class:Ce(["status-indicator",e.status])},null,2)]),c("div",Sn,[m(Ie,{onCommand:ie,trigger:"click"},{dropdown:i(()=>[m(je,null,{default:i(()=>[m(ze,{command:`edit-${e.id}`},{default:i(()=>t[32]||(t[32]=[p("编辑",-1)])),_:2,__:[32]},1032,["command"]),m(ze,{command:`members-${e.id}`},{default:i(()=>t[33]||(t[33]=[p("成员管理",-1)])),_:2,__:[33]},1032,["command"]),m(ze,{command:`analytics-${e.id}`},{default:i(()=>t[34]||(t[34]=[p("数据分析",-1)])),_:2,__:[34]},1032,["command"]),m(ze,{command:`delete-${e.id}`,divided:""},{default:i(()=>t[35]||(t[35]=[p("删除",-1)])),_:2,__:[35]},1032,["command"])]),_:2},1024)]),default:i(()=>[m(y,{text:"",class:"more-btn"},{default:i(()=>[m(n,null,{default:i(()=>[m(v(Ee))]),_:1})]),_:1})]),_:2},1024)])]),c("div",On,[c("h4",Ln,R(e.name),1),c("p",Fn,R(e.description),1),c("div",Gn,[c("div",Pn,[t[36]||(t[36]=c("span",{class:"label"},"成员",-1)),c("span",Rn,R(e.memberCount||e.current_members||0)+"/"+R(e.maxMembers||e.max_members||500),1)]),c("div",Qn,[t[37]||(t[37]=c("span",{class:"label"},"价格",-1)),c("span",Hn,"¥"+R((e.price||0).toFixed(2)),1)])]),c("div",En,[m(Ue,{type:(a=e.category,{startup:"success",finance:"warning",tech:"primary",education:"info",other:""}[a]||""),size:"small"},{default:i(()=>[p(R(ye(e.category)),1)]),_:2},1032,["type"]),m(Ue,{type:we(e.status),size:"small"},{default:i(()=>[p(R(Ve(e.status)),1)]),_:2},1032,["type"])])])],8,Mn)];var a}),_:2},1024))),128))]),_:1})])),c("div",Yn,[m(Oe,{"current-page":L.page,"onUpdate:currentPage":t[5]||(t[5]=e=>L.page=e),"page-size":L.limit,"onUpdate:pageSize":t[6]||(t[6]=e=>L.limit=e),"page-sizes":[12,24,36,48],total:l.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:fe,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),m(_a,{modelValue:o.value,"onUpdate:modelValue":t[7]||(t[7]=e=>o.value=e),"group-data":C.value,onSuccess:ge},null,8,["modelValue","group-data"]),m(Ra,{modelValue:d.value,"onUpdate:modelValue":t[8]||(t[8]=e=>d.value=e),"group-data":C.value,onSuccess:he},null,8,["modelValue","group-data"]),m(sl,{visible:h.value,"onUpdate:visible":t[9]||(t[9]=e=>h.value=e),"group-id":D.value,"group-data":C.value},null,8,["visible","group-id","group-data"]),m(yl,{modelValue:k.value,"onUpdate:modelValue":t[10]||(t[10]=e=>k.value=e),"group-data":C.value},null,8,["modelValue","group-data"]),m(Gs,{modelValue:x.value,"onUpdate:modelValue":t[11]||(t[11]=e=>x.value=e),"group-data":C.value},null,8,["modelValue","group-data"])])}}},[["__scopeId","data-v-88149c2c"]]);export{Nn as default};
