<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentUsageLog;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * 支付使用日志控制器
 * 处理支付使用记录的查询和统计
 */
class PaymentUsageLogController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 获取支付使用日志列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'string|in:user,distributor,substation',
                'user_id' => 'integer|min:1',
                'channel_code' => 'string',
                'action_type' => 'string|in:create,callback,refund,query',
                'status' => 'string|in:pending,success,failed,cancelled',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            $query = PaymentUsageLog::with(['paymentChannel:channel_code,channel_name', 'order:id,order_no']);

            // 用户筛选
            if ($request->filled('user_type') && $request->filled('user_id')) {
                $query->forUser($request->input('user_type'), $request->input('user_id'));
            }

            // 支付渠道筛选
            if ($request->filled('channel_code')) {
                $query->byChannel($request->input('channel_code'));
            }

            // 操作类型筛选
            if ($request->filled('action_type')) {
                $query->byActionType($request->input('action_type'));
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->byStatus($request->input('status'));
            }

            // 时间范围筛选
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->dateRange($request->input('start_date'), $request->input('end_date'));
            } elseif ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->input('start_date'));
            } elseif ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->input('end_date'));
            }

            // 权限检查
            if (!$this->canViewLogs($request)) {
                return $this->error('无权限查看支付日志', 403);
            }

            $perPage = $request->input('per_page', 20);
            $logs = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $result = $logs->getCollection()->map(function ($log) {
                return [
                    'id' => $log->id,
                    'user_type' => $log->user_type,
                    'user_id' => $log->user_id,
                    'channel_code' => $log->channel_code,
                    'channel_name' => $log->paymentChannel->channel_name ?? '',
                    'order_id' => $log->order_id,
                    'order_no' => $log->order_no,
                    'amount' => $log->amount,
                    'fee' => $log->fee,
                    'action_type' => $log->action_type,
                    'action_type_name' => $log->action_type_name,
                    'status' => $log->status,
                    'status_name' => $log->status_name,
                    'status_color' => $log->status_color,
                    'payment_no' => $log->payment_no,
                    'error_code' => $log->error_code,
                    'error_message' => $log->error_message,
                    'client_ip' => $log->client_ip,
                    'response_time' => $log->response_time,
                    'response_time_desc' => $log->response_time_desc,
                    'created_at' => $log->created_at->toDateTimeString(),
                ];
            });

            return $this->paginate($result, $logs);

        } catch (\Exception $e) {
            Log::error('获取支付使用日志失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付使用日志失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付使用日志详情
     */
    public function show(int $logId): JsonResponse
    {
        try {
            $log = PaymentUsageLog::with(['paymentChannel', 'order'])
                ->findOrFail($logId);

            // 权限检查
            if (!$this->canViewLogDetail($log)) {
                return $this->error('无权限查看此日志详情', 403);
            }

            $result = [
                'id' => $log->id,
                'user_type' => $log->user_type,
                'user_id' => $log->user_id,
                'channel_code' => $log->channel_code,
                'channel_name' => $log->paymentChannel->channel_name ?? '',
                'order_id' => $log->order_id,
                'order_no' => $log->order_no,
                'amount' => $log->amount,
                'fee' => $log->fee,
                'action_type' => $log->action_type,
                'action_type_name' => $log->action_type_name,
                'status' => $log->status,
                'status_name' => $log->status_name,
                'status_color' => $log->status_color,
                'payment_no' => $log->payment_no,
                'request_data' => $log->request_data,
                'response_data' => $log->response_data,
                'error_code' => $log->error_code,
                'error_message' => $log->error_message,
                'client_ip' => $log->client_ip,
                'user_agent' => $log->user_agent,
                'response_time' => $log->response_time,
                'response_time_desc' => $log->response_time_desc,
                'created_at' => $log->created_at->toDateTimeString(),
                'order' => $log->order ? [
                    'id' => $log->order->id,
                    'order_no' => $log->order->order_no,
                    'status' => $log->order->status,
                    'amount' => $log->order->amount,
                ] : null,
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error('获取支付使用日志详情失败', [
                'log_id' => $logId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付使用日志详情失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付统计数据
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'string|in:user,distributor,substation',
                'user_id' => 'integer|min:1',
                'period' => 'string|in:today,week,month,year',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 权限检查
            if (!$this->canViewStatistics($request)) {
                return $this->error('无权限查看统计数据', 403);
            }

            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            $period = $request->input('period', 'month');

            $stats = $this->paymentService->getPaymentStatistics($userType, $userId, $period);

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取支付统计数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付渠道统计
     */
    public function channelStatistics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'string|in:today,week,month,year',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 权限检查
            if (!$this->canViewChannelStatistics()) {
                return $this->error('无权限查看渠道统计', 403);
            }

            $period = $request->input('period', 'month');
            $stats = $this->paymentService->getChannelStatistics($period);

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取支付渠道统计失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取支付渠道统计失败：' . $e->getMessage());
        }
    }

    /**
     * 导出支付日志
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'string|in:user,distributor,substation',
                'user_id' => 'integer|min:1',
                'channel_code' => 'string',
                'action_type' => 'string|in:create,callback,refund,query',
                'status' => 'string|in:pending,success,failed,cancelled',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'format' => 'string|in:csv,excel',
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', 422, $validator->errors());
            }

            // 权限检查
            if (!$this->canExportLogs($request)) {
                return $this->error('无权限导出支付日志', 403);
            }

            // 这里应该实现实际的导出逻辑
            // 为了演示，返回一个模拟的导出URL
            $exportUrl = url('/api/v1/payment/logs/download/' . Str::random(32));

            return $this->success([
                'export_url' => $exportUrl,
                'expires_at' => now()->addHours(24)->toDateTimeString(),
                'message' => '导出任务已创建，请稍后下载'
            ]);

        } catch (\Exception $e) {
            Log::error('导出支付日志失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('导出支付日志失败：' . $e->getMessage());
        }
    }

    /**
     * 检查是否可以查看日志
     */
    private function canViewLogs(Request $request): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // 管理员可以查看所有日志
        if ($user->hasRole('admin')) {
            return true;
        }

        // 分站管理员可以查看自己分站的日志
        if ($user->hasRole('substation')) {
            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            
            if ($userType === 'substation' && $userId === $user->id) {
                return true;
            }
        }

        // 分销商可以查看自己的日志
        if ($user->hasRole('distributor')) {
            $userType = $request->input('user_type');
            $userId = $request->input('user_id');
            
            if ($userType === 'distributor' && $userId === $user->id) {
                return true;
            }
        }

        // 普通用户可以查看自己的日志
        $userType = $request->input('user_type');
        $userId = $request->input('user_id');
        
        if ($userType === 'user' && $userId === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否可以查看日志详情
     */
    private function canViewLogDetail(PaymentUsageLog $log): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // 管理员可以查看所有日志详情
        if ($user->hasRole('admin')) {
            return true;
        }

        // 用户只能查看自己的日志详情
        if ($log->user_type === 'user' && $log->user_id === $user->id) {
            return true;
        }

        if ($log->user_type === 'substation' && $user->hasRole('substation') && $log->user_id === $user->id) {
            return true;
        }

        if ($log->user_type === 'distributor' && $user->hasRole('distributor') && $log->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否可以查看统计数据
     */
    private function canViewStatistics(Request $request): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // 管理员可以查看所有统计
        if ($user->hasRole('admin')) {
            return true;
        }

        // 用户只能查看自己的统计
        $userType = $request->input('user_type');
        $userId = $request->input('user_id');

        if ($userType && $userId) {
            if ($userType === 'user' && $userId === $user->id) {
                return true;
            }
            if ($userType === 'substation' && $user->hasRole('substation') && $userId === $user->id) {
                return true;
            }
            if ($userType === 'distributor' && $user->hasRole('distributor') && $userId === $user->id) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否可以查看渠道统计
     */
    private function canViewChannelStatistics(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['admin', 'substation']);
    }

    /**
     * 检查是否可以导出日志
     */
    private function canExportLogs(Request $request): bool
    {
        // 导出权限与查看权限相同
        return $this->canViewLogs($request);
    }
}