<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * 自定义CORS中间件
 * 确保所有API请求都包含正确的CORS头部
 */
class CorsMiddleware
{
    /**
     * 处理传入的请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 处理预检请求（OPTIONS）
        if ($request->isMethod('OPTIONS')) {
            return response()->json('OK', 200, $this->getCorsHeaders());
        }

        // 处理正常请求
        $response = $next($request);

        // 添加CORS头部
        foreach ($this->getCorsHeaders() as $key => $value) {
            $response->header($key, $value);
        }

        return $response;
    }

    /**
     * 获取CORS头部
     */
    private function getCorsHeaders(): array
    {
        return [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-Token, X-API-Key',
            'Access-Control-Expose-Headers' => 'Authorization, X-Total-Count',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Vary' => 'Origin',
        ];
    }
} 