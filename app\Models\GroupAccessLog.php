<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 群组访问日志模型
 * 记录群组的访问情况，用于统计分析和防封监控
 */
class GroupAccessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'wechat_group_id',
        'visitor_ip',
        'user_agent',
        'referer',
        'detected_city',
        'browser_type',
        'is_wechat_browser',
        'access_domain',
        'access_result',
        'block_reason',
        'extra_data',
    ];

    protected $casts = [
        'is_wechat_browser' => 'boolean',
        'extra_data' => 'array',
    ];

    // 访问结果常量
    const RESULT_SUCCESS = 'success';
    const RESULT_BLOCKED = 'blocked';
    const RESULT_ERROR = 'error';

    // 浏览器类型常量
    const BROWSER_WECHAT = 'wechat';
    const BROWSER_QQ = 'qq';
    const BROWSER_ALIPAY = 'alipay';
    const BROWSER_CHROME = 'chrome';
    const BROWSER_SAFARI = 'safari';
    const BROWSER_FIREFOX = 'firefox';
    const BROWSER_UNKNOWN = 'unknown';

    /**
     * 关联微信群
     */
    public function wechatGroup(): BelongsTo
    {
        return $this->belongsTo(WechatGroup::class);
    }

    /**
     * 记录访问日志
     */
    public static function logAccess(array $data): self
    {
        // 检测浏览器类型
        $userAgent = $data['user_agent'] ?? '';
        $browserType = self::detectBrowserType($userAgent);
        $isWechatBrowser = $browserType === self::BROWSER_WECHAT;

        return self::create([
            'wechat_group_id' => $data['wechat_group_id'],
            'visitor_ip' => $data['visitor_ip'],
            'user_agent' => $userAgent,
            'referer' => $data['referer'] ?? '',
            'detected_city' => $data['detected_city'] ?? '',
            'browser_type' => $browserType,
            'is_wechat_browser' => $isWechatBrowser,
            'access_domain' => $data['access_domain'] ?? '',
            'access_result' => $data['access_result'] ?? self::RESULT_SUCCESS,
            'block_reason' => $data['block_reason'] ?? '',
            'extra_data' => $data['extra_data'] ?? [],
        ]);
    }

    /**
     * 检测浏览器类型
     */
    public static function detectBrowserType(string $userAgent): string
    {
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            return self::BROWSER_WECHAT;
        } elseif (strpos($userAgent, 'QQ/') !== false) {
            return self::BROWSER_QQ;
        } elseif (strpos($userAgent, 'Alipay') !== false) {
            return self::BROWSER_ALIPAY;
        } elseif (strpos($userAgent, 'Chrome') !== false) {
            return self::BROWSER_CHROME;
        } elseif (strpos($userAgent, 'Safari') !== false) {
            return self::BROWSER_SAFARI;
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            return self::BROWSER_FIREFOX;
        }

        return self::BROWSER_UNKNOWN;
    }

    /**
     * 获取访问结果名称
     */
    public function getAccessResultNameAttribute(): string
    {
        $names = [
            self::RESULT_SUCCESS => '成功',
            self::RESULT_BLOCKED => '被阻止',
            self::RESULT_ERROR => '错误',
        ];

        return $names[$this->access_result] ?? '未知';
    }

    /**
     * 获取浏览器类型名称
     */
    public function getBrowserTypeNameAttribute(): string
    {
        $names = [
            self::BROWSER_WECHAT => '微信浏览器',
            self::BROWSER_QQ => 'QQ浏览器',
            self::BROWSER_ALIPAY => '支付宝',
            self::BROWSER_CHROME => 'Chrome',
            self::BROWSER_SAFARI => 'Safari',
            self::BROWSER_FIREFOX => 'Firefox',
            self::BROWSER_UNKNOWN => '未知浏览器',
        ];

        return $names[$this->browser_type] ?? '未知浏览器';
    }

    /**
     * 查询作用域：成功访问
     */
    public function scopeSuccessful($query)
    {
        return $query->where('access_result', self::RESULT_SUCCESS);
    }

    /**
     * 查询作用域：被阻止的访问
     */
    public function scopeBlocked($query)
    {
        return $query->where('access_result', self::RESULT_BLOCKED);
    }

    /**
     * 查询作用域：微信浏览器访问
     */
    public function scopeWechatBrowser($query)
    {
        return $query->where('is_wechat_browser', true);
    }

    /**
     * 查询作用域：按城市过滤
     */
    public function scopeByCity($query, string $city)
    {
        return $query->where('detected_city', $city);
    }

    /**
     * 查询作用域：今日访问
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * 查询作用域：最近N天
     */
    public function scopeRecentDays($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 获取访问统计
     */
    public static function getAccessStats(int $groupId, int $days = 7): array
    {
        $query = self::where('wechat_group_id', $groupId)
                    ->where('created_at', '>=', now()->subDays($days));

        return [
            'total_visits' => $query->count(),
            'successful_visits' => (clone $query)->successful()->count(),
            'blocked_visits' => (clone $query)->blocked()->count(),
            'wechat_visits' => (clone $query)->wechatBrowser()->count(),
            'unique_ips' => (clone $query)->distinct('visitor_ip')->count(),
            'top_cities' => self::getTopCities($groupId, $days),
            'browser_distribution' => self::getBrowserDistribution($groupId, $days),
        ];
    }

    /**
     * 获取热门城市
     */
    public static function getTopCities(int $groupId, int $days = 7): array
    {
        return self::where('wechat_group_id', $groupId)
                  ->where('created_at', '>=', now()->subDays($days))
                  ->whereNotNull('detected_city')
                  ->where('detected_city', '!=', '')
                  ->selectRaw('detected_city, COUNT(*) as visit_count')
                  ->groupBy('detected_city')
                  ->orderBy('visit_count', 'desc')
                  ->limit(10)
                  ->get()
                  ->toArray();
    }

    /**
     * 获取浏览器分布
     */
    public static function getBrowserDistribution(int $groupId, int $days = 7): array
    {
        return self::where('wechat_group_id', $groupId)
                  ->where('created_at', '>=', now()->subDays($days))
                  ->selectRaw('browser_type, COUNT(*) as visit_count')
                  ->groupBy('browser_type')
                  ->orderBy('visit_count', 'desc')
                  ->get()
                  ->toArray();
    }
}