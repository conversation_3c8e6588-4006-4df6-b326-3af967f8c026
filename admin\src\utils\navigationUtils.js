// 导航工具函数库
// admin/src/utils/navigationUtils.js

import { 
  NAVIGATION_CONFIG, 
  PERMISSION_LEVELS, 
  DEFAULT_NAVIGATION,
  ICON_MAP,
  SEARCH_CONFIG 
} from '@/config/navigation-config'

/**
 * 权限检查工具
 */
export class PermissionUtils {
  /**
   * 检查用户是否有访问权限
   * @param {string} userRole - 用户角色
   * @param {string} requiredPermission - 需要的权限
   * @returns {boolean}
   */
  static hasPermission(userRole, requiredPermission) {
    const roleHierarchy = {
      [PERMISSION_LEVELS.SUPER_ADMIN]: 4,
      [PERMISSION_LEVELS.ADMIN]: 3,
      [PERMISSION_LEVELS.USER]: 2,
      [PERMISSION_LEVELS.PUBLIC]: 1
    }
    
    const userLevel = roleHierarchy[userRole] || 1
    const requiredLevel = roleHierarchy[requiredPermission] || 1
    
    return userLevel >= requiredLevel
  }
  
  /**
   * 过滤有权限访问的导航项
   * @param {Array} navigationItems - 导航项数组
   * @param {string} userRole - 用户角色
   * @returns {Array}
   */
  static filterByPermission(navigationItems, userRole) {
    return navigationItems.filter(item => {
      if (item.permission && !this.hasPermission(userRole, item.permission)) {
        return false
      }
      
      if (item.children) {
        item.children = this.filterByPermission(item.children, userRole)
      }
      
      if (item.modules) {
        item.modules = this.filterByPermission(item.modules, userRole)
      }
      
      return true
    })
  }
}

/**
 * 导航数据处理工具
 */
export class NavigationDataUtils {
  /**
   * 生成用户专属导航数据
   * @param {string} userRole - 用户角色
   * @param {Object} customNavigation - 自定义导航数据
   * @returns {Array}
   */
  static generateUserNavigation(userRole, customNavigation = {}) {
    const navigation = { ...DEFAULT_NAVIGATION, ...customNavigation }
    const navigationArray = Object.values(navigation)
    
    // 权限过滤
    const filteredNavigation = PermissionUtils.filterByPermission(navigationArray, userRole)
    
    // 按顺序排序
    return filteredNavigation.sort((a, b) => (a.order || 999) - (b.order || 999))
  }
  
  /**
   * 扁平化导航结构
   * @param {Array} navigation - 导航数据
   * @returns {Array}
   */
  static flattenNavigation(navigation) {
    const result = []
    
    const flatten = (items, parentPath = '') => {
      items.forEach(item => {
        const currentPath = parentPath ? `${parentPath}.${item.key}` : item.key
        
        result.push({
          ...item,
          fullPath: currentPath,
          level: currentPath.split('.').length
        })
        
        if (item.modules) {
          flatten(item.modules, currentPath)
        }
        
        if (item.children) {
          flatten(item.children, currentPath)
        }
      })
    }
    
    flatten(navigation)
    return result
  }
  
  /**
   * 根据路径查找导航项
   * @param {Array} navigation - 导航数据
   * @param {string} path - 路由路径
   * @returns {Object|null}
   */
  static findItemByPath(navigation, path) {
    const flatNavigation = this.flattenNavigation(navigation)
    return flatNavigation.find(item => item.path === path) || null
  }
  
  /**
   * 获取导航项的完整路径链
   * @param {Array} navigation - 导航数据
   * @param {string} targetPath - 目标路径
   * @returns {Array}
   */
  static getNavigationChain(navigation, targetPath) {
    const chain = []
    
    const findChain = (items, currentChain = []) => {
      for (const item of items) {
        const newChain = [...currentChain, item]
        
        if (item.path === targetPath) {
          chain.push(...newChain)
          return true
        }
        
        if (item.modules && findChain(item.modules, newChain)) {
          return true
        }
        
        if (item.children && findChain(item.children, newChain)) {
          return true
        }
      }
      return false
    }
    
    findChain(navigation)
    return chain
  }
}

/**
 * 搜索工具
 */
export class SearchUtils {
  /**
   * 搜索导航项
   * @param {Array} navigation - 导航数据
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Array}
   */
  static searchNavigation(navigation, query, options = {}) {
    const {
      fields = ['title', 'description', 'keywords'],
      fuzzy = true,
      limit = 20
    } = options
    
    if (!query.trim()) return []
    
    const flatNavigation = NavigationDataUtils.flattenNavigation(navigation)
    const results = []
    
    flatNavigation.forEach(item => {
      let score = 0
      const queryLower = query.toLowerCase()
      
      fields.forEach(field => {
        if (item[field]) {
          const fieldValue = item[field].toLowerCase()
          
          // 精确匹配得分最高
          if (fieldValue === queryLower) {
            score += 100
          }
          // 开头匹配
          else if (fieldValue.startsWith(queryLower)) {
            score += 80
          }
          // 包含匹配
          else if (fieldValue.includes(queryLower)) {
            score += 60
          }
          // 模糊匹配
          else if (fuzzy && this.fuzzyMatch(fieldValue, queryLower)) {
            score += 30
          }
        }
      })
      
      if (score > 0) {
        results.push({
          ...item,
          score,
          matchedText: this.highlightMatch(item.title, query)
        })
      }
    })
    
    // 按得分排序
    results.sort((a, b) => b.score - a.score)
    
    return results.slice(0, limit)
  }
  
  /**
   * 模糊匹配
   * @param {string} text - 文本
   * @param {string} pattern - 匹配模式
   * @returns {boolean}
   */
  static fuzzyMatch(text, pattern) {
    let textIndex = 0
    let patternIndex = 0
    
    while (textIndex < text.length && patternIndex < pattern.length) {
      if (text[textIndex] === pattern[patternIndex]) {
        patternIndex++
      }
      textIndex++
    }
    
    return patternIndex === pattern.length
  }
  
  /**
   * 高亮匹配文本
   * @param {string} text - 原文本
   * @param {string} query - 搜索查询
   * @returns {string}
   */
  static highlightMatch(text, query) {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }
  
  /**
   * 获取搜索建议
   * @param {string} query - 搜索查询
   * @param {Array} history - 搜索历史
   * @returns {Array}
   */
  static getSearchSuggestions(query, history = []) {
    const suggestions = [...SEARCH_CONFIG.SUGGESTIONS]
    
    if (!query) return suggestions.slice(0, 8)
    
    const queryLower = query.toLowerCase()
    
    // 从历史记录中匹配
    const historySuggestions = history
      .filter(item => item.query.toLowerCase().includes(queryLower))
      .map(item => item.query)
      .slice(0, 3)
    
    // 从默认建议中匹配
    const defaultSuggestions = suggestions
      .filter(suggestion => suggestion.toLowerCase().includes(queryLower))
      .slice(0, 5)
    
    return [...new Set([...historySuggestions, ...defaultSuggestions])].slice(0, 8)
  }
}

/**
 * 本地存储工具
 */
export class StorageUtils {
  /**
   * 保存导航状态
   * @param {string} key - 存储键
   * @param {any} data - 数据
   */
  static saveNavigationState(key, data) {
    try {
      const serialized = JSON.stringify(data)
      localStorage.setItem(key, serialized)
    } catch (error) {
      console.warn('保存导航状态失败:', error)
    }
  }
  
  /**
   * 读取导航状态
   * @param {string} key - 存储键
   * @param {any} defaultValue - 默认值
   * @returns {any}
   */
  static loadNavigationState(key, defaultValue = null) {
    try {
      const serialized = localStorage.getItem(key)
      return serialized ? JSON.parse(serialized) : defaultValue
    } catch (error) {
      console.warn('读取导航状态失败:', error)
      return defaultValue
    }
  }
  
  /**
   * 清除导航状态
   * @param {string} key - 存储键
   */
  static clearNavigationState(key) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('清除导航状态失败:', error)
    }
  }
  
  /**
   * 管理收藏项
   */
  static manageFavorites = {
    /**
     * 添加收藏
     * @param {Object} item - 收藏项
     */
    add(item) {
      const favorites = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.FAVORITES, 
        []
      )
      
      if (!favorites.some(fav => fav.key === item.key)) {
        favorites.unshift({
          ...item,
          favoritedAt: Date.now()
        })
        
        // 限制收藏数量
        if (favorites.length > NAVIGATION_CONFIG.MAX_FAVORITES) {
          favorites.splice(NAVIGATION_CONFIG.MAX_FAVORITES)
        }
        
        StorageUtils.saveNavigationState(
          NAVIGATION_CONFIG.CACHE_KEYS.FAVORITES,
          favorites
        )
      }
    },
    
    /**
     * 移除收藏
     * @param {string} key - 项目键
     */
    remove(key) {
      const favorites = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.FAVORITES,
        []
      )
      
      const filtered = favorites.filter(fav => fav.key !== key)
      StorageUtils.saveNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.FAVORITES,
        filtered
      )
    },
    
    /**
     * 获取收藏列表
     * @returns {Array}
     */
    getAll() {
      return StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.FAVORITES,
        []
      )
    },
    
    /**
     * 检查是否已收藏
     * @param {string} key - 项目键
     * @returns {boolean}
     */
    isFavorite(key) {
      const favorites = this.getAll()
      return favorites.some(fav => fav.key === key)
    }
  }
  
  /**
   * 管理最近访问
   */
  static manageRecent = {
    /**
     * 添加最近访问
     * @param {Object} item - 访问项
     */
    add(item) {
      const recent = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.RECENT_ITEMS,
        []
      )
      
      // 移除已存在的记录
      const filtered = recent.filter(r => r.path !== item.path)
      
      // 添加到开头
      filtered.unshift({
        ...item,
        visitedAt: Date.now(),
        visitCount: (item.visitCount || 0) + 1
      })
      
      // 限制数量
      if (filtered.length > NAVIGATION_CONFIG.MAX_RECENT_ITEMS) {
        filtered.splice(NAVIGATION_CONFIG.MAX_RECENT_ITEMS)
      }
      
      StorageUtils.saveNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.RECENT_ITEMS,
        filtered
      )
    },
    
    /**
     * 获取最近访问列表
     * @param {number} limit - 限制数量
     * @returns {Array}
     */
    getAll(limit = NAVIGATION_CONFIG.MAX_RECENT_ITEMS) {
      const recent = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.RECENT_ITEMS,
        []
      )
      return recent.slice(0, limit)
    },
    
    /**
     * 清除最近访问
     */
    clear() {
      StorageUtils.clearNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.RECENT_ITEMS
      )
    }
  }
  
  /**
   * 管理搜索历史
   */
  static manageSearchHistory = {
    /**
     * 添加搜索历史
     * @param {string} query - 搜索查询
     */
    add(query) {
      if (!query.trim()) return
      
      const history = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.SEARCH_HISTORY,
        []
      )
      
      // 移除已存在的记录
      const filtered = history.filter(h => h.query !== query)
      
      // 添加到开头
      filtered.unshift({
        query,
        timestamp: Date.now()
      })
      
      // 限制数量
      if (filtered.length > 20) {
        filtered.splice(20)
      }
      
      StorageUtils.saveNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.SEARCH_HISTORY,
        filtered
      )
    },
    
    /**
     * 获取搜索历史
     * @param {number} limit - 限制数量
     * @returns {Array}
     */
    getAll(limit = 10) {
      const history = StorageUtils.loadNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.SEARCH_HISTORY,
        []
      )
      return history.slice(0, limit)
    },
    
    /**
     * 清除搜索历史
     */
    clear() {
      StorageUtils.clearNavigationState(
        NAVIGATION_CONFIG.CACHE_KEYS.SEARCH_HISTORY
      )
    }
  }
}

/**
 * URL 工具
 */
export class UrlUtils {
  /**
   * 生成面包屑路径
   * @param {string} currentPath - 当前路径
   * @param {Array} navigation - 导航数据
   * @returns {Array}
   */
  static generateBreadcrumbs(currentPath, navigation) {
    const chain = NavigationDataUtils.getNavigationChain(navigation, currentPath)
    
    return chain.map((item, index) => ({
      title: item.title,
      path: item.path,
      icon: item.icon,
      active: index === chain.length - 1
    }))
  }
  
  /**
   * 解析查询参数
   * @param {string} search - 查询字符串
   * @returns {Object}
   */
  static parseQuery(search) {
    const query = {}
    const params = new URLSearchParams(search)
    
    for (const [key, value] of params) {
      query[key] = value
    }
    
    return query
  }
  
  /**
   * 构建查询字符串
   * @param {Object} params - 参数对象
   * @returns {string}
   */
  static buildQuery(params) {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, value)
      }
    })
    
    return searchParams.toString()
  }
  
  /**
   * 检查路径是否匹配
   * @param {string} currentPath - 当前路径
   * @param {string} targetPath - 目标路径
   * @param {boolean} exact - 是否精确匹配
   * @returns {boolean}
   */
  static isPathMatch(currentPath, targetPath, exact = false) {
    if (exact) {
      return currentPath === targetPath
    }
    
    return currentPath.startsWith(targetPath)
  }
}

/**
 * 动画工具
 */
export class AnimationUtils {
  /**
   * 创建过渡动画类名
   * @param {string} name - 动画名称
   * @returns {Object}
   */
  static createTransitionClasses(name) {
    return {
      enterActiveClass: `${name}-enter-active`,
      leaveActiveClass: `${name}-leave-active`,
      enterFromClass: `${name}-enter-from`,
      leaveToClass: `${name}-leave-to`
    }
  }
  
  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {Function}
   */
  static debounce(func, delay) {
    let timeoutId
    
    return function (...args) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func.apply(this, args), delay)
    }
  }
  
  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} limit - 时间限制
   * @returns {Function}
   */
  static throttle(func, limit) {
    let inThrottle
    
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
}

/**
 * 验证工具
 */
export class ValidationUtils {
  /**
   * 验证导航项结构
   * @param {Object} item - 导航项
   * @returns {boolean}
   */
  static validateNavigationItem(item) {
    const requiredFields = ['key', 'title']
    const validTypes = ['section', 'module', 'page', 'external', 'separator']
    
    // 检查必需字段
    for (const field of requiredFields) {
      if (!item[field]) {
        console.warn(`导航项缺少必需字段: ${field}`, item)
        return false
      }
    }
    
    // 检查类型
    if (item.type && !validTypes.includes(item.type)) {
      console.warn(`无效的导航项类型: ${item.type}`, item)
      return false
    }
    
    // 检查权限
    if (item.permission && !Object.values(PERMISSION_LEVELS).includes(item.permission)) {
      console.warn(`无效的权限级别: ${item.permission}`, item)
      return false
    }
    
    return true
  }
  
  /**
   * 验证导航数据完整性
   * @param {Array} navigation - 导航数据
   * @returns {boolean}
   */
  static validateNavigationData(navigation) {
    if (!Array.isArray(navigation)) {
      console.warn('导航数据必须是数组')
      return false
    }
    
    const keys = new Set()
    
    const validateRecursive = (items, level = 0) => {
      for (const item of items) {
        // 检查重复键
        if (keys.has(item.key)) {
          console.warn(`发现重复的导航项键: ${item.key}`)
          return false
        }
        keys.add(item.key)
        
        // 验证单个项
        if (!this.validateNavigationItem(item)) {
          return false
        }
        
        // 检查嵌套层级
        if (level > 3) {
          console.warn('导航嵌套层级过深')
          return false
        }
        
        // 递归验证子项
        if (item.modules && !validateRecursive(item.modules, level + 1)) {
          return false
        }
        
        if (item.children && !validateRecursive(item.children, level + 1)) {
          return false
        }
      }
      
      return true
    }
    
    return validateRecursive(navigation)
  }
}

/**
 * 导出所有工具类
 */
export {
  PermissionUtils,
  NavigationDataUtils,
  SearchUtils,
  StorageUtils,
  UrlUtils,
  AnimationUtils,
  ValidationUtils
}

/**
 * 默认导出常用工具函数
 */
export default {
  // 生成用户导航
  generateUserNavigation: NavigationDataUtils.generateUserNavigation,
  
  // 搜索导航
  searchNavigation: SearchUtils.searchNavigation,
  
  // 权限检查
  hasPermission: PermissionUtils.hasPermission,
  
  // 存储管理
  manageFavorites: StorageUtils.manageFavorites,
  manageRecent: StorageUtils.manageRecent,
  manageSearchHistory: StorageUtils.manageSearchHistory,
  
  // URL工具
  generateBreadcrumbs: UrlUtils.generateBreadcrumbs,
  isPathMatch: UrlUtils.isPathMatch,
  
  // 动画工具
  debounce: AnimationUtils.debounce,
  throttle: AnimationUtils.throttle,
  
  // 验证工具
  validateNavigationData: ValidationUtils.validateNavigationData
}