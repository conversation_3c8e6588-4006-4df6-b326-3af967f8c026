<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Substation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class SubstationController extends Controller
{
    /**
     * 获取分站列表
     */
    public function index(Request $request)
    {
        $query = Substation::with(['user']);
        
        // 搜索条件
        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }
        
        if ($request->filled('domain')) {
            $query->where('domain', 'like', '%' . $request->domain . '%');
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $substations = $query->orderBy('created_at', 'desc')
                            ->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => $substations
        ]);
    }
    
    /**
     * 创建分站
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|unique:substations|max:100',
            'domain' => 'required|unique:substations|max:100',
            'user_id' => 'required|exists:users,id',
            'commission_rate' => 'required|numeric|min:0|max:1',
            'expire_months' => 'required|integer|min:1|max:36',
            'description' => 'nullable|string|max:500',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        // 检查管理员是否已经有分站
        if (Substation::where('user_id', $request->user_id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => '该用户已经有分站了'
            ], 400);
        }
        
        $expireDate = Carbon::now()->addMonths($request->expire_months);
        
        $substation = Substation::create([
            'name' => $request->name,
            'domain' => $request->domain,
            'user_id' => $request->user_id,
            'commission_rate' => $request->commission_rate,
            'expire_at' => $expireDate,
            'description' => $request->description,
            'status' => 1
        ]);
        
        // 更新用户角色为分站管理员
        User::where('id', $request->user_id)->update(['role' => 'substation']);
        
        return response()->json([
            'success' => true,
            'data' => $substation,
            'message' => '分站创建成功'
        ]);
    }
    
    /**
     * 获取分站详情
     */
    public function show($id)
    {
        $substation = Substation::with(['user'])->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $substation
        ]);
    }
    
    /**
     * 更新分站
     */
    public function update(Request $request, $id)
    {
        $substation = Substation::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'max:100',
                Rule::unique('substations')->ignore($substation->id)
            ],
            'domain' => [
                'required',
                'max:100',
                Rule::unique('substations')->ignore($substation->id)
            ],
            'user_id' => 'required|exists:users,id',
            'commission_rate' => 'required|numeric|min:0|max:1',
            'description' => 'nullable|string|max:500',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        // 检查管理员是否已经有其他分站
        if (Substation::where('user_id', $request->user_id)
                     ->where('id', '!=', $id)
                     ->exists()) {
            return response()->json([
                'success' => false,
                'message' => '该用户已经有其他分站了'
            ], 400);
        }
        
        $substation->update($request->only([
            'name', 'domain', 'user_id', 'commission_rate', 'description'
        ]));
        
        return response()->json([
            'success' => true,
            'data' => $substation,
            'message' => '分站更新成功'
        ]);
    }
    
    /**
     * 删除分站
     */
    public function destroy($id)
    {
        $substation = Substation::findOrFail($id);
        
        // 检查是否有关联的用户（通过分销组）
        if (User::whereHas('distributionGroup', function($query) use ($id) {
            $query->where('substation_id', $id);
        })->exists()) {
            return response()->json([
                'success' => false,
                'message' => '该分站有关联用户，无法删除'
            ], 400);
        }
        
        $substation->delete();
        
        return response()->json([
            'success' => true,
            'message' => '分站删除成功'
        ]);
    }
    
    /**
     * 更新分站状态
     */
    public function updateStatus(Request $request, $id)
    {
        $substation = Substation::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:1,2,3'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $substation->update(['status' => $request->status]);
        
        return response()->json([
            'success' => true,
            'message' => '分站状态更新成功'
        ]);
    }
    
    /**
     * 分站续费
     */
    public function renew(Request $request, $id)
    {
        $substation = Substation::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'months' => 'required|integer|min:1|max:24'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $currentExpireDate = Carbon::parse($substation->expire_at);
        $newExpireDate = $currentExpireDate->addMonths($request->months);
        
        $substation->update(['expire_at' => $newExpireDate]);
        
        return response()->json([
            'success' => true,
            'message' => '分站续费成功',
            'data' => [
                'new_expire_date' => $newExpireDate->format('Y-m-d H:i:s')
            ]
        ]);
    }
    
    /**
     * 获取我的分站信息
     */
    public function getMy()
    {
        $user = auth()->user();
        $substation = Substation::where('user_id', $user->id)->first();
        
        if (!$substation) {
            return response()->json([
                'success' => false,
                'message' => '您没有分站权限'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $substation
        ]);
    }
    
    /**
     * 更新我的分站信息
     */
    public function updateMy(Request $request)
    {
        $user = auth()->user();
        $substation = Substation::where('user_id', $user->id)->first();
        
        if (!$substation) {
            return response()->json([
                'success' => false,
                'message' => '您没有分站权限'
            ], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'max:100',
                Rule::unique('substations')->ignore($substation->id)
            ],
            'description' => 'nullable|string|max:500',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $substation->update($request->only(['name', 'description']));
        
        return response()->json([
            'success' => true,
            'data' => $substation,
            'message' => '分站信息更新成功'
        ]);
    }
    
    /**
     * 获取我的分站统计
     */
    public function getMyStats()
    {
        $user = auth()->user();
        $substation = Substation::where('user_id', $user->id)->first();
        
        if (!$substation) {
            return response()->json([
                'success' => false,
                'message' => '您没有分站权限'
            ], 404);
        }
        
        $stats = [
            'total_users' => User::whereHas('distributionGroup', function($query) use ($substation) {
                $query->where('substation_id', $substation->id);
            })->count(),
            'total_orders' => 0, // 根据实际业务逻辑计算
            'total_amount' => 0,
            'total_commission' => 0,
            'monthly_users' => User::whereHas('distributionGroup', function($query) use ($substation) {
                $query->where('substation_id', $substation->id);
            })->whereMonth('created_at', now()->month)->count(),
            'monthly_orders' => 0,
            'monthly_amount' => 0,
            'monthly_commission' => 0,
        ];
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
    
    /**
     * 检查域名是否可用
     */
    public function checkDomain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain' => 'required|string|max:100'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        $exists = Substation::where('domain', $request->domain)->exists();
        
        return response()->json([
            'success' => true,
            'data' => [
                'available' => !$exists,
                'message' => $exists ? '域名已被使用' : '域名可用'
            ]
        ]);
    }
    
    /**
     * 开通分站（为用户开通分站权限）
     */
    public function open(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|unique:substations|max:100',
            'domain' => 'required|unique:substations|max:100',
            'commission_rate' => 'required|numeric|min:0|max:1',
            'expire_months' => 'required|integer|min:1|max:36',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }
        
        // 检查用户是否已经有分站
        if (Substation::where('user_id', $request->user_id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => '该用户已经有分站了'
            ], 400);
        }
        
        $expireDate = Carbon::now()->addMonths($request->expire_months);
        
        $substation = Substation::create([
            'name' => $request->name,
            'domain' => $request->domain,
            'user_id' => $request->user_id,
            'commission_rate' => $request->commission_rate,
            'expire_at' => $expireDate,
            'status' => 1
        ]);
        
        // 更新用户角色为分站管理员
        User::where('id', $request->user_id)->update(['role' => 'substation']);
        
        return response()->json([
            'success' => true,
            'data' => $substation,
            'message' => '分站开通成功'
        ]);
    }
} 