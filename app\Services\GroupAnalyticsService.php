<?php

namespace App\Services;

use App\Models\GroupAnalytics;
use App\Models\WechatGroup;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 群组分析服务
 */
class GroupAnalyticsService
{
    /**
     * 记录页面访问
     */
    public function recordPageView(int $groupId, array $visitorData = []): void
    {
        try {
            // 获取访客信息
            $ip = request()->ip();
            $userAgent = request()->userAgent();
            $referer = request()->header('referer');
            
            // 解析访客数据
            $deviceInfo = $this->parseUserAgent($userAgent);
            $trafficSource = $this->parseTrafficSource($referer);
            $geoLocation = $this->getGeoLocation($ip);
            
            // 检查是否为新访客
            $isNewVisitor = $this->isNewVisitor($groupId, $ip, $userAgent);
            
            // 记录到分析表
            GroupAnalytics::recordPageView($groupId, [
                'is_new' => $isNewVisitor,
                'source' => $trafficSource,
                'device' => $deviceInfo,
                'geo' => $geoLocation,
                'ip' => $ip,
                'user_agent' => $userAgent,
                'referer' => $referer
            ]);
            
            // 更新群组浏览量
            $group = WechatGroup::find($groupId);
            if ($group) {
                $group->incrementViewCount();
            }
            
        } catch (\Exception $e) {
            \Log::error('记录页面访问失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage(),
                'visitor_data' => $visitorData
            ]);
        }
    }

    /**
     * 记录转化事件
     */
    public function recordConversion(int $groupId, string $type, float $value = 0, array $metadata = []): void
    {
        try {
            GroupAnalytics::recordConversion($groupId, $type, $value);
            
            // 记录详细的转化日志
            \Log::info('转化事件记录', [
                'group_id' => $groupId,
                'type' => $type,
                'value' => $value,
                'metadata' => $metadata,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()
            ]);
            
        } catch (\Exception $e) {
            \Log::error('记录转化事件失败', [
                'group_id' => $groupId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取群组分析摘要
     */
    public function getGroupSummary(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "group_summary:{$groupId}:" . $startDate->format('Y-m-d') . ':' . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, 1800, function () use ($groupId, $startDate, $endDate) {
            return GroupAnalytics::getGroupSummary($groupId, $startDate, $endDate);
        });
    }

    /**
     * 获取趋势数据
     */
    public function getTrendData(int $groupId, Carbon $startDate, Carbon $endDate, string $metric): array
    {
        $cacheKey = "group_trend:{$groupId}:{$metric}:" . $startDate->format('Y-m-d') . ':' . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, 900, function () use ($groupId, $startDate, $endDate, $metric) {
            return GroupAnalytics::getTrendData($groupId, $startDate, $endDate, $metric);
        });
    }

    /**
     * 获取对比数据
     */
    public function getComparisonData(int $groupId, Carbon $currentStart, Carbon $currentEnd, Carbon $previousStart, Carbon $previousEnd): array
    {
        $cacheKey = "group_comparison:{$groupId}:" . 
                   $currentStart->format('Y-m-d') . ':' . $currentEnd->format('Y-m-d') . ':' .
                   $previousStart->format('Y-m-d') . ':' . $previousEnd->format('Y-m-d');
        
        return Cache::remember($cacheKey, 1800, function () use ($groupId, $currentStart, $currentEnd, $previousStart, $previousEnd) {
            return GroupAnalytics::getComparisonData($groupId, $currentStart, $currentEnd, $previousStart, $previousEnd);
        });
    }

    /**
     * 获取实时统计
     */
    public function getRealTimeStats(int $groupId): array
    {
        $cacheKey = "group_realtime:{$groupId}";
        
        return Cache::remember($cacheKey, 60, function () use ($groupId) {
            $today = today();
            
            return [
                'online_visitors' => $this->getOnlineVisitors($groupId),
                'today_page_views' => $this->getTodayPageViews($groupId),
                'today_unique_visitors' => $this->getTodayUniqueVisitors($groupId),
                'today_conversions' => $this->getTodayConversions($groupId),
                'recent_activities' => $this->getRecentActivities($groupId, 10)
            ];
        });
    }

    /**
     * 获取流量来源分析
     */
    public function getTrafficSourceAnalysis(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = GroupAnalytics::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->get();

        $sources = [];
        $domains = [];
        
        foreach ($analytics as $record) {
            // 合并流量来源数据
            if ($record->traffic_sources) {
                foreach ($record->traffic_sources as $source => $count) {
                    $sources[$source] = ($sources[$source] ?? 0) + $count;
                }
            }
            
            // 合并来源域名数据
            if ($record->referrer_domains) {
                foreach ($record->referrer_domains as $domain => $count) {
                    $domains[$domain] = ($domains[$domain] ?? 0) + $count;
                }
            }
        }

        // 排序并取前10
        arsort($sources);
        arsort($domains);

        return [
            'sources' => array_slice($sources, 0, 10, true),
            'domains' => array_slice($domains, 0, 10, true),
            'total_sources' => count($sources),
            'total_domains' => count($domains)
        ];
    }

    /**
     * 获取设备分析
     */
    public function getDeviceAnalysis(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = GroupAnalytics::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->get();

        $devices = [];
        $browsers = [];
        $os = [];
        
        foreach ($analytics as $record) {
            // 设备类型统计
            if ($record->device_types) {
                foreach ($record->device_types as $device => $count) {
                    $devices[$device] = ($devices[$device] ?? 0) + $count;
                }
            }
            
            // 浏览器统计
            if ($record->browser_types) {
                foreach ($record->browser_types as $browser => $count) {
                    $browsers[$browser] = ($browsers[$browser] ?? 0) + $count;
                }
            }
            
            // 操作系统统计
            if ($record->os_types) {
                foreach ($record->os_types as $osType => $count) {
                    $os[$osType] = ($os[$osType] ?? 0) + $count;
                }
            }
        }

        return [
            'devices' => $devices,
            'browsers' => $browsers,
            'operating_systems' => $os
        ];
    }

    /**
     * 获取地理位置分析
     */
    public function getGeoAnalysis(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = GroupAnalytics::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->get();

        $locations = [];
        
        foreach ($analytics as $record) {
            if ($record->geo_locations) {
                foreach ($record->geo_locations as $location => $count) {
                    $locations[$location] = ($locations[$location] ?? 0) + $count;
                }
            }
        }

        arsort($locations);

        return [
            'locations' => array_slice($locations, 0, 20, true),
            'total_locations' => count($locations)
        ];
    }

    /**
     * 获取转化漏斗分析
     */
    public function getConversionFunnel(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $analytics = GroupAnalytics::byGroup($groupId)
            ->byDateRange($startDate, $endDate)
            ->get();

        $totalViews = $analytics->sum('page_views');
        $totalConversionViews = $analytics->sum('conversion_views');
        $totalConversionClicks = $analytics->sum('conversion_clicks');
        $totalConversionOrders = $analytics->sum('conversion_orders');

        $funnel = [
            [
                'step' => '页面访问',
                'count' => $totalViews,
                'rate' => 100,
                'description' => '用户访问群组页面'
            ],
            [
                'step' => '查看详情',
                'count' => $totalConversionViews,
                'rate' => $totalViews > 0 ? round(($totalConversionViews / $totalViews) * 100, 2) : 0,
                'description' => '用户查看群组详细信息'
            ],
            [
                'step' => '点击加入',
                'count' => $totalConversionClicks,
                'rate' => $totalConversionViews > 0 ? round(($totalConversionClicks / $totalConversionViews) * 100, 2) : 0,
                'description' => '用户点击加入按钮'
            ],
            [
                'step' => '完成支付',
                'count' => $totalConversionOrders,
                'rate' => $totalConversionClicks > 0 ? round(($totalConversionOrders / $totalConversionClicks) * 100, 2) : 0,
                'description' => '用户完成支付加入群组'
            ]
        ];

        return [
            'funnel' => $funnel,
            'overall_conversion_rate' => $totalViews > 0 ? round(($totalConversionOrders / $totalViews) * 100, 2) : 0
        ];
    }

    /**
     * 生成分析报告
     */
    public function generateReport(int $groupId, Carbon $startDate, Carbon $endDate): array
    {
        $summary = $this->getGroupSummary($groupId, $startDate, $endDate);
        $trafficSources = $this->getTrafficSourceAnalysis($groupId, $startDate, $endDate);
        $deviceAnalysis = $this->getDeviceAnalysis($groupId, $startDate, $endDate);
        $geoAnalysis = $this->getGeoAnalysis($groupId, $startDate, $endDate);
        $conversionFunnel = $this->getConversionFunnel($groupId, $startDate, $endDate);
        
        // 获取对比数据（与上一个周期对比）
        $periodDays = $startDate->diffInDays($endDate) + 1;
        $previousStart = $startDate->copy()->subDays($periodDays);
        $previousEnd = $endDate->copy()->subDays($periodDays);
        $comparison = $this->getComparisonData($groupId, $startDate, $endDate, $previousStart, $previousEnd);

        return [
            'group_id' => $groupId,
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $periodDays
            ],
            'summary' => $summary,
            'comparison' => $comparison,
            'traffic_sources' => $trafficSources,
            'device_analysis' => $deviceAnalysis,
            'geo_analysis' => $geoAnalysis,
            'conversion_funnel' => $conversionFunnel,
            'generated_at' => now()->toDateTimeString()
        ];
    }

    // 私有方法

    /**
     * 解析User-Agent
     */
    private function parseUserAgent(string $userAgent): array
    {
        $deviceType = 'desktop';
        $browser = 'unknown';
        $os = 'unknown';

        // 简单的设备类型检测
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            $deviceType = 'mobile';
        } elseif (preg_match('/Tablet|iPad/', $userAgent)) {
            $deviceType = 'tablet';
        }

        // 浏览器检测
        if (preg_match('/Chrome\/([0-9.]+)/', $userAgent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Edge\/([0-9.]+)/', $userAgent)) {
            $browser = 'Edge';
        }

        // 操作系统检测
        if (preg_match('/Windows NT ([0-9.]+)/', $userAgent)) {
            $os = 'Windows';
        } elseif (preg_match('/Mac OS X ([0-9._]+)/', $userAgent)) {
            $os = 'macOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            $os = 'Linux';
        } elseif (preg_match('/Android ([0-9.]+)/', $userAgent)) {
            $os = 'Android';
        } elseif (preg_match('/iPhone OS ([0-9._]+)/', $userAgent)) {
            $os = 'iOS';
        }

        return [
            'type' => $deviceType,
            'browser' => $browser,
            'os' => $os
        ];
    }

    /**
     * 解析流量来源
     */
    private function parseTrafficSource(?string $referer): string
    {
        if (empty($referer)) {
            return 'direct';
        }

        $domain = parse_url($referer, PHP_URL_HOST);
        
        if (!$domain) {
            return 'direct';
        }

        // 搜索引擎检测
        $searchEngines = [
            'google' => 'google',
            'baidu' => 'baidu',
            'bing' => 'bing',
            'yahoo' => 'yahoo',
            'sogou' => 'sogou',
            '360' => '360'
        ];

        foreach ($searchEngines as $engine => $name) {
            if (strpos($domain, $engine) !== false) {
                return 'search_' . $name;
            }
        }

        // 社交媒体检测
        $socialMedia = [
            'weixin' => 'wechat',
            'qq.com' => 'qq',
            'weibo' => 'weibo',
            'facebook' => 'facebook',
            'twitter' => 'twitter'
        ];

        foreach ($socialMedia as $platform => $name) {
            if (strpos($domain, $platform) !== false) {
                return 'social_' . $name;
            }
        }

        return 'referral';
    }

    /**
     * 获取地理位置
     */
    private function getGeoLocation(string $ip): string
    {
        // 这里可以集成第三方IP地理位置服务
        // 暂时返回默认值
        return 'unknown';
    }

    /**
     * 检查是否为新访客
     */
    private function isNewVisitor(int $groupId, string $ip, string $userAgent): bool
    {
        $cacheKey = "visitor:{$groupId}:" . md5($ip . $userAgent);
        
        if (Cache::has($cacheKey)) {
            return false;
        }

        // 设置24小时过期
        Cache::put($cacheKey, true, 1440);
        
        return true;
    }

    /**
     * 获取在线访客数
     */
    private function getOnlineVisitors(int $groupId): int
    {
        // 这里可以实现更复杂的在线访客统计
        // 暂时返回模拟数据
        return rand(5, 50);
    }

    /**
     * 获取今日页面浏览量
     */
    private function getTodayPageViews(int $groupId): int
    {
        return GroupAnalytics::byGroup($groupId)
            ->today()
            ->sum('page_views');
    }

    /**
     * 获取今日独立访客数
     */
    private function getTodayUniqueVisitors(int $groupId): int
    {
        return GroupAnalytics::byGroup($groupId)
            ->today()
            ->sum('unique_visitors');
    }

    /**
     * 获取今日转化数
     */
    private function getTodayConversions(int $groupId): int
    {
        return GroupAnalytics::byGroup($groupId)
            ->today()
            ->sum('conversion_orders');
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities(int $groupId, int $limit): array
    {
        // 这里可以实现最近活动的获取
        // 暂时返回模拟数据
        return [
            [
                'type' => 'page_view',
                'description' => '用户访问了群组页面',
                'time' => now()->subMinutes(5)->toDateTimeString()
            ],
            [
                'type' => 'conversion',
                'description' => '用户完成了支付',
                'time' => now()->subMinutes(15)->toDateTimeString()
            ]
        ];
    }
}