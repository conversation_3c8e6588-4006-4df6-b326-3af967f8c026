<template>
  <div class="stats-overview-widget">
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.id">
        <div class="stat-icon" :class="stat.iconClass">
          <el-icon><component :is="stat.icon" /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatValue(stat.value) }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
            <el-icon>
              <component :is="stat.trend > 0 ? 'TrendCharts' : 'Bottom'" />
            </el-icon>
            <span>{{ Math.abs(stat.trend) }}%</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="chart-section" v-if="config.showTrends">
      <div class="chart-header">
        <h4>📈 趋势分析</h4>
        <el-select v-model="selectedPeriod" size="small" style="width: 120px">
          <el-option label="7天" value="7d" />
          <el-option label="30天" value="30d" />
          <el-option label="90天" value="90d" />
        </el-select>
      </div>
      <div class="chart-container" ref="chartContainer"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { TrendCharts, Bottom, User, ChatDotRound, ShoppingCart, DataAnalysis } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  config: {
    type: Object,
    default: () => ({ showTrends: true })
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update'])

// 响应式数据
const selectedPeriod = ref('7d')
const chartContainer = ref(null)
let chartInstance = null

// 统计数据
const stats = ref([
  {
    id: 'total_users',
    label: '总用户数',
    value: 12580,
    trend: 12.5,
    icon: 'User',
    iconClass: 'text-blue-500 bg-blue-50'
  },
  {
    id: 'total_groups',
    label: '群组总数',
    value: 856,
    trend: 8.3,
    icon: 'ChatDotRound',
    iconClass: 'text-green-500 bg-green-50'
  },
  {
    id: 'total_orders',
    label: '订单总数',
    value: 3420,
    trend: -2.1,
    icon: 'ShoppingCart',
    iconClass: 'text-orange-500 bg-orange-50'
  },
  {
    id: 'revenue',
    label: '总收入',
    value: 158900,
    trend: 15.7,
    icon: 'DataAnalysis',
    iconClass: 'text-purple-500 bg-purple-50'
  }
])

// 方法
const formatValue = (value) => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万'
  }
  return value.toLocaleString()
}

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['用户增长', '群组创建', '订单量', '收入']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: generateDateRange(selectedPeriod.value)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用户增长',
        type: 'line',
        stack: 'Total',
        data: generateTrendData(selectedPeriod.value, 'users')
      },
      {
        name: '群组创建',
        type: 'line',
        stack: 'Total',
        data: generateTrendData(selectedPeriod.value, 'groups')
      },
      {
        name: '订单量',
        type: 'line',
        stack: 'Total',
        data: generateTrendData(selectedPeriod.value, 'orders')
      },
      {
        name: '收入',
        type: 'line',
        stack: 'Total',
        data: generateTrendData(selectedPeriod.value, 'revenue')
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const generateDateRange = (period) => {
  const days = parseInt(period)
  const dates = []
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
  }
  return dates
}

const generateTrendData = (period, type) => {
  const days = parseInt(period)
  const data = []
  
  for (let i = 0; i < days; i++) {
    let value
    switch (type) {
      case 'users':
        value = Math.floor(Math.random() * 100) + 50
        break
      case 'groups':
        value = Math.floor(Math.random() * 20) + 5
        break
      case 'orders':
        value = Math.floor(Math.random() * 50) + 10
        break
      case 'revenue':
        value = Math.floor(Math.random() * 5000) + 1000
        break
      default:
        value = Math.floor(Math.random() * 100)
    }
    data.push(value)
  }
  
  return data
}

const updateChart = () => {
  if (chartInstance) {
    const option = {
      xAxis: {
        data: generateDateRange(selectedPeriod.value)
      },
      series: [
        {
          data: generateTrendData(selectedPeriod.value, 'users')
        },
        {
          data: generateTrendData(selectedPeriod.value, 'groups')
        },
        {
          data: generateTrendData(selectedPeriod.value, 'orders')
        },
        {
          data: generateTrendData(selectedPeriod.value, 'revenue')
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

// 监听器
watch(selectedPeriod, () => {
  updateChart()
})

// 生命周期
onMounted(() => {
  if (props.config.showTrends) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style lang="scss" scoped>
.stats-overview-widget {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-right: 12px;
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 500;

          &.positive {
            color: #10b981;
          }

          &.negative {
            color: #ef4444;
          }
        }
      }
    }
  }

  .chart-section {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .chart-container {
      height: 300px;
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .stats-overview-widget {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .stat-card {
        flex-direction: column;
        text-align: center;
        padding: 12px;

        .stat-icon {
          margin-right: 0;
          margin-bottom: 8px;
        }
      }
    }

    .chart-section {
      .chart-container {
        height: 250px;
      }
    }
  }
}
</style>