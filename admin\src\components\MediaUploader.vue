<template>
  <div class="media-uploader">
    <el-upload
      ref="uploadRef"
      :class="uploaderClass"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :list-type="listType"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :drag="drag"
      :disabled="disabled"
    >
      <template v-if="listType === 'picture-card'">
        <div class="upload-card">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">上传{{ typeText }}</div>
        </div>
      </template>
      
      <template v-else-if="drag">
        <div class="upload-dragger">
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">将{{ typeText }}拖到此处，或<em>点击上传</em></div>
          <div class="upload-hint">{{ uploadHint }}</div>
        </div>
      </template>
      
      <template v-else>
        <el-button :icon="Upload" type="primary" :loading="uploading">
          上传{{ typeText }}
        </el-button>
      </template>
    </el-upload>
    
    <!-- 上传提示 -->
    <div v-if="$slots.tip" class="upload-tip">
      <slot name="tip"></slot>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
    >
      <img :src="previewUrl" alt="预览图片" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Upload, UploadFilled } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  type: {
    type: String,
    default: 'image', // image, video, file
    validator: (value) => ['image', 'video', 'file'].includes(value)
  },
  multiple: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 1
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  listType: {
    type: String,
    default: 'text', // text, picture, picture-card
    validator: (value) => ['text', 'picture', 'picture-card'].includes(value)
  },
  drag: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
    default: 10 // MB
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'success', 'error'])

const uploadRef = ref()
const uploading = ref(false)
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const typeText = computed(() => {
  const typeMap = {
    image: '图片',
    video: '视频',
    file: '文件'
  }
  return typeMap[props.type] || '文件'
})

const uploaderClass = computed(() => {
  return [
    'media-uploader-component',
    `media-uploader-${props.type}`,
    {
      'is-drag': props.drag,
      'is-disabled': props.disabled
    }
  ]
})

const uploadUrl = computed(() => {
  // 在开发环境中使用Mock上传
  if (import.meta.env.DEV) {
    return '#mock-upload'
  }
  return '/api/upload'
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${getToken()}`
  }
})

const uploadData = computed(() => {
  return {
    type: props.type
  }
})

const uploadHint = computed(() => {
  const sizeText = props.maxSize > 1 ? `${props.maxSize}MB` : `${props.maxSize * 1024}KB`
  return `支持 ${props.accept}，单个文件不超过 ${sizeText}`
})

const fileList = computed(() => {
  if (!props.modelValue) return []
  
  if (Array.isArray(props.modelValue)) {
    return props.modelValue.map((url, index) => ({
      uid: index,
      name: `${typeText.value}${index + 1}`,
      status: 'success',
      url: url
    }))
  } else {
    return props.modelValue ? [{
      uid: 0,
      name: typeText.value,
      status: 'success',
      url: props.modelValue
    }] : []
  }
})

// 方法
const beforeUpload = (file) => {
  // 文件类型检查
  const isValidType = checkFileType(file)
  if (!isValidType) {
    ElMessage.error(`请上传正确的${typeText.value}格式`)
    return false
  }

  // 文件大小检查
  const isValidSize = file.size / 1024 / 1024 < props.maxSize
  if (!isValidSize) {
    ElMessage.error(`${typeText.value}大小不能超过 ${props.maxSize}MB`)
    return false
  }

  uploading.value = true

  // 在开发环境中使用Mock上传
  if (import.meta.env.DEV && uploadUrl.value === '#mock-upload') {
    // 模拟上传过程
    setTimeout(() => {
      const mockResponse = {
        success: true,
        message: '上传成功',
        data: {
          url: `https://picsum.photos/400/300?random=${Math.random()}`,
          filename: file.name,
          size: file.size,
          type: file.type,
          upload_time: new Date().toISOString()
        }
      }
      handleSuccess(mockResponse, file)
    }, 1000 + Math.random() * 2000)

    return false // 阻止实际上传
  }

  return true
}

const checkFileType = (file) => {
  const { type } = props
  const fileType = file.type
  
  switch (type) {
    case 'image':
      return fileType.startsWith('image/')
    case 'video':
      return fileType.startsWith('video/')
    case 'file':
      return true
    default:
      return true
  }
}

const handleSuccess = (response, file) => {
  uploading.value = false
  
  if (response.success) {
    const url = response.data.url
    updateValue(url)
    emit('success', response, file)
    emit('change', props.modelValue)
    ElMessage.success('上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
    emit('error', response, file)
  }
}

const handleError = (error, file) => {
  uploading.value = false
  ElMessage.error('上传失败，请重试')
  emit('error', error, file)
}

const handleRemove = (file) => {
  const url = file.url
  removeValue(url)
  emit('change', props.modelValue)
}

const handleExceed = (files) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个${typeText.value}`)
}

const handlePreview = (file) => {
  if (props.type === 'image') {
    previewUrl.value = file.url
    previewVisible.value = true
  } else {
    window.open(file.url, '_blank')
  }
}

const updateValue = (url) => {
  if (props.multiple) {
    const currentValue = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    currentValue.push(url)
    emit('update:modelValue', currentValue)
  } else {
    emit('update:modelValue', url)
  }
}

const removeValue = (url) => {
  if (props.multiple) {
    const currentValue = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    const index = currentValue.indexOf(url)
    if (index > -1) {
      currentValue.splice(index, 1)
      emit('update:modelValue', currentValue)
    }
  } else {
    emit('update:modelValue', '')
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 值变化时的处理
}, { deep: true })
</script>

<style lang="scss" scoped>
.media-uploader {
  .upload-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
    
    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 8px;
    }
    
    .upload-text {
      font-size: 14px;
      color: #8c939d;
    }
  }
  
  .upload-dragger {
    text-align: center;
    padding: 40px 20px;
    
    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
    
    .upload-text {
      font-size: 16px;
      color: #606266;
      margin-bottom: 8px;
      
      em {
        color: #409eff;
        font-style: normal;
      }
    }
    
    .upload-hint {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
  
  :deep(.el-upload-list) {
    margin-top: 8px;
  }
  
  :deep(.el-upload--picture-card) {
    width: 104px;
    height: 104px;
    line-height: 102px;
  }
  
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 104px;
    height: 104px;
  }
}

.media-uploader.is-disabled {
  .upload-card,
  .upload-dragger {
    cursor: not-allowed;
    opacity: 0.6;
  }
}
</style>
