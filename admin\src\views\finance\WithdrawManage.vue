<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">{{ withdrawStats.pending_count }}</div>
              <div class="stats-label">待审核</div>
              <div class="stats-amount">¥{{ formatNumber(withdrawStats.pending_amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">{{ withdrawStats.approved_count }}</div>
              <div class="stats-label">已批准</div>
              <div class="stats-amount">¥{{ formatNumber(withdrawStats.approved_amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed-icon">
              <i class="el-icon-success"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">{{ withdrawStats.completed_count }}</div>
              <div class="stats-label">已完成</div>
              <div class="stats-amount">¥{{ formatNumber(withdrawStats.completed_amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total-icon">
              <i class="el-icon-wallet"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">{{ withdrawStats.total_count }}</div>
              <div class="stats-label">总提现</div>
              <div class="stats-amount">¥{{ formatNumber(withdrawStats.total_amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选区域 -->
    <el-card style="margin-bottom: 20px">
      <el-form :inline="true" :model="listQuery" label-width="80px">
        <el-form-item label="用户名">
          <el-input
            v-model="listQuery.user_name"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="待审核" value="pending" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提现方式">
          <el-select v-model="listQuery.method" placeholder="请选择方式" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信" value="wechat" />
            <el-option label="银行卡" value="bank_card" />
            <el-option label="余额" value="balance" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="金额范围">
          <el-input-number 
            v-model="listQuery.min_amount" 
            :min="0" 
            :precision="2" 
            placeholder="最小金额"
            style="width: 120px"
          />
          <span style="margin: 0 10px">-</span>
          <el-input-number 
            v-model="listQuery.max_amount" 
            :min="0" 
            :precision="2" 
            placeholder="最大金额"
            style="width: 120px"
          />
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="listQuery.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card style="margin-bottom: 20px">
      <el-row>
        <el-col :span="12">
          <el-button 
            type="success" 
            icon="Check" 
            :disabled="!multipleSelection.length"
            @click="batchApprove"
          >
            批量批准
          </el-button>
          <el-button 
            type="danger" 
            icon="Close" 
            :disabled="!multipleSelection.length"
            @click="batchReject"
          >
            批量拒绝
          </el-button>
          <el-button 
            type="warning" 
            icon="Download" 
            @click="handleExportWithdraws"
            :loading="exportLoading"
          >
            导出数据
          </el-button>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button 
            type="primary" 
            icon="Refresh" 
            @click="refreshStats"
          >
            刷新统计
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" align="center" width="80" />
        
        <el-table-column label="用户信息" width="180" align="center">
          <template #default="{row}">
            <div class="user-info">
              <div class="user-avatar">
                <img :src="row.user?.avatar || '/default-avatar.png'" alt="">
              </div>
              <div class="user-details">
                <div class="user-name">{{ row.user?.name || '未知用户' }}</div>
                <div class="user-id">ID: {{ row.user?.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="提现金额" width="150" align="center">
          <template #default="{row}">
            <div class="amount-info">
              <div class="withdraw-amount">¥{{ formatNumber(row.amount) }}</div>
              <div class="fee-amount">手续费: ¥{{ formatNumber(row.fee || 0) }}</div>
              <div class="actual-amount">实际: ¥{{ formatNumber(row.amount - (row.fee || 0)) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="提现方式" width="120" align="center">
          <template #default="{row}">
            <div class="method-info">
              <el-tag :type="getMethodTagType(row.method)">
                {{ getWithdrawMethod(row.method) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="账户信息" width="200" align="center">
          <template #default="{row}">
            <div class="account-info">
              <div class="account-name">{{ row.account_name }}</div>
              <div class="account-number">{{ maskAccountNumber(row.account_number) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100" align="center">
          <template #default="{row}">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="时间信息" width="180" align="center">
          <template #default="{row}">
            <div class="time-info">
              <div class="apply-time">申请: {{ formatDateTime(row.created_at) }}</div>
              <div class="process-time" v-if="row.processed_at">
                处理: {{ formatDateTime(row.processed_at) }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{row}">
            <el-button type="text" size="small" @click="viewDetail(row)">
              详情
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="text"
              size="small"
              @click="handleApprove(row)"
            >
              批准
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="text"
              size="small"
              @click="handleReject(row)"
            >
              拒绝
            </el-button>
            <el-button
              v-if="row.status === 'approved'"
              type="text"
              size="small"
              @click="handleProcess(row)"
            >
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center">
        <el-pagination
          v-show="total > 0"
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[15, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="提现详情"
      width="700px"
    >
      <div v-if="currentRow" class="detail-container">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户">{{ currentRow.user?.name || '未知用户' }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ currentRow.user?.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRow.status)">{{ getStatusName(currentRow.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提现金额">¥{{ formatNumber(currentRow.amount) }}</el-descriptions-item>
          <el-descriptions-item label="手续费">¥{{ formatNumber(currentRow.fee || 0) }}</el-descriptions-item>
          <el-descriptions-item label="实际到账">¥{{ formatNumber(currentRow.amount - (currentRow.fee || 0)) }}</el-descriptions-item>
          <el-descriptions-item label="提现方式">{{ getWithdrawMethod(currentRow.method) }}</el-descriptions-item>
          <el-descriptions-item label="账户姓名">{{ currentRow.account_name }}</el-descriptions-item>
          <el-descriptions-item label="账户号码">{{ currentRow.account_number }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentRow.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentRow.processed_at ? formatDateTime(currentRow.processed_at) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="处理人">{{ currentRow.processed_by || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="currentRow.remark" style="margin-top: 20px;">
          <h4>申请备注</h4>
          <el-card>
            <p>{{ currentRow.remark }}</p>
          </el-card>
        </div>
        
        <div v-if="currentRow.admin_remark" style="margin-top: 20px;">
          <h4>管理员备注</h4>
          <el-card>
            <p>{{ currentRow.admin_remark }}</p>
          </el-card>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentRow && currentRow.status === 'pending'"
            type="success"
            @click="handleApprove(currentRow)"
          >
            批准
          </el-button>
          <el-button
            v-if="currentRow && currentRow.status === 'pending'"
            type="danger"
            @click="handleReject(currentRow)"
          >
            拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      :title="reviewType === 'approve' ? '批准提现' : '拒绝提现'"
      width="500px"
    >
      <div v-if="currentRow" style="margin-bottom: 20px;">
        <el-alert
          :title="`${reviewType === 'approve' ? '批准' : '拒绝'}提现申请`"
          :description="`用户: ${currentRow.user?.name} | 金额: ¥${formatNumber(currentRow.amount)}`"
          :type="reviewType === 'approve' ? 'success' : 'warning'"
          show-icon
          :closable="false"
        />
      </div>
      
      <el-form ref="reviewFormRef" :model="reviewForm" label-width="100px">
        <el-form-item label="管理员备注" prop="admin_remark">
          <el-input
            v-model="reviewForm.admin_remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button
            :type="reviewType === 'approve' ? 'success' : 'danger'"
            @click="confirmReview"
            :loading="reviewLoading"
          >
            {{ reviewType === 'approve' ? '批准' : '拒绝' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog
      v-model="batchReviewDialogVisible"
      :title="`批量${batchReviewType === 'approve' ? '批准' : '拒绝'}提现`"
      width="500px"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          :title="`即将${batchReviewType === 'approve' ? '批准' : '拒绝'}${batchReviewItems.length}条提现申请`"
          :description="`总金额: ¥${formatNumber(batchReviewItems.reduce((sum, item) => sum + item.amount, 0))}`"
          :type="batchReviewType === 'approve' ? 'success' : 'warning'"
          show-icon
          :closable="false"
        />
      </div>
      
      <el-form ref="batchReviewFormRef" :model="batchReviewForm" label-width="100px">
        <el-form-item label="管理员备注" prop="admin_remark">
          <el-input
            v-model="batchReviewForm.admin_remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchReviewDialogVisible = false">取消</el-button>
          <el-button
            :type="batchReviewType === 'approve' ? 'success' : 'danger'"
            @click="confirmBatchReview"
            :loading="batchReviewLoading"
          >
            确认{{ batchReviewType === 'approve' ? '批准' : '拒绝' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { exportWithdraws } from '@/api/export'
import { formatDateTime, formatNumber } from '@/utils/format'
import { Search, Refresh, Check, Close, Download } from '@element-plus/icons-vue'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const exportLoading = ref(false)
const reviewLoading = ref(false)
const batchReviewLoading = ref(false)
const multipleSelection = ref([])

const dialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const batchReviewDialogVisible = ref(false)
const currentRow = ref(null)
const reviewType = ref('approve')
const batchReviewType = ref('approve')
const batchReviewItems = ref([])

const listQuery = reactive({
  page: 1,
  limit: 15,
  user_name: '',
  status: '',
  method: '',
  min_amount: null,
  max_amount: null,
  date_range: []
})

const reviewForm = reactive({
  admin_remark: ''
})

const batchReviewForm = reactive({
  admin_remark: ''
})

const withdrawStats = ref({
  pending_count: 0,
  pending_amount: 0,
  approved_count: 0,
  approved_amount: 0,
  completed_count: 0,
  completed_amount: 0,
  total_count: 0,
  total_amount: 0
})

// 生命周期
onMounted(() => {
  getList()
  getStats()
})

// 方法
const getList = async () => {
  listLoading.value = true
  try {
    const params = { ...listQuery }
    if (params.date_range && params.date_range.length === 2) {
      params.start_date = params.date_range[0]
      params.end_date = params.date_range[1]
      delete params.date_range
    }
    
    const response = await request({
      url: '/withdraw-records',
      method: 'get',
      params
    })
    
    list.value = response.data.data
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    listLoading.value = false
  }
}

const getStats = async () => {
  try {
    const response = await request({
      url: '/withdraw-records/stats',
      method: 'get'
    })
    withdrawStats.value = response.data
  } catch (error) {
    console.error('获取统计数据失败')
  }
}

const handleQuery = () => {
  listQuery.page = 1
  getList()
}

const resetQuery = () => {
  Object.assign(listQuery, {
    page: 1,
    limit: 15,
    user_name: '',
    status: '',
    method: '',
    min_amount: null,
    max_amount: null,
    date_range: []
  })
  getList()
}

const refreshStats = () => {
  getStats()
  ElMessage.success('统计数据已刷新')
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const batchApprove = () => {
  const pendingItems = multipleSelection.value.filter(item => item.status === 'pending')
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待审核的提现申请')
    return
  }
  
  batchReviewType.value = 'approve'
  batchReviewItems.value = pendingItems
  batchReviewForm.admin_remark = ''
  batchReviewDialogVisible.value = true
}

const batchReject = () => {
  const pendingItems = multipleSelection.value.filter(item => item.status === 'pending')
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待审核的提现申请')
    return
  }
  
  batchReviewType.value = 'reject'
  batchReviewItems.value = pendingItems
  batchReviewForm.admin_remark = ''
  batchReviewDialogVisible.value = true
}

const confirmBatchReview = async () => {
  batchReviewLoading.value = true
  try {
    const action = batchReviewType.value === 'approve' ? 'batch-approve' : 'batch-reject'
    const actionText = batchReviewType.value === 'approve' ? '批准' : '拒绝'
    
    await request({
      url: `/withdraw-records/${action}`,
      method: 'post',
      data: {
        ids: batchReviewItems.value.map(item => item.id),
        admin_remark: batchReviewForm.admin_remark
      }
    })
    
    ElMessage.success(`批量${actionText}成功`)
    batchReviewDialogVisible.value = false
    getList()
    getStats()
  } catch (error) {
    ElMessage.error(`批量${batchReviewType.value === 'approve' ? '批准' : '拒绝'}失败`)
  } finally {
    batchReviewLoading.value = false
  }
}

const handleExportWithdraws = async () => {
  exportLoading.value = true
  try {
    const params = {
      ...listQuery,
      format: 'excel',
      fields: [
        'id', 'user_name', 'amount', 'fee', 'method', 
        'account_name', 'account_number', 'status', 'created_at'
      ]
    }
    
    const response = await exportWithdraws(params)
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `提现记录_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewDetail = (row) => {
  currentRow.value = row
  dialogVisible.value = true
}

const handleApprove = (row) => {
  currentRow.value = row
  reviewType.value = 'approve'
  reviewForm.admin_remark = ''
  reviewDialogVisible.value = true
  dialogVisible.value = false
}

const handleReject = (row) => {
  currentRow.value = row
  reviewType.value = 'reject'
  reviewForm.admin_remark = ''
  reviewDialogVisible.value = true
  dialogVisible.value = false
}

const handleProcess = (row) => {
  ElMessageBox.confirm(`确定要处理这笔提现申请吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await request({
        url: `/withdraw-records/${row.id}/process`,
        method: 'post'
      })
      ElMessage.success('处理成功')
      getList()
      getStats()
    } catch (error) {
      ElMessage.error('处理失败')
    }
  })
}

const confirmReview = async () => {
  reviewLoading.value = true
  try {
    const action = reviewType.value === 'approve' ? 'approve' : 'reject'
    const actionText = reviewType.value === 'approve' ? '批准' : '拒绝'
    
    await request({
      url: `/withdraw-records/${currentRow.value.id}/${action}`,
      method: 'post',
      data: {
        admin_remark: reviewForm.admin_remark
      }
    })
    
    ElMessage.success(`${actionText}成功`)
    reviewDialogVisible.value = false
    getList()
    getStats()
  } catch (error) {
    ElMessage.error(`${reviewType.value === 'approve' ? '批准' : '拒绝'}失败`)
  } finally {
    reviewLoading.value = false
  }
}

// 工具函数
const getStatusName = (status) => {
  const statusMap = {
    pending: '待审核',
    approved: '已批准',
    rejected: '已拒绝',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || '未知'
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    processing: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getWithdrawMethod = (method) => {
  const methodMap = {
    alipay: '支付宝',
    wechat: '微信',
    bank_card: '银行卡',
    balance: '余额'
  }
  return methodMap[method] || '未知'
}

const getMethodTagType = (method) => {
  const typeMap = {
    alipay: 'primary',
    wechat: 'success',
    bank_card: 'warning',
    balance: 'info'
  }
  return typeMap[method] || 'info'
}

const maskAccountNumber = (number) => {
  if (!number) return '-'
  if (number.length <= 4) return number
  return number.replace(/(\d{4})\d*(\d{4})/, '$1****$2')
}
</script>

<style lang="scss" scoped>
.stats-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  .stats-content {
    display: flex;
    align-items: center;
    padding: 20px;
    
    .stats-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 20px;
      color: white;
      
      &.pending-icon { background: #e6a23c; }
      &.approved-icon { background: #409eff; }
      &.completed-icon { background: #67c23a; }
      &.total-icon { background: #909399; }
    }
    
    .stats-data {
      flex: 1;
      
      .stats-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 2px;
      }
      
      .stats-amount {
        font-size: 12px;
        color: #67c23a;
        font-weight: 500;
      }
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .user-details {
    flex: 1;
    text-align: left;
    
    .user-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 2px;
    }
    
    .user-id {
      font-size: 12px;
      color: #909399;
    }
  }
}

.amount-info {
  text-align: left;
  
  .withdraw-amount {
    font-size: 16px;
    font-weight: bold;
    color: #f56c6c;
    margin-bottom: 3px;
  }
  
  .fee-amount {
    font-size: 12px;
    color: #909399;
    margin-bottom: 2px;
  }
  
  .actual-amount {
    font-size: 12px;
    color: #67c23a;
    font-weight: 500;
  }
}

.account-info {
  text-align: left;
  
  .account-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }
  
  .account-number {
    font-size: 12px;
    color: #909399;
  }
}

.time-info {
  text-align: left;
  
  .apply-time {
    font-size: 12px;
    color: #303133;
    margin-bottom: 2px;
  }
  
  .process-time {
    font-size: 12px;
    color: #909399;
  }
}

.detail-container {
  padding: 20px;
}
</style> 