<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <!-- 设置菜单 -->
        <el-card>
          <template #header>
            <h3>系统设置</h3>
          </template>
          <el-menu
            v-model="activeMenu"
            class="settings-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="basic">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </el-menu-item>

            <el-menu-item index="security">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </el-menu-item>
            <el-sub-menu index="integrations">
               <template #title>
                <el-icon><Connection /></el-icon>
                <span>集成设置</span>
              </template>
              <el-menu-item index="payment">支付快速配置</el-menu-item>
              <el-menu-item index="notification">通知设置</el-menu-item>
              <el-menu-item index="storage">存储设置</el-menu-item>
            </el-sub-menu>
             <el-sub-menu index="modules">
               <template #title>
                <el-icon><Grid /></el-icon>
                <span>模块设置</span>
              </template>
              <el-menu-item index="community_settings">社群设置</el-menu-item>
            </el-sub-menu>
            <el-menu-item index="audit_logs">
              <el-icon><Document /></el-icon>
              <span>系统日志</span>
            </el-menu-item>
            <el-menu-item index="backup">
              <el-icon><DataLine /></el-icon>
              <span>备份与恢复</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <!-- 基础设置 -->
        <el-card v-show="activeMenu === 'basic'">
          <template #header>
            <h3>基础设置</h3>
          </template>
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="120px"
          >
            <el-form-item label="网站名称" prop="site_name">
              <el-input v-model="basicForm.site_name" placeholder="请输入网站名称" />
            </el-form-item>
            <el-form-item label="网站描述" prop="site_description">
              <el-input
                v-model="basicForm.site_description"
                type="textarea"
                :rows="3"
                placeholder="请输入网站描述"
              />
            </el-form-item>
            <el-form-item label="网站关键词" prop="site_keywords">
              <el-input v-model="basicForm.site_keywords" placeholder="请输入网站关键词，用逗号分隔" />
            </el-form-item>
            <el-form-item label="网站Logo" prop="site_logo">
              <ImageUpload v-model="basicForm.site_logo" :limit="1" />
            </el-form-item>
            <el-form-item label="网站图标" prop="site_favicon">
              <ImageUpload v-model="basicForm.site_favicon" :limit="1" />
            </el-form-item>
            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input v-model="basicForm.contact_email" placeholder="请输入联系邮箱" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contact_phone">
              <el-input v-model="basicForm.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="备案号" prop="icp_number">
              <el-input v-model="basicForm.icp_number" placeholder="请输入备案号" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
              <el-button @click="resetBasicForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 支付快速配置 -->
        <el-card v-show="activeMenu === 'payment'">
          <template #header>
            <h3>支付快速配置</h3>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">快速启用和配置基本支付功能，详细配置请前往 <el-link type="primary" href="/payment" target="_blank">支付管理</el-link></p>
          </template>
          <el-form
            ref="paymentFormRef"
            :model="paymentForm"
            label-width="120px"
          >
            <el-divider content-position="left">微信支付</el-divider>
            <el-form-item label="启用微信支付">
              <el-switch v-model="paymentForm.wechat_enabled" />
            </el-form-item>
            <template v-if="paymentForm.wechat_enabled">
              <el-form-item label="应用ID" prop="wechat_app_id">
                <el-input v-model="paymentForm.wechat_app_id" placeholder="请输入微信应用ID" />
              </el-form-item>
              <el-form-item label="商户号" prop="wechat_mch_id">
                <el-input v-model="paymentForm.wechat_mch_id" placeholder="请输入微信商户号" />
              </el-form-item>
              <el-form-item label="API密钥" prop="wechat_key">
                <el-input
                  v-model="paymentForm.wechat_key"
                  type="password"
                  placeholder="请输入微信API密钥"
                  show-password
                />
              </el-form-item>
            </template>

            <el-divider content-position="left">支付宝支付</el-divider>
            <el-form-item label="启用支付宝">
              <el-switch v-model="paymentForm.alipay_enabled" />
            </el-form-item>
            <template v-if="paymentForm.alipay_enabled">
              <el-form-item label="应用ID" prop="alipay_app_id">
                <el-input v-model="paymentForm.alipay_app_id" placeholder="请输入支付宝应用ID" />
              </el-form-item>
              <el-form-item label="应用私钥" prop="alipay_private_key">
                <el-input
                  v-model="paymentForm.alipay_private_key"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝应用私钥"
                />
              </el-form-item>
              <el-form-item label="支付宝公钥" prop="alipay_public_key">
                <el-input
                  v-model="paymentForm.alipay_public_key"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入支付宝公钥"
                />
              </el-form-item>
            </template>

            <el-form-item>
              <el-button type="primary" @click="savePaymentSettings">保存设置</el-button>
              <el-button @click="testPayment">测试支付</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 通知设置 -->
        <el-card v-show="activeMenu === 'notification'">
          <template #header>
            <h3>通知设置</h3>
          </template>
          <el-form
            ref="notificationFormRef"
            :model="notificationForm"
            label-width="120px"
          >
            <el-form-item label="邮件通知">
              <el-switch v-model="notificationForm.email_enabled" />
            </el-form-item>
            <el-form-item label="短信通知">
              <el-switch v-model="notificationForm.sms_enabled" />
            </el-form-item>
            <el-form-item label="微信通知">
              <el-switch v-model="notificationForm.wechat_enabled" />
            </el-form-item>
            <el-form-item label="系统通知">
              <el-switch v-model="notificationForm.system_enabled" />
            </el-form-item>
            
            <el-divider content-position="left">通知事件</el-divider>
            <el-form-item label="新用户注册">
              <el-checkbox-group v-model="notificationForm.user_register">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
                <el-checkbox label="system">系统</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="新订单">
              <el-checkbox-group v-model="notificationForm.new_order">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
                <el-checkbox label="system">系统</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="提现申请">
              <el-checkbox-group v-model="notificationForm.withdrawal_request">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
                <el-checkbox label="system">系统</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 安全设置 -->
        <el-card v-show="activeMenu === 'security'">
          <template #header>
            <h3>安全设置</h3>
          </template>
          <el-form
            ref="securityFormRef"
            :model="securityForm"
            label-width="120px"
          >
            <el-form-item label="登录验证码">
              <el-switch v-model="securityForm.login_captcha" />
            </el-form-item>
            <el-form-item label="注册验证码">
              <el-switch v-model="securityForm.register_captcha" />
            </el-form-item>
            <el-form-item label="密码强度检查">
              <el-switch v-model="securityForm.password_strength" />
            </el-form-item>
            <el-form-item label="登录失败限制">
              <el-input-number
                v-model="securityForm.login_attempts"
                :min="3"
                :max="10"
                placeholder="登录失败次数限制"
              />
              <span style="margin-left: 10px;">次后锁定账户</span>
            </el-form-item>
            <el-form-item label="会话超时时间">
              <el-input-number
                v-model="securityForm.session_timeout"
                :min="30"
                :max="1440"
                placeholder="会话超时时间"
              />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
            <el-form-item label="IP白名单">
              <el-input
                v-model="securityForm.ip_whitelist"
                type="textarea"
                :rows="3"
                placeholder="请输入IP白名单，每行一个IP或IP段"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveSecuritySettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 新增的功能面板 -->
        <AuditLogs v-if="activeMenu === 'audit_logs'" />
        <BackupSettings v-if="activeMenu === 'backup'" />
        <CommunitySettings v-if="activeMenu === 'community_settings'" />
        
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, CreditCard, Bell, Lock, Message, FolderOpened, User, Connection, Grid, Document, DataLine } from '@element-plus/icons-vue'
import ImageUpload from '@/components/Upload/ImageUpload.vue'
import { getSystemSettings, updateSystemSettings, testPaymentConfig } from '@/api/system'

// 导入新的组件
import AuditLogs from './components/AuditLogs.vue'
import BackupSettings from './components/BackupSettings.vue'
import CommunitySettings from './components/CommunitySettings.vue'

// 响应式数据
const activeMenu = ref('basic')
const basicFormRef = ref()
const paymentFormRef = ref()
const notificationFormRef = ref()
const securityFormRef = ref()

// 基础设置表单
const basicForm = ref({
  site_name: '',
  site_description: '',
  site_keywords: '',
  site_logo: '',
  site_favicon: '',
  contact_email: '',
  contact_phone: '',
  icp_number: ''
})

// 支付设置表单
const paymentForm = ref({
  wechat_enabled: false,
  wechat_app_id: '',
  wechat_mch_id: '',
  wechat_key: '',
  alipay_enabled: false,
  alipay_app_id: '',
  alipay_private_key: '',
  alipay_public_key: ''
})

// 通知设置表单
const notificationForm = ref({
  email_enabled: true,
  sms_enabled: false,
  wechat_enabled: false,
  system_enabled: true,
  user_register: ['system'],
  new_order: ['email', 'system'],
  withdrawal_request: ['email', 'system']
})

// 安全设置表单
const securityForm = ref({
  login_captcha: true,
  register_captcha: true,
  password_strength: true,
  login_attempts: 5,
  session_timeout: 120,
  ip_whitelist: ''
})

// 表单验证规则
const basicRules = {
  site_name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 获取系统设置
const fetchSettings = async () => {
  try {
    const { data } = await getSystemSettings()
    basicForm.value = { ...basicForm.value, ...data.basic }
    paymentForm.value = { ...paymentForm.value, ...data.payment }
    notificationForm.value = { ...notificationForm.value, ...data.notification }
    securityForm.value = { ...securityForm.value, ...data.security }
  } catch (error) {
    console.error('获取系统设置失败:', error)
    ElMessage.error('获取系统设置失败')
  }
}

// 菜单选择
const handleMenuSelect = (key) => {
  activeMenu.value = key
}

// 保存基础设置
const saveBasicSettings = async () => {
  try {
    await basicFormRef.value.validate()
    await updateSystemSettings('basic', basicForm.value)
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    console.error('保存基础设置失败:', error)
    ElMessage.error('保存基础设置失败')
  }
}

// 重置基础表单
const resetBasicForm = () => {
  basicFormRef.value.resetFields()
}

// 保存支付设置
const savePaymentSettings = async () => {
  try {
    await updateSystemSettings('payment', paymentForm.value)
    ElMessage.success('支付设置保存成功')
  } catch (error) {
    console.error('保存支付设置失败:', error)
    ElMessage.error('保存支付设置失败')
  }
}

// 测试支付配置
const testPayment = async () => {
  try {
    await testPaymentConfig(paymentForm.value)
    ElMessage.success('支付配置测试通过')
  } catch (error) {
    console.error('支付配置测试失败:', error)
    ElMessage.error('支付配置测试失败')
  }
}

// 保存通知设置
const saveNotificationSettings = async () => {
  try {
    await updateSystemSettings('notification', notificationForm.value)
    ElMessage.success('通知设置保存成功')
  } catch (error) {
    console.error('保存通知设置失败:', error)
    ElMessage.error('保存通知设置失败')
  }
}

// 保存安全设置
const saveSecuritySettings = async () => {
  try {
    await updateSystemSettings('security', securityForm.value)
    ElMessage.success('安全设置保存成功')
  } catch (error) {
    console.error('保存安全设置失败:', error)
    ElMessage.error('保存安全设置失败')
  }
}

// 初始化
onMounted(() => {
  fetchSettings()
})
</script>

<style lang="scss" scoped>
.settings-menu {
  border: none;
  
  .el-menu-item {
    border-radius: 8px;
    margin-bottom: 4px;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.is-active {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }
}

.el-divider {
  margin: 24px 0;
}
</style>