<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建系统监控相关数据表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 系统健康检查记录表
        if (!Schema::hasTable('system_health_checks')) {
            Schema::create('system_health_checks', function (Blueprint $table) {
                $table->id();
                $table->timestamp('check_time');
                $table->string('overall_status', 20)->default('unknown'); // pass, warning, fail
                $table->integer('health_score')->default(0); // 0-100
                $table->json('check_results')->nullable(); // 详细检查结果
                $table->integer('issues_count')->default(0);
                $table->integer('duration')->default(0); // 检查耗时(ms)
                $table->text('summary')->nullable();
                $table->timestamps();
                
                $table->index(['check_time', 'overall_status']);
                $table->index('health_score');
            });
        }

        // 系统性能指标表
        if (!Schema::hasTable('system_performance_metrics')) {
            Schema::create('system_performance_metrics', function (Blueprint $table) {
                $table->id();
                $table->timestamp('recorded_at');
                $table->decimal('cpu_usage', 5, 2)->default(0); // CPU使用率
                $table->decimal('memory_usage', 5, 2)->default(0); // 内存使用率
                $table->decimal('disk_usage', 5, 2)->default(0); // 磁盘使用率
                $table->integer('db_response_time')->default(0); // 数据库响应时间(ms)
                $table->integer('cache_response_time')->default(0); // 缓存响应时间(ms)
                $table->integer('active_connections')->default(0); // 活跃连接数
                $table->bigInteger('memory_peak')->default(0); // 内存峰值(bytes)
                $table->json('additional_metrics')->nullable(); // 额外指标
                $table->timestamps();
                
                $table->index('recorded_at');
                $table->index(['recorded_at', 'cpu_usage']);
                $table->index(['recorded_at', 'memory_usage']);
            });
        }

        // 系统告警记录表
        if (!Schema::hasTable('system_alerts')) {
            Schema::create('system_alerts', function (Blueprint $table) {
                $table->id();
                $table->string('alert_type', 50); // health, performance, security, etc.
                $table->string('severity', 20); // low, medium, high, critical
                $table->string('title');
                $table->text('message');
                $table->json('context')->nullable(); // 告警上下文数据
                $table->timestamp('triggered_at');
                $table->timestamp('resolved_at')->nullable();
                $table->string('status', 20)->default('active'); // active, resolved, ignored
                $table->text('resolution_notes')->nullable();
                $table->timestamps();
                
                $table->index(['alert_type', 'severity']);
                $table->index(['status', 'triggered_at']);
                $table->index('triggered_at');
            });
        }

        // 系统组件状态表
        if (!Schema::hasTable('system_component_status')) {
            Schema::create('system_component_status', function (Blueprint $table) {
                $table->id();
                $table->string('component_name', 100); // database, cache, storage, etc.
                $table->string('status', 20); // healthy, warning, critical, unknown
                $table->text('status_message')->nullable();
                $table->json('metrics')->nullable(); // 组件相关指标
                $table->timestamp('last_check');
                $table->timestamp('last_healthy')->nullable();
                $table->integer('consecutive_failures')->default(0);
                $table->timestamps();
                
                $table->unique('component_name');
                $table->index(['status', 'last_check']);
            });
        }

        // 部署记录表
        if (!Schema::hasTable('deployment_records')) {
            Schema::create('deployment_records', function (Blueprint $table) {
                $table->id();
                $table->string('deployment_id', 100)->unique();
                $table->string('version', 50)->nullable();
                $table->string('environment', 50)->default('production');
                $table->string('status', 20); // pending, running, completed, failed
                $table->timestamp('started_at');
                $table->timestamp('completed_at')->nullable();
                $table->integer('duration')->nullable(); // 部署耗时(秒)
                $table->json('deployment_steps')->nullable(); // 部署步骤记录
                $table->json('health_check_results')->nullable(); // 部署后健康检查结果
                $table->text('notes')->nullable();
                $table->string('deployed_by', 100)->nullable();
                $table->timestamps();
                
                $table->index(['status', 'started_at']);
                $table->index('started_at');
            });
        }

        // 系统配置变更记录表
        if (!Schema::hasTable('system_config_changes')) {
            Schema::create('system_config_changes', function (Blueprint $table) {
                $table->id();
                $table->string('config_key');
                $table->text('old_value')->nullable();
                $table->text('new_value')->nullable();
                $table->string('changed_by', 100)->nullable();
                $table->string('change_reason')->nullable();
                $table->timestamp('changed_at');
                $table->timestamps();
                
                $table->index(['config_key', 'changed_at']);
                $table->index('changed_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_config_changes');
        Schema::dropIfExists('deployment_records');
        Schema::dropIfExists('system_component_status');
        Schema::dropIfExists('system_alerts');
        Schema::dropIfExists('system_performance_metrics');
        Schema::dropIfExists('system_health_checks');
    }
};
