<template>
  <div class="route-test">
    <h1>路由测试页面</h1>
    <p>当前路由: {{ $route.path }}</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <div class="test-links">
      <h3>测试链接:</h3>
      <ul>
        <li><router-link to="/admin/dashboard">仪表板</router-link></li>
        <li><router-link to="/404">404页面</router-link></li>
        <li><router-link to="/admin/nonexistent">不存在的admin页面</router-link></li>
        <li><router-link to="/completely-nonexistent">完全不存在的页面</router-link></li>
      </ul>
    </div>
    
    <div class="actions">
      <el-button type="primary" @click="testRoute">测试路由跳转</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSafeTimer } from '@/utils/componentCleanup'

const router = useRouter()
const currentTime = ref(new Date().toLocaleString())
const safeTimer = useSafeTimer()

const testRoute = () => {
  router.push('/admin/nonexistent-test-page')
}

const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  safeTimer.setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})
</script>

<style scoped>
.route-test {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.test-links {
  margin: 20px 0;
}

.test-links ul {
  list-style: none;
  padding: 0;
}

.test-links li {
  margin: 10px 0;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.test-links a {
  color: #409eff;
  text-decoration: none;
}

.test-links a:hover {
  text-decoration: underline;
}

.actions {
  margin-top: 20px;
}
</style>
