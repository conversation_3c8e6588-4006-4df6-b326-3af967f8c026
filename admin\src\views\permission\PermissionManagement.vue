<template>
  <PageLayout
    title="权限管理"
    subtitle="管理系统权限和角色分配"
    :loading="loading"
  >
      <template #actions>
        <el-button class="modern-btn secondary" @click="handleRefresh">
          <i class="el-icon-refresh"></i>
          刷新数据
        </el-button>
        <el-button class="modern-btn primary" @click="handleAdd">
          <i class="el-icon-plus"></i>
          新增
        </el-button>
      </template>

      <!-- 主要内容区域 -->
      <div class="content-wrapper">
        <el-card class="modern-card">
          <div class="card-header">
            <h3>权限管理</h3>
            <p class="text-muted">管理系统权限和角色分配</p>
          </div>
          
          <div class="card-body">
            <!-- 数据表格或其他内容 -->
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              class="modern-table"
            >
              <el-table-column
                prop="id"
                label="ID"
                width="80"
              />
              <el-table-column
                prop="name"
                label="权限名称"
                min-width="150"
              />
              <el-table-column
                prop="description"
                label="权限描述"
                min-width="200"
                show-overflow-tooltip
              />
              <el-table-column
                prop="module"
                label="所属模块"
                width="120"
              >
                <template #default="{ row }">
                  <el-tag size="small" :type="getModuleTagType(row.module)">
                    {{ getModuleName(row.module) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="status"
                label="状态"
                width="100"
              >
                <template #default="{ row }">
                  <el-tag
                    :type="row.status === 'active' ? 'success' : 'info'"
                    size="small"
                  >
                    {{ row.status === 'active' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="created_at"
                label="创建时间"
                width="180"
              />
              <el-table-column
                label="操作"
                width="200"
                fixed="right"
              >
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="pagination.current"
                v-model:page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-card>
      </div>
    </PageLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageLayout from '@/components/layout/PageLayout.vue'

// 页面状态
const loading = ref(false)
const tableData = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 页面方法
const handleRefresh = () => {
  loadData()
}

const handleAdd = () => {
  ElMessage.info('新增功能待实现')
}

const handleEdit = (row) => {
  ElMessage.info(`编辑功能待实现: ${row.name}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    loadData()
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

// 辅助函数
const getModuleName = (module) => {
  const moduleMap = {
    user: '用户管理',
    content: '内容管理',
    finance: '财务管理',
    system: '系统设置'
  }
  return moduleMap[module] || module
}

const getModuleTagType = (module) => {
  const typeMap = {
    user: 'primary',
    content: 'success',
    finance: 'warning',
    system: 'danger'
  }
  return typeMap[module] || 'info'
}

const loadData = async () => {
  loading.value = true

  try {
    // TODO: 实现实际的数据加载逻辑
    // 减少模拟延迟，提升用户体验
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟权限配置数据
    tableData.value = [
      {
        id: 1,
        name: '用户管理权限',
        status: 'active',
        created_at: '2024-01-01 12:00:00',
        description: '管理用户信息、角色分配等功能',
        module: 'user'
      },
      {
        id: 2,
        name: '内容管理权限',
        status: 'active',
        created_at: '2024-01-02 12:00:00',
        description: '管理内容发布、审核等功能',
        module: 'content'
      },
      {
        id: 3,
        name: '财务管理权限',
        status: 'inactive',
        created_at: '2024-01-03 12:00:00',
        description: '管理财务数据、支付配置等功能',
        module: 'finance'
      },
      {
        id: 4,
        name: '系统设置权限',
        status: 'active',
        created_at: '2024-01-04 12:00:00',
        description: '管理系统配置、参数设置等功能',
        module: 'system'
      }
    ]

    pagination.total = 4
    console.log('✅ 权限配置数据加载完成')
  } catch (error) {
    console.error('❌ 加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.content-wrapper {
  margin-top: 20px;
}

.card-header {
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.modern-table {
  --el-table-border-color: var(--border-light);
  --el-table-bg-color: var(--surface-primary);
}
</style>