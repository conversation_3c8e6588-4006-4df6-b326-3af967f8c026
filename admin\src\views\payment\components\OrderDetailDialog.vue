<template>
  <el-dialog
    v-model="visible"
    title="订单详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="order-detail" v-if="order">
      <!-- 订单基本信息 -->
      <div class="detail-section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ order.order_no }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ order.user_name }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ order.product_name }}</el-descriptions-item>
          <el-descriptions-item label="商品描述">{{ order.product_desc }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ order.amount?.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="优惠金额">¥{{ order.discount_amount?.toFixed(2) || '0.00' }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPaymentMethodText(order.payment_method) }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(order.status)">{{ getStatusText(order.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(order.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ order.paid_at ? formatDate(order.paid_at) : '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <el-button v-if="order.status === 'paid'" type="warning" @click="handleRefund">
          申请退款
        </el-button>
        <el-button v-if="order.status === 'pending'" type="danger" @click="handleCancel">
          取消订单
        </el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  order: Object
})

const emit = defineEmits(['update:modelValue', 'action'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}

const handleRefund = () => {
  emit('action', 'refund', props.order)
  handleClose()
}

const handleCancel = () => {
  emit('action', 'cancel', props.order)
  handleClose()
}

// 工具函数
const getPaymentMethodText = (method) => {
  const texts = {
    alipay: '支付宝',
    wechat: '微信支付',
    easypay: '易支付',
    bank: '银行卡'
  }
  return texts[method] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    failed: 'danger',
    refunded: 'info',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.order-detail {
  .detail-section {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .detail-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}
</style>