<template>
  <div class="file-uploader">
    <!-- 拖拽上传区域 -->
    <div 
      class="file-upload"
      :class="{ 'is-dragover': isDragover, 'is-disabled': disabled }"
      @dragover.prevent="handleDragover"
      @dragleave.prevent="handleDragleave"
      @drop.prevent="handleDrop"
      @click="triggerFileInput"
    >
      <div class="file-upload-icon">
        <i :class="uploadIcon"></i>
      </div>
      <div class="file-upload-title">{{ uploadTitle }}</div>
      <div class="file-upload-hint">{{ uploadHint }}</div>
      
      <!-- 隐藏的文件输入 -->
      <input 
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="disabled"
        class="hidden-input"
        @change="handleFileChange"
      />
    </div>
    
    <!-- 文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div 
        v-for="(file, index) in fileList" 
        :key="file.uid || index"
        class="file-item"
        :class="{ 'is-uploading': file.status === 'uploading', 'is-error': file.status === 'error' }"
      >
        <!-- 文件图标 -->
        <div class="file-item-icon">
          <i :class="getFileIcon(file)"></i>
        </div>
        
        <!-- 文件信息 -->
        <div class="file-item-info">
          <div class="file-item-name">{{ file.name }}</div>
          <div class="file-item-size">{{ formatFileSize(file.size) }}</div>
          
          <!-- 上传进度 -->
          <el-progress 
            v-if="file.status === 'uploading'"
            :percentage="file.percentage || 0"
            :stroke-width="4"
            :show-text="false"
          />
          
          <!-- 错误信息 -->
          <div class="file-item-error" v-if="file.status === 'error'">
            {{ file.error || '上传失败' }}
          </div>
        </div>
        
        <!-- 文件操作 -->
        <div class="file-item-actions">
          <!-- 预览按钮 -->
          <el-button 
            v-if="file.status === 'success' && isPreviewable(file)"
            type="primary"
            size="small"
            circle
            @click.stop="previewFile(file)"
          >
            <i class="el-icon-view"></i>
          </el-button>
          
          <!-- 重试按钮 -->
          <el-button 
            v-if="file.status === 'error'"
            type="warning"
            size="small"
            circle
            @click.stop="retryUpload(file)"
          >
            <i class="el-icon-refresh"></i>
          </el-button>
          
          <!-- 删除按钮 -->
          <el-button 
            type="danger"
            size="small"
            circle
            @click.stop="removeFile(file)"
          >
            <i class="el-icon-delete"></i>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 图片预览 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="800px"
      :append-to-body="true"
    >
      <img v-if="previewUrl" :src="previewUrl" class="preview-image" />
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'FileUploader',
  props: {
    // 文件列表
    modelValue: {
      type: Array,
      default: () => []
    },
    
    // 上传地址
    action: {
      type: String,
      required: true
    },
    
    // 接受的文件类型
    accept: {
      type: String,
      default: ''
    },
    
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 最大文件大小（字节）
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    
    // 上传前钩子
    beforeUpload: {
      type: Function,
      default: null
    },
    
    // 自定义上传方法
    customUpload: {
      type: Function,
      default: null
    },
    
    // 上传参数
    data: {
      type: Object,
      default: () => ({})
    },
    
    // 上传头部
    headers: {
      type: Object,
      default: () => ({})
    },
    
    // 上传文件名
    name: {
      type: String,
      default: 'file'
    },
    
    // 上传图标
    uploadIcon: {
      type: String,
      default: 'el-icon-upload'
    },
    
    // 上传标题
    uploadTitle: {
      type: String,
      default: '点击或拖拽文件到此处上传'
    },
    
    // 上传提示
    uploadHint: {
      type: String,
      default: '支持单个或批量上传'
    },
    
    // 自动上传
    autoUpload: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['update:modelValue', 'change', 'success', 'error', 'remove', 'exceed'],
  
  setup(props, { emit }) {
    // 引用
    const fileInput = ref(null);
    
    // 状态
    const isDragover = ref(false);
    const previewVisible = ref(false);
    const previewUrl = ref('');
    
    // 计算属性
    const fileList = computed({
      get: () => props.modelValue,
      set: (val) => emit('update:modelValue', val)
    });
    
    // 触发文件选择
    const triggerFileInput = () => {
      if (!props.disabled) {
        fileInput.value.click();
      }
    };
    
    // 处理拖拽进入
    const handleDragover = () => {
      if (!props.disabled) {
        isDragover.value = true;
      }
    };
    
    // 处理拖拽离开
    const handleDragleave = () => {
      isDragover.value = false;
    };
    
    // 处理拖拽放置
    const handleDrop = (e) => {
      if (props.disabled) return;
      
      isDragover.value = false;
      const files = e.dataTransfer.files;
      handleFiles(files);
    };
    
    // 处理文件选择
    const handleFileChange = (e) => {
      const files = e.target.files;
      handleFiles(files);
      // 重置文件输入，以便可以选择相同的文件
      e.target.value = '';
    };
    
    // 处理文件
    const handleFiles = (files) => {
      if (!files || files.length === 0) return;
      
      // 如果不是多选，只取第一个文件
      const fileArray = props.multiple ? Array.from(files) : [files[0]];
      
      // 验证文件
      const validFiles = fileArray.filter(file => {
        // 验证文件大小
        if (props.maxSize && file.size > props.maxSize) {
          ElMessage.error(`文件 ${file.name} 超过最大限制 ${formatFileSize(props.maxSize)}`);
          return false;
        }
        
        // 验证文件类型
        if (props.accept && !isFileTypeValid(file, props.accept)) {
          ElMessage.error(`文件 ${file.name} 类型不符合要求`);
          return false;
        }
        
        return true;
      });
      
      if (validFiles.length === 0) return;
      
      // 添加到文件列表
      const newFiles = validFiles.map(file => ({
        uid: Date.now() + Math.random().toString(36).substr(2, 10),
        name: file.name,
        size: file.size,
        type: file.type,
        raw: file,
        status: 'ready',
        percentage: 0
      }));
      
      // 更新文件列表
      const updatedList = props.multiple ? [...fileList.value, ...newFiles] : [...newFiles];
      fileList.value = updatedList;
      
      // 触发变更事件
      emit('change', {
        files: newFiles,
        fileList: updatedList
      });
      
      // 自动上传
      if (props.autoUpload) {
        newFiles.forEach(file => {
          uploadFile(file);
        });
      }
    };
    
    // 上传文件
    const uploadFile = async (file) => {
      // 如果文件已经上传成功，不再上传
      if (file.status === 'success') return;
      
      // 更新文件状态
      updateFileStatus(file, 'uploading');
      
      // 执行上传前钩子
      if (props.beforeUpload) {
        try {
          const result = await props.beforeUpload(file.raw, fileList.value);
          if (result === false) {
            updateFileStatus(file, 'ready');
            return;
          }
        } catch (error) {
          updateFileStatus(file, 'error', error.message || '上传前检查失败');
          return;
        }
      }
      
      // 使用自定义上传方法
      if (props.customUpload) {
        props.customUpload(file.raw, {
          onProgress: (e) => {
            updateFileProgress(file, e.percent);
          },
          onSuccess: (res) => {
            handleUploadSuccess(file, res);
          },
          onError: (err) => {
            handleUploadError(file, err);
          }
        });
        return;
      }
      
      // 默认上传方法
      const formData = new FormData();
      formData.append(props.name, file.raw);
      
      // 添加额外参数
      Object.keys(props.data).forEach(key => {
        formData.append(key, props.data[key]);
      });
      
      // 创建请求
      const xhr = new XMLHttpRequest();
      xhr.open('POST', props.action, true);
      
      // 设置请求头
      Object.keys(props.headers).forEach(key => {
        xhr.setRequestHeader(key, props.headers[key]);
      });
      
      // 监听上传进度
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded * 100) / e.total);
          updateFileProgress(file, percent);
        }
      });
      
      // 监听请求完成
      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch (e) {
            response = xhr.responseText;
          }
          handleUploadSuccess(file, response);
        } else {
          handleUploadError(file, new Error(`上传失败: ${xhr.status}`));
        }
      };
      
      // 监听请求错误
      xhr.onerror = function() {
        handleUploadError(file, new Error('网络错误'));
      };
      
      // 发送请求
      xhr.send(formData);
    };
    
    // 更新文件状态
    const updateFileStatus = (file, status, error = null) => {
      const index = fileList.value.findIndex(f => f.uid === file.uid);
      if (index !== -1) {
        const updatedFile = { ...fileList.value[index], status };
        if (error) {
          updatedFile.error = error;
        }
        const updatedList = [...fileList.value];
        updatedList[index] = updatedFile;
        fileList.value = updatedList;
      }
    };
    
    // 更新文件进度
    const updateFileProgress = (file, percentage) => {
      const index = fileList.value.findIndex(f => f.uid === file.uid);
      if (index !== -1) {
        const updatedFile = { ...fileList.value[index], percentage };
        const updatedList = [...fileList.value];
        updatedList[index] = updatedFile;
        fileList.value = updatedList;
      }
    };
    
    // 处理上传成功
    const handleUploadSuccess = (file, response) => {
      const index = fileList.value.findIndex(f => f.uid === file.uid);
      if (index !== -1) {
        const updatedFile = { 
          ...fileList.value[index], 
          status: 'success',
          response,
          url: response.url || response.data?.url || null
        };
        const updatedList = [...fileList.value];
        updatedList[index] = updatedFile;
        fileList.value = updatedList;
        
        // 触发成功事件
        emit('success', {
          file: updatedFile,
          response
        });
      }
    };
    
    // 处理上传错误
    const handleUploadError = (file, error) => {
      const index = fileList.value.findIndex(f => f.uid === file.uid);
      if (index !== -1) {
        const updatedFile = { 
          ...fileList.value[index], 
          status: 'error',
          error: error.message || '上传失败'
        };
        const updatedList = [...fileList.value];
        updatedList[index] = updatedFile;
        fileList.value = updatedList;
        
        // 触发错误事件
        emit('error', {
          file: updatedFile,
          error
        });
      }
    };
    
    // 重试上传
    const retryUpload = (file) => {
      uploadFile(file);
    };
    
    // 移除文件
    const removeFile = (file) => {
      const index = fileList.value.findIndex(f => f.uid === file.uid);
      if (index !== -1) {
        const updatedList = [...fileList.value];
        const removedFile = updatedList.splice(index, 1)[0];
        fileList.value = updatedList;
        
        // 触发移除事件
        emit('remove', {
          file: removedFile,
          fileList: updatedList
        });
      }
    };
    
    // 预览文件
    const previewFile = (file) => {
      if (file.url) {
        previewUrl.value = file.url;
      } else if (file.raw && isImageFile(file)) {
        previewUrl.value = URL.createObjectURL(file.raw);
      } else {
        return;
      }
      
      previewVisible.value = true;
    };
    
    // 检查文件是否可预览
    const isPreviewable = (file) => {
      return isImageFile(file) || (file.url && file.url.match(/\.(jpe?g|png|gif|bmp|webp)$/i));
    };
    
    // 检查是否为图片文件
    const isImageFile = (file) => {
      return file.type && file.type.startsWith('image/');
    };
    
    // 验证文件类型
    const isFileTypeValid = (file, accept) => {
      if (!accept) return true;
      
      const acceptTypes = accept.split(',').map(type => type.trim());
      const fileType = file.type || '';
      const fileExtension = file.name.substring(file.name.lastIndexOf('.'));
      
      return acceptTypes.some(type => {
        if (type.startsWith('.')) {
          // 扩展名匹配
          return fileExtension.toLowerCase() === type.toLowerCase();
        } else if (type.includes('/*')) {
          // MIME类型通配符匹配
          const mimePrefix = type.split('/*')[0];
          return fileType.startsWith(mimePrefix + '/');
        } else {
          // 完整MIME类型匹配
          return fileType === type;
        }
      });
    };
    
    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B';
      
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    
    // 获取文件图标
    const getFileIcon = (file) => {
      const type = file.type || '';
      
      if (type.startsWith('image/')) {
        return 'el-icon-picture';
      } else if (type.startsWith('video/')) {
        return 'el-icon-video-camera';
      } else if (type.startsWith('audio/')) {
        return 'el-icon-headset';
      } else if (type.includes('pdf')) {
        return 'el-icon-document';
      } else if (type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
        return 'el-icon-document-copy';
      } else if (type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
        return 'el-icon-document-checked';
      } else if (type.includes('zip') || type.includes('rar') || type.includes('tar') || type.includes('gz')) {
        return 'el-icon-folder';
      } else {
        return 'el-icon-document';
      }
    };
    
    return {
      fileInput,
      fileList,
      isDragover,
      previewVisible,
      previewUrl,
      triggerFileInput,
      handleDragover,
      handleDragleave,
      handleDrop,
      handleFileChange,
      uploadFile,
      retryUpload,
      removeFile,
      previewFile,
      isPreviewable,
      formatFileSize,
      getFileIcon
    };
  }
});
</script>

<style scoped>
.file-uploader {
  width: 100%;
}

.file-upload {
  border: 2px dashed var(--admin-gray-300);
  border-radius: var(--admin-radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all var(--admin-transition);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.5);
}

.file-upload:hover {
  border-color: var(--admin-primary);
  background: rgba(255, 255, 255, 0.8);
}

.file-upload.is-dragover {
  border-color: var(--admin-primary);
  background: rgba(102, 126, 234, 0.05);
}

.file-upload.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  border-color: var(--admin-gray-300);
}

.file-upload-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: 1rem;
}

.file-upload-title {
  font-weight: 600;
  color: var(--admin-gray-700);
  margin-bottom: 0.5rem;
}

.file-upload-hint {
  font-size: 0.875rem;
  color: var(--admin-gray-500);
}

.hidden-input {
  display: none;
}

.file-list {
  margin-top: 1.5rem;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: var(--admin-radius-lg);
  background: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  transition: all var(--admin-transition);
  border: 1px solid var(--admin-gray-200);
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--admin-shadow-sm);
}

.file-item.is-uploading {
  border-color: var(--admin-primary-light);
  background: rgba(102, 126, 234, 0.05);
}

.file-item.is-error {
  border-color: var(--admin-error);
  background: rgba(239, 68, 68, 0.05);
}

.file-item-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--admin-radius);
  background: var(--admin-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--admin-gray-600);
  font-size: 1.25rem;
}

.file-item-info {
  flex: 1;
  min-width: 0;
}

.file-item-name {
  font-weight: 500;
  color: var(--admin-gray-800);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item-size {
  font-size: 0.75rem;
  color: var(--admin-gray-500);
}

.file-item-error {
  font-size: 0.75rem;
  color: var(--admin-error);
  margin-top: 0.25rem;
}

.file-item-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
  margin: 0 auto;
}
</style>