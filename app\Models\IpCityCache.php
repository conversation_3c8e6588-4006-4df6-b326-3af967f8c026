<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

/**
 * IP城市缓存模型
 * 缓存IP地址对应的城市信息，减少API调用
 */
class IpCityCache extends Model
{
    use HasFactory;

    protected $fillable = [
        'ip_address',
        'city',
        'province',
        'country',
        'isp',
        'data_source',
        'raw_data',
        'hit_count',
        'last_hit_at',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'last_hit_at' => 'datetime',
    ];

    // 数据源常量
    const SOURCE_BAIDU = 'baidu';
    const SOURCE_BAIDU_CLOUD = 'baidu_cloud';
    const SOURCE_TAOBAO = 'taobao';
    const SOURCE_IP_API = 'ip_api';
    const SOURCE_LOCAL = 'local';

    /**
     * 获取或创建IP城市缓存
     */
    public static function getOrCreate(string $ip, array $cityData): self
    {
        $cache = self::where('ip_address', $ip)->first();

        if ($cache) {
            // 更新命中次数和时间
            $cache->increment('hit_count');
            $cache->update(['last_hit_at' => now()]);
            return $cache;
        }

        // 创建新的缓存记录
        return self::create([
            'ip_address' => $ip,
            'city' => $cityData['city'] ?? '未知',
            'province' => $cityData['province'] ?? '',
            'country' => $cityData['country'] ?? '中国',
            'isp' => $cityData['isp'] ?? '',
            'data_source' => $cityData['source'] ?? self::SOURCE_BAIDU,
            'raw_data' => $cityData['raw'] ?? [],
            'hit_count' => 1,
            'last_hit_at' => now(),
        ]);
    }

    /**
     * 获取缓存的城市信息
     */
    public static function getCachedCity(string $ip): ?string
    {
        $cache = self::where('ip_address', $ip)->first();

        if ($cache) {
            // 更新命中统计
            $cache->increment('hit_count');
            $cache->update(['last_hit_at' => now()]);
            return $cache->city;
        }

        return null;
    }

    /**
     * 批量获取城市信息
     */
    public static function batchGetCities(array $ips): array
    {
        $results = [];
        $caches = self::whereIn('ip_address', $ips)->get()->keyBy('ip_address');

        foreach ($ips as $ip) {
            if (isset($caches[$ip])) {
                $cache = $caches[$ip];
                $cache->increment('hit_count');
                $results[$ip] = $cache->city;
            } else {
                $results[$ip] = null;
            }
        }

        return $results;
    }

    /**
     * 清理过期缓存
     */
    public static function cleanExpiredCache(int $days = 30): int
    {
        return self::where('last_hit_at', '<', now()->subDays($days))->delete();
    }

    /**
     * 获取热门城市统计
     */
    public static function getPopularCities(int $limit = 20): array
    {
        return self::selectRaw('city, SUM(hit_count) as total_hits, COUNT(*) as ip_count')
                  ->where('city', '!=', '未知')
                  ->where('city', '!=', '')
                  ->groupBy('city')
                  ->orderBy('total_hits', 'desc')
                  ->limit($limit)
                  ->get()
                  ->toArray();
    }

    /**
     * 获取数据源统计
     */
    public static function getSourceStats(): array
    {
        return self::selectRaw('data_source, COUNT(*) as count, SUM(hit_count) as total_hits')
                  ->groupBy('data_source')
                  ->orderBy('count', 'desc')
                  ->get()
                  ->toArray();
    }

    /**
     * 查询作用域：按数据源过滤
     */
    public function scopeBySource($query, string $source)
    {
        return $query->where('data_source', $source);
    }

    /**
     * 查询作用域：按城市过滤
     */
    public function scopeByCity($query, string $city)
    {
        return $query->where('city', $city);
    }

    /**
     * 查询作用域：热门IP（命中次数高）
     */
    public function scopePopular($query, int $minHits = 10)
    {
        return $query->where('hit_count', '>=', $minHits);
    }

    /**
     * 查询作用域：最近活跃
     */
    public function scopeRecentlyActive($query, int $days = 7)
    {
        return $query->where('last_hit_at', '>=', now()->subDays($days));
    }

    /**
     * 获取数据源名称
     */
    public function getDataSourceNameAttribute(): string
    {
        $names = [
            self::SOURCE_BAIDU => '百度地图',
            self::SOURCE_BAIDU_CLOUD => '百度云',
            self::SOURCE_TAOBAO => '淘宝IP库',
            self::SOURCE_IP_API => 'IP-API',
            self::SOURCE_LOCAL => '本地数据库',
        ];

        return $names[$this->data_source] ?? '未知来源';
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([$this->country, $this->province, $this->city]);
        return implode(' ', $parts);
    }

    /**
     * 检查缓存是否过期
     */
    public function isExpired(int $days = 30): bool
    {
        return $this->last_hit_at < now()->subDays($days);
    }

    /**
     * 更新缓存数据
     */
    public function updateCacheData(array $newData): bool
    {
        return $this->update([
            'city' => $newData['city'] ?? $this->city,
            'province' => $newData['province'] ?? $this->province,
            'country' => $newData['country'] ?? $this->country,
            'isp' => $newData['isp'] ?? $this->isp,
            'data_source' => $newData['source'] ?? $this->data_source,
            'raw_data' => array_merge($this->raw_data ?? [], $newData['raw'] ?? []),
            'last_hit_at' => now(),
        ]);
    }
}