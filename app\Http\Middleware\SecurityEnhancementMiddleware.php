<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

/**
 * 增强安全中间件
 * 提供防暴力破解、IP白名单、请求频率限制、SQL注入防护等功能
 */
class SecurityEnhancementMiddleware
{
    /**
     * 可疑关键词列表
     */
    private const SUSPICIOUS_KEYWORDS = [
        'union', 'select', 'insert', 'update', 'delete', 'drop',
        'create', 'alter', 'script', 'javascript', 'vbscript',
        'onload', 'onerror', 'onclick', 'eval', 'expression',
        '../', '..\\', 'file://', 'php://', 'data://',
        'cmd', 'system', 'exec', 'passthru', 'shell_exec'
    ];

    /**
     * 危险文件扩展名
     */
    private const DANGEROUS_EXTENSIONS = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx',
        'jsp', 'cgi', 'pl', 'py', 'rb', 'sh', 'bat', 'exe'
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // 1. IP白名单检查
        if (!$this->checkIPWhitelist($request)) {
            return $this->blockRequest($request, 'IP not in whitelist');
        }

        // 2. 请求频率限制
        if (!$this->checkRateLimit($request)) {
            return $this->blockRequest($request, 'Rate limit exceeded');
        }

        // 3. 防暴力破解检查
        if (!$this->checkBruteForce($request)) {
            return $this->blockRequest($request, 'Brute force detected');
        }

        // 4. SQL注入检查
        if (!$this->checkSQLInjection($request)) {
            return $this->blockRequest($request, 'SQL injection detected');
        }

        // 5. XSS检查
        if (!$this->checkXSS($request)) {
            return $this->blockRequest($request, 'XSS attempt detected');
        }

        // 6. 文件上传安全检查
        if (!$this->checkFileUpload($request)) {
            return $this->blockRequest($request, 'Dangerous file upload detected');
        }

        // 7. 用户代理检查
        if (!$this->checkUserAgent($request)) {
            return $this->blockRequest($request, 'Suspicious user agent');
        }

        // 8. 记录正常请求
        $this->logRequest($request);

        $response = $next($request);

        // 9. 响应头安全加固
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * 检查IP白名单
     */
    private function checkIPWhitelist(Request $request): bool
    {
        $ip = $request->ip();
        $whitelist = Cache::get('ip_whitelist', []);
        
        // 如果没有设置白名单，则允许所有IP
        if (empty($whitelist)) {
            return true;
        }

        // 检查IP是否在白名单中
        foreach ($whitelist as $allowedIP) {
            if ($this->ipInRange($ip, $allowedIP)) {
                return true;
            }
        }

        // 记录被阻止的IP
        $this->logBlockedIP($ip, 'IP not in whitelist');
        
        return false;
    }

    /**
     * 检查请求频率限制
     */
    private function checkRateLimit(Request $request): bool
    {
        $ip = $request->ip();
        $key = 'rate_limit_' . $ip;
        
        // 不同类型的请求有不同的限制
        $limits = [
            'login' => ['max' => 5, 'window' => 300], // 5分钟内最多5次登录尝试
            'api' => ['max' => 100, 'window' => 60],  // 1分钟内最多100次API请求
            'web' => ['max' => 300, 'window' => 60],  // 1分钟内最多300次Web请求
        ];

        $requestType = $this->getRequestType($request);
        $limit = $limits[$requestType] ?? $limits['web'];

        return RateLimiter::attempt(
            $key,
            $limit['max'],
            function () {},
            $limit['window']
        );
    }

    /**
     * 检查防暴力破解
     */
    private function checkBruteForce(Request $request): bool
    {
        $ip = $request->ip();
        
        // 检查是否为登录相关请求
        if (!$this->isLoginRequest($request)) {
            return true;
        }

        $key = 'brute_force_' . $ip;
        $attempts = Cache::get($key, 0);
        
        // 如果失败次数超过限制，阻止请求
        if ($attempts >= 10) {
            $this->logSecurityEvent($ip, 'Brute force attack detected', [
                'attempts' => $attempts,
                'url' => $request->fullUrl(),
            ]);
            return false;
        }

        return true;
    }

    /**
     * 检查SQL注入
     */
    private function checkSQLInjection(Request $request): bool
    {
        $inputs = array_merge($request->all(), $request->headers->all());
        
        foreach ($inputs as $key => $value) {
            if (is_string($value) && $this->containsSQLInjection($value)) {
                $this->logSecurityEvent($request->ip(), 'SQL injection attempt', [
                    'parameter' => $key,
                    'value' => $value,
                    'url' => $request->fullUrl(),
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 检查XSS攻击
     */
    private function checkXSS(Request $request): bool
    {
        $inputs = $request->all();
        
        foreach ($inputs as $key => $value) {
            if (is_string($value) && $this->containsXSS($value)) {
                $this->logSecurityEvent($request->ip(), 'XSS attempt', [
                    'parameter' => $key,
                    'value' => $value,
                    'url' => $request->fullUrl(),
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 检查文件上传
     */
    private function checkFileUpload(Request $request): bool
    {
        if (!$request->hasFile('file')) {
            return true;
        }

        $files = $request->file('file');
        if (!is_array($files)) {
            $files = [$files];
        }

        foreach ($files as $file) {
            if ($file && $this->isDangerousFile($file)) {
                $this->logSecurityEvent($request->ip(), 'Dangerous file upload', [
                    'filename' => $file->getClientOriginalName(),
                    'extension' => $file->getClientOriginalExtension(),
                    'mime_type' => $file->getClientMimeType(),
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * 检查用户代理
     */
    private function checkUserAgent(Request $request): bool
    {
        $userAgent = $request->header('User-Agent');
        
        // 检查是否为空或可疑的用户代理
        if (empty($userAgent) || $this->isSuspiciousUserAgent($userAgent)) {
            $this->logSecurityEvent($request->ip(), 'Suspicious user agent', [
                'user_agent' => $userAgent,
                'url' => $request->fullUrl(),
            ]);
            return false;
        }

        return true;
    }

    /**
     * 阻止请求
     */
    private function blockRequest(Request $request, string $reason): \Illuminate\Http\Response
    {
        $ip = $request->ip();
        
        // 记录被阻止的请求
        $this->logBlockedRequest($ip, $reason, $request);
        
        // 增加IP的阻止计数
        $this->incrementBlockCount($ip);
        
        return response('Access Denied', 403);
    }

    /**
     * 添加安全响应头
     */
    private function addSecurityHeaders($response): void
    {
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Content-Security-Policy', "default-src 'self'");
    }

    /**
     * 记录请求日志
     */
    private function logRequest(Request $request): void
    {
        $data = [
            'ip' => $request->ip(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'user_agent' => $request->header('User-Agent'),
            'timestamp' => Carbon::now(),
        ];

        Log::info('Request logged', $data);
    }

    /**
     * 记录安全事件
     */
    private function logSecurityEvent(string $ip, string $event, array $data = []): void
    {
        $logData = array_merge([
            'ip' => $ip,
            'event' => $event,
            'timestamp' => Carbon::now(),
        ], $data);

        Log::warning('Security event', $logData);
        
        // 同时存储到缓存中用于实时监控
        $cacheKey = 'security_events_' . date('Y-m-d');
        $events = Cache::get($cacheKey, []);
        $events[] = $logData;
        Cache::put($cacheKey, $events, 86400);
    }

    /**
     * 记录被阻止的IP
     */
    private function logBlockedIP(string $ip, string $reason): void
    {
        $cacheKey = 'blocked_ips_' . date('Y-m-d');
        $blockedIPs = Cache::get($cacheKey, []);
        $blockedIPs[] = [
            'ip' => $ip,
            'reason' => $reason,
            'timestamp' => Carbon::now(),
        ];
        Cache::put($cacheKey, $blockedIPs, 86400);
    }

    /**
     * 记录被阻止的请求
     */
    private function logBlockedRequest(string $ip, string $reason, Request $request): void
    {
        Log::warning('Request blocked', [
            'ip' => $ip,
            'reason' => $reason,
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_agent' => $request->header('User-Agent'),
            'timestamp' => Carbon::now(),
        ]);
    }

    /**
     * 增加IP阻止计数
     */
    private function incrementBlockCount(string $ip): void
    {
        $key = 'block_count_' . $ip;
        $count = Cache::get($key, 0);
        Cache::put($key, $count + 1, 3600); // 1小时过期
    }

    // 辅助方法
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') !== false) {
            list($subnet, $mask) = explode('/', $range);
            $mask = pow(2, 32 - $mask) - 1;
            return (ip2long($ip) & ~$mask) == (ip2long($subnet) & ~$mask);
        }
        return $ip === $range;
    }

    private function getRequestType(Request $request): string
    {
        if ($request->is('api/*')) {
            return 'api';
        }
        if ($request->is('login') || $request->is('auth/*')) {
            return 'login';
        }
        return 'web';
    }

    private function isLoginRequest(Request $request): bool
    {
        return $request->is('login') || $request->is('auth/*') || $request->is('api/login');
    }

    private function containsSQLInjection(string $value): bool
    {
        $value = strtolower($value);
        
        // 检查SQL注入关键词
        foreach (self::SUSPICIOUS_KEYWORDS as $keyword) {
            if (strpos($value, $keyword) !== false) {
                return true;
            }
        }

        // 检查SQL注入模式
        $patterns = [
            '/\b(union|select|insert|update|delete|drop|create|alter)\b/i',
            '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
            '/\b(exec|execute|sp_executesql)\b/i',
            '/\b(xp_cmdshell|sp_oacreate)\b/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    private function containsXSS(string $value): bool
    {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript\s*:/i',
            '/vbscript\s*:/i',
            '/on\w+\s*=/i',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>.*?<\/embed>/is',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    private function isDangerousFile($file): bool
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        // 检查危险扩展名
        if (in_array($extension, self::DANGEROUS_EXTENSIONS)) {
            return true;
        }

        // 检查文件头
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file->getRealPath());
        finfo_close($finfo);

        $dangerousMimeTypes = [
            'application/x-php',
            'application/x-httpd-php',
            'text/x-php',
            'application/x-executable',
            'application/x-msdownload',
        ];

        return in_array($mimeType, $dangerousMimeTypes);
    }

    private function isSuspiciousUserAgent(string $userAgent): bool
    {
        $suspiciousPatterns = [
            '/bot|crawler|spider|scraper/i',
            '/sqlmap|nikto|nmap|masscan/i',
            '/wget|curl|libwww|python|perl/i',
            '/script|automated|tool/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取今日安全事件
     */
    public static function getTodaySecurityEvents(): array
    {
        $cacheKey = 'security_events_' . date('Y-m-d');
        return Cache::get($cacheKey, []);
    }

    /**
     * 获取今日被阻止的IP
     */
    public static function getTodayBlockedIPs(): array
    {
        $cacheKey = 'blocked_ips_' . date('Y-m-d');
        return Cache::get($cacheKey, []);
    }
} 