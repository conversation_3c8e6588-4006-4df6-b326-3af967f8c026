<template>
  <div class="anti-block-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">防封系统管理</h1>
          <p class="page-subtitle">监控和管理域名健康状态、访问统计和防封策略</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="checkAllDomains">
            <el-icon><Refresh /></el-icon>
            检查所有域名
          </el-button>
          <el-button @click="showAddDomain = true">
            <el-icon><Plus /></el-icon>
            添加域名
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stats-card primary">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ domainStats.total }}</div>
                <div class="stats-label">总域名数</div>
              </div>
            </div>
            <div class="stats-trend">
              <span>健康域名: {{ domainStats.normal }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card success">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ domainStats.avg_health_score }}%</div>
                <div class="stats-label">平均健康分数</div>
              </div>
            </div>
            <div class="stats-trend positive">
              <el-icon><CaretTop /></el-icon>
              <span>+2.3%</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card warning">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ domainStats.abnormal }}</div>
                <div class="stats-label">异常域名</div>
              </div>
            </div>
            <div class="stats-trend">
              <span>需要关注</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card danger">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ domainStats.blocked }}</div>
                <div class="stats-label">封禁域名</div>
              </div>
            </div>
            <div class="stats-trend negative">
              <span>需要处理</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="24">
      <!-- 域名列表 -->
      <el-col :span="16">
        <el-card title="域名管理">
          <template #header>
            <div class="card-header">
              <span>域名管理</span>
              <div class="header-actions">
                <el-select v-model="domainFilter" placeholder="筛选状态" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="正常" value="normal" />
                  <el-option label="异常" value="abnormal" />
                  <el-option label="封禁" value="blocked" />
                </el-select>
              </div>
            </div>
          </template>

          <el-table :data="filteredDomains" v-loading="loading">
            <el-table-column prop="domain" label="域名" min-width="200" />
            <el-table-column prop="status_name" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ row.status_name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="health_score" label="健康分数" width="120">
              <template #default="{ row }">
                <el-progress 
                  :percentage="row.health_score" 
                  :color="getHealthColor(row.health_score)"
                  :show-text="false"
                />
                <span class="health-score">{{ row.health_score }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="use_count" label="使用次数" width="100" />
            <el-table-column prop="last_check_time" label="最后检查" width="150">
              <template #default="{ row }">
                <span v-if="row.last_check_time">
                  {{ formatTime(row.last_check_time) }}
                </span>
                <span v-else class="text-muted">未检查</span>
              </template>
            </el-table-column>
            <el-table-column label="访问检测" width="120">
              <template #default="{ row }">
                <div class="access-status">
                  <el-tooltip content="微信访问" placement="top">
                    <el-icon :color="row.check_results?.wechat_accessible ? '#67c23a' : '#f56c6c'">
                      <ChatDotRound />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="QQ访问" placement="top">
                    <el-icon :color="row.check_results?.qq_accessible ? '#67c23a' : '#f56c6c'">
                      <Message />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="SSL证书" placement="top">
                    <el-icon :color="row.check_results?.ssl_valid ? '#67c23a' : '#f56c6c'">
                      <Lock />
                    </el-icon>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="checkDomain(row)">检查</el-button>
                <el-button size="small" type="success" @click="viewDetails(row)">详情</el-button>
                <el-dropdown @command="handleDomainAction">
                  <el-button size="small">
                    更多<el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'restore', domain: row}">恢复</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'block', domain: row}">封禁</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', domain: row}" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next"
              @size-change="fetchDomains"
              @current-change="fetchDomains"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧面板 -->
      <el-col :span="8">
        <!-- 浏览器统计 -->
        <el-card title="浏览器统计" class="mb-4">
          <div class="browser-stats">
            <div 
              v-for="stat in browserStats" 
              :key="stat.browser_type"
              class="browser-item"
            >
              <div class="browser-info">
                <span class="browser-name">{{ stat.browser_name }}</span>
                <span class="browser-count">{{ stat.count }}</span>
              </div>
              <el-progress 
                :percentage="stat.percentage" 
                :show-text="false"
                :stroke-width="6"
              />
            </div>
          </div>
        </el-card>

        <!-- 访问趋势 -->
        <el-card title="访问趋势">
          <div class="trend-chart" ref="trendChart" style="height: 200px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加域名对话框 -->
    <el-dialog v-model="showAddDomain" title="添加域名" width="500px">
      <el-form :model="domainForm" label-width="100px">
        <el-form-item label="域名" required>
          <el-input v-model="domainForm.domain" placeholder="请输入域名，如：example.com" />
        </el-form-item>
        <el-form-item label="域名类型">
          <el-select v-model="domainForm.domain_type">
            <el-option label="重定向域名" value="redirect" />
            <el-option label="落地页域名" value="landing" />
            <el-option label="API域名" value="api" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-input-number v-model="domainForm.priority" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="domainForm.remarks" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDomain = false">取消</el-button>
        <el-button type="primary" @click="addDomain">添加</el-button>
      </template>
    </el-dialog>

    <!-- 域名详情对话框 -->
    <el-dialog v-model="showDomainDetails" title="域名详情" width="70%">
      <div v-if="selectedDomain" class="domain-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="域名">{{ selectedDomain.domain }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedDomain.status)">
              {{ selectedDomain.status_name }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="健康分数">{{ selectedDomain.health_score }}%</el-descriptions-item>
          <el-descriptions-item label="使用次数">{{ selectedDomain.use_count }}</el-descriptions-item>
          <el-descriptions-item label="最后检查">{{ formatTime(selectedDomain.last_check_time) }}</el-descriptions-item>
          <el-descriptions-item label="最后使用">{{ formatTime(selectedDomain.last_use_time) }}</el-descriptions-item>
        </el-descriptions>

        <div class="mt-4" v-if="selectedDomain.check_results">
          <h4>检查结果详情</h4>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="check-item">
                <el-icon :color="selectedDomain.check_results.accessible ? '#67c23a' : '#f56c6c'">
                  <Connection />
                </el-icon>
                <span>可访问性</span>
                <el-tag :type="selectedDomain.check_results.accessible ? 'success' : 'danger'" size="small">
                  {{ selectedDomain.check_results.accessible ? '正常' : '异常' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="check-item">
                <el-icon :color="selectedDomain.check_results.dns_resolved ? '#67c23a' : '#f56c6c'">
                  <Connection />
                </el-icon>
                <span>DNS解析</span>
                <el-tag :type="selectedDomain.check_results.dns_resolved ? 'success' : 'danger'" size="small">
                  {{ selectedDomain.check_results.dns_resolved ? '正常' : '异常' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="check-item">
                <el-icon :color="selectedDomain.check_results.ssl_valid ? '#67c23a' : '#f56c6c'">
                  <Lock />
                </el-icon>
                <span>SSL证书</span>
                <el-tag :type="selectedDomain.check_results.ssl_valid ? 'success' : 'danger'" size="small">
                  {{ selectedDomain.check_results.ssl_valid ? '有效' : '无效' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="16" class="mt-3">
            <el-col :span="8">
              <div class="check-item">
                <el-icon :color="selectedDomain.check_results.wechat_accessible ? '#67c23a' : '#f56c6c'">
                  <ChatDotRound />
                </el-icon>
                <span>微信访问</span>
                <el-tag :type="selectedDomain.check_results.wechat_accessible ? 'success' : 'danger'" size="small">
                  {{ selectedDomain.check_results.wechat_accessible ? '正常' : '受限' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="check-item">
                <el-icon :color="selectedDomain.check_results.qq_accessible ? '#67c23a' : '#f56c6c'">
                  <Message />
                </el-icon>
                <span>QQ访问</span>
                <el-tag :type="selectedDomain.check_results.qq_accessible ? 'success' : 'danger'" size="small">
                  {{ selectedDomain.check_results.qq_accessible ? '正常' : '受限' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="check-item">
                <el-icon><Timer /></el-icon>
                <span>响应时间</span>
                <el-tag size="small">{{ selectedDomain.check_results.response_time }}ms</el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Refresh, CircleCheck, Warning, CircleClose, CaretTop,
  ChatDotRound, Message, Lock, ArrowDown, Connection, Timer
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { antiBlockApi } from '@/api/marketing'

// 响应式数据
const loading = ref(false)
const domainList = ref([])
const domainStats = ref({
  total: 0,
  normal: 0,
  abnormal: 0,
  blocked: 0,
  avg_health_score: 0
})
const browserStats = ref([])
const domainFilter = ref('')
const showAddDomain = ref(false)
const showDomainDetails = ref(false)
const selectedDomain = ref(null)
const trendChart = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const domainForm = reactive({
  domain: '',
  domain_type: 'redirect',
  priority: 5,
  remarks: ''
})

// 计算属性
const filteredDomains = computed(() => {
  if (!domainFilter.value) return domainList.value
  
  return domainList.value.filter(domain => {
    switch (domainFilter.value) {
      case 'normal':
        return domain.status === 1
      case 'abnormal':
        return domain.status === 2
      case 'blocked':
        return domain.status === 3
      default:
        return true
    }
  })
})

// 方法
const fetchDomains = async () => {
  loading.value = true
  try {
    const response = await antiBlockApi.getDomainHealth()
    domainList.value = response.data.domains || []
    domainStats.value = response.data.stats || {}
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取域名列表失败')
  } finally {
    loading.value = false
  }
}

const fetchBrowserStats = async () => {
  try {
    const response = await antiBlockApi.getBrowserStats(7)
    browserStats.value = response.data || []
  } catch (error) {
    console.error('获取浏览器统计失败:', error)
  }
}

const checkDomain = async (domain) => {
  try {
    await antiBlockApi.checkDomainHealth(domain.domain)
    ElMessage.success('域名检查完成')
    fetchDomains()
  } catch (error) {
    ElMessage.error('域名检查失败')
  }
}

const checkAllDomains = async () => {
  try {
    const response = await antiBlockApi.checkDomainHealth()
    ElMessage.success(`批量检查完成，检查了 ${response.data.checked} 个域名`)
    fetchDomains()
  } catch (error) {
    ElMessage.error('批量检查失败')
  }
}

const addDomain = async () => {
  if (!domainForm.domain) {
    ElMessage.warning('请输入域名')
    return
  }
  
  try {
    // 这里需要调用添加域名的API
    ElMessage.success('域名添加成功')
    showAddDomain.value = false
    fetchDomains()
    
    // 重置表单
    Object.keys(domainForm).forEach(key => {
      if (key === 'domain_type') domainForm[key] = 'redirect'
      else if (key === 'priority') domainForm[key] = 5
      else domainForm[key] = ''
    })
  } catch (error) {
    ElMessage.error('域名添加失败')
  }
}

const viewDetails = (domain) => {
  selectedDomain.value = domain
  showDomainDetails.value = true
}

const handleDomainAction = async ({ action, domain }) => {
  switch (action) {
    case 'restore':
      try {
        // 调用恢复域名API
        ElMessage.success('域名已恢复')
        fetchDomains()
      } catch (error) {
        ElMessage.error('域名恢复失败')
      }
      break
    case 'block':
      try {
        // 调用封禁域名API
        ElMessage.success('域名已封禁')
        fetchDomains()
      } catch (error) {
        ElMessage.error('域名封禁失败')
      }
      break
    case 'delete':
      try {
        await ElMessageBox.confirm('确定要删除这个域名吗？', '确认删除', {
          type: 'warning'
        })
        // 调用删除域名API
        ElMessage.success('域名已删除')
        fetchDomains()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('域名删除失败')
        }
      }
      break
  }
}

const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

const getHealthColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const initTrendChart = () => {
  nextTick(() => {
    if (!trendChart.value) return
    
    const chart = echarts.init(trendChart.value)
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [120, 200, 150, 80, 70, 110, 130],
        type: 'line',
        smooth: true,
        areaStyle: {
          opacity: 0.3
        }
      }]
    }
    chart.setOption(option)
  })
}

// 生命周期
onMounted(() => {
  fetchDomains()
  fetchBrowserStats()
  initTrendChart()
})
</script>

<style scoped>
.anti-block-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

.page-subtitle {
  color: #909399;
  margin: 5px 0 0 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-card.primary {
  border-left: 4px solid #409eff;
}

.stats-card.success {
  border-left: 4px solid #67c23a;
}

.stats-card.warning {
  border-left: 4px solid #e6a23c;
}

.stats-card.danger {
  border-left: 4px solid #f56c6c;
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  font-size: 32px;
  margin-right: 16px;
  opacity: 0.8;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.stats-trend {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.stats-trend.positive {
  color: #67c23a;
}

.stats-trend.negative {
  color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-score {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.access-status {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.browser-stats {
  space-y: 12px;
}

.browser-item {
  margin-bottom: 12px;
}

.browser-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.browser-name {
  font-size: 14px;
  color: #303133;
}

.browser-count {
  font-size: 14px;
  color: #909399;
}

.domain-details {
  padding: 16px 0;
}

.check-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.text-muted {
  color: #c0c4cc;
}
</style>