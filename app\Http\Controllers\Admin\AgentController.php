<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AgentAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AgentController extends Controller
{
    /**
     * 代理商列表
     */
    public function index(Request $request)
    {
        $query = AgentAccount::with(['user', 'creator']);

        // 搜索过滤
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('agent_code', 'like', "%{$search}%")
                  ->orWhere('agent_name', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 有效期过滤
        if ($request->filled('validity')) {
            switch ($request->validity) {
                case 'expired':
                    $query->where('status', 'expired')
                          ->orWhere(function($q) {
                              $q->where('is_permanent', false)
                                ->where('end_date', '<', now());
                          });
                    break;
                case 'expiring_soon':
                    $query->where('is_permanent', false)
                          ->where('end_date', '>', now())
                          ->where('end_date', '<=', now()->addDays(7));
                    break;
                case 'permanent':
                    $query->where('is_permanent', true);
                    break;
            }
        }

        $agents = $query->orderBy('created_at', 'desc')->paginate(15);

        // 统计数据
        $stats = [
            'total' => AgentAccount::count(),
            'active' => AgentAccount::where('status', 'active')->count(),
            'expired' => AgentAccount::where('status', 'expired')->count(),
            'expiring_soon' => AgentAccount::where('is_permanent', false)
                ->where('end_date', '>', now())
                ->where('end_date', '<=', now()->addDays(7))
                ->count(),
        ];

        return view('admin.agents.index', compact('agents', 'stats'));
    }

    /**
     * 创建代理商页面
     */
    public function create()
    {
        $users = User::whereDoesntHave('agentAccount')
                    ->where('status', 'active')
                    ->select('id', 'name', 'email')
                    ->get();

        return view('admin.agents.create', compact('users'));
    }

    /**
     * 保存代理商
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id|unique:agent_accounts,user_id',
            'agent_name' => 'required|string|max:100',
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'required_if:validity_period,custom|nullable|date|after:today',
            'commission_type' => 'required|in:no_commission,percentage',
            'commission_rate' => 'required_if:commission_type,percentage|nullable|numeric|min:0|max:100',
            'permissions' => 'nullable|array',
            'remark' => 'nullable|string|max:500'
        ], [
            'user_id.required' => '请选择用户',
            'user_id.exists' => '用户不存在',
            'user_id.unique' => '该用户已经是代理商',
            'agent_name.required' => '请输入代理商名称',
            'validity_period.required' => '请选择有效期',
            'custom_end_date.required_if' => '请选择自定义结束时间',
            'custom_end_date.after' => '结束时间必须大于今天',
            'commission_type.required' => '请选择佣金类型',
            'commission_rate.required_if' => '请输入佣金比例',
            'commission_rate.max' => '佣金比例不能超过100%'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();
        try {
            $agent = new AgentAccount();
            $agent->user_id = $request->user_id;
            $agent->agent_code = AgentAccount::generateAgentCode();
            $agent->agent_name = $request->agent_name;
            $agent->status = 'active';
            $agent->no_commission = $request->commission_type === 'no_commission';
            $agent->commission_rate = $request->commission_type === 'percentage' ? $request->commission_rate : 0;
            $agent->permissions = $request->permissions ?? [];
            $agent->created_by = auth()->id();
            $agent->remark = $request->remark;

            // 设置有效期
            $customEndDate = $request->validity_period === 'custom' 
                ? Carbon::parse($request->custom_end_date) 
                : null;
            $agent->setValidityPeriod($request->validity_period, $customEndDate);

            $agent->save();

            // 更新用户角色
            $user = User::find($request->user_id);
            $user->assignRole('agent');

            DB::commit();

            return redirect()->route('admin.agents.index')
                           ->with('success', '代理商开通成功！');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', '开通失败：' . $e->getMessage())->withInput();
        }
    }

    /**
     * 代理商详情
     */
    public function show(AgentAccount $agent)
    {
        $agent->load(['user', 'creator', 'commissionLogs' => function($query) {
            $query->with(['user', 'order'])->latest()->limit(10);
        }]);

        // 统计数据
        $stats = [
            'total_commission' => $agent->commissionLogs()->where('status', 'confirmed')->sum('commission_amount'),
            'pending_commission' => $agent->commissionLogs()->where('status', 'pending')->sum('commission_amount'),
            'total_orders' => $agent->commissionLogs()->where('type', 'order')->count(),
            'total_users' => $agent->user->referrals()->count(),
            'this_month_commission' => $agent->commissionLogs()
                ->where('status', 'confirmed')
                ->whereMonth('created_at', now()->month)
                ->sum('commission_amount'),
        ];

        return view('admin.agents.show', compact('agent', 'stats'));
    }

    /**
     * 编辑代理商页面
     */
    public function edit(AgentAccount $agent)
    {
        return view('admin.agents.edit', compact('agent'));
    }

    /**
     * 更新代理商
     */
    public function update(Request $request, AgentAccount $agent)
    {
        $validator = Validator::make($request->all(), [
            'agent_name' => 'required|string|max:100',
            'status' => 'required|in:active,inactive,suspended',
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'required_if:validity_period,custom|nullable|date|after:today',
            'commission_type' => 'required|in:no_commission,percentage',
            'commission_rate' => 'required_if:commission_type,percentage|nullable|numeric|min:0|max:100',
            'permissions' => 'nullable|array',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();
        try {
            $agent->agent_name = $request->agent_name;
            $agent->status = $request->status;
            $agent->no_commission = $request->commission_type === 'no_commission';
            $agent->commission_rate = $request->commission_type === 'percentage' ? $request->commission_rate : 0;
            $agent->permissions = $request->permissions ?? [];
            $agent->remark = $request->remark;

            // 更新有效期
            $customEndDate = $request->validity_period === 'custom' 
                ? Carbon::parse($request->custom_end_date) 
                : null;
            $agent->setValidityPeriod($request->validity_period, $customEndDate);

            $agent->save();

            DB::commit();

            return redirect()->route('admin.agents.show', $agent)
                           ->with('success', '代理商信息更新成功！');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', '更新失败：' . $e->getMessage())->withInput();
        }
    }

    /**
     * 续期代理商
     */
    public function renew(Request $request, AgentAccount $agent)
    {
        $validator = Validator::make($request->all(), [
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'required_if:validity_period,custom|nullable|date|after:today',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()]);
        }

        try {
            $customEndDate = $request->validity_period === 'custom' 
                ? Carbon::parse($request->custom_end_date) 
                : null;
            
            $agent->setValidityPeriod($request->validity_period, $customEndDate);
            $agent->status = 'active';
            $agent->save();

            return response()->json([
                'success' => true, 
                'message' => '续期成功！',
                'validity_text' => $agent->validity_text
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => '续期失败：' . $e->getMessage()]);
        }
    }

    /**
     * 暂停/恢复代理商
     */
    public function toggleStatus(AgentAccount $agent)
    {
        try {
            $agent->status = $agent->status === 'active' ? 'suspended' : 'active';
            $agent->save();

            $statusText = $agent->status === 'active' ? '恢复' : '暂停';
            
            return response()->json([
                'success' => true, 
                'message' => "代理商{$statusText}成功！",
                'status' => $agent->status,
                'status_text' => $agent->status_text
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除代理商
     */
    public function destroy(AgentAccount $agent)
    {
        try {
            DB::beginTransaction();

            // 移除用户的代理角色
            $agent->user->removeRole('agent');

            // 删除代理商记录
            $agent->delete();

            DB::commit();

            return response()->json(['success' => true, 'message' => '代理商删除成功！']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 佣金记录
     */
    public function commissions(Request $request, AgentAccount $agent)
    {
        $query = $agent->commissionLogs()->with(['user', 'order']);

        // 类型过滤
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 时间过滤
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereBetween('created_at', [
                    Carbon::parse($dates[0])->startOfDay(),
                    Carbon::parse($dates[1])->endOfDay()
                ]);
            }
        }

        $commissions = $query->orderBy('created_at', 'desc')->paginate(20);

        // 统计数据
        $stats = [
            'total_amount' => $agent->commissionLogs()->sum('commission_amount'),
            'confirmed_amount' => $agent->commissionLogs()->where('status', 'confirmed')->sum('commission_amount'),
            'pending_amount' => $agent->commissionLogs()->where('status', 'pending')->sum('commission_amount'),
            'this_month_amount' => $agent->commissionLogs()
                ->where('status', 'confirmed')
                ->whereMonth('created_at', now()->month)
                ->sum('commission_amount'),
        ];

        return view('admin.agents.commissions', compact('agent', 'commissions', 'stats'));
    }

    /**
     * 批量操作
     */
    public function batchAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,suspend,delete',
            'agent_ids' => 'required|array|min:1',
            'agent_ids.*' => 'exists:agent_accounts,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()]);
        }

        try {
            DB::beginTransaction();

            $agents = AgentAccount::whereIn('id', $request->agent_ids)->get();
            $count = 0;

            foreach ($agents as $agent) {
                switch ($request->action) {
                    case 'activate':
                        $agent->status = 'active';
                        $agent->save();
                        $count++;
                        break;
                    case 'suspend':
                        $agent->status = 'suspended';
                        $agent->save();
                        $count++;
                        break;
                    case 'delete':
                        $agent->user->removeRole('agent');
                        $agent->delete();
                        $count++;
                        break;
                }
            }

            DB::commit();

            $actionText = match($request->action) {
                'activate' => '激活',
                'suspend' => '暂停',
                'delete' => '删除'
            };

            return response()->json([
                'success' => true, 
                'message' => "成功{$actionText}了 {$count} 个代理商！"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => '批量操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出代理商数据
     */
    public function export(Request $request)
    {
        // 这里可以使用 Laravel Excel 包来导出数据
        // 暂时返回 JSON 格式的数据
        $agents = AgentAccount::with(['user', 'creator'])->get();
        
        $data = $agents->map(function($agent) {
            return [
                '代理商编码' => $agent->agent_code,
                '代理商名称' => $agent->agent_name,
                '用户名' => $agent->user->name,
                '邮箱' => $agent->user->email,
                '状态' => $agent->status_text,
                '佣金比例' => $agent->no_commission ? '不抽佣' : $agent->commission_rate . '%',
                '有效期' => $agent->validity_text,
                '累计佣金' => $agent->total_commission,
                '累计用户' => $agent->total_users,
                '创建时间' => $agent->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json(['data' => $data]);
    }
}