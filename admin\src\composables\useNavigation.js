/**
 * 导航组合式函数
 * 提供响应式的导航权限过滤和路由管理
 */

import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import permissionService from '@/services/PermissionService'

export function useNavigation() {
  const router = useRouter()
  const userStore = useUserStore()
  
  // 响应式数据
  const loading = ref(false)
  const error = ref(null)
  const accessibleRoutes = ref([])
  
  // 计算属性
  const userRole = computed(() => userStore.userInfo?.role)
  
  // 方法：获取可访问的路由
  const fetchAccessibleRoutes = async () => {
    if (!userRole.value) {
      accessibleRoutes.value = []
      return
    }
    
    loading.value = true
    error.value = null
    
    try {
      const allRoutes = router.options.routes.filter(route => {
        return route.path !== '/login' &&
               route.path !== '/404' &&
               route.path !== '/403' &&
               route.path !== '/' &&
               !route.meta?.hidden
      })
      
      const routes = await permissionService.getAccessibleRoutes(userRole.value, allRoutes)
      accessibleRoutes.value = routes
      
      console.log('✅ 导航路由获取成功:', {
        total: routes.length,
        userRole: userRole.value,
        routes: routes.map(r => ({ path: r.path, title: r.meta?.title }))
      })
    } catch (err) {
      console.error('❌ 导航路由获取失败:', err)
      error.value = err.message
      accessibleRoutes.value = []
    } finally {
      loading.value = false
    }
  }
  
  // 方法：检查路由权限
  const checkRoutePermission = async (routePath) => {
    if (!userRole.value) return false
    return await permissionService.hasRouteAccess(userRole.value, routePath)
  }
  
  // 方法：过滤菜单
  const filterMenuByPermissions = async (menuItems) => {
    if (!userRole.value) return []
    return await permissionService.filterAccessibleMenus(userRole.value, menuItems)
  }
  
  // 增强的过滤导航
  const filteredNavigation = computed(() => {
    if (legacyMode) {
      return accessibleRoutes.value
    }
    
    if (!navigationState.value.navigationData.length) return []
    
    let filtered = PermissionUtils.filterByPermission(
      navigationState.value.navigationData,
      userRole.value
    )
    
    if (searchState.value.query) {
      filtered = SearchUtils.searchNavigation(
        filtered,
        searchState.value.query,
        { limit: 50 }
      )
    }
    
    return filtered
  })
  
  const hasPermission = computed(() => (requiredPermission) => {
    return PermissionUtils.hasPermission(userRole.value, requiredPermission)
  })
  
  const currentBreadcrumbs = computed(() => {
    return UrlUtils.generateBreadcrumbs(route.path, filteredNavigation.value)
  })
  
  const searchSuggestions = computed(() => {
    if (!searchState.value.query) {
      return SEARCH_CONFIG.SUGGESTIONS.slice(0, 8)
    }
    
    return SearchUtils.getSearchSuggestions(
      searchState.value.query,
      searchState.value.history
    )
  })
  
  // 初始化导航系统
  const initializeNavigation = async () => {
    try {
      navigationState.value.isLoading = true
      
      if (enableCache) {
        await loadSavedState()
      }
      
      if (legacyMode) {
        await fetchAccessibleRoutes()
        return
      }
      
      const userNavigation = NavigationDataUtils.generateUserNavigation(
        userRole.value,
        navigationState.value.preferences.customNavigation
      )
      
      if (validateData && !ValidationUtils.validateNavigationData(userNavigation)) {
        console.warn('导航数据验证失败，使用默认数据')
        navigationState.value.navigationData = Object.values(DEFAULT_NAVIGATION)
      } else {
        navigationState.value.navigationData = userNavigation
      }
      
      navigationState.value.currentPath = route.path
      await expandCurrentSection()
      updateBreadcrumbs()
      loadFavorites()
      loadRecentItems()
      loadSearchHistory()
      
    } catch (error) {
      console.error('导航初始化失败:', error)
      ElMessage.error('导航加载失败')
    } finally {
      navigationState.value.isLoading = false
    }
  }
  
  const loadSavedState = async () => {
    const savedState = StorageUtils.loadNavigationState(
      NAVIGATION_CONFIG.CACHE_KEYS.NAVIGATION_STATE,
      {}
    )
    
    if (savedState.isCollapsed !== undefined) {
      navigationState.value.isCollapsed = savedState.isCollapsed
    }
    if (savedState.expandedSections) {
      navigationState.value.expandedSections = savedState.expandedSections
    }
    if (savedState.theme) {
      navigationState.value.theme = savedState.theme
    }
    if (savedState.preferences) {
      navigationState.value.preferences = savedState.preferences
    }
  }
  
  const saveState = AnimationUtils.debounce(() => {
    if (!autoSaveState) return
    
    const stateToSave = {
      isCollapsed: navigationState.value.isCollapsed,
      expandedSections: navigationState.value.expandedSections,
      theme: navigationState.value.theme,
      preferences: navigationState.value.preferences,
      timestamp: Date.now()
    }
    
    StorageUtils.saveNavigationState(
      NAVIGATION_CONFIG.CACHE_KEYS.NAVIGATION_STATE,
      stateToSave
    )
  }, 500)
  
  const navigateToPath = async (path, options = {}) => {
    const {
      replace = false,
      newTab = false,
      addToRecent = true,
      updateBreadcrumbs = true
    } = options
    
    try {
      if (newTab) {
        window.open(path, '_blank')
        return
      }
      
      const navigationItem = NavigationDataUtils.findItemByPath(
        filteredNavigation.value,
        path
      )
      
      if (addToRecent && navigationItem) {
        addToRecentItems(navigationItem)
      }
      
      if (replace) {
        await router.replace(path)
      } else {
        await router.push(path)
      }
      
      navigationState.value.currentPath = path
      
      if (updateBreadcrumbs) {
        updateBreadcrumbs()
      }
      
    } catch (error) {
      console.error('导航失败:', error)
      ElMessage.error('页面跳转失败')
    }
  }
  
  const toggleSection = (sectionKey) => {
    const expanded = navigationState.value.expandedSections
    const index = expanded.indexOf(sectionKey)
    
    if (index >= 0) {
      expanded.splice(index, 1)
    } else {
      expanded.push(sectionKey)
    }
    
    saveState()
  }
  
  const expandCurrentSection = async () => {
    const currentItem = NavigationDataUtils.findItemByPath(
      filteredNavigation.value,
      route.path
    )
    
    if (currentItem) {
      const chain = NavigationDataUtils.getNavigationChain(
        filteredNavigation.value,
        route.path
      )
      
      chain.forEach(item => {
        if (item.key && !navigationState.value.expandedSections.includes(item.key)) {
          navigationState.value.expandedSections.push(item.key)
        }
      })
      
      saveState()
    }
  }
  
  const collapseNavigation = (collapsed) => {
    navigationState.value.isCollapsed = collapsed
    saveState()
  }
  
  const updateBreadcrumbs = () => {
    breadcrumbs.value = UrlUtils.generateBreadcrumbs(
      route.path,
      filteredNavigation.value
    )
  }
  
  const performSearch = AnimationUtils.debounce((query) => {
    searchState.value.query = query
    searchState.value.isActive = !!query
    
    if (!query.trim()) {
      searchState.value.results = []
      return
    }
    
    if (legacyMode) {
      searchState.value.results = searchNavigationItems(accessibleRoutes.value, query)
      return
    }
    
    const results = SearchUtils.searchNavigation(
      filteredNavigation.value,
      query,
      {
        fields: ['title', 'description', 'keywords'],
        fuzzy: true,
        limit: 20
      }
    )
    
    searchState.value.results = results
    addToSearchHistory(query)
  }, NAVIGATION_CONFIG.SEARCH_DEBOUNCE_DELAY)
  
  const clearSearch = () => {
    searchState.value.query = ''
    searchState.value.results = []
    searchState.value.isActive = false
  }
  
  const addToSearchHistory = (query) => {
    StorageUtils.manageSearchHistory.add(query)
    loadSearchHistory()
  }
  
  const loadSearchHistory = () => {
    searchState.value.history = StorageUtils.manageSearchHistory.getAll(10)
  }
  
  const toggleFavorite = (item) => {
    const isFav = favorites.value.some(fav => fav.key === item.key)
    
    if (isFav) {
      StorageUtils.manageFavorites.remove(item.key)
      ElMessage.success('已取消收藏')
    } else {
      StorageUtils.manageFavorites.add(item)
      ElMessage.success('已加入收藏')
    }
    
    loadFavorites()
  }
  
  const isFavorite = (item) => {
    return StorageUtils.manageFavorites.isFavorite(item.key)
  }
  
  const loadFavorites = () => {
    favorites.value = StorageUtils.manageFavorites.getAll()
  }
  
  const addToRecentItems = (item) => {
    StorageUtils.manageRecent.add({
      ...item,
      visitedAt: Date.now(),
      path: route.path
    })
    loadRecentItems()
  }
  
  const loadRecentItems = () => {
    recentItems.value = StorageUtils.manageRecent.getAll(
      NAVIGATION_CONFIG.MAX_RECENT_ITEMS
    )
  }
  
  const setTheme = (theme) => {
    navigationState.value.theme = theme
    document.documentElement.setAttribute('data-theme', theme)
    saveState()
  }
  
  const toggleTheme = () => {
    const currentTheme = navigationState.value.theme
    const newTheme = currentTheme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }
  
  const isCurrentPath = (path) => {
    return UrlUtils.isPathMatch(route.path, path, false)
  }
  
  const isExactCurrentPath = (path) => {
    return UrlUtils.isPathMatch(route.path, path, true)
  }
  
  if (loadOnMount) {
    onMounted(async () => {
      await initializeNavigation()
    })
  }
  
  onBeforeUnmount(() => {
    saveState()
  })
  
  // 监听用户信息变化
  watch(userRole, (newRole) => {
    if (newRole) {
      fetchAccessibleRoutes()
    } else {
      accessibleRoutes.value = []
    }
  }, { immediate: true })
  
  // 监听用户信息完整变化
  watch(() => userStore.userInfo, async (newUserInfo) => {
    console.log('👤 用户信息变化触发导航更新:', newUserInfo)
    if (newUserInfo?.role) {
      if (legacyMode) {
        fetchAccessibleRoutes()
      } else {
        await initializeNavigation()
      }
    }
  }, { immediate: true, deep: true })
  
  // 新增：监听路由变化
  watch(() => route.path, async (newPath) => {
    navigationState.value.currentPath = newPath
    await expandCurrentSection()
    updateBreadcrumbs()
    
    if (!legacyMode) {
      const currentItem = NavigationDataUtils.findItemByPath(
        filteredNavigation.value,
        newPath
      )
      
      if (currentItem) {
        addToRecentItems(currentItem)
      }
    }
  })
  
  // 新增：监听主题变化
  watch(() => navigationState.value.theme, (newTheme) => {
    document.documentElement.setAttribute('data-theme', newTheme)
  })
  
  return {
    // 原有兼容API
    loading,
    error,
    accessibleRoutes,
    userRole,
    fetchAccessibleRoutes,
    checkRoutePermission,
    filterMenuByPermissions,
    
    // 新增强化状态
    navigationState: readonly(navigationState),
    searchState: readonly(searchState),
    favorites: readonly(favorites),
    recentItems: readonly(recentItems),
    breadcrumbs: readonly(breadcrumbs),
    
    // 新增计算属性
    currentUser,
    isAuthenticated,
    filteredNavigation,
    hasPermission,
    currentBreadcrumbs,
    searchSuggestions,
    
    // 新增核心方法
    initializeNavigation,
    navigateToPath,
    toggleSection,
    expandCurrentSection,
    collapseNavigation,
    updateBreadcrumbs,
    
    // 搜索功能
    performSearch,
    clearSearch,
    addToSearchHistory,
    loadSearchHistory,
    
    // 收藏功能
    toggleFavorite,
    isFavorite,
    loadFavorites,
    
    // 最近访问
    addToRecentItems,
    loadRecentItems,
    
    // 主题管理
    setTheme,
    toggleTheme,
    
    // 工具方法
    isCurrentPath,
    isExactCurrentPath,
    
    // 状态保存
    saveState
  }
}

/**
 * 简化的导航组合式API - 用于基础功能
 */
export function useSimpleNavigation() {
  const {
    navigationState,
    filteredNavigation,
    currentBreadcrumbs,
    navigateToPath,
    isCurrentPath,
    isExactCurrentPath
  } = useNavigation({
    autoSaveState: false,
    enableCache: false,
    loadOnMount: true,
    validateData: false,
    legacyMode: true
  })
  
  return {
    navigationState,
    filteredNavigation,
    currentBreadcrumbs,
    navigateToPath,
    isCurrentPath,
    isExactCurrentPath
  }
}

/**
 * 搜索专用组合式API
 */
export function useNavigationSearch() {
  const {
    searchState,
    searchSuggestions,
    performSearch,
    clearSearch,
    loadSearchHistory
  } = useNavigation({
    autoSaveState: true,
    enableCache: true,
    loadOnMount: false
  })
  
  return {
    searchState,
    searchSuggestions,
    performSearch,
    clearSearch,
    loadSearchHistory
  }
}

/**
 * 收藏和最近访问组合式API
 */
export function useNavigationBookmarks() {
  const {
    favorites,
    recentItems,
    toggleFavorite,
    isFavorite,
    loadFavorites,
    loadRecentItems
  } = useNavigation({
    autoSaveState: true,
    enableCache: true,
    loadOnMount: false
  })
  
  return {
    favorites,
    recentItems,
    toggleFavorite,
    isFavorite,
    loadFavorites,
    loadRecentItems
  }
}

/**
 * 权限检查组合式API
 */
export function useNavigationPermissions() {
  const { userRole, hasPermission, isAuthenticated, checkRoutePermission } = useNavigation({
    loadOnMount: false,
    legacyMode: true
  })
  
  const checkPermission = (permission) => {
    return PermissionUtils.hasPermission(userRole.value, permission)
  }
  
  const filterByPermission = (items) => {
    return PermissionUtils.filterByPermission(items, userRole.value)
  }
  
  return {
    userRole,
    hasPermission,
    isAuthenticated,
    checkPermission,
    filterByPermission,
    checkRoutePermission
  }
}

// 默认导出主要的组合式API
export default useNavigation

/**
 * 菜单项转换工具
 */
export function transformRoutesToMenu(routes) {
  return routes.map(route => {
    const menuItem = {
      path: route.path,
      meta: {
        title: route.meta?.title || route.name || route.path,
        icon: route.meta?.icon || 'Document',
        description: route.meta?.description || '',
        order: route.meta?.order || 999
      }
    }
    
    if (route.children && route.children.length > 0) {
      menuItem.children = transformRoutesToMenu(route.children)
    }
    
    return menuItem
  })
}

/**
 * 搜索导航项
 */
export function searchNavigationItems(items, query) {
  if (!query || query.length < 2) return []
  
  const results = []
  const searchTerm = query.toLowerCase()
  
  function searchRecursive(items, path = '') {
    items.forEach(item => {
      const title = item.meta?.title || ''
      const description = item.meta?.description || ''
      
      if (title.toLowerCase().includes(searchTerm) || 
          description.toLowerCase().includes(searchTerm)) {
        results.push({
          id: `${path}/${item.path}`,
          title,
          path: `${path}/${item.path}`,
          icon: item.meta?.icon || 'Document',
          description
        })
      }
      
      if (item.children) {
        searchRecursive(item.children, `${path}/${item.path}`)
      }
    })
  }
  
  searchRecursive(items)
  
  return results.slice(0, 10) // 限制结果数量
}

/**
 * 面包屑生成器
 */
export function generateBreadcrumbs(route, routes) {
  const breadcrumbs = []
  
  function findRoute(currentPath, currentRoutes) {
    for (const routeItem of currentRoutes) {
      if (routeItem.path === currentPath) {
        return routeItem
      }
      
      if (routeItem.children) {
        const found = findRoute(currentPath, routeItem.children)
        if (found) {
          breadcrumbs.unshift({
            path: routeItem.path,
            meta: routeItem.meta
          })
          return found
        }
      }
    }
    return null
  }
  
  // 从当前路由开始向上查找
  const pathSegments = route.path.split('/').filter(Boolean)
  let currentPath = ''
  
  pathSegments.forEach(segment => {
    currentPath += `/${segment}`
    const foundRoute = findRoute(currentPath, routes)
    if (foundRoute && foundRoute.meta?.title) {
      breadcrumbs.push({
        path: currentPath,
        meta: foundRoute.meta
      })
    }
  })
  
  return breadcrumbs
}