import{_ as a}from"./index-DtXAftX0.js";/* empty css                     *//* empty css                       *//* empty css                        *//* empty css                 *//* empty css                *//* empty css               *//* empty css               */import{aS as e,U as l,a$ as s,T as t,ac as n,at as o,a_ as u,aZ as r,a7 as i,bG as d,c8 as c,af as p,bR as m,aV as v,aY as f,bz as _,o as y,bm as g,bn as b,bp as h,bq as C,aM as k,ay as V,Q as j}from"./element-plus-h2SQQM64.js";import{S as w}from"./StatCard-u_ssO_Ky.js";import{L as x}from"./LineChart-CydsJ2U8.js";import{A as U}from"./AvatarUpload-Kj8d-M_w.js";import{g as z,a as F,b as A,c as S,d as q,u as E}from"./user-CJhH85FQ.js";import{r as L,L as Z,e as D,k as P,l as R,t as B,E as I,y as M,B as N,z as O,D as T,u as Y,F as $,Y as G}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 *//* empty css                  *//* empty css                    */import"./chunk-KZPPZA2C-BZQYgWVq.js";const J={class:"user-center"},K={class:"page-header"},Q={class:"user-info"},H={class:"user-details"},W={class:"user-tags"},X={class:"header-actions"},aa={class:"card-header"},ea={class:"recent-orders"},la={key:0,class:"empty-state"},sa={key:1},ta={class:"order-info"},na={class:"order-title"},oa={class:"order-meta"},ua={class:"order-no"},ra={class:"order-time"},ia={class:"order-amount"},da={class:"order-status"},ca={class:"card-header"},pa={class:"points-history"},ma={key:0,class:"empty-state"},va={key:1},fa={class:"points-info"},_a={class:"points-title"},ya={class:"points-time"},ga={class:"card-header"},ba={class:"analysis-summary"},ha={class:"summary-item"},Ca={class:"value"},ka={class:"summary-item"},Va={class:"value"},ja={class:"summary-item"},wa={class:"value"},xa={class:"summary-item"},Ua={class:"value"},za=a({__name:"UserCenter",setup(a){const za=L(!1),Fa=L(!1),Aa=L("month"),Sa=L({}),qa=L({}),Ea=L([]),La=L([]),Za=L({}),Da=Z({name:"",email:"",phone:"",avatar:"",bio:""}),Pa={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},Ra=L(),Ba=L({labels:[],datasets:[{label:"消费金额",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),Ia={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},Ma=async()=>{try{const a=await z();Sa.value=a.data,Object.assign(Da,{name:Sa.value.name||"",email:Sa.value.email||"",phone:Sa.value.phone||"",avatar:Sa.value.avatar||"",bio:Sa.value.bio||""})}catch(a){j.error("加载用户信息失败")}},Na=async()=>{try{const a=await q({period:Aa.value});Za.value=a.data.summary,Ba.value={labels:a.data.chart.labels,datasets:[{label:"消费金额",data:a.data.chart.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}}catch(a){j.error("加载消费分析失败")}},Oa=async()=>{try{await Ra.value.validate(),Fa.value=!0,await E(Da),j.success("资料更新成功"),za.value=!1,Ma()}catch(a){!1!==a&&j.error("更新失败")}finally{Fa.value=!1}},Ta=a=>{Sa.value.avatar=a.url,j.success("头像更新成功!")},Ya=a=>{console.error("头像上传失败:",a),j.error("头像上传失败，请重试")},$a=()=>{j.info("订单页面开发中...")},Ga=()=>{j.info("积分页面开发中...")},Ja=()=>{j.info("优惠券页面开发中...")},Ka=()=>{j.info("安全设置页面开发中...")},Qa=()=>{j.info("意见反馈页面开发中...")},Ha=()=>{j.info("帮助中心页面开发中...")},Wa=a=>new Date(a).toLocaleString("zh-CN"),Xa=a=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[a]||"未知状态");return D(()=>{Ma(),(async()=>{try{const a=await F();qa.value=a.data}catch(a){j.error("加载统计数据失败")}})(),(async()=>{try{const a=await A({limit:5});Ea.value=a.data}catch(a){j.error("加载订单数据失败")}})(),(async()=>{try{const a=await S({limit:5});La.value=a.data}catch(a){j.error("加载积分记录失败")}})(),Na()}),(a,j)=>{const z=e,F=s,A=t,S=o,q=u,E=r,L=f,Z=_,D=b,Ma=g,ae=C,ee=k,le=h,se=V;return R(),P("div",J,[B("div",K,[B("div",Q,[I(z,{size:80,src:Sa.value.avatar},null,8,["src"]),B("div",H,[B("h2",null,l(Sa.value.name||Sa.value.username),1),B("p",null,l(Sa.value.email),1),B("div",W,[I(F,{type:"primary"},{default:O(()=>{return[T(l((a=Sa.value.role,{admin:"管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"}[a]||"未知角色")),1)];var a}),_:1}),Sa.value.vip_level?(R(),M(F,{key:0,type:"warning"},{default:O(()=>[T("VIP"+l(Sa.value.vip_level),1)]),_:1})):N("",!0),I(F,{type:1===Sa.value.status?"success":"danger"},{default:O(()=>[T(l(1===Sa.value.status?"正常":"禁用"),1)]),_:1},8,["type"])])])]),B("div",X,[I(S,{type:"primary",onClick:j[0]||(j[0]=a=>za.value=!0)},{default:O(()=>[I(A,null,{default:O(()=>[I(Y(n))]),_:1}),j[9]||(j[9]=T(" 编辑资料 ",-1))]),_:1,__:[9]})])]),I(E,{gutter:20,class:"stats-row"},{default:O(()=>[I(q,{span:6},{default:O(()=>[I(w,{title:"我的订单",value:qa.value.total_orders||0,icon:"Tickets",color:"#409EFF"},null,8,["value"])]),_:1}),I(q,{span:6},{default:O(()=>[I(w,{title:"消费金额",value:qa.value.total_spent||0,icon:"Money",color:"#67C23A",prefix:"¥"},null,8,["value"])]),_:1}),I(q,{span:6},{default:O(()=>[I(w,{title:"积分余额",value:qa.value.points||0,icon:"Star",color:"#E6A23C"},null,8,["value"])]),_:1}),I(q,{span:6},{default:O(()=>[I(w,{title:"优惠券",value:qa.value.coupons||0,icon:"Discount",color:"#F56C6C",suffix:"张"},null,8,["value"])]),_:1})]),_:1}),I(L,{class:"quick-functions"},{header:O(()=>j[10]||(j[10]=[B("span",null,"快捷功能",-1)])),default:O(()=>[I(E,{gutter:15},{default:O(()=>[I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:$a},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(i))]),_:1}),j[11]||(j[11]=B("span",null,"我的订单",-1))])]),_:1}),I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:Ga},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(d))]),_:1}),j[12]||(j[12]=B("span",null,"积分管理",-1))])]),_:1}),I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:Ja},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(c))]),_:1}),j[13]||(j[13]=B("span",null,"优惠券",-1))])]),_:1}),I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:Ka},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(p))]),_:1}),j[14]||(j[14]=B("span",null,"安全设置",-1))])]),_:1}),I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:Qa},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(m))]),_:1}),j[15]||(j[15]=B("span",null,"意见反馈",-1))])]),_:1}),I(q,{span:4},{default:O(()=>[B("div",{class:"function-item",onClick:Ha},[I(A,{class:"function-icon"},{default:O(()=>[I(Y(v))]),_:1}),j[16]||(j[16]=B("span",null,"帮助中心",-1))])]),_:1})]),_:1})]),_:1}),I(E,{gutter:20,class:"content-row"},{default:O(()=>[I(q,{span:12},{default:O(()=>[I(L,null,{header:O(()=>[B("div",aa,[j[18]||(j[18]=B("span",null,"最近订单",-1)),I(S,{size:"small",onClick:$a},{default:O(()=>j[17]||(j[17]=[T("查看全部",-1)])),_:1,__:[17]})])]),default:O(()=>[B("div",ea,[0===Ea.value.length?(R(),P("div",la,[I(Z,{description:"暂无订单"})])):(R(),P("div",sa,[(R(!0),P($,null,G(Ea.value,a=>{return R(),P("div",{key:a.id,class:"order-item"},[B("div",ta,[B("div",na,l(a.wechat_group?.title||"订单"),1),B("div",oa,[B("span",ua,"订单号: "+l(a.order_no),1),B("span",ra,l(Wa(a.created_at)),1)])]),B("div",ia,"¥"+l(a.amount),1),B("div",da,[I(F,{type:(e=a.status,{pending:"warning",paid:"success",cancelled:"danger",refunded:"info"}[e]||"info")},{default:O(()=>[T(l(Xa(a.status)),1)]),_:2},1032,["type"])])]);var e}),128))]))])]),_:1})]),_:1}),I(q,{span:12},{default:O(()=>[I(L,null,{header:O(()=>[B("div",ca,[j[20]||(j[20]=B("span",null,"积分记录",-1)),I(S,{size:"small",onClick:Ga},{default:O(()=>j[19]||(j[19]=[T("查看全部",-1)])),_:1,__:[19]})])]),default:O(()=>[B("div",pa,[0===La.value.length?(R(),P("div",ma,[I(Z,{description:"暂无积分记录"})])):(R(),P("div",va,[(R(!0),P($,null,G(La.value,a=>(R(),P("div",{key:a.id,class:"points-item"},[B("div",fa,[B("div",_a,l(a.description),1),B("div",ya,l(Wa(a.created_at)),1)]),B("div",{class:y(["points-change","add"===a.type?"positive":"negative"])},l("add"===a.type?"+":"-")+l(a.points),3)]))),128))]))])]),_:1})]),_:1})]),_:1}),I(L,{class:"consumption-analysis"},{header:O(()=>[B("div",ga,[j[24]||(j[24]=B("span",null,"消费分析",-1)),I(Ma,{modelValue:Aa.value,"onUpdate:modelValue":j[1]||(j[1]=a=>Aa.value=a),size:"small",onChange:Na},{default:O(()=>[I(D,{label:"month"},{default:O(()=>j[21]||(j[21]=[T("本月",-1)])),_:1,__:[21]}),I(D,{label:"quarter"},{default:O(()=>j[22]||(j[22]=[T("本季度",-1)])),_:1,__:[22]}),I(D,{label:"year"},{default:O(()=>j[23]||(j[23]=[T("本年",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])])]),default:O(()=>[I(E,{gutter:20},{default:O(()=>[I(q,{span:16},{default:O(()=>[I(x,{data:Ba.value,options:Ia,height:"300px"},null,8,["data"])]),_:1}),I(q,{span:8},{default:O(()=>[B("div",ba,[B("div",ha,[j[25]||(j[25]=B("span",{class:"label"},"总消费:",-1)),B("span",Ca,"¥"+l(Za.value.total_amount||0),1)]),B("div",ka,[j[26]||(j[26]=B("span",{class:"label"},"订单数:",-1)),B("span",Va,l(Za.value.total_orders||0),1)]),B("div",ja,[j[27]||(j[27]=B("span",{class:"label"},"平均客单价:",-1)),B("span",wa,"¥"+l(Za.value.avg_amount||0),1)]),B("div",xa,[j[28]||(j[28]=B("span",{class:"label"},"最高消费:",-1)),B("span",Ua,"¥"+l(Za.value.max_amount||0),1)])])]),_:1})]),_:1})]),_:1}),I(se,{modelValue:za.value,"onUpdate:modelValue":j[8]||(j[8]=a=>za.value=a),title:"编辑个人资料",width:"600px"},{footer:O(()=>[I(S,{onClick:j[7]||(j[7]=a=>za.value=!1)},{default:O(()=>j[29]||(j[29]=[T("取消",-1)])),_:1,__:[29]}),I(S,{type:"primary",onClick:Oa,loading:Fa.value},{default:O(()=>j[30]||(j[30]=[T(" 保存 ",-1)])),_:1,__:[30]},8,["loading"])]),default:O(()=>[I(le,{model:Da,rules:Pa,ref_key:"profileFormRef",ref:Ra,"label-width":"100px"},{default:O(()=>[I(ae,{label:"头像"},{default:O(()=>[I(U,{modelValue:Da.avatar,"onUpdate:modelValue":j[2]||(j[2]=a=>Da.avatar=a),size:100,"max-size":2,"enable-preview":!0,onSuccess:Ta,onError:Ya},null,8,["modelValue"])]),_:1}),I(ae,{label:"姓名",prop:"name"},{default:O(()=>[I(ee,{modelValue:Da.name,"onUpdate:modelValue":j[3]||(j[3]=a=>Da.name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),I(ae,{label:"邮箱",prop:"email"},{default:O(()=>[I(ee,{modelValue:Da.email,"onUpdate:modelValue":j[4]||(j[4]=a=>Da.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),I(ae,{label:"手机号",prop:"phone"},{default:O(()=>[I(ee,{modelValue:Da.phone,"onUpdate:modelValue":j[5]||(j[5]=a=>Da.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),I(ae,{label:"个人简介"},{default:O(()=>[I(ee,{modelValue:Da.bio,"onUpdate:modelValue":j[6]||(j[6]=a=>Da.bio=a),type:"textarea",rows:3,placeholder:"请输入个人简介"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0bb296ec"]]);export{za as default};
