import{n as e}from"./index-DtXAftX0.js";import{f as n}from"./chunk-KZPPZA2C-BZQYgWVq.js";const t=Array.from({length:50},(e,t)=>({id:t+1,order_id:`ORD${n.string.alphanumeric(10).toUpperCase()}`,user_id:n.number.int({min:1,max:100}),user_name:n.internet.userName(),user_avatar:n.image.avatar(),amount:n.finance.amount(5,100,2),status:n.helpers.arrayElement(["pending","settled","frozen"]),created_at:n.date.past().toISOString(),settled_at:n.helpers.arrayElement([null,n.date.recent().toISOString()])})),r=Array.from({length:20},()=>({id:n.string.uuid(),order_no:`ORD${n.string.alphanumeric(12)}`,product_name:n.commerce.productName(),amount:n.commerce.price()})),s=Array.from({length:10},()=>({id:n.number.int({min:1,max:100}),username:n.internet.userName(),avatar:n.image.avatar()})),a={batchSettleCommissions:e=>(console.log("[Mock] Batch settling commissions for IDs:",e),Promise.resolve({code:0,data:{success:!0,count:e.length},message:"批量结算成功"})),addCommission(e){console.log("[Mock] Adding commission:",e);const n={...e,id:t.length+1,created_at:(new Date).toISOString(),status:"pending"};return t.unshift(n),Promise.resolve({code:0,data:n,message:"添加佣金成功"})},searchOrders(e){const n=r.filter(n=>n.product_name.includes(e)||n.order_no.includes(e));return Promise.resolve({code:0,data:{list:n},message:"成功"})},searchUsers(e){const n=s.filter(n=>n.username.includes(e));return Promise.resolve({code:0,data:{list:n},message:"成功"})}};function o(){return e({url:"/admin/finance/stats",method:"get"})}function i(n){return e({url:"/admin/finance/charts",method:"get",params:n})}function m(n){return e({url:"/admin/finance/recent-transactions",method:"get",params:n})}function c(n){return e({url:"/admin/finance/transactions",method:"get",params:n})}function u(n){return e({url:"/admin/finance/commissions",method:"get",params:n})}function d(){return e({url:"/admin/finance/commissions/stats",method:"get"})}function l(n){return e({url:`/admin/finance/commissions/${n}/settle`,method:"post"})}function f(n){return e({url:"/admin/finance/export",method:"get",params:n,responseType:"blob"})}function g(e){return a.batchSettleCommissions(e)}function h(e){return a.addCommission(e)}function p(e){return a.searchOrders(e)}function _(e){return a.searchUsers(e)}export{o as a,m as b,u as c,d,f as e,g as f,i as g,p as h,_ as i,h as j,c as k,l as s};
