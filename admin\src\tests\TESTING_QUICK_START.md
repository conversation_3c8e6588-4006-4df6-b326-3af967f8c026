# 导航测试快速上手指南

## 🚀 快速开始

### 1. 安装测试依赖

```bash
cd admin

# 安装所有依赖（包括测试依赖）
npm install

# 安装 Playwright 浏览器
npm run playwright:install
```

### 2. 运行测试

#### 单元测试
```bash
# 运行所有单元测试
npm run test:unit

# 监听模式运行测试（开发时推荐）
npm run test:unit:watch

# 运行测试并生成覆盖率报告
npm run test:coverage

# 使用UI界面运行测试
npm run test:unit:ui
```

#### E2E测试
```bash
# 运行所有E2E测试
npm run test:e2e

# 使用UI界面运行E2E测试
npm run test:e2e:ui

# 调试模式运行E2E测试
npm run test:e2e:debug

# 查看测试报告
npm run test:e2e:report
```

#### 完整测试套件
```bash
# 运行所有测试
npm run test:all

# CI环境测试（包括覆盖率）
npm run test:ci
```

## 📝 编写测试

### 单元测试示例

创建 `src/tests/components/YourComponent.test.js`:

```javascript
import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import YourComponent from '@/components/YourComponent.vue'

describe('YourComponent 测试', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(YourComponent, {
      props: {
        title: '测试标题'
      }
    })
  })

  it('应该正确渲染标题', () => {
    expect(wrapper.find('h1').text()).toBe('测试标题')
  })

  it('点击按钮应该触发事件', async () => {
    await wrapper.find('button').trigger('click')
    expect(wrapper.emitted()).toHaveProperty('click')
  })
})
```

### E2E测试示例

创建 `tests/e2e/your-feature.spec.js`:

```javascript
import { test, expect } from '@playwright/test'

test.describe('您的功能测试', () => {
  test('用户应该能够完成基本操作', async ({ page }) => {
    // 导航到页面
    await page.goto('/your-page')
    
    // 验证页面元素
    await expect(page.locator('h1')).toContainText('页面标题')
    
    // 执行操作
    await page.click('button[data-testid="submit"]')
    
    // 验证结果
    await expect(page.locator('.success-message')).toBeVisible()
  })
})
```

## 🔍 测试最佳实践

### 1. 测试命名规范
- 文件名：`ComponentName.test.js`（单元测试）、`feature-name.spec.js`（E2E测试）
- 测试用例：使用中文描述，清楚表达测试意图
- 格式：`应该 + 预期行为 + 在特定条件下`

### 2. 测试组织
```javascript
describe('组件/功能名称', () => {
  describe('具体功能模块', () => {
    it('应该执行特定行为', () => {
      // 测试代码
    })
  })
})
```

### 3. 测试覆盖重点
- **必须测试**：核心业务逻辑、用户交互、错误处理
- **重点测试**：边界条件、异常情况、权限控制
- **适度测试**：UI渲染、样式变化（关键场景）

### 4. Mock和测试数据
```javascript
// 使用工厂函数创建测试数据
const createUser = (overrides = {}) => ({
  id: 1,
  username: 'testuser',
  role: 'admin',
  ...overrides
})

// Mock API调用
vi.mock('@/api/user', () => ({
  getUser: vi.fn().mockResolvedValue(createUser())
}))
```

## 📊 测试覆盖率目标

- **整体覆盖率**：≥ 80%
- **核心组件覆盖率**：≥ 90%
- **关键业务逻辑覆盖率**：100%

## 🔧 故障排除

### 常见问题

#### 1. 测试运行缓慢
```bash
# 使用并行测试
npm run test:unit -- --reporter=dot --run

# 只运行变更相关的测试
npm run test:unit:watch
```

#### 2. Element Plus 组件报错
确保在 `tests/setup.js` 中正确配置了组件存根：
```javascript
config.global.stubs = {
  'el-button': true,
  'el-input': true,
  // 其他组件...
}
```

#### 3. E2E测试超时
增加超时时间或优化页面加载：
```javascript
// playwright.config.js
export default defineConfig({
  timeout: 60 * 1000, // 增加到60秒
  expect: {
    timeout: 10 * 1000 // 增加断言超时
  }
})
```

#### 4. 路由测试失败
确保正确模拟路由：
```javascript
// 在测试中创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes: mockRoutes
})
await router.isReady()
```

## 📈 持续集成

测试将在以下情况自动运行：
- Push 到 main/develop 分支
- 创建 Pull Request
- 合并 Pull Request

### CI流程
1. **代码检查**：ESLint 代码风格检查
2. **单元测试**：运行所有单元测试并生成覆盖率报告
3. **E2E测试**：跨浏览器端到端测试
4. **性能测试**：Lighthouse 性能基准测试
5. **构建验证**：生产构建测试

## 🎯 开发工作流

### 开发新功能时
1. 先写测试（TDD）或功能完成后立即补充测试
2. 使用 `npm run test:unit:watch` 监听测试
3. 确保测试通过后再提交代码
4. Pull Request 时检查CI测试状态

### 修复Bug时
1. 先写复现Bug的测试
2. 修复代码直到测试通过
3. 确保没有回归问题

## 📚 更多资源

- [Vitest 官方文档](https://vitest.dev/)
- [Vue Test Utils 文档](https://test-utils.vuejs.org/)
- [Playwright 官方文档](https://playwright.dev/)
- [Element Plus 测试指南](https://element-plus.org/zh-CN/guide/dev.html#testing)

## 🤝 团队协作

### 测试代码审查要点
- [ ] 测试用例覆盖主要业务场景
- [ ] 测试描述清晰，易于理解
- [ ] 没有重复或冗余的测试
- [ ] Mock使用合理，不影响测试可靠性
- [ ] 测试运行稳定，无随机失败

### 贡献指南
1. 新功能必须包含对应测试
2. 测试覆盖率不能降低
3. E2E测试关注用户体验关键路径
4. 性能敏感功能需要基准测试