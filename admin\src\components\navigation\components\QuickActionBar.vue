<template>
  <div class="quick-action-bar">
    <div class="action-buttons">
      <el-button 
        v-for="action in actions"
        :key="action.id"
        :type="action.type"
        :size="size"
        :icon="action.icon"
        @click="handleAction(action)"
      >
        {{ action.label }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue"
import { useRouter } from "vue-router"
import { useUserStore } from "@/stores/user"

const props = defineProps({
  size: {
    type: String,
    default: "small"
  },
  actions: {
    type: Array,
    default: () => [
      {
        id: 1,
        label: "新建群组",
        icon: "Plus",
        type: "primary",
        action: "create-group"
      },
      {
        id: 2, 
        label: "数据导出",
        icon: "Download",
        type: "success",
        action: "export-data"
      },
      {
        id: 3,
        label: "系统设置",
        icon: "Setting",
        type: "info",
        action: "settings"
      }
    ]
  }
})

const emit = defineEmits(["action-click"])
const router = useRouter()
const userStore = useUserStore()

const handleAction = (action) => {
  emit("action-click", action)
  
  // 默认操作处理
  switch (action.action) {
    case "create-group":
      router.push("/community/groups/create")
      break
    case "export-data":
      router.push("/system/export")
      break
    case "settings":
      router.push("/system/settings")
      break
    default:
      if (action.route) {
        router.push(action.route)
      }
  }
}
</script>

<style scoped>
.quick-action-bar {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 100px;
}
</style>
