// 现代化主题样式系统
// ================================

// 颜色系统
:root {
  // 主色调
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  // 成功色
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;

  // 警告色
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  // 错误色
  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-200: #fecaca;
  --danger-300: #fca5a5;
  --danger-400: #f87171;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;
  --danger-800: #991b1b;
  --danger-900: #7f1d1d;

  // 信息色
  --info-50: #f0f9ff;
  --info-100: #e0f2fe;
  --info-200: #bae6fd;
  --info-300: #7dd3fc;
  --info-400: #38bdf8;
  --info-500: #0ea5e9;
  --info-600: #0284c7;
  --info-700: #0369a1;
  --info-800: #075985;
  --info-900: #0c4a6e;

  // 中性色
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  // 语义化颜色
  --color-primary: var(--primary-500);
  --color-success: var(--success-500);
  --color-warning: var(--warning-500);
  --color-danger: var(--danger-500);
  --color-info: var(--info-500);

  // 文字颜色
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-muted: var(--gray-500);
  --text-light: var(--gray-400);
  --text-white: #ffffff;

  // 背景颜色
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-muted: var(--gray-100);
  --bg-dark: var(--gray-900);

  // 边框颜色
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);

  // 阴影
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  // 玻璃态效果
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  // 渐变
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--info-500) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  --gradient-danger: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  --gradient-info: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);

  // 动画时长
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  // 动画缓动
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  // 圆角
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 9999px;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  // 字体大小
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;

  // 字体粗细
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  // 行高
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  // Z-index
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

// 暗色主题
[data-theme="dark"] {
  // 重新定义暗色主题的颜色变量
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
  --text-light: var(--gray-500);

  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-muted: var(--gray-700);

  --border-light: var(--gray-700);
  --border-medium: var(--gray-600);
  --border-dark: var(--gray-500);

  --glass-bg: rgba(30, 41, 59, 0.9);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

// 全局样式重置和基础样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: color var(--duration-normal) var(--ease-out), 
              background-color var(--duration-normal) var(--ease-out);
}

// 现代化滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-sm);
  transition: background-color var(--duration-normal) var(--ease-out);

  &:hover {
    background: var(--border-dark);
  }
}

// 选择文本样式
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: var(--text-primary);
}

// 焦点样式
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

// 工具类
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.gradient-text {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out),
              box-shadow var(--duration-normal) var(--ease-out);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
}

.hover-scale {
  transition: transform var(--duration-normal) var(--ease-out);

  &:hover {
    transform: scale(1.05);
  }
}

// 动画类
.animate-fade-in {
  animation: fadeIn var(--duration-slow) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-slow) var(--ease-out);
}

.animate-slide-down {
  animation: slideDown var(--duration-slow) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式断点
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: 1440px) {
    @content;
  }
}

// 现代化按钮样式
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 12px 24px;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  line-height: 1;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out);
  }

  &:hover::before {
    left: 100%;
  }

  &.primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-light);

    &:hover {
      background: var(--bg-muted);
      border-color: var(--border-medium);
    }
  }

  &.ghost {
    background: transparent;
    color: var(--text-muted);
    border: 1px solid var(--border-light);

    &:hover {
      background: var(--bg-secondary);
      color: var(--text-primary);
    }
  }

  &.danger {
    background: var(--gradient-danger);
    color: var(--text-white);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  // 尺寸变体
  &.xs {
    padding: 6px 12px;
    font-size: var(--text-xs);
  }

  &.sm {
    padding: 8px 16px;
    font-size: var(--text-sm);
  }

  &.lg {
    padding: 16px 32px;
    font-size: var(--text-lg);
  }

  &.xl {
    padding: 20px 40px;
    font-size: var(--text-xl);
  }
}

// 现代化卡片样式
.modern-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }

  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);

    .card-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }

    .card-subtitle {
      font-size: var(--text-sm);
      color: var(--text-muted);
      margin: var(--spacing-xs) 0 0 0;
    }
  }

  .card-body {
    padding: var(--spacing-lg);
  }

  .card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-secondary);
  }
}

// 现代化表单样式
.modern-form {
  .form-group {
    margin-bottom: var(--spacing-lg);

    .form-label {
      display: block;
      font-size: var(--text-sm);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .form-control {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-light);
      border-radius: var(--radius-lg);
      font-size: var(--text-base);
      color: var(--text-primary);
      background: var(--bg-primary);
      transition: all var(--duration-normal) var(--ease-out);

      &:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &::placeholder {
        color: var(--text-light);
      }

      &.error {
        border-color: var(--color-danger);

        &:focus {
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
      }
    }

    .form-error {
      margin-top: var(--spacing-xs);
      font-size: var(--text-xs);
      color: var(--color-danger);
    }

    .form-help {
      margin-top: var(--spacing-xs);
      font-size: var(--text-xs);
      color: var(--text-muted);
    }
  }
}

// 现代化表格样式
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  thead {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);

    th {
      padding: var(--spacing-md);
      text-align: left;
      font-size: var(--text-sm);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      border-bottom: 1px solid var(--border-light);
    }
  }

  tbody {
    tr {
      transition: background-color var(--duration-normal) var(--ease-out);

      &:hover {
        background: var(--bg-secondary);
      }

      &:not(:last-child) {
        border-bottom: 1px solid var(--border-light);
      }
    }

    td {
      padding: var(--spacing-md);
      font-size: var(--text-sm);
      color: var(--text-secondary);
    }
  }
}

// 状态指示器
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 8px;
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  &.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-600);

    .status-dot {
      background: var(--success-500);
      animation: pulse 2s infinite;
    }
  }

  &.offline {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-600);

    .status-dot {
      background: var(--danger-500);
    }
  }

  &.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-600);

    .status-dot {
      background: var(--warning-500);
    }
  }
}

// 徽章样式
.modern-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  line-height: 1;

  &.primary {
    background: var(--primary-100);
    color: var(--primary-700);
  }

  &.success {
    background: var(--success-100);
    color: var(--success-700);
  }

  &.warning {
    background: var(--warning-100);
    color: var(--warning-700);
  }

  &.danger {
    background: var(--danger-100);
    color: var(--danger-700);
  }

  &.info {
    background: var(--info-100);
    color: var(--info-700);
  }
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-light);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl);
  text-align: center;

  .empty-icon {
    font-size: 48px;
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
  }

  .empty-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }

  .empty-description {
    font-size: var(--text-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
  }
}

// 打印样式
@media print {
  * {
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }

  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-after: always;
  }
}

// ===== 导航系统现代化样式增强 =====

// 现代化导航容器
.modern-navigation {
  /* 导航头部样式 */
  .navigation-header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
      opacity: 0.3;
    }
    
    .header-brand {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .brand-logo {
        width: 32px;
        height: 32px;
        background: var(--gradient-primary);
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: var(--font-bold);
        font-size: var(--text-sm);
        box-shadow: var(--shadow-md);
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover {
          transform: scale(1.1) rotate(5deg);
          box-shadow: var(--shadow-lg);
        }
      }
      
      .brand-title {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        background: var(--gradient-primary);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    .header-search {
      position: relative;
      max-width: 400px;
      flex: 1;
      
      .search-input {
        width: 100%;
        padding: 12px 16px 12px 40px;
        border: 2px solid transparent;
        border-radius: var(--radius-full);
        background: var(--bg-secondary);
        font-size: var(--text-sm);
        transition: all var(--duration-normal) var(--ease-out);
        
        &:focus {
          background: var(--bg-primary);
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        &::placeholder {
          color: var(--text-light);
        }
      }
      
      .search-icon {
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 16px;
        pointer-events: none;
      }
      
      .search-suggestions {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        right: 0;
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
        z-index: var(--z-dropdown);
        
        .suggestion-item {
          padding: var(--spacing-sm) var(--spacing-md);
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          border-bottom: 1px solid var(--border-light);
          transition: all var(--duration-fast) var(--ease-out);
          
          &:last-child {
            border-bottom: none;
          }
          
          &:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
          }
          
          .suggestion-icon {
            width: 16px;
            height: 16px;
            color: var(--text-muted);
          }
          
          .suggestion-text {
            flex: 1;
            font-size: var(--text-sm);
            color: var(--text-primary);
          }
          
          .suggestion-type {
            font-size: var(--text-xs);
            color: var(--text-light);
            padding: 2px 6px;
            background: var(--bg-muted);
            border-radius: var(--radius-sm);
          }
        }
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .action-button {
        position: relative;
        width: 40px;
        height: 40px;
        border: none;
        border-radius: var(--radius-lg);
        background: var(--bg-secondary);
        color: var(--text-muted);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover {
          background: var(--bg-muted);
          color: var(--color-primary);
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }
        
        .action-badge {
          position: absolute;
          top: -2px;
          right: -2px;
          min-width: 18px;
          height: 18px;
          background: var(--color-danger);
          color: white;
          border-radius: var(--radius-full);
          font-size: 10px;
          font-weight: var(--font-bold);
          display: flex;
          align-items: center;
          justify-content: center;
          animation: bounce 2s infinite;
        }
      }
      
      .user-menu {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover {
          background: var(--bg-secondary);
        }
        
        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: var(--radius-lg);
          background: var(--gradient-primary);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: var(--font-semibold);
          font-size: var(--text-sm);
          box-shadow: var(--shadow-sm);
        }
        
        .user-info {
          display: flex;
          flex-direction: column;
          
          .user-name {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            line-height: 1.2;
          }
          
          .user-role {
            font-size: var(--text-xs);
            color: var(--text-muted);
            line-height: 1.2;
          }
        }
      }
    }
  }
  
  /* 侧边栏样式 */
  .navigation-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    box-shadow: var(--shadow-md);
    transition: all var(--duration-normal) var(--ease-out);
    
    &.collapsed {
      .nav-item-text,
      .nav-section-title {
        opacity: 0;
        transform: translateX(-10px);
      }
    }
    
    .sidebar-header {
      padding: var(--spacing-lg);
      border-bottom: 1px solid var(--border-light);
      background: linear-gradient(135deg, var(--bg-secondary), var(--bg-muted));
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: var(--spacing-md);
        right: var(--spacing-md);
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
        opacity: 0.5;
      }
    }
    
    .sidebar-content {
      padding: var(--spacing-md) 0;
      flex: 1;
      overflow-y: auto;
      
      /* 自定义滚动条 */
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background: var(--border-medium);
        border-radius: 2px;
      }
    }
    
    .nav-section {
      margin-bottom: var(--spacing-lg);
      
      .nav-section-title {
        padding: 0 var(--spacing-lg) var(--spacing-sm);
        font-size: var(--text-xs);
        font-weight: var(--font-semibold);
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all var(--duration-normal) var(--ease-out);
      }
      
      .nav-items {
        list-style: none;
        padding: 0;
        margin: 0;
      }
    }
    
    .nav-item {
      position: relative;
      
      .nav-link {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        margin: 2px var(--spacing-sm);
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: var(--radius-lg);
        transition: all var(--duration-normal) var(--ease-out);
        position: relative;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left var(--duration-slow) var(--ease-out);
        }
        
        &:hover {
          background: var(--bg-secondary);
          color: var(--color-primary);
          transform: translateX(4px);
          
          &::before {
            left: 100%;
          }
          
          .nav-icon {
            transform: scale(1.1);
          }
        }
        
        &.active {
          background: linear-gradient(135deg, 
            rgba(59, 130, 246, 0.1), 
            rgba(139, 92, 246, 0.1)
          );
          color: var(--color-primary);
          font-weight: var(--font-medium);
          
          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--gradient-primary);
            border-radius: 0 2px 2px 0;
          }
          
          .nav-icon {
            color: var(--color-primary);
            transform: scale(1.05);
          }
        }
        
        .nav-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          transition: all var(--duration-normal) var(--ease-out);
        }
        
        .nav-item-text {
          flex: 1;
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          transition: all var(--duration-normal) var(--ease-out);
        }
        
        .nav-badge {
          min-width: 20px;
          height: 20px;
          background: var(--color-danger);
          color: white;
          border-radius: var(--radius-full);
          font-size: 10px;
          font-weight: var(--font-bold);
          display: flex;
          align-items: center;
          justify-content: center;
          animation: pulse 2s infinite;
        }
        
        .nav-arrow {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          transition: transform var(--duration-normal) var(--ease-out);
        }
      }
      
      /* 子菜单样式 */
      .nav-submenu {
        list-style: none;
        padding: 0;
        margin: 0;
        padding-left: var(--spacing-xl);
        max-height: 0;
        overflow: hidden;
        transition: all var(--duration-normal) var(--ease-out);
        
        &.expanded {
          max-height: 300px;
          padding-top: var(--spacing-xs);
        }
        
        .nav-subitem {
          .nav-link {
            padding: var(--spacing-xs) var(--spacing-md);
            margin: 1px var(--spacing-sm);
            font-size: var(--text-sm);
            
            .nav-icon {
              width: 16px;
              height: 16px;
              font-size: 14px;
            }
            
            &::before {
              content: '•';
              position: absolute;
              left: var(--spacing-md);
              color: var(--text-light);
              font-size: 12px;
            }
          }
        }
      }
      
      &.has-submenu {
        .nav-link.active ~ .nav-submenu {
          max-height: 300px;
          padding-top: var(--spacing-xs);
          
          .nav-arrow {
            transform: rotate(90deg);
          }
        }
      }
    }
    
    .sidebar-footer {
      padding: var(--spacing-md);
      border-top: 1px solid var(--border-light);
      background: var(--bg-secondary);
      
      .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
        
        .quick-action {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-sm);
          background: white;
          border-radius: var(--radius-lg);
          text-decoration: none;
          color: var(--text-secondary);
          transition: all var(--duration-normal) var(--ease-out);
          border: 1px solid var(--border-light);
          
          &:hover {
            background: var(--bg-secondary);
            border-color: var(--color-primary);
            color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }
          
          .action-icon {
            font-size: 16px;
          }
          
          .action-text {
            font-size: 11px;
            font-weight: var(--font-medium);
            text-align: center;
          }
        }
      }
    }
  }
  
  /* 面包屑导航 */
  .navigation-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    
    .breadcrumb-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      
      .breadcrumb-link {
        color: var(--text-muted);
        text-decoration: none;
        font-size: var(--text-sm);
        transition: color var(--duration-normal) var(--ease-out);
        
        &:hover {
          color: var(--color-primary);
        }
        
        &.active {
          color: var(--text-primary);
          font-weight: var(--font-medium);
        }
      }
      
      .breadcrumb-separator {
        color: var(--text-light);
        font-size: 12px;
      }
      
      &:last-child .breadcrumb-separator {
        display: none;
      }
    }
  }
  
  /* 移动端导航 */
  .navigation-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-border);
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
    z-index: var(--z-fixed);
    
    .mobile-nav-items {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: var(--spacing-sm) 0;
      
      .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
        min-width: 60px;
        text-decoration: none;
        color: var(--text-muted);
        transition: all var(--duration-normal) var(--ease-out);
        
        &:hover,
        &.active {
          color: var(--color-primary);
          transform: translateY(-2px);
        }
        
        .mobile-nav-icon {
          font-size: 20px;
          transition: all var(--duration-normal) var(--ease-out);
        }
        
        .mobile-nav-text {
          font-size: 10px;
          font-weight: var(--font-medium);
          line-height: 1;
        }
        
        .mobile-nav-badge {
          position: absolute;
          top: -2px;
          right: 8px;
          min-width: 16px;
          height: 16px;
          background: var(--color-danger);
          color: white;
          border-radius: var(--radius-full);
          font-size: 9px;
          font-weight: var(--font-bold);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        &.active .mobile-nav-icon {
          transform: scale(1.1);
        }
      }
    }
  }
}

/* 导航相关的Vue过渡动画 */
.nav-fade-enter-active,
.nav-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.nav-fade-enter-from,
.nav-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.nav-slide-enter-active,
.nav-slide-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.nav-slide-enter-from,
.nav-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.nav-expand-enter-active,
.nav-expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.nav-expand-enter-from,
.nav-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.nav-expand-enter-to,
.nav-expand-leave-from {
  max-height: 300px;
  opacity: 1;
}

/* 响应式适配 */
@include mobile {
  .modern-navigation {
    .navigation-header {
      padding: var(--spacing-sm);
      
      .header-search {
        max-width: 200px;
      }
      
      .user-info {
        display: none;
      }
    }
    
    .navigation-sidebar {
      &:not(.collapsed) {
        width: 100vw;
      }
    }
  }
}

@include tablet {
  .modern-navigation {
    .navigation-header {
      .header-search {
        max-width: 300px;
      }
    }
  }
}