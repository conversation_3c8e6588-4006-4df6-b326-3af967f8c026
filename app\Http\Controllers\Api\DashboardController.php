<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\CommissionLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * 仪表板控制器
 * 提供各种统计数据和图表数据
 */
class DashboardController extends Controller
{
    /**
     * 获取仪表板统计数据
     */
    public function getStats(Request $request)
    {
        $user = $request->user();
        
        // 根据用户角色返回不同的统计数据
        switch ($user->role) {
            case 'admin':
                return $this->getAdminStats();
            case 'substation':
                return $this->getSubstationStats($user);
            case 'distributor':
                return $this->getDistributorStats($user);
            default:
                return $this->getUserStats($user);
        }
    }

    /**
     * 管理员统计数据
     */
    private function getAdminStats()
    {
        $cacheKey = 'admin_dashboard_stats';
        
        $stats = Cache::remember($cacheKey, 300, function () {
            return [
                'users' => [
                    'total' => User::count(),
                    'today' => User::whereDate('created_at', today())->count(),
                    'active' => User::where('status', User::STATUS_ACTIVE)->count(),
                    'distributors' => User::where('role', 'distributor')->count(),
                ],
                'groups' => [
                    'total' => WechatGroup::count(),
                    'active' => WechatGroup::where('status', 1)->count(),
                    'today' => WechatGroup::whereDate('created_at', today())->count(),
                ],
                'orders' => [
                    'total' => Order::count(),
                    'paid' => Order::where('status', Order::STATUS_PAID_INT)->count(),
                    'today' => Order::whereDate('created_at', today())->count(),
                    'today_paid' => Order::whereDate('created_at', today())
                                        ->where('status', Order::STATUS_PAID_INT)->count(),
                ],
                'revenue' => [
                    'total' => Order::where('status', Order::STATUS_PAID_INT)->sum('amount'),
                    'today' => Order::whereDate('created_at', today())
                                   ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
                    'this_month' => Order::whereMonth('created_at', now()->month)
                                        ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
                ],
                'commissions' => [
                    'total' => CommissionLog::where('status', 'settled')->sum('amount'),
                    'today' => CommissionLog::whereDate('created_at', today())
                                          ->where('status', 'settled')->sum('amount'),
                    'pending' => CommissionLog::where('status', 'pending')->sum('amount'),
                ],
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 分站管理员统计数据
     */
    private function getSubstationStats(User $user)
    {
        $substationId = $user->substation_id;
        
        $stats = [
            'users' => [
                'total' => User::where('substation_id', $substationId)->count(),
                'today' => User::where('substation_id', $substationId)
                              ->whereDate('created_at', today())->count(),
                'distributors' => User::where('substation_id', $substationId)
                                     ->where('role', 'distributor')->count(),
            ],
            'groups' => [
                'total' => WechatGroup::where('substation_id', $substationId)->count(),
                'active' => WechatGroup::where('substation_id', $substationId)
                                      ->where('status', 1)->count(),
            ],
            'orders' => [
                'total' => Order::where('substation_id', $substationId)->count(),
                'paid' => Order::where('substation_id', $substationId)
                              ->where('status', Order::STATUS_PAID_INT)->count(),
                'today' => Order::where('substation_id', $substationId)
                               ->whereDate('created_at', today())->count(),
            ],
            'revenue' => [
                'total' => Order::where('substation_id', $substationId)
                               ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
                'today' => Order::where('substation_id', $substationId)
                               ->whereDate('created_at', today())
                               ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 分销商统计数据
     */
    private function getDistributorStats(User $user)
    {
        $stats = [
            'groups' => [
                'total' => $user->wechatGroups()->count(),
                'active' => $user->wechatGroups()->where('status', 1)->count(),
            ],
            'orders' => [
                'total' => Order::where('user_id', $user->id)->count(),
                'paid' => Order::where('user_id', $user->id)
                              ->where('status', Order::STATUS_PAID_INT)->count(),
            ],
            'commissions' => [
                'total' => $user->commissionLogs()->where('status', 'settled')->sum('amount'),
                'today' => $user->commissionLogs()->whereDate('created_at', today())
                                                  ->where('status', 'settled')->sum('amount'),
                'pending' => $user->commissionLogs()->where('status', 'pending')->sum('amount'),
            ],
            'referrals' => [
                'total' => $user->children()->count(),
                'active' => $user->children()->where('status', User::STATUS_ACTIVE)->count(),
            ],
            'balance' => [
                'available' => $user->balance,
                'frozen' => $user->frozen_balance,
                'total_withdraw' => $user->withdraw_total,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 普通用户统计数据
     */
    private function getUserStats(User $user)
    {
        $stats = [
            'orders' => [
                'total' => $user->orders()->count(),
                'paid' => $user->orders()->where('status', Order::STATUS_PAID_INT)->count(),
                'pending' => $user->orders()->where('status', Order::STATUS_PENDING_INT)->count(),
            ],
            'groups' => [
                'joined' => $user->orders()->where('status', Order::STATUS_PAID_INT)->count(),
            ],
            'balance' => [
                'available' => $user->balance,
                'total_spent' => $user->orders()->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 获取图表数据
     */
    public function getChartData(Request $request)
    {
        $type = $request->input('type', 'revenue');
        $period = $request->input('period', '7d');
        $user = $request->user();

        switch ($type) {
            case 'revenue':
            case 'income-trend':
                return $this->getRevenueChartData($period, $user);
            case 'orders':
                return $this->getOrdersChartData($period, $user);
            case 'users':
            case 'user-growth':
                return $this->getUsersChartData($period, $user);
            case 'order-source':
                return $this->getOrderSourceData($user);
            default:
                return $this->getDefaultChartData($user);
        }
    }

    /**
     * 获取订单来源分布数据
     */
    private function getOrderSourceData(User $user): \Illuminate\Http\JsonResponse
    {
        $cacheKey = 'order_source_data_' . $user->id;
        
        $data = Cache::remember($cacheKey, 1800, function () use ($user) {
            $query = Order::where('status', Order::STATUS_PAID_INT);
            
            // 根据用户角色过滤数据
            if ($user->role === 'substation') {
                $query->where('substation_id', $user->substation_id);
            } elseif ($user->role === 'distributor') {
                $query->where('user_id', $user->id);
            }
            
            // 获取支付方式分布
            $paymentMethods = $query->select('payment_method', DB::raw('COUNT(*) as count'))
                                   ->groupBy('payment_method')
                                   ->get();
            
            $labels = [];
            $data = [];
            $colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
            
            foreach ($paymentMethods as $index => $method) {
                $methodName = match($method->payment_method) {
                    'wechat' => '微信支付',
                    'alipay' => '支付宝',
                    'payoreo' => '易支付',
                    'qqpay' => 'QQ钱包',
                    'bank' => '银行卡',
                    default => '其他'
                };
                
                $labels[] = $methodName;
                $data[] = $method->count;
            }
            
            return [
                'labels' => $labels,
                'datasets' => [[
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data))
                ]]
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * 获取默认图表数据（综合数据）
     */
    private function getDefaultChartData(User $user): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'income' => $this->getRevenueChartData('7d', $user)->getData()->data,
                'order_source' => $this->getOrderSourceData($user)->getData()->data,
            ],
        ]);
    }

    /**
     * 收入图表数据
     */
    private function getRevenueChartData(string $period, User $user)
    {
        $days = $this->getPeriodDays($period);
        $query = Order::where('status', Order::STATUS_PAID_INT);

        // 根据用户角色过滤数据
        if ($user->role === 'substation') {
            $query->where('substation_id', $user->substation_id);
        } elseif ($user->role === 'distributor') {
            $query->where('user_id', $user->id);
        } elseif ($user->role === 'user') {
            $query->where('user_id', $user->id);
        }

        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = (clone $query)->whereDate('created_at', $date)->sum('amount');
            
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'value' => (float) $revenue,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * 订单图表数据
     */
    private function getOrdersChartData(string $period, User $user)
    {
        $days = $this->getPeriodDays($period);
        $query = Order::query();

        // 根据用户角色过滤数据
        if ($user->role === 'substation') {
            $query->where('substation_id', $user->substation_id);
        } elseif ($user->role === 'distributor') {
            $query->where('user_id', $user->id);
        } elseif ($user->role === 'user') {
            $query->where('user_id', $user->id);
        }

        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $orders = (clone $query)->whereDate('created_at', $date)->count();
            
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'value' => $orders,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * 用户图表数据（仅管理员）
     */
    private function getUsersChartData(string $period, User $user)
    {
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $days = $this->getPeriodDays($period);
        $data = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $users = User::whereDate('created_at', $date)->count();
            
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'value' => $users,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * 获取周期对应的天数
     */
    private function getPeriodDays(string $period): int
    {
        return match ($period) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            default => 7,
        };
    }

    /**
     * 获取实时数据
     */
    public function getRealTimeData(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $data = [
            'online_users' => $this->getOnlineUsersCount(),
            'pending_orders' => Order::where('status', Order::STATUS_PENDING_INT)->count(),
            'today_revenue' => Order::whereDate('created_at', today())
                                   ->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'system_load' => $this->getSystemLoad(),
        ];

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * 获取在线用户数
     */
    private function getOnlineUsersCount(): int
    {
        return Cache::remember('online_users_count', 60, function () {
            // 基于最近5分钟内有活动的用户统计
            $activeUsers = User::where('last_activity_at', '>=', now()->subMinutes(5))->count();
            
            // 如果没有last_activity_at字段，可以基于最近登录时间
            if ($activeUsers === 0) {
                $activeUsers = User::where('last_login_at', '>=', now()->subHour())->count();
            }
            
            return max($activeUsers, 1); // 至少显示1个在线用户
        });
    }

    /**
     * 获取系统负载
     */
    private function getSystemLoad(): array
    {
        $load = [];
        
        // CPU使用率（Linux系统）
        if (function_exists('sys_getloadavg')) {
            $loadAvg = sys_getloadavg();
            $load['cpu'] = round($loadAvg[0] * 100 / 4, 1); // 假设4核CPU
        } else {
            $load['cpu'] = 0;
        }
        
        // 内存使用率
        if (function_exists('memory_get_usage')) {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseSize(ini_get('memory_limit'));
            $load['memory'] = $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 1) : 0;
        } else {
            $load['memory'] = 0;
        }
        
        // 磁盘使用率
        $diskTotal = disk_total_space('.');
        $diskFree = disk_free_space('.');
        if ($diskTotal && $diskFree) {
            $load['disk'] = round((($diskTotal - $diskFree) / $diskTotal) * 100, 1);
        } else {
            $load['disk'] = 0;
        }
        
        return $load;
    }

    /**
     * 解析内存大小字符串
     */
    private function parseSize(string $size): int
    {
        $unit = strtolower(substr($size, -1));
        $value = (int) $size;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    // ==================== 前端调用的缺失方法 ====================

    /**
     * 获取收入趋势数据 (前端调用: /api/v1/admin/dashboard/income-trend)
     */
    public function getIncomeTrend(Request $request)
    {
        $period = $request->get('period', '7d');
        return $this->getRevenueChartData($period, $request->user());
    }

    /**
     * 获取订单来源分布 (前端调用: /api/v1/admin/dashboard/order-source)
     */
    public function getOrderSourceDistribution(Request $request)
    {
        return $this->getOrderSourceData($request->user());
    }

    /**
     * 获取用户增长数据 (前端调用: /api/v1/admin/dashboard/user-growth)
     */
    public function getUserGrowthData(Request $request)
    {
        $period = $request->get('period', '7d');
        return $this->getUsersChartData($period, $request->user());
    }

    /**
     * 获取系统状态 (前端调用: /api/v1/admin/dashboard/system-status)
     */
    public function getSystemStatus(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $cacheKey = 'system_status';
        
        $status = Cache::remember($cacheKey, 300, function () {
            return [
                'database' => $this->checkDatabaseStatus(),
                'redis' => $this->checkRedisStatus(),
                'storage' => $this->checkStorageStatus(),
                'queue' => $this->checkQueueStatus(),
                'services' => [
                    'web_server' => 'running',
                    'database' => 'running',
                    'cache' => 'running',
                    'queue' => 'running',
                ],
                'performance' => $this->getSystemLoad(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }

    /**
     * 获取最近订单 (前端调用: /api/v1/admin/dashboard/recent-orders)
     */
    public function getRecentOrders(Request $request)
    {
        $user = $request->user();
        $limit = $request->get('limit', 10);
        
        $query = Order::with(['user:id,username,name', 'wechatGroup:id,title'])
                     ->orderBy('created_at', 'desc');

        // 根据用户角色过滤数据
        if ($user->role === 'substation') {
            $query->where('substation_id', $user->substation_id);
        } elseif ($user->role === 'distributor') {
            $query->where('user_id', $user->id);
        } elseif ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $orders = $query->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $orders,
        ]);
    }

    /**
     * 获取顶级分销商 (前端调用: /api/v1/admin/dashboard/top-distributors)
     */
    public function getTopDistributors(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $limit = $request->get('limit', 10);
        
        $cacheKey = 'top_distributors_' . $limit;
        
        $distributors = Cache::remember($cacheKey, 1800, function () use ($limit) {
            return User::where('role', 'distributor')
                      ->where('status', User::STATUS_ACTIVE)
                      ->withSum('commissionLogs as total_commission', 'amount')
                      ->withCount('children as referral_count')
                      ->withCount(['orders as order_count' => function ($query) {
                          $query->where('status', Order::STATUS_PAID_INT);
                      }])
                      ->orderBy('total_commission', 'desc')
                      ->limit($limit)
                      ->get(['id', 'name', 'username', 'avatar', 'created_at']);
        });

        return response()->json([
            'success' => true,
            'data' => $distributors,
        ]);
    }

    // ==================== 系统状态检查辅助方法 ====================

    /**
     * 检查数据库状态
     */
    private function checkDatabaseStatus(): array
    {
        try {
            DB::connection()->getPdo();
            $connectionTime = microtime(true);
            DB::select('SELECT 1');
            $queryTime = microtime(true) - $connectionTime;
            
            return [
                'status' => 'healthy',
                'response_time' => round($queryTime * 1000, 2) . 'ms',
                'connection' => 'active',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'connection' => 'failed',
            ];
        }
    }

    /**
     * 检查Redis状态
     */
    private function checkRedisStatus(): array
    {
        try {
            $redis = app('redis');
            $startTime = microtime(true);
            $redis->ping();
            $responseTime = microtime(true) - $startTime;
            
            return [
                'status' => 'healthy',
                'response_time' => round($responseTime * 1000, 2) . 'ms',
                'connection' => 'active',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'connection' => 'failed',
            ];
        }
    }

    /**
     * 检查存储状态
     */
    private function checkStorageStatus(): array
    {
        $storagePath = storage_path();
        $totalSpace = disk_total_space($storagePath);
        $freeSpace = disk_free_space($storagePath);
        $usedSpace = $totalSpace - $freeSpace;
        $usagePercent = round(($usedSpace / $totalSpace) * 100, 1);
        
        return [
            'status' => $usagePercent > 90 ? 'warning' : 'healthy',
            'total_space' => $this->formatBytes($totalSpace),
            'free_space' => $this->formatBytes($freeSpace),
            'used_space' => $this->formatBytes($usedSpace),
            'usage_percent' => $usagePercent,
        ];
    }

    /**
     * 检查队列状态
     */
    private function checkQueueStatus(): array
    {
        try {
            // 检查队列连接
            $connection = app('queue')->connection();
            
            // 获取待处理任务数量（如果使用Redis队列）
            $pendingJobs = 0;
            if (config('queue.default') === 'redis') {
                $redis = app('redis');
                $pendingJobs = $redis->llen('queues:default');
            }
            
            return [
                'status' => 'healthy',
                'connection' => 'active',
                'pending_jobs' => $pendingJobs,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'connection' => 'failed',
            ];
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}