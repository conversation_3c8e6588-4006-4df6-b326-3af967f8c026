<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AntiBlockService;
use App\Models\DomainPool;
use Illuminate\Support\Facades\Log;

/**
 * 域名检测定时任务
 * 自动检测域名状态，发现异常及时处理
 */
class CheckDomainsCommand extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'domains:check 
                          {--limit=20 : 每次检测的域名数量}
                          {--force : 强制检测所有域名}';

    /**
     * 命令描述
     */
    protected $description = '检测域名池中域名的健康状态';

    /**
     * 防红服务
     */
    protected $antiBlockService;

    /**
     * 构造函数
     */
    public function __construct(AntiBlockService $antiBlockService)
    {
        parent::__construct();
        $this->antiBlockService = $antiBlockService;
    }

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始检测域名状态...');
        
        $limit = $this->option('limit');
        $force = $this->option('force');
        
        if ($force) {
            // 强制检测所有域名
            $domains = DomainPool::where('status', '!=', DomainPool::STATUS_MAINTENANCE)
                ->orderBy('last_check_time', 'asc')
                ->limit($limit)
                ->get();
        } else {
            // 只检测需要检测的域名
            $domains = DomainPool::getNeedCheckDomains($limit);
        }

        if ($domains->count() === 0) {
            $this->info('没有需要检测的域名');
            return;
        }

        $this->info("准备检测 {$domains->count()} 个域名");
        
        $results = [
            'success' => 0,
            'failed' => 0,
            'blocked' => 0,
            'switched' => 0,
        ];

        // 创建进度条
        $progressBar = $this->output->createProgressBar($domains->count());
        $progressBar->start();

        foreach ($domains as $domain) {
            try {
                // 检测域名状态
                $checkResults = $this->antiBlockService->checkDomainStatus($domain);
                
                $isHealthy = true;
                foreach ($checkResults as $result) {
                    if ($result['result'] !== 1) {
                        $isHealthy = false;
                        break;
                    }
                }

                if ($isHealthy) {
                    $results['success']++;
                    $this->line("\n✓ {$domain->domain} - 健康状态良好");
                } else {
                    // 检查是否需要切换该域名的短链接
                    $affectedLinks = $domain->shortLinks()->where('status', 1)->count();
                    
                    if ($affectedLinks > 0) {
                        $this->line("\n⚠ {$domain->domain} - 检测到异常，影响 {$affectedLinks} 个短链接");
                        
                        // 批量切换该域名的所有短链接
                        $switchedCount = $this->switchDomainLinks($domain);
                        $results['switched'] += $switchedCount;
                        
                        $this->line("   → 已切换 {$switchedCount} 个短链接到备用域名");
                    }

                    if ($domain->health_score === 0) {
                        $results['blocked']++;
                        $this->line("   → 域名已被标记为封禁状态");
                    } else {
                        $results['failed']++;
                        $this->line("   → 域名状态异常，健康评分: {$domain->health_score}");
                    }
                }

            } catch (\Exception $e) {
                $results['failed']++;
                $this->error("\n✗ {$domain->domain} - 检测失败: {$e->getMessage()}");
                Log::error('域名检测失败', [
                    'domain' => $domain->domain,
                    'error' => $e->getMessage(),
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        
        $this->line("\n");
        $this->info('域名检测完成！');
        $this->table(['状态', '数量'], [
            ['健康', $results['success']],
            ['异常', $results['failed']],
            ['封禁', $results['blocked']],
            ['切换链接', $results['switched']],
        ]);

        // 发送通知（如果有异常）
        if ($results['failed'] > 0 || $results['blocked'] > 0) {
            $this->sendAlert($results);
        }

        return 0;
    }

    /**
     * 切换域名的所有短链接
     */
    private function switchDomainLinks(DomainPool $domain): int
    {
        $links = $domain->shortLinks()->where('status', 1)->get();
        $switchedCount = 0;

        foreach ($links as $link) {
            try {
                if ($this->antiBlockService->switchDomainForLink($link)) {
                    $switchedCount++;
                }
            } catch (\Exception $e) {
                Log::error('短链接域名切换失败', [
                    'link_id' => $link->id,
                    'short_code' => $link->short_code,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $switchedCount;
    }

    /**
     * 发送异常告警
     */
    private function sendAlert(array $results): void
    {
        $message = "防红系统检测告警\n";
        $message .= "异常域名: {$results['failed']} 个\n";
        $message .= "封禁域名: {$results['blocked']} 个\n";
        $message .= "已切换短链接: {$results['switched']} 个\n";
        $message .= "检测时间: " . now()->format('Y-m-d H:i:s');

        // 这里可以集成短信、邮件、钉钉等通知方式
        Log::warning('防红系统告警', [
            'results' => $results,
            'message' => $message,
        ]);

        $this->warn('检测到域名异常，已记录告警日志');
    }
} 