<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\WechatGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * 微信群控制器 (V2.0 - 全功能对齐版)
 * <AUTHOR>
 * @date 2024-07-25
 */
class WechatGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        // 修改权限控制：允许分销员创建和管理自己的群组
        $this->middleware('role:admin')->only(['batchDelete']);
        // 对于 store, update, destroy, updateStatus 使用策略进行更细粒度的权限控制
    }

    /**
     * 获取微信群列表 (增强数据)
     */
    public function index(Request $request)
    {
        $query = WechatGroup::with(['user:id,nickname,avatar']);

        // 根据用户角色进行查询过滤
        $user = Auth::user();
        if ($user->hasRole('substation')) {
            $query->where('substation_id', $user->substation_id);
        } elseif ($user->hasRole('distributor') || $user->hasRole('user')) {
            $query->where('user_id', $user->id);
        }

        // 动态附加统计数据
        $query->withCount([
            'orders as total_orders',
            'orders as today_orders' => function ($q) {
                $q->whereDate('created_at', today());
            },
        ])->withSum([
            'orders as total_income' => function ($q) {
                $q->where('status', 'paid');
            }
        ], 'amount')->withSum([
            'orders as today_income' => function ($q) {
                $q->where('status', 'paid')->whereDate('created_at', today());
            }
        ], 'amount');

        // 筛选
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('price_range')) {
             $range = explode('-', $request->price_range);
             if (count($range) === 2) {
                 if ($range[1] === '') { // 201+
                     $query->where('price', '>=', $range[0]);
                 } else {
                     $query->whereBetween('price', [$range[0], $range[1]]);
                 }
             }
        }

        $groups = $query->orderBy('created_at', 'desc')
                       ->paginate($request->input('limit', 10));

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => $groups,
        ]);
    }

    /**
     * 获取全局群组统计数据
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        $queryBuilder = WechatGroup::query();
        $orderQueryBuilder = \App\Models\Order::query()->where('status', 'paid');

        // 根据角色限定统计范围
        if ($user->hasRole('substation')) {
            $queryBuilder->where('substation_id', $user->substation_id);
            $orderQueryBuilder->whereIn('wechat_group_id', function($q) use ($user) {
                $q->select('id')->from('wechat_groups')->where('substation_id', $user->substation_id);
            });
        } elseif ($user->hasRole('distributor') || $user->hasRole('user')) {
            $queryBuilder->where('user_id', $user->id);
            $orderQueryBuilder->where('user_id', $user->id);
        }

        $stats = Cache::remember('group_stats_' . $user->id, now()->addMinutes(5), function() use ($queryBuilder, $orderQueryBuilder) {
            return [
                'total' => $queryBuilder->count(),
                'active' => (clone $queryBuilder)->where('status', 'active')->count(),
                'today_income' => (clone $orderQueryBuilder)->whereDate('created_at', today())->sum('amount'),
                'pending_orders' => \App\Models\Order::where('status', 'pending')->whereIn('wechat_group_id', (clone $queryBuilder)->select('id'))->count(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 创建微信群 (优化版 V3.0)
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // 权限验证：只有管理员、分站管理员和分销员可以创建群组
        if (!$user->isAdmin() && !$user->isSubstation() && !$user->isDistributor()) {
            return response()->json(['success' => false, 'message' => '您没有权限创建群组'], 403);
        }

        // 增强的验证规则 - 15+字段
        $validator = Validator::make($request->all(), [
            // 基础信息
            'title' => 'required|string|max:200',
            'price' => 'required|numeric|min:0|max:99999',
            'member_limit' => 'required|integer|min:1|max:500',
            'category' => 'required|string|in:startup,finance,tech,education,other',
            
            // 群主信息
            'owner_name' => 'required|string|max:50',
            'owner_avatar' => 'nullable|string|max:500',
            'owner_display' => 'nullable|boolean',
            
            // 群组详情
            'description' => 'nullable|string|max:1000',
            'rules' => 'required|string|max:2000',
            'introduction' => 'nullable|string|max:1000',
            'announcement' => 'nullable|string|max:2000',
            
            // 营销配置
            'virtual_members' => 'nullable|integer|min:0|max:500',
            'current_members' => 'nullable|integer|min:0',
            'keywords' => 'nullable|string|max:200',
            'sort_order' => 'nullable|integer|min:0',
            
            // 展示配置
            'status' => 'nullable|in:active,inactive,full',
            'city_location' => 'nullable|boolean',
            'hot_display' => 'nullable|boolean',
            
            // 支付配置
            'payment_methods' => 'nullable|array',
            'payment_methods.*' => 'in:wechat,alipay,bank,epay',
            
            // 模板和媒体
            'template_id' => 'nullable|exists:group_templates,id',
            'cover_image' => 'nullable|string|max:500',
            'qr_code' => 'nullable|string|max:500',
            
            // 自定义字段
            'custom_fields' => 'nullable|array',
            'custom_fields.*.key' => 'required_with:custom_fields|string',
            'custom_fields.*.value' => 'required_with:custom_fields|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false, 
                'message' => '数据验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 使用事务确保数据一致性
            $group = DB::transaction(function () use ($request, $user) {
                // 生成唯一群组编号
                $groupNumber = $this->generateGroupNumber();
                
                // 准备数据
                $data = $request->only([
                    'title', 'description', 'price', 'member_limit', 'current_members',
                    'category', 'owner_name', 'owner_avatar', 'owner_display',
                    'rules', 'introduction', 'announcement', 'virtual_members',
                    'keywords', 'sort_order', 'payment_methods', 'city_location', 
                    'hot_display', 'template_id', 'custom_fields', 'cover_image', 'qr_code'
                ]);
                
                // 添加系统字段
                $data['group_number'] = $groupNumber;
                $data['user_id'] = $user->id;
                $data['substation_id'] = $user->substation_id ?? 1;
                
                // 应用模板数据（如果有）
                if ($request->has('template_id') && $request->template_id) {
                    $template = \App\Models\GroupTemplate::find($request->template_id);
                    if ($template) {
                        $data = $this->applyTemplateData($data, $template);
                    }
                }
                
                // 设置默认值
                $data['current_members'] = $data['current_members'] ?? 0;
                $data['virtual_members'] = $data['virtual_members'] ?? rand(50, 200);
                $data['city_location'] = $data['city_location'] ?? true;
                $data['owner_display'] = $data['owner_display'] ?? true;
                $data['hot_display'] = $data['hot_display'] ?? false;
                $data['payment_methods'] = $data['payment_methods'] ?? ['wechat', 'alipay'];
                
                // 转换状态值
                $statusMap = [
                    'active' => 1,
                    'inactive' => 2,
                    'full' => 3
                ];
                $requestStatus = $request->input('status');
                $data['status'] = $requestStatus ? ($statusMap[$requestStatus] ?? 1) : 1;
                
                // 处理城市替换标记
                if (strpos($data['title'], 'xxx') !== false) {
                    $data['auto_city_replace'] = 1;
                }
                
                // 创建群组
                $group = WechatGroup::create($data);
                
                // 初始化虚拟成员（如果配置了）
                if ($data['virtual_members'] > 0) {
                    $this->initializeVirtualMembers($group->id, $data['virtual_members']);
                }
                
                // 记录操作日志
                \App\Models\OperationLog::create([
                    'user_id' => $user->id,
                    'action' => 'create_group',
                    'target_type' => 'wechat_group',
                    'target_id' => $group->id,
                    'description' => "创建群组：{$group->title}",
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                
                return $group;
            });
            
            // 清除缓存
            Cache::forget('group_stats_' . $user->id);
            
            // 触发群组创建事件（可选）
            // event(new \App\Events\GroupCreated($group));
            
            return response()->json([
                'success' => true, 
                'message' => '群组创建成功', 
                'data' => $group->load('user:id,nickname,avatar'),
                'group_number' => $group->group_number
            ], 201);
            
        } catch (\Exception $e) {
            Log::error('群组创建失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '群组创建失败，请稍后重试',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }
    
    /**
     * 生成唯一群组编号
     */
    private function generateGroupNumber()
    {
        do {
            $number = 'GRP' . date('YmdHis') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        } while (WechatGroup::where('group_number', $number)->exists());
        
        return $number;
    }
    
    /**
     * 应用模板数据
     */
    private function applyTemplateData($data, $template)
    {
        // 从模板继承默认配置
        $templateConfig = $template->template_config ?? [];
        
        if (empty($data['description']) && !empty($templateConfig['description'])) {
            $data['description'] = $templateConfig['description'];
        }
        
        if (empty($data['rules']) && !empty($templateConfig['rules'])) {
            $data['rules'] = $templateConfig['rules'];
        }
        
        if (empty($data['introduction']) && !empty($templateConfig['introduction'])) {
            $data['introduction'] = $templateConfig['introduction'];
        }
        
        if (empty($data['announcement']) && !empty($templateConfig['announcement'])) {
            $data['announcement'] = $templateConfig['announcement'];
        }
        
        // 更新使用次数
        $template->increment('usage_count');
        
        return $data;
    }
    
    /**
     * 初始化虚拟成员
     */
    private function initializeVirtualMembers($groupId, $count)
    {
        // 这里可以调用虚拟成员服务
        // VirtualMemberService::generate($groupId, $count);
        
        // 临时实现：记录虚拟成员数量
        Log::info("为群组 {$groupId} 初始化了 {$count} 个虚拟成员");
    }

    /**
     * 获取微信群详情
     */
    public function show($id)
    {
        $group = WechatGroup::with(['user:id,nickname,avatar'])->withCount('members as joined_count')->find($id);

        if (!$group) {
            return response()->json(['code' => 404, 'message' => '微信群不存在'], 404);
        }
        
        return response()->json(['code' => 0, 'data' => $group]);
    }

    /**
     * 更新微信群
     */
    public function update(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '微信群不存在'], 404);
        }

        $user = Auth::user();
        
        // 权限验证：只有管理员、分站管理员和群主可以更新群组
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限修改此群组'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:200',
            'price' => 'sometimes|required|numeric|min:0',
            'member_limit' => 'sometimes|required|integer|min:1|max:500',
            'current_members' => 'sometimes|integer|min:0',
            'status' => 'sometimes|required|in:active,inactive,full',
            'description' => 'sometimes|nullable|string',
            'city' => 'sometimes|nullable|string',
            'keywords' => 'sometimes|nullable|string',
            'sort_order' => 'sometimes|nullable|integer|min:0',
            'payment_methods' => 'sometimes|array',
            'payment_methods.*' => 'in:wechat,alipay,bank',
            'city_location' => 'sometimes|boolean',
            'owner_display' => 'sometimes|boolean',
            'hot_display' => 'sometimes|boolean',
            'template_id' => 'sometimes|string|in:default,business,entertainment,education',
            'custom_fields' => 'sometimes|nullable|array',
            'custom_fields.*.key' => 'required_with:custom_fields|string',
            'custom_fields.*.value' => 'required_with:custom_fields|string',
            'cover_image' => 'sometimes|nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }

        $data = $request->all();
        
        // 转换状态值
        if (isset($data['status'])) {
            $statusMap = [
                'active' => 1,
                'inactive' => 2,
                'full' => 3
            ];
            $data['status'] = $statusMap[$data['status']] ?? 1;
        }

        $group->update($data);

        return response()->json(['code' => 0, 'message' => '更新成功', 'data' => $group]);
    }

    /**
     * 删除微信群 (兼容单个删除)
     */
    public function destroy($id)
    {
        $group = WechatGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '微信群不存在'], 404);
        }

        $user = Auth::user();
        
        // 权限验证：只有管理员、分站管理员和群主可以删除群组
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限删除此群组'], 403);
        }

        if ($group->qr_code) Storage::disk('public')->delete($group->qr_code);
        $group->delete();

        return response()->json(['success' => true, 'message' => '删除成功']);
    }

    /**
     * 批量删除微信群
     */
    public function batchDelete(Request $request)
    {
        $ids = $request->validate(['ids' => 'required|array'])['ids'];
        $groups = WechatGroup::whereIn('id', $ids)->get();

        foreach($groups as $group) {
            if ($group->qr_code) Storage::disk('public')->delete($group->qr_code);
            $group->delete();
        }

        return response()->json(['code' => 0, 'message' => '批量删除成功']);
    }

    /**
     * 上传群封面图片
     */
    public function uploadCover(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cover' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        try {
            $file = $request->file('cover');
            $path = $file->store('wechat_groups/covers', 'public');
            $url = Storage::disk('public')->url($path);

            return response()->json([
                'success' => true,
                'message' => '上传成功',
                'data' => [
                    'url' => $url,
                    'path' => $path
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => '上传失败：' . $e->getMessage()], 500);
        }
    }


    /**
     * 更新微信群状态
     */
    public function updateStatus(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        if (!$group) {
            return response()->json(['code' => 404, 'message' => '微信群不存在'], 404);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,inactive,full',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }

        $group->status = $request->status;
        $group->save();

        return response()->json(['code' => 0, 'message' => '状态更新成功', 'data' => $group]);
    }

    /**
     * 获取群组整体统计数据 (前端调用: /api/v1/admin/groups/stats)
     */
    public function getGroupStats(Request $request)
    {
        $user = $request->user();
        $cacheKey = 'group_stats_' . $user->id . '_' . $user->role;
        
        $stats = Cache::remember($cacheKey, 300, function () use ($user) {
            $query = WechatGroup::query();
            
            // 根据用户角色过滤数据
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            } elseif ($user->hasRole('distributor') || $user->hasRole('user')) {
                $query->where('user_id', $user->id);
            }
            
            $totalGroups = $query->count();
            $activeGroups = (clone $query)->where('status', 1)->count();
            $todayGroups = (clone $query)->whereDate('created_at', today())->count();
            
            // 计算总成员数
            $totalMembers = (clone $query)->sum('current_members');
            
            // 计算总收入
            $totalRevenue = Order::whereIn('wechat_group_id', 
                (clone $query)->pluck('id')
            )->where('status', Order::STATUS_PAID_INT)->sum('amount');
            
            return [
                'total_groups' => $totalGroups,
                'active_groups' => $activeGroups,
                'today_groups' => $todayGroups,
                'total_members' => $totalMembers,
                'total_revenue' => $totalRevenue,
                'paused_groups' => (clone $query)->where('status', 0)->count(),
                'full_groups' => (clone $query)->where('current_members', '>=', DB::raw('max_members'))->count(),
                'avg_price' => (clone $query)->avg('price'),
                'avg_members' => $totalGroups > 0 ? round($totalMembers / $totalGroups, 1) : 0,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 获取单个群组的统计数据
     */
    public function getStats($id)
    {
        $today = today();
        $yesterday = $today->copy()->subDay();
        
        $stats = [
            'total_orders' => Order::where('wechat_group_id', $id)->count(),
            'paid_orders' => Order::where('wechat_group_id', $id)->where('status', Order::STATUS_PAID_INT)->count(),
            'total_income' => Order::where('wechat_group_id', $id)->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'today_orders' => Order::where('wechat_group_id', $id)->whereDate('created_at', $today)->count(),
            'today_income' => Order::where('wechat_group_id', $id)->whereDate('created_at', $today)->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'yesterday_orders' => Order::where('wechat_group_id', $id)->whereDate('created_at', $yesterday)->count(),
            'yesterday_income' => Order::where('wechat_group_id', $id)->whereDate('created_at', $yesterday)->where('status', Order::STATUS_PAID_INT)->sum('amount'),
        ];
        
        return response()->json(['code' => 0, 'data' => $stats]);
    }

    /**
     * 获取群组详细统计数据
     */
    public function getDetailedStats(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        if (!$group) {
            return response()->json(['success' => false, 'message' => '微信群不存在'], 404);
        }

        $user = Auth::user();
        
        // 权限验证：只有管理员、分站管理员和群主可以查看详细统计
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限查看此群组统计'], 403);
        }

        $period = $request->input('period', '7days'); // 7days, 30days, 90days, 1year
        
        $stats = [
            'basic' => [
                'total_orders' => $group->orders()->count(),
                'paid_orders' => $group->orders()->where('status', 'paid')->count(),
                'pending_orders' => $group->orders()->where('status', 'pending')->count(),
                'cancelled_orders' => $group->orders()->where('status', 'cancelled')->count(),
                'total_income' => $group->orders()->where('status', 'paid')->sum('amount'),
                'success_rate' => $this->calculateSuccessRate($group),
                'avg_order_value' => $this->calculateAvgOrderValue($group),
            ],
            'daily_stats' => $this->getDailyStats($group, $period),
            'payment_methods' => $this->getPaymentMethodStats($group),
            'hourly_distribution' => $this->getHourlyDistribution($group),
            'conversion_funnel' => $this->getConversionFunnel($group),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 计算成功率
     */
    private function calculateSuccessRate($group): float
    {
        $totalOrders = $group->orders()->count();
        if ($totalOrders === 0) return 0;
        
        $paidOrders = $group->orders()->where('status', 'paid')->count();
        return round(($paidOrders / $totalOrders) * 100, 2);
    }

    /**
     * 计算平均订单价值
     */
    private function calculateAvgOrderValue($group): float
    {
        $paidOrders = $group->orders()->where('status', 'paid');
        $totalIncome = $paidOrders->sum('amount');
        $orderCount = $paidOrders->count();
        
        return $orderCount > 0 ? round($totalIncome / $orderCount, 2) : 0;
    }

    /**
     * 获取每日统计数据
     */
    private function getDailyStats($group, $period): array
    {
        $days = match($period) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            '1year' => 365,
            default => 7
        };

        $stats = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dayStats = [
                'date' => $date,
                'orders' => $group->orders()->whereDate('created_at', $date)->count(),
                'income' => $group->orders()->whereDate('created_at', $date)->where('status', 'paid')->sum('amount'),
                'paid_orders' => $group->orders()->whereDate('created_at', $date)->where('status', 'paid')->count(),
            ];
            $stats[] = $dayStats;
        }

        return $stats;
    }

    /**
     * 获取支付方式统计
     */
    private function getPaymentMethodStats($group): array
    {
        $paymentStats = $group->orders()
            ->where('status', Order::STATUS_PAID_INT)
            ->select('payment_method')
            ->selectRaw('COUNT(*) as count, SUM(amount) as total')
            ->groupBy('payment_method')
            ->get()
            ->toArray();

        return $paymentStats;
    }

    /**
     * 获取小时分布统计
     */
    private function getHourlyDistribution($group): array
    {
        // 使用单次查询获取所有小时的数据，减少数据库查询次数
        $hourlyData = $group->orders()
            ->select(DB::raw('HOUR(created_at) as hour'))
            ->selectRaw('COUNT(*) as count')
            ->groupBy(DB::raw('HOUR(created_at)'))
            ->pluck('count', 'hour')
            ->toArray();
        
        // 填充所有24小时的数据
        $hourlyStats = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $hourlyStats[] = [
                'hour' => $hour,
                'count' => $hourlyData[$hour] ?? 0
            ];
        }

        return $hourlyStats;
    }

    /**
     * 获取转化漏斗数据
     */
    private function getConversionFunnel($group): array
    {
        // 这里可以根据实际业务逻辑来计算转化漏斗
        // 比如：访问 -> 点击支付 -> 支付成功
        return [
            'page_views' => $group->getCustomField('page_views', 0),
            'payment_attempts' => $group->orders()->count(),
            'successful_payments' => $group->orders()->where('status', 'paid')->count(),
        ];
    }

    /**
     * 获取指定群组的二维码URL
     */
    public function getGroupQrCode($id)
    {
        $group = WechatGroup::findOrFail($id);
        
        if ($group->qr_code) {
            return response()->json(['code' => 0, 'data' => ['image_url' => Storage::url($group->qr_code)]]);
        }
        
        return response()->json(['code' => 1, 'message' => '暂未上传二维码']);
    }

    /**
     * 为指定群组上传或更新二维码
     */
    public function updateGroupQrCode(Request $request, $id)
    {
        $request->validate([
            'qr_code_file' => 'required|image|max:2048',
        ]);
        
        $group = WechatGroup::findOrFail($id);
        
        // 删除旧文件
        if ($group->qr_code) {
            Storage::disk('public')->delete($group->qr_code);
        }

        // 存储新文件
        $path = $request->file('qr_code_file')->store('group_qrcodes', 'public');
        $group->update(['qr_code' => $path]);

        return response()->json([
            'code' => 0, 
            'message' => '二维码更新成功',
            'data' => [
                'image_url' => Storage::url($path)
            ]
        ]);
    }

    /**
     * 导出群组数据为Excel
     */
    public function export(Request $request)
    {
        // 此处复用列表的查询逻辑，但不分页
        $query = WechatGroup::with(['user:id,nickname'])->withCount('members as joined_count');
        
        if ($request->filled('title')) {
            $query->where('title', 'like', '%' . $request->title . '%');
        }
        if ($request->filled('status')) {
            $query->where('status', 'like', '%' . $request->status . '%');
        }

        $groups = $query->get();
        
        // 使用 FastExcel 导出
        return (new FastExcel($groups))->download('wechat_groups.xlsx', function ($group) {
            return [
                'ID' => $group->id,
                '群名称' => $group->title,
                '群主' => $group->user->nickname ?? 'N/A',
                '价格' => $group->price,
                '当前人数' => $group->joined_count,
                '最大人数' => $group->max_members,
                '状态' => $group->status,
                '创建时间' => $group->created_at->toDateTimeString(),
            ];
        });
    }

    /**
     * 获取群组内容管理数据
     */
    public function getGroupContent($id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        // 权限检查：只有管理员、分站管理员和群主可以查看内容
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限查看此群组内容'], 403);
        }

        $contentData = [
            'content' => [
                // 群组介绍
                'show_intro' => true,
                'group_intro_title' => $group->group_intro_title ?? '群简介',
                'group_intro_content' => $group->group_intro_content ?? '',
                'intro_position' => 'top',
                
                // FAQ
                'show_faq' => true,
                'faq_title' => $group->faq_title ?? '常见问题',
                'faq_style' => 'collapse',
                
                // 用户评论
                'show_reviews' => true,
                'reviews_title' => '用户评价',
                'reviews_count' => 6,
                'reviews_style' => 'card',
                
                // 扩展内容
                'show_extra' => true,
                'extra_title1' => $group->extra_title1 ?? '',
                'extra_content1' => $group->extra_content1 ?? '',
                'extra_title2' => $group->extra_title2 ?? '',
                'extra_content2' => $group->extra_content2 ?? '',
                
                // 素材
                'customer_service_qr' => $group->customer_service_qr ?? '',
                'ad_image' => $group->ad_image ? json_decode($group->ad_image, true) : []
            ],
            'faq_content' => $group->faq_content ?? '',
            'user_reviews' => $group->user_reviews ?? ''
        ];

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => $contentData
        ]);
    }

    /**
     * 更新群组营销配置
     */
    public function updateMarketingConfig(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        // 权限检查：只有管理员、分站管理员和群主可以修改内容
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限修改此群组'], 403);
        }

        $validator = Validator::make($request->all(), [
            // 营销展示字段
            'read_count_display' => 'nullable|string|max:20',
            'like_count' => 'nullable|integer|min:0',
            'want_see_count' => 'nullable|integer|min:0',
            'button_title' => 'nullable|string|max:100',
            'avatar_library' => 'nullable|string|in:qq,za',
            'wx_accessible' => 'nullable|integer|in:1,2',
            'display_type' => 'nullable|integer|in:1,2',
            
            // 内容设置字段
            'group_intro_title' => 'nullable|string|max:60',
            'group_intro_content' => 'nullable|string',
            'faq_title' => 'nullable|string|max:60',
            'faq_content' => 'nullable|string',
            'member_reviews' => 'nullable|string',
            
            // 客服广告字段
            'customer_service_qr' => 'nullable|string',
            'ad_qr_code' => 'nullable|string',
            'show_customer_service' => 'nullable|integer|in:1,2',
            'customer_service_avatar' => 'nullable|string',
            'customer_service_title' => 'nullable|string|max:100',
            'customer_service_desc' => 'nullable|string|max:500',
            
            // 城市定位设置
            'auto_city_replace' => 'nullable|integer|in:0,1',
            'city_insert_strategy' => 'nullable|string|in:auto,prefix,suffix,natural,none',
            
            // 虚拟数据设置
            'virtual_members' => 'nullable|integer|min:0',
            'virtual_orders' => 'nullable|integer|min:0',
            'virtual_income' => 'nullable|numeric|min:0',
            'today_views' => 'nullable|integer|min:0',
            'total_joins' => 'nullable|integer|min:0',
            
            // 扩展内容字段
            'extra_title1' => 'nullable|string|max:60',
            'extra_content1' => 'nullable|string',
            'extra_title2' => 'nullable|string|max:60',
            'extra_content2' => 'nullable|string',
            'extra_title3' => 'nullable|string|max:60',
            'extra_content3' => 'nullable|string',
            
            // 支付设置
            'specific_payments' => 'nullable|string',
            'payment_status' => 'nullable|integer|in:1,2',
            
            // 营销标签
            'marketing_tags' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        try {
            // 更新群组数据
            $updateData = $request->only([
                'read_count_display', 'like_count', 'want_see_count', 'button_title',
                'avatar_library', 'wx_accessible', 'display_type',
                'group_intro_title', 'group_intro_content', 'faq_title', 'faq_content', 'member_reviews',
                'customer_service_qr', 'ad_qr_code', 'show_customer_service',
                'customer_service_avatar', 'customer_service_title', 'customer_service_desc',
                'auto_city_replace', 'city_insert_strategy',
                'virtual_members', 'virtual_orders', 'virtual_income', 'today_views', 'total_joins',
                'extra_title1', 'extra_content1', 'extra_title2', 'extra_content2', 'extra_title3', 'extra_content3',
                'specific_payments', 'payment_status', 'marketing_tags'
            ]);

            // 更新最后活动时间
            $updateData['last_activity_at'] = now();

            $group->update($updateData);

            return response()->json([
                'success' => true,
                'message' => '营销配置更新成功',
                'data' => $group->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('更新群组营销配置失败', [
                'group_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成虚拟群友数据
     */
    public function generateVirtualMembers(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限操作此群组'], 403);
        }

        $validator = Validator::make($request->all(), [
            'count' => 'nullable|integer|min:1|max:50',
            'avatar_type' => 'nullable|string|in:qq,za',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        try {
            $count = $request->input('count', 13);
            $avatarType = $request->input('avatar_type', $group->avatar_library ?? 'qq');
            
            $members = $group->generateVirtualMembers($count);
            
            return response()->json([
                'success' => true,
                'message' => '虚拟成员数据生成成功',
                'data' => [
                    'members' => $members,
                    'count' => count($members),
                    'avatar_type' => $avatarType
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取群组营销配置
     */
    public function getMarketingConfig($id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限查看此群组'], 403);
        }

        try {
            $marketingConfig = [
                // 基础营销设置
                'basic' => [
                    'read_count_display' => $group->read_count_display ?? '10万+',
                    'like_count' => $group->like_count ?? 0,
                    'want_see_count' => $group->want_see_count ?? 0,
                    'button_title' => $group->button_title ?? '立即加入群聊',
                    'avatar_library' => $group->avatar_library ?? 'qq',
                    'wx_accessible' => $group->wx_accessible ?? 1,
                    'display_type' => $group->display_type ?? 1,
                ],
                
                // 内容设置
                'content' => [
                    'group_intro_title' => $group->group_intro_title ?? '群简介',
                    'group_intro_content' => $group->group_intro_content ?? '',
                    'faq_title' => $group->faq_title ?? '常见问题',
                    'faq_content' => $group->faq_content ?? '',
                    'member_reviews' => $group->member_reviews ?? '',
                    'formatted_faq' => $group->formatted_faq_content,
                    'formatted_reviews' => $group->formatted_member_reviews,
                ],
                
                // 客服广告设置
                'service' => [
                    'customer_service_qr' => $group->customer_service_qr ?? '',
                    'ad_qr_code' => $group->ad_qr_code ?? '',
                    'show_customer_service' => $group->show_customer_service ?? 1,
                    'customer_service_avatar' => $group->customer_service_avatar ?? '',
                    'customer_service_title' => $group->customer_service_title ?? 'VIP专属客服',
                    'customer_service_desc' => $group->customer_service_desc ?? '出现不能付款，不能入群等问题，请联系我！',
                ],
                
                // 城市定位设置
                'location' => [
                    'auto_city_replace' => $group->auto_city_replace ?? 1,
                    'city_insert_strategy' => $group->city_insert_strategy ?? 'auto',
                    'city_insert_strategy_name' => $group->getCityInsertStrategyName(),
                ],
                
                // 虚拟数据设置
                'virtual' => [
                    'virtual_members' => $group->virtual_members ?? 0,
                    'virtual_orders' => $group->virtual_orders ?? 0,
                    'virtual_income' => $group->virtual_income ?? 0,
                    'today_views' => $group->today_views ?? 0,
                    'total_joins' => $group->total_joins ?? 0,
                    'virtual_stats' => $group->virtual_stats,
                ],
                
                // 扩展内容
                'extra' => [
                    'extra_title1' => $group->extra_title1 ?? '',
                    'extra_content1' => $group->extra_content1 ?? '',
                    'extra_title2' => $group->extra_title2 ?? '',
                    'extra_content2' => $group->extra_content2 ?? '',
                    'extra_title3' => $group->extra_title3 ?? '',
                    'extra_content3' => $group->extra_content3 ?? '',
                ],
                
                // 支付设置
                'payment' => [
                    'specific_payments' => $group->specific_payments ?? '',
                    'payment_status' => $group->payment_status ?? 1,
                    'available_payments' => $group->getDetailedPaymentMethods(),
                ],
                
                // 营销标签
                'tags' => $group->marketing_tags ?? [],
            ];

            return response()->json([
                'success' => true,
                'message' => '获取营销配置成功',
                'data' => $marketingConfig
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新群组内容管理数据 - 基于源码完善版本
     */
    public function updateGroupContent(Request $request, $id)
    {
        // 重定向到营销配置更新方法
        return $this->updateMarketingConfig($request, $id);ntent2',
                'extra_title3', 'extra_content3'
            ];
            
            foreach ($extraFields as $field) {
                if ($request->has($field)) {
                    $updateData[$field] = $request->input($field);
                }
            }
            
            // 处理支付方式
            if ($request->has('payment_methods')) {
                $updateData['payment_methods'] = json_encode($request->payment_methods);
            }
            
            // 更新时间戳
            $updateData['updated_at'] = now();

            $group->update($updateData);

            // 记录操作日志
            \Log::info('群组内容更新', [
                'group_id' => $id,
                'user_id' => $user->id,
                'updated_fields' => array_keys($updateData)
            ]);

            return response()->json([
                'success' => true,
                'message' => '内容更新成功',
                'data' => $group->fresh()
            ]);

        } catch (\Exception $e) {
            \Log::error('群组内容更新失败', [
                'group_id' => $id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取群组成员列表
     */
    public function getGroupMembers(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        // 权限检查
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限查看此群组成员'], 403);
        }

        // 这里应该从实际的成员表中获取数据，目前返回模拟数据
        $members = collect([
            [
                'id' => 1,
                'username' => 'user001',
                'nickname' => '张三',
                'phone' => '138****1234',
                'avatar' => '',
                'joined_at' => '2024-01-15 10:30:00',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'username' => 'user002',
                'nickname' => '李四',
                'phone' => '139****5678',
                'avatar' => '',
                'joined_at' => '2024-01-16 14:20:00',
                'status' => 'active'
            ]
        ]);

        // 应用筛选
        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $members = $members->filter(function ($member) use ($keyword) {
                return str_contains($member['nickname'], $keyword) || 
                       str_contains($member['phone'], $keyword);
            });
        }

        if ($request->filled('status')) {
            $members = $members->where('status', $request->status);
        }

        // 分页处理
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 20);
        $total = $members->count();
        $members = $members->forPage($page, $limit)->values();

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => [
                'data' => $members,
                'total' => $total,
                'current_page' => $page,
                'per_page' => $limit
            ]
        ]);
    }

    /**
     * 获取群组数据分析
     */
    public function getGroupAnalytics(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        // 权限检查
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限查看此群组分析'], 403);
        }

        // 模拟分析数据
        $analyticsData = [
            'overview' => [
                'total_views' => 12580,
                'conversion_rate' => 15.6,
                'total_revenue' => 8960,
                'avg_stay_time' => 145
            ],
            'visit_trend' => [
                'labels' => ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20', '1/21'],
                'data' => [120, 190, 300, 500, 200, 300, 450]
            ],
            'traffic_sources' => [
                ['name' => '微信分享', 'value' => 45],
                ['name' => '直接访问', 'value' => 25],
                ['name' => '搜索引擎', 'value' => 20],
                ['name' => '其他', 'value' => 10]
            ],
            'device_distribution' => [
                ['name' => '移动端', 'value' => 70],
                ['name' => '桌面端', 'value' => 25],
                ['name' => '平板', 'value' => 5]
            ],
            'region_distribution' => [
                ['name' => '广东', 'count' => 2580, 'percentage' => 35],
                ['name' => '北京', 'count' => 1890, 'percentage' => 26],
                ['name' => '上海', 'count' => 1456, 'percentage' => 20],
                ['name' => '浙江', 'count' => 980, 'percentage' => 13],
                ['name' => '其他', 'count' => 456, 'percentage' => 6]
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => '获取成功',
            'data' => $analyticsData
        ]);
    }

    // ==================== 缺失的路由方法 ====================
    
    /**
     * 加入群组
     */
    public function join(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        if ($group->status !== 1) {
            return response()->json(['success' => false, 'message' => '群组已关闭'], 403);
        }

        $user = $request->user();
        
        // 检查是否已经加入
        if ($group->members()->where('user_id', $user->id)->exists()) {
            return response()->json(['success' => false, 'message' => '您已经加入了该群组'], 422);
        }

        // 添加成员
        $group->members()->create([
            'user_id' => $user->id,
            'joined_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => '成功加入群组',
            'data' => [
                'group_id' => $group->id,
                'group_title' => $group->title,
                'qr_code' => $group->qr_code,
            ]
        ]);
    }

    /**
     * 更新群组二维码
     */
    public function updateQrCode(Request $request, $id)
    {
        $request->validate([
            'qr_code' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $user = Auth::user();
        if (!$user->isAdmin() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '您没有权限修改此群组'], 403);
        }

        if ($request->hasFile('qr_code')) {
            $file = $request->file('qr_code');
            $filename = 'qr_' . $group->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('qr_codes', $filename, 'public');
            
            $group->update([
                'qr_code' => '/storage/' . $path
            ]);

            return response()->json([
                'success' => true,
                'message' => '二维码更新成功',
                'data' => [
                    'qr_code' => $group->qr_code
                ]
            ]);
        }

        return response()->json(['success' => false, 'message' => '二维码上传失败'], 422);
    }

    // ==================== 管理员方法 ====================
    
    /**
     * 管理员获取群组列表
     */
    public function adminIndex(Request $request)
    {
        $query = WechatGroup::with(['user:id,name,username'])
            ->withCount('orders')
            ->withSum('orders', 'amount');

        // 搜索过滤
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 用户过滤
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // 时间范围过滤
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $groups
        ]);
    }

    /**
     * 管理员查看群组详情
     */
    public function adminShow($id)
    {
        $group = WechatGroup::with(['user:id,name,username,phone'])
            ->withCount('orders')
            ->withSum('orders', 'amount')
            ->find($id);

        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $group
        ]);
    }

    /**
     * 管理员更新群组
     */
    public function adminUpdate(Request $request, $id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'price' => 'sometimes|numeric|min:0',
            'status' => 'sometimes|in:0,1',
            'category' => 'sometimes|string|max:100',
            'tags' => 'sometimes|string|max:500',
        ]);

        $group->update($request->only([
            'title', 'description', 'price', 'status', 'category', 'tags'
        ]));

        return response()->json([
            'success' => true,
            'message' => '群组更新成功',
            'data' => $group->fresh()
        ]);
    }

    /**
     * 管理员删除群组
     */
    public function adminDestroy($id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        // 检查是否有关联订单
        if ($group->orders()->exists()) {
            return response()->json(['success' => false, 'message' => '该群组有关联订单，无法删除'], 422);
        }

        $group->delete();

        return response()->json([
            'success' => true,
            'message' => '群组删除成功'
        ]);
    }

    /**
     * 批量操作群组
     */
    public function batchOperation(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,enable,disable',
            'ids' => 'required|array',
            'ids.*' => 'exists:wechat_groups,id',
        ]);

        $groups = WechatGroup::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'delete':
                // 检查是否有关联订单
                $hasOrders = $groups->whereHas('orders')->exists();
                if ($hasOrders) {
                    return response()->json(['success' => false, 'message' => '部分群组有关联订单，无法删除'], 422);
                }
                $groups->delete();
                $message = '批量删除成功';
                break;
                
            case 'enable':
                $groups->update(['status' => 1]);
                $message = '批量启用成功';
                break;
                
            case 'disable':
                $groups->update(['status' => 0]);
                $message = '批量禁用成功';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * 获取群组统计数据
     */
    public function statistics($id)
    {
        $group = WechatGroup::find($id);
        
        if (!$group) {
            return response()->json(['success' => false, 'message' => '群组不存在'], 404);
        }

        $stats = [
            'basic_info' => [
                'id' => $group->id,
                'title' => $group->title,
                'status' => $group->status,
                'created_at' => $group->created_at,
            ],
            'business_stats' => [
                'total_orders' => $group->orders()->count(),
                'paid_orders' => $group->orders()->where('status', 'paid')->count(),
                'total_revenue' => $group->orders()->where('status', 'paid')->sum('amount'),
                'avg_order_amount' => $group->orders()->where('status', 'paid')->avg('amount'),
            ],
            'member_stats' => [
                'total_members' => $group->members()->count(),
                'active_members' => $group->members()->where('status', 'active')->count(),
                'new_members_today' => $group->members()->whereDate('joined_at', today())->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 获取分站群组列表
     */
    public function substationGroups(Request $request)
    {
        $user = $request->user();
        
        if (!$user->substation_id) {
            return response()->json(['success' => false, 'message' => '您不属于任何分站'], 403);
        }

        $query = WechatGroup::whereHas('user', function($q) use ($user) {
            $q->where('substation_id', $user->substation_id);
        })->with(['user:id,name,username'])
          ->withCount('orders')
          ->withSum('orders', 'amount');

        // 搜索过滤
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $groups
        ]);
    }
}  
   /**
     * 生成虚拟群友数据
     */
    public function generateVirtualMembers(Request $request, $id)
    {
        $group = WechatGroup::findOrFail($id);
        
        // 检查权限
        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '无权限操作'], 403);
        }
        
        $count = $request->input('count', 13);
        $count = max(1, min(50, $count)); // 限制在1-50之间
        
        $members = $group->generateVirtualMembers($count);
        
        return response()->json([
            'success' => true,
            'message' => '虚拟群友数据生成成功',
            'data' => $members
        ]);
    }

    /**
     * 获取群组营销配置
     */
    public function getMarketingConfig(Request $request, $id)
    {
        $group = WechatGroup::findOrFail($id);
        
        // 检查权限
        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '无权限访问'], 403);
        }
        
        $config = [
            'basic' => [
                'read_count_display' => $group->read_count_display,
                'like_count' => $group->like_count,
                'want_see_count' => $group->want_see_count,
                'button_title' => $group->button_title,
                'avatar_library' => $group->avatar_library,
                'wx_accessible' => $group->wx_accessible,
                'display_type' => $group->display_type,
            ],
            'content' => [
                'group_intro_title' => $group->group_intro_title,
                'group_intro_content' => $group->group_intro_content,
                'faq_title' => $group->faq_title,
                'faq_content' => $group->faq_content,
                'member_reviews' => $group->member_reviews,
            ],
            'service' => [
                'show_customer_service' => $group->show_customer_service,
                'customer_service_avatar' => $group->customer_service_avatar,
                'customer_service_title' => $group->customer_service_title,
                'customer_service_desc' => $group->customer_service_desc,
                'customer_service_qr' => $group->customer_service_qr,
                'ad_qr_code' => $group->ad_qr_code,
            ],
            'advanced' => [
                'auto_city_replace' => $group->auto_city_replace,
                'city_insert_strategy' => $group->getCustomField('city_insert_strategy', 'auto'),
                'show_virtual_activity' => $group->show_virtual_activity,
                'show_member_avatars' => $group->show_member_avatars,
                'show_member_reviews' => $group->show_member_reviews,
                'show_real_time_stats' => $group->show_real_time_stats,
            ]
        ];
        
        return response()->json([
            'success' => true,
            'data' => $config
        ]);
    }

    /**
     * 预览群组展示效果
     */
    public function previewGroup(Request $request, $id)
    {
        $group = WechatGroup::findOrFail($id);
        
        // 检查权限
        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '无权限访问'], 403);
        }
        
        $userCity = $request->input('city', '本地');
        
        $preview = [
            'title' => $group->getCityReplacedTitle($userCity),
            'original_title' => $group->title,
            'subtitle' => $group->subtitle,
            'price' => $group->price,
            'read_count_display' => $group->read_count_display,
            'like_count' => $group->like_count,
            'want_see_count' => $group->want_see_count,
            'button_title' => $group->button_title ?: '立即加入群聊',
            'virtual_members' => $group->generateVirtualMembers(13),
            'formatted_faq' => $group->formatted_faq_content,
            'formatted_reviews' => $group->formatted_member_reviews,
            'virtual_stats' => $group->virtual_stats,
            'display_config' => [
                'show_virtual_activity' => $group->show_virtual_activity,
                'show_member_avatars' => $group->show_member_avatars,
                'show_member_reviews' => $group->show_member_reviews,
                'show_real_time_stats' => $group->show_real_time_stats,
                'display_type' => $group->display_type,
            ]
        ];
        
        return response()->json([
            'success' => true,
            'data' => $preview
        ]);
    }

    /**
     * 测试城市定位功能
     */
    public function testCityLocation(Request $request, $id)
    {
        $group = WechatGroup::findOrFail($id);
        
        $testCity = $request->input('test_city', '北京');
        $originalTitle = $group->title;
        $replacedTitle = $group->getCityReplacedTitle($testCity);
        
        return response()->json([
            'success' => true,
            'data' => [
                'test_city' => $testCity,
                'original_title' => $originalTitle,
                'replaced_title' => $replacedTitle,
                'auto_city_replace' => $group->auto_city_replace,
                'city_insert_strategy' => $group->getCustomField('city_insert_strategy', 'auto'),
                'strategy_name' => $group->getCityInsertStrategyName(),
            ]
        ]);
    }

    /**
     * 批量更新群组营销设置
     */
    public function batchUpdateMarketing(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_ids' => 'required|array',
            'group_ids.*' => 'integer|exists:wechat_groups,id',
            'marketing_config' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = Auth::user();
        $groupIds = $request->input('group_ids');
        $marketingConfig = $request->input('marketing_config');
        
        // 检查权限：只能批量更新自己的群组
        $groups = WechatGroup::whereIn('id', $groupIds);
        if (!$user->isAdmin()) {
            $groups->where('user_id', $user->id);
        }
        
        $updatedCount = $groups->update($marketingConfig);
        
        return response()->json([
            'success' => true,
            'message' => "成功更新 {$updatedCount} 个群组的营销配置",
            'data' => ['updated_count' => $updatedCount]
        ]);
    }

    /**
     * 获取营销模板
     */
    public function getMarketingTemplates()
    {
        $templates = [
            [
                'id' => 'business',
                'name' => '商务模板',
                'description' => '适合商务交流、职场学习类群组',
                'config' => [
                    'avatar_library' => 'za',
                    'display_type' => 1,
                    'button_title' => '立即加入商务群',
                    'group_intro_title' => '群简介',
                    'faq_title' => '常见问题',
                    'city_insert_strategy' => 'natural',
                ]
            ],
            [
                'id' => 'social',
                'name' => '社交模板',
                'description' => '适合交友、兴趣爱好类群组',
                'config' => [
                    'avatar_library' => 'qq',
                    'display_type' => 1,
                    'button_title' => '加入我们的大家庭',
                    'group_intro_title' => '关于我们',
                    'faq_title' => '新人必看',
                    'city_insert_strategy' => 'prefix',
                ]
            ],
            [
                'id' => 'education',
                'name' => '教育模板',
                'description' => '适合学习、培训类群组',
                'config' => [
                    'avatar_library' => 'za',
                    'display_type' => 1,
                    'button_title' => '开始学习之旅',
                    'group_intro_title' => '课程介绍',
                    'faq_title' => '学习指南',
                    'city_insert_strategy' => 'suffix',
                ]
            ]
        ];
        
        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * 应用营销模板
     */
    public function applyMarketingTemplate(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|string|in:business,social,education',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $group = WechatGroup::findOrFail($id);
        
        // 检查权限
        $user = Auth::user();
        if (!$user->isAdmin() && !$user->isSubstation() && $group->user_id !== $user->id) {
            return response()->json(['success' => false, 'message' => '无权限操作'], 403);
        }

        $templateId = $request->input('template_id');
        $templates = collect($this->getMarketingTemplates()->getData()->data);
        $template = $templates->firstWhere('id', $templateId);

        if (!$template) {
            return response()->json(['success' => false, 'message' => '模板不存在'], 404);
        }

        $group->update($template->config);

        return response()->json([
            'success' => true,
            'message' => '营销模板应用成功',
            'data' => $group->fresh()
        ]);
    }
}