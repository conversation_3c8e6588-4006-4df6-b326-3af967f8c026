/**
 * 分销员组合式API
 * 提供分销员相关的响应式状态管理和业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { distributorService } from '@/services/DistributorService'
import { 
  formatMoney, 
  formatNumber, 
  formatDate,
  getCustomerLevelInfo,
  getCustomerStatusInfo,
  debounce,
  localStorage
} from '@/utils/distributorUtils'

/**
 * 分销员统计数据管理
 * @returns {Object} 统计数据相关的响应式状态和方法
 */
export function useDistributorStats() {
  // 响应式状态
  const loading = ref(false)
  const error = ref(null)
  const stats = reactive({
    customers: {
      total: 0,
      active: 0,
      new_this_month: 0,
      trend: 0
    },
    groups: {
      total: 0,
      active: 0,
      members: 0,
      trend: 0
    },
    commission: {
      total: 0,
      this_month: 0,
      pending: 0,
      rate: 0,
      trend: 0
    },
    orders: {
      total: 0,
      amount: 0,
      pending: 0,
      success_rate: 0,
      trend: 0
    },
    updated_at: null
  })

  // 计算属性
  const formattedStats = computed(() => ({
    customers: {
      ...stats.customers,
      total_formatted: formatNumber(stats.customers.total),
      trend_formatted: `${stats.customers.trend > 0 ? '+' : ''}${stats.customers.trend}%`
    },
    groups: {
      ...stats.groups,
      total_formatted: formatNumber(stats.groups.total),
      members_formatted: formatNumber(stats.groups.members),
      trend_formatted: `${stats.groups.trend > 0 ? '+' : ''}${stats.groups.trend}%`
    },
    commission: {
      ...stats.commission,
      total_formatted: formatMoney(stats.commission.total),
      this_month_formatted: formatMoney(stats.commission.this_month),
      pending_formatted: formatMoney(stats.commission.pending),
      trend_formatted: `${stats.commission.trend > 0 ? '+' : ''}${stats.commission.trend}%`
    },
    orders: {
      ...stats.orders,
      total_formatted: formatNumber(stats.orders.total),
      amount_formatted: formatMoney(stats.orders.amount),
      success_rate_formatted: `${stats.orders.success_rate}%`,
      trend_formatted: `${stats.orders.trend > 0 ? '+' : ''}${stats.orders.trend}%`
    }
  }))

  const isDataStale = computed(() => {
    if (!stats.updated_at) return true
    const now = new Date()
    const updatedAt = new Date(stats.updated_at)
    const diffMinutes = (now - updatedAt) / (1000 * 60)
    return diffMinutes > 5 // 5分钟后认为数据过期
  })

  // 方法
  const loadStats = async (useCache = true) => {
    if (loading.value) return

    try {
      loading.value = true
      error.value = null

      console.log('🔄 开始加载统计数据...')
      const data = await distributorService.getDistributorStats(useCache)

      // 更新响应式状态
      Object.assign(stats.customers, data.customers)
      Object.assign(stats.groups, data.groups)
      Object.assign(stats.commission, data.commission)
      Object.assign(stats.orders, data.orders)
      stats.updated_at = data.updated_at

      console.log('✅ 统计数据加载完成')
    } catch (err) {
      console.error('❌ 加载统计数据失败:', err)
      error.value = err.message || '加载统计数据失败'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const refreshStats = () => {
    loadStats(false)
  }

  // 自动刷新逻辑
  let refreshTimer = null
  const startAutoRefresh = (interval = 5 * 60 * 1000) => { // 默认5分钟
    stopAutoRefresh()
    refreshTimer = setInterval(() => {
      if (isDataStale.value) {
        console.log('🔄 数据过期，自动刷新统计数据')
        refreshStats()
      }
    }, interval)
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 生命周期管理
  onMounted(() => {
    loadStats()
    startAutoRefresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    stats: readonly(stats),
    formattedStats,
    isDataStale,

    // 方法
    loadStats,
    refreshStats,
    startAutoRefresh,
    stopAutoRefresh
  }
}

/**
 * 客户管理
 * @returns {Object} 客户管理相关的响应式状态和方法
 */
export function useCustomerManagement() {
  // 响应式状态
  const loading = ref(false)
  const customers = ref([])
  const selectedCustomer = ref(null)
  const pagination = reactive({
    current_page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  })

  // 搜索和筛选
  const searchParams = reactive({
    search: '',
    level: '',
    status: '',
    date_range: []
  })

  // 计算属性
  const hasCustomers = computed(() => customers.value.length > 0)
  const isEmpty = computed(() => !loading.value && customers.value.length === 0)
  
  const customersByLevel = computed(() => {
    const groups = { A: [], B: [], C: [], D: [] }
    customers.value.forEach(customer => {
      const level = customer.level || 'C'
      if (groups[level]) {
        groups[level].push(customer)
      }
    })
    return groups
  })

  const customersByStatus = computed(() => {
    const groups = { active: [], inactive: [], potential: [], lost: [] }
    customers.value.forEach(customer => {
      const status = customer.status || 'potential'
      if (groups[status]) {
        groups[status].push(customer)
      }
    })
    return groups
  })

  // 方法
  const loadCustomers = async (params = {}) => {
    if (loading.value) return

    try {
      loading.value = true

      const queryParams = {
        page: pagination.current_page,
        limit: pagination.per_page,
        ...searchParams,
        ...params
      }

      console.log('🔄 正在加载客户列表...', queryParams)
      const response = await distributorService.getCustomers(queryParams)

      customers.value = response.data || []
      pagination.total = response.total || 0
      pagination.total_pages = response.total_pages || 0

      console.log('✅ 客户列表加载完成:', customers.value.length, '条记录')
    } catch (error) {
      console.error('❌ 加载客户列表失败:', error)
      customers.value = []
      ElMessage.error('加载客户列表失败')
    } finally {
      loading.value = false
    }
  }

  const getCustomerDetail = async (customerId) => {
    try {
      console.log('🔄 正在加载客户详情...', customerId)
      const customer = await distributorService.getCustomerDetail(customerId)
      selectedCustomer.value = customer
      console.log('✅ 客户详情加载完成:', customer.name)
      return customer
    } catch (error) {
      console.error('❌ 加载客户详情失败:', error)
      ElMessage.error('加载客户详情失败')
      throw error
    }
  }

  const createCustomer = async (customerData) => {
    try {
      console.log('🔄 正在创建客户...', customerData.name)
      const newCustomer = await distributorService.createCustomer(customerData)
      
      // 更新本地列表
      customers.value.unshift(newCustomer)
      pagination.total += 1

      console.log('✅ 客户创建成功:', newCustomer.name)
      return newCustomer
    } catch (error) {
      console.error('❌ 创建客户失败:', error)
      throw error
    }
  }

  const updateCustomer = async (customerId, customerData) => {
    try {
      console.log('🔄 正在更新客户...', customerId)
      const updatedCustomer = await distributorService.updateCustomer(customerId, customerData)
      
      // 更新本地列表
      const index = customers.value.findIndex(c => c.id === customerId)
      if (index !== -1) {
        customers.value[index] = updatedCustomer
      }

      // 更新选中的客户
      if (selectedCustomer.value && selectedCustomer.value.id === customerId) {
        selectedCustomer.value = updatedCustomer
      }

      console.log('✅ 客户更新成功:', updatedCustomer.name)
      return updatedCustomer
    } catch (error) {
      console.error('❌ 更新客户失败:', error)
      throw error
    }
  }

  const deleteCustomer = async (customerId) => {
    try {
      console.log('🔄 正在删除客户...', customerId)
      await distributorService.deleteCustomer(customerId)
      
      // 更新本地列表
      const index = customers.value.findIndex(c => c.id === customerId)
      if (index !== -1) {
        customers.value.splice(index, 1)
        pagination.total -= 1
      }

      // 清除选中的客户
      if (selectedCustomer.value && selectedCustomer.value.id === customerId) {
        selectedCustomer.value = null
      }

      console.log('✅ 客户删除成功')
      return true
    } catch (error) {
      console.error('❌ 删除客户失败:', error)
      throw error
    }
  }

  // 搜索防抖
  const debouncedSearch = debounce((keyword) => {
    searchParams.search = keyword
    pagination.current_page = 1
    loadCustomers()
  }, 500)

  const handleSearch = (keyword) => {
    debouncedSearch(keyword)
  }

  const handleFilter = (filterParams) => {
    Object.assign(searchParams, filterParams)
    pagination.current_page = 1
    loadCustomers()
  }

  const handlePageChange = (page) => {
    pagination.current_page = page
    loadCustomers()
  }

  const handlePageSizeChange = (size) => {
    pagination.per_page = size
    pagination.current_page = 1
    loadCustomers()
  }

  const refreshCustomers = () => {
    loadCustomers()
  }

  // 批量操作
  const batchUpdateLevel = async (customerIds, level) => {
    try {
      console.log('🔄 正在批量更新客户等级...', customerIds.length, '个客户')
      
      const promises = customerIds.map(id => 
        distributorService.updateCustomer(id, { level })
      )
      
      await Promise.all(promises)
      
      // 更新本地数据
      customers.value.forEach(customer => {
        if (customerIds.includes(customer.id)) {
          customer.level = level
          customer.level_text = getCustomerLevelInfo(level).name
        }
      })

      ElNotification.success({
        title: '批量更新成功',
        message: `已成功更新 ${customerIds.length} 个客户的等级`
      })

      console.log('✅ 批量更新客户等级成功')
    } catch (error) {
      console.error('❌ 批量更新客户等级失败:', error)
      ElMessage.error('批量更新失败')
      throw error
    }
  }

  const batchUpdateStatus = async (customerIds, status) => {
    try {
      console.log('🔄 正在批量更新客户状态...', customerIds.length, '个客户')
      
      const promises = customerIds.map(id => 
        distributorService.updateCustomer(id, { status })
      )
      
      await Promise.all(promises)
      
      // 更新本地数据
      customers.value.forEach(customer => {
        if (customerIds.includes(customer.id)) {
          customer.status = status
          customer.status_text = getCustomerStatusInfo(status).name
        }
      })

      ElNotification.success({
        title: '批量更新成功',
        message: `已成功更新 ${customerIds.length} 个客户的状态`
      })

      console.log('✅ 批量更新客户状态成功')
    } catch (error) {
      console.error('❌ 批量更新客户状态失败:', error)
      ElMessage.error('批量更新失败')
      throw error
    }
  }

  // 数据持久化
  const saveSearchParams = () => {
    localStorage.setItem('customer_search_params', searchParams)
  }

  const loadSearchParams = () => {
    const saved = localStorage.getItem('customer_search_params', {})
    Object.assign(searchParams, saved)
  }

  // 监听搜索参数变化，自动保存
  watch(searchParams, saveSearchParams, { deep: true })

  // 初始化
  onMounted(() => {
    loadSearchParams()
    loadCustomers()
  })

  return {
    // 状态
    loading: readonly(loading),
    customers: readonly(customers),
    selectedCustomer: readonly(selectedCustomer),
    pagination: readonly(pagination),
    searchParams,

    // 计算属性
    hasCustomers,
    isEmpty,
    customersByLevel,
    customersByStatus,

    // 方法
    loadCustomers,
    getCustomerDetail,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    handleSearch,
    handleFilter,
    handlePageChange,
    handlePageSizeChange,
    refreshCustomers,
    batchUpdateLevel,
    batchUpdateStatus
  }
}

/**
 * 图表数据管理
 * @returns {Object} 图表相关的响应式状态和方法
 */
export function useChartData() {
  // 响应式状态
  const loading = ref(false)
  const revenueData = ref({
    labels: [],
    datasets: []
  })
  const customerDistributionData = ref({
    labels: [],
    datasets: []
  })
  const period = ref('30d')

  // 图表配置
  const chartOptions = reactive({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        position: 'top'
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#409EFF',
        borderWidth: 1
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  })

  const doughnutOptions = reactive({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff'
      }
    }
  })

  // 方法
  const loadRevenueData = async (selectedPeriod = period.value) => {
    try {
      loading.value = true
      period.value = selectedPeriod

      console.log('🔄 正在加载收入图表数据...', selectedPeriod)

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      const mockData = {
        '7d': {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          data: [120, 190, 300, 500, 200, 300, 450]
        },
        '30d': {
          labels: Array.from({length: 30}, (_, i) => `${i + 1}日`),
          data: Array.from({length: 30}, () => Math.floor(Math.random() * 1000) + 200)
        },
        '90d': {
          labels: ['第1月', '第2月', '第3月'],
          data: [8000, 12000, 15000]
        }
      }

      const data = mockData[selectedPeriod]
      revenueData.value = {
        labels: data.labels,
        datasets: [{
          label: '佣金收入',
          data: data.data,
          borderColor: '#409EFF',
          backgroundColor: 'rgba(64, 158, 255, 0.1)',
          tension: 0.4,
          fill: true,
          pointBackgroundColor: '#409EFF',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }]
      }

      console.log('✅ 收入图表数据加载完成')
    } catch (error) {
      console.error('❌ 加载收入图表数据失败:', error)
      ElMessage.error('加载收入数据失败')
    } finally {
      loading.value = false
    }
  }

  const loadCustomerDistributionData = async () => {
    try {
      console.log('🔄 正在加载客户分布数据...')

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 300))

      const data = [
        Math.floor(Math.random() * 50) + 20,
        Math.floor(Math.random() * 60) + 30,
        Math.floor(Math.random() * 80) + 40,
        Math.floor(Math.random() * 40) + 15
      ]

      customerDistributionData.value = {
        labels: ['A级客户', 'B级客户', 'C级客户', 'D级客户'],
        datasets: [{
          data,
          backgroundColor: ['#F56C6C', '#E6A23C', '#409EFF', '#67C23A'],
          borderWidth: 0,
          hoverOffset: 4
        }]
      }

      console.log('✅ 客户分布数据加载完成')
    } catch (error) {
      console.error('❌ 加载客户分布数据失败:', error)
      ElMessage.error('加载客户分布数据失败')
    }
  }

  const refreshChartData = async () => {
    await Promise.all([
      loadRevenueData(),
      loadCustomerDistributionData()
    ])
    ElMessage.success('图表数据已刷新')
  }

  // 监听周期变化
  watch(period, (newPeriod) => {
    loadRevenueData(newPeriod)
  })

  // 初始化
  onMounted(() => {
    loadRevenueData()
    loadCustomerDistributionData()
  })

  return {
    // 状态
    loading: readonly(loading),
    revenueData: readonly(revenueData),
    customerDistributionData: readonly(customerDistributionData),
    period,
    chartOptions: readonly(chartOptions),
    doughnutOptions: readonly(doughnutOptions),

    // 方法
    loadRevenueData,
    loadCustomerDistributionData,
    refreshChartData
  }
}

/**
 * 通知和消息管理
 * @returns {Object} 通知相关的响应式状态和方法
 */
export function useNotifications() {
  const notifications = ref([])
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  const addNotification = (notification) => {
    const newNotification = {
      id: Date.now(),
      timestamp: new Date(),
      read: false,
      ...notification
    }
    notifications.value.unshift(newNotification)
  }

  const markAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAll = () => {
    notifications.value = []
  }

  return {
    notifications: readonly(notifications),
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll
  }
}

/**
 * 使用示例：
 * 
 * <script setup>
 * import { useDistributorStats, useCustomerManagement } from '@/composables/useDistributor'
 * 
 * // 使用统计数据
 * const { 
 *   loading: statsLoading, 
 *   formattedStats, 
 *   refreshStats 
 * } = useDistributorStats()
 * 
 * // 使用客户管理
 * const {
 *   loading: customersLoading,
 *   customers,
 *   handleSearch,
 *   createCustomer
 * } = useCustomerManagement()
 * </script>
 */