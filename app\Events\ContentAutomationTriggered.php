<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 内容自动化触发事件
 * 当自动化规则被触发执行时发出
 */
class ContentAutomationTriggered implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly array $rule,
        public readonly array $actionResults,
        public readonly array $context
    ) {}

    /**
     * 获取事件应该广播的频道
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->rule['user_id']),
            new PrivateChannel('automation.rule.' . $this->rule['id']),
        ];
    }

    /**
     * 广播事件名称
     */
    public function broadcastAs(): string
    {
        return 'content.automation.triggered';
    }

    /**
     * 广播数据
     */
    public function broadcastWith(): array
    {
        return [
            'rule' => [
                'id' => $this->rule['id'],
                'name' => $this->rule['name'],
                'type' => $this->rule['type'],
                'status' => $this->rule['status'],
            ],
            'execution_summary' => $this->generateExecutionSummary(),
            'action_results' => $this->formatActionResults(),
            'performance_metrics' => $this->calculatePerformanceMetrics(),
            'next_execution' => $this->predictNextExecution(),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * 生成执行摘要
     */
    private function generateExecutionSummary(): array
    {
        $totalActions = count($this->actionResults);
        $successfulActions = count(array_filter($this->actionResults, fn($r) => $r['status'] === 'success'));
        $failedActions = $totalActions - $successfulActions;
        
        $totalExecutionTime = array_sum(array_column($this->actionResults, 'execution_time'));
        
        return [
            'total_actions' => $totalActions,
            'successful_actions' => $successfulActions,
            'failed_actions' => $failedActions,
            'success_rate' => $totalActions > 0 ? round(($successfulActions / $totalActions) * 100, 2) : 0,
            'total_execution_time' => round($totalExecutionTime, 3),
            'average_action_time' => $totalActions > 0 ? round($totalExecutionTime / $totalActions, 3) : 0,
            'execution_status' => $this->determineOverallStatus($successfulActions, $failedActions),
        ];
    }

    /**
     * 格式化动作结果
     */
    private function formatActionResults(): array
    {
        return array_map(function ($result) {
            return [
                'action_id' => $result['action_id'],
                'type' => $result['type'],
                'status' => $result['status'],
                'execution_time' => $result['execution_time'],
                'success' => $result['status'] === 'success',
                'summary' => $this->generateActionSummary($result),
            ];
        }, $this->actionResults);
    }

    /**
     * 计算性能指标
     */
    private function calculatePerformanceMetrics(): array
    {
        $executionTimes = array_column($this->actionResults, 'execution_time');
        
        return [
            'min_execution_time' => !empty($executionTimes) ? min($executionTimes) : 0,
            'max_execution_time' => !empty($executionTimes) ? max($executionTimes) : 0,
            'avg_execution_time' => !empty($executionTimes) ? array_sum($executionTimes) / count($executionTimes) : 0,
            'efficiency_score' => $this->calculateEfficiencyScore(),
            'resource_usage' => $this->estimateResourceUsage(),
        ];
    }

    /**
     * 预测下次执行
     */
    private function predictNextExecution(): ?string
    {
        if ($this->rule['type'] === 'schedule' && isset($this->rule['schedule'])) {
            return $this->calculateNextScheduledExecution();
        }
        
        if ($this->rule['type'] === 'trigger') {
            return '基于触发条件';
        }
        
        return null;
    }

    /**
     * 确定整体状态
     */
    private function determineOverallStatus(int $successful, int $failed): string
    {
        if ($failed === 0) {
            return 'success';
        } elseif ($successful === 0) {
            return 'failed';
        } else {
            return 'partial_success';
        }
    }

    /**
     * 生成动作摘要
     */
    private function generateActionSummary(array $result): string
    {
        $type = $result['type'];
        $status = $result['status'];
        
        if ($status === 'success') {
            return match ($type) {
                'optimize' => '内容优化已完成',
                'publish' => '内容发布成功',
                'analyze' => '性能分析已执行',
                'notify' => '通知已发送',
                'update' => '内容更新完成',
                default => '动作执行成功',
            };
        } else {
            return match ($type) {
                'optimize' => '内容优化失败',
                'publish' => '内容发布失败',
                'analyze' => '性能分析失败',
                'notify' => '通知发送失败',
                'update' => '内容更新失败',
                default => '动作执行失败',
            };
        }
    }

    /**
     * 计算效率得分
     */
    private function calculateEfficiencyScore(): float
    {
        $totalActions = count($this->actionResults);
        if ($totalActions === 0) return 0;
        
        $successfulActions = count(array_filter($this->actionResults, fn($r) => $r['status'] === 'success'));
        $successRate = $successfulActions / $totalActions;
        
        $avgExecutionTime = array_sum(array_column($this->actionResults, 'execution_time')) / $totalActions;
        $timeEfficiency = max(0, 1 - ($avgExecutionTime / 60)); // 假设60秒为基准
        
        return round(($successRate * 0.7 + $timeEfficiency * 0.3) * 100, 2);
    }

    /**
     * 估算资源使用
     */
    private function estimateResourceUsage(): array
    {
        $totalTime = array_sum(array_column($this->actionResults, 'execution_time'));
        $actionCount = count($this->actionResults);
        
        return [
            'cpu_time' => round($totalTime, 3),
            'memory_estimate' => $actionCount * 10, // MB估算
            'api_calls' => $this->countAPICalls(),
            'database_queries' => $actionCount * 3, // 估算
        ];
    }

    /**
     * 计算API调用次数
     */
    private function countAPICalls(): int
    {
        $apiActions = ['optimize', 'analyze', 'publish'];
        $count = 0;
        
        foreach ($this->actionResults as $result) {
            if (in_array($result['type'], $apiActions)) {
                $count++;
            }
        }
        
        return $count;
    }

    /**
     * 计算下次计划执行时间
     */
    private function calculateNextScheduledExecution(): string
    {
        $schedule = $this->rule['schedule'];
        $type = $schedule['type'] ?? 'daily';
        
        return match ($type) {
            'daily' => now()->addDay()->format('Y-m-d H:i:s'),
            'weekly' => now()->addWeek()->format('Y-m-d H:i:s'),
            'monthly' => now()->addMonth()->format('Y-m-d H:i:s'),
            default => now()->addHour()->format('Y-m-d H:i:s'),
        };
    }
}