<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserBehaviorStat;
use App\Services\AnalyticsServiceMerged;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    protected $analyticsService;

    public function __construct(AnalyticsServiceMerged $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * 用户分析总览
     */
    public function users(Request $request)
    {
        try {
            $period = $request->get('period', 'month'); // today, week, month, quarter, year
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $data = $this->analyticsService->getUserAnalytics($period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 单个用户详细分析
     */
    public function userDetail(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);
            $period = $request->get('period', 'month');

            $data = $this->analyticsService->getUserDetail($user, $period);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户行为分析
     */
    public function behavior(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'string|in:today,week,month,quarter,year',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
                'behavior_type' => 'string|in:login,order,payment,share,invite',
                'group_by' => 'string|in:hour,day,week,month',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $params = [
                'period' => $request->get('period', 'month'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date'),
                'behavior_type' => $request->get('behavior_type'),
                'group_by' => $request->get('group_by', 'day'),
            ];

            $data = $this->analyticsService->getBehaviorAnalytics($params);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户留存分析
     */
    public function retention(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cohort_period' => 'string|in:day,week,month',
                'retention_period' => 'string|in:day,week,month',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $params = [
                'cohort_period' => $request->get('cohort_period', 'week'),
                'retention_period' => $request->get('retention_period', 'day'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date'),
            ];

            $data = $this->analyticsService->getRetentionAnalytics($params);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户分群分析
     */
    public function segments(Request $request)
    {
        try {
            $data = $this->analyticsService->getSegmentAnalytics();

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户漏斗分析
     */
    public function funnel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'steps' => 'required|array|min:2',
                'steps.*' => 'required|string',
                'period' => 'string|in:today,week,month,quarter,year',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $params = [
                'steps' => $request->get('steps'),
                'period' => $request->get('period', 'month'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date'),
            ];

            $data = $this->analyticsService->getFunnelAnalytics($params);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户生命周期分析
     */
    public function lifecycle(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $data = $this->analyticsService->getLifecycleAnalytics($period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户价值分析
     */
    public function value(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $metric = $request->get('metric', 'ltv'); // ltv, clv, arpu, arppu

            $data = $this->analyticsService->getValueAnalytics($period, $metric);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户活跃度分析
     */
    public function activity(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $groupBy = $request->get('group_by', 'day');

            $data = $this->analyticsService->getActivityAnalytics($period, $groupBy);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户地理分布分析
     */
    public function geography(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $level = $request->get('level', 'province'); // province, city

            $data = $this->analyticsService->getGeographyAnalytics($period, $level);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户设备分析
     */
    public function device(Request $request)
    {
        try {
            $period = $request->get('period', 'month');
            $dimension = $request->get('dimension', 'platform'); // platform, browser, os

            $data = $this->analyticsService->getDeviceAnalytics($period, $dimension);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 实时数据
     */
    public function realtime(Request $request)
    {
        try {
            $data = $this->analyticsService->getRealtimeData();

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 自定义报表
     */
    public function customReport(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'metrics' => 'required|array',
                'dimensions' => 'required|array',
                'filters' => 'array',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $params = [
                'metrics' => $request->get('metrics'),
                'dimensions' => $request->get('dimensions'),
                'filters' => $request->get('filters', []),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date'),
            ];

            $data = $this->analyticsService->getCustomReport($params);

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取可用的分析维度和指标
     */
    public function dimensions(Request $request)
    {
        try {
            $data = $this->analyticsService->getAvailableDimensions();

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
            ], 500);
        }
    }
} 