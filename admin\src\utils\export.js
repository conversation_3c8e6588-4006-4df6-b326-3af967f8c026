import request from '@/utils/request'

/**
 * 导出数据到Excel
 * @param {string} url - 导出接口地址
 * @param {object} params - 导出参数
 * @param {string} filename - 文件名
 */
export function exportData(url, params = {}, filename = 'export.xlsx') {
  return new Promise((resolve, reject) => {
    request({
      url,
      method: 'get',
      params,
      responseType: 'blob'
    }).then(response => {
      // 创建blob对象
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      
      // 清理
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      resolve(response)
    }).catch(error => {
      reject(error)
    })
  })
}

/**
 * 导出CSV文件
 * @param {Array} data - 数据数组
 * @param {Array} headers - 表头数组
 * @param {string} filename - 文件名
 */
export function exportCSV(data, headers, filename = 'export.csv') {
  // 构建CSV内容
  let csvContent = '\uFEFF' // BOM头，解决中文乱码
  
  // 添加表头
  if (headers && headers.length > 0) {
    csvContent += headers.join(',') + '\n'
  }
  
  // 添加数据行
  data.forEach(row => {
    const values = Object.values(row).map(value => {
      // 处理包含逗号、引号、换行符的值
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    })
    csvContent += values.join(',') + '\n'
  })
  
  // 创建下载
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 导出JSON文件
 * @param {object|Array} data - 数据
 * @param {string} filename - 文件名
 */
export function exportJSON(data, filename = 'export.json') {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 批量导出处理
 * @param {Array} exportTasks - 导出任务数组
 */
export async function batchExport(exportTasks) {
  const results = []
  
  for (const task of exportTasks) {
    try {
      const result = await exportData(task.url, task.params, task.filename)
      results.push({ success: true, task, result })
    } catch (error) {
      results.push({ success: false, task, error })
    }
  }
  
  return results
}