<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Withdrawal;
use App\Models\User;
use App\Services\PaymentService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * 提现管理控制器
 * 处理用户提现申请的审核、处理等管理功能
 */
class WithdrawalController extends Controller
{
    protected PaymentService $paymentService;
    protected NotificationService $notificationService;

    public function __construct(
        PaymentService $paymentService,
        NotificationService $notificationService
    ) {
        $this->middleware('auth:api');
        $this->paymentService = $paymentService;
        $this->notificationService = $notificationService;
    }

    /**
     * 用户提现申请列表
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            $query = Withdrawal::with(['user'])
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            $withdrawals = $query->paginate($request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => '提现记录获取成功',
                'data' => $withdrawals,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '提现记录获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 管理员提现列表
     */
    public function adminIndex(Request $request)
    {
        try {
            $query = Withdrawal::with(['user'])
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // 用户筛选
            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // 时间范围筛选
            if ($request->has('start_date')) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }
            if ($request->has('end_date')) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // 金额范围筛选
            if ($request->has('min_amount')) {
                $query->where('amount', '>=', $request->min_amount);
            }
            if ($request->has('max_amount')) {
                $query->where('amount', '<=', $request->max_amount);
            }

            $withdrawals = $query->paginate($request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => '提现记录获取成功',
                'data' => $withdrawals,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '提现记录获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 创建提现申请
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'payment_method' => 'required|string|in:alipay,wechat,bank',
                'account_info' => 'required|array',
                'password' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $user = Auth::user();
            $amount = $request->amount;

            // 验证支付密码
            if (!$this->paymentService->verifyPaymentPassword($user, $request->password)) {
                return response()->json([
                    'success' => false,
                    'message' => '支付密码错误',
                ], 422);
            }

            // 检查余额
            if ($user->balance < $amount) {
                return response()->json([
                    'success' => false,
                    'message' => '余额不足',
                ], 422);
            }

            // 检查最小提现金额
            $minAmount = config('payment.min_withdrawal_amount', 100);
            if ($amount < $minAmount) {
                return response()->json([
                    'success' => false,
                    'message' => "最小提现金额为 {$minAmount} 元",
                ], 422);
            }

            // 检查今日提现次数限制
            $todayCount = Withdrawal::where('user_id', $user->id)
                ->whereDate('created_at', today())
                ->count();

            $maxDailyWithdrawals = config('payment.max_daily_withdrawals', 3);
            if ($todayCount >= $maxDailyWithdrawals) {
                return response()->json([
                    'success' => false,
                    'message' => "每日最多可提现 {$maxDailyWithdrawals} 次",
                ], 422);
            }

            DB::beginTransaction();

            // 计算手续费
            $feeRate = config('payment.withdrawal_fee_rate', 0.01);
            $fee = $amount * $feeRate;
            $actualAmount = $amount - $fee;

            // 创建提现记录
            $withdrawal = Withdrawal::create([
                'user_id' => $user->id,
                'withdrawal_no' => $this->generateWithdrawalNo(),
                'amount' => $amount,
                'fee' => $fee,
                'actual_amount' => $actualAmount,
                'payment_method' => $request->payment_method,
                'account_info' => $request->account_info,
                'status' => 'pending',
                'remark' => $request->remark,
            ]);

            // 冻结用户余额
            $user->decrement('balance', $amount);
            $user->increment('frozen_balance', $amount);

            // 记录余额变动
            $user->balanceLogs()->create([
                'type' => 'withdrawal_freeze',
                'amount' => -$amount,
                'balance_before' => $user->balance + $amount,
                'balance_after' => $user->balance,
                'description' => "提现申请冻结 - {$withdrawal->withdrawal_no}",
                'related_type' => 'withdrawal',
                'related_id' => $withdrawal->id,
            ]);

            DB::commit();

            // 发送通知
            $this->notificationService->sendWithdrawalNotification($withdrawal, 'created');

            return response()->json([
                'success' => true,
                'message' => '提现申请提交成功',
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '提现申请失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 查看提现详情
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            
            $query = Withdrawal::with(['user']);
            
            // 非管理员只能查看自己的提现记录
            if (!$user->hasRole('admin')) {
                $query->where('user_id', $user->id);
            }

            $withdrawal = $query->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '提现记录不存在',
            ], 404);
        }
    }

    /**
     * 管理员查看提现详情
     */
    public function adminShow($id)
    {
        try {
            $withdrawal = Withdrawal::with(['user', 'approver'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '提现记录不存在',
            ], 404);
        }
    }

    /**
     * 审批通过提现申请
     */
    public function approve(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'remark' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $withdrawal = Withdrawal::with('user')->findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => '该提现申请已处理',
                ], 422);
            }

            DB::beginTransaction();

            // 更新提现状态
            $withdrawal->update([
                'status' => 'approved',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
                'admin_remark' => $request->remark,
            ]);

            $user = $withdrawal->user;

            // 解冻余额并扣除
            $user->decrement('frozen_balance', $withdrawal->amount);

            // 记录余额变动
            $user->balanceLogs()->create([
                'type' => 'withdrawal_approved',
                'amount' => -$withdrawal->amount,
                'balance_before' => $user->balance,
                'balance_after' => $user->balance,
                'description' => "提现审批通过 - {$withdrawal->withdrawal_no}",
                'related_type' => 'withdrawal',
                'related_id' => $withdrawal->id,
            ]);

            DB::commit();

            // 发送通知
            $this->notificationService->sendWithdrawalNotification($withdrawal, 'approved');

            return response()->json([
                'success' => true,
                'message' => '提现申请审批通过',
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '审批失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 拒绝提现申请
     */
    public function reject(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'remark' => 'required|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $withdrawal = Withdrawal::with('user')->findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => '该提现申请已处理',
                ], 422);
            }

            DB::beginTransaction();

            // 更新提现状态
            $withdrawal->update([
                'status' => 'rejected',
                'rejected_at' => now(),
                'rejected_by' => Auth::id(),
                'admin_remark' => $request->remark,
            ]);

            $user = $withdrawal->user;

            // 解冻余额并返还
            $user->decrement('frozen_balance', $withdrawal->amount);
            $user->increment('balance', $withdrawal->amount);

            // 记录余额变动
            $user->balanceLogs()->create([
                'type' => 'withdrawal_rejected',
                'amount' => $withdrawal->amount,
                'balance_before' => $user->balance - $withdrawal->amount,
                'balance_after' => $user->balance,
                'description' => "提现申请被拒绝，余额返还 - {$withdrawal->withdrawal_no}",
                'related_type' => 'withdrawal',
                'related_id' => $withdrawal->id,
            ]);

            DB::commit();

            // 发送通知
            $this->notificationService->sendWithdrawalNotification($withdrawal, 'rejected');

            return response()->json([
                'success' => true,
                'message' => '提现申请已拒绝',
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '拒绝失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 完成提现（标记为已打款）
     */
    public function complete(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'transaction_no' => 'nullable|string|max:100',
                'remark' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $withdrawal = Withdrawal::with('user')->findOrFail($id);

            if ($withdrawal->status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => '只能完成已审批的提现申请',
                ], 422);
            }

            // 更新提现状态
            $withdrawal->update([
                'status' => 'completed',
                'completed_at' => now(),
                'completed_by' => Auth::id(),
                'transaction_no' => $request->transaction_no,
                'admin_remark' => $request->remark,
            ]);

            // 发送通知
            $this->notificationService->sendWithdrawalNotification($withdrawal, 'completed');

            return response()->json([
                'success' => true,
                'message' => '提现已完成',
                'data' => $withdrawal,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 提现统计
     */
    public function statistics(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = match($period) {
                '1d' => now()->startOfDay(),
                '7d' => now()->subDays(7),
                '30d' => now()->subDays(30),
                '90d' => now()->subDays(90),
                default => now()->subDays(30)
            };

            $stats = [
                'overview' => [
                    'total_withdrawals' => Withdrawal::where('created_at', '>=', $startDate)->count(),
                    'total_amount' => Withdrawal::where('created_at', '>=', $startDate)->sum('amount'),
                    'pending_count' => Withdrawal::where('status', 'pending')->count(),
                    'pending_amount' => Withdrawal::where('status', 'pending')->sum('amount'),
                    'approved_count' => Withdrawal::where('status', 'approved')
                                                 ->where('created_at', '>=', $startDate)->count(),
                    'completed_count' => Withdrawal::where('status', 'completed')
                                                  ->where('created_at', '>=', $startDate)->count(),
                    'rejected_count' => Withdrawal::where('status', 'rejected')
                                                 ->where('created_at', '>=', $startDate)->count(),
                ],
                'daily_trend' => $this->getDailyWithdrawalTrend($startDate),
                'payment_methods' => $this->getPaymentMethodStats($startDate),
                'top_users' => $this->getTopWithdrawalUsers($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '统计数据获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 生成提现单号
     */
    private function generateWithdrawalNo(): string
    {
        return 'WD' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 获取每日提现趋势
     */
    private function getDailyWithdrawalTrend($startDate): array
    {
        return Withdrawal::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * 获取支付方式统计
     */
    private function getPaymentMethodStats($startDate): array
    {
        return Withdrawal::where('created_at', '>=', $startDate)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('payment_method')
            ->get()
            ->toArray();
    }

    /**
     * 获取提现金额最多的用户
     */
    private function getTopWithdrawalUsers($startDate): array
    {
        return Withdrawal::with('user')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('user_id, COUNT(*) as count, SUM(amount) as total_amount')
            ->groupBy('user_id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }
}