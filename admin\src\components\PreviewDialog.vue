<template>
  <el-dialog
    v-model="visible"
    title="落地页预览"
    width="90%"
    top="5vh"
    append-to-body
    class="preview-dialog"
    @close="handleClose"
  >
    <div class="preview-dialog-content">
      <!-- 预览工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <span class="preview-title">{{ groupData.title || '群组落地页' }}</span>
        </div>
        
        <div class="toolbar-center">
          <el-button-group>
            <el-button
              :type="viewMode === 'mobile' ? 'primary' : ''"
              @click="setViewMode('mobile')"
              size="small"
            >
              <el-icon><Iphone /></el-icon>
              手机版
            </el-button>
            <el-button
              :type="viewMode === 'tablet' ? 'primary' : ''"
              @click="setViewMode('tablet')"
              size="small"
            >
              <el-icon><Monitor /></el-icon>
              平板版
            </el-button>
            <el-button
              :type="viewMode === 'desktop' ? 'primary' : ''"
              @click="setViewMode('desktop')"
              size="small"
            >
              <el-icon><Monitor /></el-icon>
              桌面版
            </el-button>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <el-button @click="refreshPreview" size="small" :icon="RefreshRight">
            刷新
          </el-button>
          <el-button @click="copyPreviewUrl" size="small" :icon="Link" type="primary" plain>
            复制链接
          </el-button>
        </div>
      </div>
      
      <!-- 预览内容区域 -->
      <div class="preview-content" :class="`view-${viewMode}`">
        <div class="preview-frame">
          <div class="frame-header">
            <div class="frame-controls">
              <span class="control-dot red"></span>
              <span class="control-dot yellow"></span>
              <span class="control-dot green"></span>
            </div>
            <div class="frame-url">
              <el-icon><Link /></el-icon>
              <span>{{ previewUrl }}</span>
            </div>
          </div>
          
          <div class="frame-body">
            <LandingPagePreview
              :group-data="groupData"
              :layout-config="layoutConfig"
              :view-mode="viewMode"
            />
          </div>
        </div>
      </div>
      
      <!-- 预览信息面板 -->
      <div class="preview-info-panel">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="页面信息" name="info">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">页面标题：</span>
                <span class="info-value">{{ groupData.title }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">群组价格：</span>
                <span class="info-value">{{ formatPrice(groupData.price) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">内容模块：</span>
                <span class="info-value">{{ visibleSections.length }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">预览链接：</span>
                <span class="info-value">{{ previewUrl }}</span>
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="SEO 信息" name="seo">
            <div class="seo-info">
              <div class="seo-item">
                <div class="seo-label">页面标题 (Title)</div>
                <div class="seo-value">{{ seoTitle }}</div>
              </div>
              <div class="seo-item">
                <div class="seo-label">页面描述 (Description)</div>
                <div class="seo-value">{{ seoDescription }}</div>
              </div>
              <div class="seo-item">
                <div class="seo-label">关键词 (Keywords)</div>
                <div class="seo-value">{{ seoKeywords }}</div>
              </div>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="分享预览" name="share">
            <div class="share-preview">
              <div class="share-card">
                <div class="share-image">
                  <img
                    :src="groupData.banner_image || '/default-share.jpg'"
                    alt="分享图片"
                  />
                </div>
                <div class="share-content">
                  <div class="share-title">{{ groupData.title }}</div>
                  <div class="share-description">{{ groupData.description }}</div>
                  <div class="share-url">{{ previewUrl }}</div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Iphone, Monitor, RefreshRight, Link } from '@element-plus/icons-vue'
import LandingPagePreview from './LandingPagePreview.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  layoutConfig: {
    type: Object,
    default: () => ({
      sections: []
    })
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const viewMode = ref('mobile')
const activeCollapse = ref(['info'])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const visibleSections = computed(() => {
  if (!props.layoutConfig.sections) return []
  return props.layoutConfig.sections.filter(section => section.visible)
})

const previewUrl = computed(() => {
  return `${window.location.origin}/landing/group/preview`
})

const seoTitle = computed(() => {
  return `${props.groupData.title} - 群组邀请`
})

const seoDescription = computed(() => {
  return props.groupData.description || `加入${props.groupData.title}，与志同道合的朋友一起交流学习`
})

const seoKeywords = computed(() => {
  const keywords = [props.groupData.title]
  if (props.groupData.tags) {
    keywords.push(...props.groupData.tags)
  }
  keywords.push('群组', '交流', '学习')
  return keywords.join(', ')
})

// 方法
const setViewMode = (mode) => {
  viewMode.value = mode
}

const refreshPreview = () => {
  emit('refresh')
  ElMessage.success('预览已刷新')
}

const copyPreviewUrl = async () => {
  try {
    await navigator.clipboard.writeText(previewUrl.value)
    ElMessage.success('预览链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制链接')
  }
}

const formatPrice = (price) => {
  if (price === 0 || price === '0') {
    return '免费'
  }
  return `¥${price}`
}

const handleClose = () => {
  emit('update:modelValue', false)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 对话框打开时的初始化逻辑
    viewMode.value = 'mobile'
    activeCollapse.value = ['info']
  }
})
</script>

<style lang="scss" scoped>
.preview-dialog {
  :deep(.el-dialog) {
    margin: 0;
    height: 90vh;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }
  
  .preview-dialog-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .preview-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #f5f7fa;
      
      .toolbar-left {
        .preview-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .toolbar-center {
        flex: 1;
        display: flex;
        justify-content: center;
      }
      
      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }
    
    .preview-content {
      flex: 1;
      padding: 20px;
      background: #f0f2f5;
      overflow: auto;
      
      .preview-frame {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin: 0 auto;
        
        .frame-header {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          background: #f5f7fa;
          border-bottom: 1px solid #e4e7ed;
          
          .frame-controls {
            display: flex;
            gap: 6px;
            margin-right: 16px;
            
            .control-dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              
              &.red { background: #ff5f56; }
              &.yellow { background: #ffbd2e; }
              &.green { background: #27ca3f; }
            }
          }
          
          .frame-url {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #909399;
            
            .el-icon {
              font-size: 14px;
            }
          }
        }
        
        .frame-body {
          min-height: 500px;
        }
      }
      
      &.view-mobile .preview-frame {
        max-width: 375px;
      }
      
      &.view-tablet .preview-frame {
        max-width: 768px;
      }
      
      &.view-desktop .preview-frame {
        max-width: 100%;
      }
    }
    
    .preview-info-panel {
      border-top: 1px solid #e4e7ed;
      background: white;
      
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        
        .info-item {
          display: flex;
          
          .info-label {
            width: 80px;
            color: #909399;
            font-size: 14px;
          }
          
          .info-value {
            flex: 1;
            color: #606266;
            font-size: 14px;
          }
        }
      }
      
      .seo-info {
        .seo-item {
          margin-bottom: 12px;
          
          .seo-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .seo-value {
            font-size: 14px;
            color: #606266;
            line-height: 1.5;
          }
        }
      }
      
      .share-preview {
        .share-card {
          display: flex;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          overflow: hidden;
          max-width: 400px;
          
          .share-image {
            width: 120px;
            height: 80px;
            flex-shrink: 0;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .share-content {
            flex: 1;
            padding: 8px 12px;
            
            .share-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .share-description {
              font-size: 12px;
              color: #909399;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .share-url {
              font-size: 11px;
              color: #c0c4cc;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .preview-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      height: 95vh !important;
      max-height: 95vh !important;
    }
    
    .preview-dialog-content {
      .preview-toolbar {
        flex-direction: column;
        gap: 8px;
        
        .toolbar-center {
          justify-content: flex-start;
        }
      }
      
      .preview-info-panel {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
