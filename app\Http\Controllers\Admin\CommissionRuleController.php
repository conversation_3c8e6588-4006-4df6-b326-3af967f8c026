<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CommissionDistributionRule;
use App\Models\Substation;
use App\Services\CommissionCalculatorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CommissionRuleController extends Controller
{
    protected $commissionCalculator;

    public function __construct(CommissionCalculatorService $commissionCalculator)
    {
        $this->commissionCalculator = $commissionCalculator;
    }

    /**
     * 显示佣金规则列表
     */
    public function index(Request $request)
    {
        $query = CommissionDistributionRule::with('substation')
                                         ->orderBy('priority', 'desc')
                                         ->orderBy('created_at', 'desc');

        // 搜索过滤
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('rule_name', 'like', "%{$search}%");
        }

        // 规则类型过滤
        if ($request->filled('rule_type')) {
            $query->where('rule_type', $request->rule_type);
        }

        // 分站过滤
        if ($request->filled('substation_id')) {
            $query->where('substation_id', $request->substation_id);
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $rules = $query->paginate(15);

        // 统计数据
        $stats = [
            'total' => CommissionDistributionRule::count(),
            'active' => CommissionDistributionRule::where('status', 'active')->count(),
            'platform_rules' => CommissionDistributionRule::where('rule_type', 'platform')->count(),
            'substation_rules' => CommissionDistributionRule::where('rule_type', 'substation')->count(),
            'agent_rules' => CommissionDistributionRule::where('rule_type', 'agent')->count(),
        ];

        // 获取分站列表用于筛选
        $substations = Substation::where('status', 'active')->get();

        return view('admin.commission-rules.index', compact('rules', 'stats', 'substations'));
    }

    /**
     * 显示创建规则表单
     */
    public function create()
    {
        $substations = Substation::where('status', 'active')->get();
        return view('admin.commission-rules.create', compact('substations'));
    }

    /**
     * 存储新规则
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rule_name' => 'required|string|max:100|unique:commission_distribution_rules,rule_name',
            'rule_type' => 'required|in:platform,substation,agent',
            'substation_id' => 'nullable|exists:substations,id',
            'platform_rate' => 'required|numeric|between:0,100',
            'substation_rate' => 'required|numeric|between:0,100',
            'agent_rate' => 'required|numeric|between:0,100',
            'parent_agent_rate' => 'required|numeric|between:0,100',
            'conditions' => 'nullable|array',
            'priority' => 'required|integer|between:0,999',
            'status' => 'required|in:active,inactive'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        // 验证分配比例
        $rates = [
            'platform_rate' => $request->platform_rate,
            'substation_rate' => $request->substation_rate,
            'agent_rate' => $request->agent_rate,
            'parent_agent_rate' => $request->parent_agent_rate
        ];

        $validation = $this->commissionCalculator->validateDistributionRule($rates);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message']
            ], 422);
        }

        DB::beginTransaction();
        try {
            $rule = CommissionDistributionRule::create([
                'rule_name' => $request->rule_name,
                'rule_type' => $request->rule_type,
                'substation_id' => $request->substation_id,
                'platform_rate' => $request->platform_rate,
                'substation_rate' => $request->substation_rate,
                'agent_rate' => $request->agent_rate,
                'parent_agent_rate' => $request->parent_agent_rate,
                'conditions' => $request->conditions,
                'priority' => $request->priority,
                'status' => $request->status
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '佣金规则创建成功',
                'data' => ['rule_id' => $rule->id]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示规则详情
     */
    public function show(CommissionDistributionRule $rule)
    {
        $rule->load('substation');
        
        // 获取使用该规则的统计数据
        $stats = [
            'total_orders' => 0, // 这里需要根据实际业务逻辑统计
            'total_commission' => 0,
            'avg_commission' => 0
        ];

        return view('admin.commission-rules.show', compact('rule', 'stats'));
    }

    /**
     * 显示编辑规则表单
     */
    public function edit(CommissionDistributionRule $rule)
    {
        $substations = Substation::where('status', 'active')->get();
        return view('admin.commission-rules.edit', compact('rule', 'substations'));
    }

    /**
     * 更新规则
     */
    public function update(Request $request, CommissionDistributionRule $rule)
    {
        $validator = Validator::make($request->all(), [
            'rule_name' => 'required|string|max:100|unique:commission_distribution_rules,rule_name,' . $rule->id,
            'rule_type' => 'required|in:platform,substation,agent',
            'substation_id' => 'nullable|exists:substations,id',
            'platform_rate' => 'required|numeric|between:0,100',
            'substation_rate' => 'required|numeric|between:0,100',
            'agent_rate' => 'required|numeric|between:0,100',
            'parent_agent_rate' => 'required|numeric|between:0,100',
            'conditions' => 'nullable|array',
            'priority' => 'required|integer|between:0,999',
            'status' => 'required|in:active,inactive'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        // 验证分配比例
        $rates = [
            'platform_rate' => $request->platform_rate,
            'substation_rate' => $request->substation_rate,
            'agent_rate' => $request->agent_rate,
            'parent_agent_rate' => $request->parent_agent_rate
        ];

        $validation = $this->commissionCalculator->validateDistributionRule($rates);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message']
            ], 422);
        }

        DB::beginTransaction();
        try {
            $rule->update([
                'rule_name' => $request->rule_name,
                'rule_type' => $request->rule_type,
                'substation_id' => $request->substation_id,
                'platform_rate' => $request->platform_rate,
                'substation_rate' => $request->substation_rate,
                'agent_rate' => $request->agent_rate,
                'parent_agent_rate' => $request->parent_agent_rate,
                'conditions' => $request->conditions,
                'priority' => $request->priority,
                'status' => $request->status
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '佣金规则更新成功'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除规则
     */
    public function destroy(CommissionDistributionRule $rule)
    {
        try {
            $rule->delete();

            return response()->json([
                'success' => true,
                'message' => '佣金规则删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证规则
     */
    public function validateRule(Request $request)
    {
        $rates = [
            'platform_rate' => $request->platform_rate ?? 0,
            'substation_rate' => $request->substation_rate ?? 0,
            'agent_rate' => $request->agent_rate ?? 0,
            'parent_agent_rate' => $request->parent_agent_rate ?? 0
        ];

        $validation = $this->commissionCalculator->validateDistributionRule($rates);

        return response()->json([
            'success' => $validation['valid'],
            'message' => $validation['message'],
            'data' => $validation
        ]);
    }

    /**
     * 测试规则
     */
    public function testRule(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rule_id' => 'required|exists:commission_distribution_rules,id',
            'test_amount' => 'required|numeric|min:0.01',
            'user_id' => 'nullable|exists:users,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $rule = CommissionDistributionRule::find($request->rule_id);
        $distribution = $rule->calculateDistribution($request->test_amount);

        $result = [
            'rule_name' => $rule->rule_name,
            'test_amount' => $request->test_amount,
            'distribution' => $distribution,
            'total_distributed' => array_sum($distribution),
            'remaining' => $request->test_amount - array_sum($distribution)
        ];

        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }

    /**
     * 批量操作
     */
    public function batchAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'rule_ids' => 'required|array',
            'rule_ids.*' => 'exists:commission_distribution_rules,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        $ruleIds = $request->rule_ids;
        $action = $request->action;
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        DB::beginTransaction();
        try {
            foreach ($ruleIds as $ruleId) {
                $rule = CommissionDistributionRule::find($ruleId);
                if (!$rule) {
                    $results['failed']++;
                    $results['errors'][] = "规则 ID {$ruleId} 不存在";
                    continue;
                }

                switch ($action) {
                    case 'activate':
                        $rule->status = 'active';
                        $rule->save();
                        break;
                    case 'deactivate':
                        $rule->status = 'inactive';
                        $rule->save();
                        break;
                    case 'delete':
                        $rule->delete();
                        break;
                }

                $results['success']++;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "批量操作完成：成功 {$results['success']} 个，失败 {$results['failed']} 个",
                'data' => $results
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量操作失败：' . $e->getMessage()
            ], 500);
        }
    }
}