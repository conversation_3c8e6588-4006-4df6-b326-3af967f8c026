import { faker } from '@faker-js/faker'

// 生成模拟群组数据
const generateMockGroups = (count = 50) => {
  const groups = []
  const categories = ['startup', 'finance', 'tech', 'education', 'other']
  const statuses = ['active', 'paused', 'full', 'pending']
  
  for (let i = 1; i <= count; i++) {
    groups.push({
      id: i,
      name: faker.company.name() + '交流群',
      description: faker.lorem.sentences(2),
      avatar: faker.image.avatar(),
      category: faker.helpers.arrayElement(categories),
      status: faker.helpers.arrayElement(statuses),
      owner_name: faker.person.fullName(),
      owner_id: faker.number.int({ min: 1, max: 100 }),
      price: faker.number.float({ min: 0, max: 999, fractionDigits: 2 }),
      current_members: faker.number.int({ min: 0, max: 500 }),
      max_members: faker.number.int({ min: 100, max: 2000 }),
      total_revenue: faker.number.float({ min: 0, max: 50000, fractionDigits: 2 }),
      health_score: faker.number.int({ min: 20, max: 100 }),
      created_at: faker.date.past({ years: 2 }).toISOString(),
      updated_at: faker.date.recent().toISOString()
    })
  }
  
  return groups
}

// 生成模拟内容数据
const generateMockContent = (count = 100) => {
  const content = []
  const types = ['text', 'image', 'video', 'link']
  const riskLevels = ['low', 'medium', 'high']
  const statuses = ['pending', 'approved', 'rejected', 'flagged']
  
  for (let i = 1; i <= count; i++) {
    const type = faker.helpers.arrayElement(types)
    const riskLevel = faker.helpers.arrayElement(riskLevels)
    
    content.push({
      id: i,
      content: faker.lorem.paragraphs(faker.number.int({ min: 1, max: 3 })),
      type,
      risk_level: riskLevel,
      status: faker.helpers.arrayElement(statuses),
      author: {
        id: faker.number.int({ min: 1, max: 100 }),
        name: faker.person.fullName(),
        avatar: faker.image.avatar()
      },
      group: {
        id: faker.number.int({ min: 1, max: 50 }),
        name: faker.company.name() + '群'
      },
      image_url: type === 'image' ? faker.image.url() : null,
      ai_analysis: riskLevel !== 'low' ? {
        confidence: faker.number.float({ min: 0.3, max: 0.95, fractionDigits: 2 }),
        tags: faker.helpers.arrayElements(['敏感词汇', '可能违规', '需人工审核', '广告嫌疑'], { min: 1, max: 3 })
      } : null,
      created_at: faker.date.past({ years: 1 }).toISOString(),
      analyzing: false
    })
  }
  
  return content
}

// 生成模拟统计数据
const generateMockStats = () => {
  return {
    // 群组统计
    total_groups: faker.number.int({ min: 100, max: 500 }),
    active_groups: faker.number.int({ min: 80, max: 400 }),
    total_members: faker.number.int({ min: 5000, max: 50000 }),
    total_revenue: faker.number.float({ min: 10000, max: 500000, fractionDigits: 2 }),
    
    // 内容审核统计
    pending: faker.number.int({ min: 10, max: 100 }),
    approved: faker.number.int({ min: 500, max: 2000 }),
    rejected: faker.number.int({ min: 50, max: 200 }),
    autoProcessed: faker.number.int({ min: 200, max: 1000 }),
    pendingIncrease: faker.number.int({ min: 0, max: 20 }),
    approvalRate: faker.number.float({ min: 80, max: 98, fractionDigits: 1 }),
    rejectionRate: faker.number.float({ min: 2, max: 20, fractionDigits: 1 }),
    autoAccuracy: faker.number.float({ min: 85, max: 98, fractionDigits: 1 }),
    
    // 实时数据
    activeUsers: faker.number.int({ min: 100, max: 1000 }),
    newMessages: faker.number.int({ min: 50, max: 500 }),
    todayRevenue: faker.number.float({ min: 1000, max: 10000, fractionDigits: 0 }),
    pendingReviews: faker.number.int({ min: 5, max: 50 })
  }
}

// 模拟API响应
export const mockCommunityEnhancedAPI = {
  // 获取群组列表
  getGroupList: (params = {}) => {
    const allGroups = generateMockGroups(100)
    let filteredGroups = [...allGroups]
    
    // 应用筛选
    if (params.keyword) {
      filteredGroups = filteredGroups.filter(group => 
        group.name.includes(params.keyword) || 
        group.owner_name.includes(params.keyword)
      )
    }
    
    if (params.status) {
      filteredGroups = filteredGroups.filter(group => group.status === params.status)
    }
    
    if (params.category) {
      filteredGroups = filteredGroups.filter(group => group.category === params.category)
    }
    
    // 分页
    const page = params.page || 1
    const size = params.size || 20
    const start = (page - 1) * size
    const end = start + size
    
    return Promise.resolve({
      code: 200,
      data: {
        list: filteredGroups.slice(start, end),
        total: filteredGroups.length,
        page,
        size
      },
      message: '获取成功'
    })
  },

  // 获取群组统计
  getGroupStats: () => {
    return Promise.resolve({
      code: 200,
      data: generateMockStats(),
      message: '获取成功'
    })
  },

  // 获取实时数据
  getRealTimeGroupStatus: () => {
    return Promise.resolve({
      code: 200,
      data: {
        activeUsers: faker.number.int({ min: 100, max: 1000 }),
        newMessages: faker.number.int({ min: 50, max: 500 }),
        todayRevenue: faker.number.float({ min: 1000, max: 10000, fractionDigits: 0 }),
        pendingReviews: faker.number.int({ min: 5, max: 50 })
      },
      message: '获取成功'
    })
  },

  // 批量更新群组状态
  batchUpdateGroupStatus: (groupIds, status) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: { updated: groupIds.length },
          message: '批量更新成功'
        })
      }, 1000)
    })
  },

  // 获取内容列表
  getContentList: (params = {}) => {
    const allContent = generateMockContent(200)
    let filteredContent = [...allContent]
    
    // 按状态筛选
    if (params.status) {
      filteredContent = filteredContent.filter(item => item.status === params.status)
    }
    
    // 按类型筛选
    if (params.type) {
      filteredContent = filteredContent.filter(item => item.type === params.type)
    }
    
    // 按风险等级筛选
    if (params.risk_level) {
      filteredContent = filteredContent.filter(item => item.risk_level === params.risk_level)
    }
    
    // 关键词搜索
    if (params.keyword) {
      filteredContent = filteredContent.filter(item => 
        item.content.includes(params.keyword) ||
        item.author.name.includes(params.keyword) ||
        item.group.name.includes(params.keyword)
      )
    }
    
    // 分页
    const page = params.page || 1
    const size = params.size || 20
    const start = (page - 1) * size
    const end = start + size
    
    return Promise.resolve({
      code: 200,
      data: {
        list: filteredContent.slice(start, end),
        total: filteredContent.length,
        page,
        size
      },
      message: '获取成功'
    })
  },

  // 获取内容审核统计
  getContentReviewStats: () => {
    const stats = generateMockStats()
    return Promise.resolve({
      code: 200,
      data: {
        pending: stats.pending,
        approved: stats.approved,
        rejected: stats.rejected,
        autoProcessed: stats.autoProcessed,
        pendingIncrease: stats.pendingIncrease,
        approvalRate: stats.approvalRate,
        rejectionRate: stats.rejectionRate,
        autoAccuracy: stats.autoAccuracy
      },
      message: '获取成功'
    })
  },

  // 批量审核内容
  batchReviewContent: (contentIds, action, reason = '') => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: { 
            processed: contentIds.length,
            action,
            reason 
          },
          message: '批量操作成功'
        })
      }, 1500)
    })
  },

  // 智能内容审核
  intelligentContentReview: (contentId) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            content_id: contentId,
            confidence: faker.number.float({ min: 0.6, max: 0.95, fractionDigits: 2 }),
            tags: faker.helpers.arrayElements(['敏感词汇', '可能违规', '需人工审核', '广告嫌疑'], { min: 1, max: 2 }),
            recommendation: faker.helpers.arrayElement(['approve', 'reject', 'flag'])
          },
          message: 'AI分析完成'
        })
      }, 2000)
    })
  },

  // 群组活跃度分析
  getGroupActivityAnalysis: (groupId, timeRange = '7d') => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 7
    const data = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      data.push({
        date: date.toISOString().split('T')[0],
        messages: faker.number.int({ min: 10, max: 200 }),
        active_users: faker.number.int({ min: 5, max: 50 }),
        new_members: faker.number.int({ min: 0, max: 10 })
      })
    }
    
    return Promise.resolve({
      code: 200,
      data: {
        group_id: groupId,
        time_range: timeRange,
        activity_data: data,
        summary: {
          total_messages: data.reduce((sum, item) => sum + item.messages, 0),
          avg_daily_active: Math.round(data.reduce((sum, item) => sum + item.active_users, 0) / data.length),
          total_new_members: data.reduce((sum, item) => sum + item.new_members, 0)
        }
      },
      message: '获取成功'
    })
  },

  // 群组收益分析
  getGroupRevenueAnalysis: (groupId, timeRange = '30d') => {
    const days = timeRange === '30d' ? 30 : timeRange === '7d' ? 7 : 30
    const data = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      data.push({
        date: date.toISOString().split('T')[0],
        revenue: faker.number.float({ min: 0, max: 1000, fractionDigits: 2 }),
        orders: faker.number.int({ min: 0, max: 20 }),
        refunds: faker.number.float({ min: 0, max: 100, fractionDigits: 2 })
      })
    }
    
    return Promise.resolve({
      code: 200,
      data: {
        group_id: groupId,
        time_range: timeRange,
        revenue_data: data,
        summary: {
          total_revenue: data.reduce((sum, item) => sum + item.revenue, 0),
          total_orders: data.reduce((sum, item) => sum + item.orders, 0),
          total_refunds: data.reduce((sum, item) => sum + item.refunds, 0),
          avg_daily_revenue: data.reduce((sum, item) => sum + item.revenue, 0) / data.length
        }
      },
      message: '获取成功'
    })
  },

  // 导出群组数据
  exportGroups: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟文件下载
        const blob = new Blob(['群组数据导出文件内容'], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `groups_export_${Date.now()}.csv`
        a.click()
        URL.revokeObjectURL(url)
        
        resolve({
          code: 200,
          message: '导出成功'
        })
      }, 1000)
    })
  },

  // 获取敏感词库
  getSensitiveWords: () => {
    return Promise.resolve({
      code: 200,
      data: {
        words: ['违法', '赌博', '色情', '暴力', '诈骗', '传销'],
        categories: {
          illegal: ['违法', '犯罪'],
          gambling: ['赌博', '博彩'],
          adult: ['色情', '成人'],
          violence: ['暴力', '血腥'],
          fraud: ['诈骗', '欺诈'],
          pyramid: ['传销', '直销']
        }
      },
      message: '获取成功'
    })
  },

  // 获取审核规则
  getReviewRules: () => {
    return Promise.resolve({
      code: 200,
      data: [
        {
          id: 1,
          name: '敏感词检测',
          type: 'keyword',
          enabled: true,
          config: {
            action: 'flag',
            keywords: ['违法', '赌博', '色情']
          }
        },
        {
          id: 2,
          name: '图片内容检测',
          type: 'image',
          enabled: true,
          config: {
            action: 'review',
            confidence_threshold: 0.8
          }
        }
      ],
      message: '获取成功'
    })
  }
}

// 导出默认的mock API
export default mockCommunityEnhancedAPI
