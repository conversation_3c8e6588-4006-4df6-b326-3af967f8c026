<template>
  <el-drawer
    v-model="visible"
    title="链接统计分析"
    size="60%"
    direction="rtl"
    :before-close="handleClose"
  >
    <div class="analytics-container" v-loading="loading">
      <!-- 基础统计 -->
      <div class="stats-overview">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ analytics.total_clicks || 0 }}</div>
              <div class="stat-label">总点击量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ analytics.unique_visitors || 0 }}</div>
              <div class="stat-label">独立访客</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ analytics.conversions || 0 }}</div>
              <div class="stat-label">转化次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ conversionRate }}%</div>
              <div class="stat-label">转化率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时间筛选 -->
      <div class="filter-section">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="loadAnalytics"
        />
        <el-button type="primary" @click="loadAnalytics">刷新数据</el-button>
      </div>

      <!-- 趋势图表 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>📈 访问趋势</span>
            <el-radio-group v-model="chartType" size="small" @change="updateChart">
              <el-radio-button label="clicks">点击量</el-radio-button>
              <el-radio-button label="visitors">访客数</el-radio-button>
              <el-radio-button label="conversions">转化数</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div class="chart-container">
          <div class="chart-placeholder" v-if="!chartData.length">
            <i class="el-icon-loading"></i>
            <p>加载中...</p>
          </div>
          <div v-else class="trend-chart">
            <!-- 这里应该集成图表组件，比如 ECharts -->
            <div class="chart-demo">
              <h4>{{ getChartTitle() }}</h4>
              <div class="chart-data">
                <div v-for="(item, index) in chartData" :key="index" class="data-point">
                  <span class="date">{{ item.date }}</span>
                  <span class="value">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 详细数据 -->
      <el-row :gutter="16">
        <!-- 来源分析 -->
        <el-col :span="12">
          <el-card class="data-card">
            <template #header>
              <span>📱 访问来源</span>
            </template>
            <div class="source-stats">
              <div v-for="source in sourceStats" :key="source.name" class="source-item">
                <div class="source-info">
                  <span class="source-name">{{ source.name }}</span>
                  <span class="source-count">{{ source.count }}</span>
                </div>
                <div class="source-bar">
                  <div class="source-fill" :style="{ width: source.percentage + '%' }"></div>
                </div>
                <span class="source-percentage">{{ source.percentage }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 地区分布 -->
        <el-col :span="12">
          <el-card class="data-card">
            <template #header>
              <span>🌍 地区分布</span>
            </template>
            <div class="region-stats">
              <div v-for="region in regionStats" :key="region.name" class="region-item">
                <div class="region-info">
                  <span class="region-name">{{ region.name }}</span>
                  <span class="region-count">{{ region.count }}</span>
                </div>
                <div class="region-bar">
                  <div class="region-fill" :style="{ width: region.percentage + '%' }"></div>
                </div>
                <span class="region-percentage">{{ region.percentage }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 访问记录 -->
      <el-card class="records-card">
        <template #header>
          <span>📋 最近访问记录</span>
        </template>
        <el-table :data="recentRecords" style="width: 100%">
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="user_agent" label="用户代理" min-width="200">
            <template #default="{ row }">
              <div class="user-agent" :title="row.user_agent">
                {{ truncateText(row.user_agent, 50) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="referer" label="来源页面" width="150">
            <template #default="{ row }">
              <div class="referer" :title="row.referer">
                {{ truncateText(row.referer, 30) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="location" label="地区" width="100" />
          <el-table-column prop="created_at" label="访问时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="exportData">导出数据</el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { promotionAnalyticsApi } from '@/api/promotion'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  linkId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const loading = ref(false)
const chartType = ref('clicks')
const dateRange = ref([])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const analytics = reactive({
  total_clicks: 0,
  unique_visitors: 0,
  conversions: 0
})

const chartData = ref([])
const sourceStats = ref([])
const regionStats = ref([])
const recentRecords = ref([])

const conversionRate = computed(() => {
  if (analytics.total_clicks === 0) return '0.0'
  return ((analytics.conversions / analytics.total_clicks) * 100).toFixed(1)
})

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.linkId) {
    initDateRange()
    loadAnalytics()
  }
})

const initDateRange = () => {
  const today = new Date()
  const lastWeek = new Date()
  lastWeek.setDate(today.getDate() - 7)
  dateRange.value = [
    lastWeek.toISOString().split('T')[0],
    today.toISOString().split('T')[0]
  ]
}

const loadAnalytics = async () => {
  if (!props.linkId) return
  
  loading.value = true
  try {
    const params = {
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    }
    
    // 加载点击统计
    const clickStats = await promotionAnalyticsApi.getClickTrend({ link_id: props.linkId, ...params })
    analytics.total_clicks = clickStats.data.total_clicks || 0
    analytics.unique_visitors = clickStats.data.unique_visitors || 0
    
    // 加载转化数据
    const conversionStats = await promotionAnalyticsApi.getConversionFunnel({ link_id: props.linkId, ...params })
    analytics.conversions = conversionStats.data.total_conversions || 0
    
    // 生成模拟图表数据
    generateChartData()
    generateSourceStats()
    generateRegionStats()
    generateRecentRecords()
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const generateChartData = () => {
  chartData.value = []
  const days = 7
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    chartData.value.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(Math.random() * 100) + 10
    })
  }
}

const generateSourceStats = () => {
  sourceStats.value = [
    { name: '微信', count: 156, percentage: 45 },
    { name: 'QQ', count: 89, percentage: 26 },
    { name: '直接访问', count: 67, percentage: 19 },
    { name: '其他', count: 34, percentage: 10 }
  ]
}

const generateRegionStats = () => {
  regionStats.value = [
    { name: '广东', count: 89, percentage: 32 },
    { name: '浙江', count: 67, percentage: 24 },
    { name: '江苏', count: 45, percentage: 16 },
    { name: '上海', count: 23, percentage: 8 },
    { name: '其他', count: 56, percentage: 20 }
  ]
}

const generateRecentRecords = () => {
  recentRecords.value = [
    {
      ip: '***********',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      referer: 'https://example.com',
      location: '广东深圳',
      created_at: new Date().toISOString()
    },
    {
      ip: '***********',
      user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      referer: 'https://weixin.qq.com',
      location: '浙江杭州',
      created_at: new Date(Date.now() - 3600000).toISOString()
    }
  ]
}

const updateChart = () => {
  generateChartData()
}

const getChartTitle = () => {
  const titles = {
    clicks: '点击量趋势',
    visitors: '访客数趋势',
    conversions: '转化数趋势'
  }
  return titles[chartType.value] || '数据趋势'
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  return new Date(datetime).toLocaleString('zh-CN')
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.analytics-container {
  padding: 20px;
}

.stats-overview {
  margin-bottom: 24px;
  
  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
    }
  }
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 24px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .chart-container {
    height: 300px;
    
    .chart-placeholder {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      
      i {
        font-size: 24px;
        margin-bottom: 10px;
      }
    }
    
    .chart-demo {
      height: 100%;
      display: flex;
      flex-direction: column;
      
      h4 {
        margin: 0 0 20px 0;
        text-align: center;
      }
      
      .chart-data {
        flex: 1;
        display: flex;
        align-items: end;
        gap: 10px;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 4px;
        
        .data-point {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .date {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }
          
          .value {
            font-size: 14px;
            font-weight: bold;
            color: #409eff;
            padding: 5px 10px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
        }
      }
    }
  }
}

.data-card {
  margin-bottom: 24px;
  height: 300px;
  
  .source-stats,
  .region-stats {
    height: 220px;
    overflow-y: auto;
  }
  
  .source-item,
  .region-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .source-info,
    .region-info {
      flex: 1;
      display: flex;
      justify-content: space-between;
      margin-right: 10px;
      
      .source-name,
      .region-name {
        font-size: 14px;
        color: #303133;
      }
      
      .source-count,
      .region-count {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .source-bar,
    .region-bar {
      width: 80px;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      margin-right: 10px;
      
      .source-fill,
      .region-fill {
        height: 100%;
        background: #409eff;
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
    
    .source-percentage,
    .region-percentage {
      font-size: 12px;
      color: #606266;
      min-width: 35px;
      text-align: right;
    }
  }
}

.records-card {
  .user-agent,
  .referer {
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }
}

.drawer-footer {
  text-align: right;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}
</style>