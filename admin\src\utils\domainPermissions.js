// 基于新4域架构的权限管理工具
// admin/src/utils/domainPermissions.js

import { 
  NAVIGATION_DOMAINS, 
  ROLE_DOMAIN_PERMISSIONS,
  generateUserNavigation,
  hasUserDomainAccess,
  hasUserModuleAccess 
} from '@/config/navigation-domains'

/**
 * 权限检查工具类
 */
export class DomainPermissionChecker {
  constructor(userRole) {
    this.userRole = userRole
    this.roleConfig = ROLE_DOMAIN_PERMISSIONS[userRole] || { domains: [], modules: [] }
  }

  /**
   * 检查用户是否有访问指定域的权限
   */
  canAccessDomain(domainKey) {
    return hasUserDomainAccess(this.userRole, domainKey)
  }

  /**
   * 检查用户是否有访问指定模块的权限
   */
  canAccessModule(moduleKey) {
    return hasUserModuleAccess(this.userRole, moduleKey)
  }

  /**
   * 检查用户是否有访问指定页面的权限
   */
  canAccessPage(pagePath) {
    // 检查是否在角色的页面白名单中
    if (this.roleConfig.pages && this.roleConfig.pages.includes(pagePath)) {
      return true
    }

    // 检查是否通过域和模块权限
    const routeInfo = this.getRoutePermissionInfo(pagePath)
    if (!routeInfo) return false

    return this.canAccessDomain(routeInfo.domainKey) && 
           this.canAccessModule(routeInfo.moduleKey)
  }

  /**
   * 获取路由权限信息
   */
  getRoutePermissionInfo(pagePath) {
    // 页面路径到域模块的映射逻辑
    const pathMappings = {
      '/dashboard': { domainKey: 'business-analytics', moduleKey: 'data-dashboard' },
      '/data-screen': { domainKey: 'business-analytics', moduleKey: 'data-dashboard' },
      '/community': { domainKey: 'customer-operations', moduleKey: 'community-management' },
      '/user': { domainKey: 'customer-operations', moduleKey: 'user-management' },
      '/distribution': { domainKey: 'customer-operations', moduleKey: 'distribution-system' },
      '/orders': { domainKey: 'customer-operations', moduleKey: 'order-management' },
      '/finance': { domainKey: 'financial-management', moduleKey: 'revenue-management' },
      '/payment': { domainKey: 'financial-management', moduleKey: 'payment-config' },
      '/system': { domainKey: 'system-settings', moduleKey: 'system-config' },
      '/permission': { domainKey: 'system-settings', moduleKey: 'security-management' },
      '/substation': { domainKey: 'system-settings', moduleKey: 'organization-structure' }
    }

    // 精确匹配
    if (pathMappings[pagePath]) {
      return pathMappings[pagePath]
    }

    // 模糊匹配（支持子路径）
    for (const [basePath, permission] of Object.entries(pathMappings)) {
      if (pagePath.startsWith(basePath + '/')) {
        return permission
      }
    }

    return null
  }

  /**
   * 获取用户可访问的导航数据
   */
  getAccessibleNavigation() {
    return generateUserNavigation(this.userRole)
  }

  /**
   * 过滤用户有权限访问的页面数组
   */
  filterAccessiblePages(pages) {
    return pages.filter(page => this.canAccessPage(page.path || page))
  }
}

/**
 * 权限装饰器 - 用于组件权限检查
 */
export function requirePermission(permission) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      const userStore = this.$store?.state?.user || this.userStore
      const userRole = userStore?.userInfo?.role
      
      if (!userRole) {
        console.warn('用户角色未定义，拒绝访问')
        return false
      }

      const checker = new DomainPermissionChecker(userRole)
      
      if (permission.domain && !checker.canAccessDomain(permission.domain)) {
        console.warn(`用户 ${userRole} 没有访问域 ${permission.domain} 的权限`)
        return false
      }

      if (permission.module && !checker.canAccessModule(permission.module)) {
        console.warn(`用户 ${userRole} 没有访问模块 ${permission.module} 的权限`)
        return false
      }

      if (permission.page && !checker.canAccessPage(permission.page)) {
        console.warn(`用户 ${userRole} 没有访问页面 ${permission.page} 的权限`)
        return false
      }

      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * Vue组合式API权限钩子
 */
export function usePermissions(userRole) {
  const checker = new DomainPermissionChecker(userRole)
  
  return {
    canAccessDomain: checker.canAccessDomain.bind(checker),
    canAccessModule: checker.canAccessModule.bind(checker),
    canAccessPage: checker.canAccessPage.bind(checker),
    getAccessibleNavigation: checker.getAccessibleNavigation.bind(checker),
    filterAccessiblePages: checker.filterAccessiblePages.bind(checker)
  }
}

/**
 * 路由守卫权限检查
 */
export function checkRoutePermission(to, userRole) {
  if (!userRole) {
    console.warn('用户角色未定义，拒绝路由访问')
    return false
  }

  const checker = new DomainPermissionChecker(userRole)
  return checker.canAccessPage(to.path)
}

/**
 * 菜单权限过滤器
 */
export function filterMenuByPermission(menuItems, userRole) {
  if (!userRole) return []

  const checker = new DomainPermissionChecker(userRole)
  
  const filterRecursive = (items) => {
    return items.filter(item => {
      // 检查当前项权限
      if (item.path && !checker.canAccessPage(item.path)) {
        return false
      }

      // 递归过滤子项
      if (item.children && item.children.length > 0) {
        item.children = filterRecursive(item.children)
        // 如果所有子项都被过滤掉，则当前项也不显示
        return item.children.length > 0
      }

      return true
    })
  }

  return filterRecursive(menuItems)
}

/**
 * 权限常量
 */
export const PERMISSIONS = {
  // 经营分析域
  DATA_DASHBOARD: { domain: 'business-analytics', module: 'data-dashboard' },
  BUSINESS_ANALYSIS: { domain: 'business-analytics', module: 'business-analysis' },
  OPERATIONS_MONITORING: { domain: 'business-analytics', module: 'operations-monitoring' },

  // 客户运营域
  COMMUNITY_MANAGEMENT: { domain: 'customer-operations', module: 'community-management' },
  USER_MANAGEMENT: { domain: 'customer-operations', module: 'user-management' },
  DISTRIBUTION_SYSTEM: { domain: 'customer-operations', module: 'distribution-system' },
  ORDER_MANAGEMENT: { domain: 'customer-operations', module: 'order-management' },

  // 财务管控域
  REVENUE_MANAGEMENT: { domain: 'financial-management', module: 'revenue-management' },
  COMMISSION_MANAGEMENT: { domain: 'financial-management', module: 'commission-management' },
  WITHDRAWAL_MANAGEMENT: { domain: 'financial-management', module: 'withdrawal-management' },
  PAYMENT_CONFIG: { domain: 'financial-management', module: 'payment-config' },

  // 系统设置域
  ORGANIZATION_STRUCTURE: { domain: 'system-settings', module: 'organization-structure' },
  SECURITY_MANAGEMENT: { domain: 'system-settings', module: 'security-management' },
  SYSTEM_CONFIG: { domain: 'system-settings', module: 'system-config' },
  HELP_SUPPORT: { domain: 'system-settings', module: 'help-support' }
}

/**
 * 角色默认路由配置
 */
export const ROLE_DEFAULT_ROUTES = {
  admin: '/dashboard',
  substation: '/dashboard',
  agent: '/distribution/dashboard',
  distributor: '/distributor/dashboard', 
  group_owner: '/owner/dashboard',
  user: '/user/center'
}

/**
 * 获取用户默认路由
 */
export function getUserDefaultRoute(userRole) {
  return ROLE_DEFAULT_ROUTES[userRole] || '/dashboard'
}

export default {
  DomainPermissionChecker,
  requirePermission,
  usePermissions,
  checkRoutePermission,
  filterMenuByPermission,
  getUserDefaultRoute,
  PERMISSIONS,
  ROLE_DEFAULT_ROUTES
}