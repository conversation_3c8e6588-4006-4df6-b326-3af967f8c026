// 四域导航配置 - 与现有路由系统集成
export const DOMAIN_CONFIG = {
  // 业务核心域
  'business-core': {
    key: 'business-core',
    title: '业务核心',
    description: '核心业务功能管理',
    icon: 'Monitor',
    color: '#6366f1',
    order: 1,
    modules: [
      {
        key: 'dashboard',
        title: '数据看板',
        description: '实时业务数据监控',
        icon: 'DataLine',
        color: '#6366f1',
        path: '/admin/dashboard',
        children: [
          { path: '/admin/dashboard', title: '概览', icon: 'Monitor' },
          { path: '/data-screen', title: '数据大屏', icon: 'FullScreen' }
        ]
      },
      {
        key: 'community-management',
        title: '社群管理',
        description: '微信群组运营管理',
        icon: 'Comment',
        color: '#059669',
        path: '/admin/groups',
        children: [
          { path: '/admin/groups', title: '群组列表', icon: 'UserFilled' },
          { path: '/admin/groups', title: '创建群组', icon: 'Plus' },
          { path: '/admin/templates', title: '模板管理', icon: 'Document' },
          { path: '/admin/analytics', title: '数据分析', icon: 'DataLine' }
        ]
      },
      {
        key: 'distribution-system',
        title: '分销体系',
        description: '分销商管理与佣金结算',
        icon: 'Share',
        color: '#dc2626',
        path: '/distribution',
        children: [
          { path: '/distribution/distributors', title: '分销商管理', icon: 'UserFilled' },
          { path: '/distributor/dashboard', title: '分销员工作台', icon: 'Monitor' },
          { path: '/distributor/group-management', title: '群组管理', icon: 'Comment' }
        ]
      },
      {
        key: 'order-management',
        title: '订单管理',
        description: '订单处理与跟踪',
        icon: 'Tickets',
        color: '#7c3aed',
        path: '/orders',
        children: [
          { path: '/orders/list', title: '订单列表', icon: 'List' },
          { path: '/orders/analytics', title: '订单分析', icon: 'DataAnalysis' }
        ]
      }
    ]
  },

  // 运营管理域
  'operation-management': {
    key: 'operation-management',
    title: '运营管理',
    description: '系统运营与推广管理',
    icon: 'Tools',
    color: '#ea580c',
    order: 2,
    modules: [
      {
        key: 'anti-block-system',
        title: '防红系统',
        description: '域名防封与短链管理',
        icon: 'Shield',
        color: '#dc2626',
        path: '/anti-block',
        children: [
          { path: '/anti-block/dashboard', title: '系统概览', icon: 'DataLine' },
          { path: '/anti-block/domains', title: '域名管理', icon: 'Connection' },
          { path: '/anti-block/short-links', title: '短链接管理', icon: 'Link' }
        ]
      },
      {
        key: 'promotion-management',
        title: '推广管理',
        description: '推广链接与落地页管理',
        icon: 'Share',
        color: '#059669',
        path: '/promotion',
        children: [
          { path: '/promotion/links', title: '推广链接', icon: 'Link' },
          { path: '/promotion/landing-pages', title: '落地页管理', icon: 'Document' }
        ]
      },
      {
        key: 'substation-management',
        title: '分站管理',
        description: '分站系统管理',
        icon: 'OfficeBuilding',
        color: '#7c3aed',
        path: '/substation',
        children: [
          { path: '/substation/list', title: '分站列表', icon: 'List' },
          { path: '/substation/finance', title: '分站财务', icon: 'Money' }
        ]
      },
      {
        key: 'user-management',
        title: '用户管理',
        description: '用户账户与权限管理',
        icon: 'User',
        color: '#0891b2',
        path: '/user',
        children: [
          { path: '/user/list', title: '用户列表', icon: 'UserFilled' },
          { path: '/user/analytics', title: '用户分析', icon: 'DataAnalysis' }
        ]
      }
    ]
  },

  // 数据分析域
  'data-analytics': {
    key: 'data-analytics',
    title: '数据分析',
    description: '业务数据分析与决策支持',
    icon: 'DataLine',
    color: '#7c2d12',
    order: 3,
    modules: [
      {
        key: 'finance-analytics',
        title: '财务分析',
        description: '财务数据与收支分析',
        icon: 'Money',
        color: '#059669',
        path: '/finance',
        children: [
          { path: '/finance/dashboard', title: '财务总览', icon: 'DataLine' },
          { path: '/finance/commission-logs', title: '佣金明细', icon: 'Medal' },
          { path: '/finance/transactions', title: '交易记录', icon: 'Goods' }
        ]
      },
      {
        key: 'performance-analytics',
        title: '业绩分析',
        description: '业务业绩数据分析',
        icon: 'TrendCharts',
        color: '#7c3aed',
        path: '/analytics',
        children: [
          { path: '/analytics/overview', title: '业绩概览', icon: 'DataLine' },
          { path: '/analytics/trends', title: '趋势分析', icon: 'TrendCharts' }
        ]
      },
      {
        key: 'user-behavior',
        title: '用户行为',
        description: '用户行为分析与洞察',
        icon: 'UserFilled',
        color: '#0891b2',
        path: '/user/analytics',
        children: [
          { path: '/user/analytics', title: '用户分析', icon: 'DataAnalysis' },
          { path: '/user/behavior', title: '行为分析', icon: 'View' }
        ]
      }
    ]
  },

  // 系统配置域
  'system-config': {
    key: 'system-config',
    title: '系统配置',
    description: '系统设置与维护管理',
    icon: 'Setting',
    color: '#374151',
    order: 4,
    modules: [
      {
        key: 'system-settings',
        title: '系统设置',
        description: '系统参数配置',
        icon: 'Tools',
        color: '#6b7280',
        path: '/system/settings',
        children: [
          { path: '/system/settings', title: '基本设置', icon: 'Setting' },
          { path: '/system/notifications', title: '通知管理', icon: 'Bell' }
        ]
      },
      {
        key: 'permission-management',
        title: '权限管理',
        description: '用户权限与角色管理',
        icon: 'Lock',
        color: '#dc2626',
        path: '/admin/permission',
        children: [
          { path: '/admin/roles', title: '角色管理', icon: 'UserFilled' },
          { path: '/admin/permission', title: '权限配置', icon: 'Key' }
        ]
      },
      {
        key: 'data-management',
        title: '数据管理',
        description: '数据导出与备份',
        icon: 'FolderOpened',
        color: '#059669',
        path: '/system/data',
        children: [
          { path: '/system/export', title: '数据导出', icon: 'Download' },
          { path: '/system/backup', title: '数据备份', icon: 'FolderOpened' }
        ]
      },
      {
        key: 'operation-logs',
        title: '操作日志',
        description: '系统操作记录查询',
        icon: 'Document',
        color: '#7c3aed',
        path: '/system/operation-logs',
        children: [
          { path: '/system/operation-logs', title: '操作日志', icon: 'Document' },
          { path: '/system/error-logs', title: '错误日志', icon: 'Warning' }
        ]
      }
    ]
  }
}

// 根据用户角色生成可访问的导航
export const generateUserDomains = (userRole) => {
  const accessibleDomains = []

  Object.values(DOMAIN_CONFIG).forEach(domain => {
    const accessibleModules = []

    domain.modules.forEach(module => {
      // 检查模块权限
      if (hasModuleAccess(module, userRole)) {
        // 过滤可访问的子页面
        const accessibleChildren = module.children?.filter(child => 
          hasRouteAccess(child.path, userRole)
        ) || []

        accessibleModules.push({
          ...module,
          children: accessibleChildren
        })
      }
    })

    // 如果有可访问的模块，添加域
    if (accessibleModules.length > 0) {
      accessibleDomains.push({
        ...domain,
        modules: accessibleModules
      })
    }
  })

  return accessibleDomains.sort((a, b) => a.order - b.order)
}

// 权限检查函数
const hasModuleAccess = (module, userRole) => {
  const modulePermissions = {
    // 管理员拥有所有权限
    admin: () => true,
    
    // 经理级权限
    manager: (module) => {
      const restrictedModules = ['permission-management']
      return !restrictedModules.includes(module.key)
    },
    
    // 分销商权限
    distributor: (module) => {
      const allowedModules = [
        'dashboard', 'distribution-system', 'finance-analytics',
        'user-behavior'
      ]
      return allowedModules.includes(module.key)
    },
    
    // 群主权限
    group_owner: (module) => {
      const allowedModules = ['dashboard', 'community-management']
      return allowedModules.includes(module.key)
    },
    
    // 普通用户权限
    user: (module) => {
      const allowedModules = ['dashboard']
      return allowedModules.includes(module.key)
    }
  }

  const checkPermission = modulePermissions[userRole]
  return checkPermission ? checkPermission(module) : false
}

const hasRouteAccess = (routePath, userRole) => {
  const routePermissions = {
    admin: () => true,
    manager: (path) => {
      const restrictedPaths = ['/permission/', '/system/backup']
      return !restrictedPaths.some(restricted => path.includes(restricted))
    },
    distributor: (path) => {
      const allowedPaths = [
        '/admin/dashboard', '/admin/distributors', '/admin/finance', '/admin/user-analytics'
      ]
      return allowedPaths.some(allowed => path.includes(allowed))
    },
    group_owner: (path) => {
      const allowedPaths = ['/admin/dashboard', '/admin/groups', '/admin/groups']
      return allowedPaths.some(allowed => path.includes(allowed))
    },
    user: (path) => {
      return path.includes('/admin/dashboard')
    }
  }

  const checkPermission = routePermissions[userRole]
  return checkPermission ? checkPermission(routePath) : false
}

// 获取路由对应的域信息
export const getRouteDomainInfo = (routePath) => {
  for (const domain of Object.values(DOMAIN_CONFIG)) {
    for (const module of domain.modules) {
      if (module.path === routePath) {
        return {
          domainKey: domain.key,
          moduleKey: module.key,
          domain,
          module
        }
      }

      // 检查子路由
      if (module.children) {
        for (const child of module.children) {
          if (child.path === routePath) {
            return {
              domainKey: domain.key,
              moduleKey: module.key,
              childPath: child.path,
              domain,
              module,
              child
            }
          }
        }
      }
    }
  }

  return null
}

// 获取面包屑导航
export const getBreadcrumbs = (routePath) => {
  const routeInfo = getRouteDomainInfo(routePath)
  if (!routeInfo) return []

  const breadcrumbs = [
    {
      title: '控制台',
      path: '/dashboard',
      icon: 'HomeFilled'
    }
  ]

  // 添加域
  if (routeInfo.domain && routeInfo.domain.key !== 'business-core') {
    breadcrumbs.push({
      title: routeInfo.domain.title,
      path: routeInfo.module.path,
      icon: routeInfo.domain.icon
    })
  }

  // 添加模块
  if (routeInfo.module && routeInfo.module.path !== routePath) {
    breadcrumbs.push({
      title: routeInfo.module.title,
      path: routeInfo.module.path,
      icon: routeInfo.module.icon
    })
  }

  // 添加子页面
  if (routeInfo.child) {
    breadcrumbs.push({
      title: routeInfo.child.title,
      path: routeInfo.child.path,
      icon: routeInfo.child.icon
    })
  }

  return breadcrumbs
}

// 搜索功能
export const searchNavigation = (query, userRole) => {
  const results = []
  const domains = generateUserDomains(userRole)

  domains.forEach(domain => {
    // 搜索域
    if (domain.title.toLowerCase().includes(query.toLowerCase())) {
      results.push({
        type: 'domain',
        title: domain.title,
        description: domain.description,
        path: domain.modules[0]?.path || '/dashboard',
        icon: domain.icon,
        color: domain.color
      })
    }

    // 搜索模块
    domain.modules.forEach(module => {
      if (module.title.toLowerCase().includes(query.toLowerCase())) {
        results.push({
          type: 'module',
          title: module.title,
          description: `${domain.title} > ${module.description}`,
          path: module.path,
          icon: module.icon,
          color: module.color
        })
      }

      // 搜索子页面
      module.children?.forEach(child => {
        if (child.title.toLowerCase().includes(query.toLowerCase())) {
          results.push({
            type: 'page',
            title: child.title,
            description: `${domain.title} > ${module.title} > ${child.title}`,
            path: child.path,
            icon: child.icon,
            color: module.color
          })
        }
      })
    })
  })

  return results
}

// 与现有路由集成
export const integrateWithExistingRoutes = (routes) => {
  // 为现有路由添加域信息
  return routes.map(route => {
    const routeInfo = getRouteDomainInfo(route.path)
    
    if (routeInfo) {
      return {
        ...route,
        meta: {
          ...route.meta,
          domain: routeInfo.domain.key,
          module: routeInfo.module.key,
          domainTitle: routeInfo.domain.title,
          moduleTitle: routeInfo.module.title
        }
      }
    }
    
    return route
  })
}

// 导出配置供其他组件使用
export { DOMAIN_CONFIG as default }