#!/bin/bash

# LinkHub Pro Git Hooks 安装脚本
# 版本: v1.0
# 更新时间: 2025-08-08

echo "🔧 正在安装 LinkHub Pro Git Hooks..."

# 检查是否在Git仓库中
if [ ! -d ".git" ]; then
    echo "❌ 错误: 当前目录不是Git仓库"
    exit 1
fi

# 创建hooks目录（如果不存在）
mkdir -p .git/hooks

# 创建pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

echo "🔍 LinkHub Pro 代码规范检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查标志
HAS_ISSUES=false

# 获取待提交的文件
STAGED_FILES=$(git diff --cached --name-only)

if [ -z "$STAGED_FILES" ]; then
    echo "📝 没有文件需要提交"
    exit 0
fi

echo "📁 检查文件: $(echo $STAGED_FILES | wc -w) 个"

# 1. 检查禁止的调试文件
echo "🚫 检查调试文件..."
DEBUG_FILES=$(echo "$STAGED_FILES" | grep -E '\.(html)$' | grep -E '(test|debug|fix|diagnosis|preview-test|verification|complete|report)' || true)
if [ ! -z "$DEBUG_FILES" ]; then
    echo -e "${RED}❌ 发现禁止的调试HTML文件:${NC}"
    echo "$DEBUG_FILES" | sed 's/^/  - /'
    HAS_ISSUES=true
fi

# 2. 检查临时脚本文件
echo "🚫 检查临时脚本..."
SCRIPT_FILES=$(echo "$STAGED_FILES" | grep -E '\.(js|bat|ps1|sh)$' | grep -E '(debug|fix|temp|quick|调试|配置测试)' || true)
if [ ! -z "$SCRIPT_FILES" ]; then
    echo -e "${RED}❌ 发现禁止的临时脚本文件:${NC}"
    echo "$SCRIPT_FILES" | sed 's/^/  - /'
    HAS_ISSUES=true
fi

# 3. 检查文档报告文件
echo "🚫 检查文档报告..."
REPORT_FILES=$(echo "$STAGED_FILES" | grep -E '\.md$' | grep -E '(report|analysis|fix|检测报告|修复报告|分析报告|实施报告)' || true)
if [ ! -z "$REPORT_FILES" ]; then
    echo -e "${RED}❌ 发现禁止的报告文档:${NC}"
    echo "$REPORT_FILES" | sed 's/^/  - /'
    HAS_ISSUES=true
fi

# 4. 检查备份文件
echo "🚫 检查备份文件..."
BACKUP_FILES=$(echo "$STAGED_FILES" | grep -E '\.(backup|bak|old)$' || echo "$STAGED_FILES" | grep -E '(backup|old)\.' || true)
if [ ! -z "$BACKUP_FILES" ]; then
    echo -e "${RED}❌ 发现禁止的备份文件:${NC}"
    echo "$BACKUP_FILES" | sed 's/^/  - /'
    HAS_ISSUES=true
fi

# 5. 检查console.log调试代码
echo "🔍 检查调试代码..."
JS_VUE_FILES=$(echo "$STAGED_FILES" | grep -E '\.(js|vue|ts)$' || true)
if [ ! -z "$JS_VUE_FILES" ]; then
    for file in $JS_VUE_FILES; do
        if git diff --cached "$file" | grep -E '^\+.*console\.log' > /dev/null; then
            echo -e "${RED}❌ 文件 $file 包含 console.log 调试代码${NC}"
            HAS_ISSUES=true
        fi
    done
fi

# 6. 检查debugger语句
echo "🔍 检查debugger语句..."
if [ ! -z "$JS_VUE_FILES" ]; then
    for file in $JS_VUE_FILES; do
        if git diff --cached "$file" | grep -E '^\+.*debugger' > /dev/null; then
            echo -e "${RED}❌ 文件 $file 包含 debugger 语句${NC}"
            HAS_ISSUES=true
        fi
    done
fi

# 7. 检查文件命名规范
echo "📝 检查文件命名..."
VUE_FILES=$(echo "$STAGED_FILES" | grep -E '\.vue$' || true)
if [ ! -z "$VUE_FILES" ]; then
    for file in $VUE_FILES; do
        filename=$(basename "$file" .vue)
        # 检查Vue组件是否使用PascalCase
        if [[ ! "$filename" =~ ^[A-Z][a-zA-Z0-9]*$ ]]; then
            echo -e "${YELLOW}⚠️  Vue组件 $file 建议使用PascalCase命名${NC}"
        fi
    done
fi

# 8. 检查大文件
echo "📏 检查文件大小..."
for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        size=$(wc -l < "$file" 2>/dev/null || echo 0)
        if [ "$size" -gt 500 ]; then
            echo -e "${YELLOW}⚠️  文件 $file 有 $size 行，建议拆分为更小的文件${NC}"
        fi
    fi
done

# 检查结果
if [ "$HAS_ISSUES" = true ]; then
    echo ""
    echo -e "${RED}❌ 代码规范检查失败！${NC}"
    echo ""
    echo "请修复以上问题后重新提交。"
    echo "参考文档: DEVELOPMENT_GUIDELINES.md"
    echo ""
    exit 1
else
    echo ""
    echo -e "${GREEN}✅ 代码规范检查通过！${NC}"
    echo ""
fi
EOF

# 创建commit-msg hook
cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash

# 读取提交消息
commit_msg=$(cat "$1")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "📝 检查提交消息格式..."

# 检查提交消息长度
if [ ${#commit_msg} -lt 10 ]; then
    echo -e "${RED}❌ 提交消息太短，至少需要10个字符${NC}"
    echo "当前消息: $commit_msg"
    exit 1
fi

# 检查是否包含中文或英文描述
if [[ ! "$commit_msg" =~ [a-zA-Z\u4e00-\u9fa5] ]]; then
    echo -e "${RED}❌ 提交消息应包含有意义的描述${NC}"
    echo "当前消息: $commit_msg"
    exit 1
fi

# 建议的提交消息格式
if [[ ! "$commit_msg" =~ ^(feat|fix|docs|style|refactor|test|chore|perf)(\(.+\))?: ]]; then
    echo -e "${YELLOW}⚠️  建议使用规范的提交消息格式:${NC}"
    echo "  feat: 新功能"
    echo "  fix: 修复bug"
    echo "  docs: 文档更新"
    echo "  style: 代码格式调整"
    echo "  refactor: 重构"
    echo "  test: 测试相关"
    echo "  chore: 构建或工具相关"
    echo "  perf: 性能优化"
    echo ""
    echo "示例: feat(user): 添加用户头像上传功能"
    echo ""
fi

echo -e "${GREEN}✅ 提交消息检查通过${NC}"
EOF

# 创建pre-push hook
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash

echo "🚀 准备推送代码..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo -e "${YELLOW}⚠️  检测到未提交的更改，建议先提交所有更改${NC}"
fi

# 运行测试（如果存在）
if [ -f "package.json" ] && grep -q "\"test\"" package.json; then
    echo "🧪 运行测试..."
    npm test
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 测试失败，推送已取消${NC}"
        exit 1
    fi
fi

# 检查admin目录的测试
if [ -d "admin" ] && [ -f "admin/package.json" ]; then
    cd admin
    if grep -q "\"test\"" package.json; then
        echo "🧪 运行前端测试..."
        npm test
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 前端测试失败，推送已取消${NC}"
            exit 1
        fi
    fi
    cd ..
fi

echo -e "${GREEN}✅ 推送前检查通过${NC}"
EOF

# 设置执行权限
chmod +x .git/hooks/pre-commit
chmod +x .git/hooks/commit-msg
chmod +x .git/hooks/pre-push

echo ""
echo -e "${GREEN}✅ Git Hooks 安装完成！${NC}"
echo ""
echo "已安装的hooks:"
echo "  - pre-commit: 提交前代码规范检查"
echo "  - commit-msg: 提交消息格式检查"
echo "  - pre-push: 推送前测试检查"
echo ""
echo "现在所有的git commit和git push操作都会自动进行规范检查。"
echo ""
echo "如需禁用检查，可以使用:"
echo "  git commit --no-verify"
echo "  git push --no-verify"
echo ""
echo "参考文档: DEVELOPMENT_GUIDELINES.md"
