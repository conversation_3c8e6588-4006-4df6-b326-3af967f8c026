<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

/**
 * 数据库优化命令
 * 用于优化数据库性能，包括索引分析、表优化等
 */
class DatabaseOptimize extends Command
{
    protected $signature = 'db:optimize {--analyze : 分析表结构} {--repair : 修复表} {--optimize : 优化表}';
    protected $description = '数据库性能优化工具';

    public function handle()
    {
        $this->info('🔧 开始数据库优化...');
        $this->newLine();

        if ($this->option('analyze') || !$this->hasOptions()) {
            $this->analyzeDatabase();
        }

        if ($this->option('repair')) {
            $this->repairTables();
        }

        if ($this->option('optimize')) {
            $this->optimizeTables();
        }

        $this->info('✅ 数据库优化完成！');
    }

    /**
     * 分析数据库结构
     */
    private function analyzeDatabase(): void
    {
        $this->info('📊 分析数据库结构...');

        // 获取所有表
        $tables = $this->getAllTables();
        
        $this->table(['表名', '记录数', '数据大小', '索引大小', '建议'], 
            collect($tables)->map(function ($table) {
                $stats = $this->getTableStats($table);
                $suggestions = $this->getOptimizationSuggestions($table, $stats);
                
                return [
                    $table,
                    number_format($stats['rows']),
                    $this->formatBytes($stats['data_size']),
                    $this->formatBytes($stats['index_size']),
                    implode(', ', $suggestions)
                ];
            })->toArray()
        );

        $this->checkMissingIndexes();
        $this->checkSlowQueries();
    }

    /**
     * 修复表
     */
    private function repairTables(): void
    {
        $this->info('🔨 修复数据表...');

        $tables = $this->getAllTables();
        
        foreach ($tables as $table) {
            try {
                DB::statement("REPAIR TABLE `{$table}`");
                $this->line("✅ 修复表: {$table}");
            } catch (\Exception $e) {
                $this->error("❌ 修复失败 {$table}: " . $e->getMessage());
            }
        }
    }

    /**
     * 优化表
     */
    private function optimizeTables(): void
    {
        $this->info('⚡ 优化数据表...');

        $tables = $this->getAllTables();
        
        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE `{$table}`");
                $this->line("✅ 优化表: {$table}");
            } catch (\Exception $e) {
                $this->error("❌ 优化失败 {$table}: " . $e->getMessage());
            }
        }
    }

    /**
     * 获取所有表名
     */
    private function getAllTables(): array
    {
        $database = config('database.connections.mysql.database');
        
        $tables = DB::select("SELECT table_name FROM information_schema.tables WHERE table_schema = ?", [$database]);
        
        return collect($tables)
            ->map(fn($table) => $table->table_name)
            ->toArray();
    }

    /**
     * 获取表统计信息
     */
    private function getTableStats(string $table): array
    {
        $database = config('database.connections.mysql.database');
        
        $stats = DB::selectOne("
            SELECT 
                table_rows as rows,
                data_length as data_size,
                index_length as index_size,
                (data_length + index_length) as total_size
            FROM information_schema.tables 
            WHERE table_schema = ? AND table_name = ?
        ", [$database, $table]);

        return [
            'rows' => $stats->rows ?? 0,
            'data_size' => $stats->data_size ?? 0,
            'index_size' => $stats->index_size ?? 0,
            'total_size' => $stats->total_size ?? 0
        ];
    }

    /**
     * 获取优化建议
     */
    private function getOptimizationSuggestions(string $table, array $stats): array
    {
        $suggestions = [];

        // 检查表大小
        if ($stats['total_size'] > 100 * 1024 * 1024) { // 100MB
            $suggestions[] = '考虑分区';
        }

        // 检查索引比例
        if ($stats['data_size'] > 0) {
            $indexRatio = $stats['index_size'] / $stats['data_size'];
            if ($indexRatio > 1) {
                $suggestions[] = '索引过多';
            } elseif ($indexRatio < 0.1 && $stats['rows'] > 1000) {
                $suggestions[] = '可能缺少索引';
            }
        }

        // 检查记录数
        if ($stats['rows'] > 1000000) {
            $suggestions[] = '大表，考虑归档';
        }

        return empty($suggestions) ? ['正常'] : $suggestions;
    }

    /**
     * 检查缺失的索引
     */
    private function checkMissingIndexes(): void
    {
        $this->info('🔍 检查缺失的索引...');

        $recommendations = [
            'users' => [
                ['email'], ['role'], ['status'], ['created_at'], ['referrer_id']
            ],
            'wechat_groups' => [
                ['owner_id'], ['status'], ['category'], ['created_at'], ['price']
            ],
            'orders' => [
                ['user_id'], ['group_id'], ['status'], ['created_at'], ['payment_status']
            ],
            'commission_logs' => [
                ['user_id'], ['order_id'], ['status'], ['created_at']
            ],
            'balance_logs' => [
                ['user_id'], ['type'], ['created_at']
            ]
        ];

        foreach ($recommendations as $table => $indexes) {
            if (Schema::hasTable($table)) {
                foreach ($indexes as $columns) {
                    $indexName = $table . '_' . implode('_', $columns) . '_index';
                    
                    // 检查索引是否存在
                    $exists = DB::select("
                        SELECT COUNT(*) as count 
                        FROM information_schema.statistics 
                        WHERE table_schema = ? AND table_name = ? AND index_name = ?
                    ", [config('database.connections.mysql.database'), $table, $indexName]);

                    if ($exists[0]->count == 0) {
                        $this->warn("建议为表 {$table} 添加索引: " . implode(', ', $columns));
                    }
                }
            }
        }
    }

    /**
     * 检查慢查询
     */
    private function checkSlowQueries(): void
    {
        $this->info('🐌 检查慢查询配置...');

        try {
            $slowQueryLog = DB::selectOne("SHOW VARIABLES LIKE 'slow_query_log'");
            $longQueryTime = DB::selectOne("SHOW VARIABLES LIKE 'long_query_time'");

            if ($slowQueryLog->Value === 'OFF') {
                $this->warn('慢查询日志未开启，建议开启以监控性能');
            } else {
                $this->info("✅ 慢查询日志已开启，阈值: {$longQueryTime->Value}秒");
            }

            // 检查是否有慢查询记录
            $slowQueries = DB::select("
                SELECT COUNT(*) as count 
                FROM mysql.slow_log 
                WHERE start_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");

            if (isset($slowQueries[0]) && $slowQueries[0]->count > 0) {
                $this->warn("过去24小时内有 {$slowQueries[0]->count} 条慢查询");
            }

        } catch (\Exception $e) {
            $this->warn('无法检查慢查询状态: ' . $e->getMessage());
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 检查是否有选项
     */
    private function hasOptions(): bool
    {
        return $this->option('analyze') || $this->option('repair') || $this->option('optimize');
    }
}