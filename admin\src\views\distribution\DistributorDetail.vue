<template>
  <div class="app-container">
    <div class="detail-header">
      <el-button @click="goBack" icon="el-icon-back" class="back-btn">返回</el-button>
      <h2>分销员详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="box-card" shadow="hover">
        <div slot="header" class="clearfix">
          <span class="card-title">基本信息</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleEdit">编辑</el-button>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-avatar">
              <el-avatar :size="80" :src="distributor.avatar">
                {{ distributor.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="info-details">
              <h3>{{ distributor.name }}</h3>
              <p class="info-email">{{ distributor.email }}</p>
              <p class="info-phone">{{ distributor.phone }}</p>
              <el-tag :type="getLevelTagType(distributor.level)" size="medium">
                {{ getLevelName(distributor.level) }}
              </el-tag>
            </div>
          </div>
          <div class="info-grid-right">
            <div class="info-row">
              <span class="info-label">用户ID:</span>
              <span class="info-value">{{ distributor.id }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">邀请码:</span>
              <span class="info-value">{{ distributor.invite_code }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">上级:</span>
              <span class="info-value">{{ distributor.parent ? distributor.parent.name : '无' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">分销组:</span>
              <span class="info-value">{{ distributor.distribution_group ? distributor.distribution_group.name : '未分配' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">状态:</span>
              <el-tag :type="distributor.status === 1 ? 'success' : 'danger'">
                {{ distributor.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
            <div class="info-row">
              <span class="info-label">注册时间:</span>
              <span class="info-value">{{ distributor.created_at }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 统计数据卡片 -->
      <el-card class="box-card" shadow="hover">
        <div slot="header" class="clearfix">
          <span class="card-title">统计数据</span>
          <el-button-group style="float: right;">
            <el-button size="mini" @click="refreshStats">刷新</el-button>
            <el-button size="mini" type="primary" @click="exportData">导出</el-button>
          </el-button-group>
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">¥{{ stats.total_commission }}</div>
            <div class="stat-label">总佣金</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥{{ stats.month_commission }}</div>
            <div class="stat-label">本月佣金</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥{{ stats.today_commission }}</div>
            <div class="stat-label">今日佣金</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.total_orders }}</div>
            <div class="stat-label">总订单</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.today_orders }}</div>
            <div class="stat-label">今日订单</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.children_count }}</div>
            <div class="stat-label">下级人数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.active_children }}</div>
            <div class="stat-label">活跃下级</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">¥{{ distributor.balance }}</div>
            <div class="stat-label">账户余额</div>
          </div>
        </div>
      </el-card>

      <!-- 下级分销员 -->
      <el-card class="box-card" shadow="hover" v-if="distributor.children && distributor.children.length > 0">
        <div slot="header" class="clearfix">
          <span class="card-title">下级分销员</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllChildren">查看全部</el-button>
        </div>
        <div class="children-list">
          <div class="child-item" v-for="child in distributor.children" :key="child.id">
            <el-avatar :size="40" :src="child.avatar">{{ child.name.charAt(0) }}</el-avatar>
            <div class="child-info">
              <div class="child-name">{{ child.name }}</div>
              <div class="child-email">{{ child.email }}</div>
            </div>
            <div class="child-actions">
              <el-button size="mini" type="text" @click="viewChild(child)">查看</el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 最近佣金记录 -->
      <el-card class="box-card" shadow="hover">
        <div slot="header" class="clearfix">
          <span class="card-title">最近佣金记录</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllCommissions">查看全部</el-button>
        </div>
        <el-table :data="recentCommissions" border fit highlight-current-row>
          <el-table-column label="时间" width="160">
            <template #default="{row}">
              {{ row.created_at }}
            </template>
          </el-table-column>
          <el-table-column label="订单号" width="140">
            <template #default="{row}">
              {{ row.order ? row.order.order_no : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="订单金额" width="120">
            <template #default="{row}">
              ¥{{ row.order ? row.order.amount : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="佣金金额" width="120">
            <template #default="{row}">
              <span class="commission-amount">¥{{ row.amount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="佣金类型" width="120">
            <template #default="{row}">
              <el-tag size="small" :type="getCommissionTypeTag(row.type)">
                {{ getCommissionTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{row}">
              <el-tag size="small" :type="row.status === 'paid' ? 'success' : 'warning'">
                {{ row.status === 'paid' ? '已发放' : '待发放' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{row}">
              {{ row.remark || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑分销员信息" v-model="editDialogVisible" width="600px">
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="分销组">
          <el-select v-model="editForm.distribution_group_id" placeholder="请选择分销组">
            <el-option v-for="group in groupOptions" :key="group.id" :label="group.name" :value="group.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import request from '@/utils/request'
import { ElNotification } from 'element-plus'
import { exportDistributors } from '@/api/export'

// 路由相关
const route = useRoute()
const router = useRouter()
const distributorId = route.params.id

// 数据定义
const loading = ref(true)
const distributor = ref({})
const stats = ref({})
const recentCommissions = ref([])
const groupOptions = ref([])

// 编辑弹窗
const editDialogVisible = ref(false)
const editForm = ref({
  name: '',
  email: '',
  phone: '',
  distribution_group_id: null,
  status: 1
})

const editRules = reactive({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
})

// 生命周期
onMounted(() => {
  fetchDistributorDetail()
  fetchGroupOptions()
})

// 方法定义
async function fetchDistributorDetail() {
  try {
    loading.value = true
    const response = await request({
      url: `/distributors/${distributorId}`,
      method: 'get'
    })
    
    const data = response.data
    distributor.value = data.distributor
    stats.value = data.stats
    recentCommissions.value = data.recent_commissions
    
    loading.value = false
  } catch (error) {
    loading.value = false
    ElNotification({
      title: '错误',
      message: '获取分销员详情失败',
      type: 'error'
    })
  }
}

async function fetchGroupOptions() {
  try {
    const response = await request({
      url: '/distribution-groups',
      method: 'get',
      params: { all: true }
    })
    groupOptions.value = response.data.data
  } catch (error) {
    console.error('获取分销组选项失败:', error)
  }
}

function goBack() {
  router.go(-1)
}

function handleEdit() {
  editForm.value = {
    name: distributor.value.name,
    email: distributor.value.email,
    phone: distributor.value.phone,
    distribution_group_id: distributor.value.distribution_group_id,
    status: distributor.value.status
  }
  editDialogVisible.value = true
}

function saveEdit() {
  this.$refs.editForm.validate(async (valid) => {
    if (valid) {
      try {
        await request({
          url: `/distributors/${distributorId}`,
          method: 'put',
          data: editForm.value
        })
        
        editDialogVisible.value = false
        await fetchDistributorDetail()
        
        ElNotification({
          title: '成功',
          message: '更新成功',
          type: 'success'
        })
      } catch (error) {
        ElNotification({
          title: '错误',
          message: '更新失败',
          type: 'error'
        })
      }
    }
  })
}

function refreshStats() {
  fetchDistributorDetail()
}

  async function exportData() {
    // 导出数据逻辑
    try {
    ElNotification({
      title: '提示',
      message: '正在导出分销商详情...',
      type: 'info'
    })
    
    // 构建导出参数
    const exportParams = {
      distributor_id: distributorId.value,
      format: 'excel',
      fields: [
        'id', 'username', 'level', 'total_commission', 'balance',
        'direct_members', 'team_members', 'created_at'
      ]
    }
    
    // 调用导出API
    const response = await exportDistributors(exportParams)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `分销商详情_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElNotification({
      title: '成功',
      message: '导出成功',
      type: 'success'
    })
  } catch (error) {
    ElNotification({
      title: '错误',
      message: '导出失败：' + error.message,
      type: 'error'
    })
  }
}

function viewAllChildren() {
  // 查看所有下级
  router.push(`/distribution/children/${distributorId}`)
}

function viewChild(child) {
  router.push(`/distribution/detail/${child.id}`)
}

function viewAllCommissions() {
  // 查看所有佣金记录
  router.push(`/finance/commission?user_id=${distributorId}`)
}

// 工具函数
function getLevelName(level) {
  const levels = {
    1: '初级分销员',
    2: '中级分销员',
    3: '高级分销员',
    4: '金牌分销员'
  }
  return levels[level] || '初级分销员'
}

function getLevelTagType(level) {
  const types = {
    1: 'info',
    2: 'success',
    3: 'warning',
    4: 'danger'
  }
  return types[level] || 'info'
}

function getCommissionTypeName(type) {
  const types = {
    'direct': '直接佣金',
    'indirect': '间接佣金',
    'bonus': '奖金佣金'
  }
  return types[type] || '佣金'
}

function getCommissionTypeTag(type) {
  const tags = {
    'direct': 'success',
    'indirect': 'warning',
    'bonus': 'danger'
  }
  return tags[type] || 'info'
}
</script>

<style scoped>
.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-btn {
  margin-right: 15px;
}

.detail-header h2 {
  margin: 0;
  color: #303133;
}

.box-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.info-details h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
}

.info-email, .info-phone {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.info-grid-right {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.children-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.child-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.child-info {
  flex: 1;
  margin-left: 12px;
}

.child-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.child-email {
  font-size: 12px;
  color: #606266;
}

.child-actions {
  margin-left: auto;
}

.commission-amount {
  font-weight: bold;
  color: #e6a23c;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid-right {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .children-list {
    grid-template-columns: 1fr;
  }
}
</style> 