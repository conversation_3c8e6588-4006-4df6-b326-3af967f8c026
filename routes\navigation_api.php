<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\NavigationConfigController;
use App\Http\Controllers\Api\NavigationController;
use App\Http\Controllers\Api\UserNavigationPreferenceController;
use App\Http\Controllers\Api\NavigationSearchController;
use App\Http\Controllers\Api\NavigationAnalyticsController;

/**
 * 导航系统API路由
 * 
 * 提供四域导航架构的完整API接口
 */

Route::prefix('navigation')->name('navigation.')->group(function () {
    
    // ========== 导航配置管理API ==========
    Route::prefix('config')->name('config.')->group(function () {
        // 获取导航配置
        Route::get('/domains', [NavigationConfigController::class, 'getDomainConfig'])->name('domains');
        Route::get('/domains/{domain}', [NavigationConfigController::class, 'getDomainConfig'])->name('domain');
        Route::get('/menus/{code}', [NavigationConfigController::class, 'getMenuDetail'])->name('menu.detail');
        
        // 管理员权限路由
        Route::middleware(['auth:sanctum', 'role:super_admin|admin'])->group(function () {
            Route::post('/menus', [NavigationConfigController::class, 'createMenu'])->name('menu.create');
            Route::put('/menus/{code}', [NavigationConfigController::class, 'updateMenu'])->name('menu.update');
            Route::delete('/menus/{code}', [NavigationConfigController::class, 'deleteMenu'])->name('menu.delete');
            Route::post('/menus/batch-sort', [NavigationConfigController::class, 'batchSort'])->name('menu.batch_sort');
        });
    });

    // ========== 基础导航API ==========
    Route::middleware(['auth:sanctum'])->group(function () {
        // 获取导航
        Route::get('/', [NavigationController::class, 'getNavigation'])->name('get');
        Route::get('/domains', [NavigationController::class, 'getDomains'])->name('domains');
        
        // 导航操作
        Route::post('/visit', [NavigationController::class, 'recordVisit'])->name('record_visit');
        Route::get('/stats', [NavigationController::class, 'getUserStats'])->name('stats');
    });

    // ========== 用户偏好管理API ==========
    Route::prefix('preferences')->name('preferences.')->middleware(['auth:sanctum'])->group(function () {
        // 偏好设置
        Route::get('/', [UserNavigationPreferenceController::class, 'getPreferences'])->name('get');
        Route::put('/', [UserNavigationPreferenceController::class, 'updatePreferences'])->name('update');
        Route::post('/reset', [UserNavigationPreferenceController::class, 'resetPreferences'])->name('reset');
        
        // 快速操作
        Route::post('/pin', [UserNavigationPreferenceController::class, 'togglePin'])->name('toggle_pin');
        Route::post('/visibility', [UserNavigationPreferenceController::class, 'toggleVisibility'])->name('toggle_visibility');
        Route::post('/quick-access', [UserNavigationPreferenceController::class, 'setQuickAccess'])->name('quick_access');
        
        // 使用记录
        Route::post('/access', [UserNavigationPreferenceController::class, 'recordAccess'])->name('record_access');
        Route::get('/usage-report', [UserNavigationPreferenceController::class, 'getUsageReport'])->name('usage_report');
    });

    // ========== 搜索与推荐API ==========
    Route::prefix('search')->name('search.')->group(function () {
        // 搜索功能
        Route::get('/', [NavigationSearchController::class, 'search'])->name('search');
        Route::get('/suggestions', [NavigationSearchController::class, 'getSuggestions'])->name('suggestions');
        Route::get('/popular', [NavigationSearchController::class, 'getPopularQueries'])->name('popular');
        
        // 认证用户功能
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::get('/recommendations', [NavigationSearchController::class, 'getRecommendations'])->name('recommendations');
            Route::get('/personalized', [NavigationSearchController::class, 'getPersonalizedNavigation'])->name('personalized');
            Route::post('/recommendation-click', [NavigationSearchController::class, 'recordRecommendationClick'])->name('recommendation_click');
            Route::get('/stats', [NavigationSearchController::class, 'getSearchStats'])->name('search_stats');
        });
    });

    // ========== 统计分析API ==========
    Route::prefix('analytics')->name('analytics.')->middleware(['auth:sanctum', 'can:view-navigation-analytics'])->group(function () {
        // 基础统计
        Route::get('/overview', [NavigationAnalyticsController::class, 'getOverview'])->name('overview');
        Route::get('/menu-ranking', [NavigationAnalyticsController::class, 'getMenuRanking'])->name('menu_ranking');
        Route::get('/user-behavior', [NavigationAnalyticsController::class, 'getUserBehaviorAnalysis'])->name('user_behavior');
        Route::get('/domain-analysis', [NavigationAnalyticsController::class, 'getDomainAnalysis'])->name('domain_analysis');
        Route::get('/search-analysis', [NavigationAnalyticsController::class, 'getSearchAnalysis'])->name('search_analysis');
        Route::get('/recommendation-analysis', [NavigationAnalyticsController::class, 'getRecommendationAnalysis'])->name('recommendation_analysis');
        
        // 实时数据
        Route::get('/real-time', [NavigationAnalyticsController::class, 'getRealTimeStats'])->name('real_time');
        Route::get('/performance', [NavigationAnalyticsController::class, 'getPerformanceMetrics'])->name('performance');
        Route::get('/anomaly-detection', [NavigationAnalyticsController::class, 'getAnomalyDetection'])->name('anomaly_detection');
        
        // 报告功能
        Route::middleware(['can:generate-navigation-reports'])->group(function () {
            Route::post('/reports', [NavigationAnalyticsController::class, 'generateReport'])->name('generate_report');
        });
        
        // 导出功能
        Route::middleware(['can:export-navigation-data'])->group(function () {
            Route::post('/export', [NavigationAnalyticsController::class, 'exportData'])->name('export');
        });
    });

    // ========== 缓存管理API (管理员专用) ==========
    Route::prefix('cache')->name('cache.')->middleware(['auth:sanctum', 'role:super_admin|admin'])->group(function () {
        Route::post('/clear', function () {
            $cacheService = app(\App\Services\NavigationCacheService::class);
            $cacheService->clearAllNavigationCache();
            
            return response()->json([
                'success' => true,
                'message' => '导航缓存已清除'
            ]);
        })->name('clear');
        
        Route::post('/warmup', function () {
            $cacheService = app(\App\Services\NavigationCacheService::class);
            $cacheService->warmupCache();
            
            return response()->json([
                'success' => true,
                'message' => '缓存预热已启动'
            ]);
        })->name('warmup');
        
        Route::get('/stats', function () {
            $cacheService = app(\App\Services\NavigationCacheService::class);
            $stats = $cacheService->getCacheStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        })->name('stats');
    });

    // ========== 系统健康检查API ==========
    Route::get('/health', function () {
        try {
            // 检查数据库连接
            \App\Models\NavigationMenu::count();
            
            // 检查缓存连接
            $cacheService = app(\App\Services\NavigationCacheService::class);
            $cacheStats = $cacheService->getCacheStats();
            
            return response()->json([
                'success' => true,
                'status' => 'healthy',
                'timestamp' => now()->timestamp,
                'components' => [
                    'database' => 'ok',
                    'cache' => $cacheStats['status'] ?? 'unknown',
                    'navigation_system' => 'ok'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status' => 'unhealthy',
                'error' => config('app.debug') ? $e->getMessage() : 'System error',
                'timestamp' => now()->timestamp
            ], 500);
        }
    })->name('health');
});

// ========== 向后兼容的路由别名 ==========
Route::prefix('nav')->group(function () {
    // 兼容旧版API
    Route::get('/', [NavigationController::class, 'getNavigation']);
    Route::post('/preferences', [UserNavigationPreferenceController::class, 'updatePreferences'])->middleware('auth:sanctum');
    Route::get('/search', [NavigationSearchController::class, 'search']);
});