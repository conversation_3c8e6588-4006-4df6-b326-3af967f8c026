import request from '@/utils/request'
import { mockPermissionAPI } from './mock/permission'

// 检查是否使用模拟数据
const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' ||
                (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL)

// ==================== 角色管理 ====================

// 获取角色列表
export function getRoleList(params) {
  if (useMock) {
    return mockPermissionAPI.getRoleList(params)
  }
  return request({
    url: '/admin/roles',
    method: 'get',
    params
  })
}

// 获取角色统计
export function getRoleStats() {
  if (useMock) {
    return mockPermissionAPI.getRoleStats()
  }
  return request({
    url: '/admin/roles/stats',
    method: 'get'
  })
}

// 获取角色详情
export function getRoleDetail(id) {
  return request({
    url: `/admin/roles/${id}`,
    method: 'get'
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/admin/roles',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/admin/roles/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  if (useMock) {
    return mockPermissionAPI.deleteRole(id)
  }
  return request({
    url: `/admin/roles/${id}`,
    method: 'delete'
  })
}

// 更新角色状态
export function updateRoleStatus(id, status) {
  if (useMock) {
    return mockPermissionAPI.updateRoleStatus(id, status)
  }
  return request({
    url: `/admin/roles/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新角色状态
export function batchUpdateRoleStatus(roleIds, status) {
  return request({
    url: '/admin/roles/batch-status',
    method: 'put',
    data: { role_ids: roleIds, status }
  })
}

// 复制角色
export function copyRole(id, data) {
  return request({
    url: `/admin/roles/${id}/copy`,
    method: 'post',
    data
  })
}

// 获取角色权限
export function getRolePermissions(id) {
  return request({
    url: `/admin/roles/${id}/permissions`,
    method: 'get'
  })
}

// 更新角色权限
export function updateRolePermissions(id, data) {
  return request({
    url: `/admin/roles/${id}/permissions`,
    method: 'put',
    data
  })
}

// 获取角色用户
export function getRoleUsers(id, params) {
  return request({
    url: `/admin/roles/${id}/users`,
    method: 'get',
    params
  })
}

// 为角色分配用户
export function assignUsersToRole(id, data) {
  return request({
    url: `/admin/roles/${id}/users`,
    method: 'post',
    data
  })
}

// 从角色移除用户
export function removeUsersFromRole(id, data) {
  return request({
    url: `/admin/roles/${id}/users`,
    method: 'delete',
    data
  })
}

// ==================== 权限管理 ====================

// 获取权限列表
export function getPermissionList(params) {
  return request({
    url: '/admin/permissions',
    method: 'get',
    params
  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/admin/permissions/tree',
    method: 'get'
  })
}

// 获取权限统计
export function getPermissionStats() {
  return request({
    url: '/admin/permissions/stats',
    method: 'get'
  })
}

// 获取权限详情
export function getPermissionDetail(id) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'get'
  })
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/admin/permissions',
    method: 'post',
    data
  })
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'put',
    data
  })
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'delete'
  })
}

// 更新权限状态
export function updatePermissionStatus(id, status) {
  return request({
    url: `/admin/permissions/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新权限状态
export function batchUpdatePermissionStatus(permissionIds, status) {
  return request({
    url: '/admin/permissions/batch-status',
    method: 'put',
    data: { permission_ids: permissionIds, status }
  })
}

// 获取权限的角色
export function getPermissionRoles(id) {
  return request({
    url: `/admin/permissions/${id}/roles`,
    method: 'get'
  })
}

// ==================== 用户权限管理 ====================

// 获取用户权限
export function getUserPermissions(userId) {
  return request({
    url: `/admin/users/${userId}/permissions`,
    method: 'get'
  })
}

// 更新用户权限
export function updateUserPermissions(userId, data) {
  return request({
    url: `/admin/users/${userId}/permissions`,
    method: 'put',
    data
  })
}

// 获取用户角色
export function getUserRoles(userId) {
  return request({
    url: `/admin/users/${userId}/roles`,
    method: 'get'
  })
}

// 更新用户角色
export function updateUserRoles(userId, data) {
  return request({
    url: `/admin/users/${userId}/roles`,
    method: 'put',
    data
  })
}

// 检查用户权限
export function checkUserPermission(userId, permission) {
  return request({
    url: `/admin/users/${userId}/check-permission`,
    method: 'post',
    data: { permission }
  })
}

// ==================== 权限组管理 ====================

// 获取权限组列表
export function getPermissionGroupList(params) {
  return request({
    url: '/admin/permission-groups',
    method: 'get',
    params
  })
}

// 创建权限组
export function createPermissionGroup(data) {
  return request({
    url: '/admin/permission-groups',
    method: 'post',
    data
  })
}

// 更新权限组
export function updatePermissionGroup(id, data) {
  return request({
    url: `/admin/permission-groups/${id}`,
    method: 'put',
    data
  })
}

// 删除权限组
export function deletePermissionGroup(id) {
  return request({
    url: `/admin/permission-groups/${id}`,
    method: 'delete'
  })
}

// ==================== 权限日志 ====================

// 获取权限操作日志
export function getPermissionLogs(params) {
  return request({
    url: '/admin/permission-logs',
    method: 'get',
    params
  })
}

// 获取用户权限变更历史
export function getUserPermissionHistory(userId, params) {
  return request({
    url: `/admin/users/${userId}/permission-history`,
    method: 'get',
    params
  })
}

// 获取角色权限变更历史
export function getRolePermissionHistory(roleId, params) {
  return request({
    url: `/admin/roles/${roleId}/permission-history`,
    method: 'get',
    params
  })
}

// ==================== 权限导入导出 ====================

// 导出权限配置
export function exportPermissionConfig() {
  return request({
    url: '/admin/permissions/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入权限配置
export function importPermissionConfig(data) {
  return request({
    url: '/admin/permissions/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出角色配置
export function exportRoleConfig() {
  return request({
    url: '/admin/roles/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入角色配置
export function importRoleConfig(data) {
  return request({
    url: '/admin/roles/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}