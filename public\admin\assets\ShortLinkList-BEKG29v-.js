import{_ as l,u as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                 *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css                */import{e as a,f as t,h as s,i,j as u,k as o,s as r,l as n,m as d}from"./anti-block-CmiVNzQG.js";import{Q as c,at as p,aY as _,bp as m,bq as v,b9 as y,b8 as f,by as h,aM as g,U as b,bh as w,bi as k,V as x,a$ as V,bw as C,bx as U,ay as z,bj as j,bg as L,b1 as R,R as q}from"./element-plus-h2SQQM64.js";import{r as M,c as S,e as D,k as $,l as I,t as B,E,z as H,D as T,u as O,F as Q,Y as F,y as P,A as Y,B as A}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const N={class:"short-link-list"},Z={class:"page-header"},G={class:"page-actions"},J={slot:"header",class:"card-header"},K={slot:"header",class:"card-header"},W={class:"short-code-info"},X={class:"short-code"},ll={class:"full-url-info"},el={class:"full-url"},al={class:"original-url"},tl={class:"domain-info"},sl={class:"domain-text"},il={class:"click-count"},ul={class:"pagination-wrapper"},ol={slot:"footer"},rl={class:"switch-domain-content"},nl={slot:"footer"},dl={class:"stats-content"},cl={class:"stats-header"},pl={class:"stats-summary"},_l={class:"summary-item"},ml={class:"summary-value"},vl={class:"summary-item"},yl={class:"summary-value"},fl={class:"summary-item"},hl={class:"summary-value"},gl={class:"qrcode-content"},bl={key:0,class:"qrcode-image"},wl=["src"],kl={class:"qrcode-info"},xl={slot:"footer"},Vl={class:"help-detail"},Cl=l({__name:"ShortLinkList",setup(l){const Cl=e(),Ul=M(!1),zl=M(!1),jl=M(!1),Ll=M(!1),Rl=M(!1),ql=M(!1),Ml=M(!1),Sl=M(!1),Dl=M(!1),$l=M(!1),Il=M([]),Bl=M([]),El=M([]),Hl=M([]),Tl=M({current:1,pageSize:20,total:0}),Ol=M({link_type:"",status:"",domain_id:"",date_range:"",keyword:""}),Ql=M({original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}),Fl={original_url:[{required:!0,message:"请输入原始URL",trigger:"blur"},{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],link_type:[{required:!0,message:"请选择链接类型",trigger:"change"}]},Pl=M({}),Yl=M(""),Al=M({total_clicks:0,today_clicks:0,unique_visitors:0}),Nl=M([]),Zl=M("trend"),Gl=M(""),Jl=[{type:"recruit",name:"推广链接",description:"用于分销员推广的链接"},{type:"payment",name:"支付链接",description:"用于支付页面的链接"},{type:"other",name:"其他链接",description:"其他用途的链接"}],Kl=S(()=>"admin"===Cl.userInfo?.role);D(()=>{Wl(),Xl()});const Wl=async()=>{Ul.value=!0;try{const l={page:Tl.value.current,per_page:Tl.value.pageSize,...Ol.value},{data:e}=await a(l);Il.value=e.data||[],Tl.value.total=e.total||0}catch(l){c.error("加载短链接列表失败")}finally{Ul.value=!1}},Xl=async()=>{try{const{data:l}=await t({per_page:100,status:1});El.value=l.data||[],Hl.value=l.data||[]}catch(l){console.error("加载域名选项失败")}},le=()=>{Ml.value=!1,zl.value=!0,pe()},ee=async()=>{try{Sl.value=!0,Ml.value?(await u(Ql.value.id,Ql.value),c.success("短链接更新成功")):(await o(Ql.value),c.success("短链接创建成功")),zl.value=!1,Wl()}catch(l){if(l.fields)return;c.error(Ml.value?"更新失败":"创建失败")}finally{Sl.value=!1}},ae=async()=>{if(Yl.value){Dl.value=!0;try{await r(Pl.value.id,Yl.value),c.success("域名切换成功"),jl.value=!1,Wl()}catch(l){c.error("域名切换失败")}finally{Dl.value=!1}}else c.warning("请选择新域名")},te=async l=>{$l.value=!0;try{Al.value={total_clicks:1250,today_clicks:45,unique_visitors:890};const{data:e}=await n({link_id:l,limit:50});Nl.value=e.data||[]}catch(e){c.error("加载统计数据失败")}finally{$l.value=!1}},se=()=>{if(!Gl.value)return;const l=document.createElement("a");l.href=Gl.value,l.download=`qrcode_${Pl.value.short_code}.png`,l.click()},ie=l=>{Bl.value=l},ue=async()=>{0!==Bl.value.length&&q.prompt("请输入新域名ID","批量切换域名",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^\d+$/,inputErrorMessage:"请输入有效的域名ID"}).then(async({value:l})=>{try{Bl.value.map(l=>l.id);c.success("批量切换成功"),Wl()}catch(e){c.error("批量切换失败")}})},oe=l=>{navigator.clipboard.writeText(l).then(()=>{c.success("复制成功")}).catch(()=>{c.error("复制失败")})},re=async()=>{try{const l=await d(Ol.value),e=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download=`short_links_${(new Date).toISOString().split("T")[0]}.xlsx`,t.click(),window.URL.revokeObjectURL(a)}catch(l){c.error("导出失败")}},ne=()=>{Ol.value={link_type:"",status:"",domain_id:"",date_range:"",keyword:""},Wl()},de=l=>{Tl.value.pageSize=l,Wl()},ce=l=>{Tl.value.current=l,Wl()},pe=()=>{Ql.value={original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}},_e=()=>{ql.value=!0},me=l=>({recruit:"推广链接",payment:"支付链接",other:"其他链接"}[l]||l),ve=l=>({1:"正常",2:"异常",3:"禁用",4:"过期"}[l]||"未知"),ye=l=>l?new Date(l).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return(l,e)=>{const a=p,t=_,u=f,o=y,r=v,n=h,d=g,c=m,q=k,M=x,S=V,D=w,Cl=U,Xl=z,fe=L,he=j,ge=R,be=C;return I(),$("div",N,[B("div",Z,[e[19]||(e[19]=B("div",{class:"page-title"},[B("h1",null,"🔗 短链接管理"),B("p",{class:"page-desc"},"管理系统生成的防红短链接，监控访问情况和域名使用")],-1)),B("div",G,[E(a,{type:"primary",onClick:le},{default:H(()=>e[16]||(e[16]=[B("i",{class:"el-icon-plus"},null,-1),T(" 创建短链接 ",-1)])),_:1,__:[16]}),E(a,{type:"success",onClick:O(s),disabled:1!==Bl.value.length},{default:H(()=>e[17]||(e[17]=[B("i",{class:"el-icon-s-grid"},null,-1),T(" 生成二维码 ",-1)])),_:1,__:[17]},8,["onClick","disabled"]),E(a,{type:"info",onClick:re},{default:H(()=>e[18]||(e[18]=[B("i",{class:"el-icon-download"},null,-1),T(" 导出数据 ",-1)])),_:1,__:[18]})])]),E(t,{class:"help-card",style:{"margin-bottom":"20px"}},{default:H(()=>[B("div",J,[e[21]||(e[21]=B("span",null,"💡 短链接使用说明",-1)),E(a,{type:"text",onClick:_e},{default:H(()=>e[20]||(e[20]=[T("查看详情",-1)])),_:1,__:[20]})]),e[22]||(e[22]=B("div",{class:"help-content"},[B("div",{class:"help-tips"},[B("div",{class:"tip-item"},[B("i",{class:"el-icon-info",style:{color:"#409eff"}}),B("span",null,[B("strong",null,"自动生成："),T("分销员推广链接会自动生成防红短链接")])]),B("div",{class:"tip-item"},[B("i",{class:"el-icon-success",style:{color:"#67c23a"}}),B("span",null,[B("strong",null,"智能切换："),T("域名异常时自动切换到备用域名")])]),B("div",{class:"tip-item"},[B("i",{class:"el-icon-view",style:{color:"#e6a23c"}}),B("span",null,[B("strong",null,"实时统计："),T("详细记录每次访问的数据和来源")])])])],-1))]),_:1,__:[22]}),E(t,{class:"filter-card"},{default:H(()=>[E(c,{inline:!0,model:Ol.value,class:"filter-form"},{default:H(()=>[E(r,{label:"链接类型"},{default:H(()=>[E(o,{modelValue:Ol.value.link_type,"onUpdate:modelValue":e[0]||(e[0]=l=>Ol.value.link_type=l),placeholder:"全部类型",clearable:""},{default:H(()=>[E(u,{label:"推广链接",value:"recruit"}),E(u,{label:"支付链接",value:"payment"}),E(u,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),E(r,{label:"链接状态"},{default:H(()=>[E(o,{modelValue:Ol.value.status,"onUpdate:modelValue":e[1]||(e[1]=l=>Ol.value.status=l),placeholder:"全部状态",clearable:""},{default:H(()=>[E(u,{label:"正常",value:1}),E(u,{label:"异常",value:2}),E(u,{label:"禁用",value:3}),E(u,{label:"过期",value:4})]),_:1},8,["modelValue"])]),_:1}),E(r,{label:"域名"},{default:H(()=>[E(o,{modelValue:Ol.value.domain_id,"onUpdate:modelValue":e[2]||(e[2]=l=>Ol.value.domain_id=l),placeholder:"全部域名",clearable:""},{default:H(()=>[(I(!0),$(Q,null,F(El.value,l=>(I(),P(u,{key:l.id,label:l.domain,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),E(r,{label:"创建时间"},{default:H(()=>[E(n,{modelValue:Ol.value.date_range,"onUpdate:modelValue":e[3]||(e[3]=l=>Ol.value.date_range=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},null,8,["modelValue"])]),_:1}),E(r,{label:"关键词"},{default:H(()=>[E(d,{modelValue:Ol.value.keyword,"onUpdate:modelValue":e[4]||(e[4]=l=>Ol.value.keyword=l),placeholder:"短链接代码或原始URL",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),E(r,null,{default:H(()=>[E(a,{type:"primary",onClick:Wl},{default:H(()=>e[23]||(e[23]=[T("查询",-1)])),_:1,__:[23]}),E(a,{onClick:ne},{default:H(()=>e[24]||(e[24]=[T("重置",-1)])),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),_:1}),E(t,null,{default:H(()=>[B("div",K,[B("span",null,"短链接列表 ("+b(Tl.value.total)+")",1),B("div",null,[Kl.value?(I(),P(a,{key:0,type:"warning",size:"small",onClick:ue,disabled:0===Bl.value.length},{default:H(()=>e[25]||(e[25]=[T(" 批量切换域名 ",-1)])),_:1,__:[25]},8,["disabled"])):A("",!0),Kl.value?(I(),P(a,{key:1,type:"danger",size:"small",onClick:l.batchDelete,disabled:0===Bl.value.length},{default:H(()=>e[26]||(e[26]=[T(" 批量删除 ",-1)])),_:1,__:[26]},8,["onClick","disabled"])):A("",!0)])]),Y((I(),P(D,{data:Il.value,onSelectionChange:ie,stripe:""},{default:H(()=>[E(q,{type:"selection",width:"55"}),E(q,{prop:"short_code",label:"短链接",width:"120"},{default:H(l=>[B("div",W,[B("span",X,b(l.row.short_code),1),E(a,{type:"text",size:"mini",onClick:e=>{return a=l.row,void oe(a.full_url);var a},style:{"margin-left":"5px"}},{default:H(()=>e[27]||(e[27]=[B("i",{class:"el-icon-copy-document"},null,-1)])),_:2,__:[27]},1032,["onClick"])])]),_:1}),E(q,{prop:"full_url",label:"完整链接",width:"200"},{default:H(l=>[B("div",ll,[B("span",el,b(l.row.full_url),1),E(a,{type:"text",size:"mini",onClick:e=>{return a=l.row,void oe(a.full_url);var a},style:{"margin-left":"5px"}},{default:H(()=>e[28]||(e[28]=[B("i",{class:"el-icon-copy-document"},null,-1)])),_:2,__:[28]},1032,["onClick"])])]),_:1}),E(q,{prop:"original_url",label:"原始URL","min-width":"250"},{default:H(l=>[B("div",al,[E(M,{content:l.row.original_url,placement:"top"},{default:H(()=>{return[B("span",null,b((e=l.row.original_url,a=40,e?e.length>a?e.substring(0,a)+"...":e:"")),1)];var e,a}),_:2},1032,["content"])])]),_:1}),E(q,{prop:"domain",label:"使用域名",width:"150"},{default:H(l=>{return[B("div",tl,[B("span",sl,b(l.row.domain.domain),1),E(S,{type:(e=l.row.domain.health_score,e>=90?"success":e>=80?"primary":e>=60?"warning":"danger"),size:"mini",style:{"margin-left":"5px"}},{default:H(()=>[T(b(l.row.domain.health_score)+"% ",1)]),_:2},1032,["type"])])];var e}),_:1}),E(q,{prop:"link_type",label:"类型",width:"100"},{default:H(l=>{return[E(S,{size:"small",type:(e=l.row.link_type,{recruit:"primary",payment:"success",other:"info"}[e]||"")},{default:H(()=>[T(b(me(l.row.link_type)),1)]),_:2},1032,["type"])];var e}),_:1}),E(q,{prop:"status",label:"状态",width:"80"},{default:H(l=>{return[E(S,{type:(e=l.row.status,{1:"success",2:"warning",3:"danger",4:"info"}[e]||""),size:"small"},{default:H(()=>[T(b(ve(l.row.status)),1)]),_:2},1032,["type"])];var e}),_:1}),E(q,{prop:"click_count",label:"点击量",width:"80"},{default:H(l=>[B("span",il,b(l.row.click_count),1)]),_:1}),E(q,{prop:"created_at",label:"创建时间",width:"140"},{default:H(l=>[B("span",null,b(ye(l.row.created_at)),1)]),_:1}),E(q,{prop:"last_click_at",label:"最后访问",width:"140"},{default:H(l=>[B("span",null,b(ye(l.row.last_click_at)),1)]),_:1}),E(q,{label:"操作",width:"180",fixed:"right"},{default:H(l=>[E(a,{type:"text",size:"small",onClick:e=>(async l=>{Pl.value=l,Ll.value=!0,await te(l.id)})(l.row)},{default:H(()=>e[29]||(e[29]=[T(" 统计 ",-1)])),_:2,__:[29]},1032,["onClick"]),Kl.value?(I(),P(a,{key:0,type:"text",size:"small",onClick:e=>{return a=l.row,Pl.value=a,Yl.value=a.domain.id,void(jl.value=!0);var a}},{default:H(()=>e[30]||(e[30]=[T(" 切换域名 ",-1)])),_:2,__:[30]},1032,["onClick"])):A("",!0),Kl.value?(I(),P(a,{key:1,type:"text",size:"small",onClick:e=>{return a=l.row,Ml.value=!0,zl.value=!0,void(Ql.value={...a,domain_id:a.domain.id});var a}},{default:H(()=>e[31]||(e[31]=[T(" 编辑 ",-1)])),_:2,__:[31]},1032,["onClick"])):A("",!0),Kl.value?(I(),P(a,{key:2,type:"text",size:"small",onClick:e=>O(i)(l.row),style:{color:"#f56c6c"}},{default:H(()=>e[32]||(e[32]=[T(" 删除 ",-1)])),_:2,__:[32]},1032,["onClick"])):A("",!0)]),_:1})]),_:1},8,["data"])),[[be,Ul.value]]),B("div",ul,[E(Cl,{onSizeChange:de,onCurrentChange:ce,"current-page":Tl.value.current,"page-sizes":[10,20,50,100],"page-size":Tl.value.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:Tl.value.total},null,8,["current-page","page-size","total"])])]),_:1}),E(Xl,{title:Ml.value?"编辑短链接":"创建短链接",visible:zl.value,width:"500px",onClose:pe},{default:H(()=>[E(c,{model:Ql.value,rules:Fl,ref_key:"linkForm",ref:Ql,"label-width":"100px"},{default:H(()=>[E(r,{label:"原始URL",prop:"original_url"},{default:H(()=>[E(d,{modelValue:Ql.value.original_url,"onUpdate:modelValue":e[5]||(e[5]=l=>Ql.value.original_url=l),placeholder:"请输入完整的URL地址",type:"textarea",rows:"2"},null,8,["modelValue"]),e[33]||(e[33]=B("div",{class:"form-tip"}," 🔗 请输入完整的URL地址，包含 http:// 或 https:// ",-1))]),_:1,__:[33]}),E(r,{label:"链接类型",prop:"link_type"},{default:H(()=>[E(o,{modelValue:Ql.value.link_type,"onUpdate:modelValue":e[6]||(e[6]=l=>Ql.value.link_type=l),placeholder:"选择链接类型",style:{width:"100%"}},{default:H(()=>[E(u,{label:"推广链接",value:"recruit"}),E(u,{label:"支付链接",value:"payment"}),E(u,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),E(r,{label:"选择域名",prop:"domain_id"},{default:H(()=>[E(o,{modelValue:Ql.value.domain_id,"onUpdate:modelValue":e[7]||(e[7]=l=>Ql.value.domain_id=l),placeholder:"选择域名",style:{width:"100%"}},{default:H(()=>[(I(!0),$(Q,null,F(Hl.value,l=>(I(),P(u,{key:l.id,label:`${l.domain} (健康度: ${l.health_score}%)`,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e[34]||(e[34]=B("div",{class:"form-tip"}," 💡 系统会自动选择最佳域名，也可手动指定 ",-1))]),_:1,__:[34]}),E(r,{label:"自定义代码"},{default:H(()=>[E(d,{modelValue:Ql.value.custom_code,"onUpdate:modelValue":e[8]||(e[8]=l=>Ql.value.custom_code=l),placeholder:"留空则自动生成",maxlength:"20"},null,8,["modelValue"]),e[35]||(e[35]=B("div",{class:"form-tip"}," 🎯 自定义短链接代码，仅支持字母数字，留空则自动生成 ",-1))]),_:1,__:[35]}),E(r,{label:"有效期"},{default:H(()=>[E(n,{modelValue:Ql.value.expires_at,"onUpdate:modelValue":e[9]||(e[9]=l=>Ql.value.expires_at=l),type:"datetime",placeholder:"选择过期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),e[36]||(e[36]=B("div",{class:"form-tip"}," ⏰ 留空则永久有效，过期后链接将无法访问 ",-1))]),_:1,__:[36]}),E(r,{label:"备注"},{default:H(()=>[E(d,{modelValue:Ql.value.remarks,"onUpdate:modelValue":e[10]||(e[10]=l=>Ql.value.remarks=l),type:"textarea",rows:"2",placeholder:"链接用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),B("div",ol,[E(a,{onClick:e[11]||(e[11]=l=>zl.value=!1)},{default:H(()=>e[37]||(e[37]=[T("取消",-1)])),_:1,__:[37]}),E(a,{type:"primary",onClick:ee,loading:Sl.value},{default:H(()=>[T(b(Ml.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1},8,["title","visible"]),E(Xl,{title:"切换域名",visible:jl.value,width:"400px"},{default:H(()=>[B("div",rl,[B("p",null,[e[38]||(e[38]=T("为短链接 ",-1)),B("strong",null,b(Pl.value.short_code),1),e[39]||(e[39]=T(" 切换域名：",-1))]),E(o,{modelValue:Yl.value,"onUpdate:modelValue":e[12]||(e[12]=l=>Yl.value=l),placeholder:"选择新域名",style:{width:"100%"}},{default:H(()=>[(I(!0),$(Q,null,F(Hl.value,l=>(I(),P(u,{key:l.id,label:`${l.domain} (健康度: ${l.health_score}%)`,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e[40]||(e[40]=B("div",{class:"form-tip",style:{"margin-top":"10px"}}," 💡 切换域名后，原链接将重定向到新域名 ",-1))]),B("div",nl,[E(a,{onClick:e[13]||(e[13]=l=>jl.value=!1)},{default:H(()=>e[41]||(e[41]=[T("取消",-1)])),_:1,__:[41]}),E(a,{type:"primary",onClick:ae,loading:Dl.value},{default:H(()=>e[42]||(e[42]=[T("切换",-1)])),_:1,__:[42]},8,["loading"])])]),_:1},8,["visible"]),E(Xl,{title:"访问统计",visible:Ll.value,width:"800px"},{default:H(()=>[B("div",dl,[B("div",cl,[B("h4",null,b(Pl.value.short_code)+" 的访问统计",1),B("div",pl,[B("div",_l,[e[43]||(e[43]=B("span",{class:"summary-label"},"总访问量",-1)),B("span",ml,b(Al.value.total_clicks),1)]),B("div",vl,[e[44]||(e[44]=B("span",{class:"summary-label"},"今日访问",-1)),B("span",yl,b(Al.value.today_clicks),1)]),B("div",fl,[e[45]||(e[45]=B("span",{class:"summary-label"},"独立访客",-1)),B("span",hl,b(Al.value.unique_visitors),1)])])]),E(he,{modelValue:Zl.value,"onUpdate:modelValue":e[14]||(e[14]=l=>Zl.value=l)},{default:H(()=>[E(fe,{label:"访问趋势",name:"trend"},{default:H(()=>e[46]||(e[46]=[B("div",{class:"chart-container"},[B("p",null,"访问趋势图表")],-1)])),_:1,__:[46]}),E(fe,{label:"访问记录",name:"logs"},{default:H(()=>[Y((I(),P(D,{data:Nl.value,size:"small"},{default:H(()=>[E(q,{prop:"access_time",label:"访问时间",width:"140"},{default:H(l=>[B("span",null,b(ye(l.row.access_time)),1)]),_:1}),E(q,{prop:"ip_address",label:"IP地址",width:"120"},{default:H(l=>[B("span",null,b(l.row.ip_address),1)]),_:1}),E(q,{prop:"user_agent",label:"设备信息","min-width":"200"},{default:H(l=>[E(M,{content:l.row.user_agent,placement:"top"},{default:H(()=>{return[B("span",null,b((e=l.row.user_agent,a=30,e?e.length>a?e.substring(0,a)+"...":e:"")),1)];var e,a}),_:2},1032,["content"])]),_:1}),E(q,{prop:"referer",label:"来源","min-width":"150"},{default:H(l=>[B("span",null,b(l.row.referer||"直接访问"),1)]),_:1}),E(q,{prop:"region",label:"地区",width:"100"},{default:H(l=>[B("span",null,b(l.row.region||"-"),1)]),_:1})]),_:1},8,["data"])),[[be,$l.value]])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["visible"]),E(Xl,{title:"二维码",visible:Rl.value,width:"400px"},{default:H(()=>[B("div",gl,[Gl.value?(I(),$("div",bl,[B("img",{src:Gl.value,alt:"二维码"},null,8,wl)])):A("",!0),B("div",kl,[B("p",null,[e[47]||(e[47]=B("strong",null,"短链接：",-1)),T(b(Pl.value.full_url),1)]),B("p",null,[e[48]||(e[48]=B("strong",null,"创建时间：",-1)),T(b(ye(new Date)),1)])])]),B("div",xl,[E(a,{onClick:e[15]||(e[15]=l=>Rl.value=!1)},{default:H(()=>e[49]||(e[49]=[T("关闭",-1)])),_:1,__:[49]}),E(a,{type:"primary",onClick:se},{default:H(()=>e[50]||(e[50]=[T("下载二维码",-1)])),_:1,__:[50]})])]),_:1},8,["visible"]),E(Xl,{title:"短链接使用说明",visible:ql.value,width:"700px"},{default:H(()=>[B("div",Vl,[e[52]||(e[52]=B("h3",null,"🔗 什么是防红短链接？",-1)),e[53]||(e[53]=B("p",null,"防红短链接是经过特殊处理的短链接，能够有效避免被微信、QQ等平台检测和封禁。",-1)),e[54]||(e[54]=B("h3",null,"🚀 主要功能",-1)),e[55]||(e[55]=B("ul",null,[B("li",null,[B("strong",null,"智能域名选择："),T("系统自动选择最健康的域名生成短链接")]),B("li",null,[B("strong",null,"自动域名切换："),T("当域名异常时自动切换到备用域名")]),B("li",null,[B("strong",null,"访问统计分析："),T("详细记录每次访问的数据和来源")]),B("li",null,[B("strong",null,"二维码生成："),T("一键生成短链接二维码")]),B("li",null,[B("strong",null,"批量管理："),T("支持批量操作和数据导出")])],-1)),e[56]||(e[56]=B("h3",null,"📊 链接类型说明",-1)),E(D,{data:Jl,size:"small"},{default:H(()=>[E(q,{prop:"type",label:"类型",width:"100"}),E(q,{prop:"name",label:"名称",width:"100"}),E(q,{prop:"description",label:"说明"})]),_:1}),e[57]||(e[57]=B("h3",null,"⚠️ 使用注意事项",-1)),E(ge,{type:"warning",closable:!1},{default:H(()=>e[51]||(e[51]=[B("ul",{style:{margin:"0","padding-left":"20px"}},[B("li",null,"原始URL必须是完整的地址，包含协议头（http://或https://）"),B("li",null,"自定义代码仅支持字母和数字，建议使用有意义的代码"),B("li",null,"设置合理的过期时间，避免链接长期有效造成安全风险"),B("li",null,"定期检查链接状态，及时处理异常链接")],-1)])),_:1,__:[51]})])]),_:1},8,["visible"])])}}},[["__scopeId","data-v-97f38363"]]);export{Cl as default};
