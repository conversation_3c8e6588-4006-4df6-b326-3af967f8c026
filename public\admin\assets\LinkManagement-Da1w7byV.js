import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{r as a,c as l,L as t,d as s,y as o,l as i,z as u,E as d,J as r,t as n,D as c,n as p,A as m,k as _,F as v,Y as f,B as g,u as h,e as b,a3 as y}from"./vue-vendor-Dy164gUc.js";import{bp as k,bq as w,aM as V,b9 as x,b8 as C,U,by as j,aZ as z,a_ as D,br as S,bm as q,bB as L,at as $,ay as R,Q as M,aY as Y,bn as I,p as O,bh as T,bi as B,bw as P,ax as Q,T as A,a0 as E,b3 as H,bS as N,Y as F,aR as G,bt as W,bu as J,a4 as K,ak as Z,b1 as X,aV as ee,cm as ae,a8 as le,ao as te,a6 as se,c3 as oe,ab as ie,a$ as ue,az as de,aT as re,aB as ne,aC as ce,bx as pe,R as me}from"./element-plus-h2SQQM64.js";import{S as _e}from"./StatCard-u_ssO_Ky.js";/* empty css                     *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                       */import{p as ve,a as fe,s as ge}from"./promotion-DjIFk3EX.js";/* empty css                        *//* empty css                 *//* empty css                          */import{f as he}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const be={class:"dialog-footer"},ye=e({__name:"LinkDialog",props:{modelValue:{type:Boolean,default:!1},linkData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:m}){const _=e,v=m,f=a(),g=a(!1),h=l({get:()=>_.modelValue,set:e=>v("update:modelValue",e)}),b=l(()=>_.linkData&&_.linkData.id),y=t({name:"",target_url:"",type:"",custom_code:"",expires_at:"",max_clicks:0,daily_limit:0,status:"active",description:""}),Y={name:[{required:!0,message:"请输入链接名称",trigger:"blur"},{min:2,max:50,message:"链接名称长度在 2 到 50 个字符",trigger:"blur"}],target_url:[{required:!0,message:"请输入目标URL",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的URL地址",trigger:"blur"}],type:[{required:!0,message:"请选择链接类型",trigger:"change"}],custom_code:[{pattern:/^[a-zA-Z0-9-]*$/,message:"自定义短码只能包含字母、数字和连字符",trigger:"blur"}]};s(()=>_.modelValue,e=>{e&&p(()=>{I()})});const I=()=>{b.value?Object.keys(y).forEach(e=>{void 0!==_.linkData[e]&&(y[e]=_.linkData[e])}):Object.keys(y).forEach(e=>{y[e]="status"===e?"active":"max_clicks"===e||"daily_limit"===e?0:""}),f.value&&f.value.clearValidate()},O=async()=>{if(f.value)try{await f.value.validate(),g.value=!0;const e={...y};e.expires_at||delete e.expires_at,e.custom_code||delete e.custom_code,e.description||delete e.description,b.value?(await ve.update(_.linkData.id,e),M.success("推广链接更新成功")):(await ve.create(e),M.success("推广链接创建成功")),v("success"),T()}catch(e){console.error("提交失败:",e),e.response?.data?.message?M.error(e.response.data.message):M.error(b.value?"更新失败":"创建失败")}finally{g.value=!1}},T=()=>{h.value=!1};return(e,a)=>{const l=V,t=w,s=C,p=x,m=j,_=S,v=D,M=z,I=L,B=q,P=k,Q=$,A=R;return i(),o(A,{modelValue:h.value,"onUpdate:modelValue":a[10]||(a[10]=e=>h.value=e),title:b.value?"编辑推广链接":"创建推广链接",width:"600px","before-close":T},{footer:u(()=>[n("div",be,[d(Q,{onClick:T},{default:u(()=>a[16]||(a[16]=[c("取消",-1)])),_:1,__:[16]}),d(Q,{type:"primary",loading:g.value,onClick:O},{default:u(()=>[c(U(b.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:u(()=>[d(P,{ref_key:"formRef",ref:f,model:y,rules:Y,"label-width":"100px",onSubmit:a[9]||(a[9]=r(()=>{},["prevent"]))},{default:u(()=>[d(t,{label:"链接名称",prop:"name"},{default:u(()=>[d(l,{modelValue:y.name,"onUpdate:modelValue":a[0]||(a[0]=e=>y.name=e),placeholder:"请输入链接名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(t,{label:"目标URL",prop:"target_url"},{default:u(()=>[d(l,{modelValue:y.target_url,"onUpdate:modelValue":a[1]||(a[1]=e=>y.target_url=e),placeholder:"请输入目标URL，如：https://example.com",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),d(t,{label:"链接类型",prop:"type"},{default:u(()=>[d(p,{modelValue:y.type,"onUpdate:modelValue":a[2]||(a[2]=e=>y.type=e),placeholder:"请选择链接类型",style:{width:"100%"}},{default:u(()=>[d(s,{label:"群组推广",value:"group"}),d(s,{label:"分销推广",value:"distribution"}),d(s,{label:"活动推广",value:"activity"}),d(s,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"自定义短码",prop:"custom_code"},{default:u(()=>[d(l,{modelValue:y.custom_code,"onUpdate:modelValue":a[3]||(a[3]=e=>y.custom_code=e),placeholder:"留空则自动生成，支持字母数字组合",maxlength:"20"},{prepend:u(()=>[n("span",null,U("https://short.example.com")+"/")]),_:1},8,["modelValue"]),a[11]||(a[11]=n("div",{class:"form-help"},[n("i",{class:"el-icon-info"}),c(" 自定义短码必须唯一，支持字母、数字和连字符 ")],-1))]),_:1,__:[11]}),d(t,{label:"过期时间",prop:"expires_at"},{default:u(()=>[d(m,{modelValue:y.expires_at,"onUpdate:modelValue":a[4]||(a[4]=e=>y.expires_at=e),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),a[12]||(a[12]=n("div",{class:"form-help"},[n("i",{class:"el-icon-info"}),c(" 留空表示永不过期 ")],-1))]),_:1,__:[12]}),d(t,{label:"访问限制"},{default:u(()=>[d(M,{gutter:16},{default:u(()=>[d(v,{span:12},{default:u(()=>[d(t,{prop:"max_clicks"},{default:u(()=>[d(_,{modelValue:y.max_clicks,"onUpdate:modelValue":a[5]||(a[5]=e=>y.max_clicks=e),min:0,placeholder:"最大点击次数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(v,{span:12},{default:u(()=>[d(t,{prop:"daily_limit"},{default:u(()=>[d(_,{modelValue:y.daily_limit,"onUpdate:modelValue":a[6]||(a[6]=e=>y.daily_limit=e),min:0,placeholder:"每日访问限制",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a[13]||(a[13]=n("div",{class:"form-help"},[n("i",{class:"el-icon-info"}),c(" 设置为0表示无限制 ")],-1))]),_:1,__:[13]}),d(t,{label:"状态",prop:"status"},{default:u(()=>[d(B,{modelValue:y.status,"onUpdate:modelValue":a[7]||(a[7]=e=>y.status=e)},{default:u(()=>[d(I,{label:"active"},{default:u(()=>a[14]||(a[14]=[c("启用",-1)])),_:1,__:[14]}),d(I,{label:"paused"},{default:u(()=>a[15]||(a[15]=[c("暂停",-1)])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"备注",prop:"description"},{default:u(()=>[d(l,{modelValue:y.description,"onUpdate:modelValue":a[8]||(a[8]=e=>y.description=e),type:"textarea",rows:3,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-5e83a5ef"]]),ke={class:"analytics-container"},we={class:"stats-overview"},Ve={class:"stat-item"},xe={class:"stat-value"},Ce={class:"stat-item"},Ue={class:"stat-value"},je={class:"stat-item"},ze={class:"stat-value"},De={class:"stat-item"},Se={class:"stat-value"},qe={class:"filter-section"},Le={class:"card-header"},$e={class:"chart-container"},Re={key:0,class:"chart-placeholder"},Me={key:1,class:"trend-chart"},Ye={class:"chart-demo"},Ie={class:"chart-data"},Oe={class:"date"},Te={class:"value"},Be={class:"source-stats"},Pe={class:"source-info"},Qe={class:"source-name"},Ae={class:"source-count"},Ee={class:"source-bar"},He={class:"source-percentage"},Ne={class:"region-stats"},Fe={class:"region-info"},Ge={class:"region-name"},We={class:"region-count"},Je={class:"region-bar"},Ke={class:"region-percentage"},Ze=["title"],Xe=["title"],ea={class:"drawer-footer"},aa=e({__name:"LinkAnalyticsDrawer",props:{modelValue:{type:Boolean,default:!1},linkId:{type:[Number,String],default:null}},emits:["update:modelValue"],setup(e,{emit:r}){const p=e,g=r,h=a(!1),b=a("clicks"),y=a([]),k=l({get:()=>p.modelValue,set:e=>g("update:modelValue",e)}),w=t({total_clicks:0,unique_visitors:0,conversions:0}),V=a([]),x=a([]),C=a([]),S=a([]),L=l(()=>0===w.total_clicks?"0.0":(w.conversions/w.total_clicks*100).toFixed(1));s(()=>p.modelValue,e=>{e&&p.linkId&&(R(),A())});const R=()=>{const e=new Date,a=new Date;a.setDate(e.getDate()-7),y.value=[a.toISOString().split("T")[0],e.toISOString().split("T")[0]]},A=async()=>{if(p.linkId){h.value=!0;try{const e={start_date:y.value[0],end_date:y.value[1]},a=await fe.getClickTrend({link_id:p.linkId,...e});w.total_clicks=a.data.total_clicks||0,w.unique_visitors=a.data.unique_visitors||0;const l=await fe.getConversionFunnel({link_id:p.linkId,...e});w.conversions=l.data.total_conversions||0,E(),H(),N(),F()}catch(e){console.error("加载统计数据失败:",e),M.error("加载统计数据失败")}finally{h.value=!1}}},E=()=>{V.value=[];for(let e=6;e>=0;e--){const a=new Date;a.setDate(a.getDate()-e),V.value.push({date:a.toISOString().split("T")[0],value:Math.floor(100*Math.random())+10})}},H=()=>{x.value=[{name:"微信",count:156,percentage:45},{name:"QQ",count:89,percentage:26},{name:"直接访问",count:67,percentage:19},{name:"其他",count:34,percentage:10}]},N=()=>{C.value=[{name:"广东",count:89,percentage:32},{name:"浙江",count:67,percentage:24},{name:"江苏",count:45,percentage:16},{name:"上海",count:23,percentage:8},{name:"其他",count:56,percentage:20}]},F=()=>{S.value=[{ip:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",referer:"https://example.com",location:"广东深圳",created_at:(new Date).toISOString()},{ip:"***********",user_agent:"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",referer:"https://weixin.qq.com",location:"浙江杭州",created_at:new Date(Date.now()-36e5).toISOString()}]},G=()=>{E()},W=()=>{M.success("数据导出功能开发中...")},J=(e,a)=>e?e.length>a?e.substring(0,a)+"...":e:"",K=()=>{k.value=!1};return(e,a)=>{const l=D,t=z,s=j,r=$,p=I,g=q,R=Y,M=B,E=T,H=Q,N=P;return i(),o(H,{modelValue:k.value,"onUpdate:modelValue":a[2]||(a[2]=e=>k.value=e),title:"链接统计分析",size:"60%",direction:"rtl","before-close":K},{footer:u(()=>[n("div",ea,[d(r,{onClick:W},{default:u(()=>a[16]||(a[16]=[c("导出数据",-1)])),_:1,__:[16]}),d(r,{type:"primary",onClick:K},{default:u(()=>a[17]||(a[17]=[c("关闭",-1)])),_:1,__:[17]})])]),default:u(()=>[m((i(),_("div",ke,[n("div",we,[d(t,{gutter:16},{default:u(()=>[d(l,{span:6},{default:u(()=>[n("div",Ve,[n("div",xe,U(w.total_clicks||0),1),a[3]||(a[3]=n("div",{class:"stat-label"},"总点击量",-1))])]),_:1}),d(l,{span:6},{default:u(()=>[n("div",Ce,[n("div",Ue,U(w.unique_visitors||0),1),a[4]||(a[4]=n("div",{class:"stat-label"},"独立访客",-1))])]),_:1}),d(l,{span:6},{default:u(()=>[n("div",je,[n("div",ze,U(w.conversions||0),1),a[5]||(a[5]=n("div",{class:"stat-label"},"转化次数",-1))])]),_:1}),d(l,{span:6},{default:u(()=>[n("div",De,[n("div",Se,U(L.value)+"%",1),a[6]||(a[6]=n("div",{class:"stat-label"},"转化率",-1))])]),_:1})]),_:1})]),n("div",qe,[d(s,{modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=e=>y.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:A},null,8,["modelValue"]),d(r,{type:"primary",onClick:A},{default:u(()=>a[7]||(a[7]=[c("刷新数据",-1)])),_:1,__:[7]})]),d(R,{class:"chart-card"},{header:u(()=>[n("div",Le,[a[11]||(a[11]=n("span",null,"📈 访问趋势",-1)),d(g,{modelValue:b.value,"onUpdate:modelValue":a[1]||(a[1]=e=>b.value=e),size:"small",onChange:G},{default:u(()=>[d(p,{label:"clicks"},{default:u(()=>a[8]||(a[8]=[c("点击量",-1)])),_:1,__:[8]}),d(p,{label:"visitors"},{default:u(()=>a[9]||(a[9]=[c("访客数",-1)])),_:1,__:[9]}),d(p,{label:"conversions"},{default:u(()=>a[10]||(a[10]=[c("转化数",-1)])),_:1,__:[10]})]),_:1},8,["modelValue"])])]),default:u(()=>[n("div",$e,[V.value.length?(i(),_("div",Me,[n("div",Ye,[n("h4",null,U({clicks:"点击量趋势",visitors:"访客数趋势",conversions:"转化数趋势"}[b.value]||"数据趋势"),1),n("div",Ie,[(i(!0),_(v,null,f(V.value,(e,a)=>(i(),_("div",{key:a,class:"data-point"},[n("span",Oe,U(e.date),1),n("span",Te,U(e.value),1)]))),128))])])])):(i(),_("div",Re,a[12]||(a[12]=[n("i",{class:"el-icon-loading"},null,-1),n("p",null,"加载中...",-1)])))])]),_:1}),d(t,{gutter:16},{default:u(()=>[d(l,{span:12},{default:u(()=>[d(R,{class:"data-card"},{header:u(()=>a[13]||(a[13]=[n("span",null,"📱 访问来源",-1)])),default:u(()=>[n("div",Be,[(i(!0),_(v,null,f(x.value,e=>(i(),_("div",{key:e.name,class:"source-item"},[n("div",Pe,[n("span",Qe,U(e.name),1),n("span",Ae,U(e.count),1)]),n("div",Ee,[n("div",{class:"source-fill",style:O({width:e.percentage+"%"})},null,4)]),n("span",He,U(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1}),d(l,{span:12},{default:u(()=>[d(R,{class:"data-card"},{header:u(()=>a[14]||(a[14]=[n("span",null,"🌍 地区分布",-1)])),default:u(()=>[n("div",Ne,[(i(!0),_(v,null,f(C.value,e=>(i(),_("div",{key:e.name,class:"region-item"},[n("div",Fe,[n("span",Ge,U(e.name),1),n("span",We,U(e.count),1)]),n("div",Je,[n("div",{class:"region-fill",style:O({width:e.percentage+"%"})},null,4)]),n("span",Ke,U(e.percentage)+"%",1)]))),128))])]),_:1})]),_:1})]),_:1}),d(R,{class:"records-card"},{header:u(()=>a[15]||(a[15]=[n("span",null,"📋 最近访问记录",-1)])),default:u(()=>[d(E,{data:S.value,style:{width:"100%"}},{default:u(()=>[d(M,{prop:"ip",label:"IP地址",width:"120"}),d(M,{prop:"user_agent",label:"用户代理","min-width":"200"},{default:u(({row:e})=>[n("div",{class:"user-agent",title:e.user_agent},U(J(e.user_agent,50)),9,Ze)]),_:1}),d(M,{prop:"referer",label:"来源页面",width:"150"},{default:u(({row:e})=>[n("div",{class:"referer",title:e.referer},U(J(e.referer,30)),9,Xe)]),_:1}),d(M,{prop:"location",label:"地区",width:"100"}),d(M,{prop:"created_at",label:"访问时间",width:"160"},{default:u(({row:e})=>{return[c(U((a=e.created_at,a?new Date(a).toLocaleString("zh-CN"):"")),1)];var a}),_:1})]),_:1},8,["data"])]),_:1})])),[[N,h.value]])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-2891b7de"]]),la={key:0,class:"qrcode-content"},ta={class:"card-header"},sa={class:"card-header"},oa={class:"header-actions"},ia={class:"preview-container"},ua={key:0,class:"loading-container"},da={key:1,class:"qrcode-preview"},ra=["src","alt"],na={class:"qrcode-info"},ca={class:"qrcode-title"},pa={class:"qrcode-url"},ma={class:"qrcode-size"},_a={key:2,class:"empty-container"},va={class:"card-header"},fa={class:"batch-options"},ga={class:"card-header"},ha={class:"tips-content"},ba={class:"dialog-footer"},ya=e({__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},link:{type:Object,default:null}},emits:["update:modelValue"],setup(e,{emit:r}){const p=e,m=r,v=a(!1),f=a(!1),b=a(""),y=a([200,300,500]),V=t({size:300,margin:2,format:"png",errorCorrection:"M"}),j=l({get:()=>p.modelValue,set:e=>m("update:modelValue",e)}),S=async()=>{if(p.link){v.value=!0;try{const e=await ge.generateQRCode(p.link.short_code,V),a=new Blob([e],{type:`image/${V.format}`});b.value=URL.createObjectURL(a),M.success("二维码生成成功")}catch(e){M.error("生成二维码失败："+e.message)}finally{v.value=!1}}},q=async()=>{if(p.link&&0!==y.value.length){f.value=!0;try{const e=y.value.map(async e=>{const a={...V,size:e},l=await ge.generateQRCode(p.link.short_code,a);return{size:e,blob:new Blob([l],{type:`image/${V.format}`}),filename:`qrcode_${p.link.short_code}_${e}x${e}.${V.format}`}}),a=await Promise.all(e);a.forEach(({blob:e,filename:a})=>{const l=URL.createObjectURL(e),t=document.createElement("a");t.href=l,t.download=a,t.click(),URL.revokeObjectURL(l)}),M.success(`成功生成并下载 ${a.length} 个二维码`)}catch(e){M.error("批量生成失败："+e.message)}finally{f.value=!1}}},L=()=>{if(!b.value)return;const e=document.createElement("a");e.href=b.value,e.download=`qrcode_${p.link.short_code}_${V.size}x${V.size}.${V.format}`,e.click(),M.success("二维码下载成功")},I=async()=>{if(p.link)try{await navigator.clipboard.writeText(p.link.short_url),M.success("链接已复制到剪贴板")}catch(e){M.error("复制失败")}};return s(()=>p.modelValue,e=>{e&&p.link?(b.value="",S()):e||b.value&&(URL.revokeObjectURL(b.value),b.value="")}),(a,l)=>{const t=A,s=C,r=x,p=w,m=D,M=z,O=k,T=Y,B=$,P=J,Q=W,le=X,te=R;return i(),o(te,{modelValue:j.value,"onUpdate:modelValue":l[6]||(l[6]=e=>j.value=e),title:"二维码生成",width:"600px","close-on-click-modal":!1},{footer:u(()=>[n("div",ba,[d(B,{onClick:l[5]||(l[5]=e=>j.value=!1)},{default:u(()=>l[20]||(l[20]=[c("关闭",-1)])),_:1,__:[20]}),d(B,{type:"primary",disabled:!b.value,onClick:L},{default:u(()=>[d(t,null,{default:u(()=>[d(h(K))]),_:1}),l[21]||(l[21]=c(" 下载二维码 ",-1))]),_:1,__:[21]},8,["disabled"]),d(B,{type:"success",disabled:!b.value,onClick:I},{default:u(()=>[d(t,null,{default:u(()=>[d(h(ae))]),_:1}),l[22]||(l[22]=c(" 复制链接 ",-1))]),_:1,__:[22]},8,["disabled"])])]),default:u(()=>[e.link?(i(),_("div",la,[d(T,{class:"config-card",shadow:"never"},{header:u(()=>[n("div",ta,[d(t,null,{default:u(()=>[d(h(E))]),_:1}),l[7]||(l[7]=n("span",null,"二维码配置",-1))])]),default:u(()=>[d(O,{model:V,"label-width":"100px"},{default:u(()=>[d(M,{gutter:20},{default:u(()=>[d(m,{span:12},{default:u(()=>[d(p,{label:"尺寸大小"},{default:u(()=>[d(r,{modelValue:V.size,"onUpdate:modelValue":l[0]||(l[0]=e=>V.size=e),onChange:S},{default:u(()=>[d(s,{label:"小 (200x200)",value:200}),d(s,{label:"中 (300x300)",value:300}),d(s,{label:"大 (500x500)",value:500}),d(s,{label:"超大 (800x800)",value:800})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(m,{span:12},{default:u(()=>[d(p,{label:"边距"},{default:u(()=>[d(r,{modelValue:V.margin,"onUpdate:modelValue":l[1]||(l[1]=e=>V.margin=e),onChange:S},{default:u(()=>[d(s,{label:"无边距",value:0}),d(s,{label:"小边距",value:1}),d(s,{label:"标准边距",value:2}),d(s,{label:"大边距",value:4})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(M,{gutter:20},{default:u(()=>[d(m,{span:12},{default:u(()=>[d(p,{label:"格式"},{default:u(()=>[d(r,{modelValue:V.format,"onUpdate:modelValue":l[2]||(l[2]=e=>V.format=e),onChange:S},{default:u(()=>[d(s,{label:"PNG",value:"png"}),d(s,{label:"JPG",value:"jpg"}),d(s,{label:"SVG",value:"svg"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(m,{span:12},{default:u(()=>[d(p,{label:"容错级别"},{default:u(()=>[d(r,{modelValue:V.errorCorrection,"onUpdate:modelValue":l[3]||(l[3]=e=>V.errorCorrection=e),onChange:S},{default:u(()=>[d(s,{label:"低 (L)",value:"L"}),d(s,{label:"中 (M)",value:"M"}),d(s,{label:"高 (Q)",value:"Q"}),d(s,{label:"最高 (H)",value:"H"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),d(T,{class:"preview-card",shadow:"never"},{header:u(()=>[n("div",sa,[d(t,null,{default:u(()=>[d(h(F))]),_:1}),l[9]||(l[9]=n("span",null,"二维码预览",-1)),n("div",oa,[d(B,{type:"primary",size:"small",loading:v.value,onClick:S},{default:u(()=>[d(t,null,{default:u(()=>[d(h(G))]),_:1}),l[8]||(l[8]=c(" 重新生成 ",-1))]),_:1,__:[8]},8,["loading"])])])]),default:u(()=>[n("div",ia,[v.value?(i(),_("div",ua,[d(t,{class:"is-loading"},{default:u(()=>[d(h(H))]),_:1}),l[10]||(l[10]=n("p",null,"正在生成二维码...",-1))])):b.value?(i(),_("div",da,[n("img",{src:b.value,alt:`${e.link.title}的二维码`,class:"qrcode-image"},null,8,ra),n("div",na,[n("p",ca,U(e.link.title||"推广链接"),1),n("p",pa,U(e.link.short_url),1),n("p",ma,U(V.size)+"x"+U(V.size)+"px",1)])])):(i(),_("div",_a,[d(t,null,{default:u(()=>[d(h(N))]),_:1}),l[11]||(l[11]=n("p",null,"点击生成二维码",-1))]))])]),_:1}),d(T,{class:"batch-card",shadow:"never"},{header:u(()=>[n("div",va,[d(t,null,{default:u(()=>[d(h(Z))]),_:1}),l[12]||(l[12]=n("span",null,"批量生成",-1))])]),default:u(()=>[n("div",fa,[d(Q,{modelValue:y.value,"onUpdate:modelValue":l[4]||(l[4]=e=>y.value=e)},{default:u(()=>[d(P,{label:200},{default:u(()=>l[13]||(l[13]=[c("小尺寸 (200x200)",-1)])),_:1,__:[13]}),d(P,{label:300},{default:u(()=>l[14]||(l[14]=[c("中尺寸 (300x300)",-1)])),_:1,__:[14]}),d(P,{label:500},{default:u(()=>l[15]||(l[15]=[c("大尺寸 (500x500)",-1)])),_:1,__:[15]}),d(P,{label:800},{default:u(()=>l[16]||(l[16]=[c("超大尺寸 (800x800)",-1)])),_:1,__:[16]})]),_:1},8,["modelValue"]),d(B,{type:"success",loading:f.value,disabled:0===y.value.length,onClick:q,style:{"margin-top":"15px"}},{default:u(()=>[d(t,null,{default:u(()=>[d(h(K))]),_:1}),l[17]||(l[17]=c(" 批量生成并下载 ",-1))]),_:1,__:[17]},8,["loading","disabled"])])]),_:1}),d(T,{class:"tips-card",shadow:"never"},{header:u(()=>[n("div",ga,[d(t,null,{default:u(()=>[d(h(ee))]),_:1}),l[18]||(l[18]=n("span",null,"使用说明",-1))])]),default:u(()=>[n("div",ha,[d(le,{title:"二维码使用提示",type:"info",closable:!1,"show-icon":""},{default:u(()=>l[19]||(l[19]=[n("ul",{class:"tips-list"},[n("li",null,"二维码扫描后将跳转到推广链接"),n("li",null,"建议使用PNG格式以获得最佳质量"),n("li",null,"容错级别越高，二维码越复杂但容错能力越强"),n("li",null,"适当的边距可以提高扫描成功率"),n("li",null,"可以将二维码保存到本地或直接分享使用")],-1)])),_:1,__:[19]})])]),_:1})])):g("",!0)]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-3d983314"]]),ka={class:"app-container"},wa={class:"filter-container"},Va={class:"card-header"},xa={class:"link-info"},Ca={class:"link-name"},Ua={class:"link-url"},ja=["title"],za={class:"click-stats"},Da={class:"total-clicks"},Sa={class:"today-clicks"},qa={class:"conversion-stats"},La={class:"conversions"},$a={class:"conversion-rate"},Ra={class:"pagination-container"},Ma=e({__name:"LinkManagement",setup(e){const l=a([]),s=a(0),r=a(!0),p=a(!1),v=a(!1),f=a(!1),k=a({}),w=a(null),j=a([]),S=a({total_links:0,total_clicks:0,unique_visitors:0,conversion_rate:0}),q=t({page:1,limit:20,keyword:"",type:"",status:""}),L=async()=>{r.value=!0;try{const{data:e}=await ve.getList(q);l.value=e.list,s.value=e.total}catch(e){console.error("获取推广链接列表失败:",e),M.error("获取推广链接列表失败")}finally{r.value=!1}},R=async()=>{try{const{data:e}=await ve.getStats();S.value=e}catch(e){console.error("获取统计数据失败:",e)}},I=()=>{q.page=1,L()},O=()=>{k.value={},p.value=!0},Q=async e=>{try{await navigator.clipboard.writeText(e),M.success("链接已复制到剪贴板")}catch(a){M.error("复制失败")}},E=async(e,a)=>{try{const l="paused"===a?"暂停":"恢复";await me.confirm(`确定要${l}这个链接吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ve.update(e,{status:a}),M.success(`${l}成功`),L()}catch(l){"cancel"!==l&&M.error("操作失败")}},H=e=>{const[a,t]=e.split("-"),s=parseInt(t),o=l.value.find(e=>e.id===s);switch(a){case"qrcode":k.value={...o},f.value=!0;break;case"copy":Q(o.short_url);break;case"pause":E(s,"paused");break;case"resume":E(s,"active");break;case"delete":(async e=>{try{await me.confirm("确定要删除这个推广链接吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await ve.delete(e),M.success("删除成功"),L()}catch(a){"cancel"!==a&&M.error("删除失败")}})(s)}},N=async()=>{try{M.success("导出成功")}catch(e){M.error("导出失败")}},G=()=>{0!==j.value.length?me.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"批量暂停",cancelButtonText:"批量恢复"}).then(()=>{W()}).catch(e=>{"cancel"===e&&W()}):M.warning("请先选择要操作的链接")},W=async e=>{try{j.value.map(e=>e.id);M.success("批量操作成功"),L()}catch(a){M.error("批量操作失败")}},J=e=>{j.value=e},K=e=>{q.limit=e,L()},Z=e=>{q.page=e,L()},X=()=>{L(),R()},ee=()=>{L()},ae=e=>({group:"群组推广",distribution:"分销推广",activity:"活动推广",other:"其他"}[e]||"未知"),fe=e=>({active:"活跃",paused:"暂停",expired:"过期"}[e]||"未知");return b(()=>{L(),R()}),(e,a)=>{const t=V,b=C,j=x,L=$,R=D,M=z,E=B,W=oe,me=A,ve=ue,ge=ce,be=ne,ke=de,we=T,Ve=pe,xe=Y,Ce=P;return i(),_("div",ka,[n("div",wa,[d(t,{modelValue:q.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>q.keyword=e),placeholder:"搜索链接名称、目标URL",style:{width:"200px"},class:"filter-item",onKeyup:y(I,["enter"]),clearable:""},null,8,["modelValue"]),d(j,{modelValue:q.type,"onUpdate:modelValue":a[1]||(a[1]=e=>q.type=e),placeholder:"链接类型",clearable:"",style:{width:"120px"},class:"filter-item"},{default:u(()=>[d(b,{label:"全部",value:""}),d(b,{label:"群组推广",value:"group"}),d(b,{label:"分销推广",value:"distribution"}),d(b,{label:"活动推广",value:"activity"}),d(b,{label:"其他",value:"other"})]),_:1},8,["modelValue"]),d(j,{modelValue:q.status,"onUpdate:modelValue":a[2]||(a[2]=e=>q.status=e),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:u(()=>[d(b,{label:"全部",value:""}),d(b,{label:"活跃",value:"active"}),d(b,{label:"暂停",value:"paused"}),d(b,{label:"过期",value:"expired"})]),_:1},8,["modelValue"]),d(L,{class:"filter-item",type:"primary",icon:"Search",onClick:I},{default:u(()=>a[8]||(a[8]=[c(" 搜索 ",-1)])),_:1,__:[8]}),d(L,{class:"filter-item",type:"success",icon:"Plus",onClick:O},{default:u(()=>a[9]||(a[9]=[c(" 创建链接 ",-1)])),_:1,__:[9]}),d(L,{class:"filter-item",type:"warning",icon:"Download",onClick:N},{default:u(()=>a[10]||(a[10]=[c(" 导出数据 ",-1)])),_:1,__:[10]})]),d(M,{gutter:20,class:"stats-row"},{default:u(()=>[d(R,{span:6},{default:u(()=>[d(_e,{type:"primary",icon:h(le),value:S.value.total_links,label:"总链接数",trend:{type:"up",value:"+15",desc:"较上月"},clickable:"",onClick:ee},null,8,["icon","value"])]),_:1}),d(R,{span:6},{default:u(()=>[d(_e,{type:"success",icon:h(F),value:S.value.total_clicks,label:"总点击量",trend:{type:"up",value:"+28.5%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),d(R,{span:6},{default:u(()=>[d(_e,{type:"warning",icon:h(te),value:S.value.unique_visitors,label:"独立访客",trend:{type:"up",value:"+18.2%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),d(R,{span:6},{default:u(()=>[d(_e,{type:"danger",icon:h(se),value:S.value.conversion_rate,label:"转化率",suffix:"%",decimals:1,trend:{type:"up",value:"+2.1%",desc:"较上月"}},null,8,["icon","value"])]),_:1})]),_:1}),d(xe,null,{header:u(()=>[n("div",Va,[a[12]||(a[12]=n("h3",null,"推广链接列表",-1)),n("div",null,[d(L,{type:"primary",size:"small",onClick:G},{default:u(()=>a[11]||(a[11]=[c("批量操作",-1)])),_:1,__:[11]})])])]),default:u(()=>[m((i(),o(we,{data:l.value,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",onSelectionChange:J},{default:u(()=>[d(E,{type:"selection",width:"55"}),d(E,{label:"链接ID",prop:"id",width:"80"}),d(E,{label:"链接信息",width:"250"},{default:u(({row:e})=>[n("div",xa,[n("div",Ca,U(e.name),1),n("div",Ua,[d(W,{href:e.short_url,target:"_blank",type:"primary"},{default:u(()=>[c(U(e.short_url),1)]),_:2},1032,["href"]),d(L,{type:"text",size:"small",onClick:a=>Q(e.short_url),class:"copy-btn"},{default:u(()=>[d(me,null,{default:u(()=>[d(h(ie))]),_:1})]),_:2},1032,["onClick"])])])]),_:1}),d(E,{label:"类型",width:"100"},{default:u(({row:e})=>{return[d(ve,{type:(a=e.type,{group:"primary",distribution:"success",activity:"warning",other:"info"}[a]||"info")},{default:u(()=>[c(U(ae(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),d(E,{label:"目标URL",width:"200"},{default:u(({row:e})=>{return[n("div",{class:"target-url",title:e.target_url},U((a=e.target_url,a?a.length>30?a.substring(0,30)+"...":a:"")),9,ja)];var a}),_:1}),d(E,{label:"点击统计",width:"120"},{default:u(({row:e})=>[n("div",za,[n("div",Da,U(e.click_count||0)+" 次",1),n("div",Sa,"今日: "+U(e.today_clicks||0),1)])]),_:1}),d(E,{label:"转化数据",width:"120"},{default:u(({row:e})=>[n("div",qa,[n("div",La,U(e.conversions||0)+" 转化",1),n("div",$a,U(((e.conversions||0)/Math.max(e.click_count||1,1)*100).toFixed(1))+"%",1)])]),_:1}),d(E,{label:"状态",width:"100"},{default:u(({row:e})=>{return[d(ve,{type:(a=e.status,{active:"success",paused:"warning",expired:"danger"}[a]||"info")},{default:u(()=>[c(U(fe(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),d(E,{label:"创建时间",width:"160"},{default:u(({row:e})=>[c(U(h(he)(e.created_at)),1)]),_:1}),d(E,{label:"操作",width:"200",fixed:"right"},{default:u(({row:e})=>[d(L,{type:"primary",size:"small",onClick:a=>(e=>{k.value={...e},p.value=!0})(e)},{default:u(()=>a[13]||(a[13]=[c(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick"]),d(L,{type:"success",size:"small",onClick:a=>(e=>{w.value=e.id,v.value=!0})(e)},{default:u(()=>a[14]||(a[14]=[c(" 统计 ",-1)])),_:2,__:[14]},1032,["onClick"]),d(ke,{onCommand:H},{dropdown:u(()=>[d(be,null,{default:u(()=>[d(ge,{command:`qrcode-${e.id}`},{default:u(()=>a[16]||(a[16]=[c("生成二维码",-1)])),_:2,__:[16]},1032,["command"]),d(ge,{command:`copy-${e.id}`},{default:u(()=>a[17]||(a[17]=[c("复制链接",-1)])),_:2,__:[17]},1032,["command"]),"active"===e.status?(i(),o(ge,{key:0,command:`pause-${e.id}`},{default:u(()=>a[18]||(a[18]=[c("暂停链接",-1)])),_:2,__:[18]},1032,["command"])):g("",!0),"paused"===e.status?(i(),o(ge,{key:1,command:`resume-${e.id}`},{default:u(()=>a[19]||(a[19]=[c("恢复链接",-1)])),_:2,__:[19]},1032,["command"])):g("",!0),d(ge,{command:`delete-${e.id}`,divided:""},{default:u(()=>a[20]||(a[20]=[c("删除链接",-1)])),_:2,__:[20]},1032,["command"])]),_:2},1024)]),default:u(()=>[d(L,{type:"info",size:"small"},{default:u(()=>[a[15]||(a[15]=c(" 更多",-1)),d(me,{class:"el-icon--right"},{default:u(()=>[d(h(re))]),_:1})]),_:1,__:[15]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ce,r.value]]),n("div",Ra,[d(Ve,{"current-page":q.page,"onUpdate:currentPage":a[3]||(a[3]=e=>q.page=e),"page-size":q.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>q.limit=e),"page-sizes":[10,20,30,50],total:s.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:K,onCurrentChange:Z},null,8,["current-page","page-size","total"])])]),_:1}),d(ye,{modelValue:p.value,"onUpdate:modelValue":a[5]||(a[5]=e=>p.value=e),"link-data":k.value,onSuccess:X},null,8,["modelValue","link-data"]),d(aa,{modelValue:v.value,"onUpdate:modelValue":a[6]||(a[6]=e=>v.value=e),"link-id":w.value},null,8,["modelValue","link-id"]),d(ya,{modelValue:f.value,"onUpdate:modelValue":a[7]||(a[7]=e=>f.value=e),"link-data":k.value},null,8,["modelValue","link-data"])])}}},[["__scopeId","data-v-e27f9de6"]]);export{Ma as default};
