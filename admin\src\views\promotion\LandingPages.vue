<template>
  <div class="landing-pages-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h1>🎨 落地页管理</h1>
        <p class="page-desc">创建和管理高转化率的推广落地页面</p>
      </div>
      
      <div class="page-actions">
        <el-button type="info" @click="handleTemplateManage">
          <el-icon><Document /></el-icon>
          模板管理
        </el-button>
        <el-button type="success" @click="handleCreateFromTemplate">
          <el-icon><Document /></el-icon>
          从模板创建
        </el-button>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          创建落地页
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.total_pages }}</div>
            <div class="stat-label">总落地页</div>
            <div class="stat-trend up">
              <el-icon><ArrowUp /></el-icon>
              <span>+12 较上月</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(stats.total_visits) }}</div>
            <div class="stat-label">总访问量</div>
            <div class="stat-trend up">
              <el-icon><ArrowUp /></el-icon>
              <span>+28.5% 较上月</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card warning">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.avg_conversion_rate }}%</div>
            <div class="stat-label">平均转化率</div>
            <div class="stat-trend up">
              <el-icon><ArrowUp /></el-icon>
              <span>+3.2% 较上月</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card danger">
          <div class="stat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.avg_stay_time }}s</div>
            <div class="stat-label">平均停留时间</div>
            <div class="stat-trend down">
              <el-icon><ArrowDown /></el-icon>
              <span>-5.1% 较上月</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索落地页名称、描述"
          style="width: 250px;"
          class="filter-item"
          @keyup.enter="handleFilter"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="listQuery.status"
          placeholder="状态筛选"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="全部" value="" />
          <el-option label="已发布" value="published" />
          <el-option label="草稿" value="draft" />
          <el-option label="已下线" value="offline" />
        </el-select>
        
        <el-select
          v-model="listQuery.template_id"
          placeholder="模板筛选"
          clearable
          style="width: 150px"
          class="filter-item"
        >
          <el-option label="全部模板" value="" />
          <el-option label="营销模板" value="1" />
          <el-option label="产品展示" value="2" />
          <el-option label="活动推广" value="3" />
          <el-option label="表单收集" value="4" />
        </el-select>
        
        <el-date-picker
          v-model="listQuery.date_range"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="filter-item"
        />
        
        <el-button type="primary" @click="handleFilter">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button type="success" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </el-card>

    <!-- 落地页列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <h3>落地页列表</h3>
          <div class="header-actions">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button value="grid">
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-radio-button>
              <el-radio-button value="list">
                <el-icon><List /></el-icon>
                列表视图
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="page in list" :key="page.id">
            <div class="landing-page-card">
              <div class="card-preview">
                <div class="preview-image">
                  <img :src="page.preview_image || '/placeholder.svg?height=200&width=300'" :alt="page.name" />
                  <div class="preview-overlay">
                    <el-button type="primary" size="small" @click="handlePreview(page)">
                      <el-icon><View /></el-icon>
                      预览
                    </el-button>
                    <el-button type="success" size="small" @click="handleEdit(page)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                  </div>
                </div>
                <div class="status-badge">
                  <el-tag :type="getStatusTagType(page.status)" size="small">
                    {{ getStatusText(page.status) }}
                  </el-tag>
                </div>
              </div>
              
              <div class="card-content">
                <div class="page-info">
                  <h4 class="page-name">{{ page.name }}</h4>
                  <p class="page-desc">{{ page.description || '暂无描述' }}</p>
                </div>
                
                <div class="page-stats">
                  <div class="stat-item">
                    <span class="stat-label">访问量</span>
                    <span class="stat-value">{{ formatNumber(page.visit_count || 0) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">转化率</span>
                    <span class="stat-value">{{ page.conversion_rate || 0 }}%</span>
                  </div>
                </div>
                
                <div class="card-actions">
                  <el-button type="text" size="small" @click="handleAnalytics(page)">
                    <el-icon><TrendCharts /></el-icon>
                    数据分析
                  </el-button>
                  <el-button type="text" size="small" @click="handleCopy(page)">
                    <el-icon><DocumentCopy /></el-icon>
                    复制
                  </el-button>
                  <el-dropdown @command="handleCommand">
                    <el-button type="text" size="small">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="`publish-${page.id}`" v-if="page.status === 'draft'">
                          发布
                        </el-dropdown-item>
                        <el-dropdown-item :command="`offline-${page.id}`" v-if="page.status === 'published'">
                          下线
                        </el-dropdown-item>
                        <el-dropdown-item :command="`delete-${page.id}`" divided>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table
          v-loading="listLoading"
          :data="list"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="落地页信息" width="300">
            <template #default="{ row }">
              <div class="page-info-cell">
                <div class="page-thumbnail">
                  <img :src="row.preview_image || '/placeholder.svg?height=60&width=80'" :alt="row.name" />
                </div>
                <div class="page-details">
                  <div class="page-name">{{ row.name }}</div>
                  <div class="page-url">{{ row.url || '未设置URL' }}</div>
                  <div class="page-template">模板: {{ getTemplateName(row.template_id) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="访问统计" width="120">
            <template #default="{ row }">
              <div class="visit-stats">
                <div class="total-visits">{{ formatNumber(row.visit_count || 0) }} 次</div>
                <div class="today-visits">今日: {{ row.today_visits || 0 }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="转化数据" width="120">
            <template #default="{ row }">
              <div class="conversion-stats">
                <div class="conversions">{{ row.conversions || 0 }} 转化</div>
                <div class="conversion-rate">{{ row.conversion_rate || 0 }}%</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="success" size="small" @click="handlePreview(row)">
                预览
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button type="info" size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`analytics-${row.id}`">数据分析</el-dropdown-item>
                    <el-dropdown-item :command="`copy-${row.id}`">复制页面</el-dropdown-item>
                    <el-dropdown-item :command="`publish-${row.id}`" v-if="row.status === 'draft'">发布</el-dropdown-item>
                    <el-dropdown-item :command="`offline-${row.id}`" v-if="row.status === 'published'">下线</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${row.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[12, 24, 36, 48]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 落地页编辑器对话框 -->
    <LandingPageEditor
      v-model="editorVisible"
      :page-data="currentPage"
      @success="handleEditorSuccess"
    />

    <!-- 模板选择对话框 -->
    <TemplateSelector
      v-model="templateSelectorVisible"
      @select="handleTemplateSelect"
    />

    <!-- 数据分析抽屉 -->
    <LandingPageAnalytics
      v-model="analyticsVisible"
      :page-id="currentPageId"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document, Plus, View, TrendCharts, Timer, ArrowUp, ArrowDown,
  Search, Download, Grid, List, Edit, DocumentCopy
} from '@element-plus/icons-vue'
import LandingPageEditor from './components/LandingPageEditor.vue'
import TemplateSelector from './components/TemplateSelector.vue'
import LandingPageAnalytics from './components/LandingPageAnalytics.vue'
import { promotionApi } from '@/api/promotion'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const viewMode = ref('grid')
const editorVisible = ref(false)
const templateSelectorVisible = ref(false)
const analyticsVisible = ref(false)
const currentPage = ref({})
const currentPageId = ref(null)
const multipleSelection = ref([])

// 统计数据
const stats = ref({
  total_pages: 0,
  total_visits: 0,
  avg_conversion_rate: 0,
  avg_stay_time: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 12,
  keyword: '',
  status: '',
  template_id: '',
  date_range: []
})

// 获取落地页列表
const getList = async () => {
  listLoading.value = true
  try {
    const params = { ...listQuery }
    if (params.date_range && params.date_range.length === 2) {
      params.start_date = params.date_range[0]
      params.end_date = params.date_range[1]
      delete params.date_range
    }
    
    const { data } = await promotionApi.getList(params)
    list.value = data.list || mockLandingPages
    total.value = data.total || mockLandingPages.length
  } catch (error) {
    console.error('获取落地页列表失败:', error)
    // 使用模拟数据
    list.value = mockLandingPages
    total.value = mockLandingPages.length
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await promotionApi.getStats()
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用模拟数据
    stats.value = {
      total_pages: 24,
      total_visits: 156789,
      avg_conversion_rate: 12.5,
      avg_stay_time: 145
    }
  }
}

// 模拟数据
const mockLandingPages = [
  {
    id: 1,
    name: '产品推广落地页',
    description: '高转化率的产品展示页面',
    url: 'https://example.com/product-landing',
    status: 'published',
    template_id: 1,
    visit_count: 12580,
    today_visits: 156,
    conversions: 1258,
    conversion_rate: 10.0,
    preview_image: '/placeholder.svg?height=200&width=300',
    created_at: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '活动报名页面',
    description: '在线活动报名收集页面',
    url: 'https://example.com/event-signup',
    status: 'published',
    template_id: 4,
    visit_count: 8960,
    today_visits: 89,
    conversions: 896,
    conversion_rate: 10.0,
    preview_image: '/placeholder.svg?height=200&width=300',
    created_at: '2024-01-14 15:20:00'
  },
  {
    id: 3,
    name: '营销活动页面',
    description: '限时优惠活动推广页面',
    url: 'https://example.com/promotion',
    status: 'draft',
    template_id: 3,
    visit_count: 0,
    today_visits: 0,
    conversions: 0,
    conversion_rate: 0,
    preview_image: '/placeholder.svg?height=200&width=300',
    created_at: '2024-01-13 09:15:00'
  }
]

// 事件处理
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleCreate = () => {
  currentPage.value = {}
  editorVisible.value = true
}

const handleCreateFromTemplate = () => {
  templateSelectorVisible.value = true
}

const handleTemplateManage = () => {
  ElMessage.info('模板管理功能开发中...')
}

const handleEdit = (page) => {
  currentPage.value = { ...page }
  editorVisible.value = true
}

const handlePreview = (page) => {
  window.open(page.url || '#', '_blank')
}

const handleAnalytics = (page) => {
  currentPageId.value = page.id
  analyticsVisible.value = true
}

const handleCopy = async (page) => {
  try {
    await ElMessageBox.confirm(`确定要复制落地页 "${page.name}" 吗？`, '确认复制', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    await promotionApi.duplicate(page.id, { name: `${page.name} - 副本` })
    ElMessage.success('复制成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('复制失败')
    }
  }
}

const handleCommand = (command) => {
  const [action, pageId] = command.split('-')
  const id = parseInt(pageId)
  const page = list.value.find(p => p.id === id)
  
  switch (action) {
    case 'analytics':
      handleAnalytics(page)
      break
    case 'copy':
      handleCopy(page)
      break
    case 'publish':
      handleUpdateStatus(id, 'published')
      break
    case 'offline':
      handleUpdateStatus(id, 'offline')
      break
    case 'delete':
      handleDelete(id)
      break
  }
}

const handleUpdateStatus = async (pageId, status) => {
  try {
    const statusText = { published: '发布', offline: '下线' }[status]
    await ElMessageBox.confirm(`确定要${statusText}这个落地页吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await promotionApi.update(pageId, { status })
    ElMessage.success(`${statusText}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (pageId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个落地页吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await promotionApi.delete(pageId)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

const handleEditorSuccess = () => {
  getList()
  getStats()
}

const handleTemplateSelect = (template) => {
  currentPage.value = { template_id: template.id }
  editorVisible.value = true
}

// 工具函数
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'W'
  }
  return num.toString()
}

const getStatusTagType = (status) => {
  const types = {
    published: 'success',
    draft: 'warning',
    offline: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    offline: '已下线'
  }
  return texts[status] || '未知'
}

const getTemplateName = (templateId) => {
  const templates = {
    1: '营销模板',
    2: '产品展示',
    3: '活动推广',
    4: '表单收集'
  }
  return templates[templateId] || '未知模板'
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.landing-pages-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  .page-title {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }
    
    .page-desc {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .page-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-row {
  margin-bottom: 24px;
  
  .stat-card {
    display: flex;
    align-items: center;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 24px;
      color: white;
    }
    
    &.primary .stat-icon {
      background: linear-gradient(135deg, #409eff, #67c23a);
    }
    
    &.success .stat-icon {
      background: linear-gradient(135deg, #67c23a, #85ce61);
    }
    
    &.warning .stat-icon {
      background: linear-gradient(135deg, #e6a23c, #f7ba2a);
    }
    
    &.danger .stat-icon {
      background: linear-gradient(135deg, #f56c6c, #f78989);
    }
    
    .stat-content {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .stat-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.filter-card {
  margin-bottom: 24px;
  
  .filter-container {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
  }
}

.list-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }
  }
}

.grid-view {
  .landing-page-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .card-preview {
      position: relative;
      
      .preview-image {
        position: relative;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 200px;
          object-fit: cover;
        }
        
        .preview-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover .preview-overlay {
          opacity: 1;
        }
      }
      
      .status-badge {
        position: absolute;
        top: 12px;
        right: 12px;
      }
    }
    
    .card-content {
      padding: 20px;
      
      .page-info {
        margin-bottom: 16px;
        
        .page-name {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }
        
        .page-desc {
          margin: 0;
          font-size: 14px;
          color: #64748b;
          line-height: 1.5;
        }
      }
      
      .page-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 16px;
        
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .stat-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }
      }
      
      .card-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}

.list-view {
  .page-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .page-thumbnail {
      width: 80px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
      flex-shrink: 0;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .page-details {
      flex: 1;
      
      .page-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;
      }
      
      .page-url {
        font-size: 12px;
        color: #409eff;
        font-family: 'Monaco', 'Consolas', monospace;
        margin-bottom: 4px;
      }
      
      .page-template {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .visit-stats,
  .conversion-stats {
    .total-visits,
    .conversions {
      font-weight: 600;
      color: #333;
      margin-bottom: 2px;
    }
    
    .today-visits,
    .conversion-rate {
      font-size: 12px;
      color: #666;
    }
  }
}

.pagination-container {
  padding: 32px 16px;
  text-align: center;
}
</style>
