<template>
  <div class="qrcode-generator">
      <el-dialog
        :model-value="visible"
        title="生成推广链接二维码"
        width="500px"
        @update:model-value="handleVisibleChange"
        @close="handleClose"
      >
      <div class="qrcode-content">
        <div class="link-info">
          <h4>推广链接信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="链接名称">{{ linkData.name }}</el-descriptions-item>
            <el-descriptions-item label="推广链接">{{ linkData.short_url }}</el-descriptions-item>
            <el-descriptions-item label="目标URL">{{ linkData.target_url }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="qrcode-section">
          <h4>二维码设置</h4>
          <el-form :model="qrConfig" label-width="100px">
            <el-form-item label="二维码大小">
              <el-slider
                v-model="qrConfig.size"
                :min="200"
                :max="500"
                :step="50"
                show-stops
                show-tooltip
                @change="generateQRCode"
              />
              <span class="size-text">{{ qrConfig.size }}px</span>
            </el-form-item>
            <el-form-item label="前景色">
              <el-color-picker
                v-model="qrConfig.color.dark"
                @change="generateQRCode"
              />
            </el-form-item>
            <el-form-item label="背景色">
              <el-color-picker
                v-model="qrConfig.color.light"
                @change="generateQRCode"
              />
            </el-form-item>
            <el-form-item label="容错级别">
              <el-select v-model="qrConfig.errorCorrectionLevel" @change="generateQRCode">
                <el-option label="低 (L)" value="L" />
                <el-option label="中 (M)" value="M" />
                <el-option label="高 (Q)" value="Q" />
                <el-option label="最高 (H)" value="H" />
              </el-select>
            </el-form-item>
            <el-form-item label="边距">
              <el-input-number
                v-model="qrConfig.margin"
                :min="0"
                :max="10"
                @change="generateQRCode"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="qrcode-preview">
          <h4>二维码预览</h4>
          <div class="qrcode-container" v-loading="generating">
            <canvas
              ref="qrcodeCanvas"
              :width="qrConfig.size"
              :height="qrConfig.size"
              class="qrcode-canvas"
            ></canvas>
          </div>
          
          <div class="qrcode-actions">
            <el-button @click="downloadQRCode" :disabled="!qrcodeDataUrl">
              <el-icon><Download /></el-icon>
              下载二维码
            </el-button>
            <el-button @click="copyQRCode" :disabled="!qrcodeDataUrl">
              <el-icon><DocumentCopy /></el-icon>
              复制图片
            </el-button>
            <el-button @click="printQRCode" :disabled="!qrcodeDataUrl">
              <el-icon><Printer /></el-icon>
              打印二维码
            </el-button>
          </div>
        </div>

        <div class="usage-tips">
          <h4>使用说明</h4>
          <el-alert type="info" :closable="false">
            <template #title>
              <div class="tips-content">
                <p><strong>扫码说明：</strong></p>
                <ul>
                  <li>用户扫描此二维码将直接跳转到您的推广链接</li>
                  <li>系统会自动记录通过二维码产生的访问和转化</li>
                  <li>建议在宣传材料、名片、海报等场景使用</li>
                  <li>二维码支持微信、支付宝等主流扫码工具</li>
                </ul>
              </div>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="downloadQRCode" :disabled="!qrcodeDataUrl">
          <el-icon><Download /></el-icon>
          下载二维码
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Download, DocumentCopy, Printer } from '@element-plus/icons-vue'
import QRCode from 'qrcode'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  linkData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'close'])

// 响应式数据
const generating = ref(false)
const qrcodeDataUrl = ref('')
const qrcodeCanvas = ref()

const qrConfig = reactive({
  size: 300,
  color: {
    dark: '#000000',
    light: '#FFFFFF'
  },
  errorCorrectionLevel: 'M',
  margin: 2
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.linkData.short_url) {
    nextTick(() => {
      generateQRCode()
    })
  }
})

// 生成二维码
const generateQRCode = async () => {
  if (!props.linkData.short_url) {
    ElMessage.warning('推广链接不能为空')
    return
  }

  try {
    generating.value = true
    
    const options = {
      width: qrConfig.size,
      height: qrConfig.size,
      color: {
        dark: qrConfig.color.dark,
        light: qrConfig.color.light
      },
      errorCorrectionLevel: qrConfig.errorCorrectionLevel,
      margin: qrConfig.margin,
      type: 'image/png'
    }

    // 生成二维码到canvas
    await QRCode.toCanvas(qrcodeCanvas.value, props.linkData.short_url, options)
    
    // 获取DataURL用于下载和复制
    qrcodeDataUrl.value = qrcodeCanvas.value.toDataURL('image/png')
    
    ElMessage.success('二维码生成成功')
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  } finally {
    generating.value = false
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrcodeDataUrl.value) {
    ElMessage.warning('请先生成二维码')
    return
  }

  try {
    const link = document.createElement('a')
    link.download = `qrcode_${props.linkData.name || 'promotion'}_${Date.now()}.png`
    link.href = qrcodeDataUrl.value
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElNotification.success({
      title: '下载成功',
      message: '二维码图片已保存到本地'
    })
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 复制二维码图片
const copyQRCode = async () => {
  if (!qrcodeDataUrl.value) {
    ElMessage.warning('请先生成二维码')
    return
  }

  try {
    // 将DataURL转换为Blob
    const response = await fetch(qrcodeDataUrl.value)
    const blob = await response.blob()
    
    // 使用Clipboard API复制图片
    if (navigator.clipboard && window.ClipboardItem) {
      const item = new ClipboardItem({ 'image/png': blob })
      await navigator.clipboard.write([item])
      
      ElNotification.success({
        title: '复制成功',
        message: '二维码图片已复制到剪贴板'
      })
    } else {
      // 降级处理：复制DataURL
      await navigator.clipboard.writeText(qrcodeDataUrl.value)
      ElMessage.success('二维码数据已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动保存图片')
  }
}

// 打印二维码
const printQRCode = () => {
  if (!qrcodeDataUrl.value) {
    ElMessage.warning('请先生成二维码')
    return
  }

  try {
    const printWindow = window.open('', '_blank')
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>打印二维码 - ${props.linkData.name}</title>
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            text-align: center;
          }
          .print-header {
            margin-bottom: 20px;
          }
          .print-header h2 {
            margin: 0 0 10px 0;
            color: #333;
          }
          .print-header p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
          }
          .qrcode-image {
            margin: 20px 0;
          }
          .qrcode-image img {
            max-width: 300px;
            height: auto;
          }
          .print-footer {
            margin-top: 20px;
            font-size: 12px;
            color: #999;
          }
          @media print {
            body { margin: 0; }
            .print-footer { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="print-header">
          <h2>${props.linkData.name || '推广链接二维码'}</h2>
          <p>推广链接: ${props.linkData.short_url}</p>
          <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        <div class="qrcode-image">
          <img src="${qrcodeDataUrl.value}" alt="推广链接二维码" />
        </div>
        <div class="print-footer">
          <p>扫描二维码访问推广链接</p>
          <p>支持微信、支付宝等主流扫码工具</p>
        </div>
      </body>
      </html>
    `
    
    printWindow.document.write(printContent)
    printWindow.document.close()
    
    // 等待图片加载完成后打印
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print()
        printWindow.close()
      }, 500)
    }
    
    ElMessage.success('正在准备打印...')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败')
  }
}

// 处理visible变化
const handleVisibleChange = (value) => {
  emit('update:visible', value)
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}
</script>

<style scoped>
.qrcode-generator {
  .qrcode-content {
    .link-info {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
      }
    }
    
    .qrcode-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .size-text {
        margin-left: 10px;
        color: #606266;
        font-size: 14px;
      }
    }
    
    .qrcode-preview {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .qrcode-container {
        display: flex;
        justify-content: center;
        margin-bottom: 15px;
        padding: 20px;
        border: 2px dashed #e4e7ed;
        border-radius: 8px;
        background: #fafafa;
        
        .qrcode-canvas {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
      
      .qrcode-actions {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
      }
    }
    
    .usage-tips {
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .tips-content {
        p {
          margin: 0 0 10px 0;
          font-weight: bold;
        }
        
        ul {
          margin: 0;
          padding-left: 20px;
          
          li {
            margin-bottom: 5px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qrcode-generator :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .qrcode-preview .qrcode-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
}
</style>