import{_ as l,u as e}from"./index-DtXAftX0.js";/* empty css                         *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                     *//* empty css                        *//* empty css                  *//* empty css                 *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               */import{af as a,r as t,L as s,c as n,e as u,k as i,l as o,t as d,E as r,u as c,z as p,D as _,B as m,F as f,Y as v,y as g,C as y}from"./vue-vendor-Dy164gUc.js";import{aS as h,U as b,a$ as C,T as V,aV as w,at as k,aw as j,ap as x,a_ as F,aZ as I,ac as q,ab as U,a6 as z,a0 as A,aY as P,bm as D,bn as E,bz as L,aR as M,bp as S,bq as R,aM as T,b9 as O,b8 as $,br as N,ay as Y,ao as Z,ai as B,bh as H,bi as K,bj as Q,bg as G,b1 as J,bK as W,bL as X,Q as ll}from"./element-plus-h2SQQM64.js";import{S as el}from"./StatCard-u_ssO_Ky.js";import{L as al}from"./LineChart-CydsJ2U8.js";import{D as tl}from"./DoughnutChart-CCHHIMjz.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const sl={class:"owner-dashboard"},nl={class:"page-header"},ul={class:"owner-info"},il={class:"info-content"},ol={class:"status-tags"},dl={class:"header-actions"},rl={class:"card-header"},cl={class:"card-header"},pl={class:"group-list"},_l={class:"group-avatar"},ml={class:"group-info"},fl={class:"group-name"},vl={class:"group-meta"},gl={class:"member-count"},yl={class:"group-type"},hl={class:"group-status"},bl={class:"group-actions"},Cl={key:0,class:"empty-state"},Vl={class:"card-header"},wl={class:"activities-list"},kl={class:"activity-icon"},jl={class:"activity-content"},xl={class:"activity-title"},Fl={class:"activity-desc"},Il={class:"activity-time"},ql={key:0,class:"activity-value"},Ul={class:"value-amount"},zl={key:0,class:"empty-state"},Al={class:"content-section"},Pl={class:"content-section"},Dl={class:"content-section"},El={class:"help-content"},Ll={class:"help-section"},Ml={class:"feature-item"},Sl={class:"feature-icon"},Rl={class:"feature-item"},Tl={class:"feature-icon"},Ol={class:"feature-item"},$l={class:"feature-icon"},Nl={class:"feature-item"},Yl={class:"feature-icon"},Zl={class:"feature-item"},Bl={class:"feature-icon"},Hl={class:"feature-item"},Kl={class:"feature-icon"},Ql={class:"help-section"},Gl={class:"help-section"},Jl={class:"help-section"},Wl={class:"tips-content"},Xl={class:"tips-content"},le={class:"tips-content"},ee={class:"help-section"},ae={class:"guide-content"},te={class:"guide-content"},se={class:"guide-content"},ne={class:"guide-content"},ue={class:"help-section"},ie=l({__name:"OwnerDashboard",setup(l){const ie=a(),oe=e(),de=t("30d"),re=t(!1),ce=t(!1),pe=t(!1),_e=t({}),me=t([]),fe=t([]),ve=t("content"),ge=t(["create-group"]),ye=t([]),he=t([{type:"免费群组",color:"success",description:"完全免费加入，无门槛限制",features:"基础功能、群公告、文件分享",suitable:"兴趣交流、学习讨论、社区建设"},{type:"付费群组",color:"primary",description:"需要支付一定费用才能加入",features:"专属内容、优质服务、定期活动",suitable:"专业培训、知识付费、高质量交流"},{type:"VIP群组",color:"warning",description:"高端付费群组，提供顶级服务",features:"一对一指导、独家资源、优先支持",suitable:"高端咨询、深度服务、精英圈层"}]),be=t([{level:"标准群主",color:"info",name:"新手群主",requirements:"完成实名认证，创建首个群组",benefits:"基础功能、新手指导、社区支持"},{level:"高级群主",color:"primary",name:"进阶群主",requirements:"管理群组≥3个，总成员≥200人，月收入≥1000元",benefits:"高级功能、数据分析、营销工具"},{level:"VIP群主",color:"warning",name:"专业群主",requirements:"管理群组≥10个，总成员≥1000人，月收入≥5000元",benefits:"专属客服、定制功能、优先更新"},{level:"金牌群主",color:"danger",name:"顶级群主",requirements:"管理群组≥20个，总成员≥5000人，月收入≥20000元",benefits:"专属经理、品牌合作、年度奖励"}]),Ce=s({title:"",type:"",price:0,description:""}),Ve={title:[{required:!0,message:"请输入群组名称",trigger:"blur"}],type:[{required:!0,message:"请选择群组类型",trigger:"change"}],description:[{required:!0,message:"请输入群组描述",trigger:"blur"}]},we=t(),ke=t({labels:[],datasets:[{label:"群组活跃度",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),je=t({labels:["免费群组","付费群组","VIP群组"],datasets:[{data:[0,0,0],backgroundColor:["#67C23A","#409EFF","#F56C6C"]}]}),xe={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},Fe={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},Ie=n(()=>({standard:"标准群主",premium:"高级群主",vip:"VIP群主"}[oe.userInfo?.owner_level||"standard"]||"标准群主")),qe=async()=>{try{_e.value={total_groups:8,total_members:1256,monthly_income:12580,activity_rate:78.5,group_growth_rate:12.5,member_growth_rate:25.8,income_trend:18.6}}catch(l){ll.error("加载统计数据失败")}},Ue=async()=>{try{const l={"7d":{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[65,78,82,75,88,92,85]},"30d":{labels:Array.from({length:30},(l,e)=>`${e+1}日`),data:Array.from({length:30},()=>Math.floor(40*Math.random())+60)},"90d":{labels:["1月","2月","3月"],data:[75,82,78]}}[de.value];ke.value={labels:l.labels,datasets:[{label:"群组活跃度",data:l.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}}catch(l){ll.error("加载活跃度数据失败")}},ze=async()=>{try{me.value=[{id:1,title:"技术交流群",cover_image:"",member_count:156,type_text:"免费群组",status:"active",status_text:"正常"},{id:2,title:"VIP学习群",cover_image:"",member_count:89,type_text:"VIP群组",status:"active",status_text:"正常"}]}catch(l){ll.error("加载群组数据失败")}},Ae=async()=>{try{fe.value=[{id:1,type:"member",title:"新成员加入",description:"技术交流群新增5名成员",created_at:new Date,value:"+5"},{id:2,type:"income",title:"收入到账",description:"VIP学习群收入299元",created_at:new Date(Date.now()-36e5),value:"¥299"}]}catch(l){ll.error("加载活动数据失败")}},Pe=()=>{re.value=!0},De=()=>{ie.push("/community/groups")},Ee=()=>{ie.push("/content/management")},Le=()=>{ie.push("/content/templates")},Me=()=>{ie.push("/community/analytics")},Se=()=>{ie.push("/community/settings")},Re=()=>{ie.push("/content/announcements")},Te=()=>{ie.push("/content/materials")},Oe=async()=>{try{await we.value.validate(),pe.value=!0,await new Promise(l=>setTimeout(l,1e3)),ll.success("群组创建成功"),re.value=!1,ze(),qe()}catch(l){!1!==l&&ll.error("创建群组失败")}finally{pe.value=!1}},$e=l=>({member:"User",income:"Money",content:"Edit"}[l]||"InfoFilled");return u(()=>{qe(),Ue(),ze(),Ae()}),(l,e)=>{const a=h,t=C,s=V,n=k,u=F,ll=I,qe=P,ze=E,Ne=D,Ye=L,Ze=T,Be=R,He=$,Ke=O,Qe=N,Ge=S,Je=Y,We=K,Xe=H,la=J,ea=G,aa=Q,ta=X,sa=W;return o(),i("div",sl,[d("div",nl,[d("div",ul,[r(a,{size:60,src:c(oe).avatar},null,8,["src"]),d("div",il,[d("h2",null,b(c(oe).nickname||"群主"),1),d("p",null,"群主ID: "+b(c(oe).userInfo?.owner_code||"O"+c(oe).userInfo?.id),1),d("div",ol,[r(t,{type:"success"},{default:p(()=>e[12]||(e[12]=[_("认证群主",-1)])),_:1,__:[12]}),r(t,{type:"primary"},{default:p(()=>[_(b(Ie.value),1)]),_:1})])])]),d("div",dl,[r(n,{type:"info",onClick:e[0]||(e[0]=l=>ce.value=!0)},{default:p(()=>[r(s,null,{default:p(()=>[r(c(w))]),_:1}),e[13]||(e[13]=_(" 功能说明 ",-1))]),_:1,__:[13]}),r(n,{type:"primary",onClick:Pe},{default:p(()=>[r(s,null,{default:p(()=>[r(c(j))]),_:1}),e[14]||(e[14]=_(" 创建群组 ",-1))]),_:1,__:[14]}),r(n,{onClick:De},{default:p(()=>[r(s,null,{default:p(()=>[r(c(x))]),_:1}),e[15]||(e[15]=_(" 群组管理 ",-1))]),_:1,__:[15]})])]),r(ll,{gutter:20,class:"stats-row"},{default:p(()=>[r(u,{span:6},{default:p(()=>[r(el,{title:"管理群组",value:_e.value.total_groups||0,icon:"Comment",color:"#409EFF",trend:_e.value.group_growth_rate},null,8,["value","trend"])]),_:1}),r(u,{span:6},{default:p(()=>[r(el,{title:"群组成员",value:_e.value.total_members||0,icon:"User",color:"#67C23A",trend:_e.value.member_growth_rate},null,8,["value","trend"])]),_:1}),r(u,{span:6},{default:p(()=>[r(el,{title:"本月收入",value:_e.value.monthly_income||0,icon:"Money",color:"#E6A23C",prefix:"¥",trend:_e.value.income_trend},null,8,["value","trend"])]),_:1}),r(u,{span:6},{default:p(()=>[r(el,{title:"活跃度",value:_e.value.activity_rate||0,icon:"TrendCharts",color:"#F56C6C",suffix:"%"},null,8,["value"])]),_:1})]),_:1}),r(qe,{class:"quick-actions-card"},{header:p(()=>e[16]||(e[16]=[d("span",null,"快捷操作",-1)])),default:p(()=>[r(ll,{gutter:15},{default:p(()=>[r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:Pe},[r(s,{class:"action-icon"},{default:p(()=>[r(c(j))]),_:1}),e[17]||(e[17]=d("span",null,"创建群组",-1))])]),_:1}),r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:De},[r(s,{class:"action-icon"},{default:p(()=>[r(c(x))]),_:1}),e[18]||(e[18]=d("span",null,"群组管理",-1))])]),_:1}),r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:Ee},[r(s,{class:"action-icon"},{default:p(()=>[r(c(q))]),_:1}),e[19]||(e[19]=d("span",null,"内容管理",-1))])]),_:1}),r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:Le},[r(s,{class:"action-icon"},{default:p(()=>[r(c(U))]),_:1}),e[20]||(e[20]=d("span",null,"模板库",-1))])]),_:1}),r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:Me},[r(s,{class:"action-icon"},{default:p(()=>[r(c(z))]),_:1}),e[21]||(e[21]=d("span",null,"数据分析",-1))])]),_:1}),r(u,{span:4},{default:p(()=>[d("div",{class:"action-item",onClick:Se},[r(s,{class:"action-icon"},{default:p(()=>[r(c(A))]),_:1}),e[22]||(e[22]=d("span",null,"群组设置",-1))])]),_:1})]),_:1})]),_:1}),r(ll,{gutter:20,class:"charts-row"},{default:p(()=>[r(u,{span:16},{default:p(()=>[r(qe,null,{header:p(()=>[d("div",rl,[e[26]||(e[26]=d("span",null,"群组活跃度趋势",-1)),r(Ne,{modelValue:de.value,"onUpdate:modelValue":e[1]||(e[1]=l=>de.value=l),size:"small",onChange:Ue},{default:p(()=>[r(ze,{label:"7d"},{default:p(()=>e[23]||(e[23]=[_("近7天",-1)])),_:1,__:[23]}),r(ze,{label:"30d"},{default:p(()=>e[24]||(e[24]=[_("近30天",-1)])),_:1,__:[24]}),r(ze,{label:"90d"},{default:p(()=>e[25]||(e[25]=[_("近3个月",-1)])),_:1,__:[25]})]),_:1},8,["modelValue"])])]),default:p(()=>[r(al,{data:ke.value,options:xe,height:"300px"},null,8,["data"])]),_:1})]),_:1}),r(u,{span:8},{default:p(()=>[r(qe,null,{header:p(()=>e[27]||(e[27]=[d("span",null,"群组类型分布",-1)])),default:p(()=>[r(tl,{data:je.value,options:Fe,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),r(ll,{gutter:20,class:"info-row"},{default:p(()=>[r(u,{span:12},{default:p(()=>[r(qe,null,{header:p(()=>[d("div",cl,[e[29]||(e[29]=d("span",null,"我的群组",-1)),r(n,{size:"small",onClick:De},{default:p(()=>e[28]||(e[28]=[_("查看全部",-1)])),_:1,__:[28]})])]),default:p(()=>[d("div",pl,[(o(!0),i(f,null,v(me.value,l=>{return o(),i("div",{key:l.id,class:"group-item"},[d("div",_l,[r(a,{size:40,src:l.cover_image},null,8,["src"])]),d("div",ml,[d("div",fl,b(l.title),1),d("div",vl,[d("span",gl,b(l.member_count)+"人",1),d("span",yl,b(l.type_text),1)])]),d("div",hl,[r(t,{type:(s=l.status,{active:"success",inactive:"warning",suspended:"danger"}[s]||"info"),size:"small"},{default:p(()=>[_(b(l.status_text),1)]),_:2},1032,["type"])]),d("div",bl,[r(n,{size:"small",onClick:e=>(l=>{ie.push(`/community/groups/${l.id}`)})(l)},{default:p(()=>e[30]||(e[30]=[_(" 管理 ",-1)])),_:2,__:[30]},1032,["onClick"])])]);var s}),128)),0===me.value.length?(o(),i("div",Cl,[r(Ye,{description:"暂无群组"})])):m("",!0)])]),_:1})]),_:1}),r(u,{span:12},{default:p(()=>[r(qe,null,{header:p(()=>[d("div",Vl,[e[32]||(e[32]=d("span",null,"最新动态",-1)),r(n,{size:"small",onClick:Ae},{default:p(()=>[r(s,null,{default:p(()=>[r(c(M))]),_:1}),e[31]||(e[31]=_(" 刷新 ",-1))]),_:1,__:[31]})])]),default:p(()=>[d("div",wl,[(o(!0),i(f,null,v(fe.value,l=>{return o(),i("div",{key:l.id,class:"activity-item"},[d("div",kl,[r(s,{color:(a=l.type,{member:"#67C23A",income:"#409EFF",content:"#E6A23C"}[a]||"#909399")},{default:p(()=>[(o(),g(y($e(l.type))))]),_:2},1032,["color"])]),d("div",jl,[d("div",xl,b(l.title),1),d("div",Fl,b(l.description),1),d("div",Il,b((e=l.created_at,new Date(e).toLocaleString("zh-CN"))),1)]),l.value?(o(),i("div",ql,[d("span",Ul,b(l.value),1)])):m("",!0)]);var e,a}),128)),0===fe.value.length?(o(),i("div",zl,[r(Ye,{description:"暂无动态"})])):m("",!0)])]),_:1})]),_:1})]),_:1}),r(qe,{class:"content-management-card"},{header:p(()=>e[33]||(e[33]=[d("span",null,"内容管理",-1)])),default:p(()=>[r(ll,{gutter:20},{default:p(()=>[r(u,{span:8},{default:p(()=>[d("div",Al,[e[35]||(e[35]=d("h4",null,"群组公告",-1)),e[36]||(e[36]=d("p",null,"管理群组公告和重要通知",-1)),r(n,{onClick:Re},{default:p(()=>e[34]||(e[34]=[_("管理公告",-1)])),_:1,__:[34]})])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Pl,[e[38]||(e[38]=d("h4",null,"内容模板",-1)),e[39]||(e[39]=d("p",null,"使用和管理群组内容模板",-1)),r(n,{onClick:Le},{default:p(()=>e[37]||(e[37]=[_("模板库",-1)])),_:1,__:[37]})])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Dl,[e[41]||(e[41]=d("h4",null,"素材管理",-1)),e[42]||(e[42]=d("p",null,"管理图片、视频等素材资源",-1)),r(n,{onClick:Te},{default:p(()=>e[40]||(e[40]=[_("素材库",-1)])),_:1,__:[40]})])]),_:1})]),_:1})]),_:1}),r(Je,{modelValue:re.value,"onUpdate:modelValue":e[7]||(e[7]=l=>re.value=l),title:"创建群组",width:"600px"},{footer:p(()=>[r(n,{onClick:e[6]||(e[6]=l=>re.value=!1)},{default:p(()=>e[43]||(e[43]=[_("取消",-1)])),_:1,__:[43]}),r(n,{type:"primary",onClick:Oe,loading:pe.value},{default:p(()=>e[44]||(e[44]=[_(" 创建群组 ",-1)])),_:1,__:[44]},8,["loading"])]),default:p(()=>[r(Ge,{model:Ce,rules:Ve,ref_key:"groupFormRef",ref:we,"label-width":"100px"},{default:p(()=>[r(Be,{label:"群组名称",prop:"title"},{default:p(()=>[r(Ze,{modelValue:Ce.title,"onUpdate:modelValue":e[2]||(e[2]=l=>Ce.title=l),placeholder:"请输入群组名称"},null,8,["modelValue"])]),_:1}),r(Be,{label:"群组类型",prop:"type"},{default:p(()=>[r(Ke,{modelValue:Ce.type,"onUpdate:modelValue":e[3]||(e[3]=l=>Ce.type=l),placeholder:"请选择群组类型"},{default:p(()=>[r(He,{label:"免费群组",value:"free"}),r(He,{label:"付费群组",value:"paid"}),r(He,{label:"VIP群组",value:"vip"})]),_:1},8,["modelValue"])]),_:1}),"free"!==Ce.type?(o(),g(Be,{key:0,label:"群组价格",prop:"price"},{default:p(()=>[r(Qe,{modelValue:Ce.price,"onUpdate:modelValue":e[4]||(e[4]=l=>Ce.price=l),min:0,precision:2,placeholder:"请输入价格",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):m("",!0),r(Be,{label:"群组描述",prop:"description"},{default:p(()=>[r(Ze,{modelValue:Ce.description,"onUpdate:modelValue":e[5]||(e[5]=l=>Ce.description=l),type:"textarea",rows:3,placeholder:"请输入群组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),r(Je,{modelValue:ce.value,"onUpdate:modelValue":e[11]||(e[11]=l=>ce.value=l),title:"群主工作台功能说明",width:"1000px",class:"help-dialog"},{default:p(()=>[d("div",El,[e[78]||(e[78]=d("div",{class:"help-section"},[d("h3",null,"👑 功能概述"),d("p",null,"群主工作台是您管理微信群组的专业平台，提供群组创建、成员管理、内容发布、数据分析等全方位功能，帮助您高效运营群组，实现流量变现。")],-1)),d("div",Ll,[e[51]||(e[51]=d("h3",null,"🚀 核心功能模块",-1)),r(ll,{gutter:20},{default:p(()=>[r(u,{span:8},{default:p(()=>[d("div",Ml,[d("div",Sl,[r(s,null,{default:p(()=>[r(c(x))]),_:1})]),e[45]||(e[45]=d("div",{class:"feature-content"},[d("h4",null,"群组管理"),d("p",null,"创建、编辑、删除群组，设置群组属性和权限")],-1))])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Rl,[d("div",Tl,[r(s,null,{default:p(()=>[r(c(Z))]),_:1})]),e[46]||(e[46]=d("div",{class:"feature-content"},[d("h4",null,"成员管理"),d("p",null,"管理群组成员，设置管理员，处理加群申请")],-1))])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Ol,[d("div",$l,[r(s,null,{default:p(()=>[r(c(q))]),_:1})]),e[47]||(e[47]=d("div",{class:"feature-content"},[d("h4",null,"内容管理"),d("p",null,"发布群公告，管理群内容，使用内容模板")],-1))])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Nl,[d("div",Yl,[r(s,null,{default:p(()=>[r(c(z))]),_:1})]),e[48]||(e[48]=d("div",{class:"feature-content"},[d("h4",null,"数据分析"),d("p",null,"查看群组活跃度、成员增长、收入统计等数据")],-1))])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Zl,[d("div",Bl,[r(s,null,{default:p(()=>[r(c(B))]),_:1})]),e[49]||(e[49]=d("div",{class:"feature-content"},[d("h4",null,"收益管理"),d("p",null,"查看群组收入，设置付费门槛，管理收益分成")],-1))])]),_:1}),r(u,{span:8},{default:p(()=>[d("div",Hl,[d("div",Kl,[r(s,null,{default:p(()=>[r(c(A))]),_:1})]),e[50]||(e[50]=d("div",{class:"feature-content"},[d("h4",null,"群组设置"),d("p",null,"配置群规则、自动回复、入群验证等功能")],-1))])]),_:1})]),_:1})]),d("div",Ql,[e[52]||(e[52]=d("h3",null,"📊 群组类型说明",-1)),r(Xe,{data:he.value,style:{width:"100%"}},{default:p(()=>[r(We,{prop:"type",label:"群组类型",width:"120"},{default:p(({row:l})=>[r(t,{type:l.color},{default:p(()=>[_(b(l.type),1)]),_:2},1032,["type"])]),_:1}),r(We,{prop:"description",label:"类型说明"}),r(We,{prop:"features",label:"主要特点"}),r(We,{prop:"suitable",label:"适用场景"})]),_:1},8,["data"])]),d("div",Gl,[e[53]||(e[53]=d("h3",null,"🏆 群主等级体系",-1)),r(Xe,{data:be.value,style:{width:"100%"}},{default:p(()=>[r(We,{prop:"level",label:"等级",width:"100"},{default:p(({row:l})=>[r(t,{type:l.color},{default:p(()=>[_(b(l.level),1)]),_:2},1032,["type"])]),_:1}),r(We,{prop:"name",label:"等级名称",width:"120"}),r(We,{prop:"requirements",label:"升级条件"}),r(We,{prop:"benefits",label:"专属权益"})]),_:1},8,["data"])]),d("div",Jl,[e[63]||(e[63]=d("h3",null,"💡 群组运营技巧",-1)),r(aa,{modelValue:ve.value,"onUpdate:modelValue":e[8]||(e[8]=l=>ve.value=l),type:"card"},{default:p(()=>[r(ea,{label:"内容运营",name:"content"},{default:p(()=>[d("div",Wl,[e[55]||(e[55]=d("h4",null,"📝 内容策略建议",-1)),e[56]||(e[56]=d("ul",null,[d("li",null,[d("strong",null,"定期发布"),_("：保持群内活跃度，建议每日至少发布1-2条有价值内容")]),d("li",null,[d("strong",null,"内容多样化"),_("：结合文字、图片、视频等多种形式，提升用户体验")]),d("li",null,[d("strong",null,"互动引导"),_("：通过提问、投票等方式引导成员参与讨论")]),d("li",null,[d("strong",null,"专业分享"),_("：分享行业知识、经验心得，建立专业形象")]),d("li",null,[d("strong",null,"及时回复"),_("：积极回应成员问题，维护良好的群氛围")])],-1)),r(la,{type:"success",closable:!1,style:{"margin-top":"15px"}},{default:p(()=>e[54]||(e[54]=[_(" 💡 建议：使用内容模板功能，提前准备优质内容，提高发布效率 ",-1)])),_:1,__:[54]})])]),_:1}),r(ea,{label:"成员管理",name:"member"},{default:p(()=>[d("div",Xl,[e[58]||(e[58]=d("h4",null,"👥 成员管理策略",-1)),e[59]||(e[59]=d("ul",null,[d("li",null,[d("strong",null,"入群审核"),_("：设置入群验证，筛选优质成员")]),d("li",null,[d("strong",null,"群规制定"),_("：明确群规则，维护群秩序")]),d("li",null,[d("strong",null,"活跃激励"),_("：对活跃成员给予奖励，提升参与度")]),d("li",null,[d("strong",null,"分层管理"),_("：设置管理员，分工协作管理群组")]),d("li",null,[d("strong",null,"定期清理"),_("：清理不活跃成员，保持群质量")])],-1)),r(la,{type:"info",closable:!1,style:{"margin-top":"15px"}},{default:p(()=>e[57]||(e[57]=[_(" 💡 提示：合理控制群成员数量，一般建议单群不超过500人 ",-1)])),_:1,__:[57]})])]),_:1}),r(ea,{label:"变现策略",name:"monetization"},{default:p(()=>[d("div",le,[e[61]||(e[61]=d("h4",null,"💰 变现方式推荐",-1)),e[62]||(e[62]=d("ul",null,[d("li",null,[d("strong",null,"付费入群"),_("：设置入群费用，筛选付费用户")]),d("li",null,[d("strong",null,"VIP服务"),_("：提供专属服务，收取会员费")]),d("li",null,[d("strong",null,"产品推广"),_("：推广相关产品，获取佣金收入")]),d("li",null,[d("strong",null,"知识付费"),_("：分享专业知识，收取学费")]),d("li",null,[d("strong",null,"广告合作"),_("：接受广告投放，获取广告费")])],-1)),r(la,{type:"warning",closable:!1,style:{"margin-top":"15px"}},{default:p(()=>e[60]||(e[60]=[_(" ⚠️ 注意：变现要适度，过度商业化可能影响群活跃度 ",-1)])),_:1,__:[60]})])]),_:1})]),_:1},8,["modelValue"])]),d("div",ee,[e[72]||(e[72]=d("h3",null,"📝 操作指南",-1)),r(sa,{modelValue:ge.value,"onUpdate:modelValue":e[9]||(e[9]=l=>ge.value=l)},{default:p(()=>[r(ta,{title:"如何创建群组？",name:"create-group"},{default:p(()=>[d("div",ae,[e[65]||(e[65]=d("ol",null,[d("li",null,'点击页面右上角的"创建群组"按钮'),d("li",null,"填写群组基本信息（名称、类型、描述）"),d("li",null,"如果是付费群组，设置入群价格"),d("li",null,'点击"创建群组"完成创建'),d("li",null,'创建成功后可在"我的群组"中查看和管理')],-1)),r(la,{type:"info",closable:!1},{default:p(()=>e[64]||(e[64]=[_(" 💡 建议：群组名称要简洁明了，描述要突出群价值和特色 ",-1)])),_:1,__:[64]})])]),_:1}),r(ta,{title:"如何查看群组数据？",name:"view-data"},{default:p(()=>[d("div",te,[e[67]||(e[67]=d("ol",null,[d("li",null,"在工作台首页查看核心数据统计卡片"),d("li",null,'查看"群组活跃度趋势"图表了解活跃情况'),d("li",null,'在"群组类型分布"图表中查看群组结构'),d("li",null,'点击"数据分析"进入详细分析页面'),d("li",null,"可按时间段筛选查看不同期间的数据")],-1)),r(la,{type:"success",closable:!1},{default:p(()=>e[66]||(e[66]=[_(" ✅ 说明：数据每小时更新一次，可实时了解群组运营状况 ",-1)])),_:1,__:[66]})])]),_:1}),r(ta,{title:"如何管理群组内容？",name:"content-management"},{default:p(()=>[d("div",se,[e[69]||(e[69]=d("ol",null,[d("li",null,'点击"内容管理"进入内容管理页面'),d("li",null,'在"群组公告"中发布重要通知'),d("li",null,'使用"内容模板"快速创建标准化内容'),d("li",null,'在"素材管理"中上传和管理图片、视频等素材'),d("li",null,"设置自动回复和欢迎语")],-1)),r(la,{type:"info",closable:!1},{default:p(()=>e[68]||(e[68]=[_(" 💡 技巧：善用模板功能，可以大大提高内容发布效率 ",-1)])),_:1,__:[68]})])]),_:1}),r(ta,{title:"如何设置群组规则？",name:"group-settings"},{default:p(()=>[d("div",ne,[e[71]||(e[71]=d("ol",null,[d("li",null,'点击"群组设置"进入设置页面'),d("li",null,'在"基础设置"中配置群基本信息'),d("li",null,'在"入群设置"中配置入群验证和审核'),d("li",null,'在"群规管理"中设置群规则和违规处理'),d("li",null,'在"自动化设置"中配置自动回复等功能')],-1)),r(la,{type:"warning",closable:!1},{default:p(()=>e[70]||(e[70]=[_(" ⚠️ 提醒：群规要明确具体，便于执行和管理 ",-1)])),_:1,__:[70]})])]),_:1})]),_:1},8,["modelValue"])]),d("div",ue,[e[77]||(e[77]=d("h3",null,"❓ 常见问题",-1)),r(sa,{modelValue:ye.value,"onUpdate:modelValue":e[10]||(e[10]=l=>ye.value=l)},{default:p(()=>[r(ta,{title:"群组收入如何结算？",name:"faq1"},{default:p(()=>e[73]||(e[73]=[d("p",null,"群组收入采用T+1结算模式，即今日产生的收入将在明日到账。您可以在收益管理中查看详细的收入明细和提现记录。",-1)])),_:1,__:[73]}),r(ta,{title:"如何提升群组活跃度？",name:"faq2"},{default:p(()=>e[74]||(e[74]=[d("p",null,"建议：1）定期发布有价值的内容；2）组织群内活动和讨论；3）及时回应成员问题；4）设置活跃奖励机制；5）邀请行业专家分享。",-1)])),_:1,__:[74]}),r(ta,{title:"付费群组如何定价？",name:"faq3"},{default:p(()=>e[75]||(e[75]=[d("p",null,"定价建议根据群价值、目标用户、市场行情等因素综合考虑。一般建议：入门群10-50元，专业群50-200元，高端群200-500元。",-1)])),_:1,__:[75]}),r(ta,{title:"群组被封怎么办？",name:"faq4"},{default:p(()=>e[76]||(e[76]=[d("p",null,"如果群组被微信封禁，请及时联系客服处理。平时要注意：1）遵守微信群规；2）避免发布违规内容；3）不要频繁拉人；4）定期备份重要信息。",-1)])),_:1,__:[76]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-a6a43998"]]);export{ie as default};
