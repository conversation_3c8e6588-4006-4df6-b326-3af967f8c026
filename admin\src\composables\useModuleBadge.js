import { ref, computed } from 'vue'

/**
 * 模块徽章 Composable
 * 用于管理导航模块的徽章显示
 */
export function useModuleBadge() {
  // 徽章数据存储
  const badges = ref({
    dashboard: { count: 0, type: 'info', visible: false },
    community: { count: 0, type: 'primary', visible: false },
    promotion: { count: 0, type: 'success', visible: false },
    distribution: { count: 0, type: 'warning', visible: false },
    system: { count: 0, type: 'danger', visible: false },
    security: { count: 0, type: 'info', visible: false }
  })

  // 徽章类型映射
  const badgeTypeMap = {
    info: '#909399',
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C'
  }

  // 设置模块徽章
  const setBadge = (moduleKey, count, type = 'primary', visible = true) => {
    if (badges.value[moduleKey]) {
      badges.value[moduleKey] = {
        count: Math.max(0, count),
        type,
        visible: visible && count > 0
      }
    }
  }

  // 增加徽章计数
  const incrementBadge = (moduleKey, increment = 1) => {
    if (badges.value[moduleKey]) {
      const newCount = badges.value[moduleKey].count + increment
      setBadge(moduleKey, newCount, badges.value[moduleKey].type)
    }
  }

  // 减少徽章计数
  const decrementBadge = (moduleKey, decrement = 1) => {
    if (badges.value[moduleKey]) {
      const newCount = Math.max(0, badges.value[moduleKey].count - decrement)
      setBadge(moduleKey, newCount, badges.value[moduleKey].type)
    }
  }

  // 清除模块徽章
  const clearBadge = (moduleKey) => {
    if (badges.value[moduleKey]) {
      badges.value[moduleKey] = {
        ...badges.value[moduleKey],
        count: 0,
        visible: false
      }
    }
  }

  // 清除所有徽章
  const clearAllBadges = () => {
    Object.keys(badges.value).forEach(key => {
      clearBadge(key)
    })
  }

  // 获取模块徽章
  const getBadge = (moduleKey) => {
    return badges.value[moduleKey] || { count: 0, type: 'info', visible: false }
  }

  // 获取徽章显示文本
  const getBadgeText = (moduleKey) => {
    const badge = getBadge(moduleKey)
    if (!badge.visible || badge.count <= 0) return ''
    
    // 如果数量大于99，显示99+
    return badge.count > 99 ? '99+' : badge.count.toString()
  }

  // 获取徽章颜色
  const getBadgeColor = (moduleKey) => {
    const badge = getBadge(moduleKey)
    return badgeTypeMap[badge.type] || badgeTypeMap.primary
  }

  // 检查模块是否有徽章
  const hasBadge = (moduleKey) => {
    const badge = getBadge(moduleKey)
    return badge.visible && badge.count > 0
  }

  // 获取总徽章数量
  const totalBadgeCount = computed(() => {
    return Object.values(badges.value).reduce((total, badge) => {
      return total + (badge.visible ? badge.count : 0)
    }, 0)
  })

  // 获取可见徽章数量
  const visibleBadgeCount = computed(() => {
    return Object.values(badges.value).filter(badge => badge.visible && badge.count > 0).length
  })

  // 模拟数据更新（用于演示）
  const simulateUpdates = () => {
    // 社群管理 - 新消息
    setBadge('community', 5, 'primary')
    
    // 推广管理 - 待审核
    setBadge('promotion', 3, 'warning')
    
    // 分销管理 - 新订单
    setBadge('distribution', 12, 'success')
    
    // 系统管理 - 警告
    setBadge('system', 2, 'danger')
  }

  // 根据业务逻辑更新徽章
  const updateBadgesByData = (data) => {
    if (!data) return

    // 根据实际业务数据更新徽章
    if (data.pendingGroups) {
      setBadge('community', data.pendingGroups, 'warning')
    }

    if (data.newPromotions) {
      setBadge('promotion', data.newPromotions, 'success')
    }

    if (data.pendingOrders) {
      setBadge('distribution', data.pendingOrders, 'primary')
    }

    if (data.systemAlerts) {
      setBadge('system', data.systemAlerts, 'danger')
    }

    if (data.securityWarnings) {
      setBadge('security', data.securityWarnings, 'danger')
    }
  }

  // 批量设置徽章
  const setBadges = (badgeData) => {
    Object.entries(badgeData).forEach(([moduleKey, badgeInfo]) => {
      if (typeof badgeInfo === 'number') {
        setBadge(moduleKey, badgeInfo)
      } else if (typeof badgeInfo === 'object') {
        setBadge(moduleKey, badgeInfo.count, badgeInfo.type, badgeInfo.visible)
      }
    })
  }

  return {
    badges,
    setBadge,
    incrementBadge,
    decrementBadge,
    clearBadge,
    clearAllBadges,
    getBadge,
    getBadgeText,
    getBadgeColor,
    hasBadge,
    totalBadgeCount,
    visibleBadgeCount,
    simulateUpdates,
    updateBadgesByData,
    setBadges
  }
}
