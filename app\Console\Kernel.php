<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CheckDomainsCommand::class,
        Commands\GenerateJWTSecretCommand::class,
        Commands\ResetAdminPassword::class,
        Commands\CheckDomainHealth::class,
        Commands\CheckDistributorLevels::class,
        Commands\SystemOptimization::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // ==================== 域名健康检查 ====================
        
        // 每10分钟进行基础域名检查
        $schedule->command('domain:check-health --batch=20')
                 ->everyTenMinutes()
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/domain-check.log'));

        // 每2小时进行智能域名检查
        $schedule->command('domain:check-health --intelligent')
                 ->everyTwoHours()
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/domain-intelligent-check.log'));

        // 每天凌晨3点进行全面域名检查（包括已封禁的）
        $schedule->command('domain:check-health --intelligent --all')
                 ->dailyAt('03:00')
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/domain-full-check.log'));

        // ==================== 分销商等级检查 ====================
        
        // 每天凌晨2点检查分销商等级
        $schedule->command('distributor:check-levels --batch=100')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/distributor-levels.log'));

        // 每周日凌晨1点强制检查所有分销商等级
        $schedule->command('distributor:check-levels --batch=50 --force')
                 ->weeklyOn(0, '01:00')
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/distributor-levels-weekly.log'));

        // ==================== 系统优化 ====================
        
        // 每天凌晨4点进行系统优化
        $schedule->command('system:optimize --all')
                 ->dailyAt('04:00')
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/system-optimization.log'));

        // 每小时清理过期缓存
        $schedule->command('system:optimize --clean-cache')
                 ->hourly()
                 ->withoutOverlapping();

        // 每6小时清理过期会话
        $schedule->command('system:optimize --clean-sessions')
                 ->everySixHours()
                 ->withoutOverlapping();

        // ==================== 数据库维护 ====================
        
        // 每周日凌晨5点优化数据库
        $schedule->command('system:optimize --optimize-db')
                 ->weeklyOn(0, '05:00')
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/database-optimization.log'));

        // ==================== 兼容旧命令 ====================
        
        // 保持旧的域名检查命令（向后兼容）
        $schedule->command('domains:check --limit=10')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // 每天清理过期的检测日志
        $schedule->call(function () {
            try {
                // 清理域名检查日志（保留60天）
                if (class_exists('\App\Models\DomainCheckLog')) {
                    \App\Models\DomainCheckLog::where('created_at', '<', now()->subDays(60))->delete();
                }
                
                // 清理链接访问日志（保留90天）
                if (class_exists('\App\Models\LinkAccessLog')) {
                    \App\Models\LinkAccessLog::where('created_at', '<', now()->subDays(90))->delete();
                }
                
                // 清理操作日志（保留30天）
                \DB::table('operation_logs')->where('created_at', '<', now()->subDays(30))->delete();
                
                // 清理支付回调日志（保留30天）
                if (\Schema::hasTable('payment_callback_logs')) {
                    \DB::table('payment_callback_logs')->where('created_at', '<', now()->subDays(30))->delete();
                }
                
            } catch (\Exception $e) {
                \Log::error('定时清理日志失败', ['error' => $e->getMessage()]);
            }
        })->daily()->at('06:00');

        // ==================== 业务相关定时任务 ====================
        
        // 每天凌晨1点处理待结算的佣金
        $schedule->call(function () {
            try {
                $commissionService = app(\App\Services\CommissionService::class);
                
                // 获取待结算的佣金（订单完成7天后自动结算）
                $pendingCommissions = \App\Models\CommissionLog::where('status', 'pending')
                    ->whereHas('order', function ($query) {
                        $query->where('status', \App\Models\Order::STATUS_PAID_INT)
                              ->where('paid_at', '<', now()->subDays(7));
                    })
                    ->limit(1000)
                    ->pluck('id')
                    ->toArray();
                
                if (!empty($pendingCommissions)) {
                    $result = $commissionService->batchSettleCommissions($pendingCommissions);
                    \Log::info('自动结算佣金完成', $result);
                }
                
            } catch (\Exception $e) {
                \Log::error('自动结算佣金失败', ['error' => $e->getMessage()]);
            }
        })->dailyAt('01:00');

        // 每小时检查订单超时
        $schedule->call(function () {
            try {
                // 取消超时的待支付订单
                $expiredOrders = \App\Models\Order::where('status', \App\Models\Order::STATUS_PENDING_INT)
                    ->where('expired_at', '<', now())
                    ->limit(100)
                    ->get();
                
                foreach ($expiredOrders as $order) {
                    $order->update(['status' => \App\Models\Order::STATUS_CANCELLED_INT]);
                    \Log::info('订单超时自动取消', ['order_no' => $order->order_no]);
                }
                
            } catch (\Exception $e) {
                \Log::error('处理超时订单失败', ['error' => $e->getMessage()]);
            }
        })->hourly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 