<template>
  <section class="form-component" :style="componentStyle">
    <div class="form-container">
      <div class="form-header" v-if="data.title">
        <h2 class="form-title">{{ data.title }}</h2>
        <p class="form-subtitle" v-if="data.subtitle">{{ data.subtitle }}</p>
      </div>
      
      <div class="form-content">
        <el-form 
          ref="formRef"
          :model="formData" 
          :rules="formRules"
          label-position="top"
          class="landing-form"
        >
          <div 
            v-for="(field, index) in data.fields" 
            :key="index"
            class="form-field"
          >
            <el-form-item 
              :label="field.label"
              :prop="field.name || `field_${index}`"
              :required="field.required"
            >
              <!-- 文本输入 -->
              <el-input 
                v-if="field.type === 'text'"
                v-model="formData[field.name || `field_${index}`]"
                :placeholder="field.placeholder || `请输入${field.label}`"
                size="large"
              />
              
              <!-- 邮箱输入 -->
              <el-input 
                v-else-if="field.type === 'email'"
                v-model="formData[field.name || `field_${index}`]"
                type="email"
                :placeholder="field.placeholder || `请输入${field.label}`"
                size="large"
              />
              
              <!-- 电话输入 -->
              <el-input 
                v-else-if="field.type === 'phone'"
                v-model="formData[field.name || `field_${index}`]"
                :placeholder="field.placeholder || `请输入${field.label}`"
                size="large"
              />
              
              <!-- 文本域 -->
              <el-input 
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.name || `field_${index}`]"
                type="textarea"
                :rows="field.rows || 4"
                :placeholder="field.placeholder || `请输入${field.label}`"
                resize="none"
              />
              
              <!-- 选择器 -->
              <el-select 
                v-else-if="field.type === 'select'"
                v-model="formData[field.name || `field_${index}`]"
                :placeholder="field.placeholder || `请选择${field.label}`"
                size="large"
                style="width: 100%"
              >
                <el-option 
                  v-for="option in field.options" 
                  :key="option.value"
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
              
              <!-- 复选框组 -->
              <el-checkbox-group 
                v-else-if="field.type === 'checkbox'"
                v-model="formData[field.name || `field_${index}`]"
              >
                <el-checkbox 
                  v-for="option in field.options" 
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>
              
              <!-- 单选框组 -->
              <el-radio-group 
                v-else-if="field.type === 'radio'"
                v-model="formData[field.name || `field_${index}`]"
              >
                <el-radio 
                  v-for="option in field.options" 
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          
          <div class="form-actions">
            <el-button 
              type="primary" 
              size="large"
              :loading="submitting"
              @click="handleSubmit"
              class="submit-button"
            >
              {{ data.buttonText || '提交' }}
            </el-button>
          </div>
        </el-form>
        
        <!-- 成功提示 -->
        <div v-if="submitted" class="success-message">
          <el-icon><Check /></el-icon>
          <h3>提交成功！</h3>
          <p>{{ data.successMessage || '感谢您的提交，我们会尽快与您联系。' }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      title: '联系我们',
      subtitle: '请填写以下信息，我们会尽快与您联系',
      fields: [
        { type: 'text', name: 'name', label: '姓名', required: true },
        { type: 'email', name: 'email', label: '邮箱', required: true },
        { type: 'phone', name: 'phone', label: '电话', required: false },
        { type: 'textarea', name: 'message', label: '留言', required: false }
      ],
      buttonText: '提交',
      successMessage: '感谢您的提交，我们会尽快与您联系。',
      backgroundColor: '#f8f9fa',
      textColor: '#333333'
    })
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const formRef = ref()
const submitting = ref(false)
const submitted = ref(false)
const formData = reactive({})
const formRules = reactive({})

const componentStyle = computed(() => ({
  backgroundColor: props.data.backgroundColor || '#f8f9fa',
  color: props.data.textColor || '#333333'
}))

// 初始化表单数据和验证规则
watch(() => props.data.fields, (fields) => {
  if (!fields) return
  
  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  Object.keys(formRules).forEach(key => {
    delete formRules[key]
  })
  
  // 初始化表单数据和规则
  fields.forEach((field, index) => {
    const fieldName = field.name || `field_${index}`
    
    // 初始化数据
    if (field.type === 'checkbox') {
      formData[fieldName] = []
    } else {
      formData[fieldName] = ''
    }
    
    // 设置验证规则
    if (field.required) {
      formRules[fieldName] = [
        { 
          required: true, 
          message: `请${field.type === 'select' ? '选择' : '输入'}${field.label}`,
          trigger: field.type === 'select' ? 'change' : 'blur'
        }
      ]
      
      // 邮箱格式验证
      if (field.type === 'email') {
        formRules[fieldName].push({
          type: 'email',
          message: '请输入正确的邮箱格式',
          trigger: 'blur'
        })
      }
      
      // 电话格式验证
      if (field.type === 'phone') {
        formRules[fieldName].push({
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur'
        })
      }
    }
  })
}, { immediate: true })

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 在实际应用中，这里应该调用API提交数据
    console.log('表单数据:', formData)
    
    submitted.value = true
    ElMessage.success('提交成功！')
    
    // 重置表单
    setTimeout(() => {
      submitted.value = false
      formRef.value?.resetFields()
    }, 3000)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单信息')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.form-component {
  padding: 80px 0;
  
  .form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
    
    .form-header {
      text-align: center;
      margin-bottom: 40px;
      
      .form-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0 0 16px 0;
      }
      
      .form-subtitle {
        font-size: 16px;
        margin: 0;
        opacity: 0.8;
      }
    }
    
    .form-content {
      background: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      
      .landing-form {
        .form-field {
          margin-bottom: 24px;
          
          :deep(.el-form-item__label) {
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
          }
          
          :deep(.el-input__wrapper) {
            border-radius: 8px;
            box-shadow: 0 0 0 1px #dcdfe6;
            
            &:hover {
              box-shadow: 0 0 0 1px #c0c4cc;
            }
            
            &.is-focus {
              box-shadow: 0 0 0 1px #409eff;
            }
          }
          
          :deep(.el-textarea__inner) {
            border-radius: 8px;
            border: 1px solid #dcdfe6;
            
            &:hover {
              border-color: #c0c4cc;
            }
            
            &:focus {
              border-color: #409eff;
            }
          }
          
          :deep(.el-select) {
            width: 100%;
          }
          
          :deep(.el-checkbox-group) {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }
          
          :deep(.el-radio-group) {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }
        }
        
        .form-actions {
          text-align: center;
          margin-top: 32px;
          
          .submit-button {
            padding: 16px 48px;
            font-size: 16px;
            border-radius: 8px;
            min-width: 120px;
          }
        }
      }
      
      .success-message {
        text-align: center;
        padding: 40px 20px;
        
        .el-icon {
          font-size: 64px;
          color: #67c23a;
          margin-bottom: 16px;
        }
        
        h3 {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 12px 0;
          color: #67c23a;
        }
        
        p {
          font-size: 16px;
          margin: 0;
          opacity: 0.8;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .form-component {
    padding: 60px 0;
    
    .form-container {
      .form-header {
        margin-bottom: 32px;
        
        .form-title {
          font-size: 24px;
        }
        
        .form-subtitle {
          font-size: 14px;
        }
      }
      
      .form-content {
        padding: 24px;
        
        .landing-form {
          .form-field {
            margin-bottom: 20px;
          }
          
          .form-actions {
            margin-top: 24px;
            
            .submit-button {
              width: 100%;
              padding: 14px 24px;
            }
          }
        }
      }
    }
  }
}
</style>