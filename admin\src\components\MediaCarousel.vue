<template>
  <div class="media-carousel">
    <div class="carousel-container" ref="carouselContainer">
      <!-- 轮播主体 -->
      <div class="carousel-wrapper" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
        <div 
          v-for="(item, index) in mediaItems" 
          :key="index"
          class="carousel-item"
          :class="{ active: index === currentIndex }"
        >
          <!-- 图片类型 -->
          <div v-if="item.type === 'image'" class="media-image">
            <img 
              :src="item.url" 
              :alt="item.title || '群组图片'"
              @load="onImageLoad"
              @error="onImageError"
            />
            <div v-if="item.title" class="image-overlay">
              <h3>{{ item.title }}</h3>
              <p v-if="item.description">{{ item.description }}</p>
            </div>
          </div>

          <!-- 视频类型 -->
          <div v-else-if="item.type === 'video'" class="media-video">
            <video 
              :src="item.url"
              :poster="item.poster"
              :muted="autoPlay"
              :autoplay="autoPlay && index === currentIndex"
              :loop="item.loop"
              controls
              playsinline
              webkit-playsinline
              @loadeddata="onVideoLoad"
              @error="onVideoError"
            >
              您的浏览器不支持视频播放
            </video>
            <div v-if="item.title" class="video-overlay">
              <h3>{{ item.title }}</h3>
              <p v-if="item.description">{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航点 -->
      <div v-if="showDots && mediaItems.length > 1" class="carousel-dots">
        <button
          v-for="(item, index) in mediaItems"
          :key="index"
          class="dot"
          :class="{ active: index === currentIndex }"
          @click="goToSlide(index)"
        >
          <span class="sr-only">第{{ index + 1 }}张</span>
        </button>
      </div>

      <!-- 导航箭头 -->
      <div v-if="showArrows && mediaItems.length > 1" class="carousel-arrows">
        <button 
          class="arrow arrow-prev" 
          @click="prevSlide"
          :disabled="currentIndex === 0 && !infinite"
        >
          <el-icon><ArrowLeft /></el-icon>
        </button>
        <button 
          class="arrow arrow-next" 
          @click="nextSlide"
          :disabled="currentIndex === mediaItems.length - 1 && !infinite"
        >
          <el-icon><ArrowRight /></el-icon>
        </button>
      </div>

      <!-- 媒体计数器 -->
      <div v-if="showCounter" class="media-counter">
        {{ currentIndex + 1 }} / {{ mediaItems.length }}
      </div>
    </div>

    <!-- 缩略图导航 -->
    <div v-if="showThumbnails && mediaItems.length > 1" class="thumbnail-nav">
      <div class="thumbnail-list">
        <button
          v-for="(item, index) in mediaItems"
          :key="index"
          class="thumbnail"
          :class="{ active: index === currentIndex }"
          @click="goToSlide(index)"
        >
          <img 
            v-if="item.type === 'image'" 
            :src="item.thumbnail || item.url" 
            :alt="item.title"
          />
          <div v-else-if="item.type === 'video'" class="video-thumbnail">
            <img 
              :src="item.poster || item.thumbnail" 
              :alt="item.title"
            />
            <div class="play-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowLeft, ArrowRight, VideoPlay } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  mediaItems: {
    type: Array,
    default: () => []
  },
  autoPlay: {
    type: Boolean,
    default: false
  },
  interval: {
    type: Number,
    default: 3000
  },
  infinite: {
    type: Boolean,
    default: true
  },
  showDots: {
    type: Boolean,
    default: true
  },
  showArrows: {
    type: Boolean,
    default: true
  },
  showCounter: {
    type: Boolean,
    default: false
  },
  showThumbnails: {
    type: Boolean,
    default: false
  },
  height: {
    type: String,
    default: '300px'
  }
})

// Emits
const emit = defineEmits(['change', 'imageLoad', 'imageError', 'videoLoad', 'videoError'])

// 响应式数据
const currentIndex = ref(0)
const carouselContainer = ref(null)
let autoPlayTimer = null

// 计算属性
const hasMedia = computed(() => props.mediaItems && props.mediaItems.length > 0)

// 方法
const goToSlide = (index) => {
  if (index >= 0 && index < props.mediaItems.length) {
    currentIndex.value = index
    emit('change', index, props.mediaItems[index])
  }
}

const nextSlide = () => {
  if (props.infinite) {
    currentIndex.value = (currentIndex.value + 1) % props.mediaItems.length
  } else if (currentIndex.value < props.mediaItems.length - 1) {
    currentIndex.value++
  }
  emit('change', currentIndex.value, props.mediaItems[currentIndex.value])
}

const prevSlide = () => {
  if (props.infinite) {
    currentIndex.value = currentIndex.value === 0 ? props.mediaItems.length - 1 : currentIndex.value - 1
  } else if (currentIndex.value > 0) {
    currentIndex.value--
  }
  emit('change', currentIndex.value, props.mediaItems[currentIndex.value])
}

const startAutoPlay = () => {
  if (props.autoPlay && props.mediaItems.length > 1) {
    autoPlayTimer = setInterval(() => {
      nextSlide()
    }, props.interval)
  }
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 事件处理
const onImageLoad = (event) => {
  emit('imageLoad', event, currentIndex.value)
}

const onImageError = (event) => {
  emit('imageError', event, currentIndex.value)
}

const onVideoLoad = (event) => {
  emit('videoLoad', event, currentIndex.value)
}

const onVideoError = (event) => {
  emit('videoError', event, currentIndex.value)
}

// 触摸事件处理（移动端支持）
let touchStartX = 0
let touchEndX = 0

const handleTouchStart = (event) => {
  touchStartX = event.touches[0].clientX
}

const handleTouchEnd = (event) => {
  touchEndX = event.changedTouches[0].clientX
  handleSwipe()
}

const handleSwipe = () => {
  const swipeThreshold = 50
  const diff = touchStartX - touchEndX

  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      nextSlide()
    } else {
      prevSlide()
    }
  }
}

// 监听器
watch(() => props.autoPlay, (newVal) => {
  if (newVal) {
    startAutoPlay()
  } else {
    stopAutoPlay()
  }
})

watch(() => props.mediaItems, () => {
  currentIndex.value = 0
}, { deep: true })

// 生命周期
onMounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.addEventListener('touchstart', handleTouchStart, { passive: true })
    carouselContainer.value.addEventListener('touchend', handleTouchEnd, { passive: true })
  }
  
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
  
  if (carouselContainer.value) {
    carouselContainer.value.removeEventListener('touchstart', handleTouchStart)
    carouselContainer.value.removeEventListener('touchend', handleTouchEnd)
  }
})
</script>

<style lang="scss" scoped>
.media-carousel {
  width: 100%;
  position: relative;
  
  .carousel-container {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    background: #f5f5f5;
    height: v-bind(height);
  }
  
  .carousel-wrapper {
    display: flex;
    height: 100%;
    transition: transform 0.3s ease-in-out;
  }
  
  .carousel-item {
    flex: 0 0 100%;
    height: 100%;
    position: relative;
    
    .media-image,
    .media-video {
      width: 100%;
      height: 100%;
      position: relative;
      
      img,
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }
    }
    
    .image-overlay,
    .video-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      color: white;
      padding: 20px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
      }
      
      p {
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.4;
      }
    }
  }
  
  .carousel-dots {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      border: none;
      background: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.active {
        background: white;
        transform: scale(1.2);
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  .carousel-arrows {
    .arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.7);
        transform: translateY(-50%) scale(1.1);
      }
      
      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        
        &:hover {
          transform: translateY(-50%);
        }
      }
      
      &.arrow-prev {
        left: 15px;
      }
      
      &.arrow-next {
        right: 15px;
      }
    }
  }
  
  .media-counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .thumbnail-nav {
    margin-top: 15px;
    
    .thumbnail-list {
      display: flex;
      gap: 10px;
      overflow-x: auto;
      padding: 5px 0;
      
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    }
    
    .thumbnail {
      flex: 0 0 60px;
      height: 60px;
      border: 2px solid transparent;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      background: none;
      padding: 0;
      
      &.active {
        border-color: #409eff;
      }
      
      &:hover {
        transform: scale(1.05);
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .video-thumbnail {
        position: relative;
        width: 100%;
        height: 100%;
        
        .play-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 16px;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .media-carousel {
    .carousel-arrows .arrow {
      width: 35px;
      height: 35px;
      
      &.arrow-prev {
        left: 10px;
      }
      
      &.arrow-next {
        right: 10px;
      }
    }
    
    .image-overlay,
    .video-overlay {
      padding: 15px;
      
      h3 {
        font-size: 16px;
      }
      
      p {
        font-size: 13px;
      }
    }
    
    .thumbnail-nav .thumbnail {
      flex: 0 0 50px;
      height: 50px;
    }
  }
}
</style>
