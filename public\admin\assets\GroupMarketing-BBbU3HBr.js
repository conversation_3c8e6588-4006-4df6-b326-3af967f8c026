import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                       *//* empty css                 *//* empty css                    *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     */import{T as l,a0 as a,at as t,aR as o,bp as i,bq as r,aM as u,b9 as d,b8 as s,aL as n,bO as _,aY as c,bh as p,bi as m,U as v,a$ as y,bw as b,bx as f,bj as g,bg as w,br as h,ay as V,bm as k,bB as x,Q as C}from"./element-plus-h2SQQM64.js";import{r as U,L as j,e as q,k as T,l as z,t as $,E as P,z as Z,D as I,u as L,A as O,y as Q,B as S,F as A,Y as B}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const D={class:"group-marketing-container"},M={class:"page-header"},Y={class:"header-content"},E={class:"header-actions"},F={class:"filter-section"},G={class:"table-section"},R={class:"card-header"},H={class:"header-actions"},J={key:0,class:"selected-info"},K={class:"pagination-wrapper"},N={class:"dialog-footer"},W={key:0,class:"preview-content"},X={class:"preview-header"},ee={class:"preview-stats"},le={class:"preview-price"},ae={key:0,class:"preview-intro"},te={key:1,class:"preview-members"},oe={class:"preview-qr"},ie=["src"],re={class:"dialog-footer"},ue=e({__name:"GroupMarketing",setup(e){const ue=U(!1),de=U(!1),se=U(!1),ne=U(!1),_e=U(!1),ce=U("basic"),pe=U("北京"),me=U([]),ve=U([]),ye=j({page:1,size:20,total:0}),be=j({title:"",auto_city_replace:"",display_type:""}),fe=U([]),ge=U(null),we=j({template_id:"",read_count_display:"",like_count:0,want_see_count:0,button_title:"",group_intro_title:"",group_intro_content:"",virtual_members:0,virtual_orders:0}),he=j({scope:"selected",template_id:""}),Ve=U(null),ke=async()=>{ue.value=!0;try{await new Promise(e=>setTimeout(e,500));let e=[...[{id:1,title:"北京商务精英交流群",price:99,avatar_library:"qq",display_type:1,virtual_members:328,virtual_orders:89,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-15T10:00:00Z"},{id:2,title:"上海副业赚钱交流群",price:58,avatar_library:"default",display_type:1,virtual_members:267,virtual_orders:156,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-10T14:30:00Z"},{id:3,title:"深圳学习成长群",price:29,avatar_library:"qq",display_type:2,virtual_members:145,virtual_orders:67,wx_accessible:0,auto_city_replace:0,created_at:"2024-01-20T09:15:00Z"},{id:4,title:"广州健身运动群",price:39,avatar_library:"default",display_type:1,virtual_members:189,virtual_orders:78,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-25T16:45:00Z"},{id:5,title:"杭州创业交流群",price:88,avatar_library:"qq",display_type:1,virtual_members:234,virtual_orders:123,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-12T11:20:00Z"}]];be.title&&(e=e.filter(e=>e.title.toLowerCase().includes(be.title.toLowerCase()))),""!==be.auto_city_replace&&(e=e.filter(e=>e.auto_city_replace==be.auto_city_replace)),""!==be.display_type&&(e=e.filter(e=>e.display_type==be.display_type)),me.value=e,ye.total=e.length,console.log("✅ 群组数据加载成功:",me.value)}catch(e){console.error("获取群组列表失败:",e),C.error("获取群组列表失败")}finally{ue.value=!1}},xe=e=>{ve.value=e},Ce=()=>{Object.assign(be,{title:"",auto_city_replace:"",display_type:""}),ke()},Ue=async()=>{if(ge.value){de.value=!0;try{await new Promise(e=>setTimeout(e,800)),console.log("✅ 营销配置保存成功:",{groupId:ge.value,config:we}),C.success("营销配置保存成功"),se.value=!1,ke()}catch(e){console.error("保存配置失败:",e),C.error("保存配置失败")}finally{de.value=!1}}},je=async()=>{try{const e="selected"===he.scope?ve.value.map(e=>e.id):me.value.map(e=>e.id);await new Promise(e=>setTimeout(e,1e3)),console.log("✅ 批量配置应用成功:",{templateId:he.template_id,groupIds:e,scope:he.scope,affectedCount:e.length}),C.success(`批量配置应用成功，共影响 ${e.length} 个群组`),_e.value=!1,ke()}catch(e){console.error("批量配置失败:",e),C.error("批量配置失败")}};return q(()=>{ke(),(async()=>{try{await new Promise(e=>setTimeout(e,300));const e=[{id:1,name:"商务交流模板",description:"适用于商务人士交流的营销模板",config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入商务群",group_intro_title:"商务交流群简介",group_intro_content:"专业的商务交流平台，汇聚各行业精英",virtual_members:150,virtual_orders:80},created_at:"2024-01-01T00:00:00Z"},{id:2,name:"社交娱乐模板",description:"适用于社交娱乐的营销模板",config:{read_count_display:"3万+",like_count:800,want_see_count:600,button_title:"加入娱乐群",group_intro_title:"娱乐交流群",group_intro_content:"轻松愉快的社交环境，分享生活乐趣",virtual_members:120,virtual_orders:60},created_at:"2024-01-02T00:00:00Z"}];fe.value=e,console.log("✅ 营销模板加载成功:",fe.value)}catch(e){console.error("获取营销模板失败:",e),C.error("获取营销模板失败")}})()}),(e,U)=>{const j=l,q=t,qe=u,Te=r,ze=s,$e=d,Pe=i,Ze=c,Ie=m,Le=y,Oe=p,Qe=f,Se=h,Ae=w,Be=g,De=V,Me=x,Ye=k,Ee=b;return z(),T("div",D,[$("div",M,[$("div",Y,[U[25]||(U[25]=$("div",{class:"header-left"},[$("h1",{class:"page-title"},"营销配置"),$("p",{class:"page-subtitle"},"管理群组的营销配置和模板应用")],-1)),$("div",E,[P(q,{type:"primary",onClick:U[0]||(U[0]=e=>_e.value=!0),disabled:!ve.value.length},{default:Z(()=>[P(j,null,{default:Z(()=>[P(L(a))]),_:1}),U[23]||(U[23]=I(" 批量配置 ",-1))]),_:1,__:[23]},8,["disabled"]),P(q,{onClick:ke},{default:Z(()=>[P(j,null,{default:Z(()=>[P(L(o))]),_:1}),U[24]||(U[24]=I(" 刷新 ",-1))]),_:1,__:[24]})])])]),$("div",F,[P(Ze,{shadow:"never"},{default:Z(()=>[P(Pe,{model:be,inline:""},{default:Z(()=>[P(Te,{label:"群组名称"},{default:Z(()=>[P(qe,{modelValue:be.title,"onUpdate:modelValue":U[1]||(U[1]=e=>be.title=e),placeholder:"请输入群组名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),P(Te,{label:"城市定位"},{default:Z(()=>[P($e,{modelValue:be.auto_city_replace,"onUpdate:modelValue":U[2]||(U[2]=e=>be.auto_city_replace=e),placeholder:"请选择",clearable:"",style:{width:"150px"}},{default:Z(()=>[P(ze,{label:"全部",value:""}),P(ze,{label:"启用",value:1}),P(ze,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),P(Te,{label:"展示类型"},{default:Z(()=>[P($e,{modelValue:be.display_type,"onUpdate:modelValue":U[3]||(U[3]=e=>be.display_type=e),placeholder:"请选择",clearable:"",style:{width:"150px"}},{default:Z(()=>[P(ze,{label:"全部",value:""}),P(ze,{label:"文字+图片",value:1}),P(ze,{label:"纯图片",value:2})]),_:1},8,["modelValue"])]),_:1}),P(Te,null,{default:Z(()=>[P(q,{type:"primary",onClick:ke},{default:Z(()=>[P(j,null,{default:Z(()=>[P(L(n))]),_:1}),U[26]||(U[26]=I(" 搜索 ",-1))]),_:1,__:[26]}),P(q,{onClick:Ce},{default:Z(()=>[P(j,null,{default:Z(()=>[P(L(_))]),_:1}),U[27]||(U[27]=I(" 重置 ",-1))]),_:1,__:[27]})]),_:1})]),_:1},8,["model"])]),_:1})]),$("div",G,[P(Ze,{shadow:"never"},{header:Z(()=>[$("div",R,[U[28]||(U[28]=$("span",null,"群组列表",-1)),$("div",H,[ve.value.length?(z(),T("span",J," 已选择 "+v(ve.value.length)+" 个群组 ",1)):S("",!0)])])]),default:Z(()=>[O((z(),Q(Oe,{data:me.value,onSelectionChange:xe,style:{width:"100%"}},{default:Z(()=>[P(Ie,{type:"selection",width:"55"}),P(Ie,{prop:"id",label:"ID",width:"80"}),P(Ie,{prop:"title",label:"群组名称","min-width":"200"}),P(Ie,{prop:"price",label:"价格",width:"100"},{default:Z(e=>[I(" ¥"+v(e.row.price),1)]),_:1}),P(Ie,{prop:"avatar_library",label:"头像库",width:"100"},{default:Z(e=>[P(Le,{type:"qq"===e.row.avatar_library?"primary":"default"},{default:Z(()=>[I(v("qq"===e.row.avatar_library?"QQ头像":"默认头像"),1)]),_:2},1032,["type"])]),_:1}),P(Ie,{prop:"display_type",label:"展示类型",width:"120"},{default:Z(e=>[P(Le,{type:1===e.row.display_type?"success":"info"},{default:Z(()=>[I(v(1===e.row.display_type?"文字+图片":"纯图片"),1)]),_:2},1032,["type"])]),_:1}),P(Ie,{prop:"virtual_members",label:"虚拟成员",width:"100"}),P(Ie,{prop:"virtual_orders",label:"虚拟订单",width:"100"}),P(Ie,{prop:"wx_accessible",label:"微信访问",width:"100"},{default:Z(e=>[P(Le,{type:e.row.wx_accessible?"success":"danger"},{default:Z(()=>[I(v(e.row.wx_accessible?"允许":"限制"),1)]),_:2},1032,["type"])]),_:1}),P(Ie,{prop:"auto_city_replace",label:"城市定位",width:"100"},{default:Z(e=>[P(Le,{type:e.row.auto_city_replace?"success":"info"},{default:Z(()=>[I(v(e.row.auto_city_replace?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),P(Ie,{label:"操作",width:"300",fixed:"right"},{default:Z(e=>[P(q,{size:"small",type:"primary",onClick:l=>(async e=>{ge.value=e.id;try{await new Promise(e=>setTimeout(e,300));const l={basic:{template_id:1,read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入群聊",group_intro_title:`${e.title}简介`,group_intro_content:`欢迎加入${e.title}，这里有最新的资讯和优质的服务！`,virtual_members:e.virtual_members||150,virtual_orders:e.virtual_orders||80}};Object.assign(we,l.basic),se.value=!0,console.log("✅ 营销配置加载成功:",we)}catch(l){console.error("获取营销配置失败:",l),C.error("获取营销配置失败")}})(e.row)},{default:Z(()=>U[29]||(U[29]=[I(" 配置 ",-1)])),_:2,__:[29]},1032,["onClick"]),P(q,{size:"small",onClick:l=>(async e=>{try{await new Promise(e=>setTimeout(e,500));const l={group_info:{title:e.title,description:`${e.title}是一个专业的交流平台`,price:e.price,member_count:e.virtual_members,order_count:e.virtual_orders},marketing_config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入群聊"},preview_url:`https://preview.example.com/group/${e.id}`,qr_code:`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://preview.example.com/group/${e.id}`,landing_page:{title:e.title,content:`欢迎加入${e.title}！这里有最新的资讯和优质的服务。`,features:["专业交流","行业资讯","商业机会","技能提升"],contact_info:"wechat123456"}};Ve.value=l,ne.value=!0,console.log("✅ 预览数据加载成功:",Ve.value)}catch(l){console.error("获取预览数据失败:",l),C.error("获取预览数据失败")}})(e.row)},{default:Z(()=>U[30]||(U[30]=[I(" 预览 ",-1)])),_:2,__:[30]},1032,["onClick"]),P(q,{size:"small",onClick:l=>(async e=>{try{await new Promise(e=>setTimeout(e,300));const l={original_title:e.title,replaced_title:pe.value?`[${pe.value}]${e.title}`:e.title,strategy_name:"城市前缀策略",test_city:pe.value||"北京",replacement_rules:["在标题前添加城市前缀","保持原有标题内容","使用方括号格式"]};C.success(`测试结果：${l.replaced_title}`),console.log("✅ 城市定位测试完成:",l)}catch(l){console.error("城市定位测试失败:",l),C.error("城市定位测试失败")}})(e.row)},{default:Z(()=>U[31]||(U[31]=[I(" 测试 ",-1)])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ee,ue.value]]),$("div",K,[P(Qe,{"current-page":ye.page,"onUpdate:currentPage":U[4]||(U[4]=e=>ye.page=e),"page-size":ye.size,"onUpdate:pageSize":U[5]||(U[5]=e=>ye.size=e),"page-sizes":[10,20,50,100],total:ye.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ke,onCurrentChange:ke},null,8,["current-page","page-size","total"])])]),_:1})]),P(De,{modelValue:se.value,"onUpdate:modelValue":U[17]||(U[17]=e=>se.value=e),title:"营销配置",width:"800px","close-on-click-modal":!1},{footer:Z(()=>[$("span",N,[P(q,{onClick:U[16]||(U[16]=e=>se.value=!1)},{default:Z(()=>U[32]||(U[32]=[I("取消",-1)])),_:1,__:[32]}),P(q,{type:"primary",onClick:Ue,loading:de.value},{default:Z(()=>U[33]||(U[33]=[I(" 保存配置 ",-1)])),_:1,__:[33]},8,["loading"])])]),default:Z(()=>[P(Pe,{model:we,"label-width":"120px"},{default:Z(()=>[P(Be,{modelValue:ce.value,"onUpdate:modelValue":U[15]||(U[15]=e=>ce.value=e)},{default:Z(()=>[P(Ae,{label:"基础配置",name:"basic"},{default:Z(()=>[P(Te,{label:"营销模板"},{default:Z(()=>[P($e,{modelValue:we.template_id,"onUpdate:modelValue":U[6]||(U[6]=e=>we.template_id=e),placeholder:"请选择模板"},{default:Z(()=>[(z(!0),T(A,null,B(fe.value,e=>(z(),Q(ze,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),P(Te,{label:"阅读量显示"},{default:Z(()=>[P(qe,{modelValue:we.read_count_display,"onUpdate:modelValue":U[7]||(U[7]=e=>we.read_count_display=e),placeholder:"如：5万+"},null,8,["modelValue"])]),_:1}),P(Te,{label:"点赞数"},{default:Z(()=>[P(Se,{modelValue:we.like_count,"onUpdate:modelValue":U[8]||(U[8]=e=>we.like_count=e),min:0},null,8,["modelValue"])]),_:1}),P(Te,{label:"想看数"},{default:Z(()=>[P(Se,{modelValue:we.want_see_count,"onUpdate:modelValue":U[9]||(U[9]=e=>we.want_see_count=e),min:0},null,8,["modelValue"])]),_:1}),P(Te,{label:"按钮文字"},{default:Z(()=>[P(qe,{modelValue:we.button_title,"onUpdate:modelValue":U[10]||(U[10]=e=>we.button_title=e),placeholder:"如：立即加入群聊"},null,8,["modelValue"])]),_:1})]),_:1}),P(Ae,{label:"内容配置",name:"content"},{default:Z(()=>[P(Te,{label:"群组介绍标题"},{default:Z(()=>[P(qe,{modelValue:we.group_intro_title,"onUpdate:modelValue":U[11]||(U[11]=e=>we.group_intro_title=e),placeholder:"如：群组简介"},null,8,["modelValue"])]),_:1}),P(Te,{label:"群组介绍内容"},{default:Z(()=>[P(qe,{modelValue:we.group_intro_content,"onUpdate:modelValue":U[12]||(U[12]=e=>we.group_intro_content=e),type:"textarea",rows:4,placeholder:"详细介绍群组的价值和特色"},null,8,["modelValue"])]),_:1}),P(Te,{label:"虚拟成员数"},{default:Z(()=>[P(Se,{modelValue:we.virtual_members,"onUpdate:modelValue":U[13]||(U[13]=e=>we.virtual_members=e),min:0},null,8,["modelValue"])]),_:1}),P(Te,{label:"虚拟订单数"},{default:Z(()=>[P(Se,{modelValue:we.virtual_orders,"onUpdate:modelValue":U[14]||(U[14]=e=>we.virtual_orders=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["model"])]),_:1},8,["modelValue"]),P(De,{modelValue:ne.value,"onUpdate:modelValue":U[18]||(U[18]=e=>ne.value=e),title:"群组预览",width:"600px"},{default:Z(()=>[Ve.value?(z(),T("div",W,[$("div",X,[$("h3",null,v(Ve.value.group_info?.title),1),$("div",ee,[$("span",null,"阅读 "+v(Ve.value.marketing_config?.read_count_display||"0"),1),$("span",null,"点赞 "+v(Ve.value.marketing_config?.like_count||0),1),$("span",null,"想看 "+v(Ve.value.marketing_config?.want_see_count||0),1)]),$("div",le,"¥"+v(Ve.value.group_info?.price),1)]),Ve.value.landing_page?.content?(z(),T("div",ae,[U[34]||(U[34]=$("h4",null,"群组介绍",-1)),$("p",null,v(Ve.value.landing_page.content),1)])):S("",!0),Ve.value.group_info?.member_count?(z(),T("div",te,[$("h4",null,"群成员 ("+v(Ve.value.group_info.member_count)+"人)",1)])):S("",!0),$("div",oe,[$("img",{src:Ve.value.qr_code,alt:"群组二维码",style:{width:"200px",height:"200px"}},null,8,ie)])])):S("",!0)]),_:1},8,["modelValue"]),P(De,{modelValue:_e.value,"onUpdate:modelValue":U[22]||(U[22]=e=>_e.value=e),title:"批量营销配置",width:"500px"},{footer:Z(()=>[$("span",re,[P(q,{onClick:U[21]||(U[21]=e=>_e.value=!1)},{default:Z(()=>U[36]||(U[36]=[I("取消",-1)])),_:1,__:[36]}),P(q,{type:"primary",onClick:je},{default:Z(()=>U[37]||(U[37]=[I(" 应用配置 ",-1)])),_:1,__:[37]})])]),default:Z(()=>[P(Pe,{model:he,"label-width":"100px"},{default:Z(()=>[P(Te,{label:"应用范围"},{default:Z(()=>[P(Ye,{modelValue:he.scope,"onUpdate:modelValue":U[19]||(U[19]=e=>he.scope=e)},{default:Z(()=>[P(Me,{label:"selected"},{default:Z(()=>[I("选中的群组 ("+v(ve.value.length)+"个)",1)]),_:1}),P(Me,{label:"all"},{default:Z(()=>U[35]||(U[35]=[I("所有群组",-1)])),_:1,__:[35]})]),_:1},8,["modelValue"])]),_:1}),P(Te,{label:"营销模板"},{default:Z(()=>[P($e,{modelValue:he.template_id,"onUpdate:modelValue":U[20]||(U[20]=e=>he.template_id=e),placeholder:"请选择模板"},{default:Z(()=>[(z(!0),T(A,null,B(fe.value,e=>(z(),Q(ze,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-8a84fdce"]]);export{ue as default};
