<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

/**
 * 测试控制器
 * 用于测试基本的API功能
 */
class TestController extends Controller
{
    /**
     * 超简单的测试接口
     */
    public function testSimple()
    {
        return Response::json([
            'success' => true,
            'message' => '超简单API测试成功',
            'data' => [
                'timestamp' => now()->toDateTimeString(),
            ],
        ]);
    }

    /**
     * 测试基本认证
     */
    public function testAuth(Request $request)
    {
        try {
            // 获取当前用户
            $user = auth()->user();
            
            if (!$user) {
                return Response::json([
                    'success' => false,
                    'message' => '用户未认证',
                    'data' => null,
                ], 401);
            }
            
            // 返回基本用户信息
            return Response::json([
                'success' => true,
                'message' => '认证测试成功',
                'data' => [
                    'user_info' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'role' => $user->role,
                    ],
                    'auth_info' => [
                        'guard' => auth()->getDefaultDriver(),
                        'user_class' => get_class($user),
                        'authenticated' => auth()->check(),
                    ],
                    'timestamp' => now()->toDateTimeString(),
                ],
            ]);
            
        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'message' => '认证测试失败',
                'data' => [
                    'error' => $e->getMessage(),
                    'file' => basename($e->getFile()),
                    'line' => $e->getLine(),
                ],
            ], 500);
        }
    }
    
    /**
     * 测试基本响应
     */
    public function testResponse(Request $request)
    {
        try {
            return Response::json([
                'success' => true,
                'message' => '基本响应测试成功',
                'data' => [
                    'timestamp' => now()->toDateTimeString(),
                    'request_method' => $request->method(),
                    'request_url' => $request->url(),
                ],
            ]);
            
        } catch (\Exception $e) {
            return Response::json([
                'success' => false,
                'message' => '基本响应测试失败',
                'data' => [
                    'error' => $e->getMessage(),
                    'file' => basename($e->getFile()),
                    'line' => $e->getLine(),
                ],
            ], 500);
        }
    }
} 