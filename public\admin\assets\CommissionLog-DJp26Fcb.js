import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css               *//* empty css                */import{r as a,L as l,e as t,k as s,l as o,E as i,z as r,t as n,u as d,D as u,A as c,y as m,B as _,G as p,F as f,Y as v}from"./vue-vendor-Dy164gUc.js";import{c as g,d as b,f as h,s as y,h as w,i as x,j as k}from"./finance-DBah1Ldq.js";import{a as V}from"./export-BIRLwzxN.js";import{a as j,f as C,b as U}from"./format-3eU4VJ9V.js";import{Q as z,a_ as $,aY as L,U as R,aZ as B,bp as D,bq as q,aM as S,b9 as T,b8 as I,by as O,at as P,bh as F,bi as Z,a$ as A,bw as E,bx as M,br as Y,ay as G,R as J}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const K={class:"app-container"},Q={class:"stats-content"},H={class:"stats-data"},N={class:"stats-number"},W={class:"stats-content"},X={class:"stats-data"},ee={class:"stats-number"},ae={class:"stats-content"},le={class:"stats-data"},te={class:"stats-number"},se={class:"stats-content"},oe={class:"stats-data"},ie={class:"stats-number"},re={class:"order-info"},ne={class:"order-no"},de={class:"order-amount"},ue={class:"user-info"},ce={class:"user-avatar"},me=["src"],_e={class:"user-details"},pe={class:"user-name"},fe={class:"user-level"},ve={class:"commission-info"},ge={class:"commission-rate"},be={class:"commission-amount"},he={class:"time-info"},ye={class:"time-detail"},we={style:{"margin-top":"20px","text-align":"center"}},xe={class:"dialog-footer"},ke=e({__name:"CommissionLog",setup(e){const ke=a([]),Ve=a(0),je=a(!0),Ce=a(!1),Ue=a(!1),ze=a([]),$e=l({page:1,limit:15,order_no:"",user_name:"",status:"",date_range:[]}),Le=a({}),Re=a(!1),Be=a(null),De=l({order_id:"",user_id:"",commission_rate:0,commission_amount:0,remark:""}),qe={order_id:[{required:!0,message:"请选择订单",trigger:"change"}],user_id:[{required:!0,message:"请选择用户",trigger:"change"}],commission_rate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],commission_amount:[{required:!0,message:"请输入佣金金额",trigger:"blur"}]},Se=a([]),Te=a([]);t(()=>{Ie(),Oe()});const Ie=async()=>{je.value=!0;try{const e={...$e};e.date_range&&2===e.date_range.length&&(e.start_date=e.date_range[0],e.end_date=e.date_range[1]),delete e.date_range;const a=await g(e);ke.value=a.data.data,Ve.value=a.data.total}catch(e){z.error("获取佣金列表失败"),console.error(e)}finally{je.value=!1}},Oe=async()=>{try{const e=await b();Le.value=e.data.basic}catch(e){console.error("获取统计数据失败",e)}},Pe=()=>{$e.page=1,Ie()},Fe=()=>{Object.assign($e,{page:1,limit:15,order_no:"",user_name:"",status:"",date_range:[]}),Ie()},Ze=e=>{ze.value=e},Ae=()=>{const e=ze.value.filter(e=>2===e.status);if(0===e.length)return void z.warning("请选择待结算的佣金记录");const a=e.map(e=>e.id);J.confirm(`确定要结算选中的 ${a.length} 笔佣金吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await h({ids:a}),z.success("批量结算成功"),Ie(),Oe()}catch(e){z.error("批量结算失败")}})},Ee=()=>{Ye(),Re.value=!0},Me=()=>{Be.value.validate(async e=>{if(e){Ue.value=!0;try{await k(De),z.success("添加成功"),Re.value=!1,Ie()}catch(a){z.error("添加失败")}finally{Ue.value=!1}}})},Ye=()=>{Be.value&&Be.value.resetFields(),Object.assign(De,{order_id:"",user_id:"",commission_rate:0,commission_amount:0,remark:""})},Ge=async e=>{if(e)try{const a=await w({keyword:e});Se.value=a.data}catch(a){console.error("搜索订单失败",a)}},Je=async e=>{if(e)try{const a=await x({keyword:e});Te.value=a.data}catch(a){console.error("搜索用户失败",a)}},Ke=async()=>{Ce.value=!0;try{const e={...$e,format:"excel",fields:["id","order_no","order_amount","commission_rate","commission_amount","user_name","status","created_at"]},a=await V(e),l=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=l,t.download=`佣金明细_${(new Date).toLocaleDateString()}.xlsx`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),z.success("导出成功")}catch(e){z.error("导出失败")}finally{Ce.value=!1}},Qe=e=>({1:"已结算",2:"待结算",3:"已取消"}[e]||"未知");return(e,a)=>{const l=L,t=$,g=B,b=S,h=q,w=I,x=T,k=O,V=P,Ye=D,He=Z,Ne=A,We=F,Xe=M,ea=Y,aa=G,la=E;return o(),s("div",K,[i(g,{gutter:20,style:{"margin-bottom":"20px"}},{default:r(()=>[i(t,{span:6},{default:r(()=>[i(l,{class:"stats-card"},{default:r(()=>[n("div",Q,[a[14]||(a[14]=n("div",{class:"stats-icon total-icon"},[n("i",{class:"el-icon-medal"})],-1)),n("div",H,[n("div",N,"¥"+R(d(j)(Le.value.total_amount)),1),a[13]||(a[13]=n("div",{class:"stats-label"},"总佣金",-1))])])]),_:1})]),_:1}),i(t,{span:6},{default:r(()=>[i(l,{class:"stats-card"},{default:r(()=>[n("div",W,[a[16]||(a[16]=n("div",{class:"stats-icon paid-icon"},[n("i",{class:"el-icon-check"})],-1)),n("div",X,[n("div",ee,"¥"+R(d(j)(Le.value.paid_amount)),1),a[15]||(a[15]=n("div",{class:"stats-label"},"已发放",-1))])])]),_:1})]),_:1}),i(t,{span:6},{default:r(()=>[i(l,{class:"stats-card"},{default:r(()=>[n("div",ae,[a[18]||(a[18]=n("div",{class:"stats-icon pending-icon"},[n("i",{class:"el-icon-time"})],-1)),n("div",le,[n("div",te,"¥"+R(d(j)(Le.value.pending_amount)),1),a[17]||(a[17]=n("div",{class:"stats-label"},"待发放",-1))])])]),_:1})]),_:1}),i(t,{span:6},{default:r(()=>[i(l,{class:"stats-card"},{default:r(()=>[n("div",se,[a[20]||(a[20]=n("div",{class:"stats-icon count-icon"},[n("i",{class:"el-icon-document"})],-1)),n("div",oe,[n("div",ie,R(Le.value.total_count),1),a[19]||(a[19]=n("div",{class:"stats-label"},"总笔数",-1))])])]),_:1})]),_:1})]),_:1}),i(l,{style:{"margin-bottom":"20px"}},{default:r(()=>[i(Ye,{inline:!0,model:$e,"label-width":"80px"},{default:r(()=>[i(h,{label:"订单号"},{default:r(()=>[i(b,{modelValue:$e.order_no,"onUpdate:modelValue":a[0]||(a[0]=e=>$e.order_no=e),placeholder:"请输入订单号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),i(h,{label:"受益人"},{default:r(()=>[i(b,{modelValue:$e.user_name,"onUpdate:modelValue":a[1]||(a[1]=e=>$e.user_name=e),placeholder:"请输入用户名或昵称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),i(h,{label:"状态"},{default:r(()=>[i(x,{modelValue:$e.status,"onUpdate:modelValue":a[2]||(a[2]=e=>$e.status=e),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:r(()=>[i(w,{label:"全部",value:""}),i(w,{label:"已结算",value:1}),i(w,{label:"待结算",value:2}),i(w,{label:"已取消",value:3})]),_:1},8,["modelValue"])]),_:1}),i(h,{label:"时间范围"},{default:r(()=>[i(k,{modelValue:$e.date_range,"onUpdate:modelValue":a[3]||(a[3]=e=>$e.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"250px"}},null,8,["modelValue"])]),_:1}),i(h,null,{default:r(()=>[i(V,{type:"primary",icon:"Search",onClick:Pe},{default:r(()=>a[21]||(a[21]=[u("搜索",-1)])),_:1,__:[21]}),i(V,{icon:"Refresh",onClick:Fe},{default:r(()=>a[22]||(a[22]=[u("重置",-1)])),_:1,__:[22]})]),_:1})]),_:1},8,["model"])]),_:1}),i(l,{style:{"margin-bottom":"20px"}},{default:r(()=>[i(g,null,{default:r(()=>[i(t,{span:12},{default:r(()=>[i(V,{type:"success",icon:"Check",disabled:0===ze.value.length,onClick:Ae},{default:r(()=>a[23]||(a[23]=[u(" 批量结算 ",-1)])),_:1,__:[23]},8,["disabled"]),i(V,{type:"warning",icon:"Download",onClick:Ke,loading:Ce.value},{default:r(()=>a[24]||(a[24]=[u(" 导出数据 ",-1)])),_:1,__:[24]},8,["loading"])]),_:1}),i(t,{span:12,style:{"text-align":"right"}},{default:r(()=>[i(V,{type:"primary",icon:"Plus",onClick:Ee},{default:r(()=>a[25]||(a[25]=[u(" 手动添加佣金 ",-1)])),_:1,__:[25]})]),_:1})]),_:1})]),_:1}),i(l,null,{default:r(()=>[c((o(),m(We,{data:ke.value,border:"",fit:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:Ze},{default:r(()=>[i(He,{type:"selection",width:"55",align:"center"}),i(He,{label:"ID",prop:"id",align:"center",width:"80"}),i(He,{label:"订单信息",width:"200",align:"center"},{default:r(({row:e})=>[n("div",re,[n("div",ne,R(e.order?.order_no||"-"),1),n("div",de,"¥"+R(d(j)(e.order_amount)),1)])]),_:1}),i(He,{label:"受益人",width:"150",align:"center"},{default:r(({row:e})=>{return[n("div",ue,[n("div",ce,[n("img",{src:e.user?.avatar||"/default-avatar.png",alt:""},null,8,me)]),n("div",_e,[n("div",pe,R(e.user?.name||"-"),1),n("div",fe,R((a=e.user?.level,{1:"初级分销商",2:"中级分销商",3:"高级分销商",4:"金牌分销商"}[a]||"普通用户")),1)])])];var a}),_:1}),i(He,{label:"佣金详情",width:"180",align:"center"},{default:r(({row:e})=>[n("div",ve,[n("div",ge,R(e.commission_rate)+"%",1),n("div",be,"¥"+R(d(j)(e.commission_amount)),1)])]),_:1}),i(He,{label:"状态",width:"100",align:"center"},{default:r(({row:e})=>{return[i(Ne,{type:(a=e.status,{1:"success",2:"warning",3:"info"}[a]||"info")},{default:r(()=>[u(R(Qe(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),i(He,{label:"备注",prop:"remark","min-width":"120","show-overflow-tooltip":""}),i(He,{label:"创建时间",width:"160",align:"center"},{default:r(({row:e})=>[n("div",he,[n("div",null,R(d(C)(e.created_at)),1),n("div",ye,R(d(U)(e.created_at)),1)])]),_:1}),i(He,{label:"操作",width:"150",align:"center",fixed:"right"},{default:r(({row:e})=>[i(V,{type:"text",size:"small",onClick:a=>(e=>{z.info(`查看佣金详情：${e.id}`)})(e)},{default:r(()=>a[26]||(a[26]=[u(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"]),2===e.status?(o(),m(V,{key:0,type:"text",size:"small",onClick:a=>(e=>{J.confirm(`确定要结算这笔佣金吗 (ID: ${e.id})?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await y(e.id),z.success("结算成功"),Ie(),Oe()}catch(a){z.error("结算失败")}})})(e)},{default:r(()=>a[27]||(a[27]=[u(" 结算 ",-1)])),_:2,__:[27]},1032,["onClick"])):_("",!0),i(V,{type:"text",size:"small",onClick:a=>(e=>{z.info(`编辑佣金：${e.id}`)})(e)},{default:r(()=>a[28]||(a[28]=[u(" 编辑 ",-1)])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[la,je.value]]),n("div",we,[c(i(Xe,{"current-page":$e.page,"onUpdate:currentPage":a[4]||(a[4]=e=>$e.page=e),"page-size":$e.limit,"onUpdate:pageSize":a[5]||(a[5]=e=>$e.limit=e),"page-sizes":[15,30,50,100],total:Ve.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ie,onCurrentChange:Ie},null,8,["current-page","page-size","total"]),[[p,Ve.value>0]])])]),_:1}),i(aa,{title:"添加佣金记录",modelValue:Re.value,"onUpdate:modelValue":a[12]||(a[12]=e=>Re.value=e),width:"600px"},{footer:r(()=>[n("div",xe,[i(V,{onClick:a[11]||(a[11]=e=>Re.value=!1)},{default:r(()=>a[30]||(a[30]=[u("取消",-1)])),_:1,__:[30]}),i(V,{type:"primary",onClick:Me,loading:Ue.value},{default:r(()=>a[31]||(a[31]=[u("确定",-1)])),_:1,__:[31]},8,["loading"])])]),default:r(()=>[i(Ye,{model:De,rules:qe,ref_key:"addFormRef",ref:Be,"label-width":"120px"},{default:r(()=>[i(h,{label:"订单号",prop:"order_id"},{default:r(()=>[i(x,{modelValue:De.order_id,"onUpdate:modelValue":a[6]||(a[6]=e=>De.order_id=e),placeholder:"请选择订单",filterable:"",remote:"","remote-method":Ge,style:{width:"100%"}},{default:r(()=>[(o(!0),s(f,null,v(Se.value,e=>(o(),m(w,{key:e.id,label:e.order_no,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(h,{label:"受益用户",prop:"user_id"},{default:r(()=>[i(x,{modelValue:De.user_id,"onUpdate:modelValue":a[7]||(a[7]=e=>De.user_id=e),placeholder:"请选择用户",filterable:"",remote:"","remote-method":Je,style:{width:"100%"}},{default:r(()=>[(o(!0),s(f,null,v(Te.value,e=>(o(),m(w,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(h,{label:"佣金比例",prop:"commission_rate"},{default:r(()=>[i(ea,{modelValue:De.commission_rate,"onUpdate:modelValue":a[8]||(a[8]=e=>De.commission_rate=e),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),a[29]||(a[29]=n("span",{style:{"margin-left":"8px",color:"#909399"}},"%",-1))]),_:1,__:[29]}),i(h,{label:"佣金金额",prop:"commission_amount"},{default:r(()=>[i(ea,{modelValue:De.commission_amount,"onUpdate:modelValue":a[9]||(a[9]=e=>De.commission_amount=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(h,{label:"备注"},{default:r(()=>[i(b,{modelValue:De.remark,"onUpdate:modelValue":a[10]||(a[10]=e=>De.remark=e),type:"textarea",placeholder:"请输入备注",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-41b740b6"]]);export{ke as default};
