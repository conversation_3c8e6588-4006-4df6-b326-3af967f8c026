/**
 * Login Component Unit Tests
 * 测试登录组件的所有功能和交互
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'
import Login from '@/views/Login.vue'
import { useUserStore } from '@/stores/user'

// 模拟路由器
const mockRouter = {
  push: vi.fn(),
  currentRoute: {
    value: {
      query: {}
    }
  }
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter
}))

// 模拟安全工具
vi.mock('@/utils/security', () => ({
  securityLogger: {
    log: vi.fn()
  },
  sessionManager: {
    createSession: vi.fn(() => ({ id: 'session-123' }))
  },
  logLogin: vi.fn(),
  logSession: vi.fn()
}))

// 模拟导航配置
vi.mock('@/config/navigation', () => ({
  getUserDefaultRoute: vi.fn().mockReturnValue('/dashboard')
}))

describe('Login Component', () => {
  let wrapper
  let pinia
  let userStore

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    userStore = useUserStore()
    
    // 清除之前的模拟调用
    vi.clearAllMocks()
    ElMessage.success.mockClear()
    ElMessage.error.mockClear()
    ElMessage.info.mockClear()
    mockRouter.push.mockClear()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (props = {}) => {
    return mount(Login, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter
        }
      },
      props
    })
  }

  describe('Component Rendering', () => {
    it('should render login form correctly', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.login-page').exists()).toBe(true)
      expect(wrapper.find('.login-card').exists()).toBe(true)
      expect(wrapper.find('.login-form').exists()).toBe(true)
      
      // 检查表单字段
      expect(wrapper.find('#username').exists()).toBe(true)
      expect(wrapper.find('#password').exists()).toBe(true)
      expect(wrapper.find('.modern-login-button').exists()).toBe(true)
      expect(wrapper.find('.preview-button').exists()).toBe(true)
    })

    it('should display company branding correctly', () => {
      wrapper = createWrapper()
      
      const logoText = wrapper.find('.logo-text h1')
      expect(logoText.exists()).toBe(true)
      expect(logoText.text()).toBe('晨鑫流量变现系统')
      
      const subtitle = wrapper.find('.logo-text p')
      expect(subtitle.exists()).toBe(true)
      expect(subtitle.text()).toBe('智能社群营销与多级分销平台')
    })

    it('should show system status indicator', () => {
      wrapper = createWrapper()
      
      const statusIndicator = wrapper.find('.status-indicator')
      expect(statusIndicator.exists()).toBe(true)
      expect(statusIndicator.text()).toContain('系统运行正常')
      
      const statusDot = wrapper.find('.status-dot')
      expect(statusDot.exists()).toBe(true)
    })
  })

  describe('Form Interaction', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should handle username input', async () => {
      const usernameInput = wrapper.find('#username')
      
      await usernameInput.setValue('testuser')
      expect(wrapper.vm.loginForm.username).toBe('testuser')
    })

    it('should handle password input', async () => {
      const passwordInput = wrapper.find('#password')
      
      await passwordInput.setValue('password123')
      expect(wrapper.vm.loginForm.password).toBe('password123')
    })

    it('should toggle password visibility', async () => {
      const passwordInput = wrapper.find('#password')
      const toggleButton = wrapper.find('.password-toggle')
      
      // 初始状态应该是隐藏密码
      expect(passwordInput.attributes('type')).toBe('password')
      expect(wrapper.vm.showPassword).toBe(false)
      
      // 点击切换按钮
      await toggleButton.trigger('click')
      expect(wrapper.vm.showPassword).toBe(true)
      expect(passwordInput.attributes('type')).toBe('text')
      
      // 再次点击切换回去
      await toggleButton.trigger('click')
      expect(wrapper.vm.showPassword).toBe(false)
      expect(passwordInput.attributes('type')).toBe('password')
    })

    it('should handle remember me checkbox', async () => {
      const checkbox = wrapper.find('#remember')
      
      expect(wrapper.vm.rememberMe).toBe(false)
      
      await checkbox.setChecked()
      expect(wrapper.vm.rememberMe).toBe(true)
    })

    it('should clear field errors on input', async () => {
      const usernameInput = wrapper.find('#username')
      
      // 设置错误状态
      wrapper.vm.errors.username = 'Username is required'
      wrapper.vm.globalError = 'Global error'
      
      await usernameInput.trigger('input')
      
      expect(wrapper.vm.errors.username).toBe('')
      expect(wrapper.vm.globalError).toBe('')
    })
  })

  describe('Form Validation', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should validate required fields', async () => {
      const form = wrapper.find('.login-form')
      const loginButton = wrapper.find('.modern-login-button')
      
      // 尝试提交空表单
      await form.trigger('submit.prevent')
      
      // 应该显示验证错误
      expect(wrapper.vm.errors.username || wrapper.vm.errors.password).toBeTruthy()
    })

    it('should validate email format when @ is present', async () => {
      const usernameInput = wrapper.find('#username')
      
      // 输入无效邮箱格式
      await usernameInput.setValue('invalid-email@')
      await usernameInput.trigger('blur')
      
      // 应该有验证错误
      // 注意：这里需要根据实际的验证实现来调整
    })

    it('should validate password complexity', async () => {
      const passwordInput = wrapper.find('#password')
      
      // 输入过于简单的密码
      await passwordInput.setValue('123')
      await passwordInput.trigger('blur')
      
      // 应该有验证错误
      // 注意：这里需要根据实际的验证实现来调整
    })
  })

  describe('Login Functionality', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should handle successful login', async () => {
      // 模拟成功登录
      const mockLoginResponse = {
        data: {
          success: true,
          data: {
            token: 'test-token',
            user: {
              id: 1,
              username: 'testuser',
              nickname: '测试用户',
              role: 'admin'
            }
          }
        }
      }
      
      userStore.login = vi.fn().mockResolvedValue(mockLoginResponse)
      
      // 填写登录表单
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      // 提交表单
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      // 验证登录调用
      expect(userStore.login).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123'
      })
      
      // 验证成功消息
      expect(ElMessage.success).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('欢迎回来，超级管理员！')
        })
      )
      
      // 验证路由跳转
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard')
    })

    it('should handle login failure', async () => {
      // 模拟登录失败
      const loginError = new Error('用户名或密码错误')
      userStore.login = vi.fn().mockRejectedValue(loginError)
      
      // 填写登录表单
      await wrapper.find('#username').setValue('wronguser')
      await wrapper.find('#password').setValue('wrongpass')
      
      // 提交表单
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      // 验证错误消息
      expect(ElMessage.error).toHaveBeenCalledWith(
        expect.objectContaining({
          message: '用户名或密码错误'
        })
      )
      
      // 验证没有路由跳转
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should handle network errors', async () => {
      // 模拟网络错误
      const networkError = new Error('Network Error')
      networkError.code = 'Network Error'
      userStore.login = vi.fn().mockRejectedValue(networkError)
      
      // 填写并提交表单
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      // 验证网络错误消息
      expect(ElMessage.error).toHaveBeenCalledWith(
        expect.objectContaining({
          message: '网络连接失败，请检查网络设置'
        })
      )
    })

    it('should disable form during login process', async () => {
      // 模拟延迟的登录请求
      let resolveLogin
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      userStore.login = vi.fn().mockReturnValue(loginPromise)
      
      // 提交表单
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      const loginButton = wrapper.find('.modern-login-button')
      
      await form.trigger('submit.prevent')
      
      // 验证加载状态
      expect(wrapper.vm.loading).toBe(true)
      expect(loginButton.attributes('disabled')).toBeDefined()
      expect(loginButton.text()).toContain('登录中...')
      
      // 解析登录请求
      resolveLogin({
        data: {
          success: true,
          data: { token: 'test', user: { id: 1, role: 'admin' } }
        }
      })
      
      await flushPromises()
      
      // 验证加载状态重置
      expect(wrapper.vm.loading).toBe(false)
      expect(loginButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('Preview Mode', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should enter preview mode successfully', async () => {
      userStore.enterPreviewMode = vi.fn()
      
      const previewButton = wrapper.find('.preview-button')
      
      await previewButton.trigger('click')
      await flushPromises()
      
      expect(userStore.enterPreviewMode).toHaveBeenCalled()
      expect(ElMessage.info).toHaveBeenCalledWith('正在进入预览模式...')
      
      // 等待预览模式设置完成
      await new Promise(resolve => setTimeout(resolve, 600))
      
      expect(ElMessage.success).toHaveBeenCalledWith('欢迎来到预览模式！')
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard')
    })

    it('should handle preview mode failure', async () => {
      userStore.enterPreviewMode = vi.fn().mockImplementation(() => {
        throw new Error('Preview mode failed')
      })
      
      const previewButton = wrapper.find('.preview-button')
      
      await previewButton.trigger('click')
      await flushPromises()
      
      expect(ElMessage.error).toHaveBeenCalledWith('无法进入预览模式，请稍后重试。')
    })
  })

  describe('Smart Redirect', () => {
    it('should redirect to specified route from query params', async () => {
      // 设置查询参数
      mockRouter.currentRoute.value.query = { redirect: '/specific-page' }
      
      wrapper = createWrapper()
      
      // 模拟成功登录
      userStore.login = vi.fn().mockResolvedValue({
        data: {
          success: true,
          data: {
            token: 'test-token',
            user: { id: 1, username: 'test', role: 'admin' }
          }
        }
      })
      
      // 模拟权限检查通过
      vi.doMock('@/utils/permission', () => ({
        checkRoutePermission: vi.fn().mockReturnValue(true)
      }))
      
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      // 验证重定向到指定路由
      expect(mockRouter.push).toHaveBeenCalledWith('/specific-page')
    })

    it('should redirect to default route when no permission for redirect route', async () => {
      mockRouter.currentRoute.value.query = { redirect: '/forbidden-page' }
      
      wrapper = createWrapper()
      
      userStore.login = vi.fn().mockResolvedValue({
        data: {
          success: true,
          data: {
            token: 'test-token',
            user: { id: 1, username: 'test', role: 'user' }
          }
        }
      })
      
      // 模拟权限检查失败
      vi.doMock('@/utils/permission', () => ({
        checkRoutePermission: vi.fn().mockReturnValue(false)
      }))
      
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      expect(ElMessage.warning).toHaveBeenCalledWith(
        expect.objectContaining({
          message: '您没有权限访问请求的页面，已跳转到默认页面'
        })
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard')
    })
  })

  describe('Responsive Design', () => {
    it('should handle screen size changes', async () => {
      wrapper = createWrapper()
      
      // 模拟小屏幕
      Object.defineProperty(window, 'innerHeight', { value: 600, configurable: true })
      Object.defineProperty(window, 'innerWidth', { value: 400, configurable: true })
      
      // 触发resize事件
      window.dispatchEvent(new Event('resize'))
      
      expect(wrapper.vm.isCompactMode).toBe(true)
      
      // 模拟大屏幕
      Object.defineProperty(window, 'innerHeight', { value: 1080, configurable: true })
      Object.defineProperty(window, 'innerWidth', { value: 1920, configurable: true })
      
      window.dispatchEvent(new Event('resize'))
      
      expect(wrapper.vm.isCompactMode).toBe(false)
    })
  })

  describe('Forgot Password', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should handle forgot password click', async () => {
      const forgotPasswordLink = wrapper.find('.forgot-password')
      
      await forgotPasswordLink.trigger('click')
      
      expect(ElMessage.info).toHaveBeenCalledWith('请联系系统管理员重置密码')
    })
  })

  describe('Keyboard Navigation', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should submit form on Enter key in password field', async () => {
      userStore.login = vi.fn().mockResolvedValue({
        data: {
          success: true,
          data: { token: 'test', user: { id: 1, role: 'admin' } }
        }
      })
      
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const passwordInput = wrapper.find('#password')
      await passwordInput.trigger('keyup.enter')
      await flushPromises()
      
      expect(userStore.login).toHaveBeenCalled()
    })

    it('should focus username field on mount when empty', async () => {
      wrapper = createWrapper()
      
      await wrapper.vm.$nextTick()
      
      // 验证用户名输入框获得焦点
      expect(document.activeElement).toBe(wrapper.find('#username').element)
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should display global error message', async () => {
      wrapper.vm.globalError = 'Global error message'
      
      await wrapper.vm.$nextTick()
      
      const errorAlert = wrapper.find('.error-alert')
      expect(errorAlert.exists()).toBe(true)
      expect(errorAlert.text()).toContain('Global error message')
    })

    it('should display field-specific error messages', async () => {
      wrapper.vm.errors.username = 'Username error'
      wrapper.vm.errors.password = 'Password error'
      
      await wrapper.vm.$nextTick()
      
      const fieldErrors = wrapper.findAll('.field-error')
      expect(fieldErrors.length).toBe(2)
      expect(fieldErrors[0].text()).toBe('Username error')
      expect(fieldErrors[1].text()).toBe('Password error')
    })
  })

  describe('Security Features', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should log login attempts', async () => {
      const { logLogin } = await import('@/utils/security')
      
      userStore.login = vi.fn().mockRejectedValue(new Error('Login failed'))
      
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      expect(logLogin).toHaveBeenCalledWith('testuser', false, expect.any(String))
    })

    it('should create session on successful login', async () => {
      const { sessionManager, logSession } = await import('@/utils/security')
      
      userStore.login = vi.fn().mockResolvedValue({
        data: {
          success: true,
          data: {
            token: 'test-token',
            user: { id: 1, username: 'testuser', role: 'admin' }
          }
        }
      })
      
      await wrapper.find('#username').setValue('testuser')
      await wrapper.find('#password').setValue('password123')
      
      const form = wrapper.find('.login-form')
      await form.trigger('submit.prevent')
      await flushPromises()
      
      expect(sessionManager.createSession).toHaveBeenCalled()
      expect(logSession).toHaveBeenCalledWith(
        'testuser',
        'login',
        'session-123',
        expect.stringContaining('角色: admin')
      )
    })
  })
})