<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\IPLocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class LocationController extends Controller
{
    public function __construct(
        private readonly IPLocationService $ipLocationService
    ) {}

    /**
     * 通过IP获取城市
     */
    public function getLocationByIP(Request $request)
    {
        $ip = $request->input('ip') ?: $this->ipLocationService->getClientIP();
        $city = $this->ipLocationService->getCity($ip);

        return response()->json([
            'success' => true,
            'data' => [
                'ip' => $ip,
                'city' => $city,
                'formatted_city' => $this->formatCityName($city),
            ]
        ]);
    }

    /**
     * 坐标反向地理编码
     */
    public function reverseGeocode(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $lat = $request->input('latitude');
        $lng = $request->input('longitude');

        $city = $this->reverseGeocodeCoordinates($lat, $lng);

        return response()->json([
            'success' => true,
            'data' => [
                'latitude' => $lat,
                'longitude' => $lng,
                'city' => $city,
                'formatted_city' => $this->formatCityName($city),
            ]
        ]);
    }

    /**
     * 获取城市列表
     */
    public function getCities()
    {
        $cities = Cache::remember('all_cities', 86400, function () {
            return [
                // 直辖市
                '北京', '上海', '天津', '重庆',
                
                // 省会城市
                '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安', '沈阳',
                '长春', '哈尔滨', '石家庄', '太原', '呼和浩特', '济南', '郑州',
                '合肥', '南昌', '长沙', '福州', '南宁', '海口', '贵阳', '昆明',
                '拉萨', '兰州', '西宁', '银川', '乌鲁木齐',
                
                // 重要地级市
                '苏州', '无锡', '常州', '南通', '徐州', '盐城', '淮安', '连云港',
                '泰州', '宿迁', '镇江', '扬州', '宁波', '温州', '嘉兴', '湖州',
                '绍兴', '金华', '衢州', '舟山', '台州', '丽水', '青岛', '淄博',
                '枣庄', '东营', '烟台', '潍坊', '济宁', '泰安', '威海', '日照',
                '莱芜', '临沂', '德州', '聊城', '滨州', '菏泽', '洛阳', '平顶山',
                '安阳', '鹤壁', '新乡', '焦作', '濮阳', '许昌', '漯河', '三门峡',
                '南阳', '商丘', '信阳', '周口', '驻马店', '芜湖', '蚌埠', '淮南',
                '马鞍山', '淮北', '铜陵', '安庆', '黄山', '滁州', '阜阳', '宿州',
                '六安', '亳州', '池州', '宣城', '厦门', '莆田', '三明', '泉州',
                '漳州', '南平', '龙岩', '宁德', '景德镇', '萍乡', '九江', '新余',
                '鹰潭', '赣州', '吉安', '宜春', '抚州', '上饶', '株洲', '湘潭',
                '衡阳', '邵阳', '岳阳', '常德', '张家界', '益阳', '郴州', '永州',
                '怀化', '娄底', '湘西', '韶关', '珠海', '汕头', '佛山', '江门',
                '湛江', '茂名', '肇庆', '惠州', '梅州', '汕尾', '河源', '阳江',
                '清远', '东莞', '中山', '潮州', '揭阳', '云浮',
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $cities
        ]);
    }

    /**
     * 智能城市推荐
     */
    public function recommendCities(Request $request)
    {
        $userIP = $this->ipLocationService->getClientIP();
        $currentCity = $this->ipLocationService->getCity($userIP);
        
        // 基于当前城市推荐相关城市
        $recommendations = $this->getRelatedCities($currentCity);

        return response()->json([
            'success' => true,
            'data' => [
                'current_city' => $currentCity,
                'recommendations' => $recommendations,
                'hot_cities' => $this->getHotCities(),
            ]
        ]);
    }

    /**
     * 批量城市定位
     */
    public function batchLocation(Request $request)
    {
        $request->validate([
            'ips' => 'required|array|max:100',
            'ips.*' => 'ip',
        ]);

        $results = [];
        foreach ($request->input('ips') as $ip) {
            $city = $this->ipLocationService->getCity($ip);
            $results[] = [
                'ip' => $ip,
                'city' => $city,
                'formatted_city' => $this->formatCityName($city),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $results
        ]);
    }

    /**
     * 坐标反向地理编码实现
     */
    private function reverseGeocodeCoordinates(float $lat, float $lng): string
    {
        try {
            // 使用高德地图API进行反向地理编码
            $apiKey = config('services.amap.key');
            if (!$apiKey) {
                return '本地';
            }
            
            $url = "https://restapi.amap.com/v3/geocode/regeo?key={$apiKey}&location={$lng},{$lat}&radius=1000&extensions=base";
            
            $response = file_get_contents($url);
            $data = json_decode($response, true);
            
            if ($data['status'] == '1' && isset($data['regeocode']['addressComponent']['city'])) {
                $city = $data['regeocode']['addressComponent']['city'];
                return str_replace('市', '', $city);
            }
            
        } catch (\Exception $e) {
            \Log::warning('坐标反向地理编码失败', [
                'lat' => $lat,
                'lng' => $lng,
                'error' => $e->getMessage()
            ]);
        }

        return '本地';
    }

    /**
     * 格式化城市名称
     */
    private function formatCityName(string $city): string
    {
        // 去掉"市"字
        $city = str_replace(['市', '区', '县'], '', $city);
        
        // 特殊处理
        $replacements = [
            '内蒙古' => '呼和浩特',
            '新疆' => '乌鲁木齐',
            '西藏' => '拉萨',
            '宁夏' => '银川',
            '广西' => '南宁',
        ];
        
        foreach ($replacements as $search => $replace) {
            if (strpos($city, $search) !== false) {
                return $replace;
            }
        }
        
        return $city ?: '本地';
    }

    /**
     * 获取相关城市
     */
    private function getRelatedCities(string $currentCity): array
    {
        // 城市关联关系映射
        $cityRelations = [
            '北京' => ['天津', '石家庄', '保定', '廊坊'],
            '上海' => ['苏州', '无锡', '南京', '杭州'],
            '广州' => ['深圳', '佛山', '东莞', '中山'],
            '深圳' => ['广州', '东莞', '惠州', '珠海'],
            '杭州' => ['上海', '苏州', '宁波', '温州'],
            '南京' => ['上海', '苏州', '无锡', '常州'],
        ];

        return $cityRelations[$currentCity] ?? [];
    }

    /**
     * 获取热门城市
     */
    private function getHotCities(): array
    {
        return [
            '北京', '上海', '广州', '深圳', '杭州', '南京', 
            '武汉', '成都', '重庆', '天津', '西安', '苏州'
        ];
    }
}