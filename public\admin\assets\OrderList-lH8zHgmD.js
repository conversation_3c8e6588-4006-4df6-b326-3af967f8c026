import{_ as e,m as a}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                       */import{c as l,y as t,l as r,z as d,t as o,E as n,k as s,B as u,D as i,r as c,L as p,d as m,e as _,a3 as f,u as y,A as v}from"./vue-vendor-Dy164gUc.js";import{bk as b,bl as g,U as h,a$ as w,aU as V,bh as k,bi as D,at as A,ay as x,bp as C,bq as j,aM as U,br as B,b9 as q,b8 as z,bm as N,bB as T,b1 as S,Q as I,bI as O,bJ as Q,R,by as $,a_ as E,a7 as F,ai as J,bv as P,a6 as L,aZ as Y,T as G,ab as K,aS as M,az as Z,aT as H,aB as W,aC as X,bw as ee,bx as ae,aY as le}from"./element-plus-h2SQQM64.js";import{S as te}from"./StatCard-u_ssO_Ky.js";/* empty css                             *//* empty css                 *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                         */import{f as re}from"./format-3eU4VJ9V.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const de={class:"order-detail"},oe={class:"amount"},ne={key:0,class:"order-notes"},se={class:"dialog-footer"},ue=e({__name:"OrderDetailDialog",props:{modelValue:{type:Boolean,default:!1},order:{type:Object,default:()=>({})}},emits:["update:modelValue","close"],setup(e,{emit:a}){const c=e,p=a,m=l({get:()=>c.modelValue,set:e=>p("update:modelValue",e)}),_=e=>({pending:"待支付",paid:"已支付",shipped:"已发货",delivered:"已送达",cancelled:"已取消",refunded:"已退款"}[e]||"未知状态"),f=()=>{p("close"),p("update:modelValue",!1)},y=()=>{window.print()};return(a,l)=>{const c=g,p=w,v=b,C=V,j=D,U=k,B=A,q=x;return r(),t(q,{modelValue:m.value,"onUpdate:modelValue":l[0]||(l[0]=e=>m.value=e),title:"订单详情",width:"800px","before-close":f},{footer:d(()=>[o("span",se,[n(B,{onClick:f},{default:d(()=>l[3]||(l[3]=[i("关闭",-1)])),_:1,__:[3]}),n(B,{type:"primary",onClick:y},{default:d(()=>l[4]||(l[4]=[i("打印订单",-1)])),_:1,__:[4]})])]),default:d(()=>[o("div",de,[n(v,{title:"订单信息",column:2,border:""},{default:d(()=>[n(c,{label:"订单号"},{default:d(()=>[i(h(e.order.orderNo||"N/A"),1)]),_:1}),n(c,{label:"订单状态"},{default:d(()=>{return[n(p,{type:(a=e.order.status,{pending:"warning",paid:"success",shipped:"info",delivered:"success",cancelled:"danger",refunded:"info"}[a]||"info")},{default:d(()=>[i(h(_(e.order.status)),1)]),_:1},8,["type"])];var a}),_:1}),n(c,{label:"用户ID"},{default:d(()=>[i(h(e.order.userId||"N/A"),1)]),_:1}),n(c,{label:"用户名"},{default:d(()=>[i(h(e.order.userName||"N/A"),1)]),_:1}),n(c,{label:"订单金额"},{default:d(()=>[o("span",oe,"¥"+h(e.order.amount||"0.00"),1)]),_:1}),n(c,{label:"支付方式"},{default:d(()=>[i(h(e.order.paymentMethod||"N/A"),1)]),_:1}),n(c,{label:"创建时间"},{default:d(()=>[i(h(e.order.createTime||"N/A"),1)]),_:1}),n(c,{label:"更新时间"},{default:d(()=>[i(h(e.order.updateTime||"N/A"),1)]),_:1})]),_:1}),n(C,{"content-position":"left"},{default:d(()=>l[1]||(l[1]=[i("商品信息",-1)])),_:1,__:[1]}),n(U,{data:e.order.items||[],style:{width:"100%"}},{default:d(()=>[n(j,{prop:"productName",label:"商品名称"}),n(j,{prop:"quantity",label:"数量",width:"80"}),n(j,{prop:"price",label:"单价",width:"100"},{default:d(e=>[i(" ¥"+h(e.row.price||"0.00"),1)]),_:1}),n(j,{prop:"total",label:"小计",width:"100"},{default:d(e=>[i(" ¥"+h(e.row.total||"0.00"),1)]),_:1})]),_:1},8,["data"]),e.order.notes?(r(),s("div",ne,[n(C,{"content-position":"left"},{default:d(()=>l[2]||(l[2]=[i("订单备注",-1)])),_:1,__:[2]}),o("p",null,h(e.order.notes),1)])):u("",!0)])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-cddb1365"]]),ie={class:"refund-form"},ce={class:"refund-info"},pe={class:"dialog-footer"},me=e({__name:"RefundDialog",props:{modelValue:{type:Boolean,default:!1},orderData:{type:Object,default:()=>({})}},emits:["update:modelValue","success","close"],setup(e,{emit:a}){const s=e,u=a,_=l({get:()=>s.modelValue,set:e=>u("update:modelValue",e)}),f=c(),y=c(!1),v=p({refund_amount:0,refund_reason:"",refund_note:"",refund_method:"original"}),b={refund_amount:[{required:!0,message:"请输入退款金额",trigger:"blur"},{type:"number",min:.01,message:"退款金额必须大于0.01",trigger:"blur"}],refund_reason:[{required:!0,message:"请选择退款原因",trigger:"change"}]};m(()=>s.orderData,e=>{e&&e.amount&&(v.refund_amount=e.amount)},{immediate:!0});const g=()=>{u("close"),u("update:modelValue",!1),h()},h=()=>{f.value&&f.value.resetFields(),Object.assign(v,{refund_amount:0,refund_reason:"",refund_note:"",refund_method:"original"})},w=async()=>{if(f.value)try{await f.value.validate(),y.value=!0,await new Promise(e=>setTimeout(e,2e3)),I.success("退款申请提交成功"),u("success"),g()}catch(e){console.error("退款失败:",e),I.error("退款申请提交失败")}finally{y.value=!1}};return(a,l)=>{const s=U,u=j,c=B,p=z,m=q,h=T,V=N,k=C,D=S,I=A,O=x;return r(),t(O,{modelValue:_.value,"onUpdate:modelValue":l[6]||(l[6]=e=>_.value=e),title:"订单退款",width:"600px","before-close":g},{footer:d(()=>[o("span",pe,[n(I,{onClick:g},{default:d(()=>l[11]||(l[11]=[i("取消",-1)])),_:1,__:[11]}),n(I,{type:"primary",onClick:w,loading:y.value},{default:d(()=>l[12]||(l[12]=[i(" 确认退款 ",-1)])),_:1,__:[12]},8,["loading"])])]),default:d(()=>[o("div",ie,[n(k,{model:v,rules:b,ref_key:"refundFormRef",ref:f,"label-width":"100px"},{default:d(()=>[n(u,{label:"订单号"},{default:d(()=>[n(s,{modelValue:e.orderData.order_no,"onUpdate:modelValue":l[0]||(l[0]=a=>e.orderData.order_no=a),disabled:""},null,8,["modelValue"])]),_:1}),n(u,{label:"订单金额"},{default:d(()=>[n(s,{modelValue:e.orderData.amount,"onUpdate:modelValue":l[1]||(l[1]=a=>e.orderData.amount=a),disabled:""},{prepend:d(()=>l[7]||(l[7]=[i("¥",-1)])),_:1},8,["modelValue"])]),_:1}),n(u,{label:"退款金额",prop:"refund_amount"},{default:d(()=>[n(c,{modelValue:v.refund_amount,"onUpdate:modelValue":l[2]||(l[2]=e=>v.refund_amount=e),min:.01,max:e.orderData.amount,precision:2,style:{width:"100%"}},null,8,["modelValue","max"])]),_:1}),n(u,{label:"退款原因",prop:"refund_reason"},{default:d(()=>[n(m,{modelValue:v.refund_reason,"onUpdate:modelValue":l[3]||(l[3]=e=>v.refund_reason=e),placeholder:"请选择退款原因",style:{width:"100%"}},{default:d(()=>[n(p,{label:"用户申请退款",value:"user_request"}),n(p,{label:"商品缺货",value:"out_of_stock"}),n(p,{label:"系统错误",value:"system_error"}),n(p,{label:"重复支付",value:"duplicate_payment"}),n(p,{label:"其他原因",value:"other"})]),_:1},8,["modelValue"])]),_:1}),n(u,{label:"退款说明",prop:"refund_note"},{default:d(()=>[n(s,{modelValue:v.refund_note,"onUpdate:modelValue":l[4]||(l[4]=e=>v.refund_note=e),type:"textarea",rows:4,placeholder:"请输入退款说明（可选）"},null,8,["modelValue"])]),_:1}),n(u,{label:"退款方式"},{default:d(()=>[n(V,{modelValue:v.refund_method,"onUpdate:modelValue":l[5]||(l[5]=e=>v.refund_method=e)},{default:d(()=>[n(h,{label:"original"},{default:d(()=>l[8]||(l[8]=[i("原路退回",-1)])),_:1,__:[8]}),n(h,{label:"manual"},{default:d(()=>l[9]||(l[9]=[i("人工处理",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",ce,[n(D,{title:"退款说明",type:"info",closable:!1,"show-icon":""},{default:d(()=>l[10]||(l[10]=[o("ul",null,[o("li",null,"退款将在1-3个工作日内到账"),o("li",null,"原路退回：退款将返回到原支付账户"),o("li",null,"人工处理：需要手动处理退款流程"),o("li",null,"退款成功后将自动发送通知给用户")],-1)])),_:1})])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-5ec8bb1a"]]),_e={class:"payment-info"},fe={class:"amount"},ye={key:0,class:"payment-details"},ve={class:"payment-timeline"},be={key:1,class:"pending-payment"},ge={class:"payment-actions"},he={key:0,class:"qr-code-section"},we={class:"qr-code"},Ve=["src"],ke={key:2,class:"cancelled-order"},De={key:3,class:"refunded-order"},Ae={class:"dialog-footer"},xe={__name:"PaymentInfoDialog",props:{modelValue:{type:Boolean,default:!1},orderData:{type:Object,default:()=>({})}},emits:["update:modelValue","close","refresh"],setup(e,{emit:a}){const p=e,m=a,_=l({get:()=>p.modelValue,set:e=>m("update:modelValue",e)}),f=c("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="),y=()=>{m("close"),m("update:modelValue",!1)},v=e=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[e]||"未知状态"),k=e=>({wechat:"微信支付",alipay:"支付宝",qqpay:"QQ钱包",bank:"银行卡"}[e]||"未知支付方式"),D=e=>e?new Date(e).toLocaleString("zh-CN"):"N/A",C=async()=>{try{await R.confirm("确定要发送支付提醒吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),I.success("支付提醒已发送")}catch(e){"cancel"!==e&&I.error("发送提醒失败")}},j=async()=>{try{await R.confirm("确定要取消这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),I.success("订单已取消"),m("refresh"),y()}catch(e){"cancel"!==e&&I.error("取消订单失败")}},U=async()=>{try{I.success("支付状态已刷新"),m("refresh")}catch(e){I.error("刷新失败")}};return(a,l)=>{const c=g,p=w,m=b,B=V,q=Q,z=O,N=A,T=S,I=x;return r(),t(I,{modelValue:_.value,"onUpdate:modelValue":l[0]||(l[0]=e=>_.value=e),title:"支付信息",width:"700px","before-close":y},{footer:d(()=>[o("span",Ae,[n(N,{onClick:y},{default:d(()=>l[19]||(l[19]=[i("关闭",-1)])),_:1,__:[19]}),"pending"===e.orderData.status?(r(),t(N,{key:0,type:"primary",onClick:U},{default:d(()=>l[20]||(l[20]=[i(" 刷新支付状态 ",-1)])),_:1,__:[20]})):u("",!0)])]),default:d(()=>[o("div",_e,[n(m,{title:"订单信息",column:2,border:""},{default:d(()=>[n(c,{label:"订单号"},{default:d(()=>[i(h(e.orderData.order_no||"N/A"),1)]),_:1}),n(c,{label:"订单状态"},{default:d(()=>{return[n(p,{type:(a=e.orderData.status,{pending:"warning",paid:"success",cancelled:"info",refunded:"danger"}[a]||"info")},{default:d(()=>[i(h(v(e.orderData.status)),1)]),_:1},8,["type"])];var a}),_:1}),n(c,{label:"订单金额"},{default:d(()=>[o("span",fe,"¥"+h(e.orderData.amount||"0.00"),1)]),_:1}),n(c,{label:"创建时间"},{default:d(()=>[i(h(D(e.orderData.created_at)),1)]),_:1})]),_:1}),n(B,{"content-position":"left"},{default:d(()=>l[1]||(l[1]=[i("支付详情",-1)])),_:1,__:[1]}),"paid"===e.orderData.status?(r(),s("div",ye,[n(m,{column:2,border:""},{default:d(()=>[n(c,{label:"支付方式"},{default:d(()=>{return[n(p,{type:(a=e.orderData.payment_method,{wechat:"success",alipay:"primary",qqpay:"warning",bank:"info"}[a]||"info")},{default:d(()=>[i(h(k(e.orderData.payment_method)),1)]),_:1},8,["type"])];var a}),_:1}),n(c,{label:"支付时间"},{default:d(()=>[i(h(D(e.orderData.paid_at)),1)]),_:1}),n(c,{label:"交易流水号"},{default:d(()=>[i(h(e.orderData.transaction_id||"N/A"),1)]),_:1}),n(c,{label:"支付状态"},{default:d(()=>[n(p,{type:"success"},{default:d(()=>l[2]||(l[2]=[i("支付成功",-1)])),_:1,__:[2]})]),_:1})]),_:1}),o("div",ve,[l[6]||(l[6]=o("h4",null,"支付流程",-1)),n(z,null,{default:d(()=>[n(q,{timestamp:"订单创建",time:D(e.orderData.created_at),type:"primary"},{default:d(()=>l[3]||(l[3]=[i(" 用户创建订单，等待支付 ",-1)])),_:1,__:[3]},8,["time"]),n(q,{timestamp:"支付完成",time:D(e.orderData.paid_at),type:"success"},{default:d(()=>l[4]||(l[4]=[i(" 用户完成支付，订单状态更新为已支付 ",-1)])),_:1,__:[4]},8,["time"]),n(q,{timestamp:"订单处理",time:D(e.orderData.updated_at),type:"info"},{default:d(()=>l[5]||(l[5]=[i(" 系统处理订单，准备发货 ",-1)])),_:1,__:[5]},8,["time"])]),_:1})])])):"pending"===e.orderData.status?(r(),s("div",be,[n(T,{title:"订单待支付",type:"warning",closable:!1,"show-icon":""},{default:d(()=>[l[9]||(l[9]=o("p",null,"该订单尚未完成支付，请提醒用户及时支付。",-1)),o("div",ge,[n(N,{type:"primary",size:"small",onClick:C},{default:d(()=>l[7]||(l[7]=[i(" 发送支付提醒 ",-1)])),_:1,__:[7]}),n(N,{type:"warning",size:"small",onClick:j},{default:d(()=>l[8]||(l[8]=[i(" 取消订单 ",-1)])),_:1,__:[8]})])]),_:1}),f.value?(r(),s("div",he,[l[11]||(l[11]=o("h4",null,"支付二维码",-1)),o("div",we,[o("img",{src:f.value,alt:"支付二维码"},null,8,Ve),l[10]||(l[10]=o("p",null,"用户可扫描此二维码完成支付",-1))])])):u("",!0)])):"cancelled"===e.orderData.status?(r(),s("div",ke,[n(T,{title:"订单已取消",type:"info",closable:!1,"show-icon":""},{default:d(()=>[l[14]||(l[14]=o("p",null,"该订单已被取消，无法进行支付操作。",-1)),o("p",null,[l[12]||(l[12]=o("strong",null,"取消时间：",-1)),i(h(D(e.orderData.cancelled_at)),1)]),o("p",null,[l[13]||(l[13]=o("strong",null,"取消原因：",-1)),i(h(e.orderData.cancel_reason||"用户主动取消"),1)])]),_:1})])):"refunded"===e.orderData.status?(r(),s("div",De,[n(T,{title:"订单已退款",type:"danger",closable:!1,"show-icon":""},{default:d(()=>[l[18]||(l[18]=o("p",null,"该订单已完成退款处理。",-1)),o("p",null,[l[15]||(l[15]=o("strong",null,"退款时间：",-1)),i(h(D(e.orderData.refunded_at)),1)]),o("p",null,[l[16]||(l[16]=o("strong",null,"退款金额：",-1)),i("¥"+h(e.orderData.refund_amount||e.orderData.amount),1)]),o("p",null,[l[17]||(l[17]=o("strong",null,"退款原因：",-1)),i(h(e.orderData.refund_reason||"用户申请退款"),1)])]),_:1})])):u("",!0)])]),_:1},8,["modelValue"])}}},Ce=e(xe,[["__scopeId","data-v-00c25970"]]),je={class:"app-container"},Ue={class:"filter-container"},Be={class:"card-header"},qe={class:"order-no"},ze={class:"user-info"},Ne={class:"user-details"},Te={class:"username"},Se={class:"user-id"},Ie={key:0,class:"group-info"},Oe={class:"group-name"},Qe={class:"group-price"},Re={key:1,class:"no-group"},$e={class:"order-amount"},Ee={key:1,class:"no-payment"},Fe={class:"pagination-container"},Je=e({__name:"OrderList",setup(e){const l=c([]),m=c(0),b=c(!0),g=c(!1),V=c(!1),x=c(!1),C=c({}),j=c([]),B=c([]),N=c({total_orders:0,total_amount:0,paid_orders:0,success_rate:0}),T=p({page:1,limit:20,keyword:"",status:"",payment_method:"",start_date:"",end_date:""}),S=async()=>{b.value=!0;try{const{data:t}=await(e=T,a.get("/admin/orders",{params:e}));l.value=t.list,m.value=t.total}catch(t){console.error("获取订单列表失败:",t),I.error("获取订单列表失败")}finally{b.value=!1}var e},O=async()=>{try{const{data:e}=await a.get("/admin/orders/stats");N.value=e}catch(e){console.error("获取统计数据失败:",e)}},Q=()=>{T.page=1,S()},de=e=>{e&&2===e.length?(T.start_date=e[0],T.end_date=e[1]):(T.start_date="",T.end_date="")},oe=e=>{C.value={...e},g.value=!0},ne=async e=>{try{await R.confirm("确定要取消这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await(l=e,a.post(`/admin/orders/${l}/cancel`)),I.success("订单取消成功"),S()}catch(t){"cancel"!==t&&I.error("取消订单失败")}var l},se=e=>{const[a,t]=e.split("-"),r=parseInt(t),d=l.value.find(e=>e.id===r);switch(a){case"detail":oe(d);break;case"payment":o=d,C.value={...o},x.value=!0;break;case"cancel":ne(r);break;case"resend":(async()=>{try{await R.confirm("确定要重新发送通知吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),I.success("通知发送成功")}catch(e){"cancel"!==e&&I.error("发送通知失败")}})()}var o},ie=async()=>{try{I.success("导出成功")}catch(e){I.error("导出失败")}},ce=()=>{0!==j.value.length?R.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"批量取消",cancelButtonText:"批量导出"}).then(()=>{pe()}).catch(e=>{"cancel"===e&&_e()}):I.warning("请先选择要操作的订单")},pe=async()=>{try{if(0===j.value.filter(e=>"pending"===e.status).map(e=>e.id).length)return void I.warning("没有可取消的订单");I.success("批量取消成功"),S()}catch(e){I.error("批量取消失败")}},_e=async()=>{try{j.value.map(e=>e.id);I.success("批量导出成功")}catch(e){I.error("批量导出失败")}},fe=e=>{j.value=e},ye=e=>{T.limit=e,S()},ve=e=>{T.page=e,S()},be=()=>{S(),O()},ge=()=>{S()},he=e=>({wechat:"微信支付",alipay:"支付宝",qqpay:"QQ钱包",bank:"银行卡"}[e]||"未知"),we=e=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[e]||"未知");return _(()=>{S(),O()}),(e,a)=>{const c=U,p=z,_=q,j=$,S=A,O=E,R=Y,ne=D,pe=G,_e=M,Ve=w,ke=X,De=W,Ae=Z,xe=k,Je=ae,Pe=le,Le=ee;return r(),s("div",je,[o("div",Ue,[n(c,{modelValue:T.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>T.keyword=e),placeholder:"搜索订单号、用户名",style:{width:"200px"},class:"filter-item",onKeyup:f(Q,["enter"]),clearable:""},null,8,["modelValue"]),n(_,{modelValue:T.status,"onUpdate:modelValue":a[1]||(a[1]=e=>T.status=e),placeholder:"订单状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:d(()=>[n(p,{label:"全部",value:""}),n(p,{label:"待支付",value:"pending"}),n(p,{label:"已支付",value:"paid"}),n(p,{label:"已取消",value:"cancelled"}),n(p,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"]),n(_,{modelValue:T.payment_method,"onUpdate:modelValue":a[2]||(a[2]=e=>T.payment_method=e),placeholder:"支付方式",clearable:"",style:{width:"120px"},class:"filter-item"},{default:d(()=>[n(p,{label:"全部",value:""}),n(p,{label:"微信支付",value:"wechat"}),n(p,{label:"支付宝",value:"alipay"}),n(p,{label:"QQ钱包",value:"qqpay"}),n(p,{label:"银行卡",value:"bank"})]),_:1},8,["modelValue"]),n(j,{modelValue:B.value,"onUpdate:modelValue":a[3]||(a[3]=e=>B.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"filter-item",onChange:de},null,8,["modelValue"]),n(S,{class:"filter-item",type:"primary",icon:"Search",onClick:Q},{default:d(()=>a[9]||(a[9]=[i(" 搜索 ",-1)])),_:1,__:[9]}),n(S,{class:"filter-item",type:"warning",icon:"Download",onClick:ie},{default:d(()=>a[10]||(a[10]=[i(" 导出数据 ",-1)])),_:1,__:[10]})]),n(R,{gutter:20,class:"stats-row"},{default:d(()=>[n(O,{span:6},{default:d(()=>[n(te,{type:"primary",icon:y(F),value:N.value.total_orders,label:"总订单数",trend:{type:"up",value:"+125",desc:"较上月"},clickable:"",onClick:ge},null,8,["icon","value"])]),_:1}),n(O,{span:6},{default:d(()=>[n(te,{type:"success",icon:y(J),value:N.value.total_amount,label:"总交易额",prefix:"¥",trend:{type:"up",value:"+28.5%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),n(O,{span:6},{default:d(()=>[n(te,{type:"warning",icon:y(P),value:N.value.paid_orders,label:"已支付订单",trend:{type:"up",value:"+18.2%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),n(O,{span:6},{default:d(()=>[n(te,{type:"danger",icon:y(L),value:N.value.success_rate,label:"支付成功率",suffix:"%",decimals:1,trend:{type:"up",value:"+2.1%",desc:"较上月"}},null,8,["icon","value"])]),_:1})]),_:1}),n(Pe,null,{header:d(()=>[o("div",Be,[a[12]||(a[12]=o("h3",null,"订单列表",-1)),o("div",null,[n(S,{type:"primary",size:"small",onClick:ce},{default:d(()=>a[11]||(a[11]=[i("批量操作",-1)])),_:1,__:[11]})])])]),default:d(()=>[v((r(),t(xe,{data:l.value,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",onSelectionChange:fe},{default:d(()=>[n(ne,{type:"selection",width:"55"}),n(ne,{label:"订单号",width:"180"},{default:d(({row:e})=>[o("div",qe,[o("span",null,h(e.order_no),1),n(S,{type:"text",size:"small",onClick:a=>(async e=>{try{await navigator.clipboard.writeText(e),I.success("订单号已复制到剪贴板")}catch(a){I.error("复制失败")}})(e.order_no),class:"copy-btn"},{default:d(()=>[n(pe,null,{default:d(()=>[n(y(K))]),_:1})]),_:2},1032,["onClick"])])]),_:1}),n(ne,{label:"用户信息",width:"150"},{default:d(({row:e})=>[o("div",ze,[n(_e,{src:e.user?.avatar,size:"small"},{default:d(()=>[i(h(e.user?.username?.charAt(0).toUpperCase()),1)]),_:2},1032,["src"]),o("div",Ne,[o("div",Te,h(e.user?.username),1),o("div",Se,"ID: "+h(e.user_id),1)])])]),_:1}),n(ne,{label:"群组信息",width:"200"},{default:d(({row:e})=>[e.wechat_group?(r(),s("div",Ie,[o("div",Oe,h(e.wechat_group.name),1),o("div",Qe,"¥"+h(e.wechat_group.price),1)])):(r(),s("span",Re,"-"))]),_:1}),n(ne,{label:"订单金额",width:"100"},{default:d(({row:e})=>[o("span",$e,"¥"+h(e.amount.toFixed(2)),1)]),_:1}),n(ne,{label:"支付方式",width:"100"},{default:d(({row:e})=>{return[e.payment_method?(r(),t(Ve,{key:0,type:(a=e.payment_method,{wechat:"success",alipay:"primary",qqpay:"warning",bank:"info"}[a]||"info")},{default:d(()=>[i(h(he(e.payment_method)),1)]),_:2},1032,["type"])):(r(),s("span",Ee,"-"))];var a}),_:1}),n(ne,{label:"订单状态",width:"100"},{default:d(({row:e})=>{return[n(Ve,{type:(a=e.status,{pending:"warning",paid:"success",cancelled:"info",refunded:"danger"}[a]||"info")},{default:d(()=>[i(h(we(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),n(ne,{label:"创建时间",width:"160"},{default:d(({row:e})=>[i(h(y(re)(e.created_at)),1)]),_:1}),n(ne,{label:"支付时间",width:"160"},{default:d(({row:e})=>[i(h(e.paid_at?y(re)(e.paid_at):"-"),1)]),_:1}),n(ne,{label:"操作",width:"200",fixed:"right"},{default:d(({row:e})=>[n(S,{type:"primary",size:"small",onClick:a=>oe(e)},{default:d(()=>a[13]||(a[13]=[i(" 查看 ",-1)])),_:2,__:[13]},1032,["onClick"]),"paid"===e.status?(r(),t(S,{key:0,type:"success",size:"small",onClick:a=>(e=>{C.value={...e},V.value=!0})(e)},{default:d(()=>a[14]||(a[14]=[i(" 退款 ",-1)])),_:2,__:[14]},1032,["onClick"])):u("",!0),n(Ae,{onCommand:se},{dropdown:d(()=>[n(De,null,{default:d(()=>[n(ke,{command:`detail-${e.id}`},{default:d(()=>a[16]||(a[16]=[i("订单详情",-1)])),_:2,__:[16]},1032,["command"]),"pending"===e.status?(r(),t(ke,{key:0,command:`payment-${e.id}`},{default:d(()=>a[17]||(a[17]=[i("支付信息",-1)])),_:2,__:[17]},1032,["command"])):u("",!0),"pending"===e.status?(r(),t(ke,{key:1,command:`cancel-${e.id}`},{default:d(()=>a[18]||(a[18]=[i("取消订单",-1)])),_:2,__:[18]},1032,["command"])):u("",!0),"paid"===e.status?(r(),t(ke,{key:2,command:`resend-${e.id}`},{default:d(()=>a[19]||(a[19]=[i("重发通知",-1)])),_:2,__:[19]},1032,["command"])):u("",!0)]),_:2},1024)]),default:d(()=>[n(S,{type:"info",size:"small"},{default:d(()=>[a[15]||(a[15]=i(" 更多",-1)),n(pe,{class:"el-icon--right"},{default:d(()=>[n(y(H))]),_:1})]),_:1,__:[15]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Le,b.value]]),o("div",Fe,[n(Je,{"current-page":T.page,"onUpdate:currentPage":a[4]||(a[4]=e=>T.page=e),"page-size":T.limit,"onUpdate:pageSize":a[5]||(a[5]=e=>T.limit=e),"page-sizes":[10,20,30,50],total:m.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ye,onCurrentChange:ve},null,8,["current-page","page-size","total"])])]),_:1}),n(ue,{modelValue:g.value,"onUpdate:modelValue":a[6]||(a[6]=e=>g.value=e),"order-data":C.value},null,8,["modelValue","order-data"]),n(me,{modelValue:V.value,"onUpdate:modelValue":a[7]||(a[7]=e=>V.value=e),"order-data":C.value,onSuccess:be},null,8,["modelValue","order-data"]),n(Ce,{modelValue:x.value,"onUpdate:modelValue":a[8]||(a[8]=e=>x.value=e),"order-data":C.value},null,8,["modelValue","order-data"])])}}},[["__scopeId","data-v-358e0db8"]]);export{Je as default};
