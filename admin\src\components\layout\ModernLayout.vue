<template>
  <div class="modern-layout" :class="{ 'sidebar-collapsed': isCollapsed }">
    <!-- 现代化侧边栏 -->
    <div class="modern-sidebar">
      <!-- Logo区域 -->
      <div class="sidebar-logo">
        <div class="logo-container" @click="toggleSidebar">
          <div class="logo-icon">
            <el-icon size="28"><Monitor /></el-icon>
          </div>
          <transition name="fade">
            <div v-show="!isCollapsed" class="logo-content">
              <div class="logo-title">LinkHub Pro</div>
              <div class="logo-subtitle">智能管理后台</div>
            </div>
          </transition>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav" :class="{ collapsed: isCollapsed }">
        <!-- 数据分析域 -->
        <div class="nav-group">
          <div v-show="!isCollapsed" class="nav-group-header">
            <el-icon class="group-icon"><TrendCharts /></el-icon>
            <span class="group-title">数据分析</span>
          </div>

          <router-link to="/admin/dashboard" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">数据看板</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/analytics" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><DataLine /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">数据分析</span>
              </transition>
            </div>
          </router-link>
        </div>

        <!-- 用户管理域 -->
        <div class="nav-group">
          <div v-show="!isCollapsed" class="nav-group-header">
            <el-icon class="group-icon"><User /></el-icon>
            <span class="group-title">用户管理</span>
          </div>

          <router-link to="/admin/users" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><User /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">用户管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/user-analytics" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">用户分析</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/groups" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">群组管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/permission/permissions" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">权限配置</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/permission/roles" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">角色管理</span>
              </transition>
            </div>
          </router-link>
        </div>

        <!-- 业务管理域 -->
        <div class="nav-group">
          <div v-show="!isCollapsed" class="nav-group-header">
            <el-icon class="group-icon"><Grid /></el-icon>
            <span class="group-title">业务管理</span>
          </div>

          <router-link to="/admin/orders" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">订单管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/agents" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Avatar /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">代理商管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/distributors" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Share /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">分销管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/promotion" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Share /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">推广管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/templates" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Document /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">模板管理</span>
              </transition>
            </div>
          </router-link>
        </div>



        <!-- 财务管理域 -->
        <div class="nav-group">
          <div v-show="!isCollapsed" class="nav-group-header">
            <el-icon class="group-icon"><Money /></el-icon>
            <span class="group-title">财务管理</span>
          </div>

          <router-link to="/admin/finance" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Money /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">财务仪表板</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/transactions" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">交易记录</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/commission-logs" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Money /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">佣金日志</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/withdraw-manage" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">提现管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/payment-settings" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Setting /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">支付设置</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/payment-channels" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><CreditCard /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">支付渠道</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/payment-orders" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Tickets /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">支付订单</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/payment-logs" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Document /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">支付日志</span>
              </transition>
            </div>
          </router-link>
        </div>

        <!-- 系统配置域 -->
        <div class="nav-group">
          <div v-show="!isCollapsed" class="nav-group-header">
            <el-icon class="group-icon"><Setting /></el-icon>
            <span class="group-title">系统配置</span>
          </div>

          <router-link to="/admin/settings" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">系统设置</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/operation-logs" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Document /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">操作日志</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/system-monitor" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">系统监控</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/notifications" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">通知管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/data-export" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Document /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">数据导出</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/file-management" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Folder /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">文件管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/anti-block" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">防红系统</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/domains" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">域名管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/short-links" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Link /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">短链管理</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/anti-block-analytics" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">防红分析</span>
              </transition>
            </div>
          </router-link>

          <router-link to="/admin/anti-block-enhanced" class="nav-item">
            <div class="nav-item-content">
              <div class="nav-icon">
                <el-icon><Star /></el-icon>
              </div>
              <transition name="slide-fade">
                <span v-show="!isCollapsed" class="nav-text">增强防红</span>
              </transition>
            </div>
          </router-link>
        </div>
      </nav>
      
      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <div class="collapse-btn" @click="toggleSidebar">
          <el-icon>
            <component :is="isCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 现代化头部 -->
      <header class="modern-header">
        <div class="header-left">
          <div class="breadcrumb-wrapper">
            <h1 class="page-title">{{ $route.meta.title || '管理后台' }}</h1>
            <div class="page-subtitle">{{ getPageDescription() }}</div>
          </div>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <!-- 搜索框 -->
            <div class="global-search">
              <el-input
                v-model="searchKeyword"
                placeholder="全局搜索..."
                prefix-icon="Search"
                clearable
                @keyup.enter="handleGlobalSearch"
              />
            </div>
            
            <!-- 通知 -->
            <el-badge :value="12" class="notification-badge">
              <el-button circle class="notification-btn">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 用户菜单 -->
            <el-dropdown trigger="click" @command="handleUserCommand">
              <div class="user-avatar">
                <el-avatar :size="36" :src="userInfo?.avatar">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
                <div class="user-info">
                  <div class="user-name">{{ userInfo?.nickname || '管理员' }}</div>
                  <div class="user-role">{{ getRoleDisplayName() }}</div>
                </div>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="user-dropdown">
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="page-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor, DataLine, ChatDotRound, Share, Tickets, Grid, Lock, Link,
  User, OfficeBuilding, Money, CreditCard, Setting, Tools, Key,
  TrendCharts, Bell, UserFilled, SwitchButton, Search, Expand, Fold,
  Document, Avatar, ShoppingCart, Connection, Star, Folder
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isCollapsed = ref(false)
const searchKeyword = ref('')

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  // 保存到本地存储
  localStorage.setItem('sidebar-collapsed', isCollapsed.value)
}

const getPageDescription = () => {
  const descriptions = {
    '/dashboard': '查看系统整体运营状况和关键指标',
    '/data-screen': '大屏展示核心业务数据',
    '/community/groups': '管理平台所有社群信息',
    '/distribution/distributors': '管理分销商和分销体系',
    '/orders/list': '处理和查看所有订单信息',
    '/user/list': '管理平台用户账户和权限',
    '/finance/dashboard': '查看财务收支和资金流向',
    '/system/settings': '配置系统参数和功能选项'
  }
  return descriptions[route.path] || '管理和配置系统功能'
}

const getRoleDisplayName = () => {
  const roleNames = {
    admin: '超级管理员',
    manager: '管理员',
    distributor: '分销员',
    user: '普通用户'
  }
  return roleNames[userInfo.value?.role] || '管理员'
}

const handleGlobalSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  ElMessage.info(`搜索: ${searchKeyword.value}`)
  // TODO: 实现全局搜索功能
}

const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'settings':
      router.push('/user/settings')  
      break
    case 'logout':
      await handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 清除用户数据
    await userStore.logout()
    
    // 清除本地存储
    localStorage.clear()
    sessionStorage.clear()
    
    // 跳转到登录页
    router.push('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  // 恢复侧边栏状态
  const savedState = localStorage.getItem('sidebar-collapsed')
  if (savedState !== null) {
    isCollapsed.value = JSON.parse(savedState)
  }
})
</script>

<style lang="scss" scoped>

.modern-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow: hidden;
}

// 现代化侧边栏
.modern-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 100;
  
  .sidebar-collapsed & {
    width: 80px;
  }
}

// Logo区域
.sidebar-logo {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  
  .logo-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    
    .logo-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      color: white;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    }
    
    .logo-content {
      margin-left: 16px;
      
      .logo-title {
        font-size: 22px;
        font-weight: 700;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        line-height: 1.2;
      }
      
      .logo-subtitle {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
        margin-top: 2px;
      }
    }
  }
}

// 导航菜单
.sidebar-nav {
  flex: 1;
  padding: 24px 0;
  overflow-y: auto;
  overflow-x: hidden;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
  }
}

// 导航分组
.nav-group {
  margin-bottom: 32px;
  
  .nav-group-header {
    display: flex;
    align-items: center;
    padding: 12px 24px 16px;
    
    .group-icon {
      width: 18px;
      height: 18px;
      color: #3b82f6;
      margin-right: 8px;
    }
    
    .group-title {
      font-size: 13px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      text-transform: uppercase;
      letter-spacing: 0.8px;
    }
  }
}

// 导航项
.nav-item {
  display: block;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.85);
  margin: 0 16px 8px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    border-radius: 0 2px 2px 0;
    transition: background 0.3s ease;
  }
  
  .nav-item-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    
    .nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-right: 16px;
      transition: all 0.3s ease;
    }
    
    .nav-text {
      font-size: 15px;
      font-weight: 500;
      flex: 1;
      white-space: nowrap;
    }
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
    color: white;
    
    &::before {
      background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
    }
    
    .nav-icon {
      transform: scale(1.1);
      color: #3b82f6;
    }
  }
  
  &.router-link-active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    
    &::before {
      background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
    }
    
    &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      background: #3b82f6;
      border-radius: 50%;
      box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
    }
    
    .nav-icon {
      color: #3b82f6;
    }
  }
}

// 侧边栏底部
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 auto;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      transform: scale(1.1);
    }
  }
}

// 主内容区
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
}

// 现代化头部
.modern-header {
  height: 72px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  
  .header-left {
    .breadcrumb-wrapper {
      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        margin: 0 0 4px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .page-subtitle {
        font-size: 14px;
        color: #909399;
        font-weight: 500;
      }
    }
  }
  
  .header-right {
    .header-actions {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .global-search {
        width: 300px;
        
        :deep(.el-input__wrapper) {
          border-radius: 20px;
          border: 1px solid #e4e7ed;
          background: #f8f9fa;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #c0c4cc;
            background: white;
          }
          
          &.is-focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          }
        }
      }
      
      .notification-badge {
        .notification-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 1px solid #e4e7ed;
          background: white;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
          }
        }
      }
      
      .user-avatar {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 16px 8px 8px;
        border-radius: 24px;
        background: white;
        border: 1px solid #e4e7ed;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #667eea;
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
          transform: translateY(-2px);
        }
        
        .user-info {
          .user-name {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
          }
          
          .user-role {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 页面内容
.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  
  .content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

// 用户下拉菜单
:deep(.user-dropdown) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  padding: 8px;
  
  .el-dropdown-menu__item {
    border-radius: 8px;
    padding: 12px 16px;
    margin: 4px 0;
    transition: all 0.3s ease;
    
    &:hover {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .el-icon {
      margin-right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-layout {
    flex-direction: column;
  }
  
  .modern-sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px !important;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
    
    &.mobile-open {
      left: 0;
    }
  }
  
  .main-container {
    width: 100%;
  }
  
  .modern-header {
    padding: 0 16px;
    
    .header-actions {
      gap: 12px;
      
      .global-search {
        display: none;
      }
    }
  }
  
  .page-content {
    padding: 16px;
  }
}
</style>
