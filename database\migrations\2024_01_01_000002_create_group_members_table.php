<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 群组成员表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('group_members', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->unsignedBigInteger('group_id')->comment('群组ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->foreign('group_id')->references('id')->on('wechat_groups')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // 成员信息
            $table->enum('status', ['active', 'inactive', 'banned'])->default('active')->comment('成员状态');
            $table->enum('role', ['member', 'admin', 'owner'])->default('member')->comment('成员角色');
            $table->timestamp('joined_at')->useCurrent()->comment('加入时间');
            $table->timestamp('last_active_at')->nullable()->comment('最后活跃时间');
            
            // 邀请信息
            $table->unsignedBigInteger('inviter_id')->nullable()->comment('邀请人ID');
            $table->foreign('inviter_id')->references('id')->on('users')->onDelete('set null');
            $table->string('invite_code', 50)->nullable()->comment('邀请码');
            
            // 统计数据
            $table->integer('message_count')->default(0)->comment('消息数量');
            $table->decimal('contribution_score', 8, 2)->default(0)->comment('贡献分数');
            
            // 时间戳
            $table->timestamps();
            
            // 唯一约束和索引
            $table->unique(['group_id', 'user_id']);
            $table->index(['group_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('joined_at');
            $table->index('inviter_id');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('group_members');
    }
};