<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 群组分析数据表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('group_analytics', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->unsignedBigInteger('group_id')->comment('群组ID');
            $table->foreign('group_id')->references('id')->on('wechat_groups')->onDelete('cascade');
            
            // 时间维度
            $table->date('date')->comment('统计日期');
            $table->enum('period_type', ['hour', 'day', 'week', 'month'])->default('day')->comment('统计周期');
            
            // 访问数据
            $table->integer('page_views')->default(0)->comment('页面浏览量');
            $table->integer('unique_visitors')->default(0)->comment('独立访客数');
            $table->integer('new_visitors')->default(0)->comment('新访客数');
            $table->integer('returning_visitors')->default(0)->comment('回访客数');
            $table->decimal('bounce_rate', 5, 2)->default(0)->comment('跳出率');
            $table->integer('avg_session_duration')->default(0)->comment('平均会话时长(秒)');
            
            // 转化数据
            $table->integer('conversion_views')->default(0)->comment('转化页面浏览');
            $table->integer('conversion_clicks')->default(0)->comment('转化按钮点击');
            $table->integer('conversion_orders')->default(0)->comment('转化订单数');
            $table->decimal('conversion_rate', 5, 2)->default(0)->comment('转化率');
            $table->decimal('conversion_revenue', 10, 2)->default(0)->comment('转化收入');
            
            // 成员数据
            $table->integer('new_members')->default(0)->comment('新增成员');
            $table->integer('active_members')->default(0)->comment('活跃成员');
            $table->integer('total_members')->default(0)->comment('总成员数');
            $table->decimal('member_growth_rate', 5, 2)->default(0)->comment('成员增长率');
            
            // 订单数据
            $table->integer('new_orders')->default(0)->comment('新增订单');
            $table->integer('paid_orders')->default(0)->comment('已支付订单');
            $table->integer('cancelled_orders')->default(0)->comment('取消订单');
            $table->decimal('order_revenue', 10, 2)->default(0)->comment('订单收入');
            $table->decimal('avg_order_value', 8, 2)->default(0)->comment('平均订单价值');
            
            // 流量来源
            $table->json('traffic_sources')->nullable()->comment('流量来源统计');
            $table->json('referrer_domains')->nullable()->comment('来源域名统计');
            $table->json('user_agents')->nullable()->comment('用户代理统计');
            $table->json('geo_locations')->nullable()->comment('地理位置统计');
            
            // 设备数据
            $table->json('device_types')->nullable()->comment('设备类型统计');
            $table->json('browser_types')->nullable()->comment('浏览器统计');
            $table->json('os_types')->nullable()->comment('操作系统统计');
            
            // 时间戳
            $table->timestamps();
            
            // 唯一约束和索引
            $table->unique(['group_id', 'date', 'period_type']);
            $table->index(['group_id', 'date']);
            $table->index(['date', 'period_type']);
            $table->index('created_at');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('group_analytics');
    }
};