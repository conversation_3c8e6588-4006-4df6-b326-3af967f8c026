<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NavigationMenu;
use App\Models\NavigationSearchLog;
use App\Services\NavigationSearchService;
use App\Services\NavigationRecommendationService;
use App\Services\NavigationPermissionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

/**
 * 导航搜索与推荐API控制器
 * 
 * 提供全局搜索和智能推荐功能
 */
class NavigationSearchController extends Controller
{
    protected NavigationSearchService $searchService;
    protected NavigationRecommendationService $recommendationService;
    protected NavigationPermissionService $permissionService;

    public function __construct(
        NavigationSearchService $searchService,
        NavigationRecommendationService $recommendationService,
        NavigationPermissionService $permissionService
    ) {
        $this->searchService = $searchService;
        $this->recommendationService = $recommendationService;
        $this->permissionService = $permissionService;
    }

    /**
     * 全局导航搜索
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:1|max:200',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'limit' => 'nullable|integer|min:1|max:50',
            'include_suggestions' => 'boolean',
            'search_type' => 'nullable|string|in:exact,fuzzy,semantic'
        ]);

        $user = Auth::user();
        $query = trim($request->query);
        $domain = $request->domain;
        $limit = $request->integer('limit', 20);
        $includeSuggestions = $request->boolean('include_suggestions', true);
        $searchType = $request->input('search_type', 'fuzzy');

        try {
            // 执行搜索
            $searchResults = $this->searchService->search(
                query: $query,
                domain: $domain,
                limit: $limit,
                user: $user,
                searchType: $searchType
            );

            // 获取搜索建议
            $suggestions = [];
            if ($includeSuggestions) {
                $suggestions = $this->searchService->getSuggestions($query, $domain, $user);
            }

            // 记录搜索日志
            $this->recordSearchLog($user, $query, $searchResults, $request);

            return response()->json([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'results' => $searchResults['results'],
                    'total' => $searchResults['total'],
                    'suggestions' => $suggestions,
                    'search_time' => $searchResults['search_time'],
                    'filters_applied' => $searchResults['filters_applied']
                ],
                'meta' => [
                    'search_type' => $searchType,
                    'domain' => $domain,
                    'has_next' => $searchResults['total'] > $limit,
                    'timestamp' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '搜索失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取搜索建议
     */
    public function getSuggestions(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:1|max:50',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'limit' => 'nullable|integer|min:1|max:20'
        ]);

        $user = Auth::user();
        $query = trim($request->query);
        $domain = $request->domain;
        $limit = $request->integer('limit', 10);

        try {
            $suggestions = $this->searchService->getSuggestions($query, $domain, $user, $limit);

            return response()->json([
                'success' => true,
                'data' => $suggestions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取搜索建议失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取热门搜索词
     */
    public function getPopularQueries(Request $request): JsonResponse
    {
        $request->validate([
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'period' => 'nullable|string|in:today,week,month',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);

        $domain = $request->domain;
        $period = $request->input('period', 'week');
        $limit = $request->integer('limit', 20);

        try {
            $cacheKey = "popular_search_queries:{$domain}:{$period}:{$limit}";
            
            $popularQueries = Cache::remember($cacheKey, 3600, function () use ($domain, $period, $limit) {
                return $this->searchService->getPopularQueries($domain, $period, $limit);
            });

            return response()->json([
                'success' => true,
                'data' => $popularQueries,
                'meta' => [
                    'domain' => $domain,
                    'period' => $period,
                    'generated_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取热门搜索失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取智能推荐
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'nullable|string|in:frequent,collaborative,role_based,trending,contextual',
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'limit' => 'nullable|integer|min:1|max:30',
            'context' => 'nullable|array'
        ]);

        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $type = $request->input('type', 'mixed');
        $domain = $request->domain;
        $limit = $request->integer('limit', 15);
        $context = $request->context;

        try {
            $recommendations = $this->recommendationService->getRecommendations(
                userId: $user->id,
                domain: $domain,
                type: $type,
                limit: $limit,
                context: $context
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'recommendations' => $recommendations,
                    'explanation' => $this->getRecommendationExplanation($type),
                    'personalization_score' => $this->calculatePersonalizationScore($user->id, $recommendations)
                ],
                'meta' => [
                    'type' => $type,
                    'domain' => $domain,
                    'user_id' => $user->id,
                    'generated_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取推荐失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取个性化导航
     */
    public function getPersonalizedNavigation(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'domain' => 'nullable|string|in:business,operation,analytics,system',
            'include_predictions' => 'boolean',
            'layout' => 'nullable|string|in:grid,list,compact'
        ]);

        $domain = $request->domain;
        $includePredictions = $request->boolean('include_predictions', true);
        $layout = $request->input('layout', 'grid');

        try {
            $cacheKey = "personalized_nav:{$user->id}:{$domain}:{$layout}";
            
            $personalizedNav = Cache::remember($cacheKey, 1800, function () use ($user, $domain, $includePredictions) {
                return [
                    'frequent_menus' => $this->searchService->getFrequentMenus($user->id, $domain, 8),
                    'recommended_menus' => $this->recommendationService->getRecommendations($user->id, $domain, 'mixed', 6),
                    'recent_searches' => $this->searchService->getRecentSearches($user->id, 5),
                    'quick_actions' => $this->getQuickActions($user, $domain),
                    'shortcuts' => $this->getPersonalizedShortcuts($user->id, $domain)
                ];
            });

            // 添加预测功能
            if ($includePredictions) {
                $personalizedNav['predicted_next'] = $this->predictNextActions($user->id, $domain);
            }

            return response()->json([
                'success' => true,
                'data' => $personalizedNav,
                'meta' => [
                    'layout' => $layout,
                    'cache_ttl' => 1800,
                    'last_updated' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取个性化导航失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 记录推荐点击
     */
    public function recordRecommendationClick(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'menu_code' => 'required|string|max:50',
            'recommendation_type' => 'required|string|max:30',
            'position' => 'nullable|integer|min:0',
            'context' => 'nullable|array'
        ]);

        try {
            $this->recommendationService->recordClick(
                userId: $user->id,
                menuCode: $request->menu_code,
                recommendationType: $request->recommendation_type,
                position: $request->position,
                context: $request->context
            );

            return response()->json([
                'success' => true,
                'message' => '推荐点击已记录'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '记录推荐点击失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取搜索统计
     */
    public function getSearchStats(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'period' => 'nullable|string|in:today,week,month',
            'domain' => 'nullable|string|in:business,operation,analytics,system'
        ]);

        $period = $request->input('period', 'week');
        $domain = $request->domain;

        try {
            $stats = $this->searchService->getUserSearchStats($user->id, $period, $domain);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'meta' => [
                    'period' => $period,
                    'domain' => $domain,
                    'user_id' => $user->id
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取搜索统计失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    // ========== 私有辅助方法 ==========

    private function recordSearchLog($user, string $query, array $results, Request $request): void
    {
        if ($user) {
            dispatch(function () use ($user, $query, $results, $request) {
                NavigationSearchLog::create([
                    'user_id' => $user->id,
                    'query' => $query,
                    'results' => collect($results['results'])->pluck('code')->toArray(),
                    'result_count' => $results['total'],
                    'searched_at' => now(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]);
            })->afterResponse();
        }
    }

    private function getRecommendationExplanation(string $type): array
    {
        $explanations = [
            'frequent' => [
                'title' => '基于使用频率',
                'description' => '根据您的历史访问频率推荐最常用的功能'
            ],
            'collaborative' => [
                'title' => '协同过滤推荐',
                'description' => '基于相似用户的使用习惯为您推荐'
            ],
            'role_based' => [
                'title' => '角色推荐',
                'description' => '根据您的角色和职责推荐相关功能'
            ],
            'trending' => [
                'title' => '热门趋势',
                'description' => '推荐当前最受欢迎的功能'
            ],
            'contextual' => [
                'title' => '智能推荐',
                'description' => '基于当前上下文和使用场景的智能推荐'
            ],
            'mixed' => [
                'title' => '综合推荐',
                'description' => '结合多种推荐算法为您提供最佳建议'
            ]
        ];

        return $explanations[$type] ?? $explanations['mixed'];
    }

    private function calculatePersonalizationScore(int $userId, array $recommendations): float
    {
        // 计算个性化评分的简化逻辑
        $totalScore = 0;
        $count = count($recommendations);

        if ($count === 0) {
            return 0;
        }

        foreach ($recommendations as $rec) {
            $totalScore += $rec['score'] ?? 0.5;
        }

        return round($totalScore / $count, 2);
    }

    private function getQuickActions($user, ?string $domain): array
    {
        // 获取快捷操作的逻辑
        return [
            ['code' => 'create_group', 'name' => '创建群组', 'icon' => 'plus'],
            ['code' => 'view_analytics', 'name' => '查看数据', 'icon' => 'chart'],
            ['code' => 'manage_users', 'name' => '用户管理', 'icon' => 'users']
        ];
    }

    private function getPersonalizedShortcuts(int $userId, ?string $domain): array
    {
        // 获取个性化快捷方式的逻辑
        return [];
    }

    private function predictNextActions(int $userId, ?string $domain): array
    {
        // 预测下一步操作的逻辑
        return [];
    }
}