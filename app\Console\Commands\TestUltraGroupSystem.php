<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EnhancedLocationService;
use App\Services\UltraAntiBlockService;
use App\Services\VirtualDataGenerator;
use App\Models\WechatGroup;
use App\Models\DomainPool;
use Illuminate\Support\Facades\Http;

/**
 * 测试超级群组系统命令
 * 
 * <AUTHOR> Enhancement
 * @date 2024-12-19
 */
class TestUltraGroupSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:ultra-group 
                            {--test=all : Test type (all, location, antiblock, virtual, create)}
                            {--verbose : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试超级群组系统的所有增强功能';

    protected $locationService;
    protected $antiBlockService;
    protected $virtualDataGenerator;

    public function __construct(
        EnhancedLocationService $locationService,
        UltraAntiBlockService $antiBlockService,
        VirtualDataGenerator $virtualDataGenerator
    ) {
        parent::__construct();
        $this->locationService = $locationService;
        $this->antiBlockService = $antiBlockService;
        $this->virtualDataGenerator = $virtualDataGenerator;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $testType = $this->option('test');
        
        $this->info('===================================');
        $this->info('超级群组系统集成测试');
        $this->info('===================================');
        
        switch ($testType) {
            case 'location':
                $this->testLocationService();
                break;
            case 'antiblock':
                $this->testAntiBlockService();
                break;
            case 'virtual':
                $this->testVirtualDataGenerator();
                break;
            case 'create':
                $this->testGroupCreation();
                break;
            case 'all':
            default:
                $this->testLocationService();
                $this->testAntiBlockService();
                $this->testVirtualDataGenerator();
                $this->testGroupCreation();
                $this->testEndToEnd();
                break;
        }
        
        $this->info("\n测试完成！");
        return Command::SUCCESS;
    }

    /**
     * 测试定位服务
     */
    private function testLocationService()
    {
        $this->info("\n[1] 测试增强版定位服务...");
        
        // 测试IP定位
        $testIps = [
            '***************' => '江苏省南京市',
            '*********' => '浙江省杭州市',
            '*******' => '美国'
        ];
        
        foreach ($testIps as $ip => $expectedLocation) {
            $this->info("  测试IP: {$ip}");
            $locationData = $this->locationService->getFullLocationData($ip);
            
            if ($this->option('verbose')) {
                $this->line("    城市: " . ($locationData['city'] ?? '未知'));
                $this->line("    省份: " . ($locationData['province'] ?? '未知'));
                $this->line("    来源: " . ($locationData['source'] ?? 'unknown'));
            }
        }
        
        // 测试占位符替换
        $this->info("  测试占位符替换...");
        $testContent = 'xxx本地群，火爆招募中！{city}最新群组，[位置]专属优惠';
        $replaced = $this->locationService->smartReplace($testContent, '***************');
        
        if ($this->option('verbose')) {
            $this->line("    原文: {$testContent}");
            $this->line("    替换后: {$replaced}");
        }
        
        // 测试ThinkPHP兼容性
        $this->info("  测试ThinkPHP兼容性...");
        $cookieValue = $_COOKIE['curcity2'] ?? null;
        $this->line("    Cookie curcity2: " . ($cookieValue ?? '未设置'));
        
        $this->info("  ✅ 定位服务测试完成");
    }

    /**
     * 测试防红系统
     */
    private function testAntiBlockService()
    {
        $this->info("\n[2] 测试超级防红系统...");
        
        // 测试域名池
        $this->info("  检查域名池...");
        $domainCount = DomainPool::where('status', 'active')->count();
        $this->line("    活跃域名数: {$domainCount}");
        
        if ($domainCount == 0) {
            $this->warn("    警告: 没有活跃域名，创建测试域名...");
            $this->createTestDomains();
        }
        
        // 测试链接生成
        $this->info("  测试防红链接生成...");
        $levels = ['low', 'medium', 'high', 'ultra'];
        
        foreach ($levels as $level) {
            $result = $this->antiBlockService->generateUltraSecureLink(
                1,
                'https://example.com/group/1',
                $level
            );
            
            $status = $result['success'] ? '✅ 成功' : '❌ 失败';
            $this->line("    级别 {$level}: {$status}");
            
            if ($this->option('verbose') && $result['success']) {
                $this->line("      短链接: " . ($result['links']['short'] ?? 'N/A'));
                $this->line("      域名: " . ($result['domain'] ?? 'N/A'));
            }
        }
        
        // 测试微信环境检测
        $this->info("  测试微信环境检测...");
        $userAgents = [
            'MicroMessenger/7.0.20' => '微信',
            'QQ/8.9.0' => 'QQ',
            'Mozilla/5.0' => '浏览器'
        ];
        
        foreach ($userAgents as $ua => $platform) {
            $request = request();
            $request->headers->set('User-Agent', $ua);
            $result = $this->antiBlockService->handleWechatEnvironment($request);
            $action = $result['allow'] ? '允许' : '拦截';
            $this->line("    {$platform}: {$action}");
        }
        
        $this->info("  ✅ 防红系统测试完成");
    }

    /**
     * 测试虚拟数据生成器
     */
    private function testVirtualDataGenerator()
    {
        $this->info("\n[3] 测试虚拟数据生成器...");
        
        // 测试虚拟成员生成
        $this->info("  生成虚拟成员...");
        $styles = ['qq', 'za', 'business', 'young'];
        
        foreach ($styles as $style) {
            $members = $this->virtualDataGenerator->generateMembers(5, $style);
            $this->line("    风格 {$style}: 生成 " . count($members) . " 个成员");
            
            if ($this->option('verbose') && !empty($members)) {
                $this->line("      示例: " . $members[0]['nickname'] ?? 'N/A');
            }
        }
        
        // 测试虚拟评论生成
        $this->info("  生成虚拟评论...");
        $comments = $this->virtualDataGenerator->generateComments(5);
        $this->line("    生成评论数: " . count($comments));
        
        if ($this->option('verbose') && !empty($comments)) {
            $this->line("      示例评论: " . ($comments[0]['content'] ?? 'N/A'));
            $this->line("      点赞数: " . ($comments[0]['likes'] ?? 0));
        }
        
        // 测试统计数据生成
        $this->info("  生成虚拟统计...");
        $stats = $this->virtualDataGenerator->generateStats();
        $this->line("    群成员: " . ($stats['total_members'] ?? 0));
        $this->line("    今日加入: " . ($stats['today_joined'] ?? 0));
        $this->line("    阅读量: " . ($stats['read_count'] ?? 0));
        
        // 测试ThinkPHP格式兼容
        $this->info("  测试ThinkPHP格式兼容...");
        $formatted = $this->virtualDataGenerator->generateFormattedComments(3);
        $lines = explode("\n", $formatted);
        $this->line("    格式化评论数: " . count($lines));
        
        if ($this->option('verbose') && !empty($lines)) {
            $this->line("      示例: " . $lines[0]);
        }
        
        $this->info("  ✅ 虚拟数据生成器测试完成");
    }

    /**
     * 测试群组创建
     */
    private function testGroupCreation()
    {
        $this->info("\n[4] 测试超级群组创建...");
        
        // 构建测试数据
        $testData = [
            'title' => 'xxx本地测试群_' . time(),
            'subtitle' => '集成测试群组',
            'description' => '这是一个用于测试的群组',
            'price' => 9.9,
            'member_limit' => 200,
            'anti_block_level' => 'high',
            'wechat_detection' => true,
            'avatar_library' => 'qq',
            'virtual_member_count' => 15,
            'use_geo_targeting' => true
        ];
        
        $this->info("  创建测试群组...");
        
        try {
            // 模拟API调用
            $response = Http::post(url('/api/v1/admin/ultra-groups/create'), $testData);
            
            if ($response->successful()) {
                $data = $response->json();
                if ($data['success'] ?? false) {
                    $this->info("    ✅ 群组创建成功");
                    $this->line("      群组ID: " . ($data['data']['group']['id'] ?? 'N/A'));
                    $this->line("      短链接: " . ($data['data']['links']['short'] ?? 'N/A'));
                } else {
                    $this->error("    ❌ 创建失败: " . ($data['message'] ?? 'Unknown error'));
                }
            } else {
                $this->error("    ❌ API调用失败: " . $response->status());
            }
        } catch (\Exception $e) {
            $this->error("    ❌ 异常: " . $e->getMessage());
        }
    }

    /**
     * 端到端测试
     */
    private function testEndToEnd()
    {
        $this->info("\n[5] 端到端集成测试...");
        
        // 测试完整流程
        $this->info("  测试完整创建和访问流程...");
        
        // 1. 创建群组
        $this->line("    1. 创建群组");
        
        // 2. 访问落地页
        $this->line("    2. 模拟访问落地页");
        
        // 3. 检查定位替换
        $this->line("    3. 验证定位替换");
        
        // 4. 检查虚拟数据
        $this->line("    4. 验证虚拟数据显示");
        
        // 5. 测试防红跳转
        $this->line("    5. 测试防红跳转");
        
        $this->info("  ✅ 端到端测试完成");
    }

    /**
     * 创建测试域名
     */
    private function createTestDomains()
    {
        $testDomains = [
            ['domain' => 'test1.example.com', 'type' => 'landing', 'status' => 'active', 'health_score' => 90],
            ['domain' => 'test2.example.com', 'type' => 'redirect', 'status' => 'active', 'health_score' => 85],
            ['domain' => 'test3.example.com', 'type' => 'api', 'status' => 'active', 'health_score' => 80],
        ];
        
        foreach ($testDomains as $domainData) {
            DomainPool::create($domainData);
        }
        
        $this->line("      创建了 " . count($testDomains) . " 个测试域名");
    }
}