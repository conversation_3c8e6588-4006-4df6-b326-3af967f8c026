import api from './index'

// 订单管理相关API
export const getOrderList = (params) => {
  return api.get('/admin/orders', { params })
}

export const getOrderDetail = (id) => {
  return api.get(`/admin/orders/${id}`)
}

export const updateOrder = (id, data) => {
  return api.put(`/admin/orders/${id}`, data)
}

export const deleteOrder = (id) => {
  return api.delete(`/admin/orders/${id}`)
}

export const getOrderStats = () => {
  return api.get('/admin/orders/stats')
}

export const batchProcessOrders = (data) => {
  return api.post('/admin/orders/batch-process', data)
}

export const refundOrder = (id, data) => {
  return api.post(`/admin/orders/${id}/refund`, data)
}

export const cancelOrder = (id) => {
  return api.post(`/admin/orders/${id}/cancel`)
}

export const getOrderStatistics = () => {
  return api.get('/admin/orders/statistics')
}