import{_ as e}from"./index-DtXAftX0.js";/* empty css               *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                *//* empty css                     *//* empty css               */import{af as a,r as l,L as s,a6 as r,k as o,l as d,t,E as i,z as u,u as c,D as n,F as m,Y as p,y as f,C as v}from"./vue-vendor-Dy164gUc.js";import{an as _,T as g,aX as b,at as h,bv as w,bp as V,aS as x,ca as y,aZ as z,a_ as U,bq as k,aM as j,ao as P,aY as q,bm as N,bB as A,U as C,b9 as $,b8 as F,af as S,cb as Z,cc as L,am as M,as as E,c9 as G,Q as T}from"./element-plus-h2SQQM64.js";import"./utils-D1VZuEZr.js";const Y={class:"modern-user-add"},B={class:"page-header"},D={class:"header-content"},I={class:"header-left"},J={class:"header-icon"},O={class:"header-actions"},Q={class:"content-container"},R={class:"form-container"},X={class:"card-header"},H={class:"card-title"},K={class:"avatar-section"},W={class:"avatar-upload"},ee={class:"upload-actions"},ae={class:"card-header"},le={class:"card-title"},se={class:"role-selection"},re={class:"role-content"},oe={class:"role-icon"},de={class:"role-info"},te={class:"role-name"},ie={class:"role-desc"},ue={class:"card-header"},ce={class:"card-title"},ne={class:"status-content"},me={class:"status-content"},pe={class:"card-header"},fe={class:"card-title"},ve={class:"form-actions"},_e=e({__name:"UserAdd",setup(e){const _e=a(),ge=l();l(!1);const be=l(!1),he=[{value:"admin",label:"超级管理员",description:"拥有系统所有权限，可以管理所有功能",icon:"Star",color:"#f56c6c"},{value:"substation",label:"分站管理员",description:"管理分站运营，拥有分站内所有权限",icon:"Management",color:"#e6a23c"},{value:"agent",label:"代理商",description:"管理下级分销员，拥有团队管理权限",icon:"Avatar",color:"#409eff"},{value:"distributor",label:"分销员",description:"推广群组链接，获得佣金收益",icon:"Share",color:"#67c23a"},{value:"group_owner",label:"群主",description:"管理自己的群组，发布群组内容",icon:"UserFilled",color:"#909399"},{value:"user",label:"普通用户",description:"基础用户权限，可以加入群组",icon:"User",color:"#909399"}],we=s({username:"",realName:"",email:"",phone:"",role:"user",department:"",password:"",confirmPassword:"",status:"active",avatar:"",remark:""}),Ve={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:10,message:"姓名长度在 2 到 10 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],password:[{required:!0,message:"请输入登录密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,message:"密码必须包含大小写字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==we.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}]},xe=()=>{ge.value?.resetFields(),Object.assign(we,{username:"",realName:"",email:"",phone:"",role:"user",department:"",password:"",confirmPassword:"",status:"active",avatar:"",remark:""}),T.info("表单已重置")},ye=async()=>{try{await ge.value.validate(),be.value=!0,await new Promise(e=>setTimeout(e,1500)),T.success("用户创建成功！"),_e.push("/user/list")}catch(e){console.error("表单验证失败:",e),T.error("请检查表单信息是否填写正确")}finally{be.value=!1}};return(e,a)=>{const l=g,s=h,T=x,_e=j,ze=k,Ue=U,ke=z,je=V,Pe=q,qe=A,Ne=N,Ae=F,Ce=$,$e=r("Shield");return d(),o("div",Y,[t("div",B,[t("div",D,[t("div",I,[t("div",J,[i(l,{size:"24"},{default:u(()=>[i(c(_))]),_:1})]),a[12]||(a[12]=t("div",{class:"header-text"},[t("h1",null,"添加用户"),t("p",null,"创建新的系统用户，配置角色权限和基本信息")],-1))]),t("div",O,[i(s,{onClick:a[0]||(a[0]=a=>e.$router.go(-1)),class:"action-btn secondary"},{default:u(()=>[i(l,null,{default:u(()=>[i(c(b))]),_:1}),a[13]||(a[13]=n(" 返回列表 ",-1))]),_:1,__:[13]}),i(s,{type:"primary",onClick:ye,loading:be.value,class:"action-btn primary"},{default:u(()=>[i(l,null,{default:u(()=>[i(c(w))]),_:1}),a[14]||(a[14]=n(" 保存用户 ",-1))]),_:1,__:[14]},8,["loading"])])])]),t("div",Q,[t("div",R,[i(Pe,{class:"form-card",shadow:"never"},{header:u(()=>[t("div",X,[t("div",H,[i(l,null,{default:u(()=>[i(c(P))]),_:1}),a[15]||(a[15]=t("span",null,"基本信息",-1))]),a[16]||(a[16]=t("div",{class:"card-subtitle"},"用户的基本身份信息",-1))])]),default:u(()=>[i(je,{ref_key:"formRef",ref:ge,model:we,rules:Ve,"label-width":"100px",size:"large",class:"modern-form"},{default:u(()=>[t("div",K,[t("div",W,[i(T,{size:80,src:we.avatar,class:"user-avatar"},{default:u(()=>[i(l,{size:"40"},{default:u(()=>[i(c(_))]),_:1})]),_:1},8,["src"]),t("div",ee,[i(s,{size:"small",type:"primary",plain:""},{default:u(()=>[i(l,null,{default:u(()=>[i(c(y))]),_:1}),a[17]||(a[17]=n(" 上传头像 ",-1))]),_:1,__:[17]}),a[18]||(a[18]=t("p",{class:"upload-tip"},"支持 JPG、PNG 格式，建议尺寸 200x200",-1))])])]),i(ke,{gutter:24},{default:u(()=>[i(Ue,{span:12},{default:u(()=>[i(ze,{label:"用户名",prop:"username"},{default:u(()=>[i(_e,{modelValue:we.username,"onUpdate:modelValue":a[1]||(a[1]=e=>we.username=e),placeholder:"请输入用户名","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),i(Ue,{span:12},{default:u(()=>[i(ze,{label:"真实姓名",prop:"realName"},{default:u(()=>[i(_e,{modelValue:we.realName,"onUpdate:modelValue":a[2]||(a[2]=e=>we.realName=e),placeholder:"请输入真实姓名","prefix-icon":"UserFilled",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),i(ke,{gutter:24},{default:u(()=>[i(Ue,{span:12},{default:u(()=>[i(ze,{label:"邮箱地址",prop:"email"},{default:u(()=>[i(_e,{modelValue:we.email,"onUpdate:modelValue":a[3]||(a[3]=e=>we.email=e),placeholder:"请输入邮箱地址","prefix-icon":"Message",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),i(Ue,{span:12},{default:u(()=>[i(ze,{label:"手机号码",prop:"phone"},{default:u(()=>[i(_e,{modelValue:we.phone,"onUpdate:modelValue":a[4]||(a[4]=e=>we.phone=e),placeholder:"请输入手机号码","prefix-icon":"Phone",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),i(Pe,{class:"form-card",shadow:"never"},{header:u(()=>[t("div",ae,[t("div",le,[i(l,null,{default:u(()=>[i(c(S))]),_:1}),a[19]||(a[19]=t("span",null,"角色权限",-1))]),a[20]||(a[20]=t("div",{class:"card-subtitle"},"配置用户的角色和权限范围",-1))])]),default:u(()=>[i(je,{model:we,rules:Ve,"label-width":"100px",size:"large",class:"modern-form"},{default:u(()=>[i(ze,{label:"用户角色",prop:"role"},{default:u(()=>[t("div",se,[i(Ne,{modelValue:we.role,"onUpdate:modelValue":a[5]||(a[5]=e=>we.role=e),class:"role-group"},{default:u(()=>[(d(),o(m,null,p(he,e=>t("div",{class:"role-option",key:e.value},[i(qe,{label:e.value,class:"role-radio"},{default:u(()=>[t("div",re,[t("div",oe,[i(l,{size:20,color:e.color},{default:u(()=>[(d(),f(v(e.icon)))]),_:2},1032,["color"])]),t("div",de,[t("div",te,C(e.label),1),t("div",ie,C(e.description),1)])])]),_:2},1032,["label"])])),64))]),_:1},8,["modelValue"])])]),_:1}),i(ze,{label:"所属部门",prop:"department"},{default:u(()=>[i(Ce,{modelValue:we.department,"onUpdate:modelValue":a[6]||(a[6]=e=>we.department=e),placeholder:"请选择所属部门",style:{width:"100%"},clearable:""},{default:u(()=>[i(Ae,{label:"技术部",value:"tech"}),i(Ae,{label:"运营部",value:"operation"}),i(Ae,{label:"市场部",value:"marketing"}),i(Ae,{label:"客服部",value:"service"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),i(Pe,{class:"form-card",shadow:"never"},{header:u(()=>[t("div",ue,[t("div",ce,[i(l,null,{default:u(()=>[i($e)]),_:1}),a[21]||(a[21]=t("span",null,"账户安全",-1))]),a[22]||(a[22]=t("div",{class:"card-subtitle"},"设置登录密码和账户状态",-1))])]),default:u(()=>[i(je,{model:we,rules:Ve,"label-width":"100px",size:"large",class:"modern-form"},{default:u(()=>[i(ke,{gutter:24},{default:u(()=>[i(Ue,{span:12},{default:u(()=>[i(ze,{label:"登录密码",prop:"password"},{default:u(()=>[i(_e,{modelValue:we.password,"onUpdate:modelValue":a[7]||(a[7]=e=>we.password=e),type:"password",placeholder:"请输入登录密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),i(Ue,{span:12},{default:u(()=>[i(ze,{label:"确认密码",prop:"confirmPassword"},{default:u(()=>[i(_e,{modelValue:we.confirmPassword,"onUpdate:modelValue":a[8]||(a[8]=e=>we.confirmPassword=e),type:"password",placeholder:"请再次输入密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),i(ze,{label:"账户状态",prop:"status"},{default:u(()=>[i(Ne,{modelValue:we.status,"onUpdate:modelValue":a[9]||(a[9]=e=>we.status=e),class:"status-group"},{default:u(()=>[i(qe,{label:"active",class:"status-radio active"},{default:u(()=>[t("div",ne,[i(l,{color:"#67c23a"},{default:u(()=>[i(c(Z))]),_:1}),a[23]||(a[23]=t("span",null,"启用",-1)),a[24]||(a[24]=t("small",null,"用户可以正常登录使用",-1))])]),_:1}),i(qe,{label:"inactive",class:"status-radio inactive"},{default:u(()=>[t("div",me,[i(l,{color:"#f56c6c"},{default:u(()=>[i(c(L))]),_:1}),a[25]||(a[25]=t("span",null,"禁用",-1)),a[26]||(a[26]=t("small",null,"用户无法登录系统",-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),i(Pe,{class:"form-card",shadow:"never"},{header:u(()=>[t("div",pe,[t("div",fe,[i(l,null,{default:u(()=>[i(c(M))]),_:1}),a[27]||(a[27]=t("span",null,"附加信息",-1))]),a[28]||(a[28]=t("div",{class:"card-subtitle"},"用户的其他相关信息",-1))])]),default:u(()=>[i(je,{model:we,"label-width":"100px",size:"large",class:"modern-form"},{default:u(()=>[i(ze,{label:"备注信息"},{default:u(()=>[i(_e,{modelValue:we.remark,"onUpdate:modelValue":a[10]||(a[10]=e=>we.remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息，如用户的特殊说明、联系方式等",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),t("div",ve,[i(s,{onClick:a[11]||(a[11]=a=>e.$router.go(-1)),size:"large",class:"action-button cancel"},{default:u(()=>[i(l,null,{default:u(()=>[i(c(E))]),_:1}),a[29]||(a[29]=n(" 取消 ",-1))]),_:1,__:[29]}),i(s,{onClick:xe,size:"large",class:"action-button reset"},{default:u(()=>[i(l,null,{default:u(()=>[i(c(G))]),_:1}),a[30]||(a[30]=n(" 重置 ",-1))]),_:1,__:[30]}),i(s,{type:"primary",onClick:ye,loading:be.value,size:"large",class:"action-button submit"},{default:u(()=>[i(l,null,{default:u(()=>[i(c(w))]),_:1}),a[31]||(a[31]=n(" 保存用户 ",-1))]),_:1,__:[31]},8,["loading"])])])])])}}},[["__scopeId","data-v-4edd18f5"]]);export{_e as default};
