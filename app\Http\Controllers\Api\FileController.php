<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

/**
 * 文件管理控制器
 * 处理文件上传、管理、处理等功能
 */
class FileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * 单文件上传
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|max:10240', // 10MB
                'type' => 'sometimes|in:image,document,video,audio',
                'folder' => 'sometimes|string|max:100',
                'resize' => 'sometimes|boolean',
                'width' => 'sometimes|integer|min:1|max:2000',
                'height' => 'sometimes|integer|min:1|max:2000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $type = $request->input('type', $this->detectFileType($file));
            $folder = $request->input('folder', $type . 's');
            
            // 验证文件类型
            if (!$this->validateFileType($file, $type)) {
                return response()->json([
                    'success' => false,
                    'message' => '不支持的文件类型'
                ], 422);
            }

            // 生成文件名
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = $this->generateFileName($originalName, $extension);
            
            // 存储路径
            $storagePath = $folder . '/' . date('Y/m/d');
            $fullPath = $storagePath . '/' . $fileName;
            
            // 存储文件
            if ($type === 'image' && $request->boolean('resize')) {
                $this->uploadAndResizeImage($file, $fullPath, $request);
            } else {
                Storage::disk('public')->putFileAs($storagePath, $file, $fileName);
            }
            
            // 记录到数据库
            $fileRecord = FileUpload::create([
                'user_id' => auth()->id(),
                'original_name' => $originalName,
                'file_name' => $fileName,
                'file_path' => $fullPath,
                'file_size' => $file->getSize(),
                'file_type' => $type,
                'mime_type' => $file->getMimeType(),
                'extension' => $extension,
                'folder' => $folder,
                'url' => Storage::disk('public')->url($fullPath),
            ]);

            return response()->json([
                'success' => true,
                'data' => $fileRecord,
                'message' => '文件上传成功'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '文件上传失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 多文件上传
     */
    public function uploadMultiple(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'files' => 'required|array|max:10',
                'files.*' => 'file|max:10240',
                'type' => 'sometimes|in:image,document,video,audio',
                'folder' => 'sometimes|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            $files = $request->file('files');
            $uploadedFiles = [];
            $errors = [];

            foreach ($files as $index => $file) {
                try {
                    $type = $request->input('type', $this->detectFileType($file));
                    $folder = $request->input('folder', $type . 's');
                    
                    if (!$this->validateFileType($file, $type)) {
                        $errors[] = "文件 {$index}: 不支持的文件类型";
                        continue;
                    }

                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $fileName = $this->generateFileName($originalName, $extension);
                    
                    $storagePath = $folder . '/' . date('Y/m/d');
                    $fullPath = $storagePath . '/' . $fileName;
                    
                    Storage::disk('public')->putFileAs($storagePath, $file, $fileName);
                    
                    $fileRecord = FileUpload::create([
                        'user_id' => auth()->id(),
                        'original_name' => $originalName,
                        'file_name' => $fileName,
                        'file_path' => $fullPath,
                        'file_size' => $file->getSize(),
                        'file_type' => $type,
                        'mime_type' => $file->getMimeType(),
                        'extension' => $extension,
                        'folder' => $folder,
                        'url' => Storage::disk('public')->url($fullPath),
                    ]);

                    $uploadedFiles[] = $fileRecord;

                } catch (\Exception $e) {
                    $errors[] = "文件 {$index}: " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => count($uploadedFiles) > 0,
                'data' => [
                    'uploaded_files' => $uploadedFiles,
                    'uploaded_count' => count($uploadedFiles),
                    'total_count' => count($files),
                    'errors' => $errors
                ],
                'message' => count($uploadedFiles) > 0 ? '文件上传完成' : '文件上传失败'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量上传失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取文件列表
     */
    public function getFiles(Request $request): JsonResponse
    {
        try {
            $query = FileUpload::with('user:id,username,nickname');
            
            // 用户筛选
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            } elseif (!auth()->user()->hasRole('admin')) {
                // 非管理员只能看自己的文件
                $query->where('user_id', auth()->id());
            }
            
            // 文件类型筛选
            if ($request->filled('file_type')) {
                $query->where('file_type', $request->file_type);
            }
            
            // 文件夹筛选
            if ($request->filled('folder')) {
                $query->where('folder', $request->folder);
            }
            
            // 时间范围筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }
            
            // 关键词搜索
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('original_name', 'like', "%{$search}%")
                      ->orWhere('file_name', 'like', "%{$search}%");
                });
            }
            
            $files = $query->orderBy('created_at', 'desc')
                          ->paginate($request->input('per_page', 20));
            
            return response()->json([
                'success' => true,
                'data' => $files,
                'message' => '文件列表获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取文件列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取文件信息
     */
    public function getFileInfo($id): JsonResponse
    {
        try {
            $file = FileUpload::with('user:id,username,nickname')->findOrFail($id);
            
            // 权限检查
            if (!auth()->user()->hasRole('admin') && $file->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限访问此文件'
                ], 403);
            }
            
            // 检查文件是否存在
            $exists = Storage::disk('public')->exists($file->file_path);
            
            $fileInfo = $file->toArray();
            $fileInfo['exists'] = $exists;
            
            if ($exists && $file->file_type === 'image') {
                // 获取图片尺寸信息
                $fullPath = Storage::disk('public')->path($file->file_path);
                if (file_exists($fullPath)) {
                    $imageInfo = getimagesize($fullPath);
                    if ($imageInfo) {
                        $fileInfo['width'] = $imageInfo[0];
                        $fileInfo['height'] = $imageInfo[1];
                    }
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $fileInfo,
                'message' => '文件信息获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取文件信息失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除文件
     */
    public function deleteFile($id): JsonResponse
    {
        try {
            $file = FileUpload::findOrFail($id);
            
            // 权限检查
            if (!auth()->user()->hasRole('admin') && $file->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限删除此文件'
                ], 403);
            }
            
            // 删除物理文件
            if (Storage::disk('public')->exists($file->file_path)) {
                Storage::disk('public')->delete($file->file_path);
            }
            
            // 删除数据库记录
            $file->delete();
            
            return response()->json([
                'success' => true,
                'message' => '文件删除成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '文件删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量删除文件
     */
    public function batchDelete(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'file_ids' => 'required|array',
                'file_ids.*' => 'integer|exists:file_uploads,id'
            ]);

            $fileIds = $validated['file_ids'];
            $query = FileUpload::whereIn('id', $fileIds);
            
            // 非管理员只能删除自己的文件
            if (!auth()->user()->hasRole('admin')) {
                $query->where('user_id', auth()->id());
            }
            
            $files = $query->get();
            $deletedCount = 0;
            $errors = [];

            foreach ($files as $file) {
                try {
                    // 删除物理文件
                    if (Storage::disk('public')->exists($file->file_path)) {
                        Storage::disk('public')->delete($file->file_path);
                    }
                    
                    // 删除数据库记录
                    $file->delete();
                    $deletedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = "文件 {$file->original_name}: " . $e->getMessage();
                }
            }

            return response()->json([
                'success' => $deletedCount > 0,
                'data' => [
                    'deleted_count' => $deletedCount,
                    'total_count' => count($fileIds),
                    'errors' => $errors
                ],
                'message' => $deletedCount > 0 ? '批量删除完成' : '批量删除失败'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量删除失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 图片缩放
     */
    public function resizeImage(Request $request, $id): JsonResponse
    {
        try {
            $file = FileUpload::findOrFail($id);
            
            // 权限检查
            if (!auth()->user()->hasRole('admin') && $file->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限处理此文件'
                ], 403);
            }
            
            if ($file->file_type !== 'image') {
                return response()->json([
                    'success' => false,
                    'message' => '只能处理图片文件'
                ], 422);
            }
            
            $validated = $request->validate([
                'width' => 'required|integer|min:1|max:2000',
                'height' => 'required|integer|min:1|max:2000',
                'keep_aspect_ratio' => 'sometimes|boolean',
                'create_new' => 'sometimes|boolean'
            ]);

            $width = $validated['width'];
            $height = $validated['height'];
            $keepAspectRatio = $validated['keep_aspect_ratio'] ?? true;
            $createNew = $validated['create_new'] ?? false;
            
            $sourcePath = Storage::disk('public')->path($file->file_path);
            
            if (!file_exists($sourcePath)) {
                return response()->json([
                    'success' => false,
                    'message' => '源文件不存在'
                ], 404);
            }
            
            // 处理图片
            $image = Image::make($sourcePath);
            
            if ($keepAspectRatio) {
                $image->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            } else {
                $image->resize($width, $height);
            }
            
            if ($createNew) {
                // 创建新文件
                $newFileName = pathinfo($file->file_name, PATHINFO_FILENAME) . 
                              "_{$width}x{$height}." . 
                              pathinfo($file->file_name, PATHINFO_EXTENSION);
                $newPath = dirname($file->file_path) . '/' . $newFileName;
                $fullNewPath = Storage::disk('public')->path($newPath);
                
                $image->save($fullNewPath);
                
                // 创建新的数据库记录
                $newFile = FileUpload::create([
                    'user_id' => $file->user_id,
                    'original_name' => pathinfo($file->original_name, PATHINFO_FILENAME) . 
                                     "_{$width}x{$height}." . 
                                     pathinfo($file->original_name, PATHINFO_EXTENSION),
                    'file_name' => $newFileName,
                    'file_path' => $newPath,
                    'file_size' => filesize($fullNewPath),
                    'file_type' => 'image',
                    'mime_type' => $file->mime_type,
                    'extension' => $file->extension,
                    'folder' => $file->folder,
                    'url' => Storage::disk('public')->url($newPath),
                ]);
                
                return response()->json([
                    'success' => true,
                    'data' => $newFile,
                    'message' => '图片缩放成功，已创建新文件'
                ]);
                
            } else {
                // 覆盖原文件
                $image->save($sourcePath);
                
                // 更新文件大小
                $file->update([
                    'file_size' => filesize($sourcePath)
                ]);
                
                return response()->json([
                    'success' => true,
                    'data' => $file->fresh(),
                    'message' => '图片缩放成功'
                ]);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '图片缩放失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 图片裁剪
     */
    public function cropImage(Request $request, $id): JsonResponse
    {
        try {
            $file = FileUpload::findOrFail($id);
            
            // 权限检查
            if (!auth()->user()->hasRole('admin') && $file->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限处理此文件'
                ], 403);
            }
            
            if ($file->file_type !== 'image') {
                return response()->json([
                    'success' => false,
                    'message' => '只能处理图片文件'
                ], 422);
            }
            
            $validated = $request->validate([
                'x' => 'required|integer|min:0',
                'y' => 'required|integer|min:0',
                'width' => 'required|integer|min:1',
                'height' => 'required|integer|min:1',
                'create_new' => 'sometimes|boolean'
            ]);

            $x = $validated['x'];
            $y = $validated['y'];
            $width = $validated['width'];
            $height = $validated['height'];
            $createNew = $validated['create_new'] ?? false;
            
            $sourcePath = Storage::disk('public')->path($file->file_path);
            
            if (!file_exists($sourcePath)) {
                return response()->json([
                    'success' => false,
                    'message' => '源文件不存在'
                ], 404);
            }
            
            // 裁剪图片
            $image = Image::make($sourcePath);
            $image->crop($width, $height, $x, $y);
            
            if ($createNew) {
                // 创建新文件
                $newFileName = pathinfo($file->file_name, PATHINFO_FILENAME) . 
                              "_crop_{$width}x{$height}." . 
                              pathinfo($file->file_name, PATHINFO_EXTENSION);
                $newPath = dirname($file->file_path) . '/' . $newFileName;
                $fullNewPath = Storage::disk('public')->path($newPath);
                
                $image->save($fullNewPath);
                
                // 创建新的数据库记录
                $newFile = FileUpload::create([
                    'user_id' => $file->user_id,
                    'original_name' => pathinfo($file->original_name, PATHINFO_FILENAME) . 
                                     "_crop_{$width}x{$height}." . 
                                     pathinfo($file->original_name, PATHINFO_EXTENSION),
                    'file_name' => $newFileName,
                    'file_path' => $newPath,
                    'file_size' => filesize($fullNewPath),
                    'file_type' => 'image',
                    'mime_type' => $file->mime_type,
                    'extension' => $file->extension,
                    'folder' => $file->folder,
                    'url' => Storage::disk('public')->url($newPath),
                ]);
                
                return response()->json([
                    'success' => true,
                    'data' => $newFile,
                    'message' => '图片裁剪成功，已创建新文件'
                ]);
                
            } else {
                // 覆盖原文件
                $image->save($sourcePath);
                
                // 更新文件大小
                $file->update([
                    'file_size' => filesize($sourcePath)
                ]);
                
                return response()->json([
                    'success' => true,
                    'data' => $file->fresh(),
                    'message' => '图片裁剪成功'
                ]);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '图片裁剪失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取文件统计
     */
    public function getFileStats(): JsonResponse
    {
        try {
            $query = FileUpload::query();
            
            // 非管理员只能看自己的统计
            if (!auth()->user()->hasRole('admin')) {
                $query->where('user_id', auth()->id());
            }
            
            $stats = [
                'total_files' => $query->count(),
                'total_size' => $query->sum('file_size'),
                'by_type' => $query->selectRaw('file_type, COUNT(*) as count, SUM(file_size) as size')
                                  ->groupBy('file_type')
                                  ->get(),
                'by_user' => auth()->user()->hasRole('admin') ? 
                           FileUpload::with('user:id,username,nickname')
                                    ->selectRaw('user_id, COUNT(*) as count, SUM(file_size) as size')
                                    ->groupBy('user_id')
                                    ->orderBy('count', 'desc')
                                    ->limit(10)
                                    ->get() : null,
                'recent_uploads' => $query->orderBy('created_at', 'desc')
                                         ->limit(10)
                                         ->get(['id', 'original_name', 'file_size', 'created_at']),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => '文件统计获取成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取文件统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // 私有辅助方法

    /**
     * 检测文件类型
     */
    private function detectFileType($file): string
    {
        $mimeType = $file->getMimeType();
        
        if (strpos($mimeType, 'image/') === 0) {
            return 'image';
        } elseif (strpos($mimeType, 'video/') === 0) {
            return 'video';
        } elseif (strpos($mimeType, 'audio/') === 0) {
            return 'audio';
        } else {
            return 'document';
        }
    }

    /**
     * 验证文件类型
     */
    private function validateFileType($file, string $type): bool
    {
        $allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
            'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
            'audio' => ['mp3', 'wav', 'flac', 'aac']
        ];
        
        $extension = strtolower($file->getClientOriginalExtension());
        
        return isset($allowedTypes[$type]) && in_array($extension, $allowedTypes[$type]);
    }

    /**
     * 生成文件名
     */
    private function generateFileName(string $originalName, string $extension): string
    {
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);
        $safeName = Str::slug($baseName);
        $timestamp = now()->format('YmdHis');
        $random = Str::random(6);
        
        return "{$safeName}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * 上传并缩放图片
     */
    private function uploadAndResizeImage($file, string $path, Request $request): void
    {
        $width = $request->input('width', 800);
        $height = $request->input('height', 600);
        
        $image = Image::make($file);
        $image->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });
        
        $fullPath = Storage::disk('public')->path($path);
        $directory = dirname($fullPath);
        
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $image->save($fullPath);
    }
}