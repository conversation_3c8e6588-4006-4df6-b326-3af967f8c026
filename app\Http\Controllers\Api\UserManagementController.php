<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Models\DistributionGroup;
use Rap2hpoutre\FastExcel\FastExcel;

class UserManagementController extends Controller
{
    /**
     * 管理员创建用户
     */
    public function createUser(Request $request)
    {
        $request->validate([
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'phone' => 'nullable|string|max:20|unique:users',
            'password' => 'required|string|min:6',
            'role' => 'required|in:user,distributor,substation,admin',
            'name' => 'required|string|max:100',
            'distribution_group_id' => 'nullable|exists:distribution_groups,id',
            
            // 分销员专用字段
            'distributor_account' => 'nullable|string|max:50|unique:users,distributor_account',
            'distributor_phone' => 'nullable|string|max:20',
            'distributor_alipay' => 'nullable|string|max:100',
            'distributor_wechat' => 'nullable|string|max:100',
            'distributor_real_name' => 'nullable|string|max:50',
            'available_templates' => 'nullable|array',
            'parent_distributor_id' => 'nullable|exists:users,id',
        ]);

        DB::beginTransaction();
        try {
            $userData = [
                'username' => $request->username,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'name' => $request->name,
                'status' => 1,
                'distribution_group_id' => $request->distribution_group_id,
                'created_at' => now(),
            ];

            // 如果是分销员角色，添加分销员字段
            if ($request->role === 'distributor') {
                $userData = array_merge($userData, [
                    'is_distributor' => true,
                    'distributor_account' => $request->distributor_account ?: $request->username,
                    'distributor_password' => Hash::make($request->password),
                    'distributor_status' => 1,
                    'distributor_balance' => 0.00,
                    'distributor_phone' => $request->distributor_phone ?: $request->phone,
                    'distributor_alipay' => $request->distributor_alipay,
                    'distributor_wechat' => $request->distributor_wechat,
                    'distributor_real_name' => $request->distributor_real_name ?: $request->name,
                    'distributor_created_at' => now(),
                    'available_templates' => $request->available_templates,
                    'parent_distributor_id' => $request->parent_distributor_id,
                ]);
            }

            $user = User::create($userData);

            // 分配角色权限
            $user->assignRole($request->role);

            DB::commit();

            return response()->json([
                'message' => '用户创建成功',
                'user' => $user->load('distributionGroup')
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => '用户创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 分销员创建下级用户
     */
    public function createSubUser(Request $request)
    {
        $distributor = $request->user();
        
        if (!$distributor->is_distributor) {
            return response()->json(['error' => '无权限创建用户'], 403);
        }

        $request->validate([
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'phone' => 'nullable|string|max:20|unique:users',
            'password' => 'required|string|min:6',
            'name' => 'required|string|max:100',
            'role' => 'required|in:user,distributor',
        ]);

        DB::beginTransaction();
        try {
            $userData = [
                'username' => $request->username,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'name' => $request->name,
                'status' => 1,
                'parent_id' => $distributor->id,
                'distribution_group_id' => $distributor->distribution_group_id,
                'created_at' => now(),
            ];

            // 如果创建下级分销员
            if ($request->role === 'distributor') {
                $userData = array_merge($userData, [
                    'is_distributor' => true,
                    'distributor_account' => $request->username . '_sub',
                    'distributor_password' => Hash::make($request->password),
                    'distributor_status' => 1,
                    'distributor_balance' => 0.00,
                    'distributor_phone' => $request->phone,
                    'distributor_real_name' => $request->name,
                    'distributor_created_at' => now(),
                    'parent_distributor_id' => $distributor->id,
                    // 继承上级的部分模板权限
                    'available_templates' => array_slice($distributor->available_templates ?? [], 0, 3),
                ]);
            }

            $user = User::create($userData);
            $user->assignRole($request->role);

            DB::commit();

            return response()->json([
                'message' => '用户创建成功',
                'user' => $user
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => '用户创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 批量创建用户
     */
    public function batchCreateUsers(Request $request)
    {
        $request->validate([
            'users' => 'required|array|min:1|max:100',
            'users.*.username' => 'required|string|max:50|distinct',
            'users.*.email' => 'required|email|distinct',
            'users.*.password' => 'required|string|min:6',
            'users.*.role' => 'required|in:user,distributor',
            'users.*.name' => 'required|string|max:100',
        ]);

        $creator = $request->user();
        $successCount = 0;
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($request->users as $index => $userData) {
                try {
                    // 检查用户名和邮箱唯一性
                    if (User::where('username', $userData['username'])->exists()) {
                        $errors[] = "第" . ($index + 1) . "行：用户名已存在";
                        continue;
                    }
                    if (User::where('email', $userData['email'])->exists()) {
                        $errors[] = "第" . ($index + 1) . "行：邮箱已存在";
                        continue;
                    }

                    $newUserData = [
                        'username' => $userData['username'],
                        'email' => $userData['email'],
                        'password' => Hash::make($userData['password']),
                        'role' => $userData['role'],
                        'name' => $userData['name'],
                        'status' => 1,
                        'parent_id' => $creator->is_distributor ? $creator->id : null,
                        'distribution_group_id' => $creator->distribution_group_id,
                        'created_at' => now(),
                    ];

                    // 如果是分销员
                    if ($userData['role'] === 'distributor') {
                        $newUserData = array_merge($newUserData, [
                            'is_distributor' => true,
                            'distributor_account' => $userData['username'],
                            'distributor_password' => Hash::make($userData['password']),
                            'distributor_status' => 1,
                            'distributor_balance' => 0.00,
                            'distributor_real_name' => $userData['name'],
                            'distributor_created_at' => now(),
                            'parent_distributor_id' => $creator->is_distributor ? $creator->id : null,
                        ]);
                    }

                    $user = User::create($newUserData);
                    $user->assignRole($userData['role']);
                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = "第" . ($index + 1) . "行：" . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'message' => "批量创建完成，成功 {$successCount} 个用户",
                'success_count' => $successCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => '批量创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取用户列表
     */
    public function getUserList(Request $request)
    {
        $query = User::with(['distributionGroup', 'parent', 'parentDistributor']);

        // 搜索过滤
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('username', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('name', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        // 角色过滤
        if ($request->role) {
            $query->where('role', $request->role);
        }

        // 状态过滤
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // 分销组过滤
        if ($request->distribution_group_id) {
            $query->where('distribution_group_id', $request->distribution_group_id);
        }

        // 如果是分销员，只能看到自己创建的用户
        $currentUser = $request->user();
        if ($currentUser->is_distributor && !$currentUser->isAdmin()) {
            $query->where('parent_id', $currentUser->id);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json($users);
    }

    /**
     * 更新用户信息
     */
    public function updateUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $currentUser = $request->user();

        // 权限检查
        if (!$currentUser->isAdmin() && $currentUser->id !== $user->parent_id) {
            return response()->json(['error' => '无权限修改此用户'], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:100',
            'email' => 'sometimes|required|email|unique:users,email,' . $id,
            'phone' => 'sometimes|nullable|string|max:20|unique:users,phone,' . $id,
            'status' => 'sometimes|required|in:1,2',
            'distribution_group_id' => 'sometimes|nullable|exists:distribution_groups,id',
            
            // 分销员字段
            'distributor_status' => 'sometimes|required|in:1,2',
            'distributor_balance' => 'sometimes|numeric|min:0',
            'distributor_alipay' => 'sometimes|nullable|string|max:100',
            'distributor_wechat' => 'sometimes|nullable|string|max:100',
            'available_templates' => 'sometimes|nullable|array',
        ]);

        $updateData = $request->only([
            'name', 'email', 'phone', 'status', 'distribution_group_id'
        ]);

        // 如果是分销员，更新分销员字段
        if ($user->is_distributor && $currentUser->isAdmin()) {
            $distributorData = $request->only([
                'distributor_status', 'distributor_balance', 
                'distributor_alipay', 'distributor_wechat', 'available_templates'
            ]);
            $updateData = array_merge($updateData, $distributorData);
        }

        $user->update($updateData);

        return response()->json([
            'message' => '用户信息更新成功',
            'user' => $user->load('distributionGroup')
        ]);
    }

    /**
     * 重置用户密码
     */
    public function resetPassword(Request $request, $id)
    {
        $request->validate([
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        $user = User::findOrFail($id);
        $currentUser = $request->user();

        // 权限检查
        if (!$currentUser->isAdmin() && $currentUser->id !== $user->parent_id) {
            return response()->json(['error' => '无权限重置此用户密码'], 403);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        // 如果是分销员，同时更新分销员密码
        if ($user->is_distributor) {
            $user->update([
                'distributor_password' => Hash::make($request->new_password)
            ]);
        }

        return response()->json(['message' => '密码重置成功']);
    }

    /**
     * 删除用户
     */
    public function deleteUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $currentUser = $request->user();

        // 权限检查
        if (!$currentUser->isAdmin() && $currentUser->id !== $user->parent_id) {
            return response()->json(['error' => '无权限删除此用户'], 403);
        }

        // 检查是否有关联数据
        if ($user->orders()->count() > 0 || $user->wechatGroups()->count() > 0) {
            return response()->json(['error' => '用户有关联数据，无法删除'], 400);
        }

        $user->delete();

        return response()->json(['message' => '用户删除成功']);
    }

    /**
     * 获取用户统计数据 (前端调用: /api/v1/admin/users/stats)
     */
    public function getUserStats(Request $request)
    {
        $user = $request->user();
        $cacheKey = 'user_stats_' . $user->id . '_' . $user->role;
        
        $stats = Cache::remember($cacheKey, 300, function () use ($user) {
            $query = User::query();
            
            // 根据用户角色过滤数据
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            } elseif ($user->hasRole('distributor')) {
                $query->where('parent_id', $user->id);
            } elseif (!$user->hasRole('admin')) {
                // 普通用户只能看到自己的统计
                return [
                    'total_users' => 1,
                    'active_users' => $user->status == User::STATUS_ACTIVE ? 1 : 0,
                    'distributors' => 0,
                    'agents' => 0,
                    'today_users' => 0,
                    'this_month_users' => 0,
                ];
            }
            
            $totalUsers = $query->count();
            $activeUsers = (clone $query)->where('status', User::STATUS_ACTIVE)->count();
            $todayUsers = (clone $query)->whereDate('created_at', today())->count();
            $thisMonthUsers = (clone $query)->whereMonth('created_at', now()->month)->count();
            
            // 按角色统计
            $distributors = (clone $query)->where('role', 'distributor')->count();
            $agents = (clone $query)->where('role', 'agent')->count();
            $substations = (clone $query)->where('role', 'substation')->count();
            
            return [
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'distributors' => $distributors,
                'agents' => $agents,
                'substations' => $substations,
                'today_users' => $todayUsers,
                'this_month_users' => $thisMonthUsers,
                'disabled_users' => (clone $query)->where('status', User::STATUS_DISABLED)->count(),
                'pending_users' => (clone $query)->where('status', User::STATUS_PENDING)->count(),
                'avg_balance' => (clone $query)->avg('balance') ?: 0,
                'total_balance' => (clone $query)->sum('balance') ?: 0,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 批量更新用户状态 (前端调用: /api/v1/admin/users/batch-status)
     */
    public function batchUpdateStatus(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'status' => 'required|in:active,disabled,pending',
        ]);

        $currentUser = $request->user();
        
        if (!$currentUser->hasRole('admin')) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        try {
            $statusMap = [
                'active' => User::STATUS_ACTIVE,
                'disabled' => User::STATUS_DISABLED,
                'pending' => User::STATUS_PENDING,
            ];

            $updatedCount = User::whereIn('id', $request->user_ids)
                               ->update(['status' => $statusMap[$request->status]]);

            return response()->json([
                'success' => true,
                'message' => "成功更新 {$updatedCount} 个用户状态",
                'updated_count' => $updatedCount,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量更新失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 导出用户数据 (前端调用: /api/v1/admin/users/export)
     */
    public function exportUsers(Request $request)
    {
        $currentUser = $request->user();
        
        if (!$currentUser->hasRole('admin')) {
            return response()->json(['success' => false, 'message' => '权限不足'], 403);
        }

        try {
            $query = User::with(['distributionGroup:id,name']);
            
            // 应用筛选条件
            if ($request->role) {
                $query->where('role', $request->role);
            }
            if ($request->status) {
                $query->where('status', $request->status);
            }
            if ($request->keyword) {
                $query->where(function ($q) use ($request) {
                    $q->where('username', 'like', '%' . $request->keyword . '%')
                      ->orWhere('email', 'like', '%' . $request->keyword . '%')
                      ->orWhere('name', 'like', '%' . $request->keyword . '%');
                });
            }

            $users = $query->orderBy('created_at', 'desc')->get();

            $exportData = $users->map(function ($user) {
                return [
                    'ID' => $user->id,
                    '用户名' => $user->username,
                    '姓名' => $user->name,
                    '邮箱' => $user->email,
                    '手机号' => $user->phone,
                    '角色' => $this->getRoleText($user->role),
                    '状态' => $this->getStatusText($user->status),
                    '余额' => $user->balance,
                    '分销组' => $user->distributionGroup->name ?? '',
                    '注册时间' => $user->created_at->format('Y-m-d H:i:s'),
                    '最后登录' => $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : '',
                ];
            });

            $filename = '用户数据导出_' . date('Y-m-d_H-i-s') . '.xlsx';
            
            return (new FastExcel($exportData))->download($filename);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取角色文本
     */
    private function getRoleText($role)
    {
        $roles = [
            'user' => '普通用户',
            'distributor' => '分销员',
            'agent' => '代理商',
            'substation' => '分站管理员',
            'admin' => '管理员',
        ];
        
        return $roles[$role] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statuses = [
            User::STATUS_ACTIVE => '正常',
            User::STATUS_DISABLED => '禁用',
            User::STATUS_PENDING => '待审核',
        ];
        
        return $statuses[$status] ?? '未知';
    }
}