/**
 * 分销员服务层
 * 提供分销员相关的业务逻辑和数据处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { ElMessage, ElNotification } from 'element-plus'
import { customerApi } from '@/api/customer'
import { mockCustomerApi } from '@/api/mock-customer'

/**
 * 分销员服务类
 * 封装分销员相关的业务逻辑，提供统一的数据接口
 */
export class DistributorService {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.apiClient = this.isDevelopment ? mockCustomerApi : customerApi
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  // ==================== 缓存管理 ====================

  /**
   * 获取缓存键
   * @param {string} method - 方法名
   * @param {Array} params - 参数数组
   * @returns {string} 缓存键
   */
  getCacheKey(method, params = []) {
    return `${method}_${JSON.stringify(params)}`
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存数据或null
   */
  getCache(key) {
    const cached = this.cache.get(key)
    if (!cached) return null

    const isExpired = Date.now() - cached.timestamp > this.cacheTimeout
    if (isExpired) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  /**
   * 清除缓存
   * @param {string} pattern - 缓存键模式（可选）
   */
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
    console.log('🗑️ 缓存已清除:', pattern || '全部')
  }

  // ==================== 客户管理 ====================

  /**
   * 获取客户列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.search - 搜索关键词
   * @param {string} params.level - 客户等级
   * @param {string} params.status - 客户状态
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 客户列表数据
   */
  async getCustomers(params = {}, useCache = true) {
    const cacheKey = this.getCacheKey('getCustomers', [params])
    
    // 检查缓存
    if (useCache) {
      const cached = this.getCache(cacheKey)
      if (cached) {
        console.log('📦 使用缓存的客户列表数据')
        return cached
      }
    }

    try {
      console.log('🔄 正在加载客户列表...', params)
      
      const response = await this.apiClient.getCustomers(params)
      
      // 数据验证和处理
      const processedData = this.processCustomerListData(response.data)
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, processedData)
      }
      
      console.log('✅ 客户列表加载完成:', processedData.data.length, '条记录')
      return processedData
      
    } catch (error) {
      console.error('❌ 加载客户列表失败:', error)
      this.handleApiError(error, '加载客户列表失败')
      throw error
    }
  }

  /**
   * 获取客户详情
   * @param {number} customerId - 客户ID
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 客户详情数据
   */
  async getCustomerDetail(customerId, useCache = true) {
    if (!customerId) {
      throw new Error('客户ID不能为空')
    }

    const cacheKey = this.getCacheKey('getCustomerDetail', [customerId])
    
    // 检查缓存
    if (useCache) {
      const cached = this.getCache(cacheKey)
      if (cached) {
        console.log('📦 使用缓存的客户详情数据')
        return cached
      }
    }

    try {
      console.log('🔄 正在加载客户详情...', customerId)
      
      const response = await this.apiClient.getCustomerDetail(customerId)
      
      // 数据验证和处理
      const processedData = this.processCustomerDetailData(response.data)
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, processedData)
      }
      
      console.log('✅ 客户详情加载完成:', processedData.name)
      return processedData
      
    } catch (error) {
      console.error('❌ 加载客户详情失败:', error)
      this.handleApiError(error, '加载客户详情失败')
      throw error
    }
  }

  /**
   * 创建客户
   * @param {Object} customerData - 客户数据
   * @returns {Promise<Object>} 创建结果
   */
  async createCustomer(customerData) {
    try {
      // 数据验证
      this.validateCustomerData(customerData)
      
      console.log('🔄 正在创建客户...', customerData.name)
      
      // 数据预处理
      const processedData = this.preprocessCustomerData(customerData)
      
      const response = await this.apiClient.createCustomer(processedData)
      
      // 清除相关缓存
      this.clearCache('getCustomers')
      
      console.log('✅ 客户创建成功:', response.data.name)
      ElNotification.success({
        title: '创建成功',
        message: `客户 ${response.data.name} 创建成功`
      })
      
      return response.data
      
    } catch (error) {
      console.error('❌ 创建客户失败:', error)
      this.handleApiError(error, '创建客户失败')
      throw error
    }
  }

  /**
   * 更新客户
   * @param {number} customerId - 客户ID
   * @param {Object} customerData - 客户数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateCustomer(customerId, customerData) {
    if (!customerId) {
      throw new Error('客户ID不能为空')
    }

    try {
      // 数据验证
      this.validateCustomerData(customerData, false)
      
      console.log('🔄 正在更新客户...', customerId)
      
      // 数据预处理
      const processedData = this.preprocessCustomerData(customerData)
      
      const response = await this.apiClient.updateCustomer(customerId, processedData)
      
      // 清除相关缓存
      this.clearCache('getCustomers')
      this.clearCache('getCustomerDetail')
      
      console.log('✅ 客户更新成功:', response.data.name)
      ElNotification.success({
        title: '更新成功',
        message: `客户 ${response.data.name} 更新成功`
      })
      
      return response.data
      
    } catch (error) {
      console.error('❌ 更新客户失败:', error)
      this.handleApiError(error, '更新客户失败')
      throw error
    }
  }

  /**
   * 删除客户
   * @param {number} customerId - 客户ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteCustomer(customerId) {
    if (!customerId) {
      throw new Error('客户ID不能为空')
    }

    try {
      console.log('🔄 正在删除客户...', customerId)
      
      await this.apiClient.deleteCustomer(customerId)
      
      // 清除相关缓存
      this.clearCache('getCustomers')
      this.clearCache('getCustomerDetail')
      
      console.log('✅ 客户删除成功')
      ElNotification.success({
        title: '删除成功',
        message: '客户删除成功'
      })
      
      return true
      
    } catch (error) {
      console.error('❌ 删除客户失败:', error)
      this.handleApiError(error, '删除客户失败')
      throw error
    }
  }

  // ==================== 统计数据 ====================

  /**
   * 获取分销员统计数据
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 统计数据
   */
  async getDistributorStats(useCache = true) {
    const cacheKey = this.getCacheKey('getDistributorStats')
    
    // 检查缓存
    if (useCache) {
      const cached = this.getCache(cacheKey)
      if (cached) {
        console.log('📦 使用缓存的统计数据')
        return cached
      }
    }

    try {
      console.log('🔄 正在加载统计数据...')
      
      // 并行加载各项统计数据
      const [customerStats, groupStats, commissionStats, orderStats] = await Promise.all([
        this.getCustomerStats(),
        this.getGroupStats(),
        this.getCommissionStats(),
        this.getOrderStats()
      ])

      const statsData = {
        customers: customerStats,
        groups: groupStats,
        commission: commissionStats,
        orders: orderStats,
        updated_at: new Date().toISOString()
      }
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, statsData)
      }
      
      console.log('✅ 统计数据加载完成')
      return statsData
      
    } catch (error) {
      console.error('❌ 加载统计数据失败:', error)
      this.handleApiError(error, '加载统计数据失败')
      throw error
    }
  }

  // ==================== 数据处理方法 ====================

  /**
   * 处理客户列表数据
   * @param {Object} rawData - 原始数据
   * @returns {Object} 处理后的数据
   * @private
   */
  processCustomerListData(rawData) {
    if (!rawData || !Array.isArray(rawData.data)) {
      throw new Error('客户列表数据格式错误')
    }

    return {
      ...rawData,
      data: rawData.data.map(customer => this.processCustomerItem(customer))
    }
  }

  /**
   * 处理客户详情数据
   * @param {Object} rawData - 原始数据
   * @returns {Object} 处理后的数据
   * @private
   */
  processCustomerDetailData(rawData) {
    if (!rawData || typeof rawData !== 'object') {
      throw new Error('客户详情数据格式错误')
    }

    return this.processCustomerItem(rawData)
  }

  /**
   * 处理单个客户数据项
   * @param {Object} customer - 客户数据
   * @returns {Object} 处理后的客户数据
   * @private
   */
  processCustomerItem(customer) {
    return {
      ...customer,
      // 确保必要字段存在
      id: customer.id || 0,
      name: customer.name || '未知客户',
      phone: customer.phone || '',
      level: customer.level || 'C',
      status: customer.status || 'potential',
      tags: Array.isArray(customer.tags) ? customer.tags : [],
      preferences: customer.preferences || {},
      
      // 格式化日期
      created_at: customer.created_at ? new Date(customer.created_at) : new Date(),
      updated_at: customer.updated_at ? new Date(customer.updated_at) : new Date(),
      
      // 计算字段
      level_text: this.getLevelText(customer.level),
      status_text: this.getStatusText(customer.status),
      days_since_created: this.calculateDaysSince(customer.created_at)
    }
  }

  /**
   * 数据预处理
   * @param {Object} customerData - 客户数据
   * @returns {Object} 预处理后的数据
   * @private
   */
  preprocessCustomerData(customerData) {
    const processed = { ...customerData }
    
    // 清理空字符串
    Object.keys(processed).forEach(key => {
      if (processed[key] === '') {
        processed[key] = null
      }
    })
    
    // 处理标签数组
    if (Array.isArray(processed.tags)) {
      processed.tags = processed.tags.filter(tag => tag && tag.trim())
    }
    
    // 处理偏好设置
    if (processed.preferences && typeof processed.preferences === 'object') {
      processed.preferences = {
        contact_method: processed.preferences.contact_method || null,
        contact_time: processed.preferences.contact_time || null
      }
    }
    
    return processed
  }

  /**
   * 验证客户数据
   * @param {Object} customerData - 客户数据
   * @param {boolean} isCreate - 是否为创建操作
   * @private
   */
  validateCustomerData(customerData, isCreate = true) {
    if (!customerData || typeof customerData !== 'object') {
      throw new Error('客户数据不能为空')
    }

    if (isCreate && !customerData.name) {
      throw new Error('客户姓名不能为空')
    }

    if (customerData.phone && !/^1[3-9]\d{9}$/.test(customerData.phone)) {
      throw new Error('手机号格式不正确')
    }

    if (customerData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email)) {
      throw new Error('邮箱格式不正确')
    }

    const validLevels = ['A', 'B', 'C', 'D']
    if (customerData.level && !validLevels.includes(customerData.level)) {
      throw new Error('客户等级不正确')
    }

    const validStatuses = ['active', 'inactive', 'potential', 'lost']
    if (customerData.status && !validStatuses.includes(customerData.status)) {
      throw new Error('客户状态不正确')
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取等级文本
   * @param {string} level - 等级代码
   * @returns {string} 等级文本
   * @private
   */
  getLevelText(level) {
    const levelMap = {
      'A': 'A级客户',
      'B': 'B级客户',
      'C': 'C级客户',
      'D': 'D级客户'
    }
    return levelMap[level] || 'C级客户'
  }

  /**
   * 获取状态文本
   * @param {string} status - 状态代码
   * @returns {string} 状态文本
   * @private
   */
  getStatusText(status) {
    const statusMap = {
      'active': '活跃',
      'inactive': '不活跃',
      'potential': '潜在',
      'lost': '流失'
    }
    return statusMap[status] || '潜在'
  }

  /**
   * 计算距离创建的天数
   * @param {string|Date} dateString - 日期字符串或日期对象
   * @returns {number} 天数
   * @private
   */
  calculateDaysSince(dateString) {
    if (!dateString) return 0
    
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now - date)
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {string} defaultMessage - 默认错误消息
   * @private
   */
  handleApiError(error, defaultMessage) {
    let message = defaultMessage
    
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status
      const data = error.response.data
      
      if (status === 400) {
        message = data.message || '请求参数错误'
      } else if (status === 401) {
        message = '未授权访问'
      } else if (status === 403) {
        message = '权限不足'
      } else if (status === 404) {
        message = '资源不存在'
      } else if (status === 500) {
        message = '服务器内部错误'
      } else {
        message = data.message || defaultMessage
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      message = error.message || defaultMessage
    }
    
    ElMessage.error(message)
  }

  /**
   * 获取客户统计数据
   * @returns {Promise<Object>} 客户统计数据
   * @private
   */
  async getCustomerStats() {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 200))
    return {
      total: 156,
      active: 142,
      new_this_month: 23,
      trend: 8.2
    }
  }

  /**
   * 获取群组统计数据
   * @returns {Promise<Object>} 群组统计数据
   * @private
   */
  async getGroupStats() {
    await new Promise(resolve => setTimeout(resolve, 250))
    return {
      total: 23,
      active: 21,
      members: 1580,
      trend: 15.6
    }
  }

  /**
   * 获取佣金统计数据
   * @returns {Promise<Object>} 佣金统计数据
   * @private
   */
  async getCommissionStats() {
    await new Promise(resolve => setTimeout(resolve, 180))
    return {
      total: 28650.50,
      this_month: 8650.50,
      pending: 1250.00,
      rate: 15.0,
      trend: 23.4
    }
  }

  /**
   * 获取订单统计数据
   * @returns {Promise<Object>} 订单统计数据
   * @private
   */
  async getOrderStats() {
    await new Promise(resolve => setTimeout(resolve, 220))
    return {
      total: 89,
      amount: 458650.50,
      pending: 12,
      success_rate: 94.2,
      trend: 12.8
    }
  }
}

// 创建服务实例
export const distributorService = new DistributorService()

/**
 * 使用示例：
 * 
 * import { distributorService } from '@/services/DistributorService'
 * 
 * // 获取客户列表
 * const customers = await distributorService.getCustomers({
 *   page: 1,
 *   limit: 20,
 *   search: '张三'
 * })
 * 
 * // 创建客户
 * const newCustomer = await distributorService.createCustomer({
 *   name: '李四',
 *   phone: '13800138002',
 *   level: 'B'
 * })
 * 
 * // 获取统计数据
 * const stats = await distributorService.getDistributorStats()
 */