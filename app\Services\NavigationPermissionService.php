<?php

namespace App\Services;

use App\Models\NavigationMenu;
use App\Models\User;
use App\Models\RoleNavigationPermission;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

/**
 * 导航权限管理服务
 * 
 * 提供细粒度的导航权限控制功能
 */
class NavigationPermissionService
{
    const CACHE_TTL = 3600; // 1小时缓存

    /**
     * 检查用户是否有菜单访问权限
     */
    public function hasMenuPermission(User $user, NavigationMenu $menu): bool
    {
        $cacheKey = "menu_permission:{$user->id}:{$menu->code}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $menu) {
            // 1. 超级管理员拥有所有权限
            if ($user->hasRole('super_admin')) {
                return true;
            }

            // 2. 检查菜单是否需要特定权限
            if (!$menu->permission) {
                return true;
            }

            // 3. 检查用户是否有直接权限
            if ($user->can($menu->permission)) {
                return true;
            }

            // 4. 检查角色级别的导航权限
            return $this->checkRoleNavigationPermission($user, $menu);
        });
    }

    /**
     * 根据权限过滤菜单列表
     */
    public function filterMenusByPermissions(Collection $menus, User $user): Collection
    {
        return $menus->filter(function ($menu) use ($user) {
            $hasPermission = $this->hasMenuPermission($user, $menu);
            
            // 递归过滤子菜单
            if ($hasPermission && $menu->children) {
                $menu->children = $this->filterMenusByPermissions($menu->children, $user);
            }
            
            return $hasPermission;
        });
    }

    /**
     * 获取用户可访问的导航域
     */
    public function getUserAccessibleDomains(User $user): array
    {
        $cacheKey = "user_accessible_domains:{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            if ($user->hasRole('super_admin')) {
                return ['business', 'operation', 'analytics', 'system'];
            }

            $rolePermissions = $this->getUserRolePermissions($user);
            $accessibleDomains = [];

            foreach (['business', 'operation', 'analytics', 'system'] as $domain) {
                if ($this->canAccessDomain($user, $domain, $rolePermissions)) {
                    $accessibleDomains[] = $domain;
                }
            }

            return $accessibleDomains;
        });
    }

    /**
     * 获取用户在指定域中的权限级别
     */
    public function getUserDomainPermissionLevel(User $user, string $domain): string
    {
        $cacheKey = "user_domain_permission:{$user->id}:{$domain}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $domain) {
            if ($user->hasRole('super_admin')) {
                return 'full';
            }

            $rolePermissions = $this->getUserRolePermissions($user);
            $domainAccess = $rolePermissions['domain_access'][$domain] ?? null;

            if (!$domainAccess) {
                return 'none';
            }

            return $domainAccess['level'] ?? 'read';
        });
    }

    /**
     * 批量检查菜单权限
     */
    public function batchCheckMenuPermissions(User $user, array $menuCodes): array
    {
        $results = [];
        
        foreach ($menuCodes as $code) {
            $menu = NavigationMenu::where('code', $code)->first();
            $results[$code] = $menu ? $this->hasMenuPermission($user, $menu) : false;
        }

        return $results;
    }

    /**
     * 创建角色导航权限配置
     */
    public function createRolePermissionConfig(string $role, array $config): RoleNavigationPermission
    {
        return RoleNavigationPermission::updateOrCreate(
            ['role' => $role],
            [
                'allowed_menus' => $config['allowed_menus'] ?? [],
                'denied_menus' => $config['denied_menus'] ?? [],
                'domain_access' => $config['domain_access'] ?? [],
                'feature_permissions' => $config['feature_permissions'] ?? [],
                'is_active' => $config['is_active'] ?? true
            ]
        );
    }

    /**
     * 获取角色的导航权限配置
     */
    public function getRolePermissionConfig(string $role): ?RoleNavigationPermission
    {
        return RoleNavigationPermission::where('role', $role)
            ->where('is_active', true)
            ->first();
    }

    /**
     * 同步权限到Laravel权限系统
     */
    public function syncNavigationPermissions(): void
    {
        $menus = NavigationMenu::whereNotNull('permission')->get();
        
        foreach ($menus as $menu) {
            if (!Permission::where('name', $menu->permission)->exists()) {
                Permission::create([
                    'name' => $menu->permission,
                    'guard_name' => 'web'
                ]);
            }
        }
    }

    /**
     * 为角色分配导航权限
     */
    public function assignNavigationPermissionsToRole(string $roleName, array $permissions): void
    {
        $role = Role::findByName($roleName);
        $permissionObjects = Permission::whereIn('name', $permissions)->get();
        
        $role->syncPermissions($permissionObjects);
        
        // 清除相关缓存
        $this->clearPermissionCache();
    }

    /**
     * 获取权限矩阵
     */
    public function getPermissionMatrix(): array
    {
        $cacheKey = 'navigation_permission_matrix';

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            $roles = Role::all();
            $menus = NavigationMenu::whereNotNull('permission')->get();
            $matrix = [];

            foreach ($roles as $role) {
                $matrix[$role->name] = [];
                $userPermissions = $role->permissions->pluck('name')->toArray();
                
                foreach ($menus as $menu) {
                    $matrix[$role->name][$menu->code] = [
                        'has_permission' => in_array($menu->permission, $userPermissions),
                        'permission_name' => $menu->permission,
                        'menu_name' => $menu->name,
                        'domain' => $menu->domain
                    ];
                }
            }

            return $matrix;
        });
    }

    /**
     * 导航权限审计
     */
    public function auditNavigationPermissions(): array
    {
        return [
            'total_menus' => NavigationMenu::count(),
            'protected_menus' => NavigationMenu::whereNotNull('permission')->count(),
            'unprotected_menus' => NavigationMenu::whereNull('permission')->count(),
            'total_permissions' => Permission::whereIn('name', 
                NavigationMenu::whereNotNull('permission')->pluck('permission')
            )->count(),
            'orphaned_permissions' => Permission::whereNotIn('name',
                NavigationMenu::whereNotNull('permission')->pluck('permission')
            )->where('name', 'like', 'menu:%')->count(),
            'roles_with_navigation' => Role::whereHas('permissions', function ($query) {
                $query->whereIn('name', 
                    NavigationMenu::whereNotNull('permission')->pluck('permission')
                );
            })->count(),
            'permission_distribution' => $this->getPermissionDistribution()
        ];
    }

    /**
     * 清除权限相关缓存
     */
    public function clearPermissionCache(?User $user = null): void
    {
        if ($user) {
            $patterns = [
                "menu_permission:{$user->id}:*",
                "user_accessible_domains:{$user->id}",
                "user_domain_permission:{$user->id}:*",
                "user_role_permissions:{$user->id}"
            ];
        } else {
            $patterns = [
                "menu_permission:*",
                "user_accessible_domains:*", 
                "user_domain_permission:*",
                "user_role_permissions:*",
                "navigation_permission_matrix"
            ];
        }

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    // ========== 私有方法 ==========

    /**
     * 检查角色级别的导航权限
     */
    private function checkRoleNavigationPermission(User $user, NavigationMenu $menu): bool
    {
        $rolePermissions = $this->getUserRolePermissions($user);

        // 检查明确拒绝的菜单
        if (in_array($menu->code, $rolePermissions['denied_menus'] ?? [])) {
            return false;
        }

        // 检查明确允许的菜单
        if (in_array($menu->code, $rolePermissions['allowed_menus'] ?? [])) {
            return true;
        }

        // 检查域级别权限
        $domainAccess = $rolePermissions['domain_access'][$menu->domain] ?? null;
        if ($domainAccess && ($domainAccess['level'] ?? 'none') !== 'none') {
            return true;
        }

        return false;
    }

    /**
     * 获取用户的角色权限配置
     */
    private function getUserRolePermissions(User $user): array
    {
        $cacheKey = "user_role_permissions:{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            $roleNames = $user->getRoleNames()->toArray();
            $permissions = [
                'allowed_menus' => [],
                'denied_menus' => [],
                'domain_access' => [],
                'feature_permissions' => []
            ];

            foreach ($roleNames as $roleName) {
                $roleConfig = $this->getRolePermissionConfig($roleName);
                if ($roleConfig) {
                    $permissions['allowed_menus'] = array_merge(
                        $permissions['allowed_menus'], 
                        $roleConfig->allowed_menus ?? []
                    );
                    $permissions['denied_menus'] = array_merge(
                        $permissions['denied_menus'], 
                        $roleConfig->denied_menus ?? []
                    );
                    $permissions['domain_access'] = array_merge(
                        $permissions['domain_access'], 
                        $roleConfig->domain_access ?? []
                    );
                    $permissions['feature_permissions'] = array_merge(
                        $permissions['feature_permissions'], 
                        $roleConfig->feature_permissions ?? []
                    );
                }
            }

            return $permissions;
        });
    }

    /**
     * 检查是否可以访问指定域
     */
    private function canAccessDomain(User $user, string $domain, array $rolePermissions): bool
    {
        $domainAccess = $rolePermissions['domain_access'][$domain] ?? null;
        
        if (!$domainAccess) {
            return false;
        }

        $level = $domainAccess['level'] ?? 'none';
        return $level !== 'none';
    }

    /**
     * 获取权限分布统计
     */
    private function getPermissionDistribution(): array
    {
        $distribution = [];
        $roles = Role::with('permissions')->get();

        foreach ($roles as $role) {
            $navigationPermissions = $role->permissions->whereIn('name', 
                NavigationMenu::whereNotNull('permission')->pluck('permission')
            );
            
            $distribution[$role->name] = [
                'total_permissions' => $role->permissions->count(),
                'navigation_permissions' => $navigationPermissions->count(),
                'domains' => NavigationMenu::whereIn('permission', $navigationPermissions->pluck('name'))
                    ->distinct('domain')
                    ->pluck('domain')
                    ->toArray()
            ];
        }

        return $distribution;
    }
}