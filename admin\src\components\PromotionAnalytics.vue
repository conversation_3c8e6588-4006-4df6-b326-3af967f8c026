<template>
  <div class="promotion-analytics">
    <el-card class="analytics-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>推广数据统计</span>
          <el-button size="small" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 统计概览 -->
      <div class="stats-overview">
        <div class="stat-item">
          <div class="stat-icon" style="background: #409eff20; color: #409eff;">
            <el-icon><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_views }}</div>
            <div class="stat-label">总浏览量</div>
            <div class="stat-change positive">+12.5%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon" style="background: #67c23a20; color: #67c23a;">
            <el-icon><Mouse /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_clicks }}</div>
            <div class="stat-label">总点击量</div>
            <div class="stat-change positive">+8.3%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon" style="background: #e6a23c20; color: #e6a23c;">
            <el-icon><ShoppingCart /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.conversions }}</div>
            <div class="stat-label">转化数量</div>
            <div class="stat-change positive">+15.2%</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon" style="background: #f56c6c20; color: #f56c6c;">
            <el-icon><Percentage /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.conversion_rate }}%</div>
            <div class="stat-label">转化率</div>
            <div class="stat-change negative">-2.1%</div>
          </div>
        </div>
      </div>

      <!-- 推广来源分析 -->
      <div class="source-analysis">
        <h4>推广来源分析</h4>
        <el-table :data="sourceData" style="width: 100%">
          <el-table-column prop="source" label="来源渠道" width="120" />
          <el-table-column prop="views" label="浏览量" width="100" />
          <el-table-column prop="clicks" label="点击量" width="100" />
          <el-table-column prop="conversions" label="转化数" width="100" />
          <el-table-column label="转化率" width="100">
            <template #default="{ row }">
              {{ ((row.conversions / row.clicks) * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column label="趋势" width="120">
            <template #default="{ row }">
              <el-tag :type="row.trend > 0 ? 'success' : 'danger'" size="small">
                {{ row.trend > 0 ? '+' : '' }}{{ row.trend }}%
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 时间趋势图 -->
      <div class="trend-chart">
        <h4>推广趋势图</h4>
        <div class="chart-container" ref="chartContainer">
          <!-- 这里可以集成 ECharts 或其他图表库 -->
          <div class="chart-placeholder">
            <el-icon size="60"><TrendCharts /></el-icon>
            <p>图表功能开发中...</p>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { TrendCharts, Refresh, View, Mouse, ShoppingCart, Percentage } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)

const stats = reactive({
  total_views: 15420,
  total_clicks: 3280,
  conversions: 156,
  conversion_rate: 4.75
})

const sourceData = ref([
  {
    source: '微信朋友圈',
    views: 8500,
    clicks: 1800,
    conversions: 85,
    trend: 12.5
  },
  {
    source: 'QQ空间',
    views: 3200,
    clicks: 680,
    conversions: 32,
    trend: 8.3
  },
  {
    source: '微博',
    views: 2100,
    clicks: 450,
    conversions: 22,
    trend: -3.2
  },
  {
    source: '直接访问',
    views: 1620,
    clicks: 350,
    conversions: 17,
    trend: 5.8
  }
])

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里应该调用真实的API获取数据
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化数据
  refreshData()
})
</script>

<style lang="scss" scoped>
.promotion-analytics {
  .analytics-card {
    border-radius: 12px;
    
    :deep(.el-card__header) {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
    
    .el-button {
      margin-left: auto;
    }
  }
  
  .stats-overview {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }
  
  .stat-content {
    flex: 1;
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }
  
  .stat-change {
    font-size: 12px;
    font-weight: 500;
    
    &.positive {
      color: #52c41a;
    }
    
    &.negative {
      color: #ff4d4f;
    }
  }
  
  .source-analysis {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
  
  .trend-chart {
    h4 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }
  
  .chart-container {
    height: 300px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
  }
  
  .chart-placeholder {
    text-align: center;
    color: #999;
    
    p {
      margin-top: 10px;
      font-size: 14px;
    }
  }
  
  @media (max-width: 768px) {
    .stats-overview {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
