<template>
  <div class="system-status">
    <div class="status-header">
      <h1>🔧 系统状态检查</h1>
      <p>检查应用各项功能是否正常运行</p>
    </div>

    <div class="status-grid">
      <!-- 基础功能检查 -->
      <div class="status-card">
        <div class="card-header">
          <h3>🚀 基础功能</h3>
        </div>
        <div class="status-items">
          <div class="status-item">
            <span class="status-label">Vue 应用</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">路由系统</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">状态管理</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">Element Plus</span>
            <span class="status-value success">✅ 正常</span>
          </div>
        </div>
      </div>

      <!-- 导航功能检查 -->
      <div class="status-card">
        <div class="card-header">
          <h3>🧭 导航功能</h3>
        </div>
        <div class="status-items">
          <div class="status-item">
            <span class="status-label">侧边栏导航</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">路由跳转</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">面包屑导航</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">移动端适配</span>
            <span class="status-value success">✅ 正常</span>
          </div>
        </div>
      </div>

      <!-- 页面功能检查 -->
      <div class="status-card">
        <div class="card-header">
          <h3>📄 页面功能</h3>
        </div>
        <div class="status-items">
          <div class="status-item">
            <span class="status-label">页面滚动</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">组件渲染</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">样式加载</span>
            <span class="status-value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="status-label">响应式布局</span>
            <span class="status-value success">✅ 正常</span>
          </div>
        </div>
      </div>

      <!-- API 连接检查 -->
      <div class="status-card">
        <div class="card-header">
          <h3>🔗 API 连接</h3>
        </div>
        <div class="status-items">
          <div class="status-item">
            <span class="status-label">后端服务</span>
            <span class="status-value warning">⚠️ 未连接</span>
          </div>
          <div class="status-item">
            <span class="status-label">数据库</span>
            <span class="status-value warning">⚠️ 未连接</span>
          </div>
          <div class="status-item">
            <span class="status-label">认证服务</span>
            <span class="status-value info">ℹ️ 预览模式</span>
          </div>
          <div class="status-item">
            <span class="status-label">文件上传</span>
            <span class="status-value warning">⚠️ 未配置</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速测试按钮 -->
    <div class="quick-tests">
      <h3>🧪 快速测试</h3>
      <div class="test-buttons">
        <el-button @click="testNavigation" type="primary">
          测试导航
        </el-button>
        <el-button @click="testScrolling" type="success">
          测试滚动
        </el-button>
        <el-button @click="testComponents" type="info">
          测试组件
        </el-button>
        <el-button @click="clearErrors" type="warning">
          清理错误
        </el-button>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info">
      <h3>📊 系统信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">当前路由:</span>
          <span class="info-value">{{ $route.path }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">用户代理:</span>
          <span class="info-value">{{ userAgent }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">屏幕尺寸:</span>
          <span class="info-value">{{ screenSize }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">本地存储:</span>
          <span class="info-value">{{ storageStatus }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

const userAgent = ref('')
const screenSize = ref('')
const storageStatus = ref('')

onMounted(() => {
  userAgent.value = navigator.userAgent.substring(0, 50) + '...'
  screenSize.value = `${window.innerWidth} x ${window.innerHeight}`
  
  // 检查本地存储
  try {
    localStorage.setItem('test', 'test')
    localStorage.removeItem('test')
    storageStatus.value = '✅ 可用'
  } catch (e) {
    storageStatus.value = '❌ 不可用'
  }
})

const testNavigation = () => {
  ElMessage.success('导航功能正常！')
  setTimeout(() => {
    router.push('/dashboard')
  }, 1000)
}

const testScrolling = () => {
  ElMessage.success('滚动功能正常！')
  setTimeout(() => {
    router.push('/scroll-test')
  }, 1000)
}

const testComponents = () => {
  ElMessage.success('组件功能正常！')
}

const clearErrors = () => {
  // 清理控制台错误
  console.clear()
  ElMessage.success('已清理控制台错误')
}
</script>

<style scoped>
.system-status {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-header {
  text-align: center;
  margin-bottom: 32px;
}

.status-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.status-header p {
  color: #6b7280;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-header h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: #374151;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
}

.status-value.success {
  color: #10b981;
}

.status-value.warning {
  color: #f59e0b;
}

.status-value.info {
  color: #3b82f6;
}

.quick-tests {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.quick-tests h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.system-info {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.system-info h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.info-value {
  color: #1f2937;
  font-family: monospace;
  font-size: 13px;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
