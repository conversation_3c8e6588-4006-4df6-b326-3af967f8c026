// 主样式文件 - 全局样式和主题系统
@use './navigation.scss';

// CSS变量定义 - 设计系统
:root {
  // 主色调系统
  --primary-color: #3B82F6;
  --primary-light: #60A5FA;
  --primary-dark: #1D4ED8;
  --primary-gradient: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  
  // 辅助色调
  --secondary-color: #8B5CF6;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  --info-color: #06B6D4;
  
  // 中性色调
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  // 背景色系
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;
  --bg-glass: rgba(255, 255, 255, 0.9);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  // 文字颜色
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6B7280;
  --text-muted: #9CA3AF;
  --text-inverse: #FFFFFF;
  
  // 边框和阴影
  --border-color: #E5E7EB;
  --border-color-light: #F3F4F6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  // 间距系统
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  // 圆角系统
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  // 动画时长
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  // 缓动函数
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

// 暗色主题
[data-theme="dark"] {
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --bg-tertiary: #0F172A;
  --bg-glass: rgba(31, 41, 59, 0.9);
  
  --text-primary: #F9FAFB;
  --text-secondary: #E5E7EB;
  --text-tertiary: #D1D5DB;
  --text-muted: #9CA3AF;
  
  --border-color: #374151;
  --border-color-light: #4B5563;
}

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  transition: background-color var(--duration-normal) var(--ease-out);
}

// 通用工具类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

// 现代化卡片样式
.modern-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color-light);
    background: var(--bg-secondary);
    
    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
    
    .card-subtitle {
      font-size: 0.875rem;
      color: var(--text-tertiary);
      margin: 0.25rem 0 0;
    }
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  .card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color-light);
  }
}

// 现代化按钮样式
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  
  // 光效动画
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out);
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }
  
  &.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    
    &:hover {
      background: var(--bg-primary);
      border-color: var(--primary-color);
      color: var(--primary-color);
    }
  }
  
  &.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
    
    &:hover {
      background: color-mix(in srgb, var(--success-color) 90%, black);
    }
  }
  
  &.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    
    &:hover {
      background: color-mix(in srgb, var(--warning-color) 90%, black);
    }
  }
  
  &.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
    
    &:hover {
      background: color-mix(in srgb, var(--error-color) 90%, black);
    }
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    
    &::before {
      display: none;
    }
  }
}

// 现代化输入框样式
.modern-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: all var(--duration-fast) var(--ease-out);
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
  
  &:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
  }
}

// 现代化标签样式
.modern-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  
  &.tag-primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
  }
  
  &.tag-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
  }
  
  &.tag-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
  }
  
  &.tag-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
  }
  
  &.tag-gray {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
  }
}

// 加载动画
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 脉冲动画
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 弹跳动画
.bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// 渐入动画
.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滑入动画
.slide-in-right {
  animation: slideInRight var(--duration-normal) var(--ease-out);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .container {
    max-width: 960px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .modern-card {
    .card-header,
    .card-body,
    .card-footer {
      padding: var(--spacing-md);
    }
  }
  
  .modern-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
  
  .container {
    padding: 0 var(--spacing-xs);
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  :root {
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
  }
  
  .modern-card {
    border-width: 2px;
  }
  
  .modern-input {
    border-width: 2px;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 打印样式
@media print {
  .modern-card {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .modern-btn {
    background: none !important;
    color: #000 !important;
    border: 1px solid #000;
  }
}