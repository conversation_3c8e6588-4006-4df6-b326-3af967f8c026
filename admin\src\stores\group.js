import { defineStore } from 'pinia'
import { 
  getGroupList, 
  createGroup, 
  updateGroup, 
  deleteGroup,
  getGroupDetail,
  toggleGroupStatus,
  getGroupStats,
  getDomainPools,
  getGroupTemplates
} from '@/api/group'

export const useGroupStore = defineStore('group', {
  state: () => ({
    // 群组列表
    groups: [],
    groupsLoading: false,
    groupsTotal: 0,
    
    // 当前群组
    currentGroup: null,
    currentGroupLoading: false,
    
    // 群组统计
    groupStats: {
      total_groups: 0,
      active_groups: 0,
      total_members: 0,
      total_revenue: 0,
      today_orders: 0,
      conversion_rate: 0
    },
    
    // 域名池
    domainPools: [],
    
    // 群组模板
    groupTemplates: [],
    
    // 分类选项
    categories: [
      { value: 'technology', label: '技术编程', icon: 'code' },
      { value: 'finance', label: '投资理财', icon: 'money' },
      { value: 'education', label: '教育学习', icon: 'book' },
      { value: 'fitness', label: '健身运动', icon: 'sport' },
      { value: 'business', label: '商业创业', icon: 'business' },
      { value: 'social', label: '社交交友', icon: 'social' },
      { value: 'entertainment', label: '娱乐休闲', icon: 'entertainment' },
      { value: 'other', label: '其他', icon: 'other' }
    ],
    
    // 支付方式
    paymentMethods: [
      { code: 'wechat', name: '微信支付', icon: '/icons/wechat-pay.png', enabled: true },
      { code: 'alipay', name: '支付宝', icon: '/icons/alipay.png', enabled: true },
      { code: 'qq', name: 'QQ钱包', icon: '/icons/qq-pay.png', enabled: false },
      { code: 'bank', name: '银行转账', icon: '/icons/bank.png', enabled: true }
    ],
    
    // 筛选条件
    filters: {
      category: '',
      status: '',
      price_range: '',
      date_range: []
    }
  }),

  getters: {
    // 获取活跃群组
    activeGroups: (state) => {
      return state.groups.filter(group => group.status === 'active')
    },
    
    // 获取分类统计
    categoryStats: (state) => {
      const stats = {}
      state.categories.forEach(cat => {
        stats[cat.value] = state.groups.filter(group => group.category === cat.value).length
      })
      return stats
    },
    
    // 获取收入统计
    revenueStats: (state) => {
      return state.groups.reduce((total, group) => {
        return total + (group.total_revenue || 0)
      }, 0)
    },
    
    // 获取可用支付方式
    availablePaymentMethods: (state) => {
      return state.paymentMethods.filter(method => method.enabled)
    },
    
    // 获取分类选项
    categoryOptions: (state) => {
      return state.categories.map(cat => ({
        label: cat.label,
        value: cat.value
      }))
    }
  },

  actions: {
    // 获取群组列表
    async fetchGroups(params = {}) {
      this.groupsLoading = true
      try {
        const response = await getGroupList({
          ...params,
          ...this.filters
        })
        
        this.groups = response.data.data
        this.groupsTotal = response.data.total
        
        return response.data
      } catch (error) {
        console.error('获取群组列表失败:', error)
        throw error
      } finally {
        this.groupsLoading = false
      }
    },
    
    // 创建群组
    async createGroup(groupData) {
      try {
        const response = await createGroup(groupData)
        
        // 添加到本地列表
        this.groups.unshift(response.data)
        this.groupsTotal += 1
        
        // 更新统计
        this.updateStats()
        
        return response.data
      } catch (error) {
        console.error('创建群组失败:', error)
        throw error
      }
    },
    
    // 获取群组详情
    async fetchGroupDetail(id) {
      this.currentGroupLoading = true
      try {
        const response = await getGroupDetail(id)
        this.currentGroup = response.data
        return response.data
      } catch (error) {
        console.error('获取群组详情失败:', error)
        throw error
      } finally {
        this.currentGroupLoading = false
      }
    },
    
    // 更新群组
    async updateGroup(id, groupData) {
      try {
        const response = await updateGroup(id, groupData)
        
        // 更新本地数据
        const index = this.groups.findIndex(group => group.id === id)
        if (index !== -1) {
          this.groups[index] = { ...this.groups[index], ...response.data }
        }
        
        // 更新当前群组
        if (this.currentGroup && this.currentGroup.id === id) {
          this.currentGroup = { ...this.currentGroup, ...response.data }
        }
        
        return response.data
      } catch (error) {
        console.error('更新群组失败:', error)
        throw error
      }
    },
    
    // 删除群组
    async deleteGroup(id) {
      try {
        await deleteGroup(id)
        
        // 从本地列表移除
        const index = this.groups.findIndex(group => group.id === id)
        if (index !== -1) {
          this.groups.splice(index, 1)
          this.groupsTotal -= 1
        }
        
        // 清空当前群组
        if (this.currentGroup && this.currentGroup.id === id) {
          this.currentGroup = null
        }
        
        // 更新统计
        this.updateStats()
        
        return true
      } catch (error) {
        console.error('删除群组失败:', error)
        throw error
      }
    },
    
    // 切换群组状态
    async toggleGroupStatus(id, status) {
      try {
        const response = await toggleGroupStatus(id, status)
        
        // 更新本地数据
        const index = this.groups.findIndex(group => group.id === id)
        if (index !== -1) {
          this.groups[index].status = status
        }
        
        // 更新当前群组
        if (this.currentGroup && this.currentGroup.id === id) {
          this.currentGroup.status = status
        }
        
        return response.data
      } catch (error) {
        console.error('切换群组状态失败:', error)
        throw error
      }
    },
    
    // 获取群组统计
    async fetchGroupStats() {
      try {
        const response = await getGroupStats()
        this.groupStats = response.data
        return response.data
      } catch (error) {
        console.error('获取群组统计失败:', error)
        throw error
      }
    },
    
    // 获取域名池
    async fetchDomainPools() {
      try {
        const response = await getDomainPools()
        this.domainPools = response.data
        return response.data
      } catch (error) {
        console.error('获取域名池失败:', error)
        throw error
      }
    },
    
    // 获取群组模板
    async fetchGroupTemplates() {
      try {
        const response = await getGroupTemplates()
        this.groupTemplates = response.data
        return response.data
      } catch (error) {
        console.error('获取群组模板失败:', error)
        throw error
      }
    },
    
    // 设置筛选条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },
    
    // 清空筛选条件
    clearFilters() {
      this.filters = {
        category: '',
        status: '',
        price_range: '',
        date_range: []
      }
    },
    
    // 更新统计数据
    updateStats() {
      this.groupStats.total_groups = this.groups.length
      this.groupStats.active_groups = this.activeGroups.length
      this.groupStats.total_revenue = this.revenueStats
    },
    
    // 搜索群组
    searchGroups(keyword) {
      if (!keyword) {
        return this.groups
      }
      
      return this.groups.filter(group => 
        group.title.toLowerCase().includes(keyword.toLowerCase()) ||
        group.description.toLowerCase().includes(keyword.toLowerCase())
      )
    },
    
    // 按分类筛选
    filterByCategory(category) {
      if (!category) {
        return this.groups
      }
      
      return this.groups.filter(group => group.category === category)
    },
    
    // 按状态筛选
    filterByStatus(status) {
      if (!status) {
        return this.groups
      }
      
      return this.groups.filter(group => group.status === status)
    },
    
    // 排序群组
    sortGroups(sortBy, sortOrder = 'desc') {
      const sortedGroups = [...this.groups]
      
      sortedGroups.sort((a, b) => {
        let aValue = a[sortBy]
        let bValue = b[sortBy]
        
        // 处理数字类型
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
        }
        
        // 处理字符串类型
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          aValue = aValue.toLowerCase()
          bValue = bValue.toLowerCase()
          
          if (sortOrder === 'desc') {
            return bValue.localeCompare(aValue)
          } else {
            return aValue.localeCompare(bValue)
          }
        }
        
        // 处理日期类型
        if (sortBy.includes('time') || sortBy.includes('date')) {
          const aTime = new Date(aValue).getTime()
          const bTime = new Date(bValue).getTime()
          return sortOrder === 'desc' ? bTime - aTime : aTime - bTime
        }
        
        return 0
      })
      
      this.groups = sortedGroups
    },
    
    // 重置状态
    resetState() {
      this.groups = []
      this.groupsTotal = 0
      this.currentGroup = null
      this.clearFilters()
    }
  }
})