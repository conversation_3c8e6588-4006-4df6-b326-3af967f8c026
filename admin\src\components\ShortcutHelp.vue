<template>
  <el-dialog
    v-model="visible"
    title="快捷键帮助"
    width="600px"
    :before-close="handleClose"
  >
    <div class="shortcut-help">
      <div class="shortcut-section">
        <h4>导航快捷键</h4>
        <div class="shortcut-list">
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>Ctrl</kbd> + <kbd>B</kbd>
            </div>
            <div class="shortcut-desc">切换侧边栏</div>
          </div>
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>Ctrl</kbd> + <kbd>K</kbd>
            </div>
            <div class="shortcut-desc">打开搜索</div>
          </div>
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>F11</kbd>
            </div>
            <div class="shortcut-desc">全屏切换</div>
          </div>
        </div>
      </div>

      <div class="shortcut-section">
        <h4>功能快捷键</h4>
        <div class="shortcut-list">
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>Ctrl</kbd> + <kbd>S</kbd>
            </div>
            <div class="shortcut-desc">保存当前页面</div>
          </div>
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>Ctrl</kbd> + <kbd>R</kbd>
            </div>
            <div class="shortcut-desc">刷新页面</div>
          </div>
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>Esc</kbd>
            </div>
            <div class="shortcut-desc">关闭弹窗</div>
          </div>
        </div>
      </div>

      <div class="shortcut-section">
        <h4>帮助快捷键</h4>
        <div class="shortcut-list">
          <div class="shortcut-item">
            <div class="shortcut-keys">
              <kbd>?</kbd>
            </div>
            <div class="shortcut-desc">显示快捷键帮助</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.shortcut-help {
  padding: 20px 0;
}

.shortcut-section {
  margin-bottom: 30px;
}

.shortcut-section:last-child {
  margin-bottom: 0;
}

.shortcut-section h4 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.shortcut-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.shortcut-keys kbd {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1;
  color: #374151;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-family: inherit;
  font-weight: 600;
}

.shortcut-desc {
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .shortcut-desc {
    font-size: 13px;
  }
}
</style>