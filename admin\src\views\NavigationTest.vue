<template>
  <div class="navigation-test">
    <div class="test-header">
      <h1>🧪 导航功能全面测试</h1>
      <p>自动化测试管理系统的所有导航功能</p>
    </div>

    <div class="test-controls">
      <el-button 
        type="primary" 
        size="large"
        @click="startFullTest"
        :loading="testing"
        :disabled="testing"
      >
        <span v-if="!testing">🚀 开始全面测试</span>
        <span v-else>🔄 测试进行中...</span>
      </el-button>
      
      <el-button 
        type="success" 
        @click="testBasicRoutes"
        :disabled="testing"
      >
        📍 基础路由测试
      </el-button>
      
      <el-button 
        type="info" 
        @click="testMenuRoutes"
        :disabled="testing"
      >
        🧭 菜单路由测试
      </el-button>
      
      <el-button 
        type="warning" 
        @click="testAuthRoutes"
        :disabled="testing"
      >
        🔐 认证路由测试
      </el-button>
      
      <el-button 
        @click="clearResults"
        :disabled="testing"
      >
        🗑️ 清空结果
      </el-button>
    </div>

    <!-- 实时测试状态 -->
    <div v-if="currentTest" class="current-test">
      <el-alert
        :title="currentTest"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 测试进度 -->
    <div v-if="testing" class="test-progress">
      <el-progress 
        :percentage="testProgress" 
        :status="testProgress === 100 ? 'success' : 'active'"
      />
      <p>{{ testProgressText }}</p>
    </div>

    <!-- 测试结果概览 -->
    <div v-if="testReport" class="test-summary">
      <h3>📊 测试结果概览</h3>
      <div class="summary-cards">
        <div class="summary-card total">
          <div class="card-number">{{ testReport.total }}</div>
          <div class="card-label">总测试数</div>
        </div>
        <div class="summary-card success">
          <div class="card-number">{{ testReport.successful }}</div>
          <div class="card-label">成功</div>
        </div>
        <div class="summary-card failed">
          <div class="card-number">{{ testReport.failed }}</div>
          <div class="card-label">失败</div>
        </div>
        <div class="summary-card rate">
          <div class="card-number">{{ testReport.successRate }}%</div>
          <div class="card-label">成功率</div>
        </div>
      </div>
    </div>

    <!-- 详细测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <h3>📋 详细测试结果</h3>
      
      <el-table :data="testResults" style="width: 100%">
        <el-table-column prop="name" label="测试项目" width="200" />
        <el-table-column prop="path" label="路径" width="200" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.success ? 'success' : 'danger'">
              {{ scope.row.success ? '✅ 成功' : '❌ 失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="loadTime" label="加载时间(ms)" width="120" />
        <el-table-column prop="type" label="类型" width="100" />
        <el-table-column label="详情" min-width="300">
          <template #default="scope">
            <div v-if="scope.row.success" class="success-details">
              <span v-if="scope.row.details">
                路径正确: {{ scope.row.details.correctPath ? '✅' : '❌' }} |
                内容加载: {{ scope.row.details.hasContent ? '✅' : '❌' }} |
                无错误: {{ scope.row.details.noErrors ? '✅' : '❌' }}
              </span>
              <span v-else-if="scope.row.type === 'redirect'">
                重定向到: {{ scope.row.actualPath }}
              </span>
              <span v-else>测试通过</span>
            </div>
            <div v-else class="error-details">
              <div v-if="scope.row.error">
                <strong>错误:</strong> {{ scope.row.error }}
              </div>
              <div v-if="scope.row.details">
                <div v-if="!scope.row.details.correctPath">
                  期望路径: {{ scope.row.path }}, 实际路径: {{ scope.row.details.actualPath }}
                </div>
                <div v-if="!scope.row.details.hasContent">
                  页面内容加载失败
                </div>
                <div v-if="!scope.row.details.noErrors">
                  存在控制台错误
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 快速导航测试 -->
    <div class="quick-nav-test">
      <h3>🎯 快速导航测试</h3>
      <p>点击下方按钮快速测试各个页面的导航功能</p>
      
      <div class="nav-test-grid">
        <el-button 
          v-for="route in quickTestRoutes" 
          :key="route.path"
          @click="quickNavTest(route)"
          :type="getRouteButtonType(route)"
          size="small"
        >
          {{ route.icon }} {{ route.name }}
        </el-button>
      </div>
    </div>

    <!-- 控制台日志 -->
    <div class="console-log">
      <h3>📝 测试日志</h3>
      <div class="log-container">
        <div 
          v-for="(log, index) in consoleLogs" 
          :key="index"
          :class="['log-entry', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { navigationTester } from '@/utils/navigationTest'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const testing = ref(false)
const currentTest = ref('')
const testProgress = ref(0)
const testProgressText = ref('')
const testResults = ref([])
const testReport = ref(null)
const consoleLogs = ref([])

// 快速测试路由
const quickTestRoutes = ref([
  { path: '/login', name: '登录', icon: '🔑' },
  { path: '/test-dashboard', name: '测试控制台', icon: '📊' },
  { path: '/system-status', name: '系统状态', icon: '🔧' },
  { path: '/scroll-test', name: '滚动测试', icon: '📜' },
  { path: '/admin/dashboard', name: '控制台', icon: '🏠' },
  { path: '/admin/groups', name: '群组', icon: '👥' },
  { path: '/admin/users', name: '用户', icon: '👤' },
  { path: '/admin/orders', name: '订单', icon: '🛒' },
  { path: '/admin/finance', name: '财务', icon: '💰' },
  { path: '/admin/settings', name: '设置', icon: '⚙️' }
])

// 初始化
onMounted(() => {
  navigationTester.init(router)
  addLog('info', '导航测试器已初始化')
  
  // 监听路由变化
  router.afterEach((to, from) => {
    addLog('success', `路由变化: ${from.path} → ${to.path}`)
  })
})

// 添加日志
const addLog = (type, message) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  consoleLogs.value.unshift({
    type,
    time,
    message
  })
  
  // 限制日志数量
  if (consoleLogs.value.length > 50) {
    consoleLogs.value = consoleLogs.value.slice(0, 50)
  }
}

// 开始全面测试
const startFullTest = async () => {
  testing.value = true
  testProgress.value = 0
  testResults.value = []
  testReport.value = null
  currentTest.value = '准备开始测试...'
  
  addLog('info', '开始全面导航测试')
  
  try {
    // 模拟测试进度
    const progressInterval = setInterval(() => {
      if (testProgress.value < 90) {
        testProgress.value += Math.random() * 10
        testProgressText.value = `测试进行中... ${Math.round(testProgress.value)}%`
      }
    }, 200)
    
    // 执行测试
    const report = await navigationTester.startTest()
    
    clearInterval(progressInterval)
    testProgress.value = 100
    testProgressText.value = '测试完成!'
    
    // 获取测试结果
    testResults.value = navigationTester.testResults
    testReport.value = report
    
    addLog('success', `测试完成! 成功率: ${report.successRate}%`)
    
    if (report.successRate === 100) {
      ElMessage.success('🎉 所有测试都通过了!')
    } else if (report.successRate >= 80) {
      ElMessage.warning(`⚠️ 测试完成，成功率: ${report.successRate}%`)
    } else {
      ElMessage.error(`❌ 测试完成，发现多个问题，成功率: ${report.successRate}%`)
    }
    
  } catch (error) {
    addLog('error', `测试失败: ${error.message}`)
    ElMessage.error('测试过程中发生错误')
  } finally {
    testing.value = false
    currentTest.value = ''
  }
}

// 测试基础路由
const testBasicRoutes = async () => {
  testing.value = true
  addLog('info', '开始基础路由测试')
  
  try {
    await navigationTester.testBasicRoutes()
    testResults.value = navigationTester.testResults
    addLog('success', '基础路由测试完成')
    ElMessage.success('基础路由测试完成')
  } catch (error) {
    addLog('error', `基础路由测试失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

// 测试菜单路由
const testMenuRoutes = async () => {
  testing.value = true
  addLog('info', '开始菜单路由测试')
  
  try {
    await navigationTester.testNavigationMenu()
    testResults.value = navigationTester.testResults
    addLog('success', '菜单路由测试完成')
    ElMessage.success('菜单路由测试完成')
  } catch (error) {
    addLog('error', `菜单路由测试失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

// 测试认证路由
const testAuthRoutes = async () => {
  testing.value = true
  addLog('info', '开始认证路由测试')
  
  try {
    await navigationTester.testAuthRoutes()
    testResults.value = navigationTester.testResults
    addLog('success', '认证路由测试完成')
    ElMessage.success('认证路由测试完成')
  } catch (error) {
    addLog('error', `认证路由测试失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

// 快速导航测试
const quickNavTest = async (route) => {
  addLog('info', `快速测试: ${route.name} (${route.path})`)
  
  try {
    await router.push(route.path)
    addLog('success', `成功导航到: ${route.name}`)
    ElMessage.success(`成功导航到: ${route.name}`)
  } catch (error) {
    addLog('error', `导航失败: ${error.message}`)
    ElMessage.error(`导航到 ${route.name} 失败`)
  }
}

// 获取路由按钮类型
const getRouteButtonType = (route) => {
  if (route.path === router.currentRoute.value.path) {
    return 'primary'
  }
  if (route.path.includes('test') || route.path.includes('status')) {
    return 'success'
  }
  if (route.path.includes('system') || route.path.includes('settings')) {
    return 'warning'
  }
  return 'default'
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  testReport.value = null
  consoleLogs.value = []
  testProgress.value = 0
  addLog('info', '测试结果已清空')
}
</script>

<style scoped>
.navigation-test {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.test-header p {
  color: #6b7280;
}

.test-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.current-test {
  margin-bottom: 16px;
}

.test-progress {
  margin-bottom: 24px;
  text-align: center;
}

.test-progress p {
  margin-top: 8px;
  color: #6b7280;
}

.test-summary {
  margin-bottom: 32px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.summary-card.total {
  border-color: #3b82f6;
}

.summary-card.success {
  border-color: #10b981;
}

.summary-card.failed {
  border-color: #ef4444;
}

.summary-card.rate {
  border-color: #8b5cf6;
}

.card-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.card-label {
  color: #6b7280;
  font-size: 14px;
}

.test-results {
  margin-bottom: 32px;
}

.success-details {
  color: #10b981;
  font-size: 12px;
}

.error-details {
  color: #ef4444;
  font-size: 12px;
}

.quick-nav-test {
  margin-bottom: 32px;
}

.nav-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.console-log {
  background: #f9fafb;
  border-radius: 12px;
  padding: 24px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  margin-bottom: 4px;
  align-items: flex-start;
}

.log-time {
  color: #9ca3af;
  margin-right: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-entry.info .log-message {
  color: #60a5fa;
}

.log-entry.success .log-message {
  color: #34d399;
}

.log-entry.error .log-message {
  color: #f87171;
}

@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nav-test-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
