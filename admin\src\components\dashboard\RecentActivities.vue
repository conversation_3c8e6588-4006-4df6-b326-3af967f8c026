<template>
  <div class="recent-activities-card">
    <div class="card-header">
      <h3 class="card-title">最新动态</h3>
      <div class="activity-filters">
        <el-button-group size="small">
          <el-button 
            v-for="filter in filters" 
            :key="filter.key"
            :type="activeFilter === filter.key ? 'primary' : ''"
            @click="activeFilter = filter.key"
          >
            {{ filter.label }}
          </el-button>
        </el-button-group>
      </div>
    </div>
    <div class="activities-timeline">
      <div 
        v-for="activity in filteredActivities" 
        :key="activity.id"
        class="timeline-item"
      >
        <div class="timeline-dot" :class="activity.type"></div>
        <div class="timeline-content">
          <div class="activity-header">
            <span class="activity-user">{{ activity.userName }}</span>
            <span class="activity-action">{{ activity.action }}</span>
            <span class="activity-target">{{ activity.target }}</span>
          </div>
          <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  activities: {
    type: Array,
    default: () => []
  }
})

const activeFilter = ref('all')

const filters = [
  { key: 'all', label: '全部' },
  { key: 'user', label: '用户' },
  { key: 'order', label: '订单' },
  { key: 'system', label: '系统' }
]

const filteredActivities = computed(() => {
  if (activeFilter.value === 'all') {
    return props.activities
  }
  return props.activities.filter(activity => activity.type === activeFilter.value)
})

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return date.toLocaleDateString()
}
</script>

<style lang="scss" scoped>
.recent-activities-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  height: 100%;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .activities-timeline {
    .timeline-item {
      display: flex;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      .timeline-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-top: 6px;
        flex-shrink: 0;

        &.user {
          background: #3b82f6;
        }

        &.order {
          background: #10b981;
        }

        &.system {
          background: #f59e0b;
        }
      }

      .timeline-content {
        flex: 1;

        .activity-header {
          font-size: 14px;
          margin-bottom: 4px;

          .activity-user {
            font-weight: 600;
            color: #1f2937;
          }

          .activity-action {
            color: #6b7280;
            margin: 0 4px;
          }

          .activity-target {
            font-weight: 500;
            color: #3b82f6;
          }
        }

        .activity-time {
          font-size: 12px;
          color: #9ca3af;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .recent-activities-card {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }
}
</style>