import{_ as e,u as a,d as s,e as l,v as t,g as n,h as r}from"./index-DtXAftX0.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                       *//* empty css                        *//* empty css                *//* empty css                             */import{af as o,r as i,c as u,d as c,e as d,k as p,l as v,t as f,E as m,z as h,D as _,u as g,F as y,Y as b,y as w,C as k,B as $}from"./vue-vendor-Dy164gUc.js";import{bk as j,bl as C,a$ as I,U as q,T as A,ao as T,aY as U,bm as z,bn as S,at as D,b0 as L,o as x,b2 as M,b1 as N,bh as V,bi as E,aw as F,af as P,am as Y,Q as B,ad as O,bo as Q,au as G,a8 as H,av as J,aj as K,a0 as R,ai as W,al as X,an as Z,ap as ee,aq as ae,ar as se}from"./element-plus-h2SQQM64.js";import{b as le,g as te,a as ne,c as re}from"./dataPermission-K5RJ0Kz8.js";import"./utils-D1VZuEZr.js";const oe={class:"test-navigation-system"},ie={class:"card-header"},ue={class:"user-info"},ce={class:"card-header"},de={class:"role-switch"},pe={class:"card-header"},ve={class:"navigation-test"},fe={class:"test-section"},me={class:"navigation-groups"},he={class:"group-header"},_e={class:"group-title"},ge={class:"group-children"},ye={class:"card-header"},be={class:"group-creation-test"},we={class:"test-result"},ke={class:"access-entries"},$e={class:"quick-actions-test"},je={class:"quick-actions"},Ce={class:"card-header"},Ie={class:"permission-test"},qe={class:"test-section"},Ae={class:"card-header"},Te={class:"test-logs"},Ue={class:"log-time"},ze={class:"log-type"},Se={class:"log-message"},De=e({__name:"TestNavigationSystem",setup(e){const De=o(),Le=a(),xe=i("admin"),Me=i(!1),Ne=i([]),Ve=[{key:"admin",name:"超级管理员"},{key:"substation",name:"分站管理员"},{key:"agent",name:"代理商"},{key:"distributor",name:"分销员"},{key:"group_owner",name:"群主"},{key:"user",name:"普通用户"}],Ee=u(()=>Le.userInfo?.role||"user"),Fe=u(()=>{const e=s(Ee.value);return Oe("info",`获取到 ${Object.keys(e).length} 个可见导航分组`),e}),Pe=u(()=>{const e=l(Ee.value);return Oe("info",`获取到 ${e.length} 个快速操作`),e}),Ye=u(()=>{const e=t(Ee.value);return Oe("info",`群组创建验证: ${e.canAccess?"通过":"失败"}, ${e.entryCount} 个入口`),e}),Be=u(()=>{const e=[],a=Ee.value;return[{resource:"group",action:"create",description:"创建群组"},{resource:"group",action:"view",description:"查看群组"},{resource:"user",action:"create",description:"创建用户"},{resource:"user",action:"view",description:"查看用户"},{resource:"order",action:"view",description:"查看订单"},{resource:"finance",action:"view",description:"查看财务"}].forEach(s=>{const l=le(s.action,s.resource,a);e.push({resource:s.description,action:s.action,allowed:l,reason:l?"权限允许":"权限不足"})}),e}),Oe=(e,a)=>{Ne.value.unshift({type:e,message:a,time:new Date}),Ne.value.length>50&&(Ne.value=Ne.value.slice(0,50))},Qe=e=>{Oe("info",`准备切换角色到: ${n(e)}`)},Ge=()=>{Me.value=!0,Oe("info",`开始切换角色到: ${n(xe.value)}`),setTimeout(()=>{Le.setUserInfo({...Le.userInfo,role:xe.value}),Me.value=!1,Oe("success",`角色切换成功: ${n(xe.value)}`),B.success(`已切换到 ${n(xe.value)}`)},1e3)},He=e=>{Oe("info",`测试导航到: ${e}`);try{De.push(e),Oe("success",`导航成功: ${e}`),B.success(`导航到 ${e} 成功`)}catch(a){Oe("error",`导航失败: ${e} - ${a.message}`),B.error(`导航失败: ${a.message}`)}},Je=()=>{Ne.value=[],B.success("日志已清空")},Ke=e=>({Monitor:se,DataLine:ae,Comment:ee,User:T,UserFilled:Z,Share:X,Money:W,Setting:R,Avatar:K,ShoppingCart:J,Link:H,Lightning:G,Management:Q,Tools:O,Plus:F}[e]||Y),Re=e=>({navigation:"导航菜单",quick_action:"快速操作",workbench:"工作台"}[e]||e);return c(()=>Ee.value,(e,a)=>{a&&Oe("info",`角色已变更: ${n(a)} → ${n(e)}`)}),d(()=>{Oe("info","导航系统测试页面已加载"),Oe("info",`当前用户角色: ${n(Ee.value)}`),setTimeout(()=>{r(),Oe("info","已完成群组创建功能完整性验证")},1e3)}),(e,a)=>{const s=A,l=I,t=C,r=j,o=U,i=S,u=z,c=D,d=N,B=E,O=V;return v(),p("div",oe,[a[15]||(a[15]=f("div",{class:"test-header"},[f("h1",null,"🧪 导航系统和权限测试"),f("p",null,"测试和验证导航系统、权限控制和群组创建功能保护")],-1)),m(o,{class:"user-info-card",shadow:"never"},{header:h(()=>[f("div",ie,[m(s,null,{default:h(()=>[m(g(T))]),_:1}),a[1]||(a[1]=f("span",null,"当前用户信息",-1))])]),default:h(()=>[f("div",ue,[m(r,{column:3,border:""},{default:h(()=>[m(t,{label:"用户角色"},{default:h(()=>{return[m(l,{type:(e=Ee.value,{admin:"danger",substation:"warning",agent:"primary",distributor:"success",group_owner:"info",user:"default"}[e]||"default")},{default:h(()=>[_(q(g(n)(Ee.value)),1)]),_:1},8,["type"])];var e}),_:1}),m(t,{label:"用户ID"},{default:h(()=>[_(q(g(Le).userInfo?.id||"N/A"),1)]),_:1}),m(t,{label:"用户名"},{default:h(()=>[_(q(g(Le).userInfo?.username||"N/A"),1)]),_:1}),m(t,{label:"数据权限范围"},{default:h(()=>[_(q(g(te)(Ee.value)),1)]),_:1}),m(t,{label:"财务权限范围"},{default:h(()=>[_(q(g(ne)(Ee.value)),1)]),_:1}),m(t,{label:"群组创建权限"},{default:h(()=>[m(l,{type:g(re)(Ee.value)?"success":"danger"},{default:h(()=>[_(q(g(re)(Ee.value)?"✅ 允许":"❌ 禁止"),1)]),_:1},8,["type"])]),_:1})]),_:1})])]),_:1}),m(o,{class:"role-switch-card",shadow:"never"},{header:h(()=>[f("div",ce,[m(s,null,{default:h(()=>[m(g(L))]),_:1}),a[2]||(a[2]=f("span",null,"角色切换测试",-1))])]),default:h(()=>[f("div",de,[m(u,{modelValue:xe.value,"onUpdate:modelValue":a[0]||(a[0]=e=>xe.value=e),onChange:Qe},{default:h(()=>[(v(),p(y,null,b(Ve,e=>m(i,{key:e.key,value:e.key},{default:h(()=>[_(q(e.name),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]),m(c,{type:"primary",onClick:Ge,loading:Me.value},{default:h(()=>a[3]||(a[3]=[_(" 应用角色切换 ",-1)])),_:1,__:[3]},8,["loading"])])]),_:1}),m(o,{class:"navigation-test-card",shadow:"never"},{header:h(()=>[f("div",pe,[m(s,null,{default:h(()=>[m(g(M))]),_:1}),a[4]||(a[4]=f("span",null,"导航权限测试",-1))])]),default:h(()=>[f("div",ve,[f("div",fe,[a[6]||(a[6]=f("h3",null,"可访问的导航分组",-1)),f("div",me,[(v(!0),p(y,null,b(Fe.value,(e,t)=>(v(),p("div",{key:t,class:"nav-group"},[f("div",he,[m(s,null,{default:h(()=>[(v(),w(k(Ke(e.icon))))]),_:2},1024),f("span",_e,q(e.title),1),m(l,{size:"small"},{default:h(()=>[_(q(e.children.length)+"个菜单",1)]),_:2},1024)]),f("div",ge,[(v(!0),p(y,null,b(e.children,e=>(v(),p("div",{key:e.path,class:x(["nav-item",{protected:e.protected}])},[m(s,null,{default:h(()=>[(v(),w(k(Ke(e.icon))))]),_:2},1024),f("span",null,q(e.title),1),e.protected?(v(),w(l,{key:0,type:"success",size:"small"},{default:h(()=>a[5]||(a[5]=[_("核心功能",-1)])),_:1,__:[5]})):$("",!0)],2))),128))])]))),128))])])])]),_:1}),m(o,{class:"group-creation-test-card",shadow:"never"},{header:h(()=>[f("div",ye,[m(s,null,{default:h(()=>[m(g(F))]),_:1}),a[7]||(a[7]=f("span",null,"群组创建功能测试",-1))])]),default:h(()=>[f("div",be,[f("div",we,[m(d,{title:Ye.value.canAccess?"✅ 群组创建功能可用":"❌ 群组创建功能不可用",type:Ye.value.canAccess?"success":"error",description:`当前角色 ${g(n)(Ee.value)} 有 ${Ye.value.entryCount} 个访问入口`,"show-icon":"",closable:!1},null,8,["title","type","description"])]),f("div",ke,[a[9]||(a[9]=f("h4",null,"访问入口列表",-1)),m(O,{data:Ye.value.entries,style:{width:"100%"}},{default:h(()=>[m(B,{prop:"type",label:"入口类型",width:"120"},{default:h(({row:e})=>{return[m(l,{type:(a=e.type,{navigation:"primary",quick_action:"success",workbench:"warning"}[a]||"default")},{default:h(()=>[_(q(Re(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),m(B,{prop:"title",label:"入口名称"}),m(B,{prop:"location",label:"位置"}),m(B,{prop:"path",label:"路径"}),m(B,{label:"操作",width:"100"},{default:h(({row:e})=>[m(c,{size:"small",onClick:a=>He(e.path)},{default:h(()=>a[8]||(a[8]=[_("测试",-1)])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),f("div",$e,[a[10]||(a[10]=f("h4",null,"快速操作测试",-1)),f("div",je,[(v(!0),p(y,null,b(Pe.value,e=>(v(),w(c,{key:e.path,type:"/community/add"===e.path?"primary":"default",onClick:a=>He(e.path)},{default:h(()=>[m(s,null,{default:h(()=>[(v(),w(k(Ke(e.icon))))]),_:2},1024),_(" "+q(e.title),1)]),_:2},1032,["type","onClick"]))),128))])])])]),_:1}),m(o,{class:"permission-test-card",shadow:"never"},{header:h(()=>[f("div",Ce,[m(s,null,{default:h(()=>[m(g(P))]),_:1}),a[11]||(a[11]=f("span",null,"权限验证测试",-1))])]),default:h(()=>[f("div",Ie,[f("div",qe,[a[12]||(a[12]=f("h4",null,"数据访问权限测试",-1)),m(O,{data:Be.value,style:{width:"100%"}},{default:h(()=>[m(B,{prop:"resource",label:"资源类型"}),m(B,{prop:"action",label:"操作"}),m(B,{prop:"allowed",label:"是否允许",width:"100"},{default:h(({row:e})=>[m(l,{type:e.allowed?"success":"danger"},{default:h(()=>[_(q(e.allowed?"✅ 允许":"❌ 禁止"),1)]),_:2},1032,["type"])]),_:1}),m(B,{prop:"reason",label:"原因"})]),_:1},8,["data"])])])]),_:1}),m(o,{class:"test-log-card",shadow:"never"},{header:h(()=>[f("div",Ae,[m(s,null,{default:h(()=>[m(g(Y))]),_:1}),a[14]||(a[14]=f("span",null,"测试日志",-1)),m(c,{size:"small",onClick:Je},{default:h(()=>a[13]||(a[13]=[_("清空日志",-1)])),_:1,__:[13]})])]),default:h(()=>[f("div",Te,[(v(!0),p(y,null,b(Ne.value,(e,a)=>{return v(),p("div",{key:a,class:x(["log-item",e.type])},[f("span",Ue,q((s=e.time,s.toLocaleTimeString())),1),f("span",ze,q(e.type.toUpperCase()),1),f("span",Se,q(e.message),1)],2);var s}),128))])]),_:1})])}}},[["__scopeId","data-v-9d8e6e30"]]);export{De as default};
