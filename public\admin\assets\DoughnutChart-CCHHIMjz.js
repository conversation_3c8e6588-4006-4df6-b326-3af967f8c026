import{C as t,A as e,D as a,p as s,a as o}from"./LineChart-CydsJ2U8.js";import{_ as n}from"./index-DtXAftX0.js";import{r as p,e as d,H as r,d as i,k as l,l as u,t as c}from"./vue-vendor-Dy164gUc.js";import{p as h}from"./element-plus-h2SQQM64.js";const g=n({__name:"Doughnut<PERSON><PERSON>",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:String,default:"400px"}},setup(n){t.register(e,a,s,o);const g=n,m=p();let f=null;const y={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}},tooltip:{callbacks:{label:function(t){const e=t.label||"",a=t.parsed;return`${e}: ${a} (${(a/t.dataset.data.reduce((t,e)=>t+e,0)*100).toFixed(1)}%)`}}}},cutout:"60%"},b=()=>{f&&(f.data=g.data,f.options={...y,...g.options},f.update())};return d(()=>{(()=>{f&&f.destroy();const e=m.value.getContext("2d");f=new t(e,{type:"doughnut",data:g.data,options:{...y,...g.options}})})()}),r(()=>{f&&f.destroy()}),i(()=>g.data,b,{deep:!0}),i(()=>g.options,b,{deep:!0}),(t,e)=>(u(),l("div",{class:"doughnut-chart",style:h({height:n.height})},[c("canvas",{ref_key:"chartRef",ref:m},null,512)],4))}},[["__scopeId","data-v-737d65a5"]]);export{g as D};
