import{_ as e}from"./index-DtXAftX0.js";/* empty css               *//* empty css               *//* empty css                  *//* empty css                *//* empty css                 */import{b1 as s,aZ as a,a_ as t,aY as l,at as c,a$ as r,U as u,b9 as i,b8 as n,Q as p,bZ as o}from"./element-plus-h2SQQM64.js";import{r as d,L as m,c as v,k as y,l as h,E as g,z as f,y as _,B as w,t as b,D as x}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const D={class:"function-test-page"},E={class:"test-content"},k={class:"test-buttons"},j={key:0,class:"test-result"},z={class:"test-content"},T={class:"test-buttons"},C={key:0,class:"test-result"},P={class:"test-content"},V={class:"test-buttons"},U={key:0,class:"test-result"},Z={class:"test-content"},B={class:"test-buttons"},F={key:0,class:"test-result"},I={class:"test-summary"},L={class:"summary-item"},M={class:"summary-number"},Q={class:"summary-item success"},Y={class:"summary-number"},$={class:"summary-item error"},q={class:"summary-number"},A={class:"summary-item"},G={class:"summary-number"},H=e({__name:"FunctionTest",setup(e){const H=d(""),J=m({userExport:!1,transactionExport:!1,distributorExport:!1,preview:!1,batch:!1,alert:!1}),K=m({export:null,preview:null,batch:null,alert:null}),N=d(!1),O=d([]),R=v(()=>O.value.length),S=v(()=>O.value.filter(e=>e.success).length),W=v(()=>O.value.filter(e=>!e.success).length),X=v(()=>R.value>0?S.value/R.value*100:0),ee=async()=>{J.userExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),K.export={success:!0,message:"用户导出功能测试成功"},O.value.push({type:"export",success:!0,time:new Date}),p.success("用户导出功能测试成功")}catch(e){K.export={success:!1,message:"用户导出功能测试失败"},O.value.push({type:"export",success:!1,time:new Date}),p.error("用户导出功能测试失败")}finally{J.userExport=!1,N.value=!0}},se=async()=>{J.transactionExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),K.export={success:!0,message:"交易导出功能测试成功"},O.value.push({type:"export",success:!0,time:new Date}),p.success("交易导出功能测试成功")}catch(e){K.export={success:!1,message:"交易导出功能测试失败"},O.value.push({type:"export",success:!1,time:new Date}),p.error("交易导出功能测试失败")}finally{J.transactionExport=!1,N.value=!0}},ae=async()=>{J.distributorExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),K.export={success:!0,message:"分销商导出功能测试成功"},O.value.push({type:"export",success:!0,time:new Date}),p.success("分销商导出功能测试成功")}catch(e){K.export={success:!1,message:"分销商导出功能测试失败"},O.value.push({type:"export",success:!1,time:new Date}),p.error("分销商导出功能测试失败")}finally{J.distributorExport=!1,N.value=!0}},te=async()=>{J.preview=!0;try{H.value;await new Promise(e=>setTimeout(e,800)),K.preview={success:!0,message:"数据预览功能测试成功"},O.value.push({type:"preview",success:!0,time:new Date}),o({title:"测试成功",message:"数据预览功能工作正常",type:"success"})}catch(e){K.preview={success:!1,message:"数据预览功能测试失败"},O.value.push({type:"preview",success:!1,time:new Date}),o({title:"测试失败",message:"数据预览功能异常",type:"error"})}finally{J.preview=!1,N.value=!0}},le=async()=>{J.batch=!0;try{await new Promise(e=>setTimeout(e,1200)),K.batch={success:!0,message:"批量处理功能测试成功"},O.value.push({type:"batch",success:!0,time:new Date}),p.success("批量处理功能测试成功")}catch(e){K.batch={success:!1,message:"批量处理功能测试失败"},O.value.push({type:"batch",success:!1,time:new Date}),p.error("批量处理功能测试失败")}finally{J.batch=!1,N.value=!0}},ce=async()=>{J.alert=!0;try{await new Promise(e=>setTimeout(e,1e3)),K.alert={success:!0,message:"告警设置功能测试成功"},O.value.push({type:"alert",success:!0,time:new Date}),p.success("告警设置功能测试成功")}catch(e){K.alert={success:!1,message:"告警设置功能测试失败"},O.value.push({type:"alert",success:!1,time:new Date}),p.error("告警设置功能测试失败")}finally{J.alert=!1,N.value=!0}};return(e,p)=>{const o=s,d=c,m=r,v=l,O=t,re=n,ue=i,ie=a;return h(),y("div",D,[g(v,null,{header:f(()=>p[1]||(p[1]=[b("div",{class:"card-header"},[b("span",null,"🧪 功能测试页面"),b("small",{style:{color:"#666","font-size":"12px","margin-left":"10px"}}," 验证所有已完善的后台功能 ")],-1)])),default:f(()=>[g(o,{title:"测试说明",type:"info",description:"此页面用于测试管理后台的各项功能是否正常工作。点击下方按钮即可测试对应功能。",style:{"margin-bottom":"20px"}}),g(ie,{gutter:20},{default:f(()=>[g(O,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[2]||(p[2]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-download"}),b("span",null,"导出功能测试")],-1)])),default:f(()=>[b("div",E,[p[6]||(p[6]=b("p",{class:"test-desc"},"测试用户列表、财务记录、分销商等数据的导出功能",-1)),b("div",k,[g(d,{type:"primary",size:"small",onClick:ee,loading:J.userExport},{default:f(()=>p[3]||(p[3]=[x(" 测试用户导出 ",-1)])),_:1,__:[3]},8,["loading"]),g(d,{type:"success",size:"small",onClick:se,loading:J.transactionExport},{default:f(()=>p[4]||(p[4]=[x(" 测试交易导出 ",-1)])),_:1,__:[4]},8,["loading"]),g(d,{type:"warning",size:"small",onClick:ae,loading:J.distributorExport},{default:f(()=>p[5]||(p[5]=[x(" 测试分销商导出 ",-1)])),_:1,__:[5]},8,["loading"])]),K.export?(h(),y("div",j,[g(m,{type:K.export.success?"success":"danger"},{default:f(()=>[x(u(K.export.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(O,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[7]||(p[7]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-view"}),b("span",null,"数据预览功能测试")],-1)])),default:f(()=>[b("div",z,[p[9]||(p[9]=b("p",{class:"test-desc"},"测试数据导出前的预览功能",-1)),b("div",T,[g(ue,{modelValue:H.value,"onUpdate:modelValue":p[0]||(p[0]=e=>H.value=e),placeholder:"选择数据类型",size:"small"},{default:f(()=>[g(re,{label:"用户数据",value:"users"}),g(re,{label:"订单数据",value:"orders"}),g(re,{label:"财务数据",value:"finance"})]),_:1},8,["modelValue"]),g(d,{type:"primary",size:"small",onClick:te,loading:J.preview,disabled:!H.value},{default:f(()=>p[8]||(p[8]=[x(" 测试预览功能 ",-1)])),_:1,__:[8]},8,["loading","disabled"])]),K.preview?(h(),y("div",C,[g(m,{type:K.preview.success?"success":"danger"},{default:f(()=>[x(u(K.preview.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(O,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[10]||(p[10]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-s-operation"}),b("span",null,"批量处理功能测试")],-1)])),default:f(()=>[b("div",P,[p[12]||(p[12]=b("p",{class:"test-desc"},"测试订单等数据的批量处理功能",-1)),b("div",V,[g(d,{type:"primary",size:"small",onClick:le,loading:J.batch},{default:f(()=>p[11]||(p[11]=[x(" 测试批量处理 ",-1)])),_:1,__:[11]},8,["loading"])]),K.batch?(h(),y("div",U,[g(m,{type:K.batch.success?"success":"danger"},{default:f(()=>[x(u(K.batch.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(O,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[13]||(p[13]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-warning"}),b("span",null,"告警设置功能测试")],-1)])),default:f(()=>[b("div",Z,[p[15]||(p[15]=b("p",{class:"test-desc"},"测试防红系统的告警设置功能",-1)),b("div",B,[g(d,{type:"primary",size:"small",onClick:ce,loading:J.alert},{default:f(()=>p[14]||(p[14]=[x(" 测试告警设置 ",-1)])),_:1,__:[14]},8,["loading"])]),K.alert?(h(),y("div",F,[g(m,{type:K.alert.success?"success":"danger"},{default:f(()=>[x(u(K.alert.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1})]),_:1}),N.value?(h(),_(v,{key:0,style:{"margin-top":"20px"}},{header:f(()=>p[16]||(p[16]=[b("div",{class:"card-header"},[b("span",null,"📊 测试结果汇总")],-1)])),default:f(()=>[b("div",I,[g(ie,{gutter:20},{default:f(()=>[g(O,{span:6},{default:f(()=>[b("div",L,[b("div",M,u(R.value),1),p[17]||(p[17]=b("div",{class:"summary-label"},"总测试数",-1))])]),_:1}),g(O,{span:6},{default:f(()=>[b("div",Q,[b("div",Y,u(S.value),1),p[18]||(p[18]=b("div",{class:"summary-label"},"成功测试",-1))])]),_:1}),g(O,{span:6},{default:f(()=>[b("div",$,[b("div",q,u(W.value),1),p[19]||(p[19]=b("div",{class:"summary-label"},"失败测试",-1))])]),_:1}),g(O,{span:6},{default:f(()=>[b("div",A,[b("div",G,u(Math.round(X.value))+"%",1),p[20]||(p[20]=b("div",{class:"summary-label"},"成功率",-1))])]),_:1})]),_:1})])]),_:1})):w("",!0)]),_:1})])}}},[["__scopeId","data-v-cd1e52ad"]]);export{H as default};
