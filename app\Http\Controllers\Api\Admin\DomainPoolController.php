<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\DomainPool;
use App\Services\AntiBlockService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

/**
 * 域名池管理控制器
 */
class DomainPoolController extends Controller
{
    protected AntiBlockService $antiBlockService;

    public function __construct(AntiBlockService $antiBlockService)
    {
        $this->antiBlockService = $antiBlockService;
    }

    /**
     * 获取域名池列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'status' => 'string|in:active,inactive',
                'search' => 'string|max:255',
                'sort_by' => 'string|in:created_at,name,total_domains,active_domains,group_count',
                'sort_order' => 'string|in:asc,desc'
            ]);

            $query = DomainPool::with('creator')
                ->withCount('groups');

            // 搜索
            if (!empty($params['search'])) {
                $search = $params['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // 状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 排序
            $sortBy = $params['sort_by'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // 分页
            $perPage = $params['per_page'] ?? 15;
            $result = $query->paginate($perPage);

            return $this->success([
                'data' => $result->items(),
                'total' => $result->total(),
                'per_page' => $result->perPage(),
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage()
            ], '获取域名池列表成功');

        } catch (\Exception $e) {
            Log::error('获取域名池列表失败', [
                'error' => $e->getMessage(),
                'params' => $request->all()
            ]);

            return $this->error('获取域名池列表失败');
        }
    }

    /**
     * 创建域名池
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:100|unique:domain_pools,name',
                'description' => 'nullable|string|max:1000',
                'domains' => 'required|array|min:1|max:50',
                'domains.*' => 'required|string|max:255|regex:/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                'check_interval' => 'integer|min:1|max:1440',
                'check_method' => 'string|in:http,ping,dns',
                'timeout' => 'integer|min:1|max:60',
                'retry_times' => 'integer|min:1|max:10',
                'switch_strategy' => 'string|in:immediate,delayed,manual',
                'switch_delay' => 'integer|min:0|max:60'
            ]);

            // 验证域名格式和去重
            $domains = array_unique($data['domains']);
            $validDomains = [];
            $invalidDomains = [];

            foreach ($domains as $domain) {
                if ($this->validateDomain($domain)) {
                    $validDomains[] = $domain;
                } else {
                    $invalidDomains[] = $domain;
                }
            }

            if (!empty($invalidDomains)) {
                return $this->error('以下域名格式不正确：' . implode(', ', $invalidDomains), 400);
            }

            $data['domains'] = $validDomains;
            $data['total_domains'] = count($validDomains);
            $data['active_domains'] = count($validDomains); // 初始假设都可用
            $data['blocked_domains'] = 0;
            $data['creator_id'] = auth()->id();

            $domainPool = DomainPool::create($data);

            // 异步检测域名状态
            dispatch(function () use ($domainPool) {
                $domainPool->checkDomainStatus();
            })->afterResponse();

            Log::info('域名池创建成功', [
                'pool_id' => $domainPool->id,
                'name' => $domainPool->name,
                'domain_count' => count($validDomains),
                'user_id' => auth()->id()
            ]);

            return $this->success($domainPool->load('creator'), '域名池创建成功');

        } catch (\Exception $e) {
            Log::error('域名池创建失败', [
                'error' => $e->getMessage(),
                'data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名池创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取域名池详情
     */
    public function show(int $id): JsonResponse
    {
        try {
            $domainPool = DomainPool::with(['creator', 'groups'])
                ->withCount('groups')
                ->find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            // 获取域名状态详情
            $domainStatuses = $this->antiBlockService->getDomainStatuses($id);
            $domainPool->domain_statuses = $domainStatuses;

            return $this->success($domainPool, '获取域名池详情成功');

        } catch (\Exception $e) {
            Log::error('获取域名池详情失败', [
                'pool_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取域名池详情失败');
        }
    }

    /**
     * 更新域名池
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            $data = $request->validate([
                'name' => [
                    'sometimes',
                    'required',
                    'string',
                    'max:100',
                    Rule::unique('domain_pools', 'name')->ignore($id)
                ],
                'description' => 'sometimes|nullable|string|max:1000',
                'domains' => 'sometimes|required|array|min:1|max:50',
                'domains.*' => 'required|string|max:255|regex:/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                'check_interval' => 'sometimes|integer|min:1|max:1440',
                'check_method' => 'sometimes|string|in:http,ping,dns',
                'timeout' => 'sometimes|integer|min:1|max:60',
                'retry_times' => 'sometimes|integer|min:1|max:10',
                'switch_strategy' => 'sometimes|string|in:immediate,delayed,manual',
                'switch_delay' => 'sometimes|integer|min:0|max:60',
                'status' => 'sometimes|string|in:active,inactive'
            ]);

            // 如果更新了域名列表
            if (isset($data['domains'])) {
                $domains = array_unique($data['domains']);
                $validDomains = [];
                $invalidDomains = [];

                foreach ($domains as $domain) {
                    if ($this->validateDomain($domain)) {
                        $validDomains[] = $domain;
                    } else {
                        $invalidDomains[] = $domain;
                    }
                }

                if (!empty($invalidDomains)) {
                    return $this->error('以下域名格式不正确：' . implode(', ', $invalidDomains), 400);
                }

                $data['domains'] = $validDomains;
                $data['total_domains'] = count($validDomains);
                
                // 如果域名列表发生变化，重新检测
                if ($domainPool->domains !== $validDomains) {
                    $data['active_domains'] = count($validDomains);
                    $data['blocked_domains'] = 0;
                    $data['last_check_at'] = null;
                }
            }

            $domainPool->update($data);

            // 如果域名列表更新了，异步检测状态
            if (isset($data['domains'])) {
                dispatch(function () use ($domainPool) {
                    $domainPool->checkDomainStatus();
                })->afterResponse();
            }

            Log::info('域名池更新成功', [
                'pool_id' => $id,
                'user_id' => auth()->id()
            ]);

            return $this->success($domainPool->load('creator'), '域名池更新成功');

        } catch (\Exception $e) {
            Log::error('域名池更新失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名池更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除域名池
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            // 检查是否有群组在使用
            $groupCount = $domainPool->groups()->count();
            if ($groupCount > 0) {
                return $this->error("该域名池正在被 {$groupCount} 个群组使用，无法删除", 400);
            }

            $domainPool->delete();

            DB::commit();

            Log::info('域名池删除成功', [
                'pool_id' => $id,
                'name' => $domainPool->name,
                'user_id' => auth()->id()
            ]);

            return $this->success(null, '域名池删除成功');

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('域名池删除失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名池删除失败：' . $e->getMessage());
        }
    }

    /**
     * 检测域名池状态
     */
    public function check(int $id): JsonResponse
    {
        try {
            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            // 执行域名检测
            $results = $domainPool->checkDomainStatus();

            Log::info('域名池检测完成', [
                'pool_id' => $id,
                'active_domains' => $domainPool->active_domains,
                'blocked_domains' => $domainPool->blocked_domains,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'pool' => $domainPool->fresh(),
                'check_results' => $results
            ], '域名池检测完成');

        } catch (\Exception $e) {
            Log::error('域名池检测失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名池检测失败：' . $e->getMessage());
        }
    }

    /**
     * 切换域名池状态
     */
    public function toggleStatus(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|string|in:active,inactive'
            ]);

            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            $status = $request->input('status');
            $domainPool->update(['status' => $status]);

            Log::info('域名池状态切换成功', [
                'pool_id' => $id,
                'status' => $status,
                'user_id' => auth()->id()
            ]);

            return $this->success($domainPool, '状态更新成功');

        } catch (\Exception $e) {
            Log::error('域名池状态切换失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('状态更新失败：' . $e->getMessage());
        }
    }

    /**
     * 添加域名到域名池
     */
    public function addDomains(Request $request, int $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'domains' => 'required|array|min:1|max:20',
                'domains.*' => 'required|string|max:255|regex:/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/'
            ]);

            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            $newDomains = array_unique($data['domains']);
            $validDomains = [];
            $invalidDomains = [];
            $existingDomains = [];

            foreach ($newDomains as $domain) {
                if (!$this->validateDomain($domain)) {
                    $invalidDomains[] = $domain;
                    continue;
                }

                if (in_array($domain, $domainPool->domains ?? [])) {
                    $existingDomains[] = $domain;
                    continue;
                }

                $validDomains[] = $domain;
            }

            if (!empty($invalidDomains)) {
                return $this->error('以下域名格式不正确：' . implode(', ', $invalidDomains), 400);
            }

            if (empty($validDomains)) {
                $message = !empty($existingDomains) ? '所有域名都已存在' : '没有有效的域名可添加';
                return $this->error($message, 400);
            }

            $addedCount = $domainPool->addDomains($validDomains);

            // 异步检测新添加的域名
            dispatch(function () use ($domainPool) {
                $domainPool->checkDomainStatus();
            })->afterResponse();

            Log::info('域名添加成功', [
                'pool_id' => $id,
                'added_count' => $addedCount,
                'domains' => $validDomains,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'added_count' => $addedCount,
                'added_domains' => $validDomains,
                'existing_domains' => $existingDomains,
                'invalid_domains' => $invalidDomains,
                'pool' => $domainPool->fresh()
            ], "成功添加 {$addedCount} 个域名");

        } catch (\Exception $e) {
            Log::error('域名添加失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名添加失败：' . $e->getMessage());
        }
    }

    /**
     * 从域名池移除域名
     */
    public function removeDomains(Request $request, int $id): JsonResponse
    {
        try {
            $data = $request->validate([
                'domains' => 'required|array|min:1',
                'domains.*' => 'required|string|max:255'
            ]);

            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            $domainsToRemove = $data['domains'];
            $removedDomains = [];
            $notFoundDomains = [];

            foreach ($domainsToRemove as $domain) {
                if ($domainPool->removeDomain($domain)) {
                    $removedDomains[] = $domain;
                } else {
                    $notFoundDomains[] = $domain;
                }
            }

            // 检查是否还有剩余域名
            $domainPool = $domainPool->fresh();
            if (empty($domainPool->domains)) {
                return $this->error('域名池至少需要保留一个域名', 400);
            }

            Log::info('域名移除成功', [
                'pool_id' => $id,
                'removed_count' => count($removedDomains),
                'domains' => $removedDomains,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'removed_count' => count($removedDomains),
                'removed_domains' => $removedDomains,
                'not_found_domains' => $notFoundDomains,
                'pool' => $domainPool
            ], "成功移除 " . count($removedDomains) . " 个域名");

        } catch (\Exception $e) {
            Log::error('域名移除失败', [
                'pool_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('域名移除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取域名池统计信息
     */
    public function stats(int $id): JsonResponse
    {
        try {
            $domainPool = DomainPool::find($id);

            if (!$domainPool) {
                return $this->error('域名池不存在', 404);
            }

            $stats = $domainPool->getStats();

            return $this->success($stats, '获取统计信息成功');

        } catch (\Exception $e) {
            Log::error('获取域名池统计失败', [
                'pool_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取统计信息失败');
        }
    }

    /**
     * 批量操作域名池
     */
    public function batchAction(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'action' => 'required|string|in:enable,disable,delete,check',
                'pool_ids' => 'required|array|min:1',
                'pool_ids.*' => 'integer|exists:domain_pools,id'
            ]);

            $action = $data['action'];
            $poolIds = $data['pool_ids'];
            $results = [];

            foreach ($poolIds as $poolId) {
                try {
                    $result = $this->executeBatchAction($action, $poolId);
                    $results[$poolId] = $result;
                } catch (\Exception $e) {
                    $results[$poolId] = [
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
            }

            $successCount = collect($results)->where('success', true)->count();
            $totalCount = count($results);

            Log::info('域名池批量操作完成', [
                'action' => $action,
                'total' => $totalCount,
                'success' => $successCount,
                'user_id' => auth()->id()
            ]);

            return $this->success([
                'action' => $action,
                'total_count' => $totalCount,
                'success_count' => $successCount,
                'results' => $results
            ], "批量操作完成，成功处理 {$successCount}/{$totalCount} 个域名池");

        } catch (\Exception $e) {
            Log::error('域名池批量操作失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }

    // 私有方法

    /**
     * 验证域名格式
     */
    private function validateDomain(string $domain): bool
    {
        // 基本格式验证
        if (!filter_var('http://' . $domain, FILTER_VALIDATE_URL)) {
            return false;
        }

        // 检查域名长度
        if (strlen($domain) > 253) {
            return false;
        }

        // 检查是否包含有效的TLD
        $parts = explode('.', $domain);
        if (count($parts) < 2) {
            return false;
        }

        $tld = end($parts);
        if (strlen($tld) < 2 || !ctype_alpha($tld)) {
            return false;
        }

        return true;
    }

    /**
     * 执行批量操作
     */
    private function executeBatchAction(string $action, int $poolId): array
    {
        $domainPool = DomainPool::find($poolId);

        if (!$domainPool) {
            return [
                'success' => false,
                'message' => '域名池不存在'
            ];
        }

        switch ($action) {
            case 'enable':
                $domainPool->enable();
                return [
                    'success' => true,
                    'message' => '启用成功'
                ];

            case 'disable':
                $domainPool->disable();
                return [
                    'success' => true,
                    'message' => '禁用成功'
                ];

            case 'delete':
                $groupCount = $domainPool->groups()->count();
                if ($groupCount > 0) {
                    return [
                        'success' => false,
                        'message' => "正在被 {$groupCount} 个群组使用，无法删除"
                    ];
                }
                $domainPool->delete();
                return [
                    'success' => true,
                    'message' => '删除成功'
                ];

            case 'check':
                $domainPool->checkDomainStatus();
                return [
                    'success' => true,
                    'message' => '检测完成'
                ];

            default:
                return [
                    'success' => false,
                    'message' => '不支持的操作'
                ];
        }
    }
}