<template>
  <div class="map-data-manager">
    <div class="page-header">
      <h1>🗺️ 地图数据管理工具</h1>
      <p>下载和管理真实的中国地图数据，用于数据大屏可视化</p>
    </div>

    <div class="data-sources">
      <h2>📡 可用数据源</h2>
      <div class="source-grid">
        <div 
          v-for="source in dataSources" 
          :key="source.name"
          class="source-card"
          :class="{ recommended: source.recommended }"
        >
          <div class="source-header">
            <h3>{{ source.title }}</h3>
            <span v-if="source.recommended" class="recommended-badge">推荐</span>
          </div>
          
          <p class="source-description">{{ source.description }}</p>
          
          <div class="source-url">
            <code>{{ source.url }}</code>
          </div>
          
          <div class="source-actions">
            <el-button 
              type="primary" 
              :loading="downloading === source.name"
              @click="downloadMapData(source.name)"
            >
              <i class="el-icon-download"></i>
              下载数据
            </el-button>
            
            <el-button 
              type="success" 
              :loading="testing === source.name"
              @click="testMapData(source.name)"
            >
              <i class="el-icon-view"></i>
              测试预览
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="instructions">
      <h2>📋 使用说明</h2>
      
      <div class="instruction-section">
        <h3>🎯 方案1：在线使用（推荐）</h3>
        <ol>
          <li>点击"测试预览"按钮验证数据源可用性</li>
          <li>数据大屏会自动从在线数据源加载真实地图</li>
          <li>如果在线数据源不可用，会自动降级到本地数据</li>
        </ol>
      </div>

      <div class="instruction-section">
        <h3>📥 方案2：下载到本地</h3>
        <ol>
          <li>点击"下载数据"按钮下载真实地图数据</li>
          <li>将下载的JSON文件重命名为 <code>china.json</code></li>
          <li>替换 <code>admin/public/data/china.json</code> 文件</li>
          <li>重启开发服务器，地图将使用真实数据</li>
        </ol>
      </div>

      <div class="instruction-section">
        <h3>⚙️ 技术细节</h3>
        <ul>
          <li><strong>数据格式</strong>：GeoJSON格式，包含省份边界坐标</li>
          <li><strong>文件大小</strong>：真实数据约500KB-2MB</li>
          <li><strong>精度级别</strong>：DataV数据源提供最高精度</li>
          <li><strong>更新频率</strong>：官方数据源定期更新</li>
        </ul>
      </div>
    </div>

    <div class="current-status">
      <h2>📊 当前状态</h2>
      <div class="status-grid">
        <div class="status-item">
          <div class="status-label">地图数据源</div>
          <div class="status-value">{{ currentMapSource }}</div>
        </div>
        <div class="status-item">
          <div class="status-label">数据加载状态</div>
          <div class="status-value" :class="mapLoadStatus.class">
            {{ mapLoadStatus.text }}
          </div>
        </div>
        <div class="status-item">
          <div class="status-label">省份数量</div>
          <div class="status-value">{{ provinceCount }}</div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog 
      v-model="previewVisible" 
      title="地图数据预览" 
      width="80%"
      :before-close="closePreview"
    >
      <div class="preview-container">
        <div ref="previewChartRef" class="preview-chart"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { getMapDataSources, downloadRealMapData, loadChinaMap } from '@/utils/mapLoader'

// 响应式数据
const dataSources = ref([])
const downloading = ref('')
const testing = ref('')
const previewVisible = ref(false)
const previewChartRef = ref(null)
const previewChart = ref(null)
const currentMapSource = ref('本地简化版')
const mapLoadStatus = ref({ text: '未知', class: 'unknown' })
const provinceCount = ref(0)

// 方法
const downloadMapData = async (sourceName) => {
  downloading.value = sourceName
  
  try {
    const success = await downloadRealMapData(sourceName)
    if (success) {
      ElMessage.success('地图数据下载成功！请查看浏览器下载文件夹')
    } else {
      ElMessage.error('地图数据下载失败，请检查网络连接')
    }
  } catch (error) {
    ElMessage.error(`下载失败: ${error.message}`)
  } finally {
    downloading.value = ''
  }
}

const testMapData = async (sourceName) => {
  testing.value = sourceName
  
  try {
    // 加载地图数据
    const success = await loadChinaMap(sourceName)
    
    if (success) {
      ElMessage.success('地图数据测试成功！')
      showPreview(sourceName)
    } else {
      ElMessage.error('地图数据测试失败，数据源可能不可用')
    }
  } catch (error) {
    ElMessage.error(`测试失败: ${error.message}`)
  } finally {
    testing.value = ''
  }
}

const showPreview = (sourceName) => {
  previewVisible.value = true
  
  // 等待对话框渲染完成
  setTimeout(() => {
    if (previewChartRef.value) {
      previewChart.value = echarts.init(previewChartRef.value)
      
      const option = {
        title: {
          text: `${sourceName} 数据源预览`,
          left: 'center',
          textStyle: { color: '#333' }
        },
        series: [{
          type: 'map',
          map: 'china',
          roam: true,
          itemStyle: {
            borderColor: '#333',
            borderWidth: 1,
            areaColor: '#4a90e2'
          },
          emphasis: {
            itemStyle: {
              areaColor: '#2c5aa0'
            }
          }
        }]
      }
      
      previewChart.value.setOption(option)
    }
  }, 100)
}

const closePreview = () => {
  if (previewChart.value) {
    previewChart.value.dispose()
    previewChart.value = null
  }
  previewVisible.value = false
}

const checkCurrentStatus = async () => {
  try {
    // 尝试加载当前地图数据
    const success = await loadChinaMap('local')
    
    if (success) {
      mapLoadStatus.value = { text: '正常', class: 'success' }
      // 这里可以添加获取省份数量的逻辑
      provinceCount.value = 31
    } else {
      mapLoadStatus.value = { text: '失败', class: 'error' }
    }
  } catch (error) {
    mapLoadStatus.value = { text: '错误', class: 'error' }
  }
}

// 生命周期
onMounted(() => {
  dataSources.value = getMapDataSources()
  checkCurrentStatus()
})

onUnmounted(() => {
  if (previewChart.value) {
    previewChart.value.dispose()
  }
})
</script>

<style scoped>
.map-data-manager {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 28px;
  margin-bottom: 8px;
  color: #333;
}

.page-header p {
  font-size: 16px;
  color: #666;
}

.data-sources {
  margin-bottom: 32px;
}

.data-sources h2 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #333;
}

.source-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.source-card {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  transition: all 0.3s ease;
}

.source-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.source-card.recommended {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff, #ffffff);
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.source-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.recommended-badge {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.source-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.source-url {
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.source-url code {
  font-size: 12px;
  color: #333;
  word-break: break-all;
}

.source-actions {
  display: flex;
  gap: 12px;
}

.instructions {
  margin-bottom: 32px;
}

.instructions h2 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #333;
}

.instruction-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.instruction-section h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #333;
}

.instruction-section ol,
.instruction-section ul {
  margin: 0;
  padding-left: 20px;
}

.instruction-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.instruction-section code {
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
}

.current-status h2 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  padding: 16px;
  background: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.status-value.success {
  color: #67c23a;
}

.status-value.error {
  color: #f56c6c;
}

.status-value.unknown {
  color: #909399;
}

.preview-container {
  height: 500px;
}

.preview-chart {
  width: 100%;
  height: 100%;
}
</style>
