<?php

namespace App\Http\Controllers;

use App\Models\WechatGroup;
use App\Models\Order;
use App\Services\PaymentService;
use App\Services\IPLocationService;
use App\Services\BrowserDetectionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 群组分享页面控制器
 * 处理群组的分享页面展示和支付流程
 */
class GroupShareController extends Controller
{
    private PaymentService $paymentService;
    private IPLocationService $ipLocationService;
    private BrowserDetectionService $browserService;

    public function __construct(
        PaymentService $paymentService,
        IPLocationService $ipLocationService,
        BrowserDetectionService $browserService
    ) {
        $this->paymentService = $paymentService;
        $this->ipLocationService = $ipLocationService;
        $this->browserService = $browserService;
    }

    /**
     * 群组分享页面（兼容源码包的share方法）
     */
    public function share(Request $request, $id)
    {
        try {
            $group = WechatGroup::find($id);
            
            if (!$group) {
                return $this->redirectToError('群组不存在');
            }

            // 检查是否有进行中的订单
            $orderNo = session('orderid');
            $orderExpireTime = session('times');
            
            if ($orderNo && $orderExpireTime && time() < $orderExpireTime) {
                $order = Order::where('order_no', $orderNo)
                             ->where('wechat_group_id', $id)
                             ->first();
                
                if ($order && $order->isPaid()) {
                    return redirect()->route('group.success', ['id' => $id]);
                }
            }

            // 获取用户城市
            $userIP = $this->ipLocationService->getClientIP();
            $userCity = $this->ipLocationService->getCity($userIP);
            
            // 生成城市替换后的标题
            $displayTitle = $group->getCityReplacedTitle($userCity);
            
            // 生成虚拟成员数据
            $virtualMembers = $group->generateVirtualMembers();
            
            // 生成虚拟群友评价
            $virtualReviews = $group->generateVirtualReviews();
            
            // 生成虚拟活动通知
            $virtualNotifications = $this->generateVirtualNotifications($group);

            // 更新浏览次数
            $group->increment('view_count');
            $group->increment('today_views');

            // 准备页面数据
            $pageData = [
                'group' => $group,
                'display_title' => $displayTitle,
                'original_title' => $group->title,
                'user_city' => $userCity,
                'has_city_placeholder' => strpos($group->title, 'xxx') !== false,
                'virtual_members' => $virtualMembers,
                'virtual_reviews' => $virtualReviews,
                'virtual_notifications' => $virtualNotifications,
                'formatted_faq' => $group->formatted_faq_content,
                'browser_info' => $this->browserService->getBrowserInfo(),
                'payment_methods' => $group->getDetailedPaymentMethods(),
                'stats' => [
                    'view_count' => $group->view_count,
                    'like_count' => $group->like_count,
                    'want_see_count' => $group->want_see_count,
                    'read_count_display' => $group->read_count_display ?? '10万+',
                    'virtual_stats' => $group->virtual_stats,
                ],
                'content' => [
                    'group_intro_title' => $group->group_intro_title ?? '群简介',
                    'group_intro_content' => $group->group_intro_content ?? '',
                    'faq_title' => $group->faq_title ?? '常见问题',
                    'extra_title1' => $group->extra_title1 ?? '',
                    'extra_content1' => $group->extra_content1 ?? '',
                    'extra_title2' => $group->extra_title2 ?? '',
                    'extra_content2' => $group->extra_content2 ?? '',
                    'extra_title3' => $group->extra_title3 ?? '',
                    'extra_content3' => $group->extra_content3 ?? '',
                ],
                'service' => [
                    'show_customer_service' => $group->show_customer_service == 2,
                    'customer_service_avatar' => $group->customer_service_avatar ?? '',
                    'customer_service_title' => $group->customer_service_title ?? 'VIP专属客服',
                    'customer_service_desc' => $group->customer_service_desc ?? '出现不能付款，不能入群等问题，请联系我！',
                    'customer_service_qr' => $group->customer_service_qr ?? '',
                    'ad_qr_code' => $group->ad_qr_code ?? '',
                ],
            ];

            // 根据展示类型返回不同的视图
            $viewName = $group->display_type == 2 ? 'group.share_image' : 'group.share';
            
            return view($viewName, $pageData);

        } catch (\Exception $e) {
            Log::error('群组分享页面异常', [
                'group_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->redirectToError('页面加载失败');
        }
    }

    /**
     * 创建支付订单（兼容源码包的paylist方法）
     */
    public function createPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:wechat_groups,id',
            'title' => 'nullable|string',
            'customer_name' => 'nullable|string|max:50',
            'customer_phone' => 'nullable|string|max:20',
            'customer_wechat' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 0,
                'msg' => $validator->errors()->first()
            ]);
        }

        try {
            $groupId = $request->input('id');
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return response()->json([
                    'status' => 0,
                    'msg' => '群组不存在'
                ]);
            }

            // 检查群组状态
            if ($group->status != WechatGroup::STATUS_ACTIVE) {
                return response()->json([
                    'status' => 0,
                    'msg' => '群组不可用'
                ]);
            }

            // 检查支付功能状态
            if ($group->payment_status == 2) {
                return response()->json([
                    'status' => 0,
                    'msg' => '支付功能已关闭'
                ]);
            }

            // 获取可用的支付方式
            $paymentMethods = $group->getDetailedPaymentMethods();
            if (empty($paymentMethods)) {
                return response()->json([
                    'status' => 0,
                    'msg' => '暂无可用的支付方式'
                ]);
            }

            // 随机选择一个支付方式
            $selectedPayment = $paymentMethods[array_rand($paymentMethods)];

            // 获取用户信息
            $userIP = $this->ipLocationService->getClientIP();
            $userCity = $this->ipLocationService->getCity($userIP);
            $finalTitle = $request->input('title') ?: $group->getCityReplacedTitle($userCity);

            // 创建订单数据
            $orderData = [
                'wechat_group_id' => $group->id,
                'user_id' => null, // 游客订单
                'amount' => $group->price,
                'customer_name' => $request->input('customer_name', ''),
                'customer_phone' => $request->input('customer_phone', ''),
                'customer_wechat' => $request->input('customer_wechat', ''),
                'customer_ip' => $userIP,
                'customer_city' => $userCity,
                'payment_methods' => $paymentMethods,
            ];

            // 创建订单
            $orderResult = $this->paymentService->createOrder($orderData);
            $order = Order::where('order_no', $orderResult['order_no'])->first();

            // 创建支付
            $paymentResult = $this->paymentService->createPayment($order, $selectedPayment);

            // 保存订单信息到session
            session([
                'orderid' => $order->order_no,
                'times' => time() + 86400 // 24小时过期
            ]);

            // 根据支付类型返回不同的响应
            if ($paymentResult['type'] === 'redirect') {
                return response()->json([
                    'status' => 1,
                    'msg' => $paymentResult['redirect_url']
                ]);
            } elseif ($paymentResult['type'] === 'qrcode') {
                return response()->json([
                    'status' => 2,
                    'msg' => view('payment.qrcode', [
                        'qr_code' => $paymentResult['qr_code'],
                        'amount' => $order->amount,
                        'order_no' => $order->order_no,
                        'payment_method' => $selectedPayment['channel_name'],
                    ])->render()
                ]);
            } else {
                return response()->json([
                    'status' => 2,
                    'msg' => view('payment.bank_info', [
                        'bank_info' => $paymentResult['payload'],
                        'amount' => $order->amount,
                        'order_no' => $order->order_no,
                    ])->render()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('创建支付订单失败', [
                'group_id' => $request->input('id'),
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'status' => 0,
                'msg' => '创建订单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 查询订单状态（用于轮询）
     */
    public function queryOrderStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'orderid' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 0,
                'msg' => '参数错误'
            ]);
        }

        try {
            $groupId = $request->input('id');
            $orderNo = $request->input('orderid');
            
            $order = Order::where('order_no', $orderNo)
                         ->where('wechat_group_id', $groupId)
                         ->first();

            if (!$order) {
                return response()->json([
                    'status' => 2,
                    'msg' => route('group.share', ['id' => $groupId])
                ]);
            }

            if ($order->isPaid()) {
                return response()->json([
                    'status' => 1,
                    'msg' => route('group.success', ['id' => $groupId])
                ]);
            }

            // 订单仍在等待支付
            return response()->json([
                'status' => 0,
                'msg' => '等待支付'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 2,
                'msg' => route('group.share', ['id' => $request->input('id')])
            ]);
        }
    }

    /**
     * 生成虚拟活动通知
     */
    private function generateVirtualNotifications(WechatGroup $group): array
    {
        $nicknames = [
            '李家闵', '青狐', '暧昧的', '笑', '黄六', '小雨点', '阳光少年',
            '梦想家', '星空下', '微笑天使', '快乐鸟', '自由风', '温柔猫'
        ];

        $notifications = [];
        for ($i = 0; $i < 5; $i++) {
            $nickname = $nicknames[array_rand($nicknames)];
            $notifications[] = [
                'nickname' => $nickname . '***',
                'action' => '刚刚支付了' . $group->price . '元入群',
                'time' => now()->subMinutes(rand(1, 30))->format('H:i')
            ];
        }

        return $notifications;
    }

    /**
     * 重定向到错误页面
     */
    private function redirectToError(string $message): \Illuminate\Http\Response
    {
        $fallbackUrl = 'https://www.baidu.com/s?wd=' . urlencode($message);
        return response($this->browserService->generateBrowserGuidePage($fallbackUrl))
                ->header('Content-Type', 'text/html');
    }
}