/**
 * 随机金额调整工具函数
 * 用于避免支付风控问题
 */

/**
 * 计算随机调整后的支付金额
 * @param {number} basePrice 基础价格
 * @param {boolean} enableRandom 是否启用随机调整
 * @param {number} minAmount 最小随机金额
 * @param {number} maxAmount 最大随机金额
 * @returns {Object} 包含调整后金额和调整信息的对象
 */
export function calculateRandomAmount(basePrice, enableRandom = false, minAmount = 0.01, maxAmount = 0.90) {
  // 如果未启用随机调整或价格为0，直接返回原价格
  if (!enableRandom || basePrice <= 0) {
    return {
      originalPrice: basePrice,
      finalPrice: basePrice,
      adjustment: 0,
      isAdjusted: false,
      note: null
    }
  }

  // 生成随机调整金额（可正可负）
  const randomRange = maxAmount - minAmount
  const randomValue = Math.random() * randomRange + minAmount
  const isPositive = Math.random() > 0.5
  const adjustment = isPositive ? randomValue : -randomValue
  
  // 计算最终价格，确保不小于0.01
  const finalPrice = Math.max(0.01, basePrice + adjustment)
  
  return {
    originalPrice: basePrice,
    finalPrice: Math.round(finalPrice * 100) / 100, // 保留两位小数
    adjustment: Math.round(adjustment * 100) / 100,
    isAdjusted: true,
    note: `为避免风控，实际支付金额已微调 ${adjustment >= 0 ? '+' : ''}${adjustment.toFixed(2)}元`
  }
}

/**
 * 生成随机金额调整的说明文本
 * @param {number} basePrice 基础价格
 * @param {number} minAmount 最小随机金额
 * @param {number} maxAmount 最大随机金额
 * @returns {string} 说明文本
 */
export function generateRandomAmountDescription(basePrice, minAmount = 0.01, maxAmount = 0.90) {
  const minPrice = Math.max(0.01, basePrice - maxAmount)
  const maxPrice = basePrice + maxAmount
  
  return `为避免大量相同金额支付触发银行风控，实际支付金额将在 ¥${minPrice.toFixed(2)} ~ ¥${maxPrice.toFixed(2)} 范围内随机调整`
}

/**
 * 验证随机金额配置的有效性
 * @param {number} minAmount 最小随机金额
 * @param {number} maxAmount 最大随机金额
 * @returns {Object} 验证结果
 */
export function validateRandomAmountConfig(minAmount, maxAmount) {
  const errors = []
  
  if (minAmount < 0.01) {
    errors.push('最小随机金额不能小于0.01元')
  }
  
  if (maxAmount > 0.99) {
    errors.push('最大随机金额不能大于0.99元')
  }
  
  if (minAmount > maxAmount) {
    errors.push('最小随机金额不能大于最大随机金额')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化随机金额调整的配置信息
 * @param {Object} config 随机金额配置
 * @returns {string} 格式化后的配置信息
 */
export function formatRandomAmountConfig(config) {
  if (!config.enable_random_amount) {
    return '未启用随机金额调整'
  }
  
  return `随机调整范围：±${config.random_amount_min.toFixed(2)}~${config.random_amount_max.toFixed(2)}元`
}

/**
 * 为支付页面生成随机金额调整的用户提示
 * @param {Object} amountResult calculateRandomAmount的返回结果
 * @returns {Object} 包含提示信息的对象
 */
export function generatePaymentTips(amountResult) {
  if (!amountResult.isAdjusted) {
    return {
      showTip: false,
      title: '',
      message: '',
      type: 'info'
    }
  }
  
  const adjustmentText = amountResult.adjustment >= 0 
    ? `增加了${amountResult.adjustment.toFixed(2)}元`
    : `减少了${Math.abs(amountResult.adjustment).toFixed(2)}元`
  
  return {
    showTip: true,
    title: '支付金额微调说明',
    message: `为避免银行风控，支付金额已自动${adjustmentText}。这是正常的风控规避措施，不会影响您的权益。`,
    type: 'info'
  }
}

/**
 * 生成随机金额调整的统计信息
 * @param {Array} payments 支付记录数组
 * @returns {Object} 统计信息
 */
export function generateRandomAmountStats(payments) {
  const adjustedPayments = payments.filter(p => p.isAdjusted)
  const totalAdjustments = adjustedPayments.reduce((sum, p) => sum + Math.abs(p.adjustment), 0)
  const avgAdjustment = adjustedPayments.length > 0 ? totalAdjustments / adjustedPayments.length : 0
  
  return {
    totalPayments: payments.length,
    adjustedPayments: adjustedPayments.length,
    adjustmentRate: payments.length > 0 ? (adjustedPayments.length / payments.length * 100).toFixed(1) : '0.0',
    totalAdjustments: totalAdjustments.toFixed(2),
    avgAdjustment: avgAdjustment.toFixed(2),
    maxPositiveAdjustment: Math.max(...adjustedPayments.map(p => p.adjustment), 0).toFixed(2),
    maxNegativeAdjustment: Math.min(...adjustedPayments.map(p => p.adjustment), 0).toFixed(2)
  }
}

/**
 * 创建随机金额调整的默认配置
 * @returns {Object} 默认配置
 */
export function createDefaultRandomAmountConfig() {
  return {
    enable_random_amount: false,
    random_amount_min: 0.01,
    random_amount_max: 0.90
  }
}

/**
 * 检查是否应该启用随机金额调整（基于价格范围的建议）
 * @param {number} price 群组价格
 * @returns {Object} 建议信息
 */
export function getRandomAmountRecommendation(price) {
  if (price === 0) {
    return {
      recommend: false,
      reason: '免费群组无需启用随机金额调整'
    }
  }
  
  if (price < 1) {
    return {
      recommend: false,
      reason: '价格过低，随机调整可能影响用户体验'
    }
  }
  
  if (price >= 1 && price <= 100) {
    return {
      recommend: true,
      reason: '该价格区间建议启用随机金额调整以避免风控'
    }
  }
  
  if (price > 100) {
    return {
      recommend: true,
      reason: '高价格群组强烈建议启用随机金额调整'
    }
  }
  
  return {
    recommend: false,
    reason: '请根据实际情况决定是否启用'
  }
}

export default {
  calculateRandomAmount,
  generateRandomAmountDescription,
  validateRandomAmountConfig,
  formatRandomAmountConfig,
  generatePaymentTips,
  generateRandomAmountStats,
  createDefaultRandomAmountConfig,
  getRandomAmountRecommendation
}
