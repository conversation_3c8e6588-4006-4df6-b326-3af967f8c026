<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 性能监控中间件
 * 记录API请求的性能指标
 */
class PerformanceMonitor
{
    /**
     * 处理请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 记录开始时间
        $startTime = microtime(true);
        
        // 记录开始时的内存使用
        $startMemory = memory_get_usage();
        
        // 记录开始时的数据库查询数
        $startQueryCount = count(DB::getQueryLog());
        
        // 处理请求
        $response = $next($request);
        
        // 只监控API请求
        if (strpos($request->path(), 'api/') === 0) {
            // 计算执行时间
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
            
            // 计算内存使用
            $endMemory = memory_get_usage();
            $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // MB
            
            // 计算数据库查询数
            $endQueryCount = count(DB::getQueryLog());
            $queryCount = $endQueryCount - $startQueryCount;
            
            // 记录性能指标
            $this->logPerformance($request, $response, $executionTime, $memoryUsage, $queryCount);
            
            // 添加性能指标到响应头
            $response->header('X-Execution-Time', $executionTime . 'ms');
            $response->header('X-Memory-Usage', $memoryUsage . 'MB');
            $response->header('X-DB-Queries', $queryCount);
            
            // 如果执行时间超过阈值，记录慢请求
            if ($executionTime > 1000) { // 1秒
                $this->logSlowRequest($request, $executionTime, $queryCount);
            }
            
            // 记录到性能日志表
            $this->savePerformanceLog($request, $executionTime, $memoryUsage, $queryCount);
        }
        
        return $response;
    }
    
    /**
     * 记录性能指标
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Http\Response  $response
     * @param  float  $executionTime
     * @param  float  $memoryUsage
     * @param  int  $queryCount
     * @return void
     */
    protected function logPerformance($request, $response, $executionTime, $memoryUsage, $queryCount)
    {
        // 只记录详细日志在非生产环境
        if (app()->environment('local', 'development', 'testing')) {
            Log::channel('performance')->info('API性能', [
                'path' => $request->path(),
                'method' => $request->method(),
                'execution_time' => $executionTime . 'ms',
                'memory_usage' => $memoryUsage . 'MB',
                'query_count' => $queryCount,
                'status' => $response->getStatusCode(),
                'user_id' => $request->user() ? $request->user()->id : null,
                'ip' => $request->ip(),
            ]);
        }
    }
    
    /**
     * 记录慢请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  float  $executionTime
     * @param  int  $queryCount
     * @return void
     */
    protected function logSlowRequest($request, $executionTime, $queryCount)
    {
        // 获取慢查询
        $slowQueries = collect(DB::getQueryLog())
            ->filter(function ($query) {
                return $query['time'] > 100; // 100ms
            })
            ->map(function ($query) {
                return [
                    'sql' => $query['query'],
                    'bindings' => $query['bindings'],
                    'time' => $query['time'] . 'ms',
                ];
            })
            ->toArray();
        
        Log::channel('slow')->warning('慢API请求', [
            'path' => $request->path(),
            'method' => $request->method(),
            'execution_time' => $executionTime . 'ms',
            'query_count' => $queryCount,
            'user_id' => $request->user() ? $request->user()->id : null,
            'ip' => $request->ip(),
            'slow_queries' => $slowQueries,
        ]);
    }
    
    /**
     * 保存性能日志到数据库
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  float  $executionTime
     * @param  float  $memoryUsage
     * @param  int  $queryCount
     * @return void
     */
    protected function savePerformanceLog($request, $executionTime, $memoryUsage, $queryCount)
    {
        // 只在生产环境记录到数据库
        if (app()->environment('production')) {
            try {
                DB::table('performance_logs')->insert([
                    'route' => $request->path(),
                    'method' => $request->method(),
                    'response_time' => $executionTime,
                    'memory_usage' => $memoryUsage * 1024 * 1024, // 转换为字节
                    'db_queries' => $queryCount,
                    'status_code' => 200, // 假设成功
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'user_id' => $request->user() ? $request->user()->id : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } catch (\Exception $e) {
                // 忽略错误，不影响主流程
                Log::error('保存性能日志失败', ['error' => $e->getMessage()]);
            }
        }
    }
}