<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑角色' : '创建角色'"
    width="600px"
    :before-close="handleClose"
    class="modern-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="large"
    >
      <el-form-item label="角色标识" prop="name">
        <el-input 
          v-model="form.name" 
          placeholder="请输入角色标识（英文）"
          :disabled="isEdit"
        />
        <div class="form-tip">角色标识用于系统内部识别，创建后不可修改</div>
      </el-form-item>
      
      <el-form-item label="角色名称" prop="display_name">
        <el-input 
          v-model="form.display_name" 
          placeholder="请输入角色显示名称"
        />
      </el-form-item>
      
      <el-form-item label="角色描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
        />
      </el-form-item>
      
      <el-form-item label="角色类型" prop="is_system">
        <el-radio-group v-model="form.is_system">
          <el-radio :label="false">自定义角色</el-radio>
          <el-radio :label="true">系统角色</el-radio>
        </el-radio-group>
        <div class="form-tip">系统角色具有特殊权限，请谨慎选择</div>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="active">启用</el-radio>
          <el-radio label="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isEdit = computed(() => !!props.roleData?.id)

// 表单数据
const form = reactive({
  name: '',
  display_name: '',
  description: '',
  is_system: false,
  status: 'active'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    { pattern: /^[a-z_]+$/, message: '角色标识只能包含小写字母和下划线', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}

// 监听角色数据变化
watch(() => props.roleData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      name: newData.name || '',
      display_name: newData.display_name || '',
      description: newData.description || '',
      is_system: newData.is_system || false,
      status: newData.status || 'active'
    })
  } else {
    // 重置表单
    Object.assign(form, {
      name: '',
      display_name: '',
      description: '',
      is_system: false,
      status: 'active'
    })
  }
}, { immediate: true, deep: true })

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '角色更新成功' : '角色创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}
</script>

<style lang="scss" scoped>
.modern-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-bottom: 1px solid #e4e7ed;
    padding: 20px 24px;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}
</style>