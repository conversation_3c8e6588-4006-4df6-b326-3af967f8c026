<template>
  <div class="group-card" :class="{ 'selected': selected, 'inactive': group.status !== 'active' }">
    <div class="card-header">
      <div class="group-avatar">
        <el-avatar :src="group.avatar" :alt="group.name" size="large">
          {{ group.name.charAt(0) }}
        </el-avatar>
        <div class="status-indicator" :class="group.status"></div>
      </div>
      <div class="card-actions">
        <el-checkbox 
          :model-value="selected" 
          @change="$emit('select', group.id)"
          class="select-checkbox"
        />
        <el-dropdown @command="handleCommand" trigger="click">
          <el-button text class="more-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">编辑群组</el-dropdown-item>
              <el-dropdown-item command="members">成员管理</el-dropdown-item>
              <el-dropdown-item command="analytics">数据分析</el-dropdown-item>
              <el-dropdown-item command="qrcode">二维码</el-dropdown-item>
              <el-dropdown-item command="clone">克隆群组</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除群组</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="card-content">
      <h4 class="group-title">{{ group.name }}</h4>
      <p class="group-description">{{ group.description || '暂无描述' }}</p>
      
      <div class="group-stats">
        <div class="stat-row">
          <div class="stat-item">
            <span class="stat-label">成员</span>
            <span class="stat-value">{{ group.current_members || 0 }}/{{ group.max_members || 500 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">价格</span>
            <span class="stat-value price">¥{{ (group.price || 0).toFixed(2) }}</span>
          </div>
        </div>
        
        <div class="stat-row">
          <div class="stat-item">
            <span class="stat-label">收益</span>
            <span class="stat-value revenue">¥{{ (group.total_revenue || 0).toFixed(0) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">健康度</span>
            <span class="stat-value" :class="getHealthScoreClass(group.health_score)">
              {{ group.health_score || 'N/A' }}
            </span>
          </div>
        </div>
      </div>

      <div class="member-progress">
        <div class="progress-label">成员占比</div>
        <el-progress
          :percentage="getMemberPercentage(group)"
          :stroke-width="6"
          :show-text="false"
          :color="getProgressColor(getMemberPercentage(group))"
        />
      </div>

      <div class="group-tags">
        <el-tag :type="getCategoryTagType(group.category)" size="small">
          {{ getCategoryText(group.category) }}
        </el-tag>
        <el-tag :type="getStatusTagType(group.status)" size="small">
          {{ getStatusText(group.status) }}
        </el-tag>
      </div>
    </div>

    <div class="card-footer">
      <div class="footer-info">
        <span class="owner-info">
          <el-icon><User /></el-icon>
          {{ group.owner_name || '未知群主' }}
        </span>
        <span class="create-time">
          <el-icon><Calendar /></el-icon>
          {{ formatDate(group.created_at) }}
        </span>
      </div>
      <div class="footer-actions">
        <el-button size="small" @click="$emit('action', 'edit', group)">
          编辑
        </el-button>
        <el-button size="small" type="primary" @click="$emit('action', 'analytics', group)">
          分析
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MoreFilled, User, Calendar } from '@element-plus/icons-vue'

defineProps({
  group: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

defineEmits(['select', 'action'])

const handleCommand = (command) => {
  // 处理下拉菜单命令
  console.log('Card command:', command)
}

// 工具函数
const getMemberPercentage = (group) => {
  const current = group.current_members || 0
  const max = group.max_members || 500
  return Math.round((current / max) * 100)
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const getHealthScoreClass = (score) => {
  if (!score || score < 40) return 'poor'
  if (score < 60) return 'fair'
  if (score < 80) return 'good'
  return 'excellent'
}

const getCategoryTagType = (category) => {
  const types = {
    startup: 'success',
    finance: 'warning',
    tech: 'primary',
    education: 'info',
    other: ''
  }
  return types[category] || ''
}

const getCategoryText = (category) => {
  const texts = {
    startup: '创业交流',
    finance: '投资理财',
    tech: '科技互联网',
    education: '教育培训',
    other: '其他'
  }
  return texts[category] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    full: 'info',
    pending: 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    paused: '暂停',
    full: '已满',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.group-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid transparent;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }

  &.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  }

  &.inactive {
    opacity: 0.7;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      pointer-events: none;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .group-avatar {
      position: relative;

      .status-indicator {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;

        &.active {
          background: #10b981;
        }

        &.paused {
          background: #f59e0b;
        }

        &.full {
          background: #6b7280;
        }

        &.pending {
          background: #ef4444;
        }
      }
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .select-checkbox {
        :deep(.el-checkbox__input) {
          .el-checkbox__inner {
            border-radius: 4px;
          }
        }
      }

      .more-btn {
        color: #64748b;
        
        &:hover {
          color: #3b82f6;
        }
      }
    }
  }

  .card-content {
    .group-title {
      font-size: 18px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 8px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .group-description {
      color: #64748b;
      font-size: 14px;
      margin: 0 0 16px 0;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      min-height: 42px;
    }

    .group-stats {
      margin-bottom: 16px;

      .stat-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .stat-label {
          font-size: 12px;
          color: #64748b;
          margin-bottom: 4px;
        }

        .stat-value {
          font-weight: 600;
          color: #1e293b;

          &.price {
            color: #ef4444;
          }

          &.revenue {
            color: #10b981;
          }

          &.excellent {
            color: #10b981;
          }

          &.good {
            color: #3b82f6;
          }

          &.fair {
            color: #f59e0b;
          }

          &.poor {
            color: #ef4444;
          }
        }
      }
    }

    .member-progress {
      margin-bottom: 16px;

      .progress-label {
        font-size: 12px;
        color: #64748b;
        margin-bottom: 6px;
      }

      :deep(.el-progress) {
        .el-progress-bar__outer {
          border-radius: 3px;
          background: #f1f5f9;
        }

        .el-progress-bar__inner {
          border-radius: 3px;
        }
      }
    }

    .group-tags {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .card-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f1f5f9;

    .footer-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-size: 12px;
      color: #64748b;

      .owner-info,
      .create-time {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }

    .footer-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .group-card {
    padding: 16px;

    .card-content {
      .group-stats {
        .stat-row {
          flex-direction: column;
          gap: 8px;
        }

        .stat-item {
          flex-direction: row;
          justify-content: space-between;
        }
      }
    }

    .card-footer {
      .footer-info {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }
    }
  }
}
</style>