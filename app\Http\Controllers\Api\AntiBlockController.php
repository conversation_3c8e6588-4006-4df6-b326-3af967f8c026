<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AntiBlockService;
use App\Models\DomainPool;
use App\Models\ShortLink;
use App\Models\LinkAccessLog;
use App\Models\DomainCheckLog; // 引入日志模型
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

/**
 * 防红系统控制器 (V2.0 - 全功能增强版)
 * <AUTHOR>
 * @date 2024-07-25
 */
class AntiBlockController extends Controller
{
    protected $antiBlockService;

    public function __construct(AntiBlockService $antiBlockService)
    {
        $this->antiBlockService = $antiBlockService;
    }

    /**
     * 获取域名列表 (资源控制器方法)
     */
    public function index(Request $request)
    {
        return $this->getDomains($request);
    }

    /**
     * 显示单个域名
     */
    public function show($id)
    {
        return $this->getDomainDetail($id);
    }

    /**
     * 创建域名
     */
    public function store(Request $request)
    {
        return $this->addDomain($request);
    }

    /**
     * 更新域名
     */
    public function update(Request $request, $id)
    {
        return $this->updateDomain($request, $id);
    }

    /**
     * 删除域名
     */
    public function destroy($id)
    {
        return $this->deleteDomain($id);
    }

    /**
     * 获取域名列表 (增强版)
     * 支持高级筛选和排序
     */
    public function getDomains(Request $request)
    {
        $query = DomainPool::query();

        // 关键词搜索
        if ($request->filled('keyword')) {
            $query->where('domain', 'like', '%' . $request->keyword . '%');
        }
        // 域名类型筛选
        if ($request->filled('domain_type')) {
            $query->where('domain_type', $request->domain_type);
        }
        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        // 健康度范围筛选
        if ($request->filled('health_range')) {
            $range = explode('-', $request->health_range);
            if(count($range) == 2) {
                $query->whereBetween('health_score', [$range[0], $range[1]]);
            }
        }

        // 排序
        $sort_by = $request->get('sort_by', 'created_at');
        $sort_order = $request->get('sort_order', 'desc');
        $query->orderBy($sort_by, $sort_order);

        $domains = $query->paginate($request->get('limit', 10));

        // 返回标准化的响应
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'items' => $domains->items(),
                'total' => $domains->total(),
            ]
        ]);
    }
    
    /**
     * 获取单个域名详情
     */
    public function getDomainDetail($id)
    {
        $domain = DomainPool::findOrFail($id);
        return response()->json(['code' => 0, 'data' => $domain]);
    }


    /**
     * 添加域名
     */
    public function addDomain(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain' => 'required|string|unique:domain_pools,domain',
            'domain_type' => 'required|in:redirect,landing,api',
            'priority' => 'required|integer|min:0|max:100',
            'remarks' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }
        
        $domain = DomainPool::create($request->all());

        return response()->json([
            'code' => 0,
            'message' => '域名添加成功',
            'data' => $domain
        ], 201);
    }

    /**
     * 更新域名
     */
    public function updateDomain(Request $request, $id)
    {
        $domain = DomainPool::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'domain' => 'required|string|unique:domain_pools,domain,' . $id,
            'domain_type' => 'required|in:redirect,landing,api',
            'priority' => 'required|integer|min:0|max:100',
            'status' => 'required|integer',
            'remarks' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }

        $domain->update($request->all());
        
        return response()->json([
            'code' => 0,
            'message' => '域名更新成功',
            'data' => $domain
        ]);
    }

    /**
     * 删除域名
     */
    public function deleteDomain($id)
    {
        $domain = DomainPool::findOrFail($id);
        // 在删除前可以增加逻辑，比如该域名下是否还有短链接
        $domain->delete();
        return response()->json(['code' => 0, 'message' => '域名删除成功'], 200);
    }
    
    /**
     * 批量删除域名
     */
    public function batchDeleteDomains(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain_ids' => 'required|array',
            'domain_ids.*' => 'integer|exists:domain_pools,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }

        $count = DomainPool::destroy($request->input('domain_ids'));

        return response()->json([
            'code' => 0,
            'message' => "成功删除 {$count} 个域名",
        ]);
    }

    /**
     * 手动检查单个域名
     */
    public function checkSingleDomain($id)
    {
        $result = $this->antiBlockService->checkSingleDomain($id);

        if ($result['success']) {
            return response()->json([
                'code' => 0,
                'message' => '域名检查完成',
                'data' => $result['data']
            ]);
        } else {
            return response()->json(['code' => 1, 'message' => $result['message']], 500);
        }
    }

    /**
     * 批量检测域名
     */
    public function batchCheckDomains(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'domain_ids' => 'required|array',
            'domain_ids.*' => 'integer|exists:domain_pools,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['code' => 1, 'message' => $validator->errors()->first()], 422);
        }

        $results = $this->antiBlockService->batchCheckDomains($request->input('domain_ids'));
        
        return response()->json([
            'code' => 0,
            'message' => '批量检测任务已启动',
            'data' => $results,
        ]);
    }

    /**
     * 获取域名检测日志
     */
    public function getDomainLogs($id, Request $request)
    {
        $logs = DomainCheckLog::where('domain_pool_id', $id)
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('limit', 10));
        
        return response()->json([
            'code' => 0,
            'message' => '获取日志成功',
            'data' => [
                'items' => $logs->items(),
                'total' => $logs->total(),
            ]
        ]);
    }


    // ================== 短链接管理 (沿用旧版，可后续增强) ==================
    
    /**
     * 获取短链接列表
     */
    public function getShortLinks(Request $request)
    {
        $links = ShortLink::with('domainPool')->orderBy('created_at', 'desc')->paginate($request->get('limit', 20));
        return Response::json(['success' => true, 'data' => $links]);
    }

    /**
     * 创建短链接
     */
    public function createShortLink(Request $request)
    {
        $request->validate(['original_url' => 'required|url']);
        
        $shortLink = $this->antiBlockService->createShortLink(
            $request->original_url,
            $request->remark,
            $request->user()->id
        );

        if (!$shortLink) {
            return Response::json([
                'success' => false,
                'message' => '创建失败，没有可用的健康域名'
            ], 500);
        }

        return Response::json([
            'success' => true,
            'message' => '短链接创建成功',
            'data' => $shortLink
        ], 201);
    }

    /**
     * 删除短链接
     */
    public function deleteShortLink($id)
    {
        ShortLink::findOrFail($id)->delete();
        return Response::json([
            'success' => true,
            'message' => '短链接删除成功'
        ], 204);
    }

    // ================== 统计与配置 (沿用旧版，可后续增强) ==================

    /**
     * 获取防红统计数据
     */
    public function getStats()
    {
        $stats = $this->antiBlockService->getStats();
        return Response::json(['success' => true, 'data' => $stats]);
    }

    /**
     * 获取访问分析数据
     */
    public function getAnalytics(Request $request)
    {
        $query = LinkAccessLog::query()->with('shortLink.domainPool');
        
        if ($request->filled('start_date')) {
            $query->where('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->where('created_at', '<=', $request->end_date);
        }

        $logs = $query->orderBy('created_at', 'desc')->paginate($request->get('limit', 20));

        return Response::json(['success' => true, 'data' => $logs]);
    }

    /**
     * 获取防红配置
     */
    public function getConfig()
    {
        $config = $this->antiBlockService->getConfig();
        return Response::json(['success' => true, 'data' => $config]);
    }

    /**
     * 更新防红配置
     */
    public function updateConfig(Request $request)
    {
        $request->validate([
            'enabled' => 'sometimes|boolean',
            'default_url' => 'sometimes|nullable|url',
        ]);

        $this->antiBlockService->updateConfig($request->only(['enabled', 'default_url']));

        return Response::json(['success' => true, 'message' => '配置更新成功']);
    }

    /**
     * 处理短链接跳转
     */
    public function handleRedirect($shortCode)
    {
        return $this->antiBlockService->handleRedirect($shortCode);
    }
}