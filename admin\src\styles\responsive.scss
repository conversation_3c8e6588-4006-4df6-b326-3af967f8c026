// 响应式断点系统
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// 媒体查询混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 最大宽度媒体查询
@mixin respond-max($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  }
}

// 响应式工具类
@mixin mobile-only {
  @include respond-max(md) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: 768px) and (max-width: 991.98px) {
    @content;
  }
}

@mixin desktop-only {
  @include respond-to(lg) {
    @content;
  }
}

// 响应式间距系统
$spacing-sizes: (0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 56, 64);

@each $size in $spacing-sizes {
  .m-#{$size} { margin: #{$size * 0.25}rem; }
  .mt-#{$size} { margin-top: #{$size * 0.25}rem; }
  .mb-#{$size} { margin-bottom: #{$size * 0.25}rem; }
  .ml-#{$size} { margin-left: #{$size * 0.25}rem; }
  .mr-#{$size} { margin-right: #{$size * 0.25}rem; }
  .p-#{$size} { padding: #{$size * 0.25}rem; }
  .pt-#{$size} { padding-top: #{$size * 0.25}rem; }
  .pb-#{$size} { padding-bottom: #{$size * 0.25}rem; }
  .pl-#{$size} { padding-left: #{$size * 0.25}rem; }
  .pr-#{$size} { padding-right: #{$size * 0.25}rem; }
}

// 响应式工具类
@each $breakpoint, $value in $breakpoints {
  @media (min-width: $value) {
    @each $size in $spacing-sizes {
      .#{$breakpoint}\:m-#{$size} { margin: #{$size * 0.25}rem; }
      .#{$breakpoint}\:mt-#{$size} { margin-top: #{$size * 0.25}rem; }
      .#{$breakpoint}\:mb-#{$size} { margin-bottom: #{$size * 0.25}rem; }
      .#{$breakpoint}\:ml-#{$size} { margin-left: #{$size * 0.25}rem; }
      .#{$breakpoint}\:mr-#{$size} { margin-right: #{$size * 0.25}rem; }
      .#{$breakpoint}\:p-#{$size} { padding: #{$size * 0.25}rem; }
      .#{$breakpoint}\:pt-#{$size} { padding-top: #{$size * 0.25}rem; }
      .#{$breakpoint}\:pb-#{$size} { padding-bottom: #{$size * 0.25}rem; }
      .#{$breakpoint}\:pl-#{$size} { padding-left: #{$size * 0.25}rem; }
      .#{$breakpoint}\:pr-#{$size} { padding-right: #{$size * 0.25}rem; }
    }
  }
}

// 隐藏/显示工具类
@mixin responsive-display($breakpoint, $display: block) {
  @include respond-to($breakpoint) {
    display: $display;
  }
}

@mixin responsive-hide($breakpoint) {
  @include respond-to($breakpoint) {
    display: none !important;
  }
}

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

@each $breakpoint, $value in $breakpoints {
  @media (min-width: $value) {
    .#{$breakpoint}\:d-none { display: none !important; }
    .#{$breakpoint}\:d-block { display: block !important; }
    .#{$breakpoint}\:d-flex { display: flex !important; }
    .#{$breakpoint}\:d-inline-flex { display: inline-flex !important; }
  }
}