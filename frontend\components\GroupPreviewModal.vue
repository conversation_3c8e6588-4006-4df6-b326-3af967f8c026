<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-hidden">
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">群组页面预览</h3>
        <button 
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 预览内容 -->
      <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
        <!-- 群组头部 -->
        <div class="relative">
          <div class="h-40 bg-gradient-to-r from-blue-400 to-purple-500"></div>
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
            <div class="p-4 text-white w-full">
              <h1 class="text-xl font-bold mb-2">
                {{ formData.title || '群组名称' }}
              </h1>
              <div class="flex items-center space-x-4 text-sm">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ formData.read_count_display || '1万+' }} 人已读
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                  </svg>
                  {{ formData.like_count || 0 }} 人点赞
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  {{ formData.current_members || 0 }}/{{ formData.member_limit || 200 }} 人
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="p-4 space-y-4">
          <!-- 价格和购买区域 -->
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="text-center mb-4">
              <div class="flex items-center justify-center space-x-2 mb-2">
                <span 
                  v-if="formData.show_limited_time && formData.original_price > formData.price"
                  class="text-lg text-gray-500 line-through"
                >
                  ¥{{ formData.original_price }}
                </span>
                <span class="text-3xl font-bold text-red-600">
                  ¥{{ formData.price || 0 }}
                </span>
              </div>
              <div class="text-sm text-gray-600">一次付费，永久入群</div>
              
              <!-- 限时优惠倒计时 -->
              <div 
                v-if="formData.show_limited_time && formData.discount_end_time"
                class="mt-3 text-sm text-orange-600 bg-orange-50 rounded-lg px-3 py-2 inline-block"
              >
                🔥 限时优惠，仅剩 {{ getTimeRemaining() }}
              </div>
            </div>

            <button 
              class="w-full bg-red-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-700 transition-colors"
            >
              {{ formData.button_title || '立即加入群聊' }}
            </button>
            
            <div class="mt-3 text-xs text-gray-500 text-center">
              支持微信支付、支付宝等多种支付方式
            </div>
          </div>

          <!-- 群组信息 -->
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="space-y-3 text-sm">
              <div class="flex items-center justify-between">
                <span class="text-gray-600">群组分类</span>
                <span class="font-medium">{{ getCategoryName(formData.category) }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">当前人数</span>
                <span class="font-medium">{{ formData.current_members || 0 }}/{{ formData.member_limit || 200 }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">想看人数</span>
                <span class="font-medium">{{ formData.want_see_count || 0 }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">创建时间</span>
                <span class="font-medium">{{ formatDate(new Date()) }}</span>
              </div>
            </div>
          </div>

          <!-- 群组描述 -->
          <div v-if="formData.description" class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">{{ formData.group_intro_title || '群简介' }}</h4>
            <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
              {{ formData.group_intro_content || formData.description }}
            </div>
          </div>

          <!-- 成员头像展示 -->
          <div v-if="formData.current_members > 0" class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-3">群成员</h4>
            <div class="grid grid-cols-8 gap-3">
              <div 
                v-for="i in Math.min(32, formData.current_members)" 
                :key="i"
                class="relative"
              >
                <img 
                  :src="getAvatarUrl(formData.avatar_library || 'default', i)"
                  :alt="`成员${i}`"
                  class="w-10 h-10 rounded-full border-2 border-gray-200"
                />
                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
            </div>
            <p class="text-sm text-gray-500 mt-3">
              还有 {{ Math.max(0, formData.current_members - 32) }} 位成员在群里...
            </p>
          </div>

          <!-- 用户评价 -->
          <div v-if="hasValidReviews" class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-4">用户评价</h4>
            <div class="space-y-4">
              <div 
                v-for="(review, index) in validReviews"
                :key="index"
                class="border-l-4 border-blue-500 pl-4 py-2"
              >
                <div class="flex items-center mb-2">
                  <img 
                    :src="getAvatarUrl(formData.avatar_library || 'default', index + 1)"
                    :alt="review.author"
                    class="w-8 h-8 rounded-full mr-3"
                  />
                  <div>
                    <div class="font-medium text-gray-900">{{ review.author || `用户${index + 1}` }}</div>
                    <div class="flex items-center">
                      <div class="flex text-yellow-400 mr-2">
                        <svg v-for="star in review.rating" :key="star" class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </div>
                      <span class="text-sm text-gray-600">{{ review.rating }}分</span>
                    </div>
                  </div>
                </div>
                <p class="text-gray-700">{{ review.content }}</p>
              </div>
            </div>
          </div>

          <!-- 常见问题 -->
          <div v-if="hasValidFAQs" class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-4">常见问题</h4>
            <div class="space-y-4">
              <div 
                v-for="(faq, index) in validFAQs"
                :key="index"
                class="border-b border-gray-200 pb-4 last:border-b-0"
              >
                <button 
                  @click="toggleFAQ(index)"
                  class="w-full flex items-center justify-between text-left py-2"
                >
                  <h5 class="font-medium text-gray-900">{{ faq.question }}</h5>
                  <svg 
                    :class="['w-5 h-5 transition-transform', { 'rotate-180': activeFAQ === index }]"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div 
                  v-show="activeFAQ === index"
                  class="text-gray-700 mt-2 pl-4 border-l-2 border-gray-200"
                >
                  {{ faq.answer }}
                </div>
              </div>
            </div>
          </div>

          <!-- 付费内容展示 -->
          <div v-if="formData.paid_content_type" class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200 p-4">
            <div class="text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">付费后可获得</h4>
              <p class="text-gray-700 mb-4">
                {{ getContentTypeLabel(formData.paid_content_type) }}
              </p>
              
              <!-- 根据内容类型显示不同的预览 -->
              <div v-if="formData.paid_content_type === 'qr_code' && formData.qr_code" class="mb-4">
                <img :src="formData.qr_code" alt="入群二维码" class="w-32 h-32 mx-auto rounded-lg border border-gray-200" />
              </div>
              
              <div v-else-if="formData.paid_content_type === 'link' && formData.paid_link" class="mb-4">
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                  <div class="flex items-center justify-center text-blue-600">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    {{ formData.paid_link_desc || '下载链接' }}
                  </div>
                </div>
              </div>
              
              <div v-else-if="formData.paid_content_type === 'document' && formData.paid_document_content" class="mb-4">
                <div class="bg-white rounded-lg p-3 border border-gray-200 text-left">
                  <div class="text-sm text-gray-700 line-clamp-3">
                    {{ formData.paid_document_content.substring(0, 100) }}...
                  </div>
                </div>
              </div>
              
              <p class="text-sm text-gray-600">
                💡 付费成功后立即获得以上内容
              </p>
            </div>
          </div>

          <!-- 安全保障 -->
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-semibold text-gray-900 mb-4">安全保障</h4>
            <div class="space-y-3">
              <div class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span>实名认证群主</span>
              </div>
              <div class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span>7天无理由退款</span>
              </div>
              <div class="flex items-center text-sm">
                <svg class="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span>24小时客服支持</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <p class="text-sm text-gray-600">
            👆 这是用户看到的群组页面效果
          </p>
          <button 
            @click="$emit('close')"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            关闭预览
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

defineEmits(['close'])

const activeFAQ = ref(null)

// 计算属性
const validReviews = computed(() => {
  return props.formData.reviews?.filter(review => review.content && review.content.trim()) || []
})

const hasValidReviews = computed(() => {
  return validReviews.value.length > 0
})

const validFAQs = computed(() => {
  return props.formData.faqs?.filter(faq => faq.question && faq.question.trim() && faq.answer && faq.answer.trim()) || []
})

const hasValidFAQs = computed(() => {
  return validFAQs.value.length > 0
})

// 方法
const toggleFAQ = (index) => {
  activeFAQ.value = activeFAQ.value === index ? null : index
}

const getCategoryName = (category) => {
  const categories = {
    'business': '商务合作',
    'entrepreneurship': '创业交流',
    'dating': '扩列交友',
    'marriage': '婚恋相亲',
    'education': '学习教育',
    'health': '健身健康',
    'parenting': '育儿教育',
    'investment': '投资理财'
  }
  return categories[category] || '其他'
}

const getAvatarUrl = (library, index) => {
  const libraries = {
    'default': '/avatars/default',
    'business': '/avatars/business',
    'dating': '/avatars/dating',
    'education': '/avatars/education'
  }
  return `${libraries[library] || libraries.default}/${index}.jpg`
}

const getContentTypeLabel = (type) => {
  const labels = {
    'qr_code': '入群二维码',
    'link': '独家下载链接',
    'document': '专属文档内容',
    'video': '独家视频内容'
  }
  return labels[type] || '专属内容'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const getTimeRemaining = () => {
  if (!props.formData.discount_end_time) return '24小时'
  
  const now = new Date()
  const endTime = new Date(props.formData.discount_end_time)
  const diff = endTime - now
  
  if (diff <= 0) return '已结束'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `${days}天${hours % 24}小时`
  }
  
  return `${hours}小时${minutes}分钟`
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>