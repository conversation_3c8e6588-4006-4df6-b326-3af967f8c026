<template>
  <div class="preview-mode">
    <!-- 顶部导航栏 -->
    <div class="preview-header">
      <div class="header-content">
        <div class="logo-section">
          <h1>🚀 晨鑫流量变现系统</h1>
          <span class="subtitle">智能社群营销与多级分销平台</span>
        </div>
        <div class="preview-badge">
          <el-tag type="success" size="large">
            <el-icon><View /></el-icon>
            预览模式 - 无需登录
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="preview-content">
      <!-- 系统概览 -->
      <div class="overview-section">
        <h2>🎯 系统功能概览</h2>
        <div class="feature-grid">
          <div class="feature-card" v-for="feature in systemFeatures" :key="feature.id">
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
            <div class="feature-status">
              <el-tag :type="feature.status === '完善' ? 'success' : 'warning'" size="small">
                {{ feature.status }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速访问 -->
      <div class="quick-access-section">
        <h2>⚡ 快速访问管理功能</h2>
        <div class="access-grid">
          <div class="access-card" v-for="module in quickAccessModules" :key="module.path">
            <div class="access-icon">{{ module.icon }}</div>
            <h3>{{ module.title }}</h3>
            <p>{{ module.description }}</p>
            <div class="access-actions">
              <el-button type="primary" @click="navigateToModule(module.path)">
                立即访问
              </el-button>
              <el-button @click="showModuleDetails(module)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div class="tech-section">
        <h2>🔧 技术架构</h2>
        <div class="tech-grid">
          <div class="tech-item" v-for="tech in techStack" :key="tech.name">
            <div class="tech-icon">{{ tech.icon }}</div>
            <h4>{{ tech.name }}</h4>
            <span class="tech-version">{{ tech.version }}</span>
            <div class="tech-status">
              <el-tag :type="tech.status === '正常' ? 'success' : 'danger'" size="small">
                {{ tech.status }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 部署状态 -->
      <div class="deployment-section">
        <h2>📊 部署状态检查</h2>
        <div class="status-grid">
          <div class="status-card" v-for="check in deploymentChecks" :key="check.name">
            <div class="status-header">
              <span class="status-icon" :class="check.status">
                {{ check.status === 'success' ? '✅' : check.status === 'warning' ? '⚠️' : '❌' }}
              </span>
              <h4>{{ check.name }}</h4>
            </div>
            <p>{{ check.description }}</p>
            <div class="status-details">
              <span class="status-value">{{ check.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模块详情弹窗 -->
    <el-dialog
      v-model="moduleDialog.visible"
      :title="moduleDialog.data?.title"
      width="600px"
      center
    >
      <div class="module-details" v-if="moduleDialog.data">
        <div class="module-info">
          <div class="module-header">
            <span class="module-icon">{{ moduleDialog.data.icon }}</span>
            <h3>{{ moduleDialog.data.title }}</h3>
          </div>
          <p class="module-desc">{{ moduleDialog.data.description }}</p>
        </div>
        
        <div class="module-features">
          <h4>主要功能：</h4>
          <ul>
            <li v-for="feature in moduleDialog.data.features" :key="feature">{{ feature }}</li>
          </ul>
        </div>

        <div class="module-stats" v-if="moduleDialog.data.stats">
          <h4>功能统计：</h4>
          <div class="stats-grid">
            <div class="stat-item" v-for="(value, key) in moduleDialog.data.stats" :key="key">
              <span class="stat-label">{{ key }}：</span>
              <span class="stat-value">{{ value }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="moduleDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="navigateToModule(moduleDialog.data?.path)">
          立即访问
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const moduleDialog = ref({
  visible: false,
  data: null
})

// 系统功能特性
const systemFeatures = ref([
  {
    id: 1,
    icon: '👥',
    title: '社群管理系统',
    description: '智能社群创建、管理、营销自动化',
    status: '完善'
  },
  {
    id: 2,
    icon: '🔗',
    title: '多级分销系统',
    description: '灵活的分销体系，支持多层级佣金分配',
    status: '完善'
  },
  {
    id: 3,
    icon: '🛡️',
    title: '防红链接系统',
    description: '智能防封检测，自动切换域名',
    status: '完善'
  },
  {
    id: 4,
    icon: '💰',
    title: '财务管理系统',
    description: '完整的财务流水、佣金结算管理',
    status: '完善'
  },
  {
    id: 5,
    icon: '📊',
    title: '数据分析系统',
    description: '实时数据监控、可视化报表',
    status: '完善'
  },
  {
    id: 6,
    icon: '🔐',
    title: '权限管理系统',
    description: '细粒度权限控制，角色管理',
    status: '完善'
  }
])

// 快速访问模块
const quickAccessModules = ref([
  {
    path: '/dashboard',
    icon: '📊',
    title: '数据看板',
    description: '系统核心数据展示，实时监控业务指标',
    features: [
      '实时数据统计',
      '可视化图表展示',
      '关键指标监控',
      '趋势分析报告'
    ],
    stats: {
      '页面组件': '5个',
      '数据图表': '12个',
      '实时更新': '支持'
    }
  },
  {
    path: '/community/groups',
    icon: '👥',
    title: '社群管理',
    description: '社群创建、管理、营销活动策划',
    features: [
      '社群创建与配置',
      '成员管理',
      '内容发布管理',
      '营销活动策划',
      '数据统计分析'
    ],
    stats: {
      '管理功能': '8个',
      '模板数量': '20+',
      '自动化规则': '支持'
    }
  },
  {
    path: '/distribution/distributors',
    icon: '🔗',
    title: '分销管理',
    description: '分销商管理、佣金设置、层级管理',
    features: [
      '分销商注册审核',
      '多层级佣金设置',
      '推广链接生成',
      '业绩统计分析',
      '提现管理'
    ],
    stats: {
      '分销层级': '无限级',
      '佣金模式': '3种',
      '结算方式': '多样化'
    }
  },
  {
    path: '/finance/dashboard',
    icon: '💰',
    title: '财务管理',
    description: '财务数据统计、佣金结算、提现管理',
    features: [
      '收支统计',
      '佣金计算',
      '提现审核',
      '财务报表',
      '对账管理'
    ],
    stats: {
      '支付方式': '5种',
      '结算周期': '灵活配置',
      '财务报表': '10+种'
    }
  }
])

// 技术栈
const techStack = ref([
  { name: 'Vue 3', version: '3.3.4', icon: '🟢', status: '正常' },
  { name: 'Element Plus', version: '2.3.8', icon: '🔵', status: '正常' },
  { name: 'Laravel', version: '10.x', icon: '🔴', status: '正常' },
  { name: 'MySQL', version: '8.0', icon: '🟡', status: '正常' },
  { name: 'Redis', version: '7.0', icon: '🔴', status: '正常' },
  { name: 'Nginx', version: '1.24', icon: '🟢', status: '正常' }
])

// 部署检查
const deploymentChecks = ref([
  {
    name: '前端构建',
    description: 'Vue.js应用构建状态',
    status: 'success',
    value: '构建完成'
  },
  {
    name: '后端API',
    description: 'Laravel API服务状态',
    status: 'success',
    value: '服务正常'
  },
  {
    name: '数据库连接',
    description: 'MySQL数据库连接状态',
    status: 'success',
    value: '连接正常'
  },
  {
    name: '缓存服务',
    description: 'Redis缓存服务状态',
    status: 'success',
    value: '运行正常'
  },
  {
    name: '文件权限',
    description: '系统文件读写权限',
    status: 'success',
    value: '权限正常'
  },
  {
    name: '环境配置',
    description: '系统环境变量配置',
    status: 'success',
    value: '配置完整'
  }
])

// 方法
const navigateToModule = (path) => {
  if (path) {
    router.push(path)
    ElMessage.success('正在跳转到管理模块...')
  }
}

const showModuleDetails = (module) => {
  moduleDialog.value.data = module
  moduleDialog.value.visible = true
}

onMounted(() => {
  ElMessage.success('欢迎使用晨鑫流量变现系统预览模式！')
})
</script>

<style scoped>
.preview-mode {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.preview-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 20px 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section h1 {
  font-size: 2rem;
  color: #1f2937;
  margin: 0;
  font-weight: 700;
}

.subtitle {
  color: #6b7280;
  font-size: 0.9rem;
}

.preview-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.overview-section,
.quick-access-section,
.tech-section,
.deployment-section {
  margin-bottom: 50px;
}

.overview-section h2,
.quick-access-section h2,
.tech-section h2,
.deployment-section h2 {
  font-size: 1.8rem;
  color: white;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 600;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.feature-card h3 {
  font-size: 1.3rem;
  color: #1f2937;
  margin-bottom: 10px;
  font-weight: 600;
}

.feature-card p {
  color: #6b7280;
  margin-bottom: 15px;
  line-height: 1.6;
}

.access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.access-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.access-card:hover {
  transform: translateY(-3px);
}

.access-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.access-card h3 {
  font-size: 1.4rem;
  color: #1f2937;
  margin-bottom: 10px;
  font-weight: 600;
}

.access-card p {
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.6;
}

.access-actions {
  display: flex;
  gap: 10px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.tech-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.tech-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.tech-item h4 {
  color: #1f2937;
  margin-bottom: 5px;
  font-weight: 600;
}

.tech-version {
  color: #6b7280;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 10px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-icon {
  font-size: 1.5rem;
  margin-right: 10px;
}

.status-card h4 {
  color: #1f2937;
  font-weight: 600;
}

.status-card p {
  color: #6b7280;
  margin-bottom: 10px;
  line-height: 1.5;
}

.status-value {
  color: #059669;
  font-weight: 600;
}

.module-details {
  padding: 20px 0;
}

.module-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.module-icon {
  font-size: 2rem;
  margin-right: 15px;
}

.module-desc {
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.6;
}

.module-features h4,
.module-stats h4 {
  color: #1f2937;
  margin-bottom: 10px;
  font-weight: 600;
}

.module-features ul {
  list-style: none;
  padding: 0;
}

.module-features li {
  color: #6b7280;
  margin-bottom: 5px;
  padding-left: 20px;
  position: relative;
}

.module-features li::before {
  content: '✓';
  color: #059669;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.stat-item {
  background: #f9fafb;
  padding: 10px;
  border-radius: 8px;
}

.stat-label {
  color: #6b7280;
  font-size: 0.9rem;
}

.stat-value {
  color: #1f2937;
  font-weight: 600;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .feature-grid,
  .access-grid {
    grid-template-columns: 1fr;
  }

  .access-actions {
    flex-direction: column;
  }
}
</style>
