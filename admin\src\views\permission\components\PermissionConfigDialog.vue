<template>
  <el-dialog
    v-model="visible"
    :title="`配置角色权限 - ${roleData?.display_name || roleData?.name}`"
    width="800px"
    :before-close="handleClose"
    class="modern-dialog permission-dialog"
  >
    <div class="permission-content">
      <!-- 权限树 -->
      <div class="permission-tree">
        <div class="tree-header">
          <h4>权限配置</h4>
          <div class="tree-actions">
            <el-button size="small" @click="expandAll">全部展开</el-button>
            <el-button size="small" @click="collapseAll">全部收起</el-button>
            <el-button size="small" type="primary" @click="checkAll">全选</el-button>
            <el-button size="small" @click="uncheckAll">取消全选</el-button>
          </div>
        </div>
        
        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="checkedPermissions"
          @check="handlePermissionCheck"
          class="permission-tree-component"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-content">
                <el-icon class="node-icon" :color="data.color || '#409eff'">
                  <component :is="data.icon || 'Key'" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
                <el-tag v-if="data.type" size="small" :type="getPermissionTagType(data.type)">
                  {{ data.type }}
                </el-tag>
              </div>
              <div v-if="data.description" class="node-description">
                {{ data.description }}
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      
      <!-- 已选权限预览 -->
      <div class="selected-permissions">
        <h4>已选权限 ({{ selectedPermissionsList.length }})</h4>
        <div class="permission-tags">
          <el-tag
            v-for="permission in selectedPermissionsList"
            :key="permission.id"
            closable
            @close="removePermission(permission.id)"
            class="permission-tag"
          >
            <el-icon><component :is="permission.icon || 'Key'" /></el-icon>
            {{ permission.label }}
          </el-tag>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          保存权限配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Key, Monitor, User, Comment, Share, Money, Lock, Setting,
  Tickets, Tools, CreditCard, DataLine, List, Plus,
  Edit, Delete, View, Download, Upload
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const treeRef = ref()
const loading = ref(false)
const checkedPermissions = ref([])
const selectedPermissionsList = ref([])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 权限树数据
const permissionTree = [
  {
    id: 'dashboard',
    label: '数据看板',
    icon: 'Monitor',
    color: '#409eff',
    type: '模块',
    description: '查看系统数据统计和分析',
    children: [
      { id: 'dashboard.view', label: '查看数据看板', icon: 'View', type: '查看' },
      { id: 'dashboard.export', label: '导出数据', icon: 'Download', type: '操作' }
    ]
  },
  {
    id: 'user',
    label: '用户管理',
    icon: 'User',
    color: '#67c23a',
    type: '模块',
    description: '管理系统用户信息和权限',
    children: [
      { id: 'user.list', label: '查看用户列表', icon: 'List', type: '查看' },
      { id: 'user.create', label: '创建用户', icon: 'Plus', type: '创建' },
      { id: 'user.edit', label: '编辑用户', icon: 'Edit', type: '编辑' },
      { id: 'user.delete', label: '删除用户', icon: 'Delete', type: '删除' },
      { id: 'user.export', label: '导出用户数据', icon: 'Download', type: '操作' }
    ]
  },
  {
    id: 'community',
    label: '社群管理',
    icon: 'Comment',
    color: '#e6a23c',
    type: '模块',
    description: '管理微信群组和社群活动',
    children: [
      { id: 'community.list', label: '查看群组列表', icon: 'List', type: '查看' },
      { id: 'community.create', label: '创建群组', icon: 'Plus', type: '创建' },
      { id: 'community.edit', label: '编辑群组', icon: 'Edit', type: '编辑' },
      { id: 'community.delete', label: '删除群组', icon: 'Delete', type: '删除' },
      { id: 'community.analytics', label: '群组数据分析', icon: 'DataLine', type: '分析' }
    ]
  },
  {
    id: 'finance',
    label: '财务管理',
    icon: 'Money',
    color: '#f56c6c',
    type: '模块',
    description: '管理财务数据和交易记录',
    children: [
      { id: 'finance.dashboard', label: '财务总览', icon: 'Monitor', type: '查看' },
      { id: 'finance.transactions', label: '交易记录', icon: 'List', type: '查看' },
      { id: 'finance.commission', label: '佣金管理', icon: 'Money', type: '管理' },
      { id: 'finance.withdraw', label: '提现管理', icon: 'Upload', type: '管理' },
      { id: 'finance.export', label: '导出财务数据', icon: 'Download', type: '操作' }
    ]
  },
  {
    id: 'distribution',
    label: '分销管理',
    icon: 'Share',
    color: '#909399',
    type: '模块',
    description: '管理分销网络和代理商',
    children: [
      { id: 'distribution.list', label: '分销商列表', icon: 'List', type: '查看' },
      { id: 'distribution.create', label: '添加分销商', icon: 'Plus', type: '创建' },
      { id: 'distribution.edit', label: '编辑分销商', icon: 'Edit', type: '编辑' },
      { id: 'distribution.analytics', label: '分销数据分析', icon: 'DataLine', type: '分析' }
    ]
  },
  {
    id: 'permission',
    label: '权限管理',
    icon: 'Lock',
    color: '#606266',
    type: '模块',
    description: '管理系统角色和权限配置',
    children: [
      { id: 'permission.roles', label: '角色管理', icon: 'User', type: '管理' },
      { id: 'permission.permissions', label: '权限配置', icon: 'Key', type: '配置' },
      { id: 'permission.assign', label: '分配权限', icon: 'Setting', type: '操作' }
    ]
  },
  {
    id: 'system',
    label: '系统管理',
    icon: 'Setting',
    color: '#303133',
    type: '模块',
    description: '系统设置和维护功能',
    children: [
      { id: 'system.settings', label: '系统设置', icon: 'Tools', type: '设置' },
      { id: 'system.logs', label: '操作日志', icon: 'List', type: '查看' },
      { id: 'system.backup', label: '数据备份', icon: 'Download', type: '操作' },
      { id: 'system.monitor', label: '系统监控', icon: 'Monitor', type: '监控' }
    ]
  }
]

// 获取权限标签类型
const getPermissionTagType = (type) => {
  const typeMap = {
    '模块': 'primary',
    '查看': 'info',
    '创建': 'success',
    '编辑': 'warning',
    '删除': 'danger',
    '操作': 'primary',
    '管理': 'warning',
    '配置': 'info',
    '分析': 'success',
    '设置': 'info',
    '监控': 'warning'
  }
  return typeMap[type] || ''
}

// 权限选择处理
const handlePermissionCheck = (data, checked) => {
  updateSelectedPermissions()
}

// 更新已选权限列表
const updateSelectedPermissions = () => {
  const checkedKeys = treeRef.value?.getCheckedKeys() || []
  const checkedNodes = treeRef.value?.getCheckedNodes() || []
  
  selectedPermissionsList.value = checkedNodes.filter(node => !node.children || node.children.length === 0)
  checkedPermissions.value = checkedKeys
}

// 移除权限
const removePermission = (permissionId) => {
  treeRef.value?.setChecked(permissionId, false)
  updateSelectedPermissions()
}

// 全部展开
const expandAll = () => {
  const expandKeys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        expandKeys.push(node.id)
        traverse(node.children)
      }
    })
  }
  traverse(permissionTree)
  
  nextTick(() => {
    expandKeys.forEach(key => {
      const node = treeRef.value?.getNode(key)
      if (node) {
        node.expanded = true
      }
    })
  })
}

// 全部收起
const collapseAll = () => {
  const collapseKeys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        collapseKeys.push(node.id)
        traverse(node.children)
      }
    })
  }
  traverse(permissionTree)
  
  nextTick(() => {
    collapseKeys.forEach(key => {
      const node = treeRef.value?.getNode(key)
      if (node) {
        node.expanded = false
      }
    })
  })
}

// 全选
const checkAll = () => {
  const allKeys = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      allKeys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(permissionTree)
  
  treeRef.value?.setCheckedKeys(allKeys)
  updateSelectedPermissions()
}

// 取消全选
const uncheckAll = () => {
  treeRef.value?.setCheckedKeys([])
  updateSelectedPermissions()
}

// 监听角色数据变化，加载角色权限
watch(() => props.roleData, (newData) => {
  if (newData && newData.id) {
    // 模拟加载角色权限
    const mockPermissions = [
      'dashboard.view',
      'user.list',
      'user.create',
      'community.list',
      'finance.dashboard'
    ]
    
    nextTick(() => {
      treeRef.value?.setCheckedKeys(mockPermissions)
      updateSelectedPermissions()
    })
  }
}, { immediate: true })

// 提交权限配置
const handleSubmit = async () => {
  try {
    loading.value = true
    
    const selectedPermissions = treeRef.value?.getCheckedKeys() || []
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('保存权限配置:', {
      roleId: props.roleData?.id,
      permissions: selectedPermissions
    })
    
    ElMessage.success('权限配置保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.permission-dialog {
  :deep(.el-dialog) {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
  
  .permission-content {
    display: flex;
    height: 600px;
    
    .permission-tree {
      flex: 1;
      border-right: 1px solid #e4e7ed;
      padding: 24px;
      overflow-y: auto;
      
      .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e4e7ed;
        
        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
        
        .tree-actions {
          display: flex;
          gap: 8px;
          
          .el-button {
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
      
      .permission-tree-component {
        :deep(.el-tree-node__content) {
          height: auto;
          padding: 8px 0;
          
          &:hover {
            background: #f5f7fa;
          }
        }
        
        .tree-node {
          flex: 1;
          padding: 4px 0;
          
          .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .node-icon {
              font-size: 16px;
            }
            
            .node-label {
              font-weight: 500;
              color: #303133;
            }
            
            .el-tag {
              margin-left: auto;
              font-size: 11px;
              height: 20px;
              line-height: 18px;
            }
          }
          
          .node-description {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
            margin-left: 24px;
            line-height: 1.4;
          }
        }
      }
    }
    
    .selected-permissions {
      width: 300px;
      padding: 24px;
      background: #fafbfc;
      overflow-y: auto;
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
      
      .permission-tags {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .permission-tag {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 6px 8px;
          border-radius: 6px;
          font-size: 12px;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>