<template>
  <div class="app-container">
    <PageLayout>
      <template #header>
        <div class="page-header">
          <h1>
            <el-icon><DataLine /></el-icon>
            数据分析与报表
          </h1>
          <p>洞察社群运营关键数据，驱动业务增长。</p>
        </div>
      </template>

      <div class="toolbar-container">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="fetchDashboardData"
        />
        <el-button type="primary" icon="Download" @click="handleExport">
          导出报表
        </el-button>
      </div>

      <!-- 核心指标卡片 -->
      <el-row :gutter="20" class="key-metrics">
        <el-col :span="6">
          <el-card shadow="hover" class="metric-card">
            <div class="metric-title">总收入</div>
            <div class="metric-value">¥{{ kpi.total_revenue.toLocaleString() }}</div>
            <div class="metric-change positive">+{{ kpi.revenue_change }}% vs 上一周期</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="metric-card">
            <div class="metric-title">总用户数</div>
            <div class="metric-value">{{ kpi.total_users.toLocaleString() }}</div>
            <div class="metric-change positive">+{{ kpi.users_change }}%</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="metric-card">
            <div class="metric-title">活跃群组</div>
            <div class="metric-value">{{ kpi.active_groups.toLocaleString() }}</div>
            <div class="metric-change negative">-{{ kpi.groups_change }}%</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="metric-card">
            <div class="metric-title">付费转化率</div>
            <div class="metric-value">{{ kpi.conversion_rate }}%</div>
            <div class="metric-change positive">+{{ kpi.conversion_change }}%</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card shadow="never" class="chart-card">
            <template #header>用户增长趋势</template>
            <div ref="userTrendChart" style="height: 350px;"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never" class="chart-card">
            <template #header>收入来源分布</template>
            <div ref="revenueSourceChart" style="height: 350px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="8">
          <el-card shadow="never" class="chart-card">
            <template #header>热门活动排行</template>
            <div ref="topEventsChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never" class="chart-card">
            <template #header>群组健康度分析</template>
             <div ref="groupHealthChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

    </PageLayout>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { DataLine, Download } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import * as echarts from 'echarts' // 假设已安装echarts

// Refs for charts
const userTrendChart = ref(null)
const revenueSourceChart = ref(null)
const topEventsChart = ref(null)
const groupHealthChart = ref(null)

const dateRange = ref([])
const loading = ref(false)

const kpi = reactive({
  total_revenue: 128530,
  revenue_change: 15.2,
  total_users: 8520,
  users_change: 8.1,
  active_groups: 128,
  groups_change: 2.5,
  conversion_rate: 5.8,
  conversion_change: 1.2,
})

const dateShortcuts = [
  { text: '最近一周', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 7); return [start, end] } },
  { text: '最近一个月', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); return [start, end] } },
  { text: '最近三个月', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 90); return [start, end] } },
]

let userChartInstance, revenueChartInstance, eventsChartInstance, healthChartInstance;

const initCharts = () => {
  userChartInstance = echarts.init(userTrendChart.value)
  revenueChartInstance = echarts.init(revenueSourceChart.value)
  eventsChartInstance = echarts.init(topEventsChart.value)
  healthChartInstance = echarts.init(groupHealthChart.value)

  // 用户增长趋势图
  userChartInstance.setOption({
    tooltip: { trigger: 'axis' },
    legend: { data: ['新增用户', '活跃用户'] },
    xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
    yAxis: { type: 'value' },
    series: [
      { name: '新增用户', type: 'line', data: [120, 132, 101, 134, 90, 230, 210] },
      { name: '活跃用户', type: 'line', data: [820, 932, 901, 934, 1290, 1330, 1320] }
    ]
  })

  // 收入来源图
  revenueChartInstance.setOption({
    tooltip: { trigger: 'item' },
    legend: { top: '5%', left: 'center' },
    series: [{
      name: '收入来源',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: { borderRadius: 10, borderColor: '#fff', borderWidth: 2 },
      data: [
        { value: 1048, name: '付费群组' },
        { value: 735, name: '活动报名' },
        { value: 580, name: '内容打赏' },
        { value: 484, name: '广告收入' },
      ]
    }]
  })

  // 热门活动图
  eventsChartInstance.setOption({
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'value', boundaryGap: [0, 0.01] },
    yAxis: { type: 'category', data: ['分享会', '沙龙', '打卡挑战'] },
    series: [{
      name: '参与人数',
      type: 'bar',
      data: [88, 45, 152]
    }]
  })
  
  // 群组健康度图
  healthChartInstance.setOption({
    tooltip: {},
    radar: {
      indicator: [
        { name: '活跃度', max: 100 },
        { name: '增长率', max: 100 },
        { name: '内容质量', max: 100 },
        { name: '互动性', max: 100 },
        { name: '付费率', max: 100 }
      ]
    },
    series: [{
      name: '群组健康度',
      type: 'radar',
      data: [{ value: [85, 90, 75, 80, 60], name: '平均水平' }]
    }]
  });
}

const fetchDashboardData = () => {
  loading.value = true
  ElMessage.info('正在根据日期范围加载新数据...')
  setTimeout(() => {
    // 在这里根据 dateRange 更新 kpi 和图表数据
    loading.value = false
  }, 1000)
}

const handleExport = () => {
  ElMessage.success('报表正在后台生成，请稍后在消息中心下载。')
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
  window.addEventListener('resize', () => {
      userChartInstance?.resize();
      revenueChartInstance?.resize();
      eventsChartInstance?.resize();
      healthChartInstance?.resize();
  });
})
</script>

<style lang="scss" scoped>
.page-header {
  h1 { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
  p { color: #64748b; font-size: 14px; margin-top: 0; }
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.key-metrics {
  margin-bottom: 20px;
  .metric-card {
    .metric-title {
      color: #64748b;
      font-size: 14px;
      margin-bottom: 8px;
    }
    .metric-value {
      font-size: 28px;
      font-weight: 700;
      color: #1e293b;
    }
    .metric-change {
      font-size: 12px;
      &.positive { color: #16a34a; }
      &.negative { color: #dc2626; }
    }
  }
}

.chart-card {
  :deep(.el-card__header) {
    font-weight: 600;
  }
}
</style>