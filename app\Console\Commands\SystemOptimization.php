<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 系统优化定时任务
 */
class SystemOptimization extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'system:optimize 
                           {--clean-logs : 清理过期日志}
                           {--clean-cache : 清理过期缓存}
                           {--clean-sessions : 清理过期会话}
                           {--optimize-db : 优化数据库}
                           {--all : 执行所有优化操作}';

    /**
     * 命令描述
     */
    protected $description = '系统优化：清理过期数据、优化性能';

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始系统优化...');
        
        $cleanLogs = $this->option('clean-logs');
        $cleanCache = $this->option('clean-cache');
        $cleanSessions = $this->option('clean-sessions');
        $optimizeDb = $this->option('optimize-db');
        $all = $this->option('all');
        
        try {
            $results = [];
            
            if ($all || $cleanLogs) {
                $results['logs'] = $this->cleanExpiredLogs();
            }
            
            if ($all || $cleanCache) {
                $results['cache'] = $this->cleanExpiredCache();
            }
            
            if ($all || $cleanSessions) {
                $results['sessions'] = $this->cleanExpiredSessions();
            }
            
            if ($all || $optimizeDb) {
                $results['database'] = $this->optimizeDatabase();
            }
            
            $this->displayOptimizationResults($results);
            $this->info('系统优化完成！');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('系统优化失败: ' . $e->getMessage());
            Log::error('系统优化命令执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 清理过期日志
     */
    private function cleanExpiredLogs(): array
    {
        $this->info('清理过期日志...');
        
        $results = [
            'operation_logs' => 0,
            'domain_check_logs' => 0,
            'link_access_logs' => 0,
            'payment_callback_logs' => 0,
        ];
        
        // 清理30天前的操作日志
        $cutoffDate = Carbon::now()->subDays(30);
        $results['operation_logs'] = DB::table('operation_logs')
                                      ->where('created_at', '<', $cutoffDate)
                                      ->delete();
        
        // 清理60天前的域名检查日志
        $cutoffDate = Carbon::now()->subDays(60);
        $results['domain_check_logs'] = DB::table('domain_check_logs')
                                         ->where('created_at', '<', $cutoffDate)
                                         ->delete();
        
        // 清理90天前的链接访问日志
        $cutoffDate = Carbon::now()->subDays(90);
        $results['link_access_logs'] = DB::table('link_access_logs')
                                        ->where('created_at', '<', $cutoffDate)
                                        ->delete();
        
        // 清理30天前的支付回调日志
        $cutoffDate = Carbon::now()->subDays(30);
        $results['payment_callback_logs'] = DB::table('payment_callback_logs')
                                           ->where('created_at', '<', $cutoffDate)
                                           ->delete();
        
        return $results;
    }

    /**
     * 清理过期缓存
     */
    private function cleanExpiredCache(): array
    {
        $this->info('清理过期缓存...');
        
        $results = [
            'cache_cleared' => false,
            'view_cache_cleared' => false,
            'config_cache_cleared' => false,
            'route_cache_cleared' => false,
        ];
        
        try {
            // 清理应用缓存
            Cache::flush();
            $results['cache_cleared'] = true;
            
            // 清理视图缓存
            $this->call('view:clear');
            $results['view_cache_cleared'] = true;
            
            // 清理配置缓存
            $this->call('config:clear');
            $results['config_cache_cleared'] = true;
            
            // 清理路由缓存
            $this->call('route:clear');
            $results['route_cache_cleared'] = true;
            
        } catch (\Exception $e) {
            Log::error('清理缓存失败', ['error' => $e->getMessage()]);
        }
        
        return $results;
    }

    /**
     * 清理过期会话
     */
    private function cleanExpiredSessions(): array
    {
        $this->info('清理过期会话...');
        
        $results = [
            'sessions_cleaned' => 0,
        ];
        
        try {
            // 清理过期的会话数据
            $cutoffDate = Carbon::now()->subDays(7);
            $results['sessions_cleaned'] = DB::table('sessions')
                                            ->where('last_activity', '<', $cutoffDate->timestamp)
                                            ->delete();
            
        } catch (\Exception $e) {
            Log::error('清理会话失败', ['error' => $e->getMessage()]);
        }
        
        return $results;
    }

    /**
     * 优化数据库
     */
    private function optimizeDatabase(): array
    {
        $this->info('优化数据库...');
        
        $results = [
            'tables_optimized' => 0,
            'indexes_analyzed' => 0,
        ];
        
        try {
            // 获取所有表名
            $tables = DB::select('SHOW TABLES');
            $databaseName = DB::getDatabaseName();
            $tableColumn = "Tables_in_{$databaseName}";
            
            foreach ($tables as $table) {
                $tableName = $table->$tableColumn;
                
                // 优化表
                DB::statement("OPTIMIZE TABLE `{$tableName}`");
                $results['tables_optimized']++;
                
                // 分析表索引
                DB::statement("ANALYZE TABLE `{$tableName}`");
                $results['indexes_analyzed']++;
            }
            
            // 更新表统计信息
            DB::statement('FLUSH TABLES');
            
        } catch (\Exception $e) {
            Log::error('数据库优化失败', ['error' => $e->getMessage()]);
        }
        
        return $results;
    }

    /**
     * 显示优化结果
     */
    private function displayOptimizationResults(array $results): void
    {
        $this->info('优化结果:');
        
        if (isset($results['logs'])) {
            $this->line('日志清理:');
            $this->line("  操作日志: {$results['logs']['operation_logs']} 条");
            $this->line("  域名检查日志: {$results['logs']['domain_check_logs']} 条");
            $this->line("  访问日志: {$results['logs']['link_access_logs']} 条");
            $this->line("  支付回调日志: {$results['logs']['payment_callback_logs']} 条");
        }
        
        if (isset($results['cache'])) {
            $this->line('缓存清理:');
            $this->line("  应用缓存: " . ($results['cache']['cache_cleared'] ? '✓' : '✗'));
            $this->line("  视图缓存: " . ($results['cache']['view_cache_cleared'] ? '✓' : '✗'));
            $this->line("  配置缓存: " . ($results['cache']['config_cache_cleared'] ? '✓' : '✗'));
            $this->line("  路由缓存: " . ($results['cache']['route_cache_cleared'] ? '✓' : '✗'));
        }
        
        if (isset($results['sessions'])) {
            $this->line('会话清理:');
            $this->line("  过期会话: {$results['sessions']['sessions_cleaned']} 个");
        }
        
        if (isset($results['database'])) {
            $this->line('数据库优化:');
            $this->line("  优化表: {$results['database']['tables_optimized']} 个");
            $this->line("  分析索引: {$results['database']['indexes_analyzed']} 个");
        }
    }
}