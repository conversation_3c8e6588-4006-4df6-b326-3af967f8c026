<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BackupService;

/**
 * 系统备份命令
 */
class BackupSystem extends Command
{
    protected $signature = 'backup:create 
                           {--type=full : 备份类型 (full|database|files)}
                           {--compress : 压缩备份文件}
                           {--cleanup : 清理旧备份}';
    
    protected $description = '创建系统备份';

    protected BackupService $backupService;

    public function __construct(BackupService $backupService)
    {
        parent::__construct();
        $this->backupService = $backupService;
    }

    public function handle()
    {
        $type = $this->option('type');
        
        $this->info("🔄 开始执行 {$type} 备份...");
        $this->newLine();

        try {
            $startTime = microtime(true);

            switch ($type) {
                case 'full':
                    $result = $this->backupService->fullBackup();
                    break;
                case 'database':
                    $result = $this->createDatabaseBackup();
                    break;
                case 'files':
                    $result = $this->createFileBackup();
                    break;
                default:
                    $this->error("不支持的备份类型: {$type}");
                    return 1;
            }

            $duration = round(microtime(true) - $startTime, 2);

            $this->displayBackupResult($result, $duration);

            // 清理旧备份
            if ($this->option('cleanup')) {
                $this->cleanupOldBackups();
            }

            $this->info('✅ 备份完成！');

        } catch (\Exception $e) {
            $this->error('❌ 备份失败: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function createDatabaseBackup(): array
    {
        $backupName = 'database_' . now()->format('Y-m-d_H-i-s');
        return [
            'backup_name' => $backupName,
            'database' => $this->backupService->backupDatabase($backupName),
        ];
    }

    private function createFileBackup(): array
    {
        $backupName = 'files_' . now()->format('Y-m-d_H-i-s');
        return [
            'backup_name' => $backupName,
            'files' => $this->backupService->backupFiles($backupName),
        ];
    }

    private function displayBackupResult(array $result, float $duration): void
    {
        $this->info("📊 备份结果:");
        $this->line("  • 备份名称: {$result['backup_name']}");
        $this->line("  • 执行时间: {$duration}秒");

        if (isset($result['database'])) {
            $db = $result['database'];
            $this->line("  • 数据库备份: {$db['filename']} ({$db['size_human']})");
            if (isset($db['tables'])) {
                $this->line("    - 表数量: {$db['tables']}");
            }
            if (isset($db['records'])) {
                $this->line("    - 记录数量: {$db['records']}");
            }
        }

        if (isset($result['files'])) {
            $files = $result['files'];
            $this->line("  • 文件备份: {$files['filename']} ({$files['size_human']})");
            if (isset($files['files_count'])) {
                $this->line("    - 文件数量: {$files['files_count']}");
            }
        }

        if (isset($result['archive'])) {
            $archive = $result['archive'];
            $this->line("  • 压缩包: {$archive['filename']} ({$archive['size_human']})");
        }

        if (isset($result['size'])) {
            $this->line("  • 总大小: " . $this->formatBytes($result['size']));
        }
    }

    private function cleanupOldBackups(): void
    {
        $this->info('🧹 清理旧备份...');
        
        $deleted = $this->backupService->cleanupOldBackups();
        
        if (empty($deleted)) {
            $this->line('  • 没有需要清理的备份');
        } else {
            $this->line('  • 已删除 ' . count($deleted) . ' 个旧备份:');
            foreach ($deleted as $backup) {
                $this->line("    - {$backup}");
            }
        }
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}