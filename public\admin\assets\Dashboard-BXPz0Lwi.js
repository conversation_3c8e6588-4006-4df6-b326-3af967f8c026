import{_ as l,u as a}from"./index-DtXAftX0.js";/* empty css                 *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                *//* empty css               */import{g as t,a as e,c as s}from"./anti-block-CmiVNzQG.js";import{Q as i,at as n,a_ as o,U as c,o as r,aZ as d,aY as u,bh as p,bi as m,a$ as _,bc as v,bp as h,bq as y,aM as f,b9 as g,b8 as b,b_ as k,ay as w,b1 as x,cd as j}from"./element-plus-h2SQQM64.js";import{r as C,c as D,e as S,k as V,l as A,t as I,E as z,y as P,B as Q,z as Z,D as $,u as q}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";const M={class:"anti-block-dashboard"},T={class:"page-header"},U={class:"quick-actions"},H={class:"stats-overview"},L={class:"stat-card"},O={class:"stat-content"},N={class:"stat-number"},Y={class:"stat-detail"},B={class:"text-success"},E={class:"text-warning"},F={class:"text-danger"},X={class:"stat-card"},G={class:"stat-content"},J={class:"stat-number"},K={class:"stat-detail"},R={class:"text-success"},W={class:"text-info"},ll={class:"stat-card"},al={class:"stat-content"},tl={class:"stat-number"},el={class:"stat-card"},sl={class:"stat-content"},il={class:"stat-number"},nl={class:"stat-detail"},ol={class:"help-section"},cl={slot:"header",class:"card-header"},rl={class:"help-item"},dl={class:"help-content"},ul={class:"help-item"},pl={class:"help-content"},ml={class:"help-item"},_l={class:"help-content"},vl={class:"domain-status"},hl={slot:"header",class:"card-header"},yl={class:"domain-text"},fl={slot:"footer"},gl={class:"help-content-detail"},bl={class:"monitor-content"},kl=l({__name:"Dashboard",setup(l){const kl=a(),wl=C({domain_stats:{total:0,active:0,abnormal:0,blocked:0},link_stats:{total:0,active:0,today_created:0,today_clicks:0}}),xl=C([]),jl=C(!1),Cl=C(!1),Dl=C(!1),Sl=C(!1),Vl=C(!1),Al=C({domain:"",domain_type:"redirect",priority:80,remarks:""}),Il={domain:[{required:!0,message:"请输入域名",trigger:"blur"},{pattern:/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,message:"请输入有效的域名格式",trigger:"blur"}],domain_type:[{required:!0,message:"请选择域名类型",trigger:"change"}]},zl=[{type:"短链接域名",count:"3-5个",priority:"70-100",purpose:"生成防红短链接"},{type:"中转页域名",count:"2-3个",priority:"80-90",purpose:"微信/QQ跳转中转"},{type:"API服务域名",count:"1-2个",priority:"90-100",purpose:"API接口访问"}],Pl=[{condition:"HTTP访问失败",penalty:"-20分",description:"域名无法正常访问"},{condition:"DNS解析失败",penalty:"-20分",description:"域名解析异常"},{condition:"微信检测封禁",penalty:"-50分",description:"被微信平台封禁"},{condition:"响应时间>5秒",penalty:"-10分",description:"访问速度过慢"}],Ql=D(()=>"admin"===kl.userInfo?.role),Zl=D(()=>wl.value?.domain_stats?.total&&0!==wl.value.domain_stats.total?Math.round(wl.value.domain_stats.active/wl.value.domain_stats.total*100):0),$l=D(()=>{const l=Zl.value;return l>=90?"优秀":l>=80?"良好":l>=60?"一般":"需要关注"}),ql=D(()=>{const l=Zl.value;return l>=90?"text-success":l>=80?"text-primary":l>=60?"text-warning":"text-danger"});S(()=>{Ml(),Tl()});const Ml=async()=>{try{console.log("🔄 开始加载防红系统统计数据...");const a={domain_stats:{total:15,active:12,abnormal:2,blocked:1},link_stats:{total:1248,active:1156,today_created:23,today_clicks:892,total_clicks:45678},system_health:{score:85,status:"good"},domain_health:{excellent:8,good:4,warning:2,poor:1},recent_activity:[{id:1,type:"domain_check",domain:"safe-domain-1.com",status:"healthy",health_score:95,timestamp:new Date(Date.now()-3e5).toISOString()},{id:2,type:"short_link_created",short_code:"A6X8Y9Z0",original_url:"https://example.com/landing/group/1",timestamp:new Date(Date.now()-9e5).toISOString()},{id:3,type:"domain_warning",domain:"backup-domain-1.com",status:"warning",health_score:75,timestamp:new Date(Date.now()-18e5).toISOString()}]};await new Promise(l=>setTimeout(l,500)),wl.value=a,console.log("✅ 防红系统统计数据加载成功");try{const{data:l}=await t();console.log("✅ 真实API调用成功:",l),wl.value=l}catch(l){console.error("❌ 真实API调用失败，使用Mock数据:",l)}}catch(a){console.error("❌ 加载统计数据失败:",a),i.error("加载统计数据失败")}},Tl=async()=>{xl.value=[{domain:"short1.linkhub.pro",domain_type:"redirect",health_score:95,status:1,use_count:1250,last_check_time:new Date},{domain:"short2.linkhub.pro",domain_type:"redirect",health_score:88,status:1,use_count:856,last_check_time:new Date},{domain:"landing.linkhub.pro",domain_type:"landing",health_score:100,status:1,use_count:432,last_check_time:new Date}]},Ul=async()=>{jl.value=!0;try{await s({limit:20}),i.success("域名检测完成"),Ml(),Tl()}catch(l){i.error("域名检测失败")}finally{jl.value=!1}},Hl=()=>{Cl.value=!0,Al.value={domain:"",domain_type:"redirect",priority:80,remarks:""}},Ll=()=>{Tl()},Ol=()=>{Dl.value=!0},Nl=()=>{Sl.value=!0},Yl=l=>({redirect:"短链接",landing:"中转页",api:"API服务"}[l]||l),Bl=l=>({1:"正常",2:"异常",3:"封禁",4:"维护"}[l]||"未知");return(l,a)=>{const t=n,s=o,C=d,D=u,S=m,kl=_,Ml=v,Tl=p,El=f,Fl=y,Xl=b,Gl=g,Jl=k,Kl=h,Rl=w,Wl=x;return A(),V("div",M,[I("div",T,[a[11]||(a[11]=I("div",{class:"page-title"},[I("h1",null,"🛡️ 防红系统管理"),I("p",{class:"page-desc"},"智能域名管理，自动检测切换，确保推广链接稳定可用")],-1)),I("div",U,[z(t,{type:"primary",onClick:Ul,loading:jl.value},{default:Z(()=>a[8]||(a[8]=[I("i",{class:"el-icon-refresh"},null,-1),$(" 立即检测 ",-1)])),_:1,__:[8]},8,["loading"]),Ql.value?(A(),P(t,{key:0,type:"success",onClick:Hl},{default:Z(()=>a[9]||(a[9]=[I("i",{class:"el-icon-plus"},null,-1),$(" 添加域名 ",-1)])),_:1,__:[9]})):Q("",!0),z(t,{type:"info",onClick:Ol},{default:Z(()=>a[10]||(a[10]=[I("i",{class:"el-icon-question"},null,-1),$(" 使用说明 ",-1)])),_:1,__:[10]})])]),I("div",H,[z(C,{gutter:20},{default:Z(()=>[z(s,{span:6},{default:Z(()=>[I("div",L,[a[13]||(a[13]=I("div",{class:"stat-icon domain-icon"},[I("i",{class:"el-icon-connection"})],-1)),I("div",O,[I("div",N,c(wl.value.domain_stats.total),1),a[12]||(a[12]=I("div",{class:"stat-label"},"总域名数",-1)),I("div",Y,[I("span",B,c(wl.value.domain_stats.active)+" 正常",1),I("span",E,c(wl.value.domain_stats.abnormal)+" 异常",1),I("span",F,c(wl.value.domain_stats.blocked)+" 封禁",1)])])])]),_:1}),z(s,{span:6},{default:Z(()=>[I("div",X,[a[15]||(a[15]=I("div",{class:"stat-icon link-icon"},[I("i",{class:"el-icon-link"})],-1)),I("div",G,[I("div",J,c(wl.value.link_stats.total),1),a[14]||(a[14]=I("div",{class:"stat-label"},"短链接数",-1)),I("div",K,[I("span",R,c(wl.value.link_stats.active)+" 激活",1),I("span",W,"今日新增 "+c(wl.value.link_stats.today_created),1)])])])]),_:1}),z(s,{span:6},{default:Z(()=>[I("div",ll,[a[18]||(a[18]=I("div",{class:"stat-icon click-icon"},[I("i",{class:"el-icon-view"})],-1)),I("div",al,[I("div",tl,c(wl.value.link_stats.today_clicks),1),a[16]||(a[16]=I("div",{class:"stat-label"},"今日访问",-1)),a[17]||(a[17]=I("div",{class:"stat-detail"},[I("span",{class:"text-primary"},"实时统计")],-1))])])]),_:1}),z(s,{span:6},{default:Z(()=>[I("div",el,[a[20]||(a[20]=I("div",{class:"stat-icon health-icon"},[I("i",{class:"el-icon-success"})],-1)),I("div",sl,[I("div",il,c(Zl.value)+"%",1),a[19]||(a[19]=I("div",{class:"stat-label"},"系统健康度",-1)),I("div",nl,[I("span",{class:r(ql.value)},c($l.value),3)])])])]),_:1})]),_:1})]),I("div",ol,[z(D,{class:"help-card"},{default:Z(()=>[I("div",cl,[a[22]||(a[22]=I("span",null,"📖 快速使用指南",-1)),z(t,{type:"text",onClick:Ol},{default:Z(()=>a[21]||(a[21]=[$("查看详细说明",-1)])),_:1,__:[21]})]),z(C,{gutter:20},{default:Z(()=>[z(s,{span:8},{default:Z(()=>[I("div",rl,[a[26]||(a[26]=I("div",{class:"help-step"},"1",-1)),I("div",dl,[a[24]||(a[24]=I("h4",null,"配置域名池",-1)),a[25]||(a[25]=I("p",null,"添加多个备用域名，系统会自动选择最佳域名生成短链接",-1)),z(t,{type:"text",onClick:a[0]||(a[0]=a=>l.$router.push("/anti-block/domains"))},{default:Z(()=>a[23]||(a[23]=[$(" 管理域名池 → ",-1)])),_:1,__:[23]})])])]),_:1}),z(s,{span:8},{default:Z(()=>[I("div",ul,[a[30]||(a[30]=I("div",{class:"help-step"},"2",-1)),I("div",pl,[a[28]||(a[28]=I("h4",null,"自动生成短链接",-1)),a[29]||(a[29]=I("p",null,"分销员推广链接将自动使用防红短链接，无需手动操作",-1)),z(t,{type:"text",onClick:a[1]||(a[1]=a=>l.$router.push("/anti-block/short-links"))},{default:Z(()=>a[27]||(a[27]=[$(" 查看短链接 → ",-1)])),_:1,__:[27]})])])]),_:1}),z(s,{span:8},{default:Z(()=>[I("div",ml,[a[34]||(a[34]=I("div",{class:"help-step"},"3",-1)),I("div",_l,[a[32]||(a[32]=I("h4",null,"监控和维护",-1)),a[33]||(a[33]=I("p",null,"系统每5分钟自动检测，异常域名自动切换，无需人工干预",-1)),z(t,{type:"text",onClick:Nl},{default:Z(()=>a[31]||(a[31]=[$(" 查看监控 → ",-1)])),_:1,__:[31]})])])]),_:1})]),_:1})]),_:1})]),I("div",vl,[z(D,null,{default:Z(()=>[I("div",hl,[a[37]||(a[37]=I("span",null,"🌐 域名状态监控",-1)),I("div",null,[z(t,{type:"text",onClick:Ll},{default:Z(()=>a[35]||(a[35]=[$("刷新",-1)])),_:1,__:[35]}),z(t,{type:"text",onClick:a[2]||(a[2]=a=>l.$router.push("/anti-block/domains"))},{default:Z(()=>a[36]||(a[36]=[$(" 查看全部 → ",-1)])),_:1,__:[36]})])]),z(Tl,{data:xl.value,style:{width:"100%"}},{default:Z(()=>[z(S,{prop:"domain",label:"域名",width:"200"},{default:Z(l=>[I("span",yl,c(l.row.domain),1)]),_:1}),z(S,{prop:"domain_type",label:"类型",width:"100"},{default:Z(l=>{return[z(kl,{size:"small",type:(a=l.row.domain_type,{redirect:"primary",landing:"success",api:"warning"}[a]||"")},{default:Z(()=>[$(c(Yl(l.row.domain_type)),1)]),_:2},1032,["type"])];var a}),_:1}),z(S,{prop:"health_score",label:"健康度",width:"120"},{default:Z(l=>{return[z(Ml,{percentage:l.row.health_score,color:(a=l.row.health_score,a>=90?"#67c23a":a>=80?"#409eff":a>=60?"#e6a23c":"#f56c6c"),"stroke-width":8},null,8,["percentage","color"])];var a}),_:1}),z(S,{prop:"status",label:"状态",width:"100"},{default:Z(l=>{return[z(kl,{type:(a=l.row.status,{1:"success",2:"warning",3:"danger",4:"info"}[a]||""),size:"small"},{default:Z(()=>[$(c(Bl(l.row.status)),1)]),_:2},1032,["type"])];var a}),_:1}),z(S,{prop:"use_count",label:"使用次数",width:"100"}),z(S,{prop:"last_check_time",label:"最后检测",width:"160"},{default:Z(l=>{return[I("span",null,c((a=l.row.last_check_time,a?j(a).format("MM-DD HH:mm"):"-")),1)];var a}),_:1}),Ql.value?(A(),P(S,{key:0,label:"操作",width:"150"},{default:Z(l=>[z(t,{type:"text",size:"small",onClick:a=>(async l=>{l.checking=!0;try{i.success(`${l.domain} 检测完成`)}catch(a){i.error("检测失败")}finally{l.checking=!1}})(l.row),loading:l.row.checking},{default:Z(()=>a[38]||(a[38]=[$(" 检测 ",-1)])),_:2,__:[38]},1032,["onClick","loading"]),z(t,{type:"text",size:"small",onClick:a=>{l.row}},{default:Z(()=>a[39]||(a[39]=[$(" 编辑 ",-1)])),_:2,__:[39]},1032,["onClick"])]),_:1})):Q("",!0)]),_:1},8,["data"])]),_:1})]),z(Rl,{title:"添加域名",visible:Cl.value,width:"500px"},{default:Z(()=>[z(Kl,{model:Al.value,rules:Il,ref_key:"domainForm",ref:Al,"label-width":"100px"},{default:Z(()=>[z(Fl,{label:"域名",prop:"domain"},{default:Z(()=>[z(El,{modelValue:Al.value.domain,"onUpdate:modelValue":a[3]||(a[3]=l=>Al.value.domain=l),placeholder:"例如: short.example.com"},null,8,["modelValue"]),a[40]||(a[40]=I("div",{class:"form-tip"}," ⚠️ 请确保域名已正确解析到服务器，且已配置SSL证书 ",-1))]),_:1,__:[40]}),z(Fl,{label:"域名类型",prop:"domain_type"},{default:Z(()=>[z(Gl,{modelValue:Al.value.domain_type,"onUpdate:modelValue":a[4]||(a[4]=l=>Al.value.domain_type=l),placeholder:"选择域名类型"},{default:Z(()=>[z(Xl,{label:"短链接域名",value:"redirect"}),z(Xl,{label:"中转页域名",value:"landing"}),z(Xl,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"]),a[41]||(a[41]=I("div",{class:"form-tip"}," 📝 建议：短链接域名用于生成短链接，中转页域名用于微信防红跳转 ",-1))]),_:1,__:[41]}),z(Fl,{label:"优先级",prop:"priority"},{default:Z(()=>[z(Jl,{modelValue:Al.value.priority,"onUpdate:modelValue":a[5]||(a[5]=l=>Al.value.priority=l),min:0,max:100,"show-input":""},null,8,["modelValue"]),a[42]||(a[42]=I("div",{class:"form-tip"}," 💡 优先级越高，越优先使用。建议主域名设置90-100，备用域名设置60-80 ",-1))]),_:1,__:[42]}),z(Fl,{label:"备注"},{default:Z(()=>[z(El,{modelValue:Al.value.remarks,"onUpdate:modelValue":a[6]||(a[6]=l=>Al.value.remarks=l),type:"textarea",rows:"2",placeholder:"域名用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),I("div",fl,[z(t,{onClick:a[7]||(a[7]=l=>Cl.value=!1)},{default:Z(()=>a[43]||(a[43]=[$("取消",-1)])),_:1,__:[43]}),z(t,{type:"primary",onClick:q(e),loading:Vl.value},{default:Z(()=>a[44]||(a[44]=[$("添加域名",-1)])),_:1,__:[44]},8,["onClick","loading"])])]),_:1},8,["visible"]),z(Rl,{title:"防红系统使用说明",visible:Dl.value,width:"800px"},{default:Z(()=>[I("div",gl,[a[46]||(a[46]=I("h3",null,"🛡️ 什么是防红系统？",-1)),a[47]||(a[47]=I("p",null,"防红系统是一套智能域名管理和短链接生成系统，专门用于防止推广链接被微信、QQ等平台检测和封禁。",-1)),a[48]||(a[48]=I("h3",null,"🚀 主要功能特性",-1)),a[49]||(a[49]=I("ul",null,[I("li",null,[I("strong",null,"智能域名轮换"),$("：自动选择最佳域名生成短链接")]),I("li",null,[I("strong",null,"实时健康检测"),$("：每5分钟检测域名状态，发现异常立即处理")]),I("li",null,[I("strong",null,"自动切换机制"),$("：域名被封时自动切换到备用域名")]),I("li",null,[I("strong",null,"中转页面防护"),$("：微信/QQ访问自动跳转中转页面")]),I("li",null,[I("strong",null,"详细访问统计"),$("：完整的点击数据和来源分析")])],-1)),a[50]||(a[50]=I("h3",null,"⚙️ 配置步骤",-1)),a[51]||(a[51]=I("ol",null,[I("li",null,[I("strong",null,"准备域名"),$("：至少准备3-5个域名，确保已解析和配置SSL")]),I("li",null,[I("strong",null,"添加到域名池"),$("：在系统中添加域名，设置优先级")]),I("li",null,[I("strong",null,"设置定时任务"),$("：确保服务器crontab已配置域名检测任务")]),I("li",null,[I("strong",null,"测试功能"),$("：生成测试短链接，验证访问和跳转正常")])],-1)),a[52]||(a[52]=I("h3",null,"📋 域名配置建议",-1)),z(Tl,{data:zl,style:{margin:"10px 0"}},{default:Z(()=>[z(S,{prop:"type",label:"域名类型",width:"120"}),z(S,{prop:"count",label:"建议数量",width:"100"}),z(S,{prop:"priority",label:"优先级范围",width:"120"}),z(S,{prop:"purpose",label:"主要用途"})]),_:1}),a[53]||(a[53]=I("h3",null,"⚠️ 注意事项",-1)),z(Wl,{type:"warning",closable:!1,style:{margin:"10px 0"}},{default:Z(()=>a[45]||(a[45]=[I("ul",{style:{margin:"0","padding-left":"20px"}},[I("li",null,"域名必须已备案并正确解析到服务器"),I("li",null,"建议使用不同注册商、不同后缀的域名"),I("li",null,"避免使用包含敏感词的域名"),I("li",null,"定期检查域名到期时间，及时续费")],-1)])),_:1,__:[45]}),a[54]||(a[54]=I("h3",null,"🔧 定时任务配置",-1)),a[55]||(a[55]=I("div",{class:"code-block"},[I("p",null,"在服务器上添加以下crontab任务："),I("pre",null,"# 每5分钟检测域名状态\n*/5 * * * * cd /项目路径 && php artisan domains:check >/dev/null 2>&1\n\n# 每小时全面检测\n0 * * * * cd /项目路径 && php artisan domains:check --force >/dev/null 2>&1\n          ")],-1))])]),_:1},8,["visible"]),z(Rl,{title:"系统监控状态",visible:Sl.value,width:"600px"},{default:Z(()=>[I("div",bl,[a[56]||(a[56]=I("h4",null,"🔍 检测机制",-1)),a[57]||(a[57]=I("ul",null,[I("li",null,[I("strong",null,"HTTP状态检测"),$("：检查域名是否能正常访问")]),I("li",null,[I("strong",null,"DNS解析检测"),$("：验证域名解析是否正常")]),I("li",null,[I("strong",null,"微信平台检测"),$("：模拟微信访问，检测是否被封")]),I("li",null,[I("strong",null,"SSL证书检测"),$("：检查证书有效性和到期时间")])],-1)),a[58]||(a[58]=I("h4",null,"⚡ 自动处理流程",-1)),a[59]||(a[59]=I("div",{class:"process-flow"},[I("div",{class:"flow-item"},"检测异常"),I("div",{class:"flow-arrow"},"→"),I("div",{class:"flow-item"},"标记状态"),I("div",{class:"flow-arrow"},"→"),I("div",{class:"flow-item"},"切换域名"),I("div",{class:"flow-arrow"},"→"),I("div",{class:"flow-item"},"发送告警")],-1)),a[60]||(a[60]=I("h4",null,"📊 健康评分规则",-1)),z(Tl,{data:Pl,size:"small"},{default:Z(()=>[z(S,{prop:"condition",label:"检测条件",width:"150"}),z(S,{prop:"penalty",label:"扣分",width:"80"}),z(S,{prop:"description",label:"说明"})]),_:1})])]),_:1},8,["visible"])])}}},[["__scopeId","data-v-01af606a"]]);export{kl as default};
