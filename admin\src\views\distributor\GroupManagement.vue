<template>
  <div class="group-management">
    <div class="page-header">
      <div class="header-left">
        <h2>群组管理</h2>
        <p class="page-description">管理您负责的群组，监控群组活跃度，优化群组运营</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建群组
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #409EFF20; color: #409EFF;">
            <el-icon :size="24"><Comment /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_groups }}</div>
            <div class="stat-title">管理群组</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              12.5%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #67C23A20; color: #67C23A;">
            <el-icon :size="24"><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total_members }}</div>
            <div class="stat-title">群组成员</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              8.3%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #E6A23C20; color: #E6A23C;">
            <el-icon :size="24"><ChatDotRound /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.active_groups }}</div>
            <div class="stat-title">活跃群组</div>
            <div class="stat-trend positive">
              <el-icon><ArrowUp /></el-icon>
              15.2%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: #F56C6C20; color: #F56C6C;">
            <el-icon :size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.conversion_rate }}%</div>
            <div class="stat-title">转化率</div>
            <div class="stat-trend negative">
              <el-icon><ArrowDown /></el-icon>
              2.1%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 群组列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>群组列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索群组名称"
              style="width: 200px; margin-right: 10px;"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="群组状态" style="width: 120px;" @change="loadGroups">
              <el-option label="全部" value="" />
              <el-option label="活跃" value="active" />
              <el-option label="不活跃" value="inactive" />
              <el-option label="已暂停" value="paused" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table :data="groups" v-loading="loading" stripe>
        <el-table-column prop="name" label="群组名称" width="200">
          <template #default="{ row }">
            <div class="group-info">
              <div class="group-name">{{ row.name }}</div>
              <div class="group-desc">{{ row.description }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100">
          <template #default="{ row }">
            <el-tag :type="getPlatformColor(row.platform)" size="small">
              {{ getPlatformText(row.platform) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="member_count" label="成员数量" width="100" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="daily_messages" label="日消息量" width="100" />
        <el-table-column prop="conversion_count" label="转化数" width="100" />
        <el-table-column label="活跃度" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.activity_rate" 
              :color="getActivityColor(row.activity_rate)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewGroup(row)">
              详情
            </el-button>
            <el-button size="small" @click="manageGroup(row)">
              管理
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="pause" v-if="row.status === 'active'">暂停</el-dropdown-item>
                  <el-dropdown-item command="resume" v-if="row.status === 'paused'">恢复</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadGroups"
          @current-change="loadGroups"
        />
      </div>
    </el-card>

    <!-- 统一的群组创建对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建群组"
      width="900px"
      :close-on-click-modal="false"
      class="unified-create-dialog"
    >
      <GroupCreateForm
        mode="dialog"
        user-role="distributor"
        :default-values="distributorDefaults"
        :show-preview="true"
        :show-templates="false"
        @success="handleCreateSuccess"
        @cancel="handleCreateCancel"
      />
    </el-dialog>

    <!-- 功能开发提示 -->
    <el-dialog v-model="showDevDialog" title="功能开发中" width="400px" center>
      <div class="dev-notice">
        <el-icon :size="60" color="#409EFF"><Tools /></el-icon>
        <h3>功能开发中</h3>
        <p>该功能正在紧急开发中，敬请期待！</p>
        <p>预计上线时间：2024年1月</p>
      </div>
      <template #footer>
        <el-button type="primary" @click="showDevDialog = false">知道了</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Comment, UserFilled, ChatDotRound, TrendCharts,
  Search, ArrowDown, ArrowUp, Tools
} from '@element-plus/icons-vue'
import GroupCreateForm from '@/components/GroupCreateForm.vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDevDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')

const stats = reactive({
  total_groups: 12,
  total_members: 1580,
  active_groups: 8,
  conversion_rate: 15.8
})

const groups = ref([])
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

// 分销员创建群组的默认配置
const distributorDefaults = reactive({
  type: 'distribution',
  auto_city_replace: 1,
  read_count_display: '5万+',
  like_count: 666,
  want_see_count: 888,
  button_title: '立即加入分销群',
  avatar_library: 'qq'
})



// 模拟数据
const mockGroups = [
  {
    id: 1,
    name: '高端客户VIP群',
    description: '专为高端客户提供的VIP服务群',
    platform: 'wechat',
    status: 'active',
    member_count: 156,
    daily_messages: 89,
    conversion_count: 12,
    activity_rate: 85,
    created_at: new Date(Date.now() - 86400000 * 30)
  },
  {
    id: 2,
    name: '产品交流群',
    description: '用户产品使用交流和反馈',
    platform: 'qq',
    status: 'active',
    member_count: 234,
    daily_messages: 156,
    conversion_count: 8,
    activity_rate: 72,
    created_at: new Date(Date.now() - 86400000 * 45)
  },
  {
    id: 3,
    name: '新手指导群',
    description: '新用户入门指导和答疑',
    platform: 'wechat',
    status: 'inactive',
    member_count: 89,
    daily_messages: 23,
    conversion_count: 3,
    activity_rate: 35,
    created_at: new Date(Date.now() - 86400000 * 60)
  }
]

// 方法
const loadGroups = async () => {
  try {
    loading.value = true
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredGroups = [...mockGroups]
    
    // 搜索过滤
    if (searchKeyword.value) {
      filteredGroups = filteredGroups.filter(group => 
        group.name.includes(searchKeyword.value) ||
        group.description.includes(searchKeyword.value)
      )
    }
    
    // 状态过滤
    if (statusFilter.value) {
      filteredGroups = filteredGroups.filter(group => group.status === statusFilter.value)
    }
    
    groups.value = filteredGroups
    pagination.total = filteredGroups.length
    
  } catch (error) {
    ElMessage.error('加载群组列表失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadGroups()
  ElMessage.success('数据已刷新')
}

const handleSearch = debounce(() => {
  pagination.current_page = 1
  loadGroups()
}, 500)

const viewGroup = (group) => {
  ElMessage.info(`查看群组"${group.name}"的详细信息`)
  showDevDialog.value = true
}

const manageGroup = (group) => {
  ElMessage.info(`管理群组"${group.name}"`)
  showDevDialog.value = true
}

const handleCommand = (command, group) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑群组"${group.name}"`)
      showDevDialog.value = true
      break
    case 'pause':
      pauseGroup(group)
      break
    case 'resume':
      resumeGroup(group)
      break
    case 'delete':
      deleteGroup(group)
      break
  }
}

const pauseGroup = async (group) => {
  try {
    await ElMessageBox.confirm(`确定要暂停群组"${group.name}"吗？`, '确认暂停', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    group.status = 'paused'
    ElMessage.success('群组已暂停')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停失败')
    }
  }
}

const resumeGroup = async (group) => {
  try {
    group.status = 'active'
    ElMessage.success('群组已恢复')
  } catch (error) {
    ElMessage.error('恢复失败')
  }
}

const deleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(`确定要删除群组"${group.name}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = groups.value.findIndex(g => g.id === group.id)
    if (index > -1) {
      groups.value.splice(index, 1)
      stats.total_groups--
    }
    
    ElMessage.success('群组删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 处理创建成功
const handleCreateSuccess = (groupData) => {
  // 添加到群组列表
  const newGroup = {
    id: groupData.id || Date.now(),
    name: groupData.title,
    description: groupData.description,
    platform: 'wechat', // 默认微信平台
    type: groupData.type,
    price: groupData.price,
    status: 'active',
    member_count: 0,
    daily_messages: 0,
    conversion_count: 0,
    activity_rate: 0,
    created_at: new Date(),
    enable_marketing: true,
    commission_rate: 5.0
  }

  groups.value.unshift(newGroup)
  stats.total_groups++

  showCreateDialog.value = false
  ElMessage.success('群组创建成功！已启用分销功能。')
}

// 处理创建取消
const handleCreateCancel = () => {
  showCreateDialog.value = false
}



// 工具方法
const getPlatformColor = (platform) => {
  const colors = {
    'wechat': 'success',
    'qq': 'primary',
    'dingtalk': 'warning',
    'work_wechat': 'info'
  }
  return colors[platform] || 'info'
}

const getPlatformText = (platform) => {
  const texts = {
    'wechat': '微信',
    'qq': 'QQ',
    'dingtalk': '钉钉',
    'work_wechat': '企微'
  }
  return texts[platform] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'warning',
    'paused': 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'active': '活跃',
    'inactive': '不活跃',
    'paused': '已暂停'
  }
  return texts[status] || '未知'
}

const getActivityColor = (rate) => {
  if (rate >= 80) return '#67C23A'
  if (rate >= 60) return '#E6A23C'
  if (rate >= 40) return '#F56C6C'
  return '#909399'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadGroups()
})
</script>

<style scoped>
.group-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-description {
  color: #909399;
  font-size: 14px;
  margin: 5px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  gap: 4px;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.group-name {
  font-weight: bold;
  color: #303133;
}

.group-desc {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dev-notice {
  text-align: center;
  padding: 20px;
}

.dev-notice h3 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.dev-notice p {
  color: #606266;
  margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-management {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-row .el-col {
    margin-bottom: 15px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/* 统一创建对话框样式 */
.unified-create-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .unified-create-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }
}
</style>