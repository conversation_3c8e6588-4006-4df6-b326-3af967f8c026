// 全局样式文件

// @use 规则必须在文件顶部，在其他规则之前
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 导入现代化主题系统
@import './modern-theme.scss';

// 导入设计系统
@import './design-system.scss';

// 导入配色修复
@import './color-fixes.scss';

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 14px;
}

body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: $bg-color;
  color: $text-color;
  line-height: 1.6;
}

#app {
  height: 100%;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: $primary-hover-color;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
  
  &:hover {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  }
}

// 工具类
.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: $text-muted-color;
}

.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-danger {
  color: $danger-color;
}

.text-info {
  color: $info-color;
}

// 间距工具类
@for $i from 0 through 5 {
  .m-#{$i} {
    margin: #{$i * 8}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 8}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 8}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 8}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 8}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 8}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 8}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 8}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 8}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 8}px;
  }
}

// 显示/隐藏工具类
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

// Flex 工具类
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.flex-1 {
  flex: 1 !important;
}

// 位置工具类
.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

// 宽度高度工具类
.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-auto {
  height: auto !important;
}

// 边框工具类
.border {
  border: 1px solid $border-color !important;
}

.border-top {
  border-top: 1px solid $border-color !important;
}

.border-right {
  border-right: 1px solid $border-color !important;
}

.border-bottom {
  border-bottom: 1px solid $border-color !important;
}

.border-left {
  border-left: 1px solid $border-color !important;
}

.border-0 {
  border: 0 !important;
}

.rounded {
  border-radius: 4px !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

// 阴影工具类
.shadow-sm {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
}

.shadow-none {
  box-shadow: none !important;
}

// 溢出工具类
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.overflow-visible {
  overflow: visible !important;
}

// 字体工具类
.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-medium {
  font-weight: 500 !important;
}

.font-weight-semibold {
  font-weight: 600 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-size-sm {
  font-size: 12px !important;
}

.font-size-base {
  font-size: 14px !important;
}

.font-size-lg {
  font-size: 16px !important;
}

.font-size-xl {
  font-size: 18px !important;
}

.font-size-2xl {
  font-size: 20px !important;
}

// 响应式工具类
@media (max-width: 768px) {
  .d-sm-none {
    display: none !important;
  }
  
  .d-sm-block {
    display: block !important;
  }
  
  .d-sm-flex {
    display: flex !important;
  }
}

@media (max-width: 992px) {
  .d-md-none {
    display: none !important;
  }
  
  .d-md-block {
    display: block !important;
  }
  
  .d-md-flex {
    display: flex !important;
  }
}

@media (max-width: 1200px) {
  .d-lg-none {
    display: none !important;
  }
  
  .d-lg-block {
    display: block !important;
  }
  
  .d-lg-flex {
    display: flex !important;
  }
}

// 动画工具类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 加载动画
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid $primary-color;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 脉冲动画
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 弹跳动画
.bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

// 摇摆动画
.shake {
  animation: shake 0.5s;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

// 渐入动画
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 滑入动画
.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 缩放动画
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// 悬停效果
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.hover-scale {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

// 自定义组件样式
.modern-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-border {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(135deg, $primary-color, $secondary-color);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}

// 打印样式
@media print {
  * {
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  .page-break {
    page-break-after: always;
  }
}