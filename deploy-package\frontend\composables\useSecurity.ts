/**
 * 安全相关 Composable
 * 提供输入清理、CSRF保护、XSS防护等安全功能
 */

export const useSecurity = () => {
  /**
   * 清理用户输入，防止XSS攻击
   */
  const sanitizeInput = (input: string): string => {
    if (typeof input !== 'string') return ''
    
    // 移除HTML标签
    const withoutTags = input.replace(/<[^>]*>/g, '')
    
    // 转义特殊字符
    const escaped = withoutTags
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
    
    return escaped.trim()
  }

  /**
   * 验证输入是否包含危险字符
   */
  const containsDangerousChars = (input: string): boolean => {
    const dangerousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /data:text\/html/gi,
      /vbscript:/gi,
      /expression\s*\(/gi,
      /url\s*\(/gi,
      /import\s+/gi,
      /eval\s*\(/gi,
      /setTimeout\s*\(/gi,
      /setInterval\s*\(/gi
    ]
    
    return dangerousPatterns.some(pattern => pattern.test(input))
  }

  /**
   * 验证SQL注入模式
   */
  const containsSqlInjection = (input: string): boolean => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /('|(\\')|(;)|(\\)|(\/\*)|(--)|(\*\/))/gi,
      /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR|ALTER|BEGIN|CAST|CREATE|CURSOR|DECLARE|DELETE|DROP|END|EXEC|EXECUTE|FETCH|INSERT|KILL|SELECT|SYS|SYSOBJECTS|SYSCOLUMNS|TABLE|UPDATE)\b)/gi
    ]
    
    return sqlPatterns.some(pattern => pattern.test(input))
  }

  /**
   * 生成CSRF令牌
   */
  const generateCsrfToken = (): string => {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 验证CSRF令牌
   */
  const validateCsrfToken = (token: string, storedToken: string): boolean => {
    if (!token || !storedToken) return false
    return token === storedToken
  }

  /**
   * 安全的密码强度检查
   */
  const checkPasswordStrength = (password: string): {
    score: number
    feedback: string[]
    isStrong: boolean
  } => {
    const feedback: string[] = []
    let score = 0

    // 长度检查
    if (password.length >= 8) {
      score += 1
    } else {
      feedback.push('密码长度至少8位')
    }

    if (password.length >= 12) {
      score += 1
    }

    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      feedback.push('需要包含小写字母')
    }

    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      feedback.push('需要包含大写字母')
    }

    // 包含数字
    if (/\d/.test(password)) {
      score += 1
    } else {
      feedback.push('需要包含数字')
    }

    // 包含特殊字符
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1
    } else {
      feedback.push('需要包含特殊字符')
    }

    // 检查常见弱密码
    const commonPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
    ]
    
    if (commonPasswords.includes(password.toLowerCase())) {
      score = 0
      feedback.push('密码过于简单，请使用更复杂的密码')
    }

    return {
      score,
      feedback,
      isStrong: score >= 4 && feedback.length === 0
    }
  }

  /**
   * 安全的表单数据清理
   */
  const sanitizeFormData = (data: Record<string, any>): Record<string, any> => {
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // 检查是否包含危险字符
        if (containsDangerousChars(value) || containsSqlInjection(value)) {
          console.warn(`Dangerous input detected in field: ${key}`)
          sanitized[key] = sanitizeInput(value)
        } else {
          sanitized[key] = sanitizeInput(value)
        }
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }

  /**
   * 检查是否为安全的URL
   */
  const isSafeUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      
      // 只允许http和https协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false
      }
      
      // 检查是否包含危险字符
      if (containsDangerousChars(url)) {
        return false
      }
      
      return true
    } catch {
      return false
    }
  }

  /**
   * 生成安全的随机字符串
   */
  const generateSecureRandom = (length: number = 32): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const array = new Uint8Array(length)
    crypto.getRandomValues(array)
    
    return Array.from(array, byte => chars[byte % chars.length]).join('')
  }

  /**
   * 安全的本地存储
   */
  const secureStorage = {
    set: (key: string, value: any, encrypt: boolean = false) => {
      try {
        const data = encrypt ? btoa(JSON.stringify(value)) : JSON.stringify(value)
        localStorage.setItem(key, data)
      } catch (error) {
        console.error('Failed to store data securely:', error)
      }
    },
    
    get: (key: string, decrypt: boolean = false) => {
      try {
        const data = localStorage.getItem(key)
        if (!data) return null
        
        const parsed = decrypt ? JSON.parse(atob(data)) : JSON.parse(data)
        return parsed
      } catch (error) {
        console.error('Failed to retrieve data securely:', error)
        return null
      }
    },
    
    remove: (key: string) => {
      localStorage.removeItem(key)
    }
  }

  return {
    sanitizeInput,
    containsDangerousChars,
    containsSqlInjection,
    generateCsrfToken,
    validateCsrfToken,
    checkPasswordStrength,
    sanitizeFormData,
    isSafeUrl,
    generateSecureRandom,
    secureStorage
  }
}
