<template>
  <div class="help-tip-container">
    <!-- 简单提示 -->
    <div v-if="type === 'simple'" class="simple-tip">
      <el-icon class="tip-icon"><InfoFilled /></el-icon>
      <span class="tip-text">{{ content }}</span>
    </div>
    
    <!-- 详细提示 -->
    <div v-else-if="type === 'detailed'" class="detailed-tip">
      <el-alert
        :title="title"
        :type="alertType"
        :closable="closable"
        show-icon
      >
        <template #default>
          <div class="tip-content">
            <p v-if="content" class="tip-description">{{ content }}</p>
            <div v-if="steps && steps.length > 0" class="tip-steps">
              <h4>操作步骤：</h4>
              <ol>
                <li v-for="(step, index) in steps" :key="index">{{ step }}</li>
              </ol>
            </div>
            <div v-if="tips && tips.length > 0" class="tip-list">
              <h4>注意事项：</h4>
              <ul>
                <li v-for="(tip, index) in tips" :key="index">{{ tip }}</li>
              </ul>
            </div>
          </div>
        </template>
      </el-alert>
    </div>
    
    <!-- 卡片提示 -->
    <div v-else-if="type === 'card'" class="card-tip">
      <el-card class="tip-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><QuestionFilled /></el-icon>
            <span class="header-title">{{ title }}</span>
          </div>
        </template>
        <div class="card-content">
          <p v-if="content" class="card-description">{{ content }}</p>
          <div v-if="examples && examples.length > 0" class="card-examples">
            <h4>示例：</h4>
            <div v-for="(example, index) in examples" :key="index" class="example-item">
              <div class="example-label">{{ example.label }}</div>
              <div class="example-value">{{ example.value }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 弹窗提示 -->
    <div v-else-if="type === 'popover'" class="popover-tip">
      <el-popover
        placement="top"
        :width="popoverWidth"
        trigger="hover"
        :content="content"
      >
        <template #reference>
          <el-button circle size="small" class="tip-button">
            <el-icon><QuestionFilled /></el-icon>
          </el-button>
        </template>
      </el-popover>
    </div>
    
    <!-- 展开式提示 -->
    <div v-else-if="type === 'collapse'" class="collapse-tip">
      <el-collapse-item :title="title" class="tip-collapse">
        <div class="collapse-content">
          <p v-if="content" class="collapse-description">{{ content }}</p>
          <div v-if="features && features.length > 0" class="collapse-features">
            <h4>功能特性：</h4>
            <div class="features-grid">
              <div v-for="(feature, index) in features" :key="index" class="feature-item">
                <el-icon class="feature-icon"><CircleCheck /></el-icon>
                <span class="feature-text">{{ feature }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </div>
    
    <!-- 视频教程提示 -->
    <div v-else-if="type === 'video'" class="video-tip">
      <el-card class="video-card">
        <template #header>
          <div class="video-header">
            <el-icon class="header-icon"><VideoPlay /></el-icon>
            <span class="header-title">{{ title }}</span>
          </div>
        </template>
        <div class="video-content">
          <p v-if="content" class="video-description">{{ content }}</p>
          <div class="video-actions">
            <el-button type="primary" @click="playVideo" v-if="videoUrl">
              <el-icon><VideoPlay /></el-icon>
              观看教程
            </el-button>
            <el-button @click="downloadGuide" v-if="guideUrl">
              <el-icon><Download /></el-icon>
              下载指南
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 配置助手提示 -->
    <div v-else-if="type === 'config'" class="config-tip">
      <el-card class="config-card">
        <template #header>
          <div class="config-header">
            <el-icon class="header-icon"><Tools /></el-icon>
            <span class="header-title">{{ title }}</span>
          </div>
        </template>
        <div class="config-content">
          <p v-if="content" class="config-description">{{ content }}</p>
          <div v-if="configs && configs.length > 0" class="config-list">
            <div v-for="(config, index) in configs" :key="index" class="config-item">
              <div class="config-label">{{ config.label }}</div>
              <div class="config-value">{{ config.value }}</div>
              <div v-if="config.description" class="config-desc">{{ config.description }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  InfoFilled, 
  QuestionFilled, 
  CircleCheck, 
  VideoPlay, 
  Download, 
  Tools 
} from '@element-plus/icons-vue'

const props = defineProps({
  type: {
    type: String,
    default: 'simple',
    validator: (value) => ['simple', 'detailed', 'card', 'popover', 'collapse', 'video', 'config'].includes(value)
  },
  title: {
    type: String,
    default: '使用说明'
  },
  content: {
    type: String,
    default: ''
  },
  alertType: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'warning', 'info', 'error'].includes(value)
  },
  closable: {
    type: Boolean,
    default: true
  },
  steps: {
    type: Array,
    default: () => []
  },
  tips: {
    type: Array,
    default: () => []
  },
  examples: {
    type: Array,
    default: () => []
  },
  features: {
    type: Array,
    default: () => []
  },
  configs: {
    type: Array,
    default: () => []
  },
  popoverWidth: {
    type: Number,
    default: 300
  },
  videoUrl: {
    type: String,
    default: ''
  },
  guideUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['play-video', 'download-guide'])

const playVideo = () => {
  if (props.videoUrl) {
    emit('play-video', props.videoUrl)
  } else {
    ElMessage.info('视频教程准备中...')
  }
}

const downloadGuide = () => {
  if (props.guideUrl) {
    emit('download-guide', props.guideUrl)
  } else {
    ElMessage.info('使用指南准备中...')
  }
}
</script>

<style lang="scss" scoped>
.help-tip-container {
  width: 100%;
  
  // 简单提示样式
  .simple-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    font-size: 12px;
    color: #0369a1;
    
    .tip-icon {
      font-size: 14px;
      color: #0ea5e9;
    }
    
    .tip-text {
      line-height: 1.4;
    }
  }
  
  // 详细提示样式
  .detailed-tip {
    .tip-content {
      .tip-description {
        margin: 0 0 16px 0;
        font-size: 14px;
        line-height: 1.5;
      }
      
      .tip-steps,
      .tip-list {
        margin-bottom: 16px;
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
        }
        
        ol, ul {
          margin: 0;
          padding-left: 20px;
          
          li {
            margin-bottom: 4px;
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
          }
        }
      }
    }
  }
  
  // 卡片提示样式
  .card-tip {
    .tip-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .header-icon {
          font-size: 18px;
          color: #3b82f6;
        }
        
        .header-title {
          font-size: 16px;
          font-weight: 600;
          color: #374151;
        }
      }
      
      .card-content {
        .card-description {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6b7280;
        }
        
        .card-examples {
          h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }
          
          .example-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8fafc;
            border-radius: 6px;
            margin-bottom: 8px;
            
            .example-label {
              font-size: 13px;
              color: #6b7280;
            }
            
            .example-value {
              font-size: 13px;
              font-weight: 500;
              color: #374151;
              font-family: monospace;
            }
          }
        }
      }
    }
  }
  
  // 弹窗提示样式
  .popover-tip {
    .tip-button {
      background: #f3f4f6;
      border: 1px solid #d1d5db;
      color: #6b7280;
      
      &:hover {
        background: #e5e7eb;
        border-color: #9ca3af;
      }
    }
  }
  
  // 展开式提示样式
  .collapse-tip {
    .tip-collapse {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      
      .collapse-content {
        padding: 16px;
        
        .collapse-description {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6b7280;
        }
        
        .collapse-features {
          h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }
          
          .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
            
            .feature-item {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .feature-icon {
                font-size: 16px;
                color: #10b981;
              }
              
              .feature-text {
                font-size: 13px;
                color: #6b7280;
              }
            }
          }
        }
      }
    }
  }
  
  // 视频教程提示样式
  .video-tip {
    .video-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .video-header {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .header-icon {
          font-size: 18px;
          color: #f59e0b;
        }
        
        .header-title {
          font-size: 16px;
          font-weight: 600;
          color: #374151;
        }
      }
      
      .video-content {
        .video-description {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6b7280;
        }
        
        .video-actions {
          display: flex;
          gap: 12px;
        }
      }
    }
  }
  
  // 配置助手提示样式
  .config-tip {
    .config-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .config-header {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .header-icon {
          font-size: 18px;
          color: #8b5cf6;
        }
        
        .header-title {
          font-size: 16px;
          font-weight: 600;
          color: #374151;
        }
      }
      
      .config-content {
        .config-description {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6b7280;
        }
        
        .config-list {
          .config-item {
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 8px;
            
            .config-label {
              font-size: 13px;
              font-weight: 500;
              color: #374151;
              margin-bottom: 4px;
            }
            
            .config-value {
              font-size: 14px;
              font-weight: 600;
              color: #1f2937;
              font-family: monospace;
              margin-bottom: 4px;
            }
            
            .config-desc {
              font-size: 12px;
              color: #6b7280;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .help-tip-container {
    .card-tip .tip-card .card-content .card-examples .example-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
    
    .collapse-tip .collapse-content .collapse-features .features-grid {
      grid-template-columns: 1fr;
    }
    
    .video-tip .video-card .video-content .video-actions {
      flex-direction: column;
    }
  }
}
</style> 