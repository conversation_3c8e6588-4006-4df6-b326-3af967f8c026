<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SubstationFinanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 分站财务控制器
 */
class SubstationFinanceController extends Controller
{
    protected $financeService;

    public function __construct(SubstationFinanceService $financeService)
    {
        $this->middleware('auth:api');
        $this->middleware('substation.permission:finance_view');
        $this->financeService = $financeService;
    }

    /**
     * 获取财务统计
     */
    public function getStats(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|in:today,week,month,quarter,year',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $period = $request->input('period', 'month');

        $stats = $this->financeService->getFinanceStats($substationId, $period);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 生成财务报表
     */
    public function generateReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'nullable|in:array,excel',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $format = $request->input('format', 'array');

        $report = $this->financeService->generateFinanceReport(
            $substationId,
            $request->start_date,
            $request->end_date,
            $format
        );

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * 获取结算记录
     */
    public function getSettlementRecords(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 20);

        $records = $this->financeService->getSettlementRecords($substationId, $page, $limit);

        return response()->json([
            'success' => true,
            'data' => $records,
        ]);
    }

    /**
     * 批量结算佣金
     */
    public function batchSettle(Request $request)
    {
        $this->middleware('substation.permission:finance_manage');

        $validator = Validator::make($request->all(), [
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'integer|exists:agent_commission_logs,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');

        try {
            $result = $this->financeService->batchSettleCommissions(
                $substationId,
                $request->commission_ids
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => "成功结算 {$result['settled_count']} 笔佣金，总金额 {$result['total_amount']} 元",
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '结算失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取收入趋势
     */
    public function getRevenueTrend(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'days' => 'nullable|integer|min:7|max:365',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $days = $request->input('days', 30);

        $startDate = now()->subDays($days)->format('Y-m-d');
        $endDate = now()->format('Y-m-d');

        $report = $this->financeService->generateFinanceReport($substationId, $startDate, $endDate);
        $trends = $report['details']['trends'] ?? [];

        return response()->json([
            'success' => true,
            'data' => $trends,
        ]);
    }

    /**
     * 获取佣金分析
     */
    public function getCommissionAnalysis(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|in:today,week,month,quarter,year',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $period = $request->input('period', 'month');

        $stats = $this->financeService->getFinanceStats($substationId, $period);
        $commissionData = $stats['commission'] ?? [];

        return response()->json([
            'success' => true,
            'data' => $commissionData,
        ]);
    }

    /**
     * 获取顶级代理商收益排行
     */
    public function getTopAgents(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|in:today,week,month,quarter,year',
            'limit' => 'nullable|integer|min:5|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');
        $period = $request->input('period', 'month');

        $stats = $this->financeService->getFinanceStats($substationId, $period);
        $topAgents = $stats['top_agents'] ?? [];

        return response()->json([
            'success' => true,
            'data' => $topAgents,
        ]);
    }

    /**
     * 导出财务数据
     */
    public function exportData(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'type' => 'required|in:revenue,commission,settlement',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $substationId = $request->attributes->get('substation_id');

        try {
            $report = $this->financeService->generateFinanceReport(
                $substationId,
                $request->start_date,
                $request->end_date,
                'excel'
            );

            // 这里应该返回文件下载响应
            // 暂时返回数据
            return response()->json([
                'success' => true,
                'data' => $report,
                'message' => '数据导出成功',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败: ' . $e->getMessage(),
            ], 500);
        }
    }
}