import request from '@/utils/request'

// 系统相关API
export const systemApi = {
  // 获取系统信息
  getSystemInfo() {
    return request({
      url: '/admin/system/info',
      method: 'get'
    })
  },

  // 获取健康状态
  getHealthStatus() {
    return request({
      url: '/admin/system/health',
      method: 'get'
    })
  },

  // 运行健康检查
  runHealthCheck() {
    return request({
      url: '/admin/system/health-check',
      method: 'post'
    })
  },

  // 运行系统检测
  runSystemDetection() {
    return request({
      url: '/admin/system/detection',
      method: 'post'
    })
  },

  // 获取检测历史
  getDetectionHistory() {
    return request({
      url: '/admin/system/detection-history',
      method: 'get'
    })
  },

  // 获取系统设置
  getSystemSettings() {
    return request({
      url: '/admin/system/settings',
      method: 'get'
    })
  },

  // 更新系统设置
  updateSystemSettings(data) {
    return request({
      url: '/admin/system/settings',
      method: 'put',
      data
    })
  },

  // 获取系统配置
  getSystemConfig() {
    return request({
      url: '/admin/system/config',
      method: 'get'
    })
  },

  // 更新系统配置
  updateSystemConfig(data) {
    return request({
      url: '/admin/system/config',
      method: 'put',
      data
    })
  },

  // 获取系统日志
  getSystemLogs(params) {
    return request({
      url: '/admin/system/logs',
      method: 'get',
      params
    })
  },

  // 清理系统缓存
  clearCache() {
    return request({
      url: '/admin/system/cache/clear',
      method: 'post'
    })
  },

  // 系统备份
  createBackup() {
    return request({
      url: '/admin/system/backup',
      method: 'post'
    })
  },

  // 获取备份列表
  getBackupList() {
    return request({
      url: '/admin/system/backup/list',
      method: 'get'
    })
  },

  // 恢复备份
  restoreBackup(backupId) {
    return request({
      url: `/admin/system/backup/${backupId}/restore`,
      method: 'post'
    })
  },

  // 测试支付配置
  testPaymentConfig(provider, config) {
    return request({
      url: '/admin/system/payment/test',
      method: 'post',
      data: { provider, config }
    })
  },

  // 获取支付配置
  getPaymentConfig() {
    return request({
      url: '/admin/system/payment/config',
      method: 'get'
    })
  },

  // 更新支付配置
  updatePaymentConfig(data) {
    return request({
      url: '/admin/system/payment/config',
      method: 'put',
      data
    })
  },

  // 获取邮件配置
  getEmailConfig() {
    return request({
      url: '/admin/system/email/config',
      method: 'get'
    })
  },

  // 更新邮件配置
  updateEmailConfig(data) {
    return request({
      url: '/admin/system/email/config',
      method: 'put',
      data
    })
  },

  // 测试邮件配置
  testEmailConfig(config) {
    return request({
      url: '/admin/system/email/test',
      method: 'post',
      data: config
    })
  },

  // 获取短信配置
  getSmsConfig() {
    return request({
      url: '/admin/system/sms/config',
      method: 'get'
    })
  },

  // 更新短信配置
  updateSmsConfig(data) {
    return request({
      url: '/admin/system/sms/config',
      method: 'put',
      data
    })
  },

  // 测试短信配置
  testSmsConfig(config) {
    return request({
      url: '/admin/system/sms/test',
      method: 'post',
      data: config
    })
  }
}

// 为了兼容性，也导出单独的函数
export const getSystemInfo = systemApi.getSystemInfo
export const getHealthStatus = systemApi.getHealthStatus
export const runHealthCheck = systemApi.runHealthCheck
export const runSystemDetection = systemApi.runSystemDetection
export const getDetectionHistory = systemApi.getDetectionHistory
export const getSystemSettings = systemApi.getSystemSettings
export const updateSystemSettings = systemApi.updateSystemSettings
export const getSystemConfig = systemApi.getSystemConfig
export const updateSystemConfig = systemApi.updateSystemConfig
export const getSystemLogs = systemApi.getSystemLogs
export const clearCache = systemApi.clearCache
export const createBackup = systemApi.createBackup
export const getBackupList = systemApi.getBackupList
export const restoreBackup = systemApi.restoreBackup
export const testPaymentConfig = systemApi.testPaymentConfig
export const getPaymentConfig = systemApi.getPaymentConfig
export const updatePaymentConfig = systemApi.updatePaymentConfig
export const getEmailConfig = systemApi.getEmailConfig
export const updateEmailConfig = systemApi.updateEmailConfig
export const testEmailConfig = systemApi.testEmailConfig
export const getSmsConfig = systemApi.getSmsConfig
export const updateSmsConfig = systemApi.updateSmsConfig
export const testSmsConfig = systemApi.testSmsConfig

export default systemApi