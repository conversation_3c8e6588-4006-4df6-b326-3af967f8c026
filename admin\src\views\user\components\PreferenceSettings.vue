<template>
  <div class="preference-settings">
    <el-form 
      ref="preferenceFormRef"
      :model="preferenceForm"
      label-width="160px"
      class="settings-form"
    >
      <!-- 主题设置 -->
      <el-divider content-position="left">主题设置</el-divider>
      <el-form-item label="主题模式" prop="theme">
        <el-radio-group v-model="preferenceForm.theme" @change="handleThemeChange">
          <el-radio-button label="light">
            <el-icon><Sunny /></el-icon>
            浅色模式
          </el-radio-button>
          <el-radio-button label="dark">
            <el-icon><Moon /></el-icon>
            深色模式
          </el-radio-button>
          <el-radio-button label="auto">
            <el-icon><Monitor /></el-icon>
            跟随系统
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="主题色" prop="primaryColor">
        <div class="color-picker-group">
          <el-color-picker 
            v-model="preferenceForm.primaryColor" 
            @change="handleColorChange"
            :predefine="predefinedColors"
            show-alpha
          />
          <span class="color-preview">
            当前: <span 
              class="color-block" 
              :style="{ backgroundColor: preferenceForm.primaryColor }"
            ></span>
          </span>
        </div>
      </el-form-item>

      <el-form-item label="字体大小" prop="fontSize">
        <el-slider
          v-model="preferenceForm.fontSize"
          :min="12"
          :max="20"
          :step="1"
          :marks="{ 12: '小', 16: '标准', 20: '大' }"
          @change="handleFontSizeChange"
          style="width: 300px"
        />
      </el-form-item>

      <!-- 界面设置 -->
      <el-divider content-position="left">界面设置</el-divider>

      <el-form-item label="侧边栏模式" prop="sidebarMode">
        <el-radio-group v-model="preferenceForm.sidebarMode" @change="handleSidebarModeChange">
          <el-radio label="vertical">垂直菜单</el-radio>
          <el-radio label="horizontal">水平菜单</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="菜单折叠" prop="sidebarCollapsed">
        <el-switch
          v-model="preferenceForm.sidebarCollapsed"
          active-text="折叠"
          inactive-text="展开"
          @change="handleSidebarCollapsedChange"
        />
      </el-form-item>

      <el-form-item label="标签页" prop="tagsView">
        <el-switch
          v-model="preferenceForm.tagsView"
          active-text="开启"
          inactive-text="关闭"
          @change="handleTagsViewChange"
        />
      </el-form-item>

      <el-form-item label="面包屑导航" prop="breadcrumb">
        <el-switch
          v-model="preferenceForm.breadcrumb"
          active-text="显示"
          inactive-text="隐藏"
          @change="handleBreadcrumbChange"
        />
      </el-form-item>

      <el-form-item label="固定头部" prop="fixedHeader">
        <el-switch
          v-model="preferenceForm.fixedHeader"
          active-text="固定"
          inactive-text="不固定"
          @change="handleFixedHeaderChange"
        />
      </el-form-item>

      <el-form-item label="Logo" prop="showLogo">
        <el-switch
          v-model="preferenceForm.showLogo"
          active-text="显示"
          inactive-text="隐藏"
          @change="handleShowLogoChange"
        />
      </el-form-item>

      <el-form-item label="动画效果" prop="animation">
        <el-switch
          v-model="preferenceForm.animation"
          active-text="开启"
          inactive-text="关闭"
          @change="handleAnimationChange"
        />
      </el-form-item>

      <el-form-item label="加载动画" prop="loadingAnimation">
        <el-select v-model="preferenceForm.loadingAnimation" @change="handleLoadingAnimationChange">
          <el-option label="旋转加载" value="spin" />
          <el-option label="跳动点" value="dots" />
          <el-option label="脉冲" value="pulse" />
          <el-option label="条形" value="bar" />
        </el-select>
      </el-form-item>

      <!-- 通知设置 -->
      <el-divider content-position="left">通知设置</el-divider>

      <el-form-item label="系统通知" prop="systemNotifications">
        <el-switch
          v-model="preferenceForm.systemNotifications"
          active-text="开启"
          inactive-text="关闭"
          @change="handleSystemNotificationsChange"
        />
      </el-form-item>

      <el-form-item label="邮件通知" prop="emailNotifications">
        <el-switch
          v-model="preferenceForm.emailNotifications"
          active-text="开启"
          inactive-text="关闭"
          @change="handleEmailNotificationsChange"
        />
      </el-form-item>

      <el-form-item label="浏览器推送" prop="browserNotifications">
        <el-switch
          v-model="preferenceForm.browserNotifications"
          active-text="开启"
          inactive-text="关闭"
          @change="handleBrowserNotificationsChange"
        />
      </el-form-item>

      <el-form-item label="声音提醒" prop="soundNotifications">
        <el-switch
          v-model="preferenceForm.soundNotifications"
          active-text="开启"
          inactive-text="关闭"
          @change="handleSoundNotificationsChange"
        />
      </el-form-item>

      <el-form-item label="通知免打扰" prop="doNotDisturb">
        <el-time-picker
          v-model="preferenceForm.doNotDisturb"
          is-range
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="HH:mm"
          @change="handleDoNotDisturbChange"
          style="width: 300px"
        />
      </el-form-item>

      <!-- 数据设置 -->
      <el-divider content-position="left">数据设置</el-divider>

      <el-form-item label="每页显示条数" prop="pageSize">
        <el-select v-model="preferenceForm.pageSize" @change="handlePageSizeChange">
          <el-option :label="`10条/页`" :value="10" />
          <el-option :label="`20条/页`" :value="20" />
          <el-option :label="`50条/页`" :value="50" />
          <el-option :label="`100条/页`" :value="100" />
        </el-select>
      </el-form-item>

      <el-form-item label="数据自动刷新" prop="autoRefresh">
        <el-switch
          v-model="preferenceForm.autoRefresh"
          active-text="开启"
          inactive-text="关闭"
          @change="handleAutoRefreshChange"
        />
      </el-form-item>

      <el-form-item label="刷新间隔" prop="refreshInterval">
        <el-select 
          v-model="preferenceForm.refreshInterval" 
          :disabled="!preferenceForm.autoRefresh"
          @change="handleRefreshIntervalChange"
        >
          <el-option label="30秒" :value="30" />
          <el-option label="1分钟" :value="60" />
          <el-option label="2分钟" :value="120" />
          <el-option label="5分钟" :value="300" />
          <el-option label="10分钟" :value="600" />
        </el-select>
      </el-form-item>

      <el-form-item label="缓存数据" prop="cacheData">
        <el-switch
          v-model="preferenceForm.cacheData"
          active-text="开启"
          inactive-text="关闭"
          @change="handleCacheDataChange"
        />
      </el-form-item>

      <el-form-item label="导出格式" prop="exportFormat">
        <el-select v-model="preferenceForm.exportFormat" @change="handleExportFormatChange">
          <el-option label="Excel" value="xlsx" />
          <el-option label="CSV" value="csv" />
          <el-option label="PDF" value="pdf" />
        </el-select>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="savePreferences" :loading="saving">
          <el-icon><Check /></el-icon>
          保存偏好
        </el-button>
        <el-button @click="resetToDefaults">恢复默认</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Check, Sunny, Moon, Monitor } from '@element-plus/icons-vue'
import { updateUserPreferences } from '@/api/user'

const userStore = useUserStore()

// 表单引用
const preferenceFormRef = ref()

// 表单数据
const preferenceForm = reactive({
  theme: 'light',
  primaryColor: '#667eea',
  fontSize: 16,
  sidebarMode: 'vertical',
  sidebarCollapsed: false,
  tagsView: true,
  breadcrumb: true,
  fixedHeader: true,
  showLogo: true,
  animation: true,
  loadingAnimation: 'spin',
  systemNotifications: true,
  emailNotifications: true,
  browserNotifications: false,
  soundNotifications: true,
  doNotDisturb: null,
  pageSize: 20,
  autoRefresh: false,
  refreshInterval: 300,
  cacheData: true,
  exportFormat: 'xlsx'
})

// 预定义颜色
const predefinedColors = [
  '#667eea',
  '#ff6b6b',
  '#4ecdc4',
  '#45b7d1',
  '#96ceb4',
  '#feca57',
  '#ff9ff3',
  '#54a0ff',
  '#5f27cd',
  '#00d2d3'
]

// 保存状态
const saving = ref(false)

// 主题变更处理
const handleThemeChange = (theme) => {
  userStore.setTheme(theme)
  applyTheme(theme)
}

// 颜色变更处理
const handleColorChange = (color) => {
  userStore.setPrimaryColor(color)
  applyPrimaryColor(color)
}

// 字体大小变更处理
const handleFontSizeChange = (size) => {
  userStore.setFontSize(size)
  applyFontSize(size)
}

// 应用主题
const applyTheme = (theme) => {
  const html = document.documentElement
  html.className = theme
}

// 应用主色调
const applyPrimaryColor = (color) => {
  document.documentElement.style.setProperty('--el-color-primary', color)
}

// 应用字体大小
const applyFontSize = (size) => {
  document.documentElement.style.setProperty('--el-font-size-base', `${size}px`)
}

// 侧边栏模式变更
const handleSidebarModeChange = (mode) => {
  userStore.setSidebarMode(mode)
}

// 侧边栏折叠变更
const handleSidebarCollapsedChange = (collapsed) => {
  userStore.setSidebarCollapsed(collapsed)
}

// 标签页变更
const handleTagsViewChange = (show) => {
  userStore.setTagsView(show)
}

// 面包屑变更
const handleBreadcrumbChange = (show) => {
  userStore.setBreadcrumb(show)
}

// 固定头部变更
const handleFixedHeaderChange = (fixed) => {
  userStore.setFixedHeader(fixed)
}

// Logo显示变更
const handleShowLogoChange = (show) => {
  userStore.setShowLogo(show)
}

// 动画效果变更
const handleAnimationChange = (animation) => {
  userStore.setAnimation(animation)
}

// 加载动画变更
const handleLoadingAnimationChange = (animation) => {
  userStore.setLoadingAnimation(animation)
}

// 系统通知变更
const handleSystemNotificationsChange = (enabled) => {
  userStore.setSystemNotifications(enabled)
}

// 邮件通知变更
const handleEmailNotificationsChange = (enabled) => {
  userStore.setEmailNotifications(enabled)
}

// 浏览器通知变更
const handleBrowserNotificationsChange = async (enabled) => {
  if (enabled) {
    const permission = await Notification.requestPermission()
    if (permission === 'granted') {
      userStore.setBrowserNotifications(true)
      ElMessage.success('浏览器通知已开启')
    } else {
      userStore.setBrowserNotifications(false)
      preferenceForm.browserNotifications = false
      ElMessage.warning('浏览器通知权限被拒绝')
    }
  } else {
    userStore.setBrowserNotifications(false)
  }
}

// 声音通知变更
const handleSoundNotificationsChange = (enabled) => {
  userStore.setSoundNotifications(enabled)
}

// 免打扰时间变更
const handleDoNotDisturbChange = (timeRange) => {
  userStore.setDoNotDisturb(timeRange)
}

// 分页大小变更
const handlePageSizeChange = (size) => {
  userStore.setPageSize(size)
}

// 自动刷新变更
const handleAutoRefreshChange = (enabled) => {
  userStore.setAutoRefresh(enabled)
}

// 刷新间隔变更
const handleRefreshIntervalChange = (interval) => {
  userStore.setRefreshInterval(interval)
}

// 缓存数据变更
const handleCacheDataChange = (enabled) => {
  userStore.setCacheData(enabled)
}

// 导出格式变更
const handleExportFormatChange = (format) => {
  userStore.setExportFormat(format)
}

// 初始化表单数据
const initFormData = () => {
  const preferences = userStore.preferences || {}
  Object.assign(preferenceForm, {
    theme: preferences.theme || 'light',
    primaryColor: preferences.primaryColor || '#667eea',
    fontSize: preferences.fontSize || 16,
    sidebarMode: preferences.sidebarMode || 'vertical',
    sidebarCollapsed: preferences.sidebarCollapsed || false,
    tagsView: preferences.tagsView !== false,
    breadcrumb: preferences.breadcrumb !== false,
    fixedHeader: preferences.fixedHeader !== false,
    showLogo: preferences.showLogo !== false,
    animation: preferences.animation !== false,
    loadingAnimation: preferences.loadingAnimation || 'spin',
    systemNotifications: preferences.systemNotifications !== false,
    emailNotifications: preferences.emailNotifications !== false,
    browserNotifications: preferences.browserNotifications || false,
    soundNotifications: preferences.soundNotifications !== false,
    doNotDisturb: preferences.doNotDisturb || null,
    pageSize: preferences.pageSize || 20,
    autoRefresh: preferences.autoRefresh || false,
    refreshInterval: preferences.refreshInterval || 300,
    cacheData: preferences.cacheData !== false,
    exportFormat: preferences.exportFormat || 'xlsx'
  })
}

// 保存偏好设置
const savePreferences = async () => {
  try {
    saving.value = true
    
    const res = await updateUserPreferences(preferenceForm)
    
    if (res.code === 200) {
      // 更新本地存储
      userStore.setPreferences(preferenceForm)
      
      // 应用所有设置
      applyAllSettings()
      
      ElMessage.success('偏好设置保存成功')
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 应用所有设置
const applyAllSettings = () => {
  applyTheme(preferenceForm.theme)
  applyPrimaryColor(preferenceForm.primaryColor)
  applyFontSize(preferenceForm.fontSize)
}

// 恢复默认设置
const resetToDefaults = () => {
  ElMessageBox.confirm(
    '确定要恢复默认设置吗？所有自定义偏好将丢失。',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const defaultPreferences = {
      theme: 'light',
      primaryColor: '#667eea',
      fontSize: 16,
      sidebarMode: 'vertical',
      sidebarCollapsed: false,
      tagsView: true,
      breadcrumb: true,
      fixedHeader: true,
      showLogo: true,
      animation: true,
      loadingAnimation: 'spin',
      systemNotifications: true,
      emailNotifications: true,
      browserNotifications: false,
      soundNotifications: true,
      doNotDisturb: null,
      pageSize: 20,
      autoRefresh: false,
      refreshInterval: 300,
      cacheData: true,
      exportFormat: 'xlsx'
    }
    
    Object.assign(preferenceForm, defaultPreferences)
    await savePreferences()
    
    ElMessage.success('已恢复默认设置')
  })
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
  applyAllSettings()
})

// 暴露方法给父组件
defineExpose({
  savePreferences
})
</script>

<style scoped>
.preference-settings {
  padding: 30px;
}

.settings-form {
  max-width: 800px;
}

.color-picker-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.color-block {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
  vertical-align: middle;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #909399;
}

@media screen and (max-width: 768px) {
  .preference-settings {
    padding: 20px;
  }
  
  .settings-form {
    max-width: 100%;
  }
  
  :deep(.el-form-item__label) {
    text-align: left;
    float: none;
    display: block;
    margin-bottom: 8px;
    width: auto !important;
  }
  
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
  
  .color-picker-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>