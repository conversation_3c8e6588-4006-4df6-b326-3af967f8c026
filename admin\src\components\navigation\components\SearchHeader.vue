<!-- 智能搜索头部组件 -->
<template>
  <div class="search-header" :class="{ 'search-active': modelValue.active }">
    <div class="search-container">
      <div class="search-input-wrapper">
        <el-input
          ref="searchInputRef"
          v-model="searchQuery"
          placeholder="全局搜索功能、用户、订单..."
          :prefix-icon="Search"
          size="large"
          clearable
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeydown"
        >
          <template #suffix>
            <div class="search-shortcuts">
              <kbd class="shortcut-key">Ctrl</kbd>
              <kbd class="shortcut-key">K</kbd>
            </div>
          </template>
        </el-input>

        <!-- 搜索建议下拉 -->
        <transition name="search-dropdown">
          <div v-if="modelValue.active && searchResults.length > 0" class="search-dropdown">
            <div class="search-results">
              <div
                v-for="(result, index) in searchResults"
                :key="result.id || index"
                class="search-result-item"
                :class="{ 
                  'result-active': modelValue.selectedIndex === index,
                  'result-priority': result.priority === 'high'
                }"
                @click="selectResult(result, index)"
                @mouseenter="modelValue.selectedIndex = index"
              >
                <div class="result-icon" :style="{ color: result.color }">
                  <el-icon><component :is="result.icon" /></el-icon>
                </div>
                <div class="result-content">
                  <div class="result-title" v-html="highlightMatch(result.title, searchQuery)"></div>
                  <div class="result-description">{{ result.description }}</div>
                  <div class="result-path">{{ result.breadcrumb }}</div>
                </div>
                <div class="result-meta">
                  <el-tag 
                    v-if="result.type" 
                    :type="getResultTypeTag(result.type)" 
                    size="small"
                  >
                    {{ result.type }}
                  </el-tag>
                  <span class="result-shortcut" v-if="result.shortcut">{{ result.shortcut }}</span>
                </div>
              </div>
            </div>

            <!-- 搜索建议 -->
            <div v-if="searchSuggestions.length > 0" class="search-suggestions">
              <div class="suggestions-title">建议搜索</div>
              <div class="suggestions-list">
                <div
                  v-for="suggestion in searchSuggestions"
                  :key="suggestion"
                  class="suggestion-item"
                  @click="applySuggestion(suggestion)"
                >
                  <el-icon><Search /></el-icon>
                  <span>{{ suggestion }}</span>
                </div>
              </div>
            </div>

            <!-- 无结果状态 -->
            <div v-else-if="searchQuery && searchResults.length === 0" class="search-empty">
              <el-icon class="empty-icon"><Search /></el-icon>
              <div class="empty-text">未找到相关结果</div>
              <div class="empty-suggestion">试试其他关键词或查看帮助</div>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <!-- 快速过滤标签 -->
    <div v-if="!modelValue.active" class="quick-filters">
      <div
        v-for="filter in quickFilters"
        :key="filter.key"
        class="filter-tag"
        :class="{ 'filter-active': activeFilter === filter.key }"
        @click="applyQuickFilter(filter)"
      >
        <el-icon><component :is="filter.icon" /></el-icon>
        <span>{{ filter.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'shortcut'])

const searchInputRef = ref(null)
const searchQuery = ref('')
const activeFilter = ref('')
const searchSuggestions = ref([])

const searchResults = computed(() => props.modelValue.results || [])

const quickFilters = computed(() => [
  { key: 'recent', label: '最近使用', icon: 'Clock' },
  { key: 'favorites', label: '收藏功能', icon: 'Star' },
  { key: 'orders', label: '订单相关', icon: 'ShoppingCart' },
  { key: 'users', label: '用户管理', icon: 'User' }
])

const highlightMatch = (text, query) => {
  if (!query) return text
  
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

const getResultTypeTag = (type) => {
  const typeMap = {
    'module': '',
    'page': 'info',
    'action': 'success',
    'data': 'warning'
  }
  return typeMap[type] || ''
}

const handleInput = (value) => {
  searchQuery.value = value
  updateModelValue({ query: value })
  emit('search', value)
  
  if (value.trim()) {
    generateSuggestions(value)
  } else {
    searchSuggestions.value = []
  }
}

const handleFocus = () => {
  updateModelValue({ active: true })
}

const handleBlur = () => {
  // 延迟关闭，允许点击结果
  setTimeout(() => {
    updateModelValue({ active: false })
  }, 200)
}

const handleKeydown = (event) => {
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      navigateResults(1)
      break
    case 'ArrowUp':
      event.preventDefault()
      navigateResults(-1)
      break
    case 'Enter':
      event.preventDefault()
      selectCurrentResult()
      break
    case 'Escape':
      event.preventDefault()
      updateModelValue({ active: false })
      searchInputRef.value?.blur()
      break
  }
  
  emit('shortcut', event)
}

const navigateResults = (direction) => {
  const resultsLength = searchResults.value.length
  if (resultsLength === 0) return
  
  const currentIndex = props.modelValue.selectedIndex
  let newIndex = currentIndex + direction
  
  if (newIndex < 0) newIndex = resultsLength - 1
  if (newIndex >= resultsLength) newIndex = 0
  
  updateModelValue({ selectedIndex: newIndex })
}

const selectCurrentResult = () => {
  const currentResult = searchResults.value[props.modelValue.selectedIndex]
  if (currentResult) {
    selectResult(currentResult, props.modelValue.selectedIndex)
  }
}

const selectResult = (result, index) => {
  updateModelValue({
    selectedIndex: index,
    active: false
  })
  
  // 触发导航或执行操作
  emit('select', result)
  
  // 清空搜索
  searchQuery.value = ''
  updateModelValue({ query: '' })
}

const applySuggestion = (suggestion) => {
  searchQuery.value = suggestion
  updateModelValue({ query: suggestion })
  emit('search', suggestion)
}

const applyQuickFilter = (filter) => {
  if (activeFilter.value === filter.key) {
    activeFilter.value = ''
    emit('filter', null)
  } else {
    activeFilter.value = filter.key
    emit('filter', filter)
  }
}

const generateSuggestions = (query) => {
  // 基于历史搜索和常用功能生成建议
  const commonSuggestions = [
    '用户管理', '订单查询', '数据导出', '系统设置',
    '群组管理', '财务报表', '权限配置', '日志查看'
  ]
  
  const filtered = commonSuggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(query.toLowerCase()) && 
    suggestion !== query
  )
  
  searchSuggestions.value = filtered.slice(0, 3)
}

const updateModelValue = (updates) => {
  emit('update:modelValue', { ...props.modelValue, ...updates })
}

// 全局快捷键处理
const handleGlobalKeydown = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    searchInputRef.value?.focus()
    updateModelValue({ active: true })
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})

// 监听外部状态变化
watch(() => props.modelValue.query, (newQuery) => {
  if (newQuery !== searchQuery.value) {
    searchQuery.value = newQuery
  }
})
</script>

<style lang="scss" scoped>
.search-header {
  padding: 20px 16px 16px 16px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 100;
  
  &.search-active {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}

.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 12px;
      border: 2px solid transparent;
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        border-color: rgba(99, 102, 241, 0.3);
        background: rgba(255, 255, 255, 1);
      }
      
      &.is-focus {
        border-color: #6366f1;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 20px rgba(99, 102, 241, 0.2);
      }
    }
    
    .el-input__inner {
      font-size: 14px;
      padding-right: 80px;
    }
  }
}

.search-shortcuts {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-right: 8px;
  
  .shortcut-key {
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 10px;
    color: #64748b;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20px);
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
  max-height: 400px;
  overflow-y: auto;
}

.search-results {
  padding: 8px;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover,
  &.result-active {
    background: linear-gradient(135deg, 
      rgba(99, 102, 241, 0.05) 0%,
      rgba(99, 102, 241, 0.08) 100%
    );
  }
  
  &.result-priority {
    border-left: 3px solid #6366f1;
  }
  
  .result-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 8px;
    margin-right: 12px;
    
    .el-icon {
      font-size: 18px;
    }
  }
  
  .result-content {
    flex: 1;
    min-width: 0;
    
    .result-title {
      font-weight: 500;
      font-size: 14px;
      color: #1f2937;
      margin-bottom: 2px;
      
      :deep(.search-highlight) {
        background: #fef3c7;
        color: #d97706;
        padding: 0 2px;
        border-radius: 2px;
      }
    }
    
    .result-description {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 2px;
    }
    
    .result-path {
      font-size: 11px;
      color: #9ca3af;
    }
  }
  
  .result-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    
    .result-shortcut {
      font-size: 10px;
      color: #9ca3af;
      background: #f9fafb;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid #e5e7eb;
    }
  }
}

.search-suggestions {
  border-top: 1px solid #f3f4f6;
  padding: 12px;
  
  .suggestions-title {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    
    .suggestion-item {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 8px;
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 11px;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
        color: #374151;
      }
      
      .el-icon {
        font-size: 10px;
      }
    }
  }
}

.search-empty {
  padding: 40px 20px;
  text-align: center;
  color: #9ca3af;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .empty-suggestion {
    font-size: 12px;
    opacity: 0.8;
  }
}

.quick-filters {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
  
  .filter-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    font-size: 12px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #6366f1;
      color: #6366f1;
      background: rgba(99, 102, 241, 0.05);
    }
    
    &.filter-active {
      background: #6366f1;
      border-color: #6366f1;
      color: white;
    }
    
    .el-icon {
      font-size: 12px;
    }
  }
}

// 动画效果
.search-dropdown-enter-active,
.search-dropdown-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-dropdown-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.search-dropdown-leave-to {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
}

// 滚动条样式
.search-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-dropdown::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  
  &:hover {
    background: #9ca3af;
  }
}
</style>