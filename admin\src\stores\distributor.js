import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 分销员工作台状态管理
 * 统一管理分销员相关的数据和业务逻辑
 */
export const useDistributorStore = defineStore('distributor', () => {
  // ==================== 状态定义 ====================
  
  // 基础信息
  const distributorInfo = ref({
    id: null,
    code: '',
    name: '',
    level: 'C',
    avatar: '',
    phone: '',
    email: '',
    status: 'active',
    created_at: null
  })

  // 统计数据
  const statistics = reactive({
    customers: {
      total: 0,
      active: 0,
      new_this_month: 0,
      trend: 0
    },
    groups: {
      total: 0,
      active: 0,
      members: 0,
      trend: 0
    },
    commission: {
      total: 0,
      this_month: 0,
      pending: 0,
      rate: 0,
      trend: 0
    },
    orders: {
      total: 0,
      amount: 0,
      pending: 0,
      success_rate: 0,
      trend: 0
    }
  })

  // 加载状态
  const loading = reactive({
    info: false,
    statistics: false,
    customers: false,
    groups: false,
    commission: false,
    orders: false
  })

  // 缓存控制
  const cache = reactive({
    statistics_updated_at: null,
    customers_updated_at: null,
    groups_updated_at: null,
    commission_updated_at: null,
    orders_updated_at: null
  })

  // ==================== 计算属性 ====================
  
  /**
   * 分销员等级文本
   */
  const levelText = computed(() => {
    const levelMap = {
      'A': 'A级分销员',
      'B': 'B级分销员',
      'C': 'C级分销员',
      'D': 'D级分销员'
    }
    return levelMap[distributorInfo.value.level] || 'C级分销员'
  })

  /**
   * 分销员代码
   */
  const distributorCode = computed(() => {
    return distributorInfo.value.code || `D${distributorInfo.value.id || '001'}`
  })

  /**
   * 是否为高级分销员
   */
  const isAdvancedDistributor = computed(() => {
    return ['A', 'B'].includes(distributorInfo.value.level)
  })

  /**
   * 本月佣金增长率
   */
  const monthlyCommissionGrowth = computed(() => {
    return statistics.commission.trend || 0
  })

  /**
   * 客户转化率
   */
  const customerConversionRate = computed(() => {
    if (statistics.customers.total === 0) return 0
    return ((statistics.orders.total / statistics.customers.total) * 100).toFixed(1)
  })

  // ==================== 业务方法 ====================

  /**
   * 加载分销员基础信息
   * @param {boolean} forceRefresh - 是否强制刷新
   */
  const loadDistributorInfo = async (forceRefresh = false) => {
    if (loading.info) return
    
    try {
      loading.info = true
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // 模拟数据
      distributorInfo.value = {
        id: 1001,
        code: 'D1001',
        name: '张三',
        level: 'B',
        avatar: '/avatars/default.jpg',
        phone: '13800138001',
        email: '<EMAIL>',
        status: 'active',
        created_at: new Date('2023-01-15')
      }
      
      console.log('✅ 分销员信息加载完成')
    } catch (error) {
      console.error('❌ 加载分销员信息失败:', error)
      ElMessage.error('加载分销员信息失败')
      throw error
    } finally {
      loading.info = false
    }
  }

  /**
   * 加载统计数据
   * @param {boolean} forceRefresh - 是否强制刷新
   */
  const loadStatistics = async (forceRefresh = false) => {
    // 缓存检查
    if (!forceRefresh && cache.statistics_updated_at) {
      const cacheAge = Date.now() - cache.statistics_updated_at
      if (cacheAge < 5 * 60 * 1000) { // 5分钟缓存
        console.log('📦 使用缓存的统计数据')
        return
      }
    }

    if (loading.statistics) return

    try {
      loading.statistics = true
      
      // 并行加载所有统计数据
      const [customerStats, groupStats, commissionStats, orderStats] = await Promise.all([
        loadCustomerStatistics(),
        loadGroupStatistics(),
        loadCommissionStatistics(),
        loadOrderStatistics()
      ])

      // 更新统计数据
      Object.assign(statistics.customers, customerStats)
      Object.assign(statistics.groups, groupStats)
      Object.assign(statistics.commission, commissionStats)
      Object.assign(statistics.orders, orderStats)

      // 更新缓存时间
      cache.statistics_updated_at = Date.now()
      
      console.log('✅ 统计数据加载完成')
    } catch (error) {
      console.error('❌ 加载统计数据失败:', error)
      ElMessage.error('加载统计数据失败')
      throw error
    } finally {
      loading.statistics = false
    }
  }

  /**
   * 加载客户统计数据
   * @private
   */
  const loadCustomerStatistics = async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return {
      total: 156,
      active: 142,
      new_this_month: 23,
      trend: 8.2
    }
  }

  /**
   * 加载群组统计数据
   * @private
   */
  const loadGroupStatistics = async () => {
    await new Promise(resolve => setTimeout(resolve, 250))
    return {
      total: 23,
      active: 21,
      members: 1580,
      trend: 15.6
    }
  }

  /**
   * 加载佣金统计数据
   * @private
   */
  const loadCommissionStatistics = async () => {
    await new Promise(resolve => setTimeout(resolve, 180))
    return {
      total: 28650.50,
      this_month: 8650.50,
      pending: 1250.00,
      rate: 15.0,
      trend: 23.4
    }
  }

  /**
   * 加载订单统计数据
   * @private
   */
  const loadOrderStatistics = async () => {
    await new Promise(resolve => setTimeout(resolve, 220))
    return {
      total: 89,
      amount: 458650.50,
      pending: 12,
      success_rate: 94.2,
      trend: 12.8
    }
  }

  /**
   * 刷新所有数据
   */
  const refreshAllData = async () => {
    try {
      await Promise.all([
        loadDistributorInfo(true),
        loadStatistics(true)
      ])
      ElMessage.success('数据刷新完成')
    } catch (error) {
      console.error('❌ 刷新数据失败:', error)
      ElMessage.error('刷新数据失败')
    }
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    Object.keys(cache).forEach(key => {
      cache[key] = null
    })
    console.log('🗑️ 缓存已清除')
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    // 重置基础信息
    distributorInfo.value = {
      id: null,
      code: '',
      name: '',
      level: 'C',
      avatar: '',
      phone: '',
      email: '',
      status: 'active',
      created_at: null
    }

    // 重置统计数据
    Object.keys(statistics).forEach(key => {
      Object.keys(statistics[key]).forEach(subKey => {
        statistics[key][subKey] = 0
      })
    })

    // 重置加载状态
    Object.keys(loading).forEach(key => {
      loading[key] = false
    })

    // 清除缓存
    clearCache()
    
    console.log('🔄 状态已重置')
  }

  // ==================== 工具方法 ====================

  /**
   * 格式化数字
   * @param {number} num - 要格式化的数字
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的字符串
   */
  const formatNumber = (num, decimals = 0) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return Number(num).toFixed(decimals)
  }

  /**
   * 格式化金额
   * @param {number} amount - 金额
   * @returns {string} 格式化后的金额字符串
   */
  const formatMoney = (amount) => {
    return Number(amount).toFixed(2)
  }

  /**
   * 获取趋势类名
   * @param {number} trend - 趋势值
   * @returns {string} CSS类名
   */
  const getTrendClass = (trend) => {
    if (trend > 0) return 'trend-up'
    if (trend < 0) return 'trend-down'
    return 'trend-neutral'
  }

  /**
   * 获取等级颜色
   * @param {string} level - 等级
   * @returns {string} 颜色值
   */
  const getLevelColor = (level) => {
    const colorMap = {
      'A': 'success',
      'B': 'primary',
      'C': 'warning',
      'D': 'info'
    }
    return colorMap[level] || 'info'
  }

  // ==================== 导出 ====================
  
  return {
    // 状态
    distributorInfo,
    statistics,
    loading,
    cache,
    
    // 计算属性
    levelText,
    distributorCode,
    isAdvancedDistributor,
    monthlyCommissionGrowth,
    customerConversionRate,
    
    // 方法
    loadDistributorInfo,
    loadStatistics,
    refreshAllData,
    clearCache,
    resetState,
    
    // 工具方法
    formatNumber,
    formatMoney,
    getTrendClass,
    getLevelColor
  }
})

/**
 * 分销员工作台状态管理使用示例：
 * 
 * import { useDistributorStore } from '@/stores/distributor'
 * 
 * const distributorStore = useDistributorStore()
 * 
 * // 加载数据
 * await distributorStore.loadDistributorInfo()
 * await distributorStore.loadStatistics()
 * 
 * // 使用计算属性
 * console.log(distributorStore.levelText)
 * console.log(distributorStore.customerConversionRate)
 * 
 * // 格式化数据
 * const formattedMoney = distributorStore.formatMoney(1234.56)
 * const formattedNumber = distributorStore.formatNumber(12345)
 */