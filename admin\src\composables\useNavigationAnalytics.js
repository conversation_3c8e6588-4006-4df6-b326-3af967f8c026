import { ref, reactive } from 'vue'

/**
 * 导航分析 Composable
 * 用于跟踪和分析用户导航行为
 */
export function useNavigationAnalytics() {
  // 分析数据存储
  const analytics = reactive({
    pageViews: {},
    navigationPaths: [],
    timeSpent: {},
    clickEvents: [],
    searchQueries: [],
    userFlow: []
  })

  // 当前会话数据
  const currentSession = ref({
    startTime: Date.now(),
    currentPage: null,
    pageStartTime: null,
    totalClicks: 0,
    totalSearches: 0
  })

  // 记录页面访问
  const trackPageView = (pagePath, pageTitle = '') => {
    const timestamp = Date.now()
    
    // 如果有当前页面，记录停留时间
    if (currentSession.value.currentPage && currentSession.value.pageStartTime) {
      const timeSpent = timestamp - currentSession.value.pageStartTime
      recordTimeSpent(currentSession.value.currentPage, timeSpent)
    }

    // 更新页面访问统计
    if (!analytics.pageViews[pagePath]) {
      analytics.pageViews[pagePath] = {
        count: 0,
        title: pageTitle,
        firstVisit: timestamp,
        lastVisit: timestamp,
        totalTime: 0
      }
    }

    analytics.pageViews[pagePath].count++
    analytics.pageViews[pagePath].lastVisit = timestamp

    // 记录导航路径
    if (currentSession.value.currentPage) {
      analytics.navigationPaths.push({
        from: currentSession.value.currentPage,
        to: pagePath,
        timestamp,
        timeSpent: timestamp - currentSession.value.pageStartTime
      })
    }

    // 更新当前会话
    currentSession.value.currentPage = pagePath
    currentSession.value.pageStartTime = timestamp

    console.log('📊 页面访问记录:', pagePath, pageTitle)
  }

  // 记录停留时间
  const recordTimeSpent = (pagePath, timeSpent) => {
    if (!analytics.timeSpent[pagePath]) {
      analytics.timeSpent[pagePath] = {
        totalTime: 0,
        sessions: 0,
        averageTime: 0
      }
    }

    analytics.timeSpent[pagePath].totalTime += timeSpent
    analytics.timeSpent[pagePath].sessions++
    analytics.timeSpent[pagePath].averageTime = 
      analytics.timeSpent[pagePath].totalTime / analytics.timeSpent[pagePath].sessions
  }

  // 记录点击事件
  const trackClick = (element, elementType = 'button', context = {}) => {
    const clickEvent = {
      timestamp: Date.now(),
      element,
      elementType,
      page: currentSession.value.currentPage,
      context,
      sessionId: currentSession.value.startTime
    }

    analytics.clickEvents.push(clickEvent)
    currentSession.value.totalClicks++

    console.log('🖱️ 点击事件记录:', element, elementType)
  }

  // 记录搜索查询
  const trackSearch = (query, results = [], context = {}) => {
    const searchEvent = {
      timestamp: Date.now(),
      query: query.trim(),
      resultsCount: results.length,
      page: currentSession.value.currentPage,
      context,
      sessionId: currentSession.value.startTime
    }

    analytics.searchQueries.push(searchEvent)
    currentSession.value.totalSearches++

    console.log('🔍 搜索事件记录:', query, results.length)
  }

  // 记录用户流程
  const trackUserFlow = (action, target, metadata = {}) => {
    const flowEvent = {
      timestamp: Date.now(),
      action,
      target,
      page: currentSession.value.currentPage,
      metadata,
      sessionId: currentSession.value.startTime
    }

    analytics.userFlow.push(flowEvent)

    console.log('🔄 用户流程记录:', action, target)
  }

  // 获取热门页面
  const getPopularPages = (limit = 10) => {
    return Object.entries(analytics.pageViews)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, limit)
      .map(([path, data]) => ({ path, ...data }))
  }

  // 获取常用导航路径
  const getCommonPaths = (limit = 10) => {
    const pathCounts = {}
    
    analytics.navigationPaths.forEach(path => {
      const key = `${path.from} → ${path.to}`
      pathCounts[key] = (pathCounts[key] || 0) + 1
    })

    return Object.entries(pathCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([path, count]) => ({ path, count }))
  }

  // 获取搜索统计
  const getSearchStats = () => {
    const queryFrequency = {}
    let totalSearches = analytics.searchQueries.length
    let successfulSearches = 0

    analytics.searchQueries.forEach(search => {
      queryFrequency[search.query] = (queryFrequency[search.query] || 0) + 1
      if (search.resultsCount > 0) {
        successfulSearches++
      }
    })

    const popularQueries = Object.entries(queryFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }))

    return {
      totalSearches,
      successfulSearches,
      successRate: totalSearches > 0 ? (successfulSearches / totalSearches) * 100 : 0,
      popularQueries
    }
  }

  // 获取会话统计
  const getSessionStats = () => {
    const sessionDuration = Date.now() - currentSession.value.startTime
    const pagesVisited = Object.keys(analytics.pageViews).length
    
    return {
      duration: sessionDuration,
      pagesVisited,
      totalClicks: currentSession.value.totalClicks,
      totalSearches: currentSession.value.totalSearches,
      averageTimePerPage: pagesVisited > 0 ? sessionDuration / pagesVisited : 0
    }
  }

  // 导出分析数据
  const exportAnalytics = () => {
    return {
      analytics: { ...analytics },
      session: { ...currentSession.value },
      stats: {
        popularPages: getPopularPages(),
        commonPaths: getCommonPaths(),
        searchStats: getSearchStats(),
        sessionStats: getSessionStats()
      },
      exportTime: Date.now()
    }
  }

  // 清除分析数据
  const clearAnalytics = () => {
    Object.keys(analytics.pageViews).forEach(key => delete analytics.pageViews[key])
    analytics.navigationPaths.length = 0
    Object.keys(analytics.timeSpent).forEach(key => delete analytics.timeSpent[key])
    analytics.clickEvents.length = 0
    analytics.searchQueries.length = 0
    analytics.userFlow.length = 0

    currentSession.value = {
      startTime: Date.now(),
      currentPage: null,
      pageStartTime: null,
      totalClicks: 0,
      totalSearches: 0
    }
  }

  // 发送分析数据到服务器（可选）
  const sendAnalytics = async (endpoint = '/api/analytics') => {
    try {
      const data = exportAnalytics()
      
      // 这里可以实现实际的数据发送逻辑
      console.log('📈 发送分析数据:', data)
      
      // 示例：使用fetch发送数据
      // await fetch(endpoint, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data)
      // })
      
      return true
    } catch (error) {
      console.error('发送分析数据失败:', error)
      return false
    }
  }

  return {
    analytics,
    currentSession,
    trackPageView,
    trackClick,
    trackSearch,
    trackUserFlow,
    getPopularPages,
    getCommonPaths,
    getSearchStats,
    getSessionStats,
    exportAnalytics,
    clearAnalytics,
    sendAnalytics
  }
}
