<!-- 增强侧边导航栏组件 -->
<!-- admin/src/components/navigation/NavigationSidebar.vue -->

<template>
  <aside 
    class="navigation-sidebar enhanced-sidebar" 
    :class="sidebarClasses"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <!-- Logo 区域 -->
      <div class="header-logo" @click="navigateHome">
        <transition name="logo-scale" mode="out-in">
          <div class="logo-container" :key="collapsed ? 'mini' : 'full'">
            <div class="logo-icon">
              <el-icon><component :is="appConfig.brandIcon" /></el-icon>
            </div>
            <div class="logo-text" v-if="!collapsed || hovered">
              <h1 class="app-title">{{ appConfig.appName }}</h1>
              <span class="app-version">v{{ appConfig.version }}</span>
            </div>
          </div>
        </transition>
      </div>
      
      <!-- 用户信息 -->
      <div class="user-summary" v-if="!collapsed || hovered">
        <transition name="user-fade">
          <div class="user-card" @click="showUserProfile = !showUserProfile">
            <el-avatar :src="userInfo.avatar" :size="32" class="user-avatar">
              <img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" alt="用户头像"/>
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ userInfo.name || '用户' }}</div>
              <div class="user-role">{{ getRoleText(userInfo.role) }}</div>
              <div class="user-status">
                <div class="status-dot online"></div>
                <span>在线</span>
              </div>
            </div>
            <el-icon class="expand-icon" :class="{ rotated: showUserProfile }">
              <ArrowDown />
            </el-icon>
          </div>
        </transition>
        
        <!-- 用户快速操作 -->
        <transition name="profile-expand">
          <div v-show="showUserProfile" class="user-actions">
            <el-button 
              v-for="action in userQuickActions"
              :key="action.key"
              :type="action.type || 'text'"
              size="small"
              @click="handleUserAction(action)"
              class="user-action-btn"
            >
              <el-icon><component :is="action.icon" /></el-icon>
              <span>{{ action.label }}</span>
            </el-button>
          </div>
        </transition>
      </div>
    </div>
    
    <!-- 导航搜索 -->
    <div class="sidebar-search" v-if="!collapsed || hovered">
      <transition name="search-fade">
        <div class="search-container">
          <el-input
            v-model="searchQuery"
            placeholder="搜索菜单..."
            :prefix-icon="Search"
            size="small"
            clearable
            @input="handleSearch"
            class="nav-search-input"
          />
          <div class="search-filters" v-if="searchQuery">
            <el-tag
              v-for="filter in activeFilters"
              :key="filter"
              closable
              size="small"
              @close="removeFilter(filter)"
            >
              {{ filter }}
            </el-tag>
          </div>
        </div>
      </transition>
    </div>
    
    <!-- 导航菜单 -->
    <div class="sidebar-content">
      <el-scrollbar class="nav-scrollbar" height="calc(100% - 240px)">
        <nav class="navigation-menu">
          <!-- 收藏菜单 -->
          <div class="nav-section favorites-section" v-if="favoriteItems.length">
            <div class="section-header" @click="toggleSection('favorites')">
              <el-icon class="section-icon"><StarFilled /></el-icon>
              <span class="section-title" v-if="!collapsed || hovered">我的收藏</span>
              <el-icon 
                class="section-toggle" 
                v-if="!collapsed || hovered"
                :class="{ rotated: expandedSections.includes('favorites') }"
              >
                <ArrowRight />
              </el-icon>
            </div>
            <transition name="section-expand">
              <div v-show="expandedSections.includes('favorites')" class="section-content">
                <div 
                  v-for="item in favoriteItems.slice(0, 5)"
                  :key="`fav-${item.key}`"
                  class="nav-item favorite-item"
                  @click="navigateToItem(item)"
                >
                  <div class="item-icon">
                    <el-icon><component :is="item.icon" /></el-icon>
                  </div>
                  <span class="item-text" v-if="!collapsed || hovered">{{ item.title }}</span>
                  <el-button 
                    type="text" 
                    size="small" 
                    @click.stop="removeFavorite(item)"
                    class="remove-favorite"
                    v-if="!collapsed || hovered"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
            </transition>
          </div>
          
          <!-- 最近访问 -->
          <div class="nav-section recent-section" v-if="recentItems.length">
            <div class="section-header" @click="toggleSection('recent')">
              <el-icon class="section-icon"><Clock /></el-icon>
              <span class="section-title" v-if="!collapsed || hovered">最近访问</span>
              <el-icon 
                class="section-toggle" 
                v-if="!collapsed || hovered"
                :class="{ rotated: expandedSections.includes('recent') }"
              >
                <ArrowRight />
              </el-icon>
            </div>
            <transition name="section-expand">
              <div v-show="expandedSections.includes('recent')" class="section-content">
                <div 
                  v-for="item in recentItems.slice(0, 5)"
                  :key="`recent-${item.path}`"
                  class="nav-item recent-item"
                  @click="navigateToItem(item)"
                >
                  <div class="item-icon">
                    <el-icon><component :is="item.icon" /></el-icon>
                  </div>
                  <div class="item-content" v-if="!collapsed || hovered">
                    <span class="item-text">{{ item.title }}</span>
                    <small class="visit-time">{{ formatTimeAgo(item.lastVisit) }}</small>
                  </div>
                </div>
              </div>
            </transition>
          </div>
          
          <!-- 主导航菜单 -->
          <div 
            v-for="section in filteredMenuSections"
            :key="section.key"
            class="nav-section main-section"
          >
            <div class="section-header" @click="toggleSection(section.key)">
              <el-icon class="section-icon" :style="{ color: section.color }">
                <component :is="section.icon" />
              </el-icon>
              <span class="section-title" v-if="!collapsed || hovered">{{ section.title }}</span>
              <div class="section-badges" v-if="!collapsed || hovered">
                <el-badge 
                  v-if="getSectionBadgeCount(section)"
                  :value="getSectionBadgeCount(section)"
                  :type="getSectionBadgeType(section)"
                  class="section-badge"
                />
              </div>
              <el-icon 
                class="section-toggle" 
                v-if="!collapsed || hovered"
                :class="{ rotated: expandedSections.includes(section.key) }"
              >
                <ArrowRight />
              </el-icon>
            </div>
            
            <transition name="section-expand">
              <div 
                v-show="expandedSections.includes(section.key)" 
                class="section-content"
              >
                <div 
                  v-for="item in section.items"
                  :key="item.key"
                  class="nav-item"
                  :class="getItemClasses(item)"
                  @click="handleItemClick(item)"
                  @contextmenu.prevent="showItemContextMenu($event, item)"
                >
                  <div class="item-icon">
                    <el-icon><component :is="item.icon" /></el-icon>
                  </div>
                  <div class="item-content" v-if="!collapsed || hovered">
                    <span class="item-text">{{ item.title }}</span>
                    <div class="item-meta">
                      <el-badge 
                        v-if="getItemBadge(item)"
                        :value="getItemBadge(item)"
                        :type="getItemBadgeType(item)"
                        class="item-badge"
                      />
                      <el-tag 
                        v-if="item.isNew"
                        type="warning"
                        size="small"
                        class="new-tag"
                      >
                        NEW
                      </el-tag>
                      <el-tag 
                        v-if="item.isHot"
                        type="danger"
                        size="small"
                        class="hot-tag"
                      >
                        HOT
                      </el-tag>
                    </div>
                  </div>
                  <div class="item-actions" v-if="!collapsed || hovered">
                    <el-button 
                      type="text" 
                      size="small"
                      @click.stop="toggleFavorite(item)"
                      class="favorite-btn"
                      :class="{ active: isFavorite(item) }"
                    >
                      <el-icon>
                        <component :is="isFavorite(item) ? 'StarFilled' : 'Star'" />
                      </el-icon>
                    </el-button>
                  </div>
                  
                  <!-- 子菜单 -->
                  <transition name="submenu-expand">
                    <div 
                      v-if="item.children && expandedItems.includes(item.key)"
                      class="submenu"
                    >
                      <div 
                        v-for="child in item.children"
                        :key="child.key"
                        class="submenu-item"
                        :class="{ active: isItemActive(child) }"
                        @click="navigateToItem(child)"
                      >
                        <div class="submenu-indicator"></div>
                        <el-icon class="submenu-icon">
                          <component :is="child.icon || 'Document'" />
                        </el-icon>
                        <span class="submenu-text">{{ child.title }}</span>
                      </div>
                    </div>
                  </transition>
                </div>
              </div>
            </transition>
          </div>
        </nav>
      </el-scrollbar>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 快速工具 -->
      <div class="quick-tools" v-if="!collapsed || hovered">
        <transition name="tools-fade">
          <div class="tools-grid">
            <el-tooltip
              v-for="tool in quickTools"
              :key="tool.key"
              :content="tool.tooltip"
              placement="top"
              :disabled="!collapsed && !hovered"
            >
              <div 
                class="tool-item"
                @click="executeTool(tool)"
                :class="{ active: tool.active }"
              >
                <el-icon><component :is="tool.icon" /></el-icon>
                <span class="tool-text" v-if="!collapsed || hovered">{{ tool.title }}</span>
                <el-badge 
                  v-if="tool.badge"
                  :value="tool.badge"
                  class="tool-badge"
                />
              </div>
            </el-tooltip>
          </div>
        </transition>
      </div>
      
      <!-- 折叠控制 -->
      <div class="collapse-control">
        <el-button 
          class="collapse-btn"
          @click="toggleCollapse"
          type="text"
          size="large"
        >
          <transition name="collapse-icon" mode="out-in">
            <el-icon :key="collapsed">
              <component :is="collapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </transition>
        </el-button>
      </div>
    </div>
    
    <!-- 右键菜单 -->
    <teleport to="body">
      <div 
        v-if="showContextMenu"
        class="context-menu"
        :style="contextMenuStyle"
        @click="hideContextMenu"
      >
        <div class="context-menu-item" @click="toggleFavorite(contextMenuItem)">
          <el-icon>
            <component :is="isFavorite(contextMenuItem) ? 'StarFilled' : 'Star'" />
          </el-icon>
          <span>{{ isFavorite(contextMenuItem) ? '取消收藏' : '加入收藏' }}</span>
        </div>
        <div class="context-menu-item" @click="copyItemLink(contextMenuItem)">
          <el-icon><Link /></el-icon>
          <span>复制链接</span>
        </div>
        <div class="context-menu-item" @click="openInNewTab(contextMenuItem)">
          <el-icon><Link /></el-icon>
          <span>新标签打开</span>
        </div>
      </div>
    </teleport>
  </aside>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Search,
  StarFilled,
  Star,
  Clock,
  Close,
  ArrowRight,
  ArrowDown,
  User,
  Setting,
  Lock,
  SwitchButton,
  Bell,
  Document,
  DataLine,
  Menu,
  Plus,
  Download,
  Upload,
  Refresh,
  FullScreen,
  Link,
  Expand,
  Fold
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 280
  },
  collapsedWidth: {
    type: Number,
    default: 64
  }
})

// Emits
const emit = defineEmits(['collapse-change', 'item-click'])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const hovered = ref(false)
const searchQuery = ref('')
const showUserProfile = ref(false)
const expandedSections = ref(['main', 'favorites', 'recent'])
const expandedItems = ref([])
const activeFilters = ref([])
const showContextMenu = ref(false)
const contextMenuItem = ref(null)
const contextMenuStyle = ref({})

// 应用配置
const appConfig = ref({
  appName: 'LinkHub Pro',
  version: '2.0.1',
  brandIcon: 'DataLine'
})

// 用户信息
const userInfo = computed(() => userStore.userInfo || {})

// 收藏项目
const favoriteItems = ref(JSON.parse(localStorage.getItem('sidebar-favorites') || '[]'))

// 最近访问项目
const recentItems = ref(JSON.parse(localStorage.getItem('sidebar-recent') || '[]'))

// 用户快速操作
const userQuickActions = computed(() => [
  { key: 'profile', label: '个人资料', icon: 'User', type: 'text' },
  { key: 'settings', label: '账户设置', icon: 'Setting', type: 'text' },
  { key: 'logout', label: '退出登录', icon: 'SwitchButton', type: 'danger' }
])

// 快速工具
const quickTools = computed(() => [
  {
    key: 'notifications',
    title: '通知',
    icon: 'Bell',
    tooltip: '通知中心',
    badge: 3,
    active: false
  },
  {
    key: 'export',
    title: '导出',
    icon: 'Download',
    tooltip: '数据导出',
    active: false
  },
  {
    key: 'import',
    title: '导入',
    icon: 'Upload',
    tooltip: '数据导入',
    active: false
  },
  {
    key: 'refresh',
    title: '刷新',
    icon: 'Refresh',
    tooltip: '刷新页面',
    active: false
  }
])

// 主导航菜单结构
const menuSections = computed(() => [
  {
    key: 'dashboard',
    title: '仪表板',
    icon: 'DataLine',
    color: '#3b82f6',
    items: [
      {
        key: 'overview',
        title: '概览',
        icon: 'DataLine',
        path: '/admin/dashboard',
        badge: null
      },
      {
        key: 'analytics',
        title: '数据分析',
        icon: 'DataLine',
        path: '/admin/analytics',
        isNew: true
      },
      {
        key: 'data-screen',
        title: '数据大屏',
        icon: 'Monitor',
        path: '/data-screen',
        badge: null,
        isHot: true
      }
    ]
  },
  {
    key: 'community',
    title: '社群管理',
    icon: 'Menu',
    color: '#10b981',
    items: [
      {
        key: 'groups',
        title: '群组列表',
        icon: 'Menu',
        path: '/admin/groups',
        badge: 5,
        badgeType: 'warning'
      },
      {
        key: 'templates',
        title: '模板管理',
        icon: 'Document',
        path: '/admin/templates',
        isHot: true
      }
    ]
  },
  {
    key: 'users',
    title: '用户管理',
    icon: 'User',
    color: '#f59e0b',
    items: [
      {
        key: 'user-list',
        title: '用户列表',
        icon: 'User',
        path: '/admin/users',
        badge: 12
      },
      {
        key: 'user-analytics',
        title: '用户分析',
        icon: 'DataLine',
        path: '/admin/user-analytics'
      }
    ]
  },
  {
    key: 'system',
    title: '系统设置',
    icon: 'Setting',
    color: '#8b5cf6',
    items: [
      {
        key: 'settings',
        title: '基础设置',
        icon: 'Setting',
        path: '/admin/settings'
      },
      {
        key: 'monitor',
        title: '系统监控',
        icon: 'DataLine',
        path: '/admin/system-monitor',
        badge: 1,
        badgeType: 'danger'
      }
    ]
  }
])

// 过滤后的菜单
const filteredMenuSections = computed(() => {
  if (!searchQuery.value) return menuSections.value
  
  return menuSections.value.map(section => ({
    ...section,
    items: section.items.filter(item =>
      item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })).filter(section => section.items.length > 0)
})

// 侧边栏样式类
const sidebarClasses = computed(() => ({
  collapsed: props.collapsed,
  hovered: hovered.value,
  'with-search': searchQuery.value.length > 0
}))

// 方法
const handleMouseEnter = () => {
  if (props.collapsed) {
    hovered.value = true
  }
}

const handleMouseLeave = () => {
  hovered.value = false
}

const navigateHome = () => {
  router.push('/dashboard')
}

const toggleCollapse = () => {
  emit('collapse-change', !props.collapsed)
}

const toggleSection = (sectionKey) => {
  const index = expandedSections.value.indexOf(sectionKey)
  if (index > -1) {
    expandedSections.value.splice(index, 1)
  } else {
    expandedSections.value.push(sectionKey)
  }
}

const handleSearch = (query) => {
  // 实现搜索逻辑
  console.log('搜索:', query)
}

const removeFilter = (filter) => {
  activeFilters.value = activeFilters.value.filter(f => f !== filter)
}

const handleUserAction = async (action) => {
  switch (action.key) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'settings':
      router.push('/user/settings')
      break
    case 'logout':
      try {
        await userStore.logout()
        router.push('/login')
        ElMessage.success('已安全退出')
      } catch (error) {
        console.error('退出失败:', error)
      }
      break
  }
  showUserProfile.value = false
}

const handleItemClick = (item) => {
  if (item.children && item.children.length > 0) {
    toggleItemExpand(item.key)
  } else {
    navigateToItem(item)
  }
  emit('item-click', item)
}

const toggleItemExpand = (itemKey) => {
  const index = expandedItems.value.indexOf(itemKey)
  if (index > -1) {
    expandedItems.value.splice(index, 1)
  } else {
    expandedItems.value.push(itemKey)
  }
}

const navigateToItem = (item) => {
  if (item.path) {
    console.log('🔗 导航到:', item.path, '项目:', item.title)
    try {
      router.push(item.path)
      addToRecent(item)
      console.log('✅ 导航成功')
    } catch (error) {
      console.error('❌ 导航失败:', error)
      ElMessage.error(`导航失败: ${error.message}`)
    }
  } else {
    console.warn('⚠️ 菜单项缺少路径:', item)
  }
}

const addToRecent = (item) => {
  const timestamp = Date.now()
  const existingIndex = recentItems.value.findIndex(recent => recent.path === item.path)
  
  if (existingIndex >= 0) {
    recentItems.value.splice(existingIndex, 1)
  }
  
  recentItems.value.unshift({
    ...item,
    lastVisit: timestamp
  })
  
  recentItems.value = recentItems.value.slice(0, 10)
  localStorage.setItem('sidebar-recent', JSON.stringify(recentItems.value))
}

const toggleFavorite = (item) => {
  const index = favoriteItems.value.findIndex(fav => fav.key === item.key)
  
  if (index >= 0) {
    favoriteItems.value.splice(index, 1)
    ElMessage.success('已取消收藏')
  } else {
    favoriteItems.value.push({
      key: item.key,
      title: item.title,
      icon: item.icon,
      path: item.path,
      timestamp: Date.now()
    })
    ElMessage.success('已加入收藏')
  }
  
  localStorage.setItem('sidebar-favorites', JSON.stringify(favoriteItems.value))
}

const removeFavorite = (item) => {
  toggleFavorite(item)
}

const isFavorite = (item) => {
  return favoriteItems.value.some(fav => fav.key === item.key)
}

const isItemActive = (item) => {
  return route.path === item.path
}

const getItemClasses = (item) => ({
  active: isItemActive(item),
  'has-children': item.children && item.children.length > 0,
  'is-new': item.isNew,
  'is-hot': item.isHot,
  favorite: isFavorite(item)
})

const getItemBadge = (item) => {
  return item.badge
}

const getItemBadgeType = (item) => {
  return item.badgeType || 'primary'
}

const getSectionBadgeCount = (section) => {
  return section.items.reduce((count, item) => count + (item.badge || 0), 0)
}

const getSectionBadgeType = (section) => {
  const hasError = section.items.some(item => item.badgeType === 'danger')
  const hasWarning = section.items.some(item => item.badgeType === 'warning')
  
  if (hasError) return 'danger'
  if (hasWarning) return 'warning'
  return 'primary'
}

const executeTool = (tool) => {
  switch (tool.key) {
    case 'notifications':
      router.push('/system/notifications')
      break
    case 'export':
      router.push('/system/export')
      break
    case 'import':
      router.push('/system/import')
      break
    case 'refresh':
      location.reload()
      break
  }
}

const showItemContextMenu = (event, item) => {
  contextMenuItem.value = item
  contextMenuStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`
  }
  showContextMenu.value = true
}

const hideContextMenu = () => {
  showContextMenu.value = false
  contextMenuItem.value = null
}

const copyItemLink = (item) => {
  if (item.path) {
    const url = `${window.location.origin}${item.path}`
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('链接已复制')
    })
  }
  hideContextMenu()
}

const openInNewTab = (item) => {
  if (item.path) {
    window.open(item.path, '_blank')
  }
  hideContextMenu()
}

const getRoleText = (role) => {
  const roles = {
    admin: '系统管理员',
    manager: '管理员',
    user: '普通用户',
    distributor: '分销商'
  }
  return roles[role] || '用户'
}

const formatTimeAgo = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 监听点击外部隐藏右键菜单
const handleClickOutside = (event) => {
  if (showContextMenu.value && !event.target.closest('.context-menu')) {
    hideContextMenu()
  }
}

// 监听路由变化
watch(() => route.path, () => {
  // 自动展开包含当前路由的菜单项
  const currentSection = menuSections.value.find(section =>
    section.items.some(item => item.path === route.path)
  )
  
  if (currentSection && !expandedSections.value.includes(currentSection.key)) {
    expandedSections.value.push(currentSection.key)
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 初始化展开包含当前路由的菜单
  const currentSection = menuSections.value.find(section =>
    section.items.some(item => item.path === route.path)
  )
  
  if (currentSection && !expandedSections.value.includes(currentSection.key)) {
    expandedSections.value.push(currentSection.key)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.navigation-sidebar {
  height: 100vh;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  &.enhanced-sidebar {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      background: linear-gradient(180deg, transparent, var(--color-primary), transparent);
      opacity: 0.3;
    }
  }
  
  &.collapsed {
    width: 64px;
    
    &.hovered {
      width: 280px;
      z-index: var(--z-dropdown);
      
      .sidebar-content,
      .sidebar-search,
      .user-summary {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }
}

// 侧边栏头部
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-muted) 100%);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-md);
    right: var(--spacing-md);
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
    opacity: 0.5;
  }
}

.header-logo {
  cursor: pointer;
  margin-bottom: var(--spacing-md);
  
  .logo-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: rgba(59, 130, 246, 0.05);
      transform: translateY(-1px);
    }
  }
  
  .logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: var(--shadow-md);
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      transform: rotate(5deg) scale(1.05);
    }
  }
  
  .logo-text {
    .app-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0 0 2px 0;
      background: var(--gradient-primary);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }
    
    .app-version {
      font-size: var(--text-xs);
      color: var(--text-muted);
      background: var(--bg-muted);
      padding: 1px 6px;
      border-radius: var(--radius-sm);
      font-family: var(--font-family-mono, monospace);
    }
  }
}

.user-summary {
  .user-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--bg-secondary);
      border-color: var(--color-primary);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    .user-avatar {
      box-shadow: var(--shadow-sm);
    }
    
    .user-details {
      flex: 1;
      min-width: 0;
      
      .user-name {
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        line-height: 1.2;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .user-role {
        font-size: var(--text-xs);
        color: var(--text-muted);
        line-height: 1.2;
        margin-bottom: 2px;
      }
      
      .user-status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: var(--text-xs);
        color: var(--text-light);
        
        .status-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          
          &.online {
            background: var(--success-500);
            animation: pulse 2s infinite;
          }
        }
      }
    }
    
    .expand-icon {
      color: var(--text-light);
      transition: transform var(--duration-normal) var(--ease-out);
      
      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
  
  .user-actions {
    margin-top: var(--spacing-sm);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    
    .user-action-btn {
      justify-content: flex-start;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      
      .el-icon {
        margin-right: var(--spacing-xs);
      }
    }
  }
}

// 搜索区域
.sidebar-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  
  .search-container {
    .nav-search-input {
      .el-input__wrapper {
        background: var(--bg-secondary);
        border: 2px solid var(--border-light);
        border-radius: var(--radius-lg);
        
        &:hover {
          border-color: var(--border-medium);
        }
        
        &.is-focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }
    
    .search-filters {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
      margin-top: var(--spacing-sm);
    }
  }
}

// 导航内容
.sidebar-content {
  flex: 1;
  overflow: hidden;
  
  .nav-scrollbar {
    :deep(.el-scrollbar__bar) {
      &.is-vertical {
        right: 2px;
        width: 4px;
        
        .el-scrollbar__thumb {
          background: var(--border-medium);
          border-radius: 2px;
          
          &:hover {
            background: var(--border-dark);
          }
        }
      }
    }
  }
}

.navigation-menu {
  padding: var(--spacing-sm) 0;
  
  .nav-section {
    margin-bottom: var(--spacing-md);
    
    .section-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-md);
      cursor: pointer;
      transition: all var(--duration-normal) var(--ease-out);
      border-radius: var(--radius-sm);
      margin: 0 var(--spacing-sm);
      
      &:hover {
        background: var(--bg-secondary);
        transform: translateX(2px);
      }
      
      .section-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
      }
      
      .section-title {
        flex: 1;
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
      }
      
      .section-badges {
        display: flex;
        gap: var(--spacing-xs);
        
        .section-badge {
          :deep(.el-badge__content) {
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
          }
        }
      }
      
      .section-toggle {
        color: var(--text-light);
        font-size: 14px;
        transition: transform var(--duration-normal) var(--ease-out);
        
        &.rotated {
          transform: rotate(90deg);
        }
      }
    }
    
    .section-content {
      padding-left: var(--spacing-lg);
      
      .nav-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        margin: 2px var(--spacing-sm);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--duration-normal) var(--ease-out);
        position: relative;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
          transition: left var(--duration-slow) var(--ease-out);
        }
        
        &:hover {
          background: var(--bg-secondary);
          transform: translateX(4px);
          
          &::before {
            left: 100%;
          }
          
          .item-actions {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        &.active {
          background: var(--gradient-primary);
          color: white;
          box-shadow: var(--shadow-md);
          
          .item-icon,
          .item-text {
            color: white;
          }
          
          &::after {
            content: '';
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: white;
            border-radius: 2px;
          }
        }
        
        &.favorite {
          border-left: 3px solid var(--warning-500);
          background: rgba(245, 158, 11, 0.05);
        }
        
        &.is-new::after {
          content: 'NEW';
          position: absolute;
          top: -2px;
          right: 8px;
          background: var(--warning-500);
          color: white;
          font-size: 9px;
          padding: 1px 4px;
          border-radius: 6px;
          font-weight: bold;
          animation: bounce 2s infinite;
        }
        
        &.is-hot::after {
          content: 'HOT';
          position: absolute;
          top: -2px;
          right: 8px;
          background: var(--danger-500);
          color: white;
          font-size: 9px;
          padding: 1px 4px;
          border-radius: 6px;
          font-weight: bold;
          animation: pulse 2s infinite;
        }
        
        .item-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          color: var(--text-muted);
          transition: all var(--duration-normal) var(--ease-out);
        }
        
        .item-content {
          flex: 1;
          min-width: 0;
          
          .item-text {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .item-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-top: 2px;
            
            .item-badge {
              :deep(.el-badge__content) {
                font-size: 10px;
                min-width: 16px;
                height: 16px;
                line-height: 16px;
              }
            }
            
            .new-tag,
            .hot-tag {
              font-size: 9px;
              padding: 1px 4px;
              font-weight: bold;
            }
          }
        }
        
        .item-actions {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          opacity: 0;
          transform: translateX(10px);
          transition: all var(--duration-normal) var(--ease-out);
          
          .favorite-btn {
            width: 20px;
            height: 20px;
            padding: 0;
            border-radius: 50%;
            
            &.active .el-icon {
              color: var(--warning-500);
            }
            
            .el-icon {
              font-size: 12px;
              color: var(--text-light);
              transition: all var(--duration-normal) var(--ease-out);
            }
            
            &:hover .el-icon {
              color: var(--warning-500);
              transform: scale(1.2);
            }
          }
        }
        
        // 子菜单
        .submenu {
          position: absolute;
          left: 0;
          right: 0;
          top: 100%;
          background: var(--bg-muted);
          border-radius: var(--radius-sm);
          overflow: hidden;
          z-index: 10;
          
          .submenu-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--text-xs);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--duration-normal) var(--ease-out);
            
            &:hover {
              background: var(--bg-secondary);
              color: var(--color-primary);
              transform: translateX(4px);
            }
            
            &.active {
              background: var(--color-primary);
              color: white;
            }
            
            .submenu-indicator {
              width: 6px;
              height: 6px;
              background: var(--border-medium);
              border-radius: 50%;
            }
            
            .submenu-icon {
              font-size: 12px;
            }
            
            .submenu-text {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
      
      // 收藏和最近访问特殊样式
      .favorite-item {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.05) 100%);
        border: 1px solid rgba(245, 158, 11, 0.2);
        
        .remove-favorite {
          opacity: 0;
          transition: opacity var(--duration-normal) var(--ease-out);
        }
        
        &:hover .remove-favorite {
          opacity: 1;
        }
      }
      
      .recent-item {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
        border-left: 3px solid var(--success-500);
        
        .visit-time {
          color: var(--text-light);
          font-size: 10px;
          margin-left: auto;
        }
      }
    }
  }
}

// 侧边栏底部
.sidebar-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  
  .quick-tools {
    margin-bottom: var(--spacing-md);
    
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-xs);
      
      .tool-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: var(--spacing-xs);
        background: white;
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        cursor: pointer;
        transition: all var(--duration-normal) var(--ease-out);
        position: relative;
        
        &:hover {
          background: var(--color-primary);
          border-color: var(--color-primary);
          color: white;
          transform: translateY(-2px);
          box-shadow: var(--shadow-sm);
        }
        
        &.active {
          background: var(--color-primary);
          border-color: var(--color-primary);
          color: white;
        }
        
        .el-icon {
          font-size: 16px;
          margin-bottom: 2px;
        }
        
        .tool-text {
          font-size: 10px;
          text-align: center;
          line-height: 1.2;
        }
        
        .tool-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          
          :deep(.el-badge__content) {
            font-size: 9px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
          }
        }
      }
    }
  }
  
  .collapse-control {
    display: flex;
    justify-content: center;
    
    .collapse-btn {
      width: 40px;
      height: 40px;
      background: var(--bg-primary);
      border: 2px solid var(--border-light);
      border-radius: var(--radius-lg);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--color-primary);
        border-color: var(--color-primary);
        color: white;
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
      }
    }
  }
}

// 右键菜单
.context-menu {
  position: fixed;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xs) 0;
  min-width: 140px;
  z-index: var(--z-modal);
  
  .context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--bg-secondary);
    }
    
    .el-icon {
      font-size: 14px;
      color: var(--text-muted);
    }
  }
}

// 动画
.logo-scale-enter-active,
.logo-scale-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.logo-scale-enter-from,
.logo-scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.user-fade-enter-active,
.user-fade-leave-active,
.search-fade-enter-active,
.search-fade-leave-active,
.tools-fade-enter-active,
.tools-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.user-fade-enter-from,
.user-fade-leave-to,
.search-fade-enter-from,
.search-fade-leave-to,
.tools-fade-enter-from,
.tools-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.profile-expand-enter-active,
.profile-expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.profile-expand-enter-from,
.profile-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.profile-expand-enter-to,
.profile-expand-leave-from {
  max-height: 200px;
  opacity: 1;
}

.section-expand-enter-active,
.section-expand-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  overflow: hidden;
}

.section-expand-enter-from,
.section-expand-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.section-expand-enter-to,
.section-expand-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

.submenu-expand-enter-active,
.submenu-expand-leave-active {
  transition: all var(--duration-fast) var(--ease-out);
  overflow: hidden;
}

.submenu-expand-enter-from,
.submenu-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.submenu-expand-enter-to,
.submenu-expand-leave-from {
  max-height: 150px;
  opacity: 1;
}

.collapse-icon-enter-active,
.collapse-icon-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.collapse-icon-enter-from,
.collapse-icon-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
}

// 响应式
@media (max-width: 768px) {
  .navigation-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-modal);
    transform: translateX(-100%);
    
    &:not(.collapsed) {
      transform: translateX(0);
    }
  }
}

@media (max-width: 480px) {
  .sidebar-header {
    padding: var(--spacing-md);
  }
  
  .tools-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>