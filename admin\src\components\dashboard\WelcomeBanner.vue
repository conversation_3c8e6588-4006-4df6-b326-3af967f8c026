<template>
  <div class="welcome-banner">
    <div class="banner-content">
      <div class="welcome-text">
        <h1 class="welcome-title">
          欢迎回来，{{ userName }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，{{ getGreeting() }}
        </p>
      </div>
      <div class="banner-actions">
        <el-button type="primary" class="modern-btn primary" @click="showQuickActions = true">
          <el-icon><Plus /></el-icon>
          快速操作
        </el-button>
        <el-button class="modern-btn secondary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    <div class="banner-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  userName: {
    type: String,
    default: '管理员'
  },
  currentDate: {
    type: String,
    default: ''
  }
})

const showQuickActions = ref(false)

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息'
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
}
</script>

<style lang="scss" scoped>
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 32px;
  position: relative;
  overflow: hidden;
  color: white;
  min-height: 120px;

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  .welcome-text {
    .welcome-title {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .banner-actions {
    display: flex;
    gap: 16px;

    .modern-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;

      &.primary {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }
      }

      &.secondary {
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(-2px);
        }
      }
    }
  }

  .banner-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: -100px;
        right: -100px;
      }

      &.circle-2 {
        width: 120px;
        height: 120px;
        top: 50px;
        right: 200px;
        background: rgba(255, 255, 255, 0.05);
      }

      &.circle-3 {
        width: 80px;
        height: 80px;
        bottom: -40px;
        right: 100px;
        background: rgba(255, 255, 255, 0.08);
      }
    }
  }
}

@media (max-width: 768px) {
  .welcome-banner {
    .banner-content {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .welcome-text .welcome-title {
      font-size: 24px;
    }

    .banner-actions {
      justify-content: center;
    }
  }
}
</style>