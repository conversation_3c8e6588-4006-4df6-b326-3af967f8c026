<template>
  <div class="avatar-upload-container">
    <el-upload
      class="avatar-uploader"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :on-progress="handleProgress"
      accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
    >
      <div class="avatar-wrapper" :class="{ 'uploading': uploading }">
        <img v-if="avatarUrl && !uploading" :src="avatarUrl" class="avatar" alt="头像" />
        <div v-else-if="uploading" class="upload-progress">
          <el-progress 
            type="circle" 
            :percentage="uploadProgress" 
            :width="Math.min(size - 20, 80)"
            :stroke-width="4"
          />
          <div class="progress-text">上传中...</div>
        </div>
        <div v-else class="avatar-placeholder">
          <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          <div class="upload-text">点击上传头像</div>
        </div>
        
        <!-- 悬浮操作按钮 -->
        <div v-if="avatarUrl && !uploading" class="avatar-overlay">
          <div class="overlay-actions">
            <el-tooltip content="更换头像" placement="top">
              <el-button 
                type="primary" 
                size="small" 
                circle
                @click.stop="triggerUpload"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除头像" placement="top">
              <el-button 
                type="danger" 
                size="small" 
                circle
                @click.stop="removeAvatar"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-upload>

    <!-- 头像预览对话框 -->
    <el-dialog v-model="showPreview" title="头像预览" width="400px" center>
      <div class="avatar-preview">
        <img :src="avatarUrl" alt="头像预览" />
      </div>
      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="downloadAvatar">下载</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  size: {
    type: Number,
    default: 100
  },
  maxSize: {
    type: Number,
    default: 2 // MB
  },
  enablePreview: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'error'])

// 响应式数据
const uploading = ref(false)
const uploadProgress = ref(0)
const showPreview = ref(false)

// 计算属性
const avatarUrl = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadUrl = computed(() => '/api/upload/avatar')

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${getToken()}`
}))

// 方法
const beforeUpload = (file) => {
  // 检查文件类型
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('头像只能是 JPG、PNG、GIF 或 WebP 格式!')
    return false
  }

  // 检查文件大小
  const isValidSize = file.size / 1024 / 1024 < props.maxSize
  if (!isValidSize) {
    ElMessage.error(`头像大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  uploading.value = true
  uploadProgress.value = 0
  return true
}

const handleProgress = (event) => {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
}

const handleSuccess = (response) => {
  uploading.value = false
  uploadProgress.value = 0
  
  if (response.success) {
    avatarUrl.value = response.data.url
    ElMessage.success('头像上传成功!')
    emit('success', response.data)
  } else {
    ElMessage.error(response.message || '头像上传失败!')
    emit('error', response)
  }
}

const handleError = (error) => {
  uploading.value = false
  uploadProgress.value = 0
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败!')
  emit('error', error)
}

const triggerUpload = () => {
  const uploadEl = document.querySelector('.avatar-uploader input[type="file"]')
  if (uploadEl) {
    uploadEl.click()
  }
}

const removeAvatar = () => {
  ElMessageBox.confirm('确定要删除当前头像吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    avatarUrl.value = ''
    emit('update:modelValue', '')
    ElMessage.success('头像已删除')
  }).catch(() => {
    // 用户取消删除
  })
}

const downloadAvatar = () => {
  if (avatarUrl.value) {
    const link = document.createElement('a')
    link.href = avatarUrl.value
    link.download = 'avatar.jpg'
    link.click()
  }
}

const previewAvatar = () => {
  if (avatarUrl.value && props.enablePreview) {
    showPreview.value = true
  }
}
</script>

<style lang="scss" scoped>
.avatar-upload-container {
  display: inline-block;
}

.avatar-uploader {
  .avatar-wrapper {
    position: relative;
    width: v-bind("`${size}px`");
    height: v-bind("`${size}px`");
    border-radius: 50%;
    overflow: hidden;
    border: 2px dashed #d9d9d9;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      
      .avatar-overlay {
        opacity: 1;
      }
    }

    &.uploading {
      border-color: #409eff;
      cursor: not-allowed;
    }

    .avatar {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .avatar-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #8c939d;

      .avatar-uploader-icon {
        font-size: 28px;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 12px;
        text-align: center;
        line-height: 1.2;
      }
    }

    .upload-progress {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .progress-text {
        margin-top: 8px;
        font-size: 12px;
        color: #409eff;
      }
    }

    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .overlay-actions {
        display: flex;
        gap: 8px;

        .el-button {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

.avatar-preview {
  text-align: center;
  
  img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
  }
}

.cropper-container {
  height: 400px;
  
  :deep(.vue-cropper) {
    width: 100%;
    height: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .avatar-wrapper {
    .avatar-overlay {
      opacity: 1; // 移动端始终显示操作按钮
    }
  }
  
  .cropper-container {
    height: 300px;
  }
}
</style>