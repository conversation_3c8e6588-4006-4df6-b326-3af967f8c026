<template>
  <div class="promotion-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>推广链接管理</h1>
        <p>管理和监控所有推广链接的使用情况</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建推广链接
        </el-button>
        <el-button @click="handleBatchAction('cleanup_expired')">
          <el-icon><Delete /></el-icon>
          清理过期链接
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Link /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.overview.total_links }}</div>
                <div class="stats-label">总链接数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.overview.active_links }}</div>
                <div class="stats-label">活跃链接</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon clicks">
                <el-icon><View /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ formatNumber(stats.overview.total_clicks) }}</div>
                <div class="stats-label">总点击量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.overview.avg_clicks_per_link }}</div>
                <div class="stats-label">平均点击率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.search"
            placeholder="搜索链接标题、短码或群组名称"
            style="width: 300px"
            clearable
            @keyup.enter="loadData"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="群组">
          <el-select
            v-model="filters.group_id"
            placeholder="选择群组"
            style="width: 200px"
            clearable
          >
            <el-option
              v-for="group in groups"
              :key="group.id"
              :label="group.title"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            style="width: 150px"
            clearable
          >
            <el-option label="活跃" value="active" />
            <el-option label="已过期" value="expired" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建者">
          <el-select
            v-model="filters.created_by"
            placeholder="选择创建者"
            style="width: 150px"
            clearable
            filterable
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">推广链接列表</div>
        <div class="table-actions">
          <el-button
            type="danger"
            size="small"
            :disabled="selectedLinks.length === 0"
            @click="handleBatchAction('delete')"
          >
            批量删除
          </el-button>
          <el-button
            type="warning"
            size="small"
            :disabled="selectedLinks.length === 0"
            @click="handleBatchAction('disable')"
          >
            批量禁用
          </el-button>
          <el-button
            type="success"
            size="small"
            :disabled="selectedLinks.length === 0"
            @click="handleBatchAction('enable')"
          >
            批量启用
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="links"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="链接信息" min-width="300">
          <template #default="{ row }">
            <div class="link-info">
              <div class="link-title">{{ row.title || '未设置标题' }}</div>
              <div class="link-details">
                <el-tag size="small" type="info">{{ row.short_code }}</el-tag>
                <span class="link-url">{{ row.short_url }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyToClipboard(row.short_url)"
                >
                  复制
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联群组" width="200">
          <template #default="{ row }">
            <div class="group-info">
              <div class="group-name">{{ row.group?.title }}</div>
              <div class="group-category">{{ row.group?.category_name }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="统计数据" width="150">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="click-count">
                <el-icon><View /></el-icon>
                {{ row.click_count }}
              </div>
              <div class="click-rate">点击率: {{ row.click_rate }}%</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status_color)"
              size="small"
            >
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="剩余天数" width="100">
          <template #default="{ row }">
            <span :class="getRemainingDaysClass(row.remaining_days)">
              {{ row.remaining_days }}天
            </span>
          </template>
        </el-table-column>

        <el-table-column label="创建者" width="120">
          <template #default="{ row }">
            {{ row.creator?.name || '未知' }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            <div class="time-info">
              <div>{{ formatDate(row.created_at) }}</div>
              <div class="time-ago">{{ row.created_at_human }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                link
                size="small"
                @click="viewDetails(row)"
              >
                详情
              </el-button>
              <el-button
                type="success"
                link
                size="small"
                @click="generateQRCode(row)"
              >
                二维码
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button type="primary" link size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                    <el-dropdown-item command="regenerate">重新生成短码</el-dropdown-item>
                    <el-dropdown-item command="extend">延长有效期</el-dropdown-item>
                    <el-dropdown-item
                      command="toggle"
                      :divided="true"
                    >
                      {{ row.is_active ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger">
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadData"
          @current-change="loadData"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <PromotionDialog
      v-model="showCreateDialog"
      :link="currentLink"
      :groups="groups"
      @success="handleDialogSuccess"
    />

    <!-- 详情对话框 -->
    <PromotionDetailDialog
      v-model="showDetailDialog"
      :link="currentLink"
    />

    <!-- 二维码对话框 -->
    <QRCodeDialog
      v-model="showQRDialog"
      :link="currentLink"
    />

    <!-- 批量操作对话框 -->
    <BatchActionDialog
      v-model="showBatchDialog"
      :action="batchAction"
      :selected-links="selectedLinks"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Link, Check, View, TrendCharts, Search, Refresh, ArrowDown } from '@element-plus/icons-vue'
import { usePromotionStore } from '@/stores/promotion'
import { useGroupStore } from '@/stores/group'
import { useUserStore } from '@/stores/user'
import PromotionDialog from './components/PromotionDialog.vue'
import PromotionDetailDialog from './components/PromotionDetailDialog.vue'
import QRCodeDialog from './components/QRCodeDialog.vue'
import BatchActionDialog from './components/BatchActionDialog.vue'

// 状态管理
const promotionStore = usePromotionStore()
const groupStore = useGroupStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const links = ref([])
const groups = ref([])
const users = ref([])
const selectedLinks = ref([])
const stats = ref({
  overview: {
    total_links: 0,
    active_links: 0,
    total_clicks: 0,
    avg_clicks_per_link: 0
  }
})

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showQRDialog = ref(false)
const showBatchDialog = ref(false)
const currentLink = ref(null)
const batchAction = ref('')

// 筛选条件
const filters = reactive({
  search: '',
  group_id: '',
  status: '',
  created_by: '',
  sort_by: 'created_at',
  sort_order: 'desc'
})

// 分页信息
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0,
  last_page: 1
})

// 计算属性
const hasSelection = computed(() => selectedLinks.value.length > 0)

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.current_page,
      per_page: pagination.per_page
    }
    
    const response = await promotionStore.getPromotionLinks(params)
    links.value = response.links
    Object.assign(pagination, response.pagination)
  } catch (error) {
    ElMessage.error('加载数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await promotionStore.getPromotionStats()
    stats.value = response
  } catch (error) {
    console.error('加载统计数据失败：', error)
  }
}

const loadGroups = async () => {
  try {
    const response = await groupStore.getGroups({ per_page: 1000 })
    groups.value = response.groups || response.data || []
  } catch (error) {
    console.error('加载群组列表失败：', error)
  }
}

const loadUsers = async () => {
  try {
    const response = await userStore.getUsers({ per_page: 1000 })
    users.value = response.users || response.data || []
  } catch (error) {
    console.error('加载用户列表失败：', error)
  }
}

const resetFilters = () => {
  Object.assign(filters, {
    search: '',
    group_id: '',
    status: '',
    created_by: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  pagination.current_page = 1
  loadData()
}

const handleSelectionChange = (selection) => {
  selectedLinks.value = selection
}

const handleDialogSuccess = () => {
  showCreateDialog.value = false
  currentLink.value = null
  loadData()
  loadStats()
}

const viewDetails = (link) => {
  currentLink.value = link
  showDetailDialog.value = true
}

const generateQRCode = (link) => {
  currentLink.value = link
  showQRDialog.value = true
}

const handleAction = async (command, link) => {
  currentLink.value = link
  
  switch (command) {
    case 'edit':
      showCreateDialog.value = true
      break
    case 'duplicate':
      await duplicateLink(link)
      break
    case 'regenerate':
      await regenerateShortCode(link)
      break
    case 'extend':
      await extendExpiry(link)
      break
    case 'toggle':
      await toggleLinkStatus(link)
      break
    case 'delete':
      await deleteLink(link)
      break
  }
}

const duplicateLink = async (link) => {
  try {
    await promotionStore.duplicatePromotionLink(link.id)
    ElMessage.success('链接复制成功')
    loadData()
  } catch (error) {
    ElMessage.error('复制链接失败：' + error.message)
  }
}

const regenerateShortCode = async (link) => {
  try {
    await ElMessageBox.confirm('确定要重新生成短码吗？原短码将失效。', '确认操作')
    await promotionStore.regenerateShortCode(link.id)
    ElMessage.success('短码重新生成成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重新生成短码失败：' + error.message)
    }
  }
}

const extendExpiry = async (link) => {
  try {
    const { value: days } = await ElMessageBox.prompt('请输入要延长的天数', '延长有效期', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^\d+$/,
      inputErrorMessage: '请输入有效的天数'
    })
    
    await promotionStore.batchAction({
      action: 'extend_expiry',
      link_ids: [link.id],
      extend_days: parseInt(days)
    })
    
    ElMessage.success('有效期延长成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('延长有效期失败：' + error.message)
    }
  }
}

const toggleLinkStatus = async (link) => {
  try {
    const action = link.is_active ? 'disable' : 'enable'
    const actionText = link.is_active ? '禁用' : '启用'
    
    await ElMessageBox.confirm(`确定要${actionText}此链接吗？`, '确认操作')
    
    await promotionStore.batchAction({
      action,
      link_ids: [link.id]
    })
    
    ElMessage.success(`${actionText}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败：' + error.message)
    }
  }
}

const deleteLink = async (link) => {
  try {
    await ElMessageBox.confirm('确定要删除此推广链接吗？此操作不可恢复。', '确认删除', {
      type: 'warning'
    })
    
    await promotionStore.deletePromotionLink(link.id)
    ElMessage.success('删除成功')
    loadData()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

const handleBatchAction = (action) => {
  if (action === 'cleanup_expired') {
    cleanupExpiredLinks()
    return
  }
  
  if (selectedLinks.value.length === 0) {
    ElMessage.warning('请先选择要操作的链接')
    return
  }
  
  batchAction.value = action
  showBatchDialog.value = true
}

const cleanupExpiredLinks = async () => {
  try {
    await ElMessageBox.confirm('确定要清理所有过期链接吗？此操作不可恢复。', '确认清理', {
      type: 'warning'
    })
    
    const response = await promotionStore.cleanupExpiredLinks()
    ElMessage.success(`成功清理 ${response.deleted_count} 个过期链接`)
    loadData()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理失败：' + error.message)
    }
  }
}

const handleBatchSuccess = () => {
  showBatchDialog.value = false
  selectedLinks.value = []
  loadData()
  loadStats()
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 工具方法
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (color) => {
  const typeMap = {
    success: 'success',
    warning: 'warning',
    danger: 'danger'
  }
  return typeMap[color] || 'info'
}

const getRemainingDaysClass = (days) => {
  if (days <= 0) return 'expired'
  if (days <= 7) return 'warning'
  return 'normal'
}

// 生命周期
onMounted(() => {
  loadData()
  loadStats()
  loadGroups()
  loadUsers()
})
</script>

<style scoped>
.promotion-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.clicks {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.link-info {
  padding: 5px 0;
}

.link-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.link-details {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  color: #909399;
}

.link-url {
  font-family: monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.group-info {
  padding: 5px 0;
}

.group-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 3px;
}

.group-category {
  font-size: 12px;
  color: #909399;
}

.stats-info {
  text-align: center;
}

.click-count {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.click-rate {
  font-size: 12px;
  color: #909399;
}

.time-info {
  text-align: center;
}

.time-ago {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.expired {
  color: #f56c6c;
  font-weight: 600;
}

.warning {
  color: #e6a23c;
  font-weight: 600;
}

.normal {
  color: #67c23a;
}

:deep(.danger) {
  color: #f56c6c;
}
</style>