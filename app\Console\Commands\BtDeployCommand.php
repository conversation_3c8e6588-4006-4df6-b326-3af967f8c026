<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class BtDeployCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bt:deploy {--force : 强制重新部署}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '宝塔面板专用一键部署命令';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== 晨鑫流量变现系统 宝塔部署 ===');
        
        try {
            // 1. 检查数据库连接
            $this->checkDatabaseConnection();
            
            // 2. 清理现有数据
            $this->cleanupExistingData();
            
            // 3. 运行迁移
            $this->runMigrations();
            
            // 4. 优化数据库
            $this->optimizeDatabase();
            
            $this->info('✅ 部署完成！');
            $this->info('访问地址：' . config('app.url'));
            
        } catch (\Exception $e) {
            $this->error('❌ 部署失败：' . $e->getMessage());
            $this->error('尝试运行：php artisan bt:deploy --force');
            return 1;
        }
        
        return 0;
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabaseConnection()
    {
        $this->info('检查数据库连接...');
        
        try {
            DB::connection()->getPdo();
            $this->info('✅ 数据库连接正常');
        } catch (\Exception $e) {
            throw new \Exception('数据库连接失败：' . $e->getMessage());
        }
    }

    /**
     * 清理现有数据
     */
    private function cleanupExistingData()
    {
        $this->info('清理现有数据库...');
        
        // 禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS = 0');
        
        // 删除所有表
        $tables = [
            'notification_recipients', 'notification_templates', 'notifications',
            'operation_logs', 'export_tasks', 'export_templates', 'export_statistics',
            'user_value_stats', 'user_retention_stats', 'user_behavior_stats',
            'user_segments', 'user_segment_members', 'feature_usage_stats',
            'link_access_logs', 'short_links', 'domain_check_logs', 'domain_pools',
            'landing_pages', 'anti_block_rules', 'payment_usage_logs',
            'payment_permissions', 'payment_configs', 'payment_channels',
            'payment_callback_logs', 'distributor_level_records', 'group_templates',
            'withdraw_records', 'balance_logs', 'commission_logs', 'orders',
            'wechat_groups', 'distribution_groups', 'substations', 'system_settings',
            'users', 'personal_access_tokens', 'cache', 'cache_locks', 'sessions',
            'jobs', 'job_batches', 'failed_jobs', 'migrations'
        ];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                Schema::drop($table);
                $this->line("删除表：{$table}");
            }
        }
        
        // 重新启用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS = 1');
        
        $this->info('✅ 数据库清理完成');
    }

    /**
     * 运行迁移
     */
    private function runMigrations()
    {
        $this->info('运行数据库迁移...');
        
        // 确保migrations表存在
        if (!Schema::hasTable('migrations')) {
            DB::statement('CREATE TABLE migrations (id INT AUTO_INCREMENT PRIMARY KEY, migration VARCHAR(255), batch INT)');
        }
        
        // 运行所有迁移
        $this->call('migrate', ['--force' => true]);
        
        $this->info('✅ 迁移完成');
    }

    /**
     * 优化数据库
     */
    private function optimizeDatabase()
    {
        $this->info('优化数据库...');
        
        try {
            // 优化表
            $tables = ['users', 'orders', 'commission_logs', 'wechat_groups'];
            foreach ($tables as $table) {
                if (Schema::hasTable($table)) {
                    DB::statement("OPTIMIZE TABLE {$table}");
                }
            }
            
            // 更新统计信息
            DB::statement('ANALYZE TABLE users');
            
            $this->info('✅ 数据库优化完成');
            
        } catch (\Exception $e) {
            $this->warn('⚠️  优化跳过：' . $e->getMessage());
        }
    }
}