<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 操作日志中间件 - 简化版本
 * 只记录基本信息，避免复杂操作导致的错误
 */
class LogOperationMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);

        try {
            // 执行请求
            $response = $next($request);
            
            // 计算执行时间
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // 简单的文件日志记录，避免数据库操作
            Log::info('Request processed', [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent', ''),
                'status' => $response->getStatusCode(),
                'execution_time' => $executionTime,
                'timestamp' => now()->toDateTimeString(),
            ]);
            
            return $response;
            
        } catch (\Exception $e) {
            // 记录错误到文件日志
            Log::error('Request failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
            ]);
            
            // 重新抛出异常
            throw $e;
        }
    }
} 