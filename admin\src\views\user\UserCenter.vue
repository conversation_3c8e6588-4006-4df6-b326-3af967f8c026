<template>
  <div class="user-center">
    <div class="page-header">
      <div class="user-info">
        <el-avatar :size="80" :src="userInfo.avatar" />
        <div class="user-details">
          <h2>{{ userInfo.name || userInfo.username }}</h2>
          <p>{{ userInfo.email }}</p>
          <div class="user-tags">
            <el-tag type="primary">{{ getRoleText(userInfo.role) }}</el-tag>
            <el-tag v-if="userInfo.vip_level" type="warning">VIP{{ userInfo.vip_level }}</el-tag>
            <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
              {{ userInfo.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showEditProfile = true">
          <el-icon><Edit /></el-icon>
          编辑资料
        </el-button>
      </div>
    </div>

    <!-- 用户统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="我的订单"
          :value="userStats.total_orders || 0"
          icon="Tickets"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="消费金额"
          :value="userStats.total_spent || 0"
          icon="Money"
          color="#67C23A"
          prefix="¥"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="积分余额"
          :value="userStats.points || 0"
          icon="Star"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="优惠券"
          :value="userStats.coupons || 0"
          icon="Discount"
          color="#F56C6C"
          suffix="张"
        />
      </el-col>
    </el-row>

    <!-- 快捷功能 -->
    <el-card class="quick-functions">
      <template #header>
        <span>快捷功能</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="function-item" @click="goToOrders">
            <el-icon class="function-icon"><Tickets /></el-icon>
            <span>我的订单</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="function-item" @click="goToPoints">
            <el-icon class="function-icon"><Star /></el-icon>
            <span>积分管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="function-item" @click="goToCoupons">
            <el-icon class="function-icon"><Discount /></el-icon>
            <span>优惠券</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="function-item" @click="goToSecurity">
            <el-icon class="function-icon"><Lock /></el-icon>
            <span>安全设置</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="function-item" @click="goToFeedback">
            <el-icon class="function-icon"><ChatDotRound /></el-icon>
            <span>意见反馈</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="function-item" @click="goToHelp">
            <el-icon class="function-icon"><QuestionFilled /></el-icon>
            <span>帮助中心</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 最近订单 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近订单</span>
              <el-button size="small" @click="goToOrders">查看全部</el-button>
            </div>
          </template>
          <div class="recent-orders">
            <div v-if="recentOrders.length === 0" class="empty-state">
              <el-empty description="暂无订单" />
            </div>
            <div v-else>
              <div v-for="order in recentOrders" :key="order.id" class="order-item">
                <div class="order-info">
                  <div class="order-title">{{ order.wechat_group?.title || '订单' }}</div>
                  <div class="order-meta">
                    <span class="order-no">订单号: {{ order.order_no }}</span>
                    <span class="order-time">{{ formatDate(order.created_at) }}</span>
                  </div>
                </div>
                <div class="order-amount">¥{{ order.amount }}</div>
                <div class="order-status">
                  <el-tag :type="getOrderStatusType(order.status)">
                    {{ getOrderStatusText(order.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 积分记录 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>积分记录</span>
              <el-button size="small" @click="goToPoints">查看全部</el-button>
            </div>
          </template>
          <div class="points-history">
            <div v-if="pointsHistory.length === 0" class="empty-state">
              <el-empty description="暂无积分记录" />
            </div>
            <div v-else>
              <div v-for="record in pointsHistory" :key="record.id" class="points-item">
                <div class="points-info">
                  <div class="points-title">{{ record.description }}</div>
                  <div class="points-time">{{ formatDate(record.created_at) }}</div>
                </div>
                <div class="points-change" :class="record.type === 'add' ? 'positive' : 'negative'">
                  {{ record.type === 'add' ? '+' : '-' }}{{ record.points }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 消费分析 -->
    <el-card class="consumption-analysis">
      <template #header>
        <div class="card-header">
          <span>消费分析</span>
          <el-radio-group v-model="analysisPeriod" size="small" @change="loadConsumptionAnalysis">
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="quarter">本季度</el-radio-button>
            <el-radio-button label="year">本年</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="16">
          <LineChart
            :data="consumptionChartData"
            :options="chartOptions"
            height="300px"
          />
        </el-col>
        <el-col :span="8">
          <div class="analysis-summary">
            <div class="summary-item">
              <span class="label">总消费:</span>
              <span class="value">¥{{ analysisData.total_amount || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="label">订单数:</span>
              <span class="value">{{ analysisData.total_orders || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="label">平均客单价:</span>
              <span class="value">¥{{ analysisData.avg_amount || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="label">最高消费:</span>
              <span class="value">¥{{ analysisData.max_amount || 0 }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 编辑资料对话框 -->
    <el-dialog v-model="showEditProfile" title="编辑个人资料" width="600px">
      <el-form :model="profileForm" :rules="profileRules" ref="profileFormRef" label-width="100px">
        <el-form-item label="头像">
          <AvatarUpload 
            v-model="profileForm.avatar"
            :size="100"
            :max-size="2"
            :enable-preview="true"
            @success="handleAvatarSuccess"
            @error="handleAvatarError"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="profileForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="个人简介">
          <el-input
            v-model="profileForm.bio"
            type="textarea"
            :rows="3"
            placeholder="请输入个人简介"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditProfile = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateProfile" :loading="updating">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Edit, Tickets, Money, Star, Discount, Lock, ChatDotRound, 
  QuestionFilled, Plus 
} from '@element-plus/icons-vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import AvatarUpload from '@/components/AvatarUpload.vue'
import { 
  getProfile, 
  getStats, 
  getRecentOrders, 
  getPointsHistory, 
  getConsumptionAnalysis, 
  updateProfile 
} from '@/api/user'

// 响应式数据
const showEditProfile = ref(false)
const updating = ref(false)
const analysisPeriod = ref('month')

const userInfo = ref({})
const userStats = ref({})
const recentOrders = ref([])
const pointsHistory = ref([])
const analysisData = ref({})

const profileForm = reactive({
  name: '',
  email: '',
  phone: '',
  avatar: '',
  bio: ''
})

const profileRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const profileFormRef = ref()

// 图表数据
const consumptionChartData = ref({
  labels: [],
  datasets: [{
    label: '消费金额',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

// 方法
const loadUserInfo = async () => {
  try {
    const response = await getProfile()
    userInfo.value = response.data
    
    // 填充编辑表单
    Object.assign(profileForm, {
      name: userInfo.value.name || '',
      email: userInfo.value.email || '',
      phone: userInfo.value.phone || '',
      avatar: userInfo.value.avatar || '',
      bio: userInfo.value.bio || ''
    })
  } catch (error) {
    ElMessage.error('加载用户信息失败')
  }
}

const loadUserStats = async () => {
  try {
    const response = await getStats()
    userStats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadRecentOrders = async () => {
  try {
    const response = await getRecentOrders({ limit: 5 })
    recentOrders.value = response.data
  } catch (error) {
    ElMessage.error('加载订单数据失败')
  }
}

const loadPointsHistory = async () => {
  try {
    const response = await getPointsHistory({ limit: 5 })
    pointsHistory.value = response.data
  } catch (error) {
    ElMessage.error('加载积分记录失败')
  }
}

const loadConsumptionAnalysis = async () => {
  try {
    const response = await getConsumptionAnalysis({ period: analysisPeriod.value })
    analysisData.value = response.data.summary
    
    // 更新图表数据
    consumptionChartData.value = {
      labels: response.data.chart.labels,
      datasets: [{
        label: '消费金额',
        data: response.data.chart.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      }]
    }
  } catch (error) {
    ElMessage.error('加载消费分析失败')
  }
}

const handleUpdateProfile = async () => {
  try {
    await profileFormRef.value.validate()
    updating.value = true
    
    await updateProfile(profileForm)
    ElMessage.success('资料更新成功')
    showEditProfile.value = false
    loadUserInfo()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('更新失败')
    }
  } finally {
    updating.value = false
  }
}

const handleAvatarSuccess = (data) => {
  // 头像上传成功后，更新用户信息
  userInfo.value.avatar = data.url
  ElMessage.success('头像更新成功!')
}

const handleAvatarError = (error) => {
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败，请重试')
}

// 导航方法
const goToOrders = () => {
  ElMessage.info('订单页面开发中...')
}

const goToPoints = () => {
  ElMessage.info('积分页面开发中...')
}

const goToCoupons = () => {
  ElMessage.info('优惠券页面开发中...')
}

const goToSecurity = () => {
  ElMessage.info('安全设置页面开发中...')
}

const goToFeedback = () => {
  ElMessage.info('意见反馈页面开发中...')
}

const goToHelp = () => {
  ElMessage.info('帮助中心页面开发中...')
}

// 工具方法
const getRoleText = (role) => {
  const roleMap = {
    'admin': '管理员',
    'substation': '分站管理员',
    'agent': '代理商',
    'distributor': '分销员',
    'group_owner': '群主',
    'user': '普通用户'
  }
  return roleMap[role] || '未知角色'
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getOrderStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'paid': 'success',
    'cancelled': 'danger',
    'refunded': 'info'
  }
  return typeMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const textMap = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return textMap[status] || '未知状态'
}

// 生命周期
onMounted(() => {
  loadUserInfo()
  loadUserStats()
  loadRecentOrders()
  loadPointsHistory()
  loadConsumptionAnalysis()
})
</script>

<style scoped>
.user-center {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 20px;
}

.user-details h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.user-details p {
  margin: 0 0 10px 0;
  color: #909399;
}

.user-tags {
  display: flex;
  gap: 8px;
}

.stats-row {
  margin-bottom: 20px;
}

.quick-functions {
  margin-bottom: 20px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e4e7ed;
}

.function-item:hover {
  background: #f5f7fa;
  border-color: #409EFF;
  transform: translateY(-2px);
}

.function-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 8px;
}

.content-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.order-item,
.points-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child,
.points-item:last-child {
  border-bottom: none;
}

.order-info,
.points-info {
  flex: 1;
}

.order-title,
.points-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.order-meta,
.points-time {
  font-size: 12px;
  color: #909399;
}

.order-no {
  margin-right: 15px;
}

.order-amount {
  font-weight: bold;
  color: #67C23A;
  margin-right: 15px;
}

.points-change {
  font-weight: bold;
  margin-right: 15px;
}

.points-change.positive {
  color: #67C23A;
}

.points-change.negative {
  color: #F56C6C;
}

.consumption-analysis {
  margin-bottom: 20px;
}

.analysis-summary {
  padding: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  color: #909399;
}

.summary-item .value {
  font-weight: bold;
  color: #303133;
}

/* 头像上传样式已移至 AvatarUpload 组件中 */
</style>