import request from '@/utils/request'

// 支付相关API
export const paymentApi = {
  // 获取支付配置
  getPaymentConfig() {
    return request({
      url: '/admin/system/payment/config',
      method: 'get'
    })
  },

  // 更新支付配置
  updatePaymentConfig(data) {
    return request({
      url: '/admin/system/payment/config',
      method: 'put',
      data
    })
  },

  // 切换支付方式状态
  togglePaymentMethod(method, enabled) {
    return request({
      url: `/admin/system/payment/${method}/toggle`,
      method: 'post',
      data: { enabled }
    })
  },

  // 测试支付通道
  testPaymentChannel(method, testData) {
    return request({
      url: `/admin/system/payment/${method}/test`,
      method: 'post',
      data: testData
    })
  },

  // 获取支付统计
  getPaymentStats() {
    return request({
      url: '/admin/system/payment/stats',
      method: 'get'
    })
  },

  // 更新安全设置
  updateSecuritySettings(data) {
    return request({
      url: '/admin/system/payment/security',
      method: 'put',
      data
    })
  },

  // 获取订单列表
  getOrders(params) {
    return request({
      url: '/admin/payment/orders',
      method: 'get',
      params
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/admin/payment/orders/${orderId}`,
      method: 'get'
    })
  },

  // 退款
  refundOrder(orderId, data) {
    return request({
      url: `/admin/payment/orders/${orderId}/refund`,
      method: 'post',
      data
    })
  },

  // 获取退款记录
  getRefunds(params) {
    return request({
      url: '/admin/payment/refunds',
      method: 'get',
      params
    })
  },

  // 批量处理订单
  batchProcessOrders(orderIds, action) {
    return request({
      url: '/admin/payment/orders/batch',
      method: 'post',
      data: { orderIds, action }
    })
  },

  // 导出订单数据
  exportOrderData(params) {
    return request({
      url: '/admin/payment/orders/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },

  // 处理退款
  processRefund(refundId, data) {
    return request({
      url: `/admin/payment/refunds/${refundId}/process`,
      method: 'post',
      data
    })
  },

  // 导出退款数据
  exportRefundData(params) {
    return request({
      url: '/admin/payment/refunds/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },

  // 取消订单
  cancelOrder(orderId, reason) {
    return request({
      url: `/admin/payment/orders/${orderId}/cancel`,
      method: 'post',
      data: { reason }
    })
  },

  // 获取支付订单列表
  getPaymentOrders(params) {
    return request({
      url: '/admin/payment/orders',
      method: 'get',
      params
    })
  },

  // 导出支付数据
  exportPaymentData(params) {
    return request({
      url: '/admin/payment/orders/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}

// 支付渠道管理API
export const paymentChannelManagementApi = {
  // 获取支付渠道列表
  getChannels(params) {
    return request({
      url: '/admin/payment/channels',
      method: 'get',
      params
    })
  },

  // 获取支付渠道详情
  getChannelDetail(channelId) {
    return request({
      url: `/admin/payment/channels/${channelId}`,
      method: 'get'
    })
  },

  // 创建支付渠道
  createChannel(data) {
    return request({
      url: '/admin/payment/channels',
      method: 'post',
      data
    })
  },

  // 更新支付渠道
  updateChannel(channelId, data) {
    return request({
      url: `/admin/payment/channels/${channelId}`,
      method: 'put',
      data
    })
  },

  // 删除支付渠道
  deleteChannel(channelId) {
    return request({
      url: `/admin/payment/channels/${channelId}`,
      method: 'delete'
    })
  },

  // 启用/禁用支付渠道
  toggleChannel(channelId, enabled) {
    return request({
      url: `/admin/payment/channels/${channelId}/toggle`,
      method: 'post',
      data: { enabled }
    })
  },

  // 测试支付渠道
  testChannel(channelId, testData) {
    return request({
      url: `/admin/payment/channels/${channelId}/test`,
      method: 'post',
      data: testData
    })
  },

  // 获取渠道统计
  getChannelStats(channelId) {
    return request({
      url: `/admin/payment/channels/${channelId}/stats`,
      method: 'get'
    })
  },

  // 批量操作渠道
  batchOperateChannels(channelIds, operation) {
    return request({
      url: '/admin/payment/channels/batch',
      method: 'post',
      data: { channelIds, operation }
    })
  },

  // 导出渠道数据
  exportChannelData(params) {
    return request({
      url: '/admin/payment/channels/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}

// 为了兼容性，也导出单独的函数
export const paymentConfigApi = paymentApi
export const getPaymentConfig = paymentApi.getPaymentConfig
export const updatePaymentConfig = paymentApi.updatePaymentConfig
export const togglePaymentMethod = paymentApi.togglePaymentMethod
export const testPaymentChannel = paymentApi.testPaymentChannel
export const getPaymentStats = paymentApi.getPaymentStats
export const updateSecuritySettings = paymentApi.updateSecuritySettings
export const getOrders = paymentApi.getOrders
export const getOrderDetail = paymentApi.getOrderDetail
export const refundOrder = paymentApi.refundOrder
export const getRefunds = paymentApi.getRefunds
export const batchProcessOrders = paymentApi.batchProcessOrders
export const exportOrderData = paymentApi.exportOrderData
export const processRefund = paymentApi.processRefund
export const exportRefundData = paymentApi.exportRefundData
export const cancelOrder = paymentApi.cancelOrder
export const getPaymentOrders = paymentApi.getPaymentOrders
export const exportPaymentData = paymentApi.exportPaymentData

// 支付渠道管理API导出
export const getChannels = paymentChannelManagementApi.getChannels
export const getChannelDetail = paymentChannelManagementApi.getChannelDetail
export const createChannel = paymentChannelManagementApi.createChannel
export const updateChannel = paymentChannelManagementApi.updateChannel
export const deleteChannel = paymentChannelManagementApi.deleteChannel
export const toggleChannel = paymentChannelManagementApi.toggleChannel
export const testChannel = paymentChannelManagementApi.testChannel
export const getChannelStats = paymentChannelManagementApi.getChannelStats
export const batchOperateChannels = paymentChannelManagementApi.batchOperateChannels
export const exportChannelData = paymentChannelManagementApi.exportChannelData

export default paymentApi