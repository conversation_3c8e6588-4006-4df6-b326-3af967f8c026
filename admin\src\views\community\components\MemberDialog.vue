<template>
  <el-dialog
    v-model="dialogVisible"
    title="成员管理"
    width="900px"
    :destroy-on-close="true"
  >
    <GroupMemberManager 
      :group-id="groupId"
      :group-data="{}"
    />
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import GroupMemberManager from './GroupMemberManager.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
</script>