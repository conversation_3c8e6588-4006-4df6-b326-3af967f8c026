<template>
  <div class="group-quick-info">
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="info-section">
          <h4>基本信息</h4>
          <div class="info-item">
            <span class="label">群组ID:</span>
            <span class="value">{{ group.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(group.created_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后更新:</span>
            <span class="value">{{ formatDate(group.updated_at) }}</span>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-section">
          <h4>成员统计</h4>
          <div class="info-item">
            <span class="label">当前成员:</span>
            <span class="value">{{ group.current_members || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">成员上限:</span>
            <span class="value">{{ group.max_members || 500 }}</span>
          </div>
          <div class="info-item">
            <span class="label">成员占比:</span>
            <span class="value">{{ getMemberPercentage(group) }}%</span>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-section">
          <h4>收益数据</h4>
          <div class="info-item">
            <span class="label">入群价格:</span>
            <span class="value price">¥{{ (group.price || 0).toFixed(2) }}</span>
          </div>
          <div class="info-item">
            <span class="label">总收益:</span>
            <span class="value revenue">¥{{ (group.total_revenue || 0).toFixed(2) }}</span>
          </div>
          <div class="info-item">
            <span class="label">健康度:</span>
            <span class="value" :class="getHealthScoreClass(group.health_score)">
              {{ group.health_score || 'N/A' }}
            </span>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
defineProps({
  group: {
    type: Object,
    required: true
  }
})

const getMemberPercentage = (group) => {
  const current = group.current_members || 0
  const max = group.max_members || 500
  return Math.round((current / max) * 100)
}

const getHealthScoreClass = (score) => {
  if (!score || score < 40) return 'poor'
  if (score < 60) return 'fair'
  if (score < 80) return 'good'
  return 'excellent'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.group-quick-info {
  .info-section {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 8px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;

      .label {
        color: #6b7280;
      }

      .value {
        font-weight: 500;
        color: #374151;

        &.price {
          color: #ef4444;
        }

        &.revenue {
          color: #10b981;
        }

        &.excellent {
          color: #10b981;
        }

        &.good {
          color: #3b82f6;
        }

        &.fair {
          color: #f59e0b;
        }

        &.poor {
          color: #ef4444;
        }
      }
    }
  }
}
</style>