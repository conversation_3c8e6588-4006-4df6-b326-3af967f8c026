<template>
  <div class="paid-content-editor">
    <!-- 内容块列表 -->
    <div class="content-blocks">
      <div 
        v-for="(block, index) in contentBlocks" 
        :key="block.id"
        class="content-block"
      >
        <div class="block-header">
          <div class="block-type-info">
            <el-icon>
              <component :is="getBlockIcon(block.type)" />
            </el-icon>
            <span class="block-type-name">{{ getBlockTypeName(block.type) }}</span>
          </div>
          <div class="block-actions">
            <el-button 
              v-if="index > 0"
              @click="moveBlock(index, -1)" 
              size="small" 
              type="text"
              :icon="ArrowUp"
            />
            <el-button 
              v-if="index < contentBlocks.length - 1"
              @click="moveBlock(index, 1)" 
              size="small" 
              type="text"
              :icon="ArrowDown"
            />
            <el-button 
              @click="removeBlock(index)" 
              size="small" 
              type="text" 
              :icon="Delete"
              class="delete-btn"
            />
          </div>
        </div>
        
        <div class="block-content">
          <!-- 富文本内容 -->
          <div v-if="block.type === 'richtext'" class="richtext-editor">
            <ModernRichTextEditor
              v-model="block.content"
              :height="200"
              placeholder="请输入富文本内容..."
              @change="updateContent"
            />
          </div>
          
          <!-- 图片内容 -->
          <div v-else-if="block.type === 'images'" class="images-editor">
            <MediaUploader
              v-model="block.content"
              type="image"
              :limit="9"
              accept="image/*"
              list-type="picture-card"
              @change="updateContent"
            >
              <template #tip>
                <div class="upload-tip">支持上传多张图片，最多9张</div>
              </template>
            </MediaUploader>
          </div>
          
          <!-- 文档内容 -->
          <div v-else-if="block.type === 'documents'" class="documents-editor">
            <el-form-item label="文档标题">
              <el-input v-model="block.title" placeholder="请输入文档标题" @input="updateContent" />
            </el-form-item>
            <MediaUploader
              v-model="block.content"
              type="file"
              :limit="5"
              accept=".pdf,.doc,.docx,.txt"
              @change="updateContent"
            >
              <template #tip>
                <div class="upload-tip">支持PDF、Word、TXT格式，最多5个文件</div>
              </template>
            </MediaUploader>
          </div>
          
          <!-- 视频内容 -->
          <div v-else-if="block.type === 'video'" class="video-editor">
            <el-form-item label="视频标题">
              <el-input v-model="block.title" placeholder="请输入视频标题" @input="updateContent" />
            </el-form-item>
            <el-tabs v-model="block.videoType" @tab-change="updateContent">
              <el-tab-pane label="上传视频" name="upload">
                <MediaUploader
                  v-model="block.content"
                  type="video"
                  :limit="1"
                  accept="video/*"
                  @change="updateContent"
                >
                  <template #tip>
                    <div class="upload-tip">支持MP4、AVI等格式，建议大小不超过100MB</div>
                  </template>
                </MediaUploader>
              </el-tab-pane>
              <el-tab-pane label="嵌入链接" name="embed">
                <el-input
                  v-model="block.content"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入视频嵌入代码或链接"
                  @input="updateContent"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
          
          <!-- 网站链接 -->
          <div v-else-if="block.type === 'links'" class="links-editor">
            <el-form-item label="链接标题">
              <el-input v-model="block.title" placeholder="请输入链接标题" @input="updateContent" />
            </el-form-item>
            <el-form-item label="链接地址">
              <el-input v-model="block.content" placeholder="https://" @input="updateContent" />
            </el-form-item>
            <el-form-item label="链接描述">
              <el-input 
                v-model="block.description" 
                type="textarea" 
                :rows="2"
                placeholder="请输入链接描述（可选）" 
                @input="updateContent" 
              />
            </el-form-item>
          </div>
          
          <!-- 二维码 -->
          <div v-else-if="block.type === 'qrcode'" class="qrcode-editor">
            <el-form-item label="二维码标题">
              <el-input v-model="block.title" placeholder="请输入二维码标题" @input="updateContent" />
            </el-form-item>
            <el-form-item label="二维码描述">
              <el-input 
                v-model="block.description" 
                type="textarea" 
                :rows="2"
                placeholder="请输入二维码描述" 
                @input="updateContent" 
              />
            </el-form-item>
            <MediaUploader
              v-model="block.content"
              type="image"
              :limit="1"
              accept="image/*"
              list-type="picture-card"
              @change="updateContent"
            >
              <template #tip>
                <div class="upload-tip">上传二维码图片</div>
              </template>
            </MediaUploader>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加内容块按钮 -->
    <div class="add-block-section">
      <el-dropdown @command="addBlock" trigger="click">
        <el-button type="primary" :icon="Plus">
          添加内容块
          <el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="richtext">
              <el-icon><Document /></el-icon>
              富文本内容
            </el-dropdown-item>
            <el-dropdown-item command="images">
              <el-icon><Picture /></el-icon>
              图片内容
            </el-dropdown-item>
            <el-dropdown-item command="documents">
              <el-icon><Folder /></el-icon>
              文档内容
            </el-dropdown-item>
            <el-dropdown-item command="video">
              <el-icon><VideoPlay /></el-icon>
              视频内容
            </el-dropdown-item>
            <el-dropdown-item command="links">
              <el-icon><Link /></el-icon>
              网站链接
            </el-dropdown-item>
            <el-dropdown-item command="qrcode">
              <el-icon><Grid /></el-icon>
              二维码
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { 
  Plus, ArrowDown, ArrowUp, Delete, Document, Picture, 
  Folder, VideoPlay, Link, Grid 
} from '@element-plus/icons-vue'

// 异步组件
const ModernRichTextEditor = defineAsyncComponent({
  loader: () => import('@/components/ModernRichTextEditor.vue'),
  errorComponent: { template: '<div class="component-error">富文本编辑器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const MediaUploader = defineAsyncComponent({
  loader: () => import('@/components/MediaUploader.vue'),
  errorComponent: { template: '<div class="component-error">媒体上传器加载失败</div>' },
  loadingComponent: { template: '<div class="component-loading">加载中...</div>' }
})

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const contentBlocks = ref([...props.modelValue])

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  contentBlocks.value = [...newValue]
}, { deep: true })

// 监听内部数据变化
watch(contentBlocks, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
}, { deep: true })

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 添加内容块
const addBlock = (type) => {
  const newBlock = {
    id: generateId(),
    type,
    title: '',
    content: '',
    description: '',
    videoType: 'upload'
  }
  
  contentBlocks.value.push(newBlock)
}

// 移除内容块
const removeBlock = (index) => {
  contentBlocks.value.splice(index, 1)
}

// 移动内容块
const moveBlock = (index, direction) => {
  const newIndex = index + direction
  if (newIndex >= 0 && newIndex < contentBlocks.value.length) {
    const block = contentBlocks.value.splice(index, 1)[0]
    contentBlocks.value.splice(newIndex, 0, block)
  }
}

// 更新内容
const updateContent = () => {
  // 触发响应式更新
  emit('update:modelValue', [...contentBlocks.value])
  emit('change', [...contentBlocks.value])
}

// 获取块类型图标
const getBlockIcon = (type) => {
  const iconMap = {
    richtext: Document,
    images: Picture,
    documents: Folder,
    video: VideoPlay,
    links: Link,
    qrcode: Grid
  }
  return iconMap[type] || Document
}

// 获取块类型名称
const getBlockTypeName = (type) => {
  const nameMap = {
    richtext: '富文本内容',
    images: '图片内容',
    documents: '文档内容',
    video: '视频内容',
    links: '网站链接',
    qrcode: '二维码'
  }
  return nameMap[type] || '未知类型'
}
</script>

<style lang="scss" scoped>
.paid-content-editor {
  .content-blocks {
    .content-block {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 16px;
      background: #fafafa;

      .block-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
        border-radius: 8px 8px 0 0;

        .block-type-info {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;

          .el-icon {
            color: #409eff;
          }
        }

        .block-actions {
          display: flex;
          gap: 4px;

          .delete-btn {
            color: #f56c6c;

            &:hover {
              color: #f56c6c;
              background: #fef0f0;
            }
          }
        }
      }

      .block-content {
        padding: 16px;
        background: white;
        border-radius: 0 0 8px 8px;

        .upload-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 8px;
        }

        .richtext-editor {
          :deep(.ql-editor) {
            min-height: 150px;
          }
        }

        .images-editor {
          :deep(.el-upload-list) {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }

        .documents-editor {
          .el-form-item {
            margin-bottom: 16px;
          }
        }

        .video-editor {
          .el-form-item {
            margin-bottom: 16px;
          }

          .el-tabs {
            margin-top: 8px;
          }
        }

        .links-editor {
          .el-form-item {
            margin-bottom: 16px;
          }
        }

        .qrcode-editor {
          .el-form-item {
            margin-bottom: 16px;
          }
        }
      }
    }
  }

  .add-block-section {
    text-align: center;
    padding: 20px 0;

    .el-dropdown {
      .el-button {
        padding: 12px 24px;
        font-size: 14px;
      }
    }
  }

  .component-error {
    padding: 20px;
    text-align: center;
    color: #f56c6c;
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    margin: 8px 0;
  }

  .component-loading {
    padding: 20px;
    text-align: center;
    color: #909399;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin: 8px 0;
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    color: #409eff;
  }
}
</style>
