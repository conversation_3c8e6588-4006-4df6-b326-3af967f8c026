<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ResetAdminPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-admin-password {--email= : The admin user\'s email} {--password= : The new password} {--force : Force run without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the password for an admin user, creating the user if they do not exist.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->option('email');
        $password = $this->option('password');

        if (!$this->option('force')) {
            if (empty($email)) {
                $email = $this->ask('Enter the admin email address', '<EMAIL>');
            }

            if (empty($password)) {
                $password = $this->secret('Enter the new password (will not be visible)');
                if (empty($password)) {
                    $this->error('Password cannot be empty.');
                    return 1;
                }
            }
        } else {
            if (empty($email) || empty($password)) {
                $this->error('Email and password are required when using --force.');
                return 1;
            }
        }

        $admin = User::where('email', $email)->first();

        if (!$admin) {
            $username = explode('@', $email)[0];
            $admin = User::create([
                'username' => $username,
                'email' => $email,
                'password' => Hash::make($password),
                'level' => 'admin', // or appropriate level
            ]);
            $this->info("User {$email} not found. A new admin user has been created.");
        } else {
            $admin->password = Hash::make($password);
            $admin->save();
            $this->info("Password for admin user {$email} has been successfully reset.");
        }
        
        $this->info("You can now log in with:");
        $this->info("Email: " . $email);
        $this->info("Password: " . $password);

        return 0;
    }
} 