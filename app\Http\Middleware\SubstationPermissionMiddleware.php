<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * 分站权限中间件
 * 确保分站管理员只能访问自己分站的数据
 */
class SubstationPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $user = Auth::user();
        
        // 检查用户是否登录
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }
        
        // 超级管理员跳过权限检查
        if ($user->hasRole('admin')) {
            return $next($request);
        }
        
        // 检查是否为分站管理员
        if (!$user->hasRole('substation')) {
            return response()->json([
                'success' => false,
                'message' => '权限不足'
            ], 403);
        }
        
        // 检查分站是否存在且有效
        $substation = $user->substation;
        if (!$substation || !$substation->isActive()) {
            return response()->json([
                'success' => false,
                'message' => '分站不存在或已禁用'
            ], 403);
        }
        
        // 检查特定权限
        if ($permission && !$this->hasPermission($substation, $permission)) {
            return response()->json([
                'success' => false,
                'message' => '没有执行此操作的权限'
            ], 403);
        }
        
        // 将分站信息添加到请求中
        $request->attributes->set('substation', $substation);
        $request->attributes->set('substation_id', $substation->id);
        
        return $next($request);
    }
    
    /**
     * 检查分站是否有特定权限
     */
    private function hasPermission($substation, $permission)
    {
        // 获取详细权限配置
        $detailedPermissions = $substation->getSetting('detailed_permissions', []);
        
        // 如果有详细权限配置，使用详细权限
        if (!empty($detailedPermissions)) {
            return $this->checkDetailedPermission($detailedPermissions, $permission);
        }
        
        // 兼容旧的权限配置
        $permissions = $substation->getSetting('permissions', []);
        
        // 默认权限配置
        $defaultPermissions = [
            'user_management' => true,
            'agent_management' => true,
            'order_management' => true,
            'group_management' => true,
            'finance_view' => true,
            'finance_manage' => false,
            'system_settings' => false,
        ];
        
        // 合并权限配置
        $allPermissions = array_merge($defaultPermissions, $permissions);
        
        return $allPermissions[$permission] ?? false;
    }
    
    /**
     * 检查详细权限配置
     */
    private function checkDetailedPermission($detailedPermissions, $permission)
    {
        // 解析权限格式: module.action (如: user.create, agent.view)
        if (strpos($permission, '.') !== false) {
            [$module, $action] = explode('.', $permission, 2);
            return $detailedPermissions[$module][$action] ?? false;
        }
        
        // 兼容旧格式权限
        $legacyMapping = [
            'user_management' => 'user.view',
            'agent_management' => 'agent.view',
            'order_management' => 'order.view',
            'group_management' => 'group.view',
            'finance_view' => 'finance.view',
            'finance_manage' => 'finance.settle',
            'system_settings' => 'system.settings',
        ];
        
        if (isset($legacyMapping[$permission])) {
            [$module, $action] = explode('.', $legacyMapping[$permission]);
            return $detailedPermissions[$module][$action] ?? false;
        }
        
        return false;
    }
}