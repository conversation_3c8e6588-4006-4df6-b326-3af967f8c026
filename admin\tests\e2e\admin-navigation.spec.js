/**
 * E2E 测试 - 管理员导航功能完整性测试
 * 测试管理员角色的完整导航流程和交互
 */

import { test, expect } from '@playwright/test'

test.describe('管理员导航功能测试', () => {
  // 测试前置条件：登录管理员账号
  test.beforeEach(async ({ page }) => {
    // 访问登录页面
    await page.goto('/login')
    
    // 填写登录信息
    await page.fill('input[placeholder*="用户名"], input[placeholder*="账号"], input[type="text"]', 'admin')
    await page.fill('input[placeholder*="密码"], input[type="password"]', 'admin123')
    
    // 点击登录按钮
    await page.click('button[type="submit"], .login-btn, .el-button--primary')
    
    // 等待导航到仪表板
    await page.waitForURL('**/dashboard', { timeout: 10000 })
    
    // 确保页面完全加载
    await page.waitForLoadState('networkidle')
    
    // 验证登录成功（检查用户信息或登出按钮）
    await expect(page.locator('.user-info, .logout-btn, [data-testid="user-menu"]')).toBeVisible()
  })

  test('侧边栏主要导航项目功能测试', async ({ page }) => {
    // 定义主要导航项目及其预期URL
    const mainNavigationItems = [
      { 
        selector: 'text="数据看板"', 
        url: '**/dashboard',
        pageTitle: /数据看板|仪表板|Dashboard/i
      },
      { 
        selector: 'text="微信群管理"', 
        url: '**/community/group-list',
        pageTitle: /微信群|群组|Group/i
      },
      { 
        selector: 'text="用户管理"', 
        url: '**/user/user-list',
        pageTitle: /用户管理|用户列表|User/i
      },
      { 
        selector: 'text="财务管理"', 
        url: '**/finance/dashboard',
        pageTitle: /财务|Finance/i
      },
      { 
        selector: 'text="分销管理"', 
        url: '**/distribution/distributor-list',
        pageTitle: /分销|Distribution/i
      },
      { 
        selector: 'text="系统设置"', 
        url: '**/system/settings',
        pageTitle: /系统设置|设置|Settings/i
      }
    ]

    for (const item of mainNavigationItems) {
      console.log(`测试导航项目: ${item.selector}`)
      
      try {
        // 点击导航项目
        await page.click(item.selector)
        
        // 等待页面导航
        await page.waitForURL(item.url, { timeout: 8000 })
        
        // 等待页面内容加载
        await page.waitForLoadState('domcontentloaded')
        
        // 确保没有加载动画
        await expect(page.locator('.el-loading-mask')).toHaveCount(0, { timeout: 5000 })
        
        // 验证页面标题或内容
        const pageContent = page.locator('h1, .page-title, .el-page-header__title, .page-header')
        await expect(pageContent.first()).toBeVisible({ timeout: 5000 })
        
        // 验证页面URL正确
        expect(page.url()).toMatch(new RegExp(item.url.replace('**', '.*')))
        
        console.log(`✓ 导航项目 ${item.selector} 测试通过`)
        
      } catch (error) {
        console.error(`✗ 导航项目 ${item.selector} 测试失败:`, error.message)
        
        // 截图保存失败状态
        await page.screenshot({ 
          path: `test-results/nav-failure-${item.selector.replace(/[^a-z0-9]/gi, '_')}.png`,
          fullPage: true 
        })
        
        throw error
      }
    }
  })

  test('快捷操作面板功能测试', async ({ page }) => {
    // 确保在仪表板页面
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')
    
    // 检查快捷操作触发按钮
    const quickActionTrigger = page.locator('.trigger-btn, .quick-actions-trigger button')
    await expect(quickActionTrigger).toBeVisible({ timeout: 5000 })
    
    // 点击快捷操作按钮
    await quickActionTrigger.click()
    
    // 验证快捷操作面板显示
    const quickActionsPanel = page.locator('.quick-actions-panel')
    await expect(quickActionsPanel).toBeVisible({ timeout: 3000 })
    
    // 验证面板标题
    await expect(page.locator('.panel-title')).toContainText('快捷操作')
    
    // 获取快捷操作项目
    const quickActionItems = page.locator('.quick-action-item')
    const itemCount = await quickActionItems.count()
    
    expect(itemCount).toBeGreaterThan(0)
    
    // 测试第一个快捷操作
    if (itemCount > 0) {
      const firstAction = quickActionItems.first()
      
      // 验证快捷操作项目结构
      await expect(firstAction.locator('.action-icon')).toBeVisible()
      await expect(firstAction.locator('.action-title')).toBeVisible()
      await expect(firstAction.locator('.action-description')).toBeVisible()
      
      // 获取操作标题用于后续验证
      const actionTitle = await firstAction.locator('.action-title').textContent()
      
      // 点击快捷操作
      await firstAction.click()
      
      // 等待导航完成
      await page.waitForLoadState('domcontentloaded')
      
      // 验证快捷操作面板关闭
      await expect(quickActionsPanel).toBeHidden({ timeout: 3000 })
      
      console.log(`✓ 快捷操作 "${actionTitle}" 测试通过`)
    }
    
    // 测试关闭按钮功能
    await quickActionTrigger.click()
    await expect(quickActionsPanel).toBeVisible()
    
    const closeButton = page.locator('.close-btn, .quick-actions-panel .el-button')
    await closeButton.click()
    
    await expect(quickActionsPanel).toBeHidden({ timeout: 3000 })
  })

  test('响应式导航布局测试', async ({ page }) => {
    // 测试桌面端布局
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForTimeout(500) // 等待响应式变化
    
    const sidebar = page.locator('.sidebar, .navigation, .menu-container')
    await expect(sidebar).toBeVisible()
    
    // 验证侧边栏是否展开
    const sidebarClasses = await sidebar.getAttribute('class')
    expect(sidebarClasses).not.toContain('collapsed')
    
    // 测试平板端布局
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    // 检查导航是否适应平板布局
    await expect(sidebar).toBeVisible()
    
    // 测试手机端布局
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    // 在手机端，侧边栏应该收起或变为移动端样式
    const updatedClasses = await sidebar.getAttribute('class')
    const isMobileLayout = updatedClasses.includes('collapsed') || 
                          updatedClasses.includes('mobile') ||
                          !await sidebar.isVisible()
    
    expect(isMobileLayout).toBeTruthy()
    
    // 测试移动端导航菜单触发
    const mobileMenuTrigger = page.locator('.mobile-menu-trigger, .hamburger, .menu-toggle')
    if (await mobileMenuTrigger.isVisible()) {
      await mobileMenuTrigger.click()
      await page.waitForTimeout(300)
      
      // 验证移动端菜单显示
      const mobileMenu = page.locator('.mobile-navigation, .mobile-menu')
      await expect(mobileMenu).toBeVisible({ timeout: 3000 })
    }
  })

  test('侧边栏折叠/展开功能测试', async ({ page }) => {
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')
    
    // 确保在桌面视图
    await page.setViewportSize({ width: 1200, height: 800 })
    
    const sidebar = page.locator('.sidebar, .navigation')
    const toggleButton = page.locator('.sidebar-toggle, .collapse-btn, .menu-toggle')
    
    // 检查初始状态（通常是展开的）
    await expect(sidebar).toBeVisible()
    let initialClasses = await sidebar.getAttribute('class')
    const initiallyCollapsed = initialClasses.includes('collapsed')
    
    // 点击折叠按钮
    if (await toggleButton.isVisible()) {
      await toggleButton.click()
      await page.waitForTimeout(300) // 等待动画完成
      
      // 验证状态切换
      const newClasses = await sidebar.getAttribute('class')
      const nowCollapsed = newClasses.includes('collapsed')
      
      expect(nowCollapsed).toBe(!initiallyCollapsed)
      
      // 再次点击切换回来
      await toggleButton.click()
      await page.waitForTimeout(300)
      
      const finalClasses = await sidebar.getAttribute('class')
      const finallyCollapsed = finalClasses.includes('collapsed')
      
      expect(finallyCollapsed).toBe(initiallyCollapsed)
    }
  })

  test('导航面包屑功能测试', async ({ page }) => {
    // 导航到多级页面
    await page.click('text="微信群管理"')
    await page.waitForURL('**/community/group-list')
    
    // 检查面包屑是否存在并正确显示
    const breadcrumb = page.locator('.el-breadcrumb, .breadcrumb, .page-breadcrumb')
    
    if (await breadcrumb.isVisible()) {
      // 验证面包屑内容
      const breadcrumbItems = page.locator('.el-breadcrumb-item, .breadcrumb-item')
      const itemCount = await breadcrumbItems.count()
      
      expect(itemCount).toBeGreaterThan(0)
      
      // 验证包含当前页面
      await expect(breadcrumb).toContainText('微信群')
    }
  })

  test('用户菜单和登出功能测试', async ({ page }) => {
    // 查找用户菜单触发器
    const userMenuTrigger = page.locator('.user-info, .user-menu, .user-avatar, [data-testid="user-menu"]')
    await expect(userMenuTrigger).toBeVisible()
    
    // 点击用户菜单
    await userMenuTrigger.click()
    
    // 等待用户菜单显示
    const userDropdown = page.locator('.user-dropdown, .el-dropdown-menu, .user-menu-dropdown')
    await expect(userDropdown).toBeVisible({ timeout: 3000 })
    
    // 检查菜单项
    const menuItems = ['个人信息', '修改密码', '退出登录', '登出']
    let logoutItem = null
    
    for (const item of menuItems) {
      const menuItem = page.locator(`text="${item}"`)
      if (await menuItem.isVisible()) {
        if (item.includes('退出') || item.includes('登出')) {
          logoutItem = menuItem
        }
      }
    }
    
    // 测试登出功能
    if (logoutItem) {
      await logoutItem.click()
      
      // 可能会有确认对话框
      const confirmDialog = page.locator('.el-message-box, .confirm-dialog')
      if (await confirmDialog.isVisible()) {
        const confirmButton = page.locator('.el-button--primary', { hasText: '确定' })
        await confirmButton.click()
      }
      
      // 验证重定向到登录页面
      await page.waitForURL('**/login', { timeout: 5000 })
      await expect(page.locator('input[placeholder*="用户名"], input[type="text"]')).toBeVisible()
    }
  })

  test('导航权限控制测试', async ({ page }) => {
    // 验证管理员可以访问所有功能
    const adminOnlyFeatures = [
      { name: '系统设置', url: '**/system/settings' },
      { name: '用户管理', url: '**/user/user-list' },
      { name: '分销管理', url: '**/distribution/distributor-list' }
    ]
    
    for (const feature of adminOnlyFeatures) {
      try {
        await page.goto(feature.url.replace('**', ''))
        await page.waitForLoadState('domcontentloaded')
        
        // 验证不会被重定向到错误页面或登录页面
        expect(page.url()).not.toMatch(/login|error|403|unauthorized/i)
        
        // 验证页面内容正常加载
        const pageContent = page.locator('.page-content, .el-main, main')
        await expect(pageContent).toBeVisible({ timeout: 5000 })
        
        console.log(`✓ 管理员权限验证通过: ${feature.name}`)
        
      } catch (error) {
        console.error(`✗ 管理员权限验证失败: ${feature.name}`, error.message)
        throw error
      }
    }
  })

  test('导航性能基准测试', async ({ page }) => {
    // 测试页面加载性能
    const navigationItems = ['/dashboard', '/community/group-list', '/user/user-list']
    
    for (const url of navigationItems) {
      const startTime = Date.now()
      
      await page.goto(url)
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // 页面应在3秒内完成加载
      expect(loadTime).toBeLessThan(3000)
      
      console.log(`页面 ${url} 加载时间: ${loadTime}ms`)
    }
    
    // 测试导航切换性能
    await page.goto('/dashboard')
    
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now()
      
      await page.click('text="微信群管理"')
      await page.waitForURL('**/community/group-list')
      await page.waitForLoadState('domcontentloaded')
      
      const navTime = Date.now() - startTime
      
      // 导航应在1.5秒内完成
      expect(navTime).toBeLessThan(1500)
      
      // 切换回仪表板
      await page.click('text="数据看板"')
      await page.waitForURL('**/dashboard')
      await page.waitForLoadState('domcontentloaded')
    }
  })
})