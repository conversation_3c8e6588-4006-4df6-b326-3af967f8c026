<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 检查并添加缺失的字段
            if (!Schema::hasColumn('wechat_groups', 'group_number')) {
                $table->string('group_number', 20)->unique()->after('id')->nullable()->comment('群组编号');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'category')) {
                $table->string('category', 50)->nullable()->after('title')->comment('群组分类');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'owner_name')) {
                $table->string('owner_name', 50)->nullable()->after('user_id')->comment('群主名称');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'owner_avatar')) {
                $table->string('owner_avatar', 500)->nullable()->after('owner_name')->comment('群主头像');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'rules')) {
                $table->text('rules')->nullable()->after('description')->comment('群规');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'introduction')) {
                $table->text('introduction')->nullable()->after('rules')->comment('群介绍');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'announcement')) {
                $table->text('announcement')->nullable()->after('introduction')->comment('群公告');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'keywords')) {
                $table->string('keywords', 200)->nullable()->after('announcement')->comment('关键词');
            }
            
            if (!Schema::hasColumn('wechat_groups', 'member_limit')) {
                $table->integer('member_limit')->default(500)->after('max_members')->comment('成员限制');
            }
            
            // 添加索引以提高查询性能
            if (!Schema::hasIndex('wechat_groups', 'idx_group_number')) {
                $table->index('group_number', 'idx_group_number');
            }
            
            if (!Schema::hasIndex('wechat_groups', 'idx_category')) {
                $table->index('category', 'idx_category');
            }
            
            if (!Schema::hasIndex('wechat_groups', 'idx_owner_name')) {
                $table->index('owner_name', 'idx_owner_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex(['group_number']);
            $table->dropIndex(['category']);
            $table->dropIndex(['owner_name']);
            
            // 删除字段
            $table->dropColumn([
                'group_number',
                'category',
                'owner_name',
                'owner_avatar',
                'rules',
                'introduction',
                'announcement',
                'keywords',
                'member_limit'
            ]);
        });
    }
};