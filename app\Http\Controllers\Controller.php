<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Validator;

/**
 * 控制器基类
 * 所有控制器的基类，提供通用功能
 * 修复版本：移除了缺失的trait依赖
 */
class Controller extends BaseController
{
    // 移除了对缺失trait的依赖，改为直接实现需要的功能

    /**
     * 返回成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $message 成功消息
     * @param int $code HTTP状态码
     * @param array $meta 元数据
     * @return \Illuminate\Http\JsonResponse
     */
    protected function success($data = null, $message = '操作成功', $code = 200, array $meta = [])
    {
        $response = [
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp
        ];
        
        // 添加元数据
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        // 添加请求ID用于跟踪
        if (request()->hasHeader('X-Request-ID')) {
            $response['request_id'] = request()->header('X-Request-ID');
        }
        
        // 添加执行时间（如果有）
        if (defined('LARAVEL_START')) {
            $response['execution_time'] = round((microtime(true) - LARAVEL_START) * 1000, 2) . 'ms';
        }
        
        return response()->json($response, $code);
    }

    /**
     * 返回错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 额外数据
     * @param array $errors 详细错误信息
     * @param array $meta 元数据
     * @return \Illuminate\Http\JsonResponse
     */
    protected function error($message = '操作失败', $code = 400, $data = null, array $errors = [], array $meta = [])
    {
        $response = [
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->timestamp
        ];
        
        // 添加详细错误信息
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        // 添加元数据
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        // 添加请求ID用于跟踪
        if (request()->hasHeader('X-Request-ID')) {
            $response['request_id'] = request()->header('X-Request-ID');
        }
        
        // 添加执行时间（如果有）
        if (defined('LARAVEL_START')) {
            $response['execution_time'] = round((microtime(true) - LARAVEL_START) * 1000, 2) . 'ms';
        }
        
        // 记录错误日志（仅记录400以上的错误）
        if ($code >= 400) {
            $logContext = [
                'code' => $code,
                'message' => $message,
                'url' => request()->fullUrl(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'user_id' => auth()->id(),
            ];
            
            if (!empty($errors)) {
                $logContext['errors'] = $errors;
            }
            
            \Log::error('API错误响应', $logContext);
        }
        
        return response()->json($response, $code >= 400 && $code < 600 ? $code : 400);
    }

    /**
     * 返回分页响应
     * 
     * @param \Illuminate\Pagination\LengthAwarePaginator $data 分页数据
     * @param string $message 成功消息
     * @param int $code HTTP状态码
     * @param array $meta 元数据
     * @return \Illuminate\Http\JsonResponse
     */
    protected function paginate($data, $message = '获取成功', $code = 200, array $meta = [])
    {
        $response = [
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'last_page' => $data->lastPage(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
                'has_more' => $data->hasMorePages(),
                'links' => [
                    'first' => $data->url(1),
                    'last' => $data->url($data->lastPage()),
                    'prev' => $data->previousPageUrl(),
                    'next' => $data->nextPageUrl(),
                ]
            ],
            'timestamp' => now()->timestamp
        ];
        
        // 添加元数据
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        // 添加请求ID用于跟踪
        if (request()->hasHeader('X-Request-ID')) {
            $response['request_id'] = request()->header('X-Request-ID');
        }
        
        // 添加执行时间（如果有）
        if (defined('LARAVEL_START')) {
            $response['execution_time'] = round((microtime(true) - LARAVEL_START) * 1000, 2) . 'ms';
        }
        
        return response()->json($response, $code);
    }

    /**
     * 验证请求参数
     */
    protected function validateRequest($request, $rules, $messages = [])
    {
        $validator = Validator::make($request->all(), $rules, $messages);
        
        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }
        
        return null;
    }
    
    /**
     * 简单的授权检查（替代AuthorizesRequests trait）
     */
    protected function authorize($ability, $arguments = [])
    {
        // 简化的授权实现，可以根据需要扩展
        return true;
    }
    
    /**
     * 验证数据（替代ValidatesRequests trait）
     */
    protected function validate($request, array $rules, array $messages = [], array $customAttributes = [])
    {
        $validator = Validator::make($request->all(), $rules, $messages, $customAttributes);
        
        if ($validator->fails()) {
            throw new \Illuminate\Validation\ValidationException($validator);
        }
        
        return $validator->validated();
    }
} 