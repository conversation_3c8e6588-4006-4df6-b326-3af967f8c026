import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                 *//* empty css                *//* empty css                     *//* empty css               */import{a_ as a,aZ as l,bp as t,bq as u,aM as s,b9 as n,b8 as o,at as i,T as d,aL as c,aR as r,aY as p,b1 as _,bv as v,as as m,bh as f,bi as b,a$ as y,U as g,bw as h,bx as w,bk as k,bl as C,ay as j,Q as x,R as V}from"./element-plus-h2SQQM64.js";import{S as z}from"./StatCard-u_ssO_Ky.js";import{b as U}from"./agent-BTWzqVJ0.js";import{r as S,L as A,c as E,e as L,k as R,l as D,t as F,E as q,B as N,z as O,D as $,u as B,A as I,y as M}from"./vue-vendor-Dy164gUc.js";import"./utils-D1VZuEZr.js";/* empty css                                                                 */const P={class:"agent-applications"},Q={key:0,class:"batch-actions"},T={class:"batch-buttons"},X={class:"pagination"},Y={key:0,class:"application-detail"},Z={class:"detail-section"},G={class:"detail-section"},H={class:"detail-section"},J={key:0,class:"detail-section"},K={class:"review-info"},W=e({__name:"AgentApplications",setup(e){const W=S(!1),ee=S(1),ae=S(20),le=S(!1),te=S(!1),ue=S(!1),se=S({}),ne=S({data:[],total:0}),oe=S([]),ie=S(null),de=S(""),ce=A({keyword:"",status:"",agent_type:"",agent_level:""}),re=A({comment:""}),pe=E(()=>"approve"===de.value?"通过申请":"拒绝申请"),_e=async()=>{try{const e=await U.getStats();se.value=e.data}catch(e){x.error("加载统计数据失败")}},ve=async()=>{try{W.value=!0;const e={page:ee.value,limit:ae.value,...ce},a=await U.getList(e);ne.value=a.data}catch(e){x.error("加载申请列表失败")}finally{W.value=!1}},me=()=>{ee.value=1,ve()},fe=()=>{Object.keys(ce).forEach(e=>{ce[e]=""}),me()},be=e=>{ae.value=e,ve()},ye=e=>{ee.value=e,ve()},ge=e=>{oe.value=e},he=e=>{ie.value=e,de.value="approve",re.comment="",te.value=!0},we=e=>{ie.value=e,de.value="reject",re.comment="",te.value=!0},ke=async()=>{try{ue.value=!0,await U.review(ie.value.id,{action:de.value,comment:re.comment}),x.success("approve"===de.value?"申请已通过":"申请已拒绝"),te.value=!1,le.value=!1,ve(),_e()}catch(e){x.error("审核失败")}finally{ue.value=!1}},Ce=async()=>{try{await V.confirm("确定要批量通过选中的申请吗？","确认操作",{type:"warning"});const e=oe.value.map(e=>e.id);await U.batchReview({application_ids:e,action:"approve",comment:"批量通过"}),x.success("批量通过成功"),ve(),_e()}catch(e){"cancel"!==e&&x.error("批量通过失败")}},je=async()=>{try{await V.confirm("确定要批量拒绝选中的申请吗？","确认操作",{type:"warning"});const e=oe.value.map(e=>e.id);await U.batchReview({application_ids:e,action:"reject",comment:"批量拒绝"}),x.success("批量拒绝成功"),ve(),_e()}catch(e){"cancel"!==e&&x.error("批量拒绝失败")}},xe=e=>e?new Date(e).toLocaleString("zh-CN"):"",Ve=e=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"}[e]||"info"),ze=e=>({individual:"primary",enterprise:"success",channel:"warning"}[e]||"info"),Ue=e=>({platform:"primary",substation:"success"}[e]||"info");return L(()=>{_e(),ve()}),(e,x)=>{const V=a,U=l,S=s,A=u,E=o,L=n,de=d,_e=i,ve=t,Se=p,Ae=_,Ee=b,Le=y,Re=f,De=w,Fe=C,qe=k,Ne=j,Oe=h;return D(),R("div",P,[x[30]||(x[30]=F("div",{class:"page-header"},[F("h2",null,"代理商申请管理"),F("p",null,"审核和管理代理商申请，包括申请列表、审核流程和批量操作")],-1)),q(U,{gutter:20,class:"stats-row"},{default:O(()=>[q(V,{span:6},{default:O(()=>[q(z,{title:"总申请数",value:se.value.total||0,icon:"Document",color:"#409EFF"},null,8,["value"])]),_:1}),q(V,{span:6},{default:O(()=>[q(z,{title:"待审核",value:se.value.pending||0,icon:"Clock",color:"#E6A23C"},null,8,["value"])]),_:1}),q(V,{span:6},{default:O(()=>[q(z,{title:"已通过",value:se.value.approved||0,icon:"Check",color:"#67C23A"},null,8,["value"])]),_:1}),q(V,{span:6},{default:O(()=>[q(z,{title:"已拒绝",value:se.value.rejected||0,icon:"Close",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),q(Se,{class:"search-card"},{default:O(()=>[q(ve,{model:ce,inline:""},{default:O(()=>[q(A,{label:"关键词"},{default:O(()=>[q(S,{modelValue:ce.keyword,"onUpdate:modelValue":x[0]||(x[0]=e=>ce.keyword=e),placeholder:"搜索用户名、姓名或手机号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),q(A,{label:"申请状态"},{default:O(()=>[q(L,{modelValue:ce.status,"onUpdate:modelValue":x[1]||(x[1]=e=>ce.status=e),placeholder:"选择状态",clearable:""},{default:O(()=>[q(E,{label:"待审核",value:"pending"}),q(E,{label:"已通过",value:"approved"}),q(E,{label:"已拒绝",value:"rejected"}),q(E,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),q(A,{label:"代理商类型"},{default:O(()=>[q(L,{modelValue:ce.agent_type,"onUpdate:modelValue":x[2]||(x[2]=e=>ce.agent_type=e),placeholder:"选择类型",clearable:""},{default:O(()=>[q(E,{label:"个人代理",value:"individual"}),q(E,{label:"企业代理",value:"enterprise"}),q(E,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),q(A,{label:"代理商等级"},{default:O(()=>[q(L,{modelValue:ce.agent_level,"onUpdate:modelValue":x[3]||(x[3]=e=>ce.agent_level=e),placeholder:"选择等级",clearable:""},{default:O(()=>[q(E,{label:"平台代理商",value:"platform"}),q(E,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),q(A,null,{default:O(()=>[q(_e,{type:"primary",onClick:me},{default:O(()=>[q(de,null,{default:O(()=>[q(B(c))]),_:1}),x[13]||(x[13]=$(" 搜索 ",-1))]),_:1,__:[13]}),q(_e,{onClick:fe},{default:O(()=>[q(de,null,{default:O(()=>[q(B(r))]),_:1}),x[14]||(x[14]=$(" 重置 ",-1))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1}),oe.value.length>0?(D(),R("div",Q,[q(Ae,{title:`已选择 ${oe.value.length} 个申请`,type:"info","show-icon":"",closable:!1},null,8,["title"]),F("div",T,[q(_e,{type:"success",onClick:Ce},{default:O(()=>[q(de,null,{default:O(()=>[q(B(v))]),_:1}),x[15]||(x[15]=$(" 批量通过 ",-1))]),_:1,__:[15]}),q(_e,{type:"danger",onClick:je},{default:O(()=>[q(de,null,{default:O(()=>[q(B(m))]),_:1}),x[16]||(x[16]=$(" 批量拒绝 ",-1))]),_:1,__:[16]})])])):N("",!0),q(Se,{class:"applications-table"},{header:O(()=>x[17]||(x[17]=[F("span",null,"申请列表",-1)])),default:O(()=>[I((D(),M(Re,{data:ne.value.data,stripe:"",onSelectionChange:ge},{default:O(()=>[q(Ee,{type:"selection",width:"55"}),q(Ee,{prop:"user.username",label:"用户名"}),q(Ee,{prop:"user.name",label:"姓名"}),q(Ee,{prop:"user.phone",label:"手机号"}),q(Ee,{prop:"agent_type_text",label:"代理商类型"},{default:O(({row:e})=>[q(Le,{type:ze(e.agent_type)},{default:O(()=>[$(g(e.agent_type_text),1)]),_:2},1032,["type"])]),_:1}),q(Ee,{prop:"agent_level_text",label:"代理商等级"},{default:O(({row:e})=>[q(Le,{type:Ue(e.agent_level)},{default:O(()=>[$(g(e.agent_level_text),1)]),_:2},1032,["type"])]),_:1}),q(Ee,{prop:"expected_commission_rate",label:"期望佣金"},{default:O(({row:e})=>[$(g(e.expected_commission_rate)+"% ",1)]),_:1}),q(Ee,{prop:"status_text",label:"状态"},{default:O(({row:e})=>[q(Le,{type:Ve(e.status)},{default:O(()=>[$(g(e.status_text),1)]),_:2},1032,["type"])]),_:1}),q(Ee,{prop:"created_at",label:"申请时间"},{default:O(({row:e})=>[$(g(xe(e.created_at)),1)]),_:1}),q(Ee,{label:"操作",width:"200"},{default:O(({row:e})=>[q(_e,{size:"small",onClick:a=>{return l=e,ie.value=l,void(le.value=!0);var l}},{default:O(()=>x[18]||(x[18]=[$(" 查看详情 ",-1)])),_:2,__:[18]},1032,["onClick"]),"pending"===e.status?(D(),M(_e,{key:0,size:"small",type:"success",onClick:a=>he(e)},{default:O(()=>x[19]||(x[19]=[$(" 通过 ",-1)])),_:2,__:[19]},1032,["onClick"])):N("",!0),"pending"===e.status?(D(),M(_e,{key:1,size:"small",type:"danger",onClick:a=>we(e)},{default:O(()=>x[20]||(x[20]=[$(" 拒绝 ",-1)])),_:2,__:[20]},1032,["onClick"])):N("",!0)]),_:1})]),_:1},8,["data"])),[[Oe,W.value]]),F("div",X,[q(De,{"current-page":ee.value,"onUpdate:currentPage":x[4]||(x[4]=e=>ee.value=e),"page-size":ae.value,"onUpdate:pageSize":x[5]||(x[5]=e=>ae.value=e),total:ne.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:ye},null,8,["current-page","page-size","total"])])]),_:1}),q(Ne,{modelValue:le.value,"onUpdate:modelValue":x[9]||(x[9]=e=>le.value=e),title:"申请详情",width:"800px"},{footer:O(()=>[q(_e,{onClick:x[6]||(x[6]=e=>le.value=!1)},{default:O(()=>x[25]||(x[25]=[$("关闭",-1)])),_:1,__:[25]}),"pending"===ie.value?.status?(D(),M(_e,{key:0,type:"success",onClick:x[7]||(x[7]=e=>he(ie.value))},{default:O(()=>x[26]||(x[26]=[$(" 通过申请 ",-1)])),_:1,__:[26]})):N("",!0),"pending"===ie.value?.status?(D(),M(_e,{key:1,type:"danger",onClick:x[8]||(x[8]=e=>we(ie.value))},{default:O(()=>x[27]||(x[27]=[$(" 拒绝申请 ",-1)])),_:1,__:[27]})):N("",!0)]),default:O(()=>[ie.value?(D(),R("div",Y,[q(qe,{column:2,border:""},{default:O(()=>[q(Fe,{label:"申请人"},{default:O(()=>[$(g(ie.value.user?.name),1)]),_:1}),q(Fe,{label:"用户名"},{default:O(()=>[$(g(ie.value.user?.username),1)]),_:1}),q(Fe,{label:"手机号"},{default:O(()=>[$(g(ie.value.user?.phone),1)]),_:1}),q(Fe,{label:"邮箱"},{default:O(()=>[$(g(ie.value.user?.email),1)]),_:1}),q(Fe,{label:"代理商类型"},{default:O(()=>[q(Le,{type:ze(ie.value.agent_type)},{default:O(()=>[$(g(ie.value.agent_type_text),1)]),_:1},8,["type"])]),_:1}),q(Fe,{label:"代理商等级"},{default:O(()=>[q(Le,{type:Ue(ie.value.agent_level)},{default:O(()=>[$(g(ie.value.agent_level_text),1)]),_:1},8,["type"])]),_:1}),q(Fe,{label:"期望佣金比例"},{default:O(()=>[$(g(ie.value.expected_commission_rate)+"% ",1)]),_:1}),q(Fe,{label:"申请状态"},{default:O(()=>[q(Le,{type:Ve(ie.value.status)},{default:O(()=>[$(g(ie.value.status_text),1)]),_:1},8,["type"])]),_:1})]),_:1}),F("div",Z,[x[21]||(x[21]=F("h4",null,"企业信息",-1)),q(qe,{column:2,border:""},{default:O(()=>[q(Fe,{label:"公司名称"},{default:O(()=>[$(g(ie.value.business_info?.company_name||"无"),1)]),_:1}),q(Fe,{label:"营业执照"},{default:O(()=>[$(g(ie.value.business_info?.business_license||"无"),1)]),_:1}),q(Fe,{label:"经营范围",span:2},{default:O(()=>[$(g(ie.value.business_info?.business_scope||"无"),1)]),_:1})]),_:1})]),F("div",G,[x[22]||(x[22]=F("h4",null,"联系信息",-1)),q(qe,{column:2,border:""},{default:O(()=>[q(Fe,{label:"联系人"},{default:O(()=>[$(g(ie.value.contact_info?.contact_person),1)]),_:1}),q(Fe,{label:"联系电话"},{default:O(()=>[$(g(ie.value.contact_info?.contact_phone),1)]),_:1}),q(Fe,{label:"联系邮箱"},{default:O(()=>[$(g(ie.value.contact_info?.contact_email||"无"),1)]),_:1}),q(Fe,{label:"联系地址"},{default:O(()=>[$(g(ie.value.contact_info?.contact_address||"无"),1)]),_:1})]),_:1})]),F("div",H,[x[23]||(x[23]=F("h4",null,"申请理由",-1)),F("p",null,g(ie.value.application_reason),1)]),ie.value.review_comment?(D(),R("div",J,[x[24]||(x[24]=F("h4",null,"审核意见",-1)),F("p",null,g(ie.value.review_comment),1),F("p",K," 审核人："+g(ie.value.reviewer?.name)+" 审核时间："+g(xe(ie.value.reviewed_at)),1)])):N("",!0)])):N("",!0)]),_:1},8,["modelValue"]),q(Ne,{modelValue:te.value,"onUpdate:modelValue":x[12]||(x[12]=e=>te.value=e),title:pe.value,width:"500px"},{footer:O(()=>[q(_e,{onClick:x[11]||(x[11]=e=>te.value=!1)},{default:O(()=>x[28]||(x[28]=[$("取消",-1)])),_:1,__:[28]}),q(_e,{type:"primary",onClick:ke,loading:ue.value},{default:O(()=>x[29]||(x[29]=[$(" 确认 ",-1)])),_:1,__:[29]},8,["loading"])]),default:O(()=>[q(ve,{model:re,"label-width":"80px"},{default:O(()=>[q(A,{label:"审核意见"},{default:O(()=>[q(S,{modelValue:re.comment,"onUpdate:modelValue":x[10]||(x[10]=e=>re.comment=e),type:"textarea",rows:4,placeholder:"请输入审核意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-f683cf37"]]);export{W as default};
