// 主路由配置 - 集成所有功能模块
import { createRouter, createWebHashHistory } from 'vue-router'
import { dataScreenRoutes } from './dataScreen.js'
import { getToken } from '@/utils/auth'

// 主要路由配置
const routes = [
  // 重定向根路径到登录页
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/ModernDashboard.vue'),
    meta: {
      title: '控制台',
      icon: 'TrendCharts',
      requiresAuth: true
    }
  },
  {
    path: '/dashboard/analytics',
    name: 'DashboardAnalytics',
    component: () => import('@/views/dashboard/Analytics.vue'),
    meta: {
      title: '数据分析',
      icon: 'DataLine',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true
    }
  },
  {
    path: '/test',
    name: 'TestRoute',
    component: () => import('@/views/TestRoute.vue'),
    meta: {
      title: '路由测试',
      hideInMenu: true
    }
  },
  {
    path: '/test-dashboard',
    name: 'TestDashboard',
    component: () => import('@/views/dashboard/ModernDashboard.vue'),
    meta: {
      title: '测试控制台',
      hideInMenu: true
    }
  },
  {
    path: '/scroll-test',
    name: 'ScrollTest',
    component: () => import('@/views/ScrollTest.vue'),
    meta: {
      title: '滚动测试',
      hideInMenu: true
    }
  },
  {
    path: '/system-status',
    name: 'SystemStatus',
    component: () => import('@/views/SystemStatus.vue'),
    meta: {
      title: '系统状态',
      hideInMenu: true
    }
  },

  // 社群管理
  {
    path: '/community',
    name: 'Community',
    redirect: '/community/groups',
    meta: {
      title: '社群管理',
      icon: 'Comment',
      requiresAuth: true
    }
  },
  {
    path: '/community/groups',
    name: 'CommunityGroups',
    component: () => import('@/views/community/GroupList.vue'),
    meta: {
      title: '群组列表',
      icon: 'Comment',
      requiresAuth: true
    }
  },
  {
    path: '/community/templates',
    name: 'CommunityTemplates',
    component: () => import('@/views/community/TemplateManagement.vue'),
    meta: {
      title: '模板管理',
      icon: 'Document',
      requiresAuth: true
    }
  },

  // 用户管理
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/user/UserList.vue'),
    meta: {
      title: '用户管理',
      icon: 'User',
      requiresAuth: true,
      roles: ['admin', 'manager']
    }
  },
  {
    path: '/users/analytics',
    name: 'UsersAnalytics',
    component: () => import('@/views/user/UserAnalytics.vue'),
    meta: {
      title: '用户分析',
      icon: 'TrendCharts',
      requiresAuth: true
    }
  },

  // 订单管理
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/orders/OrderList.vue'),
    meta: {
      title: '订单管理',
      icon: 'ShoppingCart',
      requiresAuth: true
    }
  },
  
  // 财务管理
  {
    path: '/finance',
    name: 'Finance',
    component: () => import('@/views/finance/FinanceDashboard.vue'),
    meta: {
      title: '财务管理',
      icon: 'CreditCard',
      requiresAuth: true
    }
  },

  // 系统设置
  {
    path: '/system',
    name: 'System',
    redirect: '/system/settings',
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/settings',
    name: 'SystemSettings',
    component: () => import('@/views/system/Settings.vue'),
    meta: {
      title: '基础设置',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  {
    path: '/system/monitor',
    name: 'SystemMonitor',
    component: () => import('@/views/system/Monitor.vue'),
    meta: {
      title: '系统监控',
      icon: 'TrendCharts',
      requiresAuth: true,
      roles: ['admin']
    }
  },
  
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/ErrorPage.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 合并数据大屏路由
const allRoutes = [...routes, ...dataScreenRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log(`🛣️ 路由守卫: 从 ${from.path} 到 ${to.path}`)
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - LinkHub Pro 管理系统`
  }
  
  // 检查认证
  if (to.meta.requiresAuth) {
    const token = getToken()
    console.log(`🔐 检查认证: token = ${token ? '存在' : '不存在'}`)
    if (!token) {
      console.log('❌ 无token，重定向到登录页')
      next('/login')
      return
    }
  }
  
  // 检查权限
  if (to.meta.roles) {
    const userRole = localStorage.getItem('user_role')
    console.log(`👤 检查权限: 需要角色 ${to.meta.roles}, 当前角色 ${userRole}`)
    if (!to.meta.roles.includes(userRole)) {
      console.warn('权限不足，无法访问该页面')
      next('/dashboard')
      return
    }
  }
  
  console.log('✅ 路由守卫通过，允许访问')
  next()
})

export default router
