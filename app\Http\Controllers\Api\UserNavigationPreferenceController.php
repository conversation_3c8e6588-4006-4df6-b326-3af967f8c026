<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserNavigationPreference;
use App\Models\NavigationUsageStat;
use App\Services\NavigationCacheService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * 用户导航偏好管理API控制器
 * 
 * 提供个性化导航偏好设置和管理功能
 */
class UserNavigationPreferenceController extends Controller
{
    protected NavigationCacheService $cacheService;

    public function __construct(NavigationCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * 获取用户导航偏好设置
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        try {
            $preference = UserNavigationPreference::where('user_id', $user->id)->first();
            
            if (!$preference) {
                // 创建默认偏好设置
                $preference = $this->createDefaultPreferences($user->id);
            }

            // 获取使用统计
            $stats = $this->getUserNavigationStats($user->id);

            return response()->json([
                'success' => true,
                'data' => [
                    'preferences' => $preference,
                    'stats' => $stats,
                    'recommendations' => $this->getPersonalizedRecommendations($user->id)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取偏好设置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 更新用户导航偏好设置
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'pinned_menus' => 'nullable|array',
            'pinned_menus.*' => 'string|max:50',
            'hidden_menus' => 'nullable|array', 
            'hidden_menus.*' => 'string|max:50',
            'menu_order' => 'nullable|array',
            'domain_preferences' => 'nullable|array',
            'layout_settings' => 'nullable|array',
            'quick_access' => 'nullable|array',
            'quick_access.*' => 'string|max:50',
            'enable_smart_recommend' => 'boolean',
            'default_domain' => 'nullable|string|in:business,operation,analytics,system'
        ]);

        try {
            DB::beginTransaction();

            $preference = UserNavigationPreference::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'pinned_menus' => $request->pinned_menus,
                    'hidden_menus' => $request->hidden_menus,
                    'menu_order' => $request->menu_order,
                    'domain_preferences' => $request->domain_preferences,
                    'layout_settings' => $request->layout_settings,
                    'quick_access' => $request->quick_access,
                    'enable_smart_recommend' => $request->boolean('enable_smart_recommend', true),
                    'default_domain' => $request->default_domain ?? 'business'
                ]
            );

            // 清除用户相关的导航缓存
            $this->cacheService->clearUserNavigationCache($user->id);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '偏好设置已更新',
                'data' => $preference
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '更新偏好设置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 快速操作：固定/取消固定菜单
     */
    public function togglePin(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'menu_code' => 'required|string|max:50',
            'action' => 'required|string|in:pin,unpin'
        ]);

        try {
            $preference = UserNavigationPreference::firstOrCreate(['user_id' => $user->id]);
            
            $pinnedMenus = $preference->pinned_menus ?? [];
            $menuCode = $request->menu_code;

            if ($request->action === 'pin') {
                if (!in_array($menuCode, $pinnedMenus)) {
                    $pinnedMenus[] = $menuCode;
                }
            } else {
                $pinnedMenus = array_values(array_filter($pinnedMenus, fn($code) => $code !== $menuCode));
            }

            $preference->update(['pinned_menus' => $pinnedMenus]);

            // 清除用户导航缓存
            $this->cacheService->clearUserNavigationCache($user->id);

            return response()->json([
                'success' => true,
                'message' => $request->action === 'pin' ? '菜单已固定' : '菜单已取消固定',
                'data' => ['pinned_menus' => $pinnedMenus]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 快速操作：隐藏/显示菜单
     */
    public function toggleVisibility(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'menu_code' => 'required|string|max:50',
            'action' => 'required|string|in:hide,show'
        ]);

        try {
            $preference = UserNavigationPreference::firstOrCreate(['user_id' => $user->id]);
            
            $hiddenMenus = $preference->hidden_menus ?? [];
            $menuCode = $request->menu_code;

            if ($request->action === 'hide') {
                if (!in_array($menuCode, $hiddenMenus)) {
                    $hiddenMenus[] = $menuCode;
                }
            } else {
                $hiddenMenus = array_values(array_filter($hiddenMenus, fn($code) => $code !== $menuCode));
            }

            $preference->update(['hidden_menus' => $hiddenMenus]);

            // 清除用户导航缓存
            $this->cacheService->clearUserNavigationCache($user->id);

            return response()->json([
                'success' => true,
                'message' => $request->action === 'hide' ? '菜单已隐藏' : '菜单已显示',
                'data' => ['hidden_menus' => $hiddenMenus]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '操作失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 设置快捷访问菜单
     */
    public function setQuickAccess(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'menu_codes' => 'required|array|max:10',
            'menu_codes.*' => 'string|max:50'
        ]);

        try {
            $preference = UserNavigationPreference::firstOrCreate(['user_id' => $user->id]);
            $preference->update(['quick_access' => $request->menu_codes]);

            // 清除用户导航缓存
            $this->cacheService->clearUserNavigationCache($user->id);

            return response()->json([
                'success' => true,
                'message' => '快捷访问菜单已更新',
                'data' => ['quick_access' => $request->menu_codes]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '更新快捷访问失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 记录菜单访问
     */
    public function recordAccess(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'menu_code' => 'required|string|max:50',
            'domain' => 'required|string|max:20',
            'duration' => 'nullable|integer|min:0',
            'context' => 'nullable|array'
        ]);

        try {
            // 异步记录访问统计
            dispatch(function () use ($user, $request) {
                $today = now()->format('Y-m-d');
                
                NavigationUsageStat::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'menu_code' => $request->menu_code,
                        'date' => $today
                    ],
                    [
                        'domain' => $request->domain,
                        'visit_count' => DB::raw('visit_count + 1'),
                        'last_visited_at' => now(),
                        'total_duration' => DB::raw('total_duration + ' . ($request->duration ?? 0)),
                        'avg_duration' => DB::raw('CASE WHEN visit_count > 0 THEN total_duration / visit_count ELSE 0 END'),
                        'access_patterns' => $request->context
                    ]
                );

                // 清除相关缓存
                $this->cacheService->clearUserStatsCache($user->id);
            })->afterResponse();

            return response()->json([
                'success' => true,
                'message' => '访问记录已保存'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '记录访问失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 获取用户导航使用报告
     */
    public function getUsageReport(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'domain' => 'nullable|string|in:business,operation,analytics,system'
        ]);

        $days = $request->integer('days', 30);
        $domain = $request->domain;

        try {
            $cacheKey = "user_navigation_report:{$user->id}:{$days}:{$domain}";
            
            $report = Cache::remember($cacheKey, 1800, function () use ($user, $days, $domain) {
                $query = NavigationUsageStat::where('user_id', $user->id)
                    ->where('date', '>=', now()->subDays($days)->format('Y-m-d'));

                if ($domain) {
                    $query->where('domain', $domain);
                }

                $stats = $query->get();

                return [
                    'summary' => [
                        'total_visits' => $stats->sum('visit_count'),
                        'unique_menus' => $stats->unique('menu_code')->count(),
                        'avg_daily_visits' => $stats->sum('visit_count') / $days,
                        'total_time' => $stats->sum('total_duration'),
                        'avg_session_time' => $stats->avg('avg_duration')
                    ],
                    'most_used' => $stats->sortByDesc('visit_count')->take(10)->values(),
                    'domain_distribution' => $stats->groupBy('domain')->map(function ($group) {
                        return [
                            'visits' => $group->sum('visit_count'),
                            'unique_menus' => $group->count(),
                            'time_spent' => $group->sum('total_duration')
                        ];
                    }),
                    'daily_activity' => $this->getDailyActivity($user->id, $days, $domain),
                    'time_patterns' => $this->getTimePatterns($user->id, $days, $domain)
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $report,
                'meta' => [
                    'period' => $days,
                    'domain' => $domain,
                    'generated_at' => now()->timestamp
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取使用报告失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 重置用户导航偏好设置
     */
    public function resetPreferences(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => '请先登录'], 401);
        }

        $request->validate([
            'reset_type' => 'nullable|string|in:all,preferences,stats,cache',
            'confirm' => 'required|boolean|accepted'
        ]);

        $resetType = $request->input('reset_type', 'preferences');

        try {
            DB::beginTransaction();

            switch ($resetType) {
                case 'all':
                    UserNavigationPreference::where('user_id', $user->id)->delete();
                    NavigationUsageStat::where('user_id', $user->id)->delete();
                    $this->cacheService->clearUserNavigationCache($user->id);
                    $message = '所有导航数据已重置';
                    break;
                    
                case 'stats':
                    NavigationUsageStat::where('user_id', $user->id)->delete();
                    $this->cacheService->clearUserStatsCache($user->id);
                    $message = '使用统计已重置';
                    break;
                    
                case 'cache':
                    $this->cacheService->clearUserNavigationCache($user->id);
                    $message = '导航缓存已清除';
                    break;
                    
                default:
                    UserNavigationPreference::where('user_id', $user->id)->delete();
                    $this->cacheService->clearUserNavigationCache($user->id);
                    $message = '导航偏好已重置';
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '重置失败',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    // ========== 私有辅助方法 ==========

    private function createDefaultPreferences(int $userId): UserNavigationPreference
    {
        return UserNavigationPreference::create([
            'user_id' => $userId,
            'pinned_menus' => [],
            'hidden_menus' => [],
            'menu_order' => [],
            'domain_preferences' => [
                'business' => ['weight' => 1.0],
                'operation' => ['weight' => 0.8], 
                'analytics' => ['weight' => 0.6],
                'system' => ['weight' => 0.4]
            ],
            'layout_settings' => [
                'sidebar_collapsed' => false,
                'show_icons' => true,
                'compact_mode' => false
            ],
            'quick_access' => [],
            'enable_smart_recommend' => true,
            'default_domain' => 'business'
        ]);
    }

    private function getUserNavigationStats(int $userId): array
    {
        $cacheKey = "user_nav_stats:{$userId}";
        
        return Cache::remember($cacheKey, 1800, function () use ($userId) {
            $stats = NavigationUsageStat::where('user_id', $userId)
                ->where('date', '>=', now()->subDays(30)->format('Y-m-d'))
                ->get();

            return [
                'total_visits' => $stats->sum('visit_count'),
                'unique_menus' => $stats->unique('menu_code')->count(),
                'most_used_domain' => $stats->groupBy('domain')
                    ->map->sum('visit_count')
                    ->sortDesc()
                    ->keys()
                    ->first(),
                'active_days' => $stats->unique('date')->count(),
                'avg_daily_usage' => $stats->avg('visit_count')
            ];
        });
    }

    private function getPersonalizedRecommendations(int $userId): array
    {
        // 简化版推荐逻辑，实际应调用推荐服务
        return Cache::remember("user_recommendations:{$userId}", 3600, function () use ($userId) {
            return [
                'suggested_pins' => [],
                'related_menus' => [],
                'trending_in_role' => []
            ];
        });
    }

    private function getDailyActivity(int $userId, int $days, ?string $domain): array
    {
        // 实现每日活动统计
        return [];
    }

    private function getTimePatterns(int $userId, int $days, ?string $domain): array
    {
        // 实现时间模式分析
        return [];
    }
}