<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 导航搜索记录模型
 */
class NavigationSearchLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'query', 'results', 'results_count',
        'selected_result', 'has_result', 'searched_at', 'ip_address'
    ];

    protected $casts = [
        'results' => 'array',
        'results_count' => 'integer',
        'has_result' => 'boolean',
        'searched_at' => 'datetime',
    ];

    public $timestamps = false;

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取热门搜索词
     */
    public static function getPopularQueries($days = 30, $limit = 20)
    {
        return self::where('searched_at', '>=', now()->subDays($days))
            ->selectRaw('query, COUNT(*) as search_count, AVG(results_count) as avg_results')
            ->groupBy('query')
            ->orderByDesc('search_count')
            ->limit($limit)
            ->get();
    }

    /**
     * 获取搜索成功率
     */
    public static function getSuccessRate($days = 30)
    {
        $total = self::where('searched_at', '>=', now()->subDays($days))->count();
        $successful = self::where('searched_at', '>=', now()->subDays($days))
            ->where('has_result', true)
            ->count();
        
        return $total > 0 ? ($successful / $total) * 100 : 0;
    }
}