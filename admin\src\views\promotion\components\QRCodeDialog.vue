<template>
  <el-dialog
    v-model="dialogVisible"
    title="二维码生成"
    width="600px"
    :close-on-click-modal="false"
  >
    <div v-if="link" class="qrcode-content">
      <!-- 二维码配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>二维码配置</span>
          </div>
        </template>

        <el-form :model="qrConfig" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="尺寸大小">
                <el-select v-model="qrConfig.size" @change="generateQRCode">
                  <el-option label="小 (200x200)" :value="200" />
                  <el-option label="中 (300x300)" :value="300" />
                  <el-option label="大 (500x500)" :value="500" />
                  <el-option label="超大 (800x800)" :value="800" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="边距">
                <el-select v-model="qrConfig.margin" @change="generateQRCode">
                  <el-option label="无边距" :value="0" />
                  <el-option label="小边距" :value="1" />
                  <el-option label="标准边距" :value="2" />
                  <el-option label="大边距" :value="4" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="格式">
                <el-select v-model="qrConfig.format" @change="generateQRCode">
                  <el-option label="PNG" value="png" />
                  <el-option label="JPG" value="jpg" />
                  <el-option label="SVG" value="svg" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="容错级别">
                <el-select v-model="qrConfig.errorCorrection" @change="generateQRCode">
                  <el-option label="低 (L)" value="L" />
                  <el-option label="中 (M)" value="M" />
                  <el-option label="高 (Q)" value="Q" />
                  <el-option label="最高 (H)" value="H" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 二维码预览 -->
      <el-card class="preview-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><View /></el-icon>
            <span>二维码预览</span>
            <div class="header-actions">
              <el-button
                type="primary"
                size="small"
                :loading="generating"
                @click="generateQRCode"
              >
                <el-icon><Refresh /></el-icon>
                重新生成
              </el-button>
            </div>
          </div>
        </template>

        <div class="preview-container">
          <div v-if="generating" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>正在生成二维码...</p>
          </div>
          
          <div v-else-if="qrCodeUrl" class="qrcode-preview">
            <img :src="qrCodeUrl" :alt="`${link.title}的二维码`" class="qrcode-image" />
            <div class="qrcode-info">
              <p class="qrcode-title">{{ link.title || '推广链接' }}</p>
              <p class="qrcode-url">{{ link.short_url }}</p>
              <p class="qrcode-size">{{ qrConfig.size }}x{{ qrConfig.size }}px</p>
            </div>
          </div>

          <div v-else class="empty-container">
            <el-icon><Picture /></el-icon>
            <p>点击生成二维码</p>
          </div>
        </div>
      </el-card>

      <!-- 批量生成 -->
      <el-card class="batch-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Grid /></el-icon>
            <span>批量生成</span>
          </div>
        </template>

        <div class="batch-options">
          <el-checkbox-group v-model="batchSizes">
            <el-checkbox :label="200">小尺寸 (200x200)</el-checkbox>
            <el-checkbox :label="300">中尺寸 (300x300)</el-checkbox>
            <el-checkbox :label="500">大尺寸 (500x500)</el-checkbox>
            <el-checkbox :label="800">超大尺寸 (800x800)</el-checkbox>
          </el-checkbox-group>
          
          <el-button
            type="success"
            :loading="batchGenerating"
            :disabled="batchSizes.length === 0"
            @click="batchGenerate"
            style="margin-top: 15px;"
          >
            <el-icon><Download /></el-icon>
            批量生成并下载
          </el-button>
        </div>
      </el-card>

      <!-- 使用说明 -->
      <el-card class="tips-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><QuestionFilled /></el-icon>
            <span>使用说明</span>
          </div>
        </template>

        <div class="tips-content">
          <el-alert
            title="二维码使用提示"
            type="info"
            :closable="false"
            show-icon
          >
            <ul class="tips-list">
              <li>二维码扫描后将跳转到推广链接</li>
              <li>建议使用PNG格式以获得最佳质量</li>
              <li>容错级别越高，二维码越复杂但容错能力越强</li>
              <li>适当的边距可以提高扫描成功率</li>
              <li>可以将二维码保存到本地或直接分享使用</li>
            </ul>
          </el-alert>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          :disabled="!qrCodeUrl"
          @click="downloadQRCode"
        >
          <el-icon><Download /></el-icon>
          下载二维码
        </el-button>
        <el-button
          type="success"
          :disabled="!qrCodeUrl"
          @click="copyQRCodeUrl"
        >
          <el-icon><CopyDocument /></el-icon>
          复制链接
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Setting,
  View,
  Refresh,
  Loading,
  Picture,
  Grid,
  Download,
  QuestionFilled,
  CopyDocument
} from '@element-plus/icons-vue'
import { shortLinkApi } from '@/api/promotion'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  link: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const generating = ref(false)
const batchGenerating = ref(false)
const qrCodeUrl = ref('')
const batchSizes = ref([200, 300, 500])

// 二维码配置
const qrConfig = reactive({
  size: 300,
  margin: 2,
  format: 'png',
  errorCorrection: 'M'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const generateQRCode = async () => {
  if (!props.link) return
  
  generating.value = true
  try {
    const response = await shortLinkApi.generateQRCode(props.link.short_code, qrConfig)
    
    // 创建blob URL
    const blob = new Blob([response], { type: `image/${qrConfig.format}` })
    qrCodeUrl.value = URL.createObjectURL(blob)
    
    ElMessage.success('二维码生成成功')
  } catch (error) {
    ElMessage.error('生成二维码失败：' + error.message)
  } finally {
    generating.value = false
  }
}

const batchGenerate = async () => {
  if (!props.link || batchSizes.value.length === 0) return
  
  batchGenerating.value = true
  try {
    const promises = batchSizes.value.map(async (size) => {
      const config = { ...qrConfig, size }
      const response = await shortLinkApi.generateQRCode(props.link.short_code, config)
      return {
        size,
        blob: new Blob([response], { type: `image/${qrConfig.format}` }),
        filename: `qrcode_${props.link.short_code}_${size}x${size}.${qrConfig.format}`
      }
    })
    
    const results = await Promise.all(promises)
    
    // 下载所有二维码
    results.forEach(({ blob, filename }) => {
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
      URL.revokeObjectURL(url)
    })
    
    ElMessage.success(`成功生成并下载 ${results.length} 个二维码`)
  } catch (error) {
    ElMessage.error('批量生成失败：' + error.message)
  } finally {
    batchGenerating.value = false
  }
}

const downloadQRCode = () => {
  if (!qrCodeUrl.value) return
  
  const a = document.createElement('a')
  a.href = qrCodeUrl.value
  a.download = `qrcode_${props.link.short_code}_${qrConfig.size}x${qrConfig.size}.${qrConfig.format}`
  a.click()
  
  ElMessage.success('二维码下载成功')
}

const copyQRCodeUrl = async () => {
  if (!props.link) return
  
  try {
    await navigator.clipboard.writeText(props.link.short_url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.link) {
      // 重置状态
      qrCodeUrl.value = ''
      // 自动生成二维码
      generateQRCode()
    } else if (!visible) {
      // 清理blob URL
      if (qrCodeUrl.value) {
        URL.revokeObjectURL(qrCodeUrl.value)
        qrCodeUrl.value = ''
      }
    }
  }
)
</script>

<style scoped>
.qrcode-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-actions {
  margin-left: auto;
}

.config-card,
.preview-card,
.batch-card,
.tips-card {
  margin-bottom: 20px;
}

.preview-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container {
  text-align: center;
  color: #909399;
}

.loading-container .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.qrcode-preview {
  text-align: center;
}

.qrcode-image {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qrcode-info {
  margin-top: 15px;
}

.qrcode-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 5px 0;
}

.qrcode-url {
  font-size: 14px;
  color: #606266;
  font-family: monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin: 0 0 5px 0;
}

.qrcode-size {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

.empty-container {
  text-align: center;
  color: #909399;
}

.empty-container .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.batch-options {
  padding: 10px 0;
}

.tips-content {
  padding: 10px 0;
}

.tips-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

:deep(.el-alert__content) {
  padding-left: 0;
}
</style>