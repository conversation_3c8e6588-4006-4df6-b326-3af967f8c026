<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Services\GroupOperationService;
use App\Services\GroupRecommendationService;
use App\Services\ABTestService;
use App\Services\GroupTemplateService;
use App\Services\IPLocationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

/**
 * 高级群组管理控制器
 * 整合运营智能化、个性化推荐、A/B测试、模板系统等功能
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
class AdvancedGroupController extends Controller
{
    private GroupOperationService $operationService;
    private GroupRecommendationService $recommendationService;
    private ABTestService $abTestService;
    private GroupTemplateService $templateService;
    private IPLocationService $ipLocationService;

    public function __construct(
        GroupOperationService $operationService,
        GroupRecommendationService $recommendationService,
        ABTestService $abTestService,
        GroupTemplateService $templateService,
        IPLocationService $ipLocationService
    ) {
        $this->operationService = $operationService;
        $this->recommendationService = $recommendationService;
        $this->abTestService = $abTestService;
        $this->templateService = $templateService;
        $this->ipLocationService = $ipLocationService;
    }

    /**
     * 获取群组健康度报告
     * 
     * @param int $groupId 群组ID
     * @return JsonResponse
     */
    public function getGroupHealth(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            // 检查权限
            if (!$this->checkGroupPermission($group)) {
                return response()->json(['error' => '无权限访问该群组'], 403);
            }
            
            $healthReport = $this->operationService->calculateGroupHealth($group);
            
            return response()->json([
                'success' => true,
                'data' => $healthReport
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取群组性能监控数据
     * 
     * @param int $groupId 群组ID
     * @return JsonResponse
     */
    public function getPerformanceMonitoring(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if (!$this->checkGroupPermission($group)) {
                return response()->json(['error' => '无权限访问该群组'], 403);
            }
            
            $monitoring = $this->operationService->getPerformanceMonitoring($group);
            
            return response()->json([
                'success' => true,
                'data' => $monitoring
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 自动优化群组
     * 
     * @param int $groupId 群组ID
     * @return JsonResponse
     */
    public function autoOptimizeGroup(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if (!$this->checkGroupPermission($group)) {
                return response()->json(['error' => '无权限操作该群组'], 403);
            }
            
            $optimizations = $this->operationService->autoOptimizeGroup($group);
            
            return response()->json([
                'success' => true,
                'message' => '群组自动优化完成',
                'data' => $optimizations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取智能定价建议
     * 
     * @param int $groupId 群组ID
     * @return JsonResponse
     */
    public function getSmartPricingRecommendation(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::findOrFail($groupId);
            
            if (!$this->checkGroupPermission($group)) {
                return response()->json(['error' => '无权限访问该群组'], 403);
            }
            
            $pricingRecommendation = $this->operationService->getSmartPricingRecommendation($group);
            
            return response()->json([
                'success' => true,
                'data' => $pricingRecommendation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取个性化群组推荐
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPersonalizedRecommendations(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $limit = $request->input('limit', 10);
            
            // 构建上下文信息
            $context = [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'city' => $this->ipLocationService->getCity($request->ip())
            ];
            
            $recommendations = $this->recommendationService->recommendGroups($user, $context, $limit);
            
            return response()->json([
                'success' => true,
                'data' => $recommendations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取个性化搜索建议
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPersonalizedSearch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:1|max:100',
                'limit' => 'integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 400);
            }

            $query = $request->input('query');
            $limit = $request->input('limit', 10);
            $user = Auth::user();
            
            $searchResults = $this->recommendationService->getPersonalizedSearchSuggestions($query, $user, $limit);
            
            return response()->json([
                'success' => true,
                'data' => $searchResults
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建A/B测试
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function createABTest(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100',
                'description' => 'string|max:500',
                'type' => 'required|string|in:price,title,description,layout,virtual_data',
                'variants' => 'required|array|min:2',
                'target_groups' => 'array',
                'traffic_allocation' => 'integer|min:1|max:100',
                'start_date' => 'date',
                'end_date' => 'date|after:start_date'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 400);
            }

            $testConfig = $request->all();
            $test = $this->abTestService->createABTest($testConfig);
            
            return response()->json([
                'success' => true,
                'message' => 'A/B测试创建成功',
                'data' => $test
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取A/B测试结果
     * 
     * @param string $testId 测试ID
     * @return JsonResponse
     */
    public function getABTestResults(string $testId): JsonResponse
    {
        try {
            $results = $this->abTestService->getTestResults($testId);
            
            if (empty($results)) {
                return response()->json([
                    'success' => false,
                    'error' => '测试不存在或无数据'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 停止A/B测试
     * 
     * @param string $testId 测试ID
     * @return JsonResponse
     */
    public function stopABTest(string $testId): JsonResponse
    {
        try {
            $result = $this->abTestService->stopTest($testId);
            
            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['results'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取A/B测试模板
     * 
     * @return JsonResponse
     */
    public function getABTestTemplates(): JsonResponse
    {
        try {
            $templates = $this->abTestService->getTestTemplates();
            
            return response()->json([
                'success' => true,
                'data' => $templates
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取群组模板列表
     * 
     * @return JsonResponse
     */
    public function getGroupTemplates(): JsonResponse
    {
        try {
            $templates = $this->templateService->getPresetTemplates();
            $usageStats = $this->templateService->getTemplateUsageStats();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'templates' => $templates,
                    'usage_stats' => $usageStats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 基于模板创建群组
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function createGroupFromTemplate(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'template_key' => 'required|string',
                'city' => 'string|max:20',
                'customization' => 'array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 400);
            }

            $templateKey = $request->input('template_key');
            $customization = $request->input('customization', []);
            $user = Auth::user();
            
            $group = $this->templateService->createGroupFromTemplate($templateKey, $user, $customization);
            
            return response()->json([
                'success' => true,
                'message' => '群组创建成功',
                'data' => $group
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量创建群组
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchCreateGroups(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'template' => 'required|string',
                'cities' => 'array|max:10',
                'count' => 'integer|min:1|max:5',
                'customization' => 'array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 400);
            }

            $batchConfig = $request->all();
            $user = Auth::user();
            
            $results = $this->templateService->batchCreateGroups($batchConfig, $user);
            
            return response()->json([
                'success' => true,
                'message' => '批量创建完成',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出群组为模板
     * 
     * @param int $groupId 群组ID
     * @param Request $request
     * @return JsonResponse
     */
    public function exportGroupAsTemplate(int $groupId, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'template_name' => 'required|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 400);
            }

            $group = WechatGroup::findOrFail($groupId);
            
            if (!$this->checkGroupPermission($group)) {
                return response()->json(['error' => '无权限操作该群组'], 403);
            }
            
            $templateName = $request->input('template_name');
            $template = $this->templateService->exportGroupAsTemplate($group, $templateName);
            
            return response()->json([
                'success' => true,
                'message' => '模板导出成功',
                'data' => $template
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户推荐模板
     * 
     * @return JsonResponse
     */
    public function getRecommendedTemplates(): JsonResponse
    {
        try {
            $user = Auth::user();
            $recommendations = $this->templateService->recommendTemplatesForUser($user);
            
            return response()->json([
                'success' => true,
                'data' => $recommendations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取快速创建配置
     * 
     * @return JsonResponse
     */
    public function getQuickCreateConfigs(): JsonResponse
    {
        try {
            $configs = $this->templateService->getQuickCreateConfigs();
            $recommendationTags = $this->recommendationService->getRecommendationTags();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'quick_create' => $configs,
                    'recommendation_tags' => $recommendationTags
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取群组管理仪表板数据
     * 
     * @return JsonResponse
     */
    public function getDashboard(): JsonResponse
    {
        try {
            $user = Auth::user();
            $userGroups = WechatGroup::where('user_id', $user->id)->get();
            
            // 基础统计
            $basicStats = [
                'total_groups' => $userGroups->count(),
                'active_groups' => $userGroups->where('status', WechatGroup::STATUS_ACTIVE)->count(),
                'total_members' => $userGroups->sum('current_members'),
                'total_earnings' => $userGroups->sum('total_earnings'),
                'avg_conversion_rate' => $userGroups->avg(function ($group) {
                    return $group->getConversionRate();
                })
            ];
            
            // 性能排行
            $topPerformingGroups = $userGroups->sortByDesc(function ($group) {
                return $group->getConversionRate();
            })->take(5)->values();
            
            // 健康度总览
            $healthOverview = [];
            foreach ($userGroups->take(10) as $group) {
                $health = $this->operationService->calculateGroupHealth($group);
                $healthOverview[] = [
                    'group_id' => $group->id,
                    'group_title' => $group->title,
                    'health_score' => $health['total_score'],
                    'grade' => $health['grade']
                ];
            }
            
            // 推荐操作
            $recommendedActions = [];
            foreach ($userGroups->take(5) as $group) {
                $monitoring = $this->operationService->getPerformanceMonitoring($group);
                if (!empty($monitoring['alerts'])) {
                    $recommendedActions[] = [
                        'group_id' => $group->id,
                        'group_title' => $group->title,
                        'alerts' => $monitoring['alerts']
                    ];
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'basic_stats' => $basicStats,
                    'top_performing_groups' => $topPerformingGroups,
                    'health_overview' => $healthOverview,
                    'recommended_actions' => $recommendedActions,
                    'generated_at' => now()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取数据分析报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAnalyticsReport(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', 'week'); // week, month, quarter
            $groupIds = $request->input('group_ids', []);
            $user = Auth::user();
            
            $query = WechatGroup::where('user_id', $user->id);
            
            if (!empty($groupIds)) {
                $query->whereIn('id', $groupIds);
            }
            
            $groups = $query->get();
            
            $analytics = [
                'period' => $period,
                'total_groups' => $groups->count(),
                'summary' => [],
                'trends' => [],
                'insights' => []
            ];
            
            // 计算摘要数据
            foreach ($groups as $group) {
                $stats = $group->getAdvancedStats();
                $analytics['summary'][] = [
                    'group_id' => $group->id,
                    'group_title' => $group->title,
                    'stats' => $stats
                ];
            }
            
            // 生成洞察
            $analytics['insights'] = $this->generateAnalyticsInsights($groups);
            
            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查群组权限
     * 
     * @param WechatGroup $group
     * @return bool
     */
    private function checkGroupPermission(WechatGroup $group): bool
    {
        $user = Auth::user();
        
        // 管理员可以访问所有群组
        if ($user->hasRole('admin')) {
            return true;
        }
        
        // 群主可以访问自己的群组
        if ($group->user_id === $user->id) {
            return true;
        }
        
        // 分站可以访问下属的群组
        if ($user->hasRole('substation') && $group->substation_id === $user->substation_id) {
            return true;
        }
        
        return false;
    }

    /**
     * 生成分析洞察
     * 
     * @param \Illuminate\Support\Collection $groups
     * @return array
     */
    private function generateAnalyticsInsights($groups): array
    {
        $insights = [];
        
        if ($groups->isEmpty()) {
            return $insights;
        }
        
        // 转化率洞察
        $conversionRates = $groups->map(function ($group) {
            return $group->getConversionRate();
        });
        
        $avgConversion = $conversionRates->avg();
        $maxConversion = $conversionRates->max();
        $minConversion = $conversionRates->min();
        
        if ($maxConversion - $minConversion > 5) {
            $insights[] = [
                'type' => 'conversion_variance',
                'title' => '转化率差异较大',
                'description' => "您的群组转化率在 {$minConversion}% 到 {$maxConversion}% 之间，建议分析高转化率群组的成功因素",
                'priority' => 'medium'
            ];
        }
        
        // 价格洞察
        $prices = $groups->pluck('price');
        $avgPrice = $prices->avg();
        $freeGroups = $groups->where('price', 0)->count();
        
        if ($freeGroups > $groups->count() * 0.5) {
            $insights[] = [
                'type' => 'pricing_strategy',
                'title' => '免费群组占比较高',
                'description' => '您有超过一半的群组是免费的，考虑为部分群组设置合理价格以增加收入',
                'priority' => 'high'
            ];
        }
        
        return $insights;
    }
} 