<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CommissionLog;
use App\Models\User;
use App\Models\Order;
use App\Services\CommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * 佣金管理控制器
 * 处理佣金计算、分配、结算等管理功能
 */
class CommissionController extends Controller
{
    protected CommissionService $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->middleware('auth:api');
        $this->commissionService = $commissionService;
    }

    /**
     * 用户佣金记录列表
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            $query = CommissionLog::with(['order', 'fromUser'])
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // 类型筛选
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            // 时间范围筛选
            if ($request->has('start_date')) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }
            if ($request->has('end_date')) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            $commissions = $query->paginate($request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => '佣金记录获取成功',
                'data' => $commissions,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '佣金记录获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 管理员佣金记录列表
     */
    public function adminIndex(Request $request)
    {
        try {
            $query = CommissionLog::with(['user', 'order', 'fromUser'])
                ->orderBy('created_at', 'desc');

            // 用户筛选
            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // 状态筛选
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // 类型筛选
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            // 时间范围筛选
            if ($request->has('start_date')) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }
            if ($request->has('end_date')) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // 金额范围筛选
            if ($request->has('min_amount')) {
                $query->where('amount', '>=', $request->min_amount);
            }
            if ($request->has('max_amount')) {
                $query->where('amount', '<=', $request->max_amount);
            }

            $commissions = $query->paginate($request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => '佣金记录获取成功',
                'data' => $commissions,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '佣金记录获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 查看佣金详情
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            
            $query = CommissionLog::with(['user', 'order', 'fromUser']);
            
            // 非管理员只能查看自己的佣金记录
            if (!$user->hasRole('admin')) {
                $query->where('user_id', $user->id);
            }

            $commission = $query->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $commission,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '佣金记录不存在',
            ], 404);
        }
    }

    /**
     * 管理员查看佣金详情
     */
    public function adminShow($id)
    {
        try {
            $commission = CommissionLog::with(['user', 'order', 'fromUser'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $commission,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '佣金记录不存在',
            ], 404);
        }
    }

    /**
     * 重新计算佣金
     */
    public function recalculate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_ids' => 'nullable|array',
                'order_ids.*' => 'integer|exists:orders,id',
                'user_ids' => 'nullable|array',
                'user_ids.*' => 'integer|exists:users,id',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'force' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $orderIds = $request->order_ids;
            $userIds = $request->user_ids;
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $force = $request->boolean('force', false);

            DB::beginTransaction();

            $recalculatedCount = 0;

            if ($orderIds) {
                // 重新计算指定订单的佣金
                foreach ($orderIds as $orderId) {
                    $order = Order::find($orderId);
                    if ($order && $order->status === Order::STATUS_PAID_INT) {
                        if ($force) {
                            // 删除现有佣金记录
                            CommissionLog::where('order_id', $orderId)->delete();
                        }
                        
                        $this->commissionService->calculateCommission($order);
                        $recalculatedCount++;
                    }
                }
            } elseif ($userIds || $startDate || $endDate) {
                // 重新计算指定条件的佣金
                $query = Order::where('status', Order::STATUS_PAID_INT);

                if ($userIds) {
                    $query->whereIn('user_id', $userIds);
                }

                if ($startDate) {
                    $query->whereDate('paid_at', '>=', $startDate);
                }

                if ($endDate) {
                    $query->whereDate('paid_at', '<=', $endDate);
                }

                $orders = $query->get();

                foreach ($orders as $order) {
                    if ($force) {
                        // 删除现有佣金记录
                        CommissionLog::where('order_id', $order->id)->delete();
                    }
                    
                    $this->commissionService->calculateCommission($order);
                    $recalculatedCount++;
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '请指定重新计算的条件',
                ], 422);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '佣金重新计算完成',
                'data' => [
                    'recalculated_count' => $recalculatedCount,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '佣金重新计算失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 佣金统计
     */
    public function statistics(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = match($period) {
                '1d' => now()->startOfDay(),
                '7d' => now()->subDays(7),
                '30d' => now()->subDays(30),
                '90d' => now()->subDays(90),
                default => now()->subDays(30)
            };

            $stats = [
                'overview' => [
                    'total_commissions' => CommissionLog::where('created_at', '>=', $startDate)->count(),
                    'total_amount' => CommissionLog::where('created_at', '>=', $startDate)->sum('amount'),
                    'pending_amount' => CommissionLog::where('status', 'pending')->sum('amount'),
                    'settled_amount' => CommissionLog::where('status', 'settled')
                                                  ->where('created_at', '>=', $startDate)->sum('amount'),
                    'frozen_amount' => CommissionLog::where('status', 'frozen')->sum('amount'),
                ],
                'by_level' => $this->getCommissionByLevel($startDate),
                'by_type' => $this->getCommissionByType($startDate),
                'daily_trend' => $this->getDailyCommissionTrend($startDate),
                'top_earners' => $this->getTopCommissionEarners($startDate),
                'distribution_analysis' => $this->getDistributionAnalysis($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '统计数据获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 批量结算佣金
     */
    public function batchSettle(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'commission_ids' => 'required|array',
                'commission_ids.*' => 'integer|exists:commission_logs,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            DB::beginTransaction();

            $settledCount = 0;
            $totalAmount = 0;

            foreach ($request->commission_ids as $commissionId) {
                $commission = CommissionLog::find($commissionId);
                
                if ($commission && $commission->status === 'pending') {
                    $result = $this->commissionService->settleCommission($commission);
                    
                    if ($result) {
                        $settledCount++;
                        $totalAmount += $commission->amount;
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '批量结算完成',
                'data' => [
                    'settled_count' => $settledCount,
                    'total_amount' => $totalAmount,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量结算失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 单个佣金结算
     */
    public function settle($id)
    {
        try {
            $commission = CommissionLog::findOrFail($id);

            if ($commission->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => '该佣金记录不是待结算状态',
                ], 422);
            }

            DB::beginTransaction();

            $result = $this->commissionService->settleCommission($commission);

            if (!$result) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => '佣金结算失败',
                ], 500);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '佣金结算成功',
                'data' => $commission->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '佣金结算失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取按级别统计的佣金
     */
    private function getCommissionByLevel($startDate): array
    {
        return CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('level, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('level')
            ->orderBy('level')
            ->get()
            ->toArray();
    }

    /**
     * 获取按类型统计的佣金
     */
    private function getCommissionByType($startDate): array
    {
        return CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('type, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('type')
            ->get()
            ->toArray();
    }

    /**
     * 获取每日佣金趋势
     */
    private function getDailyCommissionTrend($startDate): array
    {
        return CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * 获取佣金收入最多的用户
     */
    private function getTopCommissionEarners($startDate): array
    {
        return CommissionLog::with('user')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('user_id, COUNT(*) as count, SUM(amount) as total_amount')
            ->groupBy('user_id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * 获取分销分析数据
     */
    private function getDistributionAnalysis($startDate): array
    {
        // 分销层级分析
        $levelAnalysis = CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('level, COUNT(DISTINCT user_id) as user_count, COUNT(*) as commission_count, SUM(amount) as total_amount')
            ->groupBy('level')
            ->orderBy('level')
            ->get();

        // 活跃分销商分析
        $activeDistributors = CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('COUNT(DISTINCT user_id) as active_count')
            ->first();

        // 平均佣金分析
        $avgCommission = CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('AVG(amount) as avg_amount, MAX(amount) as max_amount, MIN(amount) as min_amount')
            ->first();

        return [
            'level_analysis' => $levelAnalysis->toArray(),
            'active_distributors' => $activeDistributors->active_count ?? 0,
            'avg_commission' => $avgCommission->toArray(),
        ];
    }
}