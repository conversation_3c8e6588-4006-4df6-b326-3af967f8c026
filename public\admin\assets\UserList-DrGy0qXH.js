import{_ as e}from"./index-DtXAftX0.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                */import{r as a,c as l,L as t,d as s,y as r,l as u,z as i,E as o,B as n,D as d,t as c,af as m,e as p,k as v,u as g,F as f,Y as _,a3 as b,A as h,C as w}from"./vue-vendor-Dy164gUc.js";import{bp as y,bq as V,aM as k,b9 as z,b8 as U,bm as x,bB as C,at as j,U as N,ay as T,Q as B,an as I,T as S,a4 as D,aw as q,aL as A,c9 as L,aY as $,bh as O,bi as F,aS as E,a$ as P,ac as Q,bQ as R,bw as Y,bx as K,R as M,p as G,o as H}from"./element-plus-h2SQQM64.js";/* empty css                     *//* empty css                       *//* empty css                 */import"./utils-D1VZuEZr.js";const J={class:"dialog-footer"},W=e({__name:"UserDialog",props:{modelValue:{type:Boolean,default:!1},userData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:m}){const p=e,v=m,g=a(),f=a(!1),_=l({get:()=>p.modelValue,set:e=>v("update:modelValue",e)}),b=l(()=>!!p.userData?.id),h=t({username:"",realName:"",email:"",phone:"",role:"user",status:"active",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少6位",trigger:"blur"}]};s(()=>p.userData,e=>{e&&Object.keys(e).length>0?Object.assign(h,{username:e.username||"",realName:e.realName||"",email:e.email||"",phone:e.phone||"",role:e.role||"user",status:e.status||"active",password:""}):Object.assign(h,{username:"",realName:"",email:"",phone:"",role:"user",status:"active",password:""})},{immediate:!0,deep:!0});const I=async()=>{try{await g.value.validate(),f.value=!0,await new Promise(e=>setTimeout(e,1e3)),B.success(b.value?"用户更新成功":"用户创建成功"),v("success"),S()}catch(e){console.error("表单验证失败:",e)}finally{f.value=!1}},S=()=>{g.value?.resetFields(),_.value=!1};return(e,a)=>{const l=k,t=V,s=U,m=z,p=C,v=x,B=y,D=j,q=T;return u(),r(q,{modelValue:_.value,"onUpdate:modelValue":a[7]||(a[7]=e=>_.value=e),title:b.value?"编辑用户":"添加用户",width:"600px","before-close":S,class:"modern-dialog"},{footer:i(()=>[c("div",J,[o(D,{onClick:S},{default:i(()=>a[10]||(a[10]=[d("取消",-1)])),_:1,__:[10]}),o(D,{type:"primary",onClick:I,loading:f.value},{default:i(()=>[d(N(b.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:i(()=>[o(B,{ref_key:"formRef",ref:g,model:h,rules:w,"label-width":"100px",size:"large"},{default:i(()=>[o(t,{label:"用户名",prop:"username"},{default:i(()=>[o(l,{modelValue:h.username,"onUpdate:modelValue":a[0]||(a[0]=e=>h.username=e),placeholder:"请输入用户名",disabled:b.value},null,8,["modelValue","disabled"])]),_:1}),o(t,{label:"真实姓名",prop:"realName"},{default:i(()=>[o(l,{modelValue:h.realName,"onUpdate:modelValue":a[1]||(a[1]=e=>h.realName=e),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),o(t,{label:"邮箱",prop:"email"},{default:i(()=>[o(l,{modelValue:h.email,"onUpdate:modelValue":a[2]||(a[2]=e=>h.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),o(t,{label:"手机号",prop:"phone"},{default:i(()=>[o(l,{modelValue:h.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>h.phone=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),o(t,{label:"角色",prop:"role"},{default:i(()=>[o(m,{modelValue:h.role,"onUpdate:modelValue":a[4]||(a[4]=e=>h.role=e),placeholder:"请选择角色",style:{width:"100%"}},{default:i(()=>[o(s,{label:"管理员",value:"admin"}),o(s,{label:"分站管理员",value:"substation"}),o(s,{label:"代理商",value:"agent"}),o(s,{label:"分销员",value:"distributor"}),o(s,{label:"群主",value:"group_owner"}),o(s,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"状态",prop:"status"},{default:i(()=>[o(v,{modelValue:h.status,"onUpdate:modelValue":a[5]||(a[5]=e=>h.status=e)},{default:i(()=>[o(p,{label:"active"},{default:i(()=>a[8]||(a[8]=[d("启用",-1)])),_:1,__:[8]}),o(p,{label:"inactive"},{default:i(()=>a[9]||(a[9]=[d("禁用",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),b.value?n("",!0):(u(),r(t,{key:0,label:"密码",prop:"password"},{default:i(()=>[o(l,{modelValue:h.password,"onUpdate:modelValue":a[6]||(a[6]=e=>h.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-ced41b7d"]]),X={class:"modern-user-list"},Z={class:"page-header"},ee={class:"header-content"},ae={class:"header-left"},le={class:"header-icon"},te={class:"header-actions"},se={class:"stats-section"},re={class:"stats-container"},ue={class:"stat-content"},ie={class:"stat-value"},oe={class:"stat-label"},ne={class:"filter-section"},de={class:"filter-content"},ce={class:"filter-left"},me={class:"filter-right"},pe={class:"table-section"},ve={class:"table-header"},ge={class:"table-title"},fe={class:"table-actions"},_e={class:"user-info"},be={class:"user-details"},he={class:"user-name"},we={class:"user-email"},ye={class:"time-info"},Ve={key:0,class:"time-info"},ke={key:1,class:"text-muted"},ze={class:"action-buttons"},Ue={class:"pagination-wrapper"},xe=e({__name:"UserList",setup(e){const l=m(),s=a(!1),y=a([]),V=a([]),x=a(!1),C=a({}),T=a(0),J=t({keyword:"",role:"",status:""}),xe=t({page:1,size:20}),Ce=a([{key:"total",label:"总用户数",value:1234,icon:"UserFilled",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:"up",trendIcon:"ArrowUp",change:"+12.5%"},{key:"active",label:"活跃用户",value:1156,icon:"User",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:"up",trendIcon:"ArrowUp",change:"****%"},{key:"distributors",label:"分销员",value:89,icon:"Share",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:"up",trendIcon:"ArrowUp",change:"+15.2%"},{key:"agents",label:"代理商",value:23,icon:"Avatar",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:"up",trendIcon:"ArrowUp",change:"****%"}]),je=[{id:1,username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",role:"admin",status:"active",avatar:"",created_at:"2024-01-01 10:00:00",last_login_at:"2024-08-06 15:30:00"},{id:2,username:"zhangsan",realName:"张三",email:"<EMAIL>",phone:"13800138001",role:"agent",status:"active",avatar:"",created_at:"2024-02-15 14:20:00",last_login_at:"2024-08-06 12:15:00"},{id:3,username:"lisi",realName:"李四",email:"<EMAIL>",phone:"13800138002",role:"distributor",status:"active",avatar:"",created_at:"2024-03-10 09:30:00",last_login_at:"2024-08-05 18:45:00"},{id:4,username:"wangwu",realName:"王五",email:"<EMAIL>",phone:"13800138003",role:"user",status:"inactive",avatar:"",created_at:"2024-04-20 16:10:00",last_login_at:null}],Ne=e=>({admin:"管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"}[e]||"未知角色"),Te=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",Be=e=>e?new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):"",Ie=()=>{l.push("/user/add")},Se=e=>{V.value=e},De=async()=>{if(0!==V.value.length)try{await M.confirm(`确定要批量删除选中的 ${V.value.length} 个用户吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),V.value.forEach(e=>{const a=y.value.findIndex(a=>a.id===e.id);a>-1&&(y.value.splice(a,1),T.value--)}),V.value=[],B.success("批量删除成功")}catch(e){"cancel"!==e&&B.error("批量删除失败")}else B.warning("请选择要删除的用户")},qe=()=>{s.value=!0,setTimeout(()=>{let e=[...je];J.keyword&&(e=e.filter(e=>e.username.includes(J.keyword)||e.email.includes(J.keyword)||e.phone.includes(J.keyword))),J.role&&(e=e.filter(e=>e.role===J.role)),J.status&&(e=e.filter(e=>e.status===J.status)),y.value=e,T.value=e.length,s.value=!1},500)},Ae=()=>{J.keyword="",J.role="",J.status="",y.value=[...je],T.value=je.length,B.info("搜索条件已重置")},Le=e=>{xe.page=e},$e=e=>{xe.size=e,xe.page=1},Oe=async()=>{try{B.success("导出功能开发中...")}catch(e){B.error("导出失败")}},Fe=()=>{y.value=[...je],B.success("用户信息更新成功")};return p(()=>{y.value=[...je],T.value=je.length}),(e,a)=>{const l=S,t=j,m=k,p=U,je=z,Ee=$,Pe=P,Qe=F,Re=E,Ye=O,Ke=K,Me=Y;return u(),v("div",X,[c("div",Z,[c("div",ee,[c("div",ae,[c("div",le,[o(l,{size:"24"},{default:i(()=>[o(g(I))]),_:1})]),a[6]||(a[6]=c("div",{class:"header-text"},[c("h1",null,"用户管理"),c("p",null,"管理平台所有用户信息，包括用户权限、状态和基本资料")],-1))]),c("div",te,[o(t,{onClick:Oe,class:"action-btn secondary"},{default:i(()=>[o(l,null,{default:i(()=>[o(g(D))]),_:1}),a[7]||(a[7]=d(" 导出数据 ",-1))]),_:1,__:[7]}),o(t,{type:"primary",onClick:Ie,class:"action-btn primary"},{default:i(()=>[o(l,null,{default:i(()=>[o(g(q))]),_:1}),a[8]||(a[8]=d(" 添加用户 ",-1))]),_:1,__:[8]})])])]),c("div",se,[c("div",re,[(u(!0),v(f,null,_(Ce.value,e=>(u(),v("div",{class:"stat-card",key:e.key},[c("div",{class:"stat-icon",style:G({background:e.color})},[o(l,{size:"20"},{default:i(()=>[(u(),r(w(e.icon)))]),_:2},1024)],4),c("div",ue,[c("div",ie,N(e.value),1),c("div",oe,N(e.label),1)]),c("div",{class:H(["stat-trend",e.trend])},[o(l,{size:"14"},{default:i(()=>[(u(),r(w(e.trendIcon)))]),_:2},1024),c("span",null,N(e.change),1)],2)]))),128))])]),c("div",ne,[o(Ee,{class:"filter-card",shadow:"never"},{default:i(()=>[c("div",de,[c("div",ce,[o(m,{modelValue:J.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>J.keyword=e),placeholder:"搜索用户名、邮箱、手机号","prefix-icon":"Search",clearable:"",class:"search-input",onKeyup:b(qe,["enter"])},null,8,["modelValue"]),o(je,{modelValue:J.role,"onUpdate:modelValue":a[1]||(a[1]=e=>J.role=e),placeholder:"用户角色",clearable:"",class:"filter-select"},{default:i(()=>[o(p,{label:"全部角色",value:""}),o(p,{label:"管理员",value:"admin"}),o(p,{label:"分站管理员",value:"substation"}),o(p,{label:"代理商",value:"agent"}),o(p,{label:"分销员",value:"distributor"}),o(p,{label:"群主",value:"group_owner"}),o(p,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"]),o(je,{modelValue:J.status,"onUpdate:modelValue":a[2]||(a[2]=e=>J.status=e),placeholder:"用户状态",clearable:"",class:"filter-select"},{default:i(()=>[o(p,{label:"全部状态",value:""}),o(p,{label:"正常",value:"active"}),o(p,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),c("div",me,[o(t,{onClick:qe,type:"primary",class:"search-btn"},{default:i(()=>[o(l,null,{default:i(()=>[o(g(A))]),_:1}),a[9]||(a[9]=d(" 搜索 ",-1))]),_:1,__:[9]}),o(t,{onClick:Ae,class:"reset-btn"},{default:i(()=>[o(l,null,{default:i(()=>[o(g(L))]),_:1}),a[10]||(a[10]=d(" 重置 ",-1))]),_:1,__:[10]})])])]),_:1})]),c("div",pe,[o(Ee,{class:"table-card",shadow:"never"},{header:i(()=>[c("div",ve,[c("div",ge,[a[11]||(a[11]=c("span",null,"用户列表",-1)),o(Pe,{size:"small",type:"info"},{default:i(()=>[d("共 "+N(T.value)+" 条记录",1)]),_:1})]),c("div",fe,[V.value.length>0?(u(),r(t,{key:0,onClick:De,type:"danger",size:"small",plain:""},{default:i(()=>[o(l,null,{default:i(()=>[o(g(R))]),_:1}),d(" 批量删除 ("+N(V.value.length)+") ",1)]),_:1})):n("",!0)])])]),default:i(()=>[h((u(),r(Ye,{data:y.value,onSelectionChange:Se,class:"modern-table",stripe:"",border:""},{default:i(()=>[o(Qe,{type:"selection",width:"55",align:"center"}),o(Qe,{label:"用户信息","min-width":"200"},{default:i(({row:e})=>[c("div",_e,[o(Re,{size:40,src:e.avatar,class:"user-avatar"},{default:i(()=>[o(l,null,{default:i(()=>[o(g(I))]),_:1})]),_:2},1032,["src"]),c("div",be,[c("div",he,N(e.username),1),c("div",we,N(e.email),1)])])]),_:1}),o(Qe,{label:"真实姓名",prop:"realName",width:"100"}),o(Qe,{label:"手机号码",prop:"phone",width:"120"}),o(Qe,{label:"用户角色",width:"120"},{default:i(({row:e})=>{return[o(Pe,{type:(a=e.role,{admin:"danger",substation:"warning",agent:"primary",distributor:"success",group_owner:"info",user:""}[a]||""),size:"small"},{default:i(()=>[d(N(Ne(e.role)),1)]),_:2},1032,["type"])];var a}),_:1}),o(Qe,{label:"状态",width:"80"},{default:i(({row:e})=>[o(Pe,{type:"active"===e.status?"success":"danger",size:"small"},{default:i(()=>[d(N("active"===e.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),o(Qe,{label:"注册时间",width:"160"},{default:i(({row:e})=>[c("div",ye,[c("div",null,N(Te(e.created_at)),1),c("small",null,N(Be(e.created_at)),1)])]),_:1}),o(Qe,{label:"最后登录",width:"160"},{default:i(({row:e})=>[e.last_login_at?(u(),v("div",Ve,[c("div",null,N(Te(e.last_login_at)),1),c("small",null,N(Be(e.last_login_at)),1)])):(u(),v("span",ke,"从未登录"))]),_:1}),o(Qe,{label:"操作",width:"200",fixed:"right"},{default:i(({row:e})=>[c("div",ze,[o(t,{onClick:a=>(e=>{C.value={...e},x.value=!0})(e),type:"primary",size:"small",plain:""},{default:i(()=>[o(l,null,{default:i(()=>[o(g(Q))]),_:1}),a[12]||(a[12]=d(" 编辑 ",-1))]),_:2,__:[12]},1032,["onClick"]),o(t,{onClick:a=>(async e=>{const a="active"===e.status?"inactive":"active",l="inactive"===a?"禁用":"启用";try{await M.confirm(`确定要${l}该用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status=a,B.success(`${l}成功`)}catch(t){"cancel"!==t&&B.error("操作失败")}})(e),type:"active"===e.status?"warning":"success",size:"small",plain:""},{default:i(()=>[o(l,null,{default:i(()=>[(u(),r(w("active"===e.status?"Lock":"Unlock")))]),_:2},1024),d(" "+N("active"===e.status?"禁用":"启用"),1)]),_:2},1032,["onClick","type"]),o(t,{onClick:a=>(async e=>{try{await M.confirm("确定要删除该用户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=y.value.findIndex(a=>a.id===e.id);a>-1&&(y.value.splice(a,1),T.value--),B.success("删除成功")}catch(a){"cancel"!==a&&B.error("删除失败")}})(e),type:"danger",size:"small",plain:""},{default:i(()=>[o(l,null,{default:i(()=>[o(g(R))]),_:1}),a[13]||(a[13]=d(" 删除 ",-1))]),_:2,__:[13]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Me,s.value]]),c("div",Ue,[o(Ke,{"current-page":xe.page,"onUpdate:currentPage":a[3]||(a[3]=e=>xe.page=e),"page-size":xe.size,"onUpdate:pageSize":a[4]||(a[4]=e=>xe.size=e),total:T.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$e,onCurrentChange:Le,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),o(W,{modelValue:x.value,"onUpdate:modelValue":a[5]||(a[5]=e=>x.value=e),"user-data":C.value,onSuccess:Fe},null,8,["modelValue","user-data"])])}}},[["__scopeId","data-v-c82d02be"]]);export{xe as default};
