<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * 群组成员模型
 */
class GroupMember extends Model
{
    use HasFactory;

    protected $table = 'group_members';

    protected $fillable = [
        'group_id',
        'user_id',
        'status',
        'role',
        'joined_at',
        'last_active_at',
        'inviter_id',
        'invite_code',
        'message_count',
        'contribution_score'
    ];

    protected $casts = [
        'joined_at' => 'datetime',
        'last_active_at' => 'datetime',
        'message_count' => 'integer',
        'contribution_score' => 'decimal:2'
    ];

    // 关联关系

    /**
     * 关联群组
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(WechatGroup::class, 'group_id');
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联邀请人
     */
    public function inviter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inviter_id');
    }

    // 查询作用域

    /**
     * 活跃成员作用域
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * 管理员作用域
     */
    public function scopeAdmins(Builder $query): Builder
    {
        return $query->whereIn('role', ['admin', 'owner']);
    }

    /**
     * 按群组筛选作用域
     */
    public function scopeByGroup(Builder $query, int $groupId): Builder
    {
        return $query->where('group_id', $groupId);
    }

    /**
     * 最近活跃作用域
     */
    public function scopeRecentlyActive(Builder $query, int $days = 7): Builder
    {
        return $query->where('last_active_at', '>=', now()->subDays($days));
    }

    // 访问器

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'active' => '正常',
            'inactive' => '非活跃',
            'banned' => '已禁用'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 获取角色名称
     */
    public function getRoleNameAttribute(): string
    {
        $roles = [
            'member' => '普通成员',
            'admin' => '管理员',
            'owner' => '群主'
        ];

        return $roles[$this->role] ?? $this->role;
    }

    /**
     * 获取加入天数
     */
    public function getJoinedDaysAttribute(): int
    {
        return $this->joined_at->diffInDays(now());
    }

    /**
     * 获取活跃度等级
     */
    public function getActivityLevelAttribute(): string
    {
        if (!$this->last_active_at) {
            return '未知';
        }

        $daysSinceActive = $this->last_active_at->diffInDays(now());

        if ($daysSinceActive <= 1) {
            return '非常活跃';
        } elseif ($daysSinceActive <= 7) {
            return '活跃';
        } elseif ($daysSinceActive <= 30) {
            return '一般';
        } else {
            return '不活跃';
        }
    }

    // 业务方法

    /**
     * 检查是否为管理员
     */
    public function isAdmin(): bool
    {
        return in_array($this->role, ['admin', 'owner']);
    }

    /**
     * 检查是否为群主
     */
    public function isOwner(): bool
    {
        return $this->role === 'owner';
    }

    /**
     * 检查是否活跃
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * 更新最后活跃时间
     */
    public function updateLastActive(): bool
    {
        return $this->update(['last_active_at' => now()]);
    }

    /**
     * 增加消息数量
     */
    public function incrementMessageCount(int $count = 1): bool
    {
        $this->increment('message_count', $count);
        $this->updateLastActive();
        
        return true;
    }

    /**
     * 增加贡献分数
     */
    public function addContributionScore(float $score): bool
    {
        $this->increment('contribution_score', $score);
        
        return true;
    }

    /**
     * 禁用成员
     */
    public function ban(): bool
    {
        return $this->update(['status' => 'banned']);
    }

    /**
     * 解禁成员
     */
    public function unban(): bool
    {
        return $this->update(['status' => 'active']);
    }

    /**
     * 设置为管理员
     */
    public function promoteToAdmin(): bool
    {
        return $this->update(['role' => 'admin']);
    }

    /**
     * 降级为普通成员
     */
    public function demoteToMember(): bool
    {
        return $this->update(['role' => 'member']);
    }

    /**
     * 获取成员统计信息
     */
    public function getStats(): array
    {
        return [
            'joined_days' => $this->joined_days,
            'message_count' => $this->message_count,
            'contribution_score' => $this->contribution_score,
            'activity_level' => $this->activity_level,
            'is_admin' => $this->isAdmin(),
            'last_active_days' => $this->last_active_at ? $this->last_active_at->diffInDays(now()) : null
        ];
    }

    /**
     * 生成邀请码
     */
    public function generateInviteCode(): string
    {
        $code = 'INV' . $this->group_id . $this->user_id . time();
        $this->update(['invite_code' => $code]);
        
        return $code;
    }

    /**
     * 验证邀请码
     */
    public static function validateInviteCode(string $code): ?self
    {
        return static::where('invite_code', $code)->first();
    }
}