<!-- 现代化顶部导航栏组件 -->
<!-- admin/src/components/navigation/NavigationHeader.vue -->

<template>
  <header class="navigation-header modern-header" :class="headerClasses">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- Logo 和品牌 -->
      <div class="header-brand" @click="navigateToHome">
        <div class="brand-logo">
          <transition name="logo-bounce" mode="out-in">
            <el-icon :key="appConfig.mode">
              <component :is="appConfig.brandIcon" />
            </el-icon>
          </transition>
        </div>
        <div class="brand-info" v-if="!isMobile">
          <h1 class="brand-title">{{ appConfig.appName }}</h1>
          <span class="brand-subtitle">{{ appConfig.tagline }}</span>
        </div>
      </div>
      
      <!-- 侧边栏切换按钮 -->
      <el-button 
        class="sidebar-toggle" 
        :class="{ active: !sidebarCollapsed }"
        @click="toggleSidebar"
        type="text"
        size="large"
      >
        <transition name="icon-rotate" mode="out-in">
          <el-icon :key="sidebarCollapsed">
            <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </transition>
      </el-button>
      
      <!-- 面包屑导航 -->
      <nav class="breadcrumb-nav" v-if="!isMobile && breadcrumbs.length">
        <transition-group name="breadcrumb-fade" tag="div" class="breadcrumb-list">
          <div 
            v-for="(crumb, index) in breadcrumbs" 
            :key="crumb.path"
            class="breadcrumb-item"
            :class="{ active: index === breadcrumbs.length - 1 }"
          >
            <router-link 
              :to="crumb.path" 
              class="breadcrumb-link"
              v-if="index < breadcrumbs.length - 1"
            >
              <el-icon v-if="crumb.icon"><component :is="crumb.icon" /></el-icon>
              <span>{{ crumb.title }}</span>
            </router-link>
            <span v-else class="breadcrumb-current">
              <el-icon v-if="crumb.icon"><component :is="crumb.icon" /></el-icon>
              <span>{{ crumb.title }}</span>
            </span>
            <el-icon v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">
              <ArrowRight />
            </el-icon>
          </div>
        </transition-group>
      </nav>
    </div>
    
    <!-- 中央搜索区域 -->
    <div class="header-center" v-if="!isMobile">
      <div class="global-search" :class="{ focused: searchFocused, expanded: searchExpanded }">
        <el-input
          ref="globalSearchInput"
          v-model="globalSearchQuery"
          placeholder="全局搜索：功能、页面、用户..."
          :prefix-icon="Search"
          size="default"
          clearable
          @focus="handleSearchFocus"
          @blur="handleSearchBlur"
          @input="handleGlobalSearch"
          @keydown.enter="handleSearchEnter"
          @keydown.down="searchNavigateDown"
          @keydown.up="searchNavigateUp"
          @keydown.esc="handleSearchEscape"
          class="search-input"
        >
          <template #suffix>
            <div class="search-actions">
              <el-button 
                v-if="globalSearchQuery" 
                type="text" 
                size="small"
                @click="clearSearch"
                class="clear-btn"
              >
                <el-icon><Close /></el-icon>
              </el-button>
              <el-tooltip content="快捷键: Ctrl/Cmd + K">
                <span class="shortcut-hint">⌘K</span>
              </el-tooltip>
            </div>
          </template>
        </el-input>
        
        <!-- 搜索结果面板 -->
        <transition name="search-dropdown-fade">
          <div 
            v-if="showSearchDropdown" 
            class="search-dropdown"
            @mousedown.prevent
          >
            <!-- 搜索建议 -->
            <div v-if="searchSuggestions.length" class="search-section">
              <div class="section-header">
                <el-icon><Search /></el-icon>
                <span>搜索建议</span>
              </div>
              <div class="suggestion-list">
                <div 
                  v-for="(suggestion, index) in searchSuggestions"
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="{ active: selectedSearchIndex === index }"
                  @click="selectSearchSuggestion(suggestion)"
                  @mouseenter="selectedSearchIndex = index"
                >
                  <div class="suggestion-icon">
                    <el-icon><component :is="suggestion.icon" /></el-icon>
                  </div>
                  <div class="suggestion-content">
                    <div class="suggestion-title" v-html="highlightSearchText(suggestion.title)"></div>
                    <div class="suggestion-desc">{{ suggestion.description }}</div>
                  </div>
                  <div class="suggestion-type">{{ suggestion.type }}</div>
                </div>
              </div>
            </div>
            
            <!-- 快速操作 -->
            <div v-if="quickSearchActions.length" class="search-section">
              <div class="section-header">
                <el-icon><Flash /></el-icon>
                <span>快速操作</span>
              </div>
              <div class="quick-actions-list">
                <div 
                  v-for="action in quickSearchActions"
                  :key="action.id"
                  class="quick-action-item"
                  @click="executeQuickAction(action)"
                >
                  <el-icon><component :is="action.icon" /></el-icon>
                  <span>{{ action.title }}</span>
                  <kbd class="action-shortcut" v-if="action.shortcut">{{ action.shortcut }}</kbd>
                </div>
              </div>
            </div>
            
            <!-- 搜索历史 -->
            <div v-if="!globalSearchQuery && searchHistory.length" class="search-section">
              <div class="section-header">
                <el-icon><Clock /></el-icon>
                <span>搜索历史</span>
                <el-button type="text" size="small" @click="clearSearchHistory">清除</el-button>
              </div>
              <div class="history-list">
                <div 
                  v-for="history in searchHistory"
                  :key="history.id"
                  class="history-item"
                  @click="applySearchHistory(history)"
                >
                  <el-icon><Search /></el-icon>
                  <span>{{ history.query }}</span>
                  <small>{{ formatTimeAgo(history.timestamp) }}</small>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>
    
    <!-- 右侧操作区域 -->
    <div class="header-right">
      <!-- 移动端搜索按钮 -->
      <el-button 
        v-if="isMobile" 
        class="mobile-search-btn"
        @click="showMobileSearch = true"
        type="text"
        size="large"
      >
        <el-icon><Search /></el-icon>
      </el-button>
      
      <!-- 主题切换 -->
      <el-tooltip content="切换主题" placement="bottom">
        <el-button 
          class="theme-toggle"
          @click="toggleTheme"
          type="text"
          size="large"
        >
          <transition name="theme-switch" mode="out-in">
            <el-icon :key="currentTheme">
              <component :is="currentTheme === 'dark' ? 'Moon' : 'Sunny'" />
            </el-icon>
          </transition>
        </el-button>
      </el-tooltip>
      
      <!-- 全屏切换 -->
      <el-tooltip content="全屏显示" placement="bottom">
        <el-button 
          class="fullscreen-toggle"
          @click="toggleFullscreen"
          type="text"
          size="large"
        >
          <transition name="fullscreen-switch" mode="out-in">
            <el-icon :key="isFullscreen">
              <component :is="isFullscreen ? 'Fold' : 'FullScreen'" />
            </el-icon>
          </transition>
        </el-button>
      </el-tooltip>
      
      <!-- 通知中心 -->
      <el-dropdown 
        class="notification-dropdown"
        @command="handleNotificationAction"
        placement="bottom-end"
      >
        <el-button class="notification-btn" type="text" size="large">
          <el-badge :value="unreadNotifications" :hidden="unreadNotifications === 0">
            <el-icon><Bell /></el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <div class="notification-panel">
            <div class="notification-header">
              <h3>通知中心</h3>
              <div class="notification-actions">
                <el-button type="text" size="small" @command="markAllRead">全部已读</el-button>
                <el-button type="text" size="small" @command="clearAll">清空</el-button>
              </div>
            </div>
            <div class="notification-list">
              <div 
                v-for="notification in recentNotifications"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon" :class="notification.type">
                  <el-icon><component :is="getNotificationIcon(notification.type)" /></el-icon>
                </div>
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-desc">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTimeAgo(notification.timestamp) }}</div>
                </div>
                <el-button 
                  v-if="!notification.read"
                  type="text" 
                  size="small"
                  @click.stop="markAsRead(notification)"
                  class="mark-read-btn"
                >
                  <el-icon><Check /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="notification-footer">
              <router-link to="/system/notifications" class="view-all-link">
                查看全部通知
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </div>
        </template>
      </el-dropdown>
      
      <!-- 用户菜单 -->
      <el-dropdown 
        class="user-dropdown"
        @command="handleUserAction"
        placement="bottom-end"
      >
        <div class="user-profile">
          <el-avatar 
            :src="userInfo.avatar" 
            :size="32"
            class="user-avatar"
          >
            <img 
              src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" 
              alt="用户头像"
            />
          </el-avatar>
          <div class="user-info" v-if="!isMobile">
            <div class="user-name">{{ userInfo.name || '用户' }}</div>
            <div class="user-role">{{ getRoleText(userInfo.role) }}</div>
          </div>
          <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <div class="user-menu">
            <div class="user-header">
              <el-avatar :src="userInfo.avatar" :size="48">
                <img 
                  src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" 
                  alt="用户头像"
                />
              </el-avatar>
              <div class="user-details">
                <div class="name">{{ userInfo.name || '用户' }}</div>
                <div class="email">{{ userInfo.email }}</div>
                <div class="role">{{ getRoleText(userInfo.role) }}</div>
              </div>
            </div>
            <el-divider />
            <el-dropdown-menu class="user-menu-list">
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                账户设置
              </el-dropdown-item>
              <el-dropdown-item command="security">
                <el-icon><Lock /></el-icon>
                安全设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 移动端搜索弹层 -->
    <el-drawer
      v-model="showMobileSearch"
      direction="ttb"
      size="100%"
      :show-close="false"
      class="mobile-search-drawer"
    >
      <template #header>
        <div class="mobile-search-header">
          <el-button @click="showMobileSearch = false" type="text" size="large">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <span>全局搜索</span>
        </div>
      </template>
      <div class="mobile-search-content">
        <el-input
          v-model="mobileSearchQuery"
          placeholder="搜索功能、页面、用户..."
          :prefix-icon="Search"
          size="large"
          clearable
          @input="handleMobileSearch"
          class="mobile-search-input"
        />
        <div class="mobile-search-results">
          <!-- 移动端搜索结果 -->
        </div>
      </div>
    </el-drawer>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Bell,
  Setting,
  User,
  Close,
  ArrowRight,
  ArrowDown,
  ArrowLeft,
  Check,
  Clock,
  Flash,
  Lock,
  SwitchButton,
  Moon,
  Sunny,
  FullScreen,
  Expand,
  Fold,
  Menu,
  Plus,
  Document,
  DataLine,
  Warning,
  Success,
  InfoFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  sidebarCollapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle-sidebar', 'theme-change'])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const isMobile = ref(false)
const globalSearchQuery = ref('')
const mobileSearchQuery = ref('')
const searchFocused = ref(false)
const searchExpanded = ref(false)
const showSearchDropdown = ref(false)
const showMobileSearch = ref(false)
const searchSuggestions = ref([])
const selectedSearchIndex = ref(-1)
const currentTheme = ref('light')
const isFullscreen = ref(false)
const globalSearchInput = ref(null)

// 应用配置
const appConfig = ref({
  appName: 'LinkHub Pro',
  tagline: '智能导航管理系统',
  brandIcon: 'DataLine',
  mode: 'pro'
})

// 用户信息
const userInfo = computed(() => userStore.userInfo || {})

// 未读通知数
const unreadNotifications = computed(() => {
  return recentNotifications.value.filter(n => !n.read).length
})

// 最近通知
const recentNotifications = ref([
  {
    id: 1,
    type: 'info',
    title: '系统更新',
    message: '新版本已发布，包含多项功能改进',
    timestamp: Date.now() - 300000,
    read: false
  },
  {
    id: 2,
    type: 'warning',
    title: '域名即将过期',
    message: 'example.com 将在7天后到期',
    timestamp: Date.now() - 1800000,
    read: false
  },
  {
    id: 3,
    type: 'success',
    title: '数据备份完成',
    message: '今日数据备份已成功完成',
    timestamp: Date.now() - 3600000,
    read: true
  }
])

// 搜索历史
const searchHistory = ref(JSON.parse(localStorage.getItem('global-search-history') || '[]'))

// 快速搜索操作
const quickSearchActions = computed(() => [
  { id: 'new-group', title: '创建新群组', icon: 'Plus', shortcut: 'Ctrl+N' },
  { id: 'export-data', title: '导出数据', icon: 'Document', shortcut: 'Ctrl+E' },
  { id: 'system-monitor', title: '系统监控', icon: 'DataLine', shortcut: 'Ctrl+M' }
])

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta.title,
    path: item.path,
    icon: item.meta.icon
  }))
})

// 头部样式类
const headerClasses = computed(() => ({
  'mobile': isMobile.value,
  'search-focused': searchFocused.value,
  'sidebar-collapsed': props.sidebarCollapsed
}))

// 方法
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const navigateToHome = () => {
  router.push('/dashboard')
}

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const toggleTheme = () => {
  currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
  document.documentElement.setAttribute('data-theme', currentTheme.value)
  emit('theme-change', currentTheme.value)
}

const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 搜索相关方法
const handleSearchFocus = () => {
  searchFocused.value = true
  showSearchDropdown.value = true
  searchExpanded.value = true
}

const handleSearchBlur = () => {
  setTimeout(() => {
    searchFocused.value = false
    showSearchDropdown.value = false
    searchExpanded.value = false
    selectedSearchIndex.value = -1
  }, 150)
}

const handleGlobalSearch = (query) => {
  if (!query.trim()) {
    searchSuggestions.value = []
    return
  }
  
  // 模拟搜索建议
  searchSuggestions.value = [
    {
      id: 1,
      title: '群组管理',
      description: '管理微信群组',
      type: '功能',
      icon: 'Menu',
      path: '/community/groups'
    },
    {
      id: 2,
      title: '用户分析',
      description: '查看用户数据',
      type: '页面',
      icon: 'DataLine',
      path: '/analytics/users'
    }
  ].filter(item => 
    item.title.toLowerCase().includes(query.toLowerCase()) ||
    item.description.toLowerCase().includes(query.toLowerCase())
  )
}

const handleMobileSearch = (query) => {
  // 移动端搜索逻辑
}

const handleSearchEnter = () => {
  if (selectedSearchIndex.value >= 0 && searchSuggestions.value[selectedSearchIndex.value]) {
    selectSearchSuggestion(searchSuggestions.value[selectedSearchIndex.value])
  }
}

const searchNavigateDown = () => {
  selectedSearchIndex.value = Math.min(selectedSearchIndex.value + 1, searchSuggestions.value.length - 1)
}

const searchNavigateUp = () => {
  selectedSearchIndex.value = Math.max(selectedSearchIndex.value - 1, -1)
}

const handleSearchEscape = () => {
  globalSearchInput.value?.blur()
}

const selectSearchSuggestion = (suggestion) => {
  router.push(suggestion.path)
  addSearchHistory(globalSearchQuery.value)
  clearSearch()
}

const clearSearch = () => {
  globalSearchQuery.value = ''
  searchSuggestions.value = []
  showSearchDropdown.value = false
}

const addSearchHistory = (query) => {
  if (!query.trim()) return
  
  const history = {
    id: Date.now(),
    query,
    timestamp: Date.now()
  }
  
  searchHistory.value.unshift(history)
  searchHistory.value = searchHistory.value.slice(0, 10)
  localStorage.setItem('global-search-history', JSON.stringify(searchHistory.value))
}

const clearSearchHistory = () => {
  searchHistory.value = []
  localStorage.removeItem('global-search-history')
}

const applySearchHistory = (history) => {
  globalSearchQuery.value = history.query
  handleGlobalSearch(history.query)
}

const highlightSearchText = (text) => {
  if (!globalSearchQuery.value) return text
  const regex = new RegExp(`(${globalSearchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 通知相关方法
const handleNotificationAction = (command) => {
  switch (command) {
    case 'markAllRead':
      recentNotifications.value.forEach(n => n.read = true)
      break
    case 'clearAll':
      recentNotifications.value = []
      break
  }
}

const handleNotificationClick = (notification) => {
  if (!notification.read) {
    markAsRead(notification)
  }
  // 导航到相关页面
}

const markAsRead = (notification) => {
  notification.read = true
}

const getNotificationIcon = (type) => {
  const icons = {
    info: 'InfoFilled',
    warning: 'Warning',
    success: 'Success',
    error: 'Warning'
  }
  return icons[type] || 'InfoFilled'
}

// 用户菜单方法
const handleUserAction = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'settings':
      router.push('/user/settings')
      break
    case 'security':
      router.push('/user/security')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        router.push('/login')
        ElMessage.success('已安全退出')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

const getRoleText = (role) => {
  const roles = {
    admin: '系统管理员',
    manager: '管理员',
    user: '普通用户',
    distributor: '分销商'
  }
  return roles[role] || '用户'
}

const executeQuickAction = (action) => {
  switch (action.id) {
    case 'new-group':
      router.push('/community/groups/create')
      break
    case 'export-data':
      router.push('/system/export')
      break
    case 'system-monitor':
      router.push('/system/monitor')
      break
  }
  clearSearch()
}

const formatTimeAgo = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 全屏事件监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 键盘快捷键
const handleKeydown = (e) => {
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    globalSearchInput.value?.focus()
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('keydown', handleKeydown)
  
  // 初始化主题
  const savedTheme = localStorage.getItem('theme') || 'light'
  currentTheme.value = savedTheme
  document.documentElement.setAttribute('data-theme', savedTheme)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.navigation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: var(--z-fixed);
  transition: all var(--duration-normal) var(--ease-out);
  
  &.modern-header {
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
      opacity: 0.3;
    }
  }
  
  &.mobile {
    padding: 0 var(--spacing-md);
  }
}

// 左侧区域
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
  min-width: 0;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    transform: scale(1.02);
    
    .brand-logo {
      background: var(--gradient-primary);
      transform: rotate(5deg);
    }
  }
  
  .brand-logo {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: var(--shadow-md);
    transition: all var(--duration-normal) var(--ease-out);
  }
  
  .brand-info {
    .brand-title {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0;
      background: var(--gradient-primary);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }
    
    .brand-subtitle {
      font-size: var(--text-xs);
      color: var(--text-muted);
      display: block;
      margin-top: 2px;
    }
  }
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
  
  &.active {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
  }
}

.breadcrumb-nav {
  flex: 1;
  min-width: 0;
  
  .breadcrumb-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    overflow: hidden;
  }
  
  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    white-space: nowrap;
    
    .breadcrumb-link {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--text-muted);
      text-decoration: none;
      font-size: var(--text-sm);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--bg-secondary);
        color: var(--color-primary);
      }
    }
    
    .breadcrumb-current {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--text-primary);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .breadcrumb-separator {
      color: var(--text-light);
      font-size: 12px;
    }
  }
}

// 中央搜索区域
.header-center {
  flex: 1;
  max-width: 600px;
  padding: 0 var(--spacing-xl);
}

.global-search {
  position: relative;
  transition: all var(--duration-normal) var(--ease-out);
  
  .search-input {
    .el-input__wrapper {
      background: var(--bg-secondary);
      border: 2px solid var(--border-light);
      border-radius: var(--radius-full);
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        border-color: var(--border-medium);
        background: var(--bg-primary);
      }
      
      &.is-focus {
        border-color: var(--color-primary);
        background: var(--bg-primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
    
    .el-input__inner {
      font-size: var(--text-sm);
      color: var(--text-primary);
      
      &::placeholder {
        color: var(--text-light);
        font-style: italic;
      }
    }
  }
  
  .search-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding-right: var(--spacing-sm);
    
    .clear-btn {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--bg-muted);
      
      &:hover {
        background: var(--color-danger);
        color: white;
      }
    }
    
    .shortcut-hint {
      font-size: 10px;
      color: var(--text-muted);
      background: var(--bg-muted);
      padding: 2px 6px;
      border-radius: var(--radius-sm);
      border: 1px solid var(--border-light);
      font-family: var(--font-family-mono, monospace);
    }
  }
  
  &.focused,
  &.expanded {
    .search-input .el-input__wrapper {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.search-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 400px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
  
  .search-section {
    padding: var(--spacing-md) 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid var(--border-light);
    }
    
    .section-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: 0 var(--spacing-md) var(--spacing-sm);
      font-size: var(--text-xs);
      font-weight: var(--font-semibold);
      color: var(--text-muted);
      text-transform: uppercase;
      
      .el-button {
        margin-left: auto;
        font-size: var(--text-xs);
      }
    }
  }
  
  .suggestion-item,
  .quick-action-item,
  .history-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-out);
    
    &:hover,
    &.active {
      background: var(--bg-secondary);
      transform: translateX(2px);
    }
  }
  
  .suggestion-item {
    .suggestion-icon {
      width: 24px;
      height: 24px;
      background: rgba(59, 130, 246, 0.1);
      color: var(--color-primary);
      border-radius: var(--radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    
    .suggestion-content {
      flex: 1;
      min-width: 0;
      
      .suggestion-title {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-primary);
        
        :deep(mark) {
          background: rgba(59, 130, 246, 0.2);
          color: var(--color-primary);
          padding: 1px 2px;
          border-radius: 2px;
        }
      }
      
      .suggestion-desc {
        font-size: var(--text-xs);
        color: var(--text-muted);
        margin-top: 2px;
      }
    }
    
    .suggestion-type {
      font-size: var(--text-xs);
      padding: 2px 6px;
      background: var(--bg-muted);
      color: var(--text-muted);
      border-radius: var(--radius-sm);
    }
  }
  
  .quick-action-item {
    .el-icon {
      width: 20px;
      height: 20px;
      color: var(--color-success);
    }
    
    span {
      flex: 1;
      font-size: var(--text-sm);
      color: var(--text-primary);
    }
    
    .action-shortcut {
      font-size: 10px;
      padding: 2px 6px;
      background: var(--bg-muted);
      border-radius: var(--radius-sm);
      font-family: var(--font-family-mono, monospace);
    }
  }
  
  .history-item {
    .el-icon {
      color: var(--text-light);
    }
    
    span {
      flex: 1;
      font-size: var(--text-sm);
      color: var(--text-secondary);
    }
    
    small {
      color: var(--text-light);
      font-size: var(--text-xs);
    }
  }
}

// 右侧区域
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.theme-toggle,
.fullscreen-toggle,
.mobile-search-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.notification-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  position: relative;
  
  &:hover {
    background: var(--color-warning);
    border-color: var(--color-warning);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
  
  .el-badge {
    :deep(.el-badge__content) {
      font-size: 10px;
      min-width: 18px;
      height: 18px;
      line-height: 18px;
      animation: bounce 2s infinite;
    }
  }
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    background: var(--bg-secondary);
  }
  
  .user-avatar {
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-md);
    }
  }
  
  .user-info {
    .user-name {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--text-primary);
      line-height: 1.2;
    }
    
    .user-role {
      font-size: var(--text-xs);
      color: var(--text-muted);
      line-height: 1.2;
    }
  }
  
  .dropdown-arrow {
    color: var(--text-light);
    transition: transform var(--duration-normal) var(--ease-out);
  }
  
  &:hover .dropdown-arrow {
    transform: rotate(180deg);
  }
}

// 通知面板
.notification-panel {
  width: 360px;
  max-height: 480px;
  
  .notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    
    h3 {
      margin: 0;
      font-size: var(--text-base);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
    }
    
    .notification-actions {
      display: flex;
      gap: var(--spacing-xs);
    }
  }
  
  .notification-list {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    
    &:hover {
      background: var(--bg-secondary);
    }
    
    &.unread {
      background: rgba(59, 130, 246, 0.02);
      border-left: 3px solid var(--color-primary);
    }
    
    .notification-icon {
      width: 32px;
      height: 32px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--color-primary);
      }
      
      &.warning {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-500);
      }
      
      &.success {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-500);
      }
    }
    
    .notification-content {
      flex: 1;
      min-width: 0;
      
      .notification-title {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-primary);
        line-height: 1.4;
        margin-bottom: 2px;
      }
      
      .notification-desc {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        line-height: 1.4;
        margin-bottom: 4px;
      }
      
      .notification-time {
        font-size: var(--text-xs);
        color: var(--text-light);
      }
    }
    
    .mark-read-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      opacity: 0;
      transition: all var(--duration-normal) var(--ease-out);
      
      &:hover {
        background: var(--color-success);
        color: white;
      }
    }
    
    &:hover .mark-read-btn {
      opacity: 1;
    }
  }
  
  .notification-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-light);
    text-align: center;
    
    .view-all-link {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--color-primary);
      text-decoration: none;
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      
      &:hover {
        color: var(--primary-600);
      }
    }
  }
}

// 用户菜单
.user-menu {
  width: 280px;
  
  .user-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    
    .user-details {
      flex: 1;
      
      .name {
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin-bottom: 2px;
      }
      
      .email {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin-bottom: 2px;
      }
      
      .role {
        font-size: var(--text-xs);
        color: var(--text-muted);
        padding: 2px 8px;
        background: var(--bg-muted);
        border-radius: var(--radius-sm);
        display: inline-block;
      }
    }
  }
  
  .user-menu-list {
    :deep(.el-dropdown-menu__item) {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-lg);
      font-size: var(--text-sm);
      
      .el-icon {
        color: var(--text-muted);
        width: 16px;
        height: 16px;
      }
      
      &:hover .el-icon {
        color: var(--color-primary);
      }
      
      &.is-divided {
        border-top: 1px solid var(--border-light);
      }
    }
  }
}

// 移动端搜索
.mobile-search-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
  }
  
  .mobile-search-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    span {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
    }
  }
  
  .mobile-search-content {
    padding: var(--spacing-lg);
    
    .mobile-search-input {
      margin-bottom: var(--spacing-lg);
      
      .el-input__wrapper {
        border-radius: var(--radius-lg);
        background: var(--bg-secondary);
        border: 2px solid var(--border-light);
      }
    }
  }
}

// 动画
.logo-bounce-enter-active,
.logo-bounce-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.logo-bounce-enter-from,
.logo-bounce-leave-to {
  opacity: 0;
  transform: scale(0.8) rotate(-180deg);
}

.icon-rotate-enter-active,
.icon-rotate-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.icon-rotate-enter-from,
.icon-rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

.theme-switch-enter-active,
.theme-switch-leave-active,
.fullscreen-switch-enter-active,
.fullscreen-switch-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.theme-switch-enter-from,
.theme-switch-leave-to,
.fullscreen-switch-enter-from,
.fullscreen-switch-leave-to {
  opacity: 0;
  transform: rotate(360deg) scale(0.5);
}

.search-dropdown-fade-enter-active,
.search-dropdown-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  transform-origin: top center;
}

.search-dropdown-fade-enter-from,
.search-dropdown-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.breadcrumb-fade-enter-active,
.breadcrumb-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.breadcrumb-fade-enter-from,
.breadcrumb-fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

// 响应式
@media (max-width: 768px) {
  .navigation-header {
    padding: 0 var(--spacing-md);
    
    .header-left {
      gap: var(--spacing-md);
    }
    
    .brand-info {
      display: none;
    }
    
    .breadcrumb-nav {
      display: none;
    }
    
    .header-center {
      display: none;
    }
    
    .user-info {
      display: none;
    }
  }
}
</style>