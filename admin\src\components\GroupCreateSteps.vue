<template>
  <div class="group-create-steps">
    <el-steps :active="currentStep" align-center class="create-steps">
      <el-step title="基础信息" description="群组基本信息"></el-step>
      <el-step title="营销设置" description="营销和展示配置"></el-step>
      <el-step title="内容配置" description="内容和模板设置"></el-step>
      <el-step title="完成创建" description="确认并创建群组"></el-step>
    </el-steps>

    <div class="step-content">
      <!-- 步骤1：基础信息 -->
      <div v-show="currentStep === 0" class="step-panel">
        <h3 class="step-title">
          <el-icon><InfoFilled /></el-icon>
          基础信息
        </h3>
        <el-divider />
        
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="title">
                <el-input 
                  v-model="form.title" 
                  placeholder="请输入群组名称，支持xxx占位符"
                  maxlength="200"
                  show-word-limit
                />
                <div class="form-tip">
                  💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组价格" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群组类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择群组类型" style="width: 300px">
              <el-option label="社区群" value="community" />
              <el-option label="VIP群" value="vip" />
              <el-option label="学习群" value="education" />
              <el-option label="商务群" value="business" />
            </el-select>
          </el-form-item>

          <el-form-item label="群组描述" prop="description">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入群组描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2：营销设置 -->
      <div v-show="currentStep === 1" class="step-panel">
        <h3 class="step-title">
          <el-icon><Promotion /></el-icon>
          营销设置
        </h3>
        <el-divider />

        <el-form :model="form" label-width="120px">
          <!-- 付费后内容配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Document /></el-icon>
                <span>付费后内容</span>
              </div>
            </template>

            <el-form-item label="内容类型">
              <el-radio-group v-model="form.paid_content_type">
                <el-radio value="qr_code">入群二维码</el-radio>
                <el-radio value="image">图片资源</el-radio>
                <el-radio value="link">下载链接</el-radio>
                <el-radio value="document">文档内容</el-radio>
                <el-radio value="video">视频内容</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 二维码配置 -->
            <div v-if="form.paid_content_type === 'qr_code'">
              <el-form-item label="入群二维码">
                <el-upload
                  class="qr-uploader"
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :show-file-list="false"
                  :on-success="handleQRSuccess"
                  :before-upload="beforeUpload"
                >
                  <img v-if="form.qr_code" :src="form.qr_code" class="qr-image">
                  <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </div>

            <!-- 链接配置 -->
            <div v-if="form.paid_content_type === 'link'">
              <el-form-item label="下载链接">
                <el-input v-model="form.paid_link" placeholder="请输入下载链接" />
              </el-form-item>
              <el-form-item label="链接描述">
                <el-input v-model="form.paid_link_desc" placeholder="请输入链接描述" />
              </el-form-item>
            </div>

            <!-- 文档内容配置 -->
            <div v-if="form.paid_content_type === 'document'">
              <el-form-item label="文档内容">
                <el-input
                  v-model="form.paid_document_content"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入文档内容"
                />
              </el-form-item>
            </div>
          </el-card>

          <!-- 城市定位配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Location /></el-icon>
                <span>城市定位</span>
                <el-switch
                  v-model="form.auto_city_replace"
                  class="header-switch"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="关闭"
                />
              </div>
            </template>

            <div v-if="form.auto_city_replace">
              <el-form-item label="插入策略">
                <el-select v-model="form.city_insert_strategy" placeholder="选择策略" style="width: 200px">
                  <el-option label="自动替换xxx" value="auto" />
                  <el-option label="前缀插入" value="prefix" />
                  <el-option label="后缀插入" value="suffix" />
                </el-select>
              </el-form-item>

              <el-form-item label="测试替换">
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-input v-model="testCity" placeholder="输入城市名" />
                  </el-col>
                  <el-col :span="8">
                    <el-button @click="testCityReplacement">测试</el-button>
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="testResult" placeholder="替换结果" readonly />
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
          </el-card>

          <!-- 营销展示配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><DataAnalysis /></el-icon>
                <span>营销展示</span>
              </div>
            </template>

            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="阅读数显示">
                  <el-input v-model="form.read_count_display" placeholder="如：10万+" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="点赞数">
                  <el-input-number v-model="form.like_count" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="想看数">
                  <el-input-number v-model="form.want_see_count" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="入群按钮文案">
                  <el-input v-model="form.button_title" placeholder="如：立即加入群聊" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="头像库选择">
                  <el-select v-model="form.avatar_library" style="width: 100%">
                    <el-option label="QQ头像库" value="qq" />
                    <el-option label="微信头像库" value="wechat" />
                    <el-option label="随机头像库" value="random" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 虚拟数据配置 -->
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="虚拟成员数">
                  <el-input-number v-model="form.virtual_members" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="虚拟订单数">
                  <el-input-number v-model="form.virtual_orders" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="今日浏览量">
                  <el-input-number v-model="form.today_views" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
      </div>

      <!-- 步骤3：内容配置 -->
      <div v-show="currentStep === 2" class="step-panel">
        <h3 class="step-title">
          <el-icon><Document /></el-icon>
          内容配置
        </h3>
        <el-divider />

        <el-form :model="form" label-width="120px">
          <!-- 智能内容助手 -->
          <el-card class="config-section ai-assistant-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Star /></el-icon>
                <span>智能内容助手</span>
                <el-button
                  type="text"
                  @click="showAIAssistant = !showAIAssistant"
                  class="toggle-button"
                >
                  {{ showAIAssistant ? '收起' : '展开' }}
                  <el-icon>
                    <component :is="showAIAssistant ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                </el-button>
              </div>
            </template>

            <el-collapse-transition>
              <div v-show="showAIAssistant" class="ai-assistant-content">
                <!-- AI工具栏 -->
                <div class="ai-tools-bar">
                  <el-button-group>
                    <el-button @click="showAIGenerator = !showAIGenerator" type="primary">
                      <el-icon><MagicStick /></el-icon>
                      AI生成助手
                    </el-button>
                    <el-button @click="showTemplateLibrary = !showTemplateLibrary" type="success">
                      <el-icon><Collection /></el-icon>
                      模板库
                    </el-button>
                    <el-button @click="analyzeContent" type="info">
                      <el-icon><TrendCharts /></el-icon>
                      内容分析
                    </el-button>
                    <el-button @click="optimizeContent" type="warning">
                      <el-icon><TrendCharts /></el-icon>
                      智能优化
                    </el-button>
                  </el-button-group>
                </div>

                <!-- AI内容生成器 -->
                <el-collapse-transition>
                  <div v-show="showAIGenerator" class="ai-generator-panel">
                    <AIContentGenerator @content-generated="handleAIGenerated" />
                  </div>
                </el-collapse-transition>

                <!-- 模板库 -->
                <el-collapse-transition>
                  <div v-show="showTemplateLibrary" class="template-library-panel">
                    <ContentTemplateLibrary @template-selected="handleTemplateSelected" />
                  </div>
                </el-collapse-transition>
              </div>
            </el-collapse-transition>
          </el-card>

          <!-- 内容管理 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Document /></el-icon>
                <span>内容管理</span>
              </div>
            </template>

            <el-form-item label="群简介标题">
              <el-input v-model="form.group_intro_title" placeholder="群简介" />
            </el-form-item>

            <el-form-item label="群简介内容">
              <el-input
                v-model="form.group_intro_content"
                type="textarea"
                :rows="4"
                placeholder="请输入群简介内容"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="FAQ标题">
              <el-input v-model="form.faq_title" placeholder="常见问题" />
            </el-form-item>

            <el-form-item label="FAQ内容">
              <el-input
                v-model="form.faq_content"
                type="textarea"
                :rows="4"
                placeholder="请输入常见问题内容"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="群友评价">
              <el-input
                v-model="form.member_reviews"
                type="textarea"
                :rows="3"
                placeholder="请输入群友评价内容"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-card>

          <!-- 客服信息配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Service /></el-icon>
                <span>客服信息</span>
                <el-switch
                  v-model="form.show_customer_service"
                  class="header-switch"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="关闭"
                />
              </div>
            </template>

            <div v-if="form.show_customer_service">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="客服标题">
                    <el-input v-model="form.customer_service_title" placeholder="联系客服" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客服描述">
                    <el-input v-model="form.customer_service_desc" placeholder="有问题请联系客服" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="客服头像">
                    <el-upload
                      class="avatar-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeUpload"
                    >
                      <img v-if="form.customer_service_avatar" :src="form.customer_service_avatar" class="avatar">
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客服二维码">
                    <el-upload
                      class="qr-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleServiceQRSuccess"
                      :before-upload="beforeUpload"
                    >
                      <img v-if="form.customer_service_qr" :src="form.customer_service_qr" class="qr-image">
                      <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </div>

      <!-- 步骤4：完成创建 -->
      <div v-show="currentStep === 3" class="step-panel">
        <h3 class="step-title">
          <el-icon><Check /></el-icon>
          完成创建
        </h3>
        <el-divider />

        <div class="summary-content">
          <el-descriptions title="群组信息预览" :column="2" border>
            <el-descriptions-item label="群组名称">{{ form.title }}</el-descriptions-item>
            <el-descriptions-item label="群组价格">¥{{ form.price }}</el-descriptions-item>
            <el-descriptions-item label="群组类型">{{ getTypeLabel(form.type) }}</el-descriptions-item>
            <el-descriptions-item label="城市定位">{{ form.auto_city_replace ? '已启用' : '未启用' }}</el-descriptions-item>
            <el-descriptions-item label="阅读数">{{ form.read_count_display }}</el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ form.like_count }}</el-descriptions-item>
            <el-descriptions-item label="入群按钮">{{ form.button_title }}</el-descriptions-item>
            <el-descriptions-item label="头像库">{{ getAvatarLibraryLabel(form.avatar_library) }}</el-descriptions-item>
          </el-descriptions>

          <div class="summary-description">
            <h4>群组描述：</h4>
            <p>{{ form.description || '暂无描述' }}</p>
          </div>

          <div class="summary-intro">
            <h4>群简介：</h4>
            <p>{{ form.group_intro_content || '暂无群简介' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤控制按钮 -->
    <div class="step-controls">
      <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
      <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="currentStep === 3" type="success" @click="handleSubmit" :loading="submitting">
        <el-icon><Check /></el-icon>
        创建群组
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  InfoFilled, Promotion, Location, DataAnalysis, Document, Check, Plus, Service,
  Star, MagicStick, Collection, TrendCharts, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import { createGroup } from '@/api/community'
import AIContentGenerator from '@/components/AIContentGenerator.vue'
import ContentTemplateLibrary from '@/components/ContentTemplateLibrary.vue'
import {
  analyzeContent as analyzeContentAPI,
  optimizeContent as optimizeContentAPI
} from '@/api/ai-content'

// Props定义
const props = defineProps({
  // 用户角色
  userRole: {
    type: String,
    default: 'owner'
  },
  // 默认值配置
  defaultValues: {
    type: Object,
    default: () => ({})
  }
})

// Emits定义
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const formRef = ref()
const currentStep = ref(0)
const submitting = ref(false)
const testCity = ref('北京')
const testResult = ref('')

// AI功能相关状态
const showAIAssistant = ref(false)
const showAIGenerator = ref(false)
const showTemplateLibrary = ref(false)

// 表单数据（基于增强版群组创建的完整功能）
const form = reactive({
  // 基础信息
  title: '',
  price: 0,
  payment_methods: ['wechat', 'alipay'],
  type: 'community',
  status: 'active',
  description: '',

  // 付费后内容配置
  paid_content_type: 'qr_code',
  qr_code: '',
  paid_images: [],
  paid_link: '',
  paid_link_desc: '',
  paid_document_content: '',
  paid_video_url: '',
  paid_video_title: '',
  paid_video_desc: '',

  // 城市定位配置
  auto_city_replace: 1,
  city_insert_strategy: 'auto',

  // 营销展示配置
  read_count_display: '8万+',
  like_count: 1500,
  want_see_count: 1000,
  button_title: '加入学习群',
  avatar_library: 'qq',
  display_type: 1,
  wx_accessible: 1,

  // 内容配置
  group_intro_title: '群简介',
  group_intro_content: '',
  faq_title: '常见问题',
  faq_content: '',
  member_reviews: '',

  // 虚拟数据配置
  virtual_members: 100,
  virtual_orders: 50,
  virtual_income: 5000.00,
  today_views: 1200,
  show_virtual_activity: 1,
  show_member_avatars: 1,
  show_member_reviews: 1,

  // 客服配置
  show_customer_service: 1,
  customer_service_title: '',
  customer_service_desc: '',
  customer_service_avatar: '',
  customer_service_qr: '',
  ad_qr_code: '',

  ...props.defaultValues
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入群组描述', trigger: 'blur' }
  ]
}

// 方法
const nextStep = async () => {
  if (currentStep.value === 0) {
    try {
      await formRef.value.validate()
      currentStep.value++
    } catch (error) {
      ElMessage.warning('请完善基础信息')
    }
  } else {
    currentStep.value++
  }
}

const prevStep = () => {
  currentStep.value--
}

const testCityReplacement = () => {
  if (!form.title || !testCity.value) {
    ElMessage.warning('请输入群组名称和测试城市')
    return
  }

  testResult.value = form.title.replace(/xxx/g, testCity.value)
  ElMessage.success('城市替换测试完成')
}

// 上传相关配置
const uploadUrl = computed(() => '/api/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}))

// 上传处理方法
const beforeUpload = (file) => {
  const isImage = file.type.indexOf('image/') === 0
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleQRSuccess = (response) => {
  form.qr_code = response.url
  ElMessage.success('二维码上传成功')
}

const handleAvatarSuccess = (response) => {
  form.customer_service_avatar = response.url
  ElMessage.success('头像上传成功')
}

const handleServiceQRSuccess = (response) => {
  form.customer_service_qr = response.url
  ElMessage.success('客服二维码上传成功')
}

const getTypeLabel = (type) => {
  const typeMap = {
    community: '社区群',
    vip: 'VIP群',
    education: '学习群',
    business: '商务群'
  }
  return typeMap[type] || type
}

const getAvatarLibraryLabel = (library) => {
  const libraryMap = {
    qq: 'QQ头像库',
    wechat: '微信头像库',
    random: '随机头像库'
  }
  return libraryMap[library] || library
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      ...form,
      user_role: props.userRole
    }
    
    // 调用API创建群组
    const response = await createGroup(submitData)
    
    if (response.code === 200) {
      ElMessage.success('群组创建成功！')
      emit('success', response.data)
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建群组失败:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// AI功能方法
const analyzeContent = async () => {
  if (!form.title && !form.description && !form.group_intro_content) {
    ElMessage.warning('请先填写一些内容再进行分析')
    return
  }

  try {
    ElMessage.info('正在分析内容，请稍候...')

    // 构建分析数据
    const analysisData = {
      content_type: 'comprehensive',
      include_suggestions: true,
      current_content: {
        title: form.title || '',
        description: form.description || '',
        intro_content: form.group_intro_content || '',
        faq_content: form.faq_content || '',
        reviews: form.member_reviews || '',
        price: form.price || 0
      }
    }

    // 使用临时ID进行分析（创建时还没有真实ID）
    const tempId = Date.now()
    const result = await analyzeContentAPI(tempId, analysisData)

    ElMessage.success('内容分析完成')

    // 显示分析结果
    const analysisReport = result.data || {}
    const reportText = `
📊 内容分析报告

🎯 质量评分：${analysisReport.quality_score || 0}/100
📈 完整度：${analysisReport.completeness || 0}%
✨ 吸引力：${analysisReport.attractiveness || 0}/10
🔥 转化潜力：${analysisReport.conversion_potential || 0}/10

💡 主要建议：
${(analysisReport.suggestions || ['暂无建议']).join('\n')}
    `.trim()

    ElMessageBox.alert(reportText, '内容分析报告', {
      type: 'info',
      customClass: 'analysis-report-dialog'
    })

  } catch (error) {
    console.error('内容分析失败:', error)
    ElMessage.error(`内容分析失败：${error.message || '请重试'}`)
  }
}

const optimizeContent = async () => {
  if (!form.title && !form.description && !form.group_intro_content) {
    ElMessage.warning('请先填写一些内容再进行优化')
    return
  }

  try {
    ElMessage.info('正在生成优化建议，请稍候...')

    // 构建当前内容数据
    const currentContent = {
      title: form.title || '',
      description: form.description || '',
      intro_content: form.group_intro_content || '',
      faq_content: form.faq_content || '',
      reviews: form.member_reviews || '',
      price: form.price || 0,
      type: form.type || 'community'
    }

    // 使用临时ID进行优化
    const tempId = Date.now()
    const result = await optimizeContentAPI(tempId, {
      current_content: currentContent,
      optimization_type: 'comprehensive',
      focus_areas: ['conversion', 'engagement', 'clarity']
    })

    ElMessage.success('优化建议生成完成')

    // 显示优化建议
    const optimizationData = result.data || {}
    const suggestions = optimizationData.suggestions || []
    const optimizedContent = optimizationData.optimized_content || {}

    if (suggestions.length === 0) {
      ElMessage.info('当前内容已经很优秀，暂无优化建议')
      return
    }

    const suggestionText = `
🚀 智能优化建议

${suggestions.map((suggestion, index) => `${index + 1}. ${suggestion}`).join('\n')}

💡 优化后预期效果：
• 转化率提升：${optimizationData.conversion_improvement || '10-20'}%
• 用户参与度提升：${optimizationData.engagement_improvement || '15-25'}%

是否应用这些优化建议？
    `.trim()

    ElMessageBox.confirm(suggestionText, '智能优化建议', {
      type: 'info',
      confirmButtonText: '应用优化',
      cancelButtonText: '暂不应用'
    }).then(() => {
      // 应用优化建议
      if (optimizedContent && Object.keys(optimizedContent).length > 0) {
        if (optimizedContent.title) form.title = optimizedContent.title
        if (optimizedContent.description) form.description = optimizedContent.description
        if (optimizedContent.intro_content) form.group_intro_content = optimizedContent.intro_content
        if (optimizedContent.faq_content) form.faq_content = optimizedContent.faq_content
        if (optimizedContent.reviews) form.member_reviews = optimizedContent.reviews

        ElMessage.success('优化建议已应用到表单')
      }
    }).catch(() => {
      ElMessage.info('已取消应用优化建议')
    })

  } catch (error) {
    console.error('内容优化失败:', error)
    ElMessage.error(`内容优化失败：${error.message || '请重试'}`)
  }
}

const handleAIGenerated = (generatedContent) => {
  console.log('AI生成的内容:', generatedContent)

  if (!generatedContent) {
    ElMessage.warning('未收到生成的内容')
    return
  }

  try {
    // 根据内容类型应用到相应字段
    if (generatedContent.type === 'title' && generatedContent.content) {
      form.title = generatedContent.content
      ElMessage.success(`群组标题已更新：${generatedContent.content.substring(0, 20)}...`)

    } else if (generatedContent.type === 'description' && generatedContent.content) {
      form.description = generatedContent.content
      ElMessage.success('群组描述已更新')

    } else if (generatedContent.type === 'intro' && generatedContent.content) {
      form.group_intro_content = generatedContent.content
      ElMessage.success('群组介绍已更新')

    } else if (generatedContent.type === 'faq' && generatedContent.content) {
      form.faq_content = generatedContent.content
      ElMessage.success('FAQ内容已更新')

    } else if (generatedContent.type === 'reviews' && generatedContent.content) {
      form.member_reviews = generatedContent.content
      ElMessage.success('用户评论已更新')

    } else {
      ElMessage.warning('未识别的内容类型或内容为空')
      return
    }

    // 成功应用后询问是否继续生成
    ElMessageBox.confirm(
      '内容已成功应用到表单，是否继续生成其他内容？',
      '应用成功',
      {
        confirmButtonText: '继续生成',
        cancelButtonText: '关闭生成器',
        type: 'success'
      }
    ).catch(() => {
      showAIGenerator.value = false
    })

  } catch (error) {
    console.error('应用AI内容失败:', error)
    ElMessage.error(`应用AI内容失败：${error.message || '未知错误'}`)
  }
}

const handleTemplateSelected = (template) => {
  console.log('选中的模板:', template)

  if (!template) {
    ElMessage.warning('未选择模板')
    return
  }

  try {
    // 显示模板预览和确认对话框
    const templatePreview = `
📋 模板信息
名称：${template.title || '未命名模板'}
分类：${template.category || '通用'}
描述：${template.description || '无描述'}

📝 包含内容：
${template.content?.title ? '✅ 群组标题' : '❌ 群组标题'}
${template.content?.description ? '✅ 群组描述' : '❌ 群组描述'}
${template.content?.intro_content ? '✅ 群组介绍' : '❌ 群组介绍'}
${template.content?.faq_content ? '✅ FAQ内容' : '❌ FAQ内容'}
${template.content?.reviews ? '✅ 用户评论' : '❌ 用户评论'}

⚠️ 应用模板将覆盖当前内容，是否继续？
    `.trim()

    ElMessageBox.confirm(templatePreview, '确认应用模板', {
      confirmButtonText: '应用模板',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 应用模板内容到表单
      if (template.content) {
        const templateContent = typeof template.content === 'string'
          ? JSON.parse(template.content)
          : template.content

        let appliedItems = []

        if (templateContent.title) {
          form.title = templateContent.title
          appliedItems.push('标题')
        }
        if (templateContent.description) {
          form.description = templateContent.description
          appliedItems.push('描述')
        }
        if (templateContent.intro_content) {
          form.group_intro_content = templateContent.intro_content
          appliedItems.push('介绍')
        }
        if (templateContent.faq_content) {
          form.faq_content = templateContent.faq_content
          appliedItems.push('FAQ')
        }
        if (templateContent.reviews) {
          form.member_reviews = templateContent.reviews
          appliedItems.push('评论')
        }
        if (templateContent.price !== undefined) {
          form.price = templateContent.price
          appliedItems.push('价格')
        }

        ElMessage.success(`模板"${template.title}"已应用，包含：${appliedItems.join('、')}`)
        showTemplateLibrary.value = false
      }
    }).catch(() => {
      ElMessage.info('已取消应用模板')
    })

  } catch (error) {
    console.error('应用模板失败:', error)
    ElMessage.error(`应用模板失败：${error.message || '解析模板内容出错'}`)
  }
}
</script>

<style lang="scss" scoped>
.group-create-steps {
  .create-steps {
    margin-bottom: 30px;
  }
  
  .step-content {
    min-height: 400px;
  }
  
  .step-panel {
    .step-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .config-section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;

        .header-switch {
          margin-left: auto;
        }

        .toggle-button {
          margin-left: auto;
          font-size: 12px;

          .el-icon {
            margin-left: 4px;
          }
        }
      }
    }

    // AI助手相关样式
    .ai-assistant-section {
      border: 2px solid #e1f5fe;
      background: linear-gradient(135deg, #f8fdff 0%, #e8f8ff 100%);

      .section-header {
        color: #0277bd;
      }
    }

    .ai-assistant-content {
      .ai-tools-bar {
        margin-bottom: 20px;
        padding: 16px;
        background: #f0f9ff;
        border-radius: 8px;
        border: 1px solid #bae6fd;

        .el-button-group {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .el-button {
            margin: 0;
            font-size: 13px;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .ai-generator-panel,
      .template-library-panel {
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
      }
    }
    
    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .summary-content {
      .summary-description,
      .summary-intro {
        margin-top: 20px;
        
        h4 {
          margin: 0 0 8px 0;
          color: #1f2937;
        }
        
        p {
          margin: 0;
          color: #4b5563;
          line-height: 1.6;
        }
      }
    }
  }
  
  .step-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }

  /* 上传组件样式 */
  .qr-uploader, .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .qr-uploader .el-upload {
    width: 120px;
    height: 120px;
  }

  .avatar-uploader .el-upload {
    width: 80px;
    height: 80px;
  }

  .qr-image, .avatar {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }

  .qr-uploader-icon, .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .group-create-steps {
    .step-controls {
      flex-wrap: wrap;
      gap: 8px;
      
      .el-button {
        flex: 1;
        min-width: 80px;
      }
    }
  }

  // AI功能对话框样式
  :deep(.analysis-report-dialog) {
    .el-message-box__message {
      white-space: pre-line;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.6;
    }
  }

  :deep(.optimization-dialog) {
    .el-message-box__message {
      white-space: pre-line;
      font-size: 14px;
      line-height: 1.6;
    }
  }
}
</style>
