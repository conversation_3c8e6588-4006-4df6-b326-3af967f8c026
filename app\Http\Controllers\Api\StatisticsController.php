<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\Order;
use App\Models\CommissionLog;
use App\Models\Withdrawal;
use App\Services\AnalyticsServiceMerged;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 统计数据控制器
 * 提供各种业务统计和分析数据
 */
class StatisticsController extends Controller
{
    protected AnalyticsServiceMerged $analyticsService;

    public function __construct(AnalyticsServiceMerged $analyticsService)
    {
        $this->middleware('auth:api');
        $this->middleware('role:admin');
        $this->analyticsService = $analyticsService;
    }

    /**
     * 综合统计概览
     */
    public function overview(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $cacheKey = "statistics_overview_{$period}_" . now()->format('Y-m-d-H');
            
            $data = Cache::remember($cacheKey, 1800, function () use ($period) {
                $startDate = $this->getStartDate($period);
                
                return [
                    'summary' => $this->getSummaryStats($startDate),
                    'trends' => $this->getTrendStats($startDate),
                    'comparisons' => $this->getComparisonStats($startDate),
                    'top_performers' => $this->getTopPerformers($startDate),
                    'growth_rates' => $this->getGrowthRates($startDate),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => '统计概览获取成功',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '统计概览获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 用户统计
     */
    public function userStats(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = $this->getStartDate($period);

            $stats = [
                'overview' => [
                    'total_users' => User::count(),
                    'new_users' => User::where('created_at', '>=', $startDate)->count(),
                    'active_users' => User::where('status', User::STATUS_ACTIVE)->count(),
                    'verified_users' => User::whereNotNull('phone_verified_at')->count(),
                    'distributors' => User::role('distributor')->count(),
                    'substations' => User::role('substation')->count(),
                ],
                'registration_trend' => $this->getUserRegistrationTrend($startDate),
                'activity_analysis' => $this->getUserActivityAnalysis($startDate),
                'role_distribution' => $this->getUserRoleDistribution(),
                'geographic_distribution' => $this->getUserGeographicDistribution(),
                'engagement_metrics' => $this->getUserEngagementMetrics($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '用户统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 群组统计
     */
    public function groupStats(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = $this->getStartDate($period);

            $stats = [
                'overview' => [
                    'total_groups' => WechatGroup::count(),
                    'new_groups' => WechatGroup::where('created_at', '>=', $startDate)->count(),
                    'active_groups' => WechatGroup::where('status', 1)->count(),
                    'paid_groups' => WechatGroup::where('price', '>', 0)->count(),
                    'free_groups' => WechatGroup::where('price', 0)->count(),
                ],
                'creation_trend' => $this->getGroupCreationTrend($startDate),
                'category_distribution' => $this->getGroupCategoryDistribution(),
                'price_analysis' => $this->getGroupPriceAnalysis(),
                'popularity_ranking' => $this->getGroupPopularityRanking($startDate),
                'performance_metrics' => $this->getGroupPerformanceMetrics($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '群组统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 订单统计
     */
    public function orderStats(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = $this->getStartDate($period);

            $stats = [
                'overview' => [
                    'total_orders' => Order::count(),
                    'new_orders' => Order::where('created_at', '>=', $startDate)->count(),
                    'paid_orders' => Order::where('status', Order::STATUS_PAID_INT)->count(),
                    'pending_orders' => Order::where('status', Order::STATUS_PENDING_INT)->count(),
                    'cancelled_orders' => Order::where('status', Order::STATUS_CANCELLED_INT)->count(),
                    'refunded_orders' => Order::where('status', Order::STATUS_REFUNDED_INT)->count(),
                ],
                'order_trend' => $this->getOrderTrend($startDate),
                'payment_analysis' => $this->getPaymentAnalysis($startDate),
                'conversion_funnel' => $this->getConversionFunnel($startDate),
                'average_metrics' => $this->getOrderAverageMetrics($startDate),
                'refund_analysis' => $this->getRefundAnalysis($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 收入统计
     */
    public function revenueStats(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = $this->getStartDate($period);

            $stats = [
                'overview' => [
                    'total_revenue' => Order::where('status', Order::STATUS_PAID_INT)->sum('amount'),
                    'period_revenue' => Order::where('status', Order::STATUS_PAID_INT)
                                           ->where('paid_at', '>=', $startDate)->sum('amount'),
                    'commission_paid' => CommissionLog::where('status', 'settled')->sum('amount'),
                    'withdrawal_paid' => Withdrawal::where('status', 'completed')->sum('amount'),
                    'net_profit' => $this->calculateNetProfit($startDate),
                ],
                'revenue_trend' => $this->getRevenueTrend($startDate),
                'revenue_sources' => $this->getRevenueSources($startDate),
                'profit_analysis' => $this->getProfitAnalysis($startDate),
                'cost_breakdown' => $this->getCostBreakdown($startDate),
                'forecasting' => $this->getRevenueForecasting($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '收入统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 分销商统计
     */
    public function distributorStats(Request $request)
    {
        try {
            $period = $request->input('period', '30d');
            $startDate = $this->getStartDate($period);

            $stats = [
                'overview' => [
                    'total_distributors' => User::role('distributor')->count(),
                    'active_distributors' => $this->getActiveDistributors($startDate),
                    'new_distributors' => User::role('distributor')
                                             ->where('created_at', '>=', $startDate)->count(),
                    'top_performers' => $this->getTopDistributors($startDate),
                ],
                'performance_analysis' => $this->getDistributorPerformance($startDate),
                'commission_distribution' => $this->getCommissionDistribution($startDate),
                'recruitment_analysis' => $this->getRecruitmentAnalysis($startDate),
                'level_progression' => $this->getLevelProgression($startDate),
                'retention_analysis' => $this->getDistributorRetention($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '分销商统计获取失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 导出统计数据
     */
    public function exportStats(Request $request)
    {
        try {
            $type = $request->input('type', 'overview');
            $period = $request->input('period', '30d');
            $format = $request->input('format', 'excel');

            $data = match($type) {
                'users' => $this->userStats($request)->getData()->data,
                'groups' => $this->groupStats($request)->getData()->data,
                'orders' => $this->orderStats($request)->getData()->data,
                'revenue' => $this->revenueStats($request)->getData()->data,
                'distributors' => $this->distributorStats($request)->getData()->data,
                default => $this->overview($request)->getData()->data,
            };

            // 这里可以集成导出服务
            $filename = "statistics_{$type}_{$period}_" . now()->format('Y_m_d_H_i_s');
            
            return response()->json([
                'success' => true,
                'message' => '统计数据导出成功',
                'data' => [
                    'download_url' => "/api/v1/export/download/{$filename}.{$format}",
                    'filename' => "{$filename}.{$format}",
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '统计数据导出失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取开始日期
     */
    private function getStartDate(string $period): Carbon
    {
        return match($period) {
            '1d' => now()->startOfDay(),
            '7d' => now()->subDays(7),
            '30d' => now()->subDays(30),
            '90d' => now()->subDays(90),
            '1y' => now()->subYear(),
            default => now()->subDays(30)
        };
    }

    /**
     * 获取汇总统计
     */
    private function getSummaryStats($startDate): array
    {
        return [
            'users' => [
                'total' => User::count(),
                'new' => User::where('created_at', '>=', $startDate)->count(),
                'active' => User::where('status', User::STATUS_ACTIVE)->count(),
            ],
            'groups' => [
                'total' => WechatGroup::count(),
                'new' => WechatGroup::where('created_at', '>=', $startDate)->count(),
                'active' => WechatGroup::where('status', 1)->count(),
            ],
            'orders' => [
                'total' => Order::count(),
                'new' => Order::where('created_at', '>=', $startDate)->count(),
                'paid' => Order::where('status', Order::STATUS_PAID_INT)->count(),
            ],
            'revenue' => [
                'total' => Order::where('status', Order::STATUS_PAID_INT)->sum('amount'),
                'period' => Order::where('status', Order::STATUS_PAID_INT)
                               ->where('paid_at', '>=', $startDate)->sum('amount'),
            ],
        ];
    }

    /**
     * 获取趋势统计
     */
    private function getTrendStats($startDate): array
    {
        $days = now()->diffInDays($startDate);
        $trends = [];
        
        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dateStr = $date->format('Y-m-d');
            
            $trends[] = [
                'date' => $dateStr,
                'users' => User::whereDate('created_at', $date)->count(),
                'groups' => WechatGroup::whereDate('created_at', $date)->count(),
                'orders' => Order::whereDate('created_at', $date)->count(),
                'revenue' => Order::where('status', Order::STATUS_PAID_INT)
                                 ->whereDate('paid_at', $date)->sum('amount'),
            ];
        }
        
        return $trends;
    }

    /**
     * 获取对比统计
     */
    private function getComparisonStats($startDate): array
    {
        $previousPeriod = $startDate->copy()->subDays(now()->diffInDays($startDate));
        
        $current = [
            'users' => User::where('created_at', '>=', $startDate)->count(),
            'groups' => WechatGroup::where('created_at', '>=', $startDate)->count(),
            'orders' => Order::where('created_at', '>=', $startDate)->count(),
            'revenue' => Order::where('status', Order::STATUS_PAID_INT)
                             ->where('paid_at', '>=', $startDate)->sum('amount'),
        ];
        
        $previous = [
            'users' => User::whereBetween('created_at', [$previousPeriod, $startDate])->count(),
            'groups' => WechatGroup::whereBetween('created_at', [$previousPeriod, $startDate])->count(),
            'orders' => Order::whereBetween('created_at', [$previousPeriod, $startDate])->count(),
            'revenue' => Order::where('status', Order::STATUS_PAID_INT)
                             ->whereBetween('paid_at', [$previousPeriod, $startDate])->sum('amount'),
        ];
        
        $growth = [];
        foreach ($current as $key => $value) {
            $prevValue = $previous[$key] ?? 0;
            if ($prevValue > 0) {
                $growth[$key] = round((($value - $prevValue) / $prevValue) * 100, 2);
            } else {
                $growth[$key] = $value > 0 ? 100 : 0;
            }
        }
        
        return [
            'current' => $current,
            'previous' => $previous,
            'growth' => $growth,
        ];
    }

    /**
     * 获取顶级表现者
     */
    private function getTopPerformers($startDate): array
    {
        return [
            'top_groups' => WechatGroup::withCount(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get(),
            
            'top_users' => User::withCount(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get(),
        ];
    }

    /**
     * 获取增长率
     */
    private function getGrowthRates($startDate): array
    {
        // 实现增长率计算逻辑
        return [
            'user_growth_rate' => 0,
            'revenue_growth_rate' => 0,
            'order_growth_rate' => 0,
        ];
    }

    // 其他私有方法的实现...
    private function getUserRegistrationTrend($startDate): array
    {
        return User::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    private function getUserActivityAnalysis($startDate): array
    {
        // 实现用户活跃度分析
        return [];
    }

    private function getUserRoleDistribution(): array
    {
        return DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->selectRaw('roles.name, COUNT(*) as count')
            ->groupBy('roles.name')
            ->get()
            ->toArray();
    }

    private function getUserGeographicDistribution(): array
    {
        // 实现地理分布分析
        return [];
    }

    private function getUserEngagementMetrics($startDate): array
    {
        // 实现用户参与度指标
        return [];
    }

    private function getGroupCreationTrend($startDate): array
    {
        return WechatGroup::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    private function getGroupCategoryDistribution(): array
    {
        return WechatGroup::selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get()
            ->toArray();
    }

    private function getGroupPriceAnalysis(): array
    {
        return [
            'avg_price' => WechatGroup::avg('price'),
            'max_price' => WechatGroup::max('price'),
            'min_price' => WechatGroup::min('price'),
            'price_ranges' => WechatGroup::selectRaw('
                CASE 
                    WHEN price = 0 THEN "免费"
                    WHEN price <= 50 THEN "1-50元"
                    WHEN price <= 100 THEN "51-100元"
                    WHEN price <= 200 THEN "101-200元"
                    ELSE "200元以上"
                END as price_range,
                COUNT(*) as count
            ')
            ->groupBy('price_range')
            ->get()
            ->toArray(),
        ];
    }

    private function getGroupPopularityRanking($startDate): array
    {
        return WechatGroup::withCount(['orders' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }])
        ->orderBy('orders_count', 'desc')
        ->limit(20)
        ->get()
        ->toArray();
    }

    private function getGroupPerformanceMetrics($startDate): array
    {
        // 实现群组性能指标
        return [];
    }

    // 继续实现其他私有方法...
    private function getOrderTrend($startDate): array
    {
        return Order::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    private function getPaymentAnalysis($startDate): array
    {
        return Order::where('created_at', '>=', $startDate)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('payment_method')
            ->get()
            ->toArray();
    }

    private function getConversionFunnel($startDate): array
    {
        // 实现转化漏斗分析
        return [];
    }

    private function getOrderAverageMetrics($startDate): array
    {
        return [
            'avg_order_value' => Order::where('created_at', '>=', $startDate)->avg('amount'),
            'avg_processing_time' => 0, // 需要计算平均处理时间
        ];
    }

    private function getRefundAnalysis($startDate): array
    {
        return [
            'refund_rate' => 0, // 需要计算退款率
            'refund_reasons' => [], // 需要分析退款原因
        ];
    }

    private function calculateNetProfit($startDate): float
    {
        $revenue = Order::where('status', Order::STATUS_PAID_INT)
                       ->where('paid_at', '>=', $startDate)->sum('amount');
        $commissions = CommissionLog::where('status', 'settled')
                                  ->where('created_at', '>=', $startDate)->sum('amount');
        
        return $revenue - $commissions;
    }

    private function getRevenueTrend($startDate): array
    {
        return Order::where('status', Order::STATUS_PAID_INT)
            ->where('paid_at', '>=', $startDate)
            ->selectRaw('DATE(paid_at) as date, SUM(amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    private function getRevenueSources($startDate): array
    {
        // 实现收入来源分析
        return [];
    }

    private function getProfitAnalysis($startDate): array
    {
        // 实现利润分析
        return [];
    }

    private function getCostBreakdown($startDate): array
    {
        // 实现成本分解
        return [];
    }

    private function getRevenueForecasting($startDate): array
    {
        // 实现收入预测
        return [];
    }

    private function getActiveDistributors($startDate): int
    {
        return CommissionLog::where('created_at', '>=', $startDate)
            ->distinct('user_id')
            ->count('user_id');
    }

    private function getTopDistributors($startDate): array
    {
        return CommissionLog::with('user')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('user_id, SUM(amount) as total_commission')
            ->groupBy('user_id')
            ->orderBy('total_commission', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    private function getDistributorPerformance($startDate): array
    {
        // 实现分销商性能分析
        return [];
    }

    private function getCommissionDistribution($startDate): array
    {
        return CommissionLog::where('created_at', '>=', $startDate)
            ->selectRaw('level, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('level')
            ->orderBy('level')
            ->get()
            ->toArray();
    }

    private function getRecruitmentAnalysis($startDate): array
    {
        // 实现招募分析
        return [];
    }

    private function getLevelProgression($startDate): array
    {
        // 实现等级进展分析
        return [];
    }

    private function getDistributorRetention($startDate): array
    {
        // 实现分销商留存分析
        return [];
    }
}