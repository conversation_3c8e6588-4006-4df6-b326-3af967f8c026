<template>
  <div class="gallery-preview">
    <div class="gallery-container">
      <div class="gallery-header">
        <h3 class="gallery-title">精彩瞬间</h3>
      </div>
      
      <div v-if="galleryImages.length > 0" class="gallery-content">
        <div class="image-grid" :class="`grid-${columns}`">
          <div
            v-for="(image, index) in displayImages"
            :key="index"
            class="image-item"
            @click="previewImage(index)"
          >
            <img :src="image" :alt="`图片${index + 1}`" />
            <div class="image-overlay">
              <el-icon class="overlay-icon"><ZoomIn /></el-icon>
            </div>
          </div>
          
          <!-- 更多图片提示 -->
          <div
            v-if="hasMoreImages"
            class="more-images"
            @click="showAllImages"
          >
            <div class="more-overlay">
              <div class="more-text">+{{ remainingCount }}</div>
              <div class="more-hint">更多图片</div>
            </div>
          </div>
        </div>
        
        <!-- 缩略图导航 -->
        <div v-if="showThumbnails && galleryImages.length > 1" class="thumbnail-nav">
          <div
            v-for="(image, index) in galleryImages"
            :key="index"
            class="thumbnail-item"
            :class="{ active: currentIndex === index }"
            @click="setCurrentImage(index)"
          >
            <img :src="image" :alt="`缩略图${index + 1}`" />
          </div>
        </div>
      </div>
      
      <div v-else class="gallery-placeholder">
        <el-icon class="placeholder-icon"><Picture /></el-icon>
        <div class="placeholder-text">暂无展示图片</div>
        <div class="placeholder-hint">请上传群组相关图片</div>
      </div>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="80%"
      append-to-body
      class="image-preview-dialog"
    >
      <div class="preview-content">
        <img
          :src="currentPreviewImage"
          :alt="`预览图片${currentIndex + 1}`"
          class="preview-image"
        />
        
        <!-- 导航按钮 -->
        <div v-if="galleryImages.length > 1" class="preview-nav">
          <el-button
            @click="prevImage"
            :disabled="currentIndex === 0"
            circle
            size="large"
            class="nav-button prev-button"
          >
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          
          <el-button
            @click="nextImage"
            :disabled="currentIndex === galleryImages.length - 1"
            circle
            size="large"
            class="nav-button next-button"
          >
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        
        <!-- 图片信息 -->
        <div class="preview-info">
          <span class="image-counter">
            {{ currentIndex + 1 }} / {{ galleryImages.length }}
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Picture, ZoomIn, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  section: {
    type: Object,
    default: () => ({})
  },
  groupData: {
    type: Object,
    default: () => ({})
  },
  preview: {
    type: Boolean,
    default: false
  }
})

const previewVisible = ref(false)
const currentIndex = ref(0)

// 计算属性
const galleryImages = computed(() => {
  return props.groupData.gallery_images || []
})

const columns = computed(() => {
  return props.section.config?.columns || 3
})

const showThumbnails = computed(() => {
  return props.section.config?.showThumbnails !== false
})

const maxDisplay = computed(() => {
  return columns.value * 2 // 显示两行
})

const displayImages = computed(() => {
  return galleryImages.value.slice(0, maxDisplay.value)
})

const hasMoreImages = computed(() => {
  return galleryImages.value.length > maxDisplay.value
})

const remainingCount = computed(() => {
  return galleryImages.value.length - maxDisplay.value
})

const currentPreviewImage = computed(() => {
  return galleryImages.value[currentIndex.value] || ''
})

// 方法
const previewImage = (index) => {
  currentIndex.value = index
  previewVisible.value = true
}

const setCurrentImage = (index) => {
  currentIndex.value = index
}

const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

const nextImage = () => {
  if (currentIndex.value < galleryImages.value.length - 1) {
    currentIndex.value++
  }
}

const showAllImages = () => {
  // 显示所有图片的逻辑
  previewImage(maxDisplay.value)
}
</script>

<style lang="scss" scoped>
.gallery-preview {
  .gallery-container {
    padding: 20px;
    
    .gallery-header {
      margin-bottom: 16px;
      
      .gallery-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #67c23a;
        display: inline-block;
      }
    }
    
    .gallery-content {
      .image-grid {
        display: grid;
        gap: 8px;
        margin-bottom: 16px;
        
        &.grid-2 {
          grid-template-columns: repeat(2, 1fr);
        }
        
        &.grid-3 {
          grid-template-columns: repeat(3, 1fr);
        }
        
        &.grid-4 {
          grid-template-columns: repeat(4, 1fr);
        }
        
        .image-item {
          position: relative;
          aspect-ratio: 1;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: transform 0.3s;
          
          &:hover {
            transform: scale(1.05);
            
            .image-overlay {
              opacity: 1;
            }
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s;
            
            .overlay-icon {
              color: white;
              font-size: 24px;
            }
          }
        }
        
        .more-images {
          position: relative;
          aspect-ratio: 1;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          background: #f5f7fa;
          border: 2px dashed #dcdfe6;
          
          .more-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #909399;
            
            .more-text {
              font-size: 24px;
              font-weight: 600;
              margin-bottom: 4px;
            }
            
            .more-hint {
              font-size: 12px;
            }
          }
        }
      }
      
      .thumbnail-nav {
        display: flex;
        gap: 4px;
        overflow-x: auto;
        padding: 8px 0;
        
        .thumbnail-item {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid transparent;
          transition: border-color 0.3s;
          flex-shrink: 0;
          
          &.active {
            border-color: #409eff;
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
    
    .gallery-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #909399;
      text-align: center;
      
      .placeholder-icon {
        font-size: 32px;
        margin-bottom: 12px;
      }
      
      .placeholder-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .placeholder-hint {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
}

// 预览对话框样式
:deep(.image-preview-dialog) {
  .el-dialog__body {
    padding: 0;
  }
  
  .preview-content {
    position: relative;
    text-align: center;
    
    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
    
    .preview-nav {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      pointer-events: none;
      
      .nav-button {
        pointer-events: auto;
        background: rgba(0, 0, 0, 0.5);
        border: none;
        color: white;
        
        &:hover {
          background: rgba(0, 0, 0, 0.7);
        }
        
        &:disabled {
          background: rgba(0, 0, 0, 0.2);
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
    
    .preview-info {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 16px;
      border-radius: 16px;
      font-size: 14px;
    }
  }
}
</style>
